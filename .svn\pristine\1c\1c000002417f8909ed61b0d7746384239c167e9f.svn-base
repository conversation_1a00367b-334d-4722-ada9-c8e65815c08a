#include <sys/ioctl.h>
#include <net/if.h>
#include <linux/mii.h>  // 定义 MII 寄存器操作结构体
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <stdio.h>
#include <stdint.h>
#include <linux/sockios.h> 
#include <linux/ethtool.h>
#include <math.h>
#include <time.h>

#define SIOCGMIIREG	0x8948		/* Read MII PHY register.	*/


// 读取PHY寄存器
int phy_read(const char *ifname, uint16_t phy_addr, uint16_t reg_num, uint16_t *val) {
    int fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (fd < 0) {
        perror("socket");
        return -1;
    }

    struct ifreq ifr;
    struct mii_ioctl_data *mii = (struct mii_ioctl_data *)&ifr.ifr_data;
    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, ifname, IFNAMSIZ - 1);

    mii->phy_id = phy_addr;
    mii->reg_num = reg_num;

    if (ioctl(fd, SIOCGMIIREG, &ifr) < 0) {
        perror("ioctl(SIOCGMIIREG)");
        close(fd);
        return -1;
    }

    *val = mii->val_out;
    close(fd);
    return 0;
}


// 写入PHY寄存器
int phy_write(const char *ifname, uint16_t phy_addr, uint16_t reg_num, uint16_t value) {
    int fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (fd < 0) {
        perror("socket");
        return -1;
    }

    struct ifreq ifr;
    struct mii_ioctl_data *mii = (struct mii_ioctl_data *)&ifr.ifr_data;
    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, ifname, IFNAMSIZ - 1);

    mii->phy_id = phy_addr;
    mii->reg_num = reg_num;
    mii->val_in = value;

    if (ioctl(fd, SIOCSMIIREG, &ifr) < 0) {
        perror("ioctl(SIOCSMIIREG)");
        close(fd);
        return -1;
    }

    close(fd);
    return 0;
}

// 打印帮助信息
void print_help(const char *prog_name) {
    printf("Usage: %s <interface> <phy_addr> <reg_num> [read|write <value>]\n", prog_name);
    printf("Example:\n");
    printf("  %s eth0 0x01 0x00 read          # Read BMCR register\n", prog_name);
    printf("  %s eth0 0x01 0x00 write 0x1200  # Write BMCR register\n", prog_name);
}

float calculate_sqi(uint16_t val)
{
    return 10.0 * log10(val / 32768.0);
}

int main(int argc, char **argv) 
{
    uint16_t valBak;
    uint16_t val;
    float sqi = .0;
    FILE *file;
    struct timespec delay = {1, 100000000}; 

    while (1) {
        if (phy_write("eth0", 0x01, 0x1e, 0x28) != 0) {
            fprintf(stderr, "Write failed\n");
            continue;
        }

        if (phy_read("eth0", 0x01, 0x1f, &valBak) != 0) {
            fprintf(stderr, "Read failed\n");
            continue;
        }
        val = valBak | 0x800;

        if (phy_write("eth0", 0x01, 0x1f, val) != 0) {
            fprintf(stderr, "Write failed\n");
            continue;
        }

        if (phy_write("eth0", 0x01, 0x1e, 0x108) != 0) {
            fprintf(stderr, "Write failed\n");
            continue;
        }

        sqi = .0;
        for (int i = 0; i < 100; i++) {
            if (phy_read("eth0", 0x01, 0x1f, &val) != 0) {
                fprintf(stderr, "Read failed\n");
            }
            sqi += calculate_sqi(val);
        }
        sqi = -20.0 - (sqi / 100);

        file = fopen("sqi", "w");

        if (sqi > 8.0) {
            fprintf(file, "{\"value\":\"5\"}");
        } else if (sqi > 6.0) {
            fprintf(file, "{\"value\":\"4\"}");
        } else if (sqi > 4.0) {
            fprintf(file, "{\"value\":\"3\"}");
        } else if (sqi > 2.0) {
            fprintf(file, "{\"value\":\"2\"}");
        } else if (sqi > 0.0) {
            fprintf(file, "{\"value\":\"1\"}");
        } else if (sqi > -2.0) {
            fprintf(file, "{\"value\":\"0A\"}");
        } else if (sqi > -4.0) {
            fprintf(file, "{\"value\":\"0B\"}");
        } else if (sqi > -6.0) {
            fprintf(file, "{\"value\":\"0C\"}");
        } else if (sqi > -8.0) {
            fprintf(file, "{\"value\":\"0D\"}");
        } else {
            fprintf(file, "{\"value\":\"FF\"}");
        }
        fclose(file);

        if (phy_write("eth0", 0x01, 0x1e, 0x28) != 0) {
            fprintf(stderr, "Write failed\n");
            continue;
        }
        
        if (phy_write("eth0", 0x01, 0x1f, valBak) != 0) {
            fprintf(stderr, "Write failed\n");
            continue;
        }
        nanosleep(&delay, NULL);
    }

    return 0;
}
