root@ZhuoTK:/# dmesg
[  230.590000] GobiNet 1-1.3:1.4 usb0: register 'GobiNet' at usb-101c0000.ehci-1.3, GobiNet Ethernet Device, 02:50:f4:00:00:00
[  230.600000] creating qcqmi0

root@ZhuoTK:/# quectel-CM -s cmnet &
[04-13_03:24:58:213] Quectel_QConnectManager_Linux_V1.6.0.25
[04-13_03:24:58:216] Find /sys/bus/usb/devices/1-1.3 idVendor=0x2c7c idProduct=0x125, bus=0x001, dev=0x004
[04-13_03:24:58:218] Auto find qmichannel = /dev/qcqmi0
[04-13_03:24:58:218] Auto find usbnet_adapter = usb0
[04-13_03:24:58:218] netcard driver = GobiNet, driver version = V1.6.2.13
[04-13_03:24:58:219] Modem works in QMI mode
[04-13_03:24:58:260] Get clientWDS = 7
[04-13_03:24:58:292] Get clientDMS = 8
[04-13_03:24:58:324] Get clientNAS = 9
[04-13_03:24:58:355] Get clientUIM = 10
[04-13_03:24:58:388] Get clientWDA = 11
[04-13_03:24:58:420] requestBaseBandVersion EC25EFAR06A11M4G
[04-13_03:24:58:548] requestGetSIMStatus SIMStatus: SIM_READY
[04-13_03:24:58:549] requestSetProfile[1] cmnet///0
[04-13_03:24:58:613] requestGetProfile[1] cmnet///0
[04-13_03:24:58:645] requestRegistrationState2 MCC: 460, MNC: 0, PS: Attached, DataCap: LTE
[04-13_03:24:58:677] requestQueryDataCall IPv4ConnectionStatus: DISCONNECTED
[04-13_03:24:58:677] ifconfig usb0 0.0.0.0
[04-13_03:24:58:696] ifconfig usb0 down
[04-13_03:24:59:028] requestSetupDataCall WdsConnectionIPv4Handle: 0x87245bd0
[04-13_03:24:59:189] ifconfig usb0 up
[04-13_03:24:59:214] you are use OpenWrt?
[04-13_03:24:59:215] should not calling udhcpc manually?
[04-13_03:24:59:215] should modify /etc/config/network as below?
[04-13_03:24:59:215] config interface wan
[04-13_03:24:59:215] 	option ifname	usb0
[04-13_03:24:59:215] 	option proto	dhcp
[04-13_03:24:59:215] should use "/sbin/ifstaus wan" to check usb0 's status?
[04-13_03:24:59:216] busybox udhcpc -f -n -q -t 5 -i usb0
[04-13_03:24:59:226] udhcpc (v1.23.2) started
[04-13_03:24:59:238] Sending discover...
[04-13_03:24:59:248] Sending select for *************...
[04-13_03:24:59:251] Lease of ************* obtained, lease time 7200
[04-13_03:24:59:257] udhcpc: ifconfig usb0 ************* netmask *************** broadcast +
[04-13_03:24:59:266] udhcpc: setting default routers: *************

root@ZhuoTK:/# ifconfig usb0
usb0      Link encap:Ethernet  HWaddr 02:50:F4:00:00:00  
          inet addr:*************  Mask:***************
          inet6 addr: fe80::50:f4ff:fe00:0/64 Scope:Link
          UP RUNNING NOARP  MTU:1500  Metric:1
          RX packets:6 errors:0 dropped:0 overruns:0 frame:0
          TX packets:6 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000 
          RX bytes:916 (916.0 B)  TX bytes:960 (960.0 B)

root@ZhuoTK:/# ip ro show
default via ************* dev usb0 
*************/30 dev usb0  proto kernel  scope link  src ************* 
***********/24 dev br-lan  proto kernel  scope link  src ************* 

root@ZhuoTK:/# killall quectel-CM
[04-13_03:25:38:779] requestDeactivateDefaultPDP WdsConnectionIPv4Handle
[04-13_03:25:39:061] ifconfig usb0 0.0.0.0
[04-13_03:25:39:072] ifconfig usb0 down
[04-13_03:25:39:284] GobiNetThread exit
[04-13_03:25:39:285] qmi_main exit
