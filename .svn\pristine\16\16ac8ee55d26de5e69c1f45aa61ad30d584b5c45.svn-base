#! /bin/sh
cd /root/ko
./load3518e -a -i -sensor imx224 -osmem 40M
cd -
mount -t tmpfs -o size=10M tmpfs /var/
#ulimit -c 10240
#echo "/var/core.%e" > /proc/sys/kernel/core_pattern
touch /var/mainStream
touch /var/subStream
touch /var/audStream
mkdir /var/snap
mkdir /var/info
mkdir /var/log
mkdir /var/log/uploaded
mkdir /var/run
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:.:/usr/lib/
cp /etc/config.xml /var/config.xml
cp /sbin/reboot /var/reboot
tar -zxf /root/webui.tar.gz -C /var/
if [ ! -f /etc/serialNumber ] && [ ! -f /etc/defaultIpaddr ];then
	telnetd &
fi
/root/ipsys >> /dev/null &
/root/wtd.sh >> /dev/null &
