#ifndef __TP9950_H__
#define __TP9950_H__

#define BRIGHTNESS			0x10
#define CONTRAST			0x11
#define SATURATION			0x12
#define HUE					0X13
#define SHARPNESS			0X14

typedef struct _tp2802_image_adjust
{
    unsigned char chip;
    unsigned char ch;
	unsigned short hue;
	unsigned short contrast;
	unsigned short brightness;
	unsigned short saturation;
	unsigned short sharpness;
} tp2802_image_adjust;

#define TP2802_IOC_MAGIC            'v'

#define TP2802_GET_VIDEO_MODE	    _IOWR(TP2802_IOC_MAGIC, 4, tp2802_video_mode)
#define TP2802_SET_IMAGE_ADJUST	    _IOW(TP2802_IOC_MAGIC, 6, tp2802_image_adjust)
#define TP2802_GET_IMAGE_ADJUST	    _IOWR(TP2802_IOC_MAGIC, 7, tp2802_image_adjust)

#endif
