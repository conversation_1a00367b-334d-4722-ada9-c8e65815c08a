﻿#ifndef __COM_PROTOCOL_H__
#define __COM_PROTOCOL_H__
//硬件平台宏定义


//让结构体内的成员连续存储，不考虑对齐问题
#pragma pack(push,1)

//请求命令的回复，opcode要 | 0x8000，应答
#define MCU_CMD_RSP (0x8000)

//请求失败的回复, opcode要 | 0x4000，应答
#define CMD_FAIL (0x4000)

//通讯协议包的大小定义
#define MCU_PACKET_SIZE (64)                                            // 包大小,再大了可能程序起不来
#define MCU_PACKET_HEAD_SIZE (14)                                       // 非数据包部分的大小
#define MCU_PACKET_DATA_SIZE (MCU_PACKET_SIZE - MCU_PACKET_HEAD_SIZE)   // 包数据大小
#define MCU_CRC_HEAD_SIZE    (10)                                       // 需要做CRC检验的头部大小
#define MCUMSG_STARTCODE (0xAABB)                                       // 包头
#define SUB_MCU_PACKET_DATA_SIZE (46)                                   // 子数据包数据大小
#define SUB_MCU_PACKET_ID_SIZE (2)                                      // 子数据包包ID
#define SUB_MCU_PACKET_DATA_LEN_SIZE (2)                                // 子数据包数据长度


//操作码定义
typedef enum OP_CODE
{   
    //RQ命令为请求命令，对于RQ命令的回复，命令码值为opcode | 0x8000 < 上位机请求下位机 >
    RQ_RQUEST_START = 0X0000,
    RQ_SHAKE_HAND,      // 握手命令: NULL
    RQ_GET_MCU_ID,      // MCU ID 获取: McuId_t
    RQ_MCU_STATUS,      // MCU状态: mcuStatus_t
    RQ_SET_RTC,         // 设置RTC时间: mcuRtcData_t
    RQ_GET_RTC,         // 获取RTC时间: mcuRtcData_t
    RQ_SET_DEVNAME,     // 设置蓝牙设备名: string
    RQ_GET_DEVNAME,     // 获取蓝牙设备名: string
    RQ_GET_VERSION,     // 获取MCU版本号: string
    RQ_POWER_CTRL,      // 电源控制: sysPowerCtl_e
    RQ_BCST_CTRL,       // 广播控制
    RQ_BLTH_CTRL,       // 蓝牙控制(低功耗控制)
    RQ_4G_CTRL,			// 4g控制
    
    //升级相关命令(OP值不可更改)
    RQ_ENTER_DFU = 0X1F00,  // 请求MCU进入设备固件升级状态: NULL
	RQ_CREATE_APP2 = 0X2F00,
	RQ_IMAGE_CRC32 = 0X2F01,
	RQ_ENTER_APP2 = 0X3F00,
	RQ_SEND_PACKET = 0X4F00,
    RQ_SEND_PACKET_END = 0X5F00,
    RQ_SEND_PACKET_20 = 0x6F00,
    RQ_SEND_PACKET_20RCV = 0x7F00,
    RQ_RQUEST_END = 0X3FFF,

    //以下命令为不需要回复的
    EVENT_START  = 0XA000,
    OP_ACK,             // 确认包: NULL
    HEART_BEAT,         // 上层心跳包: NULL
       
    
    // MCU NOTIFY
    MCU_LOG,            // MCU 日志: string
    MCU_RESET_FACTORY,  // MCU 通知ARM恢复出厂设置: NULL
    MCU_STATUS,         // MCU 状态: mcuStatus_t
    MCU_SHUTDOWN,       // MCU 长按电源键关机

	//ACC
	ACC_DELAY_CONFIG,                //ACC延时命令: AccDelay_t6
    ACC,  
    ACC_END,                    //断电确认指令
    ACC_END_LOSE,               //超时指令
    //ACC_END_NOTREADY,         //尚未准备完成
}OP_CODE_T;


//通讯协议
typedef struct tag_MsgMcuPacket
{
    unsigned short  msgStartode;    // 0xAABB
    unsigned short  msgCrc;         // 以下全部有效值校验码
    unsigned short  msgOpcode;      // MCU私有操作码 < 规定请求 OP 小于 0x8000,响应 op = requestOp|0x8000 >
    unsigned long   mPrivateVal;    // 私有值
    unsigned short  msgArgLen;      // 数据长度
    unsigned short  msgPackageId;   // 唯一 ID ，发送方累加，保证唯一性
    unsigned char   arg[MCU_PACKET_DATA_SIZE];  // 拓展数据
} MsgMcuPacket_t;

typedef struct tag_MsgMcuSubPacket
{
    unsigned short u32SubPackID;                     // 子升级包序号ID
    unsigned short u32SubPackLen;                    // 子升级包长度
    unsigned char  arg[SUB_MCU_PACKET_DATA_SIZE]; 	 // 子升级包数据
} MsgMcuSubPacket_t;

/* 从 MCU 获取到的 RTC 时间值 */
typedef struct tag_RtcData
{
    int sec;
    int usec;
} mcuRtcData_t;


/* mcu 主要状态 */
typedef struct tag_mcuStatus
{
    unsigned short u16VBus;         /* 适配器电压(mV) */
    unsigned short u16BatVoltage;   /* 电池电压(mV) */
    unsigned short u16LoadVoltage;  /* 负载电压(mV) */
    short          s16Temperature;  /* 电池温度 */
    unsigned char  u8BatLevel;      /* 电池电量(0-100) */
    unsigned char  bIsCharging;     /* 是否在充电中 */
}mcuStatus_t; 

#define DEF_MCUSTATUS_SLEEP                     (0)
#define DEF_MCUSTATUS_REGULATOR                 (1)
#define DEF_MCUSTATUS_SHAKEHAND                 (2)
#define DEF_MCUSTATUS_WORK                      (3)
#define DEF_MCUSTATUS_UPGRADE                   (4)
#define DEF_MCUSTATUS_BOOTUPGRADE               (5)
#define DEF_MCUSTATUS_POWEROFF                  (6)

//RQ_GET_MCU_ID
#define MCU_ID_LEN 6
typedef struct tag_McuId
{
    unsigned char id[MCU_ID_LEN];   /* 取蓝牙MAC做为MCU ID */
} McuId_t;

/* fail_t.type or 回调传入 res 取值 */
typedef enum MCUMSG_REQUESTRES_ENum
{
    MCUMSG_SUCCESS_RES  =  0,
    MCUMSG_DEFAULT_FAIL,            // 内部错误，暂未定义 255
    MCUMSG_TIMEOUT_FAIL,            // 执行超时 254
    MCUMSG_REQUEST_NO_REGISTER,     // 未注册的 op 253
    MCUMSG_AUTHORITY_FAIL,          // 权限校验错误 252
    MCUMSG_FORMAT_ERROR ,           // Json-Body 格式错误 251
    MCUMSG_NO_ENOUGH_MEMORY,        // 数据在传输的过程当中空间不足 250
    MCUMSG_RANGE_OUT    ,           // 超出范围（或参数不合法） 249
    MCUMSG_REPEAT_REQUEST,          // 在某执行操作尚未完成的时候，再次调用该执行操作 248
    MCUMSG_STORAGE_LOCKED,          // 电子锁解锁，SD 卡读写被禁止 247
    MCUMSG_CLIP_QUEUE_FULL,         // CLIP 请求队列满 246
    MCUMSG_DEVICE_BUSY,             // 设备忙 245
    MCUMSG_DEVICE_LACK,             // 设备缺失 244
    MCUMSG_FILE_LACK,               // 文件缺失 243
    MCUMSG_JSON_LOSTPARAM,          // JSON 当中缺失某些参数
    MCUMSG_NOTSUPPORT,
    MCUMSG_NULL_RES                 //   未赋值
}MCUMSG_REQUESTRES_E;

/* 失败包  （消息分发器内部结构体 ）
 * */
typedef struct tag_cmdFail_t
{
    char type;                      // MCUMSG_REQUESTRES_E
} CmdFail_t;


/*系统电源管理*/
typedef enum tag_sysPowerCtl
{
    PWR_SLEEP = 0,                  // 系统进行待机模式(给摄像头断电, 蓝牙进行广播)
    PWR_REBOOT,                     // 重启系统(给摄像头重新上电)
    PWR_DOWN,                       // 系统关机(摄像头断电, MCU关机)
    PWR_BUTT = 0x7FFFFFFF           // 使编译宽度达4字节
} sysPowerCtl_e;

//ACC状态定义
typedef enum ACC_STATUS
{	
    ACC_ON = 0,         //ACC上电
    ACC_OFF,            //ACC断电
    ACC_SHUT_DOWN	    //ACC关机消息上传
}ACC_STATUS_T;
//SD_ACC
typedef struct tag_AccState
{
	char state;
}AccState_t;

typedef struct tag_AccDelay
{
	unsigned int sec;	 
}AccDelay_t;

#pragma pack(pop)

#endif
