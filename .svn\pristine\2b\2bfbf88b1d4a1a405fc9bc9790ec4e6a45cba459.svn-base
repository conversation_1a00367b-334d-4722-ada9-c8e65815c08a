/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：mpp_aio.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2015-01-29

文件功能描述: 封装海思MPP音频输入输出模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明
  1. :
  2. :
  3. :
  4. :
  5. :
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _MPP_AIO_H_
#define _MPP_AIO_H_

#include "common.h"
#include "mpp_com.h"
#include "media.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */


/* AIO接口工作模式定义 */
typedef enum tagSIOMode_E
{
    MPP_AIO_MODE_I2S_MASTER  = 0,       /* I2S 主模式 */
    MPP_AIO_MODE_I2S_SLAVE,             /* I2S 从模式 */
    MPP_AIO_MODE_PCM_MASTER,            /* PCM 主模式 */
    MPP_AIO_MODE_PCM_SLAVE,             /* PCM 从模式 */
    
    MPP_AIO_MODE_BUTT  
} MPP_AIO_MODE_E;

/* 音频采样率定义 */
typedef enum tagAudioSampleRate_E
{
    MPP_AUDIO_SAMPLE_RATE_8K   = 0,     /* 8K 采样率 */
    MPP_AUDIO_SAMPLE_RATE_16K,          /* 16K 采样率 */
    MPP_AUDIO_SAMPLE_RATE_32K,          /* 32K 采样率 */

    MPP_AUDIO_SAMPLE_RATE_BUTT,
} MPP_AUDIO_SAMPLE_RATE_E;

/* 音频采样精度定义 */
typedef enum tagAudioBitWidth_E
{
    MPP_AUDIO_BIT_WIDTH_8   = 0,        /* 8bit 宽度 */
    MPP_AUDIO_BIT_WIDTH_16  = 1,        /* 16bit 宽度*/
    
    MPP_AUDIO_BIT_WIDTH_BUTT,
} MPP_AUDIO_BIT_WIDTH_E;


/* 音频输入配置结构定义 */
typedef struct tagAIOCfg_S
{
    SV_BOOL                 bPreInited;     /* 是否预初始化 */
    uint32                  u32ChnNum;      /* 输入通道数目 */
    MPP_AIO_MODE_E          enAioMode;      /* AIO接口工作模式定义 */
    MPP_AUDIO_SAMPLE_RATE_E enSampleRate;   /* 音频采样率 (ACC编码协议只支持16K) */
    MPP_AUDIO_BIT_WIDTH_E   enBitWidth;     /* 音频采样精度 */
    uint32                  u32Volume;      /* 音量 [1-30] */
    SV_BOOL                 bAudioEnable;   /* 是否使能音频 */
    SV_BOOL                 bAudioMicEnable;/* 是否使能MIC音频 */
    SV_BOOL                 bAudioAlarmEnable;  /*是否使能Alarm音频 */
    /* 目前以下仅在RV1126使用 */
    AUDIO_ENCODE_TYPE_E     enAencType;     /* 音频编码协议 */
    MEDIA_DATA_CALLBACK     pfDataCallback; /* 媒体流数据回调函数指针AI->AENC:LCPM使用 */
} MPP_AIO_CONF_S;

/* 音频输出配置结构定义 */
typedef struct tagAOCfg_S
{    
    uint32                  u32Volume;      /* 音量 [1-22] */
    MPP_AUDIO_SAMPLE_RATE_E enSampleRate;   /* 音频采样率 (ACC编码协议只支持16K) */
    SV_BOOL                 bAudioMicEnable;/* 是否使能MIC音频 */
} MPP_AO_CONF_S;


/******************************************************************************
 * 函数功能: 初始化AI模块
 * 输入参数: pstAiConf --- 音频输入配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_ai_Init(MPP_AIO_CONF_S *pstAiConf);

/******************************************************************************
 * 函数功能: 去初始化AI模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_ai_Fini();

/******************************************************************************
 * 函数功能: 启动AI模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_ai_Start();

/******************************************************************************
 * 函数功能: 关闭AI模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_ai_Stop();

/******************************************************************************
 * 函数功能: 初始化AO模块
 * 输入参数: pstAoConf --- 音频输出配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_ao_Init(MPP_AO_CONF_S *pstAoConf);

/******************************************************************************
 * 函数功能: 去初始化AO模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_ao_Fini();

/******************************************************************************
 * 函数功能: AI-AO通道绑定
 * 输入参数: s32AiChn --- AI通道号
             s32AoChn --- AO通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_aio_Bind(sint32 s32AiChn, sint32 s32AoChn);

/******************************************************************************
 * 函数功能: AI-AO通道解绑定
 * 输入参数: s32AiChn --- AI通道号
             s32AoChn --- AO通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_aio_UnBind(sint32 s32AiChn, sint32 s32AoChn);

/******************************************************************************
 * 函数功能: 设置音频通道音频大小
 * 输入参数: s32Chn --- 编码通道号
             u32Volume --- 音量值 [1,30]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 
 *****************************************************************************/
extern sint32 mpp_aio_SetVolume(sint32 s32Chn, uint32 u32Volume);

/******************************************************************************
 * 函数功能: 设置音频采样率
 * 输入参数: s32Chn --- 编码通道号
             enSampleRate --- 音频采样率
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 
 *****************************************************************************/
extern sint32 mpp_aio_SetSampleRate(sint32 s32Chn, MPP_AUDIO_SAMPLE_RATE_E enSampleRate);

/******************************************************************************
 * 函数功能: 设置音频通道为静音
 * 输入参数: s32Chn --- 编码通道号
             bEnable --- 是否使能静音
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 
 *****************************************************************************/
extern sint32 mpp_aio_SetMute(sint32 s32Chn, SV_BOOL bEnable);

/******************************************************************************
 * 函数功能: 设置输出音量大小
 * 输入参数: s32Chn --- 编码通道号
             u32Volume --- 音量值 [1,22]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 
 *****************************************************************************/
extern sint32 mpp_ao_SetVolume(sint32 s32Chn, uint32 u32Volume);

extern sint32 mpp_ao_crateAOTalkBuffer();

extern sint32 mpp_ao_playTalkBuffer(sint8 *pBuffer);

extern sint32 mpp_ao_playTalkBufferRealse();

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif  /* _MPP_AIO_H_ */

