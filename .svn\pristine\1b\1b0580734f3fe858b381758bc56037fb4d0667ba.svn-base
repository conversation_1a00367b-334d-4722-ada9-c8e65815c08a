# Sample udhcpd configuration file (/etc/udhcpd.conf)
# Values shown are defaults

# The start and end of the IP lease block
start	**************0
end		***************

# The interface that udhcpd will use
interface	eth0

# The maximum number of leases (includes addresses reserved
# by OFFER's, DECLINE's, and ARP conflicts). Will be corrected
# if it's bigger than IP lease block, but it ok to make it
# smaller than lease block.
max_leases	101

# The amount of time that an IP will be reserved (leased to nobody)
# if a DHCP decline message is received (seconds)
#decline_time	3600

# The amount of time that an IP will be reserved
# if an ARP conflict occurs (seconds)
#conflict_time	3600

# How long an offered address is reserved (seconds)
#offer_time	60

# If client asks for lease below this value, it will be rounded up
# to this value (seconds)
#min_lease	60

# The location of the pid file
#pidfile	/var/run/udhcpd.pid

# The location of the leases file
lease_file	/var/lib/misc/udhcpd.leases

# The time period at which udhcpd will write out leases file.
# If this is 0, udhcpd will never automatically write leases file.
# Specified in seconds.
#auto_time	7200

# Every time udhcpd writes a leases file, the below script will be called
#notify_file			# default: no script
#notify_file	dumpleases	# useful for debugging

# The following are bootp specific options
# next server to use in bootstrap
#siaddr		************	# default: 0.0.0.0 (none)
# tftp server name
#sname		zorak		# default: none
# tftp file to download (e.g. kernel image)
#boot_file	/var/nfs_root	# default: none

# Static leases map
#static_lease 00:60:08:11:CE:4E ************
#static_lease 00:60:08:11:CE:3E ************

# The remainder of options are DHCP options and can be specified with the
# keyword 'opt' or 'option'. If an option can take multiple items, such
# as the dns option, they can be listed on the same line, or multiple
# lines.
# Examples:
opt	dns	*******
option	subnet	*************
opt	router	**************
opt	wins	*************
option	dns	*************	# appended to above DNS servers for a total of 3
option	domain	local
option	lease	864000		# default: 10 days
#option	msstaticroutes	10.0.0.0/8 **********		# single static route
#option	staticroutes	10.0.0.0/8 **********, **********/24 **********
# Arbitrary option in hex form:
#option	0x08	01020304	# option 8: "cookie server IP addr: *******"

# Currently supported options (for more info, see options.c):
#opt lease      NUM
#opt subnet     IP
#opt broadcast  IP
#opt router     IP_LIST
#opt ipttl      NUM
#opt mtu        NUM
#opt hostname   STRING		# client's hostname
#opt domain     STRING		# client's domain suffix
#opt search     STRING_LIST	# search domains
#opt nisdomain  STRING
#opt timezone   NUM		# (localtime - UTC_time) in seconds. signed
#opt tftp       STRING		# tftp server name
#opt bootfile   STRING		# tftp file to download (e.g. kernel image)
#opt bootsize   NUM		# size of that file
#opt rootpath   STRING		# (NFS) path to mount as root fs
#opt wpad       STRING
#opt serverid   IP		# default: server's IP
#opt message    STRING		# error message (udhcpd sends it on success too)
#opt vlanid     NUM		# 802.1P VLAN ID
#opt vlanpriority NUM		# 802.1Q VLAN priority
# Options specifying server(s)
#opt dns        IP_LIST
#opt wins       IP_LIST
#opt nissrv     IP_LIST
#opt ntpsrv     IP_LIST
#opt lprsrv     IP_LIST
#opt swapsrv    IP
# Options specifying routes
#opt routes     IP_PAIR_LIST
#opt staticroutes   STATIC_ROUTES # RFC 3442 classless static route option
#opt msstaticroutes STATIC_ROUTES # same, using MS option number
# Obsolete options, no longer supported
#opt logsrv     IP_LIST	# 704/UDP log server (not syslog!)
#opt namesrv    IP_LIST	# IEN 116 name server, obsolete (August 1979!!!)
#opt cookiesrv  IP_LIST	# RFC 865 "quote of the day" server, rarely (never?) used
#opt timesrv    IP_LIST	# RFC 868 time server, rarely (never?) used
# TODO: in development
#opt userclass  STRING		# RFC 3004. set of LASCII strings. "I am a printer" etc
#opt sipserv    STRING LIST	# RFC 3361. flag byte, then: 0: domain names, 1: IP addrs
