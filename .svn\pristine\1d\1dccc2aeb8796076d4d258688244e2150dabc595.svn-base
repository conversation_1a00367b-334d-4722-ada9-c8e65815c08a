/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：autoUpdate.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2018-01-09

文件功能描述: 软件版本升级程序

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <dirent.h>
#include <ctype.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>

#define UPDATE_LOG

#include "common.h"
#include "print.h"
#include "safefunc.h"
#include "../../include/board.h"
#include "config.h"
#include "msg.h"
#include "uuid.h"
#include "log.h"
#include "shellcmd.h"
#include "a32McuUpdate.h"
#include "mcuUpdate.h"
#include "autoUpdate.h"
#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
#include "iniparser.h"
#endif

#include "mtdex_ko.h"
#include <regex.h>
#include <fnmatch.h>


#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1126B))
#include <sys/ioctl.h>
#include <mtd/mtd-abi.h>

#pragma pack(push) //保存对齐状态
#pragma pack(1)
typedef struct tag_userdata_env
{
    uint8 repeat;      /* 重复次数,当重复到 3 后,自动进入烧录模式 */

    uint8 checkSum;    /* 累加校验和 + 1 */
} userdata_env;
#pragma pack(pop)   //恢复对齐状态

#endif

sint32 matchRegex(const char* pattern, const char* userString);

/* 判断是否更新source的标志位文件 */
#define UPDATE_SOURCE_JUDGE "/etc/updatesource"

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_DMS51V1) || defined(BOARD_ADA32V4))
#define UPDATE_BLKDEV_PATH     		"/dev/sda1"
#else
#define UPDATE_BLKDEV_PATH     		"/dev/mmcblk0"
#endif
#define UPDATE_LOG_NAME				"update.log"
static SV_BOOL m_bUpdateSDWritable = SV_FALSE;

#if (defined(BOARD_DMS31V2))
#define AUTOTEST_SCRIPTS 	"/mnt/sdcard/AutoTest/dms31_autoTest.sh"
#elif (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_DMS51V1) || defined(BOARD_ADA32V4))
#define AUTOTEST_SCRIPTS 	"/mnt/sdcard/AutoTest/ada32_autoTest.sh"
#elif (defined(BOARD_ADA900V1))
#define AUTOTEST_SCRIPTS 	"/mnt/sdcard/AutoTest/ada900_autoTest.sh"
#elif (defined(BOARD_ADA47V1))
#define AUTOTEST_SCRIPTS 	"/mnt/sdcard/AutoTest/ada47_autoTest.sh"
#elif (defined(BOARD_HDW845V1))
#define AUTOTEST_SCRIPTS 	"/mnt/sdcard/AutoTest/hdw845_autoTest.sh"
#endif
#define KEY_AUTH_PWD1           "Build~Better66"
#define KEY_AUTH_FLAG           "/etc/id00"
#define KEY_AUTH_FLAG_BRIGADE   "/etc/id01"     /* brigade客户开始需要密钥的版本分割标志 */

/* mtdex.ko 外部文件变量 */
extern char * mtdex_ko_name;
extern char * mtdex_ko_md5;
extern unsigned char mtdex_ko[];
extern unsigned int mtdex_ko_len;

typedef struct tag_UpdateInfo_S
{
    SV_BOOL bIsMcuExist;            /* 是否存在MCU, MCU升级用到 */
    SV_BOOL bUpdateSDWritable;      /* SD卡是否可读写 */
    SV_BOOL bIsFixed;               /* 升级包名是否指定fixed字段(控制升级完后不删除升级包) */
    SV_BOOL bIsCover;               /* 升级包名是否指定cover字段(控制升级完后恢复出厂配置) */
    SV_BOOL bIs360;                 /* 升级包名是否指定360字段(控制升级完后,配置成360模式) */
    SV_BOOL bIsPformat;             /* 升级包名是否指定360P字段(控制升级完后,配置为360模式,P制) */
    SV_BOOL bNeedCheckSD;           /* 是否需要检查SD卡 */
    SV_BOOL bNeedReboot;            /* 升级完成后是否需要重启 */
    SV_BOOL bAutoTestMode;          /* 是否为自动化测试模式 */
    SV_BOOL bUpdateSource;          /* 是否更新专用资源 */
    SV_BOOL bUpdateSpecialVersion;  /* 更新专用版 */
    SV_BOOL bIsRecorder;            /* 是否开启录像 */
    SV_BOOL bIsUpgradeStart;        /* 是否只执行upgrade_start.sh */
    SV_BOOL bIsLaterPkt;            /* 是否正在升级later包 */
    SV_BOOL bSetSVersion;           /* 是否设置专用版 */
    SV_BOOL bIsSetConfig;           /* 是否设置部分的配置 */
    SV_BOOL bRetryFoundMcu;         /* 是否需要重新尝试寻找MCU设备，默认不需要 */
    uint32 u32LogoWidth;            /* logo宽度 */
    uint32 u32LogoHeight;           /* logo高度 */
    uint32 u32LogoFps;              /* logo显示帧率 */
    MEDIA_CVBS enCvbs;              /* CVBS 制式 */
    SV_ROT_ANGLE_E enRotate;        /* 旋转方向 */
    SV_BOOL bGenerateKey;           /* 是否生成密钥 */
    char  szFirmwareVer[32];        /* 软件版本号 */
}UPDATE_INFO_S;

UPDATE_INFO_S m_stUpdateInfo = {0};

static uint32 little2big(uint32 le) {

    return (le & 0xff) << 24
            | (le & 0xff00) << 8
            | (le & 0xff0000) >> 8
            | (le >> 24) & 0xff;
}

SV_BOOL STORAGE_IsWritable(STORAGE_POS_E pos)
{
    return m_stUpdateInfo.bUpdateSDWritable;
}

/******************************************************************************
 * 函数功能: 用系统函数执行shell命令，避免过多的日志打印
 * 输入参数: cmdStr --- 命令行
 * 输出参数: 无
 * 返回值  : shell返回值
 * 注意    : 无
 *****************************************************************************/
int system_sys(char* cmdStr)
{
	return SAFE_SV_System(cmdStr);
}

/******************************************************************************
 * 函数功能: 执行shell指令
 * 输入参数: cmdStr --- 命令行
             timeout --- 超时时间(s)
 * 输出参数: 无
 * 返回值  : shell返回值
 * 注意    : 无
 *****************************************************************************/
int system_ex(char* cmdStr, int timeout)
{
	return SAFE_System(cmdStr,timeout*1000);
}

/******************************************************************************
 * 函数功能: 判断文件是否存在
 * 输入参数: pszPath --- 文件路径
 * 输出参数: 无
 * 返回值  : SV_TRUE --- 文件存在
             SV_FALSE --- 文件不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL update_IsPathExist(char *pszPath)
{
	if (access(pszPath, F_OK) == SV_SUCCESS)
		return SV_TRUE;
	else
		return SV_FALSE;
}

/******************************************************************************
 * 函数功能: 执行shell指令并获取返回结果
 * 输入参数: cmd --- 命令行
 			 result --- 返回结果
             len --- 读取长度
 * 输出参数: 无
 * 返回值  : shell返回值
 * 注意    : 无
 *****************************************************************************/
int system_ex_recv(char* cmd, char *result, int len)
{
	return SAFE_System_Recv(cmd, result, len);
}

/******************************************************************************
 * 函数功能: 拷贝自动化测试相关文件时的日志记录
 * 输入参数: logContent --- 日志内容
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void autoTestReadyWorkLog(char *logContent)
{
	char szCmd[128] = {0};
	char *pszLogPath = "/tmp/autoTestLog";

	if (logContent == NULL)
	{
		return;
	}

	sprintf(szCmd, "echo -e \"%s\" >> %s", logContent, pszLogPath);
	system_ex(szCmd, 10);
}

/******************************************************************************
 * 函数功能: 拷贝自动化测试相关文件
 * 输入参数: pszSdcardPath --- SD卡挂载目录
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
sint32 autoTestReadyWork(char *pszSdcardPath)
{
	char *pszDestPath = "/tmp/AutoTest";
	char szIpsysPath[32] = {0};
	char szFactoryPath[32] = {0};
	char szIpPath[32] = {0};
	char szCellTestPath[64] = {0};
	char szCellReadyWavPath[64] = {0};
	char szCellErrorWavPath[64] = {0};
	char szCellFinishWavPath[64] = {0};
	char szTestBoardIpPath[64] = {0};
	char szCmd[128] = {0};
	SV_BOOL bIpsysExist = SV_FALSE, bFactoryExist = SV_FALSE, bIpExist = SV_FALSE;

#if (defined(BOARD_DMS31V2))
	SV_BOOL	bCellTestExist = SV_FALSE, bCellReadyWavExist = SV_FALSE, bCellErrorWavExist = SV_FALSE, bCellFinishWavExist = SV_FALSE;
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_DMS51V1) || defined(BOARD_ADA32V4))
	SV_BOOL bTestBoardIpExist = SV_FALSE;
#endif

	if (pszSdcardPath == NULL || update_IsPathExist(pszSdcardPath))
	{
		print_level(SV_ERROR, "%s is not exist!\n", pszSdcardPath);
		return SV_FAILURE;
	}

	sprintf(szCmd, "mkdir %s", pszDestPath);
	system_ex(szCmd, 50);

	sprintf(szIpsysPath, "%s/AutoTest/ipsys", pszSdcardPath);
	sprintf(szFactoryPath, "%s/AutoTest/factory", pszSdcardPath);
	sprintf(szIpPath, "%s/AutoTest/ip.txt", pszSdcardPath);

#if (defined(BOARD_DMS31V2))
	sprintf(szCellTestPath, "%s/AutoTest/CellTest.txt", pszSdcardPath);
	sprintf(szCellReadyWavPath, "%s/AutoTest/CellReady.wav", pszSdcardPath);
	sprintf(szCellErrorWavPath, "%s/AutoTest/CellTestError.wav", pszSdcardPath);
	sprintf(szCellFinishWavPath, "%s/AutoTest/CellTestFinish.wav", pszSdcardPath);
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_DMS51V1) || defined(BOARD_ADA32V4))
	sprintf(szTestBoardIpPath, "%s/AutoTest/TestBoardIP.txt", pszSdcardPath);
#endif

	if (update_IsPathExist(szIpsysPath))
		bIpsysExist = SV_TRUE;

	if (update_IsPathExist(szFactoryPath))
		bFactoryExist = SV_TRUE;

	if (update_IsPathExist(szIpPath))
		bIpExist = SV_TRUE;

#if (defined(BOARD_DMS31V2))
	if (update_IsPathExist(szCellTestPath))
		bCellTestExist = SV_TRUE;

	if (update_IsPathExist(szCellReadyWavPath))
		bCellReadyWavExist = SV_TRUE;

	if (update_IsPathExist(szCellErrorWavPath))
		bCellErrorWavExist = SV_TRUE;

	if (update_IsPathExist(szCellFinishWavPath))
		bCellFinishWavExist = SV_TRUE;
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) ||defined(BOARD_DMS51V1) || defined(BOARD_ADA32V4))
	if (update_IsPathExist(szTestBoardIpPath))
		bTestBoardIpExist = SV_TRUE;
#endif

	if (bIpsysExist && bFactoryExist && bIpExist)
	{
		sprintf(szCmd, "cp %s %s", szIpsysPath, pszDestPath);
		system_ex(szCmd, 10);
		sprintf(szCmd, "cp %s %s", szFactoryPath, pszDestPath);
		system_ex(szCmd, 10);
		sprintf(szCmd, "cp %s %s", szIpPath, pszDestPath);
		system_ex(szCmd, 10);

		sprintf(szIpsysPath, "%s/ipsys", pszDestPath);
		sprintf(szFactoryPath, "%s/factory", pszDestPath);
		sprintf(szIpPath, "%s/ip.txt", pszDestPath);

		bIpsysExist = SV_FALSE;
		bFactoryExist = SV_FALSE;
		bIpExist = SV_FALSE;
		if (update_IsPathExist(szIpsysPath))
			bIpsysExist = SV_TRUE;

		if (update_IsPathExist(szFactoryPath))
			bFactoryExist = SV_TRUE;

		if (update_IsPathExist(szIpPath))
			bIpExist = SV_TRUE;

		if (bIpsysExist && bFactoryExist && bIpExist)
		{
			autoTestReadyWorkLog("copy autoTest file success");
		}
		else
		{
			autoTestReadyWorkLog("copy autoTest file failed");
			sprintf(szCmd, "ipsys exist: %d, factory exist: %d, ip.txt exist: %d", bIpsysExist, bFactoryExist, bIpExist);
			autoTestReadyWorkLog(szCmd);
			return SV_FAILURE;
		}
	}
	else
	{
		sprintf(szCmd, "ipsys exist: %d, factory exist: %d, ip.txt exist: %d", bIpsysExist, bFactoryExist, bIpExist);
		autoTestReadyWorkLog(szCmd);
		return SV_FAILURE;
	}

#if (defined(BOARD_DMS31V2))
	if (bCellTestExist && bCellReadyWavExist && bCellErrorWavExist && bCellFinishWavExist)
	{
		sprintf(szCmd, "cp %s %s", szCellTestPath, pszDestPath);
		system_ex(szCmd, 10);
		sprintf(szCmd, "cp %s %s", szCellReadyWavPath, pszDestPath);
		system_ex(szCmd, 10);
		sprintf(szCmd, "cp %s %s", szCellErrorWavPath, pszDestPath);
		system_ex(szCmd, 10);
		sprintf(szCmd, "cp %s %s", szCellFinishWavPath, pszDestPath);
		system_ex(szCmd, 10);

		sprintf(szCellTestPath, "%s/CellTest.txt", pszDestPath);
		bCellTestExist = SV_FALSE;
		sprintf(szCellReadyWavPath, "%s/CellReady.wav", pszDestPath);
		bCellReadyWavExist = SV_FALSE;
		sprintf(szCellErrorWavPath, "%s/CellTestError.wav", pszDestPath);
		bCellErrorWavExist = SV_FALSE;
		sprintf(szCellFinishWavPath, "%s/CellTestFinish.wav", pszDestPath);
		bCellFinishWavExist = SV_FALSE;

		if (update_IsPathExist(szCellTestPath))
			bCellTestExist = SV_TRUE;
		if (update_IsPathExist(szCellReadyWavPath))
			bCellReadyWavExist = SV_TRUE;
		if (update_IsPathExist(szCellErrorWavPath))
			bCellErrorWavExist = SV_TRUE;
		if (update_IsPathExist(szCellFinishWavPath))
			bCellFinishWavExist = SV_TRUE;

		if (bCellTestExist && bCellReadyWavExist && bCellErrorWavExist && bCellFinishWavExist)
		{
			autoTestReadyWorkLog("copy CellTest.txt and wav files success");
		}
		else
		{
			sprintf(szCmd, "CellTest.txt exist: %d, CellReady.wav exist: %d, CellTestError.wav exist: %d, CellTestFinish.wav exist: %d", bCellTestExist, bCellReadyWavExist, bCellErrorWavExist, bCellFinishWavExist);
			autoTestReadyWorkLog(szCmd);
		}
	}
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_DMS51V1) || defined(BOARD_ADA32V4))
	if (bTestBoardIpExist)
	{
		sprintf(szCmd, "cp %s %s", szTestBoardIpPath, pszDestPath);
		system_ex(szCmd, 10);

		sprintf(szTestBoardIpPath, "%s/ip.txt", pszDestPath);

		bTestBoardIpExist = SV_FALSE;

		if (update_IsPathExist(szTestBoardIpPath))
			bTestBoardIpExist = SV_TRUE;


		if (bTestBoardIpExist)
		{
			autoTestReadyWorkLog("copy TestBoardIp.txt success");
		}
		else
		{
			autoTestReadyWorkLog("copy TestBoardIp.txt failed");
			return SV_FAILURE;
		}
	}
	else
	{
		sprintf(szCmd, "TestBoardIp.txt not exist");
		autoTestReadyWorkLog(szCmd);
		return SV_FAILURE;
	}
#endif

	return SV_SUCCESS;
}

sint32 setSpecialVersion(char *pversion, char *pcustomer, char *pinfo, char *partNum, char *pspNum)
{
    sint32 s32Ret;

    if (!m_stUpdateInfo.bSetSVersion)
    {
        s32Ret = BOARD_SetSpecialVersion(pversion, pcustomer, pinfo, partNum, pspNum);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "BOARD_SetSpecialVersion fail! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        m_stUpdateInfo.bSetSVersion = SV_TRUE;
        //m_stUpdateInfo.bUpdateSource = SV_FALSE;
    }
    else
    {
        print_level(SV_INFO, "special version has been set, skip to set!\n");
    }

    s32Ret = CONFIG_ReloadFile();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_ReloadFile failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 resetSpecialVersion()
{
    sint32 s32Ret;
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
    s32Ret = BOARD_ResetSpecialVersion();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "BOARD_ResetSpecialVersion fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = CONFIG_ReloadFile();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_ReloadFile failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 复位userdata分区环境变量
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
sint32 flashWtdReset()
{
#if (defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1))
    sint32 s32Fd, s32Ret, s32Round, i, wr_len;
    sint32 s32MtdOffset = 0x20000;
    sint32 s32BlkOffset = 1024;
    char szBuf[2048];
    userdata_env stEnv;
    struct mtd_info_user stMtdInfo;
    struct erase_info_user stEraseInfo;
    char *pDev = "/dev/mtd3";

    s32Fd = open(pDev, O_RDWR, 0666);
    if (-1 == s32Fd)
    {
        print_level(SV_ERROR, "open %s failed:%d\n", pDev);
        return SV_FAILURE;
    }

    s32Ret = ioctl(s32Fd, MEMGETINFO, &stMtdInfo);  /* 获取MTD信息 */
    if ( 0 != s32Ret)
    {
        close(s32Fd);
        print_level(SV_ERROR, "ioctl MEMGETINFO failed!\n");
        return s32Ret;
    }

    lseek(s32Fd, s32MtdOffset, SEEK_SET);
    wr_len = stMtdInfo.writesize;
    s32Ret = read(s32Fd, szBuf, wr_len);
    if(s32Ret <= 0)
    {
        close(s32Fd);
        print_level(SV_ERROR, "read failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }


    memcpy(&stEnv, szBuf+s32BlkOffset, sizeof(stEnv));


    stEraseInfo.start = s32MtdOffset;
    stEraseInfo.length = stMtdInfo.erasesize;
    s32Ret = ioctl(s32Fd, MEMERASE, &stEraseInfo);  /* 擦除数据 */
    if (0 != s32Ret)
    {
        close(s32Fd);
        print_level(SV_ERROR, "ioctl MEMERASE failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    stEnv.repeat = 0;
    stEnv.checkSum = stEnv.repeat + 1;
    memcpy(szBuf+s32BlkOffset, &stEnv, sizeof(stEnv));

    lseek(s32Fd, s32MtdOffset, SEEK_SET);
    wr_len = stMtdInfo.writesize;
    s32Ret = write(s32Fd, szBuf, wr_len);
    if(s32Ret <= 0)
    {
        close(s32Fd);
        print_level(SV_ERROR, "read failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    close(s32Fd);

#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查userdata分区是否挂载上，如果没挂载上，擦除再重新挂载
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
sint32 autoupdate_checkUserdata()
{
    char szBuf[1024];
    char szCmd[2048];
    sint32 s32Ret = -1;

    s32Ret = SAFE_System_Recv("df -h |grep userdata", szBuf, 1024);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: df -h |grep userdata failed.\n");
        return SV_FAILURE;
    }

    if (0 == strlen(szBuf) || NULL == strstr(szBuf, "/userdata"))
    {
        print_level(SV_INFO, "not found /userdata, reset /userdata now...\n");

        s32Ret = SAFE_System("ubidetach -m 3", 5000);
        s32Ret |= SAFE_System("flash_erase /dev/mtd3 0 0", 5000);
        s32Ret |= SAFE_System("nandwrite -p /dev/mtd3 /root/userdata.ubi", 5000);
        s32Ret |= SAFE_System("ubiattach /dev/ubi_ctrl -m 3", 5000);

        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "reset /userdata failed.\n");
            return SV_FAILURE;
        }
    }

    print_level(SV_INFO, "found /userdata now\n");

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 匹配合法日期位置
 * 输入参数: input 输入的字符串
 * 输出参数: ppstart 匹配的日期起始位置指针
             ppend   匹配的日期结束位置指针
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
sint32 findDatePosition(const char *input, char **ppstart, char **ppend)
{
    sint32 s32Ret;
    regex_t regex;
    const char *pattern = "([0-9]{4})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])";  //日期字符匹配

    regmatch_t match[1];

    s32Ret = regcomp(&regex, pattern, REG_EXTENDED);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Could not compile regex\n");
        return s32Ret;
    }

    s32Ret = regexec(&regex, input, 1, match, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Regex match failed\n");
        regfree(&regex);
        return s32Ret;
    }

    if (ppstart) *ppstart = input + (int)match[0].rm_so;
    if (ppend) *ppend = input + (int)match[0].rm_eo;


    regfree(&regex);

    return SV_SUCCESS;
}

sint32 checkSpecialVersion(char *pktName)
{
    sint32 s32Ret = SV_SUCCESS, i = 0;
    char szCmd[64] = {0};
    char szPath[64] = {0};
    char szPathFixed[64] = {0};
    char szBoardS[128] = {0};
    char szCustomerS[128] = {0};
    char szArticleNum[128] = {0};
    char szSpSerialNum[128] = {0};
    char *pktTailName;

    sprintf(szPath, "%s/specialVersion", UPDATE_MOUNT_PATH);
    sprintf(szPathFixed, "%s/specialVersion_fixed", UPDATE_MOUNT_PATH);
    if (update_IsPathExist(szPath) || update_IsPathExist(szPathFixed))
    {
        print_level(SV_INFO, "found specialVersion file, set specialVersion now.\n");
        if (update_IsPathExist(szPath))
            sprintf(szCmd, "mv %s %s", szPath, BOARD_SPECIAL_VERSION_PATH);
        if (update_IsPathExist(szPathFixed))
            sprintf(szCmd, "cp %s %s", szPathFixed, BOARD_SPECIAL_VERSION_PATH);
        system_ex(szCmd, 10);

        s32Ret = BOARD_GetSpecialVersion(szBoardS, szCustomerS, NULL, szArticleNum, szSpSerialNum);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "BOARD_GetSpecialVersion fail! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = setSpecialVersion(szBoardS, szCustomerS, "", szArticleNum, szSpSerialNum);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "setSpecialVersion fail! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
    else
    {
        if (NULL == pktName)
        {
            return SV_SUCCESS;
        }

        //从日期号之后开始识别,防止专用版本升级包名字冲突,日期号和客户号冲突
        s32Ret = findDatePosition(pktName, NULL, &pktTailName);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "findDatePosition %s failed", pktName);
            return SV_SUCCESS;
        }

#if defined(BOARD_DMS31V2)
        SV_BOOL bResetSVersion = SV_FALSE;
        char szSversion[128] = {0};
        char *pversion = BOARD_GetSVersion();
        char *pcustomer = BOARD_GetSCustomer();
        print_level(SV_INFO, "pversion=%s, pcustomer=%s\n", pversion, pcustomer);

        for (i = 0; i < BOARD_DMS31V2_SV_NUM; i++)
        {
            if (NULL != strstr(pktTailName, m_dms31v2SVersionInfo[i].pszCustomer))
            {
                pcustomer = m_dms31v2SVersionInfo[i].pszCustomer;
                bResetSVersion = SV_TRUE;
                print_level(SV_INFO, "packet name with customer: %s, set customer version now.\n", pcustomer);
                break;
            }
        }

        for (i = 0; i < m_s32Dms31v2HardwareNum; i++)
        {
            if(NULL != strstr(pktTailName, m_dms31v2HardwareInfo[i]))
            {
                pversion = m_dms31v2HardwareInfo[i];
                bResetSVersion = SV_TRUE;
                print_level(SV_INFO, "packet name with specialVersion: %s, set special version now.\n", pversion);
                break;
            }
        }

        if (bResetSVersion)
        {
            s32Ret = setSpecialVersion(pversion, pcustomer, "", "", "");
        }
#elif defined(BOARD_ADA46V1)
        SV_BOOL bResetSVersion = SV_FALSE;
        char szSversion[128] = {0};
        char *pversion = BOARD_GetSVersion();
        char *pcustomer = BOARD_GetSCustomer();
        print_level(SV_INFO, "%s %s\n", pversion, pcustomer);

        for (i = 0; i < BOARD_ADA46V1_SV_NUM; i++)
        {
            if (NULL != strstr(pktTailName, m_ada46v1SVersionInfo[i].pszCustomer))
            {
                pcustomer = m_ada46v1SVersionInfo[i].pszCustomer;
                bResetSVersion = SV_TRUE;
                print_level(SV_INFO, "packet name with customer: %s, set customer version now.\n", pcustomer);
                break;
            }
        }

        for (i = 0; i < m_s32Ada46v1HardwareNum; i++)
        {
            if(NULL != strstr(pktTailName, m_ada46v1HardwareInfo[i]))
            {
                sprintf(szSversion, "AICB046-%s", m_ada46v1HardwareInfo[i]);
                pversion = szSversion;
                bResetSVersion = SV_TRUE;
                print_level(SV_INFO, "packet name with specialVersion: %s, set special version: %s now.\n", m_ada46v1HardwareInfo[i], pversion);
                break;
            }
        }

        if (bResetSVersion)
            s32Ret = setSpecialVersion(pversion, pcustomer, "", "", "");
#elif (defined(BOARD_ADA32V2) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32V4))
        SV_BOOL bResetSVersion = SV_FALSE;
        char szSversion[128] = {0};
        char *pversion = BOARD_GetSVersion();
        char *pcustomer = BOARD_GetSCustomer();
        print_level(SV_INFO, "pversion=%s, pcustomer=%s\n", pversion, pcustomer);

        // 专门区分ADA32P平台, 只有pcustomer客户号和pversion硬件信息同时拥有时，才进行覆盖操作
        if (NULL != strstr(pversion, "ADA32P"))
        {
            // print_level(SV_ERROR, "\n\n\n 1-checkSpecialVersion checkSpecialVersion checkSpecialVersion................................. \n\n\n");
            // 同时存在客户号, 假定客户号 == 100002
            if (NULL != strstr(pcustomer, "100002"))
            {
                // 按照专用版本执行
                for (i = 0; i < BOARD_ADA32V2_SV_NUM; i++)
                {
                    if (NULL != strstr(pktTailName, m_ada32v2SVersionInfo[i].pszCustomer))
                    {
                        pcustomer = m_ada32v2SVersionInfo[i].pszCustomer;
                        bResetSVersion = SV_TRUE;
                        print_level(SV_INFO, "packet name with customer: %s, set customer version now.\n", pcustomer);
                        break;
                    }else{
                        bResetSVersion = SV_FALSE;
                    }
                }

                for (i = 0; i < m_s32Ada32v2HardwareNum; i++)
                {
                    if(NULL != strstr(pktTailName, m_ada32v2HardwareInfo[i]))
                    {
                        pversion = m_ada32v2HardwareInfo[i];
                        bResetSVersion = SV_TRUE;
                        print_level(SV_INFO, "packet name with specialVersion: %s, set special version now.\n", pversion);
                        break;
                    }else{
                        bResetSVersion = SV_FALSE;
                    }
                }
            }else
            {
                // 缺少客户号
                // print_level(SV_ERROR, "\n\n\n 2-checkSpecialVersion checkSpecialVersion checkSpecialVersion................................. \n\n\n");
                bResetSVersion = SV_FALSE;
            }
        }
        else
        {
            for (i = 0; i < BOARD_ADA32V2_SV_NUM; i++)
            {
                if (NULL != strstr(pktTailName, m_ada32v2SVersionInfo[i].pszCustomer))
                {
                    pcustomer = m_ada32v2SVersionInfo[i].pszCustomer;
                    bResetSVersion = SV_TRUE;
                    print_level(SV_INFO, "packet name with customer: %s, set customer version now.\n", pcustomer);
                    break;
                }
            }

            for (i = 0; i < m_s32Ada32v2HardwareNum; i++)
            {
                if(NULL != strstr(pktTailName, m_ada32v2HardwareInfo[i]))
                {
                    pversion = m_ada32v2HardwareInfo[i];
                    bResetSVersion = SV_TRUE;
                    print_level(SV_INFO, "packet name with specialVersion: %s, set special version now.\n", pversion);
                    break;
                }
            }
        }

        if (bResetSVersion){
            s32Ret = setSpecialVersion(pversion, pcustomer, "", "", "");
        }

#elif (defined(BOARD_ADA32V3))
        SV_BOOL bResetSVersion = SV_FALSE;
        char *pversion = BOARD_GetSVersion();
        char *pcustomer = BOARD_GetSCustomer();
        print_level(SV_INFO, "%s %s\n", pversion, pcustomer);
        for (i = 0; i < BOARD_ADA32V3_SV_NUM; i++)
        {
            if (NULL != strstr(pktTailName, m_ada32v3SVersionInfo[i].pszCustomer))
            {
                pcustomer = m_ada32v3SVersionInfo[i].pszCustomer;
                bResetSVersion = SV_TRUE;
                print_level(SV_INFO, "packet name with customer: %s, set customer version now.\n", pcustomer);
                break;
            }
        }

        /* 1106的镜头和ADA32大致是一样的，这里直接使用ADA32的列表去判断 */
        for (i = 0; i < m_s32Ada32v2HardwareNum; i++)
        {
            if(NULL != strstr(pktTailName, m_ada32v2HardwareInfo[i]))
            {
                pversion = m_ada32v2HardwareInfo[i];
                bResetSVersion = SV_TRUE;
                print_level(SV_INFO, "packet name with specialVersion: %s, set special version now.\n", pversion);
                break;
            }
        }

        if(bResetSVersion)
            s32Ret = setSpecialVersion(pversion, pcustomer, "", "", "");
#elif (defined(BOARD_ADA32IR))
        SV_BOOL bResetSVersion = SV_FALSE;
        char *pversion = BOARD_GetSVersion();
        char *pcustomer = BOARD_GetSCustomer();
        for (i = 0; i < BOARD_ADA32IR_SV_NUM; i++)
        {
            if (NULL != strstr(pktTailName, m_ada32IrSVersionInfo[i].pszCustomer))
            {
                pversion = m_ada32IrSVersionInfo[i].pszHardware;
                pcustomer = m_ada32IrSVersionInfo[i].pszCustomer;
                bResetSVersion = SV_TRUE;
                print_level(SV_INFO, "packet name with customer: %s, set customer version now.\n", pcustomer);
                break;
            }
        }

        if(bResetSVersion)
            s32Ret = setSpecialVersion(pversion, pcustomer, "", "", "");
#endif
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "setSpecialVersion fail! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    return SV_SUCCESS;
}

sint32 checkConfigStatus()
{
     char szCmd[64] = {0};
    char szPath[64] = {0};
    char szPathFixed[64] = {0};
    char szPathCover[64] = {0};
    char szRealPath[64] = {0};
    SV_BOOL bConfigExist = SV_FALSE;
    SV_BOOL bConfigFixExist = SV_FALSE;

    sprintf(szPathCover, "%s/reset_configuration", UPDATE_MOUNT_PATH);
    if (update_IsPathExist(szPathCover))
    {
        print_level(SV_INFO, "found file: %s, need to restore factory now\n", szPathCover);
        sprintf(szCmd, "rm -rf %s %s %s", CONFIG_XML, CONFIG_BAK1, CONFIG_BAK2);
        if(0 != system_ex(szCmd, 60))
        {
            print_level(SV_WARN, "rm config xml failed.\n");
        }

        if (!BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
        {
            remove(szPathCover);
        }
    }

    sprintf(szPath, "%s/config.tar.gz", UPDATE_MOUNT_PATH);
    sprintf(szPathFixed, "%s/config_fixed.tar.gz", UPDATE_MOUNT_PATH);
    if (update_IsPathExist(szPath))
        bConfigExist = SV_TRUE;
    if (update_IsPathExist(szPathFixed))
        bConfigFixExist = SV_TRUE;

    if (bConfigExist || bConfigFixExist)
    {
        print_level(SV_INFO, "found config file, tar to tmp path.\n");
        if (bConfigExist)
            sprintf(szCmd, "tar -xzvf %s -C /tmp/", szPath);
        if (bConfigFixExist)
            sprintf(szCmd, "tar -xzvf %s -C /tmp/", szPathFixed);
        system_ex(szCmd, 10);

        strcpy(szRealPath, "/tmp/config.json");
        if (!update_IsPathExist(szRealPath))
        {
            print_level(SV_INFO, "%s is not exist, maybe tar failed\n", szRealPath);
            goto exit;
        }

        if (bConfigExist)
        {
            remove(szPath);
        }
    }
    else
    {
        print_level(SV_INFO, "not found import config file.\n");
    }
    return SV_SUCCESS;

exit:
    return SV_FAILURE;
}

sint32 doExtraWork()
{
	sint32 s32Ret = 0;
	char szCmd[128] = {0};
	MCUUPDATE_INFO_S stMcuUpdateInfo = {0};

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
	if (update_IsPathExist(AUTOTEST_SCRIPTS))
	{
		snprintf(szCmd, 128, "cp %s /tmp", AUTOTEST_SCRIPTS);
		system_ex(szCmd, 10);
		m_stUpdateInfo.bAutoTestMode = SV_TRUE;
	}

#if (defined(BOARD_DMS31V2) || defined(DMS31SDK) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1) ||defined(BOARD_DMS51V1) || defined(BOARD_ADA32V4))
    stMcuUpdateInfo.bRetryFoundMcu = m_stUpdateInfo.bRetryFoundMcu;
    stMcuUpdateInfo.bLastBlock = SV_TRUE;
    stMcuUpdateInfo.bTestMode = SV_FALSE;
    stMcuUpdateInfo.pszPath = NULL;
	s32Ret = UPDATEMCU_A32(&stMcuUpdateInfo);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "update a32 mcu failed.\n");
	}
update_mcu_exit:;
#endif
#endif

	return SV_SUCCESS;
}

sint32 doStorageExtraWork()
{
	sint32 s32Ret = 0;
	char szCmd[128] = {0};

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
    s32Ret = checkSpecialVersion(NULL);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "checkSpecialVersion failed.\n");
    }

    s32Ret = checkConfigStatus();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "checkConfigStatus failed.\n");
    }
#endif

	return SV_SUCCESS;
}

sint64 autoUpdate_GetTimeTickMs()
{
	struct timespec time = {0, 0};

	clock_gettime(CLOCK_MONOTONIC, &time);
	return ((sint64)time.tv_sec) * 1000 + time.tv_nsec/1000000;
}


/******************************************************************************
 * 函数功能: 等待所有SD卡设备完成加载
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 sdWaitForReady(sint32 *s32StorageNum)
{
    sint32 s32Ret = 0, s32RetryCnt = 20, s32BlockNum = 0, i, j, s32Index = 0;
    SV_BOOL bFoundSd = SV_FALSE, bFoundSda = SV_FALSE;
    SV_BOOL bHasUdisk = SV_FALSE;
    char szStorageMessage[64] = {0};
#if defined(BOARD_DMS31V2)
    sint32 s32DevNum = 2;
    STORAGE_STATUS_S stStorageStatus[2] = {{"/dev/mmcblk2", SV_FALSE}, {"/dev/sda1", SV_FALSE}};
#else
    sint32 s32DevNum = 5;
    STORAGE_STATUS_S stStorageStatus[5] = {{"/dev/mmcblk0", SV_FALSE}, {"/dev/mmcblk1", SV_FALSE}, {"/dev/mmcblk2", SV_FALSE}, \
                                            {"/dev/mmcblk3", SV_FALSE}, {"/dev/sda1", SV_FALSE}};
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32V4))
    s32Ret = SAFE_System_Recv("cat /sys/kernel/debug/usb/devices | grep \"Driver=usb-storage\"", szStorageMessage, 64);
    if (strlen(szStorageMessage) == 0)
    {
        print_level(SV_WARN, "Cannot find sd device\n");
        return SV_FAILURE;
    }
#elif (defined(BOARD_ADA32V3) || defined(BOARD_DMS51V1))
    for (i = 0; i < 30; i++)
    {
        sint64 s64Last1 = autoUpdate_GetTimeTickMs();
        s32Ret = SAFE_System_Recv("cat /sys/kernel/debug/usb/devices | grep \"Driver=usb-storage\"", szStorageMessage, 64);
        if (strlen(szStorageMessage) == 0)
        {
            continue;
        }
        else
        {
            break;
        }

    }

    if (i >= 30)
    {
        print_level(SV_WARN, "Cannot find sd device\n");
        return SV_FAILURE;
    }
#endif

    print_level(SV_INFO, "wait for all sd device ready...\n");
    for (i = 0; i < s32RetryCnt; i++)
    {
/* 使用TF卡或U盘升级的设备 */
#if (!defined(BOARD_ADA32V2) && !defined(BOARD_ADA32V3) && !defined(BOARD_ADA32IR) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA900V1) && !defined(BOARD_ADA32E1) && !defined(BOARD_DMS51V1) && !defined(BOARD_ADA32V4))
        for (j = s32Index; j < s32DevNum; j++)
        {
            if (update_IsPathExist(stStorageStatus[j].szDevice) && !stStorageStatus[j].bFound)
            {
				s32BlockNum++;
				stStorageStatus[j].bFound = SV_TRUE;

                if (NULL != strstr(stStorageStatus[j].szDevice, "sda"))
                {
                    print_level(SV_INFO, "%s is ready.\n", stStorageStatus[j].szDevice);
                    bFoundSda = SV_TRUE;
                }
                else
                {
                    print_level(SV_INFO, "%s is ready.\n", stStorageStatus[j].szDevice);
                    bFoundSd = SV_TRUE;
                    break;
                }
            }
		}

/* DMS31优先使用TF卡升级 */
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
        if (bFoundSd)
        {
            m_stUpdateInfo.bRetryFoundMcu = SV_TRUE;
            break;
        }

        if (bFoundSda)
        {
	        print_level(SV_INFO, "no sd but usb storage is ready.\n");
            break;
        }

        if (i >= 2 && i % 2 == 0 && !bHasUdisk)
        {
            memset(szStorageMessage, 0, sizeof(szStorageMessage));
            s32Ret = SAFE_System_Recv("cat /sys/kernel/debug/usb/devices | grep \"Driver=usb-storage\"", szStorageMessage, 64);
            if (strlen(szStorageMessage) != 0)
            {
                print_level(SV_INFO, "found usb storage device, find sda1 device now...\n");
                s32Index = 1;
                s32RetryCnt += 15;
                bHasUdisk = SV_TRUE;
            }
        }
#else
        if (bFoundSd && bFoundSda)
	    {
	        print_level(SV_INFO, "all sd device is ready.\n");
	        break;
	    }
#endif
		sleep_ms(100);


/* 只使用U盘升级的设备 */
#else
		if (update_IsPathExist(stStorageStatus[4].szDevice) && !stStorageStatus[4].bFound)
		{
			s32BlockNum++;
			stStorageStatus[4].bFound = SV_TRUE;
			print_level(SV_INFO, "%s is ready.\n", stStorageStatus[4].szDevice);
            bFoundSda = SV_TRUE;
			break;
		}
		sleep_ms(150);
#endif

		print_level(SV_DEBUG, "wait for sd devices elapse %d times.\n", i+1);
	}

	*s32StorageNum = s32BlockNum;
    if (i >= s32RetryCnt && !bFoundSd && !bFoundSda)
    {
        print_level(SV_WARN, "wait for sd devices timeout.\n");
		return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 判断存储设备是否存在 mounted 标志文件
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  :  SV_TRUE - 存在
             SV_FALSE - 不存在
 *****************************************************************************/
static sint32 catMountStatus()
{
	char szProcMount[4096];
    char szMountedFile[64];
    char szSearchString[64];
    const char *pszMountPath = NULL;
	char *mountInfo = NULL;

	if (SAFE_CAT("/proc/mounts", szProcMount, 4096)!=0)
	{
		print_level(SV_ERROR,"could not open /proc/mounts\n");
		return UPDATE_MOUNT_NOMOUNT;
	}

	pszMountPath = UPDATE_MOUNT_PATH;
	strcpy(szSearchString, pszMountPath);
	mountInfo = strstr(szProcMount, szSearchString);
	if (mountInfo != NULL)
	{
		if (strstr(mountInfo, "rw,relatime") != NULL)
		   		return UPDATE_MOUNT_SUCCESS;
		else
			return UPDATE_MOUNT_EXCEPT;
	}
	else
	{
		return UPDATE_MOUNT_NOMOUNT;
	}

    return UPDATE_MOUNT_NOMOUNT;
}

/******************************************************************************
 * 函数功能: 检查是否有SD卡存在并从中搜索升级包
 * 输入参数: s32BlockNum --- 存储设备数量
 * 输出参数: pszPacketPath --- 寻找到的升级包路径
 * 返回值  : SV_SUCCESS - 升级包存在
             SV_FAILURE - 升级包不存在
 * 注意    : 无
 *****************************************************************************/
static sint32 checkSdcardFindPkt(sint32 s32BlockNum, char* pszPacketPath)
{
	sint32 s32Ret = 0, i, j;
    sint32 s32Fd;
    sint32 s32RetryCnt = 10, s32ErrCnt = 0, s32SdBlockNum = s32BlockNum;
#if defined(BOARD_DMS31V2)
    char *pszPatternDevs[2] = {"mmcblk[2-9]", "sda[0-1]"};
#else
    char *pszPatternDevs[2] = {"mmcblk[0-9]", "sda[0-1]"};
#endif
    char *pszPatternDevTails[2] = {"p1", NULL};
    char *pszPatternDevTail;
	const char *pszPatternPacket = PKT_NAME_PREFIX".*";
	char szCmd[128];
	char szDevPath[64];
	char szPartionPath[64];
	char szMcuPktPath[128] = {0};
	DIR *pDirDev = NULL;
    struct dirent *pstDirent = NULL;
    char szLastMountedPath[24] = {0};
    MCUUPDATE_INFO_S stMcuUpdateInfo = {0};

	sprintf(szCmd, "mkdir -p %s", UPDATE_MOUNT_PATH);
	system_ex(szCmd, 10);
	sprintf(szCmd, "mkdir -p %s", UPDATE_MOUNT_ACTUALPATH);
	system_ex(szCmd, 10);
	sync();
    pDirDev = opendir("/dev");
    if(NULL == pDirDev)
    {
        print_level(SV_ERROR, "opendir /dev failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    while (1)
    {
        pstDirent = readdir(pDirDev);
        if(NULL == pstDirent)
        {
            print_level(SV_INFO, "can not find any update packet.\n");
            break;
        }

        /* 查找分区设备 */
        for (i = 0; i < 2; i++)
        {
            //s32Ret = fnmatch(pszPatternDevs[i], pstDirent->d_name, FNM_PATHNAME|FNM_PERIOD);
            s32Ret = matchRegex(pszPatternDevs[i], pstDirent->d_name);
            if (0 == s32Ret)
            {
                print_level(SV_DEBUG, "match file: %s\n", pstDirent->d_name);
                pszPatternDevTail = pszPatternDevTails[i];
                break;
            }
        }

        if (0 != s32Ret)
        {
            print_level(SV_DEBUG, "unmatch file: %s\n", pstDirent->d_name);
            continue;
        }

        sprintf(szDevPath, "/dev/%s", pstDirent->d_name);
        print_level(SV_INFO, "try to mount device: %s\n", szDevPath);
        s32Fd = open(szDevPath, O_RDWR);
        if(s32Fd < 0)
        {
            print_level(SV_WARN, "open device: %s failed. [err: %s]\n", szDevPath, strerror(errno));
            continue;
        }
        close(s32Fd);

        if (pszPatternDevTail && NULL == strstr(szDevPath, pszPatternDevTails[0]))
        {
            sprintf(szPartionPath, "%s%s", szDevPath, pszPatternDevTail);
        }
        else
        {
            strcpy(szPartionPath, szDevPath);
        }

        if (0 != strlen(szLastMountedPath))
        {
            if (0 == strcmp(szLastMountedPath, szPartionPath))
            {
                print_level(SV_WARN, "szPartionPath: %s has mounted already!\n", szLastMountedPath, szPartionPath);
                continue;
            }
        }
        memset(szLastMountedPath, 0, sizeof(szLastMountedPath));
        strncpy(szLastMountedPath, szPartionPath, 24);

        /* 挂载分区FAT文件系统 */
        print_level(SV_INFO, "try mount %s on %s ...\n", szPartionPath, UPDATE_MOUNT_PATH);
        for (i = 0; i < 5; i++)
        {
    		for (j = 0; j < s32RetryCnt; j++)
    		{
    			s32Ret = mount(szPartionPath, UPDATE_MOUNT_PATH, "vfat", 0, NULL);
    			if(0 != s32Ret)
    			{
    				perror("mount error");
    				print_level(SV_WARN, "mount %s on %s failed, keep wait...\n", szPartionPath, UPDATE_MOUNT_PATH);
    				umount(UPDATE_MOUNT_PATH);
    				sleep_ms(10);
    			}
    			else
    			{
    				print_level(SV_INFO, "mount %s on %s success\n", szPartionPath, UPDATE_MOUNT_PATH);
    				break;
    			}
    		}
    		if (j >= s32RetryCnt)
    		{
    		    print_level(SV_WARN, "mount %s failed.\n", szPartionPath);
    			continue;
    		}

    		print_level(SV_INFO, "check mount %s on %s ...\n", szPartionPath, UPDATE_MOUNT_PATH);
    		s32Ret = catMountStatus();
    		if (UPDATE_MOUNT_SUCCESS != s32Ret)
    	    {
    	        print_level(SV_WARN, "mount %s failed in device: %s. [mount status: %d]\n", UPDATE_MOUNT_PATH, szPartionPath, s32Ret);
    			s32Ret = umount(UPDATE_MOUNT_PATH);
    			if (0 != s32Ret)
    			{
    				print_level(SV_ERROR, "umount %s failed. [err: %s]\n", UPDATE_MOUNT_PATH, strerror(errno));
    				sprintf(szCmd, "umount -l %s", UPDATE_MOUNT_PATH);
    				system_ex(szCmd, 10);
    			}
    	    }
    		else
    		{
    			break;
    		}
		}
		if (i >= 5)
		{
			print_level(SV_ERROR, "try mount %s on %s failed more than 5 times\n", szPartionPath, UPDATE_MOUNT_PATH);
			s32SdBlockNum--;
			continue;
		}

		s32SdBlockNum--;
        print_level(SV_INFO, "try find packet at %s ...\n", UPDATE_MOUNT_PATH);
		s32Ret = pktFindUpdatePacket(UPDATE_MOUNT_PATH, pszPacketPath);
	    if (SV_SUCCESS != s32Ret)
	    {
	        print_level(SV_WARN, "not found packet at %s\n", UPDATE_MOUNT_PATH);
            doStorageExtraWork();

			//找不到升级包时，顺便检查SD卡，拷贝DMS31自动化测试文件或升级ADA32的MCU
			if (s32SdBlockNum == 0)
			{
				doExtraWork();
			}
			else
			{
#if (defined(BOARD_DMS31V2) || defined(DMS31SDK) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1)  || defined(BOARD_ADA32V4))
                stMcuUpdateInfo.bRetryFoundMcu = m_stUpdateInfo.bRetryFoundMcu;
                stMcuUpdateInfo.bLastBlock = SV_FALSE;
                stMcuUpdateInfo.bTestMode = SV_FALSE;
                stMcuUpdateInfo.pszPath = NULL;
            	s32Ret = UPDATEMCU_A32(&stMcuUpdateInfo);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "update a32 mcu failed.\n");
                }
update_mcu_exit:;
#endif
			}

	        s32Ret = umount(UPDATE_MOUNT_PATH);
			if (0 != s32Ret)
			{
				print_level(SV_ERROR, "umount %s failed. [err: %s]\n", UPDATE_MOUNT_PATH, strerror(errno));
				sprintf(szCmd, "umount -l %s", UPDATE_MOUNT_PATH);
				system_ex(szCmd, 10);
			}

            if (0 == s32SdBlockNum)
            {
                print_level(SV_INFO, "no another stotage device, break!\n");
                break;
            }
	        continue;
	    }
	    else
	    {
	        print_level(SV_INFO, "found packet: %s\n", pszPacketPath);
	        m_stUpdateInfo.bUpdateSDWritable = SV_TRUE;
	        closedir(pDirDev);

	        return SV_SUCCESS;
	    }
    }
	closedir(pDirDev);

	return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 检查can升级目录中是否有升级包
 * 输入参数: 无
 * 输出参数: pszPacketPath --- 寻找到的升级包路径
 * 返回值  : SV_SUCCESS - 升级包存在
             SV_FAILURE - 升级包不存在
 * 注意    : 无
 *****************************************************************************/
static sint32 checkCanFindPkt(char* pszPacketPath)
{
    sint32 s32Ret;
    char *tmpPktPath = NULL;
    char szCmd[128] = {0};
	char szPacketPath[128] = {0};
    char szLogPath[128] = {0};

    snprintf(szCmd, 128, "ls %s%s*", CAN_UPGRADE_ENDPAC_PATH, PKT_NAME_PREFIX);
    s32Ret = system_ex_recv(szCmd, szPacketPath, 128);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "system_ex_recv %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    tmpPktPath = strtok(szPacketPath, "\n");
    if (tmpPktPath != NULL)
    {
        if (update_IsPathExist(tmpPktPath))
        {
            strncpy(pszPacketPath, tmpPktPath, 128);
            print_level(SV_INFO, "found can upgrade packet: %s\n", pszPacketPath);

            snprintf(szLogPath, 128, "%s/%s", CAN_UPGRADE_ENDPAC_PATH, UPDATE_LOG_NAME);
			if (update_IsPathExist(szLogPath))
			{
				snprintf(szCmd, 128, "rm %s", szLogPath);
				system_ex(szCmd, 10);
			}

            snprintf(szLogPath, 128, "%s/%s", CAN_UPGRADE_PATH, CAN_UPGRADE_PACKET_INFO_FILE);
			if (update_IsPathExist(szLogPath))
			{
				snprintf(szCmd, 128, "rm %s", szLogPath);
				system_ex(szCmd, 10);
			}
            return SV_SUCCESS;
        }
    }

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 比较两个文件是否有差异
 * 输入参数: pstFile1 --- 文件1
             pstFile2 --- 文件2
 * 输出参数: 无
 * 返回值  : SV_TRUE - 有差异
             SV_FALSE - 无差异
 * 注意    : 无
 *****************************************************************************/
SV_BOOL updateCmpDifferent(char *pstFile1, char *pstFile2)
{
    sint32 s32Ret = 0;
    SV_BOOL bDiff = SV_FALSE;
    sint32 s32Fd1 = -1, s32Fd2 = -1;
    sint32 s32Size1, s32Size2;
    uint8 au8Buf1[4096];
    uint8 au8Buf2[4096];

    if (NULL == pstFile1 || NULL == pstFile2)
    {
        return SV_TRUE;
    }

    s32Fd1 = open(pstFile1, O_RDONLY);
    if (s32Fd1 < 0)
    {
        //print_level(SV_ERROR, "open file: %s failed.\n", pstFile1);
        return SV_TRUE;
    }

    s32Fd2 = open(pstFile2, O_RDONLY);
    if (s32Fd2 < 0)
    {
        //print_level(SV_ERROR, "open file: %s failed.\n", pstFile2);
        close(s32Fd1);
        return SV_TRUE;
    }
	memset(au8Buf1,0,4096);
	memset(au8Buf2,0,4096);
    while (1)
    {
        s32Size1 = read(s32Fd1, au8Buf1, 4096);
        s32Size2 = read(s32Fd2, au8Buf2, 4096);
        if (s32Size1 <= 0 || s32Size2 <= 0 || s32Size1 != s32Size2)
        {
            if (s32Size1 != s32Size2)
                bDiff = SV_TRUE;
            break;
        }

        if (0 != memcmp(au8Buf1, au8Buf2, s32Size1))
        {
            bDiff = SV_TRUE;
            break;
        }
    }

    close(s32Fd1);
    close(s32Fd2);

    return bDiff;
}

/******************************************************************************
 * 函数功能: 创建路径(默认创建中间路径)
 * 输入参数: pszPath --- 路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 updateMkdirp(char *pszPath)
{
    sint32 s32Ret = 0, i;
    uint32 u32Count = 0;
    char *aszItem[16] = {NULL};
    char szDir[256];
    char szPath[256];
    struct stat stStat;

    if (NULL == pszPath || pszPath[0] != '/')
    {
        return ERR_NULL_PTR;
    }

    strcpy(szDir, pszPath);

    aszItem[u32Count] = strtok(szDir, "/");
    while (NULL != aszItem[u32Count])
    {
        u32Count++;
        if (u32Count >= 16)
        {
            break;
        }

        aszItem[u32Count] = strtok(NULL, "/");
    }

    memset(szPath, 0, sizeof(szPath));
    for (i = 0; i < u32Count; i++)
    {
        strcat(szPath, "/");
        strcat(szPath, aszItem[i]);
        s32Ret = stat(szPath, &stStat);
        if (0 != s32Ret)
        {
            print_level(SV_INFO, "mkdir %s\n", szPath);
            mkdir(szPath, 0755);
        }
        else if (!S_ISDIR(stStat.st_mode))
        {
            print_level(SV_ERROR, "normal file: %s exist.\n", szPath);
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

static inline void cutLineBreak(char *pszStr)
{
    char *pcTmp = NULL;

    pcTmp = strchr(pszStr, '\r');
	if(NULL == pcTmp)
		pcTmp = strchr(pszStr, '\n');
	if(NULL != pcTmp)
		*pcTmp = '\0';
}

sint32 getFileMd5(const char *pszFilePath, char *pszMd5)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    char szCmd[128];
    char *pszTmp = NULL;

    if (NULL == pszFilePath || NULL == pszMd5)
    {
        return SV_FAILURE;
    }

    sprintf(szCmd, "md5sum %s > %s", pszFilePath, CMD_OUTPUT_FILE);
    s32Ret = system_ex(szCmd, 60);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    s32Fd = open(CMD_OUTPUT_FILE, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", CMD_OUTPUT_FILE, strerror(errno));
        return SV_FAILURE;
    }
    read(s32Fd, pszMd5, 32);
    pszMd5[32] = '\0';
    close(s32Fd);

    return SV_SUCCESS;
}

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_SSC335))
/* 最多可同时保存最近获取的2个变量指针 */
char *fw_getenv(char *name)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    char szCmd[128];
    char *pszTmp = NULL;
    static char szValue[2][256];
    static uint32 u32Idx = 0;

    if (NULL == name)
    {
        return NULL;
    }

    sprintf(szCmd, "fw_printenv %s > %s", name, CMD_OUTPUT_FILE);
    s32Ret = system_ex(szCmd, 60);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return NULL;
    }

    s32Fd = open(CMD_OUTPUT_FILE, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", CMD_OUTPUT_FILE, strerror(errno));
        return NULL;
    }

    memset(szValue[u32Idx%2], 0x0, 256);
    read(s32Fd, szValue[u32Idx%2], 255);
    close(s32Fd);
    if (0 != strncmp(szValue[u32Idx%2], name, strlen(name)))
    {
        return NULL;
    }

    pszTmp = &szValue[u32Idx%2][strlen(name)+1];
    cutLineBreak(pszTmp);
    u32Idx++;

    return pszTmp;
}
#endif


/******************************************************************************
 * 函数功能: 通过正则表达式匹配字符串
 * 输入参数: pattern --- 正则表达式
 * 输出参数: userString --- 字符串
 * 返回值  : SV_SUCCESS - 匹配成功
             SV_FAILURE - 匹配失败
 * 注意    : 无
 *****************************************************************************/
sint32 matchRegex(const char* pattern, const char* userString)
{
    sint32 s32Ret = SV_FAILURE;
    regex_t regex;
    int regexInit = regcomp( &regex, pattern, REG_EXTENDED);
    if( regexInit )
    {
        //Error print : Compile regex failed
    }
    else
    {
        int reti = regexec(&regex, userString, 0, NULL, 0);
        if( REG_NOERROR != reti )
        {
            //Error print: match failed!
        }
        else
        {
            s32Ret = SV_SUCCESS;
        }
    }
    regfree( &regex );
    return s32Ret;
}

/******************************************************************************
 * 函数功能: 清空指定文件
 * 输入参数: pszBootPath --- 路径
 * 输出参数: pszPreFile ---  筛选文件
 * 返回值  : SV_SUCCESS - 找到升级包
             SV_FAILURE - 无升级包
 * 注意    : 无
 *****************************************************************************/
sint32 clearPreFile(const char *pszBootPath, char *pszPreFile)
{
    sint32 s32Ret = 0, i;
    DIR *pDir = NULL;
    struct dirent *pstDirent = NULL;
    char szSelectPattern[128];
    char szSelectFile[128];
    char szCmd[128] = {0};

    pDir = opendir(pszBootPath);
    if (NULL == pDir)
    {
        print_level(SV_ERROR, "opendir /boot failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    sprintf(szSelectPattern, "%s*", pszPreFile);
    while(1)
    {
        pstDirent = readdir(pDir);
        if (NULL == pstDirent)
        {
            break;
        }

        s32Ret = matchRegex(szSelectPattern, pstDirent->d_name);
        if(SV_SUCCESS != s32Ret)
        {
            continue;
        }

        sprintf(szCmd, "rm %s/%s", pszBootPath, pstDirent->d_name);
        SAFE_System(szCmd, 1000);   // 删除指定文件
    }

    closedir(pDir);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 在指定目录中查找升级包
 * 输入参数: pszBootPath --- 指定寻找升级包的目录
 * 输出参数: pszPacketPath --- 升级包路径
 * 返回值  : SV_SUCCESS - 找到升级包
             SV_FAILURE - 无升级包
 * 注意    : 无
 *****************************************************************************/
sint32 pktFindUpdatePacket(const char *pszFindPath, char *pszPacketPath)
{
    sint32 s32Ret = 0, i;
    DIR *pDir = NULL;
    struct dirent *pstDirent = NULL;
	const char *pszPatternPacket = PKT_NAME_PREFIX".*";
    char szCmd[64] = {0};
    char *pszUpgradeStart = NULL;
    char szFoundName[128] = {0};
    char szUpgradeStartName[128] = {0};
    char szRealFileName[128] = {0};
    char szLaterPacketDir[128] = {0};
    char szFirmware[128] = {0};
    SV_BOOL bFoundPacket = SV_FALSE;

    pDir = opendir(pszFindPath);
	if(NULL == pDir)
    {
        print_level(SV_ERROR, "opendir /boot failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

#if defined(BOARD_ADA32V3)
    if (update_IsPathExist(PKT_LATER_FLAG_FILE))
    {
        print_level(SV_INFO, "found /root/later file, skip found update packet and use later packet update!\n");
        goto find_packet_exit;
    }
#endif

    print_level(SV_INFO, "try to find update packet in %s, packet prefix: %s\n", pszFindPath, PKT_NAME_PREFIX);
    while (1)
    {
        pstDirent = readdir(pDir);
        if (NULL == pstDirent)
        {
            print_level(SV_WARN, "can not find any update packet in %s\n", pszFindPath);
            break;
        }

		s32Ret = matchRegex(pszPatternPacket, pstDirent->d_name);
		if(SV_SUCCESS == s32Ret)
		{
            strcpy(szFoundName, pstDirent->d_name);
            bFoundPacket = SV_TRUE;
			break;
		}
        else
        {
            continue;
        }


    }
    closedir(pDir);

find_packet_exit:
#if defined(BOARD_ADA32V3)
    /* later包一般用于需要放两个升级包进去才可以升级成功的情况，要在前面那个包已经升级成功后再使用这个later包进行升级，所以前缀改为ADA32V3_later_upgrade */
    if (!bFoundPacket)
    {
        sprintf(szCmd, "ls %s/%s*", pszFindPath, PKT_NAME_PREFIX_LATER);
        system_ex_recv(szCmd, szLaterPacketDir, 128);
        cutLineBreak(szLaterPacketDir);
        if (0 != strlen(szLaterPacketDir))
        {
            print_level(SV_INFO, "found later packet dir: %s\n", szLaterPacketDir);
            char *filename = strrchr(szLaterPacketDir, '/');
            if (NULL != filename)
            {
                filename++;
                strcpy(szFoundName, filename);
                print_level(SV_INFO, "found later packet: %s\n", szFoundName);
                bFoundPacket = SV_TRUE;
                m_stUpdateInfo.bIsLaterPkt = SV_TRUE;
            }
        }
    }
#endif

    if (bFoundPacket)
    {
        if (m_stUpdateInfo.bNeedCheckSD)
		{
			if (0 == strcmp(pszFindPath, UPDATE_MOUNT_PATH))
			{
				//sprintf(szCmd, "rm %s/%s", UPDATE_MOUNT_PATH, UPDATE_LOG_NAME);
				//system_ex(szCmd, 10);
			}

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
		    if (NULL != strstr(szFoundName, "_fixed") && NULL == strstr(pszFindPath, "/boot"))
		    {
		        print_level(SV_INFO, "packet name with flag: \"fixed\"\n");
		        m_stUpdateInfo.bIsFixed = SV_TRUE;
		    }

            if (NULL != strstr(szFoundName, "_cover"))
            {
                print_level(SV_INFO, "packet name with flag: \"cover\"\n");
                m_stUpdateInfo.bIsCover = SV_TRUE;
            }

            if (NULL != strstr(szFoundName, "_360N"))
            {
                print_level(SV_INFO, "packet name with flag: \"360\"\n");
                m_stUpdateInfo.bIs360 = SV_TRUE;

                if(NULL != strstr(szFoundName, "_360P"))
                {
                    print_level(SV_INFO, "packet name with flag: \"360P\"\n");
                    m_stUpdateInfo.bIsPformat = SV_TRUE;
                }

            }

            if (NULL != strstr(szFoundName, "_config"))
            {
                print_level(SV_INFO, "packet name with flag: \"config\"\n");
                m_stUpdateInfo.bIsSetConfig = SV_TRUE;
            }

            if (NULL != strstr(szFoundName, "_recorder"))
            {
                print_level(SV_INFO, "packet name with flag: \"recorder\"\n");
                m_stUpdateInfo.bIsRecorder = SV_TRUE;
            }

            strcpy(szUpgradeStartName, szFoundName);
            pszUpgradeStart = strstr(szUpgradeStartName, "_upgradeStart");
            if (pszUpgradeStart != NULL)
            {
                sint32 s32TargetLen = strlen("_upgradeStart");
                memmove(pszUpgradeStart, pszUpgradeStart + s32TargetLen, strlen(pszUpgradeStart + s32TargetLen) + 1);
                print_level(SV_INFO, "packet name with flag: \"upgradeStart\"\n");
                m_stUpdateInfo.bIsUpgradeStart = SV_TRUE;
            }

            if (NULL != strstr(szFoundName, "_first"))
            {
		        print_level(SV_INFO, "packet name with flag: \"first\"\n");
                strcpy(szFirmware, m_stUpdateInfo.szFirmwareVer);
                char *version = strchr(szFirmware, '.');
                if (NULL != version)
                {
                    version++;
                    sint32 s32Version = atoi(version);
                    if (s32Version >= 4186)
                    {
                        print_level(SV_INFO, "version num=%d > 4186, skip to update with first packet!\n", s32Version);
                        bFoundPacket = SV_FALSE;
                        goto find_packet_exit;
                    }
                }
            }
#endif
		}

        sprintf(pszPacketPath, "%s/%s", pszFindPath, szFoundName);
        if (m_stUpdateInfo.bIsUpgradeStart)
        {
            sprintf(szRealFileName, "%s/%s", pszFindPath, szUpgradeStartName);
            rename(pszPacketPath, szRealFileName);
            strcpy(pszPacketPath, szRealFileName);
            print_level(SV_INFO, "file name: %s -> %s\n", pszPacketPath, szRealFileName);
        }
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 将升级包文件解密生成tar.gz包文件
 * 输入参数: pszPacketPath --- 升级包路径
             pszTargzPath --- 指向解密生成的tar.gz包文件路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 解密成功
             SV_FAILURE - 解密失败
 * 注意    : 无
 *****************************************************************************/
sint32 pktDecryption(char *pszPacketPath, char *pszTargzPath)
{
    sint32 s32Ret = 0;
    sint32 s32FdSrc, s32FdDes;
    sint32 s32Interval = DECRYPTION_INTERVAL;
    uint32 u32FileSize = 0;
    uint32 u32Size = 0, u32ByteCnt = 0;
    char szBuf[1024];
    uint32 u32OffsetSrc = 0, u32OffsetDest = 0;
    void *pvBufSrc = NULL, *pvBufDest = NULL;
    struct stat stStat;

	if (NULL == pszPacketPath || NULL == pszTargzPath)
	{
	    return SV_FAILURE;
	}

    print_level(SV_INFO, "begin decryption file: %s -> %s [interval=%d]\n", pszPacketPath, pszTargzPath, s32Interval);
    s32FdSrc = open(pszPacketPath, O_RDONLY);
    if(s32FdSrc < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszPacketPath, strerror(errno));
        return SV_FAILURE;
    }

    s32FdDes = open(pszTargzPath, O_TRUNC|O_CREAT|O_RDWR);
    if (s32FdDes < 0)
    {
        print_level(SV_ERROR, "create file: %s failed. [err: %s]\n", pszTargzPath, strerror(errno));
        close(s32FdSrc);
        return SV_FAILURE;
    }

    while ((u32Size = read(s32FdSrc, szBuf, s32Interval)))
    {
        u32ByteCnt += u32Size;

        /* 读到文件尾时丢弃最后一个字节 */
        if (u32Size < s32Interval)
        {
            u32Size -= 1;
        }

        s32Ret = write(s32FdDes, szBuf, u32Size);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "write file: %s failed. [err: %s]\n", pszTargzPath, strerror(errno));
            close(s32FdSrc);
            close(s32FdDes);
            return SV_FAILURE;
        }

        /* 累计读到1KB后每间隔s32Interval字节丢弃一个字节 */
        if (u32ByteCnt >= 1024)
        {
            s32Ret = lseek(s32FdSrc, 1, SEEK_CUR);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "lseek file: %s failed. [err: %s]\n", pszPacketPath, strerror(errno));
                close(s32FdSrc);
                close(s32FdDes);
                return SV_FAILURE;
            }
        }
    }

    close(s32FdSrc);
    close(s32FdDes);
    print_level(SV_INFO, "finish decryption file: %s -> %s [interval=%d]\n", pszPacketPath, pszTargzPath, s32Interval);

	return 0;
}

/******************************************************************************
 * 函数功能: 解包tar.gz文件
 * 输入参数: pszTargzFile --- tar.gz文件
             pszUntarPath --- 解包导出的路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 解包成功
             SV_FAILURE - 解包失败
 * 注意    : 无
 *****************************************************************************/
sint32 pktUntarFile(char *pszTargzFile, char *pszUntarPath)
{
    sint32 s32Ret = 0, i = 0;
	char szCmd[128];
	char szTarFile[128] = {0};
    char szDestDir[128] = {0};
    char szDir[2][32] = {"root", "usr"}; // 定义内部被压缩的目录个数和路径，二维数组的大小要和底下for循环的次数对应上，有多少个目录就定义多大
    char szPacketPath[1024] = {0};

    if(NULL == pszTargzFile || NULL == pszUntarPath)
    {
        return SV_FAILURE;
    }

    if (!update_IsPathExist(pszTargzFile))
    {
        print_level(SV_ERROR, "access file: %s failed. [err: %s]\n", pszTargzFile, strerror(errno));
        return SV_FAILURE;
    }

#ifdef PLATFORM_RV1126
	sprintf(szCmd, "gzip -d %s", pszTargzFile);
    print_level(SV_INFO, "cmd: %s\n", szCmd);
    s32Ret = system_ex(szCmd, 300);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err：%s]\n", szCmd, strerror(errno));
        return SV_FAILURE;
    }

    strncpy(szTarFile, pszTargzFile, strlen(pszTargzFile)-3);
    sprintf(szCmd, "tar -xvf %s -C %s", szTarFile, pszUntarPath);
    print_level(SV_INFO, "cmd: %s\n", szCmd);
    s32Ret = system_ex(szCmd, 300);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err：%s]\n", szCmd, strerror(errno));
        return SV_FAILURE;
    }

    sprintf(szCmd, "rm -f %s", szTarFile);
    s32Ret = system_ex(szCmd, 60);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        return SV_FAILURE;
    }
#elif PLATFORM_RV1106
    #if (defined(BOARD_DMS51V1))
    sprintf(szCmd, "7zr x -y %s  -o%s", pszTargzFile,pszUntarPath);
    s32Ret = system_ex(szCmd, 200);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err：%s]\n", szCmd, strerror(errno));
        return SV_FAILURE;
    }
    
    sprintf(szCmd, "ls %s",pszUntarPath);
    s32Ret = system_ex_recv(szCmd,szPacketPath,200);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err：%s]\n", szCmd, strerror(errno));
        return SV_FAILURE;
    }
    
    print_level(SV_INFO,"ls :%s \n",szPacketPath);
#else
    sprintf(szCmd, "tar -zxvf %s -C %s", pszTargzFile, pszUntarPath);
    s32Ret = system_ex(szCmd, 60);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err：%s]\n", szCmd, strerror(errno));
        return SV_FAILURE;
    }
#endif 
    

    sprintf(szCmd, "rm -f %s", pszTargzFile);
    s32Ret = system_ex(szCmd, 60);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        return SV_FAILURE;
    }

    if (!update_IsPathExist(UNTAR_DIR_PATH))
    {
        print_level(SV_ERROR, "access file: %s failed. [err: %s]\n", UNTAR_DIR_PATH, strerror(errno));
        return SV_FAILURE;
    }

    for (i = 0; i < 2; i++)
    {
        sprintf(szDestDir, "%s/%s.7z", UNTAR_DIR_PATH, szDir[i]);
        if (update_IsPathExist(szDestDir))
        {
            print_level(SV_INFO, "access dir: %s\n", szDestDir);
            sprintf(szCmd, "7zr x -y %s -o%s/%s/", szDestDir, UNTAR_DIR_PATH, szDir[i]);
            s32Ret = system_ex(szCmd, 60);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
                return s32Ret;
            }

            sprintf(szCmd, "rm -rf %s", szDestDir);
            s32Ret = system_ex(szCmd, 60);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
                return s32Ret;
            }
        }
    }
#else
	sprintf(szCmd, "tar -zxvf %s -C %s", pszTargzFile, pszUntarPath);
    print_level(SV_INFO, "cmd: %s\n", szCmd);
    s32Ret = system_ex(szCmd, 60);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        return SV_FAILURE;
    }

    sprintf(szCmd, "rm -f %s", pszTargzFile);
    s32Ret = system_ex(szCmd, 60);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解析升级包的md5check文件生成所有文件md5列表信息
 * 输入参数: pszFilePath --- md5check文件路径
 * 输出参数: pstPacketInfo --- 升级包信息
 * 返回值  : SV_SUCCESS - 解析成功
             SV_FAILURE - 解析失败
 * 注意    : 使用完后需要调用者主动释放PACKET_INFO_S中分配的内存
 * 说明  : md5check文件内容格式如下:
 *  行号 1. VERSION=xxxx
         2. CreateTime=xxxx
         3. 空行
         n. :[要删除的系统文件1路径](可选)
         n. :[要删除的系统文件2路径](可选)
         n. [包中文件1]=[md5sum]:[拷贝到的目标路径]
         n. [包中文件2]=[md5sum]:[拷贝到的目标路径]
 *****************************************************************************/
sint32 pktParseMd5check(char *pszFilePath, PACKET_INFO_S *pstPacketInfo)
{
    sint32 s32Ret = 0;
    uint32 u32LineNum = 0;
    uint32 u32PktFileCnt = 0, u32DelFileCnt = 0;
    char szBuf[512] = {0};
    char szFilePath[256] = {0};
    char *pszTmp = NULL;
    FILE *fp = NULL;
    PACKET_INFO_S stPacketInfo = {0};
    struct stat stFileStat = {0};


    char szCmd[128] = {0};
	char szPacketPath[1024] = {0};

    if (NULL == pszFilePath || NULL == pstPacketInfo)
    {
        return SV_FAILURE;
    }

    fp = fopen(pszFilePath, "rt");
    if (NULL == fp)
    {
        print_level(SV_ERROR, "fopen file: %s failed.\n", pszFilePath);
        return SV_FAILURE;
    }

    /* 统计增删文件数目 */
    fseek(fp, 0, SEEK_SET);
    while (NULL != fgets(szBuf, sizeof(szBuf), fp))
    {
        u32LineNum++;
        if (u32LineNum == 1)
        {
            pszTmp = strstr(szBuf, "VERSION=");
            if (NULL == pszTmp)
            {
                print_level(SV_ERROR, "can not find \"VERSION\" in md5check line 1.\n");
                fclose(fp);
                return SV_FAILURE;
            }
            cutLineBreak(pszTmp);
            strncpy(stPacketInfo.szVersion, pszTmp, 31);
            stPacketInfo.szVersion[31] = '\0';
            continue;
        }

        if (u32LineNum == 2)
        {
            pszTmp = strstr(szBuf, "CreateTime=");
            if (NULL == pszTmp)
            {
                print_level(SV_ERROR, "can not find \"CreateTime\" in md5check line 2.\n");
                fclose(fp);
                return SV_FAILURE;
            }
            cutLineBreak(pszTmp);
            strncpy(stPacketInfo.szCreateTime, pszTmp, 64);
            stPacketInfo.szCreateTime[63] = '\0';
            continue;
        }

        if (u32LineNum == 3)
        {
            continue;
        }

        if (szBuf[0] == ':')
        {
            u32DelFileCnt++;
        }
        else
        {
            u32PktFileCnt++;
        }
    }

    print_level(SV_INFO, "delete files count: %d, packet files count: %d\n", u32DelFileCnt, u32PktFileCnt);
    fseek(fp, 0, SEEK_SET);
    u32LineNum = 0;
    if (0 != u32DelFileCnt)
    {
        stPacketInfo.pastDelFileList = malloc(u32DelFileCnt * sizeof(DEL_FILE_S));
        if (NULL == stPacketInfo.pastDelFileList)
        {
            print_level(SV_ERROR, "malloc for delete file list failed. [size=%d]\n", u32DelFileCnt * sizeof(DEL_FILE_S));
            fclose(fp);
            return SV_FAILURE;
        }
    }

    if (0 != u32PktFileCnt)
    {
        stPacketInfo.pastPktFileList = malloc(u32PktFileCnt * sizeof(PKT_FILE_S));
        if (NULL == stPacketInfo.pastPktFileList)
        {
            print_level(SV_ERROR, "malloc for packet file list failed. [size=%d]\n", u32PktFileCnt * sizeof(PKT_FILE_S));
            if (NULL != stPacketInfo.pastDelFileList)
            {
                free(stPacketInfo.pastDelFileList);
            }
            fclose(fp);
            return SV_FAILURE;
        }
    }

    /* 读取文件信息 */
    while (NULL != fgets(szBuf, sizeof(szBuf), fp))
    {
        u32LineNum++;
        if (u32LineNum <= 3)
        {
            continue;
        }

        if (szBuf[0] == ':')
        {
            cutLineBreak(&szBuf[1]);
            strcpy(stPacketInfo.pastDelFileList[stPacketInfo.u32DelFileCnt].szFilePath, &szBuf[1]);
            if (update_IsPathExist(stPacketInfo.pastDelFileList[stPacketInfo.u32DelFileCnt].szFilePath))
            {
                print_level(SV_INFO, "delete file: %s\n", stPacketInfo.pastDelFileList[stPacketInfo.u32DelFileCnt].szFilePath);
                stPacketInfo.u32DelFileCnt++;
            }
        }
        else
        {
            char *pc1 = strrchr(szBuf, '=');
            char *pc2 = strrchr(szBuf, ':');
            if (NULL == pc1 || NULL == pc2 || pc1 > pc2)
            {
                print_level(SV_ERROR, "invalid line content: %s\n", szBuf);
                continue;
            }
            cutLineBreak(szBuf);
            *pc1 = '\0';
            strncpy(stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].szFilePath, szBuf, 128);
            stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].szFilePath[127] = '\0';
            *pc2 = '\0';
            strncpy(stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].szMd5sum, pc1+1, 40);
            stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].szMd5sum[39] = '\0';
            strncpy(stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].szDestPath, pc2+1, 128);
            stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].szDestPath[127] = '\0';

            sprintf(szFilePath, "%s/%s", UNTAR_DIR_PATH, stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].szFilePath);
            s32Ret = stat(szFilePath, &stFileStat);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "stat file: %s failed!\n", szFilePath);
                stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].s32FileSize = 0;
            }
            else
            {
                stPacketInfo.pastPktFileList[stPacketInfo.u32PktFileCnt].s32FileSize = (sint32)stFileStat.st_size;
            }
            stPacketInfo.u32PktFileCnt++;
        }
    }

    fclose(fp);
    *pstPacketInfo = stPacketInfo;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 打印升级包文件信息
 * 输入参数: pstPacketInfo --- 升级包信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void pktPrintFileInfo(PACKET_INFO_S *pstPacketInfo)
{
    sint32 i;

    if (NULL == pstPacketInfo)
    {
        return;
    }

    printf("\r\n--------------------------------------------------------------------------------\r\n");
    printf("%s\n", pstPacketInfo->szVersion);
    printf("%s\n", pstPacketInfo->szCreateTime);
    for (i = 0; i < pstPacketInfo->u32DelFileCnt; i++)
    {
        printf(":%s\n", pstPacketInfo->pastDelFileList[i].szFilePath);
    }
    for (i = 0; i < pstPacketInfo->u32PktFileCnt; i++)
    {
        printf("%s=%s:%s\n", pstPacketInfo->pastPktFileList[i].szFilePath, pstPacketInfo->pastPktFileList[i].szMd5sum, pstPacketInfo->pastPktFileList[i].szDestPath);
    }
}

/******************************************************************************
 * 函数功能: 根据解析md5check得到的md5列表信息对升级包文件进行md5校验
 * 输入参数: pszUntarPath --- 升级包解包路径
             pstPacketInfo --- 升级包信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 校验成功
             SV_FAILURE - 校验失败
 * 注意    : 无
 *****************************************************************************/
sint32 pktCheckPacketMd5(char *pszUntarPath, PACKET_INFO_S *pstPacketInfo)
{
    sint32 s32Ret = 0, i;
    char szFilePath[256];
    char szMd5[40];

    if (NULL == pszUntarPath || NULL == pstPacketInfo)
    {
        return SV_FAILURE;
    }

    print_level(SV_INFO, "begin check packet md5sum...\n");
    for (i = 0; i < pstPacketInfo->u32PktFileCnt; i++)
    {
        sprintf(szFilePath, "%s/%s", pszUntarPath, pstPacketInfo->pastPktFileList[i].szFilePath);

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
        if (NULL != strstr(szFilePath, "root/System_info"))  // 判断为RK1126旧工程升级包，禁止升级
        {
            print_level(SV_ERROR, "unsupported upgrade to old project!!!\n");
            return SV_FAILURE;
        }
#endif
        s32Ret = getFileMd5(szFilePath, szMd5);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "getFileMd5 file: %s failed.\n", szFilePath);
            return SV_FAILURE;
        }

        if (0 != strcmp(pstPacketInfo->pastPktFileList[i].szMd5sum, szMd5))
        {
            print_level(SV_ERROR, "file: %s md5sum failed. [info: %s, calc: %s]\n", szFilePath, pstPacketInfo->pastPktFileList[i].szMd5sum, szMd5);
            return SV_FAILURE;
        }
    }
    print_level(SV_INFO, "check packet md5sum successful!\n");

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 删除已添加文件并释放内存
 * 输入参数: pstPacketInfo --- 升级包信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 校验成功
             SV_FAILURE - 校验失败
 * 注意    : 无
 *****************************************************************************/
sint32 pktDeleteAddedFile(PACKET_INFO_S *pstPacketInfo)
{
    sint32 s32Ret = 0, i;
    char szFilePath[256];
    char szMd5[40];

    if (NULL == pstPacketInfo)
    {
        return SV_FAILURE;
    }

    print_level(SV_INFO, "begin delete added file...\n");

    for (i = 0; i < pstPacketInfo->u32DelFileCnt; i++)
    {
        if (0 != strlen(pstPacketInfo->pastDelFileList[i].szFilePath))
        {
            memset(pstPacketInfo->pastDelFileList+i, 0, sizeof(DEL_FILE_S));
        }
    }
    pstPacketInfo->u32DelFileCnt = 0;
    if (NULL != pstPacketInfo->pastDelFileList)
        free(pstPacketInfo->pastDelFileList);

    for (i = 0; i < pstPacketInfo->u32PktFileCnt; i++)
    {
        if (0 != strlen(pstPacketInfo->pastPktFileList[i].szFilePath))
        {
            memset(pstPacketInfo->pastPktFileList+i, 0, sizeof(PKT_FILE_S));
        }
    }
    pstPacketInfo->u32PktFileCnt = 0;
    if (NULL != pstPacketInfo->pastPktFileList)
        free(pstPacketInfo->pastPktFileList);

    print_level(SV_INFO, "delete added file successful!\n");

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将外部文件数据变量导出生成KO文件并加载KO到内核
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 flashInsmodMtdexKo()
{
#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106) && !defined(PLATFORM_RV1126B))

    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    char szFilePath[128];
    char szMd5[40];
    char szCmd[64];

    print_level(SV_DEBUG, "pszFilePath: %s, pszMd5: %s, u32FileSize=%d\n", mtdex_ko_name, mtdex_ko_md5, mtdex_ko_len);
    sprintf(szFilePath, "/var/%s", mtdex_ko_name);
    s32Fd = open(szFilePath, O_CREAT | O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "create file: %s failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = write(s32Fd, mtdex_ko, mtdex_ko_len);
    if (s32Ret < 0 || s32Ret != mtdex_ko_len)
    {
        print_level(SV_ERROR, "write file: %s failed. [ret=%d, err: %s]\n", s32Ret, strerror(errno));
        close(s32Fd);
        return SV_FAILURE;
    }
    close(s32Fd);

    s32Ret = getFileMd5(szFilePath, szMd5);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "getFileMd5 file: %s failed.\n", szFilePath);
        return SV_FAILURE;
    }

    if (0 != strcmp(mtdex_ko_md5, szMd5))
    {
        print_level(SV_ERROR, "generate file: %s md5sum error. [exp: %s, cal: %s]\n", szFilePath, mtdex_ko_md5, szMd5);
        return SV_FAILURE;
    }

    sprintf(szCmd, "rmmod %s", szFilePath);
    system_ex(szCmd, 60);
    sprintf(szCmd, "insmod %s", szFilePath);
    s32Ret = system_ex(szCmd, 60);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 卸载mtdex.ko
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 flashRmmodMtdexKo()
{
#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106) && !defined(PLATFORM_RV1126B))
    char szCmd[64];

    sprintf(szCmd, "rmmod /var/%s", mtdex_ko_name);
    system_ex(szCmd, 60);
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 使能/禁止flash写保护机制
 * 输入参数: bEnable --- 是否使能
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 在进行升级覆盖文件前需要调用该接口禁止写保护
           完成升级覆盖文件之后需要调用该接口使能写保护
 *****************************************************************************/
sint32 flashProtectionEnable(SV_BOOL bEnable)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    sint32 s32EndBlk;
    char *pszPathEnd = "/proc/mtdex/mtd_protection_end";
    char szBuf[128];
    char szCmd[64];

#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106) && !defined(PLATFORM_RV1126B))
    s32EndBlk = bEnable ? 512 : 0;
    if (bEnable)
    {
        strcpy(szCmd, "mount -o remount,ro /");
        s32Ret = system_ex(szCmd, 10);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        }
    }

    s32Fd = open(pszPathEnd, O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszPathEnd, strerror(errno));
        return SV_FAILURE;
    }

    sprintf(szBuf, "%d", s32EndBlk);
    s32Ret = write(s32Fd, szBuf, strlen(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write %s failed. [err=%d]\n", pszPathEnd, s32Ret);
        close(s32Fd);
        return SV_FAILURE;
    }
    close(s32Fd);

    if (!bEnable)
    {
        strcpy(szCmd, "mount -o remount,rw /");
        s32Ret = system_ex(szCmd, 10);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
            return SV_FAILURE;
        }
    }

#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 首次烧录,更新source.7z
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateSourceFirst()
{
    sint32 s32Ret;
    char cmd[128];

    if (access(UPDATE_SOURCE_JUDGE, F_OK) == 0)
    {
        return SV_SUCCESS;
    }

    s32Ret = BOARD_UpdateSVersionSource();
    if (s32Ret != SV_SUCCESS)
    {
        return s32Ret;
    }

    /* 更新标志文件 */
    sprintf(cmd, "touch %s", UPDATE_SOURCE_JUDGE);
    SAFE_System(cmd, 1000);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 对比升级包文件与系统文件差异性
 * 输入参数: pstPacketInfo --- 升级包信息
 * 输出参数: 无
 * 返回值  : 0 - 无差异
             >0 - 有差异 (bit0: arm 差异, bit1: mcu 差异)
             -1 - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateComparePacketWithSystem(PACKET_INFO_S *pstPacketInfo)
{
    sint32 s32Ret = 0, i;
    sint32 s32ProcessNum = 0;
    sint32 s32DiffFlag = 0;
    char szFilePath[256];
    char szFilePath2[256];
    char szCmd[256];

    if (NULL == pstPacketInfo)
    {
        return -1;
    }

    print_level(SV_INFO, "begin compare packet with system...\n");
    for (i = 0; i < pstPacketInfo->u32PktFileCnt; i++)
    {
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
        char szLogoCmd[1024];
        sprintf(szLogoCmd, "logocontrol -process Check\\ File %f &", (1.0 * (++s32ProcessNum)) / (pstPacketInfo->u32PktFileCnt));
        system_sys(szLogoCmd);
#endif

#if (defined(BOARD_WFCR20S2) || defined(BOARD_ADA32C4))
        if ((0 == i || 1 == i) && NULL != strstr(pstPacketInfo->pastPktFileList[i].szFilePath, MCU_FIRMWARE_PREFIX))
        {
            if (m_stUpdateInfo.bIsMcuExist && NULL != strstr(pstPacketInfo->pastPktFileList[i].szFilePath, MCU_FIRMWARE_FILENAME))
            {
                SV_BOOL bDiff = SV_FALSE;
                sprintf(szFilePath, "%s/%s", UNTAR_DIR_PATH, pstPacketInfo->pastPktFileList[i].szFilePath);
                s32Ret = mcuIsFirmwareDifferent(szFilePath, &bDiff);
                if (SV_SUCCESS == s32Ret && bDiff)
                {
                    print_level(SV_INFO, "MCU firmware is different.\n");
                    s32DiffFlag |= 0x2;
                }
            }
            continue;
        }
#endif
        if (0 == strcmp(pstPacketInfo->pastPktFileList[i].szDestPath, "/var"))
        {
            continue;
        }

#if (defined(DMS31SDK) || defined(ADA32SDK) || defined(ADA32NSDK) || defined(ADA32ESDK))
        if (0 == strcmp(pstPacketInfo->pastPktFileList[i].szFilePath, "etc/shadow"))
        {
            continue;   // 防止客户通过SDK包篡改登陆密码
        }
#endif

        sprintf(szFilePath, "/%s", pstPacketInfo->pastPktFileList[i].szFilePath);
        if (!update_IsPathExist(szFilePath))
        {
            print_level(SV_INFO, "found new add file: %s\n", szFilePath);
            if (NULL != strstr(szFilePath, "AC601_upgrade"))
            {
                print_level(SV_INFO, "found new ac601 upgrade packet: %s, delete older packet.\n", pstPacketInfo->pastPktFileList[i].szDestPath);
                strcpy(szCmd, "rm -rf /root/mcu/ac601/AC601_upgrade*");
                s32Ret = system_ex(szCmd, 60);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
                }
            }

            s32DiffFlag |= 0x1;
            break;
        }

        sprintf(szCmd, "cmp -s %s %s/%s", szFilePath, UNTAR_DIR_PATH, pstPacketInfo->pastPktFileList[i].szFilePath);
        s32Ret = system_ex(szCmd, 60);
        if (0 != s32Ret)
        {
            print_level(SV_INFO, "found different file: %s. \n", szFilePath);
            s32DiffFlag |= 0x1;
            break;
        }
    }

    if (0 == s32DiffFlag)
    {
        print_level(SV_INFO, "compare finish. no different.\n");
    }

    return s32DiffFlag;
}

/******************************************************************************
 * 函数功能: 将升级包需要更新的文件覆盖到文件系统中
 * 输入参数: pszUntarPath --- 升级包解包路径
             pstPacketInfo --- 升级包信息
             s32DiffFlag --- 升级包与系统的差异标志 (bit0: arm 差异, bit1: mcu 差异)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 写入成功
             SV_FAILURE - 写入失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateOverwritePacketToSystem(char *pszUntarPath, PACKET_INFO_S *pstPacketInfo, sint32 s32DiffFlag)
{
    sint32 s32Ret = 0, i, j;
    sint32 s32ProcessNum = 0;
    char szSrcPath[256];
    char szDestPath[256];
    char szDestDir[256];
    char szFileTmp[256];
    char szCmd[256];
    char szLogoCmd[1024];
    sint32 s32FileSize = 0;
    char szFileName[256];
    char *pFileName = NULL;

    if (NULL == pszUntarPath || NULL == pstPacketInfo)
    {
        return SV_FAILURE;
    }

    print_level(SV_INFO, "begin overwrite packet to system...\n");
    s32Ret = flashProtectionEnable(SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "disable flash protection failed.\n");
        return SV_FAILURE;
    }

    for (i = 0; i < pstPacketInfo->u32DelFileCnt; i++)
    {
        if (update_IsPathExist(pstPacketInfo->pastDelFileList[i].szFilePath))
        {
            sprintf(szCmd, "rm -rf %s", pstPacketInfo->pastDelFileList[i].szFilePath);
            s32Ret = system_ex(szCmd, 10);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
            }
        }
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
        sprintf(szLogoCmd, "logocontrol -process Update\\ Software %f ", (1.0 * (++s32ProcessNum)) / (pstPacketInfo->u32DelFileCnt+pstPacketInfo->u32PktFileCnt));
        system_sys(szLogoCmd);
#endif
    }

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32V4))
    /* 删除/root下残留的模型 */
    system_ex("rm /root/*.rknn", 60);
#endif

    for (i = 0; i < pstPacketInfo->u32PktFileCnt; i++)
    {
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
        sprintf(szLogoCmd, "logocontrol -process Update\\ Software %f ", (1.0 * (++s32ProcessNum)) / (pstPacketInfo->u32DelFileCnt+pstPacketInfo->u32PktFileCnt));
        system_sys(szLogoCmd);
#endif
#if (defined(BOARD_WFCR20S2) || defined(BOARD_ADA32C4))
        if ((0 == i || 1 == i) && NULL != strstr(pstPacketInfo->pastPktFileList[i].szFilePath, MCU_FIRMWARE_PREFIX))
        {
            continue;
        }
#endif
        s32FileSize = pstPacketInfo->pastPktFileList[i].s32FileSize;
        sprintf(szSrcPath, "%s/%s", pszUntarPath, pstPacketInfo->pastPktFileList[i].szFilePath);
        sprintf(szDestPath, "/%s", pstPacketInfo->pastPktFileList[i].szFilePath);
        sprintf(szFileTmp, "%s-tmp", szDestPath);
        sprintf(szCmd,"cmp -s %s %s", szSrcPath, szDestPath);
        s32Ret = system_ex(szCmd, 60);
        if (0 == s32Ret)
        {
            continue;
        }
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
        if (NULL != strstr(szSrcPath, "/var/env") || NULL != strstr(szSrcPath, "/var/idblock")
            || NULL != strstr(szSrcPath, "/var/uboot") || NULL != strstr(szSrcPath, "/var/kernel")
            || NULL != strstr(szSrcPath, "/var/rootfs"))  // 跳过env/idblock/uboot/kernel/rootfs
        {
            continue;
        }
#endif
#if defined(BOARD_ADA32V3)
        if (NULL != strstr(szSrcPath, "/etc/fstab"))
        {
            m_stUpdateInfo.bNeedReboot = SV_TRUE;
        }
#endif
#if (defined(DMS31SDK) || defined(ADA32SDK) || defined(ADA32NSDK) || defined(ADA32ESDK))
        if (NULL != strstr(szSrcPath, "/etc/shadow"))
        {
            continue;   // 防止客户通过SDK包篡改登陆密码
        }
#endif

        if (!update_IsPathExist(pstPacketInfo->pastPktFileList[i].szDestPath))
        {
            print_level(SV_DEBUG, "mkdir -p %s\n", pstPacketInfo->pastPktFileList[i].szDestPath);
            sprintf(szCmd, "mkdir -p %s", pstPacketInfo->pastPktFileList[i].szDestPath);
            s32Ret = system_ex(szCmd, 60);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
                s32Ret = flashProtectionEnable(SV_TRUE);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "enable flash protection failed.\n");
                }
                return SV_FAILURE;
            }
        }
        for (j = 0; j < 3; j++)
        {
            print_level(SV_DEBUG, "%s -> %s\n", szSrcPath, szDestPath);
            if (s32FileSize >= UPDATE_COVER_FILE_SIZE)
            {
                sprintf(szCmd, "cp %s %s", szSrcPath, szDestPath);
                s32Ret = system_ex(szCmd, 60);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed. [err=%d, reason: %s]\n", szCmd, s32Ret, strerror(errno));
                    continue;
                }
            }
            else
            {
                sprintf(szCmd, "cp -f %s %s", szSrcPath, szFileTmp);

                s32Ret = system_ex(szCmd, 60);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed. [err=%d, reason: %s]\n", szCmd, s32Ret, strerror(errno));
                    remove(szFileTmp);
                    continue;
                }

                s32Ret = rename(szFileTmp, szDestPath);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "rename %s-> %s failed. [err: %s]\n", szFileTmp, szDestPath, strerror(errno));
                    continue;
                }
            }

            sprintf(szCmd,"cmp -s %s %s", szSrcPath, szDestPath);
            s32Ret = system_ex(szCmd, 60);
            if (0 == s32Ret)
            {
                break;
            }
        }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_IPCR20S3) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1)  || defined(BOARD_ADA32E1) || defined(BOARD_ADA32V4))
        if (j < 3 && strstr(szSrcPath, "source.7z") != NULL)
        {
            print_level(SV_INFO, "update source later.\n");
            m_stUpdateInfo.bUpdateSource = SV_TRUE;
        }

        if (j < 3 && strstr(szSrcPath, "specialVersion") != NULL)
        {
            print_level(SV_INFO, "update specialVersion latter.\n");
            m_stUpdateInfo.bUpdateSpecialVersion = SV_TRUE;
        }

        /* 升级包里有MCU程序，直接把旧的MCU程序删掉。避免回退程序后mcu程序不匹配的问题 */
        if (j < 3 && strstr(szSrcPath, "UpdateMcuPacket-ADA32-ARTERYCBB2") != NULL)
        {
            print_level(SV_INFO, "remove old UpdateMcuPacket-ADA32-ARTERYCBB2\n");
            pFileName = strstr(szSrcPath, "UpdateMcuPacket-ADA32-ARTERYCBB2");
            snprintf(szFileName, 256, pFileName);
            print_level(SV_INFO, "szSrcPath=%s, szFileName=%s\n", szSrcPath, szFileName);
            sprintf(szCmd, "find /root/mcu/ -name \"UpdateMcuPacket-ADA32-ARTERYCBB2*\" |grep -v %s | xargs rm", szFileName);
            s32Ret = system_ex(szCmd, 60);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed. [err=%d, reason: %s]\n", szCmd, s32Ret, strerror(errno));
            }
        }
#endif

        if (j >= 3)
        {
            print_level(SV_ERROR, "overwrite file: %s -> %s failed.\n", szSrcPath, szDestPath);
            s32Ret = flashProtectionEnable(SV_TRUE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "enable flash protection failed.\n");
            }
            return SV_FAILURE;
        }
    }

#if (defined(BOARD_ADA32C4))
	if (m_stUpdateInfo.bIsMcuExist && 0 != (s32DiffFlag & 0x2))
    {
        sprintf(szSrcPath, "%s/%s", pszUntarPath, pstPacketInfo->pastPktFileList[1].szFilePath);
		print_level(SV_DEBUG,"---------------------------------------------path:%s \n",szSrcPath);
        s32Ret = ADA32_mcuTransferFirmware(szSrcPath);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mcuTransferFirmware failed.\n");
            goto normal_end;
        }
    }

normal_end:
#endif


#if (defined(BOARD_WFCR20S2))
    if (m_stUpdateInfo.bIsMcuExist && 0 != (s32DiffFlag & 0x2))
    {
        uint16 u16MTU = 0;
        print_level(SV_INFO, "overwrite firmware to MCU...\n");
        s32Ret = mcuTransportInit(&u16MTU);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mcuTransportInit failed.\n");
            goto normal_end;
        }

        sprintf(szSrcPath, "%s/%s", pszUntarPath, pstPacketInfo->pastPktFileList[0].szFilePath);
        s32Ret = mcuTransferInitPacket(szSrcPath);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mcuTransferInitPacket failed.\n");
            goto normal_end;
        }

        sprintf(szSrcPath, "%s/%s", pszUntarPath, pstPacketInfo->pastPktFileList[1].szFilePath);
        s32Ret = mcuTransferFirmware(szSrcPath);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mcuTransferFirmware failed.\n");
            goto normal_end;
        }
    }
normal_end:
#endif

    print_level(SV_INFO, "finish overwrite packet to system.\n");
    s32Ret = flashProtectionEnable(SV_TRUE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "enable flash protection failed.\n");
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化升级环境
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateInit()
{
    sint32 s32Ret = 0;
	sint32 s32Fd = 0;
    char *pszConfigFile = CONFIG_XML;
    char *pszConfigBak1 = CONFIG_BAK1;
    char *pszConfigBak2 = CONFIG_BAK2;
    char *pszConfigDefault = CONFIG_DEFAULT;
    CFG_MEDIA_PARAM stMediaParam = {0};
    char szCmd[64];
	char szMountFile[64];
	char szDevPath[128];
	char partionDevPath[128];
	const char *pszMountPath = NULL;
	const char *pszFsType = NULL;
	const char *option = NULL;
	SV_BOOL bSdcardExist = SV_FALSE;

	sprintf(szCmd, "mount -t tmpfs -o size=10M tmpfs /var/");
	s32Ret = system_ex(szCmd, 60);
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
		return SV_FAILURE;
	}

	s32Ret = system_ex("mkdir -p /var/log/uploaded", 10);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: mkdir -p /var/log/uploaded failed. [err=%d]\n", szCmd, s32Ret);
        system_ex("umount /var/", 10);
        return SV_FAILURE;
    }

    s32Ret = system_ex("mkdir -p /var/lock", 10);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: mkdir -p /var/lock failed. [err=%d]\n", szCmd, s32Ret);
        system_ex("umount /var/", 10);
        return SV_FAILURE;
    }

#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106) && !defined(PLATFORM_RV1126B))
	sprintf(szCmd, "mount -t tmpfs -o size=15M tmpfs /tmp/");
    s32Ret = system_ex(szCmd, 60);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        system_ex("umount /var/", 10);
        return SV_FAILURE;
    }
#endif

    s32Ret = CONFIG_Init(INIT_MOD_AUTOUPDATE, pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
    s32Ret = CONFIG_GetMediaParam(&stMediaParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetMediaParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stUpdateInfo.u32LogoFps = stMediaParam.u32VoFramerate;
    if(stMediaParam.enVoResolution == RES_720P)
    {
        m_stUpdateInfo.u32LogoWidth = 1280;
        m_stUpdateInfo.u32LogoHeight = 720;
    }
    else if(stMediaParam.enVoResolution == RES_1080P)
    {
        m_stUpdateInfo.u32LogoWidth = 1920;
        m_stUpdateInfo.u32LogoHeight = 1080;
    }
    m_stUpdateInfo.enCvbs = stMediaParam.enExtscreenMode;

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32V4))
    m_stUpdateInfo.enRotate = stMediaParam.astChnParam[0].enRotateAngle;
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32V4))
    CFG_ALG_PARAM stAlgParam;
    s32Ret = CONFIG_GetAlgParam(&stAlgParam);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "CONFIG_GetAlgParam failed! [err=%#x]\n", s32Ret);
    }

    s32Ret = BOARD_SetAlarmOut(stAlgParam.enAlgTrigger ==  TRIGGER_UP ? 0 : 1);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "BOARD_SetAlarmOut failed. [err=%#x]\n", s32Ret);
    }
#endif

#if (!defined(DMS31SDK) && !defined(ADA32SDK))
    if (0 != access(KEY_AUTH_FLAG, F_OK))     /* 作为是否需要启动激活密钥的标志 */
    {
        m_stUpdateInfo.bGenerateKey = SV_TRUE;
    }

    if (0 != access(KEY_AUTH_FLAG_BRIGADE, F_OK) && BOARD_IsCustomer(BOARD_C_ADA32V2_200001))
    {
        m_stUpdateInfo.bGenerateKey = SV_TRUE;
        print_level(SV_INFO, "200001 GenerateKey\n");
    }
#endif

#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化升级环境
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateFini()
{
    sint32 s32Ret = 0;
    char szCmd[64];

    s32Ret = CONFIG_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_Fini failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = LOG_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "LOG_Fini failed. [err=%#x]\n", s32Ret);
    }

    system_ex("umount /var/", 10);	//所有平台都挂载了/var，统一umount

#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106) && !defined(PLATFORM_RV1126B))
    system_ex("umount /tmp/", 10);
#endif
    if (m_stUpdateInfo.bUpdateSDWritable)
    {
        s32Ret = umount(UPDATE_MOUNT_PATH);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "umount %s failed. [err: %s]\n", UPDATE_MOUNT_PATH, strerror(errno));
            sprintf(szCmd, "umount -l %s", UPDATE_MOUNT_PATH);
            system_ex(szCmd, 10);
        }
        else
        {
            print_level(SV_INFO, "umount %s successful\n", UPDATE_MOUNT_PATH);
            m_stUpdateInfo.bUpdateSDWritable = SV_FALSE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动升级流程
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateStart()
{
    sint32 s32Ret = 0;

    s32Ret = flashInsmodMtdexKo();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "flashInsmodMtdexKo failed.\n");
        return SV_FAILURE;
    }

    flashProtectionEnable(SV_TRUE);

    s32Ret = LOG_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "LOG_Start failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止升级流程
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateStop()
{
    sint32 s32Ret = 0;

    flashRmmodMtdexKo();
    s32Ret = LOG_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStop failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 记录检查当前升级包尝试升级次数, 超过3次则失败
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 当前尝试的次数
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateCheckTryTimes()
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    sint32 s32TryTime = 0;
    char szTmp[16] = {0};

    s32Ret = flashProtectionEnable(SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "disable flash protection failed.\n");
    }


    s32Fd = open("/boot/try_update_times", O_RDWR|O_CREAT);

    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /jffs/boot/try_update_times failed. [err:%s]\n", strerror(errno));
    }

    s32Ret = read(s32Fd, szTmp, 16);
    if (s32Ret > 0)
    {
        s32TryTime = atoi(szTmp);
    }

    s32TryTime++;
    if (s32TryTime > 3)
    {
        print_level(SV_ERROR, "try to reset to update kernel exceed 3 times. now to delete packet.\n");
        close(s32Fd);
        remove("/boot/try_update_times");
        flashProtectionEnable(SV_TRUE);
        return SV_FAILURE;
    }
    else
    {
        sprintf(szTmp, "%d", s32TryTime);
        lseek(s32Fd, 0, SEEK_SET);
        s32Ret = write(s32Fd, szTmp, strlen(szTmp));
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "write file: /jffs/boot/try_update_time failed. [err:%s]\n", strerror(errno));
        }
        close(s32Fd);
    }
    s32Ret = flashProtectionEnable(SV_TRUE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "enable flash protection failed.\n");
    }

    return s32TryTime;
}

#define KEY_AUTH_PWD2           "..GOOD"
/******************************************************************************
 * 函数功能: 生成激活密钥
 * 输入参数: 无
 * 输出参数: pszUuid --- 设备UUID
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 generateKeyAuth()
{
#ifdef PLATFORM_RV1126
    sint32 s32Ret, i;
    char szChipSN[32] = {0};
    char szBuf[4096] = {0};
    uint8 u8Digest[16] = {0};
    char szCmd[128] = {0};
    char szMd5sum1[64] = {0};
    char szMd5sum2[64] = {0};
    char szKeyAuthPwd3[16] = {0};
    char szDevId[40] = {0};
    char szPwd[64] = {0};
    char szJsonBody[4096] = {0};
    char *pszTemp = NULL;
    char *pszSrcFile = "/var/authorize.txt";
    char *pszDesFile = "/var/license.txt";
    int fdsrc, fddes;
    char *pszFixedUuid = "01234567-89ab-cdef-fedc-ba9876543210";

    if (0 == access("/etc/license.txt", F_OK) || 0 == access("/etc/license2.txt", F_OK))
    {
        print_level(SV_INFO, "device is already activate!");
        return SV_SUCCESS;
    }

    s32Ret = SAFE_CAT("/proc/cpuinfo", szBuf, 4096);
    if (0 !=- s32Ret)
    {
        print_level(SV_ERROR, "SAFE_CAT failed!\n");
        return SV_FAILURE;
    }

    pszTemp = strstr(szBuf, "Serial\t\t: ");
    if (NULL == pszTemp)
    {
        print_level(SV_ERROR, "find Serial failed.\n");
        return SV_FAILURE;
    }

    pszTemp += strlen("Serial\t\t: ");
    strncpy(szChipSN, pszTemp, 16);
    //print_level(SV_DEBUG, "Serial: %s\n", szChipSN);

    /* 拼接UUID和芯片ID并获取相应的MD5码 */
    memset(szBuf, 0x00, 4096);
    strcpy(szBuf, pszFixedUuid);
    strcat(szBuf, ":");
    strcat(szBuf, szChipSN);
    sprintf(szCmd, "echo -n \"%s\" | md5sum", szBuf);
    s32Ret = SAFE_System_Recv(szCmd, szDevId, 32+1);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "SAFE_System_Recv failed!\n");
        return SV_FAILURE;
    }

    for (i = 0; i < 32; i++)
    {
        szDevId[i] = (char)toupper(szDevId[i]);
    }
    //print_level(SV_INFO, "szDevId:%s\n", szDevId);

    memset(szBuf, 0x00, 4096);
    strcpy(szBuf, szDevId);
    strcat(szBuf, COM_STONKAM);
    memset(u8Digest, 0x00, 16);
    sprintf(szCmd, "echo -n \"%s\" | md5sum", szBuf);
    s32Ret = SAFE_System_Recv(szCmd, szKeyAuthPwd3, 16);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "SAFE_System_Recv failed!\n");
        return SV_FAILURE;
    }

    for (i = 0; i < 8; i++)
    {
        szKeyAuthPwd3[i] = (char)toupper(szKeyAuthPwd3[i]);
    }
    memset(&szKeyAuthPwd3[8], 0x00, 16 - 8);
    //print_level(SV_INFO, "szKeyAuthPwd3:%s\n", szKeyAuthPwd3);

    /* 保存文件 */
    memset(szBuf, 0x00, 4096);
    sprintf(szBuf, "{\"deviceId\":\"%s\"}", szDevId);
    remove(pszSrcFile);
    if((fdsrc = open(pszSrcFile, O_CREAT|O_RDWR)) < 0)
    {
        print_level(SV_ERROR, "open file:%s failed!\n", pszSrcFile);
        return SV_FAILURE;
    }

    s32Ret = write(fdsrc, szBuf, strlen(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write file:%s failed!\n", pszSrcFile);
        close(fdsrc);
        return SV_FAILURE;
    }
    close(fdsrc);

    sprintf(szPwd, "%s%s%s%s", KEY_AUTH_PWD1, KEY_AUTH_PWD2, szKeyAuthPwd3, COM_STONKAM);
    sprintf(szCmd, "openssl enc -aes-256-cbc -md md5 -in %s -e -a -out %s -pass pass:%s", pszSrcFile, pszDesFile, szPwd);
    //s32Ret = SAFE_System(szCmd, 60000);
    s32Ret = SAFE_System_Not_Print(szCmd, 60000);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cmd: failed.\n");
        return SV_FAILURE;
    }

    remove(pszSrcFile);
    sprintf(szCmd, "mv %s %s", pszDesFile, "/etc/license2.txt");
    s32Ret = SAFE_System_Not_Print(szCmd, 60000);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cmd: failed.\n");
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 清理模型
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateClearModel()
{
#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
    sint32 s32Ret;
    char szFilePath[128];
    char szCmd[128];
    const char *pModelPath = "/root/model";
    // 3327版本之前的程序是放在/root下面的,3327版本之后的程序是放在 /root/model/下面
    // 为了兼容两种版本的升级,autoUpdate主动删除模型文件,防止flash
    sprintf(szFilePath, "%s/%s", UNTAR_DIR_PATH, pModelPath);
    if (0 != access(szFilePath, F_OK))
    {
        sprintf(szCmd, "rm -r %s", pModelPath);
        s32Ret = SAFE_System(szCmd, 1000*5);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "SAFE_System %s failed!\n", szCmd);
        }
    }
#endif
    return SV_SUCCESS;
}


#ifdef PLATFORM_RV1126
/******************************************************************************
 * 函数功能: 检查设备UUID, 如果没有UUID则生成一个UUID
 * 输入参数: 无
 * 输出参数: pszUuid --- 设备UUID
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 checkUuid()
{
    sint32 s32Fd = -1;
    sint32 s32Ret = -1;
    uuid_t au8Uuid;

    char szCmd[64] = {0};
    char szUuid[128] = {0};

    s32Fd = open(CONFIG_UUID, O_RDONLY);
    if (s32Fd > 0)
    {
        close(s32Fd);
    }
    else
    {
        s32Fd = open(CONFIG_UUID, O_CREAT|O_RDWR, 0644);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "create file: %s failed. [err: %s]\n", CONFIG_UUID, strerror(errno));
            return SV_FAILURE;
        }
        uuid_generate(au8Uuid);
        uuid_unparse(au8Uuid, szUuid);
        write(s32Fd, szUuid, strlen(szUuid));
        fsync(s32Fd);
        close(s32Fd);
        print_level(SV_INFO, "generate uuid: %s\n", szUuid);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 同步RK旧工程的配置参数到HD900工程
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 updateSyncOldParams(char *pktName)
{
#if defined(BOARD_ADA46V1)
    return SV_SUCCESS;
#endif

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2)|| defined(BOARD_ADA32IR) || defined(BOARD_ADA32C4)  || defined(BOARD_ADA32V4))
    sint32 s32Ret = 0, i, j;
    char szCmd[128] = {0};
    char szFilePath[128] = {0};
    char szSystemInfo[128] = {0};
    char szIpaddr[128] = {0};
    char szGateway[128] = {0};
    char szWifiPwd[128] = {0};
    char *pszLanguage = NULL;
    long nAlarmVol = -1;        // 报警音量
    long nAvAlarmerVol = -1;    // 声光报警器音量
    long nGuiOnoff = -1;    // OSD叠加到VO
    long nOsdOnoff = -1;    // OSD叠加到视频流
    long nSoundType = -1;
    long nGpstimeOnoff = -1;
    long nTzHour = -1;
    long nTzMin = -1;
    long nMirrorOnoff = -1;
    long nFlipOnoff = -1;
    long nRecType = -1;
    char *pszDisplayFormat = NULL;
    char *pszCanHeartId = NULL;
    char *pszCanFrsId = NULL;
    char *pszCanDmmId = NULL;
    char *pszCanPdsId = NULL;
    long nCaliState = -1;
    long nCaliCenter = -1;
    long nCaliPitch = -1;
    long nDmmFatigue = -1;
    long nDmmDistraction = -1;
    long nDmmNodriver = -1;
    long nDmmSmoke = -1;
    long nDmmPhone = -1;
    long nDmmYawn = -1;
    long nDmmSunglass = -1;
    long nDmmNomask = -1;
    long nDmmNoseatbelt = -1;
    long nDmmFatiguel2 = -1;
    long nDmmWorkspeed = -1;
    long nDmmSensitivity = -1;
    long nDmmEyelidClosure = -1;
    long nDmmFatigueTimelimit = -1;
    long nDmmDistractionAngle = -1;
    long nDmmDistractionTimelimit = -1;
    long nDmmNodriverTimelimit = -1;
    long nDmmSmokeThreshold = -1;
    long nDmmSmokeTimelimit = -1;
    long nDmmPhoneThreshold = -1;
    long nDmmPhoneTimelimit = -1;
    long nDmmSeatbeltThreshold = -1;
    long nDmmSeatbeltTimelimit = -1;
    long nDmmNogpsWarning = -1;
    dictionary* pstDicSystem = NULL;
    dictionary* pstDicMirror = NULL;
    dictionary* pstDicRecfile = NULL;
    dictionary* pstDicDisplay = NULL;
    dictionary* pstDicCanid = NULL;
    dictionary* pstDicCalibration = NULL;
    dictionary* pstDicAlgSetting = NULL;
    CFG_NETWORK_PARAM stNetworkParam = {0};
    CFG_SYS_PARAM stSysParam = {0};
    CFG_MEDIA_PARAM stMediaParam = {0};
    CFG_ALG_PARAM stAlgParam = {0};

    sprintf(szFilePath, "%s/recovery_backup", UPDATE_MOUNT_PATH);
    if (update_IsPathExist(szFilePath) &&
        ((NULL != strstr(pktName, "recovery")) || (NULL != strstr(pktName, "full")))
    )
    {
        sprintf(szFilePath, "%s/recovery_backup/DeviceUid", UPDATE_MOUNT_PATH);
        if (update_IsPathExist(szFilePath))
        {
            sprintf(szCmd, "cp %s %s", szFilePath, CONFIG_SERAILNUM);
            system_ex(szCmd, 10);
            print_level(SV_INFO, "get deviceUid, copy to serialNum.\n");
        }

        sprintf(szFilePath, "%s/recovery_backup/System_info", UPDATE_MOUNT_PATH);
        if (update_IsPathExist(szFilePath))
        {
            sprintf(szCmd, "cat %s | awk -F '=' 'NR==1 {print$2}' 2>1", szFilePath);
            system_ex_recv(szCmd, szSystemInfo, 128);
            cutLineBreak(szSystemInfo);
            print_level(SV_INFO, "szSystemInfo:%s\n", szSystemInfo);
            if (0 == strcmp(szSystemInfo, "ADA32V2-20211122.3920"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_C_ADA32V2_100346\n");
                s32Ret = setSpecialVersion("ADA32-H-2.3", BOARD_C_ADA32V2_100346, "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "ADA32V2-20211108.3868"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_C_ADA32V2_200930\n");
                s32Ret = setSpecialVersion("ADA32-H-2.3", BOARD_C_ADA32V2_200930, "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "ADA32V2-20210721.3619")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20210728.3627")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20210918.3749")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20211011.3790")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20211101.3851")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20211215.3980")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20220424.3980")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20220430.3980"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_ADA32V2_HW\n");
                s32Ret = setSpecialVersion("ADA32-H-2.3", "", "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "ADA32V2-20211117.3906")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20211124.3930"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_C_ADA32V2_LUIS\n");
                s32Ret = setSpecialVersion("ADA43-H-2.3", BOARD_C_ADA32V2_LUIS, "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "ADA32V2-20210820.3683")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20211201.3951")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20220430.3951"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_ADA32V2_VN\n");
                s32Ret = setSpecialVersion("ADA32-H-6.0", "", "", "", "");
            }
            else if(0 == strcmp(szSystemInfo, "ADA37V2-20210622.3524")
                    || 0 == strcmp(szSystemInfo, "ADA37V2-20210622.3524")
                    || 0 == strcmp(szSystemInfo, "ADA37V2-20210701.3564")
                    || 0 == strcmp(szSystemInfo, "ADA37V2-20211103.3856")
                    || 0 == strcmp(szSystemInfo, "ADA37V2-20211111.3884")
                    || 0 == strcmp(szSystemInfo, "ADA37V2-20220430.3884"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_ADA32V2_OW\n");
                s32Ret = setSpecialVersion("ADA37-H-1.45", "", "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "ADA32V2-20211020.3819"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_ADA32V2_HW_VT\n");
                s32Ret = setSpecialVersion("ADA32-H-2.3", BOARD_C_ADA32V2_VT, "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "ADA32V2-20211119.3915")
                    || 0 == strcmp(szSystemInfo, "ADA32V2-20211124.3926"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_ADA32V2_HW_WXKY\n");
                s32Ret = setSpecialVersion("ADA32-H-2.3", BOARD_C_ADA32V2_WXKY, "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "DMS31V2-20211110.3883")
                    || 0 == strcmp(szSystemInfo, "DMS31V2-20211118.3911"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_DMS31V2_ACR\n");
                s32Ret = setSpecialVersion("DMS31W", BOARD_C_DMS31V2_ACR, "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "DMS31V2-20210714.3612")
                    || 0 == strcmp(szSystemInfo, "DMS31V2-20211115.3893")
                    || 0 == strcmp(szSystemInfo, "DMS31V2-20211117.3910")
                    || 0 == strcmp(szSystemInfo, "DMS31V2-20220301.4061"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_DMS31V2_CREARE\n");
                s32Ret = setSpecialVersion("DMS31W", BOARD_C_DMS31V2_CREARE, "", "", "");
            }
            else if (0 == strcmp(szSystemInfo, "DMS31V2-20210701.3561")
                    || 0 == strcmp(szSystemInfo, "DMS31V2-20210708.3580")
                    || 0 == strcmp(szSystemInfo, "DMS31V2-20210713.3609"))
            {
                print_level(SV_INFO, "specialVersion: BOARD_S_DMS31V2_SHIQI\n");
                s32Ret = setSpecialVersion("DMS31GW4", BOARD_C_DMS31V2_SHIQI, "", "", "");
            }

            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "setSpecialVersion failed. [err=%x]\n", s32Ret);
            }

            remove(szFilePath);
        }

        sprintf(szFilePath, "%s/recovery_backup/ip.conf", UPDATE_MOUNT_PATH);
        if (update_IsPathExist(szFilePath))
        {
            sprintf(szCmd, "cat %s | awk -F '=' 'NR==1 {print$2}' 2>1", szFilePath);
            system_ex_recv(szCmd, szIpaddr, 128);
            cutLineBreak(szIpaddr);
            sprintf(szCmd, "cat %s | awk -F '=' 'NR==2 {print$2}' 2>1", szFilePath);
            system_ex_recv(szCmd, szGateway, 128);
            cutLineBreak(szGateway);
            print_level(SV_INFO, "szIpaddr:%s, szGateway:%s\n", szIpaddr, szGateway);
            remove(szFilePath);
        }

        sprintf(szFilePath, "%s/recovery_backup/rtl_hostapd.conf", UPDATE_MOUNT_PATH);
        if (update_IsPathExist(szFilePath))
        {
            sprintf(szCmd, "cat %s | awk -F '=' 'NR==6 {print$2}' 2>1", szFilePath);
            system_ex_recv(szCmd, szWifiPwd, 128);
            cutLineBreak(szWifiPwd);
            print_level(SV_INFO, "szWifiPwd:%s\n", szWifiPwd);
            remove(szFilePath);
        }

        s32Ret = checkUuid();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "check uuid failed.\n");
        }

        s32Ret = CONFIG_GetNetworkParam(&stNetworkParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "CONFIG_GetNetworkParam failed. [err=%#x]\n", s32Ret);
        }
        else
        {
            if (0 != strlen(szIpaddr))
            {
                stNetworkParam.pszEthIpAddr = szIpaddr;
            }

            if (0 != strlen(szGateway))
            {
                stNetworkParam.pszEthGateway = szGateway;
            }

            if (0 != strlen(szWifiPwd))
            {
                stNetworkParam.pszWifiApPwd = szWifiPwd;
            }

            s32Ret = CONFIG_SetNetworkParam(&stNetworkParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "CONFIG_SetNetworkParam failed. [err=%#x]\n", s32Ret);
            }
        }

        s32Ret = CONFIG_GetSystemParam(&stSysParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "CONFIG_GetSystemParam failed. [err=%#x]\n", s32Ret);
        }

        s32Ret = CONFIG_GetMediaParam(&stMediaParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "CONFIG_GetMediaParam failed. [err=%#x]\n", s32Ret);
        }

        CONFIG_GetAlgParam(&stAlgParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "CONFIG_GetAlgParam failed. [err=%#x]\n", s32Ret);
        }

        sprintf(szFilePath, "%s/recovery_backup/params", UPDATE_MOUNT_PATH);
        if (update_IsPathExist(szFilePath))
        {
            sprintf(szFilePath, "%s/recovery_backup/params/systemsetting.ini", UPDATE_MOUNT_PATH);
            if (update_IsPathExist(szFilePath))
            {
                pstDicSystem = iniparser_load(szFilePath);
                if (NULL == pstDicSystem)
                {
                    print_level(SV_ERROR, "iniparser_load %s failed.\n", szFilePath);
                }
                else
                {
                    pszLanguage = iniparser_getstring(pstDicSystem, "setting:sv_str_language", "en");
                    nAlarmVol = iniparser_getlongint(pstDicSystem, "setting:sv_int_vol", 5);
                    nAvAlarmerVol = iniparser_getlongint(pstDicSystem, "setting:sv_int_alarmervol", 5);
                    nGuiOnoff = iniparser_getlongint(pstDicSystem, "setting:sv_int_gui_onoff", 1);
                    nOsdOnoff = iniparser_getlongint(pstDicSystem, "setting:sv_int_osd_onoff", 0);
                    nSoundType = iniparser_getlongint(pstDicSystem, "setting:sv_int_sound_type", 0);
                    nGpstimeOnoff = iniparser_getlongint(pstDicSystem, "setting:sv_int_gpstime_onoff", 1);
                    nTzHour = iniparser_getlongint(pstDicSystem, "setting:sv_int_tz_hour", 8);
                    nTzMin = iniparser_getlongint(pstDicSystem, "setting:sv_int_tz_min", 0);
                    print_level(SV_INFO, "language:%s, nGuiOnoff:%d, nOsdOnoff:%d, nSoundType:%d, nGpstimeOnoff:%d, nTzHour:%d, nTzMin:%d\n", \
                                                        pszLanguage, nGuiOnoff, nOsdOnoff, nSoundType, nGpstimeOnoff, nTzHour, nTzMin);
                    if (0 == strcmp(pszLanguage, "en"))
                    {
                        stSysParam.enLang = LANG_EN;
                    }
                    else if (0 == strcmp(pszLanguage, "cn"))
                    {
                        stSysParam.enLang = LANG_CN;
                    }
                    else if (0 == strcmp(pszLanguage, "jp"))
                    {
                        stSysParam.enLang = LANG_JP;
                    }
                    else if (0 == strcmp(pszLanguage, "es"))
                    {
                        stSysParam.enLang = LANG_ES;
                    }
                    else if (0 == strcmp(pszLanguage, "pt"))
                    {
                        stSysParam.enLang = LANG_PT;
                    }
                    else if (0 == strcmp(pszLanguage, "ru"))
                    {
                        stSysParam.enLang = LANG_RU;
                    }

                    if (nGuiOnoff >= 0)
                    {
                        if (nGuiOnoff)
                        {
                            stMediaParam.astChnParam[0].bShowGuiMask |= 0b1000;
                        }
                        else
                        {
                            stMediaParam.astChnParam[0].bShowGuiMask &= 0b0111;
                        }
                    }
                    if (nOsdOnoff >= 0)
                    {
                        if (nOsdOnoff)
                        {
                            stMediaParam.astChnParam[0].bShowGuiMask |= 0b0011;
                        }
                        else
                        {
                            stMediaParam.astChnParam[0].bShowGuiMask &= 0b1100;
                        }
                    }

                    stAlgParam.s32AudioVolume = (nAlarmVol >= 0) ? nAlarmVol : stAlgParam.s32AudioVolume;
                    stAlgParam.s32PdsAlarmerVolume = (nAvAlarmerVol >= 0) ? nAvAlarmerVol : stAlgParam.s32PdsAlarmerVolume;
                    stAlgParam.s32PdsLedBrightness = (nAvAlarmerVol >= 0) ? nAvAlarmerVol : stAlgParam.s32PdsLedBrightness;
                    stAlgParam.stAlgCh2.stPdsParam.enAudioType = (nSoundType >= 0) ? nSoundType : stAlgParam.stAlgCh2.stPdsParam.enAudioType;
                    stAlgParam.stAlgCh2.stDmsParam.enAudioType = (nSoundType >= 0) ? nSoundType : stAlgParam.stAlgCh2.stDmsParam.enAudioType;
                    stSysParam.bEnableGPStime = (nGpstimeOnoff >= 0) ? nGpstimeOnoff : stSysParam.bEnableGPStime;
                    stSysParam.s32UTChour = (nTzHour >= 0) ? nTzHour : stSysParam.s32UTChour;
                    stSysParam.s32UTCminute = (nTzMin >= 0) ? nTzMin : stSysParam.s32UTCminute;
                }

                remove(szFilePath);
            }
#if 0
            sprintf(szFilePath, "%s/recovery_backup/params/set_mirror_rotation.ini", UPDATE_MOUNT_PATH);
            if (update_IsPathExist(szFilePath))
            {
                pstDicMirror = iniparser_load(szFilePath);
                if (NULL == pstDicMirror)
                {
                    print_level(SV_ERROR, "iniparser_load %s failed.\n", szFilePath);
                }
                else
                {
                    nMirrorOnoff = iniparser_getlongint(pstDicMirror, "mirrorrotationparam:sv_int_vpss_mirror", -1);
                    nFlipOnoff = iniparser_getlongint(pstDicMirror, "mirrorrotationparam:sv_int_vpss_flip", -1);
                    print_level(SV_INFO, "nMirrorOnoff:%d, nFlipOnoff:%d\n", nMirrorOnoff, nFlipOnoff);
                    stMediaParam.bImageMirror = (nMirrorOnoff >= 0) ? nMirrorOnoff : stMediaParam.bImageMirror;
                    stMediaParam.bImageFlip = (nFlipOnoff >= 0) ? nFlipOnoff : stMediaParam.bImageFlip;
                }

                remove(szFilePath);
            }
#endif
            sprintf(szFilePath, "%s/recovery_backup/params/recfile.ini", UPDATE_MOUNT_PATH);
            if (update_IsPathExist(szFilePath))
            {
                pstDicRecfile = iniparser_load(szFilePath);
                if (NULL == pstDicRecfile)
                {
                    print_level(SV_ERROR, "iniparser_load %s failed.\n", szFilePath);
                }
                else
                {
                    nRecType = iniparser_getlongint(pstDicRecfile, "recfile:sv_int_rectype", -1);
                    print_level(SV_INFO, "nRecType:%d\n", nRecType);
                    stSysParam.enNormalRecord = (nRecType == 1) ? ((stSysParam.enNormalRecord > E_REC_NOR_OFF) ? stSysParam.enNormalRecord : E_REC_NOR_DIS) : stSysParam.enNormalRecord;
                    stSysParam.enAlarmRecord = (nRecType == 0) ? ((stSysParam.enAlarmRecord > E_REC_OFF) ? stSysParam.enAlarmRecord : E_REC_VIDEO_PIC) : stSysParam.enAlarmRecord;
                }

                remove(szFilePath);
            }

            sprintf(szFilePath, "%s/recovery_backup/params/display.ini", UPDATE_MOUNT_PATH);
            if (update_IsPathExist(szFilePath))
            {
                pstDicDisplay = iniparser_load(szFilePath);
                if (NULL == pstDicDisplay)
                {
                    print_level(SV_ERROR, "iniparser_load %s failed.\n", szFilePath);
                }
                else
                {
                    pszDisplayFormat = iniparser_getstring(pstDicDisplay, "dispparm:sv_str_disptype", "unfound");
                    print_level(SV_INFO, "pszDisplayFormat:%s\n", pszDisplayFormat);
                    if (NULL != strstr(pszDisplayFormat, "720P"))
                    {
                        stMediaParam.enVoResolution = RES_720P;
                    }
                    else if (NULL != strstr(pszDisplayFormat, "1080P"))
                    {
                        stMediaParam.enVoResolution = RES_1080P;
                    }

                    if (NULL != strstr(pszDisplayFormat, "25"))
                    {
                        stMediaParam.u32VoFramerate = 25;
                    }
                    else if (NULL != strstr(pszDisplayFormat, "30"))
                    {
                        stMediaParam.u32VoFramerate = 30;
                    }
                }

                remove(szFilePath);
            }

            sprintf(szFilePath, "%s/recovery_backup/params/can_id.ini", UPDATE_MOUNT_PATH);
            if (update_IsPathExist(szFilePath))
            {
                pstDicCanid = iniparser_load(szFilePath);
                if (NULL == pstDicCanid)
                {
                    print_level(SV_ERROR, "iniparser_load %s failed.\n", szFilePath);
                }
                else
                {
                    pszCanHeartId = iniparser_getstring(pstDicCanid, "canid:sv_str_can_heart_id", "0x18fad5fe");
                    pszCanFrsId = iniparser_getstring(pstDicCanid, "canid:sv_str_can_frs_id", "0x18fad0fe");
                    pszCanDmmId = iniparser_getstring(pstDicCanid, "canid:sv_str_can_dmm_id", "0x18fad1fe");
                    pszCanPdsId = iniparser_getstring(pstDicCanid, "canid:sv_str_can_pd_id", "0x18fad0fc");
                    print_level(SV_INFO, "pszCanHeartId:%s, pszCanFrsId:%s, pszCanDmmId:%s, pszCanPdsId:%s\n", pszCanHeartId, pszCanFrsId, pszCanDmmId, pszCanPdsId);
                    stSysParam.pszHeartCanid = (NULL != pszCanHeartId) ? pszCanHeartId : stSysParam.pszHeartCanid;
                    stSysParam.pszFrsCanid = (NULL != pszCanFrsId) ? pszCanFrsId : stSysParam.pszFrsCanid;
                    stSysParam.pszDmmCanid = (NULL != pszCanDmmId) ? pszCanDmmId : stSysParam.pszDmmCanid;
                    stSysParam.pszPdsCanid = (NULL != pszCanPdsId) ? pszCanDmmId : stSysParam.pszPdsCanid;
                    stSysParam.pszApcCanid = (NULL != pszCanPdsId) ? pszCanDmmId : stSysParam.pszApcCanid;
                }

                remove(szFilePath);
            }

            sprintf(szFilePath, "%s/recovery_backup/params/calibration.ini", UPDATE_MOUNT_PATH);
            if (update_IsPathExist(szFilePath))
            {
                pstDicCalibration = iniparser_load(szFilePath);
                if (NULL == pstDicCalibration)
                {
                    print_level(SV_ERROR, "iniparser_load %s failed.\n", szFilePath);
                }
                else
                {
                    nCaliState = iniparser_getlongint(pstDicCalibration, "calibration:sv_int_state", -1);
                    nCaliCenter = iniparser_getlongint(pstDicCalibration, "calibration:sv_int_center", -1);
                    nCaliPitch = iniparser_getlongint(pstDicCalibration, "calibration:sv_int_pitch", -1);
                    print_level(SV_INFO, "nCaliState:%d, nCaliCenter:%d, nCaliPitch:%d\n", nCaliState, nCaliCenter, nCaliPitch);
                    stAlgParam.stAlgCh2.stDmsParam.bDmsCalibrated = (nCaliState >= 0) ? nCaliState : stAlgParam.stAlgCh2.stDmsParam.bDmsCalibrated;
                    stAlgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle[1] = (nCaliCenter >= 0) ? nCaliCenter : stAlgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle[1];
                    stAlgParam.stAlgCh2.stDmsParam.ps32CalibrateGazeAngle[0] = (nCaliPitch >= 0) ? nCaliPitch : stAlgParam.stAlgCh2.stDmsParam.ps32CalibrateGazeAngle[0];
                }
#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4)  || defined(BOARD_ADA32V4))
                    float fTmp;
                    sint32 iTmp;
                    char szTmp[64];
                    char *pRoiParam, *pTmp;

                    iTmp = iniparser_getint(pstDicCalibration, "pds_roi:sv_int_state", 2);
                    switch (iTmp)
                    {
                        case 0: // 垂直左
                            stAlgParam.stAlgCh2.stPdsParam.enRoiStyle = CFG_PDROI_LEFT;
                            break;
                        case 1: // 垂直右
                            stAlgParam.stAlgCh2.stPdsParam.enRoiStyle = CFG_PDROI_RIGHT;
                            break;
                        case 2: // 水平梯形
                        default:
                            stAlgParam.stAlgCh2.stPdsParam.enRoiStyle = CFG_PDROI_BOTTOM;
                            break;
                    }


                    /* 标定点 */
                    for(i = 0; i < 2; i++)
                    {
                        for(j = 0; j < 4; j++)
                        {
                            sprintf(szTmp, "pds_roi:sv_dbl_point%d%d_x", i+1, j+1);
                            fTmp = iniparser_getdouble(pstDicCalibration, szTmp, -1);
                            if(fTmp > 0)
                            {
                                stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[4*i+j].dX = fTmp;
                            }

                            sprintf(szTmp, "pds_roi:sv_dbl_point%d%d_y", i+1, j+1);
                            fTmp = iniparser_getdouble(pstDicCalibration, szTmp, -1);
                            if(fTmp > 0)
                            {
                                stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[4*i+j].dY = fTmp;
                            }
                        }
                    }
#endif
                remove(szFilePath);
            }

            sprintf(szFilePath, "%s/recovery_backup/params/algsetting.ini", UPDATE_MOUNT_PATH);
            if (update_IsPathExist(szFilePath))
            {
                pstDicAlgSetting = iniparser_load(szFilePath);
                if (NULL == pstDicAlgSetting)
                {
                    print_level(SV_ERROR, "iniparser_load %s failed.\n", szFilePath);
                }
                else
                {
                    nDmmFatigue = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_fatigue_warning", -1);
                    nDmmDistraction = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_distraction_warning", -1);
                    nDmmNodriver = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_nodriver_warning", -1);
                    nDmmSmoke = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_smoke_warning", -1);
                    nDmmPhone = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_phone_warning", -1);
                    nDmmYawn = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_yawn_warning", -1);
                    nDmmSunglass = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_sunglass_warning", -1);
                    nDmmNomask = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_nomask_warning", -1);
                    nDmmNoseatbelt = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_noseatbelt_warning", -1);
                    nDmmFatiguel2 = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_fatiguel2_warning", -1);
                    nDmmWorkspeed = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_workspeed", -1);
                    nDmmSensitivity = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_detection_sensitivity", -1);
                    print_level(SV_INFO, "nDmmFatigue:%d, nDmmDistraction:%d, nDmmNodriver:%d, nDmmSmoke:%d, nDmmPhone:%d, nDmmYawn:%d, nDmmSunglass:%d, nDmmNomask:%d, nDmmNoseatbelt:%d, nDmmFatiguel2:%d, nDmmWorkspeed:%d, nDmmSensitivity:%d\n", \
                        nDmmFatigue, nDmmDistraction, nDmmNodriver, nDmmSmoke, nDmmPhone, nDmmYawn, nDmmSunglass, nDmmNomask, nDmmNoseatbelt, nDmmFatiguel2, nDmmWorkspeed, nDmmSensitivity);

                    nDmmEyelidClosure = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_eyelid_closure", -1);
                    nDmmFatigueTimelimit = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_fatigue_timelimit", -1);
                    nDmmDistractionAngle = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_distraction_angle", -1);
                    nDmmDistractionTimelimit = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_distraction_timelimit", -1);
                    nDmmNodriverTimelimit = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_nodriver_timelimit", -1);
                    nDmmSmokeThreshold = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_smoke_threshold", -1);
                    nDmmSmokeTimelimit = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_smoke_timelimit", -1);
                    nDmmPhoneThreshold = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_phone_threshold", -1);
                    nDmmPhoneTimelimit = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_phone_timelimit", -1);
                    nDmmSeatbeltThreshold = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_seatbelt_threshold", -1);
                    nDmmSeatbeltTimelimit = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_seatbelt_timelimit", -1);
                    nDmmNogpsWarning = iniparser_getlongint(pstDicAlgSetting, "dmm:sv_int_nogps_warning", -1);
                    print_level(SV_INFO, "nDmmEyelidClosure:%d, nDmmFatigueTimelimit:%d, nDmmDistractionAngle:%d, nDmmDistractionTimelimit:%d, nDmmNodriverTimelimit:%d, nDmmSmokeThreshold:%d, nDmmSmokeTimelimit:%d, nDmmPhoneThreshold:%d, nDmmPhoneTimelimit:%d, nDmmSeatbeltThreshold:%d, nDmmSeatbeltTimelimit:%d, nDmmNogpsWarning:%d\n", \
                        nDmmEyelidClosure, nDmmFatigueTimelimit, nDmmDistractionAngle, nDmmDistractionTimelimit, nDmmNodriverTimelimit, nDmmSmokeThreshold, nDmmSmokeTimelimit, nDmmPhoneThreshold, nDmmPhoneTimelimit, nDmmSeatbeltThreshold, nDmmSeatbeltTimelimit, nDmmNogpsWarning);

                    stAlgParam.stAlgCh2.stDmsParam.s32FatigueInterval = (nDmmFatigue >= 0) ? (nDmmFatigue <= 1 ? nDmmFatigue-1 : nDmmFatigue) : stAlgParam.stAlgCh2.stDmsParam.s32FatigueInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32DistractionInterval = (nDmmDistraction >= 0) ? (nDmmDistraction <= 1 ? nDmmDistraction-1 : nDmmDistraction) : stAlgParam.stAlgCh2.stDmsParam.s32DistractionInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32NoDriverInterval = (nDmmNodriver >= 0) ? (nDmmNodriver <= 1 ? nDmmNodriver-1 : nDmmNodriver) : stAlgParam.stAlgCh2.stDmsParam.s32NoDriverInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32SmokeInterval = (nDmmSmoke >= 0) ? (nDmmSmoke <= 1 ? nDmmSmoke-1 : nDmmSmoke) : stAlgParam.stAlgCh2.stDmsParam.s32SmokeInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32PhoneInterval = (nDmmPhone >= 0) ? (nDmmPhone <= 1 ? nDmmPhone-1 : nDmmPhone) : stAlgParam.stAlgCh2.stDmsParam.s32PhoneInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32YawnInterval = (nDmmYawn >= 0) ? (nDmmYawn <= 1 ? nDmmYawn-1 : nDmmYawn) : stAlgParam.stAlgCh2.stDmsParam.s32YawnInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32NoMaskInterval = (nDmmNomask >= 0) ? (nDmmNomask <= 1 ? nDmmNomask-1 : nDmmNomask) : stAlgParam.stAlgCh2.stDmsParam.s32NoMaskInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32SunGlassInterval = (nDmmSunglass >= 0) ? (nDmmSunglass <= 1 ? nDmmSunglass-1 : nDmmSunglass) : stAlgParam.stAlgCh2.stDmsParam.s32SunGlassInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32SeatBeltInterval = (nDmmNoseatbelt >= 0) ? (nDmmNoseatbelt <= 1 ? nDmmNoseatbelt-1 : nDmmNoseatbelt) : stAlgParam.stAlgCh2.stDmsParam.s32SeatBeltInterval;
                    //stAlgParam.stAlgCh2.stDmsParam.s32ShelterInterval = (nDmmFatigue >= 0) ? (nDmmFatigue <= 1 ? nDmmFatigue-1 : nDmmFatigue) : stAlgParam.stAlgCh2.stDmsParam.s32ShelterInterval;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsSensitivity = (nDmmSensitivity >= 0) ? nDmmSensitivity : stAlgParam.stAlgCh2.stDmsParam.s32DmsSensitivity;
                    //stAlgParam.stAlgCh2.stDmsParam.s32DmsMiddleSpeedThr = (nDmmWorkspeed >= 0) ? nDmmWorkspeed : stAlgParam.stAlgCh2.stDmsParam.s32DmsMiddleSpeedThr;
                    //stAlgParam.stAlgCh2.stDmsParam.s32DmsHighSpeedThr = (nDmmFatigue >= 0) ? nDmmFatigue : stAlgParam.stAlgCh2.stDmsParam.s32DmsHighSpeedThr;
                    stAlgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS = (nDmmNogpsWarning >= 0) ? nDmmNogpsWarning : stAlgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[0] = (nDmmWorkspeed >= 0) ? nDmmWorkspeed : stAlgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[0];
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsEyelidClosure = (nDmmEyelidClosure >= 0) ? nDmmEyelidClosure : stAlgParam.stAlgCh2.stDmsParam.s32DmsEyelidClosure;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsFatigueTimelimit = (nDmmFatigueTimelimit >= 0) ? nDmmFatigueTimelimit : stAlgParam.stAlgCh2.stDmsParam.s32DmsFatigueTimelimit;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleLeft = (nDmmDistractionAngle >= 0) ? nDmmDistractionAngle : stAlgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleLeft;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleRight = (nDmmDistractionAngle >= 0) ? nDmmDistractionAngle : stAlgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleRight;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsDistractionTimelimit = (nDmmDistractionTimelimit >= 0) ? nDmmDistractionTimelimit : stAlgParam.stAlgCh2.stDmsParam.s32DmsDistractionTimelimit;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsNodriverTimelimit = (nDmmNodriverTimelimit >= 0) ? nDmmNodriverTimelimit : stAlgParam.stAlgCh2.stDmsParam.s32DmsNodriverTimelimit;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsSmokeThreshold = (nDmmSmokeThreshold >= 0) ? nDmmSmokeThreshold : stAlgParam.stAlgCh2.stDmsParam.s32DmsSmokeThreshold;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsSmokeTimelimit = (nDmmSmokeTimelimit >= 0) ? nDmmSmokeTimelimit : stAlgParam.stAlgCh2.stDmsParam.s32DmsSmokeTimelimit;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsPhoneThreshold = (nDmmPhoneThreshold >= 0) ? nDmmPhoneThreshold : stAlgParam.stAlgCh2.stDmsParam.s32DmsPhoneThreshold;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsPhoneTimelimit = (nDmmSmokeTimelimit >= 0) ? nDmmSmokeTimelimit : stAlgParam.stAlgCh2.stDmsParam.s32DmsPhoneTimelimit;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsSeatBeltThreshold = (nDmmSeatbeltThreshold >= 0) ? nDmmSeatbeltThreshold : stAlgParam.stAlgCh2.stDmsParam.s32DmsSeatBeltThreshold;
                    stAlgParam.stAlgCh2.stDmsParam.s32DmsSeatBeltTimelimit = (nDmmSeatbeltTimelimit >= 0) ? nDmmSeatbeltTimelimit : stAlgParam.stAlgCh2.stDmsParam.s32DmsSeatBeltTimelimit;
                }

                remove(szFilePath);
            }

            s32Ret = CONFIG_SetSystemParam(&stSysParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "CONFIG_SetSystemParam failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = CONFIG_SetMediaParam(&stMediaParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "CONFIG_SetMediaParam failed. [err=%#x]\n", s32Ret);
            }

            CONFIG_SetAlgParam(&stAlgParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "CONFIG_SetAlgParam failed. [err=%#x]\n", s32Ret);
            }
        }

#if (defined(BOARD_DMS31V2))
        sprintf(szFilePath, "%s/recovery_backup/ID", UPDATE_MOUNT_PATH);
        if (update_IsPathExist(szFilePath))
        {
            sprintf(szCmd, "cp -rf %s /root/", szFilePath);
            system_ex(szCmd, 60);

            sprintf(szCmd, "rm -rf %s", szFilePath);
            system_ex(szCmd, 60);
        }
#endif
        /* 旧工程添加密钥 */
        if (BOARD_IsNeedKeyAuth())
        {
            s32Ret = generateKeyAuth();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "generateKeyAuth failed.\n");
                remove(KEY_AUTH_FLAG);
                remove(KEY_AUTH_FLAG_BRIGADE);
            }
        }
        /* 删除备份文件 */
        sprintf(szCmd, "rm -rf %s/recovery_backup", UPDATE_MOUNT_PATH);
        system_ex(szCmd, 60);
    }

#endif
    return SV_SUCCESS;
}
#endif

/* 中断退出 */
static void exit_handle(int signalnum)
{
    printf("catch signalnum %d!\n", signalnum);
    exit(EXIT_FAILURE);
}


/******************************************************************************
 * 函数功能: autoUpdate主函数
 * 输入参数: 参数1：update，表示此时是在线升级
             参数2：缓存升级包的路径，目前用于在线升级，为内存的/tmp
 * 输出参数: 无
 * 返回值  : shell返回值
             return 10：表示此时为自动化测试
             return 11：表示此时为在线升级，跑完upgrade_start.sh后需要重跑autoUpdae
 * 注意    : 无
 *****************************************************************************/
int ipsys_log_level = SV_DEBUG;
int main(int argc, char **argv)
{
    sint32 s32Ret = 0, i;
    sint32 s32DiffFlag = 0, s32StorageBlockNum = 0;
    char szPartionPath[64] = {0};
    char szPacketPath[128] = {0};
    char szFilePath[64] = {0};
    char szMcuPktPath[128] = {0};
    char szCmd[64] = {0};
    char szRecv[128] = {0};
    char szFirmwareVersion[64] = {0};
    char *pszBootPath = NULL;
    PACKET_INFO_S stPacketInfo = {0};
    CFG_DEV_INFO stDevInfo = {0};
    CFG_MEDIA_PARAM stMediaParam = {0};
    CFG_SER_PARAM stSerParam = {0};
    char szLogoCmd[1024];
    SV_BOOL bUpgradeOnline = SV_FALSE;
    SV_BOOL bUpgradeOnlineSkipPack = SV_FALSE;
    MCUUPDATE_INFO_S stMcuUpdateInfo = {0};

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) /* || defined(BOARD_IPCR20S4)*/ || defined(PLATFORM_RV1126B))
	m_stUpdateInfo.bNeedCheckSD = SV_TRUE;
#else
	m_stUpdateInfo.bNeedCheckSD = SV_FALSE;
#endif

#if defined(PLATFORM_RV1106)
    m_stUpdateInfo.u32LogoWidth = 1280;
    m_stUpdateInfo.u32LogoHeight= 720;
#else
    m_stUpdateInfo.u32LogoWidth = 1920;
    m_stUpdateInfo.u32LogoHeight= 1280;
#endif
    m_stUpdateInfo.u32LogoFps = 30;
    m_stUpdateInfo.enCvbs = CVBS_NTSC;


/* 提前复位使能USB */
#if defined(BOARD_DMS31V2)
    /* uboot中添加了对3.3V和5V上电的操作（提早更多上电），如果uboot中已经上电这里就不需要再上电 */
    do
    {
        strcpy(szCmd, "fw_printenv -n gpioInit");
        s32Ret = SAFE_System_Recv(szCmd, szRecv, 128);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
        }
        COMMON_CutLineBreak(szRecv);

        if (NULL == strstr(szRecv, "Error") && 0 == strcmp(szRecv, "y"))
        {
            strcpy(szCmd, "fw_setenv gpioInit n");
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
            }
        }
        else
        {
            system_ex("/root/gpio.sh 2 25 1", 10);
            system_ex("/root/gpio.sh 2 22 1", 10);
        }
    }while (0);

#elif defined(BOARD_ADA32V3)
    system_ex("echo -n \"xhci-hcd.0.auto\" > /sys/bus/platform/drivers/xhci-hcd/unbind && sleep 0.5", 10);
    system_ex("echo -n \"xhci-hcd.0.auto\" > /sys/bus/platform/drivers/xhci-hcd/bind", 10);

    sprintf(szCmd, "rm -rf %s", DECRYPTION_TARGZ_PATH);
    s32Ret = SAFE_SV_System(szCmd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        return SV_FAILURE;
    }
#endif

#if (defined(PLATFORM_RV1126))
    flashWtdReset();
#if defined(BOARD_ADA32V2)
    autoupdate_checkUserdata();
#endif
#endif
    /*捕获进程退出的系统消息*/
    if (SIG_ERR == signal(SIGINT, exit_handle))
    {
        print_level(SV_ERROR, "catch signal SIGINT Error: %s\n", strerror(errno));
    }

    s32Ret = BOARD_Init(INIT_MOD_AUTOUPDATE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_Init failed.\n");
    }

    s32Ret = updateInit();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "updateInit failed.\n");
        //goto error_exit;
    }

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
    s32Ret = updateSourceFirst();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "updateSourceFirst failed.\n");
    }
#endif

    s32Ret = CONFIG_GetDevInfo(&stDevInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetDevInfo failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    strcpy(m_stUpdateInfo.szFirmwareVer, stDevInfo.szFirmwareVer);

    s32Ret = CONFIG_GetMediaParam(&stMediaParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetMediaParam failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = CONFIG_GetServerParam(&stSerParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetServerParam failed. [err=%#x]\n", s32Ret);
    }
    stSerParam.eLogType = LOG_TYPE_UPDATE;

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
#if (!defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1))
    sprintf(szLogoCmd, "cp /bin/logodisplay /tmp/");
    system_ex(szLogoCmd, 10);
    sprintf(szLogoCmd, "/tmp/logodisplay -width=%d -height=%d -fps=%d -cvbs=%d -rotate=%d &",
            m_stUpdateInfo.u32LogoWidth, m_stUpdateInfo.u32LogoHeight, m_stUpdateInfo.u32LogoFps, m_stUpdateInfo.enCvbs, m_stUpdateInfo.enRotate);
    system_ex(szLogoCmd, 10);
#endif
#if (defined(PLATFORM_RV1126))
    char recv_buffer[32] = {0};
    // 进入autoupdate 后关闭uboot升级功能
    SAFE_System_Recv("fw_printenv update", recv_buffer, 32);
    if(strstr(recv_buffer, "update=y") != NULL)
    {
        system_ex("fw_setenv update n", 60);
        system_ex("sync", 60);
    }

    clearPreFile("/boot", "kernel");         //删除残留的内核文件
    //clearPreFile("/boot", "mini_rootfs");   //删除残留的文件系统
#endif
#endif

	if (argc == 2 && atoi(argv[1]) >= SV_ERROR && atoi(argv[1]) <= SV_ALWAYS)
	{
		ipsys_log_level = atoi(argv[1]);

	}
	else if (argc >= 3 && strcmp("update", argv[1]) != 0) //argv[1]="update"表示不重启方式升级
	{
				/**************************************************************************************************
		* mcu升级测试模式使用方法
		* # ./autoUpdate 3 1
		* # ./autoUpdate 3 ./UpdateMcuPacket-ADA32-ARTERYCBB2-20220127.bin
		* argv[1]为log level, argv[2]为纯数字时为mcu升级全功能测试，为字符串时是指定的升级包路径，强制升级
		***************************************************************************************************/

		strncpy(szMcuPktPath, argv[2], 128);
		SV_BOOL bForceUpdate = SV_FALSE;
		if (atoi(argv[2]) == 0)
		{
			bForceUpdate = SV_TRUE;
		}
		else
		{
			s32Ret = catMountStatus();
			if (UPDATE_MOUNT_SUCCESS != s32Ret)
			{
				s32Ret = mount(UPDATE_BLKDEV_PATH, UPDATE_MOUNT_PATH, "vfat", 0, NULL);
    			if (0 != s32Ret)
    			{
    				perror("mount error");
    				print_level(SV_WARN, "mount %s on %s failed!\n", UPDATE_BLKDEV_PATH, UPDATE_MOUNT_PATH);
    			}
			}
		}

#if (defined(BOARD_DMS31V2) || defined(DMS31SDK) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32V4))
        memset(&stMcuUpdateInfo, 0, sizeof(MCUUPDATE_INFO_S));
        stMcuUpdateInfo.bRetryFoundMcu = m_stUpdateInfo.bRetryFoundMcu;
        stMcuUpdateInfo.bLastBlock = SV_TRUE;
        stMcuUpdateInfo.bTestMode = bForceUpdate ? SV_TRUE : SV_FALSE;
        stMcuUpdateInfo.pszPath = szMcuPktPath;
		s32Ret = UPDATEMCU_A32(&stMcuUpdateInfo);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "update a32 mcu failed.\n");
        }
update_mcu_exit:;
#endif
		if (SV_SUCCESS != updateFini())
			print_level(SV_ERROR, "updateFini failed.\n");
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
    	system_ex("sleep 10s && logocontrol -quit &", 10);
#endif
		return 0;
	}

    /* DMS31优先检测是否有DVR通过can传输过来的升级包 */
#if defined(BOARD_DMS31V2)
    s32Ret = checkCanFindPkt(szPacketPath);
    if (SV_SUCCESS != s32Ret)
        print_level(SV_INFO, "not found can upgrade packet.\n");
    else
        stSerParam.eLogType = LOG_TYPE_CAN_UPGRADE;
#endif

    s32Ret = LOG_Init(&stSerParam, SV_TRUE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "LOG_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* can升级时直接跳过挂载sd卡等操作 */
    if (LOG_TYPE_CAN_UPGRADE == stSerParam.eLogType)
    {
        print_level(SV_INFO, "found can upgrade packet, skip to find sacard.\n");
        goto start_update;
    }

    pszBootPath = "/boot";

#if defined(BOARD_ADA47V1)
    pszBootPath = "/userdata";
#endif
#if defined(BOARD_IPCR20S5)
    pszBootPath = "/customer/boot";
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32V4))
	if (argc >= 3 && strcmp("update", argv[1]) == 0)
	{
		pszBootPath = argv[2];
        bUpgradeOnline = SV_TRUE;
        print_level(SV_INFO, "----------------upgrade online----------------\n");
	}

	if (argc == 4 && strcmp("skipPack", argv[3]) == 0)
	{
        bUpgradeOnlineSkipPack = SV_TRUE;
		print_level(SV_INFO, "----------------upgrade online again----------------\n");
	}
#endif

	s32Ret = pktFindUpdatePacket(pszBootPath, szPacketPath);
	if (SV_SUCCESS != s32Ret)
	{
		if (!m_stUpdateInfo.bNeedCheckSD)
		{
            if (SV_SUCCESS != updateFini())
                print_level(SV_ERROR, "updateFini failed.\n");
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
            system_ex("sleep 10s && logocontrol -quit &", 10);
#endif
            return 0;
		}
	}
	else
	{
        goto start_update;
	}

	if (m_stUpdateInfo.bNeedCheckSD)
	{
		s32Ret = sdWaitForReady(&s32StorageBlockNum);
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_INFO, "not found storage device.\n");

#if (defined(BOARD_DMS31V2) || defined(DMS31SDK) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR)  || defined(BOARD_ADA900V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32V4) )

            //找不到存储设备时也需要先升级A32的MCU程序再退出
            memset(&stMcuUpdateInfo, 0, sizeof(MCUUPDATE_INFO_S));
            stMcuUpdateInfo.bRetryFoundMcu = m_stUpdateInfo.bRetryFoundMcu;
            stMcuUpdateInfo.bLastBlock = SV_TRUE;
            stMcuUpdateInfo.bTestMode = SV_FALSE;
            stMcuUpdateInfo.pszPath = NULL;
    		s32Ret = UPDATEMCU_A32(&stMcuUpdateInfo);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "update a32 mcu failed.\n");
            }
update_mcu_exit1:;
#endif

            if (SV_SUCCESS != updateFini())
                print_level(SV_ERROR, "updateFini failed.\n");

#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
            system_ex("sleep 10s && logocontrol -quit &", 10);
#endif
            return 0;
		}

		s32Ret = checkSdcardFindPkt(s32StorageBlockNum, szPacketPath);
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_INFO, "not found update packet.\n");
            if (SV_SUCCESS != updateFini())
                print_level(SV_ERROR, "updateFini failed.\n");
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
            system_ex("sleep 10s && logocontrol -quit &", 10);
#endif


			return m_stUpdateInfo.bAutoTestMode ? 10 : 0;
        }else
        {

#if (defined(BOARD_DMS51V1))
            if(access("/root/upgrade_flag",F_OK) == 0)

            {
                return m_stUpdateInfo.bAutoTestMode ? 10 : 0;
            
}
#endif 
        }
	}


//DMS31自动化测试同版本跳过升级
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32V4))
    if (update_IsPathExist(AUTOTEST_SCRIPTS))
	{
		snprintf(szCmd, 128, "cat %s", CONFIG_FIRMWARE);
		system_ex_recv(szCmd, szFirmwareVersion, 64);
		cutLineBreak(szFirmwareVersion);
		if (NULL != strstr(szPacketPath, szFirmwareVersion))
		{
			print_level(SV_INFO, "firmwareVersion %s is the same as packet verison, skip to update for autoTest.\n", szFirmwareVersion);
			snprintf(szCmd, 128, "cp %s /tmp", AUTOTEST_SCRIPTS);
			system_ex(szCmd, 10);
			return 10;
		}
	}
#endif


start_update:
#if (defined(BOARD_ADA32C4))
    print_level(SV_INFO, "MCU device detecting...\n");
    s32Ret = mcuUpdateInit();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mcuUpdateInit failed.\n");
        goto com_update;
    }

	s32Ret = mcuCreateAPP2();
	if (SV_SUCCESS != s32Ret)
	{
        print_level(SV_WARN, "Create APP2  failed.\n");
		goto com_update;
	}
    m_stUpdateInfo.bIsMcuExist = SV_TRUE;
#elif defined(BOARD_WFCR20S2)
    s32Ret = mcuGotoBootloaderMode();
    if (SV_SUCCESS != s32Ret)
    {
        goto com_update;
    }
    m_stUpdateInfo.bIsMcuExist = SV_TRUE;
#endif

com_update:
    #if (defined(BOARD_DMS51V1) || defined(BOARD_ADA32V3))
    sprintf(szCmd, "/root/update_cma.sh 2 \r");
    SAFE_System(szCmd, NORMAL_WAIT_TIME);
    #endif 
    
        
    s32Ret = updateStart();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "updateStart failed.\n");
        goto error_exit;
    }

    print_level(SV_INFO, "found update packet: %s\n", szPacketPath);	//在LOG_Start后打印才会写进日志文件里
    if (bUpgradeOnlineSkipPack)
    {
        print_level(SV_INFO, "upgrade online and don't unpack packet again.\n");
        goto unpack_exit;
    }

    s32Ret = pktDecryption(szPacketPath, DECRYPTION_TARGZ_PATH);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "pktDecryption failed.\n");
        goto error_exit;
    }

    /* 如果是在线升级，解完包后直接删除原先的升级包，减少内存占用 */
    if (bUpgradeOnline)
    {
        remove(szPacketPath);
        sprintf(szCmd, "touch %s", szPacketPath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
    }

    s32Ret = pktUntarFile(DECRYPTION_TARGZ_PATH, "/tmp");

    remove(DECRYPTION_TARGZ_PATH);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "pktUntarFile failed.\n");
        goto error_exit;
    }

unpack_exit:

    if (m_stUpdateInfo.bIsUpgradeStart)
    {
        print_level(SV_INFO, "skip to check md5 file, exec upgrade_start.sh now!\n");
        goto upgrade_start;
    }

    s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "pktParseMd5check failed.\n");
        goto error_exit;
    }

    s32Ret = pktCheckPacketMd5(UNTAR_DIR_PATH, &stPacketInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "pktCheckPacketMd5 failed.\n");
        goto error_exit;
    }

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32C4))
    s32Ret = updateClearModel();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "updateClearModel failed.\n");
        goto error_exit;
    }
#endif

upgrade_start:
/* 先检查升级程序是否有更新，再更新uboot或内核 */
    sprintf(szFilePath, "%s/%s", UNTAR_DIR_PATH, UPGRADE_START_SH);

    if(0 == access(szFilePath, F_OK|X_OK))
    {
        /* 如果是在线升级，传入参数1 */
        if (bUpgradeOnline)
        {
            sprintf(szFilePath, "%s/%s 1", UNTAR_DIR_PATH, UPGRADE_START_SH);
        }

        print_level(SV_INFO, "exec: %s\n", szFilePath);

        s32Ret = system_ex(szFilePath, 60);
        print_level(SV_INFO, "upgrade_start.sh exit with -> %d\n", s32Ret);

        if (s32Ret == 1)
        {
            print_level(SV_ERROR, "%s exec failed.\n", szFilePath);
        }
        else if (s32Ret == 2)
        {
            print_level(SV_INFO, "update autoUpdate successful, reboot now...\n");
            return -2;
        }
        else if (s32Ret == 3)
        {
            print_level(SV_INFO, "invalid packet for recovery update!\n");
            goto error_exit;
        }
        else if (s32Ret == 4)
        {
            print_level(SV_INFO, "upgrade online. restart autoUpdate\n");
            return 11;
        }
        else if (s32Ret == 5)
        {
            print_level(SV_INFO, "exit with reparse, get packet info again.\n");

            /* 先把原来的文件列表清空 */
            pktDeleteAddedFile(&stPacketInfo);

            s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "pktParseMd5check failed.\n");
                goto error_exit;
            }

            s32Ret = pktCheckPacketMd5(UNTAR_DIR_PATH, &stPacketInfo);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "pktCheckPacketMd5 failed.\n");
                goto error_exit;
            }
        }

        if (m_stUpdateInfo.bIsUpgradeStart)
        {
            print_level(SV_INFO, "only upgrade_stat.sh, so need to reboot and restart autoUpdate!\n");
            system_ex("reboot -f", 60);
        }
    }
    else
    {
        print_level(SV_INFO, "%s does not exist...\n", szFilePath);
    }

#if 0
#ifdef PLATFORM_SSC335
    sprintf(szFilePath, "%s", NEWSDK_MARK);
    if(!update_IsPathExist(szFilePath))
    {
        print_level(SV_INFO, "%s does not exist...\n", szFilePath);
        goto error_exit;
    }
    else
    {
        print_level(SV_INFO, "found %s, upgrade can be performed.\n", szFilePath);
    }
#endif
#endif

/* 如果是在线升级，升级uboot、kernel是在前面的upgrade_start.sh就完成了。直接跳过autoUpdate本身的内核升级流程 */
    if (bUpgradeOnline)
    {
        print_level(SV_INFO, "upgrade online now. Jump to begin_ComparePacketWithSystem...\n");
        goto begin_ComparePacketWithSystem;
    }


/* RK和335平台更新uboot和内核 */
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_SSC335))
    sint32 s32Fd = -1;
    sint32 s32TryTime = 0;
    char *pszUbootver = NULL, *pszKernelver = NULL, *pszHardwarever = NULL;
    char szUbootver[16] = {0};
    char szKernelver[16] = {0};
    DIR *pDirDev = NULL;
    struct dirent *pstDirent = NULL;
    uint8 au8HeadBuf[256];
    #if defined(PLATFORM_RV1126)
    uint32 *pih_time_uboot = &au8HeadBuf[124];
    uint32 *pih_time = &au8HeadBuf[124];
    #else
    uint32 *pih_time_uboot = &au8HeadBuf[8];
    uint32 *pih_time = &au8HeadBuf[8];
    #endif

#if defined(BOARD_ADA900V1)
    /* ADA38硬件升级为ADA900程序 */
    pszHardwarever = fw_getenv("hardwareVersion");
    if (NULL != pszHardwarever && 0 == strcmp(pszHardwarever, "ADA32V2"))
    {
        system_ex("fw_setenv hardwareVersion ADA900V1", 60);
    }
#endif

    sprintf(szFilePath, "%s/var", UNTAR_DIR_PATH);
    pDirDev = opendir(szFilePath);
    if(NULL != pDirDev)
    {
        while (1)
        {
            pstDirent = readdir(pDirDev);
            if(NULL == pstDirent)
            {
                break;
            }

            if (pstDirent->d_type == DT_DIR)
            {
                continue;
            }

            if (strstr(pstDirent->d_name, "uboot") || strstr(pstDirent->d_name, "kernel"))
            {
                sprintf(szFilePath, "%s/var/%s", UNTAR_DIR_PATH, pstDirent->d_name);
                print_level(SV_DEBUG, "try to read file: %s version.\n", szFilePath);
                s32Fd = open(szFilePath, O_RDONLY);
                if (s32Fd < 0)
                {
                    print_level(SV_ERROR, "open file: %s failed. [err:%s]\n", szFilePath, strerror(errno));
                }
                else
                {
                    memset(au8HeadBuf, 0x0, 256);
                    s32Ret = read(s32Fd, au8HeadBuf, 256);
                    close(s32Fd);
                    if (s32Ret <= 0)
                    {
                        print_level(SV_ERROR, "read file: %s failed. [err:%s]\n", szFilePath, strerror(errno));
                    }
                    else
                    {
                        if (strstr(pstDirent->d_name, "uboot"))
                        {
                            sprintf(szUbootver, "%08lx", little2big(*pih_time_uboot)); // 取大端字节序
                            print_level(SV_INFO, "packet ubootver:%lx\n", *pih_time_uboot);
                        }
                        else
                        {
                            sprintf(szKernelver, "%08lx", little2big(*pih_time)); // 取大端字节序
                            print_level(SV_INFO, "packet kernelver:%lx\n", *pih_time);
                        }
                    }
                }
            }
        }

        closedir(pDirDev);
        if (0 != strlen(szUbootver) || 0 != strlen(szKernelver))
        {
            pszUbootver = fw_getenv("ubootver");
            pszKernelver = fw_getenv("kernelver");
            if ((0 != strlen(szUbootver) && (NULL == pszUbootver || 0 != strcmp(pszUbootver, szUbootver)))
                || (0 != strlen(szKernelver) && (NULL == pszKernelver || 0 != strcmp(pszKernelver, szKernelver))))
            {
                if (0 != strlen(szUbootver) && NULL == pszUbootver)
                {
                    print_level(SV_WARN, "no found env name: ubootver\n");
                }

                if (0 != strlen(szKernelver) && NULL == pszKernelver)
                {
                    print_level(SV_WARN, "no found env name: kernelver\n");
                }

                s32TryTime = updateCheckTryTimes();
                if (s32TryTime < 0)
                {
                    print_level(SV_ERROR, "updateCheckTryTimes failed.\n");
                    goto error_exit;
                }

                print_level(SV_ERROR, "ubootver or kernelver is different, try to reset to update times=%d. [env ubootver:%s, packet ubootver:%s, env kernelver:%s, packet kernelver:%s]\n", \
                        s32TryTime, pszUbootver, szUbootver, pszKernelver, szKernelver);

            #if defined(PLATFORM_SSC335)
                s32Ret = flashProtectionEnable(SV_FALSE);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "disable flash protection failed.\n");
                }
            #endif

                system_ex("fw_setenv update y", 60);
                system_ex("sync", 60);
                sleep_ms(2000);

            #if defined(PLATFORM_SSC335)
                s32Ret = flashProtectionEnable(SV_TRUE);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "enable flash protection failed.\n");
                }
            #endif
                system_ex("reboot -f", 60);
                return -2;
            }
        }

#if (defined(PLATFORM_RV1126))
        char *pszUpdaterootfs = NULL;
        pszUpdaterootfs = fw_getenv("updaterootfs");
        if (NULL != pszUpdaterootfs && 0 == strcmp(pszUpdaterootfs, "y"))
        {
            s32TryTime = updateCheckTryTimes();
            if (s32TryTime < 0)
            {
                print_level(SV_ERROR, "updateCheckTryTimes failed.\n");
                goto error_exit;
            }

            print_level(SV_ERROR, "filesystem need to reburn, try to reset to update time=%d\n", s32TryTime);
            system_ex("fw_setenv update y", 60);
            system_ex("sync", 60);
            sleep_ms(2000);
            system_ex("reboot -f", 60);
            return -2;
        }
#endif

    }
#endif

begin_ComparePacketWithSystem:

    s32DiffFlag = updateComparePacketWithSystem(&stPacketInfo);
    if (s32DiffFlag < 0)
    {
        print_level(SV_ERROR, "updateComparePacketWithSystem failed.\n");
        goto error_exit;
    }
    else if (0 == s32DiffFlag)
    {
        print_level(SV_INFO, "no file need to update in the packet: %s\n", szPacketPath);
        if (0 == stPacketInfo.u32DelFileCnt)
        {
            print_level(SV_INFO, "no file need to delete in the packet: %s\n", szPacketPath);
            goto normal_exit;
        }
    }

    for (i = 0; i < 3; i++)
    {
        s32Ret = updateOverwritePacketToSystem(UNTAR_DIR_PATH, &stPacketInfo, s32DiffFlag);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "updateOverwritePacketToSystem failed.\n");
            goto error_exit;
        }

        s32DiffFlag = updateComparePacketWithSystem(&stPacketInfo);
        if (s32DiffFlag < 0)
        {
            print_level(SV_ERROR, "updateComparePacketWithSystem failed.\n");
            goto error_exit;
        }
        else if (0 == s32DiffFlag)
        {
            break;
        }
    }
    if (i >= 3)
    {
        print_level(SV_ERROR, "updateOverwritePacketToSystem more than 3 times, but system is not effective.\n");
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106))
        sprintf(szLogoCmd, "logocontrol -string Update\\ Fail");
        system_ex(szLogoCmd, 10);
#endif
        goto error_exit;
    }

normal_exit:
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(BOARD_IPCR20S3))
    /* 检查专用版相关信息和资源并更新 */
    s32Ret = checkSpecialVersion(szPacketPath);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "checkSpecialVersion failed.\n");
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
    /* 兼容RK工程升级到HD900工程同步旧的配置参数到新工程版本，只针对DMS31和ADA32 */
    s32Ret = updateSyncOldParams(szPacketPath);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "updateSyncOldParams failed.\n");
    }
#endif

#if (defined(PLATFORM_RV1126))
    if (m_stUpdateInfo.bGenerateKey && BOARD_IsNeedKeyAuth())
    {
        s32Ret = generateKeyAuth();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "generateKeyAuth failed.\n");
            remove(KEY_AUTH_FLAG);
            remove(KEY_AUTH_FLAG_BRIGADE);
        }
    }
#endif

    if (m_stUpdateInfo.bUpdateSource)
    {
        s32Ret = flashProtectionEnable(SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "disable flash protection failed.\n");
            return SV_FAILURE;
        }
        s32Ret = BOARD_UpdateSVersionSource();
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "BOARD_UpdateSVersionSource fail!\n");
        }
        else
        {
            m_stUpdateInfo.bUpdateSource = SV_FALSE;
        }
        print_level(SV_INFO, "finish update source to system.\n");
        s32Ret = flashProtectionEnable(SV_TRUE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "enable flash protection failed.\n");
        }
    }

    char *pversionA32P = BOARD_GetSVersion();
    char *pcustomerA32P = BOARD_GetSCustomer();
    if (NULL != strstr(pversionA32P, "ADA32P"))
    {
        if (NULL != strstr(pcustomerA32P, "100002"))
        {
            // 存在在专用版
        }else
        {
            // 通用版本，忽略specicalVersion的存在
            m_stUpdateInfo.bUpdateSpecialVersion = SV_FALSE;
        }
    }

    if (m_stUpdateInfo.bUpdateSpecialVersion)
    {
        s32Ret = resetSpecialVersion();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "resetSpecialVersion failed.\n");
            return SV_FAILURE;
        }
    }

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
    s32Ret = checkConfigStatus();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "checkConfigStatus fail!\n");
    }
#endif

    /* logo只针对RK平台 */
#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || (defined(PLATFORM_RV1106)&& ! (defined(BOARD_DMS51V1))))
    print_level(SV_INFO, "%s!\n", CAN_UPGRADE_SUCCESS);      // 这句日志不要删除，CAN升级会以日志是否有这句话作为升级成功的标志
    sprintf(szLogoCmd, "logocontrol -string Update\\ Success");
    system_ex(szLogoCmd, 50);
#endif
#endif

    sprintf(szFilePath, "%s/%s", UNTAR_DIR_PATH, UPGRADE_END_SH);
    if(0 == access(szFilePath, F_OK|X_OK))
    {
        if(0 != system_ex(szFilePath, 60))
        {
            print_level(SV_ERROR, "%s exec failed.\n", szFilePath);
        }
    }

    s32Ret = flashProtectionEnable(SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "disable flash protection failed.\n");
    }

    remove("/boot/try_update_times");

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
	if (!m_stUpdateInfo.bIsFixed)
    {
        print_level(SV_INFO, "remove %s\n", szPacketPath);
        remove(szPacketPath);
    }
	else
	{
        print_level(SV_INFO, "update packet with fixed, skip to remove %s...\n", szPacketPath);
	}

    if (m_stUpdateInfo.bIsCover)
    {
        print_level(SV_INFO, "update packet with cover, remove params...\n");
        sprintf(szCmd, "rm -rf %s %s %s", CONFIG_XML, CONFIG_BAK1, CONFIG_BAK2);
        if(0 != system_ex(szCmd, 60))
        {
            print_level(SV_WARN, "rm config xml failed.\n");
        }

        sprintf(szCmd, "cp %s %s", CONFIG_DEFAULT, CONFIG_TMP_PATH);
        if(0 != system_ex(szCmd, 60))
        {
            print_level(SV_WARN, "'%s' failed.\n", szCmd);
        }

        s32Ret = CONFIG_ReloadFile();
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_ReloadFile failed! [err=%#x]\n", s32Ret);
        }
    }

    if (m_stUpdateInfo.bIs360)
    {
        CFG_ALG_PARAM stAlgParam;
        CFG_MEDIA_PARAM stMediaParam;

        s32Ret = CONFIG_GetAlgParam(&stAlgParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_GetAlgParam failed! [err=%#x]\n", s32Ret);
            goto skip_360;
        }

        s32Ret = CONFIG_GetMediaParam(&stMediaParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_GetMediaParam failed! [err=%#x]\n", s32Ret);
            goto skip_360;
        }

        stAlgParam.bYuvProtocol = SV_TRUE;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[0].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[0].dY = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[1].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[1].dY = 0.33;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[2].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[2].dY = 0.66;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[3].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[3].dY = 1.00;

        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[4].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[4].dY = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[5].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[5].dY = 0.33;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[6].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[6].dY = 0.66;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[7].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[7].dY = 1.00;

        if(m_stUpdateInfo.bIsPformat)
        {
            stMediaParam.u32VoFramerate = 25;
        }
        stMediaParam.astChnParam[0].bShowGuiMask = stMediaParam.astChnParam[0].bShowGuiMask & (~0b1000);

        s32Ret = CONFIG_SetAlgParam(&stAlgParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_SetAlgParam failed! [err=%#x]\n", s32Ret);
            goto skip_360;
        }

        s32Ret = CONFIG_SetMediaParam(&stMediaParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_SetMediaParam failed! [err=%#x]\n", s32Ret);
            goto skip_360;
        }

skip_360:
        ;
    }

    if (m_stUpdateInfo.bIsSetConfig)
    {
#if (defined(BOARD_ADA32IR))
        CFG_ALG_PARAM stAlgParam;
        CFG_MEDIA_PARAM stMediaParam;

        print_level(SV_DEBUG, "========= start to deal with conifg  =============\n");
        sprintf(szCmd, "cp %s %s", CONFIG_DEFAULT, CONFIG_TMP_PATH);
        if(0 != system_ex(szCmd, 60))
        {
            print_level(SV_WARN, "'%s' failed.\n", szCmd);
        }

        s32Ret = CONFIG_ReloadFile();
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_ReloadFile failed! [err=%#x]\n", s32Ret);
        }

        sprintf(szCmd, "cp -rf %s %s", CONFIG_DEFAULT, CONFIG_XML);
        if(0 != system_ex(szCmd, 60))
        {
            print_level(SV_WARN, "'%s' failed.\n", szCmd);
        }
        sprintf(szCmd, "cp -rf %s %s", CONFIG_DEFAULT, CONFIG_BAK1);
        if(0 != system_ex(szCmd, 60))
        {
            print_level(SV_WARN, "'%s' failed.\n", szCmd);
        }
        sprintf(szCmd, "cp -rf %s %s", CONFIG_DEFAULT, CONFIG_BAK2);
        if(0 != system_ex(szCmd, 60))
        {
            print_level(SV_WARN, "'%s' failed.\n", szCmd);
        }

        s32Ret = CONFIG_GetAlgParam(&stAlgParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_GetAlgParam failed! [err=%#x]\n", s32Ret);
            goto skip_setConfig;
        }

        s32Ret = CONFIG_GetMediaParam(&stMediaParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_GetMediaParam failed! [err=%#x]\n", s32Ret);
            goto skip_setConfig;
        }

        /*1:红外 2:普通摄像头*/
        stAlgParam.stAlgCh1.stPdsParam.enPdsModel = E_PDS_IR_PC;
        stAlgParam.stAlgCh2.stPdsParam.enPdsModel = E_PDS_P;

        stAlgParam.stAlgCh1.stPdsParam.enRoiGui = CFG_PDROI_GUI_HIDE;
        stAlgParam.stAlgCh2.stPdsParam.enRoiGui = CFG_PDROI_GUI_HIDE;
        stAlgParam.stAlgCh1.stPdsParam.enRoiStyle = CFG_PDROI_BOTTOM;
        stAlgParam.stAlgCh2.stPdsParam.enRoiStyle = CFG_PDROI_BOTTOM;

        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[0].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[0].dY = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[1].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[1].dY = 0.33;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[2].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[2].dY = 0.66;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[3].dX = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[3].dY = 1.00;

        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[4].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[4].dY = 0.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[5].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[5].dY = 0.33;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[6].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[6].dY = 0.66;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[7].dX = 1.00;
        stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints[7].dY = 1.00;

        memcpy(stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints,\
                stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints,sizeof(stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints));
        s32Ret = CONFIG_SetAlgParam(&stAlgParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_SetAlgParam failed! [err=%#x]\n", s32Ret);
            goto skip_setConfig;
        }

        s32Ret = CONFIG_SetMediaParam(&stMediaParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_SetMediaParam failed! [err=%#x]\n", s32Ret);
            goto skip_setConfig;
        }
skip_setConfig:

        m_stUpdateInfo.bIsSetConfig = SV_FALSE;

        print_level(SV_DEBUG, "===========finsh deal with config =================\n");
#endif
    }

    if (m_stUpdateInfo.bIsRecorder)
    {
        CFG_MEDIA_PARAM stMediaParam;
        CFG_SYS_PARAM   stSystemParam;

        print_level(SV_DEBUG, "===========start set recorder param =================\n");
        s32Ret = CONFIG_GetMediaParam(&stMediaParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_GetMediaParam failed! [err=%#x]\n", s32Ret);
            goto skip_recorder;
        }
        s32Ret = CONFIG_GetSystemParam(&stSystemParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_GetMediaParam failed! [err=%#x]\n", s32Ret);
            goto skip_recorder;
        }

        stMediaParam.astChnParam[0].bShowGuiMask = 12;
        s32Ret = CONFIG_SetMediaParam(&stMediaParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_SetMediaParam failed! [err=%#x]\n", s32Ret);
            goto skip_recorder;
        }

        stSystemParam.enAlarmRecord = E_REC_OFF;
        stSystemParam.enNormalRecord = E_REC_NOR_CON;
        stSystemParam.bLoopOverwrite = SV_FALSE;
        stSystemParam.bEnableStorage = SV_TRUE;
        s32Ret = CONFIG_SetSystemParam(&stSystemParam);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "CONFIG_SetSystemParam failed! [err=%#x]\n", s32Ret);
            goto skip_recorder;
        }
        print_level(SV_DEBUG, "===========finsh set recorder param =================\n");

skip_recorder:
        m_stUpdateInfo.bIsRecorder = SV_FALSE;
    }


#if defined(PLATFORM_RV1126)
    /* 同步旧工程ADA37V2新到新工程ADA32V2_OW修改uboot环境硬件版本 */
	pszHardwarever = fw_getenv("hardwareVersion");
	if (NULL != pszHardwarever && 0 == strcmp(pszHardwarever, "ADA37V2"))
	{
	    system_ex("fw_setenv hardwareVersion ADA32V2", 60);
	}
#endif
#else
	print_level(SV_INFO, "remove %s\n", szPacketPath);
    remove(szPacketPath);
#endif

    #if (defined(BOARD_IPCR20S3))
    if(0 != system_ex("rm /root/webui -rf", 60))
    {
        print_level(SV_WARN, "rm webui exec failed.\n");
    }
    #endif
    sync();
    flashProtectionEnable(SV_TRUE);

	//升级结束后，拷贝自动化测试脚本或升级A32的MCU程序
	doExtraWork();

    if (SV_SUCCESS != updateStop())
        print_level(SV_ERROR, "updateStop failed.\n");

    if (SV_SUCCESS != updateFini())
        print_level(SV_ERROR, "updateFini failed.\n");
#if ( defined(BOARD_ADA32C4))
#if defined(BOARD_WFCR20S2)
    if (m_stUpdateInfo.bIsMcuExist)
        mcuGotoAppMode();
#endif
#if defined(BOARD_ADA32C4)
	if(m_stUpdateInfo.bIsMcuExist)
		mcuGotoApp2();
#endif
    if (SV_SUCCESS != mcuUpdateFini())
        print_level(SV_ERROR, "mcuUpdateFini failed.\n");
#endif

    sprintf(szCmd, "rm %s -rf", DECRYPTION_TARGZ_PATH);
    system_ex(szCmd, 60);
    sprintf(szCmd, "rm %s -rf", UNTAR_DIR_PATH);
    system_ex(szCmd, 60);

#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106))
    system_ex("sleep 10s && logocontrol -quit &", 10);
#endif

#if 0//(defined(PLATFORM_SSC335))
        print_level(SV_INFO, "Wait 5s and reboot.\n");
        //system_ex("rmmod mtdex", 60);
        sleep_ms(5000);
        //system_ex("insmod /root/ko/extdrv/mtdex.ko", 60);
        system_ex("reboot", 60);
#endif

    if (m_stUpdateInfo.bIsLaterPkt && update_IsPathExist(PKT_LATER_FLAG_FILE))
    {
        print_level(SV_INFO, "upate later packet success!!!\n");
        sprintf(szCmd, "rm -rf %s", PKT_LATER_FLAG_FILE);
        system_ex(szCmd, 10);
    }

    if (m_stUpdateInfo.bNeedReboot)
    {
        print_level(SV_INFO, "autoUpate need to reboot now!!!\n");
        system_ex("reboot -f", 10);
    }

    return m_stUpdateInfo.bAutoTestMode ? 10 : 0;

error_exit:
    s32Ret = flashProtectionEnable(SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "disable flash protection failed.\n");
    }

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
	if (!m_stUpdateInfo.bIsFixed)
	{
        print_level(SV_INFO, "remove %s\n", szPacketPath);
    	remove(szPacketPath);
	}
#else
	print_level(SV_INFO, "remove %s\n", szPacketPath);
	remove(szPacketPath);
#endif
    sync();
    flashProtectionEnable(SV_TRUE);

    //升级结束后，拷贝自动化测试脚本或升级A32的MCU程序
    doExtraWork();

    if (SV_SUCCESS != updateStop())
        print_level(SV_ERROR, "updateStop failed.\n");

    if (SV_SUCCESS != updateFini())
        print_level(SV_ERROR, "updateFini failed.\n");
#if (defined(BOARD_ADA32C4))

#if defined(BOARD_WFCR20S2)
		if (m_stUpdateInfo.bIsMcuExist)
			mcuGotoAppMode();
#endif
#if defined(BOARD_ADA32C4)
		if(m_stUpdateInfo.bIsMcuExist)
			mcuGotoApp2();
#endif

    if (SV_SUCCESS != mcuUpdateFini())
        print_level(SV_ERROR, "mcuUpdateFini failed.\n");
#endif

    sprintf(szCmd, "rm %s -rf", DECRYPTION_TARGZ_PATH);
    system_ex(szCmd, 60);
    sprintf(szCmd, "rm %s -rf", UNTAR_DIR_PATH);
    system_ex(szCmd, 60);

#if ((defined(PLATFORM_RV1126) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1)) || defined(PLATFORM_RV1106))
    system_ex("sleep 10s && logocontrol -quit &", 10);
#endif

    return m_stUpdateInfo.bAutoTestMode ? 10 : -1;
}

