<?xml version="1.0" encoding="utf-8"?>
<!--<?xml-stylesheet type="text/xsl" href="onvif-schema-viewer.xsl"?>-->
<!--
Copyright (c) 2008-2022 by ONVIF: Open Network Video Interface Forum. All rights reserved.

Recipients of this document may copy, distribute, publish, or display this document so long as this copyright notice, license and disclaimer are retained with all copies of the document. No license is granted to modify this document.

THIS DOCUMENT IS PROVIDED "AS IS," AND THE CORPORATION AND ITS MEMBERS AND THEIR AFFILIATES, MAKE NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO, WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, OR TITLE; THAT THE CONTENTS OF THIS DOCUMENT ARE SUITABLE FOR ANY PURPOSE; OR THAT THE IMPLEMENTATION OF SUCH CONTENTS WILL NOT INFRINGE ANY PATENTS, COPYRIGHTS, <PERSON>RADEMARKS OR OTHER RIGHTS.
IN NO EVENT WILL THE CORPORATION OR ITS MEMBERS OR THEIR AFFILIATES BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL, PUNITIVE OR CONSEQUENTIAL DAMAGES, ARISING OUT OF OR RELATING TO ANY USE OR DISTRIBUTION OF THIS DOCUMENT, WHETHER OR NOT (1) THE CORPORATION, MEMBERS OR THEIR AFFILIATES HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES, OR (2) SUCH DAMAGES WERE REASONABLY FORESEEABLE, AND ARISING OUT OF OR RELATING TO ANY USE OR DISTRIBUTION OF THIS DOCUMENT.  THE FOREGOING DISCLAIMER AND LIMITATION ON LIABILITY DO NOT APPLY TO, INVALIDATE, OR LIMIT REPRESENTATIONS AND WARRANTIES MADE BY THE MEMBERS AND THEIR RESPECTIVE AFFILIATES TO THE CORPORATION AND OTHER MEMBERS IN CERTAIN WRITTEN POLICIES OF THE CORPORATION.
-->
<xs:schema xmlns:tt="http://www.onvif.org/ver10/schema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2" xmlns:xop="http://www.w3.org/2004/08/xop/include" xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope" targetNamespace="http://www.onvif.org/ver10/schema" elementFormDefault="qualified" version="22.12">
	<xs:include schemaLocation="common.xsd"/>
	<xs:import namespace="http://www.w3.org/2005/05/xmlmime" schemaLocation="http://www.w3.org/2005/05/xmlmime"/>
	<xs:import namespace="http://www.w3.org/2003/05/soap-envelope" schemaLocation="http://www.w3.org/2003/05/soap-envelope"/>
	<xs:import namespace="http://docs.oasis-open.org/wsn/b-2" schemaLocation="b-2.xsd"/>
	<xs:import namespace="http://www.w3.org/2004/08/xop/include" schemaLocation="include.xsd"/>
	<!--===============================-->
	<!--         Generic Types         -->
	<!--===============================-->
	<xs:complexType name="DeviceEntity">
		<xs:annotation>
			<xs:documentation>Base class for physical entities like inputs and outputs.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="token" type="tt:ReferenceToken" use="required">
			<xs:annotation>
				<xs:documentation>Unique identifier referencing the physical entity.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Name">
		<xs:annotation>
			<xs:documentation>User readable name. Length up to 64 characters.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="IntRectangle">
		<xs:annotation>
			<xs:documentation>Rectangle defined by lower left corner position and size. Units are pixel.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="x" type="xs:int" use="required"/>
		<xs:attribute name="y" type="xs:int" use="required"/>
		<xs:attribute name="width" type="xs:int" use="required"/>
		<xs:attribute name="height" type="xs:int" use="required"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IntRectangleRange">
		<xs:annotation>
			<xs:documentation>Range of a rectangle. The rectangle itself is defined by lower left corner position and size. Units are pixel.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="XRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of X-axis.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="YRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of Y-axis.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WidthRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of width.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="HeightRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of height.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FloatRange">
		<xs:annotation>
			<xs:documentation>Range of values greater equal Min value and less equal Max value.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Min" type="xs:float"/>
			<xs:element name="Max" type="xs:float"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DurationRange">
		<xs:annotation>
			<xs:documentation>Range of duration greater equal Min duration and less equal Max duration.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Min" type="xs:duration"/>
			<xs:element name="Max" type="xs:duration"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IntItems">
		<xs:annotation>
			<xs:documentation>List of values.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Items" type="xs:int" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IntList">
		<xs:list itemType="xs:int"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="FloatList">
		<xs:list itemType="xs:float"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="StringAttrList">
		<xs:list itemType="xs:string"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="StringList">
		<xs:list itemType="xs:string"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="ReferenceTokenList">
		<xs:list itemType="tt:ReferenceToken"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="FloatItems">
		<xs:sequence>
			<xs:element name="Items" type="xs:float" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:element name="StringItems">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Item" type="xs:string" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="StringList" type="tt:StringList"/>
	<xs:element name="IntRange" type="tt:IntRange"/>
	<xs:element name="IntList" type="tt:IntList"/>
	<xs:element name="FloatRange" type="tt:FloatRange"/>
	<xs:element name="FloatList" type="tt:FloatList"/>
	<xs:element name="DurationRange" type="tt:DurationRange"/>
	<xs:element name="IntRectangleRange" type="tt:IntRectangleRange"/>
	<!--===============================-->
	<xs:complexType name="AnyHolder">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--      End, Generic Types       -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--      Media Related Types      -->
	<!--===============================-->
	<xs:complexType name="VideoSource">
		<xs:annotation>
			<xs:documentation>Representation of a physical video input.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Framerate" type="xs:float">
						<xs:annotation>
							<xs:documentation>Frame rate in frames per second.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Resolution" type="tt:VideoResolution">
						<xs:annotation>
							<xs:documentation>Horizontal and vertical resolution</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Imaging" type="tt:ImagingSettings" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional configuration of the image sensor.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:VideoSourceExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Imaging" type="tt:ImagingSettings20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the image sensor. To be used if imaging service 2.00 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioSource">
		<xs:annotation>
			<xs:documentation>Representation of a physical audio input.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Channels" type="xs:int">
						<xs:annotation>
							<xs:documentation>number of available audio channels. (1: mono, 2: stereo) </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Profile">
		<xs:annotation>
			<xs:documentation>
			A media profile consists of a set of media configurations. Media profiles are used by a client
			to configure properties of a media stream from an NVT.<br/>
			An NVT shall provide at least one media profile at boot. An NVT should provide “ready to use”
			profiles for the most common media configurations that the device offers.<br/>
			A profile consists of a set of interconnected configuration entities. Configurations are provided
			by the NVT and can be either static or created dynamically by the NVT. For example, the
			dynamic configurations can be created by the NVT depending on current available encoding
			resources.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Name" type="tt:Name">
				<xs:annotation>
					<xs:documentation>User readable name of the profile.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoSourceConfiguration" type="tt:VideoSourceConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Video input.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioSourceConfiguration" type="tt:AudioSourceConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Audio input.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoEncoderConfiguration" type="tt:VideoEncoderConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Video encoder.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioEncoderConfiguration" type="tt:AudioEncoderConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Audio encoder.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZConfiguration" type="tt:PTZConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the pan tilt zoom unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MetadataConfiguration" type="tt:MetadataConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the metadata stream.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ProfileExtension" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Extensions defined in ONVIF 2.0</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken" use="required">
			<xs:annotation>
				<xs:documentation>Unique identifier of the profile.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="fixed" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>A value of true signals that the profile cannot be deleted. Default is false.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:ProfileExtension2" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:element name="VideoSourceConfiguration" type="tt:VideoSourceConfiguration"/>
	<xs:element name="AudioSourceConfiguration" type="tt:AudioSourceConfiguration"/>
	<xs:element name="VideoEncoderConfiguration" type="tt:VideoEncoderConfiguration"/>
	<xs:element name="AudioEncoderConfiguration" type="tt:AudioEncoderConfiguration"/>
	<xs:element name="PTZConfiguration" type="tt:PTZConfiguration"/>
	<xs:element name="MetadataConfiguration" type="tt:MetadataConfiguration"/>
	<!--===============================-->
	<xs:complexType name="ConfigurationEntity">
		<xs:annotation>
			<xs:documentation>Base type defining the common properties of a configuration.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Name" type="tt:Name">
				<xs:annotation>
					<xs:documentation>User readable name. Length up to 64 characters.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UseCount" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of internal references currently using this configuration. <p style='margin:0'>This informational parameter is read-only. Deprecated for Media2 Service.</p></xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken" use="required">
			<xs:annotation>
				<xs:documentation>Token that uniquely references this configuration. Length up to 64 characters.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--===============================-->
	<!--   VideoSourceConfiguration   -->
	<!--===============================-->
	<xs:complexType name="VideoSourceConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="SourceToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Reference to the physical input.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Bounds" type="tt:IntRectangle">
						<xs:annotation>
							<xs:documentation>Rectangle specifying the Video capturing area. The capturing area shall not be larger than the whole Video source area.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="Extension" type="tt:VideoSourceConfigurationExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:attribute name="ViewMode" type="xs:string">
					<xs:annotation>
						<xs:documentation>Readonly parameter signalling Source configuration's view mode, for devices supporting different view modes as defined in tt:viewModes.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationExtension">
		<xs:sequence>
			<xs:element name="Rotate" type="tt:Rotate" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure rotation of captured image.
						What resolutions a device supports shall be unaffected by the Rotate parameters.<br/>
						If a device is configured with Rotate=AUTO, the device shall take control over the Degree parameter and automatically update it so that a client can query current rotation.<br/>
						The device shall automatically apply the same rotation to its pan/tilt control direction depending on the following condition: 
						if Reverse=AUTO in PTControlDirection or if the device doesn’t support Reverse in PTControlDirection
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceConfigurationExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationExtension2">
		<xs:sequence>
			<xs:element name="LensDescription" type="tt:LensDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation><xs:documentation>Optional element describing the geometric lens distortion. Multiple instances for future variable lens support.</xs:documentation></xs:annotation>
			</xs:element>
			<xs:element name="SceneOrientation" type="tt:SceneOrientation" minOccurs="0" maxOccurs="1">
				<xs:annotation><xs:documentation>Optional element describing the scene orientation in the camera’s field of view.</xs:documentation></xs:annotation>
			</xs:element>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Rotate">
		<xs:sequence>
			<xs:element name="Mode" type="tt:RotateMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable Rotation feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Degree" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to configure how much degree of clockwise rotation of image  for On mode. Omitting this parameter for On mode means 180 degree rotation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RotateExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RotateExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="RotateMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF">
				<xs:annotation><xs:documentation>Enable the Rotate feature. Degree of rotation is specified Degree parameter.</xs:documentation></xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ON">
			<xs:annotation><xs:documentation>Disable the Rotate feature.</xs:documentation></xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AUTO">
				<xs:annotation><xs:documentation>Rotate feature is automatically activated by the device.</xs:documentation></xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="LensProjection">
		<xs:sequence>
			<xs:element name="Angle" type="xs:float">
				<xs:annotation><xs:documentation>Angle of incidence.</xs:documentation></xs:annotation>
			</xs:element>
			<xs:element name="Radius" type="xs:float">
				<xs:annotation><xs:documentation>Mapping radius as a consequence of the emergent angle.</xs:documentation></xs:annotation>
			</xs:element>
			<xs:element name="Transmittance" type="xs:float" minOccurs="0">
				<xs:annotation><xs:documentation>Optional ray absorption at the given angle due to vignetting. A value of one means no absorption.</xs:documentation></xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="LensOffset">
		<xs:attribute name="x" type="xs:float">
			<xs:annotation><xs:documentation>Optional horizontal offset of the lens center in normalized coordinates.</xs:documentation></xs:annotation>
		</xs:attribute>
		<xs:attribute name="y" type="xs:float">
			<xs:annotation><xs:documentation>Optional vertical offset of the lens center in normalized coordinates.</xs:documentation></xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="LensDescription">
		<xs:sequence>
			<xs:element name="Offset" type="tt:LensOffset">
				<xs:annotation><xs:documentation>Offset of the lens center to the imager center in normalized coordinates.</xs:documentation></xs:annotation>
			</xs:element>
			<xs:element name="Projection" type="tt:LensProjection" maxOccurs="unbounded">
				<xs:annotation><xs:documentation>Radial description of the projection characteristics. The resulting curve is defined by the B-Spline interpolation 
					over the given elements. The element for Radius zero shall not be provided. The projection points shall be ordered with ascending Radius. 
					Items outside the last projection Radius shall be assumed to be invisible (black).</xs:documentation></xs:annotation>
			</xs:element>
			<xs:element name="XFactor" type="xs:float">
				<xs:annotation><xs:documentation>Compensation of the x coordinate needed for the ONVIF normalized coordinate system. </xs:documentation></xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:attribute name="FocalLength" type="xs:float">
			<xs:annotation><xs:documentation>Optional focal length of the optical system.</xs:documentation></xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationOptions">
		<xs:sequence>
			<xs:element name="BoundsRange" type="tt:IntRectangleRange">
				<xs:annotation>
					<xs:documentation>
						Supported range for the capturing area.
						Device that does not support cropped streaming shall express BoundsRange option as mentioned below
						BoundsRange->XRange and BoundsRange->YRange with same Min/Max values HeightRange and WidthRange Min/Max values same as VideoSource Height and Width Limits.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoSourceTokensAvailable" type="tt:ReferenceToken" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of physical inputs.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceConfigurationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="MaximumNumberOfProfiles" type="xs:int">
			<xs:annotation>
				<xs:documentation>Maximum number of profiles.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Rotate" type="tt:RotateOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for Rotation feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceConfigurationOptionsExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationOptionsExtension2">
		<xs:sequence>
			<xs:element name="SceneOrientationMode" type="tt:SceneOrientationMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Scene orientation modes supported by the device for this configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RotateOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:RotateMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options of Rotate mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DegreeList" type="tt:IntItems" minOccurs="0">
				<xs:annotation>
					<xs:documentation>List of supported degree value for rotation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RotateOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="Reboot" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>After setting the rotation, if a device starts to reboot this value is true.
				If a device can handle rotation setting without rebooting this value is false.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RotateOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--    Scene Orientation    -->
	<!--===============================-->
	<xs:simpleType name="SceneOrientationMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MANUAL" />
			<xs:enumeration value="AUTO" />
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="SceneOrientationOption">
		<xs:restriction base="xs:string">
			<xs:annotation>
				<xs:documentation>
					Defines the acceptable values for the Orientation element of the SceneOrientation type
				</xs:documentation>
			</xs:annotation>
			<xs:enumeration value="Below" />
			<xs:enumeration value="Horizon" />
			<xs:enumeration value="Above" />
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="SceneOrientation">
		<xs:sequence>
			<xs:element name="Mode" type="tt:SceneOrientationMode">
				<xs:annotation>
					<xs:documentation>
						Parameter to assign the way the camera determines the scene orientation.
					</xs:documentation>
				</xs:annotation>
			</xs:element>      
			<xs:element name="Orientation" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						Assigned or determined scene orientation based on the Mode. When assigning the Mode to AUTO, this field 
						is optional and will be ignored by the device. When assigning the Mode to MANUAL, this field is required 
						and the device will return an InvalidArgs fault if missing. 
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ViewModes">
		<xs:annotation>
			<xs:documentation>Source view modes supported by device.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="tt:Fisheye">
				<xs:annotation>	
					<xs:documentation>Undewarped viewmode from device supporting fisheye lens.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="tt:360Panorama">
				<xs:annotation>
					<xs:documentation>360 degree panoramic view.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="tt:180Panorama">
				<xs:annotation>
					<xs:documentation>180 degree panoramic view.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="tt:Quad">
				<xs:annotation>
					<xs:documentation>View mode combining four streams in single Quad, eg., applicable for devices supporting four heads.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="tt:Original">
				<xs:annotation>
					<xs:documentation>Unaltered view from the sensor.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="tt:LeftHalf">
				<xs:annotation>
					<xs:documentation>Viewmode combining the left side sensors, applicable for devices supporting multiple sensors.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="tt:RightHalf">
				<xs:annotation>	
					<xs:documentation>Viewmode combining the right side sensors, applicable for devices supporting multiple sensors.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="tt:Dewarp">
				<xs:annotation>
					<xs:documentation>Dewarped view mode for device supporting fisheye lens.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<!--   VideoEncoderConfiguration   -->
	<!--===============================-->
	<xs:complexType name="VideoEncoderConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="Encoding" type="tt:VideoEncoding">
						<xs:annotation>
							<xs:documentation>Used video codec, either Jpeg, H.264 or Mpeg4</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Resolution" type="tt:VideoResolution">
						<xs:annotation>
							<xs:documentation>Configured video resolution</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Quality" type="xs:float">
						<xs:annotation>
							<xs:documentation>Relative value for the video quantizers and the quality of the video. A high value within supported quality range means higher quality</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="RateControl" type="tt:VideoRateControl" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure rate control related parameters.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="MPEG4" type="tt:Mpeg4Configuration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure Mpeg4 related parameters.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="H264" type="tt:H264Configuration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure H.264 related parameters.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration">
						<xs:annotation>
							<xs:documentation>Defines the multicast settings that could be used for video streaming.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SessionTimeout" type="xs:duration">
						<xs:annotation>
							<xs:documentation>The rtsp session timeout for the related video stream</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
				</xs:sequence>
				<xs:attribute name="GuaranteedFrameRate" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>
							A value of true indicates that frame rate is a fixed value rather than an upper limit,
							and that the video encoder shall prioritize frame rate over all other adaptable
							configuration values such as bitrate.  Default is false.
						</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="VideoEncoding">
		<xs:restriction base="xs:string">
			<xs:enumeration value="JPEG"/>
			<xs:enumeration value="MPEG4"/>
			<xs:enumeration value="H264"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Mpeg4Profile">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SP"/>
			<xs:enumeration value="ASP"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="H264Profile">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Baseline"/>
			<xs:enumeration value="Main"/>
			<xs:enumeration value="Extended"/>
			<xs:enumeration value="High"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="VideoResolution">
		<xs:sequence>
			<xs:element name="Width" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of the columns of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Height" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of the lines of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoRateControl">
		<xs:sequence>
			<xs:element name="FrameRateLimit" type="xs:int">
				<xs:annotation>
					<xs:documentation>Maximum output framerate in fps. If an EncodingInterval is provided the resulting encoded framerate will be reduced by the given factor.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingInterval" type="xs:int">
				<xs:annotation>
					<xs:documentation>Interval at which images are encoded and transmitted. (A value of 1 means that every frame is encoded, a value of 2 means that every 2nd frame is encoded ...)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BitrateLimit" type="xs:int">
				<xs:annotation>
					<xs:documentation>the maximum output bitrate in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Mpeg4Configuration">
		<xs:sequence>
			<xs:element name="GovLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Determines the interval in which the I-Frames will be coded. An entry of 1 indicates I-Frames are continuously generated. An entry of 2 indicates that every 2nd image is an I-Frame, and 3 only every 3rd frame, etc. The frames in between are coded as P or B Frames.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Mpeg4Profile" type="tt:Mpeg4Profile">
				<xs:annotation>
					<xs:documentation>the Mpeg4 profile, either simple profile (SP) or advanced simple profile (ASP)</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="H264Configuration">
		<xs:sequence>
			<xs:element name="GovLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Group of Video frames length. Determines typically the interval in which the I-Frames will be coded. An entry of 1 indicates I-Frames are continuously generated. An entry of 2 indicates that every 2nd image is an I-Frame, and 3 only every 3rd frame, etc. The frames in between are coded as P or B Frames.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264Profile" type="tt:H264Profile">
				<xs:annotation>
					<xs:documentation>the H.264 profile, either baseline, main, extended or high</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoEncoderConfigurationOptions">
		<xs:sequence>
			<xs:element name="QualityRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of the quality values. A high value means higher quality.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JPEG" type="tt:JpegOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional JPEG encoder settings ranges (See also Extension element).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MPEG4" type="tt:Mpeg4Options" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional MPEG-4 encoder settings ranges (See also Extension element).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264" type="tt:H264Options" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional H.264 encoder settings ranges (See also Extension element).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoEncoderOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="GuaranteedFrameRateSupported" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>
					Indicates the support for the GuaranteedFrameRate attribute on the VideoEncoderConfiguration element.
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoEncoderOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="JPEG" type="tt:JpegOptions2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional JPEG encoder settings ranges.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MPEG4" type="tt:Mpeg4Options2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional MPEG-4 encoder settings ranges.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264" type="tt:H264Options2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional H.264 encoder settings ranges.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoEncoderOptionsExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoEncoderOptionsExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="JpegOptions">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported image sizes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FrameRateRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported frame rate in fps (frames per second).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingIntervalRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported encoding interval range. The encoding interval corresponds to the number of frames devided by the encoded frames. An encoding interval value of "1" means that all frames are encoded.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="JpegOptions2">
		<xs:complexContent>
			<xs:extension base="tt:JpegOptions">
				<xs:sequence>
					<xs:element name="BitrateRange" type="tt:IntRange">
						<xs:annotation>
							<xs:documentation>Supported range of encoded bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Mpeg4Options">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported image sizes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GovLengthRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported group of Video frames length. This value typically corresponds to the I-Frame distance.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FrameRateRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported frame rate in fps (frames per second).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingIntervalRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported encoding interval range. The encoding interval corresponds to the number of frames devided by the encoded frames. An encoding interval value of "1" means that all frames are encoded.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Mpeg4ProfilesSupported" type="tt:Mpeg4Profile" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported MPEG-4 profiles.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Mpeg4Options2">
		<xs:complexContent>
			<xs:extension base="tt:Mpeg4Options">
				<xs:sequence>
					<xs:element name="BitrateRange" type="tt:IntRange">
						<xs:annotation>
							<xs:documentation>Supported range of encoded bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="H264Options">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported image sizes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GovLengthRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported group of Video frames length. This value typically corresponds to the I-Frame distance.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FrameRateRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported frame rate in fps (frames per second).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingIntervalRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported encoding interval range. The encoding interval corresponds to the number of frames devided by the encoded frames. An encoding interval value of "1" means that all frames are encoded.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264ProfilesSupported" type="tt:H264Profile" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported H.264 profiles.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="H264Options2">
		<xs:complexContent>
			<xs:extension base="tt:H264Options">
				<xs:sequence>
					<xs:element name="BitrateRange" type="tt:IntRange">
						<xs:annotation>
							<xs:documentation>Supported range of encoded bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--   VideoEncoder2Configuration   -->
	<!--===============================-->
	<xs:simpleType name="VideoEncodingMimeNames">
		<xs:annotation>
			<xs:documentation>Video Media Subtypes as referenced by IANA (without the leading "video/" Video Media Type).  See also <a href="https://www.iana.org/assignments/media-types/media-types.xhtml#video"> IANA Media Types</a>.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="JPEG"/>
			<xs:enumeration value="MPV4-ES"/>			<!-- MPV4-ES actually references MP4V-ES.  For backward compatibility, it remains misspelled. -->
			<xs:enumeration value="H264"/>
			<xs:enumeration value="H265"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="VideoEncodingProfiles">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Simple"/>			<!-- MPEG4 SP -->
			<xs:enumeration value="AdvancedSimple"/>	<!-- MPEG4 ASP -->
			<xs:enumeration value="Baseline"/>			<!-- H264 Baseline -->
			<xs:enumeration value="Main"/>				<!-- H264 Main, H.265 Main -->
			<xs:enumeration value="Main10"/>			<!-- H265 Main 10 -->
			<xs:enumeration value="Extended"/>			<!-- H264 Extended -->
			<xs:enumeration value="High"/>				<!-- H264 High -->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="VideoEncoder2Configuration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="Encoding" type="xs:string">
						<xs:annotation>
							<xs:documentation>Video Media Subtype for the video format. For definitions see tt:VideoEncodingMimeNames and <a href="https://www.iana.org/assignments/media-types/media-types.xhtml#video"> IANA Media Types</a>.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Resolution" type="tt:VideoResolution2">
						<xs:annotation>
							<xs:documentation>Configured video resolution</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="RateControl" type="tt:VideoRateControl2" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure rate control related parameters.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Defines the multicast settings that could be used for video streaming.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Quality" type="xs:float">
						<xs:annotation>
							<xs:documentation>Relative value for the video quantizers and the quality of the video. A high value within supported quality range means higher quality</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
				</xs:sequence>
				<xs:attribute name="GovLength" type="xs:int">
					<xs:annotation>
						<xs:documentation>Group of Video frames length. Determines typically the interval in which the I-Frames will be coded. An entry of 1 indicates I-Frames are continuously generated. An entry of 2 indicates that every 2nd image is an I-Frame, and 3 only every 3rd frame, etc. The frames in between are coded as P or B Frames.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="Profile" type="xs:string">
					<xs:annotation>
						<xs:documentation>The encoder profile as defined in tt:VideoEncodingProfiles.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="GuaranteedFrameRate" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>
							A value of true indicates that frame rate is a fixed value rather than an upper limit,
							and that the video encoder shall prioritize frame rate over all other adaptable
							configuration values such as bitrate.  Default is false.
						</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoResolution2">
		<xs:sequence>
			<xs:element name="Width" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of the columns of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Height" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of the lines of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoRateControl2">
		<xs:sequence>
			<xs:element name="FrameRateLimit" type="xs:float">
				<xs:annotation>
					<xs:documentation>Desired frame rate in fps. The actual rate may be lower due to e.g. performance limitations.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BitrateLimit" type="xs:int">
				<xs:annotation>
					<xs:documentation>the maximum output bitrate in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:attribute name="ConstantBitRate" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>Enforce constant bitrate.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoEncoder2ConfigurationOptions">
		<xs:sequence>
			<xs:element name="Encoding" type="xs:string">
				<xs:annotation>
					<xs:documentation>Video Media Subtype for the video format. For definitions see tt:VideoEncodingMimeNames and <a href="https://www.iana.org/assignments/media-types/media-types.xhtml#video"> IANA Media Types</a>.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="QualityRange" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>Range of the quality values. A high value means higher quality.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution2" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported image sizes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BitrateRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported range of encoded bitrate in kbps.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:attribute name="GovLengthRange" type="tt:IntList">
			<xs:annotation>
				<xs:documentation>Exactly two values, which define the Lower and Upper bounds for the supported group of Video frames length. These values typically correspond to the I-Frame distance.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="FrameRatesSupported" type="tt:FloatList">
			<xs:annotation>
				<xs:documentation>List of supported target frame rates in fps (frames per second). The list shall be sorted with highest values first.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ProfilesSupported" type="tt:StringAttrList">
			<xs:annotation>
				<xs:documentation>List of supported encoder profiles as defined in tt::VideoEncodingProfiles.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="ConstantBitRateSupported" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>Signal whether enforcing constant bitrate is supported.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="GuaranteedFrameRateSupported" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>
					Indicates the support for the GuaranteedFrameRate attribute on the VideoEncoder2Configuration element.
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--    AudioSourceConfiguration   -->
	<!--===============================-->
	<xs:complexType name="AudioSourceConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="SourceToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Token of the Audio Source the configuration applies to</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioSourceConfigurationOptions">
		<xs:sequence>
			<xs:element name="InputTokensAvailable" type="tt:ReferenceToken" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Tokens of the audio source the configuration can be used for.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:AudioSourceOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioSourceOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--   AudioEncoderConfiguration   -->
	<!--===============================-->
	<xs:complexType name="AudioEncoderConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="Encoding" type="tt:AudioEncoding">
						<xs:annotation>
							<xs:documentation>Audio codec used for encoding the audio input (either G.711, G.726 or AAC)</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Bitrate" type="xs:int">
						<xs:annotation>
							<xs:documentation>The output bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SampleRate" type="xs:int">
						<xs:annotation>
							<xs:documentation>The output sample rate in kHz.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration">
						<xs:annotation>
							<xs:documentation>Defines the multicast settings that could be used for video streaming.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SessionTimeout" type="xs:duration">
						<xs:annotation>
							<xs:documentation>The rtsp session timeout for the related audio stream</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="AudioEncoding">
		<xs:restriction base="xs:string">
			<xs:enumeration value="G711"/>
			<xs:enumeration value="G726"/>
			<xs:enumeration value="AAC"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="AudioEncoderConfigurationOptions">
		<xs:sequence>
			<xs:element name="Options" type="tt:AudioEncoderConfigurationOption" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>list of supported AudioEncoderConfigurations</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioEncoderConfigurationOption">
		<xs:sequence>
			<xs:element name="Encoding" type="tt:AudioEncoding">
				<xs:annotation>
					<xs:documentation>The enoding used for audio data (either G.711, G.726 or AAC)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BitrateList" type="tt:IntItems">
				<xs:annotation>
					<xs:documentation>List of supported bitrates in kbps for the specified Encoding</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SampleRateList" type="tt:IntItems">
				<xs:annotation>
					<xs:documentation>List of supported Sample Rates in kHz for the specified Encoding</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--   AudioEncoder2Configuration   -->
	<!--===============================-->
	<xs:simpleType name="AudioEncodingMimeNames">
		<xs:annotation>
			<xs:documentation>Audio Media Subtypes as referenced by IANA (without the leading "audio/" Audio Media Type and except for the audio types defined in the restriction).  See also <a href="https://www.iana.org/assignments/media-types/media-types.xhtml#audio"> IANA Media Types</a>.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="PCMU"/>		<!-- G.711 uLaw -->
			<xs:enumeration value="G726">
			<xs:annotation><xs:documentation>AudioEncodingMimeName G726 is used to represent G726-16,G726-24,G726-32 and G726-40 defined in the IANA Media Types</xs:documentation></xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MP4A-LATM"/>		<!-- AAC -->
			<xs:enumeration value="mpeg4-generic"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="AudioEncoder2Configuration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="Encoding" type="xs:string">
						<xs:annotation>
							<xs:documentation>Audio Media Subtype for the audio format. For definitions see tt:AudioEncodingMimeNames and <a href="https://www.iana.org/assignments/media-types/media-types.xhtml#audio"> IANA Media Types</a>.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional multicast configuration of the audio stream.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Bitrate" type="xs:int">
						<xs:annotation>
							<xs:documentation>The output bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SampleRate" type="xs:int">
						<xs:annotation>
							<xs:documentation>The output sample rate in kHz.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!-- Options used for both Audio encoder and decoder -->
	<xs:complexType name="AudioEncoder2ConfigurationOptions">
		<xs:sequence>
			<xs:element name="Encoding" type="xs:string">
				<xs:annotation>
					<xs:documentation>Audio Media Subtype for the audio format. For definitions see tt:AudioEncodingMimeNames and <a href="https://www.iana.org/assignments/media-types/media-types.xhtml#audio"> IANA Media Types</a>.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BitrateList" type="tt:IntItems">
				<xs:annotation>
					<xs:documentation>List of supported bitrates in kbps for the specified Encoding</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SampleRateList" type="tt:IntItems">
				<xs:annotation>
					<xs:documentation>List of supported Sample Rates in kHz for the specified Encoding</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--    MetadataConfiguration      -->
	<!--===============================-->
	<xs:complexType name="MetadataConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="PTZStatus" type="tt:PTZFilter" minOccurs="0">
						<xs:annotation>
							<xs:documentation>optional element to configure which PTZ related data is to include in the metadata stream</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Events" type="tt:EventSubscription" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure the streaming of events. A client might be interested in receiving all, 
								none or some of the events produced by the device:<ul>
									<li>To get all events: Include the Events element but do not include a filter.</li>
									<li>To get no events: Do not include the Events element.</li>
									<li>To get only some events: Include the Events element and include a filter in the element.</li>
								</ul>
							</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Analytics" type="xs:boolean" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Defines whether the streamed metadata will include metadata from the analytics engines (video, cell motion, audio etc.)</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration">
						<xs:annotation>
							<xs:documentation>Defines the multicast settings that could be used for video streaming.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SessionTimeout" type="xs:duration">
						<xs:annotation>
							<xs:documentation>The rtsp session timeout for the related audio stream (when using Media2 Service, this value is deprecated and ignored)</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="Extension" type="tt:MetadataConfigurationExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:attribute name="CompressionType" type="xs:string">
					<xs:annotation>
						<xs:documentation>Optional parameter to configure compression type of Metadata payload. Use values from enumeration MetadataCompressionType.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="GeoLocation" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>Optional parameter to configure if the metadata stream shall contain the Geo Location coordinates of each target.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="ShapePolygon" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>Optional parameter to configure if the generated metadata stream should contain shape information as polygon.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZFilter">
		<xs:sequence>
			<xs:element name="Status" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the metadata stream shall contain the PTZ status (IDLE, MOVING or UNKNOWN)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Position" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the metadata stream shall contain the PTZ position</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EventSubscription">
		<xs:annotation>
			<xs:documentation>Subcription handling in the same way as base notification subscription.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Filter" type="wsnt:FilterType" minOccurs="0"/>
			<xs:element name="SubscriptionPolicy" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataConfigurationOptions">
		<xs:sequence>
			<xs:element name="PTZStatusFilterOptions" type="tt:PTZStatusFilterOptions"/>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name ="Extension" type="tt:MetadataConfigurationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="GeoLocation" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>True if the device is able to stream the Geo Located positions of each target.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxContentFilterSize" type="xs:int">
			<xs:annotation>
				<xs:documentation>A device signalling support for content filtering shall support expressions with the provided expression size.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataConfigurationOptionsExtension">
		<xs:sequence>
			<xs:element name="CompressionType" type="xs:string" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported metadata compression type. Its options shall be chosen from tt:MetadataCompressionType.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:MetadataConfigurationOptionsExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataConfigurationOptionsExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="MetadataCompressionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="None"/>
			<xs:enumeration value="GZIP"/>
			<xs:enumeration value="EXI"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="PTZStatusFilterOptions">
		<xs:sequence>
			<xs:element name="PanTiltStatusSupported" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream pan or tilt status information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomStatusSupported" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream zoom status inforamtion.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="PanTiltPositionSupported" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream the pan or tilt position.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomPositionSupported" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream zoom position information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZStatusFilterOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZStatusFilterOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--          Streaming            -->
	<!--===============================-->
	<xs:complexType name="MulticastConfiguration">
		<xs:sequence>
			<xs:element name="Address" type="tt:IPAddress">
				<xs:annotation>
					<xs:documentation>The multicast address (if this address is set to 0 no multicast streaming is enaled)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Port" type="xs:int">
				<xs:annotation>
					<xs:documentation>The RTP mutlicast destination port. A device may support RTCP. In this case the port value shall be even to allow the corresponding RTCP stream to be mapped to the next higher (odd) destination port number as defined in the RTSP specification.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TTL" type="xs:int">
				<xs:annotation>
					<xs:documentation>In case of IPv6 the TTL value is assumed as the hop limit. Note that for IPV6 and administratively scoped IPv4 multicast the primary use for hop limit / TTL is to prevent packets from (endlessly) circulating and not limiting scope. In these cases the address contains the scope.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AutoStart" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Read only property signalling that streaming is persistant. Use the methods StartMulticastStreaming and StopMulticastStreaming to switch its state.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="StreamSetup">
		<xs:sequence>
			<xs:element name="Stream" type="tt:StreamType">
				<xs:annotation>
					<xs:documentation>Defines if a multicast or unicast stream is requested</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Transport" type="tt:Transport"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="StreamType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RTP-Unicast"/>
			<xs:enumeration value="RTP-Multicast"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Transport">
		<xs:sequence>
			<xs:element name="Protocol" type="tt:TransportProtocol">
				<xs:annotation>
					<xs:documentation>Defines the network protocol for streaming, either UDP=RTP/UDP, RTSP=RTP/RTSP/TCP or HTTP=RTP/RTSP/HTTP/TCP </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tunnel" type="tt:Transport" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to describe further tunnel options. This element is normally not needed </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="TransportProtocol">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UDP"/>
			<xs:enumeration value="TCP">
				<xs:annotation>
					<xs:documentation>This value is deprecated.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RTSP"/>
			<xs:enumeration value="HTTP"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="MediaUri">
		<xs:sequence>
			<xs:element name="Uri" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Stable Uri to be used for requesting the media stream</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvalidAfterConnect" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if the Uri is only valid until the connection is established. The value shall be set to "false".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvalidAfterReboot" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if the Uri is invalid after a reboot of the device. The value shall be set to "false".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Timeout" type="xs:duration">
				<xs:annotation>
					<xs:documentation>Duration how long the Uri is valid. This parameter shall be set to PT0S to indicate that this stream URI is indefinitely valid even if the profile changes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--   End, Media Related Types    -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--    Discovery Related Types    -->
	<!--===============================-->
	<xs:simpleType name="ScopeDefinition">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Fixed"/>
			<xs:enumeration value="Configurable"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Scope">
		<xs:sequence>
			<xs:element name="ScopeDef" type="tt:ScopeDefinition">
				<xs:annotation>
					<xs:documentation>Indicates if the scope is fixed or configurable.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ScopeItem" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Scope item URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="DiscoveryMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Discoverable"/>
			<xs:enumeration value="NonDiscoverable"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<!-- End, Discovery Related Types  -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--     Network Related Types     -->
	<!--===============================-->
	<xs:complexType name="NetworkInterface">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Enabled" type="xs:boolean">
						<xs:annotation>
							<xs:documentation>Indicates whether or not an interface is enabled.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Info" type="tt:NetworkInterfaceInfo" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Network interface information</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Link" type="tt:NetworkInterfaceLink" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Link configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="IPv4" type="tt:IPv4NetworkInterface" minOccurs="0">
						<xs:annotation>
							<xs:documentation>IPv4 network interface configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="IPv6" type="tt:IPv6NetworkInterface" minOccurs="0">
						<xs:annotation>
							<xs:documentation>IPv6 network interface configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>					
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceLink">
		<xs:sequence>
			<xs:element name="AdminSettings" type="tt:NetworkInterfaceConnectionSetting">
				<xs:annotation>
					<xs:documentation>Configured link settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OperSettings" type="tt:NetworkInterfaceConnectionSetting">
				<xs:annotation>
					<xs:documentation>Current active link settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InterfaceType" type="tt:IANA-IfTypes">
				<xs:annotation>
					<xs:documentation>Integer indicating interface type, for example: 6 is ethernet.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceConnectionSetting">
		<xs:sequence>
			<xs:element name="AutoNegotiation" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Auto negotiation on/off.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="xs:int">
				<xs:annotation>
					<xs:documentation>Speed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Duplex" type="tt:Duplex">
				<xs:annotation>
					<xs:documentation>Duplex type, Half or Full.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Duplex">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Full"/>
			<xs:enumeration value="Half"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IANA-IfTypes">
		<xs:restriction base="xs:int">
			<xs:annotation>
				<xs:documentation>
				For valid numbers, please refer to http://www.iana.org/assignments/ianaiftype-mib.
				</xs:documentation>
			</xs:annotation>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceInfo">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Network interface name, for example eth0.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="HwAddress" type="tt:HwAddress">
				<xs:annotation>
					<xs:documentation>Network interface MAC address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MTU" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Maximum transmission unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6NetworkInterface">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv6 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Config" type="tt:IPv6Configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv4NetworkInterface">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv4 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Config" type="tt:IPv4Configuration">
				<xs:annotation>
					<xs:documentation>IPv4 configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv4Configuration">
		<xs:sequence>
			<xs:element name="Manual" type="tt:PrefixedIPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually added IPv4 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LinkLocal" type="tt:PrefixedIPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Link local address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FromDHCP" type="tt:PrefixedIPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 address configured by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not DHCP is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6Configuration">
		<xs:sequence>
			<xs:element name="AcceptRouterAdvert" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether router advertisment is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="tt:IPv6DHCPConfiguration">
				<xs:annotation>
					<xs:documentation>DHCP configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Manual" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually entered IPv6 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LinkLocal" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of link local IPv6 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FromDHCP" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of IPv6 addresses configured by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FromRA" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of IPv6 addresses configured by using router advertisment.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:IPv6ConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6ConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IPv6DHCPConfiguration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Auto"/>
			<xs:enumeration value="Stateful"/>
			<xs:enumeration value="Stateless"/>
			<xs:enumeration value="Off"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkProtocol">
		<xs:sequence>
			<xs:element name="Name" type="tt:NetworkProtocolType">
				<xs:annotation>
					<xs:documentation>Network protocol type string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if the protocol is enabled or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Port" type="xs:int" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>The port that is used by the protocol.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkProtocolExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkProtocolExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="NetworkProtocolType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HTTP"/>
			<xs:enumeration value="HTTPS"/>
			<xs:enumeration value="RTSP"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="NetworkHostType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IPv4"/>
			<xs:enumeration value="IPv6"/>
			<xs:enumeration value="DNS"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkHost">
		<xs:sequence>
			<xs:element name="Type" type="tt:NetworkHostType">
				<xs:annotation>
					<xs:documentation>Network host type: IPv4, IPv6 or DNS.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv4Address" type="tt:IPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6Address" type="tt:IPv6Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DNSname" type="tt:DNSName" minOccurs="0">
				<xs:annotation>
					<xs:documentation>DNS name.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkHostExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkHostExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPAddress">
		<xs:sequence>
			<xs:element name="Type" type="tt:IPType">
				<xs:annotation>
					<xs:documentation>Indicates if the address is an IPv4 or IPv6 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv4Address" type="tt:IPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6Address" type="tt:IPv6Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 address</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PrefixedIPv4Address">
		<xs:sequence>
			<xs:element name="Address" type="tt:IPv4Address">
				<xs:annotation>
					<xs:documentation>IPv4 address</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PrefixLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Prefix/submask length</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IPv4Address">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="PrefixedIPv6Address">
		<xs:sequence>
			<xs:element name="Address" type="tt:IPv6Address">
				<xs:annotation>
					<xs:documentation>IPv6 address</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PrefixLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Prefix/submask length</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IPv6Address">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="HwAddress">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IPType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IPv4"/>
			<xs:enumeration value="IPv6"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="DNSName">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="HostnameInformation">
		<xs:sequence>
			<xs:element name="FromDHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether the hostname has been obtained from DHCP or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Name" type="xs:token" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates the device hostname or an empty string if no hostname has been assigned.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:HostnameInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="HostnameInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DNSInformation">
		<xs:sequence>
			<xs:element name="FromDHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not DNS information is retrieved from DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SearchDomain" type="xs:token" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Search domain.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DNSFromDHCP" type="tt:IPAddress" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of DNS addresses received from DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DNSManual" type="tt:IPAddress" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually entered DNS addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:DNSInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DNSInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NTPInformation">
		<xs:sequence>
			<xs:element name="FromDHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if NTP information is to be retrieved by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NTPFromDHCP" type="tt:NetworkHost" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of NTP addresses retrieved by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NTPManual" type="tt:NetworkHost" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually entered NTP addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NTPInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NTPInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Domain">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IPAddressFilterType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Allow"/>
			<xs:enumeration value="Deny"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="DynamicDNSInformation">
		<xs:sequence>
			<xs:element name="Type" type="tt:DynamicDNSType">
				<xs:annotation>
					<xs:documentation>Dynamic DNS type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Name" type="tt:DNSName" minOccurs="0">
				<xs:annotation>
					<xs:documentation>DNS name.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TTL" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Time to live.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:DynamicDNSInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DynamicDNSInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="DynamicDNSType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NoUpdate"/>
			<xs:enumeration value="ClientUpdates"/>
			<xs:enumeration value="ServerUpdates"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceSetConfiguration">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not an interface is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Link" type="tt:NetworkInterfaceConnectionSetting" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Link configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MTU" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Maximum transmission unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv4" type="tt:IPv4NetworkInterfaceSetConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 network interface configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6" type="tt:IPv6NetworkInterfaceSetConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 network interface configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>			
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6NetworkInterfaceSetConfiguration">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv6 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AcceptRouterAdvert" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether router advertisment is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Manual" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually added IPv6 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="tt:IPv6DHCPConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>DHCP configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv4NetworkInterfaceSetConfiguration">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv4 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Manual" type="tt:PrefixedIPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually added IPv4 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not DHCP is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkGateway">
		<xs:sequence>
			<xs:element name="IPv4Address" type="tt:IPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>IPv4 address string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6Address" type="tt:IPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>IPv6 address string.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkZeroConfiguration">
		<xs:sequence>
			<xs:element name="InterfaceToken" type="tt:ReferenceToken">
				<xs:annotation>
					<xs:documentation>Unique identifier of network interface.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether the zero-configuration is enabled or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Addresses" type="tt:IPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>The zero-configuration IPv4 address(es)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkZeroConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkZeroConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Additional" type="tt:NetworkZeroConfiguration" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Optional array holding the configuration for the second and possibly further interfaces.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkZeroConfigurationExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkZeroConfigurationExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPAddressFilter">
		<xs:sequence>
			<xs:element name="Type" type="tt:IPAddressFilterType"/>
			<xs:element name="IPv4Address" type="tt:PrefixedIPv4Address" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="IPv6Address" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:IPAddressFilterExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPAddressFilterExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--===============================-->
	<!--  End, network Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   Capabilities Related Types  -->
	<!--===============================-->
	<xs:simpleType name="CapabilityCategory">
		<xs:restriction base="xs:string">
			<xs:enumeration value="All"/>
			<xs:enumeration value="Analytics"/>
			<xs:enumeration value="Device"/>
			<xs:enumeration value="Events"/>
			<xs:enumeration value="Imaging"/>
			<xs:enumeration value="Media"/>
			<xs:enumeration value="PTZ"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Capabilities">
		<xs:sequence>
			<xs:element name="Device" type="tt:DeviceCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Device capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Events" type="tt:EventCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Event capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Imaging" type="tt:ImagingCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Imaging capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Media" type="tt:MediaCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Media capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZ" type="tt:PTZCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>PTZ capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DeviceCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Device service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Network" type="tt:NetworkCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Network capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="System" type="tt:SystemCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>System capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Security" type="tt:SecurityCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Security capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:DeviceCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DeviceCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EventCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Event service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WSSubscriptionPolicySupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Subscription policy is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WSPullPointSupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Pull Point is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WSPausableSubscriptionManagerInterfaceSupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Pausable Subscription Manager Interface is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MediaCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Media service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StreamingCapabilities" type="tt:RealTimeStreamingCapabilities">
				<xs:annotation>
					<xs:documentation>Streaming capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:MediaCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MediaCapabilitiesExtension">
		<xs:sequence>
			<xs:element name="ProfileCapabilities" type="tt:ProfileCapabilities"/>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RealTimeStreamingCapabilities">
		<xs:sequence>
			<xs:element name="RTPMulticast" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not RTP multicast is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RTP_TCP" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not RTP over TCP is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RTP_RTSP_TCP" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not RTP/RTSP/TCP is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RealTimeStreamingCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RealTimeStreamingCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileCapabilities">
		<xs:sequence>
			<xs:element name="MaximumNumberOfProfiles" type="xs:int">
				<xs:annotation>
					<xs:documentation>Maximum number of profiles.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkCapabilities">
		<xs:sequence>
			<xs:element name="IPFilter" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IP filtering is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZeroConfiguration" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not zeroconf is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPVersion6" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv6 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DynDNS" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not  is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Dot11Configuration" type="xs:boolean" minOccurs="0"/>			
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SecurityCapabilities">
		<xs:sequence>
			<xs:element name="TLS1.1" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not TLS 1.1 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TLS1.2" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not TLS 1.2 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OnboardKeyGeneration" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not onboard key generation is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AccessPolicyConfig" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not access policy configuration is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="X.509Token" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security X.509 token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SAMLToken" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security SAML token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="KerberosToken" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security Kerberos token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RELToken" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security REL token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:SecurityCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SecurityCapabilitiesExtension">
		<xs:sequence>
			<xs:element name="TLS1.0" type="xs:boolean"/>
			<xs:element name="Extension" type="tt:SecurityCapabilitiesExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SecurityCapabilitiesExtension2">
		<xs:sequence>
			<xs:element name="Dot1X" type="xs:boolean"/>
			<xs:element name="SupportedEAPMethod" type="xs:int" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>EAP Methods supported by the device. The int values refer to the <a href="http://www.iana.org/assignments/eap-numbers/eap-numbers.xhtml">IANA EAP Registry</a>.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RemoteUserHandling" type="xs:boolean"/>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemCapabilities">
		<xs:sequence>
			<xs:element name="DiscoveryResolve" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Discovery resolve requests are supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DiscoveryBye" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Discovery Bye is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RemoteDiscovery" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not remote discovery is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SystemBackup" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not system backup is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SystemLogging" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not system logging is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FirmwareUpgrade" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not firmware upgrade is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedVersions" type="tt:OnvifVersion" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Indicates supported ONVIF version(s).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:SystemCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="HttpFirmwareUpgrade" type="xs:boolean" minOccurs="0"/>
			<xs:element name="HttpSystemBackup" type="xs:boolean" minOccurs="0"/>
			<xs:element name="HttpSystemLogging" type="xs:boolean" minOccurs="0"/>
			<xs:element name="HttpSupportInformation" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Extension" type="tt:SystemCapabilitiesExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemCapabilitiesExtension2">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OnvifVersion">
		<xs:sequence>
			<xs:element name="Major" type="xs:int">
				<xs:annotation>
					<xs:documentation>Major version number.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Minor" type="xs:int">
				<xs:annotation>
					<xs:documentation>
						Two digit minor version number.
						If major version number is less than "16", X.0.1 maps to "01" and X.2.1 maps to "21" where X stands for Major version number.
						Otherwise, minor number is month of release, such as "06" for June.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Imaging service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>PTZ service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--===============================-->
	<!--End, Capabilities Related Types-->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--    System Related Types       -->
	<!--===============================-->
	<xs:simpleType name="SystemLogType">
		<xs:annotation>
			<xs:documentation>Enumeration describing the available system log modes.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="System">
				<xs:annotation>
					<xs:documentation>Indicates that a system log is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Access">
				<xs:annotation>
					<xs:documentation>Indicates that a access log is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="SystemLog">
		<xs:sequence>
			<xs:element name="Binary" type="tt:AttachmentData" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The log information as attachment data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="String" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The log information as character data.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SupportInformation">
		<xs:sequence>
			<xs:element name="Binary" type="tt:AttachmentData" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The support information as attachment data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="String" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The support information as character data.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BinaryData">
		<xs:sequence>
			<xs:element name="Data" type="xs:base64Binary" nillable="false">
				<xs:annotation>
					<xs:documentation>base64 encoded binary data.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute ref="xmime:contentType" use="optional"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AttachmentData">
		<xs:sequence>
			<xs:element ref="xop:Include"/>
		</xs:sequence>
		<xs:attribute ref="xmime:contentType" use="optional"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BackupFile">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Data" type="tt:AttachmentData"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemLogUriList">
		<xs:sequence>
			<xs:element name="SystemLog" type="tt:SystemLogUri" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemLogUri">
		<xs:sequence>
			<xs:element name="Type" type="tt:SystemLogType"/>
			<xs:element name="Uri" type="xs:anyURI"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="FactoryDefaultType">
		<xs:annotation>
			<xs:documentation>Enumeration describing the available factory default modes.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="Hard">
				<xs:annotation>
					<xs:documentation>Indicates that a hard factory default is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Soft">
				<xs:annotation>
					<xs:documentation>Indicates that a soft factory default is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="SetDateTimeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Manual">
				<xs:annotation>
					<xs:documentation>Indicates that the date and time are set manually.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="NTP">
				<xs:annotation>
					<xs:documentation>Indicates that the date and time are set through NTP</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="SystemDateTime">
		<xs:annotation>
			<xs:documentation>General date time inforamtion returned by the GetSystemDateTime method.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DateTimeType" type="tt:SetDateTimeType">
				<xs:annotation>
					<xs:documentation>Indicates if the time is set manully or through NTP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DaylightSavings" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Informative indicator whether daylight savings is currently on/off.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeZone" type="tt:TimeZone" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Timezone information in Posix format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UTCDateTime" type="tt:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Current system date and time in UTC format. This field is mandatory since version 2.0.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LocalDateTime" type="tt:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date and time in local format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:SystemDateTimeExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemDateTimeExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DateTime">
		<xs:sequence>
			<xs:element name="Time" type="tt:Time"/>
			<xs:element name="Date" type="tt:Date"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Date">
		<xs:sequence>
			<xs:element name="Year" type="xs:int"/>
			<xs:element name="Month" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 1 to 12.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Day" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 1 to 31.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Time">
		<xs:sequence>
			<xs:element name="Hour" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 0 to 23.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Minute" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 0 to 59.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Second" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 0 to 61 (typically 59).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="TimeZone">
		<xs:annotation>
			<xs:documentation>
				The TZ format is specified by POSIX, please refer to POSIX 1003.1 section 8.3<br/>
				Example: Europe, Paris TZ=CET-1CEST,M3.5.0/2,M10.5.0/3<br/>
				CET = designation for standard time when daylight saving is not in force<br/>
				-1 = offset in hours = negative so 1 hour east of Greenwich meridian<br/>
				CEST = designation when daylight saving is in force ("Central European Summer Time")<br/>
				, = no offset number between code and comma, so default to one hour ahead for daylight saving<br/>
				M3.5.0 = when daylight saving starts = the last Sunday in March (the "5th" week means the last in the month)<br/>
				/2, = the local time when the switch occurs = 2 a.m. in this case<br/>
				M10.5.0 = when daylight saving ends = the last Sunday in October.<br/>
				/3, = the local time when the switch occurs = 3 a.m. in this case<br/>
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TZ" type="xs:token">
				<xs:annotation>
					<xs:documentation>Posix timezone string.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--   End, System Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   RemoteUser Handling Types    -->
	<!--===============================-->
	<xs:complexType name="RemoteUser">
		<xs:sequence>
			<xs:element name="Username" type="xs:string"/>
			<xs:element name="Password" type="xs:string" minOccurs="0"/>
			<xs:element name="UseDerivedPassword" type="xs:boolean"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--   End, RemoteUser Handling Types    -->
	<!--===============================-->
	<!--===============================-->
	<!--   UserToken Handling Types    -->
	<!--===============================-->
	<xs:simpleType name="UserLevel">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Administrator"/>
			<xs:enumeration value="Operator"/>
			<xs:enumeration value="User"/>
			<xs:enumeration value="Anonymous"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="User">
		<xs:sequence>
			<xs:element name="Username" type="xs:string">
				<xs:annotation>
					<xs:documentation>Username string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Password" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Password string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UserLevel" type="tt:UserLevel">
				<xs:annotation>
					<xs:documentation>User level string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:UserExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="UserExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!-- End, UserToken Handling Types -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   Security Management Types   -->
	<!--===============================-->
	<xs:complexType name="CertificateGenerationParameters">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token" minOccurs="0"/>
			<xs:element name="Subject" type="xs:string" minOccurs="0"/>
			<xs:element name="ValidNotBefore" type="xs:token" minOccurs="0"/>
			<xs:element name="ValidNotAfter" type="xs:token" minOccurs="0"/>
			<xs:element name="Extension" type="tt:CertificateGenerationParametersExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateGenerationParametersExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Certificate">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token">
				<xs:annotation>
					<xs:documentation>Certificate id.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Certificate" type="tt:BinaryData">
				<xs:annotation>
					<xs:documentation>base64 encoded DER representation of certificate.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateStatus">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token">
				<xs:annotation>
					<xs:documentation>Certificate id.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Status" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not a certificate is used in a HTTPS configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateWithPrivateKey">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token" minOccurs="0"/>
			<xs:element name="Certificate" type="tt:BinaryData"/>
			<xs:element name="PrivateKey" type="tt:BinaryData"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateInformation">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token"/>
			<xs:element name="IssuerDN" type="xs:string" minOccurs="0"/>
			<xs:element name="SubjectDN" type="xs:string" minOccurs="0"/>
			<xs:element name="KeyUsage" type="tt:CertificateUsage" minOccurs="0"/>
			<xs:element name="ExtendedKeyUsage" type="tt:CertificateUsage" minOccurs="0"/>
			<xs:element name="KeyLength" type="xs:int" minOccurs="0"/>
			<xs:element name="Version" type="xs:string" minOccurs="0"/>
			<xs:element name="SerialNum" type="xs:string" minOccurs="0"/>
			<xs:element name="SignatureAlgorithm" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Validity Range is from "NotBefore" to "NotAfter"; the corresponding DateTimeRange is from "From" to "Until"</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:CertificateInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateUsage">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="Critical" type="xs:boolean" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--End, Security management Types -->
	<!--===============================-->
	<!--===============================-->

	<!--===============================-->
	<!--===============================-->
	<!--    Start PTZ Related Types    -->
	<!--===============================-->
	<xs:complexType name="PTZNode">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Name" type="tt:Name" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                A unique identifier that is used to reference PTZ Nodes.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SupportedPTZSpaces" type="tt:PTZSpaces">
						<xs:annotation>
							<xs:documentation>
                A list of Coordinate Systems available for the PTZ Node. For each Coordinate System, the PTZ Node MUST specify its allowed range.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="MaximumNumberOfPresets" type="xs:int">
						<xs:annotation>
							<xs:documentation>
                All preset operations MUST be available for this PTZ Node if one preset is supported.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="HomeSupported" type="xs:boolean">
						<xs:annotation>
							<xs:documentation>
                A boolean operator specifying the availability of a home position. If set to true, the Home Position Operations MUST be available for this PTZ Node.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="AuxiliaryCommands" type="tt:AuxiliaryData" minOccurs="0" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>
                A list of supported Auxiliary commands. If the list is not empty, the Auxiliary Operations MUST be available for this PTZ Node.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:PTZNodeExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:attribute name="FixedHomePosition" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>
							Indication whether the HomePosition of a Node is fixed or it can be changed via the SetHomePosition command.
						</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="GeoMove" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>
							Indication whether the Node supports the geo-referenced move command.
						</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZNodeExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="SupportedPresetTour" type="tt:PTZPresetTourSupported" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            Detail of supported Preset Tour feature.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZNodeExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZNodeExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSupported">
		<xs:sequence>
			<xs:element name="MaximumNumberOfPresetTours" type="xs:int">
				<xs:annotation>
					<xs:documentation>Indicates number of preset tours that can be created. Required preset tour operations shall be available for this PTZ Node if one or more preset tour is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZPresetTourOperation" type="tt:PTZPresetTourOperation" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Indicates which preset tour operations are available for this PTZ Node.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourSupportedExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSupportedExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="NodeToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>
                A mandatory reference to the PTZ Node that the PTZ Configuration belongs to.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultAbsolutePantTiltPositionSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports absolute Pan/Tilt movements, it shall specify one Absolute Pan/Tilt Position Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultAbsoluteZoomPositionSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports absolute zoom movements, it shall specify one Absolute Zoom Position Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultRelativePanTiltTranslationSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports relative Pan/Tilt movements, it shall specify one RelativePan/Tilt Translation Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultRelativeZoomTranslationSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports relative zoom movements, it shall specify one Relative Zoom Translation Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultContinuousPanTiltVelocitySpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports continuous Pan/Tilt movements, it shall specify one Continuous Pan/Tilt Velocity Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultContinuousZoomVelocitySpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports continuous zoom movements, it shall specify one Continuous Zoom Velocity Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultPTZSpeed" type="tt:PTZSpeed" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports absolute or relative PTZ movements, it shall specify corresponding default Pan/Tilt and Zoom speeds.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultPTZTimeout" type="xs:duration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports continuous movements, it shall specify a default timeout, after which the movement stops.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="PanTiltLimits" type="tt:PanTiltLimits" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                The Pan/Tilt limits element should be present for a PTZ Node that supports an absolute Pan/Tilt. If the element is present it signals the support for configurable Pan/Tilt limits. If limits are enabled, the Pan/Tilt movements shall always stay within the specified range. The Pan/Tilt limits are disabled by setting the limits to –INF or +INF.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ZoomLimits" type="tt:ZoomLimits" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                The Zoom limits element should be present for a PTZ Node that supports absolute zoom. If the element is present it signals the supports for configurable Zoom limits. If limits are enabled the zoom movements shall always stay within the specified range. The Zoom limits are disabled by settings the limits to -INF and +INF.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:PTZConfigurationExtension" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
              </xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
				<xs:attribute name="MoveRamp" type="xs:int">
					<xs:annotation>
						<xs:documentation>The optional acceleration ramp used by the device when moving.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="PresetRamp" type="xs:int">
					<xs:annotation>
						<xs:documentation>The optional acceleration ramp used by the device when recalling presets.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="PresetTourRamp" type="xs:int">
					<xs:annotation>
						<xs:documentation>The optional acceleration ramp used by the device when executing PresetTours.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="PTControlDirection" type="tt:PTControlDirection" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure PT Control Direction related features.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZConfigurationExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirection">
		<xs:sequence>
			<xs:element name="EFlip" type="tt:EFlip" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure related parameters for E-Flip.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Reverse" type="tt:Reverse" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure related parameters for reversing of PT Control Direction.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTControlDirectionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirectionExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EFlip">
		<xs:sequence>
			<xs:element name="Mode" type="tt:EFlipMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable E-Flip feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Reverse">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ReverseMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable Reverse feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="EFlipMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="ReverseMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationOptions">
		<xs:sequence>
			<xs:element name="Spaces" type="tt:PTZSpaces">
				<xs:annotation>
					<xs:documentation>
            A list of supported coordinate systems including their range limitations.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZTimeout" type="tt:DurationRange">
				<xs:annotation>
					<xs:documentation>
            A timeout Range within which Timeouts are accepted by the PTZ Node.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="PTControlDirection" type="tt:PTControlDirectionOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported options for PT Direction Control.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZConfigurationOptions2" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="PTZRamps" type="tt:IntList">
			<xs:annotation>
				<xs:documentation>
					The list of acceleration ramps supported by the device. The
					smallest acceleration value corresponds to the minimal index, the
					highest acceleration corresponds to the maximum index.					
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationOptions2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirectionOptions">
		<xs:sequence>
			<xs:element name="EFlip" type="tt:EFlipOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported options for EFlip feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Reverse" type="tt:ReverseOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported options for Reverse feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTControlDirectionOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirectionOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EFlipOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:EFlipMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Options of EFlip mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:EFlipOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EFlipOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ReverseOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ReverseMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Options of Reverse mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ReverseOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ReverseOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PanTiltLimits">
		<xs:sequence>
			<xs:element name="Range" type="tt:Space2DDescription">
				<xs:annotation>
					<xs:documentation>
            A range of pan tilt limits.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ZoomLimits">
		<xs:sequence>
			<xs:element name="Range" type="tt:Space1DDescription">
				<xs:annotation>
					<xs:documentation>
            A range of zoom limit
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZSpaces">
		<xs:sequence>
			<xs:element name="AbsolutePanTiltPositionSpace" type="tt:Space2DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Pan/Tilt Position space is provided by every PTZ node that supports absolute Pan/Tilt, since it does not relate to a specific physical range. 
			Instead, the range should be defined as the full range of the PTZ unit normalized to the range -1 to 1 resulting in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AbsoluteZoomPositionSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Zoom Position Space is provided by every PTZ node that supports absolute Zoom, since it does not relate to a specific physical range. 
			Instead, the range should be defined as the full range of the Zoom normalized to the range 0 (wide) to 1 (tele). 
			There is no assumption about how the generic zoom range is mapped to magnification, FOV or other physical zoom dimension.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RelativePanTiltTranslationSpace" type="tt:Space2DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Pan/Tilt translation space is provided by every PTZ node that supports relative Pan/Tilt, since it does not relate to a specific physical range. 
			Instead, the range should be defined as the full positive and negative translation range of the PTZ unit normalized to the range -1 to 1, 
			where positive translation would mean clockwise rotation or movement in right/up direction resulting in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RelativeZoomTranslationSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Zoom Translation Space is provided by every PTZ node that supports relative Zoom, since it does not relate to a specific physical range. 
			Instead, the corresponding absolute range should be defined as the full positive and negative translation range of the Zoom normalized to the range -1 to1, 
			where a positive translation maps to a movement in TELE direction. The translation is signed to indicate direction (negative is to wide, positive is to tele). 
			There is no assumption about how the generic zoom range is mapped to magnification, FOV or other physical zoom dimension. This results in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContinuousPanTiltVelocitySpace" type="tt:Space2DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The generic Pan/Tilt velocity space shall be provided by every PTZ node, since it does not relate to a specific physical range. 
			Instead, the range should be defined as a range of the PTZ unit’s speed normalized to the range -1 to 1, where a positive velocity would map to clockwise 
			rotation or movement in the right/up direction. A signed speed can be independently specified for the pan and tilt component resulting in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContinuousZoomVelocitySpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The generic zoom velocity space specifies a zoom factor velocity without knowing the underlying physical model. The range should be normalized from -1 to 1, 
			where a positive velocity would map to TELE direction. A generic zoom velocity space description resembles the following.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PanTiltSpeedSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The speed space specifies the speed for a Pan/Tilt movement when moving to an absolute position or to a relative translation. 
			In contrast to the velocity spaces, speed spaces do not contain any directional information. The speed of a combined Pan/Tilt 
			movement is represented by a single non-negative scalar value.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomSpeedSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The speed space specifies the speed for a Zoom movement when moving to an absolute position or to a relative translation. 
			In contrast to the velocity spaces, speed spaces do not contain any directional information. 
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZSpacesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZSpacesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Space2DDescription">
		<xs:sequence>
			<xs:element name="URI" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>
            A URI of coordinate systems.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="XRange" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
            A range of x-axis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="YRange" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
            A range of y-axis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Space1DDescription">
		<xs:sequence>
			<xs:element name="URI" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>
            A URI of coordinate systems.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="XRange" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
            A range of x-axis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZSpeed">
		<xs:sequence>
			<xs:element name="PanTilt" type="tt:Vector2D" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Pan and tilt speed. The x component corresponds to pan and the y component to tilt. If omitted in a request, the current (if any) PanTilt movement should not be affected. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Zoom" type="tt:Vector1D" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            A zoom speed. If omitted in a request, the current (if any) Zoom movement should not be affected.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPreset">
		<xs:sequence>
			<xs:element name="Name" type="tt:Name" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            A list of preset position name.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZPosition" type="tt:PTZVector" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            A list of preset position.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken">
			<xs:annotation>
				<xs:documentation>
        </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="AuxiliaryData">
		<xs:restriction base="xs:string">
			<xs:maxLength value="128"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="PTZPresetTourState">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Idle"/>
			<xs:enumeration value="Touring"/>
			<xs:enumeration value="Paused"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="PTZPresetTourDirection">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Forward"/>
			<xs:enumeration value="Backward"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="PTZPresetTourOperation">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Start"/>
			<xs:enumeration value="Stop"/>
			<xs:enumeration value="Pause"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="PresetTour">
		<xs:sequence>
			<xs:element name="Name" type="tt:Name" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Readable name of the preset tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Status" type="tt:PTZPresetTourStatus">
				<xs:annotation>
					<xs:documentation>Read only parameters to indicate the status of the preset tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AutoStart" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Auto Start flag of the preset tour. True allows the preset tour to be activated always.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StartingCondition" type="tt:PTZPresetTourStartingCondition">
				<xs:annotation>
					<xs:documentation>Parameters to specify the detail behavior of the preset tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TourSpot" type="tt:PTZPresetTourSpot" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A list of detail of touring spots including preset positions.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken">
			<xs:annotation>
				<xs:documentation>Unique identifier of this preset tour.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSpot">
		<xs:sequence>
			<xs:element name="PresetDetail" type="tt:PTZPresetTourPresetDetail">
				<xs:annotation>
					<xs:documentation>Detail definition of preset position of the tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:PTZSpeed" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify Pan/Tilt and Zoom speed on moving toward this tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StayTime" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify time duration of staying on this tour sport.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourSpotExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSpotExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourPresetDetail">
		<xs:sequence>
			<xs:choice>
				<xs:element name="PresetToken" type="tt:ReferenceToken">
					<xs:annotation>
						<xs:documentation>Option to specify the preset position with Preset Token defined in advance.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Home" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>Option to specify the preset position with the home position of this PTZ Node. "False" to this parameter shall be treated as an invalid argument.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="PTZPosition" type="tt:PTZVector">
					<xs:annotation>
						<xs:documentation>Option to specify the preset position with vector of PTZ node directly.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="TypeExtension" type="tt:PTZPresetTourTypeExtension"/>
			</xs:choice>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourTypeExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStatus">
		<xs:sequence>
			<xs:element name="State" type="tt:PTZPresetTourState">
				<xs:annotation>
					<xs:documentation>Indicates state of this preset tour by Idle/Touring/Paused.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CurrentTourSpot" type="tt:PTZPresetTourSpot" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates a tour spot currently staying.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourStatusExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStatusExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingCondition">
		<xs:sequence>
			<xs:element name="RecurringTime" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify how many times the preset tour is recurred.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RecurringDuration" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify how long time duration the preset tour is recurred.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Direction" type="tt:PTZPresetTourDirection" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to choose which direction the preset tour goes. Forward shall be chosen in case it is omitted.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourStartingConditionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="RandomPresetOrder" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>Execute presets in random order. If set to true and Direction is also present, Direction will be ignored and presets of the Tour will be recalled randomly.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingConditionExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourOptions">
		<xs:sequence>
			<xs:element name="AutoStart" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not the AutoStart is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StartingCondition" type="tt:PTZPresetTourStartingConditionOptions">
				<xs:annotation>
					<xs:documentation>Supported options for Preset Tour Starting Condition.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TourSpot" type="tt:PTZPresetTourSpotOptions">
				<xs:annotation>
					<xs:documentation>Supported options for Preset Tour Spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSpotOptions">
		<xs:sequence>
			<xs:element name="PresetDetail" type="tt:PTZPresetTourPresetDetailOptions">
				<xs:annotation>
					<xs:documentation>Supported options for detail definition of preset position of the tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StayTime" type="tt:DurationRange">
				<xs:annotation>
					<xs:documentation>Supported range of stay time for a tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourPresetDetailOptions">
		<xs:sequence>
			<xs:element name="PresetToken" type="tt:ReferenceToken" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A list of available Preset Tokens for tour spots.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Home" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>An option to indicate Home postion for tour spots.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PanTiltPositionSpace" type="tt:Space2DDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Pan and Tilt for tour spots.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomPositionSpace" type="tt:Space1DDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Zoom for a tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourPresetDetailOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourPresetDetailOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingConditionOptions">
		<xs:sequence>
			<xs:element name="RecurringTime" type="tt:IntRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Recurring Time.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RecurringDuration" type="tt:DurationRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Recurring Duration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Direction" type="tt:PTZPresetTourDirection" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options for Direction of Preset Tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourStartingConditionOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingConditionOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="MoveAndTrackMethod">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PresetToken"/>
			<xs:enumeration value="GeoLocation"/>
			<xs:enumeration value="PTZVector"/>
			<xs:enumeration value="ObjectID"/>
		</xs:restriction>
	</xs:simpleType>			
	<!--===============================-->
	<!--     End, PTZ Related Types    -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--      Imaging Related Types    -->
	<!--===============================-->
	<xs:complexType name="ImagingStatus">
		<xs:sequence>
			<xs:element name="FocusStatus" type="tt:FocusStatus"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusStatus">
		<xs:sequence>
			<xs:element name="Position" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Status of focus position.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MoveStatus" type="tt:MoveStatus">
				<xs:annotation>
					<xs:documentation>
				Status of focus MoveStatus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Error" type="xs:string">
				<xs:annotation>
					<xs:documentation>
				Error status of focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusConfiguration">
		<xs:sequence>
			<xs:element name="AutoFocusMode" type="tt:AutoFocusMode"/>
			<xs:element name="DefaultSpeed" type="xs:float"/>
			<xs:element name="NearLimit" type="xs:float">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus near limit (unit: meter).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FarLimit" type="xs:float">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus far limit (unit: meter).
If set to 0.0, infinity will be used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="AutoFocusMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="MANUAL"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="AFModes">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OnceAfterMove">
				<xs:annotation><xs:documentation>Focus of a moving camera is updated only once after stopping a pan, tilt or zoom movement.</xs:documentation></xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="ImagingSettings">
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensation" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Enabled/disabled BLC mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Brightness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Image brightness (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ColorSaturation" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Color saturation of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contrast" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contrast of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Exposure" type="tt:Exposure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Exposure mode of the device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Focus" type="tt:FocusConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Focus configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IrCutFilter" type="tt:IrCutFilterMode" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Infrared Cutoff Filter settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sharpness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sharpness of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>WDR settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WhiteBalance" type="tt:WhiteBalance" minOccurs="0">
				<xs:annotation>
					<xs:documentation>White balance settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Exposure">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode">
				<xs:annotation>
					<xs:documentation>
					Exposure Mode
					<ul>
							<li>Auto – Enabled the exposure algorithm on the NVT.</li>
							<li>Manual – Disabled exposure algorithm on the NVT.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Priority" type="tt:ExposurePriority">
				<xs:annotation>
					<xs:documentation>
				The exposure priority mode (low noise/framerate).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Window" type="tt:Rectangle">
				<xs:annotation>
					<xs:documentation>
				Rectangular exposure mask.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinExposureTime" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Minimum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxExposureTime" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Maximum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinIris" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the iris range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxIris" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the iris range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExposureTime" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				The fixed exposure time used by the image sensor (&#956;s).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gain" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				The fixed gain used by the image sensor (dB).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Iris" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				The fixed attenuation of input light affected by the iris (dB). 0dB maps to a fully opened iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="WideDynamicMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRange">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode">
				<xs:annotation>
					<xs:documentation>
				White dynamic range (on/off)
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Optional level parameter (unitless)
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="BacklightCompensationMode">
		<xs:annotation>
			<xs:documentation>Enumeration describing the available backlight compenstation modes.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF">
				<xs:annotation>
					<xs:documentation>Backlight compensation is disabled.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ON">
				<xs:annotation>
					<xs:documentation>Backlight compensation is enabled.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensation">
		<xs:sequence>
			<xs:element name="Mode" type="tt:BacklightCompensationMode">
				<xs:annotation>
					<xs:documentation>Backlight compensation mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ExposurePriority">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LowNoise"/>
			<xs:enumeration value="FrameRate"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions">
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensationOptions"/>
			<xs:element name="Brightness" type="tt:FloatRange"/>
			<xs:element name="ColorSaturation" type="tt:FloatRange"/>
			<xs:element name="Contrast" type="tt:FloatRange"/>
			<xs:element name="Exposure" type="tt:ExposureOptions"/>
			<xs:element name="Focus" type="tt:FocusOptions"/>
			<xs:element name="IrCutFilterModes" type="tt:IrCutFilterMode" maxOccurs="unbounded"/>
			<xs:element name="Sharpness" type="tt:FloatRange"/>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRangeOptions"/>
			<xs:element name="WhiteBalance" type="tt:WhiteBalanceOptions"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRangeOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode" maxOccurs="unbounded"/>
			<xs:element name="Level" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensationOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode" maxOccurs="unbounded"/>
			<xs:element name="Level" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusOptions">
		<xs:sequence>
			<xs:element name="AutoFocusModes" type="tt:AutoFocusMode" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="DefaultSpeed" type="tt:FloatRange"/>
			<xs:element name="NearLimit" type="tt:FloatRange"/>
			<xs:element name="FarLimit" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ExposureOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode" maxOccurs="unbounded"/>
			<xs:element name="Priority" type="tt:ExposurePriority" maxOccurs="unbounded"/>
			<xs:element name="MinExposureTime" type="tt:FloatRange"/>
			<xs:element name="MaxExposureTime" type="tt:FloatRange"/>
			<xs:element name="MinGain" type="tt:FloatRange"/>
			<xs:element name="MaxGain" type="tt:FloatRange"/>
			<xs:element name="MinIris" type="tt:FloatRange"/>
			<xs:element name="MaxIris" type="tt:FloatRange"/>
			<xs:element name="ExposureTime" type="tt:FloatRange"/>
			<xs:element name="Gain" type="tt:FloatRange"/>
			<xs:element name="Iris" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalanceOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode" maxOccurs="unbounded"/>
			<xs:element name="YrGain" type="tt:FloatRange"/>
			<xs:element name="YbGain" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusMove">
		<xs:sequence>
			<xs:element name="Absolute" type="tt:AbsoluteFocus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Parameters for the absolute focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Relative" type="tt:RelativeFocus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Parameters for the relative focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Continuous" type="tt:ContinuousFocus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Parameter for the continuous focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AbsoluteFocus">
		<xs:sequence>
			<xs:element name="Position" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Position parameter for the absolute focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Speed parameter for the absolute focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RelativeFocus">
		<xs:sequence>
			<xs:element name="Distance" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Distance parameter for the relative focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Speed parameter for the relative focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ContinuousFocus">
		<xs:sequence>
			<xs:element name="Speed" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Speed parameter for the Continuous focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MoveOptions">
		<xs:sequence>
			<xs:element name="Absolute" type="tt:AbsoluteFocusOptions" minOccurs="0"/>
			<xs:element name="Relative" type="tt:RelativeFocusOptions" minOccurs="0"/>
			<xs:element name="Continuous" type="tt:ContinuousFocusOptions" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AbsoluteFocusOptions">
		<xs:sequence>
			<xs:element name="Position" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the position.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RelativeFocusOptions">
		<xs:sequence>
			<xs:element name="Distance" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the distance.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ContinuousFocusOptions">
		<xs:sequence>
			<xs:element name="Speed" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ExposureMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="MANUAL"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Enabled">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ENABLED"/>
			<xs:enumeration value="DISABLED"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="WhiteBalanceMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="MANUAL"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IrCutFilterMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ON"/>
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="AUTO"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="WhiteBalance">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode">
				<xs:annotation>
					<xs:documentation>Auto whitebalancing mode (auto/manual).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CrGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>Rgain (unitless).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CbGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>Bgain (unitless).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--  End, Imaging Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--      Imaging Version 2.0 Related Types    -->
	<!--===============================-->
	<xs:complexType name="ImagingStatus20">
		<xs:sequence>
			<xs:element name="FocusStatus20" type="tt:FocusStatus20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Status of focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingStatus20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingStatus20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusStatus20">
		<xs:sequence>
			<xs:element name="Position" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Status of focus position.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MoveStatus" type="tt:MoveStatus">
				<xs:annotation>
					<xs:documentation>
				Status of focus MoveStatus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Error" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Error status of focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:FocusStatus20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusStatus20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettings20">
		<xs:annotation>
			<xs:documentation>Type describing the ImagingSettings of a VideoSource. The supported options and ranges can be obtained via the GetOptions command.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensation20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Enabled/disabled BLC mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Brightness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Image brightness (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ColorSaturation" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Color saturation of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contrast" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contrast of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Exposure" type="tt:Exposure20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Exposure mode of the device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Focus" type="tt:FocusConfiguration20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Focus configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IrCutFilter" type="tt:IrCutFilterMode" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Infrared Cutoff Filter settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sharpness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sharpness of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRange20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>WDR settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WhiteBalance" type="tt:WhiteBalance20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>White balance settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension20" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension20">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="ImageStabilization" type="tt:ImageStabilization" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure Image Stabilization feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension202" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension202">
		<xs:sequence>
			<xs:element name="IrCutFilterAutoAdjustment" type="tt:IrCutFilterAutoAdjustment" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>An optional parameter applied to only auto mode to adjust timing of toggling Ir cut filter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension203" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension203">
		<xs:sequence>
			<xs:element name="ToneCompensation" type="tt:ToneCompensation" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure Image Contrast Compensation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Defogging" type="tt:Defogging" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure Image Defogging.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoiseReduction" type="tt:NoiseReduction" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure Image Noise Reduction.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension204" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension204">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilization">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ImageStabilizationMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable Image Stabilization feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImageStabilizationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilizationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ImageStabilizationMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustment">
		<xs:sequence>
			<xs:element name="BoundaryType" type="xs:string">
				<xs:annotation>
					<xs:documentation>Specifies which boundaries to automatically toggle Ir cut filter following parameters are applied to. Its options shall be chosen from tt:IrCutFilterAutoBoundaryType.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BoundaryOffset" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Adjusts boundary exposure level for toggling Ir cut filter to on/off specified with unitless normalized value from +1.0 to -1.0. Zero is default and -1.0 is the darkest adjustment (Unitless).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ResponseTime" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delay time of toggling Ir cut filter to on/off after crossing of the boundary exposure levels.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:IrCutFilterAutoAdjustmentExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax" />
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustmentExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IrCutFilterAutoBoundaryType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Common" />
			<xs:enumeration value="ToOn" />
			<xs:enumeration value="ToOff" />
			<xs:enumeration value="Extended" />
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRange20">
		<xs:annotation>
			<xs:documentation>Type describing whether WDR mode is enabled or disabled (on/off).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode">
				<xs:annotation>
					<xs:documentation>Wide dynamic range mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensation20">
		<xs:annotation>
			<xs:documentation>Type describing whether BLC mode is enabled or disabled (on/off).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Mode" type="tt:BacklightCompensationMode">
				<xs:annotation>
					<xs:documentation>Backlight compensation mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Exposure20">
		<xs:annotation>
			<xs:documentation>Type describing the exposure settings.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode">
				<xs:annotation>
					<xs:documentation>
				Exposure Mode
				<ul>
							<li>Auto – Enabled the exposure algorithm on the device.</li>
							<li>Manual – Disabled exposure algorithm on the device.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Priority" type="tt:ExposurePriority" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The exposure priority mode (low noise/framerate).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Window" type="tt:Rectangle" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Rectangular exposure mask.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinExposureTime" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Minimum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxExposureTime" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Maximum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinIris" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the iris range allowed to be used by the algorithm.  0dB maps to a fully opened iris and positive values map to higher attenuation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxIris" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the iris range allowed to be used by the algorithm. 0dB maps to a fully opened iris and positive values map to higher attenuation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExposureTime" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The fixed exposure time used by the image sensor (&#956;s).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The fixed gain used by the image sensor (dB).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Iris" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The fixed attenuation of input light affected by the iris (dB). 0dB maps to a fully opened iris and positive values map to higher attenuation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ToneCompensation">
		<xs:sequence>
			<xs:element name="Mode" type="xs:string">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable or automatic ToneCompensation feature. Its options shall be chosen from tt:ToneCompensationMode Type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter specified with unitless normalized value from 0.0 to +1.0.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ToneCompensationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ToneCompensationExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ToneCompensationMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="AUTO"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Defogging">
		<xs:sequence>
			<xs:element name="Mode" type="xs:string">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable or automatic Defogging feature. Its options shall be chosen from tt:DefoggingMode Type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter specified with unitless normalized value from 0.0 to +1.0.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:DefoggingExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DefoggingExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded" />   <!-- first ONVIF then Vendor -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="DefoggingMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="AUTO"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NoiseReduction">
		<xs:sequence>
			<xs:element name="Level" type="xs:float">
				<xs:annotation>
					<xs:documentation>Level parameter specified with unitless normalized value from 0.0 to +1.0. Level=0 means no noise reduction or minimal noise reduction.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded" />   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20">
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensationOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Backlight Compensation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Brightness" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Brightness.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ColorSaturation" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Color Saturation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contrast" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Contrast.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Exposure" type="tt:ExposureOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Exposure.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Focus" type="tt:FocusOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IrCutFilterModes" type="tt:IrCutFilterMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				Valid range of IrCutFilterModes.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sharpness" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Sharpness.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRangeOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of WideDynamicRange.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WhiteBalance" type="tt:WhiteBalanceOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of WhiteBalance.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingOptions20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20Extension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="ImageStabilization" type="tt:ImageStabilizationOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for Image Stabilization feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingOptions20Extension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20Extension2">
		<xs:sequence>
			<xs:element name="IrCutFilterAutoAdjustment" type="tt:IrCutFilterAutoAdjustmentOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for adjustment of Ir cut filter auto mode.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingOptions20Extension3" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20Extension3">
		<xs:sequence>
			<xs:element name="ToneCompensationOptions" type="tt:ToneCompensationOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for Tone Compensation feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DefoggingOptions" type="tt:DefoggingOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for Defogging feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NoiseReductionOptions" type="tt:NoiseReductionOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameter for Noise Reduction feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingOptions20Extension4" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20Extension4">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilizationOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ImageStabilizationMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options of Image Stabilization mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Valid range of the Image Stabilization.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImageStabilizationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilizationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustmentOptions">
		<xs:sequence>
			<xs:element name="BoundaryType" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options of boundary types for adjustment of Ir cut filter auto mode. The opptions shall be chosen from tt:IrCutFilterAutoBoundaryType. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BoundaryOffset" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not boundary offset for toggling Ir cut filter is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ResponseTimeRange" type="tt:DurationRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of delay time for toggling Ir cut filter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:IrCutFilterAutoAdjustmentOptionsExtension" minOccurs="0" />
		</xs:sequence>
		<xs:anyAttribute processContents="lax" />
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustmentOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRangeOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode" maxOccurs="unbounded"/>
			<xs:element name="Level" type="tt:FloatRange" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensationOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:BacklightCompensationMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				'ON' or 'OFF'
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Level range of BacklightCompensation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ExposureOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				Exposure Mode
				<ul>
							<li>Auto – Enabled the exposure algorithm on the device.</li>
							<li>Manual – Disabled exposure algorithm on the device.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Priority" type="tt:ExposurePriority" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				The exposure priority mode (low noise/framerate).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinExposureTime" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Minimum ExposureTime.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxExposureTime" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Maximum ExposureTime.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinGain" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Minimum Gain.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxGain" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Maximum Gain.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinIris" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Minimum Iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxIris" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Maximum Iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExposureTime" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the ExposureTime.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gain" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Gain.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Iris" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MoveOptions20">
		<xs:sequence>
			<xs:element name="Absolute" type="tt:AbsoluteFocusOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges for the absolute control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Relative" type="tt:RelativeFocusOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges for the relative control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Continuous" type="tt:ContinuousFocusOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges for the continuous control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RelativeFocusOptions20">
		<xs:sequence>
			<xs:element name="Distance" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the distance.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalance20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode">
				<xs:annotation>
					<xs:documentation>
				'AUTO' or 'MANUAL'
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CrGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Rgain (unitless).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CbGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Bgain (unitless).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:WhiteBalance20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalance20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusConfiguration20">
		<xs:sequence>
			<xs:element name="AutoFocusMode" type="tt:AutoFocusMode">
				<xs:annotation>
					<xs:documentation>
						Mode of auto focus.
						<ul>
							<li>AUTO - The device automatically adjusts focus.</li>
							<li>MANUAL - The device does not automatically adjust focus.</li>
						</ul>
						Note: for devices supporting both manual and auto operation at the same time manual operation may be supported even if the Mode parameter is set to Auto.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DefaultSpeed" type="xs:float" minOccurs="0"/>
			<xs:element name="NearLimit" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus near limit (unit: meter).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FarLimit" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus far limit (unit: meter).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:FocusConfiguration20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="AFMode" type="tt:StringAttrList">
			<xs:annotation>
				<xs:documentation>Zero or more modes as defined in enumeration tt:AFModes.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusConfiguration20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalanceOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				Mode of WhiteBalance.
				<ul>
							<li>AUTO</li>
							<li>MANUAL</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="YrGain" type="tt:FloatRange" minOccurs="0"/>
			<xs:element name="YbGain" type="tt:FloatRange" minOccurs="0"/>
			<xs:element name="Extension" type="tt:WhiteBalanceOptions20Extension" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalanceOptions20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusOptions20">
		<xs:sequence>
			<xs:element name="AutoFocusModes" type="tt:AutoFocusMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
					Supported modes for auto focus.
					<ul>
						<li>AUTO - The device supports automatic focus adjustment.</li>
						<li>MANUAL - The device supports manual focus adjustment.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DefaultSpeed" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					Valid range of DefaultSpeed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NearLimit" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					Valid range of NearLimit.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FarLimit" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					Valid range of FarLimit.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:FocusOptions20Extension" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusOptions20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
			<xs:element name="AFModes" type="tt:StringAttrList"  minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported options for auto focus. Options shall be chosen from tt:AFModes.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ToneCompensationOptions">
		<xs:sequence>
			<xs:element name="Mode" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options for Tone Compensation mode. Its options shall be chosen from tt:ToneCompensationMode Type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not support Level parameter for Tone Compensation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded" />   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DefoggingOptions">
		<xs:sequence>
			<xs:element name="Mode" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options for Defogging mode. Its options shall be chosen from tt:DefoggingMode Type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not support Level parameter for Defogging.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded" />   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NoiseReductionOptions">
		<xs:sequence>
			<xs:element name="Level" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not support Level parameter for NoiseReduction.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded" />   <!-- first ONVIF then Vendor -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--===============================-->
	<!--  End, Imaging Version 2.0 Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--  Event and Analytics Types    -->
	<!--===============================-->
	<xs:simpleType name="PropertyOperation">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Initialized"/>
			<xs:enumeration value="Deleted"/>
			<xs:enumeration value="Changed"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:element name="Message">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Source" type="tt:ItemList" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Token value pairs that triggered this message. Typically only one item is present.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Key" type="tt:ItemList" minOccurs="0"/>
				<xs:element name="Data" type="tt:ItemList" minOccurs="0"/>
				<xs:element name="Extension" type="tt:MessageExtension" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="UtcTime" type="xs:dateTime" use="required"/>
			<xs:attribute name="PropertyOperation" type="tt:PropertyOperation"/>
			<xs:anyAttribute processContents="lax"/>
		</xs:complexType>
	</xs:element>
	<!--===============================-->
	<xs:complexType name="MessageExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemList">
		<xs:sequence>
			<xs:element name="SimpleItem" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Value name pair as defined by the corresponding description.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Value" type="xs:anySimpleType" use="required">
						<xs:annotation>
							<xs:documentation>Item value. The type is defined in the corresponding description.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="ElementItem" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Complex value structure.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##any" processContents="lax">
							<xs:annotation>
								<xs:documentation>XML tree contiaing the element value as defined in the corresponding description.</xs:documentation>
							</xs:annotation>
						</xs:any>
					</xs:sequence>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:ItemListExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemListExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--       Message Description     -->
	<!--===============================-->
	<xs:complexType name="MessageDescription">
		<xs:sequence>
			<xs:element name="Source" type="tt:ItemListDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Set of tokens producing this message. The list may only contain SimpleItemDescription items.
					The set of tokens identify the component within the WS-Endpoint, which is responsible for the producing the message.<br/>
					For analytics events the token set shall include the VideoSourceConfigurationToken, the VideoAnalyticsConfigurationToken
					and the name of the analytics module or rule.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Key" type="tt:ItemListDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes optional message payload parameters that may be used as key. E.g. object IDs of tracked objects are conveyed as key.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Data" type="tt:ItemListDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the payload of the message.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:MessageDescriptionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="IsProperty" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>Must be set to true when the described Message relates to a property. An alternative term of "property" is a "state" in contrast to a pure event, which contains relevant information for only a single point in time.<br/>Default is false.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MessageDescriptionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemListDescription">
		<xs:annotation>
			<xs:documentation>
			Describes a list of items. Each item in the list shall have a unique name.
			The list is designed as linear structure without optional or unbounded elements.
			Use ElementItems only when complex structures are inevitable.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SimpleItemDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Description of a simple item. The type must be of cathegory simpleType (xs:string, xs:integer, xs:float, ...).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name. Must be unique within a list.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" type="xs:QName" use="required"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="ElementItemDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            Description of a complex type. The Type must reference a defined type.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name. Must be unique within a list.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" type="xs:QName" use="required">
						<xs:annotation>
							<xs:documentation>The type of the item. The Type must reference a defined type.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:ItemListDescriptionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemListDescriptionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
	</xs:complexType>
	<!--End, Event and Analytics Types -->
	<!--===============================-->

	<!--=========================================-->
	<!--  Action event payload Types   -->
	<!--=========================================-->
	<!--===============================-->
	<xs:complexType name="ActionEngineEventPayload">
		<xs:annotation>
			<xs:documentation>Action Engine Event Payload data structure contains the information about the ONVIF command invocations. Since this event could be generated by other or proprietary actions, the command invocation specific fields are defined as optional and additional extension mechanism is provided for future or additional action definitions.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RequestInfo" type="soapenv:Envelope" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>Request Message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ResponseInfo" type="soapenv:Envelope" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>Response Message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Fault" type="soapenv:Fault" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>Fault Message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ActionEngineEventPayloadExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ActionEngineEventPayloadExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, Action event payload Types   -->
	<!--=========================================-->
        <!--=========================================-->
	<!--  Begin, Audio event types               -->
	<!--=========================================-->
	<xs:simpleType name="AudioClassType">
		<xs:annotation>
		<xs:documentation>
		  AudioClassType acceptable values are;
		   gun_shot, scream, glass_breaking, tire_screech   
		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="gun_shot"/>
			<xs:enumeration value="scream"/>
			<xs:enumeration value="glass_breaking"/>
			<xs:enumeration value="tire_screech"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="AudioClassCandidate">
	<xs:sequence>
		<xs:element name="Type" type="tt:AudioClassType">
			<xs:annotation>
			<xs:documentation>Indicates audio class label</xs:documentation>
			</xs:annotation>
		</xs:element> 
 		<xs:element name="Likelihood" type="xs:float">
			<xs:annotation>
			<xs:documentation>A likelihood/probability that the corresponding audio event belongs to this class. The sum of the likelihoods shall NOT exceed 1</xs:documentation>
			</xs:annotation>
		</xs:element>  
		<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
	</xs:sequence>
	<xs:anyAttribute processContents="lax"/>
	</xs:complexType>

	<xs:complexType name="AudioClassDescriptor">
	<xs:sequence>
		<xs:element name="ClassCandidate" type="tt:AudioClassCandidate" minOccurs="0" maxOccurs="unbounded">
			<xs:annotation>
			<xs:documentation>Array of audio class label and class probability</xs:documentation>
			</xs:annotation>
		</xs:element>
		<xs:element name="Extension" type="tt:AudioClassDescriptorExtension" minOccurs="0"/>
	</xs:sequence>
	<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<xs:complexType name="AudioClassDescriptorExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, Audio event types                 -->
	<!--=========================================-->
	<!--=========================================-->
	<!--  Begin, Media event types               -->
	<!--=========================================-->
	<xs:complexType name="ActiveConnection">
		<xs:sequence>
			<xs:element name="CurrentBitrate" type="xs:float"/>
			<xs:element name="CurrentFps" type="xs:float"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileStatus">
		<xs:sequence>
			<xs:element name="ActiveConnections" type="tt:ActiveConnection" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:ProfileStatusExtension" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileStatusExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, Media event types                 -->
	<!--=========================================-->
	<!--==================================-->
	<!--  Begin, OSD Device Types         -->
	<!--==================================-->
	<xs:complexType name="OSDReference">
		<xs:simpleContent>
			<xs:extension base="tt:ReferenceToken">
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="OSDType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Text"/>
			<xs:enumeration value="Image"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="OSDPosConfiguration">
		<xs:sequence>
			<xs:element name="Type" type="xs:string">
				<xs:annotation>
					<xs:documentation>For OSD position type, following are the pre-defined: <ul><li>UpperLeft</li>
						<li>UpperRight</li>
						<li>LowerLeft</li>
						<li>LowerRight</li>
						<li>Custom</li></ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Pos" type="tt:Vector" minOccurs="0"/>
			<xs:element name="Extension" type="tt:OSDPosConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDPosConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDColor">
		<xs:annotation>
			<xs:documentation>The value range of "Transparent" could be defined by vendors only should follow this rule: the minimum value means non-transparent and the maximum value maens fully transparent.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Color" type="tt:Color"/>
		</xs:sequence>
		<xs:attribute name="Transparent" type="xs:int" use="optional"/>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextConfiguration">
		<xs:sequence>
			<xs:element name="Type" type="xs:string">
				<xs:annotation>
					<xs:documentation>
						The following OSD Text Type are defined:<ul>
							<li>Plain - The Plain type means the OSD is shown as a text string which defined in the "PlainText" item.</li>
							<li>Date - The Date type means the OSD is shown as a date, format of which should be present in the "DateFormat" item.</li>
							<li>Time - The Time type means the OSD is shown as a time, format of which should be present in the "TimeFormat" item.</li>
							<li>DateAndTime - The DateAndTime type means the OSD is shown as date and time, format of which should be present in the "DateFormat" and the "TimeFormat" item.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateFormat" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						List of supported OSD date formats. This element shall be present when the value of Type field has Date or DateAndTime. The following DateFormat are defined:<ul>
							<li>M/d/yyyy - e.g. 3/6/2013</li>
							<li>MM/dd/yyyy - e.g. 03/06/2013</li>
							<li>dd/MM/yyyy - e.g. 06/03/2013</li>
							<li>yyyy/MM/dd - e.g. 2013/03/06</li>
							<li>yyyy-MM-dd - e.g. 2013-06-03</li>
							<li>dddd, MMMM dd, yyyy - e.g. Wednesday, March 06, 2013</li>
							<li>MMMM dd, yyyy - e.g. March 06, 2013</li>
							<li>dd MMMM, yyyy - e.g. 06 March, 2013</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeFormat" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						List of supported OSD time formats. This element shall be present when the value of Type field has Time or DateAndTime. The following TimeFormat are defined:<ul>
							<li>h:mm:ss tt - e.g. 2:14:21 PM</li>
							<li>hh:mm:ss tt - e.g. 02:14:21 PM</li>
							<li>H:mm:ss - e.g. 14:14:21</li>
							<li>HH:mm:ss - e.g. 14:14:21</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontSize" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Font size of the text in pt.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontColor" type="tt:OSDColor" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Font color of the text.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BackgroundColor" type="tt:OSDColor" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Background color of the text.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PlainText" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The content of text to be displayed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDTextConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="IsPersistentText" type="xs:boolean" use="optional">
			<xs:annotation><xs:documentation>This flag is applicable for Type Plain and defaults to true. When set to false the PlainText content will not be persistent across device reboots. </xs:documentation></xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgConfiguration">
		<xs:sequence>
			<xs:element name="ImgPath" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>The URI of the image which to be displayed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDImgConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ColorspaceRange">
		<xs:sequence>
			<xs:element name="X" type="tt:FloatRange"/>
			<xs:element name="Y" type="tt:FloatRange"/>
			<xs:element name="Z" type="tt:FloatRange"/>
			<xs:element name="Colorspace" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>
						Acceptable values are the same as in tt:Color.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ColorOptions">
		<xs:annotation>
			<xs:documentation>
				Describe the colors supported. Either list each color or define the range of color values.
			</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="ColorList" type="tt:Color" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List the supported color.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ColorspaceRange" type="tt:ColorspaceRange" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Define the range of color supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:choice>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDColorOptions">
		<xs:annotation>
			<xs:documentation>Describe the option of the color and its transparency.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Color" type="tt:ColorOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional list of supported colors.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Transparent" type="tt:IntRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Range of the transparent level. Larger means more tranparent.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDColorOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDColorOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextOptions">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported OSD text type. When a device indicates the supported number relating to Text type in MaximumNumberOfOSDs, the type shall be presented.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontSizeRange" type="tt:IntRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Range of the font size value.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateFormat" type="xs:string" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported date format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeFormat" type="xs:string" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported time format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontColor" type="tt:OSDColorOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>List of supported font color.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BackgroundColor" type="tt:OSDColorOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>List of supported background color.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDTextOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgOptions">
		<xs:sequence>
			<xs:element name="ImagePath" type="xs:anyURI" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of available image URIs.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDImgOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="FormatsSupported" type="tt:StringAttrList">
			<xs:annotation>
				<xs:documentation>List of supported image MIME types, such as "image/png".</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxSize" type="xs:int">
			<xs:annotation>
				<xs:documentation>The maximum size (in bytes) of the image that can be uploaded.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxWidth" type="xs:int">
			<xs:annotation>
				<xs:documentation>The maximum width (in pixels) of the image that can be uploaded.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="MaxHeight" type="xs:int">
			<xs:annotation>
				<xs:documentation>The maximum height (in pixels) of the image that can be uploaded.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="VideoSourceConfigurationToken" type="tt:OSDReference">
						<xs:annotation>
							<xs:documentation>Reference to the video source configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Type" type="tt:OSDType">
						<xs:annotation>
							<xs:documentation>Type of OSD.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Position" type="tt:OSDPosConfiguration">
						<xs:annotation>
							<xs:documentation>Position configuration of OSD.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="TextString" type="tt:OSDTextConfiguration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Text configuration of OSD. It shall be present when the value of Type field is Text.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Image" type="tt:OSDImgConfiguration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Image configuration of OSD. It shall be present when the value of Type field is Image</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:OSDConfigurationExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MaximumNumberOfOSDs">
		<xs:attribute name="Total" type="xs:int" use="required"/>
		<xs:attribute name="Image" type="xs:int"/>
		<xs:attribute name="PlainText" type="xs:int"/>
		<xs:attribute name="Date" type="xs:int"/>
		<xs:attribute name="Time" type="xs:int"/>
		<xs:attribute name="DateAndTime" type="xs:int"/>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfigurationOptions">
		<xs:sequence>
			<xs:element name="MaximumNumberOfOSDs" type="tt:MaximumNumberOfOSDs">
				<xs:annotation>
					<xs:documentation>The maximum number of OSD configurations supported for the specified video source configuration. If the configuration does not support OSDs, this value shall be zero and the Type and PositionOption elements are ignored. If a device limits the number of instances by OSDType, it shall indicate the supported number for each type via the related attribute.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type" type="tt:OSDType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List supported type of OSD configuration. When a device indicates the supported number for each types in MaximumNumberOfOSDs, related type shall be presented. A device shall return Option element relating to listed type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PositionOption" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List available OSD position type. Following are the pre-defined:<ul><li>UpperLeft</li>
						<li>UpperRight</li>
						<li>LowerLeft</li>
						<li>LowerRight</li>
						<li>Custom</li></ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TextOption" type="tt:OSDTextOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Option of the OSD text configuration. This element shall be returned if the device is signaling the support for Text.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ImageOption" type="tt:OSDImgOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Option of the OSD image configuration. This element shall be returned if the device is signaling the support for Image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDConfigurationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfigurationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, OSD Device Types                  -->
	<!--=========================================-->

	<!--=========================================-->
	<!--  Begin, StorageConfiguration            -->
	<!--=========================================-->
	<xs:complexType name="FileProgress">
    	<xs:sequence>
    		<xs:element name="FileName" type="xs:string">
    			<xs:annotation>
    				<xs:documentation>Exported file name</xs:documentation>
    			</xs:annotation>
    		</xs:element>
    		<xs:element name="Progress" type="xs:float">
    			<xs:annotation>
    				<xs:documentation>Normalized percentage completion for uploading the exported file</xs:documentation>
    			</xs:annotation>
    		</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
    </xs:complexType>

	<xs:complexType name="ArrayOfFileProgress">
    	<xs:sequence>
    		<xs:element name="FileProgress" type="tt:FileProgress" minOccurs="0" maxOccurs="unbounded">
    			<xs:annotation>
    				<xs:documentation>Exported file name and export progress information</xs:documentation>
    			</xs:annotation>
    		</xs:element>
			<xs:element name="Extension" type="tt:ArrayOfFileProgressExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
    </xs:complexType>
	<xs:complexType name="ArrayOfFileProgressExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>

	<xs:complexType name="StorageReferencePath">
    	<xs:sequence>
    		<xs:element name="StorageToken" type="tt:ReferenceToken">
    			<xs:annotation>
    				<xs:documentation>identifier of an existing Storage Configuration.</xs:documentation>
    			</xs:annotation>
    		</xs:element>
    		<xs:element name="RelativePath" type="xs:string" minOccurs="0">
    			<xs:annotation>
    				<xs:documentation>gives the relative directory path on the storage</xs:documentation>
    			</xs:annotation>
    		</xs:element>
			<xs:element name="Extension" type="tt:StorageReferencePathExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
    </xs:complexType>
	<xs:complexType name="StorageReferencePathExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first Vendor then ONVIF -->
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, StorageConfiguration              -->
	<!--=========================================-->
	
<xs:element name="PolygonOptions" type="tt:PolygonOptions"/>
 
<xs:complexType name="PolygonOptions">
	<xs:sequence>
		<xs:element name="RectangleOnly" type="xs:boolean" minOccurs="0">
			<xs:annotation>
				<xs:documentation>
True if the device supports defining a region only using Rectangle.
The rectangle points are still passed using a Polygon element if the device does not support polygon regions. In this case, the points provided in the Polygon element shall represent a rectangle.
				</xs:documentation>
			</xs:annotation>
		</xs:element>
		<xs:element name="VertexLimits" type="tt:IntRange" minOccurs="0">
			<xs:annotation>
				<xs:documentation>
Provides the minimum and maximum number of points that can be defined in the Polygon.
If RectangleOnly is not set to true, this parameter is required.
				</xs:documentation>
			</xs:annotation>
		</xs:element>
		<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>   <!-- first ONVIF then Vendor -->
	</xs:sequence>
	<xs:anyAttribute processContents="lax"/>
</xs:complexType>

</xs:schema>
