﻿/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_vpss.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-11-22

文件功能描述: 封装海思MPP视频处理子系统模块功能

其他: 

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述


*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <math.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>
#include <mqueue.h>

#include "print.h"
#include "common.h"
#include "mpp_com.h"
#include "mpp_vpss.h"
#include "media.h"

#include "libyuv.h"
#include "hdal.h"
#include "hd_debug.h"
#include "vendor_common.h"

#if (BOARD == BOARD_WFTR20S2) 	//lufeng, debug for wt473
sint32 mpp_vpss_Linear_Adjust3dNR(uint32 s32GrpId);
#endif

#define PIC_IN_PIC_WIDTH  640
#define PIC_IN_PIC_HEIGHT 480
#define VDO_YUV_BUFSIZE(w, h, pxlfmt)	(ALIGN_CEIL_4((w) * HD_VIDEO_PXLFMT_BPP(pxlfmt) / 8) * (h))

#define MPP_VPSS_ALG_BUF_NUM    ALG_MULTI_BUF_NUM

#define QUEUE_NAME "/my_mq"

/* 算法通道信息: update */
typedef struct tagVpssAlgInfo_S
{
    SV_BOOL bInit[VIM_MAX_DEV_NUM];
    uint32  u32FD[VIM_MAX_DEV_NUM][MPP_VPSS_ALG_BUF_NUM];
    uint64  u64Pts[VIM_MAX_DEV_NUM][MPP_VPSS_ALG_BUF_NUM];
    uint32  u32Width;
    uint32  u32Height;
} MPP_VPSS_ALG_INFO_S;

/* 视频处理模块控制信息 */
typedef struct tagVpssInfo_S
{
    uint32              u32ChnNum;              /* 视频源通道数目 */
    VIDEO_MODE_EE       enVideoMode;            /* 视频模式: 分辨率-帧率 */
    SV_BOOL             bImageMirror;           /* 是否使能画面镜像 */
    SV_BOOL             bImageFlip;             /* 是否使能画面翻转 */
    SV_ROT_ANGLE_E      enRotateAngle;          /* 画面旋转角度 */
    SV_BOOL             bInterrupt[10];         /* 是否暂停获取通道数据 */

    HD_PATH_ID          stProcCtrl[6];          /* 对应于VPROC设备的数量 */
    /* 相机1：0-主码流；1-子码流；2-图片流；3-[可选]流 */
    /* 相机2：4-主码流；5-子码流；6-图片流；7-[可选]流 */
    HD_PATH_ID          stProcPath[10];          /* 对应于VPROC通道的数量2相机-8通道(2x4) */
    HD_PATH_ID          stProcStampPath[10];     /* 对应于VPROC通道的数量2相机-8通道(2x4) */
    BOOL                bPathStarted[10];        /* 表示path是否已经启动 */
    BOOL                bPathOpened[10];         /* 表示path是否已经打开 */
    /* 相机2：0-画中画码流 */
    HD_PATH_ID          stPicInPicProcPath[10];          /*   */
    HD_PATH_ID          stPicInPicProcStampPath[10];     /*   */
    BOOL                bPicInPicPathStarted[10];        /*   */
    BOOL                bPicInPicPathOpened[10];         /*   */
    /* 两路VPE模块: 实现画中画和画面二分割功能 */ 
    HD_PATH_ID          stVpeProcPath[10];                /*   */
    BOOL                bVpePathStarted[10];              /*   */
    BOOL                bVpePathOpened[10];               /*   */
    /* 实验: OSD画面4x4分割, 需要16个Ex_Stamp */
    HD_PATH_ID          stOsdProcStampPath[16];         /* 对应于VPROC通道的数量2相机-8通道(2x4) */
    BOOL                bOsdPathOpened[16];             /* 表示path是否已经打开 */

    // 分别指定VPSS通道是纯视频流模式，还是叠加了OSD的模式
    BOOL                bVpssWithVosd[5];               /* TRUE: 叠加Vosd模式; False: 纯视频流模式；0-主码流，1-子码流，2-图片流，3-输出流 */

    MPP_VPSS_ALG_INFO_S stVpssAlgInfo;                  /* 算法信息 */
    SV_BOOL             bRunning;                       /* 线程运行标志位 */
    SV_BOOL             bAlgSingalFrame;                /* 算法是否使用单帧模式（开启多帧缓存时） */
} MPP_VPSS_INFO_S;
MPP_VPSS_INFO_S m_stVpssInfo = {0};     /* 视频处理模块控制信息 */

static volatile MPP_VPSS_CONF_S m_stVpssConf = {0}; 

/* 申请VPSS的私有内存池，暂时搁置 */ 
typedef struct tagVpss_DUMP_MEMBUF_S
{
    SV_BOOL bCreated;
    INT32   hBlock;
    INT32   hPool;
    uint64  u64PhyAddr;
    uint64  u64VirAddr;
    uint32  u32MemSize;
} MPP_VPSS_DUMP_MEMBUF_S;
static MPP_VPSS_DUMP_MEMBUF_S stVpssMem[3] = {0};

/*
    功能: 共享内存，VPSS+ALG
*/ 
void * mpp_vpss_AlgBody(void *pvArg);

/******************************************************************************
 * 函数功能: 初始化VPSS模块
 * 输入参数: pstVpssConf --- 视频处理配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS --- 成功
             ERR_NULL_PTR --- 传入参数指针为NULL
             ERR_ILLEGAL_PARAM --- 参数错误
             ERR_NOT_SURPPORT --- 不支持配置
             ERR_SYS_NOTREADY --- 系统未初始化
             SV_FAILURE --- 其它错误
 * 注意    : u32ChnNum是相机传感器数量
 *****************************************************************************/
int32_t mpp_vpss_Init(MPP_VPSS_CONF_S *pstVpssConf)
{
    int32_t s32Ret = 0, i;
    uint32 u32MaxW, u32MaxH;
    HD_RESULT eRet = HD_OK;

    hd_videoproc_uninit();

    if (NULL == pstVpssConf)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstVpssConf->u32ChnNum || pstVpssConf->u32ChnNum > VIM_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    m_stVpssConf = *pstVpssConf;

    eRet = hd_videoproc_init();
    if(HD_OK != eRet){
        print_level(SV_INFO, "hd_videoproc_init fail=%d\n", eRet);
        return ERR_SYS_NOTREADY;
    }

    u32MaxW = pstVpssConf->u32MaxW;
    u32MaxH = pstVpssConf->u32MaxH;
    
    print_level(SV_INFO, "pyl test u32MaxW = %d, u32MaxH = %d. pstVpssConf->u32ChnNum=%d\n", u32MaxW, u32MaxH, pstVpssConf->u32ChnNum);

    // 是否叠加Vosd需要根据config文件来确定初始过程中启动Vpss通道
    // 在初始化时，两个Vpss模块都进行创建, 默认是绑定无Vosd的通道，这里需要根据标志来选择绑定通道

    // 默认Vpss没有被选择叠加Vosd的通道
    m_stVpssInfo.bVpssWithVosd[0] = FALSE;
    m_stVpssInfo.bVpssWithVosd[1] = FALSE;
    m_stVpssInfo.bVpssWithVosd[2] = FALSE;
    m_stVpssInfo.bVpssWithVosd[3] = FALSE;

    // 根据相机传感器数量，初始化VPSS并创建通道
    m_stVpssConf.u32ChnNum = 1;
    for (i = 0; i < m_stVpssConf.u32ChnNum; i++)
    {
        s32Ret = mpp_vpss_CreateGrp(i, pstVpssConf->u32MaxW, pstVpssConf->u32MaxH);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_CreateGrp failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        // 绑定VI模块
        s32Ret = mpp_sys_ViVpssBind(i, i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssBind failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    // OSD图像处理模块
    s32Ret = mpp_vpss_CreateVosdOutPathGrp(2, 1920, 1080);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_CreateGrp failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    // OSD图像引入VPSS模块


    // vpss->画中画
    // 通道1
    // s32Ret = mpp_vpss_CreatePicInPicGrp(2, 2560, 1440);
    // if (SV_SUCCESS != s32Ret)
    // {
    //     print_level(SV_ERROR, "mpp_vpss_CreateGrp failed! [err=%#x]\n", s32Ret);
    //     return s32Ret;
    // }

    // // 通道2
    // s32Ret = mpp_vpss_CreatePicInPicGrp(3, 2560, 1440);
    // if (SV_SUCCESS != s32Ret)
    // {
    //     print_level(SV_ERROR, "mpp_vpss_CreateGrp failed! [err=%#x]\n", s32Ret);
    //     return s32Ret;
    // }

    // VPE1
    // s32Ret = mpp_vosd_CreateVpeModule(4);
    // if (SV_SUCCESS != s32Ret)
    // {
    //     print_level(SV_ERROR, "mpp_vpss_CreateGrp failed! [err=%#x]\n", s32Ret);
    //     return s32Ret;
    // }

    // // VPE2
    // s32Ret = mpp_vosd_CreateVpeModule(5);
    // if (SV_SUCCESS != s32Ret)
    // {
    //     print_level(SV_ERROR, "mpp_vpss_CreateGrp failed! [err=%#x]\n", s32Ret);
    //     return s32Ret;
    // }

    m_stVpssInfo.u32ChnNum      = pstVpssConf->u32ChnNum;
    m_stVpssInfo.enVideoMode    = pstVpssConf->enVideoMode;
    m_stVpssInfo.bImageMirror   = pstVpssConf->bImageMirror;
    m_stVpssInfo.bImageFlip     = pstVpssConf->bImageFlip;
    #if (BOARD == BOARD_IPCR20S2)
    m_stVpssInfo.enRotateAngle = pstVpssConf->enRotateAngle;
    #endif

    printf("mpp_vpss_Init OK...\n\n");
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_Fini()
{
    int32_t s32Ret = 0, i;
    HD_RESULT eRet = HD_OK;

    // 关闭VPSS组的通道
    for (i = 0; i < m_stVpssConf.u32ChnNum; i++)
    {
        s32Ret = mpp_vpss_DestroyGrp(i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_Fini failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        m_stVpssInfo.bPathOpened[i] = FALSE;
    }
    
    eRet = hd_videoproc_uninit();
    if(HD_OK != eRet){
        printf("hd_videoproc_uninit fail=%d\n", eRet);
        return ERR_SYS_NOTREADY;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_Start()
{
    int32_t s32Ret = 0, i, j, index;
    uint32 u32Tid = 0;
    static int rchannel[2] = {0};
    pthread_attr_t 	attr;
    m_stVpssInfo.bRunning = SV_TRUE;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程

    HD_RESULT eRet = HD_OK;
    // OSG模块开启

    // 纯视频流Vpss
    UINT8 u8TotalChannels = m_stVpssConf.u32ChnNum * 5;
    print_level(SV_INFO, "u8TotalChannels = %d... \n", u8TotalChannels);
    for (size_t i = 0; i < u8TotalChannels; i++)
    {
        if (!m_stVpssInfo.bPathOpened[i] && FALSE == m_stVpssInfo.bVpssWithVosd[i])
        {
            // 没有open, 不用start
            print_level(SV_INFO, "Without OSD and no Open yet: m_stVpssInfo.bPathOpened[%d]=%d, \n", i, m_stVpssInfo.bPathOpened[i]);
            continue;
        }
        eRet = hd_videoproc_start(m_stVpssInfo.stProcPath[i]);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "i=%d, mpp_vpss_Start hd_videoproc_start fail=%d\n", i, eRet);
            return SV_FAILURE;
        }else
        {
            print_level(SV_INFO, "vpss%d start OK... \n", i);
        }
        m_stVpssInfo.bPathStarted[i] = TRUE;
    }

    eRet = hd_videoproc_start(m_stVpssInfo.stProcStampPath[4]);
    if (eRet != HD_OK) {
        print_level(SV_ERROR, "m_stVpssInfo.stProcStampPath[4]=%d, mpp_vpss_Start hd_videoproc_start fail=%d\n", m_stVpssInfo.stProcStampPath[4], eRet);
        return SV_FAILURE;
    }else
    {
        print_level(SV_INFO, "stProcStampPath%d start OK... \n", 0);
    }

    // eRet = hd_videoproc_start(m_stVpssInfo.stProcStampPath[1]);
    // if (eRet != HD_OK) {
    //     print_level(SV_ERROR, "m_stVpssInfo.stProcStampPath[1]=%d, mpp_vpss_Start hd_videoproc_start fail=%d\n", m_stVpssInfo.stProcStampPath[1], eRet);
    //     return SV_FAILURE;
    // }else
    // {
    //     print_level(SV_INFO, "stProcStampPath%d start OK... \n", 0);
    // }

    // 叠加Vosd的Vpss通道
    for (size_t i = 0; i < 5; i++)
    {
        // 筛选出叠加Vosd且已经open通道, 其余通道不进行start操纵
        if (!m_stVpssInfo.bPicInPicPathOpened[i] && TRUE == m_stVpssInfo.bVpssWithVosd[i])
        {
            // 没有open, 不用start
            print_level(SV_INFO, "With OSD and no Open yet: m_stVpssInfo.bPicInPicPathOpened[%d]=%d, \n", i, m_stVpssInfo.bPicInPicPathOpened[i]);
            continue;
        }

        eRet = hd_videoproc_start(m_stVpssInfo.stPicInPicProcPath[i]);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "m_stVpssInfo.stPicInPicProcPath[1]=%d, mpp_vpss_Start hd_videoproc_start fail=%d\n", m_stVpssInfo.stPicInPicProcPath[1], eRet);
            return SV_FAILURE;
        }else
        {
            print_level(SV_INFO, "m_stVpssInfo.stPicInPicProcPath[i] = %x, stPicInPicProcPath%d start OK... \n", m_stVpssInfo.stPicInPicProcPath[i], i);
        }

        m_stVpssInfo.bPicInPicPathStarted[i] = TRUE;
    }

    // for (size_t i = 0; i < 10; i++)
    // {
    //     if (!m_stVpssInfo.bVpePathOpened[i])
    //     {
    //         // 没有open, 不用start
    //         continue;
    //     }
    //     eRet = hd_videoproc_start(m_stVpssInfo.stVpeProcPath[i]);
    //     if (eRet != HD_OK) {
    //         print_level(SV_ERROR, "m_stVpssInfo.stProcStampPath[1]=%d, mpp_vpss_Start hd_videoproc_start fail=%d\n", m_stVpssInfo.stProcStampPath[1], eRet);
    //         return SV_FAILURE;
    //     }else
    //     {
    //         print_level(SV_INFO, "stProcStampPath%d start OK... \n", 0);
    //     }
    //     m_stVpssInfo.bVpePathStarted[i] = TRUE;
    // }

    // 算法链接线程
    s32Ret = pthread_create(&u32Tid, &attr, mpp_vpss_AlgBody, &rchannel[index]);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread[%d %d] for VPSS failed! [err: %s]\n", i, j, strerror(errno));
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 应该优化增加stop指定传感器的index
 *****************************************************************************/
int32_t mpp_vpss_Stop()
{
    HD_RESULT eRet = HD_OK;

    UINT8 u8TotalChannels = m_stVpssConf.u32ChnNum * 5;
    for (size_t i = 0; i < u8TotalChannels; i++)
    {
        if (!m_stVpssInfo.bPathStarted[i] && FALSE == m_stVpssInfo.bVpssWithVosd[i])
        {
            // 没有start, 不用stop
            continue;
        }

        eRet = hd_videoproc_stop(m_stVpssInfo.stProcPath[i]);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "i=%d, hd_videoproc_stop fail=%d\n", i, eRet);
            return SV_FAILURE;
        }else
        {
            print_level(SV_INFO, "vpss%d start OK... \n", i);
        }

        m_stVpssInfo.bPathStarted[i] = FALSE;
    }

    for (size_t i = 0; i < 5; i++)
    {
        if (!m_stVpssInfo.bPicInPicPathStarted[i] && TRUE == m_stVpssInfo.bVpssWithVosd[i])
        {
            // 没有start, 不用stop
            continue;
        }

        eRet = hd_videoproc_stop(m_stVpssInfo.stPicInPicProcPath[i]);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "m_stVpssInfo.stPicInPicProcPath[1]=%d, mpp_vpss_Start hd_videoproc_stop fail=%d\n", m_stVpssInfo.stPicInPicProcPath[1], eRet);
            return SV_FAILURE;
        }else
        {
            print_level(SV_INFO, "m_stVpssInfo.stPicInPicProcPath[i] = %x, stPicInPicProcPath%d start OK... \n", m_stVpssInfo.stPicInPicProcPath[i], i);
        }

        m_stVpssInfo.bPicInPicPathOpened[i] = FALSE;
    }

    eRet = hd_videoproc_stop(m_stVpssInfo.stProcStampPath[4]);
    if (eRet != HD_OK) {
        print_level(SV_ERROR, "i=%d, mpp_vpss_Start hd_videoproc_start fail=%d\n", 0, eRet);
        return SV_FAILURE;
    }else
    {
        print_level(SV_INFO, "vpss%d start OK... \n", 0);
    }

    // eRet = hd_videoproc_stop(m_stVpssInfo.stProcStampPath[1]);
    // if (eRet != HD_OK) {
    //     print_level(SV_ERROR, "i=%d, mpp_vpss_Start hd_videoproc_start fail=%d\n", 1, eRet);
    //     return SV_FAILURE;
    // }else
    // {
    //     print_level(SV_INFO, "vpss%d start OK... \n", 0);
    // }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建VPSS通道组-由mpp_vpss_Init函数调用
 * 输入参数: s32GrpId --- 通道组ID, 视频处理模块序号
             u32MaxW --- 最大图像宽度
             u32MaxH --- 最大图像高度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
            SV_FAILURE - 其它错误
 * 注意    : 默认VPSS设备配置一样，s32GrpId对应于相机序号
 *****************************************************************************/
int32_t mpp_vpss_CreateGrp(int32_t s32GrpId, uint32 u32MaxW, uint32 u32MaxH)
{
    HD_RESULT                   eRet = HD_OK;
    HD_VIDEOPROC_DEV_CONFIG     stProcDevConfigs = {0};
    HD_VIDEOPROC_CTRL           stProcCtrlParam;
    HD_VIDEOPROC_IN             stProcIn;
    HD_VIDEOPROC_OUT            stProcOut;
    HD_VIDEOPROC_FUNC_CONFIG    stProcFuncConfigs;
    
    BOOL                        bUseHDR     = FALSE;
    HD_VIDEO_PXLFMT             ePlxFmt     = HD_VIDEO_PXLFMT_RAW12;
    HD_VIDEO_PXLFMT             ePlxFmtHDR  = HD_VIDEO_PXLFMT_RAW12_SHDR2;

    const UINT8                 u8VprocOutNums = 5;
    
    {
        if (1 == s32GrpId)
        {
            bUseHDR = FALSE;
        }
        // VPSS组
        eRet = hd_videoproc_open(0, HD_VIDEOPROC_CTRL(s32GrpId), &(m_stVpssInfo.stProcCtrl[s32GrpId]));
        if (eRet != HD_OK){
            print_level(SV_ERROR, "hd_videoproc_open fail=%d\n", eRet);
            return SV_FAILURE;
        }
        
        stProcDevConfigs.pipe          = HD_VIDEOPROC_PIPE_RAWALL;
        stProcDevConfigs.isp_id        = s32GrpId;
        stProcDevConfigs.in_max.func   = 0;
        stProcDevConfigs.in_max.dim.w  = m_stVpssConf.stPriVencSize.u32Width;
        stProcDevConfigs.in_max.dim.h  = m_stVpssConf.stPriVencSize.u32Height;
        stProcDevConfigs.in_max.frc    = HD_VIDEO_FRC_RATIO(1, 1);
        if (bUseHDR)
        {
            stProcDevConfigs.ctrl_max.func = HD_VIDEOPROC_FUNC_SHDR;
            stProcDevConfigs.in_max.pxlfmt = ePlxFmtHDR;
        }else
        {
            stProcDevConfigs.ctrl_max.func = HD_VIDEOPROC_FUNC_AF | HD_VIDEOPROC_FUNC_DEFOG | HD_VIDEOPROC_FUNC_3DNR | HD_VIDEOPROC_FUNC_3DNR_STA;
            stProcDevConfigs.in_max.pxlfmt = ePlxFmt;
        }

        eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32GrpId], HD_VIDEOPROC_PARAM_DEV_CONFIG, &stProcDevConfigs);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOPROC_PARAM_DEV_CONFIG fail=%d\n", eRet);
            return SV_FAILURE;
        }

        if (bUseHDR)
        {
            stProcCtrlParam.func = HD_VIDEOPROC_FUNC_SHDR;
        }else
        {
            stProcCtrlParam.func = HD_VIDEOPROC_FUNC_AF | HD_VIDEOPROC_FUNC_DEFOG | HD_VIDEOPROC_FUNC_3DNR | HD_VIDEOPROC_FUNC_3DNR_STA;
            stProcCtrlParam.ref_path_3dnr = HD_VIDEOPROC_OUT(0, 0);
        }

        eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32GrpId], HD_VIDEOPROC_PARAM_CTRL, &stProcCtrlParam);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOCAP_PARAM_CTRL fail=%d\n", eRet);
            return SV_FAILURE;
        }
    }

    {
        // VPSS通道=4
        UINT8 u8ChannelIndex = s32GrpId*u8VprocOutNums;
        for (size_t i = 0; i < u8VprocOutNums; i++) // 每个vpss组输出端口=4个
        {
            if (m_stVpssInfo.bPathOpened[u8ChannelIndex+i])
            {
                if (m_stVpssInfo.bPathStarted[u8ChannelIndex+i])
                {
                    hd_videoproc_stop(m_stVpssInfo.stProcPath[u8ChannelIndex+i]);
                }
                hd_videoproc_close(m_stVpssInfo.stProcPath[u8ChannelIndex+i]);
            }
            
            eRet = hd_videoproc_open(HD_VIDEOPROC_IN(s32GrpId, 0), HD_VIDEOPROC_OUT(s32GrpId, i), &m_stVpssInfo.stProcPath[u8ChannelIndex+i]);
            if (eRet != HD_OK) {     
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }

            if (0 == i) {
                // HD_VIDEOPROC_IN(s32GrpId, 0)指定输入，HD_STAMP_0指定输入到输出的端口
                // eRet = hd_videoproc_open(HD_VIDEOPROC_IN(s32GrpId, HD_STAMP_EX(i)), HD_VIDEOPROC_OUT(s32GrpId, i), &m_stVpssInfo.stProcStampPath[u8ChannelIndex+i]);
                // if (eRet != HD_OK) {
                //     print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", u8ChannelIndex+i, eRet);
                //     return SV_FAILURE;
                // }
                // else
                // {
                //     print_level(SV_INFO, "u8ChannelIndex=%d, hd_videoproc_open OK=%d\n", m_stVpssInfo.stProcStampPath[u8ChannelIndex+i], eRet);
                // }

                // eRet = hd_videoproc_open(HD_VIDEOPROC_IN(s32GrpId, HD_STAMP_EX(i+1)), HD_VIDEOPROC_OUT(s32GrpId, i), &m_stVpssInfo.stOsdProcStampPath[u8ChannelIndex+0]);
                // if (eRet != HD_OK) {
                //     print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", u8ChannelIndex+i, eRet);
                //     return SV_FAILURE;
                // }
                // else
                // {
                //     print_level(SV_INFO, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", m_stVpssInfo.stOsdProcStampPath[u8ChannelIndex+0], eRet);
                // }

                // eRet = hd_videoproc_open(HD_VIDEOPROC_IN(s32GrpId, HD_STAMP_EX(i+2)), HD_VIDEOPROC_OUT(s32GrpId, i), &m_stVpssInfo.stOsdProcStampPath[u8ChannelIndex+1]);
                // if (eRet != HD_OK) {
                //     print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", u8ChannelIndex+i, eRet);
                //     return SV_FAILURE;
                // }
                // else
                // {
                //     print_level(SV_INFO, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", m_stVpssInfo.stOsdProcStampPath[u8ChannelIndex+1], eRet);
                // }
            }

            if (0 == i) {
                // HD_VIDEOPROC_IN(s32GrpId, 0)指定输入，HD_STAMP_0指定输入到输出的端口
                eRet = hd_videoproc_open(HD_VIDEOPROC_IN(0, HD_STAMP_EX_0), HD_VIDEOPROC_0_OUT_4, &m_stVpssInfo.stProcStampPath[4]);
                if (eRet != HD_OK) {
                    print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", 4, eRet);
                    return SV_FAILURE;
                }
                else
                {
                    print_level(SV_INFO, "u8ChannelIndex=%d, hd_videoproc_open OK=%d\n", m_stVpssInfo.stProcStampPath[4], eRet);
                }
            }

            hd_videoproc_get(m_stVpssInfo.stProcPath[u8ChannelIndex+i], HD_VIDEOPROC_PARAM_IN, &stProcIn);
            stProcIn.dir = HD_VIDEO_DIR_MIRRORX | HD_VIDEO_DIR_MIRRORY;
            switch (i)  // 不同通道分辨率的设置，增加处理模块
            {
                case 0:     // stPriVencSize
                    // params, 如果输出绑定到其他模块，则不需要设置；如果设置了，就一定需要保持一致性
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = m_stVpssConf.stPriVencSize.u32Width;
                    stProcOut.dim.h  = m_stVpssConf.stPriVencSize.u32Height;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_DEFAULT;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 1:     // stSubVencSize
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = m_stVpssConf.stSubVencSize.u32Width;
                    stProcOut.dim.h  = m_stVpssConf.stSubVencSize.u32Height;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_DEFAULT;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    break;
                case 2:     // stJpegVencSize
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = m_stVpssConf.stJpegVencSize.u32Width;
                    stProcOut.dim.h  = m_stVpssConf.stJpegVencSize.u32Height;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_DEFAULT;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    break;
                case 3:     // 算法通道
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    // stProcOut.dim.w  = m_stVpssConf.stExtscreenSize.u32Width;
                    // stProcOut.dim.h  = m_stVpssConf.stExtscreenSize.u32Height;
                    stProcOut.dim.w  = 608;
                    stProcOut.dim.h  = 352;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_DEFAULT;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    break;
                case 4:     // OSD
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    // stProcOut.dim.w  = m_stVpssConf.stExtscreenSize.u32Width;
                    // stProcOut.dim.h  = m_stVpssConf.stExtscreenSize.u32Height;
                    stProcOut.dim.w  = 1920;
                    stProcOut.dim.h  = 1080;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_DEFAULT;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    break;
                default:
                    break;
            }
            
            eRet = hd_videoproc_set(m_stVpssInfo.stProcPath[u8ChannelIndex+i], HD_VIDEOPROC_PARAM_IN, &stProcIn);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_set HD_VIDEOPROC_PARAM_IN fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }

            eRet = hd_videoproc_set(m_stVpssInfo.stProcPath[u8ChannelIndex+i], HD_VIDEOPROC_PARAM_OUT, &stProcOut);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_set HD_VIDEOCAP_PARAM_OUT fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }

            m_stVpssInfo.bPathOpened[u8ChannelIndex+i] = TRUE;
        }
    }

    return SV_SUCCESS;
}

/*
    用于画中画功能，为了输出I420格式图像；
    输入来源于基础VPSS的第五个端口, 图像格式为YUV格式，720x480

    VPSS输出
*/ 
int32_t mpp_vpss_CreatePicInPicGrp(int32_t s32GrpId, uint32 u32MaxW, uint32 u32MaxH)
{
    HD_RESULT                   eRet = HD_OK;
    HD_VIDEOPROC_DEV_CONFIG     stProcDevConfigs;
    HD_VIDEOPROC_CTRL           stProcCtrlParam;
    HD_VIDEOPROC_OUT            stProcOut;
    HD_VIDEOPROC_FUNC_CONFIG    stProcFuncConfigs;
    
    HD_VIDEO_PXLFMT             ePlxFmt     = HD_VIDEO_PXLFMT_YUV420;
    
    {
        // VPSS组
        eRet = hd_videoproc_open(0, HD_VIDEOPROC_CTRL(s32GrpId), &(m_stVpssInfo.stProcCtrl[s32GrpId]));
        if (eRet != HD_OK){
            print_level(SV_ERROR, "hd_videoproc_open fail=%d\n", eRet);
            return SV_FAILURE;
        }
        stProcDevConfigs.pipe          = HD_VIDEOPROC_PIPE_YUVALL;
        stProcDevConfigs.isp_id        = HD_ISP_DONT_CARE;
        stProcDevConfigs.in_max.func   = 0;
        stProcDevConfigs.in_max.dim.w  = 2560;
        stProcDevConfigs.in_max.dim.h  = 1440;
        stProcDevConfigs.in_max.frc    = HD_VIDEO_FRC_RATIO(1, 1);
        stProcDevConfigs.ctrl_max.func = 0;
        stProcDevConfigs.in_max.pxlfmt = ePlxFmt;
        eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32GrpId], HD_VIDEOPROC_PARAM_DEV_CONFIG, &stProcDevConfigs);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOPROC_PARAM_DEV_CONFIG fail=%d\n", eRet);
            return SV_FAILURE;
        }
        
        stProcCtrlParam.func = 0;
        eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32GrpId], HD_VIDEOPROC_PARAM_CTRL, &stProcCtrlParam);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOCAP_PARAM_CTRL fail=%d\n", eRet);
            return SV_FAILURE;
        }
    }
    
    {
        // VPSS画中画通道， vpss3和vpss4
        UINT8 u8ChannelIndex = (s32GrpId % 2) * 5;
        for (size_t i = 0; i < 5; i++) // 画中画vpss组输出端口=1个
        {
            if (m_stVpssInfo.bPicInPicPathOpened[u8ChannelIndex+i])
            {
                if (m_stVpssInfo.bPicInPicPathStarted[u8ChannelIndex+i])
                {
                    hd_videoproc_stop(m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]);
                }
                hd_videoproc_close(m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]);
            }
            
            eRet = hd_videoproc_open(HD_VIDEOPROC_IN(s32GrpId, 0), HD_VIDEOPROC_OUT(s32GrpId, i), &m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }
            print_level(SV_INFO, "u8ChannelIndex + i = %d, m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]=%d \n", u8ChannelIndex + i, m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex + i]);
            
            switch (i)  // 不同通道分辨率的设置，增加处理模块
            {
                case 0: // stPriVencSize
                    // params, 如果输出绑定到其他模块，则不需要设置；如果设置了，就一定需要保持一致性
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 2560;
                    stProcOut.dim.h  = 1440;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 1:     // stPriVencSize
                    // params, 如果输出绑定到其他模块，则不需要设置；如果设置了，就一定需要保持一致性
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 1920;
                    stProcOut.dim.h  = 1080;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 2:     // stPriVencSize
                    // params, 如果输出绑定到其他模块，则不需要设置；如果设置了，就一定需要保持一致性
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 1280;
                    stProcOut.dim.h  = 720;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 3:
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 720;
                    stProcOut.dim.h  = 640;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 4:
                    stProcOut.depth  = 2;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = ALIGN_FLOOR(2560, 16);
                    stProcOut.dim.h  = ALIGN_FLOOR(1440, 16);
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                default:
                    break;
            }
            
            eRet = hd_videoproc_set(m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i], HD_VIDEOPROC_PARAM_OUT, &stProcOut);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_set HD_VIDEOCAP_PARAM_OUT fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }

            m_stVpssInfo.bPicInPicPathOpened[u8ChannelIndex+i] = TRUE;
        }

        // 模拟两个VPSS模块, 用于手动拉取图像数据
        if (2 == s32GrpId || 3 == s32GrpId)
        {
            eRet = hd_videoproc_bind(HD_VIDEOPROC_OUT(s32GrpId % 2, 4), HD_VIDEOPROC_IN(s32GrpId, 0));
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_set HD_VIDEOCAP_PARAM_OUT fail=%d, eRet=%d\n", HD_VIDEOPROC_OUT(1, 3), HD_VIDEOPROC_IN(2, 0), eRet);
                return SV_FAILURE;
            }
            else
            {
                print_level(SV_INFO, "pic in pic bingding OK, s32GrpId 2=%d, u8ChannelIndex+4=%d\n", s32GrpId % 2, u8ChannelIndex+4);
            }
        }
    }

    return SV_SUCCESS;
}

/*
    用于VOSD功能，为了输出YUV422格式图像；
    输入来源于基础VPSS的第4个端口, 图像格式为YUV格式，1920x1080

    VPSS输出
*/ 
int32_t mpp_vpss_CreateVosdOutPathGrp(int32_t s32GrpId, uint32 u32MaxW, uint32 u32MaxH)
{
    HD_RESULT                   eRet = HD_OK;
    HD_VIDEOPROC_DEV_CONFIG     stProcDevConfigs;
    HD_VIDEOPROC_CTRL           stProcCtrlParam;
    HD_VIDEOPROC_OUT            stProcOut;
    HD_VIDEOPROC_FUNC_CONFIG    stProcFuncConfigs;
    
    HD_VIDEO_PXLFMT             ePlxFmt     = HD_VIDEO_PXLFMT_YUV420;
    
    {
        // VPSS组
        eRet = hd_videoproc_open(0, HD_VIDEOPROC_CTRL(s32GrpId), &(m_stVpssInfo.stProcCtrl[s32GrpId]));
        if (eRet != HD_OK){
            print_level(SV_ERROR, "hd_videoproc_open fail=%d\n", eRet);
            return SV_FAILURE;
        }
        stProcDevConfigs.pipe          = HD_VIDEOPROC_PIPE_YUVALL;
        stProcDevConfigs.isp_id        = HD_ISP_DONT_CARE;
        stProcDevConfigs.in_max.func   = 0;
        stProcDevConfigs.in_max.dim.w  = 1920;
        stProcDevConfigs.in_max.dim.h  = 1080;
        stProcDevConfigs.in_max.frc    = HD_VIDEO_FRC_RATIO(1, 1);
        stProcDevConfigs.ctrl_max.func = 0;
        stProcDevConfigs.in_max.pxlfmt = ePlxFmt;
        eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32GrpId], HD_VIDEOPROC_PARAM_DEV_CONFIG, &stProcDevConfigs);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOPROC_PARAM_DEV_CONFIG fail=%d\n", eRet);
            return SV_FAILURE;
        }
        
        stProcCtrlParam.func = 0;
        eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32GrpId], HD_VIDEOPROC_PARAM_CTRL, &stProcCtrlParam);
        if (eRet != HD_OK) {
            print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOCAP_PARAM_CTRL fail=%d\n", eRet);
            return SV_FAILURE;
        }
    }
    
    {
        UINT8 u8ChannelIndex = (s32GrpId % 2) * 5;
        for (size_t i = 0; i < 5; i++)
        {
            if (m_stVpssInfo.bPicInPicPathOpened[u8ChannelIndex+i])
            {
                if (m_stVpssInfo.bPicInPicPathStarted[u8ChannelIndex+i])
                {
                    hd_videoproc_stop(m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]);
                }
                hd_videoproc_close(m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]);
            }
            
            eRet = hd_videoproc_open(HD_VIDEOPROC_IN(s32GrpId, 0), HD_VIDEOPROC_OUT(s32GrpId, i), &m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }
            print_level(SV_INFO, "u8ChannelIndex + i = %d, m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]=%d \n", u8ChannelIndex + i, m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex + i]);
            
            switch (i)  // 不同通道分辨率的设置，增加处理模块
            {
                case 0: // stPriVencSize
                    // params, 如果输出绑定到其他模块，则不需要设置；如果设置了，就一定需要保持一致性
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = m_stVpssConf.stPriVencSize.u32Width;;
                    stProcOut.dim.h  = m_stVpssConf.stPriVencSize.u32Width;;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 1:     // stPriVencSize
                    // params, 如果输出绑定到其他模块，则不需要设置；如果设置了，就一定需要保持一致性
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = m_stVpssConf.stSubVencSize.u32Width;
                    stProcOut.dim.h  = m_stVpssConf.stSubVencSize.u32Height;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 2:     // stPriVencSize
                    // params, 如果输出绑定到其他模块，则不需要设置；如果设置了，就一定需要保持一致性
                    stProcOut.depth  = 1;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = m_stVpssConf.stJpegVencSize.u32Width;
                    stProcOut.dim.h  = m_stVpssConf.stJpegVencSize.u32Height;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 3: // VO
                    stProcOut.depth  = 0;   
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 1920;
                    stProcOut.dim.h  = 1080;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 4:
                    stProcOut.depth  = 0;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 1920;
                    stProcOut.dim.h  = 1080;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                default:
                    break;
            }
            
            eRet = hd_videoproc_set(m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i], HD_VIDEOPROC_PARAM_OUT, &stProcOut);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_set HD_VIDEOCAP_PARAM_OUT fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }

            m_stVpssInfo.bPicInPicPathOpened[u8ChannelIndex+i] = TRUE;
        }

        // if (2 == s32GrpId)
        // {
        //     eRet = hd_videoproc_bind(HD_VIDEOPROC_OUT(s32GrpId % 2, 3), HD_VIDEOPROC_IN(s32GrpId, 0));
        //     if (eRet != HD_OK) {
        //         print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_set HD_VIDEOCAP_PARAM_OUT fail=%d, eRet=%d\n", HD_VIDEOPROC_OUT(1, 3), HD_VIDEOPROC_IN(2, 0), eRet);
        //         return SV_FAILURE;
        //     }
        //     else
        //     {
        //         print_level(SV_INFO, "pic in pic bingding OK, s32GrpId 2=%d, u8ChannelIndex+4=%d\n", s32GrpId % 2, u8ChannelIndex+4);
        //     }
        // }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建VPE模块, 处理VPRC模块的YUV图像, 实现画中画和画面二分割功能
 * 输入参数: s32GrpId --- 通道组ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vosd_CreateVpeModule(int32_t s32VpeId){
    HD_RESULT eRet = 0;
    HD_VIDEOPROC_DEV_CONFIG stProcDevConfigs  = {0};
    HD_VIDEOPROC_CTRL       stProcCtrlParam   = {0};
    HD_VIDEOPROC_OUT        stProcOut         = {0};

    {
        // 两路VPE模块，多个PATH
        eRet = hd_videoproc_open(0, HD_VIDEOPROC_CTRL(s32VpeId), &m_stVpssInfo.stProcCtrl[s32VpeId]);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "VPE hd_videoproc_open error eRet=%d...\n", eRet);
            return SV_FAILURE;
        }
        else
        {
            stProcDevConfigs.pipe          = HD_VIDEOPROC_PIPE_VPE;
            stProcDevConfigs.isp_id        = HD_ISP_DONT_CARE;
            stProcDevConfigs.in_max.func   = 0;
            stProcDevConfigs.in_max.dim.w  = 2560;
            stProcDevConfigs.in_max.dim.h  = 1440;
            stProcDevConfigs.in_max.frc    = HD_VIDEO_FRC_RATIO(1, 1);
            stProcDevConfigs.ctrl_max.func = 0;
            stProcDevConfigs.in_max.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
            eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32VpeId], HD_VIDEOPROC_PARAM_DEV_CONFIG, &stProcDevConfigs);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOPROC_PARAM_DEV_CONFIG fail=%d\n", eRet);
                return SV_FAILURE;
            }
            else
            {
                stProcCtrlParam.func = 0;
                eRet = hd_videoproc_set(m_stVpssInfo.stProcCtrl[s32VpeId], HD_VIDEOPROC_PARAM_CTRL, &stProcCtrlParam);
                if (eRet != HD_OK) {
                    print_level(SV_ERROR, "hd_videoproc_set HD_VIDEOCAP_PARAM_CTRL fail=%d\n", eRet);
                    return SV_FAILURE;
                }
            }
        }
    }

    {
        // VPE3和VPE4
        UINT8 u8ChannelIndex = (s32VpeId % 2) * 5;
        for (size_t i = 0; i < 5; i++) // 画中画vpss组输出端口=1个
        {
            // if (0 != (u8ChannelIndex+i) && 8 != (u8ChannelIndex+i))
            // {
            //     printf("(u8ChannelIndex+i)=%d\n", (u8ChannelIndex+i));
            //     continue;
            // }

            if (m_stVpssInfo.bVpePathOpened[u8ChannelIndex+i])
            {
                if (m_stVpssInfo.bVpePathStarted[u8ChannelIndex+i])
                {
                    hd_videoproc_stop(m_stVpssInfo.stVpeProcPath[u8ChannelIndex+i]);
                }
                hd_videoproc_close(m_stVpssInfo.stVpeProcPath[u8ChannelIndex+i]);
            }
            
            eRet = hd_videoproc_open(HD_VIDEOPROC_IN(s32VpeId, 0), HD_VIDEOPROC_OUT(s32VpeId, i), &m_stVpssInfo.stVpeProcPath[u8ChannelIndex+i]);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_open fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }
            print_level(SV_INFO, "u8ChannelIndex + i = %d, m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex+i]=%d \n", u8ChannelIndex + i, m_stVpssInfo.stPicInPicProcPath[u8ChannelIndex + i]);
            
            switch (i)  // 不同通道分辨率的设置，增加处理模块
            {
                // 如果被设置为背景画面，会自动作为其余通道的背景画面
                case 0: // stPriVencSize
                    if (5 == u8ChannelIndex)
                    {
                        stProcOut.bg.w   = ALIGN_FLOOR(2560, 16);
                        stProcOut.bg.h   = ALIGN_FLOOR(1440, 2);
                        stProcOut.rect.x = ALIGN_FLOOR(0, 4);
                        stProcOut.rect.y = ALIGN_FLOOR(0, 2);
                        stProcOut.rect.w = ALIGN_FLOOR(640, 4);
                        stProcOut.rect.h = ALIGN_FLOOR(320, 2);
                        stProcOut.depth  = 1;
                        stProcOut.func   = 0;
                        stProcOut.dim.w  = 0;
                        stProcOut.dim.h  = 0;
                    }
                    else
                    {
                        /*
                             关于VPE设置输出到同一缓存块的细节
                                1. 通过设置bg来设定背景缓存块的大小
                                2. 通过设置dim为0来表示输出到的是背景缓存块
                                3. 通过设置rect来控制输出的位置和尺寸的大小
                        */ 
                        stProcOut.bg.w   = ALIGN_FLOOR(2560, 16);
                        stProcOut.bg.h   = ALIGN_FLOOR(1440, 2);
                        stProcOut.rect.x = ALIGN_FLOOR(0, 4);
                        stProcOut.rect.y = ALIGN_FLOOR(0, 2);
                        stProcOut.rect.w = ALIGN_FLOOR(2560, 16);
                        stProcOut.rect.h = ALIGN_FLOOR(1440, 2);
                        stProcOut.depth  = 1;
                        stProcOut.func   = 0;
                        stProcOut.dim.w  = 0;
                        stProcOut.dim.h  = 0;
                    }
                    
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 1: 
                    stProcOut.depth  = 0;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 1920;
                    stProcOut.dim.h  = 1080;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 2:
                    stProcOut.depth  = 0;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 1280;
                    stProcOut.dim.h  = 720;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 3: 
                    if (5 == u8ChannelIndex)
                    {
                        stProcOut.bg.w   = ALIGN_FLOOR(2560, 16);
                        stProcOut.bg.h   = ALIGN_FLOOR(1440, 2);
                        stProcOut.rect.x = ALIGN_FLOOR(0, 4);
                        stProcOut.rect.y = ALIGN_FLOOR(0, 2);
                        stProcOut.rect.w = ALIGN_FLOOR(640, 4);
                        stProcOut.rect.h = ALIGN_FLOOR(320, 2);
                        stProcOut.depth  = 2;
                        stProcOut.func   = 0;
                        stProcOut.dim.w  = 0;
                        stProcOut.dim.h  = 0;
                    }
                    else
                    {
                        // stProcOut.bg.w   = 2560;
                        // stProcOut.bg.h   = 1440;
                        // stProcOut.rect.x = 2560-720;
                        // stProcOut.rect.y = 0;
                        // stProcOut.rect.w = 720;
                        // stProcOut.rect.h = 640;
                        stProcOut.depth  = 2;
                        stProcOut.func   = 0;
                        stProcOut.dim.w  = 720;
                        stProcOut.dim.h  = 640;
                    }
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                case 4:
                    stProcOut.depth  = 0;
                    stProcOut.func   = 0;
                    stProcOut.dim.w  = 2560;
                    stProcOut.dim.h  = 1440;
                    stProcOut.pxlfmt = HD_VIDEO_PXLFMT_YUV420;
                    stProcOut.dir    = HD_VIDEO_DIR_NONE;
                    stProcOut.frc    = HD_VIDEO_FRC_RATIO(1, 1);
                    print_level(SV_INFO, "stProcOut.dim.w=%d, stProcOut.dim.h=%d \n", stProcOut.dim.w, stProcOut.dim.h);
                    break;
                default:
                    break;
            }
            
            eRet = hd_videoproc_set(m_stVpssInfo.stVpeProcPath[u8ChannelIndex+i], HD_VIDEOPROC_PARAM_OUT, &stProcOut);
            if (eRet != HD_OK) {
                print_level(SV_ERROR, "u8ChannelIndex=%d, hd_videoproc_set HD_VIDEOCAP_PARAM_OUT fail=%d\n", u8ChannelIndex+i, eRet);
                return SV_FAILURE;
            }

            m_stVpssInfo.bVpePathOpened[u8ChannelIndex+i] = TRUE;
        }
    }
    // pull in指定输出到同一个缓存空间

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁VPSS通道组, close
 * 输入参数: s32GrpId --- 通道组ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_DestroyGrp(int32_t s32GrpId)
{
    HD_RESULT eRet = HD_OK;

    switch (s32GrpId)
    {
        case 0: //VPSS组1
            for (size_t i = 0; i < 5; i++)
            {
                if (!m_stVpssInfo.bPathOpened[i])
                {
                    // 没有打开，不用关闭
                    continue;
                }
                eRet = hd_videoproc_close(m_stVpssInfo.stProcPath[i]);
                if (eRet != HD_OK) {
                    printf("i=%d, mpp_vpss_DestroyGrp hd_videoproc_close fail=%d\n", i, eRet);
                    return SV_FAILURE;
                }

                m_stVpssInfo.bPathOpened[i] = FALSE;
            }
            break;
        case 1: //VPSS组2
            for (size_t i = 5; i < 10; i++)
            {
                if (!m_stVpssInfo.bPathOpened[i])
                {
                    // 没有打开，不用关闭
                    continue;
                }
                eRet = hd_videoproc_close(m_stVpssInfo.stProcPath[i]);
                if (eRet != HD_OK) {
                    printf("i=%d, mpp_vpss_DestroyGrp hd_videoproc_close fail=%d\n", i, eRet);
                    return SV_FAILURE;
                }
                m_stVpssInfo.bPathOpened[i] = FALSE;
            }
            break;
        default:
            break;
    }

    // 打开主码流的EX_Stamp
    eRet = hd_videoproc_close(m_stVpssInfo.stProcStampPath[4]);
    if (eRet != HD_OK) {
        printf("i=%d, mpp_vpss_DestroyGrp hd_videoproc_close fail=%d\n", 0, eRet);
        return SV_FAILURE;
    }

    // eRet = hd_videoproc_close(m_stVpssInfo.stProcStampPath[1]);
    // if (eRet != HD_OK) {
    //     printf("i=%d, mpp_vpss_DestroyGrp hd_videoproc_close fail=%d\n", 0, eRet);
    //     return SV_FAILURE;
    // }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道画面是否镜像或反转
 * 输入参数: s32Chn --- VPSS通道号
             bMirror --- 是否水平翻转
             bFlip --- 是否垂直翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_SetChnMirrorFlip(int32_t s32Chn, SV_BOOL bMirror, SV_BOOL bFlip)
{
    HD_RESULT eRet = HD_OK;

    

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取通道画面是否镜像或反转
 * 输入参数: s32Chn --- VPSS通道号
 * 输出参数: bMirror --- 是否水平翻转
             bFlip --- 是否垂直翻转
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_GetChnMirrorFlip(int32_t s32Chn, SV_BOOL* bMirror, SV_BOOL* bFlip)
{
    // 后续通过配置参数进行设置

#if 0
    int32_t s32Ret = 0, i;
    VPSS_GRP VpssGrp = 0;
    VPSS_CHN VpssChn = 0;
    VPSS_CHN_ATTR_S stChnAttr = {0};

    if (s32Chn < 0 || s32Chn > 2)
    {
        return ERR_INVALID_CHNID;
    }

    VpssChn = s32Chn;
    s32Ret = HI_MPI_VPSS_GetChnAttr(VpssGrp, VpssChn, &stChnAttr);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_VPSS_GetChnAttr failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    *bMirror = stChnAttr.bMirror;
    *bFlip = stChnAttr.bFlip;
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道画面顺时针旋转角度
 * 输入参数: s32Chn --- VPSS通道号
             s32Angle --- 旋转角度[0-3]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_SetChnRotate(int32_t s32Chn, SV_ROT_ANGLE_E enAngle)
{
    // 后续通过配置参数进行设置

#if 0
    int32_t s32Ret = 0, i;
    VPSS_GRP VpssGrp = 0;
    VPSS_CHN VpssChn = 0;
    ROTATION_E enRotation = 0;

    if (s32Chn < 0 || s32Chn > 2)
    {
        return ERR_INVALID_CHNID;
    }

    VpssChn = s32Chn;
    s32Ret = HI_MPI_VPSS_GetChnRotation(VpssGrp, VpssChn, &enRotation);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_VPSS_GetChnRotation failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    enRotation = enAngle;
    s32Ret = HI_MPI_VPSS_SetChnRotation(VpssGrp, VpssChn, enRotation);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_VPSS_SetChnRotation failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stVpssInfo.enRotateAngle = enAngle;
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道是否启动低延时
 * 输入参数: s32Chn --- VPSS通道号
             bLowDelay --- 是否启动低延时
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_SetChnLowDelay(int32_t s32Chn, SV_BOOL bLowDelay)
{
    HD_RESULT eRet = HD_OK;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取通道一张灰阶图
 * 输入参数: s32Chn --- VPSS通道号
 * 输出参数: ppvBuf --- 数据缓存指针
             pu32Width --- 画面宽度
             pu32Height --- 画面高度
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 读取完数据后需要调用 mpp_vpss_ReleaseChnGrayFrame 释放数据
 *****************************************************************************/
int32_t mpp_vpss_GetChnGrayFrame(int32_t s32Chn, int32_t s32VpssChn, void **ppvBuf, uint32 *pu32Width, uint32 *pu32Height)
{
    HD_RESULT       eRet = HD_OK;
    HD_VIDEO_FRAME  stVideoFrame = {0};
    UINTPTR         pPhyAddr, pVirAddr;
    UINT32          u32ImageSize = 0;
    char            cFilePathName[32] = {0};
    FILE            *pFileOutMain;

    print_level(SV_INFO, "mpp_vpss_GetChnGrayFrame capture image s32Chn=%d \n", s32Chn);
    
    // 需要设置VPROC的depth>=1才能用于拉取图片
    eRet = hd_videoproc_pull_out_buf(m_stVpssInfo.stProcPath[s32Chn*4+s32VpssChn], &stVideoFrame, -1);
    if (eRet != HD_OK) {
        print_level(SV_ERROR, "hd_videoproc_pull_out_buf fail=%d\n", eRet);
        return SV_FAILURE;
    }else{
        print_level(SV_INFO, "hd_videoproc_pull_out_buf OK=%d\n", eRet);
    }

    pPhyAddr = hd_common_mem_blk2pa(stVideoFrame.blk);
    if(0 == pPhyAddr){
        print_level(SV_ERROR, "hd_common_mem_blk2pa error !! \r\n");
    }else{
        print_level(SV_INFO, "hd_common_mem_blk2pa OK !! \r\n");
    }

    switch (s32VpssChn)
    {
        case 0:
            u32ImageSize = VDO_YUV_BUFSIZE(2560, 1440, HD_VIDEO_PXLFMT_YUV420);
            break;
        case 1:
            u32ImageSize = VDO_YUV_BUFSIZE(1920, 1080, HD_VIDEO_PXLFMT_YUV420);
            break;
        case 2:
            u32ImageSize = VDO_YUV_BUFSIZE(1280, 720,  HD_VIDEO_PXLFMT_YUV420);
            break;
        case 3:
            u32ImageSize = VDO_YUV_BUFSIZE(608, 352,  HD_VIDEO_PXLFMT_YUV420);
            break;
        default:
            break;
    }
    pVirAddr = (UINTPTR)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_CACHE, pPhyAddr, u32ImageSize);
    if(0 == pVirAddr){
        print_level(SV_ERROR, "hd_common_mem_mmap error !! \r\n");
    }else{
        print_level(SV_INFO, "hd_common_mem_mmap OK !! \r\n");
    }

    snprintf(cFilePathName, 32, "/mnt/pyl/yuv420.raw");
    remove(cFilePathName);

    pFileOutMain = fopen(cFilePathName, "wb");
    if(NULL == pFileOutMain){
        print_level(SV_ERROR, "open file (%s) fail.... \r\n", cFilePathName);
    }else{
        print_level(SV_INFO, "open file OK stVideoFrame.loff[0]=%d, stVideoFrame.ph[0]=%d !! \r\n", stVideoFrame.loff[0], stVideoFrame.ph[0]);
        UINT8 *ptr = (UINT8*)(pVirAddr + stVideoFrame.phy_addr[0] - pPhyAddr);
        UINT32 len = stVideoFrame.loff[0]*stVideoFrame.ph[0] * 3 / 2;
        fwrite(ptr, 1, len, pFileOutMain);
        fflush(pFileOutMain);
        print_level(SV_INFO, "write file OK len=%d !! \r\n", len);
        hd_common_mem_flush_cache((void*)ptr, len);
    }

    fclose(pFileOutMain);
    hd_common_mem_munmap((void*)pVirAddr, u32ImageSize);

    eRet = hd_videoproc_release_out_buf(m_stVpssInfo.stProcPath[s32Chn*4+s32VpssChn], &stVideoFrame);
    if (eRet != HD_OK) {
        print_level(SV_ERROR, "hd_videoproc_release_out_buf fail=%d\n", eRet);
        return SV_FAILURE;
    }else{
        print_level(SV_INFO, "hd_videoproc_release_out_buf OK=%d\n", eRet);
    }
    sleep(1);
    // *pu32Width = stVideoFrame.dim.w;
    // *pu32Height = stVideoFrame.dim.h;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 释放灰阶图数据
 * 输入参数: s32Chn --- 视频源通道号
             s32VpssChn--- VPSS通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_ReleaseChnGrayFrame(int32_t s32Chn, int32_t s32VpssChn)
{
    
    return SV_SUCCESS;
}

int32_t mpp_vpss_GetPictureInPictureImage(int32_t s32VpssChn, HD_VIDEO_FRAME *stVideoFrame, UINTPTR *pPhyAddr, UINTPTR *pVirAddr){
    HD_RESULT       eRet = HD_OK;
    UINT32          u32ImageSize = 0;

    print_level(SV_INFO, "mpp_vpss_GetPictureInPictureImage capture image s32Chn=%d \n", m_stVpssInfo.stProcPath[1*5+ s32VpssChn]);
    u32ImageSize = VDO_YUV_BUFSIZE(PIC_IN_PIC_WIDTH, PIC_IN_PIC_HEIGHT, HD_VIDEO_PXLFMT_YUV420);

    // 需要设置VPROC的depth>=1才能用于拉取图片
    eRet = hd_videoproc_pull_out_buf(m_stVpssInfo.stProcPath[0*5+ s32VpssChn], stVideoFrame, -1);
    if (eRet != HD_OK) {
        print_level(SV_ERROR, "hd_videoproc_pull_out_buf fail=%d\n", eRet);
        return FALSE;
    }else{
        // print_level(SV_INFO, "hd_videoproc_pull_out_buf OK=%d\n", eRet);
    }

    *pPhyAddr = hd_common_mem_blk2pa(stVideoFrame->blk);
    if(0 == (*pPhyAddr)){
        return FALSE;
        print_level(SV_ERROR, "hd_common_mem_blk2pa error !! \r\n");
    }else{
        *pVirAddr = (UINTPTR)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_NONCACHE, *pPhyAddr, u32ImageSize);
        if(0 == (*pVirAddr)){
            return FALSE;
            print_level(SV_ERROR, "hd_common_mem_mmap error !! \r\n");
        }
    }

    return SV_SUCCESS;
}

int32_t mpp_vpss_ReleasePictureInPictureImage(int32_t s32VpssChn, HD_VIDEO_FRAME *stVideoFrame, UINTPTR *pPhyAddr, UINTPTR *pVirAddr){
    HD_RESULT       eRet = HD_OK;
    UINT32          u32ImageSize = 0;

    // YUV
    u32ImageSize = VDO_YUV_BUFSIZE(PIC_IN_PIC_WIDTH, PIC_IN_PIC_HEIGHT, HD_VIDEO_PXLFMT_YUV420);

    eRet = hd_common_mem_munmap((void*)*pVirAddr, u32ImageSize);
    if (eRet != HD_OK) {
        print_level(SV_ERROR, "hd_common_mem_munmap fail=%d\n", eRet);
        return SV_FAILURE;
    }else{
        // print_level(SV_INFO, "hd_common_mem_munmap OK=%d\n", eRet);
    }

    eRet = hd_videoproc_release_out_buf(m_stVpssInfo.stProcPath[0*5+s32VpssChn], stVideoFrame);
    if (eRet != HD_OK) {
        print_level(SV_ERROR, "hd_videoproc_release_out_buf fail=%d\n", eRet);
        return SV_FAILURE;
    }else{
        // print_level(SV_INFO, "hd_videoproc_release_out_buf OK=%d\n", eRet);
    }
}

/*
    pyl-申请内存空间, 只是再VPSS内部使用，用完后就会进行释放操作
*/ 
static int32_t mpp_vpss_CreatCompoundVB(MPP_VPSS_DUMP_MEMBUF_S *pstVpssMem)
{
    INT8  u32RetryTimes = 30;
    UINT32 u32BlkSize   = pstVpssMem->u32MemSize;
    UINT32 u32BlkCnt    = 1;
    HD_COMMON_MEM_DDR_ID  eDDRId  = DDR_ID0;
    VENDOR_COMMON_MEM_VB_POOL   s32Pool;

    // 池
    s32Pool = vendor_common_mem_create_pool("vpss", u32BlkSize, u32BlkCnt, eDDRId);
    if (VENDOR_COMMON_MEM_VB_INVALID_POOL == s32Pool)
    {
        print_level(SV_ERROR, "vendor_common_mem_create_pool BlkSize = %d failed! \n", pstVpssMem->u32MemSize);
        vendor_common_mem_destroy_pool(s32Pool);
        return SV_FAILURE;
    }
    
    // pyl-数据类型没有一致，需要改变
    pstVpssMem->hPool = (uint32_t)s32Pool;
    print_level(SV_INFO, "HI_MPI_VB_CreatePool:id:%d BlkSize:%d\n", s32Pool, pstVpssMem->u32MemSize);

    // 块
    while (HD_COMMON_MEM_VB_INVALID_BLK == (pstVpssMem->hBlock = hd_common_mem_get_block(s32Pool, u32BlkSize, eDDRId)))
    {
        u32RetryTimes--;
        if(0 >= u32RetryTimes){
            print_level(SV_ERROR, "hd_common_mem_get_block failed!\n");
            break;
        }
        usleep(40000);
    }
     
    pstVpssMem->u64PhyAddr = hd_common_mem_blk2pa(pstVpssMem->hBlock);
    pstVpssMem->u64VirAddr = hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_NONCACHE, pstVpssMem->u64PhyAddr, pstVpssMem->u32MemSize);
    if(0 == pstVpssMem->u64VirAddr){
        print_level(SV_ERROR, "HI_MPI_SYS_Mmap failed\n");
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/*
    释放申请的内存池和内存块
*/ 
static int32_t mpp_vpss_ReleaseCompoundVB(void)
{
    HD_RESULT eRet = FALSE;
    INT8  u32RetryTimes = 10;

    for (size_t i = 0; i < 3; i++)
    {
        if (0 != stVpssMem[i].u64VirAddr)
        {
            eRet = hd_common_mem_munmap(stVpssMem[i].u64VirAddr, stVpssMem[i].u32MemSize);  
            stVpssMem[i].bCreated = SV_FALSE;
            stVpssMem[i].u64PhyAddr = 0;
            stVpssMem[i].u64VirAddr = 0;      
        }

        eRet = hd_common_mem_release_block(stVpssMem[i].hBlock);
        if(HD_OK != eRet){
            print_level(SV_ERROR, "HI_MPI_VB_ReleaseBlock failed with %#x!\n", eRet);
            return SV_FAILURE;
        }
        
        while (HD_OK != (eRet =vendor_common_mem_destroy_pool(stVpssMem[i].hPool)))
        {
            usleep(5000);
            if(u32RetryTimes++ > 10)
            {
                print_level(SV_ERROR, "HI_MPI_VB_DestroyPool failed![%#x]\n", eRet);
                break;
            }
        }   
    }
    
    return SV_SUCCESS;
}

// static int32_t mpp_vpss_VGSSend(VIDEO_FRAME_INFO_S *pstFrameIn, VIDEO_FRAME_INFO_S *pstFrameOut)
// {

//     return SV_SUCCESS;
// }

// static int32_t mpp_vpss_CompoundFrame(VIDEO_FRAME_INFO_S* pVBufIn, VIDEO_FRAME_INFO_S* pVBufOut, SV_SIZE_S stPriVencSize)
// {

//     return SV_SUCCESS;
// }

/*
    暂时用于测试VPSS各个通道处理图像流是否正常
*/ 
static void *mpp_vpss_GetSDFrameBody(void *pvArg)
{
    HD_RESULT       eRet = HD_OK;
    HD_VIDEO_FRAME  stVideoFrame = {0};

    while (m_stVpssInfo.bPathStarted[1])
    {
        // camera2
        eRet = hd_videoproc_pull_out_buf(m_stVpssInfo.stProcPath[4+0], &stVideoFrame, -1);
        if (eRet != HD_OK) {
            printf("hd_videoproc_pull_out_buf fail=%d\n", eRet);
            return SV_FAILURE; 
        }else{
            printf("hd_videoproc_pull_out_buf OK=%d\n", eRet);
        }

        hd_videoproc_release_out_buf(m_stVpssInfo.stProcPath[1], &stVideoFrame);

        print_level(SV_INFO, " accept mpp_vpss_GetSDFrameBody... w=%d, h=%d \n", stVideoFrame.dim.w, stVideoFrame.dim.h);
        sleep(1);
    }

    return NULL;
}

/*
    现在用于测试VPROC的通道是否正常出视频流数据
*/ 
pthread_t thread = 0;
int32_t mpp_vpss_GetSDFrame_Start(void)
{
    int32_t    s32Ret = 0;

    s32Ret = pthread_create(&thread, NULL, mpp_vpss_GetSDFrameBody, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VPSS_SD failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    return SV_SUCCESS;
}

int32_t mpp_vpss_GetSDFrame_Stop(void)
{
    int32_t s32Ret = 0;
    void * pvRetval = NULL;

    s32Ret = pthread_join(thread, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for VENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    
    return SV_SUCCESS;
}

int32_t mpp_vpss_RenewGrp(int32_t s32GrpId, VIDEO_MODE_EE newVideoMode, uint32 u32NewW, uint32 u32NewH)
{
    int32_t s32Ret = 0, i;

    if(m_stVpssInfo.enVideoMode == VIDEO_MODE_PAL || m_stVpssInfo.enVideoMode == VIDEO_MODE_NTSC)
    {
        s32Ret = mpp_vpss_GetSDFrame_Stop();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_GetSDFrame_Stop failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }

    s32Ret = mpp_vpss_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_Fini failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    for (i = 0; i < m_stVpssInfo.u32ChnNum; i++)
    {
        s32Ret = mpp_vpss_CreateGrp(i, u32NewW, u32NewH);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_CreateGrp failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }
    
    if(newVideoMode == VIDEO_MODE_PAL || newVideoMode == VIDEO_MODE_NTSC)
    {
        s32Ret = mpp_vpss_GetSDFrame_Start();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_GetSDFrame_Start failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    for (i = 0; i < 2; i++)
    {
        s32Ret = mpp_vpss_SetChnMirrorFlip(i, m_stVpssInfo.bImageMirror, m_stVpssInfo.bImageFlip);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
    
    m_stVpssInfo.enVideoMode = newVideoMode;
    
    return SV_SUCCESS;
}

void mpp_vpss_SetPriVencSize(SV_SIZE_S stPriVencSize)
{
    // stVpssDEI.stPriVencSize = stPriVencSize;
}

int32_t mpp_vpss_GetChnPath(STREAM_TYPE_E enStreamType, int32_t s32Chn, HD_PATH_ID *pathID){
    *pathID = m_stVpssInfo.stProcPath[s32Chn*5 + enStreamType];

    if (0 == pathID)
    {
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

HD_PATH_ID mpp_vpss_GetChnPathForOSD(int32_t u32Path, int32_t s32Chn){
    return m_stVpssInfo.stProcPath[s32Chn * 5 + u32Path];
}

HD_PATH_ID mpp_vpss_GetChnStampPathForOSD(int32_t u32Path, int32_t s32Chn){
    // 目前先测试ROI区域
    return m_stVpssInfo.stProcStampPath[4];
}

HD_PATH_ID mpp_vpss_GetVpssCtrlPath(){
    return m_stVpssInfo.stProcCtrl[2];
}

HD_PATH_ID mpp_vpss_GetChnOsdPath(int32_t u32Path, int32_t s32Chn){
    return m_stVpssInfo.stPicInPicProcPath[s32Chn * 5 + u32Path];
}

HD_PATH_ID mpp_vpss_GetPicInPicPath(STREAM_TYPE_E enStreamType, int32_t s32Chn){
    return m_stVpssInfo.stPicInPicProcPath[s32Chn * 5 + enStreamType];
}

HD_PATH_ID mpp_vpss_GetVpePath(STREAM_TYPE_E enStreamType, int32_t s32Chn){
    return m_stVpssInfo.stVpeProcPath[s32Chn * 5 + enStreamType];
}

HD_PATH_ID mpp_vpss_OSDChnStateTriggerForVO(BOOL bStates){
    HD_RESULT eRet = HD_OK;
    HD_PATH_ID u32VpssWithOSDPath    = mpp_vpss_GetChnOsdPath(3, 0);
    HD_PATH_ID u32VpssWithOutOSDPath = mpp_vpss_GetChnPathForOSD(3, 0); // TODO：复用了通道选择函数功能
    HD_PATH_ID u32VoPath             = mpp_vo_get_id();
    
    // True : 打开OSD，绑定到VO模块;
    // False: 关闭OSD，解绑到VO模块;
    if (TRUE == bStates)
    {
        hd_videoout_stop(u32VoPath);
        hd_videoproc_stop(u32VpssWithOutOSDPath);
        eRet = hd_videoproc_unbind(u32VpssWithOutOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_INFO, "hd_videoproc_unbind Fail %d...\n", eRet);
            return eRet;
        }

        eRet = hd_videoproc_bind(u32VpssWithOSDPath, u32VoPath);
        if (HD_OK != eRet)
        {
            print_level(SV_INFO, "hd_videoproc_bind Fail %d...\n", eRet);
            return eRet;
        }

        mpp_vo_UpdateBingingSrc(u32VpssWithOSDPath);
        hd_videoproc_start(u32VpssWithOSDPath);
        hd_videoout_start(u32VoPath);
    }
    else
    {
        hd_videoout_stop(u32VoPath);
        hd_videoproc_stop(u32VpssWithOSDPath);
        eRet = hd_videoproc_unbind(u32VpssWithOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_INFO, "hd_videoproc_unbind Fail %d...\n", eRet);
            return eRet;
        }

        eRet = hd_videoproc_bind(u32VpssWithOutOSDPath, u32VoPath);
        if (HD_OK != eRet)
        {
            print_level(SV_INFO, "hd_videoproc_bind Fail %d...\n", eRet);
            return eRet;
        }

        hd_videoproc_start(u32VpssWithOutOSDPath);
        hd_videoout_start(u32VoPath);
    }

    return SV_SUCCESS;
}

HD_PATH_ID mpp_vpss_OSDChnStateTriggerForStream(STREAM_TYPE_E enStreamType, BOOL bStates){
    HD_RESULT  eRet = HD_OK;
    HD_PATH_ID u32VpssWithOSDPath = 0;
    HD_PATH_ID u32VpssWithOutOSDPath = 0; // TODO：复用了通道选择函数功能
    HD_PATH_ID u32VencPath = 0;

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            u32VpssWithOSDPath    = mpp_vpss_GetChnOsdPath(0, 0);
            u32VpssWithOutOSDPath = mpp_vpss_GetChnPathForOSD(0, 0);
            u32VencPath           = mpp_venc_GetChnPathForVosd(STREAM_TYPE_PRI, 0);
            m_stVpssInfo.bVpssWithVosd[0] = bStates;
            break;
        case STREAM_TYPE_SEC:
            u32VpssWithOSDPath    = mpp_vpss_GetChnOsdPath(1, 0);
            u32VpssWithOutOSDPath = mpp_vpss_GetChnPathForOSD(1, 0);
            u32VencPath           = mpp_venc_GetChnPathForVosd(STREAM_TYPE_SEC, 0);
            m_stVpssInfo.bVpssWithVosd[1] = bStates;
            break;
        case STREAM_TYPE_SNAP0:
            u32VpssWithOSDPath    = mpp_vpss_GetChnOsdPath(2, 0);
            u32VpssWithOutOSDPath = mpp_vpss_GetChnPathForOSD(2, 0);
            u32VencPath           = mpp_venc_GetChnPathForVosd(STREAM_TYPE_SNAP0, 0);
            m_stVpssInfo.bVpssWithVosd[2] = bStates;
            break;
        case STREAM_TYPE_SNAP1:
            u32VpssWithOSDPath    = mpp_vpss_GetChnOsdPath(3, 0);
            u32VpssWithOutOSDPath = mpp_vpss_GetChnPathForOSD(3, 0);
            u32VencPath           = mpp_venc_GetChnPathForVosd(STREAM_TYPE_SNAP1, 0);
            m_stVpssInfo.bVpssWithVosd[3] = bStates;
            break;
        default:
            break;
    }

    print_level(SV_INFO, "path id = %x \n", u32VencPath);

    if (TRUE == bStates)
    {
        eRet = hd_videoenc_stop(u32VencPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoenc_stop Fail %d...\n", eRet);
        }

        eRet = hd_videoproc_stop(u32VpssWithOutOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_stop Fail %d...\n", eRet);
        }

        eRet = hd_videoproc_unbind(u32VpssWithOutOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_unbind Fail %d...\n", eRet);
        }

        eRet = hd_videoproc_bind(u32VpssWithOSDPath, u32VencPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_bind Fail %d...\n", eRet);
        }

        hd_videoproc_start(u32VpssWithOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_start Fail %d...\n", eRet);
        }

        hd_videoenc_start(u32VencPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoenc_start Fail %d...\n", eRet);
        }
    }
    else
    {
        eRet = hd_videoenc_stop(u32VencPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoenc_stop Fail %d...\n", eRet);
        }

        eRet = hd_videoproc_stop(u32VpssWithOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_stop Fail %d...\n", eRet);
        }

        eRet = hd_videoproc_unbind(u32VpssWithOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_unbind Fail %d...\n", eRet);
        }

        eRet = hd_videoproc_bind(u32VpssWithOutOSDPath, u32VencPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_bind Fail %d...\n", eRet);
        }

        hd_videoproc_start(u32VpssWithOutOSDPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoproc_start Fail %d...\n", eRet);
        }

        hd_videoenc_start(u32VencPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoenc_start Fail %d...\n", eRet);
        }
    }
}

/******************************************************************************
 * 函数功能: 暂停VPSS模块，对已经启动的通道进行stop
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
int32_t mpp_vpss_Interrupt(SV_BOOL bInterrupt)
{
    int i, j;
    
    // TODO， delete
    for(i = 0; i < m_stVpssInfo.u32ChnNum * MPP_VPSS_CHN_BUTT; i ++)
    {
        for(j = 0; j < MPP_VPSS_CHN_BUTT; j++)
        {

            m_stVpssInfo.bInterrupt[i] = bInterrupt;
        }
    }

    // 关闭被venc使用的vpss通道
    if (TRUE == bInterrupt)
    {
        return mpp_vpss_Stop();
    }
    else
    {
        return mpp_vpss_Start();
    }
}

/*
    重新设置VPSS通道输出的分辨率
*/ 
int32_t mpp_vpss_ReCreateChannel(int32_t s32Chn, int32_t s32VpssChn, SV_SIZE_S u32Size){

    
    return SV_SUCCESS;
}

/*
    1. 增加算法通道;
    2. 输出RGB格式图像;
    3. 将数据地址写入共享内存空间
    从VPSS的OSD通道获取图像帧数据，裁减成算法所需的数据，
*/

typedef struct message {
    long        mtype;
    uint32_t    u32TimeStamp;
    uint32_t    u32TimeDelta;
    unsigned long pDataPhy;
}MESSAGE_T;

/******************************************************************************
 * 函数功能: VPSS模块线程体(控制算法通道)
 * 输入参数: pstVpssInfo --- 视频处理控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_vpss_AlgBody(void *pvArg)
{
    int32_t s32Ret = 0, i;
    uint32 u32Fd;
    uint64 u64Pts = 1, u64PtsTmp = 0;
    int32_t s32ViChn, s32VpssChn;
    int32_t s32Idx, s32ClearIdx, s32ClearMbIdx;
    struct timespec tvBegin, tvEnd;
    void *mb = NULL;
    void *szMB[ALG_MULTI_BUF_NUM] = {NULL};
    int channel = *((int*)pvArg);
    char thread_name[32];
    MPP_VPSS_INFO_S *pstVpssInfo = (MPP_VPSS_INFO_S *)&m_stVpssInfo;
    memset(szMB, NULL, sizeof(szMB));
    uint64 u64PtsLast = 0;
    sint64 s64Now = 0, s64Last = 0;
    uint64 u64DiffPts = 0ll;

    sprintf(thread_name, "mpp_vpss_AlgBody[%d]", channel);
    s32Ret = prctl(PR_SET_NAME, thread_name);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    SV_BOOL bStartClear = SV_FALSE;
    m_stVpssInfo.bAlgSingalFrame = SV_FALSE;        /* optalert客户调试，先关掉这个模式 */

    s32ViChn   = channel / MPP_VPSS_CHN_BUTT;
    s32VpssChn = channel % MPP_VPSS_CHN_BUTT;

    /* 分配两个ARGB地址空间 */
    HD_COMMON_MEM_VB_BLK blk[2];
    UINTPTR phy[2];
    UINT32 *vPhy[2];
    for (size_t i = 0; i < 2; i++)
    {
        blk[i] = hd_common_mem_get_block(HD_COMMON_MEM_COMMON_POOL, 608*352*3/2, DDR_ID0);
        if (blk[i] == HD_COMMON_MEM_VB_INVALID_BLK) {
            printf("%d, get block fail\r\n", i);
            return HD_ERR_NOMEM;
        }

        phy[i] = hd_common_mem_blk2pa(blk[i]);
        if (phy[i] == 0) {
            printf("blk2pa fail, buf_blk = 0x%x\r\n", phy[i]);
            return HD_ERR_NOMEM;
        }

        // 分配HDAL的内存空间
        vPhy[i] = (UINT8 *)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_NONCACHE, phy[i], 608*352*3/2);
        if (NULL == vPhy[i])
        {
            print_level(SV_ERROR, "Error allocating memory space at the bottom canvas\n");
        }
        else
        {
            memset(vPhy[i], 0x00, 608*352*3/2);
        }
    }

    MESSAGE_T stMsg1, stTemp;
    MESSAGE_T stMsg2;
    HD_VIDEO_FRAME stVideoFrame;

    mqd_t mq;
    struct mq_attr stMqAttr;
    stMqAttr.mq_flags   = 0;
    stMqAttr.mq_maxmsg  = 2;
    stMqAttr.mq_msgsize = sizeof(MESSAGE_T);
    stMqAttr.mq_curmsgs = 0;

    // 创建或者打开队列
    mq = mq_open(QUEUE_NAME, O_CREAT | O_RDWR | O_NONBLOCK, 0644, &stMqAttr);
    if ((mqd_t)-1 == mq)
    {
        perror("mq_open");
        exit(EXIT_FAILURE);
    }
    else
    {
        print_level(SV_INFO, "打开消息队列成功...\n");
    }

    // 清空消息队列，由于ipsys是生产者，数据来源只有它
    mq_getattr(mq, &stMqAttr); 
    while (1)
    {
        ssize_t bytesRead = mq_receive(mq, (char *)&stTemp, stMqAttr.mq_msgsize, NULL);
        if (-1 == bytesRead)
        {
            if (errno == EAGAIN)
            {
                break;
            }
            else
            {
                perror("mq_receive");
            }
        }
        print_level(SV_INFO, "旧数据: bytesRead=%d...\n", bytesRead);
    }

    /*
        调整消息队列为阻塞模式，清除 O_NONBLOCK 标志，切换到阻塞模式
    */ 
    int flags = fcntl(mq, F_GETFL);
    if (flags == -1) {
        perror("fcntl F_GETFL");
        // exit(EXIT_FAILURE);
    }

    flags &= ~O_NONBLOCK;
    if (fcntl(mq, F_SETFL, flags) == -1) {
        perror("fcntl F_SETFL");
        // exit(EXIT_FAILURE);
    }

    uint32_t u32Index = 0;
    while (1)
    {
        /*
            消息队列：数量设置为2；
        */ 
        // print_level(SV_INFO, "共享物理地址给Alg进程...\n");

        // TODO 1. 假设已经拿到RGB的物理地址
        s32Ret = hd_videoproc_pull_out_buf(m_stVpssInfo.stProcPath[3], &stVideoFrame, -1);
        if (HD_OK == s32Ret)
        {
            // A: 映射
            UINTPTR pPhyAddr = hd_common_mem_blk2pa(stVideoFrame.blk);
            if(0 == pPhyAddr){
                print_level(SV_ERROR, "hd_common_mem_blk2pa error !! \r\n");
            }else{
                // print_level(SV_INFO, "hd_common_mem_blk2pa OK !! \r\n");
            }
            
            // 把数据拷贝到缓存中
            if (NULL == hd_gfx_memcpy(phy[u32Index], pPhyAddr, 608*352*3/2))
            {
                print_level(SV_ERROR, "hd_gfx_memcpy Fail ... \n");
            }

            s32Ret = hd_videoproc_release_out_buf(m_stVpssInfo.stProcPath[3], &stVideoFrame);
            if (HD_OK != s32Ret)
            {
                print_level(SV_ERROR, "hd_videoproc_release_out_buf Fail s32Ret=%d... \n", s32Ret);
            }
        }

        // mpp_vpss_GetChnGrayFrame(0, 3, NULL, NULL, NULL);

        // TODO 2. 填充共享内存
        pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn][0] = 0;
        pstVpssInfo->stVpssAlgInfo.u64Pts[s32ViChn][0] = hd_gettime_ms();
        pstVpssInfo->stVpssAlgInfo.bInit[s32ViChn] = SV_TRUE;
        u64PtsLast = pstVpssInfo->stVpssAlgInfo.u64Pts[s32ViChn][0];

        stMsg1.u32TimeStamp = hd_gettime_ms();
        stMsg1.u32TimeDelta = 11;
        stMsg1.pDataPhy     = phy[u32Index];
        if ((mqd_t)-1 == mq_send(mq, (const char*)&stMsg1, sizeof(MESSAGE_T), 0))
        {
            perror("mq_send");
        }
        else
        {
            // print_level(SV_INFO, "send image OK !! \r\n");
        }

        u32Index += 1;
        u32Index = u32Index % 2;
        // sleep_ms(30);
    }

    print_level(SV_INFO, "++mpp_vpss_AlgBody end\n");
    return NULL;
}

