#ifndef _UTILS_COM_H
#define _UTILS_COM_H

#include "defines.h"
#include "errors.h"
#include "common.h"

#define XON 0x11  //!< XON flow control character
#define XOFF 0x13 //!< XOFF flow control character
#define SEARCH "tty"
#define TIMEOUT 2


#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define     DUMP_STO_CHN_MAX        3
#define     DUMP_REC_DEV_MAX        3
#define     DUMP_GPS_VIS_MAX_NUM    20

/* 单个存储设备信息 */
typedef struct tagDumpStoDevInfo_S
{
    SV_BOOL     bInsert;        /* 卡插入状态 */
    sint32      s32FsType;      /* 文件系统类型 */
    sint32      s32DevStat;     /* 设备状态 */
    long        remainSize;     /* 卡剩余容量 */
    long        totalSize;      /* 卡总容量 */
    sint32      remainPerCent;  /* 剩余容量百分比 */
} DUMP_STO_DEV_S;

/* 单个录像设备信息 */
typedef struct tagDumpRecDevInfo_S
{
    sint32      s32RecObj;      /* 录像对象：视频或图片 */
    sint32      s32RecType;     /* 录像类型，报警或普通录像 */
    sint64      s64StartTime;   /* 录像起始时间 */
    char        szFilePath[256];/* 文件路径 */
} DUMP_REC_DEV_S;

/* WIFI dump信息 */
typedef struct tagDumpWifi_s
{
    SV_BOOL     bStaEnable;        /* STA 是否使能 */
    uint32      u32StaStatus;      /* STA 状态 */
    uint32      u32StaSignal;      /* STA 信号 */
    char        szStaIpAddr[32];   /* STA 被分配的IP地址 */
} DUMP_WIFI_S;

/* 录像dump信息 */
typedef struct tagDumpRec_s
{
    SV_BOOL     bStoEnable;     /* 是否使能存储 */
    SV_BOOL     bRecording;     /* 是否正在录像 */
    SV_BOOL     bOverWriteLoop; /* 是否写循环 */
    DUMP_REC_DEV_S astRecDev[DUMP_REC_DEV_MAX];     /* 录像设备信息 */
    char        szLastFilePath[256];    /* 上次录像文件路径 */
} DUMP_REC_S;

/* 存储dump信息 */
typedef struct tagDumpSto_S
{
    SV_BOOL     bEnable;        /* 模块使能状态 */
    SV_BOOL     bConfig;        /* 是否正在配置过程 */
    SV_BOOL     bPowerOff;      /* 是否掉电 */
    SV_BOOL     bRecording;     /* 是否正在录像 */
    SV_BOOL     bInsert;        /* 是否存储设备已插入 */
    SV_BOOL     bException;     /* 是否异常 */
    SV_BOOL     bFull;          /* 是否存储满 */
    DUMP_STO_DEV_S astStoDev[DUMP_STO_CHN_MAX];     /* 存储设备信息 */
    sint32      as32Repair[DUMP_STO_CHN_MAX];       /* 最近一次修复结果 */
} DUMP_STO_S;


/* 4G dump信息 */
typedef struct tagDumpCellular_S
{
    SV_BOOL     bEnable;            /* 4G模块使能状态 */
    SV_BOOL     bException;         /* 是否出现异常 */
    SV_BOOL     bInsertSIM;         /* SIM卡插入状态*/
    sint32      s32ModuleType;      /* 4G模块类型 */
    sint32      s32ModuleOps;       /* 模块操作 */
    sint32      s32ModuleStat;      /* 模块状态 */
    sint32      s32Temperature;     /* 模块温度 */
    uint32      u32Signal;          /* 信号强度     99:未识别 */
    sint8       s8NetType[32];      /* 网络类型 */
    sint8       s8IPAddr[32];       /* DHCP分配到的IP地址 */
    sint8       s8Version[32];      /* 版本 */
    uint8       u8Describe[128];    /* 模块状态描述 */
    char        u8IMEI[32];         /* International Mobile Equipment Identity */
    char        u8ICCD[32];         /* 卡识别号 */
} DUMP_CELL_S;

/* GPS dump信息 */
typedef struct tagDumpGps_S
{
    SV_BOOL     bException;     /* 是否出现异常 */
    SV_BOOL     bHadData;       /* 是否有gps数据 */
    double      dLatitude;      /* 纬度 */
    double      dLongitude;     /* 经度 */
    double      dDirection;     /* 路线 */
    double      dPDOP;          /* 精度因子 */
    double      dHDOP;          /* 水平精度因子 */
    double      dVDOP;          /* 垂直精度因子 */
    double      dElv;           /* 天线高度,相对地平线 */
    double      dSpk;           /* 速度(km/h) */
    sint32      s32CellModuleType;  /* 4G模块类型 */
    sint32      s32GPSSrc;      /* GPS数据源 */
    sint32      s32Status;      /* 状态 */
    sint32      s32Signal;      /* 信号 */
    sint32      s32Satinuse;    /* 使用的卫星数 */
    sint32      s32SatCount;    /* 可见卫星数 */
    sint32      s32SNR[DUMP_GPS_VIS_MAX_NUM];    /* 可见卫星数信噪比 */
} DUMP_GPS_S;

/* GPIO dump 信息 */
typedef struct tagDumpGPIO_S
{

} DUMP_GPIO_S;

/* DMM dump信息 */
typedef struct tagDumpDmm_S
{
    sint32          s32CalibrationState;    /* 标定状态 */
    sint32          s32CalibrationResult;   /* 标定结果 */
    ALARM_TYPE_E    enAlarmType;            /* 报警事件 */
    SV_BOOL         bImageMirror;           /* 是否开启镜像 */
    SV_BOOL         bDetectFace;            /* 是否检测到人脸 */
    SV_BOOL         bYawn;                  /* 是否打哈欠 */
    SV_BOOL         bNoMask;                /* 是否没戴口罩 */
    SV_BOOL         bShelter;               /* 是否遮挡 */
    uint8           u8SmokeScore[12];       /* 抽烟得分 */
    uint8           u8PhoneScore[12];       /* 打电话得分 */
    uint8           u8DrinkEatScore[12];    /* 喝东西得分 */
    uint8           u8SeatbeltScore[12];    /* 无安全带得分 */
    uint8           u8HelmetScore[12];      /* 无安全帽得分 */
    uint8           u8Ecr[2][12];           /* 眼睑闭合度 0 -- 左, 1 -- 右 */
    uint8           u8EyeScore[4][12];      /* 眼睛开合得分, 0~3分别是左眼睁开，左眼闭合，右眼睁开，右眼闭合 */
    uint8           u8GlassScore[2][12];    /* 普通眼镜和太阳眼镜得分 */
    uint8           u8HeadPose[3][12];      /* 头部姿态角度 */
    uint8           u8Gaze[2][12];          /* 视线角度 */
    uint8           u8UsrId[32];            /* 人脸识别用户编号 */
    uint8           u8UsrName[32];          /* 人脸识别用户名 */
} DUMP_DMM_S;

/* 扩展屏幕信息:CVBS */
typedef struct tagDumpExtscreen_S
{
    SV_BOOL bExist;                 /* 存在USB转CVBS扩展屏幕 */
    char    mode[32];               /* CVBS模式:PAL或者NTSC */
} DUMP_EXTSCREEN_S;

/* ShareFifo 媒体共享队列信息 */
typedef struct tagDumpShareFifo_S
{
    SV_BOOL bMainStream;            /* 使用主码流 */
    SV_BOOL bSubStream;             /* 使用子码流 */
} DUMP_SHAREFIFO_S;

/* ISP相关信息 */
typedef struct tagDumpISP_S
{
    char   iqPath[128];             /* IQ文件路径 */
} DUMP_ISP_S;

/* 变焦控制信息 */
typedef struct tagDumpZoom_S
{
    sint32      s32Times;           /* 放大倍数 */
    sint32      s32CurZoomPos;      /* 当前变焦位置 */
    sint32      s32Status;          /* 状态 0:未定义 1:正在标定 2:标定成功 3:标定失败*/
    sint32      s32Progress;        /* 进度 */
} DUMP_ZOOM_S;

/* APC dump 信息*/
typedef struct tagDumpApc_S
{
    sint32      s32TimeStamp;           /* 时间戳 */
    sint32      s32InNumber;            /* 上车人数 */
    sint32      s32OutNumber;           /* 下车人数 */
    sint32      s32RectNumber;          /* 探测区域内的人数 */
    sint32      s32CurAlarm;            /* 当前报警状态 */
    char        szReverse[48];          /* 预留字节，后续启用时会拆分 */
}__attribute__((packed)) DUMP_APC_S;


/* 画板信息 */
typedef struct tagRoiBoardCfg_S
{
    float           fGreenScale;            /* 绿色数量 */
    float           fYellowScale;           /* 黄色数量 */
    float           fRedScale;              /* 红色数量 */

    SV_POINT2_S     fGreenPoint[10];        /* 绿色点坐标 */
    SV_POINT2_S     fYellowPoint[10];       /* 黄色点坐标 */
    SV_POINT2_S     fRedPoint[10];          /* 黄色点坐标 */

    SV_BOOL         bGreenEnable;           /* 绿色区域使能 */
    SV_BOOL         bYellowEnable;          /* 绿色区域使能 */
    SV_BOOL         bRedEnable;             /* 绿色区域使能 */
} ROI_BOARD_CFG_S;



/*************************************************************************************************************************************/

/* crc相关公用函数 */
extern uint32 crc32_check(sint8 *buff, sint32 len);
extern uint8 crc8_check(uint8 *ptr, uint8 len);


/* 串口相关公用函数 */
extern sint32 serialOpen(const char *port, uint32 baud, SV_BOOL bNonBlock, sint32 *serialFd);
extern void serialClose(sint32 fd);
extern char **getSerialPorts(void);
extern uint32 serialReadRaw(sint32 fd, char *data, sint32 length);
extern uint32 serialWriteRaw(sint32 fd, const char *data, sint32 length);
extern void serialWaitUntilSent(sint32 fd);
extern sint32 serialHasChar(sint32 fd);
extern void serialReadChar(sint32 fd, char *c);
extern sint32 serialWriteChar(sint32 fd, char c);
extern void serialWriteString(sint32 fd, const char *s);
extern void serialFlush(sint32 fd);


#define     DUMP_BUF_SIZE       (10 * 1024)        /* dump信息文本内存最大字节 */
/* json文件处理相关函数 */
extern sint32 cJSON_GetJson(char *filePath, char *pszOutJson);
extern sint32 cJSON_GetInt(char *filePath, char *key, sint32 *s32Result);
extern sint32 cJSON_GetDouble(char *filePath, char *key, double *result);
extern sint32 cJSON_GetString(char *filePath, char *key, char *pszString);

/* dump文本信息获取 */
extern sint32 dump_GetWifiInfo(DUMP_WIFI_S **ppstDumpWifiInfo);
extern sint32 dump_GetRecorderInfo(DUMP_REC_S **ppstDumpRecInfo);
extern sint32 dump_GetStorageInfo(DUMP_STO_S **ppstDumpStoInfo);
extern sint32 dump_GetCellularInfo(DUMP_CELL_S **ppstDumpCellInfo);
extern sint32 dump_GetGPSInfo(DUMP_GPS_S **ppstDumpGPSInfo);
extern sint32 dump_GetGPIOInfo(DUMP_GPIO_S **ppstDumpGPIOInfo);
extern sint32 dump_GetDmmInfo(DUMP_DMM_S *pstDumpDmmInfo);

/* dump USB转CVBS信息导出和获取 */
extern sint32 dump_GetExtscreenInfo(DUMP_EXTSCREEN_S *pstDumpExtscreen);
extern sint32 dump_SetExtscreenInfo(DUMP_EXTSCREEN_S *pstDumpExtscreen);

/* dump sharefifo信息导出和获取 */
extern sint32 dump_GetShareFifoInfo(DUMP_SHAREFIFO_S *pstDumpShareFifo);
extern sint32 dump_SetShareFifoInfo(DUMP_SHAREFIFO_S *pstDumpShareFifo);

/* dump ISP信息导出和获取 */
extern sint32 dump_GetIspInfo(DUMP_ISP_S *pstDumpIsp);
extern sint32 dump_SetIspInfo(DUMP_ISP_S *pstDumpIsp);

/* dump 变焦信息导出和获取 */
extern sint32 dump_GetZoomInfo(DUMP_ZOOM_S *pstDumpZoom);
extern sint32 dump_SetZoomInfo(DUMP_ZOOM_S *pstDumpZoom);

/* dump APC信息导出和获取 */
extern sint32 dump_GetApcInfo(DUMP_APC_S * pstDumpApc);
extern sint32 dump_SetApcInfo(DUMP_APC_S * pstDumpApc);

/* g711编解码相关函数 */
extern sint32 PCM2G711A(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);
extern sint32 PCM2G711U(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);

extern sint32 G711A2PCM(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);
extern sint32 G711U2PCM(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);

/* 通用实用函数 */
extern sint64 COMMON_GetTimeTickMs();
extern void COMMON_CutLineBreak(char *pszStr);
extern SV_BOOL COMMON_IsAplaying();
extern SV_BOOL COMMON_IsPathExist(char *pszPath);
extern uint32 COMMON_UnicodeEncode(char *str);
extern sint32  COMMON_UTC2Local(struct tm* tm_utc, struct tm* result);
extern sint32 COMMON_ReadFile(const char* p_file, char* buf, sint32 size);
extern sint32 COMMON_GetFileDir(const char* szSrc, char* szDir);
extern sint64 COMMON_GetFileSize(const char *pFile);
extern sint32 COMMON_CutOutName(const char* pSrc, char* pResut);
extern uint32 COMMON_GetTimeTickSecond();
extern void COMMON_StartThread(void (*f)(void *), void *p);
extern SV_BOOL COMMON_IsNetCardExist(char *pszIfName);
extern uint32 COMMON_GetAdcValue(uint32 u32AdcChn);


/* ROI BOARD 相关参数 */
extern sint32 ROI_BOARD_Init(ROI_BOARD_CFG_S *pstRoiBoardCfg);
extern sint32 ROI_BOARD_Reset(ROI_BOARD_CFG_S *pstRoiBoardCfg);
extern sint32 ROI_BOARD_Detect(SV_POINT2_S stRectL, SV_POINT2_S stRectR, sint32 *ps32MaskTmp);

/* g711编解码相关函数 */
extern sint32 PCM2G711A(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);
extern sint32 PCM2G711U(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);

extern sint32 G711A2PCM(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);
extern sint32 G711U2PCM(uint8 *pau8InData, uint8 *pau8OutData, sint32 s32DataLen, sint32 s32Reserve);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif // _UTILS_COM_H
