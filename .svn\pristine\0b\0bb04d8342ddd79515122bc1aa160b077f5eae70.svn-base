/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file:       r_info.h
author:     lyn
version:    1.0.0
date:       2023-12-18
function:   recorder info object header file
notice:     none
*******************************************************************************/
#ifndef _R_INFO_H_
#define _R_INFO_H_
#include <stdio.h>
#include <stdlib.h>
#include <list>
#include <iostream>
#include <string>

#include "common.h"
#include "r_obj.h"

namespace recorder{

class RInfo : public RObject
{
private:
public:
    REC_ALARM_CONF_S                 stAlarmParam;          /* 新的报警录像参数配置 */
    REC_DMM_EYELID_S                 stEyelidData;          /* 新的眼部数据信息 */
    std::list<REC_DMM_EYELID_S>      listEyelid;
    mutex                            aMutex;                /* 互斥锁 */
    SV_BOOL                          isNewInfo;             /* 创建新的图片 */
    SV_BOOL                          isNewData;             /* 创建新的数据 */
    pthread_t                        threadWriteData;       /* 线程号 */
    virtual sint32 Start();
    virtual sint32 Stop();
    sint32 newInfo(REC_ALARM_CONF_S alarmParam);            /* 创建新的 */
    sint32 newData(REC_DMM_EYELID_S eyelidData);            /* 创建新的眼部信息数据 */
    REC_STATUS_E GetRecStatus();                            /* 获取状态 */

    /* 信号和槽函数 */
    void PosChangeSlot(REC_POS_E lpos, sint32 subIdx);      /* 录像位置变更 */
    void AlarmSlot(REC_ALARM_CONF_S stAlarmConf);
    void WriteDataSlot(REC_DMM_EYELID_S stEyelidConf);
    void ConfigSysSlot(REC_CONF_S stRecConf);
    RInfo(sint32 lchn, REC_OBJ_E lenObj);
    ~RInfo();

};
}

#endif/* _R_INFO_H_ */