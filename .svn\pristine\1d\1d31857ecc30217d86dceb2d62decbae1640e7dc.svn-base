#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>

#include "board.h"
#include "common.h"
#include "print.h"
#include "safefunc.h"
#include "mpp_com.h"
#include "mpp_sys.h"
#include "mpp_vi.h"
#include "mpp_vpss.h"
#include "mpp_venc.h"
#include "mpp_vmix.h"
#include "mpp_vo.h"
#include "mpp_font.h"
#include "mpp_vosd.h"
#include "mpp_aio.h"
#include "mpp_aenc.h"
#include "media.h"
#include "msg.h"
#include "mpp_ctrl.h"
#if (BOARD == BOARD_ADA32IR)
#include "mpp_ir.h"
#endif


/* 主码流,子码流,图片流,V<PERSON>通道,音频等通道重建函数回调 */
typedef sint32 (*MPP_CTRL_FUNC_RECREATE)(MEDIA_RESET_S *);

typedef struct tagMppCtlCallback_S
{
    MPP_CTRL_FUNC_RECREATE          pPriCallback;           /* 主码流重建函数 */
    MPP_CTRL_FUNC_RECREATE          pSecCallback;           /* 子码流重建函数 */
    MPP_CTRL_FUNC_RECREATE          pJpegCallback;          /* 图片码流重建函数 */
    MPP_CTRL_FUNC_RECREATE          pVoCallback;            /* VO通道重建函数 */
    MPP_CTRL_FUNC_RECREATE          pExtscreenCallback;     /* CVBS扩展通道重建函数 */
    MPP_CTRL_FUNC_RECREATE          pVosdCallback;          /* VOSD重新配置函数 */
    MPP_CTRL_FUNC_RECREATE          pAiAoAencCallback;      /* AI,AO,AENC更新配置函数 */
    MPP_CTRL_FUNC_RECREATE          pIrCallback;            /* ADA32IR专用，红外机芯通道更新配置参数 */
} MPP_CTRL_CALLBACK_S;

typedef struct tagMppCtrlInfo_S
{
    MEDIA_INIT_S            stRealParam;   /* 实时变动的画面参数 */
    MPP_CTRL_CALLBACK_S     *pstCall;      /* 码流重建通道函数 */

} MPP_CTRL_INFO_S;

MPP_CTRL_INFO_S m_stCtrlInfo;

static sint32 mpp_ctrl_pri_vi_venc_recreate(MEDIA_RESET_S *pstResetParam);
static sint32 mpp_ctrl_sec_vpss_venc_recreate(MEDIA_RESET_S *pstResetParam);
static sint32 mpp_ctrl_jpeg_vpss_venc_recreate(MEDIA_RESET_S *pstResetParam);
static sint32 mpp_ctrl_vo_recreate(MEDIA_RESET_S *pstResetParam);
static sint32 mpp_ctrl_vosd_recreate(MEDIA_RESET_S *pstResetParam);
static sint32 mpp_ctrl_aiaoaenc_recreate(MEDIA_RESET_S *pstResetParam);
static sint32 mpp_ctrl_aiao_param(MEDIA_RESET_S *pstResetParam);
static sint32 mpp_ctrl_ir_recreate(MEDIA_RESET_S *pstResetParam);

static const MPP_CTRL_CALLBACK_S m_stCtrl_A32N_Callback = {
    .pPriCallback       = mpp_ctrl_pri_vi_venc_recreate,
    .pSecCallback       = mpp_ctrl_sec_vpss_venc_recreate,
    .pJpegCallback      = mpp_ctrl_jpeg_vpss_venc_recreate,
    .pVoCallback        = mpp_ctrl_vo_recreate,
    .pExtscreenCallback = NULL,
    .pVosdCallback      = mpp_ctrl_vosd_recreate,
    .pAiAoAencCallback  = mpp_ctrl_aiaoaenc_recreate,
};

static const MPP_CTRL_CALLBACK_S m_stCtrl_A900V1_Callback = {
    .pPriCallback       = mpp_ctrl_pri_vi_venc_recreate,
    .pSecCallback       = mpp_ctrl_sec_vpss_venc_recreate,
    .pJpegCallback      = mpp_ctrl_jpeg_vpss_venc_recreate,
    .pVoCallback        = mpp_ctrl_vo_recreate,
    .pExtscreenCallback = NULL,
    .pVosdCallback      = mpp_ctrl_vosd_recreate,
    .pAiAoAencCallback  = mpp_ctrl_aiaoaenc_recreate,
};

static const MPP_CTRL_CALLBACK_S m_stCtrl_A47V1_Callback = {
    .pPriCallback       = mpp_ctrl_pri_vi_venc_recreate,
    .pSecCallback       = mpp_ctrl_sec_vpss_venc_recreate,
    .pJpegCallback      = mpp_ctrl_jpeg_vpss_venc_recreate,
    .pVoCallback        = mpp_ctrl_vo_recreate,
    .pExtscreenCallback = NULL,
    .pVosdCallback      = mpp_ctrl_vosd_recreate,
    .pAiAoAencCallback  = mpp_ctrl_aiaoaenc_recreate,//mpp_ctrl_aiao_param,
};

static const MPP_CTRL_CALLBACK_S m_stCtrl_A32IR_Callback = {
    .pPriCallback       = mpp_ctrl_pri_vi_venc_recreate,
    .pSecCallback       = mpp_ctrl_sec_vpss_venc_recreate,
    .pJpegCallback      = mpp_ctrl_jpeg_vpss_venc_recreate,
    .pVoCallback        = mpp_ctrl_vo_recreate,
    .pExtscreenCallback = NULL,
    .pVosdCallback      = mpp_ctrl_vosd_recreate,
    .pAiAoAencCallback  = mpp_ctrl_aiaoaenc_recreate,//mpp_ctrl_aiao_param,
    .pIrCallback        = mpp_ctrl_ir_recreate,
};

#if (BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 || BOARD == BOARD_ADA32C4)
static const MPP_CTRL_CALLBACK_S *pstCtrl_Default_Callback = &m_stCtrl_A32N_Callback;
#elif (BOARD == BOARD_ADA900V1)
static const MPP_CTRL_CALLBACK_S *pstCtrl_Default_Callback = &m_stCtrl_A900V1_Callback;
#elif (BOARD == BOARD_ADA47V1)
static const MPP_CTRL_CALLBACK_S *pstCtrl_Default_Callback = &m_stCtrl_A47V1_Callback;
#elif (BOARD == BOARD_ADA32IR)
static const MPP_CTRL_CALLBACK_S *pstCtrl_Default_Callback = &m_stCtrl_A32IR_Callback;
#endif


sint32 mpp_ctrl_sys_Init(MEDIA_INIT_S * pstInitParam)
{
    sint32 s32Ret = 0, i, s32Chn = 0;
    MPP_VB_CONF_S   stVbConf   = {0};
    MPP_VI_CONF_S   stViConf   = {0};
    MPP_VPSS_CONF_S stVpssConf = {0};
    MPP_VENC_CONF_S stVencConf = {0};
    MPP_VO_CONF_S   stVoConf   = {0};
    MPP_FONT_CONF_S stFontConf = {0};
    MPP_VOSD_CONF_S stVosdConf = {0};
    MPP_AIO_CONF_S  stAiConf   = {0};
    MPP_AO_CONF_S   stAoConf   = {0};
    MPP_AENC_CONF_S stAencConf = {0};
	MEDIA_IMAGE_S 	stCustomImage = {0};
    if (NULL == pstInitParam)
    {   
        return ERR_NULL_PTR;
    }

    print_level(SV_INFO, "DevType:%d, Sensor:%d, ADC:%d, VideoMode:%d, WDRMode:%d, ChnNum:%d, AencType:%d, AudioSam:%d.\n", \
                pstInitParam->stTypeMode.enDevType, pstInitParam->stTypeMode.enSenChipType, pstInitParam->stTypeMode.enAdChipType, \
                pstInitParam->stTypeMode.enVideoMode, pstInitParam->stTypeMode.enSysWDRMode, pstInitParam->u32ChnNum, \
                pstInitParam->enAencType, pstInitParam->enAudioSampleRate);

    if (pstInitParam->stTypeMode.enDevType >= DEV_TYPE_BUTT || 
        pstInitParam->stTypeMode.enSenChipType >= SEN_TYPE_BUTT || 
        pstInitParam->stTypeMode.enAdChipType >= AD_TYPE_BUTT || 
        pstInitParam->stTypeMode.enVideoMode >= VIDEO_MODE_BUTTE ||
        pstInitParam->stTypeMode.enSysWDRMode >= WDR_MODE_BUTTT ||
        pstInitParam->u32ChnNum > VIODE_MAX_CHN ||
        pstInitParam->enAencType >= AUDIO_ENCODE_BUTT ||
        pstInitParam->enAudioSampleRate >= AUD_SR_BUTT)
    {
        print_level(SV_ERROR, "Invalid Param!\n");
        return ERR_ILLEGAL_PARAM;
    }

    if (strlen(pstInitParam->szChnName) > VOSD_MAX_CHNNAME_LEN)
    {
        print_level(SV_ERROR, "ChnName exceed max len: %d\n", VOSD_MAX_CHNNAME_LEN);
        return ERR_ILLEGAL_PARAM;
    }

	MEDIA_VI_GetCustomImage(&stCustomImage);

    s32Ret = mpp_sys_Init(&stVbConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA47V1)
    /* 初始化红外灯状态 */
    mpp_sys_UpdateLED_Status(pstInitParam->u32LedBright);
#endif

    mpp_sys_UpdateIRCUT_Status(pstInitParam->u32IRcutMode);  

    /* 初始化VI模块 */
    s32Ret = mpp_vi_SetParam(pstInitParam, &stViConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetParam failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vi_Init(&stViConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* VI层面配置画面镜像翻转 */
    for (i = 0; i < stViConf.u32ChnNum; i++)
    {
        s32Ret = mpp_vi_SetChnMirrorFlip(s32Chn, pstInitParam->bImageMirror, pstInitParam->bImageFlip);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }



#if (BOARD == BOARD_ADA47V1)

	s32Ret = MEDIA_VI_ImageParamSet(stCustomImage.u8Brightness, stCustomImage.u8Contrast, stCustomImage.u8Saturation, stCustomImage.u8Sharpness);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MEDIA_VI_ImageParamSet failed. [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	s32Ret = mpp_vi_SetExposure(pstInitParam->enExposureTime,pstInitParam->u32GainAdjustment);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetExposure failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

	if (BOARD_GetVersion() == BOARD_ADA47V1_V1) //定焦摄像头不支持
	{	
		s32Ret = mpp_vi_SetBackLight(pstInitParam->u8BLCLevel,pstInitParam->enBLCEnable);
		if (SV_SUCCESS != s32Ret)
	    {
	        print_level(SV_ERROR, "mpp_vi_SET_BackLight failed! [err=%#x]\n", s32Ret);
	        return s32Ret;
	    }

		s32Ret = mpp_vi_SetLightInhibition(pstInitParam->bSLSEnable,pstInitParam->u8SLSLevel,100);//暗区提升默认值100
		if (SV_SUCCESS != s32Ret)
	    {
	        print_level(SV_ERROR, "mpp_vi_SetLightInhibition failed! [err=%#x]\n", s32Ret);
	        return s32Ret;
	    }
	}
	
	s32Ret = mpp_vi_SetHDRMode(pstInitParam->enHDRMode,pstInitParam->u8HDRLevel);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "mpp_vi_SetHDRMode failed! [err=%#x]\n", s32Ret);
		return s32Ret;
	}

	s32Ret = mpp_vi_SetWhiteBalance(pstInitParam->enWhiteBalance,pstInitParam->u8RGain,pstInitParam->u8GGain,pstInitParam->u8BGain);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetWhiteBalance failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

	s32Ret = mpp_vi_SetDNR(pstInitParam->enDNREnable,pstInitParam->u16DNRLevel & 0xFF,(pstInitParam->u16DNRLevel >> 8) & 0xFF);//低8位为空域 高8位为时域
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "mpp_vi_SetDNR failed! [err=%#x]\n", s32Ret);
		return s32Ret;
	}

	s32Ret = mpp_vi_SetDefog(pstInitParam->enDefogMode,pstInitParam->u8DefogLevel);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "mpp_vi_SetDefog failed! [err=%#x]\n", s32Ret);
		return s32Ret;
	}

	s32Ret = mpp_vi_SetImgEnhance(pstInitParam->bImgEnhance);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetImgEnhance failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

	s32Ret = mpp_vi_SetFocusMode(pstInitParam->enFocusControl);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetFocusMode failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    
end_isp:
#endif

    /* VPSS相关参数配置 */
    stVpssConf.enVideoMode = pstInitParam->stTypeMode.enVideoMode;
    stVpssConf.u32ChnNum = pstInitParam->u32ChnNum;
    stVpssConf.enRotateAngle = pstInitParam->enRotateAngle;
    stVpssConf.u32MaxH = pstInitParam->stSrcSize.u32Height;
    stVpssConf.u32MaxW = pstInitParam->stSrcSize.u32Width;

    print_level(SV_DEBUG, "srcW = %d, srcH = %d.\n", pstInitParam->stSrcSize.u32Width, pstInitParam->stSrcSize.u32Height);
    stVpssConf.stPriVencSize.u32Width = pstInitParam->stPriVencAttr.u32PicWidth;
    stVpssConf.stPriVencSize.u32Height = pstInitParam->stPriVencAttr.u32PicHeight;
    stVpssConf.stSubVencSize.u32Width = pstInitParam->stSecVencAttr.u32PicWidth;
    stVpssConf.stSubVencSize.u32Height = pstInitParam->stSecVencAttr.u32PicHeight;
    stVpssConf.stJpegVencSize.u32Width = pstInitParam->stJpegVencAttr.u32PicWidth;
    stVpssConf.stJpegVencSize.u32Height = pstInitParam->stJpegVencAttr.u32PicHeight;
    stVpssConf.stExtscreenSize.u32Width = pstInitParam->stExtScreenAttr.u32OutWidth;
    stVpssConf.stExtscreenSize.u32Height = pstInitParam->stExtScreenAttr.u32OutHeight;

    for(i = 0; i < pstInitParam->u32ChnNum; i ++)
    {
        stVpssConf.enAlgType[i] = pstInitParam->stExtern[i].enAlgType;
    }

    stVpssConf.pfYuvCallback = pstInitParam->pfYuvCallback;
    stVpssConf.pfRgbCallback = pstInitParam->pfRgbCallback;
    stVpssConf.pfBufCallback = pstInitParam->pfBufCallback;
    s32Ret = mpp_vpss_Init(&stVpssConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA32IR)
    MPP_IR_CONF_S stIRConfig = {0};
    stIRConfig.bImageMirror = pstInitParam->stIrMediaCfg.bImageMirror;
    stIRConfig.bImageFlip = pstInitParam->stIrMediaCfg.bImageFlip;
    stIRConfig.s32IrShutterInr = pstInitParam->stIrMediaCfg.s32IrShutterInr;
    stIRConfig.stSrcVideoSize.u32Width = 1920;
    stIRConfig.stSrcVideoSize.u32Height = 1080;
    stIRConfig.u32ViFrmRate = pstInitParam->stPriVencAttr.u32ViFrmRate;
    stIRConfig.u32Bitrate = pstInitParam->stPriVencAttr.u32Bitrate;
    stIRConfig.enVoSplitMode = pstInitParam->enVoSplitMode;
    stIRConfig.pfStatCallback = pstInitParam->pfStatCallback;
    stIRConfig.pfRgbCallback = pstInitParam->pfRgbCallback;
    stIRConfig.s32ExtscreenWidth = pstInitParam->stExtScreenAttr.u32OutWidth;
    stIRConfig.s32ExtscreenHeight = pstInitParam->stExtScreenAttr.u32OutHeight;
    s32Ret = mpp_ir_Init(&stIRConfig);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    stVoConf.u32ChnNum = pstInitParam->u32ChnNum;
#if (BOARD == BOARD_ADA32IR)
    stVoConf.u32ChnNum = pstInitParam->u32ChnNum + 1;
#endif
    stVoConf.stInSize.u32Width   = 1920;
    stVoConf.stInSize.u32Height  = 1080;
    stVoConf.stOutSize.u32Width  = pstInitParam->stVoAttr.u32OutWidth;
    stVoConf.stOutSize.u32Height = pstInitParam->stVoAttr.u32OutHeight;
    stVoConf.u32Fps              = pstInitParam->stVoAttr.u32FrmRate;
    stVoConf.enRotateAngle       = pstInitParam->enRotateAngle;
#if USING_MAINSTREAM_ALPHA
    if (BOARD_GetVersion() != BOARD_ADA47V1_V3)
    {
        stVoConf.bOsdEnable      = pstInitParam->stOverlay.stGui.bShowChn[MPP_VPSS_CHN_PRI];
    }    
#else
    stVoConf.bOsdEnable          = pstInitParam->stOverlay.stGui.bShowChn[MPP_VPSS_CHN_VO];
#endif
    for(i = 0; i < MEDIA_CHNNUM; i++)
    {
        memcpy(&stVoConf.stAlgExtern[i], &pstInitParam->stExtern[i], sizeof(VALG_EXTERN_S));
    }
    stVoConf.enSplitMode = pstInitParam->enSplitMode;
    s32Ret = mpp_vo_Init(&stVoConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 初始化VENC模块 */
    stVencConf.u32ChnNum = pstInitParam->u32ChnNum;
    stVencConf.enVideoMode = pstInitParam->stTypeMode.enVideoMode;
    stVencConf.enSysWDRMode = pstInitParam->stTypeMode.enSysWDRMode;
    stVencConf.stPriVencAttr = pstInitParam->stPriVencAttr;
    stVencConf.stSecVencAttr = pstInitParam->stSecVencAttr;
    stVencConf.stJpegVencAttr = pstInitParam->stJpegVencAttr;
    stVencConf.pfDataCallback = pstInitParam->pfDataCallback;

    s32Ret = mpp_venc_Init(&stVencConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 初始化字体模块 */
    strcpy(stFontConf.szFontFile, MEDIA_FONT_FILE_PATH);
    s32Ret = mpp_font_Init(&stFontConf);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_font_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 初始化OSD模块 */
    stVosdConf.bKeyAuth = pstInitParam->bKeyAuth;
    stVosdConf.u32ChnNum = pstInitParam->u32ChnNum;
    stVosdConf.stOverlay.bShowTime = pstInitParam->stOverlay.bShowTime;
    stVosdConf.stOverlay.bShowChnName = pstInitParam->stOverlay.bShowChnName;
    stVosdConf.stOverlay.bShowCamera = pstInitParam->stOverlay.bShowCamera;
    stVosdConf.stOverlay.enTimeFmt = pstInitParam->stOverlay.enTimeFmt;
    stVosdConf.stOverlay.stGui.bShowChn[0] = pstInitParam->stOverlay.stGui.bShowChn[0];
    stVosdConf.stOverlay.stGui.bShowChn[1] = pstInitParam->stOverlay.stGui.bShowChn[1];
    stVosdConf.stOverlay.stGui.bShowChn[2] = pstInitParam->stOverlay.stGui.bShowChn[2];
    stVosdConf.stOverlay.stGui.bShowChn[3] = pstInitParam->stOverlay.stGui.bShowChn[3];
    stVosdConf.stOverlay.enlang = pstInitParam->stOverlay.enlang;
    strcpy(stVosdConf.szChnName, pstInitParam->szChnName);
    strcpy(stVosdConf.szWifiName, pstInitParam->szWifiName);
    strcpy(stVosdConf.szHardwareVer, pstInitParam->szHardwareVer);
    strcpy(stVosdConf.szFirmwareVer, pstInitParam->szFirmwareVer);
    strcpy(stVosdConf.szPdsCanid, pstInitParam->szPdsCanid);
    sprintf(stVosdConf.szVoFormat, "%s%d", (pstInitParam->stVoAttr.u32OutHeight == 720 ? "720P" : "1080P"), pstInitParam->stVoAttr.u32FrmRate);
    sprintf(stVosdConf.szExternScreenFormat, "%s", pstInitParam->stExtScreenAttr.u32OutHeight == 480 ? "NTSC" : "PAL");
    stVosdConf.bYuvProtocol = pstInitParam->bYuvProtocol;
    memcpy(stVosdConf.stAlgExtern, pstInitParam->stExtern, sizeof(pstInitParam->stExtern));
    stVosdConf.enRotationAngle = pstInitParam->enRotateAngle;
    stVosdConf.stOverlay.bShowRoIcon = pstInitParam->stOverlay.bShowRoIcon;
    stVosdConf.stOverlay.bShowAlarmIcon = pstInitParam->stOverlay.bShowAlarmIcon;
    
    s32Ret = mpp_vosd_Init(&stVosdConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if MPP_AUDIO_AI_ENABLE
    /* 初始化AI模块 */
    stAiConf.u32ChnNum = pstInitParam->u32ChnNum;
    stAiConf.enAioMode = MPP_AIO_MODE_I2S_SLAVE;
    stAiConf.enSampleRate = pstInitParam->enAudioSampleRate;
    stAiConf.enBitWidth = MPP_AUDIO_BIT_WIDTH_16;
    stAiConf.u32Volume = pstInitParam->u32Volume;
    stAiConf.bAudioEnable = pstInitParam->bAudioEnable;
    stAiConf.bAudioMicEnable = pstInitParam->bAudioMicEnable;
    stAiConf.bAudioAlarmEnable = pstInitParam->bAudioAlarmEnable;
    stAiConf.enAencType = pstInitParam->enAencType;
    stAiConf.pfDataCallback = pstInitParam->pfDataCallback;
    s32Ret = mpp_ai_Init(&stAiConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ai_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 初始化AENC模块 */
    stAencConf.u32ChnNum = pstInitParam->u32ChnNum;
    stAencConf.enAencType = pstInitParam->enAencType;
    stAencConf.enAudioSampleRate = pstInitParam->enAudioSampleRate;
    stAencConf.pfDataCallback = pstInitParam->pfDataCallback;
    stAencConf.bAudioMicEnable = pstInitParam->bAudioMicEnable;
    s32Ret = mpp_aenc_Init(&stAencConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif


#if MPP_AUDIO_AO_ENABLE
    if (BOARD_IsNotCustomer(BOARD_C_ADA47V1_CZAEX))
    {
        /* 初始化AO模块 */
        stAoConf.u32Volume = pstInitParam->u32OutputVolume;
        stAoConf.bAudioMicEnable = pstInitParam->bAudioMicEnable;
        stAoConf.enSampleRate = pstInitParam->enAudioSampleRate;
        s32Ret = mpp_ao_Init(&stAoConf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_ao_Init failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif


    m_stCtrlInfo.stRealParam = *pstInitParam;
    m_stCtrlInfo.pstCall = pstCtrl_Default_Callback;

    print_level(SV_DEBUG, "Media Init Success!\n");
    return SV_SUCCESS;
}


sint32 mpp_ctrl_sys_Fini()
{
    sint32 s32Ret = 0;

#if MPP_AUDIO_AO_ENABLE
    s32Ret = mpp_ao_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_ao_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

#if MPP_AUDIO_AI_ENABLE
    s32Ret = mpp_aenc_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_aenc_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_ai_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_ai_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_vo_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_vo_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vosd_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_vosd_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_venc_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vi_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_vi_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_sys_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_sys_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
 
    return SV_SUCCESS;

}

sint32 mpp_ctrl_sys_Start()
{
    sint32 s32Ret;
    
    s32Ret = mpp_sys_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vosd_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif/* BOARD_ADA32IR */

    s32Ret = mpp_vpss_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vo_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vi_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if MPP_AUDIO_AI_ENABLE
    s32Ret = mpp_ai_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ai_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_aenc_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}



sint32 mpp_ctrl_sys_Stop()
{
    sint32 s32Ret;

#if MPP_AUDIO_AI_ENABLE
    s32Ret = mpp_aenc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_aenc_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_ai_Stop();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ai_Stop failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_vosd_Stop();
    if (0 != s32Ret)
    {
        print_level(SV_WARN, "mpp_vosd_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vo_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vi_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vpss_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_venc_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_sys_Stop();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 mpp_ctrl_getParam(MEDIA_RESET_S *pstResetParam)
{
    if (NULL == pstResetParam)
    {   
        return ERR_NULL_PTR;
    }

    memcpy(pstResetParam, &m_stCtrlInfo.stRealParam, sizeof(MEDIA_RESET_S));
    return SV_SUCCESS;
}



sint32 mpp_ctrl_setParam(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret;
    sint32 s32Chn = 0;

    if (STRUCT_EQUAL(*pstResetParam, m_stCtrlInfo.stRealParam))
    {
        return SV_SUCCESS;
    }

    mpp_sys_UpdateIRCUT_Status(pstResetParam->u32IRcutMode);
#if (BOARD == BOARD_ADA47V1)
    mpp_sys_UpdateLED_Status(pstResetParam->u32LedBright);
#endif  

    s32Ret = mpp_vi_SetFps(pstResetParam->stVoAttr.u32FrmRate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_SYS_SetViFramerate failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_vi_SetChnMirrorFlip(s32Chn, pstResetParam->bImageMirror, pstResetParam->bImageFlip);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA47V1)
   

	s32Ret = mpp_vi_SetExposure(pstResetParam->enExposureTime,pstResetParam->u32GainAdjustment);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "mpp_vi_SetExposure failed. [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	if (BOARD_GetVersion() == BOARD_ADA47V1_V1)
	{
		s32Ret = mpp_vi_SetBackLight(pstResetParam->u8BLCLevel,pstResetParam->enBLCEnable);
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "mpp_vi_SetBackLight failed! [err=%#x]\n", s32Ret);
			return s32Ret;
		}

		s32Ret = mpp_vi_SetLightInhibition(pstResetParam->bSLSEnable,pstResetParam->u8SLSLevel,100);
		if (SV_SUCCESS != s32Ret)
	    {
	        print_level(SV_ERROR, "mpp_vi_SetLightInhibition failed! [err=%#x]\n", s32Ret);
	        return s32Ret;
	    }
	}

	s32Ret = mpp_vi_SetHDRMode(pstResetParam->enHDRMode,pstResetParam->u8HDRLevel);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetHDRMode failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
	
	s32Ret = mpp_vi_SetWhiteBalance(pstResetParam->enWhiteBalance,pstResetParam->u8RGain,pstResetParam->u8GGain,pstResetParam->u8BGain);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetWhiteBalance failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
	
	s32Ret = mpp_vi_SetDNR(pstResetParam->enDNREnable,pstResetParam->u16DNRLevel & 0xFF,(pstResetParam->u16DNRLevel >> 8) & 0xFF);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "mpp_vi_SetDNR failed! [err=%#x]\n", s32Ret);
		return s32Ret;
	}

	s32Ret = mpp_vi_SetDefog(pstResetParam->enDefogMode,pstResetParam->u8DefogLevel);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "mpp_vi_SetDefog failed! [err=%#x]\n", s32Ret);
		return s32Ret;
	}

	s32Ret = mpp_vi_SetImgEnhance(pstResetParam->bImgEnhance);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetImgEnhance failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

	s32Ret = mpp_vi_SetFocusMode(pstResetParam->enFocusControl);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetFocusMode failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    
end_isp:
#endif

    s32Ret = mpp_venc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Stop failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    sleep_ms(30);

    s32Ret = mpp_vpss_Interrupt(SV_TRUE);
    print_level(SV_INFO, "++mpp_vpss_Interrupt\n");
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Stop failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    sleep_ms(30);

    s32Ret = mpp_vi_Interrupt(SV_TRUE);
    print_level(SV_INFO, "++mpp_vi_Interrupt\n");
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_Interrupt failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (m_stCtrlInfo.pstCall->pPriCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pPriCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pPriCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    if (m_stCtrlInfo.pstCall->pSecCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pSecCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pPriCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    if (m_stCtrlInfo.pstCall->pJpegCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pJpegCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pPriCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    if (m_stCtrlInfo.pstCall->pVoCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pVoCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pPriCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    if (m_stCtrlInfo.pstCall->pExtscreenCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pExtscreenCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pPriCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    if (m_stCtrlInfo.pstCall->pVosdCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pVosdCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pVosdCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    if (m_stCtrlInfo.pstCall->pAiAoAencCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pAiAoAencCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pAiAoAencCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

#if (BOARD == BOARD_ADA32IR)
    if (m_stCtrlInfo.pstCall->pIrCallback != NULL)
    {
        s32Ret = m_stCtrlInfo.pstCall->pIrCallback(pstResetParam);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "m_stCtrlInfo.pstCall->pAiAoAencCallback failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif

    s32Ret = mpp_vi_Interrupt(SV_FALSE);
    print_level(SV_INFO, "--mpp_vi_Interrupt\n");
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_Interrupt failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_vpss_Interrupt(SV_FALSE);
    print_level(SV_INFO, "--mpp_vpss_Interrupt\n");
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Interrupt failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_venc_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Start failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    memcpy(&m_stCtrlInfo.stRealParam, pstResetParam, sizeof(MEDIA_RESET_S));
    return SV_SUCCESS;
}


static sint32 mpp_ctrl_pri_vi_venc_recreate(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret;
    sint32 s32Chn = 0;
    STREAM_TYPE_E enStreamType = STREAM_TYPE_PRI;
    VIDEO_ENCODE_H264_S *pstPriVencAttr = &pstResetParam->stPriVencAttr;

    if (STRUCT_EQUAL(*pstPriVencAttr, m_stCtrlInfo.stRealParam.stPriVencAttr))
    {
        return SV_SUCCESS;
    }

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_venc_H264DestroyChn(enStreamType, s32Chn);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_vi_RecreateChn(s32Chn, pstPriVencAttr->u32PicWidth, pstPriVencAttr->u32PicHeight);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vi_RecreateChn failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (pstPriVencAttr->enEncode == VIDEO_ENCODE_MJPEG)
    {
        s32Ret = mpp_venc_MJpegCreateChn(enStreamType, s32Chn, pstPriVencAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_MJpegCreateChn PriChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }
    }
    else
    {
        s32Ret = mpp_venc_H264CreateChn(enStreamType, s32Chn, pstPriVencAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    s32Ret = mpp_venc_EnableChn(enStreamType, s32Chn);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_vo_Primary_Update();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vo_Primary_Update failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

static sint32 mpp_ctrl_sec_vpss_venc_recreate(MEDIA_RESET_S *pstResetParam)
{

    sint32 s32Ret = 0;
    sint32 s32Chn = 0;
    STREAM_TYPE_E enStreamType = STREAM_TYPE_SEC;
    VIDEO_ENCODE_H264_S *pstSecVencAttr = &pstResetParam->stSecVencAttr;

    if (STRUCT_EQUAL(*pstSecVencAttr, m_stCtrlInfo.stRealParam.stSecVencAttr))
    {
        return SV_SUCCESS;
    }

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_H264DestroyChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }


    SV_SIZE_S stSize = {pstSecVencAttr->u32PicWidth, pstSecVencAttr->u32PicHeight};
    s32Ret = mpp_vpss_ReCreateChannel(s32Chn, enStreamType, stSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ReCreateChannel failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    if (pstSecVencAttr->enEncode == VIDEO_ENCODE_MJPEG)
    {
        s32Ret = mpp_venc_MJpegCreateChn(enStreamType, s32Chn, pstSecVencAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_MJpegCreateChn PriChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }
    }
    else
    {
        s32Ret = mpp_venc_H264CreateChn(enStreamType, s32Chn, pstSecVencAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    s32Ret = mpp_venc_EnableChn(enStreamType, s32Chn);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}


static sint32 mpp_ctrl_jpeg_vpss_venc_recreate(MEDIA_RESET_S *pstResetParam)
{

    sint32 s32Ret = 0;
    sint32 s32Chn = 0;
    STREAM_TYPE_E enStreamType = STREAM_TYPE_SNAP0;
    VIDEO_ENCODE_JPEG_S *pstJpegVencAttr = &pstResetParam->stJpegVencAttr;
    
    if (STRUCT_EQUAL(*pstJpegVencAttr, m_stCtrlInfo.stRealParam.stJpegVencAttr))
    {
        return SV_SUCCESS;
    }

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_JpegDestroyChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    SV_SIZE_S stSize = {pstJpegVencAttr->u32PicWidth, pstJpegVencAttr->u32PicHeight};
    s32Ret = mpp_vpss_ReCreateChannel(s32Chn, enStreamType, stSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ReCreateChannel failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }


    s32Ret = mpp_venc_JpegCreateChn(enStreamType, s32Chn, pstJpegVencAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }


    s32Ret = mpp_venc_EnableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}


static sint32 mpp_ctrl_vo_recreate(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret;

    VIDEO_VO_S *pstVoAttr = &pstResetParam->stVoAttr;
    VALG_EXTERN_S (*pstExtern)[MEDIA_MAX_CHN] = pstResetParam->stExtern;
    
    if (STRUCT_EQUAL(*pstVoAttr, m_stCtrlInfo.stRealParam.stVoAttr))
    {
        goto skip_VoAttr;
    }

    s32Ret = mpp_vo_ReCreateChannel(pstVoAttr);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vo_ReCreateChannel failed! [err=%d]!\n", s32Ret);
        return s32Ret;
    }
skip_VoAttr:

    if (STRUCT_EQUAL(*pstExtern, m_stCtrlInfo.stRealParam.stExtern))
    {
        goto skip_Extern;
    }

    s32Ret = mpp_vo_SetPrimaryStr(pstExtern);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vo_SetPrimaryStr failed! [err=%d]!\n", s32Ret);
        return s32Ret;
    }

skip_Extern:
    return SV_SUCCESS;
}


static sint32 mpp_ctrl_vosd_recreate(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret, i;
    sint32 s32Chn = 0;

    VOSD_OVERLAY_S *pstOverlayAttr = &pstResetParam->stOverlay;
    VALG_EXTERN_S (*pstExtern)[MEDIA_MAX_CHN] = pstResetParam->stExtern;

    if (STRUCT_EQUAL(*pstOverlayAttr, m_stCtrlInfo.stRealParam.stOverlay))
    {
        goto skip_Overlay;
    }
    
    for(i = 0; i < 4; i++)
    {
        s32Ret = mpp_vosd_ChnOverlayAttrSet(s32Chn, i, pstOverlayAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MEDIA_OSD_OverlayGet failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        s32Ret = mpp_vosd_RoIconAttrSet(i, pstOverlayAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MEDIA_OSD_RoIconAttrSet failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }

skip_Overlay:
    s32Ret = mpp_vosd_SetChnName(s32Chn, pstResetParam->szChnName);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnName failed. [err=%#x]\n", s32Ret);
    }


    if (STRUCT_EQUAL(*pstExtern, m_stCtrlInfo.stRealParam.stExtern))
    {
        goto skip_Extern;
    }

    s32Ret = mpp_vosd_SetAlgExtern(pstExtern);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vo_SetPrimaryStr failed! [err=%d]!\n", s32Ret);
        return s32Ret;
    }


skip_Extern:


    return SV_SUCCESS;
}

static sint32 mpp_ctrl_aiaoaenc_recreate(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret, i;
    MPP_AIO_CONF_S  stAiConf   = {0};
    MPP_AO_CONF_S   stAoConf   = {0};
    MPP_AENC_CONF_S stAencConf = {0};

    if ((pstResetParam->bAudioEnable == m_stCtrlInfo.stRealParam.bAudioEnable)
     && (pstResetParam->bAudioMicEnable == m_stCtrlInfo.stRealParam.bAudioMicEnable)
     && (pstResetParam->bAudioAlarmEnable == m_stCtrlInfo.stRealParam.bAudioAlarmEnable)
     && (pstResetParam->enAencType == m_stCtrlInfo.stRealParam.enAencType)
     && (pstResetParam->enAudioSampleRate == m_stCtrlInfo.stRealParam.enAudioSampleRate)
     && (pstResetParam->u32Volume == m_stCtrlInfo.stRealParam.u32Volume)
     && (pstResetParam->u32OutputVolume == m_stCtrlInfo.stRealParam.u32OutputVolume)
    )
    {
        return SV_SUCCESS;
    }

#if MPP_AUDIO_AO_ENABLE
    s32Ret = mpp_ao_Fini();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ao_Fini failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
#endif


#if MPP_AUDIO_AI_ENABLE
    s32Ret = mpp_aenc_Stop();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_aenc_Stop failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_ai_Stop();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ai_Stop failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_aenc_Fini();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_aenc_Fini failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_ai_Fini();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ai_Fini failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
#endif

#if MPP_AUDIO_AI_ENABLE
    stAiConf.u32ChnNum = pstResetParam->u32ChnNum;
    stAiConf.enAioMode = MPP_AIO_MODE_I2S_SLAVE;
    stAiConf.enSampleRate = pstResetParam->enAudioSampleRate;
    stAiConf.enBitWidth = MPP_AUDIO_BIT_WIDTH_16;
    stAiConf.u32Volume = pstResetParam->u32Volume;
    stAiConf.bAudioEnable = pstResetParam->bAudioEnable;
    stAiConf.bAudioMicEnable = pstResetParam->bAudioMicEnable;
    stAiConf.bAudioAlarmEnable = pstResetParam->bAudioAlarmEnable;
    stAiConf.enAencType = pstResetParam->enAencType;
    stAiConf.pfDataCallback = pstResetParam->pfDataCallback;

    s32Ret = mpp_ai_Init(&stAiConf);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ai_Init failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    stAencConf.u32ChnNum = pstResetParam->u32ChnNum;
    stAencConf.enAencType = pstResetParam->enAencType;
    stAencConf.enAudioSampleRate = pstResetParam->enAudioSampleRate;
    stAencConf.bAudioMicEnable = pstResetParam->bAudioMicEnable;
    stAencConf.pfDataCallback = pstResetParam->pfDataCallback;
    s32Ret = mpp_aenc_Init(&stAencConf);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_aenc_Init failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_ai_Start();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ai_Start failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_aenc_Start();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_aenc_Start failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
#endif

#if MPP_AUDIO_AO_ENABLE
    stAoConf.bAudioMicEnable = pstResetParam->bAudioMicEnable;
    stAoConf.u32Volume = pstResetParam->u32OutputVolume;
    stAoConf.enSampleRate = pstResetParam->enAudioSampleRate;
    s32Ret = mpp_ao_Init(&stAoConf);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ao_Init failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
#endif

    return SV_SUCCESS;
}

static sint32 mpp_ctrl_aiao_param(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret;

    if (m_stCtrlInfo.stRealParam.u32Volume != pstResetParam->u32Volume)
    {
        s32Ret = mpp_aio_SetVolume(0, pstResetParam->u32Volume);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_aio_SetVolume failed! [err=%d]\n", s32Ret);
            return s32Ret;
        }
        m_stCtrlInfo.stRealParam.u32Volume = pstResetParam->u32Volume;
    }

    if (m_stCtrlInfo.stRealParam.u32OutputVolume != pstResetParam->u32OutputVolume)
    {
        s32Ret = mpp_ao_SetVolume(0, pstResetParam->u32OutputVolume);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_ao_SetVolume failed! [err=%d]\n", s32Ret);
            return s32Ret;
        }
        m_stCtrlInfo.stRealParam.u32OutputVolume = pstResetParam->u32OutputVolume;
    }

    return SV_SUCCESS;
}

static sint32 mpp_ctrl_ir_recreate(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret;

    s32Ret = mpp_ir_SetChnMirrorFlip(pstResetParam->bIrImageMirror, pstResetParam->bIrImageFlip);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_SetChnMirrorFlip failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}


