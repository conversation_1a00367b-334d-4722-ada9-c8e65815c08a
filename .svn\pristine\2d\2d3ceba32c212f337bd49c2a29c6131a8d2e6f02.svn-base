#ifndef _MPP_VDEC_H_
#define _MPP_VDEC_H_

#include "common.h"
#include "mpp_com.h"
#include "media.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define VIDEO_VDEC_CHN_MAX               5            /* 最大解码通道数 */
#define VDEC_DRM_BUF_NUM                 3            /* 解码使用DRMBUF数 */
#define VDEC_HEAP_BUF_NUM                3            /* 解码使用堆数 */

/* 视频解码通道，用于索引 */
typedef enum tagVdecChnType_E
{
    VDEC_CHN_H264 = 0x00,                             /* (0)H264 */
    VDEC_CHN_H265,                                    /* (1)H265 */
    VDEC_CHN_JPEG,                                    /* (2)JPEG */
    VDEC_CHN_MJPEG,                                   /* (3)MJPEG */

    VDEC_CHN_BUTT
} VDEC_CHN_TYPE_E;

/* 视频解码通道状态信息 */
typedef struct tagVdecChnConfig_S
{
    SV_BOOL             bVaild;         /* 是否有效 */
    SV_BOOL             bCreated;       /* 是否已创建 */
    SV_BOOL             bEnable;        /* 是否使能解码 */
    SV_BOOL             bStart;         /* 是否正在解码 */
    sint32              s32VdecFd;      /* 通道设备文件句柄 */
    sint32              s32VdecChnId;   /* 解码通道ID */
    uint32              u32Width;       /* 解码图像宽（申请存放解码后数据缓存区用） */
    uint32              u32Height;      /* 解码图像高 */
    VDEC_DATA_MODE_E    enDataMode;     /* 帧/包模块发送码流 */
    VDEC_DECODE_MODE_E  enDecodeMode;   /* 视频解码方式 */
    VDEC_CODEC_TYPE_E   enCodeType;     /* 数据编码类型 */
    VDEC_CHN_TYPE_E     enChnIndex;     /* 通道索引,一般情况等于通道ID*/

} MPP_VDEC_CHN_CFG_S;


/* 视频解码配置结构定义 */
typedef struct tagVDECConfig_S
{
    uint32                  u32ChnNum;     /* 输入通道数目 */
    uint32                  u32Width;      /* 解码图像宽（申请存放解码后数据缓存区用） */
    uint32                  u32Height;     /* 解码图像高 */
    VDEC_CODEC_TYPE_E       enVdecType;    /* 解码类型 */
    VDEC_DECODE_MODE_E      enDecodeMode;  /* 解码方式 */
    VDEC_DATA_MODE_E        enDataMode;    /* 输入数据类型 */
} MPP_VDEC_CONF_S;

/******************************************************************************
 * 函数功能: 创建H264解码通道
 * 输入参数: s32Chn -- 通道
             pstDecodeH264 -- H264编解码信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_H264ChnCreate(sint32 s32Chn, VIDEO_DECODE_H264_S *pstDecodeH264);

/******************************************************************************
 * 函数功能: 销魂H264解码通道
 * 输入参数: s32Chn -- 通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_H264ChnDestroy(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 使能H264解码通道
 * 输入参数: s32Chn -- 通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_H264ChnEnable(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 关闭H264解码通道
 * 输入参数: s32Chn -- 通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_H264ChnDisable(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 往H264通道发送待解码数据
 * 输入参数: s32Chn -- 通道 pstVdecFrame -- 解码数据信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_H264DataSend(sint32 s32Chn, VDEC_FRAME_S *pstVdecFrame, SV_BOOL bBlock);

/******************************************************************************
 * 函数功能: 创建解码通道
 * 输入参数: s32Chn -- 通道
              pstVdecCfg -- 解码配置信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_ChnCreate(sint32 s32Chn, MPP_VDEC_CONF_S *pstVdecCfg);

/******************************************************************************
 * 函数功能: 销毁解码通道
 * 输入参数: s32Chn -- 通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_ChnDestroy(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 使能解码通道
 * 输入参数: s32Chn -- 通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_ChnEnable(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 使能解码通道
 * 输入参数: s32Chn -- 通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_ChnDisable(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 往通道发送待解码数据
 * 输入参数: s32Chn -- 通道 pu8Addr -- 数据地址 u32Size -- 数据长度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_DataSend(sint32 s32Chn, uint8 *pu8Addr, uint32 u32Size);

/******************************************************************************
 * 函数功能: 往通道发送待解码数据
 * 输入参数: s32Chn -- 通道 pu8Addr -- 数据地址 u32Size -- 数据长度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_GetFrame(void **ppvBuf);

/******************************************************************************
 * 函数功能: 释放VDEC通道数据
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_VPSS_CHN_BUTT)
             ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_ReleaseFrame(void **ppvBuf);

/******************************************************************************
 * 函数功能: 根据虚拟地址获取FD文件描述符
 * 输入参数: pvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : DRMbuffer fd
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_GetFD(void *pvBuf);

/******************************************************************************
 * 函数功能: 根据虚拟地址获取缓存块大小
 * 输入参数: pvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : DRMbuffer fd
 * 注意    : 无
 *****************************************************************************/
extern uint32 mpp_vdec_GetSize(void *pvBuf);

/******************************************************************************
 * 函数功能: 获取DRM虚拟地址，显示绑定drm需要
 * 输入参数: s32Num -- 对应块
 * 输出参数: ppvBuf -- 数据指针
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_GetDrmAddr(sint32 s32Num, void **ppvBuf);

/******************************************************************************
 * 函数功能: 设置回播模式
 * 输入参数: bPlayback --- 是否开启回播
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_SetPlayback(SV_BOOL bPlayback, uint32 width, uint32 height);

/******************************************************************************
 * 函数功能: vdec初始化
 * 输入参数: pstVdecConf -- 通道配置信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_Init(MPP_VDEC_CONF_S *pstVdecConf);

/******************************************************************************
 * 函数功能: vdec去初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_Fini(void);

/******************************************************************************
 * 函数功能: 启动解码模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_Start(void);

/******************************************************************************
 * 函数功能: 结束解码模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vdec_Stop(void);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _MPP_VDEC_H_ */

