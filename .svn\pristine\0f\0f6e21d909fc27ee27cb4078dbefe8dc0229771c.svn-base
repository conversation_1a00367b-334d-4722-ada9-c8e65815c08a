/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：log.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-12-20

文件功能描述: 定义日志模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <errno.h>
#include <sys/sem.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/time.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h>
#include <dirent.h>

#define FORBID_LOG

#include "common.h"
#include "print.h"
#include "safefunc.h"
#include "config.h"
#include "msg.h"
#include "storage.h"
#include "../../autoUpdate/autoUpdate.h"
#include "sqlite3.h"

#define KERNEL_LOG(level, fmt, arg...) do{ \
				char logLine[1024]; \
				snprintf(logLine, 1024, "type=\"kernelLog\"\tlevel=%d\tfile=\"%s\"\tfunc=\"%s\"\tcontent=\"%d, " fmt "\"",level, __FILE__, __FUNCTION__, __LINE__, ## arg); \
				logLine[1023] = '\0'; \
				LOG_Submit(level, logLine); \
		}while(0)

#define LOG_QUE_LEN     4096        /* 日志消息队列长度 */
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
#define LOG_FILE_SIZE   (2*1000*1024)   /* 日志文件最大大小 */
#else
#define LOG_FILE_SIZE   (500*1024)      /* 日志文件最大大小 */
#endif
#define LOG_MAX_VARLOGSIZE (2*1024*1024)    /* var日志文件最大占用空间 */
#define LOG_MAX_SDLOGSIZE (150*1024*1024)   /* sd卡日志文件最大占用空间 */

#define LOG_PATH_VAR        "/var/log"
#if (defined(BOARD_ADA47V1))
#define LOG_DB_PATH         "/userdata/log.db";
#define LOG_DB_STORAGE      STORAGE_INNER_EMMC
#else
#define LOG_DB_PATH         "/mnt/sdcard/log.db";
#define LOG_DB_STORAGE      STORAGE_MAIN_SD1
#endif


/* 模块控制信息 */
typedef struct tagLogInfo_S
{
    sint32              s32LogSerial;       /* 升级日志序号 */
    CFG_DEV_INFO        stDevInfo;          /* 设备信息 */
    sint32              bStarted;           /* 模块是否被启动 */
    sint32              s32LogFileSem;      /* 日志文件信号量 (用于cache文件互斥写入) */
    sint32              s32RecordLevel;     /* 日志记录级别 */
    sint32              s32UploadLevel;     /* 日志上传级别 */
    uint32              u32RecLogNum;       /* 当前记录的日志条数 */
    uint32              u32UplLogNum;       /* 当前上传的日志条数 */
    sint32              s32LogQueId;        /* 日志消息队列ID */
    sint32              s32RecvTid;         /* 日志消息接收线程ID */
    sint32              s32KmsgTid;         /* 内核日志处理线程ID */
    LOG_TYPE_E          eLogType;           /* 日志类型 */
	SV_BOOL		        bUpdateLog;			/* 是否是更新日志 */
	SV_BOOL		        bNeedCheckSD;		/* 是否需要检查存在SD卡 */
    SV_BOOL             bRunning;           /* 日志消息接收线程是否正在运行 */
    SV_BOOL     bCreateUploadMask;  /* 是否已经检测插入上传掩码项 */
    pthread_mutex_t mutexLockDB;    /* 数据库访问互斥锁 */
} LOG_INFO_S;

LOG_INFO_S m_stLogInfo = {0};       /* 模块控制信息 */

static char * log_GetNowTimeStr()
{
    time_t tNow = time(NULL);
    static time_t tLastTime = 0;
    static char szNowTime[20];

    if (tLastTime != tNow)
    {
        struct tm stTime = {0};
        struct timeval tvNow;
        struct timezone tz;
        gettimeofday(&tvNow, &tz);
        tvNow.tv_sec += (tz.tz_minuteswest * 60);
        gmtime_r((time_t *)&tvNow.tv_sec, &stTime);
        sprintf(szNowTime, "%4d%02d%02d%02d%02d%02d", stTime.tm_year+1900, stTime.tm_mon+1, stTime.tm_mday, \
                            stTime.tm_hour, stTime.tm_min, stTime.tm_sec);
        tLastTime = tNow;
    }

    return szNowTime;
}

/******************************************************************************
 * 函数功能: 日志文件写入锁定 (互斥写入)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
static sint32 log_WriteLock()
{
    sint32 s32Ret = 0;
    sint32 s32SemId = m_stLogInfo.s32LogFileSem;

    struct sembuf stSemOpt;
    stSemOpt.sem_num = 0;
    stSemOpt.sem_op = -1;
    stSemOpt.sem_flg = SEM_UNDO;
    s32Ret = semop(s32SemId, &stSemOpt, 1);
    if (s32Ret < 0)
    {
        if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 日志文件写入解锁 (互斥写入)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
static sint32 log_WriteUnlock()
{
    sint32 s32Ret = 0;
    sint32 s32SemId = m_stLogInfo.s32LogFileSem;

    struct sembuf stSemOpt;
    stSemOpt.sem_num = 0;
    stSemOpt.sem_op = 1;
    stSemOpt.sem_flg = SEM_UNDO;
    s32Ret = semop(s32SemId, &stSemOpt, 1);
    if (s32Ret < 0)
    {
        if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检测存储是否可读写，并获取对应的存储路径
 * 输入参数: 无
 * 输出参数: bStorageIsWritable --- 存储是否可写
             pszLogDir --- 存储路径
 * 返回值  : SV_FAILURE --- 处理出错
             SV_SUCCESS --- 处理正常
 * 注意    : 无
 *****************************************************************************/
sint32 log_GetStorageStatus(SV_BOOL *bStorageIsWritable, char *pszLogDir)
{
    sint32 i = 0;
    char szCmd[64] = {0};
    char szLogDirPath[64] = {0};
    SV_BOOL bIsWritable = SV_FALSE;
    STORAGE_PATH_S stStorageLogPath[5] = {{STORAGE_MAIN_SD1, STORAGE_PATH_SDCARD1}, {STORAGE_MAIN_SD2, STORAGE_PATH_SDCARD2}, \
                                                {STORAGE_MAIN_SD3, STORAGE_PATH_SDCARD3}, {STORAGE_INNER_EMMC, STORAGE_PATH_EMMC}, \
                                                {STORAGE_EXTRA_SD, STORAGE_PATH_UDISK}};
    if (NULL == pszLogDir)
        return SV_FAILURE;

    if (!m_stLogInfo.bNeedCheckSD)
        goto exit;

#if USING_STORAGE
    if (m_stLogInfo.bUpdateLog)
    {
        bIsWritable = STORAGE_IsWritable(0);
        if (bIsWritable)
            strcpy(pszLogDir, STORAGE_PATH_SDCARD1);
    }
    else
    {
#if (defined(BOARD_IPCR20S4))
        if (STORAGE_IsWritable(stStorageLogPath[0].eStoragePos))
        {
            bIsWritable = SV_TRUE;
            strcpy(pszLogDir, stStorageLogPath[0].szStoragePath);
        }
#elif (defined(BOARD_ADA47V1))
        if (STORAGE_IsWritable(stStorageLogPath[3].eStoragePos))
        {
            bIsWritable = SV_TRUE;
            strcpy(pszLogDir, stStorageLogPath[3].szStoragePath);
        }
#elif (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32V4))
        if (STORAGE_IsWritable(stStorageLogPath[4].eStoragePos))
        {
            bIsWritable = SV_TRUE;
            strcpy(pszLogDir, stStorageLogPath[4].szStoragePath);
        }
#else
        for (i = 0; i < 5; i++)
        {
            if (STORAGE_IsWritable(stStorageLogPath[i].eStoragePos))
            {
                bIsWritable = SV_TRUE;
                strcpy(pszLogDir, stStorageLogPath[i].szStoragePath);
                break;
            }
        }
#endif
    }
#endif

exit:
    if (!bIsWritable)
    {
        if (LOG_TYPE_CAN_UPGRADE == m_stLogInfo.eLogType)
        {
            strcpy(pszLogDir, CAN_UPGRADE_ENDPAC_PATH);
        }
        else if (LOG_TYPE_UPDATE == m_stLogInfo.eLogType)
        {
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126B))
            strcpy(pszLogDir, "/boot");
#else
            strcpy(pszLogDir, STORAGE_PATH_MEMORY);
#endif
        }
        else
        {
            strcpy(pszLogDir, STORAGE_PATH_MEMORY);
        }
    }

    sprintf(szLogDirPath, "%s/log", pszLogDir);
    if (SV_SUCCESS != access(szLogDirPath, F_OK))
    {
        snprintf(szCmd, 64, "mkdir -p %s", szLogDirPath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);

        sprintf(szLogDirPath, "%s/log/uploaded", pszLogDir);
        snprintf(szCmd, 64, "mkdir -p %s", szLogDirPath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
    }

    *bStorageIsWritable = bIsWritable;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取所有日志文件总大小(字节)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : >0 - 总大小
             <0 - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 log_GetAllLogFileSize()
{
    sint32 s32Ret = 0;
    DIR *pDir = NULL;
    struct dirent *pstDirent = NULL;
    sint32 s32TotalSize = 0;
    struct stat stFileInfo;
    char szFilePath[64];
    char szLogDir[64] = {0};
	char szLogPath[64];
	char szUploadedPath[64];
    SV_BOOL bSdIsWritable = SV_FALSE;

    s32Ret = log_GetStorageStatus(&bSdIsWritable, szLogDir);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "log_GetStorageStatus failed.\n");
        strcpy(szLogDir, STORAGE_PATH_MEMORY);
    }
    snprintf(szLogPath, 64, "%s/log", szLogDir);

	pDir = opendir(szLogPath);
	if(NULL == pDir)
    {
        print_level(SV_ERROR, "opendir failed. [err: %s]\n", strerror(errno));
        return -1;
    }

    while (1)
    {
        pstDirent = readdir(pDir);
        if(NULL == pstDirent)
        {
            closedir(pDir);
            break;
        }

        if (0 == strcmp(pstDirent->d_name, "uploaded"))
        {
            continue;
        }

        sprintf(szFilePath, "%s/%s", szLogPath, pstDirent->d_name);
        s32Ret = stat(szFilePath, &stFileInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "stat file: %s failed. [err: %s]\n", szFilePath, strerror(errno));
            return -1;
        }
        s32TotalSize += stFileInfo.st_size;
    }

	sprintf(szUploadedPath, "%s/uploaded", szLogPath);
    pDir = opendir(szUploadedPath);
    if(NULL == pDir)
    {
        print_level(SV_WARN, "opendir %s failed. [err: %s]\n", szUploadedPath, strerror(errno));
        return s32TotalSize;
    }
    while (1)
    {
        pstDirent = readdir(pDir);
        if(NULL == pstDirent)
        {
            closedir(pDir);
            break;
        }

        sprintf(szFilePath, "%s/%s", szUploadedPath, pstDirent->d_name);
        s32Ret = stat(szFilePath, &stFileInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "stat file: %s failed. [err: %s]\n", szFilePath, strerror(errno));
            return -1;
        }
        s32TotalSize += stFileInfo.st_size;
    }

    return s32TotalSize;
}

/******************************************************************************
 * 函数功能: 删除过旧的日志文件释放空间
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 log_RemoveOldLogFile()
{
    sint32 s32Ret = 0;
    DIR *pDir = NULL;
    struct dirent *pstDirent = NULL;
    char szOldestFile[64] = {0};
    char szFilePath[64];
	char szLogDir[64];
	char szLogPath[64];
	char szUploadedPath[64];
    SV_BOOL bSdIsWritable = SV_FALSE;

    s32Ret = log_GetStorageStatus(&bSdIsWritable, szLogDir);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "log_GetStorageStatus failed.\n");
        strcpy(szLogDir, STORAGE_PATH_MEMORY);
    }
    snprintf(szLogPath, 64, "%s/log", szLogDir);

	sprintf(szUploadedPath, "%s/uploaded", szLogPath);
    pDir = opendir(szUploadedPath);
    if(NULL == pDir)
    {
        print_level(SV_ERROR, "opendir %s failed. [err: %s]\n", szUploadedPath, strerror(errno));
        goto next;
    }
    while (1)
    {
        pstDirent = readdir(pDir);
        if(NULL == pstDirent)
        {
            closedir(pDir);
            break;
        }

        if (NULL != strstr(pstDirent->d_name, "log_"))
        {
            if (0 == strlen(szOldestFile))
            {
                strcpy(szOldestFile, pstDirent->d_name);
            }
            else if (strcmp(pstDirent->d_name, szOldestFile) < 0)
            {
                strcpy(szOldestFile, pstDirent->d_name);
            }
        }
    }
    if (0 != strlen(szOldestFile))
    {
        sprintf(szFilePath, "%s/%s", szUploadedPath, szOldestFile);
        s32Ret = remove(szFilePath);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "remove file: %s failed. [err: %s]\n", szFilePath, strerror(errno));
            goto next;
        }
        else
        {
            print_level(SV_INFO, "remove file: %s.\n", szFilePath);
            return SV_SUCCESS;
        }
    }

next:
    pDir = opendir(szLogPath);
    if(NULL == pDir)
    {
        print_level(SV_ERROR, "opendir %s failed. [err: %s]\n", szLogPath, strerror(errno));
        return SV_FAILURE;
    }
    while (1)
    {
        pstDirent = readdir(pDir);
        if(NULL == pstDirent)
        {
            closedir(pDir);
            break;
        }

        if (NULL != strstr(pstDirent->d_name, "log_"))
        {
            if (0 == strlen(szOldestFile))
            {
                strcpy(szOldestFile, pstDirent->d_name);
            }
            else if (strcmp(pstDirent->d_name, szOldestFile) < 0)
            {
                strcpy(szOldestFile, pstDirent->d_name);
            }
        }
    }

    if (0 != strlen(szOldestFile))
    {
        sprintf(szFilePath, "%s/%s", szLogPath, szOldestFile);
        s32Ret = remove(szFilePath);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "remove file: %s failed. [err: %s]\n", szFilePath, strerror(errno));
            return SV_FAILURE;
        }
        print_level(SV_INFO, "remove file: %s.\n", szFilePath);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重命名由于系统异常退出残留的cache文件
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 log_RenameCacheFile(char *szLogPath, SV_BOOL bMoveLogFile)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    sint64 s64Date = 0;
    char szBuf[16] = {0};
	char szCmd[128];
    char szFilePath[64];
	char szCachePath[64];
    char szLogDir[64] = {0};
    char szDestPath[64] = {0};
    SV_BOOL bSdIsWritable = SV_FALSE;

	if (NULL == szLogPath)
	{
		print_level(SV_ERROR, "szLogPath is invalid.");
		return SV_FAILURE;
	}

	sprintf(szCachePath, "%s/cache", szLogPath);
    s32Fd = open(szCachePath, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", szCachePath, strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = read(s32Fd, szBuf, 14);
    if (s32Ret <= 0)
    {
        print_level(SV_ERROR, "read file: %s failed. [err: %s]\n", szCachePath, strerror(errno));
        close(s32Fd);
        return SV_FAILURE;
    }
    close(s32Fd);

    s32Ret = sscanf(szBuf, "%lld", &s64Date);
    if (1 != s32Ret)
    {
        print_level(SV_WARN, "get cache log file date failed! szBuf: %s\n", szBuf);
        strcpy(szBuf, "20200101000000");
    }

    sprintf(szFilePath, "%s/log_%s.txt", szLogPath, szBuf);
    s32Ret = rename(szCachePath, szFilePath);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "rename file: %s -> %s failed. [err: %s]\n", szCachePath, szFilePath, strerror(errno));
        return SV_FAILURE;
    }
    print_level(SV_INFO, "rename file: %s -> %s successful.\n", szCachePath, szFilePath);

	if (bMoveLogFile)
	{
        log_GetStorageStatus(&bSdIsWritable, szLogDir);
        snprintf(szDestPath, 64, "%s/log", szLogDir);
		sprintf(szCmd, "mv %s %s", szFilePath, szDestPath);
		s32Ret = SAFE_System(szCmd, 10000);
		if (0 != s32Ret)
		{
			printf("cmd: %s failed.\n", szCmd);
			return SV_FAILURE;
		}
	}

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 循环清理日志文件
 * 输入参数: file --- 日志文件路径
 * 输出参数: pindex --- 日志文件序号
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 log_cycle_file_init(char *file, char*head, int *pindex)
{
    sint32 s32Ret;
    sint32 local_num, local_index;
    sint32 s32MaxNum = 10;
    char szcmd[128], szbuf[128];
    if((access(file, F_OK))==-1)
    {
        *pindex = 1;
        sprintf(szcmd, "echo \"%s local num [%d] \" >> %s", head, 1, file);
        SAFE_System(szcmd, NORMAL_WAIT_TIME);
        return SV_SUCCESS;
    }
    sprintf(szcmd, "sed -i '/^%s/!d' %s", head, file); // 删除非 head 开头的行
    SAFE_System(szcmd, NORMAL_WAIT_TIME);

    sprintf(szcmd, "head -n 1 %s 2>/dev/null | awk -F \"%s local num \"  '{print $2}' | awk -F '[][]' '{print $2}'", file, head);
    s32Ret = SAFE_System_Recv(szcmd, szbuf, 128);
    if(s32Ret != SV_SUCCESS || strlen(szbuf) == 0)
    {
        *pindex = 1;
        sprintf(szcmd, "sed -i \"1c %s local num [%d]\" %s", head, 1, file);
        SAFE_System(szcmd, NORMAL_WAIT_TIME);
        return SV_FAILURE;
    }

    local_num = atoi(szbuf);
    if(local_num < s32MaxNum)
    {
        local_num+=1;
        sprintf(szcmd, "sed -i \"1c %s local num [%d]\" %s", head, local_num, file);
        SAFE_System(szcmd, NORMAL_WAIT_TIME);
    }
    else
    {
        memset(szbuf, 0x0, 128);
        sprintf(szcmd, "sed -n \"2,2p\" %s | awk '{print $1}' | sed 's#\\[#\\\\\\[#g' | sed 's#\\]#\\\\\\]#g' ", file); // awk 进行格式转化
        SAFE_System_Recv(szcmd, szbuf, 128);
        if(strlen(szbuf) > 0 && szbuf[strlen(szbuf)-1] == '\n')
        {
            szbuf[strlen(szbuf)-1] = '\0';
        }
        sprintf(szcmd, "sed -i \"/%s/d\" %s", szbuf, file);  // 删除太旧的信息
        SAFE_System(szcmd, NORMAL_WAIT_TIME);
    }

    memset(szbuf, 0x0, 128);
    sprintf(szcmd, "tail -n 1 %s | awk '{print $1}' | awk -F '[][]' '{print $2}'", file);
    s32Ret = SAFE_System_Recv(szcmd, szbuf, 128);
    if(s32Ret != SV_SUCCESS || strlen(szbuf) == 0)
    {
        *pindex = 1;
        return SV_FAILURE;
    }
    *pindex = atoi(szbuf) + 1;
    return SV_SUCCESS;
}

void *log_Write_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    char szBuf[4096];
    char szCacheLogRelease[30*1024] = {0};
    char szCmd[64] = {0};
    char szLogDir[64] = {0};
    char szLogPath[64] = {0};
    char szCachePath[64] = {0};
    static char szLastLogDir[64] = {0};
    SV_BOOL bSdIsWritable = SV_FALSE;
    static char head_buf[128] = {0};
    static int index = 0;
    static uint32 u32LogCnt = 0;
    char *pszLogBuf = (char *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "log_write_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    log_WriteLock();
    s32Ret = log_GetStorageStatus(&bSdIsWritable, szLogDir);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "log_GetStorageStatus failed.\n");
        strcpy(szLogDir, STORAGE_PATH_MEMORY);
    }
    snprintf(szLogPath, 64, "%s/log", szLogDir);

    if (m_stLogInfo.bUpdateLog)
    {
        snprintf(szCachePath, 64, "%s/update.log", szLogDir);
        if(index <= 0)
        {
            s32Ret = log_cycle_file_init(szCachePath, "autoupdate", &index);
            if(s32Ret != SV_SUCCESS)
            {
                printf("log_cycle_file_init fail!\n");
            }
            sprintf(head_buf, "autoupdate[%d] ", index);
            m_stLogInfo.s32LogSerial = index;
        }
    }
    else
    {
        if (bSdIsWritable && 0 == access("/var/log/cache", F_OK))
        {
            log_RenameCacheFile(LOG_PATH_VAR, SV_TRUE);
            snprintf(szCmd, 64, "mv %s/log_* %s", LOG_PATH_VAR, szLogPath);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
        }
        snprintf(szCachePath, 64, "%s/log/cache", szLogDir);
        if (0 != strlen(szLastLogDir) && 0 != strcmp(szLogDir, szLastLogDir) && 0 == access(szCachePath, F_OK))
        {
            printf("storage log path is changed to: %s, rename cache file now!!\n", szLogDir);
            log_RenameCacheFile(szLogPath, SV_FALSE);
        }
        strcpy(szLastLogDir, szLogDir);
    }

    s32Fd = open(szCachePath, O_CREAT|O_RDWR, S_IRUSR|S_IWUSR|S_IRGRP|S_IROTH);
    if (s32Fd < 0)
    {
        printf("open file: %s failed. [err: %s]\n", szCachePath, strerror(errno));
        if (!m_stLogInfo.bUpdateLog)
        {
            sprintf(szCachePath, "%s/log/cache", "/var");
            s32Fd = open(szCachePath, O_CREAT|O_RDWR, S_IRUSR|S_IWUSR|S_IRGRP|S_IROTH);
            if (s32Fd < 0)
            {
                printf("open file: %s failed. [err: %s]\n", szCachePath, strerror(errno));
                goto exit;
            }
        }
        else
        {
            goto exit;
        }
    }

    lseek(s32Fd, 0, SEEK_END);
    if (m_stLogInfo.bUpdateLog)
    {
        snprintf(szCacheLogRelease, 30*1024, pszLogBuf, head_buf);
        s32Ret = write(s32Fd, szCacheLogRelease, strlen(szCacheLogRelease));
    }
    else
    {
        s32Ret = write(s32Fd, pszLogBuf, strlen(pszLogBuf));
    }

    if (s32Ret <= 0)
    {
        printf("write failed. [err: %s]\n", strerror(errno));
        close(s32Fd);
        goto exit;
    }

    s32Ret = lseek(s32Fd, 0, SEEK_CUR);
    close(s32Fd);

    if (!m_stLogInfo.bUpdateLog)
    {
        if (s32Ret > LOG_FILE_SIZE || s32Ret < 0)
        {
            log_RenameCacheFile(szLogPath, SV_FALSE);
        }
    }

exit:
    if (NULL != pszLogBuf)
    {
        free(pszLogBuf);
    }
    log_WriteUnlock();

    /* 检查所有日志的大小，超出后删除旧的日志文件 */
    if (!m_stLogInfo.bUpdateLog)
	{
        log_GetStorageStatus(&bSdIsWritable, szLogDir);
        if (0 == u32LogCnt%50)
		{
		    while (log_GetAllLogFileSize() > (bSdIsWritable ? LOG_MAX_SDLOGSIZE : LOG_MAX_VARLOGSIZE))
		    {
    			log_RemoveOldLogFile();
		    }
		}
	}
    u32LogCnt++;

    return NULL;
}

/******************************************************************************
 * 函数功能: 将一条日志写入到日志文件中
 * 输入参数: s32Level --- 日志级别
             pszLog --- 日志内容
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 log_WriteLog(sint32 s32Level, char *pszLog)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    char szBuf[4096];
    char *pcTmp = NULL;
    time_t tNow = time(NULL);
    static time_t tCacheTime = 0;
    static char szCacheLog[20*1024] = {0};
    char szCacheLogRelease[30*1024] = {0};
    char szCmd[64] = {0};
    char szLogDir[64] = {0};
    char szLogPath[64] = {0};
    char szCachePath[64] = {0};
    static char szLastLogDir[64] = {0};
    SV_BOOL bSdIsWritable = SV_FALSE;
    static char head_buf[128] = {0};
    static int index = 0;
    pthread_t thread_trigger;
    pthread_attr_t 	attr;
    sint32 s32SyncLogTime = 3;
    char *pszLogBuf = NULL;

    if (m_stLogInfo.s32RecordLevel < s32Level)
    {
        if (abs(tNow - tCacheTime) > s32SyncLogTime && strlen(szCacheLog) > 0)
        {
            goto sync_log;
        }

        return SV_SUCCESS;
    }

    pcTmp = pszLog;
    while((pcTmp=strchr(pcTmp,'\n'))!=NULL)
    {
        if (*(pcTmp-1) == '\r')
            *(pcTmp-1) = ' ';
        *pcTmp = ' ';
    }

    if (m_stLogInfo.bUpdateLog)
    {
                                                                                                                  /* %1$s在此添加位置参数 */
        snprintf(szBuf, 4096, "autoUpdate[%d]%s\tserialNum=\"%s\"\thardware=\"%s\"\tfirmware=\"%s\"\t%s\tupLoadStatus=00001\n", m_stLogInfo.s32LogSerial, log_GetNowTimeStr(), \
                            m_stLogInfo.stDevInfo.szSerialNum, m_stLogInfo.stDevInfo.szHardwareVer, m_stLogInfo.stDevInfo.szFirmwareVer, pszLog);

    }
    else
    {
                                                                                                                  /* %1$s在此添加位置参数 */
        snprintf(szBuf, 4096, "%s\tserialNum=\"%s\"\thardware=\"%s\"\tfirmware=\"%s\"\t%s\tupLoadStatus=00001\n", log_GetNowTimeStr(), \
                            m_stLogInfo.stDevInfo.szSerialNum, m_stLogInfo.stDevInfo.szHardwareVer, m_stLogInfo.stDevInfo.szFirmwareVer, pszLog);
    }

    strncat(szCacheLog, szBuf, strlen(szBuf));
    if (abs(tNow - tCacheTime) > s32SyncLogTime
        || strlen(szCacheLog) >= 10*1024
        || strstr(szBuf, "Write all remaining logs to cache.") != NULL
        || strstr(szBuf, "update finished.") != NULL
        || m_stLogInfo.bUpdateLog)
    {
sync_log:
        pszLogBuf = (char *)malloc(20*1024);
        if (NULL == pszLogBuf)
        {
            printf("malloc pszLogBuf failed!\n");
            goto exit;
        }
        strncpy(pszLogBuf, szCacheLog, strlen(szCacheLog)+1);

		pthread_attr_init(&attr);
		pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);	   //设置为分离线程
		s32Ret = pthread_create(&thread_trigger, &attr, log_Write_Body, pszLogBuf);
		if (s32Ret < 0)
		{
			print_level(SV_ERROR, "pthread_create  failed. [err: %s]\n", strerror(errno));
			return SV_FAILURE;
		}
        pthread_attr_destroy(&attr);

exit:
        tCacheTime = tNow;
        memset(szCacheLog, 0, sizeof(szCacheLog));
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将一条日志上传到日志服务器
 * 输入参数: s32Level --- 日志级别
             pszLog --- 日志内容
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 log_UploadLog(sint32 s32Level, char *pszLog)
{
    sint32 s32Ret = 0;
    char szBuf[1024];
    char *pcTmp = NULL;
    MSG_PACKET_S stMsgPkt = {0};
    MSG_LOG_Upload_CFG stLogUploadCfg = {0};

    if (m_stLogInfo.s32UploadLevel < s32Level)
        return SV_SUCCESS;

    pcTmp = pszLog;
    while((pcTmp=strchr(pcTmp,'\n'))!=NULL)
    {
        if (*(pcTmp-1) == '\r')
            *(pcTmp-1) = ' ';
        *pcTmp = ' ';
    }

    snprintf(szBuf, 1024, "{\"DeviceId\":%s,\"ErrorCode\":%s,\"Time\":%s}",  \
                        m_stLogInfo.stDevInfo.szSerialNum, pszLog, log_GetNowTimeStr());

    stLogUploadCfg.u8UploadLevel = (uint8)s32Level;
    stLogUploadCfg.u32LogID = ++m_stLogInfo.u32UplLogNum;
    strcpy(stLogUploadCfg.szTime, log_GetNowTimeStr());
    memcpy(stLogUploadCfg.szContent, szBuf, strlen(szBuf));
    stLogUploadCfg.u16ContentLength = strlen(stLogUploadCfg.szContent);
    stMsgPkt.pu8Data = (uint8 *)&stLogUploadCfg;
    stMsgPkt.u32Size = sizeof(MSG_LOG_Upload_CFG);

    s32Ret = Msg_execRequestBlock(EP_LOG, EP_CONNSERVER, OP_REQ_UPLOADLOG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_UPLOADLOG failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    return SV_SUCCESS;
}

void * log_Recive_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32Level = 0;
    LOG_INFO_S *pstLogInfo = (LOG_INFO_S *)pvArg;
    MSG_BUF_S stRecvBuf = {0};
    uint8 au8Buf[4096];
    uint32 u32Size = 0;
    char szLogDir[64] = {0};
    SV_BOOL bSdIsWritable = SV_FALSE;
    s32Ret = prctl(PR_SET_NAME, "log_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstLogInfo->bRunning)
    {
        stRecvBuf.u16DestId = EP_LOG;
        s32Ret = MSG_Read(m_stLogInfo.s32LogQueId, &stRecvBuf, au8Buf, &u32Size);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_Read failed. [err=%#x]\n", s32Ret);
            sleep_ms(100);
            continue;
        }

        s32Level = stRecvBuf.s32Param;
        log_WriteLog(s32Level, au8Buf);

#if 0
		if (m_stLogInfo.bNeedCheckSD)
		{
	        //s32Ret = log_UploadLog(s32Level, au8Buf);
	        if (SV_SUCCESS != s32Ret)
	        {
	            print_level(SV_ERROR, "log_UploadLog failed. [err=%#x]\n", s32Ret);
	        }
		}
#endif
    }

    return NULL;
}

void * log_Kmsg_Body(void *pvArg)
{
    sint32 s32Ret = 0;
	sint32 errCnt = 0;
    uint8 szCmd[128] = {0};
    uint8 au8Buf[1024] = {0};
	static FILE *pFileKsg = NULL;
    LOG_INFO_S *pstLogInfo = (LOG_INFO_S *)pvArg;
    s32Ret = prctl(PR_SET_NAME, "log_kmsg_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    while (pstLogInfo->bRunning)
    {
		sleep_ms(1000);
		if (pFileKsg == NULL)
		{
			pFileKsg = fopen("/proc/kmsg", "r");
			if (pFileKsg == NULL)
			{
				print_level(SV_ERROR, "fopen /proc/kmsg failed! [err:%s]\n", strerror(errno));
				continue;
			}
		}

		while (pstLogInfo->bRunning)
		{
            memset(au8Buf, 0, sizeof(au8Buf));
			if (fgets(au8Buf, 1024, pFileKsg) != NULL)
			{
				if (strstr(au8Buf, "Persistence not supported for GPIO") != NULL)
				{
					memset(au8Buf, 0, sizeof(au8Buf));
					continue;
				}

                #if defined(BOARD_DMS31V2)
                if (NULL != strstr(au8Buf, "rockchip-mipi-csi2: ERR1: crc errors"))
                {
                    printf("get mipi error msg: %s\n", au8Buf);
                    if (0 != access(NETWORK_CHECK_FILE, F_OK))
                    {
                        sprintf(szCmd, "touch %s", NETWORK_CHECK_FILE);
                        SAFE_System(szCmd, NORMAL_WAIT_TIME);
                    }
                }

                if (NULL != strstr(au8Buf, "can0: bus-off"))
                {
                    printf("get mipi error msg: %s\n", au8Buf);
                    if (0 != access(CAN_CHECK_FILE, F_OK))
                    {
                        sprintf(szCmd, "touch %s", CAN_CHECK_FILE);
                        SAFE_System(szCmd, NORMAL_WAIT_TIME);
                    }
                }
                #endif
				KERNEL_LOG(SV_WARN, "[kmsg]: %s", au8Buf);
				errCnt = 0;
			}
			else
			{
				if (++errCnt >= 3)
				{
					errCnt = 0;
					fclose(pFileKsg);
					pFileKsg = NULL;
					break;
				}
				else
				{
					sleep_ms(50);
				}
			}
		}
    }
	if (pFileKsg != NULL)
	{
		fclose(pFileKsg);
		pFileKsg = NULL;
	}
    return NULL;
}
sint32 LOG_Init(CFG_SER_PARAM *pstSerParam, SV_BOOL bMaster)
{
    sint32 s32Ret = 0;
    sint32 s32SemId;
    char szCmd[64] = {0};
    union semun {
       int              val;    /* Value for SETVAL */
       struct semid_ds *buf;    /* Buffer for IPC_STAT, IPC_SET */
       unsigned short  *array;  /* Array for GETALL, SETALL */
       struct seminfo  *__buf;  /* Buffer for IPC_INFO */
    } unSemArgs;

    if (NULL == pstSerParam)
    {
        return ERR_NULL_PTR;
    }

    memset(&m_stLogInfo, 0, sizeof(LOG_INFO_S));
    s32Ret = pthread_mutex_init(&m_stLogInfo.mutexLockDB, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    CONFIG_GetDevInfo(&m_stLogInfo.stDevInfo);
    if (bMaster && 0 == access("/var/log/cache", F_OK))
    {
        log_RenameCacheFile(LOG_PATH_VAR, SV_FALSE);
    }

    if (0 != access(LOG_PATH_VAR, F_OK))
    {
        snprintf(szCmd, 64, "mkdir -p %s", LOG_PATH_VAR);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
    }

    s32SemId = semget(LOGFILE_SEM_KEY, 1, IPC_CREAT|0600);
    if (s32SemId < 0)
    {
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }
    unSemArgs.val = 1;
    s32Ret = semctl(s32SemId, 0, SETVAL, unSemArgs);
    if (s32Ret < 0)
    {
        semctl(s32SemId, 0, IPC_RMID, 0);
        return ERR_SYS_NOTREADY;
    }

    MSG_SetMaster(bMaster);

    s32Ret = MSG_CreateQue(LOG_QUE_KEY, LOG_QUE_LEN, &m_stLogInfo.s32LogQueId);
    if (s32Ret == ERR_EXIST)
    {
        print_level(SV_WARN, "log queue is exist, try to open it.\n");
        s32Ret = MSG_OpenQue(LOG_QUE_KEY, &m_stLogInfo.s32LogQueId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_OpenQue failed. [err=%#x]\n", s32Ret);
            semctl(s32SemId, 0, IPC_RMID, 0);
            return s32Ret;
        }
    }
    else if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_CreateQue failed. [err=%#x]\n", s32Ret);
        semctl(s32SemId, 0, IPC_RMID, 0);
        return s32Ret;
    }

    m_stLogInfo.s32LogFileSem = s32SemId;
    m_stLogInfo.eLogType = pstSerParam->eLogType;
	if (LOG_TYPE_UPDATE == m_stLogInfo.eLogType || LOG_TYPE_CAN_UPGRADE == m_stLogInfo.eLogType)
	{
		m_stLogInfo.s32RecordLevel = 3;
		m_stLogInfo.s32UploadLevel = 3;
        m_stLogInfo.bUpdateLog = SV_TRUE;
	}
	else
	{
        m_stLogInfo.s32RecordLevel = pstSerParam->s32RecordLevel;
	    m_stLogInfo.s32UploadLevel = pstSerParam->s32UploadLevel;
        m_stLogInfo.bUpdateLog = SV_FALSE;
	}

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106) || defined(BOARD_IPCR20S4) || defined(PLATFORM_RV1126B))
	m_stLogInfo.bNeedCheckSD = SV_TRUE;
#else
	m_stLogInfo.bNeedCheckSD = SV_FALSE;
#endif

    if (!bMaster)
    {
        m_stLogInfo.bStarted = SV_TRUE;
    }

    return SV_SUCCESS;
}

sint32 LOG_Fini()
{
    sint32 s32Ret = 0, i;

    s32Ret = MSG_DestroyQue(m_stLogInfo.s32LogQueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "MSG_DestroyQue failed. [err=%#x]\n", s32Ret);
    }

    semctl(m_stLogInfo.s32LogFileSem, 0, IPC_RMID, 0);

    pthread_mutex_destroy(&m_stLogInfo.mutexLockDB);
    memset(&m_stLogInfo, 0, sizeof(LOG_INFO_S));

    return SV_SUCCESS;
}

sint32 LOG_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread1;
    pthread_t thread2;

    m_stLogInfo.bRunning = SV_TRUE;

	s32Ret = pthread_create(&thread1, NULL, log_Recive_Body, &m_stLogInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
	if (!m_stLogInfo.bUpdateLog)
	{
		s32Ret = pthread_create(&thread2, NULL, log_Kmsg_Body, &m_stLogInfo);
	    if (0 != s32Ret)
	    {
	        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
	        return ERR_SYS_NOTREADY;
	    }
		m_stLogInfo.s32KmsgTid = thread2;
	}


    m_stLogInfo.bStarted = SV_TRUE;
    m_stLogInfo.s32RecvTid = thread1;

    return SV_SUCCESS;
}

sint32 LOG_Stop()
{
    sint32 s32Ret = 0;
    pthread_t thread1 = m_stLogInfo.s32RecvTid;
    pthread_t thread2 = m_stLogInfo.s32KmsgTid;
    void *pvRetval = NULL;
	sint32 s32Level = 3;
	char szFiniBuf[1024];

	m_stLogInfo.bRunning = SV_FALSE;
    LOG_Submit(4, "exit log");

	if (m_stLogInfo.bUpdateLog)
		snprintf(szFiniBuf, 1024, "type=\"updateLog\"\tlevel=%d\tfile=\"%s\"\tfunc=\"%s\"\tcontent=\"%d, update finished. \"", s32Level, __FILE__, __FUNCTION__, __LINE__);
	else
		snprintf(szFiniBuf, 1024, "type=\"normalLog\"\tlevel=%d\tfile=\"%s\"\tfunc=\"%s\"\tcontent=\"%d, LOG_Stop! Write all remaining logs to cache. \"", s32Level, __FILE__, __FUNCTION__, __LINE__);
	szFiniBuf[1023] = '\0';
	log_WriteLog(s32Level, szFiniBuf);

    s32Ret = pthread_join(thread1, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

	if (!m_stLogInfo.bUpdateLog)
	{
		s32Ret = pthread_join(thread2, &pvRetval);
		if (0 != s32Ret)
		{
			print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
			return SV_FAILURE;
		}
	}

    m_stLogInfo.s32RecvTid = 0;
    m_stLogInfo.s32KmsgTid = 0;
    m_stLogInfo.bStarted = SV_FALSE;

    return SV_SUCCESS;
}

void LOG_Submit(sint32 s32Level, char *pszLog)
{
    sint32 s32Ret = 0;
    static uint16 u16Serial = 0;
    MSG_BUF_S stMsgBuf;
    char szBuf[4096];

    if (NULL == pszLog || !m_stLogInfo.bStarted)
    {
        return;
    }

    strncpy(szBuf, pszLog, 4096);
    stMsgBuf.u16DestId = EP_LOG;
    stMsgBuf.u16SrcId = EP_ALL;
    stMsgBuf.u16OpCode = OP_EVENT_LOG;
    stMsgBuf.s32Param = s32Level;
    stMsgBuf.u16Serial = u16Serial;
    s32Ret = MSG_Write(m_stLogInfo.s32LogQueId, &stMsgBuf, szBuf, strlen(szBuf)+1);
    if (SV_SUCCESS != s32Ret)
    {
		printf("MSG_Write failed. [err=%#x]\n", s32Ret);
        return;
    }
    u16Serial++;
}

sint32 LOG_SetConfig(CFG_SER_PARAM *pstSerParam)
{
    if (NULL == pstSerParam)
    {
        return ERR_NULL_PTR;
    }

    m_stLogInfo.s32RecordLevel = pstSerParam->s32RecordLevel;
    m_stLogInfo.s32UploadLevel = pstSerParam->s32UploadLevel;

    return SV_SUCCESS;
}

#if (defined(BOARD_ADA47V1))
static SV_BOOL m_bColumnExist = SV_FALSE;
static uint8 m_u8UploadFlag = 0x0;

int log_db_upload_callback(void *arg, int column_size, char *column_value[], char *column_name[])
{
	int i;

	for(i = 0; i < column_size; i++)
	{
		if (0 == strcmp("uploadMask", column_name[i]))
		{
		    m_u8UploadFlag = atoi(column_value[i]);
		    break;
		}
	}

	return 0;
}

int log_db_callback(void *arg, int column_size, char *column_value[], char *column_name[])
{
	int i;

	for(i = 0; i < column_size; i++)
	{
		if (0 == strcmp((char *)arg, column_name[i]))
		{
		    m_bColumnExist = SV_TRUE;
		    break;
		}
	}

	return 0;
}

SV_BOOL log_db_IsExistColumn(sqlite3 *pstDB, char *pszName)
{
    sint32 s32Ret = 0;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    char *pszErrMsg = NULL;
    char szSQL[256];

    if (NULL == pstDB || NULL == pszName)
    {
        return SV_FALSE;
    }

    m_bColumnExist = SV_FALSE;
    sprintf(szSQL, "select * from events where id = '1'");
    s32Ret = sqlite3_exec(pstDB, szSQL, log_db_callback, pszName, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "select:%s\n", sqlite3_errmsg(pstDB));
        return SV_FALSE;
    }

    return m_bColumnExist;
}

sint32 LOG_InserDbLog(time_t tTime, int mainType, int subType, char *pszContent)
{
    sint32 s32Ret = 0;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    sqlite3 *pstDB = NULL;
    char *pszErrMsg = NULL;
    static char szSQL[256];
    struct timeval tvNow;
    struct timezone tz;
    struct tm stTime = {0};
    struct tm *pstTime = &stTime;
    static char szDateTime[32];

    if (NULL == pszContent)
    {
        return -1;
    }

    if (!STORAGE_IsWritable(LOG_DB_STORAGE))
    {
        return -1;
    }

    pthread_mutex_lock(&m_stLogInfo.mutexLockDB);
    s32Ret = sqlite3_open(pszDbName, &pstDB);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "SQLite error: %s\n", sqlite3_errmsg(pstDB));
        goto err;
    }

    // 建表
    sprintf(szSQL, "create table if not exists events ("
                "'id' INTEGER PRIMARY KEY AUTOINCREMENT, "
                "'time' datetime not null, "
                "'mainType' int(1) not null, "
                "'subType' int(1) not null, "
                "'uploadMask' int(1), "
                "'content' varchar(256) default '', "
                "'picturePath' varchar(256) default '', "
                "'videoPath' varchar(256) default '')");
    s32Ret = sqlite3_exec(pstDB, szSQL, NULL, NULL, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "create table:%s\n", pszErrMsg);
        sqlite3_close(pstDB);
        goto err;
    }

    if (!m_stLogInfo.bCreateUploadMask)
    {
        if (!log_db_IsExistColumn(pstDB, "uploadMask"))
        {
            sprintf(szSQL, "alter table events add column uploadMask int(1)");
            s32Ret = sqlite3_exec(pstDB, szSQL, NULL, NULL, &pszErrMsg);
            if(SQLITE_OK != s32Ret)
            {
                print_level(SV_ERROR, "alter table:%s\n", pszErrMsg);
            }
        }
        m_stLogInfo.bCreateUploadMask = SV_TRUE;
    }

    gettimeofday(&tvNow, &tz);
    tTime += (tz.tz_minuteswest * 60);
    gmtime_r(&tTime, pstTime);
    sprintf(szDateTime, "%04d-%02d-%02d %02d:%02d:%02d", pstTime->tm_year+1900, pstTime->tm_mon+1, pstTime->tm_mday, pstTime->tm_hour, pstTime->tm_min, pstTime->tm_sec);

    // 插入数据
    sprintf(szSQL, "insert into events (time, mainType, subType, uploadMask, content) values ('%s', '%d', '%d', '0', '%s')", szDateTime, mainType, subType, pszContent);
    //print_level(SV_DEBUG, "szSQL: %s\n", szSQL);
    s32Ret = sqlite3_exec(pstDB, szSQL, NULL, NULL, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "insert into:%s\n", pszErrMsg);
        sqlite3_close(pstDB);
        goto err;
    }

    s32KeyId = sqlite3_last_insert_rowid(pstDB);
    sqlite3_close(pstDB);
    sync();
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return s32KeyId;

err:
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return -1;
}

sint32 LOG_UpdateDbPicturePath(uint32 u32Id, char *pszPicturePath)
{
    sint32 s32Ret = 0;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    sqlite3 *pstDB = NULL;
    char *pszErrMsg = NULL;
    char szSQL[256];

    if (NULL == pszPicturePath)
    {
        return -1;
    }

    if (!STORAGE_IsWritable(LOG_DB_STORAGE))
    {
        return -1;
    }

    pthread_mutex_lock(&m_stLogInfo.mutexLockDB);
    s32Ret = sqlite3_open(pszDbName, &pstDB);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "SQLite error: %s\n", sqlite3_errmsg(pstDB));
        goto err;
    }

    sprintf(szSQL, "update events set picturePath = '%s' where id = '%d'", pszPicturePath, u32Id);
    //print_level(SV_DEBUG, "szSQL: %s\n", szSQL);
    s32Ret = sqlite3_exec(pstDB, szSQL, NULL, NULL, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "update into:%s\n", pszErrMsg);
        sqlite3_close(pstDB);
        goto err;
    }

    sqlite3_close(pstDB);
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return 0;

err:
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return -1;
}

sint32 LOG_UpdateDbVideoPath(uint32 u32Id, char *pszVideoPath)
{
    sint32 s32Ret = 0;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    sqlite3 *pstDB = NULL;
    char *pszErrMsg = NULL;
    char szSQL[256];

    if (NULL == pszVideoPath)
    {
        return -1;
    }

    if (!STORAGE_IsWritable(LOG_DB_STORAGE))
    {
        return -1;
    }

    pthread_mutex_lock(&m_stLogInfo.mutexLockDB);
    s32Ret = sqlite3_open(pszDbName, &pstDB);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "SQLite error: %s\n", sqlite3_errmsg(pstDB));
        goto err;
    }

    sprintf(szSQL, "update events set videoPath = '%s' where id = '%d'", pszVideoPath, u32Id);
    //print_level(SV_DEBUG, "szSQL: %s\n", szSQL);
    s32Ret = sqlite3_exec(pstDB, szSQL, NULL, NULL, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "update into:%s\n", pszErrMsg);
        sqlite3_close(pstDB);
        goto err;
    }

    sqlite3_close(pstDB);
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return 0;

err:
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return -1;
}

sint32 LOG_QueryDbItem(uint32 u32Id, void *pvData, int (*callback)(void*,int,char**,char**))
{
    sint32 s32Ret = 0;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    sqlite3 *pstDB = NULL;
    char *pszErrMsg = NULL;
    char szSQL[256];


    if (NULL == callback)
    {
        return -1;
    }

    if (!STORAGE_IsWritable(LOG_DB_STORAGE))
    {
        return -1;
    }

    pthread_mutex_lock(&m_stLogInfo.mutexLockDB);
    s32Ret = sqlite3_open(pszDbName, &pstDB);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "SQLite error: %s\n", sqlite3_errmsg(pstDB));
        goto err;
    }

    sprintf(szSQL, "select * from events where id = '%d'", u32Id);
    s32Ret = sqlite3_exec(pstDB, szSQL, callback, pvData, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "select:%s\n", sqlite3_errmsg(pstDB));
        sqlite3_close(pstDB);
        goto err;
    }

    sqlite3_close(pstDB);
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return 0;

err:
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return -1;
}

sint32 LOG_QueryDbItems(time_t tBeginTime, time_t tEndTime, int mainType, int subType, void *pvData, int (*callback)(void*,int,char**,char**))
{
    sint32 s32Ret = 0;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    sqlite3 *pstDB = NULL;
    char *pszErrMsg = NULL;
    char szSQL[256];
    struct timeval tvNow;
    struct timezone tz;
    struct tm stTime = {0};
    struct tm *pstTime = &stTime;
    char szBeginTime[32];
    char szEndTime[32];

    if (NULL == callback || (tBeginTime != 0 && tEndTime != 0 && tBeginTime > tEndTime))
    {
        return -1;
    }

    if (!STORAGE_IsWritable(LOG_DB_STORAGE))
    {
        return -1;
    }

    pthread_mutex_lock(&m_stLogInfo.mutexLockDB);
    s32Ret = sqlite3_open(pszDbName, &pstDB);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "SQLite error: %s\n", sqlite3_errmsg(pstDB));
        goto err;
    }

    gettimeofday(&tvNow, &tz);
    tBeginTime += (tz.tz_minuteswest * 60);
    tEndTime += (tz.tz_minuteswest * 60);
    gmtime_r(&tBeginTime, pstTime);
    sprintf(szBeginTime, "%04d-%02d-%02d %02d:%02d:%02d", pstTime->tm_year+1900, pstTime->tm_mon+1, pstTime->tm_mday, pstTime->tm_hour, pstTime->tm_min, pstTime->tm_sec);
    gmtime_r(&tEndTime, pstTime);
    sprintf(szEndTime, "%04d-%02d-%02d %02d:%02d:%02d", pstTime->tm_year+1900, pstTime->tm_mon+1, pstTime->tm_mday, pstTime->tm_hour, pstTime->tm_min, pstTime->tm_sec);
    if (mainType != 0 && subType != 0)
    {
        sprintf(szSQL, "select * from events where time between '%s' and '%s' and mainType=%d and subType=%d", szBeginTime, szEndTime, mainType, subType);
    }
    else if (mainType != 0 && subType == 0)
    {
        sprintf(szSQL, "select * from events where time between '%s' and '%s' and mainType=%d", szBeginTime, szEndTime, mainType);
    }
    else if (mainType == 0 && subType != 0)
    {
        sprintf(szSQL, "select * from events where time between '%s' and '%s' and subType=%d", szBeginTime, szEndTime, subType);
    }
    else
    {
        sprintf(szSQL, "select * from events where time between '%s' and '%s'", szBeginTime, szEndTime);
    }

    s32Ret = sqlite3_exec(pstDB, szSQL, callback, pvData, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "select:%s\n", sqlite3_errmsg(pstDB));
        sqlite3_close(pstDB);
        goto err;
    }

    sqlite3_close(pstDB);
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return 0;

err:
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return -1;
}

sint32 LOG_QueryDbUploads(uint8 u8Flag, void *pvData, int (*callback)(void*,int,char**,char**))
{
    sint32 s32Ret = 0, i;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    sqlite3 *pstDB = NULL;
    char *pszErrMsg = NULL;
    char szSQL[256];
    uint8 au8Flags[7] = {0x1, 0x2, 0x4, 0x3, 0x5, 0x6, 0x7};
    char szZone[64] = {0};
    char szTmp[16];

    if (0 == u8Flag || NULL == callback)
    {
        return -1;
    }

    if (!STORAGE_IsWritable(LOG_DB_STORAGE))
    {
        return -1;
    }

    pthread_mutex_lock(&m_stLogInfo.mutexLockDB);
    s32Ret = sqlite3_open(pszDbName, &pstDB);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "SQLite error: %s\n", sqlite3_errmsg(pstDB));
        goto err;
    }

    for (i = 0; i < 7; i++)
    {
        if (au8Flags[i] & u8Flag)
        {
            sprintf(szTmp, "%d,", au8Flags[i]);
            strcat(szZone, szTmp);
        }
    }

    szZone[strlen(szZone)-1] = '\0';
    sprintf(szSQL, "select * from events where uploadMask not in(%s)", szZone);
    s32Ret = sqlite3_exec(pstDB, szSQL, callback, pvData, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        //print_level(SV_ERROR, "select:%s\n", sqlite3_errmsg(pstDB));
        sqlite3_close(pstDB);
        goto err;
    }

    sqlite3_close(pstDB);
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return 0;

err:
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return -1;
}

sint32 LOG_UpdateDbUploaed(uint32 u32Id, uint8 u8Flag)
{
    sint32 s32Ret = 0;
    sint32 s32KeyId = -1;
    const char *pszDbName = LOG_DB_PATH;
    sqlite3 *pstDB = NULL;
    char *pszErrMsg = NULL;
    char szSQL[256];

    if (0 == u8Flag)
    {
        return -1;
    }

    if (!STORAGE_IsWritable(LOG_DB_STORAGE))
    {
        return -1;
    }

    pthread_mutex_lock(&m_stLogInfo.mutexLockDB);
    s32Ret = sqlite3_open(pszDbName, &pstDB);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "SQLite error: %s\n", sqlite3_errmsg(pstDB));
        goto err;
    }

    m_u8UploadFlag = 0x0;
    sprintf(szSQL, "select * from events where id = '%d'", u32Id);
    s32Ret = sqlite3_exec(pstDB, szSQL, log_db_upload_callback, NULL, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "select:%s\n", sqlite3_errmsg(pstDB));
        return SV_FALSE;
    }

    m_u8UploadFlag |= u8Flag;
    sprintf(szSQL, "update events set uploadMask = '%d' where id = '%d'", m_u8UploadFlag, u32Id);
    //print_level(SV_DEBUG, "szSQL: %s\n", szSQL);
    s32Ret = sqlite3_exec(pstDB, szSQL, NULL, NULL, &pszErrMsg);
    if(SQLITE_OK != s32Ret)
    {
        print_level(SV_ERROR, "update into:%s\n", pszErrMsg);
        sqlite3_close(pstDB);
        goto err;
    }

    sqlite3_close(pstDB);
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return 0;

err:
    pthread_mutex_unlock(&m_stLogInfo.mutexLockDB);
    return -1;
}
#endif
