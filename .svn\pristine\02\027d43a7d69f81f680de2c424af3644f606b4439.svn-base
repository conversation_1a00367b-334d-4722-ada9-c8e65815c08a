/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名: librtsp2.cpp

作者: 许家铭    版本: v1.0.0(初始版本号)    日期: 2020-04-30

文件功能描述: RTSP服务器端功能定义

版本: v1.0.0(最新版本号)

历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <errno.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <time.h>

#define COM_MOD_ID  COM_ID_IPSERVER

#include "common.h"
#include "print.h"
#include "op.h"
#include "msg.h"
#include "sharefifo.h"
#include "cJSON.h"
#include "xop/RtspServer.h"
#include "net/Timer.h"
#include "librtsp.h"
#include "board.h"

extern "C"
{
#include "libavformat/avformat.h"
#include "libavformat/avio.h"
#include "libavformat/movenc.h"
#include "libavformat/avcommon.h"
}
/* 模块控制信息 */
typedef struct tag_rtspSvrComInfo_S
{
    uint32      u32MainFramerate;       /* 主码流帧率 */
    SV_SIZE_S   stMainResolution;       /* 主码流分辨率 */
    ENCODE_E    enMainEncode;           /* 主码流编码格式 */
    uint32      u32SubFramerate;        /* 子码流帧率 */
    SV_SIZE_S   stSubResolution;        /* 子码流分辨率 */
    ENCODE_E    enSubEncode;            /* 子码流编码格式 */
    SV_SIZE_S   stPicResolution;        /* 图片流分辨率 */
    SV_BOOL     bAudioEnable;           /* 是否使能音频流 */
    AUD_ENC_E   enAudioEncType;         /* 音频编码类型[AUD_ENC_E] */
    AUD_SR_E    enAudioSampleRate;      /* 音频采样率 */
    uint32      u32ServicePort;         /* 服务端口 */
	char 		szServiceMainUri[64];		/* 主码流uri */
	char 		szServiceSubUri[64];		/* 子码流uri */
	char 		szServicePicUri[64];		/* 图片流uri */
    std::shared_ptr<xop::EventLoop>   pcsEventLoop; /* RtspServer 消息体 */
    std::shared_ptr<xop::RtspServer>  pcsRtspServer;/* RtspServer 实例 */
    xop::MediaSessionId u32MainSessionId;/* 主码流会话ID */
    xop::MediaSessionId u32SubSessionId;/* 子码流会话ID */
    xop::MediaSessionId u32PicSessionId;/* 图片流会话ID */
	xop::MediaSessionId u32PlaybackSessionId;/* 回放流会话ID */
    xop::MediaExtraData stMainExtraData;/* 主码流额外信息 */
    xop::MediaExtraData stSubExtraData; /* 主码流额外信息 */
	xop::MediaExtraData stPlaybackExtraData; /* 回放流额外信息 */
	xop::H264Source *pcsPlaybackSource; /* 回放流视频源 */
	 char        szPlaybackFilePath[256];/* 当前请求回放流的文件路径 */
    uint32      u32MainClients;         /* 正在请求主码流的客户端数目 */
    uint32      u32SubClients;          /* 正在请求子码流的客户端数目 */
    uint32      u32PicClients;          /* 正在请求图片流的客户端数目 */
	uint32      u32PlaybackClients;     /* 正在请求回放流的客户端数目 */
    SV_BOOL     bRtspStreamPause;       /* rtsp数据流暂时 */
    uint32      u32TidService;          /* 服务端线程ID */
    uint32      u32TidStatus;           /* 状态信息线程ID */
	uint32      u32TidPlayback;         /* 回放流服务线程ID */
    SV_BOOL     bRunning;               /* 线程是否正在运行 */
    char        eventLoopWatchVariable; /* live555 循环监测变量(非0退出) */
} RTSP_COM_INFO_S;

RTSP_COM_INFO_S m_stRtspInfo;           /* 模块控制信息 */

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
#define EXTRA_HEAD_FLAG_LEN     12      // H264码流slice标识头，00 00 00 01，共12个字节
#else
#define EXTRA_HEAD_FLAG_LEN     16
#endif
#define EXTRA_HEAD_FLAG_VPS_LEN 4

static void sendRtspLog(char const *pszClientIp, char const *pszEvent, char const *pszStream)
{
    char logLine[1024];
    snprintf(logLine, 1024, "type=\"RTSP\"\tclient=\"%s\"\tevent=\"%s\"\tstream=\"%s\"", pszClientIp, pszEvent, pszStream);
    LOG_Submit(-1, logLine);
}

uint32_t rtsp_findMjpgData(unsigned char *data, uint32_t length)
{
    uint32_t i = 0;
    uint32_t tmpLen = 0;
    static uint32_t pos = 0;

    if(pos != 0)
        return pos;

    while(i < length - 1)
    {
        if(data[i] == 0xff && data[i+1] == 0xda)
            break;

        i++;
    }

    tmpLen = data[i+2] << 4 | data[i+3];

    pos = i+2+tmpLen;

    return pos;
}


static uint32 transSampleRate2Num(AUD_SR_E enSampleRate)
{
    uint32 u32SampleRate = 0;

    switch (enSampleRate)
    {
        case AUD_SR_8K:
            u32SampleRate = 8000;
            break;
        case AUD_SR_16K:
            u32SampleRate = 16000;
            break;
        case AUD_SR_32K:
            u32SampleRate = 32000;
            break;
    }

    return u32SampleRate;
}

void * rtsp_Service_Body(void *pvArg)
{
    sint32 s32Ret = 0, i;
    sint32 s32MainQueId, s32SubQueId, s32PicQueId;
    sint32 s32MainConsumerId, s32SubConsumerId, s32PicConsumerId;
    uint32 u32ExtraSize = 0;
    uint32 u32MainSerial = 0;
    uint32 u32SubSerial = 0;
    uint32 u32PicSerial = 0;
    SV_BOOL bMainStarted = SV_FALSE, bSubStarted = SV_FALSE, bPicStarted = SV_FALSE;
    SV_BOOL bMainFirstFrm = SV_FALSE, bSubFirstFrm = SV_FALSE, bPicFirstFrm = SV_FALSE;
    SFIFO_MSHEAD *pstPacket = NULL;
	struct timeval presentationTime = {0};
    xop::AVFrame stMainFrame = {0};
    xop::AVFrame stSubFrame = {0};
    xop::AVFrame stTmpFrame = {0};
    xop::AVFrame stPicFrame = {0};
    uint8 *pau8Tmp = NULL;
    uint8 *pu8BufVideo = NULL;
    uint8 *pu8SubVideo = NULL;
    uint8 *pu8PicVideo = NULL;
    uint32 MaxPacketSize = 501*1024;
    RTSP_COM_INFO_S *pstRtspInfo = (RTSP_COM_INFO_S *)pvArg;
    uint64 u64LastPts = 0;
    int iPos = 0;

    s32Ret = prctl(PR_SET_NAME, "rtsp_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    pu8BufVideo = malloc(501*1024);
    if (NULL == pu8BufVideo)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }

    pau8Tmp = malloc(1024);
    if (NULL == pau8Tmp)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }

    stMainFrame.buffer.reset(pu8BufVideo);
    stTmpFrame.buffer.reset(pau8Tmp);

#if (!defined(BOARD_WFCR20S2))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
    if (BOARD_WFTR20S3_V1 == BOARD_GetVersion())
    {
        goto skip1;
    }
#endif

    pu8SubVideo = malloc(501*1024);
    if (NULL == pu8SubVideo)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        free(pu8BufVideo);
        return NULL;
    }

    stSubFrame.buffer.reset(pu8SubVideo);
skip1:;
#endif

#if (defined(BOARD_IPCR20S3))
    pu8PicVideo = malloc(501*1024);
    if (NULL == pu8PicVideo)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }

    stPicFrame.buffer.reset(pu8PicVideo);
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32E1) || defined(BOARD_ADA900V1))
    uint8_t* pu8BufHeader = malloc(1024);
    if (NULL == pu8BufVideo)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }
    stMainFrame.header.reset(pu8BufHeader);
#endif

    while (pstRtspInfo->bRunning)
    {
        //print_level(SV_DEBUG, "rtsp_Service_Body running... [clients:%d]\n", pstRtspInfo->u32MainClients);
        if (0 == pstRtspInfo->u32MainClients && 0 == pstRtspInfo->u32SubClients && \
            0 == pstRtspInfo->u32PicClients)
        {
            sleep_ms(100);
            if (bMainStarted)
            {
                SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
                bMainStarted = SV_FALSE;
                u64LastPts = 0;
            }

            if (bSubStarted)
            {
                SFIFO_ForReadClose(s32SubQueId, s32SubConsumerId);
                bSubStarted = SV_FALSE;
            }

            if (bPicStarted)
            {
                SFIFO_ForReadClose(s32PicQueId, s32PicConsumerId);
                bPicStarted = SV_FALSE;
            }

            continue;
        }

        if (0 != pstRtspInfo->u32MainClients)
        {
            if (!bMainStarted)
            {
                s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed[%#x].\n", SFIFO_MAIN_STREAM, s32Ret);
                    sleep_ms(1);
                    goto substream;
                }
                else
                {
                    bMainStarted = SV_TRUE;
                    bMainFirstFrm = SV_TRUE;
                }
            }

            if (bMainStarted)
            {
                s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
                    sleep_ms(1);
                    goto substream;
                }
                //print_level(SV_WARN, "%lld\n", pstPacket->pts);
#if (defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_WFCR20S2))
                if ((!pstRtspInfo->bAudioEnable || ENCODE_MJPEG == pstRtspInfo->enMainEncode) && pstPacket->type == 2)
                {
                    SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                    goto substream;
                }
#else
                if (!pstRtspInfo->bAudioEnable && pstPacket->type == 2)
                {
                    SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                    goto substream;
                }
#endif
                //print_level(SV_WARN, "pstPacket->type = %d, pstPacket->msdsize = %d\n", pstPacket->type, pstPacket->msdsize);

                #if 0
                if (u64LastPts != 0)
                {
                    print_level(SV_INFO, "interval: %d, now: %lld, last: %lld\n", pstPacket->pts - u64LastPts, pstPacket->pts, u64LastPts);
                }
                u64LastPts = pstPacket->pts;
                #endif

#ifndef PLATFORM_RV1106
                if (pstPacket->type != 2)  // Debug for frame serial number
                {
                    //print_level(SV_WARN, "%d\n", pstPacket->algorithm);
                    //print_level(SV_DEBUG, "FrameType: %d(%dx%d) pts: %lld, serial: %d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->serial);
                    if (pstPacket->serial != u32MainSerial + 1)
                    {
                        print_level(SV_WARN, "Abnormal Mainstream Frame Serial Number. Present Frame: %d, Last Frame: %d.\n", pstPacket->serial, u32MainSerial);
                    }
                    u32MainSerial = pstPacket->serial;
                }
#endif

                presentationTime.tv_sec = pstPacket->pts / 1000000ll;
				presentationTime.tv_usec = pstPacket->pts % 1000000ll;
                if (bMainFirstFrm)
                {
                    if (pstPacket->type != 1)
                    {
                        print_level(SV_WARN, "Get first main frame invalid. [type=%d]\n", pstPacket->type);
                        SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                        goto substream;
                    }
                    bMainFirstFrm = SV_FALSE;
                }

                stMainFrame.type = pstPacket->type == 2 ? xop::AUDIO_FRAME : (pstPacket->type == 1 ? xop::VIDEO_FRAME_I : xop::VIDEO_FRAME_P);
    			stMainFrame.size = pstPacket->msdsize;
                //print_level(SV_DEBUG, "stMainFrame.size = %d\n", stMainFrame.size);
				stMainFrame.presentationTime = presentationTime;
    			switch (stMainFrame.type)
                {
                    case xop::VIDEO_FRAME_I:
                        if(pstRtspInfo->enMainEncode == ENCODE_H265)
                        {
                            u32ExtraSize = pstRtspInfo->stMainExtraData.vps_size + pstRtspInfo->stMainExtraData.sps_size + pstRtspInfo->stMainExtraData.pps_size + pstRtspInfo->stMainExtraData.sei_size;
                            stMainFrame.size -= (u32ExtraSize + EXTRA_HEAD_FLAG_LEN + EXTRA_HEAD_FLAG_VPS_LEN);

#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
                            if (stMainFrame.size > MaxPacketSize)
                            {
                                stMainFrame.buffer.reset();
                                pu8BufVideo = malloc(stMainFrame.size + 100*1024);
                                MaxPacketSize = stMainFrame.size + 100*1024;
                                stMainFrame.buffer.reset(pu8BufVideo);
                            }
#endif

                            memcpy(stMainFrame.buffer.get(), pstPacket->data+(u32ExtraSize+EXTRA_HEAD_FLAG_LEN+EXTRA_HEAD_FLAG_VPS_LEN), stMainFrame.size);
                        }
                        else if (pstRtspInfo->enMainEncode == ENCODE_H264)
                        {
                            u32ExtraSize = pstRtspInfo->stMainExtraData.sps_size + pstRtspInfo->stMainExtraData.pps_size + pstRtspInfo->stMainExtraData.sei_size;
                            //print_level(SV_INFO, "get u32ExtraSize: %d %d %d %d\n", stMainFrame.size, pstRtspInfo->stMainExtraData.sps_size, pstRtspInfo->stMainExtraData.pps_size, pstRtspInfo->stMainExtraData.sei_size);
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
                            if (stMainFrame.size > MaxPacketSize)
                            {
                                stMainFrame.buffer.reset();
                                pu8BufVideo = malloc(stMainFrame.size + 100*1024);
                                MaxPacketSize = stMainFrame.size + 100*1024;
                                stMainFrame.buffer.reset(pu8BufVideo);
                            }
#endif
                            stMainFrame.size -= (u32ExtraSize + EXTRA_HEAD_FLAG_LEN);
                            memcpy(stMainFrame.buffer.get(), pstPacket->data+(u32ExtraSize+EXTRA_HEAD_FLAG_LEN), stMainFrame.size);
                        }
                        else if (pstRtspInfo->enMainEncode == ENCODE_MJPEG)
                        {
#if (!defined(BOARD_IPCR20S4) &&!defined(BOARD_WFCR20S2)) // 335DE ipc 使用CBR时计算q值有问题，不能跳过头部
                            iPos = rtsp_findMjpgData((unsigned char *)pstPacket->data, pstPacket->msdsize);
#endif
                            stMainFrame.size = pstPacket->msdsize - iPos;
#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32E1) || defined(BOARD_ADA900V1))
                            memcpy(stMainFrame.header.get(), pstPacket->data, iPos);
#endif
                            stMainFrame.ptrData = pstPacket->data + iPos;
                        }
                        break;
                    case xop::VIDEO_FRAME_P:
                        if (stMainFrame.size <= MaxPacketSize)
                        {
                            if (stMainFrame.size > 4)
                            {
                                stMainFrame.size -= 4;
                                memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
                            }
                            else
                            {
                                memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
                            }
                        }
                        break;
                    case xop::AUDIO_FRAME:
                        memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
                        break;
                }

                s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "s32Ret = %#x\n", s32Ret);
    				print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %s]\n", strerror(errno));
                }
#if 1
                /* 拆包单独发送VPS,SPS,PPS,SEI */
                if (pstRtspInfo->enMainEncode != ENCODE_MJPEG)
                {
                    if (stMainFrame.type == xop::VIDEO_FRAME_I)
                    {
                        stTmpFrame.type = xop::EXTRA_DATA;
    					stTmpFrame.presentationTime = stMainFrame.presentationTime;
                        if (pstRtspInfo->enMainEncode == ENCODE_H265)
                        {
                            stTmpFrame.size = pstRtspInfo->stMainExtraData.vps_size;
                            memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stMainExtraData.vps_data, stTmpFrame.size);
                            pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32MainSessionId, xop::channel_0, stTmpFrame);
                        }
                        stTmpFrame.size = pstRtspInfo->stMainExtraData.sps_size;
                        memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stMainExtraData.sps_data, stTmpFrame.size);
                        pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32MainSessionId, xop::channel_0, stTmpFrame);
                        stTmpFrame.size = pstRtspInfo->stMainExtraData.pps_size;
                        memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stMainExtraData.pps_data, stTmpFrame.size);
                        pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32MainSessionId, xop::channel_0, stTmpFrame);
#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))    // RK平台设备没有sei信息
                        stTmpFrame.size = pstRtspInfo->stMainExtraData.sei_size;
                        memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stMainExtraData.sei_data, stTmpFrame.size);
                        pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32MainSessionId, xop::channel_0, stTmpFrame);
#endif
                    }
                }
#endif
                //print_level(SV_DEBUG, "type:%02x, size:%d\n", stMainFrame.type, stMainFrame.size);
                //print_level(SV_DEBUG, "pts sec: %ld, usec: %ld\n", presentationTime.tv_sec, presentationTime.tv_usec);
				#if 0
				if (pstPacket->type != 2)
                {
					print_level(SV_DEBUG, "rtsp pts: %llu\n", pstPacket->pts);
					struct timeval tv;
		    		gettimeofday(&tv, NULL);
					print_level(SV_INFO, "_now pts: %llu\n", tv.tv_sec*1000000ll+tv.tv_usec);
				}
				#endif

                if (pstRtspInfo->bAudioEnable || stMainFrame.type != xop::AUDIO_FRAME)
                {
                    pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32MainSessionId, (stMainFrame.type != xop::AUDIO_FRAME) ? xop::channel_0 : xop::channel_1, stMainFrame);
                }
            }
        }
        else if (bMainStarted)
        {
            SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
            bMainStarted = SV_FALSE;
        }

substream:;
#if (!defined(BOARD_WFCR20S2))
        if (0 != pstRtspInfo->u32SubClients)
        {
            if (!bSubStarted)
            {
                s32Ret = SFIFO_ForReadOpen(SFIFO_SUB_STREAM, &s32SubQueId, &s32SubConsumerId);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_SUB_STREAM);
                    sleep_ms(1);
                    continue;
                }
                else
                {
                    bSubStarted = SV_TRUE;
                    bSubFirstFrm = SV_TRUE;
                }
            }

            if (bSubStarted)
            {
                s32Ret = SFIFO_GetPacket(s32SubQueId, s32SubConsumerId, &pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
                    sleep_ms(1);
                    continue;
                }
#ifndef PLATFORM_RV1106
                if (pstPacket->serial != u32SubSerial + 1)
                {
                    print_level(SV_WARN, "Abnormal Substream Frame Serial Number. Present Frame: %d, Last Frame: %d.\n", pstPacket->serial, u32SubSerial);
                }
                u32SubSerial = pstPacket->serial;
#endif

                presentationTime.tv_sec = pstPacket->pts / 1000000ll;
				presentationTime.tv_usec = pstPacket->pts % 1000000ll;
                if (bSubFirstFrm)
                {
                    if (pstPacket->type != 1)
                    {
                        print_level(SV_WARN, "Get first sub frame invalid. [type=%d]\n", pstPacket->type);
                        SFIFO_ReleasePacket(s32SubQueId, s32SubConsumerId, pstPacket);
                        continue;
                    }
                    bSubFirstFrm = SV_FALSE;
                }

                stSubFrame.type = pstPacket->type ? xop::VIDEO_FRAME_I : xop::VIDEO_FRAME_P;
    			stSubFrame.size = pstPacket->msdsize;
				stSubFrame.presentationTime = presentationTime;
    			switch (stSubFrame.type)
                {
                    case xop::VIDEO_FRAME_I:
                        if(pstRtspInfo->enSubEncode == ENCODE_H265)
                        {
                            u32ExtraSize = pstRtspInfo->stSubExtraData.vps_size + pstRtspInfo->stSubExtraData.sps_size + pstRtspInfo->stSubExtraData.pps_size + pstRtspInfo->stSubExtraData.sei_size;
                            stSubFrame.size -= (u32ExtraSize + EXTRA_HEAD_FLAG_LEN+EXTRA_HEAD_FLAG_VPS_LEN);
                            memcpy(stSubFrame.buffer.get(), pstPacket->data+(u32ExtraSize+EXTRA_HEAD_FLAG_LEN+EXTRA_HEAD_FLAG_VPS_LEN), stSubFrame.size);
                        }
                        else
                        {
                            u32ExtraSize = pstRtspInfo->stSubExtraData.sps_size + pstRtspInfo->stSubExtraData.pps_size + pstRtspInfo->stSubExtraData.sei_size;
                            stSubFrame.size -= (u32ExtraSize + EXTRA_HEAD_FLAG_LEN);
                            memcpy(stSubFrame.buffer.get(), pstPacket->data+(u32ExtraSize+EXTRA_HEAD_FLAG_LEN), stSubFrame.size);
                        }
                        break;
                    case xop::VIDEO_FRAME_P:
                        stSubFrame.size -= 4;
                        memcpy(stSubFrame.buffer.get(), pstPacket->data+4, stSubFrame.size);
                        break;
                }
                s32Ret = SFIFO_ReleasePacket(s32SubQueId, s32SubConsumerId, pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
    				print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %s]\n", strerror(errno));
                }

                /* 拆包单独发送SPS,PPS,SEI */
                if (stSubFrame.type == xop::VIDEO_FRAME_I)
                {
                    stTmpFrame.type = xop::EXTRA_DATA;
					stTmpFrame.presentationTime = stSubFrame.presentationTime;
                    if(pstRtspInfo->enSubEncode == ENCODE_H265)
                    {
                        stTmpFrame.size = pstRtspInfo->stSubExtraData.vps_size;
                        memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stSubExtraData.vps_data, stTmpFrame.size);
                        pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32SubSessionId, xop::channel_0, stTmpFrame);
                    }
                    stTmpFrame.size = pstRtspInfo->stSubExtraData.sps_size;
                    memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stSubExtraData.sps_data, stTmpFrame.size);
                    pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32SubSessionId, xop::channel_0, stTmpFrame);
                    stTmpFrame.size = pstRtspInfo->stSubExtraData.pps_size;
                    memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stSubExtraData.pps_data, stTmpFrame.size);
                    pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32SubSessionId, xop::channel_0, stTmpFrame);
#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))
                    stTmpFrame.size = pstRtspInfo->stSubExtraData.sei_size;
                    memcpy(stTmpFrame.buffer.get(), pstRtspInfo->stSubExtraData.sei_data, stTmpFrame.size);
                    pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32SubSessionId, xop::channel_0, stTmpFrame);
#endif
                }

                pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32SubSessionId, xop::channel_0, stSubFrame);
            }
        }
        else if (bSubStarted)
        {
            SFIFO_ForReadClose(s32SubQueId, s32SubConsumerId);
            bSubStarted = SV_FALSE;
        }
skip2:;
#endif

#if (defined(BOARD_IPCR20S3))
        if (0 != pstRtspInfo->u32PicClients)
        {
            if (!bPicStarted)
            {
                s32Ret = SFIFO_ForReadOpen(SFIFO_PIC_STREAM, &s32PicQueId, &s32PicConsumerId);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_PIC_STREAM);
                    sleep_ms(1);
                    continue;
                }
                else
                {
                    bPicStarted = SV_TRUE;
                    bPicFirstFrm = SV_TRUE;
                }
            }

            if (bPicStarted)
            {
                s32Ret = SFIFO_GetPacket(s32PicQueId, s32PicConsumerId, &pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
                    sleep_ms(1);
                    continue;
                }
                if (pstPacket->type == 2)
                {
                    SFIFO_ReleasePacket(s32PicQueId, s32PicConsumerId, pstPacket);
                    continue;
                }

                if (pstPacket->type != 2)  // Debug for frame serial number
                {
                    //print_level(SV_WARN, "%d\n", pstPacket->algorithm);
                    //print_level(SV_DEBUG, "FrameType: %d(%dx%d) pts: %lld, serial: %d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->serial);
                    if (pstPacket->serial != u32PicSerial + 1)
                    {
                        print_level(SV_WARN, "Abnormal Picstream Frame Serial Number. Present Frame: %d, Last Frame: %d.\n", pstPacket->serial, u32MainSerial);
                    }
                    u32PicSerial = pstPacket->serial;
                }

				presentationTime.tv_sec = pstPacket->pts / 1000000ll;
				presentationTime.tv_usec = pstPacket->pts % 1000000ll;
                if (bPicFirstFrm)
                {
                    if (pstPacket->type != 1)
                    {
                        print_level(SV_WARN, "Get first pic frame invalid. [type=%d]\n", pstPacket->type);
                        SFIFO_ReleasePacket(s32PicQueId, s32PicConsumerId, pstPacket);
                        continue;
                    }
                    bPicFirstFrm = SV_FALSE;
                }
#if 0
                //iPos = rtsp_findMjpgData((unsigned char *)pstPacket->data, pstPacket->msdsize);
                stPicFrame.type = xop::VIDEO_FRAME_I;
    			stPicFrame.size = pstPacket->msdsize;
				stPicFrame.presentationTime = presentationTime;

                stPicFrame.ptrData = pstPacket->data;
                //memcpy(stPicFrame.buffer.get(), pstPacket->data, stPicFrame.size);
#else
                if (1)//(BOARD_IsCustomer(BOARD_C_IPCR20S3_PARKER))
                {
                    iPos = rtsp_findMjpgData((unsigned char *)pstPacket->data, pstPacket->msdsize);
                }
                else
                {
                    iPos = 0;
                }
                stPicFrame.type = xop::VIDEO_FRAME_I;
    			stPicFrame.size = pstPacket->msdsize - iPos;
				stPicFrame.presentationTime = presentationTime;
                stPicFrame.ptrData = pstPacket->data + iPos;
#endif
                s32Ret = SFIFO_ReleasePacket(s32PicQueId, s32PicConsumerId, pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
    				print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %s]\n", strerror(errno));
                }

                //print_level(SV_DEBUG, "type:%02x, size:%d\n", stMainFrame.type, stMainFrame.size);
                //print_level(SV_DEBUG, "pts sec: %ld, usec: %ld\n", presentationTime.tv_sec, presentationTime.tv_usec);
				#if 0
				if (pstPacket->type != 2)
                {
					print_level(SV_DEBUG, "rtsp pts: %llu\n", pstPacket->pts);
					struct timeval tv;
		    		gettimeofday(&tv, NULL);
					print_level(SV_INFO, "_now pts: %llu\n", tv.tv_sec*1000000ll+tv.tv_usec);
				}
				#endif
                pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32PicSessionId, xop::channel_0, stPicFrame);
            }
        }
        else if (bPicStarted)
        {
            SFIFO_ForReadClose(s32PicQueId, s32PicConsumerId);
            bPicStarted = SV_FALSE;
        }
#endif
    }

    if (bMainStarted)
        SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
    if (bSubStarted)
        SFIFO_ForReadClose(s32SubQueId, s32SubConsumerId);
    if (bPicStarted)
        SFIFO_ForReadClose(s32PicQueId, s32PicConsumerId);

    print_level(SV_INFO, "++++++++++++++\n");
    return NULL;
}

const int sampling_frequencies[] = {
    96000,  // 0x0
    88200,  // 0x1
    64000,  // 0x2
    48000,  // 0x3
    44100,  // 0x4
    32000,  // 0x5
    24000,  // 0x6
    22050,  // 0x7
    16000,  // 0x8
    12000,  // 0x9
    11025,  // 0xa
    8000   // 0xb
    // 0xc d e f是保留的
};
 
int adts_header(char * const p_adts_header, const int data_length,
                const int profile, const int samplerate,
                const int channels)
{
 
    int sampling_frequency_index = 3; // 默认使用48000hz
    int adtsLen = data_length + 7;
 
    int frequencies_size = sizeof(sampling_frequencies) / sizeof(sampling_frequencies[0]);
    int i = 0;
    for(i = 0; i < frequencies_size; i++)
    {
        if(sampling_frequencies[i] == samplerate)
        {
            sampling_frequency_index = i;
            break;
        }
    }
    if(i >= frequencies_size)
    {
        printf("unsupport samplerate:%d\n", samplerate);
        return -1;
    }
 
    p_adts_header[0] = 0xff;         //syncword:0xfff                          高8bits
    p_adts_header[1] = 0xf0;         //syncword:0xfff                          低4bits
    p_adts_header[1] |= (0 << 3);    //MPEG Version:0 for MPEG-4,1 for MPEG-2  1bit
    p_adts_header[1] |= (0 << 1);    //Layer:0                                 2bits
    p_adts_header[1] |= 1;           //protection absent:1                     1bit
 
    p_adts_header[2] = (profile)<<6;            //profile:profile               2bits
    p_adts_header[2] |= (sampling_frequency_index & 0x0f)<<2; //sampling frequency index:sampling_frequency_index  4bits
    p_adts_header[2] |= (0 << 1);             //private bit:0                   1bit
    p_adts_header[2] |= (channels & 0x04)>>2; //channel configuration:channels  高1bit
    p_adts_header[3] = (channels & 0x03)<<6; //channel configuration:channels 低2bits
    p_adts_header[3] |= (0 << 5);               //original：0                1bit
    p_adts_header[3] |= (0 << 4);               //home：0                    1bit
    p_adts_header[3] |= (0 << 3);               //copyright id bit：0        1bit
    p_adts_header[3] |= (0 << 2);               //copyright id start：0      1bit
    p_adts_header[3] |= ((adtsLen & 0x1800) >> 11);           //frame length：value   高2bits
    p_adts_header[4] = (uint8_t)((adtsLen & 0x7f8) >> 3);     //frame length:value    中间8bits
    p_adts_header[5] = (uint8_t)((adtsLen & 0x7) << 5);       //frame length:value    低3bits
    p_adts_header[5] |= 0x1f;                                 //buffer fullness:0x7ff 高5bits
    p_adts_header[6] = 0xfc;      //‭11111100‬       //buffer fullness:0x7ff 低6bits
    // number_of_raw_data_blocks_in_frame：
    //    表示ADTS帧中有number_of_raw_data_blocks_in_frame + 1个AAC原始帧。
 
    return 0;
}

void * rtsp_Playback_Body(void *pvArg)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    sint32 s32Ret = 0, i;
    uint32 u32ExtraSize = 0;
    uint32 u32FrmCnt = 0;
    SV_BOOL bMainFirstFrm = SV_FALSE;
    uint32 u32FrameRate = 0;
    uint32 u32AudioFrameRate = 0;
    uint32 u32StepMs = 0;
    uint32 u32AudioStepMs = 0;
	struct timeval presentationTime = {0};
    xop::AVFrame stPlayFrame = {0};
    xop::AVFrame stTmpFrame = {0};
    uint8 *pau8Tmp = NULL;
    uint8 *pu8BufVideo = NULL;
    RTSP_COM_INFO_S *pstRtspInfo = (RTSP_COM_INFO_S *)pvArg;
    
    AVFormatContext stContext = {0};
    AVFormatContext *pstContext = NULL;
	MOVContext stMov = {0};
	AVIContext stAvi = {0};
    AVFormatInternal stAvFormatInternal = {0};
    AVRational stFrameRate = {0};
    AVRational stAudioFrameRate = {0};

    s32Ret = prctl(PR_SET_NAME, "playback_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    pu8BufVideo = malloc(501*1024);
    if (NULL == pu8BufVideo)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }

    pau8Tmp = malloc(1024);
    if (NULL == pau8Tmp)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }

    stPlayFrame.buffer.reset(pu8BufVideo);
    stTmpFrame.buffer.reset(pau8Tmp);

    av_register_all();
    while (pstRtspInfo->bRunning)
    {
        //print_level(SV_DEBUG, "rtsp_Playback_Body running... [clients:%d]\n", pstRtspInfo->u32PlaybackClients);
        if (0 == pstRtspInfo->u32PlaybackClients)
        {
            sleep_ms(100);
            continue;
        }

        print_level(SV_INFO, "try to playback file: %s\n", pstRtspInfo->szPlaybackFilePath);

		memset(&stContext, 0, sizeof(stContext));
        memset(&stMov, 0, sizeof(stMov));
        memset(&stAvFormatInternal, 0, sizeof(AVFormatInternal));
		memset(&stAvi, 0, sizeof(stAvi));
        
    	stContext.max_streams = 3;
        stContext.internal = &stAvFormatInternal;
		if (NULL != strstr(pstRtspInfo->szPlaybackFilePath, "mp4"))
		{
			AVPacket stMovPkt = {0};
			memset(&stMovPkt, 0, sizeof(stMovPkt));
			s32Ret = avformat_open_input(&pstContext, pstRtspInfo->szPlaybackFilePath, NULL, NULL);
        	if (s32Ret < 0)
        	{
        		print_level(SV_ERROR, "avformat_open_input file: %s error!\n", pstRtspInfo->szPlaybackFilePath);
        		sleep_ms(1000);
                continue;
        	}

			AVBitStreamFilterContext * stH264Bsfc =  av_bitstream_filter_init("h264_mp4toannexb");
            if (!stH264Bsfc)
            {
                printf("Failed to initialize bitstream filter: h264_mp4toannexb\n");
                return -1;
            }

            stFrameRate = av_guess_frame_rate(pstContext, pstContext->streams[0], NULL);
            printf("Video Frame Rate: %d/%d (%.2f fps)\n", stFrameRate.num, stFrameRate.den, (float)stFrameRate.num / stFrameRate.den);
            u32FrameRate = (float)stFrameRate.num / stFrameRate.den;
	        if (u32FrameRate == 0 || u32FrameRate > 30)  //帧率大于30或者等于0，就限制为30，所以视频的帧率为0~30
	        {
	            u32FrameRate = 30;
	        }
            
	    	bMainFirstFrm = SV_TRUE;
	    	u32FrmCnt = 0;
	    	u32StepMs = (uint32)(1000.0 / (float)u32FrameRate); //步长，以毫秒做单位
	        while (av_read_frame(pstContext, &stMovPkt) >= 0 && 0 != pstRtspInfo->u32PlaybackClients)  //当文件还没到底且有客户端申请时
	        {	
	            //printf("[%d] stream_index:%d, flags:%d, data:%#x, size:%d, pts:%lld, dts:%lld\n", u32FrmCnt, stMovPkt.stream_index, stMovPkt.flags, stMovPkt.data, stMovPkt.size, stMovPkt.pts, stMovPkt.dts);
                if (stMovPkt.stream_index > 1)
                {
                    goto mp4_free;
                }

	            //print_level(SV_DEBUG, "[%d] stream_index:%d, flags:%d, data:%#x, size:%d, pts:%lld, dts:%lld\n", u32FrmCnt, stMovPkt.stream_index, stMovPkt.flags, stMovPkt.data, stMovPkt.size, stMovPkt.pts, stMovPkt.dts);
	            if (bMainFirstFrm)
	            {
	                if (stMovPkt.flags != 1)  //判断是否为关键帧，如果是，则后面的帧都不需要判断，继续读包传包；如果不是，丢弃掉这个包，重新开始读包
	                {
                        goto mp4_free;
	                }
	                bMainFirstFrm = SV_FALSE;
	            }

	            u32FrmCnt++;  //帧数
	            gettimeofday(&presentationTime, NULL);
                //presentationTime.tv_sec = stMovPkt.pts / 1000000ll;
			    //presentationTime.tv_usec = stMovPkt.pts % 1000000ll;
                stPlayFrame.presentationTime = presentationTime;
                    
	            if (stMovPkt.stream_index == 0)    // 视频
                {                    
                    stPlayFrame.type = stMovPkt.flags == 1 ? xop::VIDEO_FRAME_I : xop::VIDEO_FRAME_P;
                    av_bitstream_filter_filter(stH264Bsfc, pstContext->streams[0]->codec, NULL, &stMovPkt.data, &stMovPkt.size, stMovPkt.data, stMovPkt.size, 0);
    	    		stPlayFrame.size = stMovPkt.size;
    				memcpy(stPlayFrame.buffer.get(), stMovPkt.data, stMovPkt.size);
                    //print_level(SV_DEBUG, "type:%02x, timestamp:%u, size:%d\n", stPlayFrame.type, stPlayFrame.timestamp, stPlayFrame.size);
    	            pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32PlaybackSessionId, xop::channel_0, stPlayFrame);
                    sleep_ms(u32StepMs);
                }
                else    // 音频
                {
                    //printf("Audio Frame SR: %d, Chns: %d\n", pstContext->streams[1]->codecpar->sample_rate, pstContext->streams[1]->codecpar->channels);
                    char adts_header_buf[7] = {0};
                    adts_header(adts_header_buf, stMovPkt.size,
                    pstContext->streams[1]->codecpar->profile,
                    pstContext->streams[1]->codecpar->sample_rate,
                    pstContext->streams[1]->codecpar->channels);

                    stPlayFrame.type = xop::AUDIO_FRAME;
                    stPlayFrame.size = stMovPkt.size + 7;
                    memcpy(stPlayFrame.buffer.get(), adts_header_buf, 7);
                    memcpy(stPlayFrame.buffer.get() + 7, stMovPkt.data, stMovPkt.size);
                    //print_level(SV_DEBUG, "type:%02x, timestamp:%u, size:%d\n", stPlayFrame.type, stPlayFrame.timestamp, stPlayFrame.size);
    	            pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32PlaybackSessionId, xop::channel_1, stPlayFrame);
                    //sleep_ms(u32StepMs);
                }
mp4_free:
			    av_packet_unref(&stMovPkt);     //释放掉缓冲区
	        }
            print_level(SV_INFO, "playback finished.\n");
            av_bitstream_filter_close(stH264Bsfc);
			avformat_close_input(&pstContext);
		}
		else if (NULL != strstr(pstRtspInfo->szPlaybackFilePath, "avi"))
		{
			AVPacket stAviPkt = {0};
			memset(&stAviPkt, 0, sizeof(stAviPkt));

			s32Ret = avio_open(&stContext.pb, pstRtspInfo->szPlaybackFilePath, AVIO_FLAG_READ); //打开所读的AVI文件
	    	if (s32Ret  < 0)
	    	{
	    		print_level(SV_ERROR, "url_fopen file: %s error!\n", pstRtspInfo->szPlaybackFilePath);
	    		sleep_ms(1000);
	            continue;
	    	}

	    	stContext.priv_data = &stAvi;	   //将MovContext结构体变量成为AVFormatContext结构体的一部分
	    	s32Ret = avi_read_header(&stContext);  //读取数据头
	    	if (s32Ret < 0)
	    	{
	    		print_level(SV_ERROR, "mov_read_header failed. [err=%d]\n", s32Ret);
	    		//avi_read_close(&stContext);
	    		avio_close(stContext.pb);
	    		sleep_ms(1000);
	            continue;
	    	}
	        u32FrameRate = stContext.streams[0]->time_base.den;  //获取视频流的时间基准的分母，这个为帧率
	        if (u32FrameRate == 0 || u32FrameRate > 30)  //帧率大于30或者等于0，就限制为30，所以视频的帧率为0~30
	        {
	            u32FrameRate = 30;
	        }

	    	bMainFirstFrm = SV_TRUE;
	    	u32FrmCnt = 0;
	    	u32StepMs = (uint32)(1000.0 / (float)u32FrameRate); //步长，以毫秒做单位
	        while (!avio_feof(stContext.pb) && 0 != pstRtspInfo->u32PlaybackClients)  //当文件还没到底且有客户端申请时
	        {
	            s32Ret = avi_read_packet(&stContext, &stAviPkt);  //读包
	    		if (s32Ret < 0)
	    		{
	    			print_level(SV_WARN, "Avi_read_packet failed!\n");
                    goto avi_free;
	    			break;
	    		}

				if(stAviPkt.stream_index >= 1)
				{
                    goto avi_free;
				}

                //print_level(SV_DEBUG, "[%d] stream_index:%d, flags:%d, data:%#x, size:%d, pts:%lld, dts:%lld\n", u32FrmCnt, stAviPkt.stream_index, stAviPkt.flags, stAviPkt.data, stAviPkt.size, stAviPkt.pts, stAviPkt.dts);
	            if (bMainFirstFrm)
	            {
	                if (stAviPkt.flags != 1)  //判断是否为关键帧，如果是，则后面的帧都不需要判断，继续读包传包；如果不是，丢弃掉这个包，重新开始读包
	                {
                        goto avi_free;
	                }
	                bMainFirstFrm = SV_FALSE;
	            }

	            u32FrmCnt++;  //帧数
	            stPlayFrame.type = stAviPkt.flags == 1 ? xop::VIDEO_FRAME_I : xop::VIDEO_FRAME_P;   //如果是I帧，stPlayFrame的type将其记录为VIDEO_FRAME_I，否则则记录为VIDEO_FRAME_P
	    		stPlayFrame.size = stAviPkt.size;   //xop::AVFrame的变量的size元素是读取到的packet的大小
				gettimeofday(&presentationTime, NULL);  //获取当前时间
				stPlayFrame.presentationTime = presentationTime;        //用上述函数得出的时间作为加入RTCP协议后用的时间戳

				memcpy(stPlayFrame.buffer.get(), stAviPkt.data, stAviPkt.size);     //手动修改读出来的I帧
				pstRtspInfo->pcsRtspServer->PushFrame(pstRtspInfo->u32PlaybackSessionId, xop::channel_0, stPlayFrame);
				sleep_ms(u32StepMs);//休眠这么多个步长时间

avi_free:
				av_packet_unref(&stAviPkt);
	        }
			avi_read_close(&stContext);
            avio_close(stContext.pb);       //关闭文件
		}

        pstRtspInfo->u32PlaybackClients = 0;        //重新观察客户端的数量
    }
#endif
    return NULL; //客户端为0时就结束了
}

void * rtsp_Status_Body(void *pvArg)
{
    sint32 s32Ret = 0, i;
    sint32 s32Fd = -1;
    sint32 s32ClientNum = 0;
    //CONNECTION_INFO_S astConnectionInfo[32];
    RTSP_COM_INFO_S *pstRtspInfo = (RTSP_COM_INFO_S *)pvArg;
    cJSON *pstJson = NULL, *pstClientList = NULL, *pstClientInfo = NULL;
    char szClientAddr[64];
    char szBuf[5*1024];

    s32Ret = prctl(PR_SET_NAME, "rtsp_status");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return NULL;
    }

    while (pstRtspInfo->bRunning)
    {
        //print_level(SV_DEBUG, "rtsp_Status_Body running...\n");
        sleep_ms(5000);

        if (!pstRtspInfo->bRtspStreamPause)
        {
            cJSON_DeleteItemFromObject(pstJson, "clientList");
            pstClientList = cJSON_CreateArray();
            if (NULL == pstClientList)
            {
                print_level(SV_ERROR, "cJSON_CreateArray failed.\n");
                return NULL;
            }

            cJSON_AddItemToObject(pstJson, "clientList", pstClientList);
#if 0
            memset(astConnectionInfo, 0, sizeof(astConnectionInfo));
            pstRtspInfo->pcsRtspLiveServer->lookupClientConnectionsList(&s32ClientNum, astConnectionInfo);
            for (i = 0; i < s32ClientNum; i++)
            {
                //print_level(SV_DEBUG, "%s:%d - %s\n", AddressString(astConnectionInfo[i].clientAddr).val(), \
                            AddressString(astConnectionInfo[i].clientAddr).port(), astConnectionInfo[i].streamName);
                pstClientInfo = cJSON_CreateObject();
                if (NULL == pstClientInfo)
                {
                    print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
                    continue;
                }
                sprintf(szClientAddr, "%s:%d", AddressString(astConnectionInfo[i].clientAddr).val(), AddressString(astConnectionInfo[i].clientAddr).port());
                cJSON_AddItemToObject(pstClientInfo, "clientAddr", cJSON_CreateString(szClientAddr));
                cJSON_AddItemToObject(pstClientInfo, "streamName", cJSON_CreateString(astConnectionInfo[i].streamName));
                cJSON_AddItemToArray(pstClientList, pstClientInfo);
            }
#endif
            memset(szBuf, 0, 5*1024);
            cJSON_PrintPreallocated(pstJson, szBuf, 5*1024, 0);
            s32Fd = open("/var/info/rtsp-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "open file: /var/info/rtsp-tmp failed. [err:%s]\n", strerror(errno));
                continue;
            }

            s32Ret = write(s32Fd, szBuf, strlen(szBuf));
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "write file: /var/info/rtsp-tmp failed. [err:%s]\n", strerror(errno));
                close(s32Fd);
                continue;
            }

            close(s32Fd);
            rename("/var/info/rtsp-tmp", "/var/info/rtsp");
        }
    }

    cJSON_Delete(pstJson);

    return NULL;
}

sint32 callbackMediaStreamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret =0, i;
    xop::MediaSession *session1, *session2, *session3;
    MSG_VIDEO_CFG *pstVideoCfg = (MSG_VIDEO_CFG *)pstMsgPkt->pu8Data;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    uint32 u32NewMainWidth = 0;
    uint32 u32NewMainHeight = 0;
    uint32 u32NewSubWidth = 0;
    uint32 u32NewSubHeight = 0;
    uint32 u32NewPicWidth = 0;
    uint32 u32NewPicHeight = 0;
    uint8 u8MainW = 0, u8PicW = 0;
    uint8 u8MainH = 0, u8PicH = 0;
    uint8 u8MainQ = 60, u8PicQ = 70;     // quality
    uint8 u8MainType = 1, u8PicType = 0; // 0:YUV422 1:YUV420
    SV_BOOL bStopServer = SV_FALSE;

    u32NewMainWidth = pstVideoCfg->astChnParam[0].u32MainWidth;
    u32NewMainHeight = pstVideoCfg->astChnParam[0].u32MainHeight;
    u32NewSubWidth = pstVideoCfg->astChnParam[0].u32SubWidth;
    u32NewSubHeight = pstVideoCfg->astChnParam[0].u32SubHeight;

    print_level(SV_DEBUG, "Old Mainstream Width = %d, New Mainstream Width = %d.\n", \
                m_stRtspInfo.stMainResolution.u32Width, u32NewMainWidth);
    print_level(SV_DEBUG, "Old Substream Width = %d, New Substream Width = %d.\n", \
                m_stRtspInfo.stSubResolution.u32Width, u32NewSubWidth);

#if (defined(BOARD_IPCR20S3))
    u32NewPicWidth = pstVideoCfg->astChnParam[0].u32JpegWidth;
    u32NewPicHeight = pstVideoCfg->astChnParam[0].u32JpegHeight;
    print_level(SV_DEBUG, "Old Picstream Width = %d, New Picstream Width = %d.\n", \
                m_stRtspInfo.stPicResolution.u32Width, u32NewPicWidth);
#endif

#if 1
    if (m_stRtspInfo.bAudioEnable != pstVideoCfg->astChnParam[0].bAudioEnable
        || m_stRtspInfo.enAudioEncType != pstVideoCfg->enAudioEncType
        || m_stRtspInfo.enAudioSampleRate != pstVideoCfg->enAudioSampleRate
        || m_stRtspInfo.enMainEncode != pstVideoCfg->astChnParam[0].enMainEncode
        || m_stRtspInfo.u32MainFramerate != pstVideoCfg->astChnParam[0].u32MainFramerate
        || m_stRtspInfo.stMainResolution.u32Width != u32NewMainWidth
        || m_stRtspInfo.stMainResolution.u32Height != u32NewMainHeight)
    {
        print_level(SV_INFO, "callbackMediaStreamChange.\n mainStream: enMainEncode: %d (%dx%d) @%dfps @%dkbps rc%d %di\n audioStream:%d, audioSampleRate:%d\n", \
                    pstVideoCfg->astChnParam[0].enMainEncode, pstVideoCfg->astChnParam[0].u32MainWidth, pstVideoCfg->astChnParam[0].u32MainHeight, \
                    pstVideoCfg->astChnParam[0].u32MainFramerate, pstVideoCfg->astChnParam[0].u32MainBitrate, pstVideoCfg->astChnParam[0].enMainRcMode, pstVideoCfg->astChnParam[0].u32MainIfrmInterval, \
                    pstVideoCfg->astChnParam[0].bAudioEnable, pstVideoCfg->enAudioSampleRate);

        m_stRtspInfo.u32MainClients = 0;
        m_stRtspInfo.u32SubClients = 0;
        m_stRtspInfo.u32PicClients = 0;
        bStopServer = SV_TRUE;
        m_stRtspInfo.pcsRtspServer->Stop();
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32MainSessionId);

        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
            //print_level(SV_DEBUG, "stMediaAttr.stMainStreamAttr.bExtraValid = %d\n", stMediaAttr.stMainStreamAttr.bExtraValid);
            if (SV_SUCCESS == s32Ret && (stMediaAttr.stMainStreamAttr.bExtraValid || stMediaAttr.stMainStreamAttr.bNoExtraData) \
				&& stMediaAttr.stMainStreamAttr.u32Width != 0 && stMediaAttr.stMainStreamAttr.u32Height != 0 )
            {
                if (stMediaAttr.stMainStreamAttr.enCodeType != VIDEO_ENCODE_MJPEG)
                {
                    memcpy(&m_stRtspInfo.stMainExtraData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stMainExtraData));
                }
                m_stRtspInfo.stMainResolution.u32Width = stMediaAttr.stMainStreamAttr.u32Width;
                m_stRtspInfo.stMainResolution.u32Height = stMediaAttr.stMainStreamAttr.u32Height;

                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "Mainstream wait for extra data timeout!\n");
        }
        else
        {
            print_level(SV_INFO, "Mainstream get extra data succcess!\n");
        }

        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
        {
            session1 = xop::MediaSession::CreateNew("h264");
        }
        else
        {
            session1 = xop::MediaSession::CreateNew(m_stRtspInfo.szServiceMainUri);
        }

    	if(pstVideoCfg->astChnParam[0].enMainEncode == ENCODE_H265)
        {
    	    session1->AddSource(xop::channel_0, xop::H265Source::CreateNew(pstVideoCfg->astChnParam[0].u32MainFramerate, &m_stRtspInfo.stMainExtraData));
    	}
        else if (pstVideoCfg->astChnParam[0].enMainEncode == ENCODE_H264)
        {
    	    session1->AddSource(xop::channel_0, xop::H264Source::CreateNew(pstVideoCfg->astChnParam[0].u32MainFramerate, &m_stRtspInfo.stMainExtraData));
        }
        else
        {
            u8MainW = (uint8)(m_stRtspInfo.stMainResolution.u32Width >> 3);
            u8MainH = (uint8)(m_stRtspInfo.stMainResolution.u32Height >> 3);
    	    session1->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(pstVideoCfg->astChnParam[0].u32MainFramerate, u8MainW, u8MainH, u8MainQ, u8MainType));
        }

#if (defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5)  || defined(BOARD_WFCR20S2))
        if (pstVideoCfg->astChnParam[0].bAudioEnable && pstVideoCfg->astChnParam[0].enMainEncode != ENCODE_MJPEG)
#else
        if (pstVideoCfg->astChnParam[0].bAudioEnable/* && pstVideoCfg->astChnParam[0].enMainEncode != ENCODE_MJPEG*/)
#endif
    	{
    	    if(pstVideoCfg->enAudioEncType == AUD_ENC_G711A)
            {
    	    	session1->AddSource(xop::channel_1, xop::G711ASource::CreateNew(transSampleRate2Num(pstVideoCfg->enAudioSampleRate)));
    	    }
            else if(pstVideoCfg->enAudioEncType == AUD_ENC_G711U)
    	    {
    	        session1->AddSource(xop::channel_1, xop::G711USource::CreateNew(transSampleRate2Num(pstVideoCfg->enAudioSampleRate)));
    	    }
            else if(pstVideoCfg->enAudioEncType == AUD_ENC_LPCM)
            {
    	        session1->AddSource(xop::channel_1, xop::LPCMSource::CreateNew(transSampleRate2Num(pstVideoCfg->enAudioSampleRate)));
    	    }
        }
    	session1->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
    		m_stRtspInfo.u32MainClients = num_clients;
    		print_level(SV_INFO, "mainstream clients: %d\n", num_clients);
    	});

    	m_stRtspInfo.u32MainSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session1);

        m_stRtspInfo.u32MainFramerate = pstVideoCfg->astChnParam[0].u32MainFramerate;
        m_stRtspInfo.enMainEncode = pstVideoCfg->astChnParam[0].enMainEncode;
        m_stRtspInfo.bAudioEnable = pstVideoCfg->astChnParam[0].bAudioEnable;
        m_stRtspInfo.enAudioEncType = pstVideoCfg->enAudioEncType;
        m_stRtspInfo.enAudioSampleRate = pstVideoCfg->enAudioSampleRate;
    }

#if (!defined(BOARD_WFCR20S2) && !defined(BOARD_WFTR20S3))
    if (m_stRtspInfo.enSubEncode != pstVideoCfg->astChnParam[0].enSubEncode
        || m_stRtspInfo.u32SubFramerate != pstVideoCfg->astChnParam[0].u32SubFramerate
        || m_stRtspInfo.stSubResolution.u32Width != u32NewSubWidth
        || m_stRtspInfo.stSubResolution.u32Height != u32NewSubHeight)
    {
        print_level(SV_INFO, "callbackMediaStreamChange.\n subStream: enSubEncode: %d (%dx%d) @%dfps @%dkbps rc%d %di\n", \
                    pstVideoCfg->astChnParam[0].enSubEncode, pstVideoCfg->astChnParam[0].u32SubWidth, pstVideoCfg->astChnParam[0].u32SubHeight, \
                    pstVideoCfg->astChnParam[0].u32SubFramerate, pstVideoCfg->astChnParam[0].u32SubBitrate, pstVideoCfg->astChnParam[0].enSubRcMode, pstVideoCfg->astChnParam[0].u32SubIfrmInterval);

#if (!defined(BOARD_WFCR20S2))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
        if ( BOARD_WFTR20S3_V1 == BOARD_GetVersion())
        {
            goto skip;
        }
#endif
        bStopServer = SV_TRUE;
        m_stRtspInfo.pcsRtspServer->Stop();
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32SubSessionId);
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(1, &stMediaAttr);
            //print_level(SV_DEBUG, "stMediaAttr.stSubStreamAttr.bExtraValid = %d\n", stMediaAttr.stSubStreamAttr.bExtraValid);
            if (SV_SUCCESS == s32Ret && stMediaAttr.stSubStreamAttr.bExtraValid)
            {
                memcpy(&m_stRtspInfo.stSubExtraData, stMediaAttr.stSubStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stSubExtraData));
                m_stRtspInfo.stSubResolution.u32Width = stMediaAttr.stSubStreamAttr.u32Width;
                m_stRtspInfo.stSubResolution.u32Height = stMediaAttr.stSubStreamAttr.u32Height;
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "Substream wait for extra data timeout!\n");
        }
        else
        {
            print_level(SV_INFO, "Substream get extra data succcess!\n");
        }

        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
        {
            session2 = xop::MediaSession::CreateNew("h264_2");
        }
        else
        {
            session2 = xop::MediaSession::CreateNew(m_stRtspInfo.szServiceSubUri);
        }

    	if(pstVideoCfg->astChnParam[0].enSubEncode == ENCODE_H265)
        {
    	    session2->AddSource(xop::channel_0, xop::H265Source::CreateNew(pstVideoCfg->astChnParam[0].u32SubFramerate, &m_stRtspInfo.stSubExtraData));
    	}
        else
    	{
    	    session2->AddSource(xop::channel_0, xop::H264Source::CreateNew(pstVideoCfg->astChnParam[0].u32SubFramerate, &m_stRtspInfo.stSubExtraData));
    	}
        session2->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
    		m_stRtspInfo.u32SubClients = num_clients;
    		print_level(SV_INFO, "substream clients: %d\n", num_clients);
    	});

    	m_stRtspInfo.u32SubSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session2);

        m_stRtspInfo.u32SubFramerate = pstVideoCfg->astChnParam[0].u32SubFramerate;
        m_stRtspInfo.enSubEncode = pstVideoCfg->astChnParam[0].enSubEncode;
skip:;
#endif
    }
#endif

#if (defined(BOARD_IPCR20S3))
    if (m_stRtspInfo.stPicResolution.u32Width != u32NewPicWidth
        || m_stRtspInfo.stPicResolution.u32Height != u32NewPicHeight)
    {
        bStopServer = SV_TRUE;
        m_stRtspInfo.pcsRtspServer->Stop();
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32PicSessionId);
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(2, &stMediaAttr);
            if (SV_SUCCESS == s32Ret)
            {
                m_stRtspInfo.stPicResolution.u32Width = stMediaAttr.stPicStreamAttr.u32Width;
                m_stRtspInfo.stPicResolution.u32Height = stMediaAttr.stPicStreamAttr.u32Height;
                //print_level(SV_DEBUG, "Picstream Width = %d.\n", m_stRtspInfo.stPicResolution.u32Width);
                //print_level(SV_DEBUG, "Picstream Height = %d.\n", m_stRtspInfo.stPicResolution.u32Height);
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "Picstream wait for extra data timeout!\n");
        }

        session3 = xop::MediaSession::CreateNew(m_stRtspInfo.szServicePicUri);
        u8PicW = (uint8)(m_stRtspInfo.stPicResolution.u32Width >> 3);
        u8PicH = (uint8)(m_stRtspInfo.stPicResolution.u32Height >> 3);
        session3->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(15, u8PicW, u8PicH, u8PicQ, u8PicType));
        session3->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
            sint32 s32Ret = 0;
            MSG_PACKET_S stMsgPkt = {0};

            m_stRtspInfo.u32PicClients = num_clients;
            print_level(SV_INFO, "picstream clients: %d\n", num_clients);
            if (0 == m_stRtspInfo.u32PicClients)
            {
                //sleep(2);
                stMsgPkt.stMsg.s32Param = -1;
                s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                    return SV_FAILURE;
                }
            }
            else
            {
                stMsgPkt.stMsg.s32Param = 3; // quality 3, q factor 70
                s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                    return SV_FAILURE;
                }
            }
        });
        m_stRtspInfo.u32PicSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session3);
    }
#endif

    if (bStopServer)
    {
	    m_stRtspInfo.pcsRtspServer->Start("0.0.0.0", m_stRtspInfo.u32ServicePort);
    }

#else

    if (m_stRtspInfo.bAudioEnable != pstVideoCfg->astChnParam[0].bAudioEnable
        || m_stRtspInfo.enAudioEncType != pstVideoCfg->enAudioEncType
        || m_stRtspInfo.enAudioSampleRate != pstVideoCfg->enAudioSampleRate
        || m_stRtspInfo.enMainEncode != pstVideoCfg->astChnParam[0].enMainEncode
        || m_stRtspInfo.u32MainFramerate != pstVideoCfg->astChnParam[0].u32MainFramerate
        || m_stRtspInfo.stMainResolution.u32Width != u32NewMainWidth
        || m_stRtspInfo.stMainResolution.u32Height != u32NewMainHeight
#if (!defined(BOARD_WFCR20S2) && !defined(BOARD_WFTR20S3) && !defined(BOARD_IPTR20S1))
        || m_stRtspInfo.enSubEncode != pstVideoCfg->astChnParam[0].enSubEncode
        || m_stRtspInfo.u32SubFramerate != pstVideoCfg->astChnParam[0].u32SubFramerate
        || m_stRtspInfo.stSubResolution.u32Width != u32NewSubWidth
        || m_stRtspInfo.stSubResolution.u32Height != u32NewSubHeight
#endif
#if (defined(BOARD_IPCR20S3))
        || m_stRtspInfo.stPicResolution.u32Width != u32NewPicWidth
        || m_stRtspInfo.stPicResolution.u32Height != u32NewPicHeight
#endif

       )
    {
        print_level(SV_INFO, "callbackMediaStreamChange.\n mainStream: enMainEncode: %d (%dx%d) @%dfps @%dkbps rc%d %di\n subStream: enSubEncode: %d (%dx%d) @%dfps @%dkbps rc%d %di\n audioStream: %d\n", \
                    pstVideoCfg->astChnParam[0].enMainEncode, pstVideoCfg->astChnParam[0].u32MainWidth, pstVideoCfg->astChnParam[0].u32MainHeight, pstVideoCfg->astChnParam[0].u32MainFramerate, pstVideoCfg->astChnParam[0].u32MainBitrate, pstVideoCfg->astChnParam[0].enMainRcMode, pstVideoCfg->astChnParam[0].u32MainIfrmInterval, \
                    pstVideoCfg->astChnParam[0].enSubEncode, pstVideoCfg->astChnParam[0].u32SubWidth, pstVideoCfg->astChnParam[0].u32SubHeight, pstVideoCfg->astChnParam[0].u32SubFramerate, pstVideoCfg->astChnParam[0].u32SubBitrate, pstVideoCfg->astChnParam[0].enSubRcMode, pstVideoCfg->astChnParam[0].u32SubIfrmInterval, \
                    pstVideoCfg->astChnParam[0].bAudioEnable);
    #if (defined(BOARD_IPCR20S3))
        print_level(SV_INFO, "picStream: (%dx%d)\n", pstVideoCfg->astChnParam[0].u32JpegWidth, pstVideoCfg->astChnParam[0].u32JpegHeight);
    #endif

        m_stRtspInfo.u32MainClients = 0;
        m_stRtspInfo.u32SubClients = 0;
        m_stRtspInfo.u32PicClients = 0;
        m_stRtspInfo.pcsRtspServer->Stop();
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32MainSessionId);

        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
            //print_level(SV_DEBUG, "stMediaAttr.stMainStreamAttr.bExtraValid = %d\n", stMediaAttr.stMainStreamAttr.bExtraValid);
            if (SV_SUCCESS == s32Ret && (stMediaAttr.stMainStreamAttr.bExtraValid || stMediaAttr.stMainStreamAttr.bNoExtraData))
            {
                if (stMediaAttr.stMainStreamAttr.enCodeType != VIDEO_ENCODE_MJPEG)
                {
                    memcpy(&m_stRtspInfo.stMainExtraData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stMainExtraData));
                }
                m_stRtspInfo.stMainResolution.u32Width = stMediaAttr.stMainStreamAttr.u32Width;
                m_stRtspInfo.stMainResolution.u32Height = stMediaAttr.stMainStreamAttr.u32Height;
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "Mainstream wait for extra data timeout!\n");
        }
        else
        {
            print_level(SV_INFO, "Mainstream get extra data succcess!\n");
        }

        session1 = xop::MediaSession::CreateNew(RTSP_MAIN_URI);
    	if(pstVideoCfg->astChnParam[0].enMainEncode == ENCODE_H265)
        {
    	    session1->AddSource(xop::channel_0, xop::H265Source::CreateNew(pstVideoCfg->astChnParam[0].u32MainFramerate, &m_stRtspInfo.stMainExtraData));
    	}
        else if (pstVideoCfg->astChnParam[0].enMainEncode == ENCODE_H264)
        {
    	    session1->AddSource(xop::channel_0, xop::H264Source::CreateNew(pstVideoCfg->astChnParam[0].u32MainFramerate, &m_stRtspInfo.stMainExtraData));
        }
        else
        {
            u8MainW = (uint8)(m_stRtspInfo.stMainResolution.u32Width >> 3);
            u8MainH = (uint8)(m_stRtspInfo.stMainResolution.u32Height >> 3);
    	    session1->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(pstVideoCfg->astChnParam[0].u32MainFramerate, u8MainW, u8MainH, u8MainQ, u8MainType));
        }

    	if (pstVideoCfg->astChnParam[0].bAudioEnable/* && pstVideoCfg->astChnParam[0].enMainEncode != ENCODE_MJPEG*/)
    	{
    	    if(pstVideoCfg->enAudioEncType == AUD_ENC_G711A)
            {
    	    	session1->AddSource(xop::channel_1, xop::G711ASource::CreateNew(transSampleRate2Num(pstVideoCfg->enAudioSampleRate)));
    	    }
            else if(pstVideoCfg->enAudioEncType == AUD_ENC_G711U)
    	    {
    	        session1->AddSource(xop::channel_1, xop::G711USource::CreateNew(transSampleRate2Num(pstVideoCfg->enAudioSampleRate)));
    	    }
            else if(pstVideoCfg->enAudioEncType == AUD_ENC_LPCM)
            {
    	        session1->AddSource(xop::channel_1, xop::LPCMSource::CreateNew(transSampleRate2Num(pstVideoCfg->enAudioSampleRate)));
    	    }
        }
    	session1->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
    		m_stRtspInfo.u32MainClients = num_clients;
    		print_level(SV_INFO, "mainstream clients: %d\n", num_clients);
    	});

    	m_stRtspInfo.u32MainSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session1);

substream:;
#if (!defined(BOARD_WFCR20S2))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
        if (BOARD_WFTR20S3_V1 == BOARD_GetVersion() || BOARD_IPTR20S1_V1 == BOARD_GetVersion())
        {
            goto skip;
        }
#endif
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32SubSessionId);
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(1, &stMediaAttr);
            //print_level(SV_DEBUG, "stMediaAttr.stSubStreamAttr.bExtraValid = %d\n", stMediaAttr.stSubStreamAttr.bExtraValid);
            if (SV_SUCCESS == s32Ret && stMediaAttr.stSubStreamAttr.bExtraValid)
            {
                memcpy(&m_stRtspInfo.stSubExtraData, stMediaAttr.stSubStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stSubExtraData));
                m_stRtspInfo.stSubResolution.u32Width = stMediaAttr.stSubStreamAttr.u32Width;
                m_stRtspInfo.stSubResolution.u32Height = stMediaAttr.stSubStreamAttr.u32Height;
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "Substream wait for extra data timeout!\n");
        }
        else
        {
            print_level(SV_INFO, "Substream get extra data succcess!\n");
        }

    	session2 = xop::MediaSession::CreateNew(RTSP_SUB_URI);
    	if(pstVideoCfg->astChnParam[0].enSubEncode == ENCODE_H265)
        {
    	    session2->AddSource(xop::channel_0, xop::H265Source::CreateNew(pstVideoCfg->astChnParam[0].u32SubFramerate, &m_stRtspInfo.stSubExtraData));
    	}
        else
    	{
    	    session2->AddSource(xop::channel_0, xop::H264Source::CreateNew(pstVideoCfg->astChnParam[0].u32SubFramerate, &m_stRtspInfo.stSubExtraData));
    	}
        session2->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
    		m_stRtspInfo.u32SubClients = num_clients;
    		print_level(SV_INFO, "substream clients: %d\n", num_clients);
    	});

    	m_stRtspInfo.u32SubSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session2);

skip:;
#endif

#if (defined(BOARD_IPCR20S3))
picstream:;
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32PicSessionId);
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(2, &stMediaAttr);
            if (SV_SUCCESS == s32Ret)
            {
                m_stRtspInfo.stPicResolution.u32Width = stMediaAttr.stPicStreamAttr.u32Width;
                m_stRtspInfo.stPicResolution.u32Height = stMediaAttr.stPicStreamAttr.u32Height;
                //print_level(SV_DEBUG, "Picstream Width = %d.\n", m_stRtspInfo.stPicResolution.u32Width);
                //print_level(SV_DEBUG, "Picstream Height = %d.\n", m_stRtspInfo.stPicResolution.u32Height);
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "Picstream wait for extra data timeout!\n");
        }

        session3 = xop::MediaSession::CreateNew(RTSP_PIC_URI);
        u8PicW = (uint8)(m_stRtspInfo.stPicResolution.u32Width >> 3);
        u8PicH = (uint8)(m_stRtspInfo.stPicResolution.u32Height >> 3);
        session3->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(15, u8PicW, u8PicH, u8PicQ, u8PicType));
        session3->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
            sint32 s32Ret = 0;
            MSG_PACKET_S stMsgPkt = {0};

            m_stRtspInfo.u32PicClients = num_clients;
            print_level(SV_INFO, "picstream clients: %d\n", num_clients);
            if (0 == m_stRtspInfo.u32PicClients)
            {
                //sleep(2);
                stMsgPkt.stMsg.s32Param = -1;
                s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                    return SV_FAILURE;
                }
            }
            else
            {
                stMsgPkt.stMsg.s32Param = 3; // quality 3, q factor 70
                s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                    return SV_FAILURE;
                }
            }
        });
        m_stRtspInfo.u32PicSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session3);
#endif

	    m_stRtspInfo.pcsRtspServer->Start("0.0.0.0", m_stRtspInfo.u32ServicePort);
        m_stRtspInfo.u32MainFramerate = pstVideoCfg->astChnParam[0].u32MainFramerate;
        m_stRtspInfo.enMainEncode = pstVideoCfg->astChnParam[0].enMainEncode;
        m_stRtspInfo.u32SubFramerate = pstVideoCfg->astChnParam[0].u32SubFramerate;
        m_stRtspInfo.enSubEncode = pstVideoCfg->astChnParam[0].enSubEncode;
        m_stRtspInfo.bAudioEnable = pstVideoCfg->astChnParam[0].bAudioEnable;
        m_stRtspInfo.enAudioEncType = pstVideoCfg->enAudioEncType;
        m_stRtspInfo.enAudioSampleRate = pstVideoCfg->enAudioSampleRate;

    }
#endif

    return SV_SUCCESS;
}

sint32 callbackStopMediaStream(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_INFO, "recive: OP_EVENT_DOWNLOAD_FIRMWARE\n");
    m_stRtspInfo.bRtspStreamPause = SV_TRUE;
    sleep_ms(200);
    m_stRtspInfo.u32MainClients = 0;
    m_stRtspInfo.u32SubClients = 0;
    m_stRtspInfo.u32PicClients = 0;
    m_stRtspInfo.pcsRtspServer->Stop();
    m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32MainSessionId);
    m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32SubSessionId);
    m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32PicSessionId);
    sleep_ms(200);

    return SV_SUCCESS;
}

sint32 callbackNetworkParamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0, i;
    uint8 u8MainW = 0, u8PicW = 0;
    uint8 u8MainH = 0, u8PicH = 0;
    uint8 u8MainQ = 60, u8PicQ = 70;     // quality
    uint8 u8MainType = 1, u8PicType = 0; // 0:YUV422 1:YUV420
    xop::MediaSession *session1, *session2, *session3;
    MSG_NETWORK_CFG *pstNetWorkCfg = (MSG_NETWORK_CFG *)pstMsgPkt->pu8Data;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};

    if (m_stRtspInfo.u32ServicePort != pstNetWorkCfg->u32RtspServPort || strcmp(m_stRtspInfo.szServiceMainUri,pstNetWorkCfg->szRtspServMainUri) != 0
#if (!defined(BOARD_WFCR20S2))
		|| strcmp(m_stRtspInfo.szServiceSubUri,pstNetWorkCfg->szRtspServSubUri) != 0
#endif
#if (defined(BOARD_IPCR20S3))
		|| strcmp(m_stRtspInfo.szServicePicUri,pstNetWorkCfg->szRtspServPicUri) != 0
#endif
	)
    {
        print_level(SV_INFO, "callbackNetworkParamChange.\n");
        print_level(SV_INFO, "u32RtspServPort: %d->%d\n", m_stRtspInfo.u32ServicePort, pstNetWorkCfg->u32RtspServPort);
        m_stRtspInfo.u32MainClients = 0;
        m_stRtspInfo.u32SubClients = 0;
        m_stRtspInfo.u32PicClients = 0;
        m_stRtspInfo.pcsRtspServer->Stop();

        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32MainSessionId);
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
            if (SV_SUCCESS == s32Ret && (stMediaAttr.stMainStreamAttr.bExtraValid || stMediaAttr.stMainStreamAttr.bNoExtraData))
            {
                if (stMediaAttr.stMainStreamAttr.enCodeType != VIDEO_ENCODE_MJPEG)
                {
                    memcpy(&m_stRtspInfo.stMainExtraData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stMainExtraData));
                }
                m_stRtspInfo.stMainResolution.u32Width = stMediaAttr.stMainStreamAttr.u32Width;
                m_stRtspInfo.stMainResolution.u32Height = stMediaAttr.stMainStreamAttr.u32Height;
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "wait extra data timeout!\n");
        }

        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
        {
            session1 = xop::MediaSession::CreateNew("h264");
        }
        else
        {
            session1 = xop::MediaSession::CreateNew(pstNetWorkCfg->szRtspServMainUri);
        }

    	if(m_stRtspInfo.enMainEncode == ENCODE_H265)
        {
    	    session1->AddSource(xop::channel_0, xop::H265Source::CreateNew(m_stRtspInfo.u32MainFramerate, &m_stRtspInfo.stMainExtraData));
    	}
        else if (m_stRtspInfo.enMainEncode == ENCODE_H264)
        {
    	    session1->AddSource(xop::channel_0, xop::H264Source::CreateNew(m_stRtspInfo.u32MainFramerate, &m_stRtspInfo.stMainExtraData));
        }
        else
        {
            u8MainW = (uint8)(m_stRtspInfo.stMainResolution.u32Width >> 3);
            u8MainH = (uint8)(m_stRtspInfo.stMainResolution.u32Height >> 3);
    	    session1->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(m_stRtspInfo.u32MainFramerate, u8MainW, u8MainH, u8MainQ, u8MainType));
        }

#if (defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5)  || defined(BOARD_WFCR20S2)) 
        if (m_stRtspInfo.bAudioEnable && m_stRtspInfo.enMainEncode != ENCODE_MJPEG)
#else
    	if (m_stRtspInfo.bAudioEnable)
#endif
    	{
    	    if(m_stRtspInfo.enAudioEncType == AUD_ENC_G711A)
            {
    	    	session1->AddSource(xop::channel_1, xop::G711ASource::CreateNew(transSampleRate2Num(m_stRtspInfo.enAudioSampleRate)));
    	    }
            else if(m_stRtspInfo.enAudioEncType == AUD_ENC_G711U)
            {
    	        session1->AddSource(xop::channel_1, xop::G711USource::CreateNew(transSampleRate2Num(m_stRtspInfo.enAudioSampleRate)));
    	    }
            else if(m_stRtspInfo.enAudioEncType == AUD_ENC_LPCM)
            {
    	        session1->AddSource(xop::channel_1, xop::LPCMSource::CreateNew(transSampleRate2Num(m_stRtspInfo.enAudioSampleRate)));
    	    }
        }
    	session1->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
    		m_stRtspInfo.u32MainClients = num_clients;
    		print_level(SV_INFO, "mainstream clients: %d\n", num_clients);
    	});

    	m_stRtspInfo.u32MainSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session1);

#if (!defined(BOARD_WFCR20S2))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
        if (BOARD_WFTR20S3_V1 == BOARD_GetVersion())
        {
            goto skip;
        }
#endif
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32SubSessionId);
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(1, &stMediaAttr);
            if (SV_SUCCESS == s32Ret && stMediaAttr.stSubStreamAttr.bExtraValid)
            {
                memcpy(&m_stRtspInfo.stSubExtraData, stMediaAttr.stSubStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stSubExtraData));
                m_stRtspInfo.stSubResolution.u32Width = stMediaAttr.stSubStreamAttr.u32Width;
                m_stRtspInfo.stSubResolution.u32Height = stMediaAttr.stSubStreamAttr.u32Height;
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "wait extra data timeout!\n");
        }

        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
        {
            session2 = xop::MediaSession::CreateNew("h264_2");
        }
        else
        {
            session2 = xop::MediaSession::CreateNew(pstNetWorkCfg->szRtspServSubUri);
        }

    	if(m_stRtspInfo.enSubEncode == ENCODE_H265)
        {
    	    session2->AddSource(xop::channel_0, xop::H265Source::CreateNew(m_stRtspInfo.u32SubFramerate, &m_stRtspInfo.stSubExtraData));
    	}
        else
    	{
    	    session2->AddSource(xop::channel_0, xop::H264Source::CreateNew(m_stRtspInfo.u32SubFramerate, &m_stRtspInfo.stSubExtraData));
    	}
        session2->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
    		m_stRtspInfo.u32SubClients = num_clients;
    		print_level(SV_INFO, "substream clients: %d\n", num_clients);
    	});

    	m_stRtspInfo.u32SubSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session2);
skip:;
#endif
#if (defined(BOARD_IPCR20S3))
        m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32PicSessionId);
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(2, &stMediaAttr);
            if (SV_SUCCESS == s32Ret)
            {
                m_stRtspInfo.stPicResolution.u32Width = stMediaAttr.stPicStreamAttr.u32Width;
                m_stRtspInfo.stPicResolution.u32Height = stMediaAttr.stPicStreamAttr.u32Height;
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "wait extra data timeout!\n");
        }

    	session3 = xop::MediaSession::CreateNew(pstNetWorkCfg->szRtspServPicUri);
    	u8PicW = (uint8)(m_stRtspInfo.stPicResolution.u32Width >> 3);
        u8PicH = (uint8)(m_stRtspInfo.stPicResolution.u32Height >> 3);
        session3->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(15, u8PicW, u8PicH, u8PicQ, u8PicType));
        session3->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
            sint32 s32Ret = 0;
            MSG_PACKET_S stMsgPkt = {0};
    		m_stRtspInfo.u32PicClients = num_clients;
    		print_level(SV_INFO, "picstream clients: %d\n", num_clients);
            if (0 == m_stRtspInfo.u32PicClients)
            {
                sleep(2);
                stMsgPkt.stMsg.s32Param = -1;
                s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                    return SV_FAILURE;
                }
            }
            else
            {
                stMsgPkt.stMsg.s32Param = 3; // quality 3, q factor 70
                s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                    return SV_FAILURE;
                }
            }
    	});

    	m_stRtspInfo.u32PicSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session3);
#endif
	    m_stRtspInfo.pcsRtspServer->Start("0.0.0.0", pstNetWorkCfg->u32RtspServPort);
	    m_stRtspInfo.u32ServicePort = pstNetWorkCfg->u32RtspServPort;
    }

    return SV_SUCCESS;
}


static sint32 callbackUsrParamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    MSG_USR_CFG *pstUsrCfg = (MSG_USR_CFG *)pstMsgPkt->pu8Data;
    print_level(SV_INFO, "recive: OP_EVENT_USR_CHANGE\n");

    m_stRtspInfo.pcsRtspServer->SetAuthConfig("", "", "");
    if (strlen(pstUsrCfg->szUserPassword[0][2]))
    {
    	m_stRtspInfo.pcsRtspServer->SetAuthConfig("-_-", "admin", pstUsrCfg->szUserPassword[0][2]);
    }

    return SV_SUCCESS;
}

static sint32 callbackRecreate(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0, i;
    uint8 u8MainW = 0, u8PicW = 0;
    uint8 u8MainH = 0, u8PicH = 0;
    uint8 u8MainQ = 60, u8PicQ = 70;     // quality
    uint8 u8MainType = 1, u8PicType = 0; // 0:YUV422 1:YUV420
    xop::MediaSession *session1, *session2, *session3 ,*session4;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    NETWORK_STAT_S *pstNetworkStat = (NETWORK_STAT_S *)pstMsgPkt->pu8Data;

    if (NETWORK_TYPE_LAN != pstNetworkStat->enNetworkType && SV_FALSE != pstNetworkStat->bExist)
    {
        return SV_SUCCESS;
    }

    m_stRtspInfo.u32MainClients = 0;
    m_stRtspInfo.u32SubClients = 0;
    m_stRtspInfo.pcsRtspServer->Stop();
    m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32MainSessionId);

    print_level(SV_DEBUG, "RTSP callbackRecreate.\n");

    for (i = 0; i < 10; i++)
    {
        s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
        if (SV_SUCCESS == s32Ret && (stMediaAttr.stMainStreamAttr.bExtraValid || stMediaAttr.stMainStreamAttr.bNoExtraData))
        {
            if (stMediaAttr.stMainStreamAttr.enCodeType != VIDEO_ENCODE_MJPEG)
            {
                memcpy(&m_stRtspInfo.stMainExtraData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stMainExtraData));
            }
            m_stRtspInfo.stMainResolution.u32Width = stMediaAttr.stMainStreamAttr.u32Width;
            m_stRtspInfo.stMainResolution.u32Height = stMediaAttr.stMainStreamAttr.u32Height;
            break;
        }
        sleep_ms(500);
    }
    if (i >= 10)
    {
        print_level(SV_ERROR, "wait extra data timeout!\n");
    }

    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
    {
        session1 = xop::MediaSession::CreateNew("h264");
    }
    else
    {
        session1 = xop::MediaSession::CreateNew(m_stRtspInfo.szServiceMainUri);
    }

	if(m_stRtspInfo.enMainEncode == ENCODE_H265)
    {
	    session1->AddSource(xop::channel_0, xop::H265Source::CreateNew(m_stRtspInfo.u32MainFramerate, &m_stRtspInfo.stMainExtraData));
	}
    else if (m_stRtspInfo.enMainEncode == ENCODE_H264)
    {
	    session1->AddSource(xop::channel_0, xop::H264Source::CreateNew(m_stRtspInfo.u32MainFramerate, &m_stRtspInfo.stMainExtraData));
    }
    else
    {
        u8MainW = (uint8)(m_stRtspInfo.stMainResolution.u32Width >> 3);
        u8MainH = (uint8)(m_stRtspInfo.stMainResolution.u32Height >> 3);
	    session1->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(m_stRtspInfo.u32MainFramerate, u8MainW, u8MainH, u8MainQ, u8MainType));
    }

#if (defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5)  || defined(BOARD_WFCR20S2))
    if (m_stRtspInfo.bAudioEnable && m_stRtspInfo.enMainEncode != ENCODE_MJPEG)
#else
    if (m_stRtspInfo.bAudioEnable)
#endif
	{
	    if (m_stRtspInfo.enAudioEncType == AUD_ENC_G711A)
        {
	    	session1->AddSource(xop::channel_1, xop::G711ASource::CreateNew(transSampleRate2Num(m_stRtspInfo.enAudioSampleRate)));
	    }
        else if (m_stRtspInfo.enAudioEncType == AUD_ENC_G711U)
        {
	        session1->AddSource(xop::channel_1, xop::G711USource::CreateNew(transSampleRate2Num(m_stRtspInfo.enAudioSampleRate)));
	    }
        else if (m_stRtspInfo.enAudioEncType == AUD_ENC_LPCM)
        {
	        session1->AddSource(xop::channel_1, xop::LPCMSource::CreateNew(transSampleRate2Num(m_stRtspInfo.enAudioSampleRate)));
	    }
    }
	session1->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
		m_stRtspInfo.u32MainClients = num_clients;
		print_level(SV_INFO, "mainstream clients: %d\n", num_clients);
	});

	m_stRtspInfo.u32MainSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session1);

substream:;
#if (!defined(BOARD_WFCR20S2))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
    if (BOARD_WFTR20S3_V1 == BOARD_GetVersion())
    {
        goto skip;
    }
#endif
    m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32SubSessionId);
    for (i = 0; i < 10; i++)
    {
        s32Ret = SFIFO_GetMediaAttr(1, &stMediaAttr);
        if (SV_SUCCESS == s32Ret && stMediaAttr.stSubStreamAttr.bExtraValid)
        {
            memcpy(&m_stRtspInfo.stSubExtraData, stMediaAttr.stSubStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stSubExtraData));
            m_stRtspInfo.stSubResolution.u32Width = stMediaAttr.stSubStreamAttr.u32Width;
            m_stRtspInfo.stSubResolution.u32Height = stMediaAttr.stSubStreamAttr.u32Height;
            break;
        }
        sleep_ms(500);
    }
    if (i >= 10)
    {
        print_level(SV_ERROR, "wait extra data timeout!\n");
    }

    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
    {
        session2 = xop::MediaSession::CreateNew("h264_2");
    }
    else
    {
        session2 = xop::MediaSession::CreateNew(m_stRtspInfo.szServiceSubUri);
    }

	if(m_stRtspInfo.enSubEncode == ENCODE_H265)
    {
	    session2->AddSource(xop::channel_0, xop::H265Source::CreateNew(m_stRtspInfo.u32SubFramerate, &m_stRtspInfo.stSubExtraData));
	}
    else
    {
	    session2->AddSource(xop::channel_0, xop::H264Source::CreateNew(m_stRtspInfo.u32SubFramerate, &m_stRtspInfo.stSubExtraData));
	}
    session2->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
		m_stRtspInfo.u32SubClients = num_clients;
		print_level(SV_INFO, "substream clients: %d\n", num_clients);
	});

	m_stRtspInfo.u32SubSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session2);
skip:;
#endif
#if (defined(BOARD_IPCR20S3))
    m_stRtspInfo.pcsRtspServer->RemoveSession(m_stRtspInfo.u32PicSessionId);
    for (i = 0; i < 10; i++)
    {
        s32Ret = SFIFO_GetMediaAttr(2, &stMediaAttr);
        if (SV_SUCCESS == s32Ret)
        {
            m_stRtspInfo.stPicResolution.u32Width = stMediaAttr.stPicStreamAttr.u32Width;
            m_stRtspInfo.stPicResolution.u32Height = stMediaAttr.stPicStreamAttr.u32Height;
            break;
        }
        sleep_ms(500);
    }
    if (i >= 10)
    {
        print_level(SV_ERROR, "wait extra data timeout!\n");
    }

	session3 = xop::MediaSession::CreateNew(m_stRtspInfo.szServicePicUri);
	u8PicW = (uint8)(m_stRtspInfo.stPicResolution.u32Width >> 3);
    u8PicH = (uint8)(m_stRtspInfo.stPicResolution.u32Height >> 3);
    session3->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(15, u8PicW, u8PicH, u8PicQ, u8PicType));
    session3->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
        sint32 s32Ret = 0;
        MSG_PACKET_S stMsgPkt = {0};
		m_stRtspInfo.u32PicClients = num_clients;
		print_level(SV_INFO, "picstream clients: %d\n", num_clients);
        if (0 == m_stRtspInfo.u32PicClients)
        {
            sleep(2);
            stMsgPkt.stMsg.s32Param = -1;
            s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
        }
        else
        {
            stMsgPkt.stMsg.s32Param = 3; // quality 3, q factor 70
            s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
        }
	});

	m_stRtspInfo.u32PicSessionId = m_stRtspInfo.pcsRtspServer->AddSession(session3);
#endif
    m_stRtspInfo.pcsRtspServer->Start("0.0.0.0", m_stRtspInfo.u32ServicePort);

	return SV_SUCCESS;
}

sint32 RTSP_SVR_Init(RTSP_CFG_PARAM_S *pstInitParam)
{
    sint32 s32Ret = 0, i;
    uint32 u32SampleRate = 0;
    uint8 u8MainW = 0, u8PicW = 0;
    uint8 u8MainH = 0, u8PicH = 0;
    uint8 u8MainQ = 60, u8PicQ = 70;     // quality
    uint8 u8MainType = 1, u8PicType = 0; // 0:YUV422 1:YUV420
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    xop::MediaSession *session1, *session2, *session3, *session4;

    if (NULL == pstInitParam)
    {
        return ERR_NULL_PTR;
    }

    memset(&m_stRtspInfo, 0x0, sizeof(RTSP_COM_INFO_S));
    std::shared_ptr<xop::EventLoop> pcsEventLoop(new xop::EventLoop());
    std::shared_ptr<xop::RtspServer> pcsRtspServer = xop::RtspServer::Create(pcsEventLoop.get());
    if (strlen(pstInitParam->szAdminPassword))
    {
		pcsRtspServer->SetAuthConfig("-_-", "admin", pstInitParam->szAdminPassword);
	}

    for (i = 0; i < 10; i++)
    {
        s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
        //print_level(SV_DEBUG, "stMediaAttr.stMainStreamAttr.bExtraValid = %d\n", stMediaAttr.stMainStreamAttr.bExtraValid);
        //print_level(SV_INFO, "stMediaAttr.stMainStreamAttr.bNoExtraData:%d\n", stMediaAttr.stMainStreamAttr.bNoExtraData);
        if (SV_SUCCESS == s32Ret && (stMediaAttr.stMainStreamAttr.bExtraValid || stMediaAttr.stMainStreamAttr.bNoExtraData))
        {
            if (stMediaAttr.stMainStreamAttr.enCodeType != VIDEO_ENCODE_MJPEG)
            {
                memcpy(&m_stRtspInfo.stMainExtraData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stMainExtraData));
            }
            m_stRtspInfo.stMainResolution.u32Width = stMediaAttr.stMainStreamAttr.u32Width;
            m_stRtspInfo.stMainResolution.u32Height = stMediaAttr.stMainStreamAttr.u32Height;
            print_level(SV_DEBUG, "Mainstream Width = %d.\n", m_stRtspInfo.stMainResolution.u32Width);
            print_level(SV_DEBUG, "Mainstream Height = %d.\n", m_stRtspInfo.stMainResolution.u32Height);
            break;
        }
        sleep_ms(500);
    }
    if (i >= 10)
    {
        print_level(SV_ERROR, "Mainstream wait for extra data timeout!\n");
    }

    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
    {
        session1 = xop::MediaSession::CreateNew("h264");
    }
    else
    {
        session1 = xop::MediaSession::CreateNew(pstInitParam->szServerMainUri);
    }

	if (pstInitParam->enMainEncode == ENCODE_H265)
    {
	    session1->AddSource(xop::channel_0, xop::H265Source::CreateNew(pstInitParam->u32MainFramerate, &m_stRtspInfo.stMainExtraData));
	}
    else if (pstInitParam->enMainEncode == ENCODE_H264)
    {
	    session1->AddSource(xop::channel_0, xop::H264Source::CreateNew(pstInitParam->u32MainFramerate, &m_stRtspInfo.stMainExtraData));
    }
    else
    {
        u8MainW = (uint8)(m_stRtspInfo.stMainResolution.u32Width >> 3);
        u8MainH = (uint8)(m_stRtspInfo.stMainResolution.u32Height >> 3);
    	session1->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(pstInitParam->u32MainFramerate, u8MainW, u8MainH, u8MainQ, u8MainType));
    }

#if (defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5)  || defined(BOARD_WFCR20S2))
    if (pstInitParam->bAudioEnable && pstInitParam->enMainEncode != ENCODE_MJPEG)
#else
    if (pstInitParam->bAudioEnable)
#endif
	{
	    if(pstInitParam->enAudioEncType == AUD_ENC_G711A)
        {
	    	session1->AddSource(xop::channel_1, xop::G711ASource::CreateNew(transSampleRate2Num(pstInitParam->enAudioSampleRate)));
	    }
        else if(pstInitParam->enAudioEncType == AUD_ENC_G711U)
        {
	        session1->AddSource(xop::channel_1, xop::G711USource::CreateNew(transSampleRate2Num(pstInitParam->enAudioSampleRate)));
	    }
        else if(pstInitParam->enAudioEncType == AUD_ENC_LPCM)
        {
	        session1->AddSource(xop::channel_1, xop::LPCMSource::CreateNew(transSampleRate2Num(pstInitParam->enAudioSampleRate)));
	    }
    }
	session1->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
		m_stRtspInfo.u32MainClients = num_clients;
		print_level(SV_INFO, "mainstream clients: %d\n", num_clients);
	});
	m_stRtspInfo.u32MainSessionId = pcsRtspServer->AddSession(session1);

substream:;
#if (!defined(BOARD_WFCR20S2))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
        if (BOARD_WFTR20S3_V1 == BOARD_GetVersion() || BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA32E1_V1 == BOARD_GetVersion())
        {
            goto skip;
        }
#endif
    for (i = 0; i < 10; i++)
    {
        s32Ret = SFIFO_GetMediaAttr(1, &stMediaAttr);
        if (SV_SUCCESS == s32Ret && stMediaAttr.stSubStreamAttr.bExtraValid)
        {
            memcpy(&m_stRtspInfo.stSubExtraData, stMediaAttr.stSubStreamAttr.au8VpsData, sizeof(m_stRtspInfo.stSubExtraData));
            m_stRtspInfo.stSubResolution.u32Width = stMediaAttr.stSubStreamAttr.u32Width;
            m_stRtspInfo.stSubResolution.u32Height = stMediaAttr.stSubStreamAttr.u32Height;
            print_level(SV_DEBUG, "Substream Width = %d.\n", m_stRtspInfo.stSubResolution.u32Width);
            print_level(SV_DEBUG, "Substream Height = %d.\n", m_stRtspInfo.stSubResolution.u32Height);
            break;
        }
        sleep_ms(500);
    }
    if (i >= 10)
    {
        print_level(SV_ERROR, "Substream wait for extra data timeout!\n");
    }

    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
    {
        session2 = xop::MediaSession::CreateNew("h264_2");
    }
    else
    {
        session2 = xop::MediaSession::CreateNew(pstInitParam->szServerSubUri);
    }

	if(pstInitParam->enSubEncode == ENCODE_H265)
    {
	    session2->AddSource(xop::channel_0, xop::H265Source::CreateNew(pstInitParam->u32SubFramerate, &m_stRtspInfo.stSubExtraData));
	}
    else
    {
	    session2->AddSource(xop::channel_0, xop::H264Source::CreateNew(pstInitParam->u32SubFramerate, &m_stRtspInfo.stSubExtraData));
	}
    session2->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
		m_stRtspInfo.u32SubClients = num_clients;
		print_level(SV_INFO, "substream clients: %d\n", num_clients);
	});
	m_stRtspInfo.u32SubSessionId = pcsRtspServer->AddSession(session2);
skip:;
#endif

#if (defined(BOARD_IPCR20S3))
picstream:;
    for (i = 0; i < 10; i++)
    {
        s32Ret = SFIFO_GetMediaAttr(2, &stMediaAttr);
        if (SV_SUCCESS == s32Ret)
        {
            m_stRtspInfo.stPicResolution.u32Width = stMediaAttr.stPicStreamAttr.u32Width;
            m_stRtspInfo.stPicResolution.u32Height = stMediaAttr.stPicStreamAttr.u32Height;
            print_level(SV_DEBUG, "Picstream Width = %d.\n", m_stRtspInfo.stPicResolution.u32Width);
            print_level(SV_DEBUG, "Picstream Height = %d.\n", m_stRtspInfo.stPicResolution.u32Height);
            break;
        }
        sleep_ms(500);
    }
    if (i >= 10)
    {
        print_level(SV_ERROR, "Picstream wait for extra data timeout!\n");
    }

	session3 = xop::MediaSession::CreateNew(pstInitParam->szServerPicUri);
	u8PicW = (uint8)(m_stRtspInfo.stPicResolution.u32Width >> 3);
    u8PicH = (uint8)(m_stRtspInfo.stPicResolution.u32Height >> 3);
    session3->AddSource(xop::channel_0, xop::MJPEGSource::CreateNew(15, u8PicW, u8PicH, u8PicQ, u8PicType));
    session3->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
        sint32 s32Ret = 0;
        MSG_PACKET_S stMsgPkt = {0};

		m_stRtspInfo.u32PicClients = num_clients;
		print_level(SV_INFO, "picstream clients: %d\n", num_clients);
        if (0 == m_stRtspInfo.u32PicClients)
        {
            stMsgPkt.stMsg.s32Param = -1;
            s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
        }
        else
        {
            stMsgPkt.stMsg.s32Param = 3; // quality 3, q factor 70
            s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
        }
	});
	m_stRtspInfo.u32PicSessionId = pcsRtspServer->AddSession(session3);
#endif

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
	m_stRtspInfo.pcsPlaybackSource = xop::H264Source::CreateNew(30, &m_stRtspInfo.stPlaybackExtraData);
    session4 = xop::MediaSession::CreateNew("playback");
    session4->AddSource(xop::channel_0, m_stRtspInfo.pcsPlaybackSource);
    session4->AddSource(xop::channel_1, xop::AACSource::CreateNew(16000, 1));
	session4->SetNotifyCallback([](xop::MediaSessionId session_id, uint32_t num_clients) {
		m_stRtspInfo.u32PlaybackClients = num_clients;
		print_level(SV_INFO, "playbackstream clients: %d\n", num_clients);
	});
	session4->SetParamCallback([](xop::MediaSessionId session_id, std::string strParam) {   // 验证url中的文件名是否存在
	    sint32 s32Ret = 0, i, j;
	    sint32 s32SpsPos = -1, s32PpsPos = -1, s32SeiPos = -1, s32IdrPos = -1;
	    SV_BOOL bGotExtraData = SV_FALSE;
	    AVFormatContext stContext = {0};
	    AVPacket stMovpkt = {0};
	    MOVContext stMov = {0};

		print_level(SV_INFO, "strParam: %s\n", strParam.c_str());
		int idxFile = strParam.find_first_of("file=");
		std::string strFilePath = strParam.substr(strParam.find_first_of("file=")+5);
		if (idxFile < 0 || strFilePath.length() > 1000)
		{
		    print_level(SV_ERROR, "url param: %s invalid!\n", strParam.c_str());
		    return -1;
		}

		sprintf(m_stRtspInfo.szPlaybackFilePath, "/mnt/sdcard/%s", strFilePath.c_str());
#if (defined(BOARD_ADA47V1))
        if (0 != access(m_stRtspInfo.szPlaybackFilePath, F_OK))
        {
            sprintf(m_stRtspInfo.szPlaybackFilePath, "/userdata/%s", strFilePath.c_str());
        }
#endif
		print_level(SV_INFO, "try to open file: %s\n", m_stRtspInfo.szPlaybackFilePath);
		/*s32Ret = url_fopen(&stContext.pb, m_stRtspInfo.szPlaybackFilePath, URL_RDONLY);
    	if (s32Ret  < 0 )
    	{
    		print_level(SV_ERROR, "url_fopen file: %s failed!\n", m_stRtspInfo.szPlaybackFilePath);
    		return -1;
    	}

    	stContext.priv_data = &stMov;
    	s32Ret = avi_read_header(&stContext, NULL);
    	if (s32Ret < 0)
    	{
    		print_level(SV_ERROR, "avi_read_header failed. [err=%d]\n", s32Ret);
    		return -1;
    	}

    	print_level(SV_DEBUG, "(%x%d) bitrate:%d, framerate:%d\n", stContext.streams[0]->codec->width, \
            stContext.streams[0]->codec->height, stContext.streams[0]->bit_rate, stContext.streams[0]->time_base.den);

        for (i = 0; i < 10; i++)
        {
            if (url_feof(stContext.pb))
            {
                break;
            }

            s32Ret = avi_read_packet(&stContext, &stAvipkt);
    		if (s32Ret < 0)
    		{
    			print_level(SV_ERROR, "avi_read_packet failed. [err=%d]\n", s32Ret);
    			break;
    		}

    		if (stAvipkt.flags != 1)
    		{
    		    continue;
    		}

    		for (j = 0; j < 40; j++)
    		{
    		    if (stAvipkt.data[j] == 0x00 && stAvipkt.data[j+1] == 0x00 && stAvipkt.data[j+2] == 0x00 && stAvipkt.data[j+3] == 0x01 && stAvipkt.data[j+4] == 0x67)
    		    {
    		        s32SpsPos = j;
    		    }
    		    if (stAvipkt.data[j] == 0x00 && stAvipkt.data[j+1] == 0x00 && stAvipkt.data[j+2] == 0x00 && stAvipkt.data[j+3] == 0x01 && stAvipkt.data[j+4] == 0x68)
    		    {
    		        s32PpsPos = j;
    		    }
    		    if (stAvipkt.data[j] == 0x00 && stAvipkt.data[j+1] == 0x00 && stAvipkt.data[j+2] == 0x00 && stAvipkt.data[j+3] == 0x01 && stAvipkt.data[j+4] == 0x06)
    		    {
    		        s32SeiPos = j;
    		    }
    		    if (stAvipkt.data[j] == 0x00 && stAvipkt.data[j+1] == 0x00 && stAvipkt.data[j+2] == 0x00 && stAvipkt.data[j+3] == 0x01 && stAvipkt.data[j+4] == 0x65)
    		    {
    		        s32IdrPos = j;
    		    }
    		    if (s32SpsPos >= 0 && s32PpsPos >= 0 && s32SeiPos >= 0 && s32IdrPos >= 0 && s32SpsPos < s32PpsPos && s32PpsPos < s32SeiPos && s32SeiPos < s32IdrPos)
    		    {
    		        m_stRtspInfo.stPlaybackExtraData.sps_size = s32PpsPos - s32SpsPos - 4;
    		        memcpy(m_stRtspInfo.stPlaybackExtraData.sps_data, &stAvipkt.data[s32SpsPos+4], m_stRtspInfo.stPlaybackExtraData.sps_size);
    		        m_stRtspInfo.stPlaybackExtraData.pps_size = s32SeiPos - s32PpsPos - 4;
    		        memcpy(m_stRtspInfo.stPlaybackExtraData.pps_data, &stAvipkt.data[s32PpsPos+4], m_stRtspInfo.stPlaybackExtraData.pps_size);
    		        m_stRtspInfo.stPlaybackExtraData.sei_size = s32IdrPos - s32SeiPos - 4;
    		        memcpy(m_stRtspInfo.stPlaybackExtraData.sei_data, &stAvipkt.data[s32SeiPos+4], m_stRtspInfo.stPlaybackExtraData.sei_size);
    		        print_level(SV_DEBUG, "sps_size:%d, pps_size:%d, sei_size:%d\n", m_stRtspInfo.stPlaybackExtraData.sps_size, \
    		                    m_stRtspInfo.stPlaybackExtraData.pps_size, m_stRtspInfo.stPlaybackExtraData.sei_size);
    		        if (m_stRtspInfo.pcsPlaybackSource)
    		        {
    		            m_stRtspInfo.pcsPlaybackSource->SetExtraData(&m_stRtspInfo.stPlaybackExtraData);
    		        }
    		        bGotExtraData = SV_TRUE;
    		        break;
    		    }
    		}
        }

        av_free_packet(&stAvipkt);
    	avi_read_close(&stContext);
        url_fclose(stContext.pb);
		if (!bGotExtraData)
		{
		    print_level(SV_ERROR, "no found extra data!\n", s32Ret);
		    return -1;
		}*/

		return 0;
	});
	m_stRtspInfo.u32PlaybackSessionId = pcsRtspServer->AddSession(session4);
#endif

    m_stRtspInfo.pcsEventLoop = pcsEventLoop;
    m_stRtspInfo.pcsRtspServer = pcsRtspServer;
    m_stRtspInfo.u32MainFramerate = pstInitParam->u32MainFramerate;
    m_stRtspInfo.enMainEncode = pstInitParam->enMainEncode;
    m_stRtspInfo.u32SubFramerate = pstInitParam->u32SubFramerate;
    m_stRtspInfo.enSubEncode = pstInitParam->enSubEncode;
    m_stRtspInfo.bAudioEnable = pstInitParam->bAudioEnable;
    m_stRtspInfo.enAudioEncType = pstInitParam->enAudioEncType;
    m_stRtspInfo.enAudioSampleRate = pstInitParam->enAudioSampleRate;
    m_stRtspInfo.u32ServicePort = pstInitParam->u32ServicePort;

	strcpy(m_stRtspInfo.szServiceMainUri,pstInitParam->szServerMainUri);
	strcpy(m_stRtspInfo.szServiceSubUri,pstInitParam->szServerSubUri);
	strcpy(m_stRtspInfo.szServicePicUri,pstInitParam->szServerPicUri);
    return SV_SUCCESS;
}

sint32 RTSP_SVR_Fini()
{
    sint32 s32Ret = 0;


    return SV_SUCCESS;
}


sint32 RTSP_SVR_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread1 = 0, thread2 = 0, thread3 = 0;

    s32Ret = MSG_ReciverStart(EP_RTSPSERVER);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_USR_CHANGE, callbackUsrParamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_MEDIA_CHANGE, callbackMediaStreamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_DOWNLOAD_FIRMWARE, callbackStopMediaStream);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_NETWORK_STAT, callbackRecreate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_NETWORK_CHANGE, callbackNetworkParamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    print_level(SV_INFO, "Rtsp start at port:%d\n", m_stRtspInfo.u32ServicePort);
    if (!m_stRtspInfo.pcsRtspServer->Start("0.0.0.0", m_stRtspInfo.u32ServicePort))
    {
        print_level(SV_ERROR, "RtspServer Start failed.\n");
		return SV_FAILURE;
	}

    m_stRtspInfo.bRunning = SV_TRUE;
    s32Ret = pthread_create(&thread1, NULL, rtsp_Service_Body, &m_stRtspInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    m_stRtspInfo.u32TidService = thread1;

    s32Ret = pthread_create(&thread2, NULL, rtsp_Status_Body, &m_stRtspInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }
    m_stRtspInfo.u32TidStatus = thread2;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
	s32Ret = pthread_create(&thread3, NULL, rtsp_Playback_Body, &m_stRtspInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }
    m_stRtspInfo.u32TidPlayback = thread3;
#endif

    return SV_SUCCESS;
}


sint32 RTSP_SVR_Stop()
{
    sint32 s32Ret = 0;
    pthread_t thread1 = m_stRtspInfo.u32TidService;
    pthread_t thread2 = m_stRtspInfo.u32TidStatus;
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
	pthread_t thread3 = m_stRtspInfo.u32TidPlayback;
#endif
    void *pvRetval = NULL;

    m_stRtspInfo.bRunning = SV_FALSE;
    m_stRtspInfo.eventLoopWatchVariable = 1;
    s32Ret = pthread_join(thread1, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = pthread_join(thread2, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
	s32Ret = pthread_join(thread3, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    m_stRtspInfo.pcsRtspServer->Stop();
    s32Ret = MSG_ReciverStop(EP_RTSPSERVER);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStop failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}


sint32 RTSP_SVR_ConfigSet(RTSP_CFG_PARAM_S *pstConnCfg)
{

    return SV_SUCCESS;
}


