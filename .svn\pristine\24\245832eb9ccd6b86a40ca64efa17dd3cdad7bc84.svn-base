
TARGET_LIB  = libwebrtc.a

include ../../../Makefile.param

ifneq ($(BOARD),$(findstring $(BOARD),ADA47V1 ADA32N1 ADA32NSDK ADA32ESDK IPCR20S3 IPCR20S4))
IGNORE_THIS_BUILD = metartc
endif

SRCS = $(wildcard ./src/yangavutil/*.c)
SRCS += $(wildcard ./src/yangice/*.c)
SRCS += $(wildcard ./src/yangipc/*.c)
SRCS += $(wildcard ./src/yangp2p/*.c)
SRCS += $(wildcard ./src/yangrtc/*.c)
SRCS += $(wildcard ./src/yangrtp/*.c)
SRCS += $(wildcard ./src/yangsdp/*.c)
SRCS += $(wildcard ./src/yangsrs/*.c)
SRCS += $(wildcard ./src/yangssl/*.c)
SRCS += $(wildcard ./src/yangstream/*.c)
SRCS += $(wildcard ./src/yangutil/buffer2/*.c)
SRCS += $(wildcard ./src/yangutil/sys/*.c)
SRCS += $(wildcard ./src/yangzlm/*.c)
SRCS += libwebrtc.c
CFLAGS += -DMG_ENABLE_OPENSSL=1 -DMG_ENABLE_MD5=1  -I$(INC_PATH) -I$(shell pwd) -I$(shell pwd)/include -I$(shell pwd)/src -I$(shell pwd)/thirdparty/include \
			-I$(shell pwd)/thirdparty/user_include -I$(INC_PATH)/opensslv1.1 -I$(INC_PATH)/cjson -I$(shell pwd)/../../recorder/ffmpeg4.1.3/include \
			-I$(shell pwd)/../../media/rockchip/rv1126_rebuild/rkmedia
#CPPFLAGS += -std=c++11 -O2
CPPFLAGS += $(CFLAGS)

_TARGET_DIR_ = $(TOP_LIB)

include $(AUTO_DEP_MK) 
