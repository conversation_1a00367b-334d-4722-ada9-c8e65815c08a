#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <unistd.h>
#include <signal.h>

#include "common.h"
#include "print.h"
#include "msg.h"
#include "CUnit.h"
#include "Concurrent.h"

/* 宏定义批量注册 */
#define TMP_0 0
#define TMP_1 1
#define TMP_2 2
#define TMP_3 3
#define TMP_4 4
#define TMP_5 5

#define MEDIA_GUI_EVENT(plane, dev, op, idx) itest_SubmitMediaGuiEvent_##op##plane##dev##idx

#define DEFINE_SUBMIT_MEDIA_GUI_EVENT(plane, dev, op, idx) \
void itest_SubmitMediaGuiEvent_##op##dev##plane##idx() \
{\
    itest_SubmitMediaGuiEvent_##op(TMP_##dev, TMP_##plane);\
}


/***************************************************************
*-# 用例编号: itest_MSG_SysInit_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_MSG_SysInit_001()
{
    sint32 s32Ret = 0;
    sint32 chChoice = 0;

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    while (1)
    {
        chChoice = toupper(getchar());
        if (chChoice == 'Q')
        {
            break;
        }
        printf("\r\n press 'Q' for exit.\r\n");
    }
    
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_MSG_SysInit_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_MSG_ReciverStart_001()
{
    sint32 s32Ret = 0;
    MSG_EP_ID enReciverId = EP_CONTROL;

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    sleep_ms(2000);

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_Msg_submitEvent_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
sint32 test_submitCallback(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_DEBUG, "s32Param: %d\n", pstMsgPkt->stMsg.s32Param);
    return SV_SUCCESS;
}

void itest_Msg_submitEvent_001()
{
    sint32 s32Ret = 0, i;
    MSG_EP_ID enReciverId = EP_CONTROL;
    MSG_PACKET_S stMsgPkt = {0};

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = Msg_registerOpCallback(enReciverId, OP_EVENT_TEST, test_submitCallback);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    for (i = 0; i < 10; i++)
    {
        stMsgPkt.stMsg.s32Param = 100;
        s32Ret = Msg_submitEvent(enReciverId, OP_EVENT_BSD, &stMsgPkt);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        sleep_ms(1000);
    }

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_Msg_submitEvent_002
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_Msg_submitEvent_002()
{
    sint32 s32Ret = 0;
    MSG_EP_ID enReciverId = EP_CONTROL;
    MSG_PACKET_S stMsgPkt = {0};

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = Msg_registerOpCallback_ThreadExec(enReciverId, OP_EVENT_TEST, test_submitCallback);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    stMsgPkt.stMsg.s32Param = 100;
    s32Ret = Msg_submitEvent(enReciverId, OP_EVENT_TEST, &stMsgPkt);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    sleep_ms(1000);

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_Msg_execRequestBlock_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
sint32 test_blockRequestOkCallback(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_DEBUG, "s32Param: %d\n", pstMsgPkt->stMsg.s32Param);
    pstRetPkt->stMsg.s32Param = 200;
    return SV_SUCCESS;
}

sint32 test_blockRequestErrCallback(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_DEBUG, "s32Param: %d\n", pstMsgPkt->stMsg.s32Param);
    return ERR_NOT_CONFIG;
}

sint32 test_blockRequestDevInfoCallback(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    MSG_DEV_INFO *pstDevInfo = (MSG_DEV_INFO *)pstRetPkt->pu8Data;
    
    print_level(SV_DEBUG, "s32Param: %d\n", pstMsgPkt->stMsg.s32Param);
    strcpy(pstDevInfo->szVersion, "v1.0.0");
    strcpy(pstDevInfo->szSerial, "1234");
    strcpy(pstDevInfo->szHardware, "IPC01");
    strcpy(pstDevInfo->szUuid, "56789");
    pstRetPkt->u32Size = sizeof(MSG_DEV_INFO);
    
    return SV_SUCCESS;
}

void itest_Msg_execRequestBlock_001()
{
    sint32 s32Ret = 0;
    MSG_EP_ID enReciverId = EP_CONTROL, enSendId = EP_ONVIFSERVER;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = Msg_registerOpCallback(enReciverId, OP_REQ_TEST, test_blockRequestOkCallback);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    stMsgPkt.stMsg.s32Param = 100;
    s32Ret = Msg_execRequestBlock(enSendId, enReciverId, OP_REQ_TEST, &stMsgPkt, &stRetPkt, 0);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    CU_ASSERT_EQUAL(stRetPkt.stMsg.s32Param, 200);
    sleep_ms(1000);

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStop(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_Msg_execRequestBlock_002
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_Msg_execRequestBlock_002()
{
    sint32 s32Ret = 0;
    MSG_EP_ID enReciverId = EP_CONTROL, enSendId = EP_ONVIFSERVER;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = Msg_registerOpCallback_ThreadExec(enReciverId, OP_REQ_TEST, test_blockRequestOkCallback);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    stMsgPkt.stMsg.s32Param = 100;
    s32Ret = Msg_execRequestBlock(enSendId, enReciverId, OP_REQ_TEST, &stMsgPkt, &stRetPkt, 0);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    CU_ASSERT_EQUAL(stRetPkt.stMsg.s32Param, 200);
    sleep_ms(1000);

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStop(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_Msg_execRequestBlock_003
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_Msg_execRequestBlock_003()
{
    sint32 s32Ret = 0;
    MSG_EP_ID enReciverId = EP_CONTROL, enSendId = EP_ONVIFSERVER;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = Msg_registerOpCallback_ThreadExec(enReciverId, OP_REQ_TEST, test_blockRequestErrCallback);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    stMsgPkt.stMsg.s32Param = 100;
    s32Ret = Msg_execRequestBlock(enSendId, enReciverId, OP_REQ_TEST, &stMsgPkt, &stRetPkt, 0);
    CU_ASSERT_EQUAL(s32Ret, SV_FAILURE);
    CU_ASSERT_EQUAL(stRetPkt.stMsg.s32Param, ERR_NOT_CONFIG);
    sleep_ms(1000);

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStop(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_Msg_execRequestBlock_004
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_Msg_execRequestBlock_004()
{
    sint32 s32Ret = 0;
    MSG_EP_ID enReciverId = EP_CONTROL, enSendId = EP_ONVIFSERVER;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = Msg_registerOpCallback(enReciverId, OP_REQ_TEST, test_blockRequestErrCallback);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    stMsgPkt.stMsg.s32Param = 100;
    s32Ret = Msg_execRequestBlock(enSendId, enReciverId, OP_REQ_TEST, &stMsgPkt, &stRetPkt, 0);
    CU_ASSERT_EQUAL(s32Ret, SV_FAILURE);
    CU_ASSERT_EQUAL(stRetPkt.stMsg.s32Param, ERR_NOT_CONFIG);
    sleep_ms(1000);

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStop(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_Msg_execRequestBlock_005
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_Msg_execRequestBlock_005()
{
    sint32 s32Ret = 0;
    MSG_EP_ID enReciverId = EP_CONTROL, enSendId = EP_ONVIFSERVER;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_DEV_INFO stDevInfo = {0};

    s32Ret = MSG_SysInit(SV_TRUE);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStart(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = Msg_registerOpCallback_ThreadExec(enReciverId, OP_REQ_GET_DEVINFO, test_blockRequestDevInfoCallback);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    stMsgPkt.stMsg.s32Param = 100;
    stRetPkt.pu8Data = (uint8 *)&stDevInfo;
    s32Ret = Msg_execRequestBlock(enSendId, enReciverId, OP_REQ_GET_DEVINFO, &stMsgPkt, &stRetPkt, sizeof(MSG_DEV_INFO));
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szVersion: %s, szSerial: %s, szHardware: %s, szUuid:%s\n", stDevInfo.szVersion, stDevInfo.szSerial, stDevInfo.szHardware, stDevInfo.szUuid);
    sleep_ms(1000);

    s32Ret = MSG_ReciverStop(enReciverId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_ReciverStop(enSendId);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = MSG_SysFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_SubmitAlgAlarmEvent_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitAlgAlarmEvent_001()
{
    sint32 s32Ret = 0, i;
    MSG_EP_ID enReciverId = EP_CONTROL;
    MSG_PACKET_S stMsgPkt = {0};

    stMsgPkt.stMsg.u32Param = ALARM_FATIGUE;
    s32Ret = Msg_submitEvent(enReciverId, OP_EVENT_ALG_ALARM, &stMsgPkt);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_SubmitAlgAlarmEvent_002
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitAlgAlarmEvent_002()
{
    sint32 s32Ret = 0, i;
    MSG_EP_ID enReciverId = EP_CONTROL;
    MSG_PACKET_S stMsgPkt = {0};

    for (i = 0; i < ALARM_TYPE_BUFF; i++)
    {
        stMsgPkt.stMsg.u32Param = i;
        s32Ret = Msg_submitEvent(enReciverId, OP_EVENT_ALG_ALARM, &stMsgPkt);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

        sleep_ms(6000);
    }
}

/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_Clear()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_Clear(int dev, int plane)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0};
    uint8 u8mask;
    MEDIA_GUI_NULL_S stGuiNull = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));

    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_CLEAR);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiNull);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT fail\n");
        return;
    }

    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_DrawLine_002()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_DrawLine(int dev, int plane)
{
    sint32 s32Ret = 0;
    uint8 u8mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    MEDIA_GUI_LINE_S stGuiLine = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));

    stGuiLine.x1 = 0.1;
    stGuiLine.y1 = 0.1;
    stGuiLine.x2 = 0.9;
    stGuiLine.y2 = 0.9;
    stGuiLine.color = 0xffff0000;
    stGuiLine.stick = 4;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_LINE);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiLine);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT fail\n");
        return;
    }
    
    stGuiLine.x1 = 0.1;
    stGuiLine.y1 = 0.9;
    stGuiLine.x2 = 0.9;
    stGuiLine.y2 = 0.1;
    stGuiLine.color = 0xff00ff00;
    stGuiLine.stick = 4;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_LINE);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiLine);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT fail\n");
        return;
    }

    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_DrawRect_3()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_DrawRect(int dev, int plane)
{
    sint32 s32Ret = 0;
    uint8 u8mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    MEDIA_GUI_RECT_S stGuiRect = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    
    stGuiRect.x1 = 0.1;
    stGuiRect.y1 = 0.1;
    stGuiRect.x2 = 0.9;
    stGuiRect.y2 = 0.9;
    stGuiRect.stick = 4;
    stGuiRect.color = 0xff0000ff;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_RECT);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiRect);

    stGuiRect.x1 = 0.3;
    stGuiRect.y1 = 0.3;
    stGuiRect.x2 = 0.7;
    stGuiRect.y2 = 0.7;
    stGuiRect.stick = 4;
    stGuiRect.color = 0xffffff00;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_RECT);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiRect);


    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_DrawCircle_4()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_DrawCircle(int dev, int plane)
{
    sint32 s32Ret = 0;
    uint8 u8mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    MEDIA_GUI_CIRCLE_S stGuiCircle = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));

    stGuiCircle.x = 0.4;
    stGuiCircle.y = 0.6;
    stGuiCircle.radius = 100;
    stGuiCircle.color = 0xffff0000;

    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_CIRCLE);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiCircle);

    stGuiCircle.x = 0.6;
    stGuiCircle.y = 0.6;
    stGuiCircle.radius = 100;
    stGuiCircle.color = 0xffff0000;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_CIRCLE);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiCircle);

    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}


/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_FillRect_5()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_FillRect(int dev, int plane)
{
    sint32 s32Ret = 0;
    uint8 u8mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    MEDIA_GUI_RECT_S stGuiRect = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    
    stGuiRect.x1 = 0.40;
    stGuiRect.y1 = 0.45;
    stGuiRect.x2 = 0.50;
    stGuiRect.y2 = 0.55;
    stGuiRect.stick = 2;
    stGuiRect.color = 0xff00ffff;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_FILL_RECT);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiRect);

    stGuiRect.x1 = 0.50;
    stGuiRect.y1 = 0.45;
    stGuiRect.x2 = 0.60;
    stGuiRect.y2 = 0.55;
    stGuiRect.stick = 3;
    stGuiRect.color = 0xffff00ff;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_FILL_RECT);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiRect);


    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if(s32Ret != SV_SUCCESS)
    {
        printf("%s:%d\n", __func__, __LINE__);
    }
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_FillCircle_6()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_FillCircle(int dev, int plane)
{
    sint32 s32Ret = 0;
    uint8 u8mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    MEDIA_GUI_CIRCLE_S stGuiCircle = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));

    stGuiCircle.x = 0.4;
    stGuiCircle.y = 0.6;
    stGuiCircle.radius = 3;
    stGuiCircle.color = 0xff00ff00;

    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_FILL_CIRCLE);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiCircle);

    stGuiCircle.x = 0.6;
    stGuiCircle.y = 0.6;
    stGuiCircle.radius = 4;
    stGuiCircle.color = 0xff00ff00;
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_FILL_CIRCLE);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiCircle);

    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}


/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_PaintString_7()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_DrawString(int dev, int plane)
{
    sint32 s32Ret = 0;
    uint8 u8mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    MEDIA_GUI_STRING_S stGuiString = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));

    stGuiString.x = 0.5;
    stGuiString.y = 0.1;
    stGuiString.color = 0xff00ff00;
    stGuiString.fontsize = 2;
    strcpy(stGuiString.string, "RK GUI TEST 1");
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_STRING);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiString);

    stGuiString.x = 0.5;
    stGuiString.y = 0.8;
    stGuiString.fontsize = 3;
    stGuiString.color = 0xffff0000;
    strcpy(stGuiString.string, "RK GUI TEST 2");
    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_DRAW_STRING);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiString);

    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if(s32Ret != SV_SUCCESS)
    {
        printf("%s:%d\n", __func__, __LINE__);
    }
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}


/***************************************************************
*-# 用例编号: itest_SubmitMediaGuiEvent_PaintPerson_8()
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_SubmitMediaGuiEvent_DrawPerson(int dev, int plane)
{
    sint32 s32Ret = 0;
    uint8 u8mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stGuiDraw = {0};
    MEDIA_GUI_PERSON_S stGuiPerson = {0};
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));

    stGuiPerson.u32PersonNum = 2;
    stGuiPerson.au32Score[0] = 32;
    stGuiPerson.astPersonsRect[0].color = 0xff00ff00;
    stGuiPerson.astPersonsRect[0].stick = 3;
    stGuiPerson.astPersonsRect[0].x1 = 0.1;
    stGuiPerson.astPersonsRect[0].y1 = 0.1;
    stGuiPerson.astPersonsRect[0].x2 = 0.3;
    stGuiPerson.astPersonsRect[0].y2 = 0.3;
    

    stGuiPerson.au32Score[1] = 32;
    stGuiPerson.astPersonsRect[1].color = 0xff00ff00;
    stGuiPerson.astPersonsRect[1].stick = 3;
    stGuiPerson.astPersonsRect[1].x1 = 0.5;
    stGuiPerson.astPersonsRect[1].y1 = 0.6;
    stGuiPerson.astPersonsRect[1].x2 = 0.7;
    stGuiPerson.astPersonsRect[1].y2 = 0.8;

    u8mask = MEDIA_GUI_GET_MASK(dev, plane, MEDIA_GUI_OP_PERSON_RECT);
    s32Ret = MEDIA_GUI_INSERT(stGuiDraw, u8mask, stGuiPerson);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "MEDIA_GUI_INSERT fail\n");
    }
    stMsgPkt.pu8Data = &stGuiDraw;
    stMsgPkt.u32Size = sizeof(stGuiDraw);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if(s32Ret != SV_SUCCESS)
    {
        printf("%s:%d\n", __func__, __LINE__);
    }
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}



DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, Clear, 1)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, DrawLine, 2)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, DrawRect, 3)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, DrawCircle, 4)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, FillRect, 5)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, FillCircle, 6)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, DrawString, 7)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 0, DrawPerson, 8)


DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, Clear, 1)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, DrawLine, 2)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, DrawRect, 3)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, DrawCircle, 4)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, FillRect, 5)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, FillCircle, 6)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, DrawString, 7)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(0, 1, DrawPerson, 8)


DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, Clear, 1)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, DrawLine, 2)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, DrawRect, 3)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, DrawCircle, 4)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, FillRect, 5)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, FillCircle, 6)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, DrawString, 7)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 0, DrawPerson, 8)


DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, Clear, 1)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, DrawLine, 2)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, DrawRect, 3)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, DrawCircle, 4)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, FillRect, 5)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, FillCircle, 6)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, DrawString, 7)
DEFINE_SUBMIT_MEDIA_GUI_EVENT(1, 1, DrawPerson, 8)


static int si_msg_fun()
{
    sint32 s32Ret = 0, i;
    MSG_EP_ID enReciverId = EP_FACTORY;
    MSG_PACKET_S stMsgPkt = {0};

    s32Ret = MSG_SysInit(SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_SysInit failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    
    s32Ret = MSG_ReciverStart(enReciverId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    
    return 0;
}

static int fs_msg_fun()
{
    sint32 s32Ret = 0, i;
    MSG_EP_ID enReciverId = EP_FACTORY;
    s32Ret = MSG_ReciverStop(enReciverId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStop failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    s32Ret = MSG_SysFini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_SysFini failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    
    return 0;
}

static int si_msg_gui()
{
    sint32 s32Ret = 0, i;
    //MSG_EP_ID enReciverId = EP_BSD;
    MSG_PACKET_S stMsgPkt = {0};

    s32Ret = MSG_SysInit(SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_SysInit failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    
    //s32Ret = MSG_ReciverStart(enReciverId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    
    return 0;
}

static int fs_msg_gui()
{
    sint32 s32Ret = 0, i;
    //MSG_EP_ID enReciverId = EP_BSD;
    //s32Ret = MSG_ReciverStop(enReciverId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStop failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    s32Ret = MSG_SysFini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_SysFini failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    
    return 0;
}


void Suite_MSG_API()
{
    CU_pSuite pSuite;

    if (NULL == CU_get_registry())
    {
        printf("CUnit not registry!\n");
        return;
    }

    pSuite = CU_ADD_SUITE(NULL, NULL);
    CU_ADD_TEST(pSuite, itest_MSG_SysInit_001);
    CU_ADD_TEST(pSuite, itest_MSG_ReciverStart_001);
    CU_ADD_TEST(pSuite, itest_Msg_submitEvent_001);
    CU_ADD_TEST(pSuite, itest_Msg_submitEvent_002);
    CU_ADD_TEST(pSuite, itest_Msg_execRequestBlock_001);
    CU_ADD_TEST(pSuite, itest_Msg_execRequestBlock_002);
    CU_ADD_TEST(pSuite, itest_Msg_execRequestBlock_003);
    CU_ADD_TEST(pSuite, itest_Msg_execRequestBlock_004);
    CU_ADD_TEST(pSuite, itest_Msg_execRequestBlock_005);
}

void Suite_MSG_Fun()
{
    CU_pSuite pSuite;

    if (NULL == CU_get_registry())
    {
        printf("CUnit not registry!\n");
        return;
    }

    pSuite = CU_ADD_SUITE(si_msg_fun, fs_msg_fun);
    CU_ADD_TEST(pSuite, itest_SubmitAlgAlarmEvent_001);
    CU_ADD_TEST(pSuite, itest_SubmitAlgAlarmEvent_002);
}

void Suite_MSG_Gui()
{
    CU_pSuite pSuite;

    if (NULL == CU_get_registry())
    {
        printf("CUnit not registry!\n");
        return;
    }
    
    pSuite = CU_ADD_SUITE(si_msg_gui, fs_msg_gui);
    
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,Clear,1));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,DrawLine,2));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,DrawRect,3));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,DrawCircle,4));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,FillRect,5));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,FillCircle,6));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,DrawString,7));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,0,DrawPerson,8));

    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,Clear,1));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,DrawLine,2));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,DrawRect,3));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,DrawCircle,4));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,FillRect,5));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,FillCircle,6));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,DrawString,7));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(0,1,DrawPerson,8));

    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,Clear,1));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,DrawLine,2));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,DrawRect,3));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,DrawCircle,4));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,FillRect,5));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,FillCircle,6));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,DrawString,7));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,0,DrawPerson,8));

    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,Clear,1));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,DrawLine,2));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,DrawRect,3));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,DrawCircle,4));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,FillRect,5));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,FillCircle,6));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,DrawString,7));
    CU_ADD_TEST(pSuite, MEDIA_GUI_EVENT(1,1,DrawPerson,8));

}



void AddTests_itest_msg()
{
    Suite_MSG_API();
    Suite_MSG_Fun();
	Suite_MSG_Gui();
}


