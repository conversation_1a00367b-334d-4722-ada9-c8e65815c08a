



#include <unistd.h>
#include <sys/prctl.h>
#include <list>
#include <errno.h>
#include <pthread.h>
#include <string.h>
#include <stdint.h>
#include <cjson/cJSON.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>


#include "sv_bb_common.h"
#include "sv_bb_interface.h"
#include "sv_bb_alarm.h"
#include "sv_dvr_common_interface.h"
#include "sv_common_time.h"
#include "sv_common_interface.h"
#include "sv_common.h"
#include "utils.h"
#include "common.h"
#include "storage.h"
#include "safefunc.h"
#include "../recorder/inc/r_common.h"
#include "../cmsServer/cms_offlineInfo.h"
#include "BB_Convert.h"

using namespace std;

static const char* g_chDir[] = {
	"/offline_data/event",
	"/offline_data/info",
	"/offline_data/can_data",
	"/offline_data/download",
	"/offline_data/check_list"
};

static const char* g_chNode[] = {
	"/data/ssd",
	"/data/sd",
	"/data/usb",
	"/data/sd2",
	"/data/sd3",
	"/data/sd4",
	"/data/usb2"
};

#define OFFLINE_READBUF_LEN 2048

#define BB_OFFLINE_BBALARM_DIR 		"offlineBBAlarm/"
#define BB_OFFLINE_INFO_DIR 		"offlineInfo/"
#define BB_OFFLINE_CANDATA_DIR 	"offlineCanData/"
#define BB_OFFLINE_DOWNLOAD_DIR 	"offlineDownload/"
#define BB_OFFLINE_CHECKLIST_DIR 	"offlineCheckList/"


#define BB_ALARMINFO_PATH_MEM          "/tmp"              /* 离线报警信息文件内存目录 */
#define BB_ALARMINFO_PATH_SD           "/mnt/sdcard"       /* 离线报警信息文件SD卡目录 */
#define BB_ALARMINFO_PATH_UDISK        "/mnt/udisk"        /* 离线报警信息文件U盘目录 */

static char szAlarmInfoUploadFilePath[256];


//保存待上传附件的附件信息
list<STSbAlarmFileUpload> g_lis_Appendix;
list<string>	g_lis_alarmpath;
pthread_mutex_t g_alarmpath_mutex = PTHREAD_MUTEX_INITIALIZER;


//保存最近获取的报警信息路径
char g_chAlarmPath[256] = {0};

typedef enum e_state
{
	E_REQ,			//获取附件请求
	E_GETFILE,		//获取报警信息文件
	E_CLIP,			//开始clip并等待完成
	E_UPLOAD,		//等待网络上传
	E_CLIPCNT
}E_clipState;

//当前clip状态
struct
{
	STSbAlarmFileUpload	stInfo;
	E_clipState eStatus;

	char		chAlarmPath[256];	//当前clip对应的报警信息文件
	char		chStart[16];		//开始时间
	char		chEnd[16];			//结束时间
	char		chAlarmTime[128];	//报警时间
	uint32	u32Mask;			//通道掩码
	uint8		u8Type;				//报警类型
	uint8		u8Serial;			//报警序号
	sint8		s8starttimeisDst;		//0:is not dst,1: is dst , -1: no this item;
	sint8		s8endtimeisDst;		//0:is not dst,1: is dst , -1: no this item;
	SV_BOOL		bExistAlarmVideo;	//是不是存在报警视频
	uint8		u8AlarmRecord;		//报警记录文件类型:0-off 1-video 2-pic 3-video&pic
	uint32	u32Count;			//待上传文件个数
	char		chUpload[HYBRID_MEDIA_CHANNEL_CNT][256];	//待上传文件路径
}g_stClip;

/********************************获取报警信息文件路径****************************/

REC_TYPE_E bb_TransString2RecType(char *cEventType)
{
    if (0 == strncmp(cEventType, "NM", 2))          return REC_TYPE_NORMAL;
    else if (0 == strncmp(cEventType, "FT", 2))     return REC_TYPE_FATIGUE;
    else if (0 == strncmp(cEventType, "DS", 2))     return REC_TYPE_DISTRACTION;
    else if (0 == strncmp(cEventType, "ND", 2))     return REC_TYPE_NO_DRIVER;
    else if (0 == strncmp(cEventType, "SM", 2))     return REC_TYPE_SMOKE;
    else if (0 == strncmp(cEventType, "CA", 2))     return REC_TYPE_PHONE;
    else if (0 == strncmp(cEventType, "RA", 2))     return REC_TYPE_RADAR;
    else if (0 == strncmp(cEventType, "YW", 2))     return REC_TYPE_YAWN;
    else if (0 == strncmp(cEventType, "AF", 2))     return REC_TYPE_FACE_AUTH_FAILED;
    else if (0 == strncmp(cEventType, "AT", 2))     return REC_TYPE_FACE_AUTH_TIMEOUT;
    else if (0 == strncmp(cEventType, "NK", 2))     return REC_TYPE_NO_MASK;
    else if (0 == strncmp(cEventType, "SG", 2))     return REC_TYPE_SUNGLASS;
    else if (0 == strncmp(cEventType, "L2", 2))     return REC_TYPE_FATIGUE;
    else if (0 == strncmp(cEventType, "PB", 2))     return REC_TYPE_PD_ROI;
    else if (0 == strncmp(cEventType, "NB", 2))     return REC_TYPE_SEATBELT;
    else if (0 == strncmp(cEventType, "CO", 2))     return REC_TYPE_SHELTER;
    else if (0 == strncmp(cEventType, "DE", 2))     return REC_TYPE_DRINKEAT;
    else if (0 == strncmp(cEventType, "CG", 2))     return REC_TYPE_CHANGE_GUARD;
    else if (0 == strncmp(cEventType, "OS", 2))     return REC_TYPE_OVERSPEED;
    else if (0 == strncmp(cEventType, "NH", 2))     return REC_TYPE_NO_HELMET;
    //else if (0 == strncmp(cEventType, "LF", 2))     return REC_TYPE_KSS7_MILD_FATIGUE;
    //else if (0 == strncmp(cEventType, "MF", 2))     return REC_TYPE_KSS8_MODERATE_FATIGUE;
    else if (0 == strncmp(cEventType, "SF", 2))     return REC_TYPE_FATIGUE;
    else if (0 == strncmp(cEventType, "UA", 2))     return REC_TYPE_USER_ALARM;
    else                                            return REC_TYPE_NORMAL;

}


void bb_alarm_CloseEvec(sint32 s32Fd)
{
	int flags = fcntl(s32Fd, F_GETFD);
	flags |= FD_CLOEXEC;
	fcntl(s32Fd, F_SETFD, flags);
}

sint32 bb_alarm_getFileDir(const char *szSrc, char *szDir)
{
	char *pTemp = szSrc;
	sint32 s32len = strlen(szSrc);
	sint32 s32Count = 0, s32DirLen = 0;

	for(s32Count = 0; s32Count < s32len; s32Count++)
	{
		if( szSrc[s32Count] == '/' )
		{
			pTemp = szSrc + s32Count;
		}
	}
	s32DirLen = pTemp - szSrc;
	//print_level(SV_DEBUG,"s32DirLen:%d pTemp:%s\n", s32DirLen, pTemp);

	strncpy(szDir, szSrc, s32DirLen);
	szDir[s32DirLen] = 0;

	return SV_SUCCESS;
}

sint32 bb_alarm_RenameofflineInfo(char *pszPath)
{
	char szNewName[STORAGE_FULLPATH_LEN] = {0};
	snprintf(szNewName, STORAGE_FULLPATH_LEN, "%s_BBupload", pszPath);
	print_level(SV_DEBUG,"rename file %s ===> %s \n",pszPath,szNewName);
	rename(pszPath, szNewName);
	memcpy(pszPath,szNewName,strlen(szNewName));
	return SV_SUCCESS;
}

SV_BOOL bb_alarm_getStorageStatus(char *pszStorageDir)
{
    sint32 i = 0;
    char szCmd[64] = {0};
    char szLogDirPath[64] = {0};
    char szPath[32] = {0};
    SV_BOOL bIsWritable = SV_FALSE;
    const sint32 s32StorageDev = 3;
    STORAGE_PATH_S stStorageLogPath[s32StorageDev] = {{STORAGE_MAIN_SD1, STORAGE_PATH_SDCARD1},
                                                          {STORAGE_EXTRA_SD, STORAGE_PATH_UDISK},
                                                          {STORAGE_INNER_EMMC, STORAGE_PATH_EMMC}};

#if USING_STORAGE
#if (defined(BOARD_IPCR20S4))
        if (STORAGE_IsWritable(stStorageLogPath[0].eStoragePos))
        {
            bIsWritable = SV_TRUE;
            strcpy(szPath, stStorageLogPath[0].szStoragePath);
        }
#elif (defined(BOARD_ADA47V1))
        if (STORAGE_IsWritable(stStorageLogPath[2].eStoragePos))
        {
            bIsWritable = SV_TRUE;
            strcpy(szPath, stStorageLogPath[2].szStoragePath);
        }
#elif (defined(BOARD_ADA32C4))
		if (STORAGE_IsWritable(stStorageLogPath[0].eStoragePos))
		{
			bIsWritable = SV_TRUE;
			strcpy(szPath, stStorageLogPath[0].szStoragePath);
		}
#elif (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32E1))
        if (STORAGE_IsWritable(stStorageLogPath[1].eStoragePos))
        {
            bIsWritable = SV_TRUE;
            strcpy(szPath, stStorageLogPath[1].szStoragePath);
        }
#else
        for (i = 0; i < s32StorageDev; i++)
        {
            if (STORAGE_IsWritable(stStorageLogPath[i].eStoragePos))
            {
                bIsWritable = SV_TRUE;
                strcpy(szPath, stStorageLogPath[i].szStoragePath);
                break;
            }
        }
#endif
#endif

    if (!bIsWritable)
    {
        strcpy(szPath, "/tmp");
    }

    if (NULL != pszStorageDir)
    {
        strcpy(pszStorageDir, szPath);
    }

    return bIsWritable;
}

static sint32 bb_alarm_ReadFileBuf(const char *pFile, char *pBuf, uint32 u32Size)
{
	print_level(SV_DEBUG,"pFile:%s\n", pFile);
	sint32 s32Fd = -1, s32ReadNum = -1;

	if( (s32Fd = open(pFile, O_RDWR)) < 0 )
	{
	//	print_level(SV_DEBUG,"Open error! file:%s\n", pFile);
		return SV_FAILURE;
	}

	bb_alarm_CloseEvec(s32Fd);
	s32ReadNum = read(s32Fd, pBuf, u32Size);
	if( s32ReadNum <= 0 )
	{
		print_level(SV_ERROR,"Read file:%s error! %s\n", pFile, strerror(errno));
		close(s32Fd);
		unlink(pFile);
		return SV_FAILURE;
	}

	close(s32Fd);
	//print_level(SV_DEBUG,"SV_SUCCESS\n");
	return SV_SUCCESS;
}

sint32 bb_alarm_CheckAndDeletEmptyDir(const char *pDirPath)
{
	struct dirent* file = NULL;
	DIR *pDir;
	SV_BOOL bDeleteFlag = SV_TRUE;

	if( !(pDir=opendir(pDirPath)) )
	{
		//print_level(SV_DEBUG,"Open dir:%s error!\n", pDirPath);
		return SV_FAILURE;
	}
	else
	{
	//	print_level(SV_DEBUG,"Open dir:%s successfully!\n", pDirPath);
	}

	while( (file = readdir(pDir)) != NULL )
	{
		if(strcmp(file->d_name,".")==0 || strcmp(file->d_name,"..")==0)
		{
			continue;
		}
		else
		{
			bDeleteFlag = SV_FALSE;
		}
	}
	closedir(pDir);

	if(bDeleteFlag)
	{
		print_level(SV_DEBUG,"delete dir: %s\n", pDirPath);
		remove(pDirPath);
		return SV_SUCCESS;
	}

	return SV_FAILURE;
}

sint32 bb_alarm_GetofflineDirInfo(const char *pDirPath, uint8 u8Type, char *pBuf, uint32 u32Size, SV_BOOL bUpload)
{

	struct dirent* file = NULL;
	DIR *pDir;
	char szPath[STORAGE_FULLPATH_LEN] = {0};
	char szOldFile[STORAGE_FULLPATH_LEN] = {0};
	char szOldDir[STORAGE_FULLPATH_LEN] = {0};
	sint32 s32Ret = SV_FAILURE;
	SV_BOOL bHaveDir = SV_FALSE;
	strncpy(szOldFile, "CanData_999999", STORAGE_FULLPATH_LEN);
	strncpy(szOldDir, "999999", STORAGE_FULLPATH_LEN);

	if( bb_alarm_CheckAndDeletEmptyDir(pDirPath) == SV_SUCCESS )
	{
		return SV_FAILURE;
	}

	if (NULL == strstr(pDirPath, BB_ALARMINFO_PATH_MEM) && !bb_alarm_getStorageStatus(NULL))
	{
		return SV_FAILURE;
	}

	if( !(pDir=opendir(pDirPath)) )
	{
		//print_level(SV_DEBUG,"Open dir:%s error!\n", pDirPath);
		return SV_FAILURE;
	}
	else
	{
		//print_level(SV_DEBUG,"Open dir:%s successfully!\n", pDirPath);
	}

	while( (file = readdir(pDir)) != NULL )
	{
		if(strcmp(file->d_name,".")==0 || strcmp(file->d_name,"..")==0)
		{
			continue;
		}

		if(file->d_type & DT_DIR)
		{
			if(u8Type == OFFLINE_CANDATA_TYPE)
			{
				bHaveDir = SV_TRUE;
				if( strncmp(szOldDir, file->d_name, STORAGE_FULLPATH_LEN) > 0 )
				{
					strncpy(szOldDir, file->d_name, STORAGE_FULLPATH_LEN);
				}
			}
			else
			{
				snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s/", pDirPath, file->d_name);
				if( bb_alarm_GetofflineDirInfo(szPath, u8Type, pBuf, u32Size, bUpload) == SV_SUCCESS)
				{
					s32Ret = SV_SUCCESS;
					break;
				}
			}
		}
		else
		{
			if(u8Type == OFFLINE_CANDATA_TYPE)
			{
				if( strncmp(szOldFile, file->d_name, STORAGE_FULLPATH_LEN) > 0 )
				{
					strncpy(szOldFile, file->d_name, STORAGE_FULLPATH_LEN);
				}
			}
			else
			{
				if(bUpload && strstr(file->d_name, "_BBupload") && u8Type == OFFLINE_BBALARM_TYPE)
				{
					snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", pDirPath, file->d_name);
					if( bb_alarm_ReadFileBuf(szPath, pBuf, u32Size) == SV_SUCCESS)
					{
						strncpy(szAlarmInfoUploadFilePath, szPath, STORAGE_FULLPATH_LEN);
						s32Ret = SV_SUCCESS;

					}
				}
				else if(!bUpload && !strstr(file->d_name, "_BBupload"))//获取没有上传过的
				{
					snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", pDirPath, file->d_name);
					if( bb_alarm_ReadFileBuf(szPath, pBuf, u32Size) == SV_SUCCESS)
					{
						strncpy(g_chAlarmPath, szPath, STORAGE_FULLPATH_LEN);
						s32Ret = SV_SUCCESS;
						break;
					}
				}
			}

		}
	}
	closedir(pDir);

	return s32Ret;

}

sint32 bb_alarm_GetofflineInfo(uint8 u8Type, char *pBuf, uint32 u32Size, SV_BOOL bUpload)
{
    char szStoragePath[32] = {0};
	char szSaveDir[128] = {0}, szPath[STORAGE_FULLPATH_LEN];

	if(u8Type == OFFLINE_BBALARM_TYPE)
	{
		strncpy(szSaveDir, BB_OFFLINE_BBALARM_DIR, 128);
	}
	else
	{
		strncpy(szSaveDir, BB_OFFLINE_INFO_DIR, 128);
	}

	snprintf(szPath, STORAGE_FULLPATH_LEN, "%s/%s", BB_ALARMINFO_PATH_MEM, szSaveDir);
	if( bb_alarm_GetofflineDirInfo(szPath, u8Type, pBuf, u32Size, bUpload) == SV_SUCCESS)
	{
		return SV_SUCCESS;
	}

	if (bb_alarm_getStorageStatus(szStoragePath))
	{
		snprintf(szPath, STORAGE_FULLPATH_LEN, "%s/%s", szStoragePath, szSaveDir );
		if( bb_alarm_GetofflineDirInfo(szPath, u8Type, pBuf, u32Size, bUpload) == SV_SUCCESS)
		{
			return SV_SUCCESS;
		}

	}

	return SV_FAILURE;

}

sint32 bb_alarm_DelFileAndEmptyDir(char *szFilePath)
{
    char szStoragePath[32] = {0};
	char szDir[STORAGE_FULLPATH_LEN], szTempDir[STORAGE_FULLPATH_LEN];
	uint8 u8len = 0;

	//print_level(SV_INFO,"szFilePath:%s\n", szFilePath);
	if( unlink(szFilePath) != 0 )
	{
		print_level(SV_ERROR,"file:%s unlink error:%s\n", szFilePath, strerror(errno));
	}

    bb_alarm_getStorageStatus(szStoragePath);
    u8len = strlen(szStoragePath);// /mnt/sdcard

	bb_alarm_getFileDir(szFilePath, szDir);
	while(strlen(szDir) > u8len) // /data/ssd
	{
		strncpy(szTempDir, szDir, STORAGE_FULLPATH_LEN);
		//print_level(SV_DEBUG,"szTempDir:%s\n", szTempDir);
		bb_alarm_CheckAndDeletEmptyDir(szDir);
		bb_alarm_getFileDir(szTempDir, szDir);
	}

	return SV_SUCCESS;
}

sint32 bb_alarm_DelofflineInfo(char *pszPath)
{
	bb_alarm_DelFileAndEmptyDir(pszPath);
	print_level(SV_DEBUG,"delete file:%s\n", pszPath);

	return SV_SUCCESS;
}

/******************************************************************************
	功能: 获取附件请求生成
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 waitReq()
{
	if(g_stClip.eStatus != E_REQ)
		return SV_FAILURE;

	if(g_lis_Appendix.empty())
		return SV_FAILURE;


	memcpy(&g_stClip.stInfo, &g_lis_Appendix.front(), sizeof(g_stClip.stInfo));
	g_lis_Appendix.pop_front();
	g_stClip.eStatus = E_GETFILE;

	return SV_SUCCESS;
}

uint32 getMaxClipTime()
{
	sint32 s32Ret;
	sv_config_st	st_config;
	st_config.e_type = CONFIG_TYPE_MEDIA;
	st_config.st_media.e_opcode = SV_CONFIG_OPCODE_MEDIA_RECORD;
	s32Ret = BB_GetConfig(&st_config);
	if(s32Ret != SV_SUCCESS)
		return SV_FAILURE;
	return st_config.st_media.st_record.u32_alarm_recfile_len;
}

uint32 getClipTime()
{
	sint32 s32Ret;
	sv_config_st	st_config;
	st_config.e_type = CONFIG_TYPE_MEDIA;
	st_config.st_media.e_opcode = SV_CONFIG_OPCODE_MEDIA_RECORD;
	s32Ret = BB_GetConfig(&st_config);
	if(s32Ret != SV_SUCCESS)
		return SV_FAILURE;
	return st_config.st_media.st_record.u32_alarm_rec_time_dur;
}

/******************************************************************************
	功能: 查找请求对应的报警信息照片
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 IsAlarmHavePic(char *pAlarmTime)

{
	std :: list < string >	List;
	STStorageSingleList stPicList;
	stPicList.eType = STORAGE_DIR_REC_JPG;
	stPicList.pList = (void *)&List;
	memcpy(stPicList.szAlarmTime,pAlarmTime,strlen(pAlarmTime));

	if(SV_SUCCESS == BbGetAlarmFile(&stPicList))
	{

		if(List.size()>0)
		{
			print_level(SV_DEBUG,"alarmtime[%s] have pic:%s \n",pAlarmTime,List.front().c_str());
			return SV_SUCCESS;
		}

	}

	return SV_FAILURE;
}

/******************************************************************************
	功能: 查找请求对应的报警信息文件
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 getAlarmFile()
{
	if(g_stClip.eStatus != E_GETFILE)
		return SV_FAILURE;

	cJSON* pRoot, *pTemp, *pOther;
	struct tm tmLocal;
	char chName[128] = {0};
	char chPath[256] = {0};
	char chBuf[2048] = {0};
	uint8	u8Index;
	int32_t	s32Fd = -1;
	char*	pCh = NULL;
	struct timezone tz;
	struct timeval tvNow;
	gettimeofday(&tvNow, &tz);
	tvNow.tv_sec += (tz.tz_minuteswest * 60);
	time_t u32FileTime = 0;
	uint8 u8AlarmFileCount;
	uint8 u8TmpBcd[6] = {0};

	//部标是2000年开始，1~12月，1~31日
	tmLocal.tm_year = 100 + g_stClip.stInfo.stSubiaoMark.u8Bcd[0];
	tmLocal.tm_mon = g_stClip.stInfo.stSubiaoMark.u8Bcd[1] - 1;
	tmLocal.tm_mday = g_stClip.stInfo.stSubiaoMark.u8Bcd[2] ;
	tmLocal.tm_hour = g_stClip.stInfo.stSubiaoMark.u8Bcd[3];
	tmLocal.tm_min = g_stClip.stInfo.stSubiaoMark.u8Bcd[4];
	tmLocal.tm_sec = g_stClip.stInfo.stSubiaoMark.u8Bcd[5];

	snprintf(chName, 128, "%04d%02d%02d%02d%02d%02d_%d",(1900 + tmLocal.tm_year), (1 + tmLocal.tm_mon), tmLocal.tm_mday, tmLocal.tm_hour, tmLocal.tm_min, tmLocal.tm_sec, g_stClip.stInfo.stSubiaoMark.u8SerialNum);
	GetAlarmPath(chName, chPath, sizeof(chPath));

	snprintf(g_stClip.chAlarmTime, 128, "%04d%02d%02d%02d%02d%02d",
		(1900 + tmLocal.tm_year), (1 + tmLocal.tm_mon), tmLocal.tm_mday,tmLocal.tm_hour, tmLocal.tm_min, tmLocal.tm_sec);


	if(access(chPath, F_OK) == 0)
	{
		print_level(SV_INFO,"find file[%s] \n", chPath);
	}
	else
	{
		print_level(SV_ERROR,"file name[%s],path[%s] not exist\n", chName,chPath);
	}

	if(u8Index == (sizeof(g_chNode)/sizeof(g_chNode[0])))
	{
		print_level(SV_ERROR,"can not find alarm file\n");
		goto fail;
	}

	//解析这个文件，获取开始时间、结束时间、类型、掩码等参数
	if(COMMON_ReadFile(chPath, chBuf, 2048) != SV_SUCCESS)
	{
		print_level(SV_ERROR,"read file error\n");
		goto fail;
	}

	strncpy(g_stClip.chAlarmPath, chPath, 256);

	//Json解析
	pRoot = cJSON_Parse(chBuf);
	if(pRoot == NULL)
	{
		print_level(SV_ERROR,"Error before: [%s]\n",cJSON_GetErrorPtr());
		goto fail;
	}

	//获取相同时间的序号
	if(pCh = strrchr(g_stClip.chAlarmPath, '_'))
	{
		g_stClip.u8Serial = atoi(pCh+1);
	}

	if((pTemp = cJSON_GetObjectItem(pRoot, "Type")))
	{
		g_stClip.u8Type = pTemp->valueint;
	}

	if((pOther = cJSON_GetObjectItem(pRoot, "Other")))
	{
		if((pTemp = cJSON_GetObjectItem(pOther, "alarmRecord")))
		{
			g_stClip.u8AlarmRecord = pTemp->valueint;
		}
	}

	cJSON_Delete(pRoot);
	//g_stClip.bExistAlarmVideo = SV_TRUE;
	g_stClip.eStatus = E_CLIP;
	return SV_SUCCESS;

fail:
     //如果报警信息文件被删除了，那搜索录像文件链表
	/*if(SV_SUCCESS == IsAlarmHavePic(g_stClip.chAlarmTime))
	{
		//g_stClip.bExistAlarmVideo = SV_FALSE;
		g_stClip.u8AlarmRecord = 2;
		g_stClip.eStatus = E_CLIP;
		return SV_SUCCESS;
	}*/

	//g_stClip.bExistAlarmVideo = SV_FALSE;
	g_stClip.eStatus = E_REQ;
	return SV_FAILURE;
}

/******************************************************************************
	功能: 把照片添加到报警上传附件中
	参数:
	返值: 上传个照片数
	应用:
******************************************************************************/
sint32 AddAlarmPic(sint32 s32VidCnt)
{
	struct tm tmLocal;
	//照片个数
	sint32 s32PiCnt = 0;
	//照片路径
	char s8AlarmPicPath[128] = { 0 };
	char chFullPath[256] = {0};
	char chFile[256] = {0};

	sint32 s32Chn = 0;
	SV_MEDIA_FILE_NAME_PARAMS_ST stFileNameParams;
	REC_TYPE_E enAlarmType;

	std :: list < string >	List;
	STStorageSingleList stPicList;
	stPicList.eType = STORAGE_DIR_REC_JPG;
	memcpy(stPicList.szAlarmTime,g_stClip.chAlarmTime,strlen(g_stClip.chAlarmTime));
	stPicList.pList = (void *)&List;

	if(SV_SUCCESS == BbGetAlarmFile(&stPicList))
	{
		print_level(SV_INFO,"pic list.size:%d \n", List.size());
		for(std::list<string>::iterator it = List.begin(); it != List.end(); it++)
		{
			memset(s8AlarmPicPath, 0x0, sizeof(s8AlarmPicPath));
			COMMON_GetFileDir((char *)it->c_str(), s8AlarmPicPath);//获取出目录
			COMMON_CutOutName((char *)it->c_str(),chFile);
			print_level(SV_INFO,"pic name:%s s8AlarmPicPath:%s \n", it->c_str(), s8AlarmPicPath);

				//照片没改名的改下名
			sscanf(chFile, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
					,&stFileNameParams.s32Year\
					,&stFileNameParams.s32Month\
					,&stFileNameParams.s32Day\
					,&stFileNameParams.s32Hour\
					,&stFileNameParams.s32Minute\
					,&stFileNameParams.s32Second\
					,&stFileNameParams.cEventType\
					,&stFileNameParams.s32Duration\
					,&stFileNameParams.s32Size\
					,&stFileNameParams.s32Width\
					,&stFileNameParams.s32Height\
					,&stFileNameParams.s32FrameRate\
					,&stFileNameParams.s32BitRate\
					,&stFileNameParams.cPlateNum\
					,&stFileNameParams.s32ChNum\
					,&stFileNameParams.u32DeviceID\
					,&stFileNameParams.u8Flag\
					,&stFileNameParams.s32Msec\
					,&stFileNameParams.s32PreRecordMsec\
					,&stFileNameParams.cVersionNum\
					,&stFileNameParams.cCustomerNum\
					,&stFileNameParams.cTimeZone\
					,&stFileNameParams.cDST\
					,&stFileNameParams.cFileType);

				enAlarmType = bb_TransString2RecType(stFileNameParams.cEventType);
				snprintf(chFullPath, 256, "%s/%02d_%02d_%02x%02x_%02d_%s.jpg",
					s8AlarmPicPath, FILE_TYPE_PIC, s32Chn,
					getSubiaoAlarmModuleType(enAlarmType),
					getSubiaoSubAlarmType(enAlarmType),
					g_stClip.u8Serial, g_stClip.stInfo.u8AlarmNum);

				//rename(it->c_str(), chFullPath);
				SAFE_CP(it->c_str(), chFullPath);
				print_level(SV_INFO,"serial:%d fullpath:%s \n",g_stClip.u8Serial, chFullPath);
				if( access(chFullPath, F_OK) == 0 && COMMON_GetFileSize(chFullPath) > 0)
				{
					memcpy(g_stClip.chUpload[s32PiCnt+s32VidCnt], chFullPath, 256);
					s32PiCnt++;
				}

		}
		g_stClip.u32Count += s32PiCnt;
	}

	print_level(SV_INFO,"g_stClip.u32Count:%d u32PiCnt:%d \n", g_stClip.u32Count, s32PiCnt);
	return s32PiCnt;
}


/******************************************************************************
	功能: 把视频添加到报警上传附件中
	参数:
	返值: 上传个视频数
	应用:
******************************************************************************/
sint32 AddAlarmVideo()
{
	struct tm tmLocal;
	//视频个数
	sint32 s32VideoCnt = 0;
	//视频路径
	char s8AlarmVideoPath[128] = { 0 };
	char chFullPath[256] = {0};
	char chFile[256] = {0};

	sint32 s32Chn = 0;
	SV_MEDIA_FILE_NAME_PARAMS_ST stFileNameParams;
	REC_TYPE_E enAlarmType;
	sint32 s32VideoNum = 0;

	std :: list < string >	List;
	STStorageSingleList stVideoList = {0};
	stVideoList.eType = STORAGE_DIR_REC_ALARM;
	memset(stVideoList.szAlarmTime,0,20);
	memcpy(stVideoList.szAlarmTime,g_stClip.chAlarmTime,strlen(g_stClip.chAlarmTime));
	stVideoList.pList = (void *)&List;



	if(SV_SUCCESS == BbGetAlarmFile(&stVideoList))
	{
		print_level(SV_INFO,"alarmTime:%s   video list.size:%d \n",stVideoList.szAlarmTime, List.size());

		for(std::list<string>::iterator it = List.begin(); it != List.end(); it++,s32VideoNum++)
		{
			memset(s8AlarmVideoPath, 0x0, sizeof(s8AlarmVideoPath));
			COMMON_GetFileDir((char *)it->c_str(), s8AlarmVideoPath);//获取出目录
			COMMON_CutOutName((char *)it->c_str(),chFile);
			print_level(SV_INFO,"video name:%s s8AlarmPicPath:%s \n", it->c_str(), s8AlarmVideoPath);

				//视频没改名的改下名
			sscanf(chFile, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
					,&stFileNameParams.s32Year\
					,&stFileNameParams.s32Month\
					,&stFileNameParams.s32Day\
					,&stFileNameParams.s32Hour\
					,&stFileNameParams.s32Minute\
					,&stFileNameParams.s32Second\
					,&stFileNameParams.cEventType\
					,&stFileNameParams.s32Duration\
					,&stFileNameParams.s32Size\
					,&stFileNameParams.s32Width\
					,&stFileNameParams.s32Height\
					,&stFileNameParams.s32FrameRate\
					,&stFileNameParams.s32BitRate\
					,&stFileNameParams.cPlateNum\
					,&stFileNameParams.s32ChNum\
					,&stFileNameParams.u32DeviceID\
					,&stFileNameParams.u8Flag\
					,&stFileNameParams.s32Msec\
					,&stFileNameParams.s32PreRecordMsec\
					,&stFileNameParams.cVersionNum\
					,&stFileNameParams.cCustomerNum\
					,&stFileNameParams.cTimeZone\
					,&stFileNameParams.cDST\
					,&stFileNameParams.cFileType);

				enAlarmType = bb_TransString2RecType(stFileNameParams.cEventType);
				snprintf(chFullPath, 256, "%s/%02d_%02d_%02x%02x_%02d_%s.%s",
					s8AlarmVideoPath, FILE_TYPE_VIDEO, s32Chn,
					getSubiaoAlarmModuleType(enAlarmType),
					getSubiaoSubAlarmType(enAlarmType),
					g_stClip.u8Serial, g_stClip.stInfo.u8AlarmNum,
					stFileNameParams.cFileType);//文件后缀名

				//rename(it->c_str(), chFullPath);//重命名
				SAFE_CP(it->c_str(), chFullPath);
				print_level(SV_INFO,"serial:%d fullpath:%s \n",g_stClip.u8Serial, chFullPath);
				if( access(chFullPath, F_OK) == 0 && COMMON_GetFileSize(chFullPath) > 0)
				{
					memcpy(g_stClip.chUpload[g_stClip.u8Serial++], chFullPath, 256);
					g_stClip.u32Count++;
					s32VideoCnt ++;
				}

		}

	}

	//print_level(SV_INFO,"g_stClip.u32Count:%d u32VideoCnt:%d \n", g_stClip.u32Count, s32VideoCnt);
	return s32VideoCnt;
}

/******************************************************************************
	功能: 找到报警信息,调用媒体接口生成附件文件
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 waitClip()
{
	if(g_stClip.eStatus != E_CLIP)
		return SV_FAILURE;

	char chFile[128] = {0};
	char chPath[128] = {0};
	char chFullPath[256] = {0};
	sint32 s32PiCnt = 0;
	sint32 s32VidCnt = 0;
	static sint32 s32WaitCount = 0;
	uint32 u32MaxClipTimeOut = 10;
	uint32 u32RecDuration = 10;
	char chAlarmName[128] = { 0 };


	snprintf(chAlarmName, 128, "%s_%d",g_stClip.chAlarmTime, g_stClip.stInfo.stSubiaoMark.u8SerialNum);

	u32MaxClipTimeOut = getMaxClipTime() + 3;//获取报警视频最大时长，再加上3秒
	u32RecDuration = getClipTime();

	sv_state_event_set_clip_st	stClipInfo;
	sv_state_event_get_alarm_list_st	stClipList;
	sv_state_media_record_parse_filename_st	stFileInfo;

	strncpy(stClipInfo.sz_start_time, g_stClip.chStart, sizeof(stClipInfo.sz_start_time));
	strncpy(stClipInfo.sz_end_time, g_stClip.chEnd, sizeof(stClipInfo.sz_end_time));

	stClipInfo.u8_type = g_stClip.u8Type;
	stClipInfo.u32_mask = g_stClip.u32Mask;

	if(g_stClip.u8AlarmRecord == 1 || g_stClip.u8AlarmRecord == 3)
	{

		//waitClip()这个接口，每个报警信息调用一次，如果出现连续报警，第一个报警信息有视频，后续的报警没有视频;由于每个报警都是独立处理，这里简化处理只等待60s
		//待优化：除了第一个报警需要等待视频生成，后续的报警可以不等待
		s32VidCnt = AddAlarmVideo();
		if(s32VidCnt > 0)
		{
			if(g_stClip.u8AlarmRecord == 3) //视频获取成功，再获取图片
			{
				s32PiCnt = AddAlarmPic(s32VidCnt);
			}

			for(int32_t i = 0; i < g_stClip.u32Count; i++)
			{
				print_level(SV_INFO,"g_stClip.chUpload[%d]:%s \n", i, g_stClip.chUpload[i]);
			}

			g_stClip.eStatus = E_UPLOAD;
			s32WaitCount = 0;
			return SV_SUCCESS;
		}
		else
		{
			//print_level(SV_INFO,"wait video clip,alarmTime:%s waitCount:%d !!!!!\n",g_stClip.chAlarmTime,s32WaitCount);//视频文件还没生成，等待
			if(s32WaitCount++ > 60 )//等到视频获取超时 60秒 ，如果有图片就只发送图片
			{
				if(g_stClip.u8AlarmRecord == 3)
				{
					s32PiCnt = AddAlarmPic(s32VidCnt);
					if(s32PiCnt > 0)
					{
						for(int32_t i = 0; i < g_stClip.u32Count; i++)
						{
							print_level(SV_INFO,"g_stClip.chUpload[%d]:%s \n", i, g_stClip.chUpload[i]);
						}
						g_stClip.eStatus = E_UPLOAD;
						s32WaitCount = 0;
						return SV_SUCCESS;
					}
				}

				EraseAlarmPath(chAlarmName);
				g_stClip.u32Count = 0;
				g_stClip.u8Serial = 0;
				s32WaitCount = 0;
				print_level(SV_ERROR,"can not find alarm index file!!!!not pic either video，timeiout:%d \n",u32MaxClipTimeOut);
				g_stClip.eStatus = E_REQ;
			}

		}

	}
	else if(g_stClip.u8AlarmRecord == 2)
	{
		g_stClip.u32Count = 0;
		g_stClip.u8Serial = 0;
		s32PiCnt = AddAlarmPic(0);//没有视频，传入0
		if(s32PiCnt > 0)
		{
			for(int32_t i = 0; i < g_stClip.u32Count; i++)
			{
				print_level(SV_INFO,"g_stClip.chUpload[%d]:%s \n", i, g_stClip.chUpload[i]);
			}

			g_stClip.eStatus = E_UPLOAD;
			s32WaitCount = 0;
			return SV_SUCCESS;
		}
		else
		{
			print_level(SV_INFO,"BbSetMainClip fail delete\n");
			//bb_alarm_DelofflineInfo(g_stClip.chAlarmPath);
			if(s32WaitCount++ >= u32RecDuration)
			{
				EraseAlarmPath(chAlarmName);
				g_stClip.u32Count = 0;
				g_stClip.u8Serial = 0;
				s32WaitCount = 0;
				print_level(SV_ERROR,"can not find alarm index file!!!!not pic either video\n");
				g_stClip.eStatus = E_REQ;
			}

			g_stClip.eStatus = E_REQ;
		}
	}

	return SV_FAILURE;
}

/******************************************************************************
	功能: 等待部标网络上传完成
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 waitUpload()
{
	uint8 u8Abnormal = SV_TRUE;

	if(g_stClip.eStatus != E_UPLOAD)
		return SV_FAILURE;

	for(int32_t i = 0; i < g_stClip.u32Count; i++)
	{
		if(access(g_stClip.chUpload[i], F_OK) == 0)
		{
			u8Abnormal = SV_FALSE;
		}
	}

	if(u8Abnormal)
	{
		g_stClip.eStatus = E_REQ;
	}

	print_level(SV_INFO,"wait upload\n");
	return SV_SUCCESS;
}


/******************************************************************************
	功能: 根据附件请求生成附件并等待获取
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
void* bbAlarmThread(void* arg)
{
	prctl(PR_SET_NAME, "bbAlarmThread");
	while(1)
	{
		waitReq();
		getAlarmFile();
		waitClip();
		waitUpload();
		sleep(1);
	}

	return NULL;
}

/******************************************************************************
	功能: 测试线程
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
void* bbAlarmTest(void* arg)
{
	STSbAlarmFileUpload	stReq;
	STUploadList stUploadlist = {0};

	STAlarmInfo	stAlarm;
	print_level(SV_INFO,"start bbAlarmTest\n");
	sleep(30);

	while(1)
	{
		if(GetAlarmInfo(&stAlarm) ==  SV_SUCCESS)
		{
			sleep(10);
			SetAlarmInfo(&stAlarm);
			sleep(10);

			memcpy(stReq.stSubiaoMark.u8Bcd, stAlarm.u8Time, 6);
			memcpy(stReq.u8AlarmNum, "abcdefghigklmnopqrstuvwxyzabcdef", 32);
			SetAlarmAppendix(stReq);
			sleep(10);
			while(1)
			{
				if(GetAlarmAppendix(&stUploadlist) == SV_SUCCESS)
				{
					print_level(SV_INFO,"GetAlarmAppendix success[%s]\n", stUploadlist.chPath);
					//SetAlarmAppendixUpload();
					break;
				}
				else
				{
					sleep(10);
				}
			}
		}
		else
		{
			print_level(SV_INFO,"no alarm\n");
			sleep(10);
		}
	}

	return NULL;
}

/******************************************************************************
	功能: 根据附件请求生成附件并等待获取
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 BbAlarmInit()
{
	pthread_t	bbId, testId;

	pthread_attr_t attr;
	pthread_attr_init(&attr);
	pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
	if(pthread_create(&bbId, NULL, bbAlarmThread, NULL) != 0)
	{
		print_level(SV_ERROR,"Create gps_storage_pid thread failed err[%s]\n",strerror(errno));
		return -1;
	}

#if 0
	if(pthread_create(&testId, NULL, bbAlarmTest, NULL) != 0)
	{
		print_level(SV_ERROR,"Create gps_storage_pid thread failed err[%s]\n",strerror(errno));
		return -1;
	}
#endif

	return 0;
}

/******************************************************************************
	功能: 获取报警信息，用于判断是否有报警需要上传
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 GetAlarmInfo(STAlarmInfo* pstInfo)
{
	cJSON* pRoot, *pTemp, *pSensor, *pOther;
	char chPath[256] = {0};
	char chBuf[2048] = {0};
	char* pCh = NULL;
	struct timezone tz;
	struct timeval tvNow;
	gettimeofday(&tvNow, &tz);
	tvNow.tv_sec += (tz.tz_minuteswest * 60);

	if(bb_alarm_GetofflineInfo(OFFLINE_BBALARM_TYPE, chBuf, 2000, SV_FALSE) != SV_SUCCESS)
	{
		//print_level(SV_ERROR,"SV_DVR_GetofflineInfo fail!\n");
		return SV_FAILURE;
	}
	print_level(SV_DEBUG,"offline info:\n%s\n",chBuf);
	pRoot = cJSON_Parse(chBuf);
	if (!pRoot)
	{
		print_level(SV_ERROR,"Error before: [%s]\n",cJSON_GetErrorPtr());
		return SV_FAILURE;
	}

	//获取相同时间的序号
	if(pCh = strrchr(g_chAlarmPath, '_'))
	{
		pstInfo->u8Serial = atoi(pCh+1);
		print_level(SV_INFO,"serial [%d]\n", pstInfo->u8Serial);
	}

	//1~4 右后左前
	if((pTemp = cJSON_GetObjectItem(pRoot, "Type")) != NULL)
	{
		pstInfo->u8Type = getSubiaoAlarmModuleType(pTemp->valueint);
		pstInfo->u8SubType = getSubiaoSubAlarmType(pTemp->valueint);
		print_level(SV_ERROR,"type:0x%x subtype:0x%x \n",pstInfo->u8Type, pstInfo->u8SubType);
	}


	if((pTemp = cJSON_GetObjectItem(pRoot, "DeviceId")) != NULL)
	{
		pstInfo->u32Index = pTemp->valueint;
	}

	if((pTemp = cJSON_GetObjectItem(pRoot, "DriverId")) != NULL)
	{
		memcpy(pstInfo->chId, pTemp->valuestring, (strlen(pTemp->valuestring) > 7)?7:strlen(pTemp->valuestring));
	}

	if((pSensor = cJSON_GetObjectItem(pRoot, "SensorValue")) != NULL)
	{
		if((pTemp = cJSON_GetObjectItem(pSensor, "Lat")) != NULL)
		{
			pstInfo->u32Lat = pTemp->valuedouble*1000000;
		}

		if((pTemp = cJSON_GetObjectItem(pSensor, "Lng")) != NULL)
		{
			pstInfo->u32Lon = pTemp->valuedouble*1000000;
		}

		if((pTemp = cJSON_GetObjectItem(pSensor, "Speed")) != NULL)
		{
			pstInfo->u8Speed = (uint8)pTemp->valueint;
		}

		if((pTemp = cJSON_GetObjectItem(pSensor, "Altitude")) != NULL)
		{
			pstInfo->u16Alt = (uint16)pTemp->valuedouble;
		}

		if((pTemp = cJSON_GetObjectItem(pSensor, "Status")) != NULL)
		{
			pstInfo->u16CarStatus = (uint16)pTemp->valueint;
		}
	}

	if((pOther = cJSON_GetObjectItem(pRoot, "Other")) != NULL)
	{
		struct tm tmTime, tmLocal;
		if((pTemp = cJSON_GetObjectItem(pOther, "Time")) != NULL)
		{
			sscanf((const char*)pTemp->valuestring, "%04d-%02d-%02d %02d:%02d:%02d",
				&tmTime.tm_year, &tmTime.tm_mon, &tmTime.tm_mday, &tmTime.tm_hour, &tmTime.tm_min, &tmTime.tm_sec);

			tmTime.tm_mon -= 1;
			tmTime.tm_year -= 1900;
			COMMON_UTC2Local(&tmTime, &tmLocal);

			pstInfo->u8Time[0] = tmLocal.tm_year-100;
			pstInfo->u8Time[1] = tmLocal.tm_mon + 1;
			pstInfo->u8Time[2] = tmLocal.tm_mday;
			pstInfo->u8Time[3] = tmLocal.tm_hour + tz.tz_minuteswest/60;
			pstInfo->u8Time[4] = tmLocal.tm_min + tz.tz_minuteswest%60;
			pstInfo->u8Time[5] = tmLocal.tm_sec;
		}
	}

	cJSON_Delete(pRoot);

	char *pAlarmFile = NULL;
	if(NULL != (pAlarmFile = strrchr(g_chAlarmPath, '/')))
	{
		if(!strncasecmp(pAlarmFile + 1,INVALID_ALARM_FILE_HEAR, strlen(INVALID_ALARM_FILE_HEAR)))
		{
			/* 报警信息文件名带有1900开头，必定无法产生附件，直接删掉 */
			unlink(g_chAlarmPath);
		}
	}

	return SV_SUCCESS;
}

/******************************************************************************
	功能: 合肥协力定制
	参数:
	返值: SV_TRUE 有附件 SV_FALSE 没有附件
	应用:
******************************************************************************/
SV_BOOL IsBbAlarmHaveAttachs(STAlarmInfo* pstInfo)
{
	if(SUBIAO_ALARM_DMS == pstInfo->u8Type)
	{
		/*if(E_DMS_FATIGUE == pstInfo->u8SubType ||
		   E_DMS_CALLING == pstInfo->u8SubType ||
		   E_DMS_SMOKING == pstInfo->u8SubType)
		{
			return SV_TRUE;
		}
		else
		{
			return SV_FALSE;
		}*/
		return SV_TRUE;
	}
	else if(SUBIAO_ALARM_ADAS == pstInfo->u8Type)
	{
		return SV_TRUE;
	}
	else if(SUBIAO_ALARM_BSD == pstInfo->u8Type)
	{
		return SV_TRUE;
	}

	return SV_FALSE;
}

/******************************************************************************
	功能: 报警事件上传完成
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 SetAlarmInfo(STAlarmInfo* pstInfo)
{
	print_level(SV_INFO,"Rename alarm [%s]\n", g_chAlarmPath);
	if(SV_TRUE == IsBbAlarmHaveAttachs(pstInfo))
	{
		bb_alarm_RenameofflineInfo(g_chAlarmPath);
	}
	else
	{
		bb_alarm_DelofflineInfo(g_chAlarmPath);
		return SV_SUCCESS;
	}

	pthread_mutex_lock(&g_alarmpath_mutex);
	g_lis_alarmpath.push_back(g_chAlarmPath);
	pthread_mutex_unlock(&g_alarmpath_mutex);
	return SV_SUCCESS;
}

/******************************************************************************
	功能: 生成报警附件请求
	参数: u8Id 报警标识号，u8Num报警编号
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 SetAlarmAppendix(STSbAlarmFileUpload	stReq)
{
	g_lis_Appendix.push_back(stReq);
	print_level(SV_INFO,"u8AlarmNum:%s \n\n", stReq.u8AlarmNum);

	return SV_SUCCESS;
}

/******************************************************************************
	功能: 获取报警附件
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 GetAlarmAppendix(STUploadList* stInfo)
{
	if(g_stClip.eStatus != E_UPLOAD)
		return SV_FAILURE;

	stInfo->u32Count = g_stClip.u32Count;
	for(int32_t i = 0; i < stInfo->u32Count; i++)
	{
		stInfo->chPath[i] = g_stClip.chUpload[i];
	}
	memcpy(&stInfo->stInfo, &g_stClip.stInfo, sizeof(g_stClip.stInfo));

	return SV_SUCCESS;
}

/******************************************************************************
	功能: 报警附件上传完成
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 SetAlarmAppendixUpload(char *pAlarmId)
{
	if(g_stClip.eStatus != E_UPLOAD)
		return SV_FAILURE;

	char chAlarmName[128] = { 0 };
	char szUploadedName[STORAGE_FULLPATH_LEN] = {0};

	snprintf(chAlarmName, 128, "%s_%d",g_stClip.chAlarmTime, g_stClip.stInfo.stSubiaoMark.u8SerialNum);
	//照片路径
	//char s8AlarmPicPath[128];
	//memset(s8AlarmPicPath, 0x0, sizeof(s8AlarmPicPath));

	//删除目录下的照片
	for(int32_t i = 0; i < g_stClip.u32Count; i++)
	{
		if(access(g_stClip.chUpload[i], F_OK) == 0)
		{
			print_level(SV_INFO,"unlink [%s]\n", g_stClip.chUpload[i]);
			unlink(g_stClip.chUpload[i]);
		}
	}

	print_level(SV_INFO,"s8AlarmTime:%s alarmpath:%s\n",g_stClip.chAlarmTime,g_stClip.chAlarmPath);
	bb_alarm_DelofflineInfo(g_stClip.chAlarmPath);
	EraseAlarmPath(chAlarmName);

	g_stClip.eStatus = E_REQ;
	g_stClip.u32Count = 0;
	g_stClip.u8Serial = 0;
	return SV_SUCCESS;
}

/******************************************************************************
	功能: 根据报警名字获取报警路径
	参数:
	返值: 0 成功，-1 失败；
	应用:
******************************************************************************/
sint32 GetAlarmPath(char *pInFileName,char *pOutFilePath, int len)
{
	if(NULL == pInFileName || NULL == pOutFilePath)
	{
		return SV_FAILURE;
	}

	pthread_mutex_lock(&g_alarmpath_mutex);
	while(g_lis_alarmpath.size() > 1000)
	{
		g_lis_alarmpath.pop_front();
	}
	for(list<string>::iterator iter = g_lis_alarmpath.begin(); iter != g_lis_alarmpath.end(); iter++)
	{
		//print_level(SV_DEBUG,"--------------------------listname:%s findname:%s \n",iter->c_str(), pInFileName);
		if(NULL != strstr(iter->c_str(), pInFileName))//上传过的不要再上传
		{
			strncpy(pOutFilePath, iter->c_str(), len);
			break;
		}
	}
	pthread_mutex_unlock(&g_alarmpath_mutex);

	return SV_SUCCESS;
}

/******************************************************************************
	功能: 删除list里面路径
	参数:
	返值:
	应用:
******************************************************************************/
void EraseAlarmPath(char *pInFileName)

{
	pthread_mutex_lock(&g_alarmpath_mutex);
	for(list<string>::iterator iter = g_lis_alarmpath.begin(); iter != g_lis_alarmpath.end(); )
	{
		if(NULL != strstr(iter->c_str(), pInFileName))
		{
			print_level(SV_WARN,"erase FileName[%s] \n",pInFileName);
			g_lis_alarmpath.erase(iter++);
			break;
		}
		else
		{
			iter++;
		}
	}

	pthread_mutex_unlock(&g_alarmpath_mutex);
}

