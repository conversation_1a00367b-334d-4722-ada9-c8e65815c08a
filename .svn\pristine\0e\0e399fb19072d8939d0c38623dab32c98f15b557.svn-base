/******************************************************************************
Copyright (C) 2022-2024 广州敏视数码科技有限公司版权所有.

文件名：rs485.h

日期: 2022-12-16

文件功能描述: 定义485通讯接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#ifndef _RS485_H_
#define _RS485_H_

#include "common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/******************************************************************************
 * 函数功能: 初始化RS485模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RS485_Init(CFG_SYS_PARAM *pstSysParam);

/******************************************************************************
 * 函数功能: 去初始化RS485模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RS485_Fini();

/******************************************************************************
 * 函数功能: 启动RS485模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RS485_Start();

/******************************************************************************
 * 函数功能: 停止RS485模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RS485_Stop();

/******************************************************************************
 * 函数功能: 配置RS485模块工作参数
 * 输入参数: pstSysParam --- 系统参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RS485_SetConfig(CFG_SYS_PARAM *pstSysParam);

/******************************************************************************
 * 函数功能: 通过RS485发送数据
 * 输入参数: pszData --- 数据指针 | u32Len --- 数据长度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RS485_SendData(char *pszData, uint32 u32Len);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _RS485_H_ */

