/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：sharefifo.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-12-06

文件功能描述: 定义共享媒体队列功能函数 (单生产者-多消费者模型)

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _SHARE_FIFO_H_
#define _SHARE_FIFO_H_

#include "common.h"
#include "media.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define SFIFO_MAIN_STREAM   "/var/mainStream"   /* 主码流键值 */
#define SFIFO_SUB_STREAM    "/var/subStream"    /* 子码流键值 */
#define SFIFO_AUD_STREAM    "/var/audStream"    /* 音频流键值 */
#define SFIFO_PIC_STREAM    "/var/picStream"    /* 图片流键值 */
#define SFIFO_PTZ_STREAM    "/var/ptzStream"    /* 云台客户端拉流键值 */
#define SFIFO_PTZ_MPPSTREAM "/var/ptzMppStream" /* 云台媒体层拉流键值 */
#define SFIFO_IR_SUB_STREAM "/var/irStream"     /* 红外码录像子码流键值 */
#define SFIFO_CMS_TALK		"/var/talkStream"	/* 对讲音频流键值*/
#if defined(BOARD_ADA32IR)
#define SFIFO_IR_VMIX_STREAM "/var/irVmixStream"     /* 红外码Vmix码流键值 */
#endif

#if defined(BOARD_DMS31V2)
/* 录像那边已经开辟了自己的预录队列，sharefifo共享队列不再需要那么大，改回10s */
#define SFIFO_MULTIPLE_NUM  10
#else
#define SFIFO_MULTIPLE_NUM  10
#endif

#if (defined(BOARD_IPCR20S3))
#define SFIFO_VIDEO_QUE_SIZE   (3*1024*1024)                /* 视频队列长度 */
#define SFIFO_SUB_QUE_SIZE     (1*1024*1024)//(512*1024)    /* 子视频队列长度 */
#define SFIFO_PIC_QUE_SIZE     (3*1024*1024)                /* 图片队列长度 */
#define SFIFO_AUDIO_QUE_SIZE   (96*1024)                    /* 音频队列长度 */
#define SFIFO_VIDEO_RESERVE    (512*1024)                   /* 视频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_SUB_RESERVE      (160*1024)//(96*1024)        /* 子视频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_PIC_RESERVE      (512*1024)                   /* 图片队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_AUDIO_RESERVE    (32*1024)                    /* 音频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_VIDEO_MIN_COVER  (300*1024)                   /* 视频每次最小覆盖数据大小 */
#define SFIFO_SUB_MIN_COVER    (100*1024)//(64*1024)        /* 子视频每次最小覆盖数据大小 */
#define SFIFO_PIC_MIN_COVER    (300*1024)                   /* 图片每次最小覆盖数据大小 */
#define SFIFO_AUDIO_MIN_COVER  (24*1024)                    /* 音频每次最小覆盖数据大小 */
#elif (defined(BOARD_IPCR20S4))
#define SFIFO_VIDEO_QUE_SIZE   (8*1024*1024)    /* 视频队列长度 */
#define SFIFO_SUB_QUE_SIZE     (2*1024*1024)//(5*1024*1024)     /* 子视频队列长度 */
#define SFIFO_AUDIO_QUE_SIZE   (96*1024)        /* 音频队列长度 */
#define SFIFO_VIDEO_RESERVE    (1*1024*1024)    /* 视频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_SUB_RESERVE      (512*1024)//(1*1024*1024)        /* 子视频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_AUDIO_RESERVE    (32*1024)        /* 音频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_VIDEO_MIN_COVER  (500*1024)       /* 视频每次最小覆盖数据大小 */
#define SFIFO_SUB_MIN_COVER    (300*1024)//(500*1024)        /* 子视频每次最小覆盖数据大小 */
#define SFIFO_AUDIO_MIN_COVER  (24*1024)        /* 音频每次最小覆盖数据大小 */
#elif (defined(PLATFORM_RV1126))
#define SFIFO_VIDEO_QUE_SIZE   (SFIFO_MULTIPLE_NUM*1024*1024)   /* 视频队列长度 */
#define SFIFO_AUDIO_QUE_SIZE   (96*1024)        /* 音频队列长度 */
#define SFIFO_VIDEO_RESERVE    (1*1024*1024)    /* 视频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_AUDIO_RESERVE    (32*1024)        /* 音频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_VIDEO_MIN_COVER  (500*1024)       /* 视频每次最小覆盖数据大小 */
#define SFIFO_AUDIO_MIN_COVER  (24*1024)        /* 音频每次最小覆盖数据大小 */
#else
#define SFIFO_VIDEO_QUE_SIZE   (5*1024*1024)    /* 视频队列长度 */
#define SFIFO_AUDIO_QUE_SIZE   (96*1024)        /* 音频队列长度 */
#define SFIFO_VIDEO_RESERVE    (1*1024*1024)    /* 视频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_AUDIO_RESERVE    (32*1024)        /* 音频队列尾部保留空间大小(防止数据帧越界) */
#define SFIFO_VIDEO_MIN_COVER  (500*1024)       /* 视频每次最小覆盖数据大小 */
#define SFIFO_AUDIO_MIN_COVER  (24*1024)        /* 音频每次最小覆盖数据大小 */
#endif
#define SFIFO_VIDEO_MIN_QUE_SIZE    (2*SFIFO_VIDEO_RESERVE)                     /* 视频最小允许的队列长度 */
#define SFIFO_VIDEO_MAX_QUE_SIZE    (SFIFO_MULTIPLE_NUM*SFIFO_VIDEO_RESERVE)    /* 视频最大允许的队列长度 */
#define SFIFO_VIDEO_MAX_FRONT_TIME  (SFIFO_MULTIPLE_NUM*1000)   /* 视频队列允许前移的最大时长(s) */
#define SFIFO_SUB_MIN_QUE_SIZE      (2*SFIFO_SUB_RESERVE)       /* 子视频最小允许的队列长度 */
#define SFIFO_SUB_MAX_QUE_SIZE      (10*SFIFO_SUB_RESERVE)      /* 子视频最大允许的队列长度 */
#define SFIFO_PIC_MIN_QUE_SIZE      (2*SFIFO_PIC_RESERVE)       /* 视频最小允许的队列长度 */
#define SFIFO_PIC_MAX_QUE_SIZE      (10*SFIFO_PIC_RESERVE)      /* 视频最大允许的队列长度 */
#define SFIFO_AUDIO_MIN_QUE_SIZE    (2*SFIFO_AUDIO_RESERVE)     /* 音频最小允许的队列长度 */
#define SFIFO_AUDIO_MAX_QUE_SIZE    (10*SFIFO_AUDIO_RESERVE)    /* 音频最大允许的队列长度 */
#define SFIFO_MAX_DATA_NUM      8               /* 最大缓存列表数目 */
#define MSHEAD_FLAG             0x534d5653      /* "SVMS" - SharpVision Media Standard V1.0 */

/* 媒体数据帧头结构定义 */
typedef struct tagMediaStandardHead_S
{
    uint32 flag;            /* MSHEAD_FLAG */
    uint32 mshsize : 8;     /* 媒体头信息大小(MAX size=256) */
    uint32 msdsize : 20;    /* 媒体数据流大小 */
    uint32 algorithm : 4;   /* 媒体编码标准,ISO_... */
    uint32 type : 2;        /* 0-P frame, 1-I frame, 2-Audio */
    uint32 width : 12;      /* 画面宽度(单位: 像素) */
    uint32 height : 12;     /* 画面高度(单位: 像素) */
    uint32 reserve : 6;     /* 保留 */
    uint32 serial;          /* 码流的帧序号 */
    uint64 pts;             /* 当前帧时间(单位: us);表示从公元1970年1月1日0时0分0秒算起至今的UTC时间所经过的微秒数 */
    sint8 data[];           /* 紧跟着为段信息或者媒体数据 */
} SFIFO_MSHEAD;


/* 媒体缓存数据列表 */
typedef struct tagMediaDataAddr_S
{
    uint32 u32DataCnt;                      /* 数据包个数 */
    uint8 *pau8Addr[SFIFO_MAX_DATA_NUM];    /* 数据指针 */
    uint32 au32Len[SFIFO_MAX_DATA_NUM];     /* 数据长度 */
} SFIFO_MDADDR;

/* 视频流属性 */
typedef struct tagVideoAttr_S
{
    SV_BOOL  bValid;          /* 属性是否有效 */
    uint32   u32FrameRate;    /* 帧率(fps) */
    uint32   u32Bitrate;      /* 码率(kbps) */
    uint32   u32Width;        /* 画面宽度 */
    uint32   u32Height;       /* 画面高度 */
    SV_BOOL  bExtraValid;     /* 额外信息是否生效 */
    SV_BOOL  bNoExtraData;    /* 是否需要额外信息 */
    uint8    au8VpsData[32];  /* VPS数据 */
    uint32   u32VpsLen;       /* VPS长度 */
    uint8    au8SpsData[64];  /* SPS数据 */
    uint32   u32SpsLen;       /* SPS长度 */
    uint8    au8PpsData[32];  /* PPS数据 */
    uint32   u32PpsLen;       /* PPS长度 */
    uint8    au8SeiData[32];  /* SEI数据 */
    uint32   u32SeiLen;       /* SEI长度 */
    ENCODE_E enCodeType;      /* 视频编码类型 */
} SFIFO_VIDEO_ATTR;

/* 图片流属性 */
typedef struct tagPicAttr_S
{
    SV_BOOL  bValid;          /* 属性是否有效 */
    uint32   u32FrameRate;    /* 帧率(fps) */
    uint32   u32Bitrate;      /* 码率(kbps) */
    uint32   u32Width;        /* 画面宽度 */
    uint32   u32Height;       /* 画面高度 */
    SV_BOOL  bNoExtraData;    /* 是否需要额外信息 */
} SFIFO_PIC_ATTR;


/* 音频流属性 */
typedef struct tagAudioAttr_S
{
    SV_BOOL bValid;         /* 属性是否有效 */
    uint32  u32SampleRate;  /* 音频采样率(Hz) */
    uint32  u32BitWidth;    /* 音频采样位宽(bit) */
    AUDIO_ENCODE_TYPE_E enCodeType; /* 音频编码类型 */
} SFIFO_AUDIO_ATTR;

/* 媒体属性 */
typedef struct tagMediaAttr_S
{
    SFIFO_VIDEO_ATTR stMainStreamAttr;  /* 主码流视频属性 */
    SFIFO_VIDEO_ATTR stSubStreamAttr;   /* 子码流视频属性 */
    SFIFO_VIDEO_ATTR stPicStreamAttr;   /* 图片流属性 */
    SFIFO_AUDIO_ATTR stAudioStreamAttr; /* 音频流属性 */
#if defined(BOARD_ADA32IR)
    SFIFO_VIDEO_ATTR stVmixStreamAttr;  /* 红外Vmix码流属性 */
#endif
} SFIFO_MEDIA_ATTR;

/******************************************************************************
 * 函数功能: 创建共享队列
 * 输入参数: pszPathName --- 队列的键值路径: 作为打开共享队列的标识
             u32Size --- 队列大小: 表明满载的数据量 (SFIFO_VIDEO_RESERVE, SFIFO_MAX_QUE_SIZE]
 * 输出参数: ps32QueId --- 队列ID
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 非法队列大小值
             ERR_NULL_PTR - 传入指针为空
             ERR_EXIST - 队列已经存在
             ERR_NOMEM - 内存不足
             SV_FAILURE - 其它错误
 * 注意    : 对同一个pszPathNameq键值路径, 该函数只能被调用一次。
 *****************************************************************************/
extern sint32 SFIFO_CreateQue(char *pszPathName, uint32 u32Size, sint32 *ps32QueId);

/******************************************************************************
 * 函数功能: 销毁共享队列
 * 输入参数: s32QueId --- 队列ID
 * 输出参数:
 * 返回值  : SV_SUCCESS - 成功
             ERR_UNEXIST - 队列不存在
             ERR_DATAFALSE - 数据错误
             ERR_NOT_PERM - 不允许操作
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_DestroyQue(sint32 s32QueId);

/******************************************************************************
 * 函数功能: 查询者打开共享队列
 * 输入参数: pszPathName --- 队列的键值路径: 作为打开共享队列的标识
 * 输出参数: ps32QueId --- 队列ID
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             SV_FAILURE - 其它错误
 * 注意    : 仅仅提供查询信息, 无写入能力
 *****************************************************************************/
extern sint32 SFIFO_ForQueryOpen(char *pszPathName, sint32 *ps32QueId);

/******************************************************************************
 * 函数功能: 生产者打开共享队列
 * 输入参数: pszPathName --- 队列的键值路径: 作为打开共享队列的标识
 * 输出参数: ps32QueId --- 队列ID
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             ERR_NOT_PERM - 不允许重复打开
             SV_FAILURE - 其它错误
 * 注意    : 同一个队列只允许打开一次(只允许一个生产者)
 *****************************************************************************/
extern sint32 SFIFO_ForWriteOpen(char *pszPathName, sint32 *ps32QueId);

/******************************************************************************
 * 函数功能: 消费者打开共享队列
 * 输入参数: pszPathName --- 队列的键值路径: 作为打开共享队列的标识
 * 输出参数: ps32QueId --- 队列ID
             ps32ConsumerId --- 消费者ID
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             ERR_BUF_FULL - 消费者已满
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_ForReadOpen(char *pszPathName, sint32 *ps32QueId, sint32 *ps32ConsumerId);

/******************************************************************************
 * 函数功能: 消费者关闭共享队列
 * 输入参数: s32QueId --- 队列ID
             s32ConsumerId --- 消费者ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_UNEXIST - 队列或消费者不存在
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_ForReadClose(sint32 s32QueId, sint32 s32ConsumerId);

/******************************************************************************
 * 函数功能: 向共享队列写入一个媒体数据包
 * 输入参数: s32QueId --- 队列ID
             pstPacketHead --- 包头信息
             pstDataInfo --- 媒体数据缓存地址
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             ERR_BUF_FULL - buffer 已满
             SV_FAILURE - 其它错误
 * 注意    : pstPacketHead->(data,mshsize,msdsize)字段不需要填写, pstDataInfo提供要拷贝的数据地址
           整个媒体包数据长度不能超过 SFIFO_VIDEO_RESERVE
 *****************************************************************************/
extern sint32 SFIFO_WritePacket(sint32 s32QueId, SFIFO_MSHEAD *pstPacketHead, SFIFO_MDADDR *pstDataInfo);

/******************************************************************************
 * 函数功能: 从共享队列获取一个媒体数据包
 * 输入参数: s32QueId --- 队列ID
             s32ConsumerId --- 消费者ID
 * 输出参数: ppstPacket --- 媒体数据包指针
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             ERR_BUF_EMPTY - buffer 为空
             ERR_BADADDR - 队列内部地址错误
             SV_FAILURE - 其它错误
 * 注意    : 获取到的数据包要调用SFIFO_ReleasePacket进行释放
 *****************************************************************************/
extern sint32 SFIFO_GetPacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD **ppstPacket);

/******************************************************************************
 * 函数功能: 释放读取到的媒体数据包
 * 输入参数: s32QueId --- 队列ID
             s32ConsumerId --- 消费者ID
             pstPacket --- 媒体数据包
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_ReleasePacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD *pstPacket);

/******************************************************************************
 * 函数功能: 设置队列媒体数据属性
 * 输入参数: s32QueId --- 队列ID
             pstMediaAttr --- 媒体数据属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_SetMediaAttr(sint32 s32QueId, SFIFO_MEDIA_ATTR *pstMediaAttr);

/******************************************************************************
 * 函数功能: 获取队列媒体数据属性
 * 输入参数: s32QueId --- 队列ID
             pstMediaAttr --- 媒体数据属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_GetMediaAttr(sint32 s32QueId, SFIFO_MEDIA_ATTR *pstMediaAttr);

/******************************************************************************
 * 函数功能: 查看指定当前的消费者数量
 * 输入参数: s32QueId --- 队列ID
 * 输出参数: 无
 * 返回值  : <0 - 失败
             >=0 - 消费者数量
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_CurConsumerNum(sint32 s32QueId);

/******************************************************************************
 * 函数功能: 打印共享队列信息
 * 输入参数: s32QueId --- 队列ID
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
extern void SFIFO_PrintShmQueInfo(sint32 s32QueId);

/******************************************************************************
 * 函数功能: 录像消费者打开共享队列
 * 输入参数: pszPathName --- 队列的键值路径: 作为打开共享队列的标识
 * 输出参数: ps32QueId --- 队列ID
             ps32ConsumerId --- 消费者ID
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             ERR_BUF_FULL - 消费者已满
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_ForRecReadOpen(char *pszPathName, sint32 *ps32QueId, sint32 *ps32ConsumerId );

/******************************************************************************
 * 函数功能: 录像消费者关闭共享队列
 * 输入参数: s32QueId --- 队列ID
             s32ConsumerId --- 消费者ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_UNEXIST - 队列或消费者不存在
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_ForRecReadClose(sint32 s32QueId, sint32 s32ConsumerId);

/******************************************************************************
 * 函数功能: 录像释放读取到的媒体数据包
 * 输入参数: s32QueId --- 队列ID
             s32ConsumerId --- 消费者ID
             pstPacket --- 媒体数据包
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 SFIFO_RecReleasePacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD *pstPacket);

/******************************************************************************
 * 函数功能: 录像从共享队列获取一个媒体数据包
 * 输入参数: s32QueId --- 队列ID
             s32ConsumerId --- 消费者ID
 * 输出参数: ppstPacket --- 媒体数据包指针
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入指针为空
             ERR_UNEXIST - 队列不存在
             ERR_BUF_EMPTY - buffer 为空
             ERR_BADADDR - 队列内部地址错误
             SV_FAILURE - 其它错误
 * 注意    : 获取到的数据包要调用SFIFO_RecReleasePacket进行释放
 *****************************************************************************/
extern sint32 SFIFO_RecGetPacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD **ppstPacket);

/******************************************************************************
 * 函数功能: 录像移动读取帧位置到相对当前最新PTS帧的前置时间(ms), 用于预录
 * 输入参数: s32QueId --- 队列ID
             s32ConsumerId --- 消费者ID
             s32FrontMs --- 前置时间(ms) [0, 10000]
 * 输出参数: pu64Pts --- 移动到的读取位置PTS值
 * 返回值  : SV_SUCCESS - 成功
             ERR_UNEXIST - 队列不存在
             SV_FAILURE - 其它错误
 * 注意    : 要先确保该消费者没有占用帧, 已经调用SFIFO_RecReleasePacket释放帧
 *****************************************************************************/
extern sint32 SFIFO_RecMoveReadFrontPtsFrm(sint32 s32QueId, sint32 s32ConsumerId, sint32 s32FrontMs, uint64 *pu64Pts);

/******************************************************************************
 * 函数功能: SFIFO 重置看门狗状态
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 由于长时间不拉流,主码流和子码流会处于低帧率状态。在配置参数的时候可能
           会出现问题，因此复位看门狗，让主码流和子码流重新恢复状态
 *****************************************************************************/
extern void SFIFO_Reset_Dog();


extern sint32 SFIFO_Start();

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _SHARE_FIFO_H_ */


