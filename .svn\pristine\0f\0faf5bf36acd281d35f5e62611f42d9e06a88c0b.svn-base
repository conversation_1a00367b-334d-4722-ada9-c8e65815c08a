/******************************************************************************
Copyright (C) 2018-2012 广州敏视数码科技有限公司版权所有.

文件名：wf61.c

作者: 胡钦洋版本: v1.0.0(初始版本号)   日期: 2018-08-07

文件功能描述: wf61模块功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "wf61.h"

sint32 wf61_STA_Config(SV_BOOL bEnable, char *pszSsid, char *pszPwd)
{
    return SV_SUCCESS;
}

sint32 wf61_AP_Config(WIFI_AUTH enWifiAuth, char *pszSsid, char *pszPassword, WIFI_FREQ enWifiFreq)
{
    return SV_SUCCESS;
}

sint32 wf61_Init(WIFI_CONF_S *pstWifiParam)
{
    return SV_SUCCESS;
}

sint32 wf61_Fini()
{
    return SV_SUCCESS;
}

sint32 wf61_ResetMoudule()
{
    return SV_SUCCESS;
}

uint32 wf61_GetSignalLevel(uint32 u32SteamKbps)
{ 
    return 100;
}

uint32 wf61_GetBandwidthLevel()
{
    uint u32Bandwidth = 50;
    
    return u32Bandwidth;
}