#! /bin/sh

echo "upgrade_start.sh"
S02bootstart_md5=""
S02bootstart_path=""
autoUpdate_md5=""
autoUpdate_path=""
update_path=""
config_md5=""
config_path=""
uboot_md5=""
uboot_path=""
kernel_md5=""
kernel_path=""
rootfs_path=""
reboot_flag=0

string_head=upgrade_start		#字符串头部
local_index=1					#log 的次数标识
max_local_num=50				#log 缓存的最大数量
log_path=/mnt/sdcard/upgrade_start.log 	#log 文件的路径

# 判断是否是数字
function isDigit()
{
	if [ "$1" -gt 0 ] 2>/dev/null ;then 
		return 0
	else 
		return 1
	fi 
}

# Log 初始化
function log_init()
{
	# 获取升级文件路径
	if [ -d "/mnt/sdcard" ]; then
		log_path="/mnt/sdcard/upgrade_start.log"
	elif [ -d "/tmp/sdcard" ]; then
		log_path="/tmp/sdcard/upgrade_start.log"
	else
		log_path="/boot/upgrade_start.log"
	fi

	head_line=`head -n 1 $log_path 2>/dev/null`
	if [ $? -ne 0 ]; then
		echo "$string_head local num [1] ">> $log_path
		echo "$string_head local num [1] "
		return
	fi

	local_num=`echo "$head_line" | awk -F "$string_head local num " "{print $2}" | awk -F '[][]' '{print $2}'`
	echo local_num: $local_num
	if ! isDigit $local_num; then
		sed -i "1c $string_head local num [1]" $log_path
	elif [ $local_num -lt $max_local_num ]; then
		let local_num+=1
		sed -i "1c $string_head local num [$local_num]" $log_path
	else
		clear_line=`sed -n "2,2p" $log_path | awk '{print $1}'`
		tempname=`echo $clear_line | sed 's#\[#\\\[#g' | sed 's#\]#\\\]#g'`
		sed -i "/$tempname/d" $log_path
	fi

	tail_line=`tail -n 1 $log_path`
	if [ $? -ne 0 ]; then
		return
	fi
	tmp_index=`echo $tail_line | awk '{print $1}' | awk -F '[][]' '{print $2}'`
	if ! isDigit $tmp_index; then
		return
	fi
	let local_index=tmp_index+1
}

function log_print()
{
	string=upgrade_start:$1
	echo $string_head[$local_index] $string >> $log_path
	echo $string_head[$local_index] $string
}

log_init
log_print "start"

fw_printenv | grep hardwareVersion
if [ $? -ne 0 ]; then
	log_print "fw_setenv hardwareVersion $(cat /etc/hardwareVersion)"
	fw_setenv hardwareVersion $(cat /etc/hardwareVersion)
fi

if [ ! -f "/etc/config.xml" ]; then
	# 禁止RK1126工程升级HD900工程的普通升级包
	ls $update_path/var/ | grep rootfs
	if [ $? -ne 0 ]; then
		log_print "Is not recovery packet!!!"
		cd /tmp/sdcard/
		find $(cat /etc/hardwareVersion)_upgrade* -exec mv {} invalid-{} \;
		cd -
		umount /tmp/sdcard/
		sync
		reboot
		exit 2
	fi
	
	md5sum $config_path | grep $config_md5
	if [ $? -ne 0 ]; then
		log_print "config.xml md5sum check error!"
		exit 3
	fi
	md5sum $uboot_path | grep $uboot_md5
	if [ $? -ne 0 ]; then
		log_print "uboot md5sum check error!"
		exit 3
	fi
	log_print "mkdir -p /tmp/sdcard/recovery_backup"
	mkdir -p /tmp/sdcard/recovery_backup
	rm -rf /tmp/sdcard/recovery_backup/*
	if [ -d /root/params ]; then
		log_print "cp -rf /root/params /tmp/sdcard/recovery_backup/"
		cp -rf /root/params /tmp/sdcard/recovery_backup/
	fi
	if [ -f /root/System_info ]; then
		log_print "cp /root/System_info /tmp/sdcard/recovery_backup/"
		cp /root/System_info /tmp/sdcard/recovery_backup/
	fi
	if [ -f /etc/ip.conf ]; then
		log_print "cp /etc/ip.conf /tmp/sdcard/recovery_backup/"
		cp /etc/ip.conf /tmp/sdcard/recovery_backup/
	fi
	if [ -f /etc/rtl_hostapd.conf ]; then
		log_print "cp /etc/rtl_hostapd.conf /tmp/sdcard/recovery_backup/"
		cp /etc/rtl_hostapd.conf /tmp/sdcard/recovery_backup/
	fi
	if [ -f /etc/DeviceUid ]; then
		echo "cp /etc/DeviceUid /tmp/sdcard/recovery_backup/"
		cp /etc/DeviceUid /tmp/sdcard/recovery_backup/
	fi
	if [ -d /root/ID ]; then
		log_print "cp -rf /root/ID /tmp/sdcard/recovery_backup/"
		cp -rf /root/ID /tmp/sdcard/recovery_backup/
	fi
	ubootver_dst=`hexdump -s 124 -n 4 -e '4/1 "%02x" "\n"' $uboot_path`
	cp $update_path/bin/fw_printenv /bin/
	ln -s fw_printenv /bin/fw_setenv
	cp $update_path/etc/fw_env.config /etc/
	flash_erase /dev/mtd0 0 0
	nandwrite -p /dev/mtd0 $uboot_path
	fw_setenv  ubootver $ubootver_dst
	fw_setenv hardwareVersion $(cat /etc/hardwareVersion)
	fw_setenv updaterootfs y
	fw_setenv update y
	reboot_flag=1
	# 拷贝内核和文件系统
	cp $kernel_path /boot/
	cp $rootfs_path /boot/ 
	# 在最后才拷贝配置文件
	cp $config_path /etc/config.xml
	cp $config_path /etc/config_bak1.xml
	cp $config_path /etc/config_bak2.xml
	cp $config_path /etc/config_default.xml
	sync
fi

if [ -n "$uboot_path" ] && [ -n "$kernel_path" ]; then
	ubootver_src=`hexdump -s 124 -n 4 -e '4/1 "%02x" "\n"' /dev/mtd0`
	ubootver_dst=`hexdump -s 124 -n 4 -e '4/1 "%02x" "\n"' $uboot_path`
	kernelver_src=`hexdump -s 124 -n 4 -e '4/1 "%02x" "\n"' /dev/mtd1`
	kernelver_dst=`hexdump -s 124 -n 4 -e '4/1 "%02x" "\n"' $kernel_path`
	updaterootfs=`fw_printenv -n updaterootfs 2>/dev/null`
	
	log_print "ubootver_src ==> $ubootver_src"
	log_print "ubootver_dst ==> $ubootver_dst"
	log_print "kernelver_src ==> $kernelver_src"
	log_print "kernelver_dst ==> $kernelver_dst"
	if [ -n "$ubootver_dst" ] && [ "$ubootver_src"x != "$ubootver_dst"x ]; then
		log_print "flash_erase mtd0 and burn uboot"
		flash_erase /dev/mtd0 0 0
		nandwrite -p /dev/mtd0 $uboot_path
		fw_setenv ubootver $ubootver_dst
		fw_setenv kernelver $kernelver_src
		fw_setenv hardwareVersion $(cat /etc/hardwareVersion)
		fw_setenv updaterootfs $updaterootfs
	else
		fw_printenv | grep ubootver
		if [ $? -ne 0 ]; then
			log_print "fw_setenv ubootver $ubootver_src"
			fw_setenv ubootver $ubootver_src
		fi
	fi
	
	if [ -n "$kernelver_dst" ] && [ "$kernelver_src"x != "$kernelver_dst"x ]; then
		log_print "flash_erase mtd1 and burn kernel"
		fw_setenv update y
		flash_erase /dev/mtd1 0 0
		nandwrite -p /dev/mtd1 $kernel_path
		fw_setenv kernelver $kernel_dst
		fw_setenv update n
	else
		fw_printenv | grep kernelver
		if [ $? -ne 0 ]; then
			log_print "fw_setenv kernelver $kernelver_src"
			fw_setenv kernelver $kernelver_src
		fi
	fi
fi


# 判断S02bootstart脚本是否发生变化
if [ ! -f /etc/init.d/S98bootstart ] && [ -n $S02bootstart_path ] && [ -f "$S02bootstart_path" ]; then
    md5sum $S02bootstart_path | grep $S02bootstart_md5
    if [ $? -ne 0 ]; then
        log_print "S02bootstart md5sum check error!"
        exit 1
    fi

    diff /etc/init.d/S02bootstart $S02bootstart_path
    if [ $? -eq 0 ]; then
        log_print "S02bootstart no different."
    else
        fs_write_enable 1
        cp $S02bootstart_path /etc/init.d/tmp-S02bootstart
        md5sum /etc/init.d/tmp-S02bootstart | grep $S02bootstart_md5
        if [ $? -ne 0 ]; then
            log_print "tmp-S02bootstart md5sum check error!"
            fs_write_enable 0
            exit 1
        fi

        mv /etc/init.d/tmp-S02bootstart /etc/init.d/S02bootstart
        log_print "update S02bootstart successful!"
        reboot_flag=1
        fs_write_enable 0
    fi
fi

# 判断autoUpdate是否发生变化
if [ -n $autoUpdate_path ] && [ -f "$autoUpdate_path" ]; then
    md5sum $autoUpdate_path | grep $autoUpdate_md5
    if [ $? -ne 0 ]; then
        log_print "autoUpdate md5sum check error!"
        exit 1
    fi

    diff /boot/autoUpdate $autoUpdate_path
    if [ $? -eq 0 ]; then
        log_print "autoUpdate no different."
    else
        fs_write_enable 1
        cp $autoUpdate_path /boot/autoUpdate-tmp
        md5sum /boot/autoUpdate-tmp | grep $autoUpdate_md5
        if [ $? -ne 0 ]; then
            log_print "autoUpdate-tmp md5sum check error!"
            fs_write_enable 0
            exit 1
        fi

        mv /boot/autoUpdate-tmp /boot/autoUpdate
        log_print "update autoUpdate successful!"
        reboot_flag=1
        fs_write_enable 0
    fi
fi

if [ $reboot_flag -eq 1 ]; then
    log_print "reboot -f"
    sync
    umount /mnt/sdcard
    reboot -f
    exit 2
fi
