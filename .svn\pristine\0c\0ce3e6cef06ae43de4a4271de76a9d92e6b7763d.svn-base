#
# Link all library, and build the final excutable file
#

include ../../Makefile.param

# Link SV Common library
SV_COM_LIBS = -llog -lmsg -lsharefifo -lssl -lcrypto

# Link other SV libs
OTHER_SV_LIBS 	= -lcunit

ifeq ($(BOARD), WFCR10S1LOS)
else ifeq ($(BOARD), WFCR20S1_LOS)
else
IGNORE_THIS_BUILD = shellcmd
endif

ifeq ($(OSTYPE), liteos)
SRCS    := $(wildcard *.c)
SRCS	+= ../liteos/$(PLATFORM_DIR)/app_init.c
SRCS	+= ../liteos/$(PLATFORM_DIR)/sdk_init.c
LD_FLAGS	+= $(OTHER_SV_LIBS)
SYSTEM_LIB	= -lshellcmd -lm 
CUNIT_PLATFORM = $(PLATFORM_DIR)
endif

CFLAGS += -I../cunit/inc 
CPPFLAGS = $(CFLAGS)

TARGET_BIN	= test_shellcmd
LIB_DEPEND	= $(COMP_DEPEND)
LD_FLAGS	+= -L$(LIB_PATH) -L$(TOP_LIB) -L../cunit/lib/$(OSTYPE)/$(CUNIT_PLATFORM)
LD_FLAGS	+= $(SYSTEM_LIB) $(OTHER_SV_LIBS) $(SV_COM_LIBS)

COPY_TO_DIR = /work/nfs/
include $(BIN_AUTO_DEP_MK)

# vim:noet:sw=4:ts=4

