#! /bin/bash

check_shared_folders()
{
    #.host:/ on /mnt/hgfs type vmhgfs (rw,ttl=1)
	if [[ `pwd | grep "hgfs"` ]]; then
		${ECHO} "Cant execute this because you are now under windows share folder:/mnt/hgfs"
		${ECHO} "Please excute this in linux native file system"
		exit 1
	fi
}

check_necessary_file()
{
    for file in $@; do
        if [[ ! -f "$file" && ! -d "$file" ]]; then
            ${ECHO} "checking necessary file exists failed: $file"
            exit 1
        fi
    done
}

# input is a file list
# output is true or false, depend that have files newer then the first input file.
check_timestamp()
{
    if [ "$#" -lt 2 ]; then
        return;
    fi
    
    TARGET_FILE=$1
    if [ ! -e ${TARGET_FILE} ]; then
        return 1
    fi
    TARGET_TIME=`date +%s%N -r ${TARGET_FILE}`
    shift
     
    for file in $@; do
        if [[ ! -e ${file} ]];then
            continue;
        fi
        
        file_time=`date +%s%N -r ${file}`
        if [[ $file_time > ${TARGET_TIME} ]]; then
            return 1
        fi
    done
    
    return 0
}

add_executeable_perm()
{
	${CHMOD} 755 ${UPGRADE_MAKER}
	${CHMOD} 755 ${UPGRADE_UNMAKER}
	${CHMOD} 755 ${RESTORE_MAKER}
	${CHMOD} 755 ${GUI_LANG_TOOL}
	${CHMOD} a+x ${SCRIPTS_DIR}/*.sh
}

generate_platform_define()
{
	if [[ `strings key` == HB7208XT3 ]]; then
		${ECHO} PLATFORM=HI3521 > ${PLATFROM_DEFINE_FROM_KEY};
	elif [[ `strings key` == HB3520A ]]; then
		${ECHO} PLATFORM=HI3520A > ${PLATFROM_DEFINE_FROM_KEY};
	elif [[ `strings key` == HB3520D ]]; then
		${ECHO} PLATFORM=HI3520D > ${PLATFROM_DEFINE_FROM_KEY};
	elif [[ `strings key` == HB3520AU ]]; then
		${ECHO} PLATFORM=HI3520A_UCLIBC > ${PLATFROM_DEFINE_FROM_KEY};
	elif [[ `strings key` == HB3521U ]]; then
		${ECHO} PLATFORM=HI3521_UCLIBC > ${PLATFROM_DEFINE_FROM_KEY};
	elif [[ `strings key` == HB3535 ]]; then
		${ECHO} PLATFORM=HI3535 > ${PLATFROM_DEFINE_FROM_KEY};
	else
		${ECHO} PLATFORM=HI3531 > ${PLATFROM_DEFINE_FROM_KEY};
	fi
}

generate_4Mnor_binary()
{
    check_timestamp 4M-nor.bin uboot.bin uImage
    if [ $? -eq 1 ];then
        ${ECHO} "Generate 4M-nor.bin"
        ${RM} 4M-nor.bin
        dd if=/dev/zero     of=4M-nor.bin bs=1024 count=4096
        dd if=uboot.bin     of=4M-nor.bin bs=1024 count=512  conv=notrunc seek=0
        dd if=uImage        of=4M-nor.bin bs=1024 count=3584 conv=notrunc seek=512
    else
        ${ECHO} "4M-nor.bin is uptodate"
    fi
}

generate_usr_yaffs2()
{
    check_timestamp ${USR_YAFFS2_NAME} ${OUTPUT_HB_BK}/${USR_CRAMFS_NAME} ${OUTPUT_HB_BK}/${MD5_CHECKSUM_FILE}
    if [ $? -eq 1 ];then
        ${ECHO} "Generate ${USR_YAFFS2_NAME}"
        ${MK_YAFFS2} ${OUTPUT_HB_BK} ${USR_YAFFS2_NAME} ${YAFFS2_PAGE_SIZE} ${YAFFS2_ECC_TYPE}
    else
        ${ECHO} "${USR_YAFFS2_NAME} is uptodate"
    fi
}

pack_upgrade_binary()
{
    # Pack upgrade.bin should use Makefile dependency directly
    ${MAKE} ${UPGRADE_FILE_NAME}
}

pack_restore_binary()
{
    # ${RESTORE_MAKER} File Type defines
    RM_FT_DEFAULT=0
    RM_FT_CRAMFS=1
    RM_FT_YAFFS2=2
    RM_FT_JFFS2=3
    
    # ${RESTORE_MAKER} Flash Type defines
    RM_FLT_NOR=0
    RM_FLT_NAND=1
    
    if [[ ${SMALL_FLASH_SIZE} == true ]]; then
        check_necessary_file uboot.bin uImage ${ROOT_CRAMFS_NAME} ${USR_CRAMFS_NAME} ${WEB_CRAMFS_NAME} ${LOGO_JFFS2_NAME} ${CFG_JFFS2_NAME}
    	${RESTORE_MAKER} 	\
            uboot.bin 				${RM_FT_DEFAULT}	${RM_FLT_NOR}	0 			0x60000 	\
    	    uImage 					${RM_FT_DEFAULT}	${RM_FLT_NOR}	0x60000		0x180000 	\
    	    ${ROOT_CRAMFS_NAME} 	${RM_FT_CRAMFS} 	${RM_FLT_NOR}	0x1E0000 	0x100000	\
    	    ${USR_CRAMFS_NAME} 		${RM_FT_CRAMFS} 	${RM_FLT_NOR}	0x2E0000 	0x9A0000 	\
    	    ${WEB_CRAMFS_NAME} 		${RM_FT_CRAMFS} 	${RM_FLT_NOR}	0xC80000 	0x210000 	\
    	    ${LOGO_JFFS2_NAME}		${RM_FT_YAFFS2} 	${RM_FLT_NOR}	0xE90000 	0xE0000		\
    	    ${CFG_JFFS2_NAME}		${RM_FT_YAFFS2} 	${RM_FLT_NOR}	0xF70000 	0x80000
    else
        check_necessary_file uboot.bin uImage ${OUTPUT_USR_FS} ${CFG_YAFFS2_NAME}
        generate_4Mnor_binary
        generate_usr_yaffs2
        ${RESTORE_MAKER} 	\
    	    4M-nor.bin 				${RM_FT_DEFAULT}	${RM_FLT_NOR}	0			0x400000 	\
    	    ${ROOT_CRAMFS_NAME} 	${RM_FT_CRAMFS} 	${RM_FLT_NAND}	0 			0x1000000	\
    	    ${USR_YAFFS2_NAME} 		${RM_FT_YAFFS2} 	${RM_FLT_NAND}	0x1000000 	0x4000000 	\
    	    ${USR_YAFFS2_NAME} 		${RM_FT_YAFFS2} 	${RM_FLT_NAND}	0x5000000 	0x4000000 	\
    	    ${CFG_YAFFS2_NAME} 		${RM_FT_YAFFS2} 	${RM_FLT_NAND}	0x9000000 	0x7000000
    fi
    
    if [[ "$?" != 0 ]]; then
        echo "Pack ${RESTORE_FILE_NAME} failed"
        exit 1
    fi
}

unpack_upgrade_binary()
{
    # generate key
    ${UPGRADE_UNMAKER} -k ${UPGRADE_FILE_NAME}
    
    # generate platform_pack.mk
	generate_platform_define

	if [[ -e ./usr ]]; then
        ${ECHO} "**** \"usr\" already exist, please delete or rename it";
        ls usr -hl;
        ${ECHO} "**** You can try make unpack_clean to remove all files unpacked";
        exit 1;
	fi
	
	if [[ `strings key` == HB9000 || `strings key` == HB3535 ]]; then
		if [[ -f ${SLAVE_ROOT_UNPACK} || -d ${SLAVE_ROOT_UNPACK} ]]; then
            ${ECHO} "**** \"${SLAVE_ROOT_UNPACK}\" already exist, please delete or rename it";
            ls ${SLAVE_ROOT_UNPACK} -hl;
            ${ECHO} "**** You can try make unpack_clean to remove all files unpacked";
            exit 1;
		fi
	fi
	
	mkdir -p ${GUI_LANG_MERGED};
	mkdir -p ${GUI_LANG_SPLITED}/config/;
	
	if [[ `strings key` == HB3520D || `strings key` == HB3520AU || `strings key` == HB3521U ]]; then
		${ECHO} "Unpack Small flash ${UPGRADE_FILE_NAME}";
		${UPGRADE_UNMAKER} ${UPGRADE_FILE_NAME} uboot.bin uImage ${ROOT_CRAMFS_NAME} ${USR_CRAMFS_NAME} mnt.tar;
		tar xzvf mnt.tar;
		${RM} mnt.tar;
		tar xzvf mnt/logo/${GUI_LANG_TAR_NAME} -C ${GUI_LANG_MERGED};
		${RM} mnt/logo/${GUI_LANG_TAR_NAME};
		
		${MKDIR} -p ${TMP_MOUNT_DIR};
		# unpack output/${USR_CRAMFS_NAME} to output/usr
		sudo mount -o loop -t cramfs ${USR_CRAMFS_NAME} ${TMP_MOUNT_DIR};
		${CP} -r ${TMP_MOUNT_DIR} ./usr;
		sudo umount ${TMP_MOUNT_DIR};
		${RM} ${USR_CRAMFS_NAME};
		
		# unpack output/usr/bin/exec.cramfs to output/usr/bin/sys, output/usr/bin/daemon
		if [ -e ${OUTPUT_USR_BIN}/${EXEC_CRAMFS_NAME} ]; then
    		sudo mount -o loop -t cramfs ${OUTPUT_USR_BIN}/${EXEC_CRAMFS_NAME} ${TMP_MOUNT_DIR};
    		${CP} -r ${TMP_MOUNT_DIR} ./exec;
    		sudo umount ${TMP_MOUNT_DIR};
        else
            ${MKDIR} -p ./exec;
    	    ${MV} ${OUTPUT_USR_BIN}/sys ${OUTPUT_USR_BIN}/daemon ./exec
		fi
		${RM} ${TMP_MOUNT_DIR};
	else
		${ECHO} "Unpack Full flash ${UPGRADE_FILE_NAME}";
		${UPGRADE_UNMAKER} ${UPGRADE_FILE_NAME} uboot.bin uImage ${ROOT_CRAMFS_NAME} ${HB_NAME}.tar;   
		tar Pxzvf ${HB_NAME}.tar;
		${RM} ${HB_NAME}.tar;
		tar xzvf hb2/logo/${GUI_LANG_TAR_NAME} -C ${GUI_LANG_MERGED};
		${RM} hb2/logo/${GUI_LANG_TAR_NAME};
		
		${MKDIR} -p ${TMP_MOUNT_DIR};
		# unpack output/hb1/${USR_CRAMFS_NAME} to output/usr
		sudo mount -o loop -t cramfs ${HB_NAME}/${USR_CRAMFS_NAME} ${TMP_MOUNT_DIR};
		${CP} -r ${TMP_MOUNT_DIR} ./usr;
		sudo umount ${TMP_MOUNT_DIR};
		${RM} ${HB_NAME}/${USR_CRAMFS_NAME};
		
		# unpack output/usr/bin/exec.cramfs to output/usr/bin/sys, output/usr/bin/daemon
		if [ -e ${OUTPUT_USR_BIN}/${EXEC_CRAMFS_NAME} ]; then
    		sudo mount -o loop -t cramfs ${OUTPUT_USR_BIN}/${EXEC_CRAMFS_NAME} ${TMP_MOUNT_DIR};
    		${CP} -r ${TMP_MOUNT_DIR} ./exec;
    		sudo umount ${TMP_MOUNT_DIR};
    	else
    	    ${MKDIR} -p ./exec;
    	    ${MV} ${OUTPUT_USR_BIN}/sys ${OUTPUT_USR_BIN}/daemon ./exec
		fi
		
	    if [[ `strings key` == HB9000 || `strings key` == HB3535 ]]; then
			dd if=${OUTPUT_HB_BK_SLV}/${SLAVE_ROOT_FS_IMG} of=${SLAVE_ROOT_FS_CRAMFS} bs=64 skip=1;
			sudo mount -o loop -t cramfs ${SLAVE_ROOT_FS_CRAMFS} ${TMP_MOUNT_DIR};
			${CP} -r ${TMP_MOUNT_DIR}/ ${SLAVE_ROOT_UNPACK};
			sudo umount ${TMP_MOUNT_DIR};
			${RM} ${SLAVE_ROOT_FS_CRAMFS};
			${CP} -r ${SCRIPTS_DIR}/ ${SLAVE_CHIP_DIR};
		fi;
		
		${RM} ${TMP_MOUNT_DIR};
	fi > ${UNPACK_LOG}
    
	${GUI_LANG_TOOL} -d ${GUI_LANG_MERGED} ${GUI_LANG_SPLITED}/config/
}

unpack_restore_binary()
{
    # generate key
    ${RESTORE_MAKER} -k ${RESTORE_FILE_NAME}
    
    # generate platform_pack.mk
	generate_platform_define
    
	if [[ `strings key` == HB3520D || `strings key` == HB3520AU || `strings key` == HB3521U ]]; then
		${RESTORE_MAKER} -u ${RESTORE_FILE_NAME} uboot.bin uImage ${ROOT_CRAMFS_NAME} ${USR_CRAMFS_NAME} ${WEB_CRAMFS_NAME} ${LOGO_JFFS2_NAME} ${CFG_JFFS2_NAME};
	else
		${RESTORE_MAKER} -u ${RESTORE_FILE_NAME} 4M-nor.bin ${ROOT_CRAMFS_NAME} ${USR_YAFFS2_NAME} ${USR_YAFFS2_NAME} ${CFG_YAFFS2_NAME};
	fi
	
	if [[ "$?" != 0 ]]; then
        echo "Unpack ${RESTORE_FILE_NAME} failed"
        exit 1
    fi
}

clean_unpacked_files()
{
    ${RM} uboot.bin uImage
	${RM} uboot.hbgk.bin kernel.hbgk.bin 4M-nor.bin
	${RM} usr exec ${HB_NAME}
	${RM} *.cramfs *.jffs2 *.yaffs2
	${RM} usr.bin root.bin ${TAR_BIN_WITH_HEADER}
	${RM} mnt mnt.tar -Rf
	${RM} key hb2/logo/key
	${RM} ${PLATFROM_DEFINE_FROM_KEY}
	${RM} ${SLAVE_ROOT_UNPACK}
	${RM} ${OUTPUT_HB_BK}
	${RM} ${OUTPUT_HB2}
	${RM} ${UNPACK_LOG}
	${RM} ${SLAVE_CHIP_DIR}/scripts
	${RM} ${SLAVE_OUTPUT_DIR}/${SLAVE_ROOT_FS_CRAMFS}
	${RM} ${SLAVE_OUTPUT_DIR}/${SLAVE_ROOT_FS_IMG}
	${RM} ${GUI_LANG_MERGED} ${GUI_LANG_SPLITED} -Rf
}

main()
{
    # Check if we are in vmware shared folder
    check_shared_folders
    
    # Check if we miss some necessary files
    check_necessary_file "upgrade_unmaker" "upgrade_maker" "restore_maker" "gui_lang_tool" "Makefile"
    
    # Add executeable permission
    add_executeable_perm
    
    case $1 in
        pack_upgrade)
            ${ECHO} "Pack ${UPGRADE_FILE_NAME}";
            pack_upgrade_binary;
            ;;
        unpack_upgrade)
            ${ECHO} "Unpack ${UPGRADE_FILE_NAME}";
            unpack_upgrade_binary;
            ;;
        pack_restore)
            ${ECHO} "Pack ${RESTORE_FILE_NAME}";
            ${ECHO} "Note: This cmd CAN ONLY REPACK uboot.bin uImage usr_fs to ${RESTORE_FILE_NAME}";
            ${ECHO} "      Before this cmd, you must have executed below cmd IN ORDER:"
            ${ECHO} "      1) \$ make unpack_clean";
            ${ECHO} "      2) \$ make unpack_restore";
            ${ECHO} "      3) \$ make unpack";
            ${ECHO} "      4) Modify something, like *.ko, uboot.bin, uImage, sys;";
            ${ECHO} "      5) \$ make pack";
            ${ECHO} "      6) \$ make pack_restore";
            ${ECHO} "Press CTRL+C to abort"
            ${ECHO} "Press ENTER to continue"
            read
            pack_restore_binary;
            ;;
        unpack_restore) 
            ${ECHO} "Unpack ${RESTORE_FILE_NAME}";
            unpack_restore_binary;
            ;;
        unpack_clean)
            ${ECHO} "Clean all unpacked files";
            clean_unpacked_files;
            ;;
        *)
            ${ECHO} "Operation is not supportted";
            ;;
    esac
}

main $@
