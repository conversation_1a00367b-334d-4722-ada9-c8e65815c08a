/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名: test_onvif.c

作者: 许家铭    版本: v1.0.0(初始版本号)    日期: 2017-11-07

文件功能描述: 

版本: v1.0.0(最新版本号)
  
历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/

//-----------------------------------------------------------------------------
// Standard include files:
//-----------------------------------------------------------------------------
//
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#ifndef __HuaweiLite__
#include <sys/ioctl.h>
#include <sys/signal.h>
#include <sys/time.h>
#endif
#include "common.h"
#include "print.h"
#include "CUnit.h"
#include "Console.h"

#ifdef __HuaweiLite__
char *m_pszCmdName = "test_control";
#endif

extern "C" {
int app_main(int argc, char *argv[]);
void CU_console_run_tests(void);
}

extern void AddTests_itest_control();
void control_add_testslist(void)
{
    AddTests_itest_control();
}

#ifndef __HuaweiLite__
/* 中断退出 */
static void exit_handle(int signalnum)
{
    printf("catch signalnum %d!\n", signalnum);

    exit(EXIT_FAILURE);
}
#endif

/******************************************************************************
  函数功能: 自动化测试模块入口函数
  输入参数: argc: 输入的参数个数
            argv: 对应的参数
  输出参数: 无
  返回值  : =0-成功,<0-错误代码
  其他    : //其他说明
******************************************************************************/
int ipsys_log_level = SV_DEBUG;

#ifdef __HuaweiLite__
int app_main(int argc, char *argv[])
#else
int main(int argc, char **argv)
#endif
{
    sint32 s32Ret = 0;

#ifndef __HuaweiLite__
    /*捕获进程退出的系统消息*/
    if (SIG_ERR == signal(SIGINT, exit_handle))
    {
        printf("catch signal SIGKILL Error: %d, %s\n", errno, strerror(errno));
    }

    if (SIG_ERR == signal(SIGPIPE, SIG_IGN))
    {
        printf("catch signal SIGPIPE Error: %d, %s\n", errno, strerror(errno));
    }
#endif

    if (CU_initialize_registry()) 
    {
		printf("\nInitialize of test Registry failed.\n");
		return -1;
	}

    /* 添加测试用例 */
    control_add_testslist();

	CU_console_run_tests();

	CU_cleanup_registry();

    return 0;
}

