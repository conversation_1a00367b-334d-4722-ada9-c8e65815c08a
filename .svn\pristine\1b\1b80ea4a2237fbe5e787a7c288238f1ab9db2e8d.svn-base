/******************************************************************************
Copyright (C) 2020-2022 广州敏视数码科技有限公司版权所有.

文件名：cellular.c

作者: lyn       版本: v1.0.0(初始版本号)   日期: 2021-07-17

文件功能描述: 定义4G设备管理功能

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <errno.h>
#include <fcntl.h>

#include "print.h"
#include "../../../include/board.h"
#include "safefunc.h"
#include "cJSON.h"
#include "cellular.h"
#include "ec25.h"
#include "simcom.h"
#include "ec200u.h"
#include "ec200a.h"

//厂商ID
#define VID_GEMATO          "0061"
#define VID_SERRIA          "1199"
#define VID_QUECTEL         "05c6"
#define VID_HUAWEI          "12d1"
#define VID_TELIT           "0036"
#define VID_TELIT910_C1     "1201"
#define VID_YIYUAN          "2c7c"
#define VID_FIBOCOM         "1508:1001"
//厂商和设备ID
#define VPID_HUAWEI_MU709S_6         "12d1:1c25"
#define VPID_HUAWEI_ME909S_120       "12d1:15c1"
#define VPID_YIYUAN_EC25             "2c7c:0125"
#define VPID_EC200T                  "2c7c:6026"
#define VPID_EC25                    "2c7c:0125"
#define VPID_SIM7600                 "1e0e:9001"
#define VPID_EC200U                  "2c7c:0901"
#define VPID_EC200A					 "2c7c:6005"

#define CELLULAR_INFO_FILE           "/var/info/cellular"

#if (defined(BOARD_DMS31V2))
#define CELL_VCC_PIN_BAND            2
#define CELL_VCC_PIN_NUM             22
#define CELL_POWER_PIN_BAND          3
#define CELL_POWER_PIN_NUM           20
#define CELL_VCC_POWER_DOWN          0
#define CELL_VCC_POWER_ON            1
#define CELL_POWER_DOWN              1
#define CELL_POWER_ON                0

#elif(defined(BOARD_ADA32C4))
#define CELL_VCC_PIN_BAND            2
#define CELL_VCC_PIN_NUM             22
#define CELL_POWER_PIN_BAND          2
#define CELL_POWER_PIN_NUM           22
#define CELL_VCC_POWER_DOWN          0
#define CELL_VCC_POWER_ON            1
#define CELL_POWER_DOWN              1
#define CELL_POWER_ON                0


#else
#define CELL_VCC_PIN_BAND            0
#define CELL_VCC_PIN_NUM             0
#define CELL_POWER_PIN_BAND          0
#define CELL_POWER_PIN_NUM           0
#define CELL_VCC_POWER_DOWN          0
#define CELL_VCC_POWER_ON            0
#define CELL_POWER_DOWN              0
#define CELL_POWER_ON                0
#endif

#if(defined(BOARD_ADA32C4))
//4G_W_DIS G20 (simcom CFUN to 1)
#define SIMCOM_CFUN_GPIO_SET           "/root/gpio.sh 2 22 1"
#else
#define SIMCOM_CFUN_GPIO_SET           "/root/gpio.sh 3 23 1"
#endif


/* Cellular 模块控制信息 */
typedef struct tag_CellularInfo_S
{
    SV_BOOL                    bEnable;            /* 是否使能4G模块 */
    SV_BOOL                    bConfig;            /* 是否需要配置参数 */
    CELLULAR_MODULE_E          enModuleType;       /* 模块类型 */
    CELLULAR_SUB_MODULE_E      enSubModuleType;    /* 子模块类型 */
    CELLULAR_OPS_E             enModuleOps;        /* 模块操作 */
    CELLULAR_MODULE_PARAM_S    *pstModuleParam;    /* 子模块参数 */
    SV_BOOL                    bIsInsert;          /* SIM卡插入状态 */
    pthread_mutex_t            mutexLock;          /* 参数设置线程互斥锁 */
    SV_BOOL                    bRunning;           /* 线程运行状态 */
    uint32                     u32Tid;             /* 线程ID */
    CELLULAR_SETUP_S           stTmpSetUp;         /* 临时存放连接参数 */
    SV_BOOL                    bModuleExist;       /* 模块是否存在 */
}CELLULAR_INFO_S;

CELLULAR_INFO_S m_stCellularInfo = {0};
char *m_pszCellularType[7] = {"12d1:1c25", "12d1:15c1", "2c7c:0125", "2c7c:6026", "1e0e:9001", "2c7c:0901", "2c7c:6005"};
const char* pIpList[4] = {"*******","************","*******", "*********"};    //用于判断网络通断时ping的IP


#define RECV_BUFFER_LEN	512
#define SEND_BUFFER_LEN	140
#define AT_GET_ATI   "ATI\r"                      /* 显示MT标识信息:查询子模块版本*/


/******************************************************************************
 * 函数功能: 获取指令的文本返回内容,通过管道方式
 * 输入参数: cpInstruciton -- 指令 pBuf -- 应用提供的缓存区 u32Size -- 缓存大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 GetInsContext(const char* cpInstruciton, char* pszBuf, uint32 u32Size)
{
    FILE *fStream;

    if((fStream = popen(cpInstruciton, "r")) == NULL)
    {
        print_level(SV_ERROR, "popen %s error \n", cpInstruciton);
        return SV_FAILURE;
    }

    if((fread(pszBuf, sizeof(char), u32Size,  fStream)) < 1)
    {
        if(feof(fStream))
        {
            pclose(fStream);
            return SV_SUCCESS;
        }
        pclose(fStream);
        print_level(SV_ERROR, "fread %s error \n",cpInstruciton);
        return SV_FAILURE;
    }
    pclose( fStream );

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置模块VCC情况
 * 输入参数: bSwitch -- 模块上电开关 SV_TRUE:上电 SV_FALSE:掉电
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32  cellular_SwitchVCC(SV_BOOL bSwitch)
{
    sint32 s32Ret = 0;
    if(SV_FALSE == bSwitch)
    {
        s32Ret = BOARD_RK_SetGPIO(CELL_VCC_PIN_BAND, CELL_VCC_PIN_NUM, CELL_VCC_POWER_DOWN);
    }
    else
    {
        s32Ret = BOARD_RK_SetGPIO(CELL_VCC_PIN_BAND, CELL_VCC_PIN_NUM, CELL_VCC_POWER_ON);
    }

    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 4g联网指示灯
 * 输入参数: bStat:联网状态
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_SetLed(CELLULAR_STAT_E bStat)
{

	static SV_BOOL bEnable = SV_TRUE;
	switch(bStat)
	{
	    case CELL_STAT_INIT:
	    case CELL_STAT_CONNECT:
	    case CELL_STAT_SIM_ERR:
	    case CELL_STAT_CONFIG:
	    case CELL_STAT_FACTORY:
	    case CELL_STAT_RESET:
#if (defined(BOARD_ADA32C4))
			BOARD_RK_SetGPIO(2,30,bEnable?1:0);
			bEnable = !bEnable;
#endif
			break;
		case CELL_STAT_NORMAL:
#if (defined(BOARD_ADA32C4))
			BOARD_RK_SetGPIO(2,30,1);
#endif
			break;
		default:
			break;
	}
	return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 设置模块上电情况
 * 输入参数: bSwitch -- 模块上电开关 SV_TRUE:上电 SV_FALSE:掉电
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32  cellular_SwitchPower(SV_BOOL bSwitch)
{
    sint32 s32Ret = 0;
    if(SV_FALSE == bSwitch)
    {
        s32Ret = BOARD_RK_SetGPIO(CELL_POWER_PIN_BAND, CELL_POWER_PIN_NUM, CELL_POWER_DOWN);
    }
    else
    {
        s32Ret = BOARD_RK_SetGPIO(CELL_POWER_PIN_BAND, CELL_POWER_PIN_NUM, CELL_POWER_ON);
    }

    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取模块VCC引脚电源情况
 * 输入参数: *bVCCStatus -- VCC状态返回指针
 * 输出参数: *bVCCStatus -- VCC状态返回指针
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_GetVCCStatus(SV_BOOL *bVCCStatus)
{
    uint8 u8Value = 0;
    sint32 s32Ret = 0;

    if(NULL== bVCCStatus)
    {
        return SV_FAILURE;
    }

    s32Ret = BOARD_RK_GetGPIO(CELL_VCC_PIN_BAND, CELL_VCC_PIN_NUM , &u8Value);
    if(SV_SUCCESS == s32Ret)
    {
        if(CELL_VCC_POWER_ON == u8Value)
        {
            *bVCCStatus = SV_TRUE;
        }
        else
        {
            *bVCCStatus = SV_FALSE;
        }
    }
    else
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取模块上电情况
 * 输入参数: *bPowerStatus -- 上电状态返回指针
 * 输出参数: *bPowerStatus -- 上电状态返回指针
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_GetPowerStatus(SV_BOOL *bPowerStatus)
{
    uint8 u8Value = 0;
    sint32 s32Ret = 0;

    if(NULL== bPowerStatus)
    {
        return SV_FAILURE;
    }

    s32Ret = BOARD_RK_GetGPIO(CELL_POWER_PIN_BAND, CELL_POWER_PIN_NUM , &u8Value);
    if(SV_SUCCESS == s32Ret)
    {
        if(CELL_POWER_ON == u8Value)
        {
            *bPowerStatus = SV_TRUE;
        }
        else
        {
            *bPowerStatus = SV_FALSE;
        }
    }
    else
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 通过引脚上下电复位模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_ResetModuleForce(void)
{
    sint32 s32Ret = 0;
    SV_BOOL bPower = SV_FALSE;
    s32Ret = cellular_SwitchPower(bPower);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }
    sleep_ms(500);

    bPower  = SV_TRUE;
    s32Ret = cellular_SwitchPower(bPower);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 获取模块型号
 * 输入参数: penModuleType -- 模块类型存储区
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_GetModuleType(CELLULAR_MODULE_E *penModuleType)
{
    sint32 s32Ret = 0;
    char szBuf[1024] = {0};
    uint8 u8TryTimes = 4;
    CELLULAR_MODULE_E enModuleType = CELLULAR_UNKNOWN;

    if(NULL == penModuleType)
    {
        return SV_FAILURE;
    }
    while(u8TryTimes > 1)
    {
        --u8TryTimes;
        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = GetInsContext("lsusb", szBuf, sizeof(szBuf));
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "get instruction context error\n");
            continue;
        }
        //print_level(SV_INFO, "lsusb : \n %s \n", szBuf);
        if(strstr(szBuf, VPID_EC25) != NULL)
        {
            enModuleType = CELLULAR_EC25;
            break;
        }
        else if(strstr(szBuf, VPID_SIM7600) != NULL)
        {
            enModuleType = CELLULAR_SIM7600;
            break;
        }
		else if(strstr(szBuf, VPID_EC200U) != NULL)
        {
            enModuleType = CELLULAR_EC200U;
            break;
        }
		else if(strstr(szBuf, VPID_EC200A) != NULL)
        {
            enModuleType = CELLULAR_EC200A;
            break;
        }
    }

    print_level(SV_INFO, "cellular module type :%d\n", enModuleType);
    if(enModuleType == CELLULAR_UNKNOWN)
    {
        return SV_FAILURE;
    }

    *penModuleType = enModuleType;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取子模块型号
 * 输入参数: s32Fd文件描述符, penSubModuleType -- 子模块类型存储区
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_GetSubModuleType(sint32 s32Fd, CELLULAR_SUB_MODULE_E *penSubModuleType)
{
    CELLULAR_SUB_MODULE_E enSubModuleType = CELLULAR_SUB_UNKNOWN;
    uint8 u8TryTimes = 0;
    char chRecv[RECV_BUFFER_LEN] = {0};
    uint32 timeout = 100;        // 发送AT超时时间-毫秒
    char subModuleTypeStr[20];           // 子模块类型字符串


    // 4次发送AT指令不成功则超时
    while(u8TryTimes < 4)
    {
        memset(chRecv, 0, RECV_BUFFER_LEN);
        if(ec25_SendAndRecv(s32Fd, AT_GET_ATI, chRecv, timeout) != SV_SUCCESS)
        {
             print_level(SV_ERROR, "## AT_GET_ATI  : Failed to send command! u8TryTimes=%d\n", u8TryTimes);
             u8TryTimes++;
        }
        else
        {
            break;
        }
        
    }
    
    if(u8TryTimes == 4)
    {
        return SV_FAILURE;
    }


    print_level(SV_INFO, "GET_ATI: %s \n", chRecv);

    // 提取子模块类型
    memset(subModuleTypeStr, 0, 20);
    char *revision = strstr(chRecv, "Revision: ");
    if ( revision != NULL )
    {
        revision += strlen("Revision: ");
        strncpy(subModuleTypeStr, revision, 10);  // 提取Revision:   后 10 个字符
        subModuleTypeStr[11] = '\0';
    }
        
    if (strstr(subModuleTypeStr, "EC25JF") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_EC25JF;
    }
    else if(strstr(subModuleTypeStr, "EC25EU") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_EC25EU;
    }
    else if(strstr(subModuleTypeStr, "EC25AU") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_EC25AU;
    }
    else if(strstr(subModuleTypeStr, "EC25AF") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_EC25AF;
    }
    else if(strstr(subModuleTypeStr, "EC20CE") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_EC20CE;
    }
    else if(strstr(subModuleTypeStr, "EC20EA") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_EC20EA;
    }
    else if(strstr(subModuleTypeStr, "SIM7600NA") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_SIM7600NA;
    }
    else if(strstr(subModuleTypeStr, "SIM7600CE") != NULL)
    {
        enSubModuleType = CELLULAR_SUB_SIM7600CE;
    }
    else
    {
        enSubModuleType = CELLULAR_SUB_UNKNOWN;
    }
    
    print_level(SV_INFO, "cellular sub module type :%d\n", enSubModuleType);
    *penSubModuleType = enSubModuleType;
    if(enSubModuleType == CELLULAR_SUB_UNKNOWN)
    {
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 上电等待USB就绪
 * 输入参数: u32Timeout -- 超时等待时间(ms) 0：使用默认值
             *pbDiscovery -- SV_TRUE：发现设备
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意     : 无
 *****************************************************************************/
sint32 cellular_WaitForProbe(uint32 u32Timeout, SV_BOOL *pbDiscovery, CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;
    char szBuf[1024] = {0};
    uint32 u32CurTime = 0;
    uint32 u32Count = 0;
    uint32 u32OldDevNum = 0;
    uint32 u32CurDevNum = 0;
  	sint32 i = 0;

    if (pbDiscovery == NULL)
    {
        print_level(SV_ERROR, "pbDiscovery == NULL\n");
        return SV_FAILURE;
    }

    //关闭电源
    s32Ret = cellular_SwitchPower(SV_FALSE);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "turn off devices power error\n");
    }

    sleep_ms(1000);

    //重启电源
    s32Ret = cellular_SwitchPower(SV_TRUE);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "turn off devices power error\n");
    }

    if (u32Timeout == 0)
    {
        u32Timeout = 20000;
    }

    //等待设备发现

    while (u32CurTime < u32Timeout)
    {
        u32CurTime += 1000;

        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = GetInsContext("lsusb", szBuf, sizeof(szBuf));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "get instruction context error\n");
            sleep_ms(1000);
            continue;
        }

        for (i = 0 ; i < sizeof(m_pszCellularType)/sizeof(char*); i++)
        {
        	if (strstr(szBuf, m_pszCellularType[i]))
        	{
                print_level(SV_INFO, "Have found 4G Module\n");
        		*pbDiscovery = SV_TRUE;
        	    pstCellularInfo->bModuleExist = SV_TRUE;
        	    return SV_SUCCESS;
        	}
        }

        sleep_ms(1000);
    }

    *pbDiscovery = SV_FALSE;
    pstCellularInfo->bModuleExist = SV_FALSE;

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 初始化设备电源上电情况，有两个引脚需要同时上电4G模块才能启动
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意     : 无
 *****************************************************************************/
sint32 cellular_PowerInit(void)
{
    sint32 s32Ret = -1;
    SV_BOOL bPowerStatus = SV_FALSE;
    //电源开启，模块有两个电源控制引脚
#if 0
    s32Ret = cellular_GetVCCStatus(&bPowerStatus);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR,"get cellular power status error\n");
        return SV_FAILURE;
    }
    print_level(SV_INFO, "cellular_PowerInit 2222\n");
    if(SV_FALSE == bPowerStatus)
    {
        s32Ret = cellular_SwitchVCC(SV_TRUE);
    }

    print_level(SV_INFO, "cellular_PowerInit 3333\n");
    s32Ret = cellular_GetPowerStatus(&bPowerStatus);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR,"get cellular power status error\n");
        return SV_FAILURE;
    }

    if(SV_FALSE == bPowerStatus)
    {
        s32Ret = cellular_SwitchPower(SV_TRUE);
    }
#endif

    s32Ret = cellular_SwitchPower(SV_TRUE);
    if (SV_FAILURE == s32Ret)
    {
        s32Ret = cellular_SwitchPower(SV_TRUE);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 测试4G模块是否能ping通
 * 输入参数: enModuleType -- 模块类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_PingIpaddr(const CELLULAR_MODULE_E enModuleType)
{
    sint32 s32Ret = SV_FAILURE;
    float fSpeedTime = 0;

    if(CELLULAR_UNKNOWN == enModuleType)
    {
        return SV_FAILURE;
    }
    switch(enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_CmdPingIpaddrList(EC25_NET_CARD, pIpList, 2000);
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_CmdPingIpaddrList(SIM7600_NET_CARD, pIpList, 2000);
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_CmdPingIpaddr(EC200U_NET_CARD, "*******", 3000);
            if (SV_SUCCESS != s32Ret)
            {
                s32Ret = ec200u_CmdPingIpaddr(EC200U_NET_CARD, "*******", 3000);
            }
            break;
		case CELLULAR_EC200A:
			s32Ret = ec200a_CmdPingIpaddr(EC200A_NET_CARD, "*******", 3000);
            if (SV_SUCCESS != s32Ret)
            {
                s32Ret = ec200a_CmdPingIpaddr(EC200A_NET_CARD, "*******", 3000);
            }
        default:
            break;
    }
    return s32Ret;
}


/******************************************************************************
 * 函数功能: 设备驱动加载情况监测
 * 输入参数: pbIsExist -- 驱动存在情况存储区
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_DriverProbe(SV_BOOL *pbIsExist,const CELLULAR_MODULE_E enModuleType)
{
    sint32 s32Ret = SV_FAILURE;

    if(NULL== pbIsExist|| CELLULAR_UNKNOWN == enModuleType)
    {
        return SV_FAILURE;
    }
    switch(enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_DriverProbe(pbIsExist);
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_DriverProbe(pbIsExist);
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_DriverProbe(pbIsExist);
            break;
		case CELLULAR_EC200A:
            s32Ret = ec200a_DriverProbe(pbIsExist);
            break;
        default:
            break;
    }
    return s32Ret;
}

/******************************************************************************
 * 函数功能: 加载设备驱动文件
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_LoadDriver(const CELLULAR_MODULE_E enModuleType)
{
    sint32 s32Ret = SV_FAILURE;
    switch(enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_LoadDriver();
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_LoadDriver();
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_LoadDriver();
            break;
		case CELLULAR_EC200A:
            s32Ret = ec200a_LoadDriver();
            break;
        default:
            break;
    }
    return s32Ret;
}

/******************************************************************************
 * 函数功能: 卸载设备驱动文件
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_UnLoadDriver(CELLULAR_MODULE_E enModuleType)
{
    sint32 s32Ret = SV_FAILURE;
    switch(enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_UnLoadDriver();
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_UnLoadDriver();
            break;
        default:
            break;
    }
    return s32Ret;
}

/******************************************************************************
 * 函数功能: 4G模块硬件设备初始化
 * 输入参数: pstCellularInfo -- 模块控制信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_HardwareInit(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;
    SV_BOOL bDiscovery = SV_FALSE;
    SV_BOOL bIsExist = SV_FALSE;
    static sint32 s32NoModuleCnt = 0;

    //电源设置
    s32Ret = cellular_PowerInit();
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR,"cellular device init error\n");
    }

    //获取模块类型
    while(1)
    {
        print_level(SV_INFO, "pstCellularInfo->enModuleType=%d\n", pstCellularInfo->enModuleType);
        s32Ret = cellular_GetModuleType(&(pstCellularInfo->enModuleType));
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR,"get cellular type error\n");
            s32NoModuleCnt++;
            print_level(SV_INFO, "s32NoModuleCnt = %d\n", s32NoModuleCnt);
            if (s32NoModuleCnt >= 15)
            {
                pstCellularInfo->bRunning = SV_FALSE;
                pstCellularInfo->bModuleExist = SV_FALSE;
                cellular_DumpExistInfo(pstCellularInfo->bModuleExist);
                print_level(SV_INFO, "stop cellular.\n");
                return SV_FAILURE;
            }
        }
        else
        {
            s32NoModuleCnt = 0;
            pstCellularInfo->bModuleExist = SV_TRUE;
            break;
        }

        s32Ret = cellular_WaitForProbe(0, &bDiscovery, pstCellularInfo);
        if((SV_FAILURE == s32Ret) || (SV_FALSE == bDiscovery))
        {
            print_level(SV_ERROR, "wait for probe error or doesn't find devices\n");
            sleep_ms(500);
            continue;
        }

    }

    //加载驱动
    s32Ret = cellular_DriverProbe(&bIsExist, pstCellularInfo->enModuleType);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "probe driver error\n");
    }
    if(SV_FALSE == bIsExist)
    {
        s32Ret = cellular_LoadDriver(pstCellularInfo->enModuleType);
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "load driver error\n");
        }
    }

    print_level(SV_INFO, "hardware init success.\n");
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 模块初始化，绑定子模块的信息到主模块上
 * 输入参数: pstCellularInfo -- 模块控制信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_AppInit(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;
    sint32 s32CmdRet = -1;
    char szCmd[64];

    if (CELLULAR_UNKNOWN == pstCellularInfo->enModuleType)
    {
        return SV_FAILURE;
    }

    switch (pstCellularInfo->enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
        case CELLULAR_EC200T:
             break;
        case CELLULAR_EC25:
            s32Ret = ec25_APPInit(&pstCellularInfo->pstModuleParam);
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_APPInit(&pstCellularInfo->pstModuleParam);
            sprintf(szCmd, SIMCOM_CFUN_GPIO_SET);
            s32CmdRet = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (0 != s32CmdRet)
            {
                s32Ret = -1;
                print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            }
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_APPInit(&pstCellularInfo->pstModuleParam);
            break;
		case CELLULAR_EC200A:
            s32Ret = ec200a_APPInit(&pstCellularInfo->pstModuleParam);
            break;
        default:
            break;
    }

    // 获取子模块类型
    cellular_GetSubModuleType(pstCellularInfo->pstModuleParam->s32Fd, &(pstCellularInfo->enSubModuleType));

    if (SV_FAILURE == s32Ret)
    {
        strcpy(pstCellularInfo->pstModuleParam->u8Describe, "AppInit fail");
    }
    else
    {
        strcpy(pstCellularInfo->pstModuleParam->u8Describe, "AppInit success");
    }

	if(SV_FAILURE == s32Ret && pstCellularInfo->pstModuleParam->enModuleStatus == CELL_STAT_RESET)
	{
		s32Ret = cellular_RebootMoudle(pstCellularInfo);
	    if (SV_SUCCESS == s32Ret)
	    {
	        pstCellularInfo->enModuleOps = CELL_OPS_INIT;
	        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
	        pstCellularInfo->pstModuleParam->bException = SV_FALSE;
	    }
	    else
	    {
	        pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
	        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
	    }
	}
    return s32Ret;

}

/******************************************************************************
 * 函数功能: 清除子模块信息
 * 输入参数: enModuleType -- 模块类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_ClearModule(CELLULAR_MODULE_E enModuleType)
{
    sint32 s32Ret = -1;
    if(CELLULAR_UNKNOWN == enModuleType)
    {
        return SV_FAILURE;
    }

    switch(enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_ClearModule();
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_ClearModule();
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_ClearModule();
            break;
        case CELLULAR_EC200A:
            s32Ret = ec200a_ClearModule();
            break;
        default:
            break;
    }
    return s32Ret;
}

/******************************************************************************
 * 函数功能: 重启模块
 * 输入参数: pstCellularInfo -- 模块控制信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_RebootMoudle(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;
    CELLULAR_STAT_E eTmpMoudleStatus;
    CELLULAR_SETUP_S stTmpSetUp;

    eTmpMoudleStatus = pstCellularInfo->pstModuleParam->enModuleStatus;
    stTmpSetUp = pstCellularInfo->pstModuleParam->stSetup;

    s32Ret = cellular_ClearModule(pstCellularInfo->enModuleType);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }

    pstCellularInfo->pstModuleParam->enModuleStatus = eTmpMoudleStatus;
    pstCellularInfo->pstModuleParam->stSetup = stTmpSetUp;

    pstCellularInfo->enModuleOps = CELL_OPS_INIT;
    pstCellularInfo->bIsInsert = SV_FALSE;
    pstCellularInfo->pstModuleParam->bHadInit = SV_FALSE;

    s32Ret = cellular_ResetModuleForce();
    if(SV_FAILURE == s32Ret)
    {
        pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
        pstCellularInfo->bIsInsert = SV_FALSE;
        return SV_FAILURE;
    }

    s32Ret = cellular_HardwareInit(pstCellularInfo);
    if(SV_FAILURE == s32Ret)
    {
        pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
        pstCellularInfo->bIsInsert = SV_FALSE;
        return SV_FAILURE;
    }

    s32Ret = cellular_AppInit(pstCellularInfo);
    if(SV_FAILURE == s32Ret)
    {
        pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
        pstCellularInfo->bIsInsert = SV_FALSE;
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 出错处理
 * 输入参数: pstCellularInfo -- 模块控制信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_ErrorHandle(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;

    print_level(SV_INFO, "enModuleStatus = %d.\n", pstCellularInfo->pstModuleParam->enModuleStatus);

    if ((CELL_STAT_SIM_ERR != pstCellularInfo->pstModuleParam->enModuleStatus)
        && (CELL_STAT_RESET != pstCellularInfo->pstModuleParam->enModuleStatus)
        && (CELL_STAT_INIT != pstCellularInfo->pstModuleParam->enModuleStatus)
        && (CELL_STAT_CONNECT != pstCellularInfo->pstModuleParam->enModuleStatus))
    {
        return SV_FAILURE;
    }
    switch(pstCellularInfo->pstModuleParam->enModuleStatus)
    {
        case CELL_STAT_INIT:
            s32Ret = SV_SUCCESS;
            break;
        case CELL_STAT_CONNECT:
        case CELL_STAT_SIM_ERR:
        case CELL_STAT_RESET:
            s32Ret = cellular_RebootMoudle(pstCellularInfo);
            break;
        default:
            break;

    }
    if(SV_SUCCESS == s32Ret)
    {
        print_level(SV_INFO, "ErrorHandle success.\n");
        pstCellularInfo->enModuleOps = CELL_OPS_INIT;
        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
        pstCellularInfo->pstModuleParam->bException = SV_FALSE;
    }
    return s32Ret;
}

/******************************************************************************
 * 函数功能: SIM卡检测
 * 输入参数: bIsCardExist -- 卡存在？ enModuleType -- 模块类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_SimCardProbe(SV_BOOL *bIsCardExist, CELLULAR_MODULE_E enModuleType, CELLULAR_OPS_E *penModuleOps, CELLULAR_MODULE_PARAM_S *pstSubMoudleParam)
{
    sint32 s32Ret = -1;
    sint32 s32StopRet = -1;

    if(NULL == bIsCardExist || CELLULAR_UNKNOWN == enModuleType || NULL == penModuleOps || NULL == pstSubMoudleParam)
    {
        return SV_FAILURE;
    }

    switch(enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_SimCardProbe(bIsCardExist, pstSubMoudleParam);
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_SimCardProbe(bIsCardExist, pstSubMoudleParam);
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_SimCardProbe(bIsCardExist, pstSubMoudleParam);
            break;
		case CELLULAR_EC200A:
            s32Ret = ec200a_SimCardProbe(bIsCardExist, pstSubMoudleParam);
            break;
        default:
            break;
    }
    if(SV_SUCCESS == s32Ret)
    {
        print_level(SV_INFO, "SimCardProbe success.\n");
        *penModuleOps = CELL_OPS_CONNECT;
        pstSubMoudleParam->enModuleStatus = CELL_STAT_CONNECT;
        pstSubMoudleParam->bHadInit = SV_TRUE;

        /* 工厂测试跳转回测试模式 */
        if (SV_TRUE == pstSubMoudleParam->bFactory)
        {
            *penModuleOps = CELL_OPS_FACTORY;
            pstSubMoudleParam->enModuleStatus = CELL_STAT_FACTORY;
        }

        strcpy(pstSubMoudleParam->u8Describe, "SIM success");
    }
    else
    {
        print_level(SV_INFO, "SimCardProbe failed.\n");
        strcpy(pstSubMoudleParam->u8Describe, "SIM failure");
    }

    return s32Ret;
}

/******************************************************************************
 * 函数功能: 网络连接过程
 * 输入参数: pstCellularInfo -- 主模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_Connect(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;
    static sint32 s32ErrCnt = 0;
    if(NULL == pstCellularInfo || CELLULAR_UNKNOWN == pstCellularInfo->enModuleType)
    {
        return SV_FAILURE;
    }

    strcpy(pstCellularInfo->pstModuleParam->stSetup.szApn, pstCellularInfo->stTmpSetUp.szApn);
    strcpy(pstCellularInfo->pstModuleParam->stSetup.szUserName, pstCellularInfo->stTmpSetUp.szUserName);
    strcpy(pstCellularInfo->pstModuleParam->stSetup.szPassWd, pstCellularInfo->stTmpSetUp.szPassWd);

    switch(pstCellularInfo->enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_Connect(pstCellularInfo->pstModuleParam);
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_Connect(pstCellularInfo->pstModuleParam);
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_Connect(pstCellularInfo->pstModuleParam);
            break;
		case CELLULAR_EC200A:
            s32Ret = ec200a_Connect(pstCellularInfo->pstModuleParam);
            break;
        default:
            break;
    }

    if(SV_SUCCESS == s32Ret)
    {
        print_level(SV_INFO, "Connect success.\n");
        strcpy(pstCellularInfo->pstModuleParam->u8Describe, "Connect success");
        pstCellularInfo->enModuleOps = CELL_OPS_NORMAL;
        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_NORMAL;
        s32ErrCnt = 0;
    }
    else
    {
        s32ErrCnt++;
        pstCellularInfo->enModuleOps = CELL_OPS_INIT;
        sleep_ms(1000);
    }

#if 0
    if (s32ErrCnt > 3)
    {
        s32ErrCnt = 0;
        pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_CONNECT;
        if (SV_TRUE == pstCellularInfo->pstModuleParam->bFactory)
        {
            pstCellularInfo->enModuleOps = CELL_OPS_FACTORY;
            pstCellularInfo->pstModuleParam->enModuleStatus  = CELL_STAT_FACTORY;
        }

        return s32Ret;
    }
#endif

    return s32Ret;
}

/******************************************************************************
 * 函数功能: 更新模块信息
 * 输入参数: pstCellularInfo -- 主模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_Update(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;

    if(NULL == pstCellularInfo || CELLULAR_UNKNOWN == pstCellularInfo->enModuleType)
    {
        return SV_FAILURE;
    }

    switch(pstCellularInfo->enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_Update(pstCellularInfo->pstModuleParam);
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_Update(pstCellularInfo->pstModuleParam);
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_Update(pstCellularInfo->pstModuleParam);
            break;
        case CELLULAR_EC200A:
            s32Ret = ec200a_Update(pstCellularInfo->pstModuleParam);
            break;
        default:
            break;
    }

    if(pstCellularInfo->pstModuleParam->enModuleStatus == CELL_STAT_SIM_ERR)
    {
        pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
    }
    
    return s32Ret;
}

/******************************************************************************
 * 函数功能: 外部配置命令处理
 * 输入参数: pstCellularInfo -- 主模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_Config(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;

    if(NULL == pstCellularInfo || CELLULAR_UNKNOWN == pstCellularInfo->enModuleType)
    {
        return SV_FAILURE;
    }

    if (NULL == pstCellularInfo->pstModuleParam)
    {
        return SV_FAILURE;
    }

    strcpy(pstCellularInfo->pstModuleParam->stSetup.szApn, pstCellularInfo->stTmpSetUp.szApn);
    strcpy(pstCellularInfo->pstModuleParam->stSetup.szUserName, pstCellularInfo->stTmpSetUp.szUserName);
    strcpy(pstCellularInfo->pstModuleParam->stSetup.szPassWd, pstCellularInfo->stTmpSetUp.szPassWd);

    //TODO:    2021/08/16待实现各个模块接收外部配置命令时功能
    switch(pstCellularInfo->enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_Config(pstCellularInfo->pstModuleParam);
            break;
        case CELLULAR_EC200T:
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_Config(pstCellularInfo->pstModuleParam);
            break;
		case CELLULAR_EC200U:
            s32Ret = ec200u_Config(pstCellularInfo->pstModuleParam);
            break;
        case CELLULAR_EC200A:
            s32Ret = ec200a_Config(pstCellularInfo->pstModuleParam);
            break;
        default:
            break;
    }

    if(SV_SUCCESS == s32Ret)
    {
        switch(pstCellularInfo->pstModuleParam->enModuleStatus)
        {
            case CELL_STAT_NORMAL:
                pstCellularInfo->enModuleOps = CELL_OPS_INIT;
                break;
            case CELL_STAT_CONNECT:
                pstCellularInfo->enModuleOps = CELL_OPS_CONNECT;
                break;
            case CELL_STAT_SIM_ERR:
                pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
                break;
            case CELL_STAT_CONFIG:
                pstCellularInfo->enModuleOps = CELL_OPS_INIT;
                break;
            case CELL_STAT_FACTORY:
                pstCellularInfo->enModuleOps = CELL_OPS_FACTORY;
                break;
            case CELL_STAT_RESET:
                pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
                break;
            default:
                pstCellularInfo->enModuleOps = CELL_OPS_INIT;
            break;
        }
        pstCellularInfo->bConfig = SV_FALSE;
        strcpy(pstCellularInfo->pstModuleParam->u8Describe, "config success");

        return SV_SUCCESS;
    }

    strcpy(pstCellularInfo->pstModuleParam->u8Describe, "config fail");

    if (CELL_STAT_FACTORY == pstCellularInfo->pstModuleParam->enModuleStatus)
    {
        pstCellularInfo->enModuleOps = CELL_OPS_FACTORY;
        return SV_SUCCESS;
    }

    s32Ret = cellular_RebootMoudle(pstCellularInfo);
    if (SV_SUCCESS == s32Ret)
    {
        pstCellularInfo->enModuleOps = CELL_OPS_INIT;
        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
        pstCellularInfo->pstModuleParam->bException = SV_FALSE;
    }
    else
    {
        pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
    }
    return SV_FAILURE;

}

/******************************************************************************
 * 函数功能: 工厂测试
 * 输入参数: pstCellularInfo -- 主模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_DoFactory(CELLULAR_INFO_S *pstCellularInfo)
{
    if(NULL == pstCellularInfo || CELLULAR_UNKNOWN == pstCellularInfo->enModuleType)
    {
        return SV_FAILURE;
    }
    if (SV_FALSE == pstCellularInfo->pstModuleParam->bHadInit)
    {
        pstCellularInfo->enModuleOps = CELL_OPS_INIT;
        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
    }
    /* 工厂测试,让出控制权给factory模块 */
    sleep_ms(1000);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 执行AT指令并返回结果
 * 输入参数: pstCellularInfo -- 主模块参数    pcCmd -- AT指令
             pBuf -- 返回结果的缓存区                 u32Timeout -- 执行指令超时时间（秒）
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_ATCmd(CELLULAR_INFO_S *pstCellularInfo, const char* pcCmd, char* pBuf, uint32 u32Timeout)
{
    sint32 s32Ret = -1;
    if(pstCellularInfo->pstModuleParam->s32Fd <= 0 || pstCellularInfo->enModuleType == CELLULAR_UNKNOWN || NULL == pcCmd || NULL == pBuf)
    {
        return SV_FAILURE;
    }
    switch(pstCellularInfo->enModuleType)
    {
        case CELLULAR_GEMATO:
        case CELLULAR_SERRIA:
        case CELLULAR_HUAWEI:
        case CELLULAR_HUAWEI_MU709S_6:
        case CELLULAR_HUAWEI_ME909S_120:
        case CELLULAR_TELIT:
        case CELLULAR_TELIT910C1:
        case CELLULAR_FIBOCOM:
            return SV_FAILURE;
            break;
        case CELLULAR_EC25:
            s32Ret = ec25_SendAndRecv(pstCellularInfo->pstModuleParam->s32Fd, pcCmd, pBuf, u32Timeout);
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "ec25_SendAndRecv fail.\n");
                return SV_FAILURE;
            }
            break;
        case CELLULAR_EC200T:
            return SV_FAILURE;
            break;
        case CELLULAR_SIM7600:
            s32Ret = sim7600_SendAndRecv(pstCellularInfo->pstModuleParam->s32Fd, pcCmd, pBuf, u32Timeout);
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "sim7600_SendAndRecv fail.\n");
                return SV_FAILURE;
            }
            break;
        default:
            return SV_FAILURE;
            break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将模块信息更新进文件/var/info/cellular
 * 输入参数: pstCellularInfo -- 主模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_DumpModuleInfo(CELLULAR_INFO_S *pstCellularInfo)
{
    sint32 s32Ret = -1;
    sint32 s32Fd = 0;
    cJSON *pstJson = NULL;
    char szBuf[2048];
    cJSON *pstEnable = NULL;
    cJSON *pstModuleType = NULL;
    cJSON *pstSubModuleType = NULL;
    cJSON *pstModuleOps = NULL;
    cJSON *pstModuleStat = NULL;
    cJSON *pstbIsSIM = NULL;
    cJSON *pstNetType = NULL;
    cJSON *pstIPAddr = NULL;
    cJSON *pstSignal = NULL;
    cJSON *pstVersion = NULL;
    cJSON *pstTemperature = NULL;
    cJSON *pstDescribe = NULL;
    cJSON *pstIMEI = NULL;
    cJSON *pstICCID = NULL;
    cJSON *pstCellExist = NULL;

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return SV_FAILURE;
    }
    //使能位
    pstEnable = cJSON_CreateBool(pstCellularInfo->bEnable);
    if (NULL == pstEnable)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "enable", pstEnable);
    //异常位
    pstEnable = cJSON_CreateBool(pstCellularInfo->pstModuleParam->bException);
    if (NULL == pstEnable)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Exception", pstEnable);
    //模块类型
    pstModuleType = cJSON_CreateNumber(pstCellularInfo->enModuleType);
    if (NULL == pstModuleType)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "ModuleType", pstModuleType);
    
    //子模块类型
    pstSubModuleType = cJSON_CreateNumber(pstCellularInfo->enSubModuleType);
    if (NULL == pstSubModuleType)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "SubModuleType", pstSubModuleType);
    
    //操作
    pstModuleOps = cJSON_CreateNumber(pstCellularInfo->enModuleOps);
    if (NULL == pstModuleOps)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "ModuleOps", pstModuleOps);
    //状态
    pstModuleStat = cJSON_CreateNumber(pstCellularInfo->pstModuleParam->enModuleStatus);
    if (NULL == pstModuleStat)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "ModuleStat", pstModuleStat);
    //Sim卡插入状态
    pstbIsSIM = cJSON_CreateBool(pstCellularInfo->bIsInsert);
    if (NULL == pstbIsSIM)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "hadSIM", pstbIsSIM);
    //网络类型
    pstNetType = cJSON_CreateString(pstCellularInfo->pstModuleParam->szNetType);
    if (NULL == pstNetType)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "NetType", pstNetType);
    //IP地址
    pstIPAddr = cJSON_CreateString(pstCellularInfo->pstModuleParam->szIPAddr);
    if (NULL == pstIPAddr)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "IP", pstIPAddr);
    //信号强度
    pstSignal = cJSON_CreateNumber(pstCellularInfo->pstModuleParam->u32Signal);
    if (NULL == pstSignal)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Signal", pstSignal);
    //版本
    pstVersion = cJSON_CreateString(pstCellularInfo->pstModuleParam->szVersion);
    if (NULL == pstVersion)
    {
        print_level(SV_ERROR, "cJSON_CreateString failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Version", pstVersion);
    //温度
    pstTemperature = cJSON_CreateNumber(pstCellularInfo->pstModuleParam->s32Temperature);
    if (NULL == pstTemperature)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Temperature", pstTemperature);
    //模块状态
    pstDescribe = cJSON_CreateString(pstCellularInfo->pstModuleParam->u8Describe);
    if (NULL == pstDescribe)
    {
        print_level(SV_ERROR, "cJSON_CreateString failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "ModuleDescribe", pstDescribe);

    //IMEI
    pstIMEI = cJSON_CreateString(pstCellularInfo->pstModuleParam->szIMEI);
    if (NULL == pstIMEI)
    {
        print_level(SV_ERROR, "cJSON_CreateString failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "IMEI", pstIMEI);

    //ICCID
    pstICCID = cJSON_CreateString(pstCellularInfo->pstModuleParam->szICCD);
    if (NULL == pstICCID)
    {
        print_level(SV_ERROR, "cJSON_CreateString failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "ICCID", pstICCID);

    //模块是否存在
    pstCellExist = cJSON_CreateBool(pstCellularInfo->bModuleExist);
    if (NULL == pstCellExist)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "bCellExist", pstCellExist);

    //写文件
    memset(szBuf, 0, 2048);
    cJSON_PrintPreallocated(pstJson, szBuf, 2048, 0);
    s32Fd = open("/var/info/cellular-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        goto exit;
    }

    s32Ret = write(s32Fd, szBuf, strlen(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        close(s32Fd);
        goto exit;
    }

    close(s32Fd);
    rename("/var/info/cellular-tmp", "/var/info/cellular");

exit:
    cJSON_Delete(pstJson);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将模块是否存在的信息更新进文件/var/info/cellular
 * 输入参数: pstCellularInfo -- 主模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellular_DumpExistInfo(SV_BOOL bCellExist)
{
    sint32 s32Ret = -1;
    sint32 s32Fd = 0;
    cJSON *pstJson = NULL;
    char szBuf[2048];
    cJSON *pstCellExist = NULL;

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return SV_FAILURE;
    }

    //模块是否存在
    pstCellExist = cJSON_CreateBool(bCellExist);
    if (NULL == pstCellExist)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "bCellExist", pstCellExist);

    //写文件
    memset(szBuf, 0, 2048);
    cJSON_PrintPreallocated(pstJson, szBuf, 2048, 0);
    s32Fd = open("/var/info/cellular-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        goto exit;
    }

    s32Ret = write(s32Fd, szBuf, strlen(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        close(s32Fd);
        goto exit;
    }

    close(s32Fd);
    rename("/var/info/cellular-tmp", "/var/info/cellular");

exit:
    cJSON_Delete(pstJson);
    return SV_SUCCESS;
}


void * cellular_Watch_Body(void *pvArg)
{
    sint32 s32Ret = -1;
    CELLULAR_INFO_S *pstCellularInfo = (CELLULAR_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "cellular_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    /* 如果4G能ping通网络，跳过硬件初始化部分 */
    s32Ret = cellular_GetModuleType(&(pstCellularInfo->enModuleType));
    if (SV_FAILURE == s32Ret)
    {
        goto HardwareInit;
    }

    if (SV_SUCCESS == cellular_PingIpaddr(pstCellularInfo->enModuleType))
    {
        print_level(SV_INFO, "ping success.\n");
        pstCellularInfo->enModuleOps = CELL_OPS_NORMAL;
        pstCellularInfo->bIsInsert = SV_TRUE;
        pstCellularInfo->bModuleExist = SV_TRUE;
        goto AppInit;
    }
    else
    {
        print_level(SV_INFO, "ping failed.\n");
    }

HardwareInit:
    cellular_DumpExistInfo(pstCellularInfo->bModuleExist);
    //初始化设备(硬件部分)
    s32Ret = cellular_HardwareInit(pstCellularInfo);
    if (SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "hardware init error\n");
    }

AppInit:
    //初始化应用(平台软件部分)
    s32Ret = cellular_AppInit(pstCellularInfo);
    if (SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "cellular app init error\n");
    }
    else if (CELL_OPS_NORMAL == pstCellularInfo->enModuleOps)
    {
        pstCellularInfo->pstModuleParam->eConStatus = CELL_CONNETCTED;
        strcpy(pstCellularInfo->pstModuleParam->u8Describe, "Connect success");
    }
    else
    {
        print_level(SV_INFO, "cellular_AppInit success.\n");
        strcpy(pstCellularInfo->pstModuleParam->u8Describe, "AppInit success");
    }

    pstCellularInfo->bEnable = SV_TRUE;
    while (pstCellularInfo->bRunning)
    {
       
        cellular_DumpModuleInfo(pstCellularInfo);
        if (!pstCellularInfo->bEnable)
        {
            sleep_ms(500);
            continue;
        }

        //print_level(SV_DEBUG, "operation: %d status: %d\n", pstCellularInfo->enModuleOps, pstCellularInfo->pstModuleParam->enModuleStatus);
        switch (pstCellularInfo->enModuleOps)
        {
            case CELL_OPS_INIT:
                s32Ret = cellular_SimCardProbe(&pstCellularInfo->bIsInsert, pstCellularInfo->enModuleType, &pstCellularInfo->enModuleOps, pstCellularInfo->pstModuleParam);
                break;
            case CELL_OPS_CONNECT:
                s32Ret = cellular_Connect(pstCellularInfo);
                break;
            case CELL_OPS_DOERROR:
                s32Ret = cellular_ErrorHandle(pstCellularInfo);

                break;
            case CELL_OPS_CONFIG:
                s32Ret = cellular_Config(pstCellularInfo);
                break;
            case CELL_OPS_FACTORY:
                s32Ret = cellular_DoFactory(pstCellularInfo);
                break;
            case CELL_OPS_NORMAL:
                pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_NORMAL;
                break;
            default:
                break;
        }

        if (SV_TRUE == pstCellularInfo->bConfig && CELLULAR_UNKNOWN != pstCellularInfo->enModuleType)
        {
            pstCellularInfo->enModuleOps = CELL_OPS_CONFIG;
            print_level(SV_WARN, "cellular catch config.\n");
            sleep_ms(500);
            continue;
        }

        if (SV_TRUE == pstCellularInfo->pstModuleParam->bFactory)
        {
            sleep_ms(500);
            continue;
        }

        s32Ret = cellular_Update(pstCellularInfo);

        if (pstCellularInfo->pstModuleParam->bConnInterrupt)
        {
            pstCellularInfo->enModuleOps = CELL_OPS_INIT;
            pstCellularInfo->pstModuleParam->bConnInterrupt = SV_FALSE;
            print_level(SV_WARN, "4G connect interrupt.\n");
        }

        if (pstCellularInfo->pstModuleParam->bException)
        {
            pstCellularInfo->enModuleOps = CELL_OPS_DOERROR;
            print_level(SV_WARN, "4G catch exception\n");
        }

		//cellular_SetLed(pstCellularInfo->pstModuleParam->enModuleStatus);

        sleep_ms(1000);
    }
    return NULL;
}

sint32 Cellular_Init(CFG_NETWORK_PARAM stNetworkParam)
{
    sint32 s32Ret = -1;
    memset(&m_stCellularInfo, 0, sizeof(CELLULAR_INFO_S));

    if (NULL != stNetworkParam.ps8Apn)
        strcpy(m_stCellularInfo.stTmpSetUp.szApn, stNetworkParam.ps8Apn);
    else
    {
        print_level(SV_ERROR, "stNetworkParam.ps8Apn is null.\n");
        return ERR_BADADDR;
    }

    if (NULL != stNetworkParam.ps8ApnUserName)
        strcpy(m_stCellularInfo.stTmpSetUp.szUserName, stNetworkParam.ps8ApnUserName);
    else
    {
        print_level(SV_ERROR, "stNetworkParam.ps8ApnUserName is null.\n");
        return ERR_BADADDR;
    }

    if (NULL != stNetworkParam.ps8ApnPassWd)
        strcpy(m_stCellularInfo.stTmpSetUp.szPassWd, stNetworkParam.ps8ApnPassWd);
    else
    {
        print_level(SV_ERROR, "stNetworkParam.ps8ApnPassWd is null.\n");
        return ERR_BADADDR;
    }

    s32Ret = pthread_mutex_init(&m_stCellularInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%d]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
    return SV_SUCCESS;
}

sint32 Cellular_Fini(void)
{
    return SV_SUCCESS;
}

sint32 Cellular_Start(void)
{

    sint32 s32Ret = 0;
    pthread_t thread;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    m_stCellularInfo.bRunning = SV_TRUE;

    s32Ret = pthread_create(&thread, &attr, cellular_Watch_Body, &m_stCellularInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    m_stCellularInfo.u32Tid = thread;
    pthread_attr_destroy(&attr);

    return SV_SUCCESS;
}

sint32 Cellular_Stop(void)
{
    sint32 s32Ret = 0;
    pthread_t thread = m_stCellularInfo.u32Tid;
    void *pvRetval = NULL;

    m_stCellularInfo.bRunning = SV_FALSE;
    //s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 Cellular_GetModTypeforCheck(CELLULAR_MODULE_E *penModuleType)
{
    *penModuleType = m_stCellularInfo.enModuleType;
    return SV_SUCCESS;
}


//TODO: 外部控制模块使能配置尚未接入
sint32 Cellular_SetConfig(CFG_NETWORK_PARAM *pstNetworkParam)
{
    if (NULL == pstNetworkParam)
    {
        return ERR_NULL_PTR;
    }
    if (0 != strcmp(m_stCellularInfo.stTmpSetUp.szApn, pstNetworkParam->ps8Apn)
        || 0 != strcmp(m_stCellularInfo.stTmpSetUp.szUserName, pstNetworkParam->ps8ApnUserName)
        || 0 != strcmp(m_stCellularInfo.stTmpSetUp.szPassWd, pstNetworkParam->ps8ApnPassWd))
    {
        strcpy(m_stCellularInfo.stTmpSetUp.szApn, pstNetworkParam->ps8Apn);
        strcpy(m_stCellularInfo.stTmpSetUp.szUserName, pstNetworkParam->ps8ApnUserName);
        strcpy(m_stCellularInfo.stTmpSetUp.szPassWd, pstNetworkParam->ps8ApnPassWd);
        m_stCellularInfo.bConfig = SV_TRUE;
    }

    return SV_SUCCESS;
}

sint32 Cellular_GetInfo(CELLUALR_OUTPUT_INFO_S *pstCellOutputInfo)
{
    if (NULL == pstCellOutputInfo)
    {
        print_level(SV_INFO, "Cellular_GetInfo failed.\n");
        return ERR_NULL_PTR;
    }

    if (SV_TRUE != m_stCellularInfo.bModuleExist)
    {
        pstCellOutputInfo->bMoudleExist = SV_FALSE;
        pstCellOutputInfo->u32Signal = 99;
        return SV_SUCCESS;
    }

    pstCellOutputInfo->bMoudleExist = SV_TRUE;
    pstCellOutputInfo->bSDcardInsert = m_stCellularInfo.bIsInsert;

    if (NULL == m_stCellularInfo.pstModuleParam)
    {
        pstCellOutputInfo->u32Signal = 99;
        pstCellOutputInfo->eConStatus = CELL_DISCONNECT;
        return SV_SUCCESS;
    }

    pstCellOutputInfo->eConStatus = m_stCellularInfo.pstModuleParam->eConStatus;
    pstCellOutputInfo->eMoudleStatus = m_stCellularInfo.pstModuleParam->enModuleStatus;
    pstCellOutputInfo->u32Signal = m_stCellularInfo.pstModuleParam->u32Signal;
	pstCellOutputInfo->pstModuleParam = m_stCellularInfo.pstModuleParam;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 进入工厂模式，AT指令收发让factory自己处理
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 Cellular_FactoryEnter(void)
{
    if (NULL == m_stCellularInfo.pstModuleParam)
    {
        return ERR_BUSY;
    }
    m_stCellularInfo.pstModuleParam->enModuleStatus = CELL_STAT_FACTORY;
    m_stCellularInfo.pstModuleParam->bFactory = SV_TRUE;
    m_stCellularInfo.bConfig = SV_TRUE;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 退出工厂模式
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 Cellular_FactoryExit(void)
{
    if (NULL == m_stCellularInfo.pstModuleParam)
    {
        return ERR_BUSY;
    }

    m_stCellularInfo.pstModuleParam->enModuleStatus = CELL_STAT_INIT;
    m_stCellularInfo.pstModuleParam->bFactory = SV_FALSE;
    m_stCellularInfo.bConfig = SV_TRUE;
    return SV_SUCCESS;
}

sint32 Cellular_FactoryOps(CELLULAR_FACTORY_E *peOPS, const char* pcCmd, char* pBuf, uint32 u32Timeout)
{
    sint32 s32Ret = -1;
    CELLULAR_INFO_S *pstCellularInfo = &m_stCellularInfo;
    if(pstCellularInfo == NULL)
    {
        print_level(SV_ERROR, "Use null pointer.\n");
        return SV_FAILURE;
    }
    switch(*peOPS)
    {
        case CELL_FACTORY_RESET:
            s32Ret = cellular_GetModuleType(&(pstCellularInfo->enModuleType));
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR,"get cellular type failed.\n");
                return SV_FAILURE;
            }
            s32Ret = cellular_RebootMoudle(pstCellularInfo);
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR,"Reboot cellular fail.\n");
                return SV_FAILURE;
            }
            else
            {
                pstCellularInfo->enModuleOps = CELL_OPS_INIT;
                pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
            }
            break;
        case CELL_FACTORY_ATCMD:
            s32Ret = cellular_ATCmd(pstCellularInfo, pcCmd, pBuf, u32Timeout);
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR,"Execute AT command fail.\n");
                return SV_FAILURE;
            }
            break;
        default:
            return SV_FAILURE;
            break;
    }
    return SV_SUCCESS;
}

sint32 Cellular_Reset(void)
{
    sint32 s32Ret = -1;
    CELLULAR_INFO_S *pstCellularInfo = &m_stCellularInfo;
    if(NULL == pstCellularInfo)
    {
        print_level(SV_ERROR, "Use null pointer.\n");
        return SV_FAILURE;
    }
    s32Ret = cellular_RebootMoudle(pstCellularInfo);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR,"Reboot cellular fail.\n");
        return SV_FAILURE;
    }
    else
    {
        pstCellularInfo->enModuleOps = CELL_OPS_INIT;
        pstCellularInfo->pstModuleParam->enModuleStatus = CELL_STAT_INIT;
    }
    return SV_SUCCESS;
}

