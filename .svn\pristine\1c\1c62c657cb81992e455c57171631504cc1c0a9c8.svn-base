########################################
#
# IPC/WFC/WFT specific setup
# 
########################################

### GC2053 + SSC335 ###
ifeq ($(BOARD), IPCR20S3)
export ARCH				=arm
export PLATFORM			=SSC335
export PLATFORM_DIR		=ssc335
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_SIGMASTAR_SSC335)
export CROSS_COMPILE	=$(CROSS_ARM_SIGM_STAR)
export CFLAGS 			= -DBOARD_IPCR20S3
export CPPFLAGS 		= -DBOARD_IPCR20S3
endif

### GC2053 + SSC335DE ###
ifeq ($(BOARD), WFCR20S2)
export ARCH				=arm
export PLATFORM			=SSC335
export PLATFORM_DIR		=ssc335
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_SIGMASTAR_SSC335)
export CROSS_COMPILE	=$(CROSS_ARM_SIGM_STAR)
export CFLAGS 			= -DBOARD_WFCR20S2
export CPPFLAGS 		= -DBOARD_WFCR20S2
endif

### GC2053 + SSC335DE ###
ifeq ($(BOARD), IPCR20S4)
export ARCH				=arm
export PLATFORM			=SSC335
export PLATFORM_DIR		=ssc335
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_SIGMASTAR_SSC335)
export CROSS_COMPILE	=$(CROSS_ARM_SIGM_STAR)
export CFLAGS 			= -DBOARD_IPCR20S4
export CPPFLAGS 		= -DBOARD_IPCR20S4
endif

### TP9950 + SSC335DE ###
ifeq ($(BOARD), WFTR20S3)
export ARCH				=arm
export PLATFORM			=SSC335
export PLATFORM_DIR		=ssc335
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_SIGMASTAR_SSC335)
export CROSS_COMPILE	=$(CROSS_ARM_SIGM_STAR)
export CFLAGS 			= -DBOARD_WFTR20S3
export CPPFLAGS 		= -DBOARD_WFTR20S3
endif

### TP2815 + RV1126 (AI Box) ###
ifeq ($(BOARD), ADA42V1)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA42V1
export CPPFLAGS 		= -DBOARD_ADA42V1
endif

### OS05A20 + RV1126 (Dash Cam) ###
ifeq ($(BOARD), ADA47V1)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA47V1
export CPPFLAGS 		= -DBOARD_ADA47V1
endif

### GC2053 + RV1126  ###
ifeq ($(BOARD), DMS31V2)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_DMS31V2
export CPPFLAGS 		= -DBOARD_DMS31V2
endif

### GC2053 + RV1126  ###
ifeq ($(BOARD), ADA32V2)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA32V2
export CPPFLAGS 		= -DBOARD_ADA32V2
endif

### GC2053 + RV1126 + TC933 IRCAM ###
ifeq ($(BOARD), ADA32IR)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA32IR
export CPPFLAGS 		= -DBOARD_ADA32IR
ifeq ($(MOUDLETYPE), 933)
export CFLAGS 			+= -DMOUDLETYPE_TC933
export CPPFLAGS 		+= -DMOUDLETYPE_TC933
endif
ifeq ($(MOUDLETYPE), 639)
export CFLAGS 			+= -DMOUDLETYPE_TC639
export CPPFLAGS 		+= -DMOUDLETYPE_TC639
endif
endif

### OS05A10 + RV1126 ###
ifeq ($(BOARD), ADA32N1)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA32N1
export CPPFLAGS 		= -DBOARD_ADA32N1
endif

### GC2053 + RV1126  ###
ifeq ($(BOARD), ADA900V1)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA900V1
export CPPFLAGS 		= -DBOARD_ADA900V1
endif

### GC2053 + RV1126  ###
ifeq ($(BOARD), DMS31SDK)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_DMS31V2 -DDMS31SDK
export CPPFLAGS 		= -DBOARD_DMS31V2 -DDMS31SDK
endif

### GC2053 + RV1126  ###
ifeq ($(BOARD), ADA32SDK)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA32V2 -DADA32SDK
export CPPFLAGS 		= -DBOARD_ADA32V2 -DADA32SDK
endif

### GC2053 + RV1106  ###
ifeq ($(BOARD), ADA32V3)
export ARCH				=arm
export PLATFORM			=RV1106
export PLATFORM_DIR		=rv1106
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1106)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_1106_CHIP)
export CFLAGS 			= -DBOARD_ADA32V3
export CPPFLAGS 		= -DBOARD_ADA32V3
endif


### GC2053 + RV1126 (Zoom Cam) ###
ifeq ($(BOARD), HDW845V1)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_HDW845V1
export CPPFLAGS 		= -DBOARD_HDW845V1
endif


### TP9950 + SSC335DE ###
ifeq ($(BOARD), IPTR20S1)
export ARCH				=arm
export PLATFORM			=SSC335
export PLATFORM_DIR		=ssc335
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_SIGMASTAR_SSC335_V042)
export CROSS_COMPILE	=$(CROSS_ARM_SIGM_STAR)
export CFLAGS 			= -DBOARD_IPTR20S1
export CPPFLAGS 		= -DBOARD_IPTR20S1
endif

ifeq ($(BOARD), ADA32C4)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA32C4
export CPPFLAGS 		= -DBOARD_ADA32C4
endif
### OS05A10 + RV1126 ###
ifeq ($(BOARD), ADA32NSDK)
export ARCH				=arm
export PLATFORM			=RV1126
export PLATFORM_DIR		=rv1126
export OSTYPE			=linux
export HISIL_SDK		=$(SDK_ROCKCHIP_RV1126)
export CROSS_COMPILE	=$(CROSS_ARM_ROCK_CHIP)
export CFLAGS 			= -DBOARD_ADA32N1 -DADA32NSDK
export CPPFLAGS 		= -DBOARD_ADA32N1 -DADA32NSDK
endif

########################### media ##############################
### UVC ###
ifeq ($(BOARD), $(findstring $(BOARD), ADA32V2 ADA32SDK ADA32C4))
export CFLAGS 			+= -DMAKE_UVC
export CPPFLAGS 		+= -DMAKE_UVC
endif

ifeq ($(BOARD), $(findstring $(BOARD), HDW845V1))
export CFLAGS 			+= -DMAKE_ZOOM
export CPPFLAGS 		+= -DMAKE_ZOOM
endif

########################### peripheral ###########################
### STORAGE ###
ifeq ($(BOARD),$(findstring $(BOARD), WFCR20S2 IPCR20S4 DMS31V2 ADA32V2 ADA32IR ADA32N1 ADA32NSDK ADA32E1 ADA42V1 ADA42PTZV1 ADA47V1 ADA900V1 ADA32V3 DMS31SDK ADA32SDK HDW845V1 ADA32C4 ))
export CFLAGS 			+= -DMAKE_STORAGE
export CPPFLAGS 		+= -DMAKE_STORAGE
endif
### RECORDER ###
ifeq ($(BOARD),$(findstring $(BOARD), IPCR20S4 DMS31V2 ADA32V2 ADA32IR ADA32N1 ADA32NSDK ADA32E1 ADA42V1 ADA42PTZV1 ADA47V1 ADA900V1 ADA32V3 DMS31SDK ADA32SDK HDW845V1 ADA32C4))
export CFLAGS 			+= -DMAKE_RECORDER
export CPPFLAGS 		+= -DMAKE_RECORDER
endif

### LED ###
ifeq ($(BOARD), $(findstring $(BOARD), ADA32V2 ADA32SDK))
export CFLAGS 			+= -DMAKE_LED
export CPPFLAGS 		+= -DMAKE_LED
endif

########################### network protocol ###########################
### RTSP ###
ifeq ($(BOARD), $(findstring $(BOARD), DMS31V2))
export CFLAGS 			+= -DMAKE_RTP_EXTRA_INFO
export CPPFLAGS 		+= -DMAKE_RTP_EXTRA_INFO
endif

########################################
#
# xxxx specific setup
# 
########################################

# vim:noet:sw=4:ts=4
