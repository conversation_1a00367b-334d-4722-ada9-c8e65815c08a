﻿/******************************************************************************
  File Name     : occlusion_analyzer.h
  Version       : 1.0.0

  Author        : Lin <PERSON>ing
  Created       : 2022-03-08
  Last Modified :
  Description   : 
  Function List :
  History       : 
  日期，修改内容：
  日期，修改内容：
******************************************************************************/
#ifndef OCCLUSION_ANALYZER_H
#define OCCLUSION_ANALYZER_H
#include "ocdCommon.h"

#define OCCLUSION_NUM 40
namespace ocdalg
{
	/*遮挡分析类*/
	class COcclusionAnalyzer
	{
	private:
		uint8_t u8State[OCCLUSION_NUM]; //存储遮挡状态数组
		uint32_t u32StateCount; //统计最新四帧遮挡状态
		bool bTimerWork; //计时器是否在工作
		double dTimerBegin; //计时器开始工作时间点
		double dTimerEnd; //计时器当前工作时间点
		double dTimerLength; //计时时长
		float fThreshold; //判断单帧遮挡阈值
		float fTimeThreshold; //判断遮挡时间阈值
	public:
		/******************************************************************************
		功能: 类COcclusionAnalyzer构造函数；
		参数:   fThr，单帧遮挡阈值，fTimeThr，遮挡时间阈值；
		返值: 无；
		应用: 初始化类别成员；
		******************************************************************************/
		COcclusionAnalyzer(float fThr,float fTimeThr);
		/******************************************************************************
		功能: 类COcclusionAnalyzer默认析构函数；
		参数:   无；
		返值: 无；
		应用: 释放类别实例；
		******************************************************************************/
		~COcclusionAnalyzer(){}
		/******************************************************************************
		功能: 分析是否遮挡超过规定时长；
		参数:   fScore，当帧遮挡分数；
		返值: 是否遮挡；
		应用: 用于分析检测类结果并统计是否遮挡；
		******************************************************************************/
		bool OcclusionAnalyzer(STOcdDetectResult stDetectResult);
		/******************************************************************************
		功能: 设置单帧遮挡阈值；
		参数:   fThr，单帧遮挡阈值；
		返值: 无；
		应用: 用于设置单帧遮挡阈值；
		******************************************************************************/
		void SetOcclusionThreshold(float fThr);
		/******************************************************************************
		功能: 设置遮挡时间阈值；
		参数:   fTimeThr，遮挡时间阈值；
		返值: 无；
		应用: 用于设置遮挡时间阈值；
		******************************************************************************/
		void SetOcclusionTimeThreshold(float fTimeThr);
    };

}

#endif