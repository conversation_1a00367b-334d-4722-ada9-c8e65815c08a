﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../css/jquery.mobile-1.4.5.min.css">
	<script src="../js/jquery.min.js"></script>
	<script src="../js/jquery.mobile-1.4.5.min.js"></script>
	<link rel="stylesheet" href="qunit/qunit-2.4.0.css">
	<title>Test</title>
<script>
$(document).ready(function(){
	console.log("swipepages ready");
	$(document).on("swipeleft", ".ui-page", function(){
		console.log("swipeleft:"+this.id);
		var next = $(this).jqmData("next");
		next && console.log("goto:"+next);
		next && $.mobile.changePage( "#"+next, {transition: "slide"});
	});
	$(document).on("swiperight", ".ui-page", function(){
		console.log("swiperight:"+this.id);
		var prev = $(this).jqmData("prev");
		prev && console.log("goto:"+prev);
		prev && $.mobile.changePage( "#"+prev, {transition: "slide", reverse:true});
	});
	$(document).on("swipedown", ".ui-page", function(){
		console.log("swipedown:"+this.id);
	});
	$(document).on("swipetop", ".ui-page", function(){
		console.log("swipetop:"+this.id);
	});
	$(document).on("swipe", ".ui-page", function(){
		console.log("swipe:"+this.id);
	});
});
$(document).on("pagecreate", ".swipe-page", function(){
	console.log("pagecreate:"+this.id);
});
</script>
</head>
<body>
	<div data-role="page" id="page1" class="swipe-page" data-theme="b" data-dom-cache="true" data-next="page2" data-url="page">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test swipe pages</h1>
		</div>
		<div>
			<fieldset data-role="controlgroup" data-type="horizontal">
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext ui-btn-active"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			</fieldset>
		</div>
		<div role="main" class="ui-content">
			<p>Page1</p>
		</div>
	</div>
	<div data-role="page" id="page2" class="swipe-page" data-theme="b" data-dom-cache="true" data-prev="page1" data-next="page3" data-url="page">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test swipe pages</h1>
		</div>
		<div>
			<fieldset data-role="controlgroup" data-type="horizontal">
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext ui-btn-active"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
				<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			</fieldset>
		</div>
		<div role="main" class="ui-content">
			<p>Page2</p>
		</div>
	</div>
	<div data-role="page" id="page3" class="swipe-page" data-theme="b" data-dom-cache="true" data-prev="page2" data-next="page4" data-url="page">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test swipe pages</h1>
		</div>
		<fieldset data-role="controlgroup" data-type="horizontal">
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext ui-btn-active"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
		</fieldset>
		<div role="main" class="ui-content">
			<p>Page3</p>
		</div>
	</div>
	<div data-role="page" id="page4" class="swipe-page" data-theme="b" data-dom-cache="true" data-prev="page3" data-next="page5" data-url="page">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test swipe pages</h1>
		</div>
		<fieldset data-role="controlgroup" data-type="horizontal">
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext ui-btn-active"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
		</fieldset>
		<div role="main" class="ui-content">
			<p>Page4</p>
		</div>
	</div>
	<div data-role="page" id="page5" class="swipe-page" data-theme="b" data-dom-cache="true" data-prev="page4" data-next="page6" data-url="page">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test swipe pages</h1>
		</div>
		<fieldset data-role="controlgroup" data-type="horizontal">
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext ui-btn-active"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
		</fieldset>
		<div role="main" class="ui-content">
			<p>Page5</p>
		</div>
	</div>
	<div data-role="page" id="page6" class="swipe-page" data-theme="b" data-dom-cache="true" data-prev="page5"  data-url="page">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test swipe pages</h1>
		</div>
		<fieldset data-role="controlgroup" data-type="horizontal">
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext"></a>
			<a href="#" class="ui-btn ui-btn-inline ui-icon-search ui-btn-icon-notext ui-btn-active"></a>
		</fieldset>
		<div role="main" class="ui-content">
			<p>Page6</p>
		</div>
	</div>
</body>
</html>