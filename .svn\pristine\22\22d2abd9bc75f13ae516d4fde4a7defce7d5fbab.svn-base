#ifndef _ZOOM_TRK_H_
#define _ZOOM_TRK_H_
#include "../../../include/common.h"
#include "config.h"
#include "op.h"
#include "zoom.h"
#include "SiameseFCpp.h"


typedef struct tag_AlgTrkSta_S
{
    SV_BOOL          bClear;                /* 清除屏幕或报警信息 */
    SV_BOOL          bLost;                 /* 目标丢失 */

} TRK_STAT_S;


/* 跟踪算法信息 */
typedef struct tag_TrkInfo_S
{
    SV_BOOL         bPosUpdate;               /* 更新目标坐标标志 */
    SV_BOOL         bIsInit;                  /* 是否已经初始化 */
    SFCP_BOX        stFirstBox;               /* 配置目标框坐标 */
} TRK_INFO_S;


/* 跟踪算类 */
class ZoomTrk : public ZoomAlg
{
private :
    
public :
   ZoomTrk()
   {
       enAlg        = ALG_ZOOM;
       width        = 608;
       height       = 352;
       format       = ALG_FMT_RGB888;
       modelPath    = "/root/model/track.rknn";
       channel      = 0;
       device       = 0;
       threshold    = 0.3;
       memset(&stTrkInfo, 0, sizeof(TRK_INFO_S));
       memset(&stTrkStat, 0, sizeof(TRK_STAT_S));
       aScopePreset[0] = 0.5;
       aScopePreset[1] = 0.65;
       aScopePreset[2] = 0.8;
   }

    SFCP                *pSFCP;
    TRK_INFO_S          stTrkInfo;          /* 配置信息 */
    TRK_STAT_S          stTrkStat;          /* 状态信息 */
    double              threshold;          /* 得分阈值 */
    double              aScopePreset[ZOOM_MAX_SEN];/* 预设对应三个不同灵敏度不同的搜索范围 */
    double              scope;              /* 搜索范围 */
    virtual sint32 Init(void *initParam);
    virtual sint32 Fini();
    virtual sint32 Start(void *startParam);
    virtual sint32 Stop();
    virtual sint32 PreProcess();
    virtual sint32 DoProcess(ALG_RES_S *result);
    virtual sint32 PostProcess(ALG_RES_S *result);
    virtual sint32 SetConifg(void   *data);
    sint32 UpdatePos(void * data);

};


#endif /* _ZOOM_TRK_H_ */

