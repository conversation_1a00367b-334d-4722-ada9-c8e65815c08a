#
# Link all library, and build the final excutable file
#

include ../../Makefile.param

ifneq ($(BOARD), ADA32V2)
IGNORE_THIS_BUILD = pd
endif

# Link SV Common library
SV_COM_LIBS = -lalg_pd -lalarm -lpds_normal_rv1126 -lcjson -lsharefifo -llog -lmsg -lboard -lconfig -lmxml -luuid -lcrypto

# Link other SV libs
OTHER_SV_LIBS 	= -lcunit -lrknn_api -lv4l2 -lv4lconvert -lrga -ldrm -lrockchip_mpp

ifeq ($(OSTYPE), linux)
SV_COM_LIBS += -lsafefunc
SYSTEM_LIB	= -lsafefunc -lssl -lcrypto -ldl -lpthread -lm  
ifeq ($(PLATFORM), SSC335)
CUNIT_PLATFORM = mstar
else ifeq ($(PLATFORM), RV1126)
CUNIT_PLATFORM = rk
else
CUNIT_PLATFORM = hisi
endif
endif


CFLAGS += -I$(INC_PATH)/libdrm -I$(INC_PATH)/libdrm/drm -I../cunit/inc -I$(SRC_PATH)/alg/include -I$(INC_PATH)/cjson -L$(LIB_PATH) 
CPPFLAGS = $(CFLAGS)

TARGET_BIN	= test_pd
LIB_DEPEND	= $(COMP_DEPEND)
LD_FLAGS	+= -L$(LIB_PATH) -L$(TOP_LIB) -L../cunit/lib/$(OSTYPE)/$(CUNIT_PLATFORM)
LD_FLAGS	+=$(SYSTEM_LIB) $(OTHER_SV_LIBS) $(SV_COM_LIBS)

COPY_TO_DIR = /work/nfs/
include $(BIN_AUTO_DEP_MK)

# vim:noet:sw=4:ts=4

