#include <stdio.h>
#include <string.h>
#include <unistd.h> 
#include <fcntl.h>
#include <errno.h>
#include <sys/types.h>
#include <signal.h> 
#include <sys/wait.h>
#include <sys/syscall.h>
#include <dirent.h>
#include <stdlib.h>
#include <fcntl.h>
#include <sys/prctl.h>
#include <stdarg.h>
#include <dirent.h>
#include <string>
#include <sys/stat.h>
#include <errno.h>

#include "cms_common.h"
#include "cms_devInfo.h"

using namespace std;


SV_BOOL SV_DVR_COMMON_IsFileExist(const char *pFile)
{

	if(access(pFile, 0) == 0)
	{
		return SV_TRUE;
	}
	else
	{
		return SV_FALSE;
	}
	
}

sint32 SV_DVR_COMMON_getPopenBuf(const char *pcmd, char *pbuf, uint32 u32size)
{

	SV_CHECK(pcmd);
	SV_CHECK(pbuf);

	FILE *stream;
	
	if( (stream = popen( pcmd, "r" )) == SV_NULL )
	{
		printf("popen error!\n");
		printf("pcmd:%s\n", pcmd);
		return SV_FAILURE;
	}

	if( (fread( pbuf, sizeof(char), u32size,  stream)) < 1 )
	{
		pclose( stream );
		return SV_FAILURE;
	}
	pclose( stream );

	return SV_SUCCESS;

}


void SV_DVR_COMMON_CloseEvec(sint32 s32Fd)
{ 
	int flags = fcntl(s32Fd, F_GETFD);  
	flags |= FD_CLOEXEC;  
	fcntl(s32Fd, F_SETFD, flags);
}


void SV_DVR_COMMON_CloseFileEvec(FILE *fp)
{ 
	int s32Fd;
    s32Fd = fileno(fp); 
	SV_DVR_COMMON_CloseEvec(s32Fd);
}

sint32 SV_DVR_SV_COMMON_system_ex(const char *cmdStr, int timeout)
{
	pid_t pid;
	int   status = 0;
	int   ret;
	if(NULL == cmdStr)
		return -1;
	if(timeout <=0)
		return -1;

	
	if((pid = fork()) < 0)
		return -1;
	else
	{	
		if(0 == pid)
		{
			execl("/bin/sh","sh","-c",cmdStr,NULL);
		}
		else
		{
			// parent process
			usleep(10000);
			while((ret=waitpid(pid,&status,WNOHANG)) <= 0)
			{
				if(ret == -1)
				{
					if(errno != EINTR)
					{
						if(kill(pid,9) == 0)
                    	{
                        	waitpid(pid,NULL,0);
                        	return -1;
                    	}
                    	else
                    	{
                            return -1;
                    	}
					}
					else
						continue;
				}
				

				if(timeout > 0)
				{
					sleep(1);
					timeout--;
				}
				else
				{
					if(kill(pid,9) == 0)
					{
						waitpid(pid,NULL,0);
						printf("Kill pid:%d success\n", pid);
						return RETURN_SYSTEM_TIMEOUT;
					}
					else
					{
						printf("Kill pid:%d fail\n", pid);
						return -1;
					}
				}
			}
			return status;
		}
	}
}


void SV_DVR_COMMON_KillallProcess(char *cmdStr)
{
	char tmpFilePath[128];					// 存放 cmdline 文件的地址
	char tmpBuf[128];							// 临时存放从 cmdline 文件中取出的数据 <二进制>
	char CmdLineStr[128];					// cmdline 内不二进制数据所转化的字符串
	char NeedMatchStr[128];				// 存放根据 cmdStr 计算出来的需要匹配的字符串
	long tmpPos,tmpNeedMatchStrPos,CmdLineStrPos,len;
	DIR *pdir;
	int fd;
	/* 获取 cmdStr 中的关键词，根据管池杀掉相关的继承 ------ 与当前进程 cmdline 相等的除外 */
	struct dirent *pdirent;
	tmpPos = 0;
	tmpNeedMatchStrPos = 0;
	while(1)
	{
		if(cmdStr[tmpPos] == 0)
			break;
		if(tmpNeedMatchStrPos >=127)
			break;
		if((cmdStr[tmpPos] != ' ')&&(cmdStr[tmpPos] != '&'))
		{
			NeedMatchStr[tmpNeedMatchStrPos] = cmdStr[tmpPos];
			tmpNeedMatchStrPos++;
		}
		tmpPos++;
	}
	NeedMatchStr[tmpNeedMatchStrPos] = 0;
	NeedMatchStr[127] = 0;
	printf("killall keyword : %s\n",NeedMatchStr);
	if((pdir =  opendir("/proc/"))!=NULL)
	{
		int tmpPid,ret;
		while((pdirent = readdir(pdir)))
		{
			tmpPid = atoi(pdirent->d_name);
			if(tmpPid > 0)
			{
				snprintf(tmpFilePath, 128, "/proc/%d/cmdline",tmpPid);
				fd = open(tmpFilePath,O_RDONLY);
				if(fd < 0)
				{
					printf("open %s fail\n",tmpFilePath);
					continue;
				}
				len = read(fd,tmpBuf,128);
				CmdLineStrPos = 0;
				tmpPos		= 0;
				while(1)
				{
					if(tmpPos >=128)
						break;
					if(tmpPos >= len)
						break;
					if(tmpBuf[tmpPos]!=0)
					{
						CmdLineStr[CmdLineStrPos] = tmpBuf[tmpPos];
						CmdLineStrPos++;
					}
					tmpPos++;
				}
				CmdLineStr[CmdLineStrPos] = 0;
				CmdLineStr[127] = 0;
				// print_level(INFO,"%s %s\n",CmdLineStr,NeedMatchStr);
				if(strcmp(CmdLineStr,NeedMatchStr)==0)
				{
					printf("kill %s %d\n",tmpBuf,tmpPid);
					if(kill(tmpPid,9) < 0)
						printf("killfail \n");
					usleep(1000);
				}
				close(fd);
			}
		}
		closedir(pdir);
	}
	else
		printf("open dir fail\n");
}

void SV_COMMON_CutLineBreak(char *pszStr)
{
	char *pcTmp = NULL;
    
    pcTmp = strchr(pszStr, '\r');
	if(NULL == pcTmp)
		pcTmp = strchr(pszStr, '\n');
	if(NULL != pcTmp)
		*pcTmp = '\0';
}

sint32 SV_COMMON_cutOutName(const char *pSrc, char *pResut)
{

	char *pTemp = pSrc;
	sint32 s32len = strlen(pSrc);
	sint32 s32Count = 0;

	for(s32Count = 0; s32Count < s32len; s32Count++)
	{
		if( pSrc[s32Count] == '/' )
		{
			pTemp = pSrc + s32Count + 1;
		}
	}

	strcpy(pResut, pTemp);

	return SV_SUCCESS;
}

sint32 SV_COMMON_getFileDir(const char *szSrc, char *szDir)
{
	char *pTemp = szSrc;
	sint32 s32len = strlen(szSrc);
	sint32 s32Count = 0, s32DirLen = 0;

	for(s32Count = 0; s32Count < s32len; s32Count++)
	{
		if( szSrc[s32Count] == '/' )
		{
			pTemp = szSrc + s32Count;
		}
	}
	s32DirLen = pTemp - szSrc;
	strncpy(szDir, szSrc, s32DirLen);
	szDir[s32DirLen] = 0;
	
	return SV_SUCCESS;
}

sint32 SV_COMMON_getAlarmFileDir(UPLOAD_FILE_OPTS_E enUploadFileOpts, char *pFileName, char *pFullPath)
{
    char szDir[128] = {0};
    char szSubDir1[128] = {0};
	char szSubDir2[128] = {0};
	char szSubDir3[128] = {0};
    char szStoragePath[32] = {0};
    SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);

    switch (enUploadFileOpts)
    {
        case UPLOAD_FILE_ALARM_VIDEO:
            sprintf(szDir, "%s/alarm/", szStoragePath);
            break;

        case UPLOAD_FILE_ALARM_PICTURE:
            sprintf(szDir, "%s/picture/", szStoragePath);
            break;

        case UPLOAD_FILE_ALARM_ALL:
            if (NULL != strstr(pFileName, ".jpg"))
                sprintf(szDir, "%s/picture/", szStoragePath);
            else
                sprintf(szDir, "%s/alarm/", szStoragePath);
            break;

        case UPLOAD_FILE_NORMAL_VIDEO:
            sprintf(szDir, "%s/normal/", szStoragePath);
            break;

        default:
            return SV_FAILURE;
    }

	strncpy(szSubDir1, pFileName, sizeof("19700101")-1);
	strncpy(szSubDir2, pFileName+sizeof("19700101")-1, sizeof("23")-1);
	strncpy(szSubDir3, pFileName+sizeof("1970010123")-1, sizeof("59")-1);
	sprintf(pFullPath, "%s%s/%s/%s/%s", szDir, szSubDir1,szSubDir2,szSubDir3,pFileName);

    return SV_SUCCESS;
}


sint64 SV_COMMON_getFileSize(const char *pFile)
{
	sint64 filesize = -1;	
	struct stat statbuff;
	if(stat(pFile, &statbuff) < 0)
	{
		return filesize;
	}
	else
	{
		filesize = statbuff.st_size;
	}
	
	return filesize;
}

sint32 SV_COMMON_SetThread(const char *pName)
{
	prctl(PR_SET_NAME, pName);

	return SV_SUCCESS;
}

sint32 SV_COMMON_system(const char *pcmd)
{

	pid_t status; 

	status = SAFE_SV_System(pcmd);  
	if (-1 == status)  
	{  
		perror("system error!");
		printf("pcmd:%s\n", pcmd);
		return SV_FAILURE;
	}  
    else  
    {  
       // printf("exit status value = [0x%x]\n", status);  
  
        if (WIFEXITED(status))  
        {  
            if (0 == WEXITSTATUS(status))  
            {  
               // printf("run shell script successfully.\n");
                return SV_SUCCESS;
            }  
            else  
            {  
            	
              //  printf("pcmd:%s fail exit code: %d \n", pcmd, WEXITSTATUS(status)); 
                return SV_FAILURE;
            }  
        }  
        else  
        {  
            printf("exit status = [%d]\n", WEXITSTATUS(status)); 
            return SV_FAILURE;
        }  
    }  

}

sint32 SV_COMMON_checkMd5(char *pFileName, char *pMd5)
{

	char cmd[COMMON_CMD_LEN];
	char buf[128];
	snprintf(cmd, COMMON_CMD_LEN, "md5sum %s", pFileName);
	FILE *stream;
	
	if( (stream = popen( cmd, "r" )) == NULL )
	{
		printf("popen error!\n");
		return SV_FAILURE;
	}

	if( (fread( buf, sizeof(char), 128,  stream)) < 1 )
	{
		pclose( stream );
		return SV_FAILURE;
	}
	pclose( stream );

	printf("buf:%s, pMd5:%s\n", buf, pMd5);

	if( strncmp(buf, pMd5,32) == 0 )
	{
		printf("checkMd5 return 0\n");
		return SV_SUCCESS;
	}
	else
	{
		return SV_FAILURE;
	}
	
}

uint64 SV_ADAS_getDeviceId()
{
	char buf[64] = {0};
	sint32 s32Fd = -1;
	static uint64 u64Uid = 0;
	if(u64Uid != 0)
	{
		return u64Uid;
	}
	
	s32Fd = open(CONFIG_SERAILNUM, O_RDONLY);
	if(s32Fd <0)
	{
		return u64Uid;
	}

	read(s32Fd, buf, 64);
	close(s32Fd);
	sscanf(buf, "%llu", &u64Uid);	

	return u64Uid;	
}

sint32 SV_ADAS_getuuid(char *pUuid)
{
	int fd = -1;
	fd = open("/proc/sys/kernel/random/uuid", O_RDONLY);
	if(fd < 0)
	{
		return SV_FAILURE;
	}
	
	read(fd, pUuid, 36);
	close(fd);


	return SV_SUCCESS;
}

uint32 SV_ADAS_getTimeTick()
{
	uint32 u32TimeMs = 0;
    struct timespec time1 = {0, 0};
    if(clock_gettime(CLOCK_MONOTONIC, &time1)<0)
        return 0;
    u32TimeMs = time1.tv_sec * 1000 + time1.tv_nsec/1000000;
    return u32TimeMs;  
}


sint32 SV_COMMON_JOIN_THREAD::start()
{
    if(pthread_create(&pid, SV_NULL, start_thread, (void *)this) != 0) 
    {      
    	printf("create thread error\n");
        return SV_FAILURE;
    }     
    
    return SV_SUCCESS;
}

void* SV_COMMON_JOIN_THREAD::start_thread(void *arg)
{
    SV_COMMON_THREAD *ptr = (SV_COMMON_THREAD *)arg;
    ptr->run();  

    return (void*) 0;
}

sint32 SV_COMMON_JOIN_THREAD::stop()
{
	sint32 s32Ret;
	s32Ret = pthread_join(pid,NULL);
	if(s32Ret != 0)
	{
		printf("FilePrivateUpload thread join error [err: %s]\n", strerror(errno));
		return SV_FAILURE;
	}
	return SV_SUCCESS;
}

sint32 SV_COMMON_JOIN_THREAD::cancel()
{	
	printf("pthread_cancel\n");
	pthread_cancel(pid);
	return SV_SUCCESS;
}


sint32 SV_COMMON_THREAD::start()
{
	pthread_attr_t attr;
	pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    
    if(pthread_create(&pid, &attr, start_thread, (void *)this) != 0) 
    {      
    	printf("create thread error\n");
        return SV_FAILURE;
    }     
    
    pthread_attr_destroy(&attr);
    return SV_SUCCESS;
}

void* SV_COMMON_THREAD::start_thread(void *arg)
{
    SV_COMMON_THREAD *ptr = (SV_COMMON_THREAD *)arg;
    ptr->run();  

    return (void*) 0;
}

sint32 SV_COMMON_THREAD::stop()
{
	return pthread_cancel(pid);
}




