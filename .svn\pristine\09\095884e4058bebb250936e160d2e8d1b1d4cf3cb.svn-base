﻿/******************************************************************************
  Version       : 2.0.0

  Author        : Lin Jiaping
  Created       : 2023-12-20
  Description   : 定义与应用层交互的信息结构体
******************************************************************************/
// DMSAPI：修饰符宏定义（定义库对外接口，在需要导出的接口、类、对象处添加）；
// WinDMSDLL(WinDMSDLL_EXPORTS): 各平台DMS算法库导入\导出类型宏定义，cmake中通过target_compile_definitions（）使用；
// 一个项目可以只定义一处即可，其它需要定义对外接口的头文件，包含此头文件即可；
#ifdef WinDMSDLL
    #ifdef WinDMSDLL_EXPORTS
        #define DMSAPI __declspec(dllexport) // Windows 平台导出符号
    #else
        #define DMSAPI __declspec(dllimport)  // Windows 平台导入符号
    #endif
#else
    // Windows平台静态库、Linux平台不需要定义修饰符 __declspec
    #define DMSAPI
#endif

#ifndef DMS_COMMON_H
#define DMS_COMMON_H
#include <iostream>
#include <fstream>
#include <cstring>
#include <stdio.h>
#include <stdint.h>
#include <vector>

using namespace std;

namespace dmsalg
{
    /***********************
    * 算法功能模式
    ***********************/
    typedef enum ALG_MODE
    {
        E_DMS = 0,          /*传统模式*/
        E_DMS_P,            /*加强Plus模式*/
        E_DDAW_ADDW,        /*符合DDAW&ADDW模式*/
    }EAlgMode;
    /***********************
    * 算法运行模式
    ***********************/
    typedef enum RUN_MODE
    {
        E_RUN_NORMAL = 0,          /*产品正常运行*/
        E_RUN_TEST =1,             /*日常测试，展会模式*/
    }ERunMode;
    typedef enum CUSTOMER_INFO
    {
        E_CUMSTOMER_NORMAL = 0,     /*通用版本*/
        E_CUMSTOMER_1,              /*中联重科客户*/
        E_CUMSTOMER_2,              /*embedded客户-Sigmastar*/
    }ECustomerInfo;
    typedef enum NO_DRIVER_STRATERY
    {
        E_NO_DRIVER_BODY = 0,           /*以检测不到身体为判断依据*/
        E_NO_DRIVER_FACE,              /*以检测不到人脸为判断依据*/
    }ENoDriverStratery ;
    /***********************
    * 目标类型
    ***********************/
    typedef enum ALG_OBJ_CLS {
        E_CLS_FACE = 0,
        E_CLS_PHONE = 1,
        E_CLS_SMOKE,
        E_CLS_SEAT_BELT,
        E_CLS_HELMET,
        E_CLS_DRINK,
        E_CLS_PHONE_SMOKE_DRINK,
        E_CLS_PEOPLE,            /* 保留类型, 未知 */
        E_CLS_RESERVE1,            /* 保留类型, 未知 */
        E_CLS_RESERVE2,            /* 保留类型, 未知 */
        E_CLS_RESERVE3,            /* 保留类型, 未知 */
    } EAlgObjectClass;

    /***********************
     * 目标框
     ***********************/
    struct STRectBox {
        // 该结构体的坐标值, 是基于长宽为1的框, 再乘以实际图像的宽或者高才是实际坐标
        float fX1;       // 左上角x坐标, 区间[0,1]
        float fY1;    // 左上角y坐标, 区间[0,1]
        float fX2;    // 右下角x坐标, 区间[0,1]
        float fY2;    // 右下角y坐标, 区间[0,1]
        float fConfidence;   // 目标的置信度
        EAlgObjectClass eCls;  // 目标类型
        STRectBox():fX1(0),fY1(0),fX2(0),fY2(0),fConfidence(0),eCls(E_CLS_RESERVE1){}
    };

    struct STPeopleType 
    {
        //用于乘员检测的结构体，在STRectBox的基础上增加了一个区分前后排乘客的标志位
        STRectBox stBoundindBox;
        float fPeople;          //前后排乘客的标志位,0为前排，1为后排
        STPeopleType():fPeople(0){}
    };
    /*************************
     * DMS检测结果
     ************************/

    #define LANDMARK_POINT_NUM 28
    struct STDmsDetectResult
    {
        bool bDetectFace;                                            //是否有检测到人脸
        float pfLandmarkPoints[LANDMARK_POINT_NUM * 2];              //人脸特征点坐标， 取值范围是0.0～1.0
        float pfEyeCloseRate[2];                                     //眼睛闭合度的绝对值，0,左眼，1，右眼
        float pfpHeadPose[3];                                        //头部姿态，0,俯仰角，1,水平角， 2旋转角
        float pfGaze[2];                                             //视线方向，0,俯仰角，1,水平角
        float fSmokeScore;                                           //抽烟检测分数
        float fPhoneScore;                                           //打电话检测分数
        float fDrinkScore;                                           //喝东西检测分数
        bool bYawn;                                                  //是否打哈欠
        float fYawnScore;                                            //打哈欠分数
        bool bNoMask;                                                //是否不带口罩
        float pfpEyeStateScore[4];                                   //左眼睁开，左眼闭合，右眼睁开，右眼闭合
        float pfpSunglassesScore[2];                                 //0，带非红外阻断眼镜分数，1,带红外阻断眼镜分数
        float fSeatbeltScore;                                        //不带安全带检测分数
        float fHelmetScore;                                          //不带安全帽检测分数
        float fOcclusionScore;                                       //遮挡检测分数
        float fDazzleScore;                                          //炫光检测分数
        STRectBox stFaceBoundBox;                                    //人脸方框
        std::vector<STPeopleType> vec_stPeopleInfo;                  //舱内人员信息
        uint8_t * pcFaceImgPtr;
        float pfFeature[512];
        bool bFeatureVal;
        char pu8UserPath[512];
        STDmsDetectResult():bDetectFace(true),fSmokeScore(0.0),fPhoneScore(0.0),fDrinkScore(0.0),fYawnScore(0.0),bYawn(false),
                            bNoMask(false),fSeatbeltScore(0.99),fHelmetScore(1.0), fOcclusionScore(0.0),fDazzleScore(0.0),bFeatureVal(false)
                            {
                                // memset(pfLandmarkPoints, -1, LANDMARK_POINT_NUM * sizeof (float) * 2);
                                for(int i = 0 ;i<LANDMARK_POINT_NUM * 2;i++)
                                {
                                    pfLandmarkPoints[i] = -1;
                                }
                                memset(pfFeature, 0, sizeof (float) * 512);
                                pfEyeCloseRate[0] = 0.35;
                                pfEyeCloseRate[1] = 0.35;
                                memset(pfpHeadPose, 0, 3 * sizeof(float));
                                memset(pfpEyeStateScore, 0, 4 * sizeof(float));
                                memset(pfpSunglassesScore, 0, sizeof(float));
                                memset(pfGaze, 0, 2 * sizeof(float));
                                vec_stPeopleInfo.clear();
                                pcFaceImgPtr = new uint8_t[112 * 112 * 1];
                                memset(pu8UserPath, 0, sizeof(pu8UserPath));
                            }

        // 自定义拷贝构造函数
        STDmsDetectResult(const STDmsDetectResult& other)
        {
            bDetectFace = other.bDetectFace;
            memcpy(pfLandmarkPoints, other.pfLandmarkPoints, sizeof(pfLandmarkPoints));
            memcpy(pfEyeCloseRate, other.pfEyeCloseRate, sizeof(pfEyeCloseRate));
            memcpy(pfpHeadPose, other.pfpHeadPose, sizeof(pfpHeadPose));
            memcpy(pfGaze, other.pfGaze, sizeof(pfGaze));
            fSmokeScore = other.fSmokeScore;
            fPhoneScore = other.fPhoneScore;
            fDrinkScore = other.fDrinkScore;
            fYawnScore = other.fYawnScore;
            bYawn = other.bYawn;
            bNoMask = other.bNoMask;
            memcpy(pfpEyeStateScore, other.pfpEyeStateScore, sizeof(pfpEyeStateScore));
            memcpy(pfpSunglassesScore, other.pfpSunglassesScore, sizeof(pfpSunglassesScore));
            fSeatbeltScore = other.fSeatbeltScore;
            fHelmetScore = other.fHelmetScore;
            fOcclusionScore = other.fOcclusionScore;
            fDazzleScore = other.fDazzleScore;
            stFaceBoundBox = other.stFaceBoundBox;
            vec_stPeopleInfo = other.vec_stPeopleInfo; // 自动处理

            memcpy(pcFaceImgPtr, other.pcFaceImgPtr, 112 * 112 * 1);
            memcpy(pfFeature, other.pfFeature, sizeof(pfFeature));
            bFeatureVal = other.bFeatureVal;
            memcpy(pu8UserPath, other.pu8UserPath, sizeof(pu8UserPath));
        }

        STDmsDetectResult& operator=(const STDmsDetectResult& other)
        {
            if (this != &other) {
                // 释放当前资源
                if(pcFaceImgPtr)
                {
                    delete[] pcFaceImgPtr;
                }

                // 手动复制其他成员
                bDetectFace = other.bDetectFace;
                memcpy(pfLandmarkPoints, other.pfLandmarkPoints, sizeof(pfLandmarkPoints));
                memcpy(pfEyeCloseRate, other.pfEyeCloseRate, sizeof(pfEyeCloseRate));
                memcpy(pfpHeadPose, other.pfpHeadPose, sizeof(pfpHeadPose));
                memcpy(pfGaze, other.pfGaze, sizeof(pfGaze));
                fSmokeScore = other.fSmokeScore;
                fPhoneScore = other.fPhoneScore;
                fDrinkScore = other.fDrinkScore;
                fYawnScore = other.fYawnScore;
                bYawn = other.bYawn;
                bNoMask = other.bNoMask;
                memcpy(pfpEyeStateScore, other.pfpEyeStateScore, sizeof(pfpEyeStateScore));
                memcpy(pfpSunglassesScore, other.pfpSunglassesScore, sizeof(pfpSunglassesScore));
                fSeatbeltScore = other.fSeatbeltScore;
                fHelmetScore = other.fHelmetScore;
                fOcclusionScore = other.fOcclusionScore;
                fDazzleScore = other.fDazzleScore;
                stFaceBoundBox = other.stFaceBoundBox; // 假设该结构体支持拷贝
                vec_stPeopleInfo = other.vec_stPeopleInfo; // 自动处理

                // 处理图像数据
                if (other.pcFaceImgPtr)
                {
                    pcFaceImgPtr = new uint8_t[112 * 112 * 1]; // 假设为 RGB 图像
                    memcpy(pcFaceImgPtr, other.pcFaceImgPtr, 112 * 112 * 1);
                }
                else
                {
                    pcFaceImgPtr = nullptr;
                }

                memcpy(pfFeature, other.pfFeature, sizeof(pfFeature));
                bFeatureVal = other.bFeatureVal;
                memcpy(pu8UserPath, other.pu8UserPath, sizeof(pu8UserPath));
            }
            return *this;
        }

        // 析构函数
        ~STDmsDetectResult()
        {
            if(pcFaceImgPtr)
            {
                delete[] pcFaceImgPtr;
            }
        }
    };
    /***********************
    * 算法参数
    ***********************/
    struct STAlgParam
    {
        uint32_t u32Width;  // (返回值)算法要求的图像的宽
        uint32_t u32Height;  // (返回值)算法要求的图像的高
        uint32_t u32Channel;  // (返回值)算法要求的图像的通道数
        float fScore;  // (默认0.52) 得分阈值
        float fNMS;  // (默认0.45) nms阈值
        // 默认值
        STAlgParam():fScore(0.52),fNMS(0.45){}
        STAlgParam(float set_score, float set_nms):fScore(set_score),fNMS(set_nms){}
    };

    /***********************
     * 算法模型路径
     ***********************/
    struct STAlgModelPath {
        char* pu8ModelFD;                                //人脸位置检测模型——centerface_output.rknn
        char* pu8ModelFA;                                //人脸特征点检测模型——HG_output.rknn
        char* pu8ModelFR;                                //人脸识别模型——FR_output.rknn
        char* pu8ModelEyeState;                          //眼睛状态分类模型——eyeNet_output.rknn
        char* pu8ModelHelmet;                            //安全帽检测模型——helmetNet_output.rknn
        char* pu8ModelMask;                              //口罩检测模型——mask_output.rknn
        char* pu8ModelCameraAnomaly;                     //遮挡异常模型——RGB_256_256_CAD.rknn
        char* pu8ModelMultifunction;                     //多功能检测模型（打电话，香烟，喝东西）——phone_drink_smoke.rknn
        char* pu8ModelSeatbelt;                          //安全带检测模型——seatbelt_output.rknn
        char* pu8ModelSmokeT1;                           //抽烟手势检测模型——smoket1_output.rknn
        char* pu8ModelSunglasses;                        //红外阻断眼睛检测模型——glassnet_output.rknn
        char* pu8ModelGaze;                              //视线检测模型——gazeNet_output.rknn
        char* pu8ModelFAS;                               //人脸特征点检测小模型——GRAY_256_256_FAS.rknn
        char* pu8ModelPeople;                            //乘员检测模型
        // 默认值
        STAlgModelPath():pu8ModelFD(nullptr), pu8ModelFA(nullptr), pu8ModelFR(nullptr), pu8ModelEyeState(nullptr), pu8ModelHelmet(nullptr),
                         pu8ModelMask(nullptr),pu8ModelCameraAnomaly(nullptr),pu8ModelMultifunction(nullptr),pu8ModelSeatbelt(
                        nullptr), pu8ModelSmokeT1(nullptr), pu8ModelSunglasses(nullptr), pu8ModelGaze(nullptr), pu8ModelFAS(
                        nullptr),pu8ModelPeople(nullptr){}
    };


    /***********************
     * 算法模型版本
     ***********************/
    struct STAlgModelVersion {
        char* pu8ModelFDVersion ;
        char* pu8ModelFAVersion ;
        char* pu8ModelFRVersion ;
        char* pu8ModelEyeStateVersion ;
        char* pu8ModelHelmetVersion ;
        char* pu8ModelMaskVersion ;
        char* pu8ModelCameraAnomalyVersion ;
        char* pu8ModelPhoneVersion ;
        char* pu8ModelSeatbeltVersion ;
        char* pu8ModelSmokeT1Version ;
        char* pu8ModelSunglassesVersion ;
        char* pu8ModelFASVersion;
        char* pu8ModelPeopleVersion;

        // 默认值
        STAlgModelVersion():pu8ModelFDVersion(nullptr), pu8ModelFAVersion(nullptr), pu8ModelFRVersion(nullptr), pu8ModelEyeStateVersion(nullptr), pu8ModelHelmetVersion(nullptr),
                            pu8ModelMaskVersion(nullptr),pu8ModelCameraAnomalyVersion(nullptr),pu8ModelPhoneVersion(nullptr),pu8ModelSeatbeltVersion(
                        nullptr),  pu8ModelSmokeT1Version(nullptr), pu8ModelSunglassesVersion(nullptr), pu8ModelFASVersion(
                        nullptr), pu8ModelPeopleVersion(nullptr){}
    };
    /***********************
       * 舱内状态
      ***********************/
    typedef enum DMS_ALG_STATE_CODE_E {
        E_STATE_NORMAL = 0,          /* 0 无报警 */
        E_STATE_FATIGUE,             /* 1 疲劳 */
        E_STATE_DISTRACTION,         /* 2 分心 */
        E_STATE_NO_DRIVER,           /* 3 无司机 */
        E_STATE_SMOKE,               /* 4 抽烟 */
        E_STATE_PHONE,               /* 5 打电话 */
        E_STATE_YAWN,                /* 6 打哈欠 */
        E_STATE_NO_MASK,             /* 7 无口罩 */
        E_STATE_SUNGLASSES,          /* 8 红外阻断眼镜 */
        E_STATE_NO_SEATBELT,         /* 9 无安全带 */
        E_STATE_OCCLUSION,           /* 10 摄像头遮挡 */
        E_STATE_FATIGUE_L2,          /* 11 二级疲劳 */
        E_STATE_DRINK,               /* 12 喝东西 */
        E_STATE_NO_HELMET,           /* 13 无安全帽 */
        E_STATE_OUT_CAMERA,          /* 14 摄像头角度偏移*/
        E_STATE_DAZZLE,              /* 15 摄像头炫光 */
        E_STATE_FATIGUE_MILD,        /* 16 DMS31P 疲劳定义：轻度疲劳*/
        E_STATE_FATIGUE_MODERATE,    /* 17 DMS31P 疲劳定义：中度疲劳*/
        E_STATE_FATIGUE_SEVERE,      /* 18 DMS31P 疲劳定义：重度疲劳*/
    } EDmsAlgStateCode;

    struct STDMSFuncSwitch
    {
        bool bNoDriverSW;                        //离岗警报开关
        bool bFatigueSW;                         //疲劳警报开关
        bool bDistractSW;                        //分心报警开关
        bool bSmokeSW;                           //抽烟报警开关
        bool bPhoneSW;                           //打电话报警开关
        bool bYawnSW;                            //打哈欠报警开关
        bool bNoMaskSW;                          //无口罩报警开关
        bool bFatigueL2SW;                       //二级疲劳报警开关
        bool bSunglassesSW;                      //红外阻断眼镜报警开关
        bool bSeatbeltSW;                        //无安全带报警开关
        bool bOcclusionSW;                       //遮挡报警开关
        bool bHelmetSW;                          //无安全帽报警开关
        bool bDrinkSW;                           //喝东西报警开关
        bool bOutCameraSW;                       //摄像头方向偏离报警开关
        bool bDazzleSW;                          //炫光报警开关
        bool bPeopleSW;                          //乘员报警开关
        STDMSFuncSwitch():bNoDriverSW(false),bFatigueSW(false),bDistractSW(false),bSmokeSW(false),bPhoneSW(false),bYawnSW(false),
                          bNoMaskSW(false),bFatigueL2SW(false),bSunglassesSW(false),bSeatbeltSW(false),bOcclusionSW(false),bHelmetSW(false),
                          bDrinkSW(false),bOutCameraSW(false),bDazzleSW(false),bPeopleSW(false){}
    };

    struct STDMSAlgParam
    {
        float fEcrThr;                            //判断闭眼的阈值, 0.0-1.0
        float fFatigueTime;                       //疲劳警报时间, 1-6
        float fDictThrLeft;                       //左边分心偏角阈值, 0-75
        float fDictThrRight;                      //右边分心偏角阈值, 0-75
        float fDictThrUp;                         //上边分心仰角阈值, 20-50
        float fDictThrDown;                       //下边分心俯角阈值, 20-50
        float fDictTime;                          //分心警报时间, 1-9
        float fNoDriverTime;                      //离岗检测警报时间, 1-30
        float fSmThr;                             //抽烟检测阈值, 0.0-1.0
        float fSmTime;                            //抽烟检测警报时间, 1-5
        float fPhoneThr;                          //电话检测阈值, 0.0-1.0
        float fPhoneTime;                         //电话检测警报时间, 1-5
        float fSeatbeltThr;                       //安全带检测阈值, 0.0-1.0
        float fSeatbeltTime;                      //安全带检测警报时间, 1-30
        float fOcclusionThr;                      //遮挡摄像头阈值, 0.0-1.0
        float fOcclusionTime;                     //遮挡摄像头警报时间, 1-30
        float fDazzleThr;                         //摄像头炫光阈值, 0.0-1.0
        float fDazzleTime;                        //摄像头炫光警报时间, 1-30
        float fYawnTime;                          //打哈欠检测警报时间,1-5
        float fNoMaskTime;                        //不带口罩检测警报时间，1-10
        float fSunGlassesTime;                    //红外阻断墨镜检测警报时间，1-10
        float fHelmetThr;                         //安全带检测阈值, 0.0-1.0
        float fHelmetTime;                        //安全带检测警报时间, 1-30
        float fPoseTimeLength;                    //头部姿态统计间距;
        float fFatigueSumTimeLength;              //疲劳统计时长, 30.0-120.0(秒)
        float fClosePercent;                      //闭眼百分比, 10.0-100.0；
        float fDrinkThr;                          //喝水检测阈值，0.0-1.0;
        float fDrinkTime;                         //喝水检测警报时间，1-10;
        float fOutCameraTime;                     //摄像机角度偏离报警时间;
        float fDistractionSumTimeLength;          //分心统计时长, 20.0-60.0(秒)，目前只有DVR客户要求这个功能，原理跟疲劳累计一样
        float fDistractionPercent;                //分心百分比, 10.0-100.0, 目前只有DVR客户要求这个功能，原理跟疲劳累计一样
        float fFatigueMildThr;                    //DMS31P轻度疲劳的阈值
        float fFatigueModerateThr;                //DMS31P中度疲劳的阈值
        float fFatigueSevereThr;                  //DMS31P重度疲劳的阈值
        STDMSAlgParam():fEcrThr(0.45),fFatigueTime(3.0),fDictThrLeft(35),fDictThrRight(35),fDictThrUp(30),fDictThrDown(30),fDictTime(5.0),fNoDriverTime(15.0),
                        fSmThr(0.35),fSmTime(3.0),fPhoneThr(0.6),fPhoneTime(3.0),fSeatbeltThr(0.48),fSeatbeltTime(10.0),fOcclusionThr(0.6),fOcclusionTime(10.0),
                        fDazzleThr(0.6),fDazzleTime(10.0),fYawnTime(2.0),fNoMaskTime(5.0),fSunGlassesTime(5.0),fHelmetThr(0.5),fHelmetTime(10.0),
                        fPoseTimeLength(300.0),fFatigueSumTimeLength(60.0),fClosePercent(20),fDrinkThr(0.5),fDrinkTime(3),fOutCameraTime(3),
                        fDistractionSumTimeLength(20.0),fDistractionPercent(40),fFatigueMildThr(0.3),fFatigueModerateThr(0.5),fFatigueSevereThr(0.7){}
    };

    struct STCalibrateParam
    {
        int32_t ps32CalibrateHeadAngle[2];             //头部标定信息，0，俯仰角，1,水平角
        int32_t ps32CalibrateGazeAngle[2];             //视线标定信息，0，俯仰角，1,水平角
        STCalibrateParam()
        {
            //设定未标定时默认角度-101
            ps32CalibrateHeadAngle[0] = -101;
            ps32CalibrateHeadAngle[1] = -101;
            // memset(ps32CalibrateGazeAngle, 0, sizeof(int32_t) * 2);
            ps32CalibrateGazeAngle[0] = 0;
            ps32CalibrateGazeAngle[1] = 0;
        }
    };

    struct STFaceIdInfo
    {
        int32_t s32Id;
        char* ps8UserName;
        STFaceIdInfo():s32Id(-1),ps8UserName(nullptr){}
    };

    typedef enum
    {
        E_LEFT = 0,                             /*左边驾驶位*/
        E_RIGHT = 1,                            /*右边驾驶位*/
        E_ALL = 2                               /*不区分驾驶位*/
    } EDriverPoseType;

    typedef enum CalibrateState_
    {
        E_DMS_FIRST_CALIBRATING,                 //第一次自动标定过程中，默认状态
        E_DMS_CALIBRATE_FAIL,                    //第一次自动标定失败
        E_DMS_CALIBRATE_SUCCESS,                 //第一次自动标定成功
        E_DMS_CALIBRATED,                        //设备已经标定过
        E_DMS_CALIBRATE_UPDATE                   //最新一次自动标定成功, 且头部姿态变化较大
    }CalibrateState;

    typedef enum CalibrateFailReason_
    {
        E_DMS_CALIBRATE_NONE,                    //标定过程中默认状态，无失败原因
        E_DMS_CALIBRATE_NO_FACE,                 //最新一次自动标定失败，失败原因为检测不到人脸
        E_DMS_CALIBRATE_OCCLUSION,               //最新一次自动标定失败，失败原因为摄像头被遮挡
        E_DMS_CALIBRATE_SEATBELT_INVISIBLE,      //最新一次自动标定失败，失败原因为安全带不可见(人脸框下延与图像下延距离小于图像的六份之一)
        E_DMS_CALIBRATE_DAZZLE                   //最新一次自动标定失败，失败原因为摄像头炫光
    }CalibrateFailReason;

    struct STCalibrateResult
    {
        CalibrateState eCalibrateState;             //最新一次自动标定状态
        CalibrateFailReason eCalibrateFailReason;   //若最新一次自动标定状态失败，的失败原因
        float pfCalibrateHeadPose[3];               //若最近一次自动标定成功的标定角度
        float fCalibrateProgress;                   //第一次自动标定的进度条
        bool fCalibrateProgressPause;               //进度条是否进入等待状态
        bool bCaliSleep;                            //自动标定是否进入等待状态
    };

    struct STDDAWResult
    {
        float pfEcr[2];                             //眼睛闭合度（0～1.0,显示需要乘于100规整），0为左眼，1为右眼
        float pfHeadPose[3];                        //头部姿态，0,俯仰角，1,水平角，2,旋转角
        int32_t s32BlinkPerMin;                     //过去一分钟眨眼次数
        int32_t s32YawnPerMin;                      //过去一分钟打哈欠次数
        float fEcrGrad;                             //眼睛闭合度的变化率
        float fGazeGrad;                            //视线方向的变化率
        float fPoseYawGrad;                         //头部水平角度的变化率
        float fPosePitchGrad;                       //头部俯仰角度的变化率
        int32_t s32DDAWScore;                       //DDAW疲劳打分（0～100）
        bool bOcclusion;                            //是否遮挡
        bool bDazzle;                               //是否炫光
        bool bSunglasses;                           //是否有带红外阻断眼睛
        STDDAWResult():s32YawnPerMin(-1),s32BlinkPerMin(-1),fEcrGrad(0),fGazeGrad(0),fPosePitchGrad(0),fPoseYawGrad(0),s32DDAWScore(0)
                        ,bOcclusion(false),bDazzle(false),bSunglasses(false)
        {
            // memset(pfEcr, -1, sizeof(pfEcr));
            pfEcr[0]=-1;
            pfEcr[1]=-1;
            memset(pfHeadPose, 0, sizeof(pfHeadPose));
        }
    };
    struct STDDAWParam
    {
        float fBlinkScorer;                                    //当因为疲劳出现频繁眨眼增加的分数
        float fYawnScorer;                                     //打哈欠一次增加的分数，时限是过去的15秒内
        float fBlinkThr;                                       //用于判断因为疲劳眨眼次数变化的阈值，也就是给予fBlinkScorer的阈值
        float fGazeThreshold;                                  //疲劳时视线俯仰角相对于正前方的范围， 不开放
        float fFatigueYawRadius;                               //在头部水平角相对于标定水平角的范围内，才会判断是疲劳，例如，标定值是10,该值是25,那就是[-15,35]范围内才会判断为疲劳
        float fFatigueEcrGradThreshold;                        //疲劳闭合度变化率阈值，当闭合度变小时（还没完全闭合），闭合度的变化率较小时，才会认为是疲劳，该值是判断闭合度的变化率的阈值
        float fFatigueYawGradThreshold;                        //疲劳时头部水平角变化率应该是较小的，该值是阈值
        float fFatiguePitchGradThreshold;                      //疲劳时头部俯仰角变化率应该是较小的，该值是阈值
        float fOcclusionThreshold;                             //遮挡检测阈值
        float fDazzleThreshold;                                //炫光检测阈值
        STDDAWParam():fBlinkScorer(10.0f),fYawnScorer(30.0f),fBlinkThr(30.0f),fGazeThreshold(25.0f),fFatigueYawRadius(25.0f),fFatigueEcrGradThreshold(0.25f),
                    fFatigueYawGradThreshold(8.0f),fFatiguePitchGradThreshold(8.0f),fOcclusionThreshold(0.5f),fDazzleThreshold(0.5f){}
    };

    struct STEventScore
    {
        float fFatigueEventScore;                   //疲劳事件置信度
        float fFatigueCumulativeEventScore;         //累计疲劳事件置信度
        float fDistEventScore;                      //分心事件置信度
        float fDistCumulativeEventScore;            //累计分心事件置信度
        float fPhoneEventScore;                     //打电话事件置信度
        float fSmokeEventScore;                     //抽烟事件置信度
        float fDrinkEventScore;                     //喝东西事件置信度
        float fYawnEventScore;                      //打哈欠事件置信度
        float fOcclusionEventScore;                 //遮挡事件置信度
        float fDazzleEventScore;                    //眩光事件置信度
        float fNoMaskEventScore;                    //不带口罩事件置信度
        float fInfraredBlockGlassEventScore;        //红外阻断眼镜事件置信度
        float fNoDriverEventScore;                  //离岗事件置信度
        float fSeatbeltEventScore;                  //不带安全带事件置信度
        float fHelmetEventScore;                    //不带安全帽事件置信度
        float fCameraOutScore;                      //人脸框超出图像边界
        STEventScore():fFatigueEventScore(0),fFatigueCumulativeEventScore(0),fDistEventScore(0),fDistCumulativeEventScore(0),fPhoneEventScore(0),
                       fSmokeEventScore(0),fDrinkEventScore(0),fYawnEventScore(0),fOcclusionEventScore(0),fDazzleEventScore(0),fNoMaskEventScore(0),
                       fInfraredBlockGlassEventScore(0),fNoDriverEventScore(0),fSeatbeltEventScore(0),fHelmetEventScore(0),fCameraOutScore(0){}
    };


    struct STDMSResult
    {
        EDmsAlgStateCode eDmsAlgStateCode;         //当前分析状态
        float fAbsEcr[2];                          //眼睛绝对闭合度（0.0～1.0），0为左眼，1为右眼
        STEventScore stEventScore;                 //各个报警事件的置信度
        STDMSResult():eDmsAlgStateCode(E_STATE_NORMAL)
        {
            // memset(fAbsEcr, -1, sizeof(fAbsEcr));
            fAbsEcr[0]=-1;
            fAbsEcr[1]=-1;
        }
    };



    /****************************************
     * 错误码返回值
     ****************************************/
    typedef enum ALG_PROCESS_CODE_E {
        /********************************NPU的错误码***********************************************/
        E_ALG_SUCCESS                                     = 0,    //  成功
        E_ALG_ERR_FAIL                                    = -1,   //  执行失败
        E_ALG_ERR_TIMEOUT                                 = -2,   //  线性库优化失败
        E_ALG_ERR_DEVICE_UNAVAILABLE                      = -3,   //  设备不可访问
        E_ALG_ERR_MALLOC_FAIL                             = -4,   //  内存创建失败
        E_ALG_ERR_PARAM_INVALID                           = -5,   //  参数无效
        E_ALG_ERR_MODEL_INVALI                            = -6,   //  模型无效
        E_ALG_ERR_CTX_INVALID                             = -7,   //  context无效
        E_ALG_ERR_INPUT_INVALID                           = -8,   //  输入无效
        E_ALG_ERR_OUTPUT_INVALID                          = -9,   //  输出无效
        E_ALG_ERR_DEVICE_UNMATCH                          = -10,  //  设备不匹配, 需要更新RKNN的sdk和npu的驱动和固件
        E_ALG_ERR_INCOMPATILE_PRE_COMPILE_MODEL           = -11,  //  加载的RKNN模型是预编译的, 当前驱动不支持这种模式
        E_ALG_ERR_INCOMPATILE_OPTIMIZATION_LEVEL_VERSION  = -12,  //  加载的RKNN模型设置了优化级别, 当前驱动不支持这种模式
        E_ALG_ERR_TARGET_PLATFORM_UNMATCH                 = -13,  //  加载的RKNN模型和当前运行平台不符合
        E_ALG_ERR_NON_PRE_COMPILED_MODEL_ON_MINI_DRIVE    = -14,  //  加载的RKNN模型不是预编译的, 当前用的mini driver跑不了
        E_ALG_INVALID                                     = -15,  //  没有加载相关模型结果无效
        /********************************NPU的错误码***********************************************/


        /********************************本算法库的错误码***********************************************/
        E_ALG_ERR_RE_INIT                                 = -20,  //  请不要重复初始化
        E_ALG_ERR_MODEL_NULL                              = -21,  //  初始化时, 模型文件的指针为空, 请正确设置参数
        E_ALG_ERR_LOAD_MODEL                              = -22,  //  初始化时, 加载模型文件失败
        E_ALG_ERR_MUST_INIT                               = -23,  //  需要先进行初始化
        E_ALG_ERR_TYPE_UNMATCH                            = -24,  //  加载的模型与设置的算法类型不符合, 例如设置的是只检人的模型, 却加载了检人检车的模型
        E_ALG_ERR_TYPE_UNSUPPORT                          = -25,  //  不支持的算法模型
        E_ALG_ERR_ALG_VERSION                             = -26,  //  算法版本问题, 不知道算法库对应的是YOLOv5还是YOLOx, 需要联系开发者进行联调,确认,Debug
        E_ALG_ERR_DMA_BUFFER                              = -27,  //  申请DMAbuffer错误
        E_ALG_ERR_ZERO_COPY_NO_FD                         = -28,  //  零拷贝模式(加载模型后识别到是零拷贝的模型), 但是没有DMAbuffer的文件描述符, 无法获取硬件地址和使用零拷贝, 要么换个不是零拷贝的模型
        E_ALG_ERR_CALLBACK_NULL_PTR                       = -29,  //  回调函数为空
        E_ALG_ERR_AUTHENTICATE                            = -30,  //  算法认证失败
        E_ALG_ERR_FILE_NOT_EXIST                          = -31,   //  模型文件不存在
        E_ALG_ERR_ALG_INIT_EYE                            = -32,   //  检眼睛状态的模型初始化失败
        E_ALG_ERR_ALG_INIT_FA                             = -33,   //  人脸对齐的模型初始化失败
        E_ALG_ERR_ALG_INIT_FD                             = -34,   //  人脸检测的模型初始化失败
        E_ALG_ERR_ALG_INIT_HELMET                         = -35,   //  检安全帽的模型初始化失败
        E_ALG_ERR_ALG_INIT_MASK                           = -36,   //  检口罩的模型初始化失败
        E_ALG_ERR_ALG_INIT_CAMERAANOMALY                  = -37,    //  摄像头异常检测的模型初始化失败
        E_ALG_ERR_ALG_INIT_PHONE                          = -38,    //  打电话的模型初始化失败
        E_ALG_ERR_ALG_INIT_SEATBELT                       = -39,    //  安全带的模型初始化失败
        E_ALG_ERR_ALG_INIT_SMOKE                          = -40,    //  检测抽烟的模型初始化失败
        E_ALG_ERR_ALG_INIT_SMOKET1                        = -41,    //  检测抽烟手势的模型初始化失败
        E_ALG_ERR_ALG_INIT_SUNGLASSES                     = -42,    //  检测红外阻断眼镜的模型初始化失败
        E_ALG_ERR_ALG_INIT_FR                             = -43,    //  检测红外阻断眼镜的模型初始化失败
        E_ALG_ERR_ALG_NEED_MODEL                          = -44,    //  DMS必须加载的模型
        E_ALG_FA_INVALD                                   = -45,
        E_ALG_ERR_ALG_INIT_People                         = -46,    //  检测乘员的模型初始化失败
        /********************************本算法库的错误码***********************************************/

    }EAlgProcessCode;
}

#endif
