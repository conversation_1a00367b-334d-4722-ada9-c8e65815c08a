Index: include/config_factory.h
===================================================================
--- include/config_factory.h	(revision 5496)
+++ include/config_factory.h	(working copy)
@@ -524,6 +524,10 @@
     {
         enRoiGui = CFG_PDROI_GUI_HIDE;
     }
+    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        enRoiGui = CFG_PDROI_GUI_HIDE;
+    }
 #if (defined(BOARD_ADA32IR))
     else if(BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
     {
@@ -577,6 +581,11 @@
         enRoiStyle = CFG_PDROI_DRAWBOARD;
     }
 
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        enRoiStyle = CFG_PDROI_DRAWBOARD;
+    }
+
 #if (defined(BOARD_ADA32IR))
     enRoiStyle = CFG_PDROI_SEMICIRCLE;
 #elif (defined(BOARD_ADA46V1))
@@ -713,6 +722,10 @@
     {
         bPdRoiYellow = SV_FALSE;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        bPdRoiYellow = SV_FALSE;
+    }
 
     return bPdRoiYellow;
 }
@@ -747,6 +760,10 @@
     {
         bPdRoiGreen = SV_FALSE;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        bPdRoiGreen = SV_FALSE;
+    }
 
     return bPdRoiGreen;
 }
@@ -775,6 +792,10 @@
     {
         bPdAlarmOutYellow = SV_FALSE;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        bPdAlarmOutYellow = SV_FALSE;
+    }
 
     return bPdAlarmOutYellow;
 }
@@ -804,6 +825,10 @@
     {
         bPdAlarmOutGreen = SV_FALSE;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        bPdAlarmOutGreen = SV_FALSE;
+    }
 
     return bPdAlarmOutGreen;
 }
@@ -823,6 +848,10 @@
     {
         s32RedWireModelMask = 7;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        s32RedWireModelMask = 1;
+    }
 
     return s32RedWireModelMask;
 }
@@ -842,6 +871,10 @@
     {
         s32YellowWireModelMask = 7;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        s32YellowWireModelMask = 1;
+    }
 
     return s32YellowWireModelMask;
 }
@@ -865,6 +898,10 @@
     {
         s32GreenWireModelMask = 7;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        s32GreenWireModelMask = 1;
+    }
 
     return s32GreenWireModelMask;
 }
@@ -1776,6 +1813,38 @@
         stPdRoiRedPoints[9].dX = 0.51;
         stPdRoiRedPoints[9].dY = 0.39;
     }
+    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        stPdRoiRedPoints[0].dX = 0.2;
+        stPdRoiRedPoints[0].dY = 0.6;
+
+        stPdRoiRedPoints[1].dX = 0.12;
+        stPdRoiRedPoints[1].dY = 0.77;
+
+        stPdRoiRedPoints[2].dX = 0.02;
+        stPdRoiRedPoints[2].dY = 1;
+
+        stPdRoiRedPoints[3].dX = 0.41;
+        stPdRoiRedPoints[3].dY = 1;
+
+        stPdRoiRedPoints[4].dX = 0.68;
+        stPdRoiRedPoints[4].dY = 1;
+
+        stPdRoiRedPoints[5].dX = 1;
+        stPdRoiRedPoints[5].dY = 0.99;
+
+        stPdRoiRedPoints[6].dX = 0.87;
+        stPdRoiRedPoints[6].dY = 0.75;
+
+        stPdRoiRedPoints[7].dX = 0.8;
+        stPdRoiRedPoints[7].dY = 0.6;
+
+        stPdRoiRedPoints[8].dX = 0.6;
+        stPdRoiRedPoints[8].dY = 0.6;
+
+        stPdRoiRedPoints[9].dX = 0.4;
+        stPdRoiRedPoints[9].dY = 0.6;
+    }
     else
     {
         stPdRoiRedPoints[0].dX = 0.2;
@@ -2255,10 +2324,6 @@
     {
         enPdsModel = E_PDS_MANHOLE;
     }
-    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
-    {
-        enPdsModel = E_PDS_SH;
-    }
     else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202406))
     {
         enPdsModel = E_PDS_PC;
@@ -3590,6 +3655,10 @@
     {
         enLang = LANG_FI;
     }
+    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
+    {
+        enLang = LANG_CN;
+    }
     return enLang;
 }
 
Index: src/common/board/board.c
===================================================================
--- src/common/board/board.c	(revision 5496)
+++ src/common/board/board.c	(working copy)
@@ -1834,8 +1834,14 @@
     }
     else
     {
-        BOARD_RK_GetGPIO(1, 11, &u8Value); // 带灯硬件增加下拉电阻
-        m_enBoard = u8Value == 1 ? BOARD_ADA32V2_V1 : BOARD_ADA32V2_V2;
+        if (BOARD_IsADA32IRCUT())
+        {
+            m_enBoard = BOARD_ADA32V2_V2;
+        }
+        else
+        {
+            m_enBoard = BOARD_ADA32V2_V1;
+        }
     }
 
     // A32P专用版本识别
Index: src/webui/js/webapp-view.js
===================================================================
--- src/webui/js/webapp-view.js	(revision 5496)
+++ src/webui/js/webapp-view.js	(working copy)
@@ -3366,7 +3366,11 @@
     roiBoardReset:function(){
         if (this.calibration_drawboard == null)
             return ;
-        
+		
+        if (this.customer == "100394")
+		    {
+            $('#roi-board-hide-other').prop("checked",true);			
+		    }
         var $select = $('#roi-board-color-select');
         var $range = $('#roi-board-color-scale');
         var $checked = $('#roi-board-hide-other');
