/******************************************************************************
Copyright (C) 2021-2023 广州敏视数码科技有限公司版权所有.

文件名：gpio.c

作者: lyn    版本: v1.0.0(初始版本号)   日期: 2021-11-26

文件功能描述: 定义gpio功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

说明：gpio模块包含了控制输出和监测输入两部分功能
      如果需要添加步骤如下：（1）在gpio.h GPIO_PIN_E结构体添加对应引脚编号，
      注意最多引脚数量定义了20个，（2）在gpio.c文件添加该引脚实际编号
      （3）在gpio_pinInit内关联引脚（4）在gpio_getAllModStaus函数添加获取状态
      （5）在gpio_SetAllModPinStatus函数设置要执行的操作

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <pthread.h>
#include <errno.h>
#include <sys/sem.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/time.h>
#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <fnmatch.h>
#include <signal.h>

#include "common.h"
#include "print.h"
#include "board.h"
#include "safefunc.h"
#include "gpio.h"
#include "cJSON.h"

#if defined(BOARD_ADA47V1)

/* 引脚列表 */
#define        SWITCH1_IN_PIN             68        /* GPIO2_A4 */
#define        SWITCH2_IN_PIN             69        /* GPIO2_A5 */
#define        SWITCH3_OUT_PIN            70        /* GPIO2_A6 */
#define        SWITCH4_OUT_PIN            71        /* GPIO2_A7 */

/* 文件列表 */
#define        GPIO_EXPORT_PATH           "/sys/class/gpio/export"
#define        GPIO_FILE_PATH             "/sys/class/gpio"

#else

#define        SYS_LED_PIN                (-1)      /* default 表示不使用该引脚功能 */
#define        WIFI_LED_PIN               (-1)      /* default */
#define        CELL_LED_PIN               (-1)      /* default */
#define        GPS_LED_PIN                (-1)      /* default */
#define        RADAR_LED_PIN              (-1)      /* default */

#define        TRI_L_DET_PIN              (-1)      /* default */
#define        TRI_R_DET_PIN              (-1)      /* default */
#define        ACC_DET_PIN                (-1)      /* default */
#define        TURN_L_DET_PIN             (-1)      /* default */
#define        TRUN_R_DET_PIN             (-1)      /* default */

#define        ALARM_PERSON_PIN           (-1)      /* default */

#define        GPIO_EXPORT_PATH           " "
#define        GPIO_FILE_PATH             " "

#endif

#define        GPIO_HIGH                  1
#define        GPIO_LOW                   0

typedef enum tagGPIODirection_E
{
    GPIO_UNKNOWN = 0x00,                           /* 未定义 */
    GPIO_INPUT,                                    /* 输入 */
    GPIO_OUTPUT,                                   /* 输出 */
    
    GPIO_DIR_BUTT
} GPIO_DIR_E;

typedef struct tagGPIOPinInfo_S
{
    SV_BOOL       bValid;                          /* 有效性 */
    SV_BOOL       bUpdateOPS;                      /* 是否需要更新操作 */
    sint32        s32PinNum;                       /* 引脚编号 */
    sint32        s32DirectionFd;                  /* 输入输出方向文件描述符 */
    sint32        s32ValueFd;                      /* 值文件描述符 */
    GPIO_OPS_E    eGPIOOps;                        /* gpio操作 */
    GPIO_PIN_E    ePinIndex;                       /* 引脚索引编号 */
    GPIO_DIR_E    eDirection;                      /* 引脚方向 */
    uint32        u32Value;                        /* 引脚电平值 */
} GPIO_PIN_INFO_S;

typedef struct tagGPIOInfo_S
{
    SV_BOOL             bEnable;                   /* 使能模块 */
    SV_BOOL             bRunning;                  /* 运行状态 */
    GPIO_PIN_INFO_S     astGPIOPin[GPIO_PIN_NUM];  /* GPIO引脚信息列表 */
    MODULE_STA_S        stModStatus;               /* 各个模块信息 */
    sint32              s32UpdateTid;              /* gpio更新线程ID */
    sint32              s32CheckTid;               /* gpio获取状态线程ID */
} GPIO_INFO_S;

GPIO_INFO_S m_stGPIOInfo = {0};


/******************************************************************************
 * 函数功能: 获取gpio引脚电平
 * 输入参数: stGPIOPin -- 引脚配置信息
 * 输出参数: pu32Value -- 引脚电平 1:高电平0；低电平
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetPinVal(GPIO_PIN_INFO_S stGPIOPin, uint32* pu32Value)
{
    sint32 s32Ret = -1;
    uint8 u8Value = 0;
    if((SV_TRUE != stGPIOPin.bValid)|| stGPIOPin.s32ValueFd <= 0)
    {
        return SV_FAILURE;
    }
    
    lseek(stGPIOPin.s32ValueFd, 0, SEEK_SET);
    
    s32Ret = read(stGPIOPin.s32ValueFd, &u8Value, 1);
    if(s32Ret < 1)
    {   
        print_level(SV_ERROR, "gpio read file failed. [err: %s]\n",strerror(errno));
        return SV_FAILURE;
    }

    if(u8Value == '1')
    {
        *pu32Value = 1;
    }
    else
    {
        *pu32Value = 0;  
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置GPIO引脚IO输出电平
 * 输入参数: stGPIOPin -- 引脚配置信息
              u32Value -- 设置电平值 1：高电平 0：低电平
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_SetPinVal(GPIO_PIN_INFO_S stGPIOPin, uint32 u32Value)
{
    sint32 s32Ret = -1;
    char *pData = NULL;
    if((SV_TRUE != stGPIOPin.bValid)|| stGPIOPin.s32ValueFd <= 0)
    {
        return SV_FAILURE;
    }

    if(1 == u32Value)
    {
        pData = "1";
    }
    else
    {
        pData = "0";
    }
    s32Ret = write(stGPIOPin.s32ValueFd, pData, 3);
    if(s32Ret < 0)
    {
        print_level(SV_ERROR, "write pin value error \n");
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置GPIO引脚IO口输出翻转
 * 输入参数: stGPIOPin -- 引脚配置信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_TogglePin(GPIO_PIN_INFO_S stGPIOPin)
{
    sint32 s32Ret = -1;
    uint8 u8Value = 0;
    char *pData = NULL;
    if((SV_TRUE != stGPIOPin.bValid)|| stGPIOPin.s32ValueFd <= 0)
    {
        return SV_FAILURE;
    }
    
    lseek(stGPIOPin.s32ValueFd, 0, SEEK_SET);
    
    s32Ret = read(stGPIOPin.s32ValueFd, &u8Value, 1);
    if(s32Ret < 1)
    {   
        print_level(SV_ERROR, "gpio read file failed. [err: %s]\n",strerror(errno));
        return SV_FAILURE;
    }

    if(u8Value == '1')
    {
        pData = "0";
    }
    else
    {
        pData = "1";  
    }
    
    s32Ret = write(stGPIOPin.s32ValueFd, pData, 3);
    if(s32Ret < 0)
    {
        print_level(SV_ERROR, "write pin value error \n");
        return SV_FAILURE;
    }    

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置GPIO引脚IO口参数
 * 输入参数: s32Pin -- 引脚编号
              s32Direction -- 方向 1：输出 0：输入
              ePinIndex -- 引脚索引
 * 输出参数: stGPIOPin -- 引脚输出配置
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_PinConfig(GPIO_PIN_INFO_S *pstGPIOPin, GPIO_PIN_E ePinIndex,  sint32 s32Pin, GPIO_DIR_E eDirection)
{
    sint32 s32Ret = -1;
    char szCmd[128] = {0};
    char szStr[8] = {0};
    sint32 s32ExportFd = -1, s32DirectionFd = -1, s32ValueFd = -1;
    
    sprintf(szStr, "%d", s32Pin);
    sprintf(szCmd, "%s/gpio%d",GPIO_FILE_PATH, s32Pin);
    
    //print_level(SV_DEBUG, "szStr %s, szCmd %s\n",szStr, szCmd);
    if(access(szCmd, F_OK) != 0)
    {

        s32ExportFd = open(GPIO_EXPORT_PATH, O_WRONLY);
        if(-1 == s32ExportFd)
        {
            print_level(SV_ERROR, "open gpio export file error\n");
            return SV_FAILURE;
        }
        
        s32Ret    = write(s32ExportFd, szStr, strlen(szStr));
        if(s32Ret < 0)
        {
            print_level(SV_ERROR, "write export error %d \n",s32Ret);
            close(s32ExportFd);
            return SV_FAILURE;
        }
        close(s32ExportFd);
    }
    
    memset(szCmd, 0, sizeof(szCmd));
    sprintf(szCmd, "%s/gpio%d/direction",GPIO_FILE_PATH, s32Pin);
    s32DirectionFd = open(szCmd, O_RDWR);
    if(s32DirectionFd < 0)
    {
        print_level(SV_ERROR, "open gpio direction file error\n");
        return SV_FAILURE;            
    }

    if(GPIO_OUTPUT == eDirection)
    {
        s32Ret = write(s32DirectionFd, "out" , sizeof("out"));
    }
    else
    {
        s32Ret = write(s32DirectionFd, "in" , sizeof("in"));
    }

    if(s32Ret < 0)
    {
        print_level(SV_ERROR, "write direction error\n");
        close(s32DirectionFd);
        return SV_FAILURE;
    }
    
    memset(szCmd, 0, sizeof(szCmd));
    sprintf(szCmd, "%s/gpio%d/value",GPIO_FILE_PATH, s32Pin);
    s32ValueFd = open(szCmd, O_RDWR);
    if(s32ValueFd < 0)
    {
        print_level(SV_ERROR, "open gpio value file error\n");
        close(s32DirectionFd);
        return SV_FAILURE;            
    }
    
    if(GPIO_OUTPUT == eDirection)
    {
        pstGPIOPin->eDirection = GPIO_OUTPUT;
    }
    else
    {
        pstGPIOPin->eDirection = GPIO_INPUT;
    }

    pstGPIOPin->bValid = SV_TRUE;
    pstGPIOPin->s32PinNum = s32Pin;
    pstGPIOPin->s32DirectionFd = s32DirectionFd;
    pstGPIOPin->s32ValueFd = s32ValueFd;
    pstGPIOPin->ePinIndex = ePinIndex;
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化GPIO引脚
 * 输入参数: 无
 * 输出参数: pstGPIOInfo -- 模块参数
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_PinInit(GPIO_INFO_S *pstGPIOInfo)
{
    sint32 s32Ret = -1;
    sint32 s32PinNum = -1;

    GPIO_PIN_INFO_S astGPIOPin[GPIO_PIN_NUM] = {0};
    
#if defined(BOARD_ADA47V1)
    /* Switch1/In引脚初始化 */
    if(-1 != (s32PinNum = SWITCH1_IN_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[SWITCH_1_IN]), SWITCH_1_IN, SWITCH1_IN_PIN, GPIO_INPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }
    
    /* Switch2/In引脚初始化 */
    if(-1 != (s32PinNum = SWITCH2_IN_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[SWITCH_2_IN]), SWITCH_2_IN, SWITCH2_IN_PIN, GPIO_INPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }
    
    /* Switch3/Out引脚初始化 */
    if(-1 != (s32PinNum = SWITCH3_OUT_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[SWITCH_3_OUT]), SWITCH_3_OUT, SWITCH3_OUT_PIN, GPIO_OUTPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }
    
    /* Switch4/Out引脚初始化 */
    if(-1 != (s32PinNum = SWITCH4_OUT_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[SWITCH_4_OUT]), SWITCH_4_OUT, SWITCH4_OUT_PIN, GPIO_OUTPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }

#else
    /* LED灯引脚初始化 */
    if(-1 != (s32PinNum = SYS_LED_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[LED_SYS]), LED_SYS, SYS_LED_PIN, GPIO_OUTPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }

    if(-1 != (s32PinNum = WIFI_LED_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[LED_WIFI]), LED_WIFI, WIFI_LED_PIN, GPIO_OUTPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }

    if(-1 != (s32PinNum = CELL_LED_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[LED_4G]), LED_4G, CELL_LED_PIN, GPIO_OUTPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }

    if(-1 != (s32PinNum = GPS_LED_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[LED_GPS]), LED_GPS, GPS_LED_PIN, GPIO_OUTPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }

    if(-1 != (s32PinNum = STORAGE_LED_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[LED_STORAGE]), LED_STORAGE, STORAGE_LED_PIN, GPIO_OUTPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }
    
    /* 输入引脚电平监测 */
    if(-1 != (s32PinNum = ACC_DET_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[STA_ACC]), STA_ACC, ACC_DET_PIN, GPIO_INPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }

    if(-1 != (s32PinNum = TURN_L_DET_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[STA_TURN_LEFT]), STA_TURN_LEFT, TURN_L_DET_PIN, GPIO_INPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }

    if(-1 != (s32PinNum = TURN_R_DET_PIN))
    {
        s32Ret = gpio_PinConfig(&(astGPIOPin[STA_TURN_RIGHT]), STA_TURN_RIGHT, TURN_R_DET_PIN, GPIO_INPUT);
        if(SV_FAILURE == s32Ret)
        {
            return SV_FAILURE;
        }
    }
#endif

    memcpy(pstGPIOInfo->astGPIOPin, astGPIOPin, sizeof(GPIO_PIN_INFO_S)*GPIO_PIN_NUM);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化GPIO引脚
 * 输入参数: 无
 * 输出参数: pstGPIOInfo -- 模块参数
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_PinFini(GPIO_INFO_S *pstGPIOInfo)
{
    uint32 i = 0;
    
    for(i=0; i<GPIO_PIN_NUM; i++)
    {
        if(SV_TRUE == pstGPIOInfo->astGPIOPin[i].bValid)
        {
            close(pstGPIOInfo->astGPIOPin[i].s32DirectionFd);
            close(pstGPIOInfo->astGPIOPin[i].s32ValueFd);
            pstGPIOInfo->astGPIOPin[i].bValid = SV_FALSE;
            pstGPIOInfo->astGPIOPin[i].s32PinNum = -1;
        }
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置GPIO状态
 * 输入参数: eGPIOPin -- 引脚 eGPIOOPS -- 操作
 * 输出参数: stGPIOPin -- 引脚输出配置
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_SetPinStatus(GPIO_INFO_S *pstGPIOInfo, GPIO_PIN_E eGPIOPin, GPIO_OPS_E eGPIOOPS)
{
    if(SV_TRUE != pstGPIOInfo->astGPIOPin[eGPIOPin].bValid)
    {
        return SV_FAILURE;
    }
    
    if(pstGPIOInfo->astGPIOPin[eGPIOPin].eGPIOOps != eGPIOOPS)
    {
        pstGPIOInfo->astGPIOPin[eGPIOPin].eGPIOOps = eGPIOOPS;
        pstGPIOInfo->astGPIOPin[eGPIOPin].bUpdateOPS = SV_TRUE;
    }
    
    return SV_SUCCESS;
}

#if defined(BOARD_ADA47V1)
/******************************************************************************
 * 函数功能: 获取Switch3/Out状态
 * 输入参数: 无
 * 输出参数: pstGPIOInfo -- GPIO模块信息
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetSwitch3Status(GPIO_INFO_S *pstGPIOInfo)
{
    if (1 == pstGPIOInfo->astGPIOPin[SWITCH_1_IN].u32Value)
    {
        pstGPIOInfo->stModStatus.eSwitch3OutStatus = GPIO_OUTPUT_LOW;
    }
    else
    {
        pstGPIOInfo->stModStatus.eSwitch3OutStatus = GPIO_OUTPUT_HIGH;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取Switch4/Out状态
 * 输入参数: 无
 * 输出参数: pstGPIOInfo -- GPIO模块信息
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetSwitch4Status(GPIO_INFO_S *pstGPIOInfo)
{
    if (1 == pstGPIOInfo->astGPIOPin[SWITCH_2_IN].u32Value)
    {
        pstGPIOInfo->stModStatus.eSwitch4OutStatus = GPIO_OUTPUT_LOW;
    }
    else
    {
        pstGPIOInfo->stModStatus.eSwitch4OutStatus = GPIO_OUTPUT_HIGH;
    }

    return SV_SUCCESS;
}

#else
/******************************************************************************
 * 函数功能: 获取系统状态
 * 输入参数: 无
 * 输出参数: pstModStatus -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetSysStatus(MODULE_STA_S *pstModStatus)
{
    pstModStatus->eSysStatus = LED_SYS_ON;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取SD卡块设备状态
 * 输入参数: 无
 * 输出参数: pstModStatus -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetWIFIStatus(MODULE_STA_S *pstModStatus)
{
    sint32 s32Ret = -1, s32Fd = -1;
    char szBuf[4096] = {0};
    cJSON *pststaConStat;
    if(access(DUMP_INFO_WIFI, F_OK) != 0)
    {
        pstModStatus->eWIFIStatus = LED_WIFI_PROBID;
        return SV_FAILURE;
    }
    s32Fd = open(DUMP_INFO_WIFI,O_RDONLY);
    if(s32Fd < 0)
    {
        pstModStatus->eWIFIStatus = LED_WIFI_PROBID;
        return SV_FAILURE;
    }
    s32Ret = read(s32Fd, szBuf, sizeof(szBuf));
    if(s32Ret < 0)
    {
        goto error_exit;
    }
    cJSON * pstJson = cJSON_Parse(szBuf);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        goto error_exit;
    }
    pststaConStat = cJSON_GetObjectItemCaseSensitive(pstJson, "staConnStat");
    if (NULL == pststaConStat )
    {
        goto error_exit;
    }

    switch(pststaConStat->valueint)
    {
        case WIFI_CONN_DISCONNECTED:
            pstModStatus->eWIFIStatus = LED_WIFI_DISCON;
            break;
        case WIFI_CONN_CONNECTING:
            pstModStatus->eWIFIStatus = LED_WIFI_CONNECTING;
            break;
        case WIFI_CONN_CONNECTED:
            pstModStatus->eWIFIStatus = LED_WIFI_CONNECTED;
            break;
        default:
            pstModStatus->eWIFIStatus = LED_WIFI_PROBID;
            break;
    }

    close(s32Fd);
    return SV_SUCCESS;
error_exit:
    pstModStatus->eWIFIStatus = LED_WIFI_PROBID;
    close(s32Fd);
    return SV_FAILURE;    
}
/******************************************************************************
 * 函数功能: 获取SD卡块设备状态
 * 输入参数: 无
 * 输出参数: pstModStatus -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetStorageStatus(MODULE_STA_S *pstModStatus)
{
    sint32 s32Ret = -1, s32Fd = -1;
    char szBuf[4096] = {0};
    cJSON *pstEnable, *pstRec;
    if(access(DUMP_INFO_STORAGE, F_OK) != 0)
    {
        pstModStatus->eStorageStatus = LED_STOR_PROBID;
        return SV_FAILURE;
    }
    s32Fd = open(DUMP_INFO_STORAGE,O_RDONLY);
    if(s32Fd < 0)
    {
        pstModStatus->eStorageStatus = LED_STOR_PROBID;
        return SV_FAILURE;
    }
    s32Ret = read(s32Fd, szBuf, sizeof(szBuf));
    if(s32Ret < 0)
    {
        goto error_exit;
    }
    cJSON * pstJson = cJSON_Parse(szBuf);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        goto error_exit;
    }
    pstEnable = cJSON_GetObjectItemCaseSensitive(pstJson, "enable");
    if (NULL == pstEnable )
    {
        goto error_exit;
    }
    
    if(1 != pstEnable->valueint)
    {
        pstModStatus->eStorageStatus = LED_STOR_PROBID;
        goto exit;
    }
    
    pstRec = cJSON_GetObjectItemCaseSensitive(pstJson, "recStat");
    if (NULL == pstRec )
    {
        pstModStatus->eStorageStatus = LED_STOR_NORMAL;
        goto exit;
    }
    
    if(0 == strcmp(pstRec->valuestring, "Recording"))
    {
        pstModStatus->eStorageStatus = LED_STOR_RECORD;
    }
exit:
    close(s32Fd);
    return SV_SUCCESS;
error_exit:
    pstModStatus->eStorageStatus = LED_STOR_PROBID;
    close(s32Fd);
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取4G设备状态
 * 输入参数: 无
 * 输出参数: pstModStatus -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetCellStatus(MODULE_STA_S *pstModStatus)
{
    sint32 s32Ret = -1, s32Fd = -1;
    char szBuf[4096] = {0};
    cJSON *pstCellModStat;
    if(access(DUMP_INFO_CELLULAR, F_OK) != 0)
    {
        pstModStatus->eCellStatus = LED_CELL_PROBID;
        return SV_FAILURE;
    }
    s32Fd = open(DUMP_INFO_CELLULAR,O_RDONLY);
    if(s32Fd < 0)
    {
        pstModStatus->eCellStatus = LED_CELL_PROBID;
        return SV_FAILURE;
    }
    s32Ret = read(s32Fd, szBuf, sizeof(szBuf));
    if(s32Ret < 0)
    {
        goto error_exit;
    }
    cJSON * pstJson = cJSON_Parse(szBuf);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        goto error_exit;
    }
    pstCellModStat = cJSON_GetObjectItemCaseSensitive(pstJson, "ModuleStat");
    if (NULL == pstCellModStat )
    {
        goto error_exit;
    }

    switch(pstCellModStat->valueint)
    {
        case CELL_STAT_INIT:
        case CELL_STAT_CONFIG:
            pstModStatus->eCellStatus = LED_CELL_DISCON;
            break;
        case CELL_STAT_NORMAL:
            pstModStatus->eCellStatus = LED_CELL_CONNECTED;
            break;
        case CELL_STAT_SIM_ERR:
            pstModStatus->eCellStatus = LED_CELL_PROBID;
            break;
        case CELL_STAT_CONNECT:
            pstModStatus->eCellStatus = LED_CELL_CONNECTING;
            break;
        default:
            pstModStatus->eCellStatus = LED_CELL_PROBID;
            break;
    }
    close(s32Fd);
    return SV_SUCCESS;
error_exit:
    pstModStatus->eCellStatus = LED_CELL_PROBID;
    close(s32Fd);
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取GPS设备状态
 * 输入参数: 无
 * 输出参数: pstModStatus -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetGPSStatus(MODULE_STA_S *pstModStatus)
{
    sint32 s32Ret = -1, s32Fd = -1;
    char szBuf[4096] = {0};
    cJSON *pstGPSModStat = NULL;
    cJSON *pstGPSHadData = NULL;
    if(access(DUMP_INFO_GPS, F_OK) != 0)
    {
        pstModStatus->eGPSStatus = LED_GPS_PROBID;
        return SV_FAILURE;
    }
    s32Fd = open(DUMP_INFO_GPS,O_RDONLY);
    if(s32Fd < 0)
    {
        pstModStatus->eGPSStatus = LED_GPS_PROBID;
        return SV_FAILURE;
    }
    s32Ret = read(s32Fd, szBuf, sizeof(szBuf));
    if(s32Ret < 0)
    {
        goto error_exit;
    }
    cJSON * pstJson = cJSON_Parse(szBuf);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        goto error_exit;
    }
	
    pstGPSModStat = cJSON_GetObjectItemCaseSensitive(pstJson, "Status");
    if (NULL == pstGPSModStat)
    {
        goto error_exit;
    }

	pstGPSHadData = cJSON_GetObjectItemCaseSensitive(pstJson, "HadData");
    if (NULL == pstGPSHadData)
    {
        goto error_exit;
    }

	if (0 == pstGPSHadData->valueint)
	{
		pstModStatus->eGPSStatus = LED_GPS_DISCON;
	}
	else if (0 == pstGPSModStat->valueint)
	{
		pstModStatus->eGPSStatus = LED_GPS_CONNECTING;
	}
	else if (3 == pstGPSModStat->valueint)
	{
		pstModStatus->eGPSStatus = LED_GPS_CONNECTED;
	}
	else
	{
    	pstModStatus->eGPSStatus = LED_GPS_PROBID;
	}

    close(s32Fd);
    return SV_SUCCESS;
	
error_exit:
    pstModStatus->eGPSStatus = LED_GPS_PROBID;
    close(s32Fd);
    return SV_FAILURE;
}

#endif

/******************************************************************************
 * 函数功能: 普通输入引脚状态
 * 输入参数: 无
 * 输出参数: pstModStatus -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetAllInputStatus(GPIO_INFO_S *pstGPIOInfo)
{
    uint32 i = 0;
    for(i=0; i<GPIO_PIN_NUM; i++)
    {
        if(GPIO_INPUT == pstGPIOInfo->astGPIOPin[i].eDirection)
        {
            pstGPIOInfo->stModStatus.eGPIOInStatus[i] = GPIO_INPUT_ON;
        }
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取所有模块状态
 * 输入参数: 无
 * 输出参数: pstModStatus -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_GetAllModStatus(GPIO_INFO_S *pstGPIOInfo)
{
    if(NULL == pstGPIOInfo)
    {
        return SV_FAILURE;
    }

#if defined(BOARD_ADA47V1)
    /* 获取Switch3/Out状态 */
    gpio_GetSwitch3Status(pstGPIOInfo);
    /* 获取Switch4/Out状态 */
    gpio_GetSwitch4Status(pstGPIOInfo);
#else
    /* 获取系统状态 */
    gpio_GetSysStatus(&(pstGPIOInfo->stModStatus));
    /* 获取SD卡状态 */
    gpio_GetStorageStatus(&(pstGPIOInfo->stModStatus));
    /* WIFI状态 */
    gpio_GetWIFIStatus(&(pstGPIOInfo->stModStatus));
    /* 获取4G */
    gpio_GetCellStatus(&(pstGPIOInfo->stModStatus));
    /* 获取GPS */
    gpio_GetGPSStatus(&(pstGPIOInfo->stModStatus));
#endif
    /* 设置普通输入 */
    gpio_GetAllInputStatus(pstGPIOInfo);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置所有模块引脚的状态信息
 * 输入参数: 无
 * 输出参数: pstGPIOInfo -- 模块信息
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 gpio_SetAllModPinStatus(GPIO_INFO_S *pstGPIOInfo)
{
    uint32 i = 0;
    static MODULE_STA_S stOldModSta = {0};
    if(NULL == pstGPIOInfo)
    {
        return SV_FAILURE;
    }

#if defined(BOARD_ADA47V1)
    /* Switch3/Out */
    if(stOldModSta.eSwitch3OutStatus != pstGPIOInfo->stModStatus.eSwitch3OutStatus)
    {
        switch(pstGPIOInfo->stModStatus.eSwitch3OutStatus)
        {
            case GPIO_OUTPUT_LOW:
                gpio_SetPinStatus(pstGPIOInfo,SWITCH_3_OUT,GPIO_OPS_O_L);
                break;
            case GPIO_OUTPUT_HIGH:
                gpio_SetPinStatus(pstGPIOInfo,SWITCH_3_OUT,GPIO_OPS_O_H);
                break;
            
            default:
                gpio_SetPinStatus(pstGPIOInfo,SWITCH_3_OUT,GPIO_OPS_O_L);
                break;
        }
    }
    
    /* Switch4/Out */
    if(stOldModSta.eSwitch4OutStatus != pstGPIOInfo->stModStatus.eSwitch4OutStatus)
    {
        switch(pstGPIOInfo->stModStatus.eSwitch4OutStatus)
        {
            case GPIO_OUTPUT_LOW:
                gpio_SetPinStatus(pstGPIOInfo,SWITCH_4_OUT,GPIO_OPS_O_L);
                break;
            case GPIO_OUTPUT_HIGH:
                gpio_SetPinStatus(pstGPIOInfo,SWITCH_4_OUT,GPIO_OPS_O_H);
                break;
            
            default:
                gpio_SetPinStatus(pstGPIOInfo,SWITCH_4_OUT,GPIO_OPS_O_L);
                break;
        }
    }
#else
    /* 系统状态灯 */
    if(stOldModSta.eSysStatus != pstGPIOInfo->stModStatus.eSysStatus)
    {
        switch(pstGPIOInfo->stModStatus.eSysStatus)
        {
            case LED_SYS_OFF:
                gpio_SetPinStatus(pstGPIOInfo,LED_SYS,GPIO_OPS_O_L);
                break;
            case LED_SYS_ON:
                gpio_SetPinStatus(pstGPIOInfo,LED_SYS,GPIO_OPS_FLASH_S);
                break;
            
            default:
                gpio_SetPinStatus(pstGPIOInfo,LED_SYS,GPIO_OPS_O_L);
                break;
        }
    }

    /* 存储状态灯 */
    if(stOldModSta.eStorageStatus != pstGPIOInfo->stModStatus.eStorageStatus)
    {
        switch(pstGPIOInfo->stModStatus.eStorageStatus)
        {
            case LED_STOR_PROBID:
                gpio_SetPinStatus(pstGPIOInfo,LED_STORAGE,GPIO_OPS_O_L);
                break;
            case LED_STOR_NORMAL:
                gpio_SetPinStatus(pstGPIOInfo,LED_STORAGE,GPIO_OPS_O_H);
                break;
            case LED_STOR_RECORD:
                gpio_SetPinStatus(pstGPIOInfo,LED_STORAGE,GPIO_OPS_FLASH_S);
                break;
            default:
                gpio_SetPinStatus(pstGPIOInfo,LED_STORAGE,GPIO_OPS_O_L);
                break;
        }
    }
    
    /* wifi 灯 */
    if(stOldModSta.eWIFIStatus != pstGPIOInfo->stModStatus.eWIFIStatus)
    {
        switch(pstGPIOInfo->stModStatus.eWIFIStatus)
        {
            case LED_WIFI_PROBID:
                gpio_SetPinStatus(pstGPIOInfo,LED_WIFI,GPIO_OPS_O_L);
                break;
            case LED_WIFI_DISCON:
                gpio_SetPinStatus(pstGPIOInfo,LED_WIFI,GPIO_OPS_FLASH_S);
                break;
            case LED_WIFI_CONNECTING:
                gpio_SetPinStatus(pstGPIOInfo,LED_WIFI,GPIO_OPS_FLASH_Q);
                break;
            case LED_WIFI_CONNECTED:
                gpio_SetPinStatus(pstGPIOInfo,LED_WIFI,GPIO_OPS_O_H);
                break;

            default:
                gpio_SetPinStatus(pstGPIOInfo,LED_WIFI,GPIO_OPS_O_L);
                break;
        }

    }
    
    /* 4G状态灯 */
    if(stOldModSta.eCellStatus != pstGPIOInfo->stModStatus.eCellStatus)
    {
        switch(pstGPIOInfo->stModStatus.eCellStatus)
        {
            case LED_CELL_PROBID:
                gpio_SetPinStatus(pstGPIOInfo,LED_4G,GPIO_OPS_O_L);
                break;
            case LED_CELL_DISCON:
                gpio_SetPinStatus(pstGPIOInfo,LED_4G,GPIO_OPS_FLASH_S);
                break;
            case LED_CELL_CONNECTING:
                gpio_SetPinStatus(pstGPIOInfo,LED_4G,GPIO_OPS_FLASH_Q);
                break;
            case LED_CELL_CONNECTED:
                gpio_SetPinStatus(pstGPIOInfo,LED_4G,GPIO_OPS_O_H);
                break;

            default:
                gpio_SetPinStatus(pstGPIOInfo,LED_4G,GPIO_OPS_O_L);
                break;
        }

    }
    
    /* GPS灯 */
    if(stOldModSta.eGPSStatus != pstGPIOInfo->stModStatus.eGPSStatus)
    {
        switch(pstGPIOInfo->stModStatus.eGPSStatus)
        {
            case LED_GPS_PROBID:
                gpio_SetPinStatus(pstGPIOInfo,LED_GPS,GPIO_OPS_O_L);
                break;
            case LED_GPS_DISCON:
                gpio_SetPinStatus(pstGPIOInfo,LED_GPS,GPIO_OPS_FLASH_S);
                break;
            case LED_GPS_CONNECTING:
                gpio_SetPinStatus(pstGPIOInfo,LED_GPS,GPIO_OPS_FLASH_Q);
                break;
            case LED_GPS_CONNECTED:
                gpio_SetPinStatus(pstGPIOInfo,LED_GPS,GPIO_OPS_O_H);
                break;

            default:
                gpio_SetPinStatus(pstGPIOInfo,LED_GPS,GPIO_OPS_O_L);
                break;
        }

    }
#endif

    /* 输入引脚监测 */
    for(i=0; i<GPIO_PIN_NUM; i++)
    {
    
        if((GPIO_INPUT == pstGPIOInfo->astGPIOPin[i].eDirection) 
            && (stOldModSta.eGPIOInStatus[i] != pstGPIOInfo->stModStatus.eGPIOInStatus[i]))
        {
            gpio_SetPinStatus(pstGPIOInfo, pstGPIOInfo->astGPIOPin[i].ePinIndex, GPIO_OPS_I);
        }
    }
    
    memcpy(&stOldModSta,&(pstGPIOInfo->stModStatus),sizeof(MODULE_STA_S));
    return SV_SUCCESS;
}
void* gpio_Update_Body(void *pvArg)
{
    sint32 s32Ret = -1;
    uint32 i = 0, u32Cnt = 0;
    GPIO_INFO_S *pstGPIOInfo = (GPIO_INFO_S *)pvArg;
    
    s32Ret = prctl(PR_SET_NAME, "gpio_Update_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    
    while(pstGPIOInfo->bRunning)
    {
        sleep_ms(250);
        for(i=0; i<GPIO_PIN_NUM; i++)
        {
            /* 状态切换，避免多次设置输出更改 */
            if(SV_TRUE == pstGPIOInfo->astGPIOPin[i].bUpdateOPS)
            {
                switch(pstGPIOInfo->astGPIOPin[i].eGPIOOps)
                {
                    case GPIO_OPS_I:
                        break;
                    case GPIO_OPS_O_H:
                        gpio_SetPinVal(pstGPIOInfo->astGPIOPin[i], GPIO_HIGH);
                        break;
                    case GPIO_OPS_O_L:
                        gpio_SetPinVal(pstGPIOInfo->astGPIOPin[i], GPIO_LOW);
                        break;
                    case GPIO_OPS_O_T:
                        gpio_TogglePin(pstGPIOInfo->astGPIOPin[i]);
                        break;
                    case GPIO_OPS_FLASH_Q:
                    case GPIO_OPS_FLASH_S:
                        break;
                    default:
                        continue;
                        break;
                }
            }
            
            /* 日常更新输入和闪烁状态 */
            if(SV_TRUE == pstGPIOInfo->astGPIOPin[i].bValid)
            {
                switch(pstGPIOInfo->astGPIOPin[i].eGPIOOps)
                {
                    case GPIO_OPS_I:
                        gpio_GetPinVal(pstGPIOInfo->astGPIOPin[i], &(pstGPIOInfo->astGPIOPin[i].u32Value));
                        break;
                    case GPIO_OPS_FLASH_Q:
                        gpio_TogglePin(pstGPIOInfo->astGPIOPin[i]);
                        break;
                    case GPIO_OPS_FLASH_S:
                        if(0 == (u32Cnt%2))
                        {
                            gpio_TogglePin(pstGPIOInfo->astGPIOPin[i]);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        u32Cnt++;
    }
    return NULL;
}

void* gpio_Check_Body(void *pvArg)
{
    sint32 s32Ret = -1;
    GPIO_INFO_S *pstGPIOInfo = (GPIO_INFO_S *)pvArg;
    
    s32Ret = prctl(PR_SET_NAME, "gpio_Check_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    gpio_PinInit(pstGPIOInfo);
    while(pstGPIOInfo->bRunning)
    {
        gpio_GetAllModStatus(pstGPIOInfo);
        gpio_SetAllModPinStatus(pstGPIOInfo);
        sleep_ms(250);
    }
    return NULL;

}


sint32 GPIO_Init(void)
{
    memset(&m_stGPIOInfo, 0, sizeof(GPIO_INFO_S));
    
    return SV_SUCCESS;
}

sint32 GPIO_Fini(void)
{
    gpio_PinFini(&m_stGPIOInfo);
    return SV_SUCCESS;
}

sint32 GPIO_Start(void)
{
    sint32 s32Ret = 0;
    pthread_t thread;

    m_stGPIOInfo.bRunning = SV_TRUE;

    s32Ret = pthread_create(&thread, NULL, gpio_Update_Body, &m_stGPIOInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    m_stGPIOInfo.s32UpdateTid = thread;

    s32Ret = pthread_create(&thread, NULL, gpio_Check_Body, &m_stGPIOInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    m_stGPIOInfo.s32CheckTid = thread;

    return SV_SUCCESS;
}

sint32 GPIO_Stop(void)
{
    sint32 s32Ret = 0;
    pthread_t thread = m_stGPIOInfo.s32UpdateTid;
    void *pvRetval = NULL;

    m_stGPIOInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    thread = m_stGPIOInfo.s32CheckTid;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }    
    return SV_SUCCESS;
}


