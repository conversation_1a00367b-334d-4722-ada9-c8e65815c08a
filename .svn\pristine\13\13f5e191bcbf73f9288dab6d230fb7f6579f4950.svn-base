#!/bin/sh

case "$1" in
    start)
    mount -o remount -o sync -o noatime -o nodiratime -o norelatime,rw /

    autoUpdateRes=0
    cd /boot
    if [ -e autoUpdate ]
    then
        ./autoUpdate
        autoUpdateRes=$?
    fi

    /root/update_cma.sh 1
    if [ $autoUpdateRes -ne 254 ]
    then
        if [ $autoUpdateRes -ne 10 ]
        then
            cd /root
            if [ -e bootstart.sh ]
            then
                ./bootstart.sh
            fi
        else
            cd /tmp
            if [ -e ada32_autoTest.sh ]
            then
                ./ada32_autoTest.sh
            fi
        fi
    fi
    ;;
    stop)
    umount /mnt/sdcard
    ;;
esac
