/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_venc.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-11-22

文件功能描述: 封装海思MPP视频编码模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include <sys/types.h>
#ifndef __HuaweiLite__ 
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#endif
#include <sys/select.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "common.h"
//#include "hi_common.h"
//#include "mpi_venc.h"
#include "mpp_com.h"
#include "mpp_sys.h"
#include "mpp_venc.h"
#include "media.h"

// pyl - NT98539
#include "hdal.h"
#include "hd_debug.h"
#include "vendor_videoenc.h"
#include <sys/time.h>

#define MPP_VENC_MAX_PACKS  (STREAM_MAX_PACKS / 2)  /* 一帧码流最大的包个数 */
#define REC_FIX_TBR(w,h)	((((w) * (h) * 3/2) /5) * 8)

/* 视频编码通道状态信息 */
typedef struct tagVencChn_S
{
    // VENC_CHN    VeChnId;                     /* 编码通道ID */
    SV_BOOL                 bCreated;           /* 是否已创建 */
    SV_BOOL                 bEnable;            /* 是否使能编码 */
    sint32                  s32VencFd;          /* 通道设备文件句柄 */
    // NT98539
	HD_PATH_ID              u32EncPath;         // 代替VeChnId
    HD_PATH_ID              u32StampPath;       // 内部Stamp通道
    HD_PATH_ID              u32EXStampPath;     // 扩展Stamp通道
    HD_VIDEOENC_BUFINFO     stStreamBufInfo;
    HD_VIDEOENC_BS          stBitStream;
    UINTPTR                 pu32VirAddr;
    UINT32                  u32StampSize;
    HD_COMMON_MEM_VB_BLK    stStampBlk;
    UINTPTR                 pStampPhyAddress;
} MPP_VENC_CHN_S;

/* 视频编码模块控制信息 */
typedef struct tagVencInfo_S
{
    uint32          u32ChnNum;                  /* 视频源通道数目 */
    VIDEO_MODE_EE   enVideoMode;                /* 视频制式 */
    ENCODE_E        enMainEncode;               /* 主码流编码格式 */
    ENCODE_E        enSubEncode;                /* 子码流编码格式 */
    SV_BOOL         bColor2Grey;                /* 是否使能彩色转灰色处理(用于夜视) */
    // 两路相机-每路4通道； 0-相机1； 1-相机2
    MPP_VENC_CHN_S  astPriChn[2];               /* 主码流编码通道 */
    MPP_VENC_CHN_S  astSecChn[2];               /* 次码流编码通道 */
    MPP_VENC_CHN_S  astSnap0Chn[2];             /* 图片主流编码通道 */
    MPP_VENC_CHN_S  astSnap1Chn[2];             /* 图片次流编码通道 */

    sint32          s32MaxVencFd;               /* 视频编码通道设备文件句柄最大值 */
    MEDIA_DATA_CALLBACK pfDataCallback;         /* 媒体流数据回调函数指针 */
    ulng32          u32MainTid;                 /* 视频编码线程ID */
    ulng32          u32SECTid;                  /* 视频编码线程ID */
    ulng32          u32SnapTid;                 /* 图片编码线程ID */
    SV_BOOL         bRunning;                   /* 线程是否正在运行 */
    SV_BOOL         bException;                 /* 线程是否出现异常 */
    pthread_mutex_t mutexLock;                  /* 通道操作线程互斥锁 */
    fd_set          static_fds;                 /* 编码通道静态select fds */
} MPP_VENC_INFO_S;

MPP_VENC_INFO_S m_stVencInfo = {0};             /* 视频编码控制信息 */

void * mpp_venc_MainBody(void *pvArg);
void * mpp_venc_SECBody(void *pvArg);
void * mpp_venc_SnapBody(void *pvArg);
sint32 mpp_venc_GetStream(STREAM_TYPE_E enStreamType, sint32 s32Chn, sint32 s32MaxFrmNum);

static uint64 mpp_enc_GetSysTimeByUsec(void)
{
    struct timeval stTime;

    gettimeofday(&stTime, NULL);

    return  (stTime.tv_sec * 1000000LLU) + stTime.tv_usec;
}

/****************************************************gd库实现硬件OSD功能: start**********************************************************/ 
#include <stdlib.h>
#include <limits.h>
#include "gd/gd.h"
#include "gd/gd_io.h"
#include "gd/gd_errors.h"

static gdImagePtr   p_stImgForOSG;
unsigned short      imDateARGB4444[320][640];
unsigned short      *imDataBufferARGB4444 = NULL;

/*
    函数功能:       调整gd的指针指向hdal的内存空间, 再后续需求中，还可以传递进来一些点、几何曲线和图形以及BMP图形的结构体(携带必要信息)，用于绘制操作
    函数参数:       来自HDAL的内存块指针，在外部申请好再传递进来
    函数返回值:     返回是否已经执行成功或者失败的结果, 用于后续配置STAMP的buffer区域
    函数使用方法:   传入申请到的common内存(该内存回到系统运行结束才会回收)，函数在这个空间内绘制图形
*/
typedef enum
{
    GD_DRAW_LINE = 0,
    GD_DRAW_GEOMETRIC,
    GD_DRAW_BMP,

    GD_DRAW_BUFF = 0x10
}GDDRAWTYPE;

/*
    函数功能: 重新复现libgd库的构造函数, 在里面加入common内存空间的指定
*/ 
// static int overflow2ForHdalCommon(int a, int b)
// {
// 	if(a <= 0 || b <= 0) {
// 		gd_error_ex(GD_WARNING, "one parameter to a memory allocation multiplication is negative or zero, failing operation gracefully\n");
// 		return 1;
// 	}
// 	if(a > INT_MAX / b) {
// 		gd_error_ex(GD_WARNING, "product of memory allocation multiplication would exceed INT_MAX, failing operation gracefully\n");
// 		return 1;
// 	}
// 	return 0;
// }
static gdImagePtr gdImageCreateTrueColorWithHdalCommon(int sx, int sy, int* pVirCommonAddress){
    int i;
    gdImagePtr im;

    // if (overflow2ForHdalCommon(sx, sy)) {
    //     return NULL;
	// }
	// if (overflow2ForHdalCommon(sizeof (int *), sy)) {
	// 	return 0;
	// }
	// if (overflow2ForHdalCommon(sizeof(int), sx)) {
	// 	return NULL;
	// }

    im = (gdImage *) malloc(sizeof (gdImage));
	if (!im) {
		return 0;
	}
	memset (im, 0, sizeof (gdImage));

    im->tpixels = (int **) malloc(sizeof (int *) * sy);
	if (!im->tpixels) {
		gdFree(im);
		return NULL;
	}
    im->polyInts = 0;
	im->polyAllocated = 0;
	im->brush = 0;
	im->tile = 0;
	im->style = 0;
    
    for (i = 0; (i < sy); i++) {
        im->tpixels[i] = (int *)(pVirCommonAddress);
        pVirCommonAddress = pVirCommonAddress + sx;
	}
    
    im->sx = sx;
	im->sy = sy;
	im->transparent = (-1);
	im->interlace = 0;
	im->trueColor = 1;
	/* 2.0.2: alpha blending is now on by default, and saving of alpha is
	   off by default. This allows font antialiasing to work as expected
	   on the first try in JPEGs -- quite important -- and also allows
	   for smaller PNGs when saving of alpha channel is not really
	   desired, which it usually isn't! */
	im->saveAlphaFlag = 0;
	im->alphaBlendingFlag = 1;
	im->thick = 1;
	im->AA = 0;
	im->cx1 = 0;
	im->cy1 = 0;
	im->cx2 = im->sx - 1;
	im->cy2 = im->sy - 1;
	im->res_x = GD_RESOLUTION;
	im->res_y = GD_RESOLUTION;
	im->interpolation = NULL;
	im->interpolation_id = GD_BILINEAR_FIXED;
	return im;
}

/*
    函数功能: 针对HDAL的common内存, 释放image的指针, 避免释放common内存空间
*/ 
static void gdImageDestroyWithHdalCommon(gdImagePtr im){
    int i;
    if (im->pixels) {
		for (i = 0; (i < im->sy); i++) {
			gdFree (im->pixels[i]);
		}
		gdFree (im->pixels);
	}
	if (im->tpixels) {
		// 保留common的内存空间
        // for (i = 0; (i < im->sy); i++) {
		// 	gdFree (im->tpixels[i]);
		// }
		gdFree (im->tpixels);
	}
	if (im->polyInts) {
		gdFree (im->polyInts);
	}
	if (im->style) {
		gdFree (im->style);
	}
	gdFree (im);
}

static BOOL createGDImageWithHdalMemSpace(int *pBufAddress, void *pstDrawData, GDDRAWTYPE eDrawType){
    struct timeval start, end;
    long seconds, micros;
    FILE *pngout;

    gdImagePtr pstImageDrawer = NULL;

    const int s32Width  = 2560;
    const int s32Height = 1440;

    if (NULL == pBufAddress || GD_DRAW_BUFF == eDrawType)
    {
        return FALSE;
    }

    // 分配GD对象的思路: 创建并分配指针的内存空间, 接着释放掉tpixels的一级指针地址, 重新分配一级指针并设定指针指向common空间
    // 结束后，重置一级指针的指向为NULL, 最后释放drawer
    pstImageDrawer = gdImageCreateTrueColorWithHdalCommon(s32Width, s32Height, pBufAddress);
    int white = gdImageColorAllocateAlpha(pstImageDrawer, 125, 125, 0, 0);            // 白色, alpha=0表示不透明, 127表示全部透明
    // gdImageFill(pstImageDrawer, 0, 0, white);

    gettimeofday(&start, NULL);

    gdImageSetThickness(pstImageDrawer, 3);
    int black = gdImageColorAllocateAlpha(pstImageDrawer, 255, 0, 255, 0);            // 白色, alpha=0表示不透明, 127表示全部透明
    for (size_t i = 0; i < 5; i++)
    {
        gdImageLine(pstImageDrawer, 0, 50*i, 1000, 50*i, black);
    }
    gdImageSetThickness(pstImageDrawer, 3);
    for (size_t i = 0; i < 5; i++)
    {
        gdImageRectangle(pstImageDrawer, 0+50*i, 500, 500+50*i, 1000, black);
    }

    gettimeofday(&end, NULL);
    seconds = end.tv_sec - start.tv_sec;
    micros = seconds * 1000000 + end.tv_usec - start.tv_usec;
    print_level(SV_INFO, "createGDImageWithHdalMemSpace-代码运行时间：%ld微秒\n", micros);

    pngout = fopen("apiNew.png", "wb");
    gdImagePng(pstImageDrawer, pngout);
    fclose(pngout);

    gdImageDestroyWithHdalCommon(pstImageDrawer);
    return TRUE;
}

static void createGDImageWithHdal(){
    struct timeval start, end;
    long seconds, micros;
    FILE *pngout;

    const int s32Width  = 2560;
    const int s32Height = 1440;

    gdImagePtr test;
    // gdImage newTest;
    gdImage stContentHdalRAM;
    test      = gdImageCreateTrueColor(s32Width, s32Height);
    int white = gdImageColorAllocateAlpha(test, 7, 7, 7, 12);            // 白色, alpha=0表示不透明, 127表示全部透明
    gdImageFill(test, 0, 0, white);

    gettimeofday(&start, NULL);

    HD_COMMON_MEM_VB_BLK pBufBlk = hd_common_mem_get_block(HD_COMMON_MEM_COMMON_POOL, sizeof(int) * 2560 * 1440, DDR_ID0);
    if (pBufBlk == HD_COMMON_MEM_VB_INVALID_BLK) {
        printf("get block fail\r\n");
        return HD_ERR_NOMEM;
	}
    UINTPTR pBufPhyAddress = hd_common_mem_blk2pa(pBufBlk);
    if (pBufPhyAddress == 0) {
        printf("blk2pa fail, buf_blk = 0x%x\r\n", pBufPhyAddress);
        return HD_ERR_NOMEM;
	}
    void *pVirAddress = hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_CACHE, pBufPhyAddress, sizeof(int) * 2560 * 1440);
    stContentHdalRAM = *test;
    // 置换stContentHdalRAM内部的trueColor指向的内存地址
    INT32 *pTemp = (INT32 *)pVirAddress;
    stContentHdalRAM.tpixels = (int **)malloc(sizeof(int *)*s32Height);
    for (size_t y = 0; y < s32Height; y++)
    {
        for (size_t x = 0; x < s32Width; x++)
        {
            // 拷贝到hdal空间
            pTemp[y * s32Width + x] = 0x1F0500FF;
        }
    }
    for (size_t y = 0; y < s32Height; y++)
    {
        stContentHdalRAM.tpixels[y] = (int *)(pTemp);
        pTemp = pTemp + s32Width;
    }
    gdImageSetThickness(&stContentHdalRAM, 10);
    int black = gdImageColorAllocateAlpha(test, 0, 0, 0, 50);            // 白色, alpha=0表示不透明, 127表示全部透明
    gdImageLine(&stContentHdalRAM, 0, 1000, 1000, 1000, black);
    gdImageLine(&stContentHdalRAM, 0, 1200, 1000, 1200, black);

    // newTest = *test;
    int *pImagePtr = (int *)malloc(sizeof(int)*s32Width*s32Height);
    // for (size_t y = 0; y < s32Height; y++)
    // {
    //     for (size_t x = 0; x < s32Width; x++)
    //     {
    //         // 拷贝到hdal空间
    //         pImagePtr[y * s32Width + x] = 0x7FFFFFFF;
    //     }
    // }
    // newTest.tpixels = (int **)malloc(sizeof(int *)*s32Height);
    // for (size_t y = 0; y < s32Height; y++)
    // {
    //     newTest.tpixels[y] = (int *)(pImagePtr+s32Width);
    // }

    gettimeofday(&end, NULL);
    seconds = end.tv_sec - start.tv_sec;
    micros = seconds * 1000000 + end.tv_usec - start.tv_usec;
    printf("createGDImageWithHdal-代码运行时间：%ld微秒\n", micros);

    pngout = fopen("new_alpha.png", "wb");
    gdImagePng(&stContentHdalRAM, pngout);
    fclose(pngout);

    if (pImagePtr)
    {
        free(pImagePtr);
    }

    hd_common_mem_munmap(pVirAddress, sizeof(int) * 2560 * 1440);
    hd_common_mem_release_block(pBufBlk);

    gdImageDestroy(test);
}

static void createDeviceInfoForImage(unsigned short *ARGB4444){
    struct timeval start, end;
    long seconds, micros;
    int brect[8];
    int black;
    gdImagePtr imTrueColor;
    FILE *pngout;
    unsigned short a, r, g, b, color16;
    int c;

    gettimeofday(&start, NULL);
    int colorPlatte = 0;
    // start
    imTrueColor = gdImageCreateTrueColor(640, 320);
    gdImageAlphaBlending(imTrueColor, gdEffectReplace);
                                                    // r(16) g(8) b(0) a(24) A*B/127 = 95*20 / 127
    // int white = gdImageColorAllocateAlpha(imTrueColor, 7, 7, 7, 20);         // 白色, alpha=0表示不透明, 127表示全部透明
    int white = gdImageColorAllocateAlpha(imTrueColor, 7, 7, 7, 12);            // 白色, alpha=0表示不透明, 127表示全部透明
    gdImageFill(imTrueColor, 0, 0, white);
    // int color = gdImageColorAllocateAlpha(imTrueColor, 0, 30, 0, 95);        // NT98539中alpha=0表示完全透明，15表示不透明
    int color = gdImageColorAllocateAlpha(imTrueColor, 0, 15, 0, 15);           // NT98539中alpha=0表示完全透明，15表示不透明
    gdImageLine(imTrueColor, 0, 0, 100, 100, color);
    gdImageStringFT(imTrueColor, brect, color, "/mnt/app/pyl/PangMenZhengDaoBiaoTiTiMianFeiBan/PangMenZhengDaoBiaoTiTiMianFeiBan-2.ttf", 40, 0.0, 100, 100, "ADA32-20240826");
    // gdImageStringFT(imTrueColor, brect, color, "/mnt/app/pyl/PangMenZhengDaoBiaoTiTiMianFeiBan/PangMenZhengDaoBiaoTiTiMianFeiBan-2.ttf", 40, 0.0, 300, 300, "ADA32设备");
    gettimeofday(&end, NULL);
    seconds = end.tv_sec - start.tv_sec;
    micros = seconds * 1000000 + end.tv_usec - start.tv_usec;
    printf("0-代码运行时间：%ld微秒\n", micros);

    gdImageStringFT(imTrueColor, brect, color, "/mnt/app/pyl/PangMenZhengDaoBiaoTiTiMianFeiBan/PangMenZhengDaoBiaoTiTiMianFeiBan-2.ttf", 40, 0.0, 300, 300, "ADA32设备");
    gettimeofday(&end, NULL);
    seconds = end.tv_sec - start.tv_sec;
    micros = seconds * 1000000 + end.tv_usec - start.tv_usec;
    printf("2-代码运行时间：%ld微秒\n", micros);

    print_level(SV_INFO, "font position = %d, %d", brect[0], brect[1]);
    
    imDataBufferARGB4444 = (unsigned short*)malloc(640*320*sizeof(unsigned short));
    for (size_t y = 0; y < 320; y++)
    {
        for (size_t x = 0; x < 640; x++)
        {
            c = gdImageGetPixel(imTrueColor, x, y);
            // print_level(SV_INFO, "c = %d \n", c);
            // print_level(SV_INFO, "imTrueColor->tpixels red=%d, green=%d, blue=%d alpha=%d\n", \ 
            //                     gdImageRed(imTrueColor, c), gdImageGreen(imTrueColor, c), gdImageBlue(imTrueColor, c), gdImageAlpha(imTrueColor, c));
            a = gdImageAlpha(imTrueColor, c) & 0x0F;
            // print_level(SV_INFO, "gdImageAlpha(imTrueColor, c)=%d, a = %d \n", gdImageAlpha(imTrueColor, c), a);
            r = gdImageRed(imTrueColor, c) & 0x0F;
            g = gdImageGreen(imTrueColor, c) & 0x0F;
            // print_level(SV_INFO, "gdImageAlpha(imTrueColor, c)=%d, a = %d \n", gdImageGreen(imTrueColor, c), g);
            b = gdImageBlue(imTrueColor, c) & 0x0F;
            color16 = (a<<12) | (r<<8) | (g<<4) | (b<<0);
            imDataBufferARGB4444[640*y + x] = color16;
        }
    }
    gettimeofday(&end, NULL);
    seconds = end.tv_sec - start.tv_sec;
    micros  = seconds * 1000000 + end.tv_usec - start.tv_usec;
    printf("2-代码运行时间：%ld微秒\n", micros);

    pngout = fopen("alpha.png", "wb");
    gdImagePng(imTrueColor, pngout);
    fclose(pngout);

    gdImageDestroy(imTrueColor);

    print_level(SV_INFO, "This is gd draw graphic \n");
}

static void destoryDeviceInfoForImage(void)
{
    print_level(SV_INFO, "release buffer \n");
    if (p_stImgForOSG)
    {
        gdImageDestroy(p_stImgForOSG);   
    }

    if(imDataBufferARGB4444){
        free(imDataBufferARGB4444);
    }
}
/*****************************************************gd库实现硬件OSD功能: end***********************************************************/ 

unsigned short *novatek_logo = NULL;
/*
    * 函数功能: 申请OSG的内存指针
    * 输入参数: 
    * 输出参数: 无
    * 返回值  : 
    * 注意    : 无
*/
static UINT32 calculate_osg_buf_size(UINT32 width, UINT32 height)
{
    HD_VIDEO_FRAME  stStampFrame = {0};
    UINT32          u32StampSize = 0;

    stStampFrame.sign   = MAKEFOURCC('O','S','G','P');
    stStampFrame.dim.w  = width;
    stStampFrame.dim.h  = height;
    stStampFrame.pxlfmt = HD_VIDEO_PXLFMT_ARGB4444;
    
    u32StampSize = hd_common_mem_calc_buf_size(&stStampFrame);
    if (!u32StampSize)
    {
        print_level(SV_ERROR, " calculate osg buffer size error...\n ");
    }
    
    u32StampSize *= 2;
    u32StampSize = ALIGN_CEIL(u32StampSize, 128);
    print_level(SV_INFO, " u32StampSize=%d \n ", u32StampSize);

    return u32StampSize;    
}

static BOOL request_osg_mem_buffer(UINT32 stampSize, HD_COMMON_MEM_VB_BLK *pStampBlk, UINTPTR *pStampPhyAddress)
{
    UINTPTR                 pa;
    HD_COMMON_MEM_VB_BLK    blk;

    if (0 == stampSize)
    {
        print_level(SV_ERROR, " Something is wrong with the incoming parameters \n");
        return FALSE;
    }

    blk = hd_common_mem_get_block(HD_COMMON_MEM_OSG_POOL, stampSize, DDR_ID0);
    if (HD_COMMON_MEM_VB_INVALID_BLK == blk)
    {
        print_level(SV_ERROR, " HD_COMMON_MEM_VB_INVALID_BLK error... \n");
        return FALSE;
    }else
    {
        *pStampBlk = blk;
    }

    pa = hd_common_mem_blk2pa(blk);
    if (NULL == pa)
    {
        print_level(SV_ERROR, " hd_common_mem_blk2pa error\n ");
    }
    *pStampPhyAddress = pa;

    return TRUE;
}

static HD_RESULT mpp_venc_set_osg(void)
{
    HD_OSG_STAMP_BUF    buf;
    HD_OSG_STAMP_IMG    img;
    HD_OSG_STAMP_ATTR   attr;
    HD_RESULT           eRet = HD_OK;

    m_stVencInfo.astPriChn[0].u32StampSize = calculate_osg_buf_size(640, 320);
    eRet = request_osg_mem_buffer(m_stVencInfo.astPriChn[0].u32StampSize, &(m_stVencInfo.astPriChn[0].stStampBlk), \
                                                                            &(m_stVencInfo.astPriChn[0].pStampPhyAddress));
    
    buf.type    = HD_OSG_BUF_TYPE_PING_PONG;
    buf.p_addr  = m_stVencInfo.astPriChn[0].pStampPhyAddress;
    buf.size    = m_stVencInfo.astPriChn[0].u32StampSize;
    eRet = hd_videoenc_set(m_stVencInfo.astPriChn[0].u32StampPath, HD_VIDEOENC_PARAM_IN_STAMP_BUF, &buf);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, " fail to stamp buffer set...\n ");
        return eRet;
    }
    
    img.fmt     = HD_VIDEO_PXLFMT_ARGB4444;
    img.dim.w   = 640;
    img.dim.h   = 320;
    // img.p_addr  = p_stImgForOSG->tpixels[0];
    // img.p_addr  = (UINTPTR)imDateARGB4444;
    img.p_addr  = (UINTPTR)imDataBufferARGB4444;
    // print_level(SV_INFO, "p_stImgForOSG.tpixels[0]=%d \n", p_stImgForOSG->tpixels[0]);
    
    eRet = hd_videoenc_set(m_stVencInfo.astPriChn[0].u32StampPath, HD_VIDEOENC_PARAM_IN_STAMP_IMG, &img);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "fail to set stamp image... eRet=%d\n", eRet);
        return eRet;
    }

    attr.position.x = 200;
    attr.position.y = 200;
    attr.alpha      = 0;
    attr.layer      = 0;
    attr.region     = 0;
    attr.qp_fix     = 1;
    attr.qp_val     = 30;
    attr.qp_en      = 1;
    eRet = hd_videoenc_set(m_stVencInfo.astPriChn[0].u32StampPath, HD_VIDEOENC_PARAM_IN_STAMP_ATTR, &attr);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "fail to set stamp attr... eRet=%d\n", eRet);
    }

    return eRet;
}

/******************************************************************************
 * 函数功能: 初始化VENC模块
 * 输入参数: pstVencConf --- 视频编码配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_ILLEGAL_PARAM - 传入参数非法
             ERR_EXIST - 编码通道组或通道已存在
             ERR_SYS_NOTREADY - 系统未初始化
             ERR_NOT_PERM - 操作不允许
             ERR_NOMEM - 内存不足
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_Init(MPP_VENC_CONF_S *pstVencConf)
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
	uint8  eSceneMode = 0;

    HD_RESULT eRet = HD_OK;

    createDeviceInfoForImage(imDataBufferARGB4444);
    // createGDImageWithHdal();
    HD_COMMON_MEM_VB_BLK    blk = hd_common_mem_get_block(HD_COMMON_MEM_COMMON_POOL, sizeof(int)*2560*1440, DDR_ID0);
    UINTPTR pBufPhyAddress = NULL;
    int    *pVirAddress   = NULL;
    // 映射内存块空间到虚拟地址中
    pBufPhyAddress = hd_common_mem_blk2pa(blk);
    pVirAddress    = (int *)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_NONCACHE, pBufPhyAddress, sizeof(int)*2560*1440);
    if (NULL == pBufPhyAddress || NULL == pVirAddress)
    {
        print_level(SV_ERROR, "block 2 physical address error, error type is %d \n", HD_ERR_NOMEM);
        return FALSE;
    }
    memset(pVirAddress, 0x00FFFFFFF, sizeof(int) * 2560 * 1440);
    createGDImageWithHdalMemSpace(pVirAddress, NULL, GD_DRAW_LINE);
    hd_common_mem_munmap(pVirAddress, sizeof(int) * 2560 * 1440);
    eRet = hd_common_mem_release_block(blk);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_common_mem_release_block fail=%d\n", eRet);
        return ERR_SYS_NOTREADY;
    }

    if (NULL == pstVencConf || NULL == pstVencConf->pfDataCallback)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstVencConf->u32ChnNum || pstVencConf->u32ChnNum > VIM_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    m_stVencInfo.u32ChnNum   = pstVencConf->u32ChnNum;    
    m_stVencInfo.enVideoMode = pstVencConf->enVideoMode;

    // 初始化编码模块-一个ENC模块，多输入多输出
    eRet = hd_videoenc_init();
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_init fail=%d\n", eRet);
        return ERR_SYS_NOTREADY;
    }

    // u32ChnNum是相机index, 默认主码流输入端口为0, 其余码流的输入端口逐渐+1, 通过增加对应编码功能的create函数，可分别创建出对应的通道
    // [0-1]是主码流；[2-3]是子码流; [4-5]是JPEG流; [6-7]是可选流
    print_level(SV_INFO, "Venc Width: %d, Height: %d.\n", pstVencConf->stPriVencAttr.u32PicWidth, pstVencConf->stPriVencAttr.u32PicHeight);
    for (i = 0; i < pstVencConf->u32ChnNum; i++)
    {
        s32Chn = i;                     //编码通道      相机输入   配置参数 = STREAM_TYPE_PRI+s32Chn
        s32Ret = mpp_venc_H264CreateChn(STREAM_TYPE_PRI, s32Chn, &pstVencConf->stPriVencAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264CreateChn PriChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }else{
            print_level(SV_INFO, "mpp_venc_H264CreateChn OK...\n");
        }

        s32Ret = mpp_venc_H264CreateChn(STREAM_TYPE_SEC, s32Chn, &pstVencConf->stSecVencAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264CreateChn SecChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }  

        s32Ret = mpp_venc_JpegCreateChn(STREAM_TYPE_SNAP0, s32Chn, &pstVencConf->stJpegVencAttr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_JpegCreateChn Sanp0Chn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }
    }

    s32Ret = pthread_mutex_init(&m_stVencInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        // free(pstPack);
        return ERR_SYS_NOTREADY;
    }

    // m_stVencInfo.pstPack = pstPack;
    m_stVencInfo.enMainEncode = pstVencConf->stPriVencAttr.enEncode;
    m_stVencInfo.enSubEncode = pstVencConf->stSecVencAttr.enEncode;
    m_stVencInfo.pfDataCallback = pstVencConf->pfDataCallback;
    m_stVencInfo.u32MainTid = 0;
    m_stVencInfo.u32SnapTid = 0;
    m_stVencInfo.bRunning = SV_FALSE;
    m_stVencInfo.bException = SV_FALSE;
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化VENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_UNEXIST - 编码通道不存在
             ERR_SYS_NOTREADY - 系统未初始化
             ERR_NOT_PERM - 操作不允许
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_Fini()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    HD_RESULT eRet = HD_OK;

    // 分别对每一个摄像头传感器的接口进行操作
    for (i = 0; i < m_stVencInfo.u32ChnNum; i++)
    {
        s32Chn = i;
        s32Ret = mpp_venc_DisableChn(STREAM_TYPE_PRI, s32Chn);  // stop模块对应于开启enble函数
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "mpp_venc_DisableChn PriChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
        }
        s32Ret = mpp_venc_H264DestroyChn(STREAM_TYPE_PRI, s32Chn);  // close模块
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264DestroyChn PriChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_venc_DisableChn(STREAM_TYPE_SEC, s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "mpp_venc_DisableChn SecChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
        }
        s32Ret = mpp_venc_H264DestroyChn(STREAM_TYPE_SEC, s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264DestroyChn SecChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }

        // 
        // s32Ret = mpp_venc_DisableChn(STREAM_TYPE_SNAP0, s32Chn);
        // if (SV_SUCCESS != s32Ret)
        // {
        //     print_level(SV_WARN, "mpp_venc_DisableChn SecChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
        // }
        s32Ret = mpp_venc_JpegDestroyChn(STREAM_TYPE_SNAP0, s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_JpegDestroyChn Snap0Chn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }

        // s32Ret = mpp_venc_DisableChn(STREAM_TYPE_SNAP1, s32Chn);        // stop模块
        // if (SV_SUCCESS != s32Ret)
        // {
        //     print_level(SV_ERROR, "mpp_venc_EnableChn snap ch%d failed. [err=%#x]\n", s32Chn, s32Ret);
        //     return s32Ret;
        // }
        // s32Ret = mpp_venc_JpegDestroyChn(STREAM_TYPE_SNAP1, s32Chn);    //close模块
        // if (SV_SUCCESS != s32Ret)
        // {
        //     print_level(SV_ERROR, "mpp_venc_JpegDestroyChn Snap1Chn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
        //     return s32Ret;
        // }
    }

    pthread_mutex_destroy(&m_stVencInfo.mutexLock);
    // free(m_stVencInfo.pstPack);
    // m_stVencInfo.pstPack = NULL;
    m_stVencInfo.pfDataCallback = NULL;
    m_stVencInfo.s32MaxVencFd = 0;

    eRet = hd_videoenc_uninit();
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_uninit eRet=%d\n", eRet);
        return SV_FAILURE;
    }

    // destoryDeviceInfoForImage();

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动VENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : pyl
 *****************************************************************************/
sint32 mpp_venc_Start()
{
    sint32 s32Ret = 0;
    ulng32 u32MainTid = 0;
    ulng32 u32SECTid = 0;
    ulng32 u32SnapTid = 0;

    m_stVencInfo.bRunning = SV_TRUE;
    m_stVencInfo.bException = SV_FALSE;

    s32Ret = pthread_create(&u32MainTid, NULL, mpp_venc_MainBody, &m_stVencInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VENC failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    // s32Ret = pthread_create(&u32SECTid, NULL, mpp_venc_SECBody, &m_stVencInfo);
    // if (0 != s32Ret)
    // {
    //     print_level(SV_ERROR, "Start thread for VENC failed! [err: %s]\n", strerror(errno));
    //     return s32Ret;
    // }

    // s32Ret = pthread_create(&u32SnapTid, NULL, mpp_venc_SnapBody, &m_stVencInfo);
    // if (0 != s32Ret)
    // {
    //     print_level(SV_ERROR, "Start thread for VENC failed! [err: %s]\n", strerror(errno));
    //     return s32Ret;
    // }

    print_level(SV_INFO, "mpp_venc_Start-m_stVencInfo.u32MainTid=%d\n", u32MainTid);

    m_stVencInfo.u32MainTid = u32MainTid;
    m_stVencInfo.u32SECTid  = u32SECTid;
    m_stVencInfo.u32SnapTid = u32SnapTid;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止VENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_Stop()
{
    sint32 s32Ret = 0;
    uint32  *pvRetval = NULL;

    m_stVencInfo.bRunning = SV_FALSE;
    
    // s32Ret = pthread_join(m_stVencInfo.u32SnapTid, &pvRetval);
    // if (SV_SUCCESS != s32Ret)
    // {
    //     print_level(SV_ERROR, "Stop thread for VENC failed! [err=%#x]\n", s32Ret);
    //     return s32Ret;
    // }
    // print_level(SV_INFO, " mpp_venc_Stop ... 1\n");
    
    s32Ret = pthread_join(m_stVencInfo.u32MainTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for VENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    print_level(SV_INFO, " mpp_venc_Stop ... 2\n");

    // s32Ret = pthread_join(m_stVencInfo.u32SECTid, &pvRetval);
    // if (SV_SUCCESS != s32Ret)
    // {
    //     print_level(SV_ERROR, "Stop thread for VENC failed! [err=%#x]\n", s32Ret);
    //     return s32Ret;
    // }

    // print_level(SV_INFO, " mpp_venc_Stop ... 3\n");
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VENC模块线程体
 * 输入参数: pstVencInfo --- 视频编码控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_venc_MainBody(void *pvArg)
{
    sint32 s32Ret = 0, i;
    fd_set read_fds, static_fds;
    struct timeval timeout = {2, 0};
    MPP_VENC_INFO_S *pstVencInfo = (MPP_VENC_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "mpp_venc_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    sleep(1);
    // 修改FD读取数据帧的select模式, NT的方法是调用pull_out_buf拉取内存(阻塞模式)
    while (pstVencInfo->bRunning)
    {
        pthread_mutex_lock(&pstVencInfo->mutexLock);
        s32Ret = mpp_venc_GetStream(STREAM_TYPE_PRI, i, 1);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "venc get Pri Stream ch:%d failed! [ret=%#x]\n", i, s32Ret);
            pstVencInfo->bException = SV_TRUE;
        }else {
            // print_level(SV_INFO, "mpp_venc_MainBody... \n");
        }
        pthread_mutex_unlock(&pstVencInfo->mutexLock);
        // sleep_ms(10);
    }

    print_level(SV_INFO, "mpp_venc_MainBody successfully... \n");

    return NULL;
}

void * mpp_venc_SECBody(void *pvArg)
{
    sint32 s32Ret = 0, i;
    fd_set read_fds, static_fds;
    struct timeval timeout = {2, 0};
    MPP_VENC_INFO_S *pstVencInfo = (MPP_VENC_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "mpp_venc_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    sleep(1);
    // 修改FD读取数据帧的select模式, NT的方法是调用pull_out_buf拉取内存(阻塞模式)
    while (pstVencInfo->bRunning)
    {
        pthread_mutex_lock(&pstVencInfo->mutexLock);
        // 结合STREAM_TYPE_SEC和序号1组合出具体的pathID
        s32Ret = mpp_venc_GetStream(STREAM_TYPE_SEC, 1, 1);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "venc get Pri Stream ch:%d failed! [ret=%#x]\n", i, s32Ret);
            pstVencInfo->bException = SV_TRUE;
        }
        pthread_mutex_unlock(&pstVencInfo->mutexLock);
        // sleep_ms(10);
    }

    return NULL;
}

/******************************************************************************
 * 函数功能: VENC模块线程体
 * 输入参数: pstVencInfo --- 视频编码控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_venc_SnapBody(void *pvArg)
{
    sint32 s32Ret = 0, i;
    MPP_VENC_INFO_S *pstVencInfo = (MPP_VENC_INFO_S *)pvArg;

    UINT32 u32FrameNums = 0;

    s32Ret = prctl(PR_SET_NAME, "mpp_snap_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }   

    sleep(1);

    while (pstVencInfo->bRunning)
    {       
        pthread_mutex_lock(&pstVencInfo->mutexLock);
        s32Ret = mpp_venc_GetStream(STREAM_TYPE_SNAP0, i, 1);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "venc get Snap1 Stream ch:%d failed! [ret=%#x]\n", i, s32Ret);
            pstVencInfo->bException = SV_TRUE;
        }
        pthread_mutex_unlock(&pstVencInfo->mutexLock);
        
        u32FrameNums++;
        if(u32FrameNums >= 1){
            break;
        }
    }

    return NULL;
}

#if 0
/******************************************************************************
 * 函数功能: 获取H264编码的数据包类型
 * 输入参数: enNaluType --- 海思的数据包类型值
 * 输出参数: 无
 * 返回值  : 数据包类型
 * 注意    : 无
 *****************************************************************************/
PACK_TYPE_E mpp_venc_GetH264PackType(H264E_NALU_TYPE_E enNaluType)
{
    switch (enNaluType)
    {
        case H264E_NALU_ISLICE:
        case H264E_NALU_IDRSLICE:
            return PACK_TYPE_ISLICE;

        case H264E_NALU_PSLICE:
            return PACK_TYPE_PSLICE;

        case H264E_NALU_SEI:
            return PACK_TYPE_SEI;

        case H264E_NALU_SPS:
            return PACK_TYPE_SPS;

        case H264E_NALU_PPS:
            return PACK_TYPE_PPS;

        default: 
            return PACK_TYPE_BUTT;
    }
}

/******************************************************************************
 * 函数功能: 获取H265编码的数据包类型
 * 输入参数: enNaluType --- 海思的数据包类型值
 * 输出参数: 无
 * 返回值  : 数据包类型
 * 注意    : 无
 *****************************************************************************/
PACK_TYPE_E mpp_venc_GetH265PackType(H265E_NALU_TYPE_E enNaluType)
{
    switch (enNaluType)
    {
        case H265E_NALU_ISLICE:
        case H265E_NALU_IDRSLICE:
            return PACK_TYPE_ISLICE;

        case H265E_NALU_PSLICE:
            return PACK_TYPE_PSLICE;

        case H265E_NALU_SEI:
            return PACK_TYPE_SEI;

        case H265E_NALU_SPS:
            return PACK_TYPE_SPS;

        case H265E_NALU_PPS:
            return PACK_TYPE_PPS;

        case H265E_NALU_VPS:
            return PACK_TYPE_VPS;    
    
        default: 
            return PACK_TYPE_BUTT;
    }
}
#endif

/******************************************************************************
 * 函数功能: 获取编码码流数据
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)指的是摄像头传感器序号
             s32MaxFrmNum --- 本次调用最大允许获取的帧数目
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_GetStream(STREAM_TYPE_E enStreamType, sint32 s32Chn, sint32 s32MaxFrmNum)
{
    // 拉取编码数据，存到STREAM_FRAME_S，回调函数m_stVencInfo.pfDataCallback(s32Chn, enStreamType, &stFrame, 0); 最后释放
    HD_RESULT             eRet = HD_OK;
    HD_VIDEOENC_POLL_LIST stPollList[0];  //  需要记录已经创建的通道数量
    UINT8                 *ptr = NULL;
    UINT32                len = 0;
    HD_PATH_ID            u32EncPath = 0;
    HD_VIDEOENC_BUFINFO   *stStreamBufInfo = NULL;
    HD_VIDEOENC_BS        *stBitStream = NULL;
    UINTPTR               pu32VirAddr;

    STREAM_FRAME_S        stFrame;
    ENCODE_E              enEncode = ENCODE_BUTT;

    if (enStreamType > STREAM_TYPE_SNAP1 || s32Chn > VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            u32EncPath      = m_stVencInfo.astPriChn[s32Chn].u32EncPath;
            enEncode        = m_stVencInfo.enMainEncode;
			stStreamBufInfo = &(m_stVencInfo.astPriChn[s32Chn].stStreamBufInfo);
            stBitStream     = &(m_stVencInfo.astPriChn[s32Chn].stBitStream);
            pu32VirAddr     = m_stVencInfo.astPriChn[s32Chn].pu32VirAddr;
            break;

        case STREAM_TYPE_SEC:
            u32EncPath      = m_stVencInfo.astSecChn[s32Chn].u32EncPath;
            enEncode        = m_stVencInfo.enSubEncode;
			stStreamBufInfo = &(m_stVencInfo.astSecChn[s32Chn].stStreamBufInfo);
            stBitStream     = &(m_stVencInfo.astSecChn[s32Chn].stBitStream);
            pu32VirAddr     = m_stVencInfo.astSecChn[s32Chn].pu32VirAddr;
            break;

        case STREAM_TYPE_SNAP0:
            u32EncPath      = m_stVencInfo.astSnap0Chn[s32Chn].u32EncPath;
            stStreamBufInfo = &(m_stVencInfo.astSnap0Chn[s32Chn].stStreamBufInfo);
            stBitStream     = &(m_stVencInfo.astSnap0Chn[s32Chn].stBitStream);
            pu32VirAddr     = m_stVencInfo.astSnap0Chn[s32Chn].pu32VirAddr;
            break;

        case STREAM_TYPE_SNAP1:
            u32EncPath      = m_stVencInfo.astSnap1Chn[s32Chn].u32EncPath;
            stStreamBufInfo = &(m_stVencInfo.astSnap1Chn[s32Chn].stStreamBufInfo);
            stBitStream     = &(m_stVencInfo.astSnap1Chn[s32Chn].stBitStream);
            pu32VirAddr     = m_stVencInfo.astSnap1Chn[s32Chn].pu32VirAddr;
            break;

        default: 
            return ERR_ILLEGAL_PARAM;
    }

    // 指定要监听的PathID, 这个函数被独立调用，所以不需要遍历所有的path，只需要每次进来监听对应的path即可
    stPollList[0].path_id = u32EncPath;

    // 读取的最大数量视频帧
    for (size_t i = 0; i < s32MaxFrmNum; i++)
    {
        if (HD_OK == hd_videoenc_poll_list(stPollList, 1, -1))
        {
            // 指定通道的数据可读,可获取一帧压缩好的数据码流
            if (TRUE == stPollList[0].revent.event)
            {
                eRet = hd_videoenc_pull_out_buf(u32EncPath, stBitStream, 0);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_pull_out_buf fail=%d\n", eRet);
                }else
                {
                    // stFrame.u64PTS       = stBitStream->timestamp;
                    mpp_sys_GetPts(&stFrame.u32Sep);
                    mpp_sys_GetPts(&stFrame.u64PTS);
                    stFrame.u32PackCount = stBitStream->pack_num;

                    // print_level(SV_INFO, " stBitStream->pack_num = %d \n", stBitStream->pack_num);
                    for (size_t j = 0; j < stBitStream->pack_num; j++)
                    {
                        ptr = (UINT8*)(pu32VirAddr + stBitStream->video_pack[j].phy_addr - stStreamBufInfo->buf_info.phy_addr);
                        len = stBitStream->video_pack[j].size;
                        stFrame.astPacks[j].enPackType = stBitStream->frame_type;
                        stFrame.astPacks[j].pu8Addr    = ptr;
                        stFrame.astPacks[j].u32Len     = len;
                    }

                    // 使用压缩的码流, 接着释放这部分内存空间
                    if (NULL != m_stVencInfo.pfDataCallback)
                    {
                        mpp_sys_CallbackLock();
                        m_stVencInfo.pfDataCallback(s32Chn, enStreamType, &stFrame, 0);           
                        mpp_sys_CallbackUnlock();
                    }
                }

                eRet = hd_videoenc_release_out_buf(u32EncPath, stBitStream);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_release_out_buf fail=%d\n", eRet);
                }
            }
            
        }
    }
    // sleep_ms(10);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建H264编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIChnNum), 同一个码流的通道序号
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_EXIST - 通道已存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : H264没有分配SNAP的功能
 *****************************************************************************/
sint32 mpp_venc_H264CreateChn(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr)
{
    HD_RESULT               eRet = HD_OK;
    HD_PATH_ID              u32EncPath;
    HD_VIDEOENC_PATH_CONFIG stVideoencPathConfig;
    HD_VIDEOENC_IN          stVideoencInParam;
    HD_VIDEOENC_OUT         stVideoencOutParam;
    HD_H26XENC_RATE_CONTROL stRcCtrl;
    UINT8                   u8VencChn = 0;
    // 获取摄像头数量
    UINT8                   u8CameraNums = 0;

    sint32                  s32Ret = 0;

    u8CameraNums = m_stVencInfo.u32ChnNum;
    u8CameraNums = 2;

    // ENC通道设置
    stVideoencPathConfig.max_mem.codec_type = HD_CODEC_TYPE_H264;
    stVideoencPathConfig.max_mem.max_dim.w  = pstVencAttr->u32PicWidth;
    stVideoencPathConfig.max_mem.max_dim.h  = pstVencAttr->u32PicHeight;
    stVideoencPathConfig.max_mem.bitrate    = (1 * 1024 * 1024);
    stVideoencPathConfig.max_mem.enc_buf_ms = 10000;
    stVideoencPathConfig.max_mem.svc_layer  = HD_SVC_4X;
    stVideoencPathConfig.max_mem.ltr        = TRUE;
    stVideoencPathConfig.max_mem.rotate     = FALSE;
    stVideoencPathConfig.max_mem.source_output = FALSE;
    stVideoencPathConfig.isp_id             = 0;
    // IN输入设置
    stVideoencInParam.dir     = HD_VIDEO_DIR_NONE;
    stVideoencInParam.pxl_fmt = HD_VIDEO_PXLFMT_YUV420;
    stVideoencInParam.dim.w   = pstVencAttr->u32PicWidth;
    stVideoencInParam.dim.h   = pstVencAttr->u32PicHeight;
    stVideoencInParam.frc     = HD_VIDEO_FRC_RATIO(1, 1);
    // H264编码
    stVideoencOutParam.codec_type           = HD_CODEC_TYPE_H264;
    stVideoencOutParam.h26x.profile         = HD_H264E_HIGH_PROFILE;
    stVideoencOutParam.h26x.level_idc       = HD_H264E_LEVEL_5_1;
    stVideoencOutParam.h26x.gop_num         = 15;
    stVideoencOutParam.h26x.chrm_qp_idx     = 0;
    stVideoencOutParam.h26x.sec_chrm_qp_idx = 0;
    stVideoencOutParam.h26x.ltr_interval    = 0;
    stVideoencOutParam.h26x.ltr_pre_ref     = 0;
    stVideoencOutParam.h26x.gray_en         = 0;
    stVideoencOutParam.h26x.source_output   = 0;
    stVideoencOutParam.h26x.svc_layer       = HD_SVC_DISABLE;
    stVideoencOutParam.h26x.entropy_mode    = HD_H264E_CABAC_CODING;
    // 码率控制
    switch (pstVencAttr->enRcMode)
    {
        case HD_RC_MODE_CBR:
            stRcCtrl.rc_mode             = HD_RC_MODE_CBR;
            stRcCtrl.cbr.bitrate         = 1*1024*1024;
            stRcCtrl.cbr.frame_rate_base = 30;
            stRcCtrl.cbr.frame_rate_incr = 1;
            stRcCtrl.cbr.init_i_qp       = 26;
            stRcCtrl.cbr.min_i_qp        = 10;
            stRcCtrl.cbr.max_i_qp        = 45;
            stRcCtrl.cbr.init_p_qp       = 26;
            stRcCtrl.cbr.min_p_qp        = 10;
            stRcCtrl.cbr.max_p_qp        = 45;
            stRcCtrl.cbr.static_time     = 0;
            stRcCtrl.cbr.ip_weight       = 0;
            break;
        case HD_RC_MODE_VBR:
            stRcCtrl.rc_mode             = HD_RC_MODE_VBR;
            stRcCtrl.vbr.bitrate         = REC_FIX_TBR(pstVencAttr->u32PicWidth, pstVencAttr->u32PicHeight);
            stRcCtrl.vbr.frame_rate_base = 30;
            stRcCtrl.vbr.frame_rate_incr = 1;
            stRcCtrl.vbr.init_i_qp       = 26;
            stRcCtrl.vbr.min_i_qp        = 10;
            stRcCtrl.vbr.max_i_qp        = 45;
            stRcCtrl.vbr.init_p_qp       = 26;
            stRcCtrl.vbr.min_p_qp        = 10;
            stRcCtrl.vbr.max_p_qp        = 45;
            stRcCtrl.vbr.static_time     = 4;
            stRcCtrl.vbr.ip_weight       = 0;
            break;
        case HD_RC_MODE_FIX_QP:
            stRcCtrl.rc_mode               = HD_RC_MODE_FIX_QP;
            stRcCtrl.fixqp.fix_i_qp        = 26;
            stRcCtrl.fixqp.fix_p_qp        = 10;
            stRcCtrl.fixqp.frame_rate_base = 30;
            stRcCtrl.fixqp.frame_rate_incr = 1;
            break;
        case HD_RC_MODE_EVBR:
            stRcCtrl.rc_mode              = HD_RC_MODE_EVBR;
            stRcCtrl.evbr.bitrate         = REC_FIX_TBR(pstVencAttr->u32PicWidth, pstVencAttr->u32PicHeight);
            stRcCtrl.evbr.frame_rate_base = 30;
            stRcCtrl.evbr.frame_rate_incr = 1;
            stRcCtrl.evbr.init_i_qp       = 26;
            stRcCtrl.evbr.min_i_qp        = 10;
            stRcCtrl.evbr.max_i_qp        = 45;
            stRcCtrl.evbr.init_p_qp       = 26;
            stRcCtrl.evbr.min_p_qp        = 10;
            stRcCtrl.evbr.max_p_qp        = 45;
            stRcCtrl.evbr.ip_weight       = 0;
            stRcCtrl.evbr.key_p_period    = 60;
            stRcCtrl.evbr.kp_weight       = 0;
            break;
        case HD_RC_MODE_CVBR:
            stRcCtrl.rc_mode = HD_RC_MODE_CVBR;
            break;
        default:
            break;
    }
    // 区分编码码流-主码流-子码流等, m_stVencInfo.u32ChnNum是主码流通道数量
    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:   // 主码流[0, 1]
            if (m_stVencInfo.astPriChn[s32Chn].bCreated)
            {
                return ERR_EXIST;
            }
            u8VencChn = s32Chn; //[0, 1]
            // 主码流输入端口为0，打开主码流0-s32Chn的path
            eRet = hd_videoenc_open(HD_VIDEOENC_IN(0, u8VencChn), HD_VIDEOENC_OUT(0, u8VencChn), &(m_stVencInfo.astPriChn[s32Chn].u32EncPath));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_open s32Chn fail=%d\n", eRet);
                return SV_FAILURE;
            }
            if (0 == s32Chn)
            {
                eRet = hd_videoenc_open(HD_VIDEOENC_IN(0, u8VencChn), HD_STAMP(u8VencChn), &(m_stVencInfo.astPriChn[s32Chn].u32StampPath));
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_open stamp s32Chn fail=%d\n", eRet);
                    return SV_FAILURE;
                }
                eRet = hd_videoenc_open(HD_VIDEOENC_IN(0, u8VencChn), HD_STAMP_EX(u8VencChn), &(m_stVencInfo.astPriChn[s32Chn].u32EXStampPath));
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_open stamp s32Chn fail=%d\n", eRet);
                    return SV_FAILURE;
                }
            }
            
            // astPriChn对应每个传感器的视频流, 用s32Chn来表示序号
            u32EncPath = m_stVencInfo.astPriChn[s32Chn].u32EncPath;
            break;
        case STREAM_TYPE_SEC:   // 子码流
            if (m_stVencInfo.astSecChn[s32Chn].bCreated)
            {
                return ERR_EXIST;
            }
            // 实际m_stVencInfo.u32ChnNum是摄像头传感器数量
            u8VencChn = 1*u8CameraNums + s32Chn; //[2, 3]
             // 子码流输入端口为0，打开主码流u32ChnNum-u8VencChn的path
            eRet = hd_videoenc_open(HD_VIDEOENC_IN(0, u8VencChn), HD_VIDEOENC_OUT(0, u8VencChn), &(m_stVencInfo.astSecChn[s32Chn].u32EncPath));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_open u8VencChn fail=%d\n", eRet);
                return SV_FAILURE;
            }
            u32EncPath = m_stVencInfo.astSecChn[s32Chn].u32EncPath;
            break;
        default:
            break;
    }

    // set-1
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_PATH_CONFIG, &stVideoencPathConfig);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_PATH_CONFIG fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // set-2
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_IN, &stVideoencInParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_IN fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // set-3
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_OUT_ENC_PARAM, &stVideoencOutParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_OUT_ENC_PARAM fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // set-4
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_OUT_RATE_CONTROL, &stRcCtrl);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_OUT_RATE_CONTROL fail=%d\n", eRet);
        return SV_FAILURE;
    }

    // 暂时在这里进行OSG的配置
    if (STREAM_TYPE_PRI == enStreamType && 0 == s32Chn)
    {
        mpp_venc_set_osg();
    }
    

    // 通道绑定
    if(m_stVencInfo.enVideoMode != VIDEO_MODE_PAL && m_stVencInfo.enVideoMode != VIDEO_MODE_NTSC)
    {
        //指定VPSS和VENC的端口        camera_index vpss output  venc input 
        s32Ret = mpp_sys_VpssVencBind(s32Chn, enStreamType, u8VencChn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_VpssVencBind failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        print_level(SV_INFO, "H264 mpp_sys_VpssVencBind OK...s32Chn=%d, enStreamType=%d, u8VencChn=%d\n", s32Chn, enStreamType, u8VencChn);
    }

    if (STREAM_TYPE_PRI == enStreamType)
    {
        m_stVencInfo.astPriChn[s32Chn].bCreated   = SV_TRUE;
        m_stVencInfo.astPriChn[s32Chn].bEnable    = SV_FALSE;
    }
    else
    {
        m_stVencInfo.astSecChn[s32Chn].bCreated   = SV_TRUE;
        m_stVencInfo.astSecChn[s32Chn].bEnable    = SV_FALSE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁H264编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 销毁前必需将通道停止编码并对它进行反注册脱离于通道组
 *****************************************************************************/
sint32 mpp_venc_H264DestroyChn(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    HD_RESULT   eRet = HD_OK;
    HD_PATH_ID  u32EncPath;
    UINT8       u8VencChn = 0;
    // 获取摄像头数量
    UINT8                   u8CameraNums = 0;

    sint32                  s32Ret = 0;

    u8CameraNums = m_stVencInfo.u32ChnNum;
    // u8CameraNums = 2;

    if (s32Chn < 0 || s32Chn >= u8CameraNums)
    {
        return ERR_ILLEGAL_PARAM;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            if (!m_stVencInfo.astPriChn[s32Chn].bCreated)
            {
                return ERR_UNEXIST;
            }
            u8VencChn  = s32Chn;
            u32EncPath = m_stVencInfo.astPriChn[s32Chn].u32EncPath;
            break;

        case STREAM_TYPE_SEC:
            if (!m_stVencInfo.astSecChn[s32Chn].bCreated)
            {
                return ERR_UNEXIST;
            }
            u8VencChn  = 1*u8CameraNums + s32Chn;
            u32EncPath = m_stVencInfo.astSecChn[s32Chn].u32EncPath;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    // 1
    eRet = hd_videoenc_close(u32EncPath);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_close fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // 2
    if (STREAM_TYPE_PRI == enStreamType && 0 == s32Chn)
    {
        eRet = hd_videoenc_close(m_stVencInfo.astPriChn[0].u32StampPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoenc_close fail=%d\n", eRet);
            return SV_FAILURE;
        }
        eRet = hd_videoenc_close(m_stVencInfo.astPriChn[0].u32EXStampPath);
        if (HD_OK != eRet)
        {
            print_level(SV_ERROR, "hd_videoenc_close fail=%d\n", eRet);
            return SV_FAILURE;
        }
        destoryDeviceInfoForImage();
    }
    
    if (STREAM_TYPE_PRI == enStreamType)
    {
        m_stVencInfo.astPriChn[s32Chn].bCreated = SV_FALSE;
    }
    else
    {
        m_stVencInfo.astSecChn[s32Chn].bCreated = SV_FALSE;
    }

    s32Ret = mpp_sys_VpssVencUnBind(s32Chn, enStreamType, u8VencChn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_VpssVencUnBind VeChnId=%d failed! [err=%#x]\n", u8VencChn, s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建MJPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIChnNum)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_EXIST - 通道已存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_MJpegCreateChn(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr)
{

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁MJPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 销毁前必需将通道停止编码并对它进行反注册脱离于通道组
 *****************************************************************************/
sint32 mpp_venc_MJpegDestroyChn(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 创建JPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_SNAP0, STREAM_TYPE_SNAP1)
             s32Chn --- 编码通道号 [0, VIChnNum)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_MEDIA_NOT_SURPPORT - 操作不支持
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : JPEG对应于SNAP抓图功能
 *****************************************************************************/
sint32 mpp_venc_JpegCreateChn(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_JPEG_S *pstVencAttr)
{
    UINT8                   u8VencChn = 0;
    HD_RESULT               eRet = HD_OK;
    HD_PATH_ID              u32EncPath;
    HD_VIDEOENC_PATH_CONFIG stVideoencPathConfig;
    HD_VIDEOENC_IN          stVideoencInParam;
    HD_VIDEOENC_OUT         stVideoencOutParam;
    HD_H26XENC_RATE_CONTROL stRcCtrl;
    // 获取摄像头数量
    UINT8                   u8CameraNums = 0;

    sint32                  s32Ret = 0;

    u8CameraNums = m_stVencInfo.u32ChnNum;
    u8CameraNums = 2;
    
    if (s32Chn < 0 || s32Chn >= m_stVencInfo.u32ChnNum)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstVencAttr)
    {
        return ERR_NULL_PTR;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_SNAP0:
            if (m_stVencInfo.astSnap0Chn[s32Chn].bCreated)
            {
                return ERR_EXIST;
            }
            u8VencChn = 2 * u8CameraNums + s32Chn;
            // 抓图码流输入端口为0，打开主码流u8EncChn的path
            eRet = hd_videoenc_open(HD_VIDEOENC_IN(0, u8VencChn), HD_VIDEOENC_OUT(0, u8VencChn), &(m_stVencInfo.astSnap0Chn[s32Chn].u32EncPath));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_open s32Chn fail=%d\n", eRet);
                return SV_FAILURE;
            }
            u32EncPath = m_stVencInfo.astSnap0Chn[s32Chn].u32EncPath;

            break;

        case STREAM_TYPE_SNAP1:
            if (m_stVencInfo.astSnap1Chn[s32Chn].bCreated)
            {
                return ERR_EXIST;
            }
            u8VencChn = 3 * u8CameraNums + s32Chn;
            // 抓图码流输入端口为0，打开主码流u8EncChn的path
            eRet = hd_videoenc_open(HD_VIDEOENC_IN(0, u8VencChn), HD_VIDEOENC_OUT(0, u8VencChn), &(m_stVencInfo.astSnap1Chn[s32Chn].u32EncPath));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_open s32Chn fail=%d\n", eRet);
                return SV_FAILURE;
            }
            u32EncPath = m_stVencInfo.astSnap1Chn[s32Chn].u32EncPath;
            break;

        default : 
            return ERR_ILLEGAL_PARAM;
    }

    // pyl
    stVideoencPathConfig.max_mem.codec_type = HD_CODEC_TYPE_JPEG;
    stVideoencPathConfig.max_mem.max_dim.w  = 1080;
    stVideoencPathConfig.max_mem.max_dim.h  = 720;
    stVideoencPathConfig.max_mem.bitrate    = 1*1080*720;
    stVideoencPathConfig.max_mem.enc_buf_ms = 2000;
    stVideoencPathConfig.max_mem.svc_layer  = HD_SVC_4X;
    stVideoencPathConfig.max_mem.ltr        = TRUE;
    stVideoencPathConfig.max_mem.rotate     = FALSE;
    stVideoencPathConfig.max_mem.source_output = FALSE;
    stVideoencPathConfig.isp_id             = 0;
    // IN输入设置
    stVideoencInParam.dir                   = HD_VIDEO_DIR_NONE;
    stVideoencInParam.pxl_fmt               = HD_VIDEO_PXLFMT_YUV420;
    stVideoencInParam.dim.w                 = 1080;
    stVideoencInParam.dim.h                 = 720;
    stVideoencInParam.frc                   = HD_VIDEO_FRC_RATIO(1, 1);
    // JPEG编码-OUT
    stVideoencOutParam.codec_type             = HD_CODEC_TYPE_JPEG;
    stVideoencOutParam.jpeg.retstart_interval = 0;
    stVideoencOutParam.jpeg.image_quality     = 70;

    // 1
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_PATH_CONFIG, &stVideoencPathConfig);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_PATH_CONFIG fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // 2
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_IN, &stVideoencInParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_IN fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // 3
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_OUT_ENC_PARAM, &stVideoencOutParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_OUT_ENC_PARAM fail=%d\n", eRet);
        return SV_FAILURE;
    }

    // 绑定VPROC的输出通道
    if(m_stVencInfo.enVideoMode != VIDEO_MODE_PAL && m_stVencInfo.enVideoMode != VIDEO_MODE_NTSC)
    {
        s32Ret = mpp_sys_VpssVencBind(s32Chn, enStreamType, u8VencChn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_VpssVencBind failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        print_level(SV_INFO, "SNAP0 mpp_sys_VpssVencBind OK...s32Chn=%d, enStreamType=%d, u8VencChn=%d\n", s32Chn, enStreamType, u8VencChn);
    }

    if (STREAM_TYPE_SNAP0 == enStreamType)
    {
        m_stVencInfo.astSnap0Chn[s32Chn].bCreated   = SV_TRUE;
        m_stVencInfo.astSnap0Chn[s32Chn].bEnable    = SV_FALSE;
    }
    else
    {
        m_stVencInfo.astSnap1Chn[s32Chn].bCreated   = SV_TRUE;
        m_stVencInfo.astSnap1Chn[s32Chn].bEnable    = SV_FALSE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁JPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_SNAP0, STREAM_TYPE_SNAP1)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_JpegDestroyChn(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    HD_RESULT   eRet = HD_OK;
    HD_PATH_ID  u32EncPath;
    UINT8       u8VencChn = 0;
    // 获取摄像头数量
    UINT8                   u8CameraNums = 0;

    sint32                  s32Ret = 0;

    u8CameraNums = m_stVencInfo.u32ChnNum;
    u8CameraNums = 2;

    if (s32Chn < 0 || s32Chn >= u8CameraNums)
    {
        return ERR_ILLEGAL_PARAM;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_SNAP0:
            u8VencChn = 2 * u8CameraNums + s32Chn;
            u32EncPath = m_stVencInfo.astSnap0Chn[s32Chn].u32EncPath;
            break;

        case STREAM_TYPE_SNAP1:
            u8VencChn = 3 * u8CameraNums + s32Chn;
            u32EncPath = m_stVencInfo.astSnap1Chn[s32Chn].u32EncPath;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    eRet = hd_videoenc_close(u32EncPath);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_close fail=%d\n", eRet);
        return SV_FAILURE;
    }

    if (STREAM_TYPE_SNAP0 == enStreamType)
    {
        m_stVencInfo.astSnap0Chn[s32Chn].bCreated = SV_FALSE;
    }
    else
    {
        m_stVencInfo.astSnap1Chn[s32Chn].bCreated = SV_FALSE;
    }

    s32Ret = mpp_sys_VpssVencUnBind(s32Chn, enStreamType, u8VencChn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_VpssVencUnBind VeChnId=%d failed! [err=%#x]\n", u8VencChn, s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将编码通道注册到编码通道组中
 * 输入参数: VeGroup --- 编码通道组
             enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 注册前必需保证编码通道组和编码通道已创建
 *****************************************************************************/
sint32 mpp_venc_RegisterChn(sint32 VeGroup, STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将编码通道反注册脱离编码通道组
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 注册前必需保证编码通道组和编码通道已创建
 *****************************************************************************/
sint32 mpp_venc_UnRegisterChn(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 使能彩色转灰色处理
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_Color2GreyEnable()
{
#if 0
    sint32 s32Ret = 0, i;
    VENC_CHN VeChn = 0;
    VENC_CHN_PARAM_S   stVencChnParam;
    SV_BOOL abCreate[3] = {0};

    abCreate[0] = m_stVencInfo.astPriChn[0].bCreated;
    abCreate[1] = m_stVencInfo.astSecChn[0].bCreated;
    abCreate[2] = m_stVencInfo.astSnap0Chn[0].bCreated;
    for (i = 0; i < 3; i++)
    {
        if (!abCreate[i])
        {
            continue;
        }
        
        VeChn = i;
        s32Ret = HI_MPI_VENC_GetChnParam(VeChn, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_GetChnParam failed! [err=%#x]\n", s32Ret);
        }
        
        stVencChnParam.bColor2Grey = HI_TRUE;
        s32Ret = HI_MPI_VENC_SetChnParam(VeChn, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_SetChnParam failed. [err=%#x]\n", s32Ret);
        }
    }

    for (i = 0; i < 2; i++)
    {
        if (!abCreate[i])
        {
            continue;
        }
        
        VeChn = i;
        s32Ret = HI_MPI_VENC_RequestIDR(VeChn, HI_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_RequestIDR failed. [err=%#x]\n", s32Ret);
        }
    }

    m_stVencInfo.bColor2Grey = SV_TRUE;
#endif    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 禁止彩色转灰色处理
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_Color2GreyDisable()
{
#if 0
    sint32 s32Ret = 0, i;
    VENC_CHN VeChn = 0;
    VENC_CHN_PARAM_S   stVencChnParam;
    SV_BOOL abCreate[3] = {0};

    abCreate[0] = m_stVencInfo.astPriChn[0].bCreated;
    abCreate[1] = m_stVencInfo.astSecChn[0].bCreated;
    abCreate[2] = m_stVencInfo.astSnap0Chn[0].bCreated;

    for (i = 0; i < 3; i++)
    {
        if (!abCreate[i])
        {
            continue;
        }
        
        VeChn = i;
        s32Ret = HI_MPI_VENC_GetChnParam(VeChn, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_GetChnParam failed! [err=%#x]\n", s32Ret);
        }
        
        stVencChnParam.bColor2Grey = HI_FALSE;
        s32Ret = HI_MPI_VENC_SetChnParam(VeChn, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_SetChnParam failed. [err=%#x]\n", s32Ret);
        }
    }

    m_stVencInfo.bColor2Grey = SV_FALSE;
#endif    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 请求立即生成一个H264关键帧
 * 输入参数: s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
             enStreamType --- 码流类型 [STREAM_TYPE_PRI, STREAM_TYPE_SEC]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 使用Vendor接口
 *****************************************************************************/
sint32 mpp_venc_RequestIFrame(sint32 s32Chn, STREAM_TYPE_E enStreamType)
{
    HD_RESULT  eRet = HD_OK;
    HD_PATH_ID stEncPath;
    VENDOR_VIDEOENC_REQ_TARGET_I stIFrameReq;

    if (s32Chn >= VIODE_MAX_CHN || STREAM_TYPE_PRI != enStreamType || STREAM_TYPE_SEC != enStreamType)
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    if (STREAM_TYPE_PRI == enStreamType && !m_stVencInfo.astPriChn[s32Chn].bCreated
        || STREAM_TYPE_SEC == enStreamType && !m_stVencInfo.astSecChn[s32Chn].bCreated)
    {
        return ERR_UNEXIST;
    }

    stIFrameReq.enable = TRUE;
    stIFrameReq.target_timestamp = mpp_enc_GetSysTimeByUsec();   // 需要获取系统时间

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            stEncPath = m_stVencInfo.astPriChn[s32Chn].u32EncPath;
            break;

        case STREAM_TYPE_SEC:
            stEncPath = m_stVencInfo.astSecChn[s32Chn].u32EncPath;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    eRet = vendor_videoenc_set(stEncPath, VENDOR_VIDEOENC_PARAM_OUT_REQ_TARGET_I, &stIFrameReq);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "vendor_videoenc_set VENDOR_VIDEOENC_PARAM_OUT_REQ_TARGET_I fail=%d\n", eRet);
        return SV_FAILURE;
    }
 
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 使能某编码通道进行编码
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : start编码通道
 *****************************************************************************/
sint32 mpp_venc_EnableChn(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    HD_RESULT eRet = HD_OK;
    
    if (enStreamType > STREAM_TYPE_SNAP1 || s32Chn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    print_level(SV_INFO, " mpp_venc_EnableChn enStreamType=%d... \n", enStreamType);
    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            if (!m_stVencInfo.astPriChn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            pthread_mutex_lock(&m_stVencInfo.mutexLock);
            eRet = hd_videoenc_start(m_stVencInfo.astPriChn[s32Chn].u32EncPath);
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_start astPriChn fail=%d\n", eRet);
                return SV_FAILURE;
            }
            if (0 == s32Chn)
            {           
                eRet = hd_videoenc_start(m_stVencInfo.astPriChn[0].u32StampPath);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_start astPriChn fail=%d\n", eRet);
                    return SV_FAILURE;
                }
                eRet = hd_videoenc_start(m_stVencInfo.astPriChn[0].u32EXStampPath);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_start astPriChn fail=%d\n", eRet);
                    return SV_FAILURE;
                }
            }

            eRet = hd_videoenc_get(m_stVencInfo.astPriChn[s32Chn].u32EncPath, HD_VIDEOENC_PARAM_BUFINFO, 
                                                                                &(m_stVencInfo.astPriChn[s32Chn].stStreamBufInfo));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_BUFINFO fail=%d\n", eRet);
                return SV_FAILURE;
            }

            m_stVencInfo.astPriChn[s32Chn].pu32VirAddr = (UINTPTR)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_CACHE, \
                                                                        m_stVencInfo.astPriChn[s32Chn].stStreamBufInfo.buf_info.phy_addr, \
                                                                        m_stVencInfo.astPriChn[s32Chn].stStreamBufInfo.buf_info.buf_size);
            if (!m_stVencInfo.astPriChn[s32Chn].pu32VirAddr)
            {
                print_level(SV_ERROR, " hd_common_mem_mmap fail... \n");
                return SV_FAILURE;
            }
            pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            break;

        case STREAM_TYPE_SEC:
            if (!m_stVencInfo.astSecChn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            pthread_mutex_lock(&m_stVencInfo.mutexLock);
            eRet = hd_videoenc_start(m_stVencInfo.astSecChn[s32Chn].u32EncPath);
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_start astSecChn fail=%d\n", eRet);
                return SV_FAILURE;
            }

            eRet = hd_videoenc_get(m_stVencInfo.astSecChn[s32Chn].u32EncPath, HD_VIDEOENC_PARAM_BUFINFO, 
                                                                                &(m_stVencInfo.astSecChn[s32Chn].stStreamBufInfo));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_BUFINFO fail=%d\n", eRet);
                return SV_FAILURE;
            }

            m_stVencInfo.astSecChn[s32Chn].pu32VirAddr = (UINTPTR)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_CACHE, \
                                                                        m_stVencInfo.astSecChn[s32Chn].stStreamBufInfo.buf_info.phy_addr, \
                                                                        m_stVencInfo.astSecChn[s32Chn].stStreamBufInfo.buf_info.buf_size);
            if (!m_stVencInfo.astSecChn[s32Chn].pu32VirAddr)
            {
                print_level(SV_ERROR, " hd_common_mem_mmap fail... \n");
                return SV_FAILURE;
            }
            pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            break;

        case STREAM_TYPE_SNAP0:
            if (!m_stVencInfo.astSnap0Chn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            pthread_mutex_lock(&m_stVencInfo.mutexLock);
            eRet = hd_videoenc_start(m_stVencInfo.astSnap0Chn[s32Chn].u32EncPath);
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_start astSnap0Chn fail=%d\n", eRet);
                return SV_FAILURE;
            }
           
            eRet = hd_videoenc_get(m_stVencInfo.astSnap0Chn[s32Chn].u32EncPath, HD_VIDEOENC_PARAM_BUFINFO, 
                                                                                &(m_stVencInfo.astSnap0Chn[s32Chn].stStreamBufInfo));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_BUFINFO fail=%d\n", eRet);
                return SV_FAILURE;
            }

            m_stVencInfo.astSnap0Chn[s32Chn].pu32VirAddr = (UINTPTR)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_CACHE, \
                                                                        m_stVencInfo.astSnap0Chn[s32Chn].stStreamBufInfo.buf_info.phy_addr, \
                                                                        m_stVencInfo.astSnap0Chn[s32Chn].stStreamBufInfo.buf_info.buf_size);
            if (!m_stVencInfo.astSnap0Chn[s32Chn].pu32VirAddr)
            {
                print_level(SV_ERROR, " hd_common_mem_mmap fail... \n");
                return SV_FAILURE;
            }
            pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            break;

        case STREAM_TYPE_SNAP1:
            if (!m_stVencInfo.astSnap1Chn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            pthread_mutex_lock(&m_stVencInfo.mutexLock);
            eRet = hd_videoenc_start(m_stVencInfo.astSnap1Chn[s32Chn].u32EncPath);
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_start astSnap1Chn fail=%d\n", eRet);
                return SV_FAILURE;
            }

            eRet = hd_videoenc_get(m_stVencInfo.astSnap1Chn[s32Chn].u32EncPath, HD_VIDEOENC_PARAM_BUFINFO, 
                                                                                &(m_stVencInfo.astSnap1Chn[s32Chn].stStreamBufInfo));
            if (HD_OK != eRet)
            {
                print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_BUFINFO fail=%d\n", eRet);
                return SV_FAILURE;
            }

            m_stVencInfo.astSnap1Chn[s32Chn].pu32VirAddr = (UINTPTR)hd_common_mem_mmap(HD_COMMON_MEM_MEM_TYPE_CACHE, \
                                                                        m_stVencInfo.astSnap1Chn[s32Chn].stStreamBufInfo.buf_info.phy_addr, \
                                                                        m_stVencInfo.astSnap1Chn[s32Chn].stStreamBufInfo.buf_info.buf_size);
            if (!m_stVencInfo.astSnap1Chn[s32Chn].pu32VirAddr)
            {
                print_level(SV_ERROR, " hd_common_mem_mmap fail... \n");
                return SV_FAILURE;
            }
            pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            break;

        default: return ERR_ILLEGAL_PARAM;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 禁止某编码通道编码
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : stop编码通道
 *****************************************************************************/
sint32 mpp_venc_DisableChn(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    HD_RESULT eRet = HD_OK;
    
    if (enStreamType > STREAM_TYPE_SNAP1 || s32Chn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    print_level(SV_INFO, "mpp_venc_DisableChn ...enStreamType=%d\n", enStreamType);

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            if (m_stVencInfo.astPriChn[s32Chn].bCreated)
            {
                pthread_mutex_lock(&m_stVencInfo.mutexLock);
                eRet = hd_videoenc_stop(m_stVencInfo.astPriChn[s32Chn].u32EncPath);
                if (HD_OK != eRet)
                {
                    pthread_mutex_unlock(&m_stVencInfo.mutexLock);
                    print_level(SV_ERROR, "hd_videoenc_stop astPriChn fail=%d\n", eRet);
                    return SV_FAILURE;
                }
                if (0 == s32Chn)
                {
                    eRet = hd_videoenc_stop(m_stVencInfo.astPriChn[s32Chn].u32StampPath);
                    if (HD_OK != eRet)
                    {
                        pthread_mutex_unlock(&m_stVencInfo.mutexLock);
                        print_level(SV_ERROR, "hd_videoenc_stop astPriChn fail=%d\n", eRet);
                        return SV_FAILURE;
                    }
                    eRet = hd_videoenc_stop(m_stVencInfo.astPriChn[s32Chn].u32EXStampPath);
                    if (HD_OK != eRet)
                    {
                        pthread_mutex_unlock(&m_stVencInfo.mutexLock);
                        print_level(SV_ERROR, "hd_videoenc_stop astPriChn fail=%d\n", eRet);
                        return SV_FAILURE;
                    }
                    if (m_stVencInfo.astPriChn[0].stStampBlk)
                    {
                        eRet = hd_common_mem_release_block((void*)m_stVencInfo.astPriChn[0].stStampBlk);
                        if (HD_OK != eRet)
                        {
                            print_level(SV_ERROR, "hd_common_mem_release_block fail=%d\n", eRet);
                        }
                    }
                }

                eRet = hd_common_mem_munmap((void*)m_stVencInfo.astPriChn[s32Chn].pu32VirAddr, m_stVencInfo.astPriChn[s32Chn].stStreamBufInfo.buf_info.buf_size);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_release_out_buf fail=%d\n", eRet);
                }
                
                pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            }
            break;

        case STREAM_TYPE_SEC:
            if (m_stVencInfo.astSecChn[s32Chn].bCreated)
            {
                pthread_mutex_lock(&m_stVencInfo.mutexLock);
                eRet = hd_videoenc_stop(m_stVencInfo.astSecChn[s32Chn].u32EncPath);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_stop astSecChn fail=%d\n", eRet);
                    return SV_FAILURE;
                }

                eRet = hd_common_mem_munmap((void*)m_stVencInfo.astSecChn[s32Chn].pu32VirAddr, m_stVencInfo.astSecChn[s32Chn].stStreamBufInfo.buf_info.buf_size);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_release_out_buf fail=%d\n", eRet);
                }
                pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            }
            break;

        case STREAM_TYPE_SNAP0:
            if (m_stVencInfo.astSnap0Chn[s32Chn].bCreated)
            {
                pthread_mutex_lock(&m_stVencInfo.mutexLock);
                eRet = hd_videoenc_stop(m_stVencInfo.astSnap0Chn[s32Chn].u32EncPath);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_stop astSnap0Chn fail=%d\n", eRet);
                    return SV_FAILURE;
                }

                eRet = hd_common_mem_munmap((void*)m_stVencInfo.astSnap0Chn[s32Chn].pu32VirAddr, m_stVencInfo.astSnap0Chn[s32Chn].stStreamBufInfo.buf_info.buf_size);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_release_out_buf fail=%d\n", eRet);
                }
                pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            }
            break;

        case STREAM_TYPE_SNAP1:
            if (m_stVencInfo.astSnap1Chn[s32Chn].bCreated)
            {
                pthread_mutex_lock(&m_stVencInfo.mutexLock);
                eRet = hd_videoenc_stop(m_stVencInfo.astSnap1Chn[s32Chn].u32EncPath);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_stop astSnap1Chn fail=%d\n", eRet);
                    return SV_FAILURE;
                }

                eRet = hd_common_mem_munmap((void*)m_stVencInfo.astSnap1Chn[s32Chn].pu32VirAddr, m_stVencInfo.astSnap1Chn[s32Chn].stStreamBufInfo.buf_info.buf_size);
                if (HD_OK != eRet)
                {
                    print_level(SV_ERROR, "hd_videoenc_release_out_buf fail=%d\n", eRet);
                }
                pthread_mutex_unlock(&m_stVencInfo.mutexLock);
            }
            break;

        default: return ERR_ILLEGAL_PARAM;
    }
 
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 开启某路通道的快速抓拍功能
 * 输入参数: s32ChnMask --- 通道掩码
             u32Quality ---- 图片质量级别 [0-5]: 数值超高质量越好
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 可根据掩码开启多路通道
 *****************************************************************************/
sint32 mpp_venc_FastSnapStart(sint32 s32ChnMask, uint32 u32Quality)
{
#if 0
    sint32 s32Ret = 0, i;
    VENC_CHN VeChnId = 0;
    VENC_FRAME_RATE_S stFrameRate;
    VENC_CHN_PARAM_S  stVencChnParam;
    VENC_JPEG_PARAM_S stVencJpegParam;

    if (s32ChnMask != 0x1 || u32Quality > 5)
    {
        return ERR_ILLEGAL_PARAM;
    }

    mpp_venc_FastSnapStop();
    pthread_mutex_lock(&m_stVencInfo.mutexLock);
    for (i = 0; i < 1; i++)
    {        
        VeChnId = (i == 0) ? m_stVencInfo.astSnap0Chn[0].VeChnId : m_stVencInfo.astSnap1Chn[0].VeChnId;
        stFrameRate.s32SrcFrmRate = -1;
        stFrameRate.s32DstFrmRate = -1;
        s32Ret = HI_MPI_VENC_GetChnParam(VeChnId, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_GetChnParam failed! [err=%#x]\n", s32Ret);
        }
        
        stVencChnParam.stFrameRate = stFrameRate;
        s32Ret = HI_MPI_VENC_SetChnParam(VeChnId, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_SetChnParam failed. [err=%#x]\n", s32Ret);
        }
        
        s32Ret = HI_MPI_VENC_GetJpegParam(VeChnId, &stVencJpegParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_GetJpegParam failed! [ch%d, err=%#x]\n", i, s32Ret);
        }

        stVencJpegParam.u32Qfactor = u32Quality * 20 + 10;
        stVencJpegParam.u32Qfactor = (stVencJpegParam.u32Qfactor > 99) ? 99 : stVencJpegParam.u32Qfactor;
        s32Ret = HI_MPI_VENC_SetJpegParam(VeChnId, &stVencJpegParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_SetJpegParam failed! [ch%d, err=%#x]\n", i, s32Ret);
        }
    }

    pthread_mutex_unlock(&m_stVencInfo.mutexLock);
#endif    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 关闭快速抓拍功能
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_venc_FastSnapStop()
{
#if 0
    sint32 s32Ret = 0, i;
    uint32 u32Quality = 3;
    VENC_CHN VeChnId = 0;
    VENC_FRAME_RATE_S stFrameRate;
    VENC_CHN_PARAM_S  stVencChnParam;
    VENC_JPEG_PARAM_S stVencJpegParam;

    pthread_mutex_lock(&m_stVencInfo.mutexLock);
    for (i = 0; i < 1; i++)
    {
        VeChnId = (i == 0) ? m_stVencInfo.astSnap0Chn[0].VeChnId : m_stVencInfo.astSnap1Chn[0].VeChnId;
        stFrameRate.s32SrcFrmRate = 30;
        stFrameRate.s32DstFrmRate = 1;
        s32Ret = HI_MPI_VENC_GetChnParam(VeChnId, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_GetChnParam failed! [err=%#x]\n", s32Ret);
        }
        
        stVencChnParam.stFrameRate = stFrameRate;
        s32Ret = HI_MPI_VENC_SetChnParam(VeChnId, &stVencChnParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_VENC_SetChnParam failed. [err=%#x]\n", s32Ret);
        }
        
        s32Ret = HI_MPI_VENC_GetJpegParam(VeChnId, &stVencJpegParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "HI_MPI_VENC_GetJpegParam failed! [ch%d, err=%#x]\n", i, s32Ret);
        }

        stVencJpegParam.u32Qfactor = u32Quality * 20 + 10;
        s32Ret = HI_MPI_VENC_SetJpegParam(VeChnId, &stVencJpegParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "HI_MPI_VENC_SetJpegParam failed! [ch%d, err=%#x]\n", i, s32Ret);
        }
    }
    pthread_mutex_unlock(&m_stVencInfo.mutexLock);
#endif    
    return SV_SUCCESS;
}

sint32 mpp_venc_H264AttrSet(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr)
{
    HD_RESULT  eRet = HD_OK;
    HD_PATH_ID u32EncPath;
    HD_VIDEOENC_PATH_CONFIG stVideoencPathConfig;
    HD_VIDEOENC_IN          stVideoencInParam;
    HD_VIDEOENC_OUT         stVideoencOutParam;
    HD_H26XENC_RATE_CONTROL stRcCtrl;

    if (s32Chn < 0 || s32Chn >= m_stVencInfo.u32ChnNum)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstVencAttr)
    {
        return ERR_NULL_PTR;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            if (!m_stVencInfo.astPriChn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            u32EncPath = m_stVencInfo.astPriChn[s32Chn].u32EncPath;
            break;

        case STREAM_TYPE_SEC:
            if (!m_stVencInfo.astSecChn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            u32EncPath = m_stVencInfo.astSecChn[s32Chn].u32EncPath;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_PATH_CONFIG, &stVideoencPathConfig);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_PATH_CONFIG fail=%d\n", eRet);
        return SV_FAILURE;
    }
    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_IN, &stVideoencInParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_IN fail=%d\n", eRet);
        return SV_FAILURE;
    }
    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_OUT_ENC_PARAM, &stVideoencOutParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_OUT_ENC_PARAM fail=%d\n", eRet);
        return SV_FAILURE;
    }
    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_OUT_RATE_CONTROL, &stRcCtrl);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_OUT_RATE_CONTROL fail=%d\n", eRet);
        return SV_FAILURE;
    }

    // ENC通道设置, 部分参数在create步骤已经设置，后续调试成功后可以删除简化
    stVideoencPathConfig.max_mem.codec_type = HD_CODEC_TYPE_H264;
    stVideoencPathConfig.max_mem.max_dim.w  = pstVencAttr->u32PicWidth;
    stVideoencPathConfig.max_mem.max_dim.h  = pstVencAttr->u32PicHeight;
    stVideoencPathConfig.max_mem.bitrate    = pstVencAttr->u32Bitrate;
    stVideoencPathConfig.max_mem.enc_buf_ms = 10000;
    stVideoencPathConfig.max_mem.svc_layer  = HD_SVC_4X;
    stVideoencPathConfig.max_mem.ltr        = TRUE;
    stVideoencPathConfig.max_mem.rotate     = FALSE;
    stVideoencPathConfig.max_mem.source_output = FALSE;
    stVideoencPathConfig.isp_id             = 0;
    // IN输入设置
    stVideoencInParam.dir                   = HD_VIDEO_DIR_NONE;
    stVideoencInParam.pxl_fmt               = HD_VIDEO_PXLFMT_YUV420;
    stVideoencInParam.dim.w                 = pstVencAttr->u32PicWidth;
    stVideoencInParam.dim.h                 = pstVencAttr->u32PicHeight;
    stVideoencInParam.frc                   = HD_VIDEO_FRC_RATIO(pstVencAttr->u32TargetFrmRate, pstVencAttr->u32ViFrmRate);
    // H264编码
    stVideoencOutParam.codec_type           = HD_CODEC_TYPE_H264;
    stVideoencOutParam.h26x.profile         = HD_H264E_HIGH_PROFILE;
    stVideoencOutParam.h26x.level_idc       = HD_H264E_LEVEL_5_1;
    stVideoencOutParam.h26x.gop_num         = pstVencAttr->u32Gop;
    stVideoencOutParam.h26x.chrm_qp_idx     = 1;
    stVideoencOutParam.h26x.ltr_interval    = 0;
    stVideoencOutParam.h26x.ltr_pre_ref     = 0;
    stVideoencOutParam.h26x.gray_en         = 0;
    stVideoencOutParam.h26x.source_output   = 0;
    stVideoencOutParam.h26x.svc_layer       = HD_SVC_DISABLE;
    stVideoencOutParam.h26x.entropy_mode    = HD_H264E_CABAC_CODING;
    // 码率控制
    switch (pstVencAttr->enRcMode)
    {
        case HD_RC_MODE_CBR:
            stRcCtrl.rc_mode             = HD_RC_MODE_CBR;
            stRcCtrl.cbr.bitrate         = REC_FIX_TBR(pstVencAttr->u32PicWidth, pstVencAttr->u32PicHeight);
            stRcCtrl.cbr.frame_rate_base = pstVencAttr->u32TargetFrmRate;
            stRcCtrl.cbr.frame_rate_incr = 1;
            stRcCtrl.cbr.init_i_qp       = pstVencAttr->s32IQp;
            stRcCtrl.cbr.min_i_qp        = 10;
            stRcCtrl.cbr.max_i_qp        = 45;
            stRcCtrl.cbr.init_p_qp       = pstVencAttr->s32PQp;
            stRcCtrl.cbr.min_p_qp        = 10;
            stRcCtrl.cbr.max_p_qp        = 45;
            stRcCtrl.cbr.static_time     = 4;
            stRcCtrl.cbr.ip_weight       = 0;
            break;
        case HD_RC_MODE_VBR:
            stRcCtrl.rc_mode             = HD_RC_MODE_VBR;
            stRcCtrl.vbr.bitrate         = REC_FIX_TBR(pstVencAttr->u32PicWidth, pstVencAttr->u32PicHeight);
            stRcCtrl.vbr.frame_rate_base = pstVencAttr->u32TargetFrmRate;
            stRcCtrl.vbr.frame_rate_incr = 1;
            stRcCtrl.vbr.init_i_qp       = pstVencAttr->s32IQp;
            stRcCtrl.vbr.min_i_qp        = 10;
            stRcCtrl.vbr.max_i_qp        = 45;
            stRcCtrl.vbr.init_p_qp       = pstVencAttr->s32PQp;
            stRcCtrl.vbr.min_p_qp        = 10;
            stRcCtrl.vbr.max_p_qp        = 45;
            stRcCtrl.vbr.static_time     = 4;
            stRcCtrl.vbr.ip_weight       = 0;
            break;
        case HD_RC_MODE_FIX_QP:
            stRcCtrl.rc_mode               = HD_RC_MODE_FIX_QP;
            stRcCtrl.fixqp.fix_i_qp        = pstVencAttr->s32IQp;
            stRcCtrl.fixqp.fix_p_qp        = pstVencAttr->s32PQp;
            stRcCtrl.fixqp.frame_rate_base = pstVencAttr->u32TargetFrmRate;
            stRcCtrl.fixqp.frame_rate_incr = 1;
            break;
        case HD_RC_MODE_EVBR:
            stRcCtrl.rc_mode              = HD_RC_MODE_EVBR;
            stRcCtrl.evbr.bitrate         = REC_FIX_TBR(2560, 1440);
            stRcCtrl.evbr.frame_rate_base = pstVencAttr->u32TargetFrmRate;
            stRcCtrl.evbr.frame_rate_incr = 1;
            stRcCtrl.evbr.init_i_qp       = pstVencAttr->s32IQp;
            stRcCtrl.evbr.min_i_qp        = 10;
            stRcCtrl.evbr.max_i_qp        = 45;
            stRcCtrl.evbr.init_p_qp       = pstVencAttr->s32PQp;
            stRcCtrl.evbr.min_p_qp        = 10;
            stRcCtrl.evbr.max_p_qp        = 45;
            stRcCtrl.evbr.ip_weight       = 0;
            stRcCtrl.evbr.key_p_period    = 60;
            stRcCtrl.evbr.kp_weight       = 0;
            break;
        default:
            break;
    }

    // set-1
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_PATH_CONFIG, &stVideoencPathConfig);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_PATH_CONFIG fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // set-2
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_IN, &stVideoencInParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_IN fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // set-3
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_OUT_ENC_PARAM, &stVideoencOutParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_OUT_ENC_PARAM fail=%d\n", eRet);
        return SV_FAILURE;
    }
    // set-4
    eRet = hd_videoenc_set(u32EncPath, HD_VIDEOENC_PARAM_OUT_RATE_CONTROL, &stRcCtrl);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_set HD_VIDEOENC_PARAM_OUT_RATE_CONTROL fail=%d\n", eRet);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 mpp_venc_H264AttrGet(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr)
{
    HD_RESULT               eRet = HD_OK;
    HD_PATH_ID              u32EncPath;
    HD_VIDEOENC_PATH_CONFIG stVideoencPathConfig;
    HD_VIDEOENC_IN          stVideoencInParam;
    HD_VIDEOENC_OUT         stVideoencOutParam;
    HD_H26XENC_RATE_CONTROL stRcCtrl;

    if (s32Chn < 0 || s32Chn >= m_stVencInfo.u32ChnNum)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstVencAttr)
    {
        return ERR_NULL_PTR;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            if (!m_stVencInfo.astPriChn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            u32EncPath = m_stVencInfo.astPriChn[s32Chn].u32EncPath;
            break;

        case STREAM_TYPE_SEC:
            if (!m_stVencInfo.astSecChn[s32Chn].bCreated)
            {
                return ERR_INVALID_CHNID;
            }
            u32EncPath = m_stVencInfo.astSecChn[s32Chn].u32EncPath;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_PATH_CONFIG, &stVideoencPathConfig);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_PATH_CONFIG fail=%d\n", eRet);
        return SV_FAILURE;
    }
    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_IN, &stVideoencInParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_IN fail=%d\n", eRet);
        return SV_FAILURE;
    }
    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_OUT_ENC_PARAM, &stVideoencOutParam);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_OUT_ENC_PARAM fail=%d\n", eRet);
        return SV_FAILURE;
    }
    eRet = hd_videoenc_get(u32EncPath, HD_VIDEOENC_PARAM_OUT_RATE_CONTROL, &stRcCtrl);
    if (HD_OK != eRet)
    {
        print_level(SV_ERROR, "hd_videoenc_get HD_VIDEOENC_PARAM_OUT_RATE_CONTROL fail=%d\n", eRet);
        return SV_FAILURE;
    }

    pstVencAttr->u32PicWidth    = stVideoencPathConfig.max_mem.max_dim.w;
    pstVencAttr->u32PicHeight   = stVideoencPathConfig.max_mem.max_dim.h;
    pstVencAttr->bMainStream    = STREAM_TYPE_PRI == enStreamType ? SV_TRUE : SV_FALSE;
    pstVencAttr->u32Bitrate     = stVideoencPathConfig.max_mem.bitrate;
    pstVencAttr->u32ViFrmRate   = 30;
    pstVencAttr->u32Gop         = stVideoencOutParam.h26x.gop_num;


    switch (stRcCtrl.rc_mode)
    {
        case HD_RC_MODE_CBR:;
            pstVencAttr->enRcMode           = HD_RC_MODE_CBR;
            pstVencAttr->u32ViFrmRate       = stRcCtrl.cbr.frame_rate_base;
            pstVencAttr->u32TargetFrmRate   = stRcCtrl.cbr.frame_rate_base;
            pstVencAttr->s32IQp             = stRcCtrl.cbr.init_i_qp;
            pstVencAttr->s32PQp             = stRcCtrl.cbr.init_p_qp;
            break;

        case HD_RC_MODE_VBR:
            pstVencAttr->enRcMode           = VRC_MODE_VBR;
            pstVencAttr->u32ViFrmRate       = stRcCtrl.vbr.frame_rate_base;
            pstVencAttr->u32TargetFrmRate   = stRcCtrl.vbr.frame_rate_base;
            pstVencAttr->u32Bitrate         = stRcCtrl.vbr.bitrate;
            pstVencAttr->s32IQp             = stRcCtrl.vbr.init_i_qp;
            pstVencAttr->s32PQp             = stRcCtrl.vbr.init_p_qp;
            break;

        case HD_RC_MODE_FIX_QP:
            pstVencAttr->enRcMode           = HD_RC_MODE_FIX_QP;
            pstVencAttr->u32ViFrmRate       = stRcCtrl.fixqp.frame_rate_base;
            pstVencAttr->u32TargetFrmRate   = stRcCtrl.fixqp.frame_rate_base;
            pstVencAttr->s32IQp             = stRcCtrl.fixqp.fix_i_qp;
            pstVencAttr->s32PQp             = stRcCtrl.fixqp.fix_p_qp;
            break;

        case HD_RC_MODE_EVBR:
            pstVencAttr->enRcMode           = VRC_MODE_FIXQP;
            pstVencAttr->u32ViFrmRate       = stRcCtrl.evbr.frame_rate_base;
            pstVencAttr->u32TargetFrmRate   = stRcCtrl.evbr.frame_rate_base;
            pstVencAttr->s32IQp             = stRcCtrl.evbr.init_i_qp;
            pstVencAttr->s32PQp             = stRcCtrl.evbr.init_p_qp;
            break;

        default :
            break;
    }

    return SV_SUCCESS;
}

/*
    重新设置JPEG压缩格式的尺寸
*/ 
sint32 mpp_venc_RenewJpegSize(VIDEO_MODE_EE newVideoMode, uint32 u32NewW, uint32 u32NewH)
{
    HD_RESULT eRet = HD_OK;

    // 调整JPEG图片大小


    return SV_SUCCESS;
}

void mpp_venc_UpdateVedioMode(VIDEO_MODE_EE newVideoMode)
{
    m_stVencInfo.enVideoMode = newVideoMode;//This statment must be updated each time and before MEDIA_VENC_H264ChnRecreate
}

