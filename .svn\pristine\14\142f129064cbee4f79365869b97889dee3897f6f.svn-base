/******************************************************************************
Copyright (C) 2018-2020 广州敏视数码科技有限公司版权所有.

文件名: httpHandle.c

作者: 许家铭    版本: v1.0.0(初始版本号)    日期: 2018-01-08

文件功能描述: 定义http消息处理功能函数

版本: v1.0.0(最新版本号)

历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>
#include <ctype.h>
#include <sys/file.h>

#define COM_MOD_ID  COM_ID_IPSERVER
#include "board.h"
#include "common.h"
#include "print.h"
#include "msg.h"
#include "uuid.h"
#include "openssl/hmac.h"
#include "config.h"
#include "libhttp.h"
#include "jsonHandle.h"
#include "httpHandle.h"
#include "sharefifo.h"
#include "faac/faac.h"
#include "flv_format_define.h"
#include "cJSON.h"
#include "utils.h"

//#define HTTP_FLV_CHUNKED 1

extern char   m_szUserPassword[3][3][CONFIG_MAX_BUF_SIZE];                  /* 用户管理     [0][0]--用户名 [0][1]--默认密码 [0][2]--当前使用密码*/
extern pthread_mutex_t m_mutexLockStrtok;
extern SNAP_INFO_S m_stSnapInfo;
extern SV_BOOL bResetMJpeg;
extern int HTTP_SearchSpsPpsData(sint8 *pNuil, const int iNuilLen, char *sps_data, int *iOutSpsDataLen, char *pps_data, int *iOutPpsDataLen);

char * http_TransEncode2String(uint32 enRcMode)
{
    switch (enRcMode)
    {
        case 0:
            return "H264";
        case 1:
            return "H265";
        case 2:
            return "MJPEG";
        default:
            return "unknown";
    }
}

uint32 http_TransEncode2Number(char *pszEncode)
{
    if (0 == strcmp(pszEncode, "H264"))
    {
        return 0;
    }
    else if (0 == strcmp(pszEncode, "H265"))
    {
        return 1;
    }
    else if (0 == strcmp(pszEncode, "MJPEG"))
    {
        return 2;
    }
    else
    {
        return 0;
    }
}


#if (defined(BOARD_IPCR20S3))
sint32 HTTP_R2P_GetCgi(char*pszReqPath,char *pszRequest, char *pszResponse)
{
	sint32              s32Ret          = 0;
    MSG_PACKET_S        stRetPkt        = {0};
	MSG_VIDEO_CFG 		stVideoCfg = {0};
	MSG_NETWORK_CFG stNetworkCfg = {0};
	MSG_IMAGE_CFG stImageCfg = {0};

	stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
	s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed. [err=%#x]\n", s32Ret);
		return MSG_DEFAULT_FAIL;
	}


   	if(NULL != strcasestr(pszReqPath, "GetInputOsd.cgi"))
   	{
		uint8 u8OsdPos = stVideoCfg.astChnParam[0].enOsdPosition == 0?0:2;
		sprintf(pszResponse,"i_OSD_En=%d<br>i_OSDPos=%d<br>", stVideoCfg.astChnParam[0].bShowTime,u8OsdPos);
   	}
	else if(NULL != strcasestr(pszReqPath, "GetNetwork.cgi"))
	{
		stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
		s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed. [err=%#x]\n", s32Ret);
			return MSG_DEFAULT_FAIL;
		}
		sprintf(pszResponse,"Proto=%d<br>Ipaddr=%s<br>Netmask=%s<br>Gateway=%s<br>Dns=%s<br>"
				"Ipaddr1=%s<br>Netmask1=%s<br>Gateway1=%s<br>Dns2=%s<br>"
				"Rtsp_Port=%d<br>Path_Live1=mainstream<br>Path_Live2=substream<br>"
				"MulticastIP0=*********<br>MulticastPort0=%d<br>",
				stNetworkCfg.bDHCPEnable,stNetworkCfg.szIpAddr,stNetworkCfg.szSubmask,stNetworkCfg.szGateway, stNetworkCfg.szDnsServer,\
				stNetworkCfg.szIpAddr,stNetworkCfg.szSubmask,stNetworkCfg.szGateway,stNetworkCfg.szDnsServer,\
				stNetworkCfg.u32RtspServPort,stNetworkCfg.szRtspServMainUri,stNetworkCfg.szRtspServSubUri,stNetworkCfg.u32MulticastPort);



	}
	else if(NULL != strcasestr(pszReqPath,"GetImageValue.cgi"))
	{
		stRetPkt.pu8Data = (uint8 *)&stImageCfg;
	    s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_IMAGE_CFG, NULL, &stRetPkt, sizeof(MSG_IMAGE_CFG));
	    if (SV_SUCCESS != s32Ret)
	    {
	        print_level(SV_ERROR, "OP_REQ_GET_IMAGE_CFG failed. [err=%#x]\n", s32Ret);
	        return MSG_DEFAULT_FAIL;
	    }
		sprintf(pszResponse,"Sharpness=%d<br>Brightness=%d<br>Contrast=%d<br>Saturation=%d<br>",
							  stImageCfg.u8Sharpness,stImageCfg.u8Brightness,stImageCfg.u8Contrast,stImageCfg.u8Saturation);
	}
	else if(NULL != strcasestr(pszReqPath,"GetMultimedia.cgi"))
	{
		uint8 u8RcMode = stVideoCfg.astChnParam[0].enMainRcMode==RC_MODE_VBR?2:0;
		sprintf(pszResponse,"Bitrate=%d<br>Compression_Type=%d<br>Jpeg_Quality=3<br>"
							 "Resolution=%dx%d<br>Fps=%d<br>Video_Rotate=%d<br>"
							 "Mode=%s<br>StreamMode=1<br>Audio=%d<br>Audio_Type=0<br>GOP=%d<br>",
							 stVideoCfg.astChnParam[0].u32MainBitrate,u8RcMode,stVideoCfg.astChnParam[0].u32MainWidth,stVideoCfg.astChnParam[0].u32MainHeight,
							 stVideoCfg.astChnParam[0].u32MainFramerate,stVideoCfg.astChnParam[0].enRotateAngle,http_TransEncode2String(stVideoCfg.astChnParam[0].enMainEncode),
							 stVideoCfg.astChnParam[0].bAudioEnable,stVideoCfg.astChnParam[0].u32MainIfrmInterval);
	}
	else
	{
		print_level(SV_ERROR, "not support cgi with method: %s\n",pszReqPath);
		return MSG_NOTSUPPORT;
	}
	return SV_SUCCESS;

}

sint32 HTTP_R2P_SetCgi(char*pszReqPath,char *pszReqParam, char *pszResponse)
{
	sint32              s32Ret = 0;
   MSG_PACKET_S         stMsgPkt = {0}, stRetPkt = {0};
    char                buf[32]         = {0};
	MSG_VIDEO_CFG 		stVideoCfg = {0};
	MSG_IMAGE_CFG       stImageCfg = {0};
	MSG_NETWORK_CFG     stNetworkCfg = {0};
	char 				*token = NULL;
	char				*pstTmp = NULL;
	char 				*pstTTmp = NULL;

	stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
	s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed. [err=%#x]\n", s32Ret);
		return MSG_DEFAULT_FAIL;
	}

   	if(NULL != strcasestr(pszReqPath, "SetInputOsd.cgi"))
   	{
		pstTmp = strcasestr(pszReqParam, "i_video_en1=");
        if (NULL != pstTmp)
            stVideoCfg.astChnParam[0].bShowTime = atoi(pstTmp+strlen("i_video_en1="));

		pstTmp = strcasestr(pszReqParam,"i_OSD_En=");
		if(NULL!=pstTmp)
			 stVideoCfg.astChnParam[0].enOsdPosition = atoi(pstTmp+strlen("i_OSD_En=")) == 0?0:1;

		stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
	    stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
	    Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);
   	}
	else if(NULL != strcasestr(pszReqPath,"SetNetwork.cgi"))
	{
		stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
		s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed. [err=%#x]\n", s32Ret);
			return MSG_DEFAULT_FAIL;
		}
		token = strtok_r(pszReqParam, "&",&pstTTmp);
	    while (token != NULL) {
	        char *equalSign = strstr(token, "=");
	        if (equalSign != NULL)
			{
	            char key[50], value[50];
	            strncpy(key, token, equalSign - token);
	            key[equalSign - token] = '\0';
	            strcpy(value, equalSign + 1);
				if(strcmp(key,"Proto") == 0)
				{
					stNetworkCfg.bDHCPEnable = atoi(value);
				}
				else if(strcmp(key,"Ipaddr1") == 0)
				{
					strcpy(stNetworkCfg.szIpAddr,value);
				}
				else if(strcmp(key,"Netmask1") == 0)
				{
					strcpy(stNetworkCfg.szSubmask,value);
				}
				else if(strcmp(key,"Gateway1") == 0)
				{
					strcpy(stNetworkCfg.szGateway,value);
				}
				else if(strcmp(key,"Dns2") == 0)
				{
					strcpy(stNetworkCfg.szDnsServer,value);
				}
				else if(strcmp(key,"Rtsp_Port") == 0)
				{
					strcpy(stNetworkCfg.u32RtspServPort,value);
				}
				else if(strcmp(key,"MulticastPort0") == 0)
				{
					strcpy(stNetworkCfg.u32MulticastPort,value);
				}

	            //print_level(SV_DEBUG,"Key: %s, Value: %s\n", key, value); // 打印键和值
	        }

	        token = strtok_r(NULL, "&",&pstTTmp);
	    }

		stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
	    stMsgPkt.u32Size = sizeof(stNetworkCfg);
	    Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);

	}
	else if(NULL != strcasestr(pszReqPath,"SetImageValue.cgi"))
	{
		stRetPkt.pu8Data = (uint8 *)&stImageCfg;
		s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_IMAGE_CFG, NULL, &stRetPkt, sizeof(MSG_IMAGE_CFG));
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "OP_REQ_GET_IMAGE_CFG failed. [err=%#x]\n", s32Ret);
			return MSG_DEFAULT_FAIL;
		}

		pstTmp = strcasestr(pszReqParam, "Sharpness=");
		if (NULL != pstTmp)
		{
			stImageCfg.u8Saturation = atoi(pstTmp+strlen("Sharpness="));
		}

		pstTmp = strcasestr(pszReqParam, "Brightness=");
		if (NULL != pstTmp)
			stImageCfg.u8Brightness = atoi(pstTmp+strlen("Brightness="));

		pstTmp = strcasestr(pszReqParam, "Contrast=");
		if (NULL != pstTmp)
			stImageCfg.u8Contrast = atoi(pstTmp+strlen("Contrast="));

		pstTmp = strcasestr(pszReqParam, "Saturation=");
		if (NULL != pstTmp)
			stImageCfg.u8Saturation = atoi(pstTmp+strlen("Saturation="));

		stMsgPkt.pu8Data = (uint8 *)&stImageCfg;
		stMsgPkt.u32Size = sizeof(stImageCfg);
		Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_SET_IMAGE_CFG, &stMsgPkt, NULL, 0);

	}
	else if(NULL != strcasestr(pszReqPath,"SetMultimedia.cgi"))
	{
		token = strtok_r(pszReqParam, "&",&pstTTmp);
	    while (token != NULL) {
	        char *equalSign = strstr(token, "=");
	        if (equalSign != NULL)
			{
	            char key[50], value[50];
	            strncpy(key, token, equalSign - token);
	            key[equalSign - token] = '\0';
	            strcpy(value, equalSign + 1);
				if(strcmp(key,"Bitrate") == 0)
				{
					stVideoCfg.astChnParam[0].u32MainBitrate = atoi(value);
				}
				else if(strcmp(key,"Compression_Type") == 0)
				{
					stVideoCfg.astChnParam[0].enMainRcMode = atoi(value)==RC_MODE_VBR?2:0;
				}
				else if(strcmp(key,"Resolution") == 0)
				{
					sscanf(value,"%dx%d",&stVideoCfg.astChnParam[0].u32MainWidth,&stVideoCfg.astChnParam[0].u32MainHeight);
				}
				else if(strcmp(key,"Fps") == 0)
				{
					stVideoCfg.astChnParam[0].u32MainFramerate = atoi(value);
				}
				else if(strcmp(key,"Video_Rotate")==0)
				{
					stVideoCfg.astChnParam[0].enRotateAngle = atoi(value);
				}
				else if(strcmp(key,"Mode") == 0)
				{
					stVideoCfg.astChnParam[0].enMainEncode = http_TransEncode2Number(value);
				}
				else if(strcmp(key,"Audio") == 0)
				{
					stVideoCfg.astChnParam[0].bAudioEnable = atoi(value);
				}
				else if(strcmp(key,"GOP") == 0)
				{
					stVideoCfg.astChnParam[0].u32MainIfrmInterval = atoi(value);
				}

	            //print_level(SV_DEBUG,"Key: %s, Value: %s\n", key, value); // 打印键和值
	        }

	        token = strtok_r(NULL, "&",&pstTTmp);
	    }
		stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
		stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
		Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);

	}
	else
	{
		print_level(SV_ERROR, "not support cgi with method: %s\n",pszReqPath);
		return MSG_NOTSUPPORT;
	}
	strcpy(pszResponse, "{}");
	return SV_SUCCESS;

}

#endif

sint32 HTTP_HDL_ParseHeader(uint8 *pu8Message, uint32 u32MsgSize, HTTP_HEADER_S *pstHeader)
{
    sint32 s32Ret = 0;
    char *pszTmp = NULL;
	char *pszTTmp = NULL;
    if (NULL == pu8Message || NULL == pstHeader)
    {
        return ERR_NULL_PTR;
    }

    if (u32MsgSize == 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

	//pthread_mutex_lock(&m_mutexLockStrtok);
    pstHeader->pszMethod = strtok_r((char *)pu8Message, " ", &pszTTmp);
    if (NULL == pstHeader->pszMethod)
    {
        print_level(SV_ERROR, "http header parse Method error.\n");
        return ERR_INVALID_CHNID;
    }

    pstHeader->pszUrl = strtok_r(NULL, " ", &pszTTmp);
    if (NULL == pstHeader->pszUrl)
    {
        print_level(SV_ERROR, "http header parse URL error.\n");
		//pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

    pstHeader->pszVersion = pstHeader->pszUrl + strlen(pstHeader->pszUrl) + 1;
    if (NULL == pstHeader->pszVersion)
    {
        print_level(SV_ERROR, "http header parse VERSION error.\n");
		//pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

    pstHeader->pu8Body = (uint8 *)strstr(pstHeader->pszVersion, "\r\n\r\n");
    if (NULL == pstHeader->pu8Body)
    {
        print_level(SV_ERROR, "http header parse Body error.\n");
		//pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

	pszTmp = strtok_r(pstHeader->pszVersion, "\r\n", &pszTTmp);
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "http header parse VERSION error.\n");
		//pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

    while (NULL != pszTmp)
    {
    	//print_level(SV_DEBUG, "%s\n", pszTmp);
    	if (NULL != strcasestr(pszTmp, "Host: "))
    	{
    		pstHeader->pszHost = pszTmp + strlen("Host: ");
    	}
    	else if (NULL != strcasestr(pszTmp, "Cookie: "))
    	{
    		pstHeader->pszCookie = pszTmp + strlen("Cookie: ");
    	}
		else if (NULL != strcasestr(pszTmp, "Referer: "))
    	{
    		pstHeader->pszReferer = pszTmp + strlen("Referer: ");
    	}
    	else if (NULL != strcasestr(pszTmp, "If-None-Match: "))
    	{
    		pstHeader->pszIfNotMatch = pszTmp + strlen("If-None-Match: ");
    	}
    	else if (NULL != strcasestr(pszTmp, "Range: bytes="))
    	{
    		pstHeader->pszRange = pszTmp + strlen("Range: bytes=");
    	}
    	else if (NULL != strcasestr(pszTmp, "Content-Type: multipart/form-data; boundary="))
    	{
    		pstHeader->pszBoundary = pszTmp + strlen("Content-Type: multipart/form-data; boundary=");
    	}
    	else if (NULL != strcasestr(pszTmp, "Content-Length: "))
    	{
    		pstHeader->pszContentLenght = pszTmp + strlen("Content-Length: ");
    	}
        else if (NULL != strcasestr(pszTmp, "Content-Type: "))
        {
            pstHeader->pszContentType = pszTmp + strlen("Content-Type: ");
        }
        else if (NULL != strcasestr(pszTmp, "IsFactory"))
        {
            pstHeader->bFactory = SV_TRUE;
        }

    	if ((pszTmp + strlen(pszTmp)) >= (char *)pstHeader->pu8Body)
    	{
    		break;
    	}
    	pszTmp = strtok_r(NULL, "\r\n", &pszTTmp);

    }
    pstHeader->pu8Body += 4;
    pstHeader->u32Size = u32MsgSize - (pstHeader->pu8Body - pu8Message);
	//pthread_mutex_unlock(&m_mutexLockStrtok);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 根据执行操作的错误码解析成json格式的文字描述
 * 输入参数: s32ErrCode --- 操作错误码
 * 输出参数: pszOutString --- json格式的字符串
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 HTTP_HDL_ParseError(sint32 s32ErrCode, char *pszOutString)
{
    int s32Ret = 0;
    char szBuf[256] = {0};

    if (NULL == pszOutString)
    {
        print_level(SV_ERROR, "input prt=null\n");
        return ERR_NULL_PTR;
    }

    sprintf(pszOutString, "{\"errorType\": %u, \"description\": ", s32ErrCode);
    switch ((signed char)s32ErrCode)
    {
        case MSG_NULL_RES:
            strcpy(szBuf, "\"No result.\"");
            break;
        case MSG_SUCCESS_RES:
            strcpy(szBuf, "\"Successful.\"");
            break;
        case MSG_DEFAULT_FAIL:
            strcpy(szBuf, "\"Internal error.\"");
            break;
        case MSG_TIMEOUT_FAIL:
            strcpy(szBuf, "\"Execution timeout.\"");
            break;
        case MSG_REQUEST_NO_REGISTER:
            strcpy(szBuf, "\"Unregistered operation.\"");
            break;
        case MSG_AUTHORITY_FAIL:
            strcpy(szBuf, "\"Authentication error.\"");
            break;
        case MSG_FORMAT_ERROR:
            strcpy(szBuf, "\"Json body format error.\"");
            break;
        case MSG_NO_ENOUGH_MEMORY:
            strcpy(szBuf, "\"No enough memory for transmission.\"");
            break;
        case MSG_RANGE_OUT:
            strcpy(szBuf, "\"Parameter is out of range.\"");
            break;
        case MSG_OLD_PWD_FAIL:
        	strcpy(szBuf, "\"Old password error.\"");
            break;
        case MSG_REPEAT_REQUEST:
            strcpy(szBuf, "\"Repeat request during executing.\"");
            break;
        case MSG_DEVICE_BUSY:
            strcpy(szBuf, "\"Device is busy now.\"");
            break;
        case MSG_DEVICE_LACK:
            strcpy(szBuf, "\"Device is lacked.\"");
            break;
        case MSG_FILE_LACK:
            strcpy(szBuf, "\"File is lacked.\"");
            break;
        case MSG_JSON_LOSTPARAM:
            strcpy(szBuf, "\"Json lost some must parameter.\"");
            break;
        case MSG_NOTSUPPORT:
            strcpy(szBuf, "\"No support request.\"");
            break;
        case MSG_DATA_UNMATCH:
            strcpy(szBuf, "\"The data provided does not match.\"");
            break;
        case MSG_RES_EXIST:
            strcpy(szBuf, "\"The resource provided is already exist.\"");
            break;
		case MSG_USER_UNEXIST:
			strcpy(szBuf,"\"The usrname is not unexist.\"");
			break;
        default:
            strcpy(szBuf, "\"Unknown error.\"");
    }
    strcat(pszOutString, szBuf);
    strcat(pszOutString, "}");

    print_level(SV_DEBUG, "%s\n", pszOutString);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 生成http处理失败的json格式回包字符串内容
 * 输入参数: s32ErrCode --- 操作错误码
 * 输出参数: pszOutString --- 回包字符串内容
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 HTTP_HDL_GenerateErrorReply(sint32 s32ErrCode, char *pszOutString)
{
    char szMsgBuf[128];

    HTTP_HDL_ParseError(s32ErrCode, szMsgBuf);
    sprintf(pszOutString, "HTTP/1.1 500 Internal Server Error\r\n"
                          "Content-Type: application/json; charset=UTF-8\r\n"
                          "Content-Length: %d\r\n\r\n", strlen(szMsgBuf));
    strcat(pszOutString, szMsgBuf);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 生成http处理成功的json格式回包字符串内容
 * 输入参数: pszJsonBody --- json body字符串
 * 输出参数: pszOutString --- 回包字符串内容
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 HTTP_HDL_GenerateSuccessReply(char *pszJsonBody, char *pszOutString)
{
    sint32 s32Len;
    time_t now = time(NULL);
    char szGMTTime[64];

    if (NULL == pszJsonBody || NULL == pszOutString)
    {
        print_level(SV_ERROR, "null pointer.\n");
        return SV_FAILURE;
    }

    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&now));
    s32Len = snprintf(pszOutString, HTTP_BUF_SIZE,
                        "HTTP/1.1 200 OK\r\n"
                        "Date: %s\r\n"
                        "Access-Control-Allow-Origin:*\r\n"
                        "Content-Type: application/json; charset=UTF-8\r\n"
                        "Content-Length: %d\r\n\r\n%s", szGMTTime, strlen(pszJsonBody), pszJsonBody);
    if (s32Len > HTTP_BUF_SIZE)
    {
        print_level(SV_ERROR, "not enough space\n");
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

#if (OPENSSL_VERSION_NUMBER < 0x10100000L)
sint32 HTTP_HDL_GenerateETag(char *pszFilePath, sint32 s32Size, sint32 s32Time, char *pszETag)
{
	sint32 s32Ret = 0;
	struct stat stFileInfo = {0};
	HMAC_CTX stCtx;
	EVP_ENCODE_CTX  stEctx;
    EVP_MD *pstEngine = NULL;
    char szKeyStr[256];
    char szBase64[64];
    uint8 au8HMAC[64];
    uint32 u32Len = 0;
    sint32 s32TmpLen = 0;
    sint32 s32FileSize = 0;
    sint32 s32ModifyTime = 0;

	if (NULL == pszFilePath)
	{
		return SV_FAILURE;
	}

    if (0 != s32Size && 0 != s32Time)
    {
        s32FileSize = s32Size;
        s32ModifyTime = s32Time;
    }
    else
    {
    	if (0 != stat(pszFilePath, &stFileInfo) && NULL == strstr(pszFilePath, ".mp4"))
    	{
    		print_level(SV_ERROR, "stat file: %s failed. [err=%#x]\n", pszFilePath, errno);
    		return SV_FAILURE;
    	}
        s32FileSize = (sint32)stFileInfo.st_size;
        s32Time = (sint32)stFileInfo.st_mtime;
    }

	sprintf(szKeyStr, "%s:%d:%d", pszFilePath, s32FileSize, s32Time);
	//print_level(SV_DEBUG, "%s\n", szKeyStr);
	pstEngine = (EVP_MD*)EVP_md5();
    if (NULL == pstEngine)
    {
        print_level(SV_ERROR, "EVP_md5 failed.\n");
        return SV_FAILURE;
    }

	HMAC_CTX_init(&stCtx);
	s32Ret = HMAC_Init_ex(&stCtx, szKeyStr, strlen(szKeyStr), pstEngine, NULL);
    if (1 != s32Ret)
    {
        print_level(SV_ERROR, "HMAC_Init_ex failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = HMAC_Final(&stCtx, au8HMAC, &u32Len);
    if (1 != s32Ret)
    {
        print_level(SV_ERROR, "HMAC_Final failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    HMAC_CTX_cleanup(&stCtx);
    EVP_EncodeInit(&stEctx);
    EVP_EncodeUpdate(&stEctx, (uint8 *)szBase64, &s32TmpLen, au8HMAC, u32Len);
    EVP_EncodeFinal(&stEctx, (uint8 *)(szBase64 + s32TmpLen), &s32TmpLen);
    szBase64[s32TmpLen-1] = '\0';
    if (NULL != pszETag)
    {
    	strcpy(pszETag, szBase64);
    }

	return SV_SUCCESS;
}

#else
sint32 HTTP_HDL_GenerateETag(char *pszFilePath, sint32 s32Size, sint32 s32Time, char *pszETag)
{
	sint32 s32Ret = 0;
	struct stat stFileInfo = {0};
	HMAC_CTX *pstCtx;
	EVP_ENCODE_CTX *pstEctx;
    EVP_MD *pstEngine = NULL;
    char szKeyStr[256];
    char szBase64[64];
    uint8 au8HMAC[64];
    uint32 u32Len = 0;
    sint32 s32TmpLen = 0;
    sint32 s32FileSize = 0;
    sint32 s32ModifyTime = 0;

	if (NULL == pszFilePath)
	{
		return SV_FAILURE;
	}

    if (0 != s32Size && 0 != s32Time)
    {
        s32FileSize = s32Size;
        s32ModifyTime = s32Time;
    }
    else
    {
    	if (0 != stat(pszFilePath, &stFileInfo) && NULL == strstr(pszFilePath, ".mp4"))
    	{
    		print_level(SV_ERROR, "stat file: %s failed. [err=%#x]\n", pszFilePath, errno);
    		return SV_FAILURE;
    	}
        s32FileSize = (sint32)stFileInfo.st_size;
        s32Time = (sint32)stFileInfo.st_mtime;
    }

	sprintf(szKeyStr, "%s:%d:%d", pszFilePath, s32FileSize, s32Time);
	//print_level(SV_DEBUG, "%s\n", szKeyStr);
	pstEngine = (EVP_MD*)EVP_md5();
    if (NULL == pstEngine)
    {
        print_level(SV_ERROR, "EVP_md5 failed.\n");
        return SV_FAILURE;
    }

	pstCtx = HMAC_CTX_new();
	s32Ret = HMAC_Init_ex(pstCtx, szKeyStr, strlen(szKeyStr), pstEngine, NULL);
    if (1 != s32Ret)
    {
        print_level(SV_ERROR, "HMAC_Init_ex failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = HMAC_Final(pstCtx, au8HMAC, &u32Len);
    if (1 != s32Ret)
    {
        print_level(SV_ERROR, "HMAC_Final failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    HMAC_CTX_free(pstCtx);
    pstEctx = EVP_ENCODE_CTX_new();
    EVP_EncodeInit(pstEctx);
    EVP_EncodeUpdate(pstEctx, (uint8 *)szBase64, &s32TmpLen, au8HMAC, u32Len);
    EVP_EncodeFinal(pstEctx, (uint8 *)(szBase64 + s32TmpLen), &s32TmpLen);
    szBase64[s32TmpLen-1] = '\0';
    if (NULL != pszETag)
    {
    	strcpy(pszETag, szBase64);
    }
    EVP_ENCODE_CTX_free(pstEctx);

	return SV_SUCCESS;
}
#endif

sint32 HTTP_HDL_IsMatchETag(char *pszFilePath, char *pszETag)
{
	sint32 s32Ret = 0;
	char szETag[32];

	if (NULL == pszFilePath || NULL == pszETag)
	{
		return SV_FALSE;
	}

	s32Ret = HTTP_HDL_GenerateETag(pszFilePath, 0, 0, szETag);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HTTP_HDL_GenerateETag failed. [err=%#x]\n", s32Ret);
		return SV_FALSE;
	}

	if (0 == strcmp(pszETag, szETag))
	{
		return SV_TRUE;
	}

	return SV_FALSE;
}

sint32 HTTP_HDL_MsgHandle(SV_BOOL bAuth, uint8 *pu8Message, uint32 u32MsgSize, uint8 *pu8Result)
{
    sint32 s32Ret = 0, s32Res = 0;
    char *pszReqPath = NULL;
    char *pszReqParam = NULL;
	char *pszReferer = NULL;
    HTTP_HEADER_S stHeader = {0};
    char szJsonBody[20*1024];
	char *szLargeJsonBody=NULL;
	char szCmd[64];

    if (NULL == pu8Message || NULL == pu8Result)
    {
        return ERR_NULL_PTR;
    }

    if (u32MsgSize == 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pu8Message[u32MsgSize] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(pu8Message, u32MsgSize, &stHeader);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

	pszReqPath = stHeader.pszUrl;
	pszReferer = stHeader.pszReferer;
    pszReqParam = strchr(stHeader.pszUrl, '?');
    if (NULL != pszReqParam)
    {
        *pszReqParam = '\0';
        pszReqParam++;
    }

    print_level(SV_INFO, "%s %s\n%s\n", stHeader.pszMethod, pszReqPath, stHeader.pu8Body);
    if (NULL != strcasestr(pszReqPath, "config"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = JSON_HDL_GetConfig(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth && (NULL != pszReferer && NULL== strcasestr(pszReferer, "no_password.html")))
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_SetConfig(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
	else if (NULL != strcasestr(pszReqPath, "guimask"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_SetGuiMask(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "special_version"))    /*Special Version 专用版本*/
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Ret = JSON_HDL_GetSpecialVesion(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_SetSpecialVesion(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "exhibition"))    /* 展会专用版设置 */
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_SetExhibitionVesion(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#if (defined(BOARD_DMS31V2) || defined(DMS31SDK) || defined(BOARD_ADA32V2) || defined(ADA32SDK))
    else if (NULL != strcasestr(pszReqPath, "playback"))    /* 控制回播 */
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_CtrlPlayback(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#endif
    else if (NULL != strcasestr(pszReqPath, "change_spsnum"))    /* 修改客户的序列号（前半） */
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_ChangeSpsNum(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "change_artnum"))    /* 修改客户的序列号（后半） */
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_ChangeArtNum(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "ping_netaddr"))   /* Ping 相应的IP地址 */
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = JSON_HDL_PingNetAddr(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "image_param"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = JSON_HDL_GetImageParam(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_SetImageParam(stHeader.pu8Body, szJsonBody);
        }
    }
    else if (NULL != strcasestr(pszReqPath, "get_devtime"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = JSON_HDL_GetDevTime(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "test_wifi"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = JSON_HDL_GetTestWifiParam(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            //HTTP_HDL_GenerateSuccessReply("{}", (char *)pu8Result);
            s32Res = JSON_HDL_SetTestWifiParam(stHeader.pu8Body, szJsonBody);
            //return strlen((char *)pu8Result);
        }
    }
    else if (NULL != strcasestr(pszReqPath, "test_gps"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = JSON_HDL_GetTestGpsParam(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            //HTTP_HDL_GenerateSuccessReply("{}", (char *)pu8Result);
            s32Res = JSON_HDL_SetTestGpsParam(stHeader.pu8Body, szJsonBody);
            //return strlen((char *)pu8Result);
        }
    }
	else if (NULL != strcasestr(pszReqPath, "test_ipc"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_TestIpc(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
#if defined(BOARD_DMS31V2)
    else if (NULL != strcasestr(pszReqPath, "ddawAlarm"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_DDAWAlarm(stHeader.pu8Body, szJsonBody);
        }
        else
		{
			print_level(SV_ERROR, "not support test_ddawAlarm with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
    }
    else if (NULL != strcasestr(pszReqPath, "user_alarm"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_UserAlarm(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support dms_alarm_record with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
#endif
#if 0
    else if (NULL != strcasestr(pszReqPath, "collect_imu"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = JSON_HDL_GetCollectIMUParam(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    return strlen((char *)pu8Result);
                }
            }
            //HTTP_HDL_GenerateSuccessReply("{}", (char *)pu8Result);
            s32Res = JSON_HDL_SetCollectIMUParam(stHeader.pu8Body, szJsonBody);
            //return strlen((char *)pu8Result);
        }
    }
#endif
    else if (NULL != strcasestr(pszReqPath, "restore_factory"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_RestoreFactory(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "reboot"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_RebootSystem(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
	else if (NULL != strcasestr(pszReqPath, "quit_factory"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
			s32Res = JSON_HDL_QuitFactory(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support quit_factory with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "query_status"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_QueryStatus(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
	else if (NULL != strcasestr(pszReqPath, "alarm_log"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_AlarmLog(stHeader.pu8Body, szJsonBody); //读取alarmlog.csv，文件不存在也返回success
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "update_ready"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_UpdateReady(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "change_stateinfo"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_ChangeState(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "change_pwd"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_ChangePwd(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
	else if (NULL != strcasestr(pszReqPath, "lockInTime"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_LockInTime(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "scan_code"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_ScanCode(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "delete_code"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_DeleteCode(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "open_dhcp"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_OpenDhcp(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "open_telnetd"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_OpenTelnet(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "format"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_FormatSd(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "repair_file_sys"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_RepairFileSys(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "repair_partition"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_RepairPartition(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
    else if (NULL != strcasestr(pszReqPath, "qrcode_calibration"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                    if (MSG_SUCCESS_RES != s32Res)
                    {
                        print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                        HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                        goto error_exit;
                    }
            }
            s32Res = JSON_HDL_QRCodeCalibration(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
	else if (NULL != strcasestr(pszReqPath, "calibration"))
	{
	    if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth && BOARD_IsNotCustomer(BOARD_C_DMS31V2_202018)) // dms31 202018客户不验证
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_Calibration(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "face_recognition"))
	{
	    if (0 == strcmp(stHeader.pszMethod, "POST"))
		{
			if (!bAuth)
			{
				s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
				if (MSG_SUCCESS_RES != s32Res)
				{
					print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
					HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
				}
			}
			s32Res = JSON_HDL_FaceRecognition(stHeader.pu8Body, szJsonBody);
		}
		else
		{
			print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
			s32Res =  MSG_NOTSUPPORT;
		}
	}
	else if (NULL != strcasestr(pszReqPath, "resetPwd"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = JSON_HDL_ResetPwd(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support resetPwd with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) \
   || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    else if (NULL != strcasestr(pszReqPath, "display_userpic"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Ret = JSON_HDL_InsertUserPic(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#endif
    else if (NULL != strcasestr(pszReqPath, "test"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Res = JSON_HDL_Test(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support test with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    else if (NULL != strcasestr(pszReqPath, "TrackPosition"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Ret = JSON_HDL_DumpTrackPoint(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support TrackPosition with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#endif
#if (defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) || defined(BOARD_IPTR20S1))
    else if (NULL != strcasestr(pszReqPath, "ptz"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Ret = JSON_HDL_GetPtz(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    return strlen((char *)pu8Result);
                }
            }
            s32Res = JSON_HDL_SetPtz(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#if (!defined(BOARD_IPTR20S1))
    else if (NULL != strcasestr(pszReqPath, "af_cal"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    return strlen((char *)pu8Result);
                }
            }
            s32Res = JSON_HDL_SetAFCal(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
	else if (NULL != strcasestr(pszReqPath, "af_auto"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    return strlen((char *)pu8Result);
                }
            }
            s32Res = JSON_HDL_AFTest(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#endif
#endif
#if (defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
    else if (NULL != strcasestr(pszReqPath, "stepmotor"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Ret = JSON_HDL_GetStepmotor(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    return strlen((char *)pu8Result);
                }
            }
            s32Res = JSON_HDL_SetStepmotor(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#endif
#if (defined(BOARD_HDW845V1))
    else if (NULL != strcasestr(pszReqPath, "zoom"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Ret = JSON_HDL_GetZoom(stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    return strlen((char *)pu8Result);
                }
            }
            s32Res = JSON_HDL_SetZoom(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support config with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }
#endif
#if (defined(BOARD_IPCR20S3))
	else if((strncmp(pszReqPath, "/Get", 4) == 0 || strncmp(pszReqPath, "/Set", 4) == 0)&& NULL != strcasestr(pszReqPath,".cgi"))
	{
		if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = HTTP_R2P_GetCgi(pszReqPath,stHeader.pu8Body, szJsonBody);
        }
        else if (0 == strcmp(stHeader.pszMethod, "POST") && NULL != pszReqParam)
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    return strlen((char *)pu8Result);
                }
            }
            s32Res = HTTP_R2P_SetCgi(pszReqPath,pszReqParam, szJsonBody);

        }
        else
        {
            print_level(SV_ERROR, "not support cgi with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
	}
#endif
#if (defined(BOARD_ADA47V1)) /* ada47 配置项自定义 */
	else if(NULL != strcasestr(pszReqPath, "customAlg"))
	{
		if (0 == strcmp(stHeader.pszMethod, "GET"))
		{
			s32Res = JSON_HDL_GetCustomAlg(stHeader.pu8Body, szJsonBody);
		}
		else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Ret = JSON_HDL_SetCustomAlg(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support customAlg with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
	}
	else if(NULL != strcasestr(pszReqPath, "plugList"))
	{
		if (0 == strcmp(stHeader.pszMethod, "GET"))
		{
			s32Res = JSON_HDL_GetPlugList(stHeader.pu8Body, szJsonBody);
		}
		else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Ret = JSON_HDL_SetPlugList(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support plugList with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
	}
	else if(NULL != strcasestr(pszReqPath, "plugConf"))
	{
		if (0 == strcmp(stHeader.pszMethod, "GET"))
		{
			s32Res = JSON_HDL_GetPlugConfig(pszReqParam, stHeader.pu8Body, szJsonBody);
		}
		else if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Ret = JSON_HDL_SetPlugConfig(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support plugConf with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
	}
	else if(NULL != strcasestr(pszReqPath, "deletePlug"))
	{
		if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    goto error_exit;
                }
            }
            s32Ret = JSON_HDL_DeletePlug(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support deletePlug with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
	}
#endif
    else
    {
        print_level(SV_WARN, "not support requset: %s %s\n", stHeader.pszMethod, pszReqPath);
        s32Res =  MSG_NOTSUPPORT;
    }

normal_exit:
    if (SV_SUCCESS != s32Res)
    {
        HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
        return strlen((char *)pu8Result);
    }


    HTTP_HDL_GenerateSuccessReply(szJsonBody, (char *)pu8Result);
    return strlen((char *)pu8Result);

error_exit:
    return strlen((char *)pu8Result);
}

sint32 HTTP_HDL_Authentication(uint8 *pu8Message, uint32 u32MsgSize, uint8 *pu8Result, HTTP_SESSION_S *pstSession)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    char szGMTTime[64];
	time_t tNow = time(NULL);
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tNow));
    HTTP_HEADER_S stHeader = {0};
    char szPwd[64] = {0};
    static char szSuperPwd[64] = {0};
    char szBody[1024] = {0};
    char *pszPwd = NULL;
	char *pstJsonTmp = NULL;
    uuid_t au8Uuid;
    char szSid[64];
	char szJsonData[128];
	uint32 u32JsonLen;
	cJSON *pstJson = NULL;
	cJSON *pstTmp = NULL;
	char szUserName[128] = {0};
	char szJsonPwd[128] = {0};
	uint32 u32UseJson = SV_FALSE;
	uint8  u8IsVisitor = SV_FALSE;
	uint8  u8PwdRight = SV_FALSE;
	int i = 0;

    s32Ret = HTTP_HDL_ParseHeader(pu8Message, u32MsgSize, &stHeader);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
        HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, (char *)pu8Result);
        return s32Ret;
    }

	pstJsonTmp = strstr((char *)stHeader.pu8Body, "}");
	if(NULL != pstJsonTmp)
	{
		u32JsonLen = pstJsonTmp+ 1 - (char*)stHeader.pu8Body;
		strncpy(szJsonData, (char *)stHeader.pu8Body, u32JsonLen);
		szJsonData[u32JsonLen] = '\0';
	    pstJson = cJSON_Parse(szJsonData);
	    if (NULL == pstJson)
	    {
	        print_level(SV_ERROR, "Password json parse failed.\n");
	    }

		pstTmp = cJSON_GetObjectItemCaseSensitive(pstJson, "pwd");
		if (NULL != pstTmp)
		{
			strcpy(szJsonPwd, pstTmp->valuestring);
		}

		pstTmp = cJSON_GetObjectItemCaseSensitive(pstJson, "usr");
		if (NULL != pstTmp)
		{
		 	strcpy(szUserName, pstTmp->valuestring);
		}
		if(0 == strcmp("visitor",szUserName))
		{
			u8IsVisitor = SV_TRUE;
			cJSON_Delete(pstJson);
			goto is_visitor;
		}

		cJSON_Delete(pstJson);
		print_level(SV_DEBUG,"login jsondata username:%s pwd:%s \n",szUserName,szJsonPwd);
		u32UseJson = SV_TRUE;
	}
    else
    {
		u32UseJson = SV_FALSE;
    }

    pszPwd = strstr((char *)stHeader.pu8Body, "pwd=");
    if (NULL == pszPwd && SV_FALSE == u32UseJson)
    {
        print_level(SV_ERROR, "cannot found pwd keyword.\n");
    	HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, (char *)pu8Result);
    	return SV_FAILURE;
    }

    if (strlen(szSuperPwd) == 0)
    {
        if (0 == access(CONFIG_SUPER_PWD, F_OK))
        {
            s32Fd = open(CONFIG_SUPER_PWD, O_RDONLY);
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "open %s failed. [err: %s]\n", CONFIG_SUPER_PWD, strerror(errno));
            }
            else
            {
                read(s32Fd, szSuperPwd, 64);
                close(s32Fd);
            }
        }
    }

    if(NULL != pszPwd)
	{
		pszPwd += 4;
    	strcpy(szPwd, pszPwd);
    }

	if(NULL == pszPwd && SV_TRUE == u32UseJson)
	{
		strcpy(szPwd, szJsonPwd);
	}

    print_level(SV_DEBUG, "pwd: %s\n", szPwd);

	for(i = 0;i<3;i++)
	{
		if(0 == strcmp(m_szUserPassword[i][0],szUserName) && 0 == strcmp(m_szUserPassword[i][2],szPwd))
		{
			u8PwdRight = SV_TRUE;
			break;
		}
	}
is_visitor:
    //print_level(SV_DEBUG, "m_szAdminPassword: %s\n", m_szAdminPassword);
    if ((u8PwdRight == SV_FALSE || (0 != strlen(szSuperPwd) && 0 == strcmp(szSuperPwd, szPwd))) && SV_FALSE == u8IsVisitor)
    {
        print_level(SV_ERROR, "login password error!\n");
        HTTP_HDL_ParseError(MSG_AUTHORITY_FAIL, szBody);
    	snprintf((char *)pu8Result, HTTP_BUF_SIZE - 1, "HTTP/1.1 401 Unauthorized \r\n"
    							"Content-Type: application/json; charset=UTF-8 \r\n"
    							"Content-Length: %d \r\n\r\n", strlen(szBody));
    	strcat((char *)pu8Result, szBody);
    	return SV_FAILURE;
    }
    else
    {
        uuid_generate(au8Uuid);
        uuid_unparse(au8Uuid, szSid);
        print_level(SV_DEBUG, "ssid: %s\n", szSid);
        strcpy(szBody, "{}");
    	snprintf((char *)pu8Result, HTTP_BUF_SIZE - 1, "HTTP/1.1 200 OK \r\n"
    	                        "Date: %s \r\n"
    	                        "Set-Cookie: sid=%s \r\n"
    							"Content-Type: application/json; charset=UTF-8 \r\n"
    							"Content-Length: %d \r\n\r\n", szGMTTime, szSid, strlen(szBody));
    	strcat((char *)pu8Result, szBody);
       	pstSession->bValid = SV_TRUE;
       	pstSession->tLastTime = tNow;
       	strcpy(pstSession->szSid, szSid);
    }
    print_level(SV_DEBUG, "%s\n", pu8Result);

    return SV_SUCCESS;
}

sint32 HTTP_HDL_QueryLog(SV_BOOL bAuth, uint8 *pu8Message, uint32 u32MsgSize, uint8 *pu8Result, uint32 *pu32ResLen)
{
    sint32 s32Ret = 0, s32Res = 0;
    char *pszReqPath = NULL;
    char *pszReqParam = NULL;
    HTTP_HEADER_S stHeader = {0};
    char szJsonBody[10*1024];

    if (NULL == pu8Message || NULL == pu8Result)
    {
        return ERR_NULL_PTR;
    }

    if (u32MsgSize == 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pu8Message[u32MsgSize] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(pu8Message, u32MsgSize, &stHeader);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    pszReqPath = stHeader.pszUrl;
    pszReqParam = strchr(stHeader.pszUrl, '?');
    if (NULL != pszReqParam)
    {
        *pszReqParam = '\0';
        pszReqParam++;
    }

    print_level(SV_DEBUG, "%s %s\n%s\n", stHeader.pszMethod, pszReqPath, stHeader.pu8Body);
    if (NULL != strcasestr(pszReqPath, "query_log"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    *pu32ResLen = strlen((char *)pu8Result);
                    return s32Res;
                }
            }
            s32Res = JSON_HDL_QueryLog(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }

    if (SV_SUCCESS != s32Res)
    {
        HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
        *pu32ResLen = strlen((char *)pu8Result);
        return s32Res;
    }

    HTTP_HDL_GenerateSuccessReply(szJsonBody, (char *)pu8Result);
    *pu32ResLen = strlen((char *)pu8Result);

    return SV_SUCCESS;
}

sint32 HTTP_HDL_QueryRecord(SV_BOOL bAuth, uint8 *pu8Message, uint32 u32MsgSize, uint8 *pu8Result, uint32 *pu32ResLen)
{
    sint32 s32Ret = 0, s32Res = 0;
    char *pszReqPath = NULL;
    char *pszReqParam = NULL;
    HTTP_HEADER_S stHeader = {0};
    char szJsonBody[10*1024];

    if (NULL == pu8Message || NULL == pu8Result)
    {
        return ERR_NULL_PTR;
    }

    if (u32MsgSize == 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pu8Message[u32MsgSize] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(pu8Message, u32MsgSize, &stHeader);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    pszReqPath = stHeader.pszUrl;
    pszReqParam = strchr(stHeader.pszUrl, '?');
    if (NULL != pszReqParam)
    {
        *pszReqParam = '\0';
        pszReqParam++;
    }

    print_level(SV_DEBUG, "%s %s\n%s\n", stHeader.pszMethod, pszReqPath, stHeader.pu8Body);
    if (NULL != strcasestr(pszReqPath, "query_record"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            if (!bAuth)
            {
                s32Res = JSON_HDL_Authentication(stHeader.pu8Body);
                if (MSG_SUCCESS_RES != s32Res)
                {
                    print_level(SV_WARN, "JSON_HDL_Authentication failed.\n");
                    HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
                    *pu32ResLen = strlen((char *)pu8Result);
                    return s32Res;
                }
            }
            s32Res = JSON_HDL_QueryRecord(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  MSG_NOTSUPPORT;
        }
    }

    if (SV_SUCCESS != s32Res)
    {
        HTTP_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
        *pu32ResLen = strlen((char *)pu8Result);
        return s32Res;
    }

    HTTP_HDL_GenerateSuccessReply(szJsonBody, (char *)pu8Result);
    *pu32ResLen = strlen((char *)pu8Result);

    return SV_SUCCESS;
}

sint32 HTTP_HDL_ReplySnapJpeg(sint32 socketFd)
{
    sint32 s32Ret = 0;
    sint32 s32ImgSize = 0;
    char *pszContentType = NULL;
    char szETag[32];
    char szGMTTime[64];
    char szModifyTime[64];
    char szBuf[16*1024];
    time_t tNow;
    static time_t tLast = 0;


    pthread_mutex_lock(&m_stSnapInfo.mutexSnap);
    s32ImgSize = m_stSnapInfo.s32ImgSize;
    pszContentType = "image/jpeg";
    tNow = time(NULL);
    if (tLast == 0)
        tLast = tNow;
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tNow));
    strftime(szModifyTime, sizeof(szModifyTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tLast));
    HTTP_HDL_GenerateETag("snap0", s32ImgSize, (sint32)tLast, szETag);
    sprintf(szBuf, 	"HTTP/1.1 200 OK \r\n"
					"Content-Type: %s \r\n"
					"Last-Modified: %s\r\n"
					"ETag: \"%s\"\r\n"
					"Date: %s\r\n"
                    "Access-Control-Allow-Origin: *\r\n"
					"Content-Length: %d \r\n\r\n", pszContentType, szModifyTime, szETag, szGMTTime, s32ImgSize);

    s32Ret = send(socketFd, szBuf, strlen(szBuf), 0);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "send header to client failed. [err=%#x]\n", errno);
    }

    //print_level(SV_DEBUG, "s32ImgSize=%d\n", s32ImgSize);
    s32Ret = send(socketFd, m_stSnapInfo.apvImgBuf, s32ImgSize, 0);
    if (s32Ret < 0)
    {
        print_level(SV_WARN, "send body to client failed. [err=%#x]\n", errno);
    }
    tLast = tNow;


    pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
    return SV_SUCCESS;
}

sint32 HTTP_HDL_ReplyByFile(SV_BOOL bMg, struct mg_connection *nc, sint32 socketFd, char *pszFilePath, sint32 s32BeginOffset, sint32 s32EndOffset)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    sint32 s32Size = 0;
    uint32 u32SendSize = 0;
    sint32 s32LeftSize = 0;
    char *pszContentType = NULL;
    char szFilePath[256];
    char szETag[32];
    char szGMTTime[64];
    char szModifyTime[64];
    char szBuf[16*1024];
    time_t tNow, tLast;
    SV_BOOL bRange = SV_FALSE;
    struct stat stFileInfo = {0};

	if (bMg && NULL == nc)
	{
        print_level(SV_ERROR, "get null nc.\n");
        return SV_FAILURE;
    }

    if (strlen(pszFilePath) > 250)
    {
        print_level(SV_ERROR, "invalied pszFilePath:%s.\n", pszFilePath);
        goto fail_exit;
    }

    strcpy(szFilePath, pszFilePath);
#if 0
    if(NULL != strcasestr(pszFilePath, "snap0.jpeg"))
    {
        sprintf(szFilePath, "%s_tmp", pszFilePath);
        rename(pszFilePath, szFilePath);
    }
#endif

    if (NULL != strcasestr(szFilePath, HTTP_RESOURCE_PATH) && NULL != strcasestr(szFilePath, ".mp4"))
    {
        /* 直接从SD卡里读录像文件 */
        strcpy(szFilePath, pszFilePath+strlen(HTTP_RESOURCE_PATH));
    }

    s32Ret = stat(szFilePath, &stFileInfo);
    if (s32Ret < 0)
    {
        print_level(SV_WARN,"stat file: %s failed. [err=%#x]\n", szFilePath, errno);
        goto fail_exit;
    }


    s32Fd = open(szFilePath, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err=%#x]\n", szFilePath, errno);
        goto fail_exit;
    }

    if (NULL != strcasestr(szFilePath, ".html") || NULL != strcasestr(szFilePath, ".htm"))
    {
        pszContentType = "text/html";
    }
    else if (NULL != strcasestr(szFilePath, ".css"))
    {
        pszContentType = "text/css";
    }
    else if (NULL != strcasestr(szFilePath, ".json"))
    {
        pszContentType = "application/json";
    }
    else if (NULL != strcasestr(szFilePath, ".js"))
    {
        pszContentType = "application/x-javascript";
    }
    else if (NULL != strcasestr(szFilePath, ".jpg") || NULL != strcasestr(szFilePath, ".jpeg"))
    {
        pszContentType = "image/jpeg";
    }
    else if (NULL != strcasestr(szFilePath, ".png"))
    {
        pszContentType = "image/png";
    }
    else if (NULL != strcasestr(szFilePath, ".gif"))
    {
        pszContentType = "image/gif";
    }
    else if (NULL != strcasestr(szFilePath, ".mp4"))
    {
        pszContentType = "video/mp4";
    }
    else if (NULL != strcasestr(szFilePath, ".swf"))
    {
        pszContentType = "application/x-shockwave-flash";
    }
    else if (NULL != strcasestr(szFilePath, ".svg"))
    {
        pszContentType = "image/svg+xml";
    }
    else if (NULL != strcasestr(szFilePath, ".vtt"))
    {
        pszContentType = "text/vtt";
    }
    else if (NULL != strcasestr(szFilePath, ".txt"))
    {
        pszContentType = "text/txt";
    }
    else if (NULL != strcasestr(szFilePath, ".ico"))
    {
        pszContentType = "image/x-icon";
    }
    /*
    else if (NULL != strcasestr(szFilePath, ".flv"))
    {
        pszContentType = "video/x-flv";
    }
    */
    else
    {
        pszContentType = "text/plain";
    }


    if (s32BeginOffset != -1 && s32BeginOffset < stFileInfo.st_size && s32EndOffset < stFileInfo.st_size)
    {
		s32EndOffset = (s32EndOffset == -1) ? stFileInfo.st_size-1 : s32EndOffset;
		bRange = SV_TRUE;
		print_level(SV_INFO, "request %s range: %d-%d\n", szFilePath, s32BeginOffset, s32EndOffset);
    }


    HTTP_HDL_GenerateETag(szFilePath, 0, 0, szETag);
    tLast = stFileInfo.st_mtime;
    tNow = time(NULL);
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tNow));
    strftime(szModifyTime, sizeof(szModifyTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tLast));
    if (bRange)
    {
    	sprintf(szBuf, 	"HTTP/1.1 206 Partial Content\r\n"
						"Content-Type: %s \r\n"
						"Last-Modified: %s\r\n"
						"Accept-Ranges: bytes\r\n"
						"ETag: \"%s\"\r\n"
						"Date: %s\r\n"
						"Content-Length: %d \r\n"
                        "Access-Control-Allow-Origin: *\r\n"
						"Content-Range: bytes %d-%d/%d\r\n\r\n", pszContentType, szModifyTime, szETag, szGMTTime, s32EndOffset - s32BeginOffset +1, \
														s32BeginOffset, s32EndOffset, (sint32)stFileInfo.st_size);
    }
    else
    {
	    sprintf(szBuf, 	"HTTP/1.1 200 OK \r\n"
						"Content-Type: %s \r\n"
						"Last-Modified: %s\r\n"
						"ETag: \"%s\"\r\n"
						"Date: %s\r\n"
                        "Access-Control-Allow-Origin: *\r\n"
						"Content-Length: %d \r\n\r\n", pszContentType, szModifyTime, szETag, szGMTTime, (sint32)stFileInfo.st_size);
	}


	//print_level(SV_DEBUG, "[%s] reply header:\n%s", szFilePath, szBuf);
    s32Ret = mg_http_send(bMg, nc, socketFd, szBuf, strlen(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "send header to client failed. [err=%#x]\n", errno);
    }

    if (bRange)
    {
    	lseek(s32Fd, s32BeginOffset, SEEK_SET);
    }


    s32Size = read(s32Fd, szBuf, 16*1024);
    while (s32Size > 0)
    {
    	if (bRange)
    	{
    		s32LeftSize = s32EndOffset - s32BeginOffset + 1 - u32SendSize;
    		if (s32LeftSize < s32Size)
    		{
    			s32Size = s32LeftSize;
    		}
    		if (s32LeftSize <= 0)
    		{
    			break;
    		}
    	}

        s32Ret = mg_http_send(bMg, nc, socketFd, szBuf, s32Size);
        if (s32Ret < 0)
        {
            print_level(SV_WARN, "send body to client failed. [err=%s]\n", strerror(errno));
            if (EPIPE == errno)
            	break;
        }

        u32SendSize += s32Size;
        s32Size = read(s32Fd, szBuf, 16*1024);
    }
    close(s32Fd);


    return SV_SUCCESS;

fail_exit:
    tNow = time(NULL);
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tNow));
    sprintf((char *)szBuf, "HTTP/1.1 404 Not Found \r\n"
						    "Content-Type: text/plain \r\n"
						    "Date: %s\r\n"
						    "Content-Length: 0 \r\n\r\n", szGMTTime);

    s32Ret = mg_http_send(bMg, nc, socketFd, szBuf, strlen((char*)szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "send header to client failed. [err=%#x]\n", errno);
    }

    return SV_FAILURE;
}

sint32 HTTP_HDL_ReplyByMultipartFormFile(SV_BOOL bMg, struct mg_connection *nc, sint32 socketFd, uint8 *pu8Buf, char *pszFilePath)
{
    sint32 s32Ret = 0, i = 0;
    time_t now = time(NULL);
    char szGMTTime[64];
    char szHttpHeader[1024];
    char boundary[]="----ZnGpDtePMx0KrHh_G0X99Yef9r8JZsRJSXC";
    char name[]="file";
    char *filename = NULL;
    char szFileText[1024]="12314564654114564asasdfas;dfklasjfaslknlkaskldfashfjdas999";
    int filesize = 0;
    int nReadbytes = 0;
    int nOffset = 0;
    int nSize = 0;
    FILE *fp = NULL;
    sint32 s32MsgRet = MSG_SUCCESS_RES;

    if ((bMg && NULL == nc) || socketFd <= 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pu8Buf || NULL == pszFilePath)
    {
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

#if defined(BOARD_DMS31V2)
	char *pszDir[3] = {"/tmp/sdcard", "/mnt/sdcard", "/mnt/udisk"};
	char szPath[256] = {0};
	sint32 s32PathLen = strlen(pszFilePath);
	sint32 s32Index = -1;
	char *tmp = NULL;

	if ((NULL == strstr(pszFilePath, ".mp4") && NULL == strstr(pszFilePath, ".avi") && NULL == strstr(pszFilePath, ".jpg"))
		|| NULL == strstr(pszFilePath, pszDir[0]))
	{
		goto check_file_exit;
	}

	strncpy(szPath, pszFilePath, s32PathLen);
	if (COMMON_IsPathExist(szPath))
	{
		goto check_file_exit;
	}

	for (i = 0; i < 3; i++)
	{
		tmp = strstr(pszFilePath, pszDir[i]);
		if (NULL != tmp)
		{
			s32Index = i;
			break;
		}
	}

	for (i = s32Index+1; i < 3; i++)
	{
		strcpy(szPath, pszDir[i]);
		strncat(szPath, tmp+strlen(pszDir[s32Index]), s32PathLen-strlen(pszDir[s32Index]));

		if (COMMON_IsPathExist(szPath))
		{
			print_level(SV_INFO, "change file to read: %s\n", szPath);
			memset(pszFilePath, 0, s32PathLen);
			memcpy(pszFilePath, szPath, strlen(szPath));
			break;
		}
	}

check_file_exit:;
#endif

    filename = strrchr(pszFilePath, '/');
    if (NULL == filename)
    {
        print_level(SV_ERROR, "filepath: %s is not full path.\n", pszFilePath);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

	filename++;
	fp = fopen(pszFilePath, "rb+");
	if(NULL == fp)
	{
        print_level(SV_ERROR, "fopen:%s fail!\n", pszFilePath);
		s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
	}
	fseek(fp,0,SEEK_END);
	filesize = ftell(fp);

    nSize += strlen("--%s\r\n") + strlen(boundary) - 2;
    nSize += strlen("Content-Disposition: form-data; name=\"%s\"; filename=\"%s\"\r\n") + strlen(name) + strlen(filename) - 4;
    nSize += strlen("Content-Type: application/octet-stream\r\n\r\n");

	nSize += filesize;
	nSize += strlen("\r\n--%s--") + strlen(boundary) - 2;
	strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&now));
	memset(szHttpHeader, 0x0, sizeof(szHttpHeader));
    sprintf(szHttpHeader,
    		"HTTP/1.1 200 OK\r\n"
			"Date: %s\r\n"
			"Access-Control-Allow-Origin:*\r\n"
			"Content-type: multipart/form-data; boundary=--%s\r\n"
			"Content-Length: %d\r\n\r\n"
    ,szGMTTime,boundary,nSize);

    memset(pu8Buf, 0x0, HTTP_BUF_SIZE);
    strcpy((char *)pu8Buf,szHttpHeader);
    s32Ret = mg_http_send(bMg, nc, socketFd, pu8Buf, strlen((char *)pu8Buf));
    if (s32Ret <= 0)
    {
        print_level(SV_INFO, "send size: %d\n", s32Ret);
        fclose(fp);
        return ERR_BADADDR;
    }

	nSize = 0;
	memset(szHttpHeader, 0x0, sizeof(szHttpHeader));
	nSize += sprintf(szHttpHeader+nSize, "--%s\r\n", boundary);
	nSize += sprintf(szHttpHeader+nSize, "Content-Disposition: form-data; name=\"%s\"; filename=\"%s\"\r\n\r\n", name, filename);
	nSize += sprintf(szHttpHeader+nSize, "Content-Type: application/octet-stream\r\n\r\n");
	memset(pu8Buf, 0x0, HTTP_BUF_SIZE);
	strcpy((char *)pu8Buf,szHttpHeader);
    s32Ret = mg_http_send(bMg, nc, socketFd, pu8Buf, strlen((char *)pu8Buf));
    if (s32Ret <= 0)
    {
        print_level(SV_INFO, "send size: %d\n", s32Ret);
        fclose(fp);
        return ERR_BADADDR;
    }

	while(1)
	{
		fseek(fp, nOffset, SEEK_SET);
		memset(pu8Buf, 0x0, HTTP_BUF_SIZE);
		nReadbytes = fread((char *)pu8Buf, sizeof(char), 1024, fp);
		nOffset += nReadbytes;
		if(nReadbytes <= 0)
		{
			fclose(fp);
			break;
		}
		else
		{
            s32Ret = mg_http_send(bMg, nc, socketFd, pu8Buf, nReadbytes);
			if (s32Ret <= 0)
			{
				print_level(SV_ERROR, "libhttp send errno. [err: %s]\n", strerror(errno));
				if (errno != EINTR || errno != EWOULDBLOCK || errno != EAGAIN)
                {
                	print_level(SV_ERROR, "clinet disconnect!! close current client now. [err: %s]\n", strerror(errno));
                    fclose(fp);
                    return ERR_NOT_PERM;    // 原来是break，改为return是防止现在nc已经被释放了，后续还继续send
                }
            }
		}
	}

	memset(szHttpHeader, 0x0, sizeof(szHttpHeader));
	nSize = sprintf(szHttpHeader, "\r\n--%s--", boundary);
	memset(pu8Buf, 0x0, HTTP_BUF_SIZE);
	strcpy((char *)pu8Buf,szHttpHeader);
    mg_http_send(bMg, nc, socketFd, pu8Buf, strlen((char *)pu8Buf));

	return SV_SUCCESS;

error_exit:
    HTTP_HDL_GenerateErrorReply(s32MsgRet, pu8Buf);
    mg_http_send(bMg, nc, socketFd, pu8Buf, strlen((char *)pu8Buf));
    return ERR_ILLEGAL_PARAM;
}

sint32 HTTP_HDL_QuickJpeg(sint32 s32Quality)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0};
    static sint32 s32LastQuality = -1;

    if (s32Quality < 0)
        s32Quality = -1;
    if (s32Quality > 5)
        s32Quality = 5;

    //print_level(SV_DEBUG, "s32LastQuality=%d, s32Quality=%d\n", s32LastQuality, s32Quality);
    if (s32LastQuality == s32Quality)
    {
        return SV_SUCCESS;
    }
    print_level(SV_DEBUG, "s32LastQuality=%d, s32Quality=%d\n", s32LastQuality, s32Quality);

    stMsgPkt.stMsg.s32Param = s32Quality;
    stMsgPkt.stMsg.u16OpCode = OP_EVENT_QUICKJPEG;
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_QUICKJPEG, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    s32LastQuality = s32Quality;

    return SV_SUCCESS;
}

sint32 HTTP_HDL_NewJpeg(sint32 s32Quality)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0};

    stMsgPkt.stMsg.s32Param = s32Quality;
    s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_NEWJPEG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 HTTP_HDL_FLV(sint32 socketFd)
{
#if (defined(BOARD_IPCR20S3) || defined(BOARD_WFCR20S2) || defined(BOARD_IPCR20S4) || defined(BOARD_WFTR20S3))

    sint32 s32Ret = 0;
    sint32 s32Size = 0;
    uint32 u32Cnt = 0;
    uint32 u32Serial = 0;
    int iMetaTagSize = 0;
    int iAVTagSize = 0;
	int iVideoTagSize = 0;
    int iAATagSize = 0;
    int iAudioTagSize = 0;
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    SFIFO_MSHEAD *pstPacket = NULL;

    uint8 *pu8Buf = NULL;         // 写入flv缓存
    uint8 *pu8PCMBuf = NULL;      // 输入pcm数据缓存
    uint8 *pu8AACBuf = NULL;      // 输出aac数据缓存
    uint32 nChannels = 1;         // PCM数据声道数
    uint32 nPCMBitSize = 16;      // PCM数据单样本采样深度
    uint32 nSampleRate = 16000;   // PCM数据采样率
    uint32 nInputSize = 0;        // 单次输入数据大小
    uint32 nInputSamples = 0;     // 单次输入样本数
    uint32 nPCMBufSize = 0;       // 输入缓存大小
    uint32 nMaxOutputBytes = 0;   // 输出缓存大小

    int nRet;                     // 单次编码生成aac数据长度
    faacEncHandle hEncoder;
    faacEncConfigurationPtr pConfiguration;

    s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }

    s32Ret = SFIFO_GetMediaAttr(s32MainQueId, &stMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_GetMediaAttr stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }

	pu8Buf = (uint8 *)malloc(512*1024);
	if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc: pu8Buf failed.\n");
        return;
    }

    /* 获取当前时间 */
    time_t now = time(NULL);
    char szGMTTime[64];
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&now));

    /* 发送http响应头 */
    #ifdef HTTP_FLV_CHUNKED
    sprintf((char *)pu8Buf, "HTTP/1.1 200 OK\r\n"
                            //"Server: x264-HTTP\r\n"
                            "Server: Microsoft-HTTPAPI/2.0\r\n"
                            "Content-Type: application/octet-stream\r\n"
                            "Transfer-Encoding: chunked\r\n"
                            "Connection: keep-alive\r\n"
                            "Cache-Control: no-cache, no-store\r\n"
                            "Access-Control-Allow-Origin: *\r\n"
                            "Date: %s\r\n"
                            "\r\n", szGMTTime);
    #else
    sprintf((char *)pu8Buf, "HTTP/1.1 200 OK\r\n"
                            //"Server: x264-HTTP\r\n"
                            "Server: Microsoft-HTTPAPI/2.0\r\n"
                            "Content-Type: video/x-flv\r\n"
                            "Connection: keep-alive\r\n"
                            "Cache-Control: no-cache, no-store\r\n"
                            "Access-Control-Allow-Origin: *\r\n"
                            "Date: %s\r\n"
                            "\r\n", szGMTTime);
    #endif
    s32Size = send(socketFd, pu8Buf, strlen((char *)pu8Buf), 0);
    if (s32Size < 0)
    {
        print_level(SV_ERROR, "Sent Http Header to client failed. [err=%#x]\n", errno);
        print_level(SV_ERROR, "Error: %s\n", strerror(errno));
    }

    memset(pu8Buf, 0, 512*1024);
    int iOffset = 0;                                                    // 写入数据位置指示标志，写入flv的各部分时重复使用

    /* 创建FlvHeader */

    //print_level(SV_DEBUG, "Create FlvHeader.\n");
    flv_header_t flv_header = createFlvHeader();

    #ifdef HTTP_FLV_CHUNKED
	uint32 u32HeadChunkedSize = sizeof(flv_header);

	char szChunkedSize[8];
	memset(szChunkedSize, 0, sizeof(szChunkedSize));

	sprintf(szChunkedSize, "%x", u32HeadChunkedSize);
	memcpy(pu8Buf + iOffset, szChunkedSize, strlen(szChunkedSize));
	iOffset += sizeof(szChunkedSize);

	memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Size和Chunk Data之间插入1个"\r\n"
	iOffset += 2;

    memcpy(pu8Buf + iOffset, (char*)&flv_header, sizeof(flv_header));
    iOffset += sizeof(flv_header);

    memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Data的结尾插入1个"/r/n"
	iOffset += 2;

	s32Size = send(socketFd, pu8Buf, iOffset, 0);

	#else
	memcpy(pu8Buf, (char*)&flv_header, sizeof(flv_header));
	s32Size = send(socketFd, pu8Buf, FLV_HEAD_SIZE + FLV_TAG_PRE_SIZE, 0);
    #endif

    if (s32Size < 0)
    {
        print_level(SV_ERROR, "Sent FlvHeader to client failed. [err=%#x]\n", errno);
        print_level(SV_ERROR, "Error: %s\n", strerror(errno));
    }
    //print_level(SV_DEBUG, "FLV Header Sent Size: %d\n", s32Size);

    memset(pu8Buf, 0, 512*1024);
    iOffset = 0;

    /* 创建MetaTag */

    //print_level(SV_DEBUG, "Create MetaTag.\n");
    tag_avInfo_t stAVInfo = {0};

    stAVInfo.avcBitRate = stMediaAttr.stMainStreamAttr.u32Bitrate;
    stAVInfo.avcFrameRate = stMediaAttr.stMainStreamAttr.u32FrameRate;
    stAVInfo.avcWidth = stMediaAttr.stMainStreamAttr.u32Width;
    stAVInfo.avcHeight = stMediaAttr.stMainStreamAttr.u32Height;

    #ifdef HTTP_FLV_CHUNKED
	memset(szChunkedSize, 0, sizeof(szChunkedSize));                    // 初始化Chunk Size字符串
	uint32 u32MetaChunkedSize = 0;
	sprintf(szChunkedSize, "%x", u32MetaChunkedSize);

	memcpy(pu8Buf + iOffset, szChunkedSize, strlen(szChunkedSize));     // 先写入0占位,写完MetaData后再写入实际Size
	iOffset += sizeof(szChunkedSize);

	memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Size和Chunk Data之间插入1个"\r\n"
	iOffset += 2;

    createMetaTag(stAVInfo, pu8Buf + iOffset, &iMetaTagSize);
    iOffset += iMetaTagSize;

    u32MetaChunkedSize = iMetaTagSize;
	sprintf(szChunkedSize, "%x", u32MetaChunkedSize);
	memcpy(pu8Buf, szChunkedSize, strlen(szChunkedSize));               // 写入实际Size覆盖原来写入的0

    memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Data的结尾插入1个"/r/n"
	iOffset += 2;

	s32Size = send(socketFd, pu8Buf, iOffset, 0);
	#else
	createMetaTag(stAVInfo, pu8Buf, &iMetaTagSize);
	s32Size = send(socketFd, pu8Buf, iMetaTagSize, 0);
    #endif

    if (s32Size < 0)
    {
        print_level(SV_ERROR, "Sent MetaTag to client failed. [err=%#x]\n", errno);
        print_level(SV_ERROR, "Error: %s\n", strerror(errno));
    }
    //print_level(SV_DEBUG, "MetaTag Sent Size: %d\n", s32Size);

    memset(pu8Buf, 0, 512*1024);
    iOffset = 0;

    /* 打开faac编码器 */
    hEncoder = faacEncOpen((ulng32)nSampleRate, nChannels, (ulng32*)&nInputSamples, (ulng32*)&nMaxOutputBytes);
    if(hEncoder == NULL)
    {
        print_level(SV_ERROR, "Failed to open faac encoder.\n");
        return -1;
    }
    //print_level(SV_DEBUG, "InputSampleNum: %d MaxOutputSize: %d\n", nInputSamples, nMaxOutputBytes);

    nPCMBufSize = nInputSamples * (nPCMBitSize / 8);

    pu8PCMBuf = (uint8 *)malloc(nPCMBufSize);
    if (NULL == pu8PCMBuf)
    {
        print_level(SV_ERROR, "malloc pu8PCMBuf failed.\n");
        return;
    }

    pu8AACBuf = (uint8 *)malloc(nMaxOutputBytes);
	if (NULL == pu8AACBuf)
    {
        print_level(SV_ERROR, "malloc pu8AACBuf failed.\n");
        return;
    }

    /*获取和修改当前编码器参数设置*/
    pConfiguration = faacEncGetCurrentConfiguration(hEncoder);
    pConfiguration->inputFormat = FAAC_INPUT_16BIT;                     // 输入PCM数据采样深度：16Bit
    pConfiguration->outputFormat = 0;                                   // 输出AAC数据类型，Raw或Adts
    pConfiguration->bitRate = 16000;                                    // 输出AAC数据采样频率，根据输出数据分析，似乎没起作用
    pConfiguration->aacObjectType = 2;                                  // AAC对象类型，设为AAC-LC

    /*更新编码器参数设置*/
    nRet = faacEncSetConfiguration(hEncoder, pConfiguration);

    //print_level(SV_DEBUG, "Resolution: (%dx%d) Fps: %d\n", stMediaAttr.stMainStreamAttr.u32Width, stMediaAttr.stMainStreamAttr.u32Height, stMediaAttr.stMainStreamAttr.u32FrameRate);
    //print_level(SV_DEBUG, "EncType: %d BitRate: %d SampleRate: %d\n", stMediaAttr.stAudioStreamAttr.enCodeType, stMediaAttr.stAudioStreamAttr.u32BitWidth, stMediaAttr.stAudioStreamAttr.u32SampleRate);
    bool bSpsPpsFlag = false;
    bool bSpeCfgFlag = false;
    int iSavPos = 0;                                                    // 编码AAC时分段存储PCM数据的起始位置指示
    #ifdef HTTP_FLV_CHUNKED
    uint32 u32TagChunkedSize = 0;                                       // 一个Chunk Data的大小
    #endif

    while (1)
    {
        s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "SFIFO_GetPacket failed. [err=%#x]\n", s32Ret);
            //print_level(SV_ERROR, "Error: %s\n", strerror(errno));
            sleep_ms(1);
            continue;
        }
        if (pstPacket->type != 2)
        {
            //print_level(SV_DEBUG, "FrameType: %d(%dx%d) pts: %lld, size: %d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->msdsize);
            if (pstPacket->serial != u32Serial + 1)
            {
                print_level(SV_WARN, "Abnormal Frame Serial Number. Present Frame: %d, Last Frame: %d.\n", pstPacket->serial, u32Serial);
            }
            u32Serial = pstPacket->serial;

            uint32 u32pts = ((pstPacket -> pts)/1000)%0xFFFFFF;     // 时间戳

            if(pstPacket->type == 1 && bSpsPpsFlag == false)
            {
                char sps[256];
				char pps[256];
				int iSpsDataLen, iPpsDataLen;

                /* 在H264视频流数据中搜索Sps和Pps */

                //print_level(SV_DEBUG, "Search AVCC Data.\n");
                int iRes = HTTP_SearchSpsPpsData(pstPacket -> data, pstPacket -> msdsize, sps, &iSpsDataLen, pps, &iPpsDataLen);
		        if(iRes != 0)
		        {
			        continue;
		        }

                bSpsPpsFlag = true;
                //print_level(SV_DEBUG, "Find Sps Pps Data Success.\n");

                /* 将Sps和Pps封入第一个VideoTag */
                //print_level(SV_DEBUG, "Create First VideoTag.\n");

                #ifdef HTTP_FLV_CHUNKED
                memset(szChunkedSize, 0, sizeof(szChunkedSize));                    // 初始化Chunk Size字符串
                uint32 u32AVChunkedSize = 0;
                sprintf(szChunkedSize, "%x", u32AVChunkedSize);                     // AVCCData总长度未知, 先初始化Size为0

                memcpy(pu8Buf + iOffset, szChunkedSize, strlen(szChunkedSize));     // 先写入0占位, 写完AVCCData后再写入实际Size
                iOffset += sizeof(szChunkedSize);

                memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Size和Chunk Data之间插入1个"\r\n"
                iOffset += 2;

                createSpsPpsTag(u32pts, sps, iSpsDataLen, pps, iPpsDataLen, pu8Buf + iOffset, &iAVTagSize);
                iOffset += iAVTagSize;

                u32AVChunkedSize = iAVTagSize;
                sprintf(szChunkedSize, "%x", u32AVChunkedSize);
                memcpy(pu8Buf, szChunkedSize, strlen(szChunkedSize));               // 写入实际Size覆盖原来写入的0

                memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Data的结尾插入1个"/r/n"
                iOffset += 2;

                s32Size = send(socketFd, pu8Buf, iOffset, 0);
                #else
                createSpsPpsTag(u32pts, sps, iSpsDataLen, pps, iPpsDataLen, pu8Buf, &iAVTagSize);
                s32Size = send(socketFd, pu8Buf, iAVTagSize, 0);
                #endif

                if (s32Size < 0)
                {
                    print_level(SV_ERROR, "Sent First VideoTag to client failed. [err=%#x]\n", errno);
                    print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                }
                //print_level(SV_DEBUG, "First Video Tag Sent Size: %d\n", s32Size);

                memset(pu8Buf, 0, 512*1024);
				iOffset = 0;
            }
            /*
            else if(pstPacket->type == 0 && bSpsPpsFlag == false)
            {
                print_level(SV_DEBUG, "This is not I frame. Checking next frame.\n");
                //continue;
            }
            */
            else
            {
                /* 创建一般VideoTag的Header */
                //print_level(SV_DEBUG, "Create VideoTag Header.\n");
                tag_header_t tag_header;

                #ifdef HTTP_FLV_CHUNKED
				int iVideoTagDataSize = 1 + 1 + 3 + 4 + pstPacket -> msdsize;       // VideoInfo(1) + AVCPacketType(1) + Compotion Time(3) + NaluSize(4) + StreamSize (include 0x0001)
				/* 计算ChunkData的大小 */
	            u32TagChunkedSize = sizeof(tag_header) + iVideoTagDataSize + FLV_TAG_PRE_SIZE;

                memset(szChunkedSize, 0, sizeof(szChunkedSize));                    // 初始化Size字符串
                sprintf(szChunkedSize, "%x", u32TagChunkedSize);                    // 以16进制字符串格式写入Chunk Size

                memcpy(pu8Buf + iOffset, szChunkedSize, strlen(szChunkedSize));     // 将ChunkedSize写入缓存
                iOffset += sizeof(szChunkedSize);

                memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Size和Chunk Data之间插入1个"\r\n"
                iOffset += 2;

				#else
                int iVideoTagDataSize = 5 + 4 + pstPacket -> msdsize - 4;           // VideoInfo(1) + AVCPacketType(1) + Compotion Time(3) + NaluSize(4) + StreamSize (not include 0x0001)
                #endif

                tag_header = createTagHeader(0x09, iVideoTagDataSize, u32pts, 0, 0);

                /* 创建普通的VideoTag */
                //print_level(SV_DEBUG, "Create VidaoTag.\n");

			    #ifdef HTTP_FLV_CHUNKED
			    createVideoTag(tag_header, pstPacket -> data, pstPacket -> msdsize, pstPacket -> type, 0x01, pu8Buf + iOffset, &iVideoTagSize);
                iOffset += iVideoTagSize;

                memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Data的结尾插入1个"/r/n"
                iOffset += 2;

                s32Size = send(socketFd, pu8Buf, iOffset, 0);
			    #else
			    createVideoTag(tag_header, (pstPacket -> data) + 4, (pstPacket -> msdsize) - 4, pstPacket -> type, 0x01, pu8Buf, &iVideoTagSize);
                s32Size = send(socketFd, pu8Buf, iVideoTagSize, 0);
			    #endif

                memset(pu8Buf, 0, 512*1024);
			    iOffset = 0;

			    if (s32Size < 0)
                {
                    print_level(SV_ERROR, "Sent Video Tag to client failed. [err=%#x]\n", errno);
                    print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                    SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                    break;
                }
			    //print_level(SV_DEBUG, "Video Tag Sent Size: %d\n", s32Size);

			}
        }
        else
        {
            //print_level(SV_DEBUG, "FrameType: %d EncodeType: %d pts: %lld size: %d\n", pstPacket->type, pstPacket->algorithm, pstPacket->pts, pstPacket->msdsize);

            uint32 u32pts = ((pstPacket -> pts)/1000)%0xFFFFFF;                     // 时间戳

            if(bSpeCfgFlag == false)
            {
                //print_level(SV_DEBUG, "Create First AudioTag.\n");

                #ifdef HTTP_FLV_CHUNKED
                memset(szChunkedSize, 0, sizeof(szChunkedSize));                    // 初始化Chunk Size字符串
                uint32 u32AAChunkedSize = 0;
                sprintf(szChunkedSize, "%x", u32AAChunkedSize);                     // SequenceHead总长度未知, 先初始化Size为0

                memcpy(pu8Buf + iOffset, szChunkedSize, strlen(szChunkedSize));     // 先写入0占位, 写完SequenceHead后再写入实际Size
                iOffset += sizeof(szChunkedSize);

                memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Size和Chunk Data之间插入1个"\r\n"
                iOffset += 2;

                createSpeCfgTag(u32pts, pu8Buf + iOffset, &iAATagSize);
                iOffset += iAATagSize;

                u32AAChunkedSize = iAATagSize;
                sprintf(szChunkedSize, "%x", u32AAChunkedSize);
                memcpy(pu8Buf, szChunkedSize, strlen(szChunkedSize));               // 写入实际Size覆盖原来写入的0

                memcpy(pu8Buf + iOffset, "\r\n", 2);                                // 在Chunk Data的结尾插入1个"/r/n"
                iOffset += 2;

                s32Size = send(socketFd, pu8Buf, iOffset, 0);
                #else
                createSpeCfgTag(u32pts, pu8Buf, &iAATagSize);
                s32Size = send(socketFd, pu8Buf, iAATagSize, 0);
                #endif
                if (s32Size < 0)
                {
                    print_level(SV_ERROR, "Sent First AudioTag to client failed. [err=%#x]\n", errno);
                    print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                }
                //print_level(SV_DEBUG, "First Audio Tag Sent Size: %d\n", s32Size);

                bSpeCfgFlag = true;
                memset(pu8Buf, 0, 512*1024);
                iOffset = 0;

                //print_level(SV_DEBUG, "Provisionally save PCM data to Buffer.\n");
                memcpy(pu8PCMBuf + iSavPos, pstPacket->data, pstPacket->msdsize);
                iSavPos += pstPacket->msdsize;
                //print_level(SV_DEBUG, "Write Position: %d\n", iSavPos);
            }
            else
            {
                if(nPCMBufSize - iSavPos > pstPacket->msdsize)
                {
                    //print_level(SV_DEBUG, "Provisionally save PCM data to Buffer.\n");
                    memcpy(pu8PCMBuf + iSavPos, pstPacket->data, pstPacket->msdsize);
                    iSavPos += pstPacket->msdsize;
                    //print_level(SV_DEBUG, "Write Position: %d\n", iSavPos);
                }
                else
                {
                    /* Buffer长度不足，存储此packet数据的一部分写满缓存 */
                    memcpy(pu8PCMBuf + iSavPos, pstPacket->data, nPCMBufSize - iSavPos);

                    /* 编码 */
                    nRet = faacEncEncode(hEncoder, (int*)pu8PCMBuf, nInputSamples, pu8AACBuf, nMaxOutputBytes);
                    //print_level(SV_DEBUG, "faacEncEncode returns %d\n", nRet);

                    /* 创建普通的AudioTag的Header */
                    //print_level(SV_DEBUG, "Create AudioTag Header.\n");
                    tag_header_t tag_header;
                    int iAudioTagDataSize = 1 + 1 + nRet;                           // AudioInfo(1) + AACPacketType(1) + StreamSize

                    /* 创建普通的AudioTag */
                    //print_level(SV_DEBUG, "Create AudioTag.\n");

                    #ifdef HTTP_FLV_CHUNKED
                    /* 计算一个Chunk Data的大小*/
		            u32TagChunkedSize = sizeof(tag_header) + iAudioTagDataSize + FLV_TAG_PRE_SIZE;
                    memset(szChunkedSize, 0, sizeof(szChunkedSize));
	                sprintf(szChunkedSize, "%x", u32TagChunkedSize);                // 以16进制字符串格式写入Chunk Size

	                memcpy(pu8Buf + iOffset, szChunkedSize, strlen(szChunkedSize)); // 将ChunkedSize写入缓存
	                iOffset += sizeof(szChunkedSize);

	                memcpy(pu8Buf + iOffset, "\r\n", 2);                            // 在Chunk Size和Chunk Data之间插入1个"\r\n"
	                iOffset += 2;
                    #endif

                    tag_header = createTagHeader(0x08, iAudioTagDataSize, u32pts, 0, 0);

                    #ifdef HTTP_FLV_CHUNKED
                    createAudioTag(tag_header, pu8AACBuf, nRet, pu8Buf + iOffset, &iAudioTagSize);
                    iOffset += iAudioTagSize;

                    memcpy(pu8Buf + iOffset, "\r\n", 2);                            // 在Chunk Data的结尾插入1个"/r/n"
	                iOffset += 2;

                    s32Size = send(socketFd, pu8Buf, iOffset, 0);

                    #else
                    createAudioTag(tag_header, pu8AACBuf, nRet, pu8Buf, &iAudioTagSize);
                    s32Size = send(socketFd, pu8Buf, iAudioTagSize, 0);
                    #endif
                    memset(pu8Buf, 0, 512*1024);
                    iOffset = 0;

                    memset(pu8AACBuf, 0, nMaxOutputBytes);
                    memset(pu8PCMBuf, 0, nPCMBufSize);

                    memcpy(pu8PCMBuf, pstPacket->data + (nPCMBufSize - iSavPos), pstPacket->msdsize - (nPCMBufSize - iSavPos));
                    iSavPos = pstPacket->msdsize - (nPCMBufSize - iSavPos);         // 写入当前packet剩下数据

                    if (s32Size < 0)
                    {
                        print_level(SV_ERROR, "Sent Audio Tag to client failed. [err=%#x]\n", errno);
                        print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                        SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                        break;
                    }
                    //print_level(SV_DEBUG, "Audio Tag Sent Size: %d\n", s32Size);
                }
            }
        }
        SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
    }

    nRet = faacEncClose(hEncoder);
    SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);

    free(pu8PCMBuf);
    pu8PCMBuf = NULL;
    free(pu8AACBuf);
   	pu8AACBuf = NULL;
   	free(pu8Buf);
   	pu8Buf = NULL;
#endif
    return SV_SUCCESS;
}

sint32 HTTP_HDL_MJPEG(sint32 socketFd, uint32* u32Clients)
{
    sint32 s32Ret = 0;
    sint32 s32Size = 0;
    uint32 u32Cnt = 0;
    uint32 u32Serial =0;
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    sint32 s32SubQueId;
    sint32 s32SubConsumerId;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    SFIFO_MSHEAD *pstPacket = NULL;
    uint8 *pu8Buf = NULL;         // 写入mjpeg缓存
    uint32 u32VideoBufSize = 501*1024;
    uint8 *pu8VideoBuf = NULL;
    uint32 u32PacketSize = 0;
    MSG_PACKET_S stMsgPkt = {0};

    print_level(SV_INFO, "http-mjpeg clients: %d\n", *u32Clients);

	pu8Buf = (uint8 *)malloc(2*1024);
	if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc: pu8Buf failed.\n");
        return;
    }

    pu8VideoBuf = (uint8 *)malloc(u32VideoBufSize);
	if (NULL == pu8VideoBuf)
    {
        print_level(SV_ERROR, "malloc: pu8Buf failed.\n");
        return;
    }

    /* 获取当前时间 */
    time_t now = time(NULL);
    char szGMTTime[64];
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&now));

    sprintf((char *)pu8Buf, "HTTP/1.1 200 OK\r\n"
                            "Access-Control-Allow-Origin: *\r\n"
                            "Connection: keep-alive\r\n"
                            "Server: MJPG-Streamer/0.2\r\n"
                            "Cache-Control: no-store, no-cache, must-revalidate, pre-check=0, post-check=0, max-age=0\r\n"
                            "Pragma: no-cache\r\n"
                            "Expires: %s\r\n"
                            "Content-Type: multipart/x-mixed-replace;boundary=BOUNDARY\r\n"
                            "\r\n"
                            "--BOUNDARY\r\n", szGMTTime);

    s32Size = send(socketFd, pu8Buf, strlen((char *)pu8Buf), 0);
    if (s32Size < 0)
    {
        print_level(SV_ERROR, "Sent Http Header to client failed. [err=%#x]\n", errno);
        print_level(SV_ERROR, "Error: %s\n", strerror(errno));
    }

#if (defined(BOARD_IPCR20S3))
    stMsgPkt.stMsg.s32Param = 3;
    s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = SFIFO_ForReadOpen(SFIFO_PIC_STREAM, &s32MainQueId, &s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }
#else
    s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }
#endif

    print_level(SV_DEBUG, "s32MainQueId = %d, s32MainConsumerId =%d\n", s32MainQueId, s32MainConsumerId);

/*
    s32Ret = SFIFO_GetMediaAttr(s32MainQueId, &stMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_GetMediaAttr stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }
*/

    while (1)
    {
        if (SV_TRUE == bResetMJpeg)    // 修改ip时先关闭之前打开的队列
        {
            print_level(SV_INFO, "http-mjpeg reset\n");
            //bResetMJpeg = SV_FALSE;
            break;
        }
        s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "SFIFO_GetPacket failed. [err=%#x]\n", s32Ret);
            //print_level(SV_ERROR, "Error: %s\n", strerror(errno));
            //sleep_ms(10);
            continue;
        }

        if (pstPacket->type == 1)
        {
            //print_level(SV_DEBUG, "pts: %lld\n", pstPacket->pts);
            //print_level(SV_DEBUG, "size: %d\n", pstPacket->msdsize);
            //print_level(SV_DEBUG, "type = %d, serial = %d\n", pstPacket->type, pstPacket->serial);
            memset(pu8VideoBuf, 0, u32VideoBufSize);
            if (pstPacket->msdsize > u32VideoBufSize)
            {
                print_level(SV_ERROR, "packet size %d lager than buf size!\n", pstPacket->msdsize);
                continue;
            }
            u32PacketSize = pstPacket->msdsize;
            memcpy(pu8VideoBuf, pstPacket->data, pstPacket->msdsize);

            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
            }

            if (pstPacket->serial != u32Serial + 1) // 监测帧顺序是否正常
            {
                print_level(SV_WARN, "Abnormal Frame Serial Number. Present Frame: %d, Last Frame: %d.\n", pstPacket->serial, u32Serial);
                //print_level(SV_DEBUG, "u32PacketSize = %d\n", u32PacketSize);
            }
            u32Serial = pstPacket->serial;

            memset(pu8Buf, 0, 2*1024);
            //print_level(SV_DEBUG, "FrameType: %d(%dx%d) pts: %lld, serial: %d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->serial);

            sprintf((char*)pu8Buf, "Content-Type: image/jpeg\r\n" \
                                   "Content-Length: %d\r\n" \
                                   "\r\n", u32PacketSize);

            s32Size = send(socketFd, pu8Buf, strlen((char *)pu8Buf), 0);
            if (s32Size < 0)
            {
                print_level(SV_ERROR, "Sent MJpeg Header to client failed. [err=%#x]\n", errno);
                print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                //break;
            }

            //memcpy(pu8Buf, pstPacket->data, pstPacket->msdsize);
            //s32Size = send(socketFd, pu8Buf, pstPacket->msdsize, 0);
            s32Size = send(socketFd, pu8VideoBuf, u32PacketSize, 0);
            if (s32Size < 0)
            {
                print_level(SV_ERROR, "Sent MJpeg Data to client failed. [err=%#x]\n", errno);
                print_level(SV_ERROR, "Error: %s\n", strerror(errno));

                ++u32Cnt;
                if(u32Cnt > 3)
                {
                    break;
                }
            }
            else
            {
                u32Cnt = 0;
            }

            memset(pu8Buf, 0, 2*1024);
            sprintf((char*)pu8Buf, "\r\n"
                                   "--BOUNDARY\r\n");
            s32Size = send(socketFd, pu8Buf, strlen((char *)pu8Buf), 0);
            if (s32Size < 0)
            {
                print_level(SV_ERROR, "Sent Http boundary to client failed. [err=%#x]\n", errno);
                print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                //break;
            }
                //print_level(SV_DEBUG, "sent size: %d\n", s32Size);

        }
        else
        {
            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
            }
        }
        //sleep_ms(5);
    }

    print_level(SV_DEBUG, "s32MainQueId = %d, s32MainConsumerId =%d\n", s32MainQueId, s32MainConsumerId);
    s32Ret = SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadClose stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }

    stMsgPkt.stMsg.s32Param = -1;
    s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    (*u32Clients) --;
    print_level(SV_INFO, "http-mjpeg clients: %d\n", *u32Clients);
    free(pu8Buf);
    free(pu8VideoBuf);
   	pu8Buf = NULL;
    pu8VideoBuf = NULL;

    if (0 == *u32Clients)    // 每个客户端打开的队列都被关闭后则将标志位置回false
    {
        bResetMJpeg = SV_FALSE;
    }

    return SV_SUCCESS;
}

