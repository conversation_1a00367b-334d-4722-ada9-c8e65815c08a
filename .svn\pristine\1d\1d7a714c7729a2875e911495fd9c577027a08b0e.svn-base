Index: include/board.h
===================================================================
--- include/board.h	(版本 5148)
+++ include/board.h	(工作副本)
@@ -402,7 +402,7 @@
 #define BOARD_C_ADA32V2_200049           "200049"   /* 200049客户 */
 #define BOARD_C_ADA32V2_202462           "202462"   /* 202462客户 */
 #define BOARD_C_ADA32V2_202668           "202668"   /* 202668客户 */
-#define BOARD_C_ADA32V2_LEIWO            "111119"   /* 王瑞雷沃客户 */
+#define BOARD_C_ADA32V2_111119           "111119"   /* 王瑞雷沃客户 */
 
 
 /* ADA32 版本文件路径 */
Index: include/mcu.h
===================================================================
--- include/mcu.h	(版本 5148)
+++ include/mcu.h	(工作副本)
@@ -235,6 +235,7 @@
  * 注意    : 无
  *****************************************************************************/
 extern sint32 MCU_SendCanData(char *pszCanid, char *pszMcuCanData, sint32 s32DataLen, SV_BOOL bSingleFrame);
+extern sint32 MCU_SendData(char *pszMcuCanData, sint32 s32DataLen, SV_BOOL bSingleFrame);
 
 /******************************************************************************
  * 函数功能: DMS685通过MCU发送报警CAN数据
Index: include/op.h
===================================================================
--- include/op.h	(版本 5148)
+++ include/op.h	(工作副本)
@@ -125,6 +125,9 @@
     OP_EVENT_DRAW_CALIBRATION,           // 0x47, 请求绘制标定状态
 	OP_REQ_GET_AFSTATUS,				//0x48,请求获取AF标定状态
 	OP_REQ_UPDATE_PLUG, 				//0x49,  请求更新算法插件:string(要更新的插件路径)
+    OP_REQ_SET_CAN_RECV_ID,             // 0x4A,  请求设置CAN过滤可接收的ID
+    OP_REQ_GET_CAN_RECV_ID,             // 0x4B,  请求获取CAN过滤可接收的ID
+    OP_REQ_CAN_SINGLE,                  // 0X4C, 请求获取CAN数据: MSG_CAN_DATA_S
 
     /* 事件类型，无回包 */
     OP_EVENT_BEGIN  = 0XA000,           /* 事件类型OP码起始值 */
@@ -672,6 +675,7 @@
 /* CAN数据消息包 */
 typedef struct tagMsgCanData_S
 {
+    uint32 u32CanId;                   
     SV_BOOL bSingleFrame;               /* 是否为单帧模式 */
     sint32 s32DataLen;                  /* 数据长度 */
     char szCanData[256];                /* 数据内容 */
Index: src/alg/alg.cpp
===================================================================
--- src/alg/alg.cpp	(版本 5148)
+++ src/alg/alg.cpp	(工作副本)
@@ -708,6 +708,30 @@
     return SV_SUCCESS;
 }
 
+/******************************************************************************
+ * 函数功能: 读取到单帧CAN数据的回调函数
+ * 返回值  : SV_SUCCESS - 成功
+ 			 SV_FAILURE - 其它错误
+ * 说明    :   无 
+ *****************************************************************************/
+static sint32 callbackReadCanData(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
+{	
+
+    sint32 i, s32Ret = 0;
+	MSG_CAN_DATA_S stMsgCanData = {0};
+    memcpy(&stMsgCanData, pstMsgPkt->pu8Data, pstMsgPkt->u32Size);
+    
+    /* 打印can 数据 */
+    print_level(SV_DEBUG, "alg recv CANid[%#x] data: \n", stMsgCanData.u32CanId);
+    for (i=0; i < CAN_UTILS_START_FRAME_LEN; i++){
+        printf("%02X ", stMsgCanData.szCanData[i]);
+    }
+    printf("\n ");
+
+
+    return SV_SUCCESS;
+}
+
 sint32 alg_RegisterMsgCallBack()
 {
     sint32 s32Ret;
@@ -796,6 +820,14 @@
         return s32Ret;
     }
 
+    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_CAN_SINGLE, callbackReadCanData);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
+        return s32Ret;
+    }
+
+
 #if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
     s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_GPS_DATA, callbackUpdateGpsData);
     if (SV_SUCCESS != s32Ret)
Index: src/alg/demo/media/main.cpp
===================================================================
--- src/alg/demo/media/main.cpp	(版本 5148)
+++ src/alg/demo/media/main.cpp	(工作副本)
@@ -194,34 +194,31 @@
     return SV_SUCCESS;
 }
 
+
 /******************************************************************************
- * 函数功能: 读取到CAN数据的回调函数
+ * 函数功能: 雷沃客户读取到单帧CAN数据的回调函数
  * 返回值  : SV_SUCCESS - 成功
  			 SV_FAILURE - 其它错误
- * 说明    : 串口发送CAN数据,总共4帧
- * 数据包示例：
-			FE 21 10 00 93 FE FE FE     (start frame, OPcode: 21, crc8: 93) 
-
-			AB BA 03 65 18 00 00 00		(data frame0, extra info: crc32+validDataLen)
-
-			48 65 6C 6C 6F 21 00 00		(data frame1, valid data)
-
-			FF FF FF FF FF FF FF FF		(end frame)
+ * 说明    :   无 
  *****************************************************************************/
 static sint32 callbackReadCanData(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
 {	
 
-    sint32 s32Ret = 0;
+    sint32 i, s32Ret = 0;
 	MSG_CAN_DATA_S stMsgCanData = {0};
     memcpy(&stMsgCanData, pstMsgPkt->pu8Data, pstMsgPkt->u32Size);
     
-    /* 打印can 数据，字符串形式 */
-    print_level(SV_INFO, "Demo get CAN data (string): [%s]\n", stMsgCanData.szCanData);
+    /* 打印can 数据 */
+    print_level(SV_DEBUG, "alg recv CANid[%#x] data: \n", stMsgCanData.u32CanId);
+    for (i=0; i < CAN_UTILS_START_FRAME_LEN; i++){
+        printf("%02X ", stMsgCanData.szCanData[i]);
+    }
+    printf("\n ");
 
+
     return SV_SUCCESS;
 }
 
-
 /******************************************************************************
  * 函数功能: 通过socket获取MediaBuf Fd / Get the MediaBuf Fd via socket
  * 输入参数: s32SocketFd --- socket fd
@@ -765,6 +762,8 @@
     MSG_CAN_DATA_S stMsgCanData = {0};
 
     memset(&stMsgCanData, 0, sizeof(stMsgCanData));
+    stMsgCanData.u32CanId = 0x18fada32;
+
     stMsgCanData.bSingleFrame = SV_TRUE;
     strcpy(stMsgCanData.szCanData, "Hello!");
     stMsgCanData.s32DataLen = strlen(stMsgCanData.szCanData);
@@ -784,6 +783,7 @@
     }
 }
 
+
 /***************************************************************
 *-# 用例编号: Media_SetCanConfig
 *-# 测试功能: 设置CAN配置参数 / Setting the CAN config
@@ -1790,6 +1790,13 @@
         return s32Ret;
     }
 
+    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_CAN_SINGLE, callbackReadCanData);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
+        return s32Ret;
+    }
+
     g_running = 1;
     s32Ret = pthread_create(&thread, NULL, media_Watch_BufferFd, NULL);
     Media_Demo_Usage();
@@ -1800,7 +1807,6 @@
         {
             break;
         }
-
         switch (chChoice)
         {
             case '0':
@@ -1819,7 +1825,7 @@
                 Media_SendAlarmEvent();
                 break;
             case '5':
-                Media_SendCanData();
+                Media_SendCanData();  
                 break;
             case '6':
                 Media_SetCanConfig();
Index: src/common/board/board.c
===================================================================
--- src/common/board/board.c	(版本 5148)
+++ src/common/board/board.c	(工作副本)
@@ -129,7 +129,7 @@
 	{BOARD_C_ADA32V2_200049,    BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
 	{BOARD_C_ADA32V2_202462,    BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
 	{BOARD_C_ADA32V2_202668,    BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
-	{BOARD_C_ADA32V2_LEIWO,     BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
+	{BOARD_C_ADA32V2_111119,     BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
     {BOARD_C_ADA32V2_EXHIBITION_A, BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
     {BOARD_C_ADA32V2_EXHIBITION_B, BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
     {BOARD_C_ADA32V2_EXHIBITION_C, BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
Index: src/ipserver/rtsp2/librtsp2.cpp
===================================================================
--- src/ipserver/rtsp2/librtsp2.cpp	(版本 5148)
+++ src/ipserver/rtsp2/librtsp2.cpp	(工作副本)
@@ -396,8 +396,18 @@
                         }
                         break;
                     case xop::VIDEO_FRAME_P:
-                        stMainFrame.size -= 4;
-                        memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
+                        if (stMainFrame.size <= MaxPacketSize)
+                        {
+                            if (stMainFrame.size > 4)
+                            {
+                                stMainFrame.size -= 4;
+                                memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
+                            }
+                            else
+                            {
+                                memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
+                            }
+                        }
                         break;
                     case xop::AUDIO_FRAME:
                         memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
Index: src/media/rockchip/com/mpp_vosd.cpp
===================================================================
--- src/media/rockchip/com/mpp_vosd.cpp	(版本 5148)
+++ src/media/rockchip/com/mpp_vosd.cpp	(工作副本)
@@ -2789,6 +2789,10 @@
         {
             sprintf(stSoftWareStr, "%s %s", "C-R-Ai-AHDV2", m_stVosdInfo.stBodyAttr[enType].hardware.firmware);
         }
+
+#if (defined(ADA32SDK) || defined(ADA32NSDK))
+        strcat(stSoftWareStr, " (SDK)");
+#endif
     }
 
 
Index: src/media/rockchip/rv1126/mpp_vi.c
===================================================================
--- src/media/rockchip/rv1126/mpp_vi.c	(版本 5148)
+++ src/media/rockchip/rv1126/mpp_vi.c	(工作副本)
@@ -342,7 +342,7 @@
         sprintf(pszIqfilesPath, "%s/fisheye", tmpIqfilesPath);
     }
 
-    if (BOARD_IsCustomer(BOARD_C_ADA32V2_LEIWO) && BOARD_IsSVersion(BOARD_S_2M3))
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_111119) && BOARD_IsSVersion(BOARD_S_2M3))
     {
         char szTmpIqfilesPath[128];
         strcpy(szTmpIqfilesPath, pszIqfilesPath);
Index: src/peripheral/can/can.c
===================================================================
--- src/peripheral/can/can.c	(版本 5148)
+++ src/peripheral/can/can.c	(工作副本)
@@ -3627,8 +3627,8 @@
 static sint32 callbackSendCanData(MSG_PACKET_S * pstMsgPkt, MSG_PACKET_S * pstRetPkt)
 {
     MSG_CAN_DATA_S *pstMsgCanData = (MSG_CAN_DATA_S *)pstMsgPkt->pu8Data;
-    sint32 s32Ret = CAN_SendUserData(pstMsgCanData->szCanData, pstMsgCanData->s32DataLen, pstMsgCanData->bSingleFrame);
-    if (SV_SUCCESS != s32Ret)
+    sint32 s32Ret = CAN_SendUserDataMsg(pstMsgCanData->szCanData, pstMsgCanData->s32DataLen,\
+                    pstMsgCanData->bSingleFrame, pstMsgCanData->u32CanId);
     {
         print_level(SV_ERROR, "CAN_SendUserData failed!\n");
         return SV_FAILURE;
@@ -4312,3 +4312,150 @@
     return SV_SUCCESS;
 }
 
+/******************************************************************************
+ * 函数功能: 发送任意CAN数据，目前是只给ADA32使用
+ * 输入参数: pszCanData - CAN数据
+             s32DataLen - 数据长度
+ * 输出参数: 无
+ * 返回值  : SV_SUCCESS - 成功
+             SV_FAILURE - 失败
+ * 注意    : 无
+ *****************************************************************************/
+sint32 CAN_SendUserDataMsg(const char *pszCanData, const sint32 s32DataLen, SV_BOOL bSingleFrame, uint32 u32msgCanid)
+{
+    sint32 s32Ret, i, k;
+	uint32 u32Canid = 0;
+    sint32 s32BytesNum = 8;
+    struct can_frame stCanFrame;
+    uint8 u8DataCnt = 0, u8FrameOffset = 0;
+	uint8 s32ValidDataCnt = 0, s32CanDataFrameCnt = 0;
+    char *pszData = NULL;
+	char szCmd[128] = {0};
+	char szBuf[64] = {0};
+    sint32 s32DetectCnt = 5;    // 连续5次发送数据之后如果TX没有增长的话，认为没有外接CAN接收设备，重启一次CAN
+    sint32 s32CanLastTxCount = 0, s32CanTxCount = 0;
+    static sint32 s_s32SendCnt = 0;
+
+    if (NULL == pszCanData || 0 == s32DataLen || s32DataLen > CAN_MAX_DATA_LEN)
+    {
+        print_level(SV_ERROR, "data len is out of range, dataLen: %d, maxLen: %d\n", s32DataLen, CAN_MAX_DATA_LEN);
+        return SV_FAILURE;
+    }
+
+    if (bSingleFrame && s32DataLen > 8)
+    {
+        print_level(SV_ERROR, "single frame mode, but data len larger than 8, dataLen: %d\n", s32DataLen);
+        return SV_FAILURE;
+    }
+
+    s_s32SendCnt++;
+    if (s_s32SendCnt >= s32DetectCnt)
+    {
+        strncpy(szCmd, "ifconfig | grep can0 -A3 | grep TX | awk -F ':' '{print$2}' | awk -F ' ' '{print$1}'", 128);
+    	s32Ret = can_SafeSystem_Recv(szCmd, szBuf, 64);
+    	if (SV_SUCCESS == s32Ret && 0 != strlen(szBuf))
+    	{
+    	    s32CanLastTxCount = atoi(szBuf);
+    	}
+    }
+    
+    pszData = pszCanData;
+    s32ValidDataCnt = s32DataLen;
+	if (s32ValidDataCnt % s32BytesNum != 0) 	//s32BytesNum data bit
+		s32CanDataFrameCnt = (s32ValidDataCnt / s32BytesNum) + 1;  //有效数据帧数
+	else
+		s32CanDataFrameCnt = s32ValidDataCnt / s32BytesNum;
+	
+	/* can data init */
+    pthread_mutex_lock(&m_stCanInfo.mutexSetConfig);
+    
+    if (u32msgCanid) u32Canid = u32msgCanid;
+    else u32Canid = strtol(m_stCanInfo.szPdsCanid, NULL, 16);
+
+    stCanFrame.can_id = can_GetCanid(u32Canid);
+	pthread_mutex_unlock(&m_stCanInfo.mutexSetConfig);
+    stCanFrame.can_dlc = 8;
+
+    if (!bSingleFrame)
+    {
+    	/* starting stCanFrame */
+    	k = 0;            
+        stCanFrame.data[k++] = 0xfe;
+        stCanFrame.data[k++] = s32ValidDataCnt;
+        stCanFrame.data[k++] = crc8_check((uint8 *)pszCanData, s32ValidDataCnt);
+        while (k < 8)
+    	{
+            stCanFrame.data[k++] = 0xfe;
+        }
+        s32Ret = can_WriteFrame(&stCanFrame);
+    	if (SV_SUCCESS != s32Ret)
+    	{
+    		print_level(SV_ERROR, "can_WriteFrame failed.\n");
+    		return SV_FAILURE;
+    	}
+    }
+
+	/* data stCanFrame */
+    u8FrameOffset = 0;
+    u8DataCnt = (s32ValidDataCnt < s32BytesNum) ? s32ValidDataCnt : s32BytesNum;
+	for (i = 0; i<s32CanDataFrameCnt; i++)
+	{
+        if (i == s32CanDataFrameCnt-1)
+        {
+            u8DataCnt = s32ValidDataCnt - u8FrameOffset;
+        }
+        
+		memcpy(stCanFrame.data, pszCanData+u8FrameOffset, u8DataCnt);        
+        stCanFrame.can_dlc = u8DataCnt;
+        s32Ret = can_WriteFrame(&stCanFrame);
+    	if (SV_SUCCESS != s32Ret)
+    	{
+    		break;
+    	}
+		u8FrameOffset += u8DataCnt;
+        
+	}
+    if (SV_SUCCESS != s32Ret)
+	{
+		print_level(SV_ERROR, "can_WriteFrame failed.\n");
+		return SV_FAILURE;
+	}
+    
+    if (!bSingleFrame)
+    {
+    	/* ending stCanFrame */
+    	k = 0;
+    	stCanFrame.data[k++] = 0xff;
+        while (k < 8)
+    	{
+            stCanFrame.data[k++] = 0xff;
+        }
+        stCanFrame.can_dlc = 8;
+        s32Ret = can_WriteFrame(&stCanFrame);
+    	if (SV_SUCCESS != s32Ret)
+    	{
+    		print_level(SV_ERROR, "can_WriteFrame failed.\n");
+    		return SV_FAILURE;
+    	}
+    }
+    
+    if (s_s32SendCnt >= s32DetectCnt)
+    {
+        strncpy(szCmd, "ifconfig | grep can0 -A3 | grep TX | awk -F ':' '{print$2}' | awk -F ' ' '{print$1}'", 128);
+    	s32Ret = can_SafeSystem_Recv(szCmd, szBuf, 64);
+    	if (SV_SUCCESS == s32Ret && 0 != strlen(szBuf))
+    	{
+    	    s32CanTxCount = atoi(szBuf);
+    	}
+        
+        if (s32CanLastTxCount == s32CanTxCount)
+    	{
+    		print_level(SV_WARN, "can device need to restart, last: %d, now: %d\n", s32CanLastTxCount, s32CanTxCount);
+
+            SAFE_SV_System("canconfig can0 stop >> /dev/null && canconfig can0 start >> /dev/null");
+    	}
+        s_s32SendCnt = 0;
+    }
+
+    return SV_SUCCESS;
+}
Index: src/peripheral/mcu/mcu.c
===================================================================
--- src/peripheral/mcu/mcu.c	(版本 5148)
+++ src/peripheral/mcu/mcu.c	(工作副本)
@@ -360,8 +360,12 @@
     }
 
     MSG_CAN_DATA_S *pstMsgCanData = (MSG_CAN_DATA_S *)pstMsgPkt->pu8Data;
+
     //print_level(SV_INFO, "get user data len: %d, data: %s\n", pstMsgCanData->s32DataLen, pstMsgCanData->szCanData);
-    sint32 s32Ret = MCU_SendCanData(m_stMcuInfo.szPdsCanid, pstMsgCanData->szCanData, pstMsgCanData->s32DataLen, pstMsgCanData->bSingleFrame);
+    sint32 s32Ret = MCU_SendDataMsg(pstMsgCanData->szCanData, pstMsgCanData->s32DataLen,\
+                                pstMsgCanData->bSingleFrame, pstMsgCanData->u32CanId);
+    
+
     if (SV_SUCCESS != s32Ret)
     {
         print_level(SV_ERROR, "MCU_SendCanData failed!\n");
@@ -1787,6 +1791,39 @@
     return SV_SUCCESS;
 }
 
+uint32 mcu_ProcessCan_SingleFrame(CAN_UTILS_DATA_BUF_S * pstMcuCanDataBuf){
+    sint32 s32Ret;
+    int i;
+    MSG_PACKET_S stMsgPkt = {0} ;
+	MSG_CAN_DATA_S stMsgCanData = {0};
+
+    if (NULL == pstMcuCanDataBuf)
+    {
+        print_level(SV_ERROR, "get null pointer.\n");
+        return SV_FAILURE;
+    }
+    print_level(SV_INFO, "MCU recv CAN data:  ");
+    for (i=0; i < CAN_UTILS_START_FRAME_LEN; i++){
+        printf( "%02X ", pstMcuCanDataBuf->u8CanData[i]);
+    }
+    printf("\n ");
+    stMsgCanData.s32DataLen = pstMcuCanDataBuf->u16CanDataLen;
+    memcpy(stMsgCanData.szCanData, pstMcuCanDataBuf->u8CanData, stMsgCanData.s32DataLen);
+    stMsgCanData.u32CanId = pstMcuCanDataBuf->u32CanId;
+    
+	stMsgPkt.pu8Data = (uint8 *)&stMsgCanData;
+    stMsgPkt.u32Size = sizeof(MSG_CAN_DATA_S);
+    s32Ret = Msg_execRequestBlock(EP_MCU, EP_ALG, OP_REQ_CAN_SINGLE, &stMsgPkt, NULL, 0);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "OP_REQ_CAN_SINGLE failed.[%d]\n", s32Ret);
+        return SV_FAILURE;
+    }
+    
+    return SV_SUCCESS;
+}
+
+
 sint32 mcu_Response_BroadCast()
 {
     sint32 s32Ret,i;
@@ -2668,6 +2705,26 @@
         }
     }
 
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_111119))
+    {
+        memcpy(&pstMcuCanDataBuf->u8CanData, &pstMcuSerialPackage->data[MCU_CAN_BASE_INFO_LEN+u32BufIndex], CAN_UTILS_START_FRAME_LEN);
+        pstMcuCanDataBuf->u32CanId = stCanInfo.u32CanId;
+        pstMcuCanDataBuf->u16CanDataLen = CAN_UTILS_START_FRAME_LEN;
+        mcu_ProcessCan_SingleFrame(pstMcuCanDataBuf);
+
+        return s32Ret;
+    }
+    
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_111119))
+    {
+        memcpy(&pstMcuCanDataBuf->u8CanData, &pstMcuSerialPackage->data[MCU_CAN_BASE_INFO_LEN+u32BufIndex], CAN_UTILS_START_FRAME_LEN);
+        pstMcuCanDataBuf->u32CanId = stCanInfo.u32CanId;
+        pstMcuCanDataBuf->u16CanDataLen = CAN_UTILS_START_FRAME_LEN;
+        mcu_ProcessCan_SingleFrame(pstMcuCanDataBuf);
+
+        return s32Ret;
+    }
+
     // 第一次接收到该canid的包
     if (i >= CAN_UTILS_DATA_BUF_MAX_NUM || bRemain)
     {
@@ -4767,7 +4824,6 @@
 	print_level(SV_INFO, "pdscanid: %s,szPdsExtCanid :%s, apccanid: %s, baudrate: %d, enAlgTrigger: %d, s32PdAlarmOutInterval: %d\n",
             m_stMcuInfo.szPdsCanid, m_stMcuInfo.szPdsExtCanid,m_stMcuInfo.szApcCanid, m_stMcuInfo.s32Baudrate, m_stMcuInfo.enAlgTrigger, m_stMcuInfo.s32PdAlarmOutInterval);
     pthread_mutex_unlock(&m_stMcuInfo.mutexSetConfig);
-
     return SV_SUCCESS;
 }
 
@@ -5086,6 +5142,201 @@
 }
 
 /******************************************************************************
+ * 函数功能: 封装CAN数据成USB包发给MCU，发送CAN数据
+ * 输入参数: pszMcuCanData - CAN数据
+             s32DataLen - 数据长度
+ * 输出参数: 无
+ * 返回值  : SV_SUCCESS - 成功
+             SV_FAILURE - 失败
+ * 注意    : 无
+ *****************************************************************************/
+sint32 MCU_SendData(char *pszMcuCanData, sint32 s32DataLen, SV_BOOL bSingleFrame)
+{
+    sint32 s32Ret, i, j;
+	sint32 s32Count = -1;
+    sint32 s32BytesNum = 8;
+	sint32 s32Baudrate, s32CrcLen, s32SendSize;
+    uint32 u32Canid = 0;
+    uint8 u8CanFrameFlag = 0;
+    MCU_SERIAL_PACKET_S stMcuSerialPacket = {0};
+	MCU_CAN_INFO_S stMcuCanPacket = {0};
+
+    if (NULL == pszMcuCanData || 0 == s32DataLen || s32DataLen > MCU_CAN_VALID_DATA_LEN)
+    {
+        print_level(SV_ERROR, "data len is out of range, dataLen: %d, maxLen: %d\n", s32DataLen, MCU_CAN_VALID_DATA_LEN);
+        return SV_FAILURE;
+    }
+
+    if (bSingleFrame && s32DataLen > 8)
+    {
+        print_level(SV_ERROR, "single frame mode, but data len larger than 8, dataLen: %d\n", s32DataLen);
+        return SV_FAILURE;
+    }
+
+	pthread_mutex_lock(&m_stMcuInfo.mutexSetConfig);
+    u32Canid = strtol(m_stMcuInfo.szPdsCanid, NULL, 16);
+	s32Baudrate = m_stMcuInfo.s32Baudrate;
+    u8CanFrameFlag = (uint8)m_stMcuInfo.enFrameFormat;
+	pthread_mutex_unlock(&m_stMcuInfo.mutexSetConfig);
+
+    //can头数据填充
+	stMcuCanPacket.u32CanId = u32Canid;
+	stMcuCanPacket.u16CanBaudRate = s32Baudrate;
+	stMcuCanPacket.u8CanFrameFlag = u8CanFrameFlag;
+    stMcuCanPacket.u8CanDataLen = bSingleFrame ? s32DataLen : s32DataLen + 16;
+
+	stMcuSerialPacket.stMcuSerialHeader.u16Startcode = 0xAAAA;
+	stMcuSerialPacket.stMcuSerialHeader.u8Opcode = 0x2;  // 0x2表示CAN数据
+	stMcuSerialPacket.stMcuSerialHeader.u32PacketId = 0;
+	stMcuSerialPacket.stMcuSerialHeader.u16Len	= MCU_CAN_INFO_HEADER_SIZE + stMcuCanPacket.u8CanDataLen;  // can头数据 + can数据长度
+
+    if (!bSingleFrame)
+    {
+    	// 头帧
+    	for (i=0; i<8; i++)
+    		stMcuCanPacket.u8CanData[i] = 0xfe;
+        stMcuCanPacket.u8CanData[1] = s32DataLen;
+    	stMcuCanPacket.u8CanData[2] = crc8_check(pszMcuCanData, s32DataLen);
+
+        // 数据帧
+    	memcpy(stMcuCanPacket.u8CanData+8, pszMcuCanData, s32DataLen);
+
+        // 尾帧
+    	for (i=stMcuCanPacket.u8CanDataLen-8; i<stMcuCanPacket.u8CanDataLen; i++)
+    		stMcuCanPacket.u8CanData[i] = 0xff;
+    }
+    else
+    {
+        // 单帧发送只有数据帧
+    	memcpy(stMcuCanPacket.u8CanData, pszMcuCanData, s32DataLen);
+    }
+
+	s32CrcLen = MCU_SERIAL_HEADER_CRC_LEN + stMcuSerialPacket.stMcuSerialHeader.u16Len;	//7表示usb包头数据，从opcode开始算
+	memcpy(stMcuSerialPacket.data, &stMcuCanPacket, stMcuSerialPacket.stMcuSerialHeader.u16Len);
+	stMcuSerialPacket.stMcuSerialHeader.u32Crc = crc32_check((char *)&stMcuSerialPacket.stMcuSerialHeader.u8Opcode, s32CrcLen);
+
+	s32SendSize = MCU_SERIAL_HEADER_SIZE + stMcuSerialPacket.stMcuSerialHeader.u16Len;
+
+#if 0
+	char *tmp = &stMcuSerialPacket;
+	printf("sendSize: %d\n", s32SendSize);
+	for (int k=0; k<s32SendSize; k++)
+		printf("%02X ", *tmp++);
+	printf("\n");
+#endif
+    pthread_mutex_lock(&m_stMcuInfo.mutexSendData);
+    s32Ret = mcu_SendSerialData((char *)&stMcuSerialPacket, s32SendSize);
+	pthread_mutex_unlock(&m_stMcuInfo.mutexSendData);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "mcu_SendSerialData failed!\n");
+        return SV_FAILURE;
+    }
+
+    return SV_SUCCESS;
+}
+
+/******************************************************************************
+ * 函数功能: 封装CAN数据成USB包发给MCU，发送CAN数据
+ * 输入参数: pszMcuCanData - CAN数据
+             s32DataLen - 数据长度
+ * 输出参数: 无
+ * 返回值  : SV_SUCCESS - 成功
+             SV_FAILURE - 失败
+ * 注意    : 无
+ *****************************************************************************/
+
+sint32 MCU_SendDataMsg(char *pszMcuCanData, sint32 s32DataLen, SV_BOOL bSingleFrame, uint32 u32msgCanid)
+{
+    sint32 s32Ret, i, j;
+	sint32 s32Count = -1;
+    sint32 s32BytesNum = 8;
+	sint32 s32Baudrate, s32CrcLen, s32SendSize;
+    uint32 u32Canid = 0;
+    uint8 u8CanFrameFlag = 0;
+    MCU_SERIAL_PACKET_S stMcuSerialPacket = {0};
+	MCU_CAN_INFO_S stMcuCanPacket = {0};
+
+    if (NULL == pszMcuCanData || 0 == s32DataLen || s32DataLen > MCU_CAN_VALID_DATA_LEN)
+    {
+        print_level(SV_ERROR, "data len is out of range, dataLen: %d, maxLen: %d\n", s32DataLen, MCU_CAN_VALID_DATA_LEN);
+        return SV_FAILURE;
+    }
+
+    if (bSingleFrame && s32DataLen > 8)
+    {
+        print_level(SV_ERROR, "single frame mode, but data len larger than 8, dataLen: %d\n", s32DataLen);
+        return SV_FAILURE;
+    }
+
+	pthread_mutex_lock(&m_stMcuInfo.mutexSetConfig);
+
+    if (u32msgCanid) u32Canid = u32msgCanid;
+    else u32Canid = strtol(m_stMcuInfo.szPdsCanid, NULL, 16);
+    
+	s32Baudrate = m_stMcuInfo.s32Baudrate;
+    u8CanFrameFlag = (uint8)m_stMcuInfo.enFrameFormat;
+	pthread_mutex_unlock(&m_stMcuInfo.mutexSetConfig);
+
+    //can头数据填充
+	stMcuCanPacket.u32CanId = u32Canid;
+	stMcuCanPacket.u16CanBaudRate = s32Baudrate;
+	stMcuCanPacket.u8CanFrameFlag = u8CanFrameFlag;
+    stMcuCanPacket.u8CanDataLen = bSingleFrame ? s32DataLen : s32DataLen + 16;
+
+	stMcuSerialPacket.stMcuSerialHeader.u16Startcode = 0xAAAA;
+	stMcuSerialPacket.stMcuSerialHeader.u8Opcode = 0x2;  // 0x2表示CAN数据
+	stMcuSerialPacket.stMcuSerialHeader.u32PacketId = 0;
+	stMcuSerialPacket.stMcuSerialHeader.u16Len	= MCU_CAN_INFO_HEADER_SIZE + stMcuCanPacket.u8CanDataLen;  // can头数据 + can数据长度
+
+    if (!bSingleFrame)
+    {
+    	// 头帧
+    	for (i=0; i<8; i++)
+    		stMcuCanPacket.u8CanData[i] = 0xfe;
+        stMcuCanPacket.u8CanData[1] = s32DataLen;
+    	stMcuCanPacket.u8CanData[2] = crc8_check(pszMcuCanData, s32DataLen);
+
+        // 数据帧
+    	memcpy(stMcuCanPacket.u8CanData+8, pszMcuCanData, s32DataLen);
+
+        // 尾帧
+    	for (i=stMcuCanPacket.u8CanDataLen-8; i<stMcuCanPacket.u8CanDataLen; i++)
+    		stMcuCanPacket.u8CanData[i] = 0xff;
+    }
+    else
+    {
+        // 单帧发送只有数据帧
+    	memcpy(stMcuCanPacket.u8CanData, pszMcuCanData, s32DataLen);
+    }
+
+	s32CrcLen = MCU_SERIAL_HEADER_CRC_LEN + stMcuSerialPacket.stMcuSerialHeader.u16Len;	//7表示usb包头数据，从opcode开始算
+	memcpy(stMcuSerialPacket.data, &stMcuCanPacket, stMcuSerialPacket.stMcuSerialHeader.u16Len);
+	stMcuSerialPacket.stMcuSerialHeader.u32Crc = crc32_check((char *)&stMcuSerialPacket.stMcuSerialHeader.u8Opcode, s32CrcLen);
+
+	s32SendSize = MCU_SERIAL_HEADER_SIZE + stMcuSerialPacket.stMcuSerialHeader.u16Len;
+
+#if 0
+	char *tmp = &stMcuSerialPacket;
+	printf("sendSize: %d\n", s32SendSize);
+	for (int k=0; k<s32SendSize; k++)
+		printf("%02X ", *tmp++);
+	printf("\n");
+#endif
+    pthread_mutex_lock(&m_stMcuInfo.mutexSendData);
+    s32Ret = mcu_SendSerialData((char *)&stMcuSerialPacket, s32SendSize);
+	pthread_mutex_unlock(&m_stMcuInfo.mutexSendData);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "mcu_SendSerialData failed!\n");
+        return SV_FAILURE;
+    }
+
+    return SV_SUCCESS;
+}
+
+
+/******************************************************************************
  * 函数功能: 封装CAN数据成USB包发给MCU，发送三线触发Trigger电平数据(demo用)
  * 输入参数: pszMsgIO - 三线电平信息
              s32DataLen - 数据长度
Index: src/webui/config.html
===================================================================
--- src/webui/config.html	(版本 5148)
+++ src/webui/config.html	(工作副本)
@@ -1858,7 +1858,7 @@
 							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdRectPerson" {{#if pdRectPerson}}checked="checked"{{/if}}>
 							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdRectPerson"></label>
 						</div>
-						<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32V2" "200055"}}}>
+						<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32V2" "200055 200055A"}}}>
 							<p data-desc="pd-BorderAlarm">警告边框</p>
 							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdBorderAlarm" {{#if pdBorderAlarm}}checked="checked"{{/if}}>
 							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdBorderAlarm"></label>
@@ -1896,8 +1896,8 @@
 						</div>
 						<div class="custom-select-box pds-det-interval" {{{hideHardware "HDW845V1"}}}>
 							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdInterval-yellow">
-								{{#isCustomer "200055"}}{{getKeyLang "pdOrange-interval"}}{{/isCustomer}}
-								{{#isNotCustomer "200055"}}{{getKeyLang "pdYellow-interval"}}{{/isNotCustomer}}
+								{{#isCustomer "200055 200055A"}}{{getKeyLang "pdOrange-interval"}}{{/isCustomer}}
+								{{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdYellow-interval"}}{{/isNotCustomer}}
 							</label>
 							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdInterval-yellow" data-role="none" value="{{pdInterval.yellow}}">
 								<option value="-1" {{#equal pdInterval.yellow -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
@@ -1917,8 +1917,8 @@
 						</div>
 						<div class="custom-select-box pds-det-interval" {{{hideHardware "HDW845V1"}}}>
 							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdInterval-green">
-								{{#isCustomer "200055"}}{{getKeyLang "pdPurple-interval"}}{{/isCustomer}}
-								{{#isNotCustomer "200055"}}{{getKeyLang "pdGreen-interval"}}{{/isNotCustomer}}
+								{{#isCustomer "200055 200055A"}}{{getKeyLang "pdPurple-interval"}}{{/isCustomer}}
+								{{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdGreen-interval"}}{{/isNotCustomer}}
 							</label>
 							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdInterval-green" data-role="none" value="{{pdInterval.green}}">
 								<option value="-1" {{#equal pdInterval.green -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
@@ -2103,8 +2103,8 @@
 							</div>
 							<div data-role="none" class="input-text-box">
 								<label data-role="none">
-									{{#isCustomer "200055"}}{{getKeyLang "pdOrangeSensitivity"}}{{/isCustomer}}
-								    {{#isNotCustomer "200055"}}{{getKeyLang "pdYellowSensitivity"}}{{/isNotCustomer}}
+									{{#isCustomer "200055 200055A"}}{{getKeyLang "pdOrangeSensitivity"}}{{/isCustomer}}
+								    {{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdYellowSensitivity"}}{{/isNotCustomer}}
 								</label>	
 								<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
 									<td><li>
@@ -2119,9 +2119,9 @@
 								</table>
 							</div>
 							<div data-role="none" class="input-text-box">
-								<label data-role="none"">
-									{{#isCustomer "200055"}}{{getKeyLang "pdPurpleSensitivity"}}{{/isCustomer}}
-								    {{#isNotCustomer "200055"}}{{getKeyLang "pdGreenSensitivity"}}{{/isNotCustomer}}
+								<label data-role="none">
+									{{#isCustomer "200055 200055A"}}{{getKeyLang "pdPurpleSensitivity"}}{{/isCustomer}}
+								    {{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdGreenSensitivity"}}{{/isNotCustomer}}
 								</label>	
 								<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
 									<td><li>
Index: src/webui/index.html
===================================================================
--- src/webui/index.html	(版本 5148)
+++ src/webui/index.html	(工作副本)
@@ -444,12 +444,12 @@
 						<label class="single_option_text" data-desc="select-zone">颜色选择</label>
 						<div><select id="roi-board-color-select" class="custom-select" data-role="none" value=2>
 							<option value=0 >
-								{{#isCustomer "200055"}}&#x1F7EA;{{/isCustomer}}
-								{{#isNotCustomer "200055"}}&#x1F7E9;{{/isNotCustomer}} 
+								{{#isCustomer "200055 200055A"}}&#x1F7EA;{{/isCustomer}}
+								{{#isNotCustomer "200055 200055A"}}&#x1F7E9;{{/isNotCustomer}} 
 							</option>
 							<option value=1 >
-								{{#isCustomer "200055"}}&#x1F7E7;{{/isCustomer}}
-								{{#isNotCustomer "200055"}}&#x1F7E8;{{/isNotCustomer}} 
+								{{#isCustomer "200055 200055A"}}&#x1F7E7;{{/isCustomer}}
+								{{#isNotCustomer "200055 200055A"}}&#x1F7E8;{{/isNotCustomer}} 
 							</option>
 							<option value=2 selected="selected"> &#x1F7E5; </option>
 						</select></div>
Index: src/webui/js/webapp-calibration-screen.js
===================================================================
--- src/webui/js/webapp-calibration-screen.js	(版本 5148)
+++ src/webui/js/webapp-calibration-screen.js	(工作副本)
@@ -2140,7 +2140,7 @@
 
 		this.original_colors.line1_color = this.normal_color;
 		this.original_colors.line4_color = this.red_color;
-		if(this.customer == "200055")
+		if(this.customer == "200055" || this.customer == "200055A")
 		{
 			this.original_colors.line2_color = this.purple_color;
 			this.original_colors.line3_color = this.orange_color;
@@ -2154,7 +2154,7 @@
 		if (this.points.sv_int_state == 1) { //竖直
 			this.original_colors.line1_color = this.red_color;
 			this.original_colors.line4_color = this.normal_color;
-			if(this.customer == "200055")
+			if(this.customer == "200055" || this.customer == "200055A")
 			{
 				this.original_colors.line2_color = this.orange_color;
 				this.original_colors.line3_color = this.purple_color;
@@ -2934,7 +2934,7 @@
 		}
 	}
 
-	if(this.customer == "200055")
+	if(this.customer == "200055" || this.customer == "200055A")
 		this.roi_color = ["#FF00FF", "#FFA500", "#FF0000"];				// 紫色，橙色，红色
 	else
 		this.roi_color = ["#00FF00", "#FFFF00", "#FF0000"];				// 绿色，黄色，红色
Index: src/webui/js/webapp-language.js
===================================================================
--- src/webui/js/webapp-language.js	(版本 5148)
+++ src/webui/js/webapp-language.js	(工作副本)
@@ -166,8 +166,8 @@
 	 "enable-pdRoiGreen": {"EN": "Green Zone", "CN": "绿色识别区域", "JP": "緑の識別エリア", "ES": "Zona Verde", "PT": "Zona Verde", "RU": "Зеленая зона", "TUR": "Yeşil Alan", "DG": "Grüner Bereich", "ITA": "Zona verde", "FRA": "Zone verte"},
 	 "pdroigui": {"EN": "Detection Zone Style", "CN": "检测区域显示模式", "JP": "検知エリア表示方法", "ES": "Estilo de zona detección", "PT": "Estilo de Exibição Zona", "RU": "Тип отображения зоны", "TUR": "Algılama Bölgesi Stili", "DG": "Erkennungszonenstil", "ITA": "Stile Zona(Rilevamento)", "FRA": "Style de Zone Détection"},
 	 "pdroigui-hide": {"EN": "Hide", "CN": "隐藏", "JP": "隠れる", "ES": "Ocultar", "PT": "ocultar", "RU": "Спрятать", "TUR": "Sakla", "DG": "Ausblenden", "ITA": "Nascondere", "FRA": "Cacher"},
-	 "pdroigui-line": {"EN": "Line", "CN": "线段", "JP": "線分", "ES": "Línea", "PT": "segmento de linha", "RU": "Линия", "TUR": "Çizgi Segmenti", "DG": "Linie", "ITA": "Linea", "FRA": "Ligne"},
-	 "pdroigui-fill": {"EN": "Fill", "CN": "填充", "JP": "充填", "ES": "Rellenar", "PT": "o preenchimento", "RU": "Заполнение", "TUR": "Dolgu", "DG": "Füllen", "ITA": "Riempimento", "FRA": "Remplie"},
+	 "pdroigui-line": {"EN": "Line", "CN": "线段", "JP": "線分", "ES": "Línea", "PT": "Linha", "RU": "Линия", "TUR": "Çizgi Segmenti", "DG": "Linie", "ITA": "Linea", "FRA": "Ligne"},
+	 "pdroigui-fill": {"EN": "Fill", "CN": "填充", "JP": "充填", "ES": "Rellenar", "PT": "preencher", "RU": "Заполнение", "TUR": "Dolgu", "DG": "Füllen", "ITA": "Riempimento", "FRA": "Remplie"},
 	 "gateway": {"EN": "Gateway", "CN": "网关", "JP": "ゲートウェイ", "ES": "Puerta de enlace", "PT": "Porta de entrada", "RU": "Шлюз", "TUR": "Geçit", "DG": "Zugang", "ITA": "Gateway", "FRA": "Passerelle"},
 	 "osd display": {"EN": "OSD Display", "CN": "OSD 显示", "JP": "OSD表示", "ES": "Visualización OSD", "PT": "OSD Exposição", "RU": "OSD", "TUR": "Gösterge Ekranı", "DG": "OSD-Anzeige", "ITA": "Visualizzazione OSD", "FRA": "Affichage OSD"},
 	 "all": {"EN": "ALL", "CN": "全部", "JP": "すべて", "ES": "TODO", "PT": "todo", "RU": "Все", "TUR": "Tüm", "DG": "ALLE", "ITA": "TUTTI", "FRA": "TOUTES"},
@@ -923,7 +923,7 @@
 	 "greenwire-diagnostic": {"EN": "Diagnostic", "CN": "开机诊断", "JP": "診断 ", "ES": "Diagnóstico", "PT": "Diagnóstico", "RU": "Диагностика ", "TUR": "Tanı", "DG": "Diagnose", "ITA": "Diagnostico", "FRA": "Diagnostic"},
 	 "GreenWireAlarmEvent": {"EN": "Alarm Event", "CN": "报警事件类型", "JP": "警報イベントタイプ", "ES": "Tipo de evento de alarma", "PT": "Tipo de evento de alarme", "RU": "Тип события тревоги", "TUR": "Alarm olay türü", "DG": "Alarmereignistyp", "ITA": "Tipo di evento di allarme", "FRA": "Type d'événement d'alarme"},
 	 "greenwire-alarm": {"EN": "Alarm", "CN": "警报", "JP": "アラーム", "ES": "Alarma", "PT": "Alarme", "RU": "Тревога ", "TUR": "Alarm", "DG": "Alarm", "ITA": "Allarme", "FRA": "Alarme"},
-	 "pd-AlarmOutShelter": {"EN": "shelter alarm triggers output", "CN": "遮挡报警触发线输出", "JP": "避難所の警報が出力を引き起こす", "ES": "La alarma del refugio activa la salida", "PT": "O alarme do abrigo dispara a saída", "RU": "Сигнал тревоги убежища активирует выход ", "TUR": "Sığınak alarmı çıkışı tetikler", "DG": "Schutzraumalarm löst Ausgabe aus", "ITA": "L'allarme del rifugio attiva l'uscita", "FRA": "L'alarme de l'abri déclenche la sortie"},
+	 "pd-AlarmOutShelter": {"EN": "shelter alarm triggers output", "CN": "遮挡报警触发线输出", "JP": "避難所の警報が出力を引き起こす", "ES": "La alarma del refugio activa la salida", "PT": "O alarme do abrigo dispara a saída", "RU": "Сигнализация при блокировке камеры", "TUR": "Sığınak alarmı çıkışı tetikler", "DG": "Schutzraumalarm löst Ausgabe aus", "ITA": "L'allarme del rifugio attiva l'uscita", "FRA": "L'alarme de l'abri déclenche la sortie"},
 	 "pd-AlarmIconSize": {"EN": "Person/Car Icon Size", "CN": "行人/车辆 图标大小", "JP": "歩行者/車両のアイコンサイズ", "ES": "Tamaño del icono de peatones/vehículos", "PT": "Tamanho do ícone de pedestres/veículos", "RU": "Размер иконки пешеходов/транспортных средств", "TUR": "Yaya/araç simge boyutu", "DG": "Größe des Fußgänger-/Fahrzeugicons", "ITA": "Dimensione dell'icona pedoni/veicoli", "FRA": "Taille de l'icône piétons/véhicules"},
 	 "shelter-timelimit": {"EN": "Shelter Time Limit", "CN": "遮挡报警时长", "JP": "遮蔽アラームの時間", "ES": "Duración de la alarma de bloqueo", "PT": "Duração do alarme de bloqueio", "RU": "Длительность тревоги при заслонении", "TUR": "Engel alarm süresi", "DG": " Dauer des Verdeckungsalarmes", "ITA": "Durata dell'allarme di occlusione", "FRA": "Durée de l'alarme de masquage"},
 	 "pdCamPos": {"EN": "Camera Postion", "CN": "摄像头安装位置", "JP": "Droite", "ES": "Posición de la cámara", "PT": "Posição da câmera", "RU": "Позиция камеры", "TUR": "Kamera konumu", "DG": "Kameraposition", "ITA": "Posizione della camera", "FRA": "Position de la caméra"},
@@ -1811,7 +1811,7 @@
 	"4gNetWork": {"EN": "4G NetWork", "CN": "4G网络", "JP": "4Gネットワーク", "ES": "Red 4G", "PT": "Rede 4G", "RU": "4G сеть", "TUR": "4G ağı", "DG": "4G-Netzwerk", "ITA": "Rete 4G", "FRA": "Réseau 4G"},
 	"APNuser": {"EN": "APN UserName", "CN": "APN 用户名", "JP": "APN ユーザー", "ES": "APN Nombre de usuario", "PT": "APN do utilizador", "RU": "APN Логин", "TUR": "APN Kullanıcı adı", "DG": "APN ", "ITA": "APN Nome utente", "FRA": "APN Nom d'utilisateur"},
 	"APNpassword": {"EN": "APN Password", "CN": "APN 密码", "JP": "APN パスワード", "ES": "APN Contraseña", "PT": "APN senha", "RU": "APN Пароль", "TUR": "APN Şifre", "DG": "APN Kennwort", "ITA": "APN Password", "FRA": "APN Mot de passe"},
-	"pd-AlarmOutShelter": {"EN": "shelter alarm triggers output", "CN": "遮挡报警触发线输出", "JP": "避難所の警報が出力を引き起こす", "ES": "La alarma del refugio activa la salida", "PT": "O alarme do abrigo dispara a saída", "RU": "Сигнал тревоги убежища активирует выход ", "TUR": "Sığınak alarmı çıkışı tetikler", "DG": "Schutzraumalarm löst Ausgabe aus", "ITA": "L'allarme del rifugio attiva l'uscita", "FRA": "L'alarme de l'abri déclenche la sortie"},
+	"pd-AlarmOutShelter": {"EN": "shelter alarm triggers output", "CN": "遮挡报警触发线输出", "JP": "避難所の警報が出力を引き起こす", "ES": "La alarma del refugio activa la salida", "PT": "O alarme do abrigo dispara a saída", "RU": "Сигнализация при блокировке камеры", "TUR": "Sığınak alarmı çıkışı tetikler", "DG": "Schutzraumalarm löst Ausgabe aus", "ITA": "L'allarme del rifugio attiva l'uscita", "FRA": "L'alarme de l'abri déclenche la sortie"},
 	"shelterAudio": {"EN": "Shelter Audio Enable", "CN": "遮挡报警声音开关", "JP": "遮断警報音のスイッチ", "ES": "Activar audio de alarma de refugio", "PT": "Ativar áudio de alarme de abrigo", "RU": "Включить аудио сигнала укрытия", "TUR": "Engel alarm sesi anahtarı", "DG": "Schalter für Blockieralarmton", "ITA": "Attiva audio allarme rifugioo", "FRA": "interrupteur audio masqué"},
 	"pd-Model-Sign": {"EN": "Sign", "CN": "标识", "JP": "サイン", "ES": "señal", "PT": "sinal", "RU": "знак", "TUR": "işaret", "DG": "Schild", "ITA": "segno", "FRA": "signe"},
 	"pd-Model-PersonSign": {"EN": "Person&Sign", "CN": "行人标识模型", "JP": "人物とサイン", "ES": "Persona y señal", "PT": "Pessoa e sinal", "RU": "Человек и знак", "TUR": "Kişi ve işaret", "DG": "Person und Zeichen", "ITA": "Persona e segno", "FRA": "Personne et signe"},
Index: src/webui/js/webapp-view.js
===================================================================
--- src/webui/js/webapp-view.js	(版本 5148)
+++ src/webui/js/webapp-view.js	(工作副本)
@@ -970,7 +970,7 @@
 
             if(this != null && this.customer != null)
             {
-                if(v1 == this.customer)
+                if(v1.indexOf(this.customer) != -1 && this.customer != "")
                 {
                     return options.fn(this);
                 }
@@ -990,7 +990,7 @@
 
             if(this != null && this.customer != null)
             {
-                if(v1 != this.customer)
+                if(v1.indexOf(this.customer) == -1 || this.customer == "")
                 {
                     return options.fn(this);
                 }
@@ -4551,7 +4551,7 @@
         }
         if(this.hardware.indexOf("ADA") != -1)
         {
-            if (this.hardware === "ADA32V2" && this.customer === "200055") {            
+            if (this.hardware === "ADA32V2" && (this.customer == "200055" || this.customer == "200055A")) {            
                 $("#algConfig-pdsConf-0-pdRoiEnable-yellow").addClass("checkBtnOrange");
                 $("#algConfig-pdsConf-0-pdRoiEnable-green").addClass("checkBtnPurple");
                 $("#algConfig-pdsConf-0-pdAlarmOutEnable-yellow").addClass("checkBtnOrange");
Index: src/webui/login.html
===================================================================
--- src/webui/login.html	(版本 5148)
+++ src/webui/login.html	(工作副本)
@@ -277,7 +277,7 @@
 					}
 					else
 					{
-						if (customer == "200055")
+						if (customer == "200055" || customer == "200055A")
 						{ //ADA32 专用版VT
 							//console.log(JSON.stringify(data.ipcIdentification.customer));
 							$("#logo").css("display","block");
