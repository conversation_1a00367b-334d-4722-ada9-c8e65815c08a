root@OpenWrt:/# lspci
00:00.0 Class 0604: 17cb:1001
01:00.0 Class ff00: 17cb:0306

root@OpenWrt:/# insmod pcie_mhi.ko mhi_mbim_enabled=1
[   63.094154] mhi_init Quectel_Linux_PCIE_MHI_Driver_V1.3.0.17
[   63.094739] mhi_pci_probe pci_dev->name = 0000:01:00.0, domain=0, bus=1, slot=0, vendor=17CB, device=0306
[   63.099373] mhi_q 0000:01:00.0: BAR 0: assigned [mem 0x48000000-0x48000fff 64bit]
[   63.108476] mhi_q 0000:01:00.0: enabling device (0140 -> 0142)
[   63.293451] [I][mhi_netdev_enable_iface] Prepare the channels for transfer
[   63.324757] [I][mhi_netdev_enable_iface] Exited.
[   63.326265] rmnet_vnd_register_device(rmnet_mhi0.1)=0

root@OpenWrt:/# quectel-CM -s cment &
[04-13_09:25:23:910] Quectel_QConnectManager_Linux_V1.6.0.25
[04-13_09:25:23:912] network interface '' or qmidev '' is not exist
[04-13_09:25:23:912] netcard driver = pcie_mhi, driver version = V1.3.0.17
[04-13_09:25:23:913] mbim_qmap_mode = 1, vlan_id = 0x00, qmap_netcard = rmnet_mhi0.1
[04-13_09:25:23:913] Modem works in MBIM mode
[04-13_09:25:23:965] cdc_wdm_fd = 7
[04-13_09:25:23:965] mbim_open_device()
[04-13_09:25:24:549] mbim_device_caps_query()
[04-13_09:25:24:575] DeviceId:     869710030002905
[04-13_09:25:24:575] FirmwareInfo: RM500QGLABR10A03M4G_01.001.03
[04-13_09:25:24:575] HardwareInfo: RM500QGL_VH
[04-13_09:25:24:576] mbim_device_services_query()
[04-13_09:25:24:585] mbim_set_radio_state( 1 )
[04-13_09:25:24:588] HwRadioState: 1, SwRadioState: 1
[04-13_09:25:24:588] mbim_subscriber_status_query()
[04-13_09:25:24:612] SubscriberId: ***************
[04-13_09:25:24:612] SimIccId:     89860015120716380461
[04-13_09:25:24:613] SubscriberReadyState NotInitialized -> Initialized 
[04-13_09:25:24:613] mbim_register_state_query()
[04-13_09:25:24:617] RegisterState Unknown -> Home 
[04-13_09:25:24:617] mbim_packet_service_query()
[04-13_09:25:24:619] PacketServiceState Unknown -> Attached 
[04-13_09:25:24:619] CurrentDataClass = 5G_NSA
[04-13_09:25:24:620] mbim_query_connect(sessionID=0)
[04-13_09:25:24:631] ActivationState Unknown -> Deactivated 
[04-13_09:25:24:631] ifconfig rmnet_mhi0 down
[04-13_09:25:24:657] ifconfig rmnet_mhi0.1 0.0.0.0
ifconfig: SIOCSIFFLAGS: Network is down
[04-13_09:25:24:681] ifconfig rmnet_mhi0.1 down
[04-13_09:25:24:705] mbim_register_state_query()
[04-13_09:25:24:709] mbim_packet_service_query()
[04-13_09:25:24:713] CurrentDataClass = 5G_NSA
[04-13_09:25:24:713] mbim_set_connect(onoff=1, sessionID=0)
[04-13_09:25:25:096] ActivationState Deactivated -> Activated 
[04-13_09:25:25:097] mbim_ip_config(sessionID=0)
[04-13_09:25:25:100] < SessionId = 0
[04-13_09:25:25:100] < IPv4ConfigurationAvailable = 0xf
[04-13_09:25:25:100] < IPv6ConfigurationAvailable = 0x0
[04-13_09:25:25:101] < IPv4AddressCount = 0x1
[04-13_09:25:25:101] < IPv4AddressOffset = 0x3c
[04-13_09:25:25:101] < IPv6AddressCount = 0x0
[04-13_09:25:25:102] < IPv6AddressOffset = 0x0
[04-13_09:25:25:102] < IPv4 = **************/30
[04-13_09:25:25:103] < gw = **************
[04-13_09:25:25:103] < dns1 = *************
[04-13_09:25:25:103] < dns2 = *************
[04-13_09:25:25:104] < ipv4 mtu = 1500
[04-13_09:25:25:112] ifconfig rmnet_mhi0 up
[04-13_09:25:25:141] ifconfig rmnet_mhi0.1 up
[04-13_09:25:25:170] ip -4 address flush dev rmnet_mhi0.1
[04-13_09:25:25:190] ip -4 address add **************/30 dev rmnet_mhi0.1
[04-13_09:25:25:213] ip -4 route add default via ************** dev rmnet_mhi0.1

root@OpenWrt:/# ifconfig rmnet_mhi0.1
rmnet_mhi0.1 Link encap:Ethernet  HWaddr 02:50:F4:00:00:00  
          inet addr:**************  Mask:***************
          inet6 addr: fe80::50:f4ff:fe00:0/64 Scope:Link
          UP RUNNING NOARP  MTU:1500  Metric:1
          RX packets:19 errors:0 dropped:0 overruns:0 frame:0
          TX packets:29 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000 
          RX bytes:2326 (2.2 KiB)  TX bytes:2991 (2.9 KiB)

root@OpenWrt:/# ping *******
PING ******* (*******): 56 data bytes
64 bytes from *******: seq=0 ttl=52 time=278.561 ms
