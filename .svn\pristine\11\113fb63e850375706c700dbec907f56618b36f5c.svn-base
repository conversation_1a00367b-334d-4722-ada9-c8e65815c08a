#ifndef _COM_ERRNO_H
#define _COM_ERRNO_H

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/* 1010 0000b
 * VTOP use APPID from 0~31
 * so, sharpvision use APPID based on 32
 */
#define COM_ERR_APPID  (0x80000000L + 0x20000000L)


/* 错误级别定义 */
typedef enum tagERR_LEVEL_E
{
    COM_ERR_LEVEL_DEBUG = 0,  /* debug-level                                  */
    COM_ERR_LEVEL_INFO,       /* informational                                */
    COM_ERR_LEVEL_NOTICE,     /* normal but significant condition             */
    COM_ERR_LEVEL_WARNING,    /* warning conditions                           */
    COM_ERR_LEVEL_ERROR,      /* error conditions                             */
    COM_ERR_LEVEL_CRIT,       /* critical conditions                          */
    COM_ERR_LEVEL_ALERT,      /* action must be taken immediately             */
    COM_ERR_LEVEL_FATAL,      /* just for compatibility with previous version */
    COM_ERR_LEVEL_BUTT
}COM_ERR_LEVEL_E;


/******************************************************************************
|----------------------------------------------------------------|
| 1 |   APP_ID   |   MOD_ID    | ERR_LEVEL |   ERR_ID            |
|----------------------------------------------------------------|
|<--><--7bits----><----8bits---><--3bits---><------13bits------->|
******************************************************************************/

#define COM_DEF_ERR(module, level, errid) \
    ((sint32)( (COM_ERR_APPID) | ((module) << 16 ) | ((level)<<13) | (errid) ))

#define COM_ERRNO(module, errid) \
    ((sint32)( (COM_ERR_APPID) | ((module) << 16 ) | ((COM_ERR_LEVEL_ERROR)<<13) | (errid) ))

/* 注意! 以下定义通用的错误码ID,
 * 所有模块必要保留 0~63 作为它们的通用错误码ID */
typedef enum tag_ERR_CODE_E
{
    COM_ERR_INVALID_DEVID = 1,   /* invlalid device ID */
    COM_ERR_INVALID_CHNID = 2,   /* 无效通道 ID */
    COM_ERR_ILLEGAL_PARAM = 3,   /* 至少存在一个参数非法, 例如: 传入超范围的枚举值 */
    COM_ERR_EXIST = 4,           /* 资源已经存在, 例如: 重复初始化同一个模块 */
    COM_ERR_UNEXIST = 5,         /* 资源未初始化, 例如: 操作未初始化的模块 */

    COM_ERR_NULL_PTR = 6,        /* 使用了NULL指针 */

    COM_ERR_NOT_CONFIG = 7,      /* 未配置属性前初始化系统、设备、通道 */
    COM_ERR_NOT_SURPPORT = 8,    /* 操作或类型不支持 */
    COM_ERR_NOT_PERM = 9,        /* 操作不允许, 例如: 试图修改模块的静态属性 */

    COM_ERR_NOMEM = 12 ,         /* 内存不足, malloc memory */
    COM_ERR_NOBUF = 13,          /* buffer 不足, malloc buffer */

    COM_ERR_BUF_EMPTY = 14,      /* buffer 为空 */
    COM_ERR_BUF_FULL = 15,       /* buffer 已满 */

    COM_ERR_SYS_NOTREADY = 16,   /* 系统忙碌, 例如: 系统未准备、未初始化或未加载完成 */

    COM_ERR_BADADDR = 17,        /* 地址错误, 例如: 将媒体流数据拷贝到媒体队列中传入错误队列内部地址 */

    COM_ERR_BUSY = 18,           /* 资源忙碌, 例如: 在未去激活编码通道下销毁该通道 */

    COM_ERR_TIMEOUT = 19,        /* 执行超时 */
    COM_ERR_INVALID_DATA = 20,   /* 无效数据 */

    COM_ERR_BUTT = 63,           /* 通道错误ID 最大值, 各模块的私有错误ID 必需大于该值 */
}COM_ERR_CODE_E;


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _COM_ERRNO_H */

