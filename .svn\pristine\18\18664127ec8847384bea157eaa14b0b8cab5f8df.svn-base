#include "trace_common.h"


#include <unistd.h>
#include <sys/time.h>

//阻塞式休眠线程，输入秒、微秒
void select_sleep(int sec, int usec)
{
    struct timeval timeout;
    timeout.tv_sec = sec;
    timeout.tv_usec = usec;
    select(0, NULL, NULL, NULL, &timeout); //阻塞
}


//获取系统微秒时间
long long int microtime(){
	struct timeval time;
	gettimeofday(&time, NULL); //This actually returns a struct that has microsecond precision.
	long long int microsec = ((long long)time.tv_sec * 1000000) + time.tv_usec;
	return microsec;
}



