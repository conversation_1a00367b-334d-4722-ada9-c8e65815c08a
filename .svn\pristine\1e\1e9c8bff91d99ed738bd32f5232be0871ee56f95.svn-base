[sensor_common]
SensorNum    =1                         ;sensor num

EnSceneAuto     =FALSE                    ;enable scene auto
SceneAutoMode   =SCENEAUTO_SPECIAL_SCENE_NONE ;SCENEAUTO_SPECIAL_SCENE_NONE = 0,
                                        ;SCENEAUTO_SPECIAL_SCENE_BLC,
                                        ;SCENEAUTO_SPECIAL_SCENE_IR,
                                        ;SCENEAUTO_SPECIAL_SCENE_HLC,
                                        ;SCENEAUTO_SPECIAL_SCENE_DYNAMIC,
                                        ;SCENEAUTO_SPECIAL_SCENE_DRC,
                                        ;SCENEAUTO_SPECIAL_SCENE_AUTO_FSWDR,
                                        ;SCENEAUTO_SPECIAL_SCENE_MANUAL_LONG_FRAME,
                                        ;SCENEAUTO_SPECIAL_SCENE_MANUAL_NORMAL_WDR,
                                        ;SCENEAUTO_SPECIAL_SCENE_TRAFFIC
[sensor.0]
SensorType   =imx323                    ;sensor name
DllFile      =libsns_imx323.so          ;sensor lib path
SensorModeNum=1                         ;sensor mode num
UseWdrMode   = WDR_MODE_NONE            ;WDR_MODE_NONE = 0
                                        ;WDR_MODE_BUILT_IN,
                                        ;WDR_MODE_2To1_LINE,
                                        ;WDR_MODE_2To1_FRAME,
                                        ;WDR_MODE_2To1_FRAME_FULL_RATE,
                                        ;WDR_MODE_3To1_LINE,
                                        ;WDR_MODE_3To1_FRAME,
                                        ;WDR_MODE_3To1_FRAME_FULL_RATE,
                                        ;WDR_MODE_4To1_LINE,
                                        ;WDR_MODE_4To1_FRAME,
                                        ;WDR_MODE_4To1_FRAME_FULL_RATE,
WdrMode0         =WDR_MODE_NONE          
SceneAutoFile0   =./configs/scene_auto/sceneauto_imx323.ini
 

[sensor_mode.0.0] 
devno =0                                ;device number, select sensor0 and sensor 1
                              
input_mode =INPUT_MODE_CMOS             ;INPUT_MODE_MIPI = 0
                                        ;INPUT_MODE_SUBLVDS = 1
                                        ;INPUT_MODE_LVDS = 2 ...etc
phy_clk_share =PHY_CLK_SHARE_NONE       ;PHY_CLK_SHARE_NONE = 0
                                        ;PHY_CLK_SHARE_PHY0 = 1
                                        ;PHY_CLK_SHARE_BUTT = 2

img_rect_x = 0                          ;oringnal sensor input image size x
img_rect_y = 0                          ;oringnal sensor input image size y
img_rect_w = 1920                       ;oringnal sensor input image size W
img_rect_h = 1080                       ;oringnal sensor input image size H
raw_data_type =RAW_DATA_12BIT            ;RAW_DATA_8BIT = 0
                                        ;RAW_DATA_10BIT = 1
                                        ;RAW_DATA_12BIT = 2
                                        ;RAW_DATA_14BIT = 3
                                        ;RAW_DATA_16BIT = 4
;----------only for mipi_dev---------               

mipi_wdr_mode =HI_MIPI_WDR_MODE_NONE    ;HI_MIPI_WDR_MODE_NONE =0
                                        ;HI_MIPI_WDR_MODE_VC = 1
                                        ;HI_MIPI_WDR_MODE_DT = 2
                                        ;HI_MIPI_WDR_MODE_DOL =3
mipi_lane_id = -1|-1|-1|-1|-1|-1|-1|-1      ;lane_id: -1 - disable

;----------only for lvds_dev---------                                                               
img_size_w = 1920                       ;only for Hi3516CV300
img_size_h = 1080                       ;only for Hi3516CV300

wdr_mode = HI_WDR_MODE_NONE        ;HI_WDR_MODE_NONE =0
                                        ;HI_WDR_MODE_2F = 1
                                        ;HI_WDR_MODE_3F = 2
                                        ;HI_WDR_MODE_4F =3
                                        ;HI_WDR_MODE_DOL_2F=4
                                        ;HI_WDR_MODE_DOL_3F=5
                                        ;HI_WDR_MODE_DOL_4F=6
sync_mode = LVDS_SYNC_MODE_SOF          ;LVDS_SYNC_MODE_SOF = 0
                                        ;LVDS_SYNC_MODE_SAV = 1
lvds_vsync_type = LVDS_VSYNC_NORMAL     ;LVDS_VSYNC_NORMAL = 0
                                        ;LVDS_VSYNC_SHARE = 1
                                        ;LVDS_VSYNC_HCONNECT = 2
hblank1 = -1                             ;hconnect vsync blanking len, valid when the sync_type is LVDS_VSYNC_HCONNECT
hblank2 = -1
lvds_fid_type = LVDS_FID_NONE           ;LVDS_FID_NONE = 0
                                        ;LVDS_FID_IN_SAV = 1
                                        ;LVDS_FID_IN_DATA = 2
output_fil = TRUE                                       
data_endian = LVDS_ENDIAN_BIG           ;LVDS_ENDIAN_LITTLE = 0
                                        ;LVDS_ENDIAN_BIG = 1
sync_code_endian =LVDS_ENDIAN_BIG       ;LVDS_ENDIAN_LITTLE = 0
                                        ;LVDS_ENDIAN_BIG = 1
lvds_lane_num = -1                      ;LVDS_LANE_NUM
wdr_vc_num = -1                         ;WDR_VC_NUM
sync_code_num = -1                      ;SYNC_CODE_NUM
lane_id = -1|-1|-1|-1|-1|-1|-1|-1|      ;lane_id: -1 - disable
sync_code_0 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_1 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_2 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_3 = 0xab0|0xb60|0x800|0x9d0|0xab0|0xb60|0x800|0x9d0|0xab0|0xb60|0x800|0x9d0|0xab0|0xb60|0x800|0x9d0|
sync_code_4 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_5 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_6 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_7 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|

[sensor_mode.0.1]
devno =0                                ;device number, select sensor0 and sensor 1

input_mode =INPUT_MODE_MIPI             ;INPUT_MODE_MIPI    = 0
                                        ;INPUT_MODE_SUBLVDS = 1
                                        ;INPUT_MODE_LVDS    = 2
                                        ;INPUT_MODE_HISPI   = 3
                                        ;INPUT_MODE_CMOS    = 4
                                        ;INPUT_MODE_BT1120  = 5
										
;phy_clk_share,img_rect only for Hi3519V101
phy_clk_share =PHY_CLK_SHARE_NONE       ;PHY_CLK_SHARE_NONE = 0
                                        ;PHY_CLK_SHARE_PHY0 = 1
                                        ;PHY_CLK_SHARE_BUTT = 2
img_rect_x = 0                          ;oringnal sensor input image size x
img_rect_y = 0                          ;oringnal sensor input image size y
img_rect_w = 1920                       ;oringnal sensor input image size W
img_rect_h = 1080                       ;oringnal sensor input image size H

raw_data_type = RAW_DATA_12BIT          ;RAW_DATA_8BIT = 0
                                        ;RAW_DATA_10BIT = 1
                                        ;RAW_DATA_12BIT = 2
                                        ;RAW_DATA_14BIT = 3
                                        ;RAW_DATA_16BIT = 4
										
;----------only for mipi_dev---------
mipi_wdr_mode =HI_MIPI_WDR_MODE_DOL     ;HI_MIPI_WDR_MODE_NONE =0
                                        ;HI_MIPI_WDR_MODE_VC = 1
                                        ;HI_MIPI_WDR_MODE_DT = 2
                                        ;HI_MIPI_WDR_MODE_DOL =3
mipi_lane_id = 0|1|2|3|-1|-1|-1|-1      ;lane_id: -1 - disable

;----------only for lvds_dev---------
img_size_w = 1920                       ;only for Hi3516CV300
img_size_h = 1080                       ;only for Hi3516CV300
wdr_mode = HI_WDR_MODE_NONE             ;HI_WDR_MODE_NONE =0
                                        ;HI_WDR_MODE_2F = 1
                                        ;HI_WDR_MODE_3F = 2
                                        ;HI_WDR_MODE_4F =3
                                        ;HI_WDR_MODE_DOL_2F=4
                                        ;HI_WDR_MODE_DOL_3F=5
                                        ;HI_WDR_MODE_DOL_4F=6
sync_mode = LVDS_SYNC_MODE_SAV          ;LVDS_SYNC_MODE_SOF = 0
                                        ;LVDS_SYNC_MODE_SAV = 1
lvds_vsync_type = LVDS_VSYNC_NORMAL     ;LVDS_VSYNC_NORMAL = 0
                                        ;LVDS_VSYNC_SHARE = 1
                                        ;LVDS_VSYNC_HCONNECT = 2
hblank1 = -1                             ;hconnect vsync blanking len, valid when the sync_type is LVDS_VSYNC_HCONNECT
hblank2 = -1
lvds_fid_type = LVDS_FID_NONE           ;LVDS_FID_NONE = 0
                                        ;LVDS_FID_IN_SAV = 1
                                        ;LVDS_FID_IN_DATA = 2
output_fil = TRUE
data_endian = LVDS_ENDIAN_BIG           ;LVDS_ENDIAN_LITTLE = 0
                                        ;LVDS_ENDIAN_BIG = 1
sync_code_endian =LVDS_ENDIAN_BIG       ;LVDS_ENDIAN_LITTLE = 0
                                        ;LVDS_ENDIAN_BIG = 1
lvds_lane_num = -1                      ;LVDS_LANE_NUM
wdr_vc_num = -1                         ;WDR_VC_NUM
sync_code_num = -1                      ;SYNC_CODE_NUM
lane_id = -1|-1|-1|-1|-1|-1|-1|-1|      ;lane_id: -1 - disable
sync_code_0 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_1 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_2 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_3 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_4 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_5 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_6 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|
sync_code_7 = -1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|-1|

[isp_image.0]
Isp_x        =0
Isp_y        =0
Isp_w        =1920
Isp_h        =1080
SensorWidth   =1920
SensorHeight  =1080
Isp_FrameRate=30
Isp_Bayer    =BAYER_RGGB           ;BAYER_RGGB=0,
                                   ;BAYER_GRBG=1,
                                   ;BAYER_GBRG=2,
                                   ;BAYER_BGGR=3,

[defog.0]
;----------defog attribute-------- 
EnDefog          =FALSE                        ;enable sensor defog 
Strength         =94

[antiflicker.0]
;----------anti-flicker attribute--------   
EnAntiflicker     =FALSE                    ;enable anti-flicker    
AntiflickerMode   =ANTIFLICKER_MODE_50HZ  ;ANTIFLICKER_MODE_50HZ =0,
                                        ;ANTIFLICKER_MODE_60HZ

[video_common]
ViDevNum         =1           ;support vi count
VpssGrpNum       =1
VencNum          =2           ;support venc count
VoChnNum         =1           ;vo chn count
PoolNum          =2           ;max count of pools

EnStitch         =FALSE

SupportOSD       =FALSE

SupportFishEye   =FALSE
FishEyeCnt       =0           ;support FishEye count(only for EnFisheye=TRUE)
CNFonts          = ./font/hzk16
ENFonts          = ./font/asc16
                                               
[vb_conf]   
;----- only for 1080p ----------
BlkSize0         =4181760     ;(1920+16)*1080*2
BlkCnt0          =20
;----- only for 720p ----------
BlkSize1         =1382400     ;1280*720*3/2
BlkCnt1          =10

         
[vi_dev.0]
ViDev    =0 
IntfMode =VI_MODE_DIGITAL_CAMERA              ;VI_MODE_BT656 = 0
                                    ;VI_MODE_BT601,
                                    ;VI_MODE_DIGITAL_CAMERA
                                    ;VI_MODE_BT1120_STANDARD
                                    ;VI_MODE_BT1120_INTERLEAVED
                                    ;VI_MODE_MIPI,
                                    ;VI_MODE_LVDS,
                                    ;VI_MODE_HISPI,
WorkMode =VI_WORK_MODE_1Multiplex   ;VI_WORK_MODE_1Multiplex = 0
                                    ;VI_WORK_MODE_2Multiplex
                                    ;VI_WORK_MODE_4Multiplex
CompMask0   =0xfff0000   
CompMask1   =0x0  
ScanMode    =VI_SCAN_PROGRESSIVE    ;VI_SCAN_INTERLACED = 0
                                    ;VI_SCAN_PROGRESSIVE,                                   
DataSeq     =VI_INPUT_DATA_YUYV     ;data sequence (ONLY for YUV format)
                                    ;----2th component U/V sequence in bt1120
                                    ;    VI_INPUT_DATA_VUVU = 0,
                                    ;    VI_INPUT_DATA_UVUV,
                                    ;----input sequence for yuv
                                    ;    VI_INPUT_DATA_UYVY = 0,
                                    ;    VI_INPUT_DATA_VYUY,
                                    ;    VI_INPUT_DATA_YUYV,
                                    ;    VI_INPUT_DATA_YVYU
                
;----- only for BT601/CAMERA ----------
Vsync   =VI_VSYNC_PULSE             ;vertical synchronization signal
                                    ;VI_VSYNC_FIELD = 0, 
                                    ;VI_VSYNC_PULSE,
VsyncNeg=VI_VSYNC_NEG_HIGH          ;Polarity of the vertical synchronization signal
                                    ;VI_VSYNC_NEG_HIGH = 0, 
                                    ;VI_VSYNC_NEG_LOW
Hsync   =VI_HSYNC_VALID_SINGNAL     ;Attribute of the horizontal synchronization signal
                                    ;VI_HSYNC_VALID_SINGNAL = 0,
                                    ;VI_HSYNC_PULSE,
HsyncNeg =VI_HSYNC_NEG_HIGH         ;Polarity of the horizontal synchronization signal
                                    ;VI_HSYNC_NEG_HIGH = 0, 
                                    ;VI_HSYNC_NEG_LOW
VsyncValid =VI_VSYNC_VALID_SINGAL   ;Attribute of the valid vertical synchronization signal
                                    ;VI_VSYNC_NORM_PULSE = 0,
                                    ;VI_VSYNC_VALID_SINGAL, 
VsyncValidNeg =VI_VSYNC_VALID_NEG_HIGH ;Polarity of the valid vertical synchronization signal
                                    ;VI_VSYNC_VALID_NEG_HIGH = 0,
                                    ;VI_VSYNC_VALID_NEG_LOW 
Timingblank_HsyncHfb  =0     ;Horizontal front blanking width
Timingblank_HsyncAct  =1920  ;Horizontal effetive width
Timingblank_HsyncHbb  =0     ;Horizontal back blanking width
Timingblank_VsyncVfb  =0     ;Vertical front blanking height
Timingblank_VsyncVact =1080  ;Vertical effetive width
Timingblank_VsyncVbb  =0     ;Vertical back blanking height
Timingblank_VsyncVbfb =0     ;Even-field vertical front blanking height(interlace, invalid progressive)
Timingblank_VsyncVbact=0     ;Even-field vertical effetive width(interlace, invalid progressive)
Timingblank_VsyncVbbb =0     ;Even-field vertical back blanking height(interlace, invalid progressive)

;----- only for bt656 ----------
FixCode   =0    ;BT656_FIXCODE_1 = 0,
                ;BT656_FIXCODE_0 
FieldPolar=0    ;BT656_FIELD_POLAR_STD = 0
                ;BT656_FIELD_POLAR_NSTD
                
DataPath  =VI_PATH_ISP      ;ISP enable or bypass
                            ;VI_PATH_BYPASS    = 0,/* ISP bypass */
                            ;VI_PATH_ISP       = 1,/* ISP enable */
                            ;VI_PATH_RAW       = 2,/* Capture raw data, for debug */
InputDataType=VI_DATA_TYPE_RGB ;VI_DATA_TYPE_YUV = 0,
                               ;VI_DATA_TYPE_RGB = 1,
DataRev  =FALSE ;Data reverse. FALSE = 0; TRUE = 1
DevRect_x=200     ;
DevRect_y=20     ;
DevRect_w=1920  ;
DevRect_h=1080  ;

EnWdrCompress=FALSE;VI WDR compress switch

SupportBas=FALSE;
BasSize_w=1920  ;
BasSize_h=1080  ;
BasCompress=FALSE;
HRephaseMode=VI_REPHASE_MODE_NONE   ;VI_REPHASE_MODE_NONE       = 0,
                                    ;VI_REPHASE_MODE_SKIP_1_2   = 1,        /*skip 1/2*/
                                    ;VI_REPHASE_MODE_SKIP_1_3   = 2,        /* skip 1/3 */
                                    ;VI_REPHASE_MODE_BINNING_1_2= 3,        /* binning 1/2*/
                                    ;VI_REPHASE_MODE_BINNING_1_3= 4,        /* binning 1/3*/
VRephaseMode=VI_REPHASE_MODE_NONE   ;VI_REPHASE_MODE_NONE       = 0,
                                    ;VI_REPHASE_MODE_SKIP_1_2   = 1,        /*skip 1/2*/
                                    ;VI_REPHASE_MODE_SKIP_1_3   = 2,        /* skip 1/3 */
                                    ;VI_REPHASE_MODE_BINNING_1_2= 3,        /* binning 1/2*/
                                    ;VI_REPHASE_MODE_BINNING_1_3= 4,        /* binning 1/3*/
GenTimingEn = FALSE;
GenTimingFps = 20;
[vi_chn.0]
ViChn        =0
;enViExtChn   =FALSE
;BindChn      =-1     ;Source physical channel to be bound (ONLY for enViExtChn=TRUE)

;----- only for phy chn ONLY for enViExtChn=FALSE)----------
CapRectX     =0      ;
CapRectY     =0
CapRectWidth =1920
CapRectHeight=1080
CapSel       =VI_CAPSEL_BOTH  ;Frame/field select. ONLY used in interlaced mode
                ;VI_CAPSEL_TOP = 0,                  /* top field */
                ;VI_CAPSEL_BOTTOM,                   /* bottom field */
                ;VI_CAPSEL_BOTH,                     /* top and bottom field */
Mirror           =FALSE       ;Whether to mirror
Flip             =FALSE       ;Whether to flip
enDIS            =FALSE        ;
DIS_X            =64          ;
DIS_Y            =64          ;
DIS_W            =256         ;
DIS_H            =256         ;
DestSizeWidth =1920
DestSizeHeight=1080             
PixFormat    =23;PIXEL_FORMAT_YUV_SEMIPLANAR_422 = 22
                ;PIXEL_FORMAT_YUV_SEMIPLANAR_420 = 23 ...etc
CompressMode =COMPRESS_MODE_NONE ;COMPRESS_MODE_NONE = 0
                                 ;COMPRESS_MODE_SEG =1 ...etc

SrcFrameRate=30 ;Source frame rate. -1: not controll
DstFrameRate=30 ;Target frame rate. -1: not controll

;----------LDC attribute--------   
EnLDC           =FALSE
LDCType         =LDC_VIEW_TYPE_ALL  ;LDC_VIEW_TYPE_ALL = 0,
                                    ;LDC_VIEW_TYPE_CROP,
LDCRatio        =0                  ;

;----------vi dis only for Hi3519V101-----------
DIS_Enable=FALSE
DIS_MovingSubjectLevel=2
DIS_NoMovementLevel=1
DIS_TimeLag=0
DIS_AngleType=VI_DIS_ANGLE_TYPE_HORIZONTAL  ;VI_DIS_ANGLE_TYPE_HORIZONTAL
                                            ;VI_DIS_ANGLE_TYPE_VERTICAL
                                            ;VI_DIS_ANGLE_TYPE_DIAGONAL
DIS_Vangle=934
DIS_bStillCrop=FALSE
DIS_Accuracy=VI_DIS_ACCURACY_HIGH   ;VI_DIS_ACCURACY_HIGH
                                    ;VI_DIS_ACCURACY_MIDDLE
                                    ;VI_DIS_ACCURACY_LOW
DIS_CameraMode=VI_DIS_CAMERA_MODE_IPC   ;VI_DIS_CAMERA_MODE_NORMAL
                                        ;VI_DIS_CAMERA_MODE_IPC
DIS_MotionType=VI_DIS_MOTION_6DOF_SOFT  ;VI_DIS_MOTION_4DOF_SOFT
                                        ;VI_DIS_MOTION_6DOF_SOFT
                                        ;VI_DIS_MOTION_6DOF_HYBRID
                                        ;VI_DIS_MOTION_8DOF_HARD
DIS_FixLevel=4
DIS_RollingShutterCoef=80
DIS_BufNum=6
DIS_CropRatio=80
DIS_FrameRate=30
DIS_bScale=TRUE
DIS_DelayFrmNum=0
DIS_RetCenterStrength=7
DIS_GyroWeight=0
;----------vi spread only for Hi3519V101-----------
SpreadEn = FALSE
SpreadCoef = 8
SpreadWidth = 1920
SpreadHeight = 1080

;----------stitch only for Hi3519V101-----------
[VI_Stitch.0]
Ldc_Enable =FALSE ;
Ldc_Ratio =0        ;
Ldc_MinRatio =0     ;
Ldc_ViewType =LDC_VIEW_TYPE_ALL
                     ;LDC_VIEW_TYPE_ALL     = 0,    /* View scale all but x and y independtly, this will keep both x and y axis ,but corner maybe lost*/
                     ;LDC_VIEW_TYPE_CROP    = 1,    /* Not use view scale, this will lost some side and corner */
Ldc_CenterXOffset =0
Ldc_CenterYOffset =0
PMFEnable = FALSE
PMFCoef=532571|14619|2041785|-3560|534299|50969118|15|12|524288|
Dest_Width=1920
Dest_Height=1080


[vpss_group.0]
VpssGrp   =0
VpssChnNum  =2
Vpss_NrEn   =TRUE
NrType      =VPSS_NR_TYPE_VIDEO;
                ;VPSS_NR_TYPE_VIDEO
                ;VPSS_NR_TYPE_SNAP
NrRefFrameNum=1;
NrRefSource = VPSS_NR_REF_FROM_RFR;
            ;VPSS_NR_REF_FROM_RFR       = 0,
            ;VPSS_NR_REF_FROM_CHN0      = 1,
            ;VPSS_NR_REF_FROM_SRC       = 2,
NrOutputMode = VPSS_NR_OUTPUT_NORMAL;
            ;VPSS_NR_OUTPUT_NORMAL      = 0,
            ;VPSS_NR_OUTPUT_DELAY       = 1,
MaxW =1920
MaxH =1080
SharpenEn=FALSE	;only for Hi3516CV300

;----------stitch only for Hi3519V101-----------
StitchBlendEn=FALSE
StitchBlend_Mode = STITCH_MODE_PERSPECTIVE;
				;STITCH_MODE_PERSPECTIVE = 0,
				;STITCH_MODE_CYLINDRICAL = 1,
StitchBlend_OutWidth=3020
StitchBlend_OutHeight=1020 
StitchBlend_OverlapPoint=1100|0|1100|0|1100|986|1881|1019

Crop_enable =FALSE   ;
Coordinate  =VPSS_CROP_ABS_COOR     ;VPSS_CROP_RATIO_COOR = 0,   /*Ratio coordinate*/
                                    ;VPSS_CROP_ABS_COOR = 1      /*Absolute coordinate*/
Crop_X      =0      ;
Crop_Y      =0      ;
Crop_W      =1920   ;
Crop_H      =1080   ;

[vpss_chn.0.0]
VpssChn      =0
enVpssExtChn =FALSE
VpssPhyChn   =-1     ;Source physical channel to be bound (ONLY for enVpssExtChn=TRUE)
Vpss_W       =1920
Vpss_H       =1080
CompressMode=COMPRESS_MODE_NONE  ;COMPRESS_MODE_NONE = 0
                ;COMPRESS_MODE_SEG =1 ...etc
;----------vpss spread only for Hi3519V101-----------
SpreadEn = FALSE
SpreadCoef = 8
SpreadWidth = 1920
SpreadHeight = 1080
       
[vpss_chn.0.1]
VpssChn      =1
enVpssExtChn =FALSE
VpssPhyChn   =-1     ;Source physical channel to be bound (ONLY for enVpssExtChn=TRUE)
Vpss_W       =1280
Vpss_H       =720
CompressMode=COMPRESS_MODE_NONE  ;COMPRESS_MODE_NONE = 0
                ;COMPRESS_MODE_SEG =1 ...etc
;----------vpss spread only for Hi3519V101-----------
SpreadEn = FALSE
SpreadCoef = 8
SpreadWidth = 1280
SpreadHeight = 720
[venc.0]
VencChn         =0
EnVenc          =TRUE
VencFormat      =VENC_FORMAT_H265   ;VENC_FORMAT_H261  = 0,  /*H261  */                                  
                                    ;VENC_FORMAT_H263  = 1,  /*H263  */                                  
                                    ;VENC_FORMAT_MPEG2 = 2,  /*MPEG2 */                                  
                                    ;VENC_FORMAT_MPEG4 = 3,  /*MPEG4 */                                  
                                    ;VENC_FORMAT_H264  = 4,  /*H264  */                                  
                                    ;VENC_FORMAT_MJPEG = 5,  /*MOTION_JPEG*/                             
                                    ;VENC_FORMAT_YUV   = 6,  /*YVU Nonsupport*/                                    
                                    ;VENC_FORMAT_JPEG  = 7,  /*JPEG*/                                  
                                    ;VENC_FORMAT_H265  = 8,  /*H265  */                                  
PicWidth        =1920
PicHeight       =1080
Profile         =0
RcMode          =VENC_RC_MODE_CBR   ;VENC_RC_MODE_CBR = 0,
                                    ;VENC_RC_MODE_VBR,
                                    ;VENC_RC_MODE_AVBR,
                                    ;VENC_RC_MODE_FIXQP,

Gop          =60
StatTime     =40
TargetFrmRate=30
BitRate      =3072
;----- only for VENC_RC_MODE_VBR ----------
QualityLevel=VBR_QUALITYLEVEL_NORMAL ;VBR_QUALITYLEVEL_GOOD=0;
                                    ;VBR_QUALITYLEVEL_NORMAL=1;
                                    ;VBR_QUALITYLEVEL_BAD=2;
GoodMaxQp=38
GoodMinQp=20
NormalMaxQp=51
NormalMinQp=30
BadMaxQp=51
BadMinQp=40
;MinIQp=MinQp+1
;----- only for VENC_RC_MODE_FIXQP ----------
IQp=28
PQp=28
BQp=28

;---- for VENC_GOP_ATTR_S ---------
GopMode =VENC_GOPMODE_SMARTP       ;VENC_GOPMODE_NORMALP    = 0,
                                    ;VENC_GOPMODE_DUALP      = 1,
                                    ;VENC_GOPMODE_SMARTP     = 2,
                                    ;VENC_GOPMODE_BIPREDB    = 3,only for Hi3519V101
                                    ;VENC_GOPMODE_LOWDELAYB  = 4,not support
;----- only for VENC_GOPMODE_NORMALP ----------
NormalP_IPQpDelta = 2
;----- only for VENC_GOPMODE_DUALP ----------
DualP_SPInterval = 4
DualP_SPQpDelta  = 2
DualP_IPQpDelta  = 2
;----- only for VENC_GOPMODE_SMARTP ----------
SmartP_BgInterval = 1200
SmartP_BgQpDelta  = 7
SmartP_ViQpDelta  = 2

;----- only for VENC_GOPMODE_BIPREDB/VENC_GOPMODE_LOWDELAYB ----------
BipredB_BFrmNum   = 1
BipredB_BQpDelta  = 2
BipredB_IpQpDelta = 2
                                           
[venc.1]
VencChn         =1
EnVenc          =TRUE
VencFormat      =VENC_FORMAT_H264   ;VENC_FORMAT_H261  = 0,  /*H261  */
                                    ;VENC_FORMAT_H263  = 1,  /*H263  */
                                    ;VENC_FORMAT_MPEG2 = 2,  /*MPEG2 */
                                    ;VENC_FORMAT_MPEG4 = 3,  /*MPEG4 */
                                    ;VENC_FORMAT_H264  = 4,  /*H264  */
                                    ;VENC_FORMAT_MJPEG = 5,  /*MOTION_JPEG*/
                                    ;VENC_FORMAT_YUV   = 6,  /*YVU Nonsupport*/
                                    ;VENC_FORMAT_JPEG  = 7,  /*JPEG*/
                                    ;VENC_FORMAT_H265  = 8,  /*H265  */
PicWidth        =1280
PicHeight       =720
Profile         =0
RcMode          =VENC_RC_MODE_CBR   ;VENC_RC_MODE_CBR = 0,
                                    ;VENC_RC_MODE_VBR,
                                    ;VENC_RC_MODE_AVBR,
                                    ;VENC_RC_MODE_FIXQP,

Gop          =60  
StatTime     =40  
TargetFrmRate=30
BitRate=4096
;----- only for VENC_RC_MODE_VBR ----------
QualityLevel=VBR_QUALITYLEVEL_NORMAL ;VBR_QUALITYLEVEL_GOOD=0;
                                    ;VBR_QUALITYLEVEL_NORMAL=1;
                                    ;VBR_QUALITYLEVEL_BAD=2;
GoodMaxQp=38
GoodMinQp=20
NormalMaxQp=51
NormalMinQp=30
BadMaxQp=51
BadMinQp=40
;MinIQp=MinQp+1
;----- only for VENC_RC_MODE_FIXQP ----------
IQp=28
PQp=28
BQp=28

;---- for VENC_GOP_ATTR_S ---------
GopMode =VENC_GOPMODE_SMARTP       ;VENC_GOPMODE_NORMALP    = 0,
                                    ;VENC_GOPMODE_DUALP      = 1,
                                    ;VENC_GOPMODE_SMARTP     = 2,
                                    ;VENC_GOPMODE_BIPREDB    = 3,only for Hi3519V101
                                    ;VENC_GOPMODE_LOWDELAYB  = 4,not support
;----- only for VENC_GOPMODE_NORMALP ----------
NormalP_IPQpDelta = 2
;----- only for VENC_GOPMODE_DUALP ----------
DualP_SPInterval = 4
DualP_SPQpDelta  = 2
DualP_IPQpDelta  = 2
;----- only for VENC_GOPMODE_SMARTP ----------
SmartP_BgInterval = 1200
SmartP_BgQpDelta  = 7
SmartP_ViQpDelta  = 2

;----- only for VENC_GOPMODE_BIPREDB/VENC_GOPMODE_LOWDELAYB ----------
BipredB_BFrmNum   = 1
BipredB_BQpDelta  = 2
BipredB_IpQpDelta = 2

;----------osd only for Hi3519V101-----------
[osd.0]
;----- only for StringOsd ----------
EnStringOsd   =FALSE
StringOsdData =hisilicon
EnAbsQp       =FALSE
Qp            =0
StringOsdX    =0
StringOsdY    =0

;----- only for TimeOsd ----------
EnTimeOsd     =FALSE
TimeOsdFormat =TIMEOSD_FORMAT_YMD_24HOUR ;TIMEOSD_FORMAT_YMD_12HOUR=0,                               
                                    ;TIMEOSD_FORMAT_YMD_24HOUR,                               
                                    ;TIMEOSD_FORMAT_DMY_12HOUR,                               
                                    ;TIMEOSD_FORMAT_DMY_24HOUR,                               
                                    ;TIMEOSD_FORMAT_MDY_12HOUR,                               
                                    ;TIMEOSD_FORMAT_MDY_24HOUR,  
TimeOsdX     =1500
TimeOsdY     =0

;----- only for InfoOsd ----------
EnInfoOsd    =FALSE
InfoOsdX     =0
InfoOsdY     =64    

EnInvertColor=FALSE  ;OSD color inversion enable
BgColor      =17969
FgColor      =32767
BgAlpha      =0
FgAlpha      =64
[osd.1]
;----- only for StringOsd ----------
EnStringOsd   =FALSE
StringOsdData =hisilicon
EnAbsQp       =FALSE
Qp            =0
StringOsdX    =0
StringOsdY    =0

;----- only for TimeOsd ----------
EnTimeOsd     =FALSE
TimeOsdFormat =TIMEOSD_FORMAT_YMD_24HOUR ;TIMEOSD_FORMAT_YMD_12HOUR=0,                               
                                    ;TIMEOSD_FORMAT_YMD_24HOUR,                               
                                    ;TIMEOSD_FORMAT_DMY_12HOUR,                               
                                    ;TIMEOSD_FORMAT_DMY_24HOUR,                               
                                    ;TIMEOSD_FORMAT_MDY_12HOUR,                               
                                    ;TIMEOSD_FORMAT_MDY_24HOUR,  
TimeOsdX     =960
TimeOsdY     =0

;----- only for InfoOsd ----------
EnInfoOsd    =FALSE
InfoOsdX     =0
InfoOsdY     =48    

EnInvertColor=FALSE  ;OSD color inversion enable
BgColor      =17969
FgColor      =32767
BgAlpha      =0
FgAlpha      =64

[vo.0]
EnVo          =TRUE
VoDev         =0
VoChn         =0
width         =720
height        =576
framerate     =30
bDoubleFrame  =0
IntfType      =VO_INTF_CVBS     ;sample:IntfType = VO_INTF_BT1120
                                ;VO_INTF_CVBS,
                                ;VO_INTF_YPBPR,
                                ;VO_INTF_VGA,
                                ;VO_INTF_BT656, 
                                ;VO_INTF_BT1120,
                                ;VO_INTF_HDMI,
                                ;VO_INTF_LCD,
                                ;VO_INTF_BT656_H,
                                ;VO_INTF_BT656_L,
                                ;VO_INTF_LCD_6BIT,
                                ;VO_INTF_LCD_8BIT,
                                ;VO_INTF_LCD_16BIT,
                                ;VO_INTF_LCD_24BIT,
IntfSync      =VO_OUTPUT_PAL
                                ;VO_OUTPUT_PAL = 0,
                                ;VO_OUTPUT_NTSC,
                                ;VO_OUTPUT_1080P24,
                                ;VO_OUTPUT_1080P25,
                                ;VO_OUTPUT_1080P30,
                                ;VO_OUTPUT_720P50, 
                                ;VO_OUTPUT_720P60,   
                                ;VO_OUTPUT_1080I50,
                                ;VO_OUTPUT_1080I60,    
                                ;VO_OUTPUT_1080P50,
                                ;VO_OUTPUT_1080P60,  ...etc
PixelFormat = PIXEL_FORMAT_YUV_SEMIPLANAR_420
                                ;PIXEL_FORMAT_RGB_1BPP = 0,
                                ;PIXEL_FORMAT_RGB_2BPP,
                                ;PIXEL_FORMAT_RGB_4BPP,
                                ;PIXEL_FORMAT_RGB_8BPP,
                                ;PIXEL_FORMAT_RGB_444,
                                ;PIXEL_FORMAT_RGB_4444,
                                ;PIXEL_FORMAT_RGB_555,
                                ;PIXEL_FORMAT_RGB_565,
                                ;PIXEL_FORMAT_RGB_1555,
                                ;PIXEL_FORMAT_RGB_888,
                                ;PIXEL_FORMAT_RGB_8888,
                                ;PIXEL_FORMAT_RGB_PLANAR_888,
                                ;PIXEL_FORMAT_RGB_BAYER_8BPP,
                                ;PIXEL_FORMAT_RGB_BAYER_10BPP,
                                ;PIXEL_FORMAT_RGB_BAYER_12BPP,
                                ;PIXEL_FORMAT_RGB_BAYER_14BPP,
                                ;PIXEL_FORMAT_RGB_BAYER,
                                ;PIXEL_FORMAT_YUV_A422,
                                ;PIXEL_FORMAT_YUV_A444,
                                ;PIXEL_FORMAT_YUV_PLANAR_422,
                                ;PIXEL_FORMAT_YUV_PLANAR_420,
                                ;PIXEL_FORMAT_YUV_PLANAR_444,
                                ;PIXEL_FORMAT_YUV_SEMIPLANAR_422,
                                ;PIXEL_FORMAT_YUV_SEMIPLANAR_420,
                                ;PIXEL_FORMAT_YUV_SEMIPLANAR_444,
                                ;PIXEL_FORMAT_UYVY_PACKAGE_422,
                                ;PIXEL_FORMAT_YUYV_PACKAGE_422,
                                ;PIXEL_FORMAT_VYUY_PACKAGE_422,
                                ;PIXEL_FORMAT_YCbCr_PLANAR,
                                ;PIXEL_FORMAT_YUV_400,
ViDev       =0
ViChn       =0
[Bind_commom]
VpssBindViNum    = 1
VencBindVpssNum  = 2

[VpssbindVi.0]
VpssGrp =0
ViDev   =0
ViChn   =0
[VencbindVpss.0]
VencGrp =0
VencChn =0
VpssGrp =0
VpssChn =0
[VencbindVpss.1]
VencGrp =1
VencChn =1
VpssGrp =0
VpssChn =1
[SnapbindVpss]
VencSnapGrp=9
VencSnapChn=9
VpssSnapGrp=0
VpssSnapChn=0
[VobindVpss]
VoDev   =0
VoChn   =0
VpssGrp =0
VpssChn =0
bUseVpss=TRUE                   ;TRUE:vo bind to vpss,
                                ;FALSE:vo bind to vi