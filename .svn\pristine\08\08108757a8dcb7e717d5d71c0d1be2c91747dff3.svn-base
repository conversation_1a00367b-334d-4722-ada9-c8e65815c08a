/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_vosd.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-11-22

文件功能描述: 封装Sigmastar视频遮挡叠加模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <linux/fb.h>
#include <error.h>
#include <sys/ioctl.h>
#include <sys/prctl.h>
#include <fcntl.h>
#include <time.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "board.h"
#include "common.h"
#include "mi_common.h"
#include "mi_rgn.h"
#include "mpp_com.h"
#include "mpp_font.h"
#include "mpp_bmp.h"
#include "mpp_vosd.h"
#include "media.h"

#define MPP_VOSD_TIMEBMP_BUFSIZE1 16 * 1024     /* 分配给时间位图的缓冲空间 (1x尺寸) */
#define MPP_VOSD_TIMEBMP_BUFSIZE2 32 * 1024     /* 分配给时间位图的缓冲空间 (2x尺寸) */
#define MPP_VOSD_TIMEBMP_BUFSIZE3 64 * 1024     /* 分配给时间位图的缓冲空间 (3x尺寸) */
#define MPP_VOSD_TIMEBMP_BUFSIZE4 120 * 1024    /* 分配给时间位图的缓冲空间 (4x尺寸) */
#define MPP_VOSD_TEMPBMP_BUFSIZE  512 * 1024    /* 分配给车牌号或通道名的位图缓冲空间 */
#define MPP_VOSD_STATE_BUFSIZE    512 * 1024    /* 分配给状态信息的位图缓冲空间 */

#define MPP_OVERLAY_NUM         6
#define MPP_OVERLAY_TIME        0
#define MPP_OVERLAY_CHNNAME     1
#define MPP_OVERLAY_BATTERY     2
#define MPP_OVERLAY_SINGNAL     3
#define MPP_OVERLAY_ROICON      4
#define MPP_OVERLAY_STATE       5

#define MPP_VOSD_LINE_MAXCHAR       32

MI_RGN_PaletteTable_t g_stPaletteTable =
{
    { //index0 ~ index15
         {255,   0,   0,   0}, {255, 255,   0,   0}, {255,   0, 255,   0}, {255,   0,   0, 255},
         {255, 255, 255,   0}, {255,   0, 112, 255}, {255,   0, 255, 255}, {255, 255, 255, 255},
         {255, 128,   0,   0}, {255, 128, 128,   0}, {255, 128,   0, 128}, {255,   0, 128,   0},
         {255,   0,   0,   0}, {255,   0, 128, 128}, {255, 128, 128, 128}, {255,  64,  64,  64},
         //index16 ~ index31
         {255,   0,   0,   0}, {  0,   0,   0,  30}, {  0,   0, 255,  60}, {  0, 128,   0,  90},
         {255,   0,   0, 120}, {  0, 255, 255, 150}, {255, 255,   0, 180}, {  0, 255,   0, 210},
         {255,   0, 255, 240}, {192, 192, 192, 255}, {128, 128, 128,  10}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index32 ~ index47
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index48 ~ index63
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index64 ~ index79
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index80 ~ index95
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index96 ~ index111
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index112 ~ index127
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index128 ~ index143
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index144 ~ index159
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index160 ~ index175
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index176 ~ index191
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index192 ~ index207
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index208 ~ index223
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index224 ~ index239
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         // (index236 :192,160,224 defalut colorkey)
         {192, 160, 224, 255}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         //index240 ~ index255
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0},
         {  0,   0,   0,   0}, {  0,   0,   0,   0}, {  0,   0,   0,   0}, {192, 160, 224, 255}
    }
};


/* 区域状态 */
typedef struct tagRgnStat_S
{
    SV_BOOL     bCreated;            /* 是否被创建 */
    uint32      u32Handle;           /* 区域句柄值 */
    SV_BOOL     bShow;               /* 区域是否显示 */
    SV_RECT_S   stRect;              /* 区域位置及大小 */
} MPP_VOSD_RGN_S;

/* 画面遮挡区域 */
typedef struct tagCoverRgn_S
{
    MPP_VOSD_RGN_S astRegion[VOSD_MAX_COVER_RECT];      /* 画面遮挡区域 */
} MPP_COVER_RGN_S;

/* 视频叠加区域 */
typedef struct tagOverlayRgn_S
{
    BATTERY_STAT   enBatteryStat;    /* 电池电量状态 */
    SINGNAL_STAT   enSingnalStat;    /* 信号值状态 */
    VOSD_TIME_FMT  enTimeFmt;        /* 时间日期格式 */
    MPP_VOSD_RGN_S stTimeRgn;        /* 时间叠加区域 */
    MPP_VOSD_RGN_S stChnNameRgn;     /* 通道名叠加区域 */
	MPP_VOSD_RGN_S stChnName2Rgn;     /* 通道名叠加区域 */
    MPP_VOSD_RGN_S stStateRgn;       /* 状态信息叠加区域 */
    MPP_VOSD_RGN_S stBatteryRgn;     /* 电池电量叠加区域 */
    MPP_VOSD_RGN_S stSingnalRgn;     /* 信号值叠加区域 */
    MPP_VOSD_RGN_S stCameraRgn;      /* 摄像头标识叠加区域 */
    MPP_VOSD_RGN_S stRoIconRgn;      /* 旋转图标叠加区域 */
} MPP_OVERLAY_RGN_S;

/* 叠加字符串参数 */
typedef struct tagOverStr_S
{
    SV_BOOL     bUpdate;             /* 字符串是否有更新 */
    char        szOverStr[MPP_VOSD_MAX_OVERSTR_LEN];    /* 叠加的字符串内容 */
} MPP_OVER_STR_S;

/* 叠加字符位图样式 */
typedef struct tagOverStyle_S
{
    SV_BOOL     bUpdate;             /* 字符样式是否有更新 */
    MPP_VOSD_SIZE_E nCharSize;       /* 字符的尺寸 */
} MPP_OVER_STYLE_S;

/* 叠加字符串位图信息 */
typedef struct tagOverBmp_S
{
    char        szOverStr[MPP_VOSD_MAX_OVERSTR_LEN];    /* 叠加的字符串内容 */
    SV_SIZE_S   stBmpSize;           /* 位图的尺寸 */
    void       *pvBuf1;              /* 位图缓冲区指针 (1x尺寸) */
    void       *pvBuf2;              /* 位图缓冲区指针 (2x尺寸) */
    void       *pvBuf3;              /* 位图缓冲区指针 (3x尺寸) */
    void       *pvBuf4;              /* 位图缓冲区指针 (4x尺寸) */
} MPP_OVER_BMP_S;

/* 视频遮挡叠加模块控制信息 */
typedef struct tagVosdInfo_S
{
    uint32      u32ChnNum;           /* 视频源通道数目 */
    MPP_COVER_RGN_S astChnCover[VIODE_MAX_CHN*4];       /* 通道画面区域遮挡 */
    MPP_OVERLAY_RGN_S astChnOverlay[VIODE_MAX_CHN*4];   /* 通道视频叠加 */
    MPP_OVER_STR_S stTimeStr;        /* 叠加的时间字符串 */
    MPP_OVER_STR_S stCameraStr;      /* 叠加的摄像头标识字符串 */
    MPP_OVER_STR_S stChnName;        /* 叠加的通道名 */
    MPP_OVER_STR_S stState;          /* 叠加的状态信息（位置与速度） */
    MPP_OVER_STYLE_S astChnOverStyle[VIODE_MAX_CHN*4];  /* 通道叠加字符的样式 */
    MPP_OVER_BMP_S stTimeBmp;        /* 叠加的时间位图 */
    sint32      s32TimeMs;           /* BSD事件叠加倒计时(ms) */
    uint32      u32TID;              /* 视频叠加线程ID */
    SV_BOOL     bRunning;            /* 线程是否正在运行 */
    SV_BOOL     bException;          /* 线程是否出现异常 */
    SV_BOOL     bResetBuf;           /* 是否重置字库缓存 */
    pthread_mutex_t mutexLock;       /* 参数设置线程互斥锁 */
    VOSD_POS_E      enOsdPosition;   /* OSD叠加位置 */
} MPP_VOSD_INFO_S;

extern MPP_PHY_CHN g_astViPhyChn[VIM_MAX_CHN_NUM];

MPP_VOSD_INFO_S m_stVosdInfo = {0};  /* 视频遮挡叠加控制信息 */


sint32 mpp_vosd_CreateCoverRgn(sint32 s32Chn, VOSD_COVER_S *pstCover);
sint32 mpp_vosd_DestroyCoverRgn(sint32 s32Chn);
sint32 mpp_vosd_CreateOverlayRgn(sint32 s32Chn, VOSD_OVERLAY_S *pstOverlay);
sint32 mpp_vosd_DestroyOverlayRgn(sint32 s32Chn);
void * mpp_vosd_Body(void *pvArg);
sint32 mpp_vosd_UpdateOverRgn();
sint32 mpp_vosd_UpdateTimeBmp(char *pszTimeStr);
sint32 mpp_vosd_UpdateChnNameBmp();
sint32 mpp_vosd_UpdateStateBmp();
sint32 mpp_vosd_UpdateBatteryBmp(sint32 s32Chn);
sint32 mpp_vosd_UpdateSingnalBmp(sint32 s32Chn);
sint32 mpp_vosd_UpdateRoIconBmp(sint32 s32Chn, uint32 u32PicNum);

/******************************************************************************
 * 函数功能: 初始化VOSD模块
 * 输入参数: pstVosdConf --- 视频遮挡叠加配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Init(MPP_VOSD_CONF_S *pstVosdConf)
{
    sint32 s32Ret = 0, i;
    sint32 VeChnId = 0;
    MPP_VOSD_SIZE_E enCharSize = MPP_VOSD_SIZE_1X;
    VOSD_COVER_S stCover = {0};
    void *pvBuf1 = NULL;
    void *pvBuf2 = NULL;
    void *pvBuf3 = NULL;
    void *pvBuf4 = NULL;

    if (NULL == pstVosdConf)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstVosdConf->u32ChnNum || pstVosdConf->u32ChnNum > VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = MI_RGN_Init(&g_stPaletteTable);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MI_RGN_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    memset(&m_stVosdInfo, 0x0, sizeof(MPP_VOSD_INFO_S));
    m_stVosdInfo.u32ChnNum = pstVosdConf->u32ChnNum;  

    pvBuf1 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE1);
    if (NULL == pvBuf1)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        return ERR_NOMEM;
    }
    memset(pvBuf1, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE1);

    pvBuf2 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE2);
    if (NULL == pvBuf2)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        free(pvBuf1);
        return ERR_NOMEM;
    }
    memset(pvBuf2, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE2);

    pvBuf3 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE3);
    if (NULL == pvBuf3)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        free(pvBuf1);
        free(pvBuf2);
        return ERR_NOMEM;
    }
    memset(pvBuf3, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE3);

    pvBuf4 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE4);
    if (NULL == pvBuf4)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        free(pvBuf1);
        free(pvBuf2);
        free(pvBuf3);
        return ERR_NOMEM;
    }
    memset(pvBuf4, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE4);

    s32Ret = pthread_mutex_init(&m_stVosdInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        free(pvBuf1);
        free(pvBuf2);
        free(pvBuf3);
        free(pvBuf4);
        return ERR_SYS_NOTREADY;
    }

    stCover.bEnable = SV_FALSE;
    stCover.u32RectNum = VOSD_MAX_COVER_RECT;
    for (i = 0; i < stCover.u32RectNum; i++)
    {
        stCover.astRect[i].u32Width = 10;
        stCover.astRect[i].u32Height = 10;
    }
    
    for (i = 0; i < pstVosdConf->u32ChnNum; i++)
    {
        s32Ret = mpp_vosd_CreateCoverRgn(i, &stCover);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateCoverRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }

        /* 主码流 */
        VeChnId = m_stVosdInfo.u32ChnNum + i;//vpe的主码流为port1
        if (pstVosdConf->u32PriHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
        }
        else if (pstVosdConf->u32PriHeight < 720)
        {
            enCharSize = MPP_VOSD_SIZE_2X;
        }
        else if (pstVosdConf->u32PriHeight < 1080)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_4X;
        }
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
        s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }

        /* 子码流 */
    #if (BOARD == BOARD_IPCR20S3 || BOARD == BOARD_IPCR20S4 || BOARD == BOARD_IPCR20S5 || BOARD == BOARD_IPTR20S1)
        VeChnId = 2 * m_stVosdInfo.u32ChnNum + i;//vpe的子码流为port2
        if (pstVosdConf->u32SecHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_2X;
        }
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
        s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }
    #endif
        
        /* 主图片流 */
        VeChnId = i;//vpe的图片流为port0
        if (pstVosdConf->u32JpegHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
        }
        else if (pstVosdConf->u32JpegHeight < 720)
        {
            enCharSize = MPP_VOSD_SIZE_2X;
        }
        else if (pstVosdConf->u32JpegHeight < 1080)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_4X;
        }
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
        s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }

        /* 子图片流 */
        VeChnId = 3 * m_stVosdInfo.u32ChnNum + i;
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = MPP_VOSD_SIZE_1X;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
/*
        s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }
*/
    }

    strcpy(m_stVosdInfo.stChnName.szOverStr, pstVosdConf->szChnName);
    print_level(SV_DEBUG, "m_stVosdInfo.stChnName.szOverStr is %s.\n", m_stVosdInfo.stChnName.szOverStr);
    strcpy(m_stVosdInfo.stState.szOverStr, pstVosdConf->szState);
    print_level(SV_DEBUG, "m_stVosdInfo.stState.szOverStr is %s.\n", m_stVosdInfo.stState.szOverStr);
    strcpy(m_stVosdInfo.stCameraStr.szOverStr, "No Camera");
    m_stVosdInfo.stChnName.bUpdate = SV_TRUE;
    m_stVosdInfo.stCameraStr.bUpdate = SV_TRUE;
    m_stVosdInfo.stState.bUpdate = SV_TRUE;
    m_stVosdInfo.stTimeBmp.pvBuf1 = pvBuf1;
    m_stVosdInfo.stTimeBmp.pvBuf2 = pvBuf2;
    m_stVosdInfo.stTimeBmp.pvBuf3 = pvBuf3;
    m_stVosdInfo.stTimeBmp.pvBuf4 = pvBuf4;
    m_stVosdInfo.enOsdPosition = pstVosdConf->stOverlay.enOsdPosition;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化VOSD模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Fini()
{
    sint32 s32Ret = 0, i;

    for (i = 0; i < m_stVosdInfo.u32ChnNum; i++)
    {
        s32Ret = mpp_vosd_DestroyCoverRgn(i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyCoverRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vosd_DestroyOverlayRgn(i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vosd_DestroyOverlayRgn(m_stVosdInfo.u32ChnNum + i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vosd_DestroyOverlayRgn(2 * m_stVosdInfo.u32ChnNum + i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
/*
        s32Ret = mpp_vosd_DestroyOverlayRgn(3 * m_stVosdInfo.u32ChnNum + i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
*/
    }

    s32Ret = pthread_mutex_destroy(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_destroy failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    free(m_stVosdInfo.stTimeBmp.pvBuf1);
    free(m_stVosdInfo.stTimeBmp.pvBuf2);
    free(m_stVosdInfo.stTimeBmp.pvBuf3);
    free(m_stVosdInfo.stTimeBmp.pvBuf4);
    m_stVosdInfo.stTimeBmp.pvBuf1 = NULL;
    m_stVosdInfo.stTimeBmp.pvBuf2 = NULL;
    m_stVosdInfo.stTimeBmp.pvBuf3 = NULL;
    m_stVosdInfo.stTimeBmp.pvBuf4 = NULL;

    s32Ret = MI_RGN_DeInit();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "MI_RGN_DeInit failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动VOSD模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread;

    m_stVosdInfo.bRunning = SV_TRUE;
    m_stVosdInfo.bException = SV_FALSE;
    m_stVosdInfo.bResetBuf = SV_TRUE;
    s32Ret = pthread_create(&thread, NULL, mpp_vosd_Body, &m_stVosdInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VOSD failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stVosdInfo.u32TID = thread;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止VOSD模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Stop()
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;

    m_stVosdInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(m_stVosdInfo.u32TID, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for VOSD failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    
    return SV_SUCCESS;
}

static uint32 mpp_utf8ToUnicode(char *str,uint32 *szUnicode) {
    uint32 len = strlen(str);
    uint32 i, j,count=0;
    for (i=0,j=0; i<len; ++i) {
        if ((str[i] & 0x80) == 0) { // ASCII 码值范围：0 ~ 127
            szUnicode[j++] = str[i];
        } else if ((str[i] & 0xE0) == 0xC0 && i+1<len && (str[i+1] & 0xC0) == 0x80) { // 双字节编码，U+0080 ~ U+07FF
            szUnicode[j++] = ((str[i] & 0x1F) << 6) | (str[i+1] & 0x3F);
            ++i;
        } else if ((str[i] & 0xF0) == 0xE0 && i+2<len && (str[i+1] & 0xC0) == 0x80 && (str[i+2] & 0xC0) == 0x80) { //三字节编码，U+08000 ~ U+FFFFF
            szUnicode[j++] = ((str[i] & 15)<<12)|((str[++i]&63)<<6)|(str[++i]&63);
        } else {
            printf("Unsupported character: %c\n", str[i]);
            return;
        }
		count++;
    }
	
	return count;
}

/******************************************************************************
 * 函数功能: VOSD模块线程体
 * 输入参数: pstVencInfo --- 视频编码控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_vosd_Body(void *pvArg)
{
    sint32 s32Ret = 0, i, j;
    uint32 u32PicNum = 0;
    SV_BOOL bException = 0;
    struct timeval tvLast, tvNow;
    struct timezone tz;
    struct tm stTmNow = {0};
    char szTimeStr[MPP_VOSD_MAX_OVERSTR_LEN];
    char szCameraStr[MPP_VOSD_MAX_OVERSTR_LEN];
    char szStateStr[MPP_VOSD_MAX_OVERSTR_LEN];
    MPP_VOSD_INFO_S *pstVosdInfo = (MPP_VOSD_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "mpp_vosd_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }    

    while (pstVosdInfo->bRunning)
    {
        sleep_ms(10);
        s32Ret = gettimeofday(&tvNow, &tz);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "gettimeofday failed! [err=%#x]\n", errno);
            continue;
        }
        
#if (BOARD == BOARD_IPCR20S3)
        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
        {
            if((tvNow.tv_usec / 10000) % 20 == 0)
            {   
                j = (u32PicNum++)%ROTATING_ICON_BITMAP_NUM;
                for(i = 0; i < 3; i++)
                {
                    s32Ret = mpp_vosd_UpdateRoIconBmp(i, j);
                    if (MI_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "mpp_vosd_UpdateRoIconBmp failed! s32Ret: 0x%x.\n", s32Ret);
                        return s32Ret;
                    }   
                }
            }
            
            if ((tvNow.tv_usec > 150000 && tvNow.tv_usec < 850000) || (tvLast.tv_sec == tvNow.tv_sec)
                && (!m_stVosdInfo.astChnOverStyle[0].bUpdate && !m_stVosdInfo.astChnOverStyle[1].bUpdate))
    		{
    			continue;
    		}
        }
        else
        {
            if ((tvNow.tv_usec > 50000 && tvNow.tv_usec < 950000) || (tvLast.tv_sec == tvNow.tv_sec)
                && (!m_stVosdInfo.astChnOverStyle[0].bUpdate && !m_stVosdInfo.astChnOverStyle[1].bUpdate))
    		{
    			continue;
    		}
        }
#else
        if ((tvNow.tv_usec > 50000 && tvNow.tv_usec < 950000) || (tvLast.tv_sec == tvNow.tv_sec)
                && (!m_stVosdInfo.astChnOverStyle[0].bUpdate && !m_stVosdInfo.astChnOverStyle[1].bUpdate))
        	{
        		continue;
        	}
#endif

		tvLast.tv_sec = tvNow.tv_sec;
		tvLast.tv_usec = tvNow.tv_usec;
        tvNow.tv_sec += (tz.tz_minuteswest * 60);
        gmtime_r((time_t *)&tvNow.tv_sec, &stTmNow);

        switch (m_stVosdInfo.astChnOverlay[0].enTimeFmt)
        {
            case VOSD_TIME_FMT_YYYYMMDD:
                sprintf(szTimeStr, "%04d-%02d-%02d %02d:%02d:%02d", 1900 + stTmNow.tm_year, 1 + stTmNow.tm_mon, stTmNow.tm_mday, \
                                                                stTmNow.tm_hour, stTmNow.tm_min, stTmNow.tm_sec);
                break;

            case VOSD_TIME_FMT_MMDDYYYY:
                sprintf(szTimeStr, "%02d-%02d-%04d %02d:%02d:%02d", 1 + stTmNow.tm_mon, stTmNow.tm_mday, 1900 + stTmNow.tm_year, \
                                                                stTmNow.tm_hour, stTmNow.tm_min, stTmNow.tm_sec);
                break;

            case VOSD_TIME_FMT_DDMMYYYY:
                sprintf(szTimeStr, "%02d-%02d-%04d %02d:%02d:%02d", stTmNow.tm_mday, 1 + stTmNow.tm_mon, 1900 + stTmNow.tm_year, \
                                                                stTmNow.tm_hour, stTmNow.tm_min, stTmNow.tm_sec);
                break;
        }
        
        s32Ret = pthread_mutex_lock(&m_stVosdInfo.mutexLock);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
            continue;
        }

        s32Ret = mpp_vosd_UpdateOverRgn();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateOverRgn failed! [err=%#x]\n", s32Ret);
        }

        s32Ret = mpp_vosd_UpdateTimeBmp(szTimeStr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateTimeBmp failed! [err=%#x]\n", s32Ret);
        }

        s32Ret = mpp_vosd_UpdateChnNameBmp();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateChnNameBmp failed! [err=%#x]\n", s32Ret);
        }
#if (BOARD == BOARD_WFTR20S3)		
		
		s32Ret = mpp_vosd_UpdateChnName2Bmp();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateChnNameBmp failed! [err=%#x]\n", s32Ret);
        }
			
#endif


#if (BOARD == BOARD_IPCR20S3)
        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
        {
            s32Ret = mpp_vosd_UpdateStateBmp();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vosd_UpdateChnNameBmp failed! [err=%#x]\n", s32Ret);
            }
        }
#endif
        s32Ret = mpp_vosd_UpdateCameraBmp();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateChnNameBmp failed! [err=%#x]\n", s32Ret);
        }

        s32Ret = pthread_mutex_unlock(&m_stVosdInfo.mutexLock);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        }
    }

    return NULL;
}


/******************************************************************************
 * 函数功能: 创建通道的遮挡区域
 * 输入参数: s32Chn --- 通道号
             pstCover --- 画面遮挡区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_CreateCoverRgn(sint32 s32Chn, VOSD_COVER_S *pstCover)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁通道的遮挡区域
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_DestroyCoverRgn(sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
             pstOverlay --- 视频叠加区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_CreateOverlayRgn(sint32 s32Chn, VOSD_OVERLAY_S *pstOverlay)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_Attr_t stRgnAttr;
    MI_RGN_ChnPortParam_t stChnAttr = {0};

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstOverlay)
    {
        return ERR_NULL_PTR;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    /* 时间区域 */
    u32Handle = MPP_OVERLAY_NUM * s32Chn;
    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = MPP_VOSD_TIMEWID_2X;
    stRgnAttr.stOsdInitParam.stSize.u32Height = MPP_VOSD_HEIGHT_2X;
    s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = pstOverlay->bShowTime;
    stChnAttr.stPoint.u32X = pstOverlay->stTimePos.s32X;
    stChnAttr.stPoint.u32Y = pstOverlay->stTimePos.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_TIME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 255;
    }
    else
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    }    
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

    m_stVosdInfo.astChnOverlay[s32Chn].enTimeFmt = pstOverlay->enTimeFmt;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow = pstOverlay->bShowTime;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.s32X = pstOverlay->stTimePos.s32X;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.s32Y = pstOverlay->stTimePos.s32Y;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.u32Width = MPP_VOSD_TIMEWID_2X;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;

	/* 通道名区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 1;
    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt          = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width    = 80;
    stRgnAttr.stOsdInitParam.stSize.u32Height   = MPP_VOSD_HEIGHT_2X;
    s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = pstOverlay->bShowChnName;
    stChnAttr.stPoint.u32X = pstOverlay->stChnNamePos.s32X;
    stChnAttr.stPoint.u32Y = pstOverlay->stChnNamePos.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_CHNNAME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 255;
    }
    else
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    }
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow = pstOverlay->bShowChnName;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.s32X = pstOverlay->stChnNamePos.s32X;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.s32Y = pstOverlay->stChnNamePos.s32Y;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.u32Width = 80;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;

#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    /* 摄像头标识 */
    u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;
    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt       = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = MPP_VOSD_TIMEWID_2X;
    stRgnAttr.stOsdInitParam.stSize.u32Height = MPP_VOSD_HEIGHT_2X;
    s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = pstOverlay->bShowCamera;
    stChnAttr.stPoint.u32X = pstOverlay->stCameraPos.s32X;
    stChnAttr.stPoint.u32Y = pstOverlay->stCameraPos.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_TIME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    print_level(SV_DEBUG, "handle:%d, Camera(%d,%d),bShow:%d\n", u32Handle, pstOverlay->stCameraPos.s32X, pstOverlay->stCameraPos.s32Y, stChnAttr.bShow);
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bShow = pstOverlay->bShowCamera;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.stRect.s32X = pstOverlay->stCameraPos.s32X;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.stRect.s32Y = pstOverlay->stCameraPos.s32Y;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.stRect.u32Width = MPP_VOSD_TIMEWID_2X;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;

	/* 通道名2区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 3;
    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt          = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width    = 80;
    stRgnAttr.stOsdInitParam.stSize.u32Height   = MPP_VOSD_HEIGHT_2X;
    s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = pstOverlay->bShowChnName;
    stChnAttr.stPoint.u32X = pstOverlay->stChnName2Pos.s32X;
    stChnAttr.stPoint.u32Y = pstOverlay->stChnName2Pos.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_CHNNAME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bShow = pstOverlay->bShowChnName;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.stRect.s32X = pstOverlay->stChnName2Pos.s32X;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.stRect.s32Y = pstOverlay->stChnName2Pos.s32Y;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.stRect.u32Width = 80;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;

#elif (BOARD == BOARD_IPCR20S3)
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {
        /* 旋转图标区域 */
        u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;    
        stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
        stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stRgnAttr.stOsdInitParam.stSize.u32Width  = MPP_VOSD_ROICON_W_4X;
        stRgnAttr.stOsdInitParam.stSize.u32Height = MPP_VOSD_ROICON_H_4X;

        s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
        if(MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_Create failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        //stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bShow;
        stChnAttr.bShow  = pstOverlay->bShowRoIcon;
        stChnAttr.stPoint.u32X = MPP_VOSD_ROICON_X_4X;
        stChnAttr.stPoint.u32Y = MPP_VOSD_ROICON_Y_4X;
        stChnAttr.unPara.stOsdChnPort.u32Layer    = MPP_OVERLAY_BATTERY; //MPP_OVERLAY_ROICON;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 0;
        
        s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bCreated = SV_TRUE;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.u32Handle = u32Handle;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bShow = pstOverlay->bShowRoIcon;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.stRect.s32X = MPP_VOSD_ROICON_X_4X;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.stRect.s32Y = MPP_VOSD_ROICON_Y_4X;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.stRect.u32Width = MPP_VOSD_ROICON_W_4X;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.stRect.u32Height = MPP_VOSD_ROICON_H_4X;
    }
    else if (BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        /* 状态信息区域 */
    	u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;
        stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
        stRgnAttr.stOsdInitParam.ePixelFmt          = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stRgnAttr.stOsdInitParam.stSize.u32Width    = 80;
        stRgnAttr.stOsdInitParam.stSize.u32Height   = MPP_VOSD_HEIGHT_2X;
        s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_Create failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow  = pstOverlay->bShowState;
        stChnAttr.stPoint.u32X = pstOverlay->stStatePos.s32X;
        stChnAttr.stPoint.u32Y = pstOverlay->stStatePos.s32Y;
        stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_BATTERY;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
        s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bCreated = SV_TRUE;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle = u32Handle;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow = pstOverlay->bShowState;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.stRect.s32X = pstOverlay->stStatePos.s32X;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.stRect.s32Y = pstOverlay->stStatePos.s32Y;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.stRect.u32Width = 80;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;
    }
#endif

	print_level(SV_DEBUG, "[ch%d]handle: time=%d, chName=%d\n", s32Chn, \
	                        m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle, \
	                        m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle);
#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    print_level(SV_DEBUG, "[ch%d]handle: camera=%d\n", s32Chn, \
	                        m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle);
#elif (BOARD == BOARD_IPCR20S3)
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {
        print_level(SV_DEBUG, "[ch%d]handle: roIcon=%d\n", s32Chn, \
	                        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.u32Handle);
    }
    else if (BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        print_level(SV_DEBUG, "[ch%d]handle: state=%d\n", s32Chn, \
	                        m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle);
    }
#endif
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
             pstCover --- 画面遮挡区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_DestroyOverlayRgn(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    SV_BOOL bFailure = SV_FALSE;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;
    
    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = MI_RGN_Destroy(u32Handle);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated = SV_FALSE;
    }

    if (m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = MI_RGN_Destroy(u32Handle);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated = SV_FALSE;
    }
    
#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    if (m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle;
        s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = MI_RGN_Destroy(u32Handle);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bCreated = SV_FALSE;
    }
	
	if (m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.u32Handle;
        s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = MI_RGN_Destroy(u32Handle);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bCreated = SV_FALSE;
    }
#elif (BOARD == BOARD_IPCR20S3)
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {
        if (m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bCreated)
        {
            u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.u32Handle;
            s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
            if(MI_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
            }
            
            s32Ret = MI_RGN_Destroy(u32Handle);
            if(MI_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "MI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
                bFailure = SV_TRUE;
            }
            m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bCreated = SV_FALSE;
        }
    }
    else if (BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        if (m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bCreated)
        {
            u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle;
            s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
            if(MI_SUCCESS != s32Ret)
        	{
        		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
        	}
        	
            s32Ret = MI_RGN_Destroy(u32Handle);
            if(MI_SUCCESS != s32Ret)
        	{
        		print_level(SV_ERROR, "MI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
        		bFailure = SV_TRUE;
        	}
        	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bCreated = SV_FALSE;
        }
    }
#endif

    if (bFailure)
    {
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新创建通道的各视频叠加区域
 * 输入参数: s32Chn --- 通道号
             stTimeRect --- 时间矩形区域
             stChnNameRect --- 通道名矩形区域
             stBatteryRect --- 电池电量矩形区域
             stSingnalRect --- 信号值矩形区域
             stRoIconRect --- 旋转图标矩形区域
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_RecreateOverlayRgn(sint32 s32Chn, SV_RECT_S stTimeRect, SV_RECT_S stChnNameRect,SV_RECT_S stChnName2Rect, SV_RECT_S stCameraRect, SV_RECT_S stRoIconRect, SV_RECT_S stStateRect)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;    
    MI_RGN_ChnPort_t stChn;
    MI_RGN_Attr_t stRgnAttr = {0};
    MI_RGN_ChnPortParam_t stChnAttr = {0};

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4
        || 0 == stTimeRect.u32Width || stTimeRect.u32Width > 2047
        || 0 == stTimeRect.u32Height || stTimeRect.u32Height >2047
        || 0 == stChnNameRect.u32Width || stChnNameRect.u32Width > 2047
        || 0 == stChnNameRect.u32Height || stChnNameRect.u32Height >2047)
    {
        return ERR_ILLEGAL_PARAM;
    }

    //mpp_vosd_DestroyOverlayRgn(s32Chn);
	stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    /* 时间区域 */
    u32Handle = MPP_OVERLAY_NUM * s32Chn;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x] [handle:%d PortId:%d]\n", s32Ret, u32Handle, stChn.s32OutputPortId);
	}

	s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = stTimeRect.u32Width;
    stRgnAttr.stOsdInitParam.stSize.u32Height = stTimeRect.u32Height;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow;
    stChnAttr.stPoint.u32X = stTimeRect.s32X;
    stChnAttr.stPoint.u32Y = stTimeRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_TIME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 255;
    }
    else
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    }
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect = stTimeRect;

	/* 通道名区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 1;
	s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
	}

	s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = stChnNameRect.u32Width;
    stRgnAttr.stOsdInitParam.stSize.u32Height = stChnNameRect.u32Height;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow;
	stChnAttr.stPoint.u32X = stChnNameRect.s32X;
    stChnAttr.stPoint.u32Y = stChnNameRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_CHNNAME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 255;
    }
    else
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    }
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect = stChnNameRect;

#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    /* 摄像头标识区域 */
    u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
	}

	s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
    
    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = stCameraRect.u32Width;
    stRgnAttr.stOsdInitParam.stSize.u32Height = stCameraRect.u32Height;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bShow;
    stChnAttr.stPoint.u32X = stCameraRect.s32X;
    stChnAttr.stPoint.u32Y = stCameraRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_TIME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.stRect = stCameraRect;

	/* 通道名2区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 3;
	s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
	}

	s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = stChnName2Rect.u32Width;
    stRgnAttr.stOsdInitParam.stSize.u32Height = stChnName2Rect.u32Height;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bShow;
	stChnAttr.stPoint.u32X = stChnName2Rect.s32X;
    stChnAttr.stPoint.u32Y = stChnName2Rect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_CHNNAME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.stRect = stChnName2Rect;
#elif (BOARD == BOARD_IPCR20S3)
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {
        /* 旋转图标区域 */
        u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;
        s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
        }

        s32Ret = MI_RGN_Destroy(u32Handle);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
        
        stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
        stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stRgnAttr.stOsdInitParam.stSize.u32Width  = stRoIconRect.u32Width;
        stRgnAttr.stOsdInitParam.stSize.u32Height = stRoIconRect.u32Height;
        
        s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
        if(MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_Create failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        print_level(SV_WARN, "m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bShow = %d.\n", m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bShow);
        stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bShow;
        //stChnAttr.bShow  = SV_TRUE;
        stChnAttr.stPoint.u32X = stRoIconRect.s32X;
        stChnAttr.stPoint.u32Y = stRoIconRect.s32Y;
        stChnAttr.unPara.stOsdChnPort.u32Layer    = MPP_OVERLAY_BATTERY; //MPP_OVERLAY_ROICON;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 0;
        
        s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bCreated = SV_TRUE;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.u32Handle = u32Handle;
        m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.stRect = stRoIconRect;        
    }
    else if (BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        /* 状态信息区域 */
    	u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;
    	s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
    	}

    	s32Ret = MI_RGN_Destroy(u32Handle);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
        stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stRgnAttr.stOsdInitParam.stSize.u32Width  = stStateRect.u32Width;
        stRgnAttr.stOsdInitParam.stSize.u32Height = stStateRect.u32Height;
    	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_Create failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow;
    	stChnAttr.stPoint.u32X = stStateRect.s32X;
        stChnAttr.stPoint.u32Y = stStateRect.s32Y;
        stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_BATTERY;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
        s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bCreated = SV_TRUE;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle = u32Handle;
    	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.stRect = stStateRect;
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新创建通道的通道名视频叠加区域
 * 输入参数: stRgnRect --- 视频叠加区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_RecreateChnNameOverlayRgn(sint32 s32Chn, SV_RECT_S stRgnRect)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_Attr_t stRgnAttr = {0};
    MI_RGN_ChnPortParam_t stChnAttr = {0};

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4
        || 0 == stRgnRect.u32Width || stRgnRect.u32Width > 2047
        || 0 == stRgnRect.u32Height || stRgnRect.u32Height >2047)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x] [handle:%d PortId:%d]\n", s32Ret, u32Handle, stChn.s32OutputPortId);
	}
    	
    s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = (stRgnRect.u32Width + 1) & 0xfffffffe;
    stRgnAttr.stOsdInitParam.stSize.u32Height = (stRgnRect.u32Height + 1) & 0xfffffffe;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow;
	stChnAttr.stPoint.u32X = stRgnRect.s32X;
    stChnAttr.stPoint.u32Y = stRgnRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_CHNNAME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 255;
    }
    else
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    }
    print_level(SV_DEBUG, "handle:%d, plate(%d,%d)\n", u32Handle, stRgnRect.s32X, stRgnRect.s32Y);
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect = stRgnRect;

    return SV_SUCCESS;
}


sint32 mpp_vosd_RecreateChnName2OverlayRgn(sint32 s32Chn, SV_RECT_S stRgnRect)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_Attr_t stRgnAttr = {0};
    MI_RGN_ChnPortParam_t stChnAttr = {0};

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4
        || 0 == stRgnRect.u32Width || stRgnRect.u32Width > 2047
        || 0 == stRgnRect.u32Height || stRgnRect.u32Height >2047)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.u32Handle;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x] [handle:%d PortId:%d]\n", s32Ret, u32Handle, stChn.s32OutputPortId);
	}
    	
    s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = (stRgnRect.u32Width + 1) & 0xfffffffe;
    stRgnAttr.stOsdInitParam.stSize.u32Height = (stRgnRect.u32Height + 1) & 0xfffffffe;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bShow;
	stChnAttr.stPoint.u32X = stRgnRect.s32X;
    stChnAttr.stPoint.u32Y = stRgnRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_CHNNAME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    print_level(SV_DEBUG, "handle:%d, plate(%d,%d)\n", u32Handle, stRgnRect.s32X, stRgnRect.s32Y);
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.stRect = stRgnRect;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新创建通道的状态信息视频叠加区域
 * 输入参数: stRgnRect --- 视频叠加区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_RecreateStateOverlayRgn(sint32 s32Chn, SV_RECT_S stRgnRect)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_Attr_t stRgnAttr = {0};
    MI_RGN_ChnPortParam_t stChnAttr = {0};

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4
        || 0 == stRgnRect.u32Width || stRgnRect.u32Width > 2047
        || 0 == stRgnRect.u32Height || stRgnRect.u32Height >2047)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x] [handle:%d PortId:%d]\n", s32Ret, u32Handle, stChn.s32OutputPortId);
	}
    	
    s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

    stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = (stRgnRect.u32Width + 1) & 0xfffffffe;
    stRgnAttr.stOsdInitParam.stSize.u32Height = (stRgnRect.u32Height + 1) & 0xfffffffe;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow;
	stChnAttr.stPoint.u32X = stRgnRect.s32X;
    stChnAttr.stPoint.u32Y = stRgnRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_BATTERY;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    //print_level(SV_DEBUG, "handle:%d, plate(%d,%d)\n", u32Handle, stRgnRect.s32X, stRgnRect.s32Y);
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.stRect = stRgnRect;

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 重新创建通道的摄像头标识视频叠加区域
 * 输入参数: stRgnRect --- 视频叠加区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_RecreateCameraOverlayRgn(sint32 s32Chn, SV_RECT_S stRgnRect)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_Attr_t stRgnAttr = {0};
    MI_RGN_ChnPortParam_t stChnAttr = {0};

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4
        || 0 == stRgnRect.u32Width || stRgnRect.u32Width > 2047
        || 0 == stRgnRect.u32Height || stRgnRect.u32Height >2047)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;
    
    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}
    	
    s32Ret = MI_RGN_Destroy(u32Handle);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	stRgnAttr.eType = E_MI_RGN_TYPE_OSD;
    stRgnAttr.stOsdInitParam.ePixelFmt        = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
    stRgnAttr.stOsdInitParam.stSize.u32Width  = (stRgnRect.u32Width + 1) & 0xfffffffe;
    stRgnAttr.stOsdInitParam.stSize.u32Height = (stRgnRect.u32Height + 1) & 0xfffffffe;
	s32Ret = MI_RGN_Create(u32Handle, &stRgnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bShow;
	stChnAttr.stPoint.u32X = stRgnRect.s32X;
    stChnAttr.stPoint.u32Y = stRgnRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.u32Layer = MPP_OVERLAY_CHNNAME;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    print_level(SV_DEBUG, "handle:%d, plate(%d,%d)\n", u32Handle, stRgnRect.s32X, stRgnRect.s32Y);
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.stRect = stRgnRect;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 显示通道的遮挡区域 (默认创建后为隐藏)
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ShowCoverRgn(sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 隐藏通道的遮挡区域 (默认创建后为隐藏)
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_HideCoverRgn(sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道的遮挡区域属性
 * 输入参数: s32Chn --- 通道号
             pstCoverAttr --- 画面遮挡区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ChnCoverAttrSet(sint32 s32Chn, VOSD_COVER_S *pstCoverAttr)
{
	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取通道的遮挡区域属性
 * 输入参数: s32Chn --- 通道号
             pstCoverAttr --- 画面遮挡区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ChnCoverAttrGet(sint32 s32Chn, VOSD_COVER_S *pstCoverAttr)
{
	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道的视频叠加属性
 * 输入参数: s32Chn --- Vi输入通道
             s32OsdChn --- OSD叠加通道
             pstOverlayAttr --- 视频叠加区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 视频画面叠加显示坐标值必需为偶数且X坐标为8的倍数
 *****************************************************************************/
sint32 mpp_vosd_ChnOverlayAttrSet(sint32 s32Chn, sint32 s32OsdChn, VOSD_OVERLAY_S *pstOverlayAttr)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_ChnPortParam_t stChnAttr;
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    char szFilePath[64];
    char *pszSize = "2x";

    if (s32OsdChn < 0 || s32OsdChn >= VIODE_MAX_CHN * 4 || pstOverlayAttr->enOsdPosition >= MPP_VOSD_POS_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstOverlayAttr)
    {
        return ERR_NULL_PTR;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32OsdChn;
    
    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32OsdChn];
  
    m_stVosdInfo.enOsdPosition = pstOverlayAttr->enOsdPosition;

#if (BOARD == BOARD_IPCR20S3)
    m_stVosdInfo.astChnOverStyle[s32OsdChn].bUpdate = SV_TRUE;
#endif
    
    if (pstChnOverlay->stTimeRgn.bShow != pstOverlayAttr->bShowTime)
    {
        u32Handle = pstChnOverlay->stTimeRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowTime;
        s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	pstChnOverlay->stTimeRgn.bShow = pstOverlayAttr->bShowTime;
    }

    if (pstChnOverlay->stChnNameRgn.bShow != pstOverlayAttr->bShowChnName)
    {
        u32Handle = pstChnOverlay->stChnNameRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowChnName;
        if (pstOverlayAttr->bShowChnName)
        {
            switch (m_stVosdInfo.astChnOverStyle[s32OsdChn].nCharSize)
            {
                case MPP_VOSD_SIZE_1X:
                    if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_1X : MPP_VOSD_TIME_Y_1X;
                    }
                    else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowState ? MPP_VOSD_CHNNAME_LOW_Y_1X : MPP_VOSD_STATE_LOW_Y_1X;
                    }
                    else
                    {
                        stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_1X;
                    }
                    break;
                case MPP_VOSD_SIZE_2X:
                    if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_2X : MPP_VOSD_TIME_Y_2X;
                    }
                    else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowState ? MPP_VOSD_CHNNAME_LOW_Y_2X : MPP_VOSD_STATE_LOW_Y_2X;
                    }
                    else
                    {
                        stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_2X;
                    }
                    break;
                case MPP_VOSD_SIZE_3X:
                    if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_3X : MPP_VOSD_TIME_Y_3X;
                    }
                    else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowState ? MPP_VOSD_CHNNAME_LOW_Y_3X : MPP_VOSD_STATE_LOW_Y_3X;
                    }
                    else
                    {
                        stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_3X;
                    }
                    break;
                case MPP_VOSD_SIZE_4X:
                    if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_4X : MPP_VOSD_TIME_Y_4X;
                    }
                    else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                    {
                        stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowState ? MPP_VOSD_CHNNAME_LOW_Y_4X : MPP_VOSD_STATE_LOW_Y_4X;
                    }
                    else
                    {
                        stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_4X;
                    }
                    break;
                default:
                    return ERR_ILLEGAL_PARAM;
            }
        }
        s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	pstChnOverlay->stChnNameRgn.bShow = pstOverlayAttr->bShowChnName;
    }

#if (BOARD == BOARD_IPCR20S3)
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {
        if (pstChnOverlay->stRoIconRgn.bShow != pstOverlayAttr->bShowRoIcon)
        {
            u32Handle = pstChnOverlay->stRoIconRgn.u32Handle;
            s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
            if(MI_SUCCESS != s32Ret)
        	{
        		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
        		return SV_FAILURE;
        	}
        	
            stChnAttr.bShow = pstOverlayAttr->bShowRoIcon;
            s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
            if(MI_SUCCESS != s32Ret)
        	{
        		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
        		return SV_FAILURE;
        	}
        	
        	pstChnOverlay->stRoIconRgn.bShow = pstOverlayAttr->bShowRoIcon;
        }
    }
    else if (BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        if (pstChnOverlay->stStateRgn.bShow != pstOverlayAttr->bShowState)
        {
            u32Handle = pstChnOverlay->stStateRgn.u32Handle;
            s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
            if(MI_SUCCESS != s32Ret)
        	{
        		print_level(SV_ERROR, "MI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
        		return SV_FAILURE;
        	}
        	
            stChnAttr.bShow = pstOverlayAttr->bShowState;
            if (pstOverlayAttr->bShowState)
            {
                switch (m_stVosdInfo.astChnOverStyle[s32OsdChn].nCharSize)
                {
                    case MPP_VOSD_SIZE_1X:
                        if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                        {
                            if (pstOverlayAttr->bShowTime && pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_Y_1X;
                            }
                            else if (!pstOverlayAttr->bShowTime && !pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_1X;
                            }
                            else
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_CHNNAME_Y_1X;
                            }
                        }
                        else
                        {
                            stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_LOW_Y_1X;
                        }
                        break;
                    case MPP_VOSD_SIZE_2X:
                        if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                        {
                            if (pstOverlayAttr->bShowTime && pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_Y_2X;
                            }
                            else if (!pstOverlayAttr->bShowTime && !pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_2X;
                            }
                            else
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_CHNNAME_Y_2X;
                            }
                        }
                        else
                        {
                            stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_LOW_Y_2X;
                        }
                        break;
                    case MPP_VOSD_SIZE_3X:
                        if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                        {
                            if (pstOverlayAttr->bShowTime && pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_Y_3X;
                            }
                            else if (!pstOverlayAttr->bShowTime && !pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_3X;
                            }
                            else
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_CHNNAME_Y_3X;
                            }
                        }
                        else
                        {
                            stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_LOW_Y_3X;
                        }
                        break;
                    case MPP_VOSD_SIZE_4X:
                        if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                        {
                            if (pstOverlayAttr->bShowTime && pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_Y_4X;
                            }
                            else if (!pstOverlayAttr->bShowTime && !pstOverlayAttr->bShowChnName)
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_TIME_Y_4X;
                            }
                            else
                            {
                                stChnAttr.stPoint.u32Y = MPP_VOSD_CHNNAME_Y_4X;
                            }
                        }
                        else
                        {
                            stChnAttr.stPoint.u32Y = MPP_VOSD_STATE_LOW_Y_4X;
                        }
                        break;
                    default:
                        return ERR_ILLEGAL_PARAM;
                }
            }
            s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
            if(MI_SUCCESS != s32Ret)
        	{
        		print_level(SV_ERROR, "MI_RGN_SetDisplayAttr failed! [err=%#x]\n", s32Ret);
        		return SV_FAILURE;
        	}
        	
        	pstChnOverlay->stStateRgn.bShow = pstOverlayAttr->bShowState;
        }
    }
#endif

    pstChnOverlay->enTimeFmt = pstOverlayAttr->enTimeFmt;
    
#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    if (pstChnOverlay->stCameraRgn.bShow != pstOverlayAttr->bShowCamera)
    {
        u32Handle = pstChnOverlay->stCameraRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowCamera;
        if (pstOverlayAttr->bShowCamera)
        {
            switch (m_stVosdInfo.astChnOverStyle[s32OsdChn].nCharSize)
            {
                case MPP_VOSD_SIZE_1X:
                    stChnAttr.stPoint.u32Y = MPP_VOSD_CAMERA_Y_1X;
                    break;
                case MPP_VOSD_SIZE_2X:
                    stChnAttr.stPoint.u32Y = MPP_VOSD_CAMERA_Y_2X;
                    break;
                case MPP_VOSD_SIZE_3X:
                    stChnAttr.stPoint.u32Y = MPP_VOSD_CAMERA_Y_3X;
                    break;
                case MPP_VOSD_SIZE_4X:
                    stChnAttr.stPoint.u32Y = MPP_VOSD_CAMERA_Y_4X;
                    break;
                default:
                    return ERR_ILLEGAL_PARAM;
            }
        }
        s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	pstChnOverlay->stCameraRgn.bShow = pstOverlayAttr->bShowCamera;
    }


	if (pstChnOverlay->stChnName2Rgn.bShow != pstOverlayAttr->bShowChnName)
    {
        u32Handle = pstChnOverlay->stChnName2Rgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowChnName;
        if (pstOverlayAttr->bShowChnName)
        {
            switch (m_stVosdInfo.astChnOverStyle[s32OsdChn].nCharSize)
            {
                case MPP_VOSD_SIZE_1X:
                    stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME2_Y_1X : MPP_VOSD_CHNNAME_Y_1X;
                    break;
                case MPP_VOSD_SIZE_2X:
                    stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME2_Y_2X : MPP_VOSD_CHNNAME_Y_2X;
                    break;
                case MPP_VOSD_SIZE_3X:
                    stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME2_Y_3X : MPP_VOSD_CHNNAME_Y_3X;
                    break;
                case MPP_VOSD_SIZE_4X:
                    stChnAttr.stPoint.u32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME2_Y_4X : MPP_VOSD_CHNNAME_Y_4X;
                    break;
                default:
                    return ERR_ILLEGAL_PARAM;
            }
        }
        s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	pstChnOverlay->stChnName2Rgn.bShow = pstOverlayAttr->bShowChnName;
    }
#endif
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取通道的视频叠加属性
 * 输入参数: s32Chn --- Vi输入通道
             s32OsdChn --- OSD叠加通道
             pstOverlayAttr --- 视频叠加区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ChnOverlayAttrGet(sint32 s32Chn, sint32 s32OsdChn, VOSD_OVERLAY_S *pstOverlayAttr)
{
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    
    if (s32OsdChn < 0 || s32OsdChn >= VIODE_MAX_CHN * 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstOverlayAttr)
    {
        return ERR_NULL_PTR;
    }

    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32OsdChn];
    pstOverlayAttr->bShowTime = pstChnOverlay->stTimeRgn.bShow;
    pstOverlayAttr->bShowChnName = pstChnOverlay->stChnNameRgn.bShow;    
    pstOverlayAttr->bShowCamera = pstChnOverlay->stCameraRgn.bShow;
    pstOverlayAttr->enTimeFmt = pstChnOverlay->enTimeFmt;
    pstOverlayAttr->stTimePos.s32X = pstChnOverlay->stTimeRgn.stRect.s32X;
    pstOverlayAttr->stTimePos.s32Y = pstChnOverlay->stTimeRgn.stRect.s32Y;
    pstOverlayAttr->stChnNamePos.s32X = pstChnOverlay->stChnNameRgn.stRect.s32X;
    pstOverlayAttr->stChnNamePos.s32Y = pstChnOverlay->stChnNameRgn.stRect.s32Y;  
#if (BOARD == BOARD_WFTR20S3) 
	pstOverlayAttr->stChnName2Pos.s32X = pstChnOverlay->stChnName2Rgn.stRect.s32X;
    pstOverlayAttr->stChnName2Pos.s32Y = pstChnOverlay->stChnName2Rgn.stRect.s32Y;  
#endif
    pstOverlayAttr->stCameraPos.s32X = pstChnOverlay->stCameraRgn.stRect.s32X;
    pstOverlayAttr->stCameraPos.s32Y = pstChnOverlay->stCameraRgn.stRect.s32Y;
    pstOverlayAttr->bShowBattery = pstChnOverlay->stBatteryRgn.bShow;
    pstOverlayAttr->bShowSingnal = pstChnOverlay->stSingnalRgn.bShow;
    pstOverlayAttr->enBatteryStat = pstChnOverlay->enBatteryStat;
    pstOverlayAttr->enSingnalStat = pstChnOverlay->enSingnalStat;
#if (BOARD == BOARD_IPCR20S3)
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {        
        pstOverlayAttr->bShowRoIcon = pstChnOverlay->stRoIconRgn.bShow;
    }
    else if (BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        pstOverlayAttr->bShowState = pstChnOverlay->stStateRgn.bShow;
        pstOverlayAttr->stStatePos.s32X = pstChnOverlay->stStateRgn.stRect.s32X;
        pstOverlayAttr->stStatePos.s32Y = pstChnOverlay->stStateRgn.stRect.s32Y;
    }
#endif
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 显示通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 只对时间和车牌号区域有效
 *****************************************************************************/
sint32 mpp_vosd_ShowOverlayRgn(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_ChnPortParam_t stChnAttr;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    /* 时间区域 */
    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_TRUE;
    	s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow = SV_TRUE;
	}
    
	/* 通道名区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_TRUE;
    	s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
        m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow = SV_TRUE;
    }

#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    /* 摄像头标识区域 */
    if (m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_TRUE;
    	s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bShow = SV_TRUE;
	}
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 隐藏通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 只对时间和车牌号区域有效
 *****************************************************************************/
sint32 mpp_vosd_HideOverlayRgn(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_ChnPortParam_t stChnAttr;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    /* 时间区域 */
    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow = SV_FALSE;
	}

	/* 通道名区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow = SV_FALSE;
	}

#if (BOARD == BOARD_WFCR20S2) 
#if 0
	/* 电池电量区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bShow = SV_FALSE;
	}

	/* 信号值区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.u32Handle;
        s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bShow = SV_FALSE;
	}
#endif
#endif
	
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解除OSD叠加通道与编码通道的绑定关系
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 在销毁编码通道前应该先调该接口解绑
 *****************************************************************************/
sint32 mpp_vosd_OverlayRgnDetach(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    
    if (s32Chn < 0 || s32Chn >= m_stVosdInfo.u32ChnNum)
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            s32Chn = s32Chn;
            break;

        case STREAM_TYPE_SEC:
            s32Chn = m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        case STREAM_TYPE_SNAP0:
            s32Chn = 2 * m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;
    
    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}

	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}

#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle;
    s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}
#elif (BOARD == BOARD_IPCR20S3)
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.u32Handle;
        s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
    	}
    }
    else if (BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle;
        s32Ret = MI_RGN_DetachFromChn(u32Handle, &stChn);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
    	}
    }
#endif	

	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 绑定OSD叠加通道与编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 解绑重新创建完编码通道号需调该接口重新绑定
 *****************************************************************************/
sint32 mpp_vosd_OverlayRgnAttach(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_ChnPortParam_t stChnAttr = {0};
    MPP_VOSD_RGN_S *pstVosdRgn = NULL;
    
    if (s32Chn < 0 || s32Chn >= m_stVosdInfo.u32ChnNum)
    {
        return ERR_ILLEGAL_PARAM;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            s32Chn = s32Chn;
            break;

        case STREAM_TYPE_SEC:
            s32Chn = m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        case STREAM_TYPE_SNAP0:
            s32Chn = 2 * m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    stChn.eModId  = E_MI_RGN_MODID_VPE;
    stChn.s32DevId = 0;
    stChn.s32ChnId = 0;
    stChn.s32OutputPortId = s32Chn;

    pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn;
    u32Handle = pstVosdRgn->u32Handle;
    stChnAttr.bShow  = pstVosdRgn->bShow;
    stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_TIME;
    stChnAttr.stPoint.u32X = pstVosdRgn->stRect.s32X;
    stChnAttr.stPoint.u32Y = pstVosdRgn->stRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 255;
    }
    else
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    }
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
	}

	pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn;
    u32Handle = pstVosdRgn->u32Handle;
    stChnAttr.bShow  = pstVosdRgn->bShow;
    stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_CHNNAME;
    stChnAttr.stPoint.u32X = pstVosdRgn->stRect.s32X;
    stChnAttr.stPoint.u32Y = pstVosdRgn->stRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 0;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 255;
    }
    else
    {
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    }
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
	}

#if (BOARD == BOARD_WFTR20S3 || BOARD == BOARD_IPTR20S1)
    pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn;
    u32Handle = pstVosdRgn->u32Handle;
    stChnAttr.bShow  = pstVosdRgn->bShow;
    stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_TIME;
    stChnAttr.stPoint.u32X = pstVosdRgn->stRect.s32X;
    stChnAttr.stPoint.u32Y = pstVosdRgn->stRect.s32Y;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
    stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 128;
    s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(MI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
	}
#elif (BOARD == BOARD_IPCR20S3)
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    {
        pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn;
        u32Handle = pstVosdRgn->u32Handle;
        stChnAttr.bShow  = pstVosdRgn->bShow;
        stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_BATTERY;
        stChnAttr.stPoint.u32X = pstVosdRgn->stRect.s32X;
        stChnAttr.stPoint.u32Y = pstVosdRgn->stRect.s32Y;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 0;
        s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
    	}
    }
    else if(BOARD_IsCustomer(BOARD_C_IPCR20S3_CREARE))
    {
        pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn;
        u32Handle = pstVosdRgn->u32Handle;
        stChnAttr.bShow  = pstVosdRgn->bShow;
        stChnAttr.unPara.stOsdChnPort.u32Layer     = MPP_OVERLAY_BATTERY;
        stChnAttr.stPoint.u32X = pstVosdRgn->stRect.s32X;
        stChnAttr.stPoint.u32Y = pstVosdRgn->stRect.s32Y;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.eAlphaMode = E_MI_RGN_PIXEL_ALPHA;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8BgAlpha = 128;
        stChnAttr.unPara.stOsdChnPort.stOsdAlphaAttr.stAlphaPara.stArgb1555Alpha.u8FgAlpha = 0;
        s32Ret = MI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
        if(MI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "MI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
    	}
    }
#endif

	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查更新叠加区域
 * 输入参数: szTimeStr --- 时间字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateOverRgn()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32PicNum = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    MPP_VOSD_SIZE_E enCharSize;
    SV_RECT_S stTimeRect = {0};
    SV_RECT_S stChnNameRect = {0};
	SV_RECT_S stChnName2Rect = {0};
    SV_RECT_S stBatteryRect = {0};
    SV_RECT_S stSingnalRect = {0};
    SV_RECT_S stCameraRect = {0};
    SV_RECT_S stRoIconRect = {0};
    SV_RECT_S stStateRect = {0};
    uint32 u32MaxX = 0;
    uint32 u32Space = 0;

    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i;
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated || 
            !m_stVosdInfo.astChnOverStyle[s32Chn].bUpdate)
        {
            continue;
        }

        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                u32MaxX = MPP_VOSD_MAX_X_1X;
                u32Space = MPP_VOSD_SPACE_1X;
                stTimeRect.s32X = MPP_VOSD_TIME_X_1X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_1X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_1X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_1X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_1X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_1X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_1X;
				stChnName2Rect.s32X = MPP_VOSD_CHNNAME_X_1X;
                stChnName2Rect.s32Y = MPP_VOSD_CHNNAME2_Y_1X;
                stChnName2Rect.u32Width = 80;
                stChnName2Rect.u32Height = MPP_VOSD_HEIGHT_1X;
                stCameraRect.s32X = MPP_VOSD_TIME_X_1X*2;
                stCameraRect.s32Y = MPP_VOSD_TIME_Y_1X*2;
                stCameraRect.u32Width = MPP_VOSD_TIMEWID_1X*2;
                stCameraRect.u32Height = MPP_VOSD_HEIGHT_1X*2;
                stRoIconRect.s32X = MPP_VOSD_ROICON_X_1X;
                stRoIconRect.s32Y = MPP_VOSD_ROICON_Y_1X;
                stRoIconRect.u32Width = MPP_VOSD_ROICON_W_1X;
                stRoIconRect.u32Height = MPP_VOSD_ROICON_H_1X;
                stStateRect.s32X = MPP_VOSD_STATE_X_1X;
                stStateRect.s32Y = MPP_VOSD_STATE_Y_1X;
                stStateRect.u32Width = 80;
                stStateRect.u32Height = MPP_VOSD_HEIGHT_1X;
                if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_TIME_LOW_Y_1X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_STATE_LOW_Y_1X;
                    }
                    else
                    {
                        stTimeRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_1X;
                    }
                    //stTimeRect.s32Y = MPP_VOSD_TIME_LOW_Y_1X;
                    stChnNameRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_1X;
                    stStateRect.s32Y = MPP_VOSD_STATE_LOW_Y_1X;
                }
                else if (MPP_VOSD_POS_UP_SEP == m_stVosdInfo.enOsdPosition)
                {
                    stTimeRect.s32X = MPP_VOSD_TIME_SEP_X_1X;
                    stTimeRect.s32Y = MPP_VOSD_TIME_Y_1X;
                    stChnNameRect.s32Y = MPP_VOSD_TIME_Y_1X;
                }                
                break;

            case MPP_VOSD_SIZE_2X:
                u32MaxX = MPP_VOSD_MAX_X_2X;
                u32Space = MPP_VOSD_SPACE_2X;
                stTimeRect.s32X = MPP_VOSD_TIME_X_2X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_2X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_2X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_2X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_2X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_2X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_2X;
				stChnName2Rect.s32X = MPP_VOSD_CHNNAME_X_2X;
                stChnName2Rect.s32Y = MPP_VOSD_CHNNAME2_Y_2X;
                stChnName2Rect.u32Width = 80;
                stChnName2Rect.u32Height = MPP_VOSD_HEIGHT_2X;
                stCameraRect.s32X = MPP_VOSD_TIME_X_2X*2;
                stCameraRect.s32Y = MPP_VOSD_TIME_Y_2X*2;
                stCameraRect.u32Width = MPP_VOSD_TIMEWID_2X*2;
                stCameraRect.u32Height = MPP_VOSD_HEIGHT_2X*2;
                stRoIconRect.s32X = MPP_VOSD_ROICON_X_2X;
                stRoIconRect.s32Y = MPP_VOSD_ROICON_Y_2X;
                stRoIconRect.u32Width = MPP_VOSD_ROICON_W_2X;
                stRoIconRect.u32Height = MPP_VOSD_ROICON_H_2X;
                stStateRect.s32X = MPP_VOSD_STATE_X_2X;
                stStateRect.s32Y = MPP_VOSD_STATE_Y_2X;
                stStateRect.u32Width = 80;
                stStateRect.u32Height = MPP_VOSD_HEIGHT_2X;
                if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_TIME_LOW_Y_2X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_STATE_LOW_Y_2X;
                    }
                    else
                    {
                        stTimeRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_2X;
                    }                
                    stChnNameRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_2X;
                    stStateRect.s32Y = MPP_VOSD_STATE_LOW_Y_2X;
                }
                else if (MPP_VOSD_POS_UP_SEP == m_stVosdInfo.enOsdPosition)
                {
                    stTimeRect.s32X = MPP_VOSD_TIME_SEP_X_2X;
                    stTimeRect.s32Y = MPP_VOSD_TIME_Y_2X;
                    stChnNameRect.s32Y = MPP_VOSD_TIME_Y_2X;
                }
                break;

            case MPP_VOSD_SIZE_3X:
                u32MaxX = MPP_VOSD_MAX_X_3X;
                u32Space = MPP_VOSD_SPACE_3X;
                stTimeRect.s32X = MPP_VOSD_TIME_X_3X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_3X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_3X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_3X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_3X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_3X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_3X;
				stChnName2Rect.s32X = MPP_VOSD_CHNNAME_X_3X;
                stChnName2Rect.s32Y = MPP_VOSD_CHNNAME2_Y_3X;
                stChnName2Rect.u32Width = 80;
                stChnName2Rect.u32Height = MPP_VOSD_HEIGHT_3X;
                stCameraRect.s32X = MPP_VOSD_TIME_X_3X*2;
                stCameraRect.s32Y = MPP_VOSD_TIME_Y_3X*2;
                stCameraRect.u32Width = MPP_VOSD_TIMEWID_3X*2;
                stCameraRect.u32Height = MPP_VOSD_HEIGHT_3X*2;
                stRoIconRect.s32X = MPP_VOSD_ROICON_X_3X;
                stRoIconRect.s32Y = MPP_VOSD_ROICON_Y_3X;
                stRoIconRect.u32Width = MPP_VOSD_ROICON_W_3X;
                stRoIconRect.u32Height = MPP_VOSD_ROICON_H_3X;
                stStateRect.s32X = MPP_VOSD_STATE_X_3X;
                stStateRect.s32Y = MPP_VOSD_STATE_Y_3X;
                stStateRect.u32Width = 80;
                stStateRect.u32Height = MPP_VOSD_HEIGHT_3X;
                if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_TIME_LOW_Y_3X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_STATE_LOW_Y_3X;
                    }
                    else
                    {
                        stTimeRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_3X;
                    }
                    stChnNameRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_3X;
                    stStateRect.s32Y = MPP_VOSD_STATE_LOW_Y_3X;
                }
                else if (MPP_VOSD_POS_UP_SEP == m_stVosdInfo.enOsdPosition)
                {
                    stTimeRect.s32X = MPP_VOSD_TIME_SEP_X_3X;
                    stTimeRect.s32Y = MPP_VOSD_TIME_Y_3X;
                    stChnNameRect.s32Y = MPP_VOSD_TIME_Y_3X;
                }
                break;

            case MPP_VOSD_SIZE_4X:
                u32MaxX = MPP_VOSD_MAX_X_4X;
                u32Space = MPP_VOSD_SPACE_4X;
                stTimeRect.s32X = MPP_VOSD_TIME_X_4X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_4X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_4X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_4X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_4X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_4X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_4X;
				stChnName2Rect.s32X = MPP_VOSD_CHNNAME_X_4X;
                stChnName2Rect.s32Y = MPP_VOSD_CHNNAME2_Y_4X;
                stChnName2Rect.u32Width = 80;
                stChnName2Rect.u32Height = MPP_VOSD_HEIGHT_4X;
                stCameraRect.s32X = MPP_VOSD_TIME_X_4X*2;
                stCameraRect.s32Y = MPP_VOSD_TIME_Y_4X*2;
                stCameraRect.u32Width = MPP_VOSD_TIMEWID_4X*2;
                stCameraRect.u32Height = MPP_VOSD_HEIGHT_4X*2;
                stRoIconRect.s32X = MPP_VOSD_ROICON_X_4X;
                stRoIconRect.s32Y = MPP_VOSD_ROICON_Y_4X;
                stRoIconRect.u32Width = MPP_VOSD_ROICON_W_4X;
                stRoIconRect.u32Height = MPP_VOSD_ROICON_H_4X;
                stStateRect.s32X = MPP_VOSD_STATE_X_4X;
                stStateRect.s32Y = MPP_VOSD_STATE_Y_4X;
                stStateRect.u32Width = 80;
                stStateRect.u32Height = MPP_VOSD_HEIGHT_4X;
                if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_TIME_LOW_Y_4X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stTimeRect.s32Y = MPP_VOSD_STATE_LOW_Y_4X;
                    }
                    else
                    {
                        stTimeRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_4X;
                    }
                    stChnNameRect.s32Y = MPP_VOSD_CHNNAME_LOW_Y_4X;
                    stStateRect.s32Y = MPP_VOSD_STATE_LOW_Y_4X;
                }
                else if (MPP_VOSD_POS_UP_SEP == m_stVosdInfo.enOsdPosition)
                {
                    stTimeRect.s32X = MPP_VOSD_TIME_SEP_X_4X;
                    stTimeRect.s32Y = MPP_VOSD_TIME_Y_4X;
                    stChnNameRect.s32Y = MPP_VOSD_TIME_Y_4X;
                }
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SZHQ))
        {
            stTimeRect.u32Width = stTimeRect.u32Width * 3 / 4;
            stTimeRect.u32Height = stTimeRect.u32Height * 3 / 4;
            stChnNameRect.u32Width = stChnNameRect.u32Width * 3 / 4;
            stChnNameRect.u32Height = stChnNameRect.u32Height * 3 / 4;
            if (MPP_VOSD_POS_UP_SEP == m_stVosdInfo.enOsdPosition)    // 因字符缩小需要右移至正常边缘间距
                stTimeRect.s32X = u32MaxX - u32Space - stTimeRect.u32Width;
        }
        
        s32Ret = mpp_vosd_RecreateOverlayRgn(s32Chn, stTimeRect, stChnNameRect, stChnName2Rect,stCameraRect, stRoIconRect, stStateRect);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_RecreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }    
/*
        #if (BOARD == BOARD_IPCR20S3)
        for(u32PicNum = 0; u32PicNum < ROTATING_ICON_BITMAP_NUM; u32PicNum++)
        {
            s32Ret = mpp_vosd_UpdateRoIconBmp(s32Chn, u32PicNum);
            if (MI_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vosd_UpdateRoIconBmp failed! s32Ret: 0x%x.\n", s32Ret);
                return s32Ret;
            }
        }
        #endif
*/
        m_stVosdInfo.astChnOverStyle[s32Chn].bUpdate = SV_FALSE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新时间位图
 * 输入参数: szTimeStr --- 时间字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateTimeBmp(char *pszTimeStr)
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    uint32 u32SizeIdx = 0;
    SV_SIZE_S *pstBmpSize = NULL;
    SV_SIZE_S astBmpSize[MPP_VOSD_SIZE_BUTT] = {0};
    SV_BOOL abUpSize[MPP_VOSD_SIZE_BUTT] = {SV_FALSE};
    uint32 u32Stride = 0;
    uint16 *pu16Buf = NULL;
    uint16 *apu16Buf[MPP_VOSD_SIZE_BUTT] = {m_stVosdInfo.stTimeBmp.pvBuf1, m_stVosdInfo.stTimeBmp.pvBuf2, \
                                            m_stVosdInfo.stTimeBmp.pvBuf3, m_stVosdInfo.stTimeBmp.pvBuf4};
    
    uint32 u32Handle = 0;
    uint32 u32StrLen = 0;
	MI_RGN_Bitmap_t stBitmap;

    if (NULL == pszTimeStr)
    {
        return ERR_NULL_PTR;
    }

    for (i = 0; i < MPP_VOSD_SIZE_BUTT; i++)
    {
        if (NULL == apu16Buf[i])
        {
            return ERR_NULL_PTR;
        }
    }

    u32StrLen = strlen(pszTimeStr);
    if (u32StrLen > MPP_VOSD_MAX_OVERSTR_LEN - 1)
    {
        return ERR_ILLEGAL_PARAM;
    }

    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i; 
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
        {
            continue;
        }

        u32SizeIdx = m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize;
        if (u32SizeIdx >= MPP_VOSD_SIZE_BUTT)
        {
            return ERR_ILLEGAL_PARAM;
        }

        abUpSize[u32SizeIdx] = SV_TRUE; 
    }

    for (i = MPP_VOSD_SIZE_1X; i < MPP_VOSD_SIZE_BUTT; i++)
    {
        if (abUpSize[i])
        {
            s32Ret = mpp_font_UpdateStrToARGB1555(pszTimeStr, i, apu16Buf[i], &astBmpSize[i], &u32Stride, m_stVosdInfo.bResetBuf);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_font_UpdateStrToARGB1555 failed! [err=%#x]\n", s32Ret);
            	return s32Ret;
            }
            m_stVosdInfo.bResetBuf = SV_FALSE;
        }
    }
   
    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i; 
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
        {
            continue;
        }

        u32SizeIdx = m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize;
        pu16Buf = apu16Buf[u32SizeIdx];
        pstBmpSize = &astBmpSize[u32SizeIdx];     
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        stBitmap.ePixelFormat = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stBitmap.stSize.u32Width = pstBmpSize->u32Width;
        stBitmap.stSize.u32Height = pstBmpSize->u32Height;
        stBitmap.pData = pu16Buf;
        s32Ret = MI_RGN_SetBitMap(u32Handle, &stBitmap);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_RGN_SetBitMap failed! [err=%#x]\n", s32Ret);
        }
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新通道名位图
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateChnNameBmp()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    MPP_VOSD_SIZE_E enCharSize;
    MPP_FONT_SCALE_E enScale;
    SV_SIZE_S stBmpSize = {0}; 
    SV_RECT_S stRgnRect = {0};
    uint32 u32Stride = 0;
    uint16 *pu16Buf = NULL;
    uint32 u32Handle = 0;
	char szTmpStr[128] = {0};
	uint32 u32FontCount = 0;
    uint32 szUnicode[128] = {0};
	MI_RGN_Bitmap_t stBitmap;

	if (!m_stVosdInfo.stChnName.bUpdate)
	{
	    return SV_SUCCESS;
	}

    pu16Buf = (uint16 *)malloc(MPP_VOSD_TEMPBMP_BUFSIZE);
    if (NULL == pu16Buf)
    {
        return ERR_NOMEM;
    }

	u32FontCount = mpp_utf8ToUnicode(m_stVosdInfo.stChnName.szOverStr,szUnicode);

    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i;
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated)
        {
            continue;
        }

        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                enScale = MPP_FONT_SCALE_1X;
                break;

            case MPP_VOSD_SIZE_2X:
                enScale = MPP_FONT_SCALE_2X;
                break;

            case MPP_VOSD_SIZE_3X:
                enScale = MPP_FONT_SCALE_3X;
                break;

            case MPP_VOSD_SIZE_4X:
                enScale = MPP_FONT_SCALE_4X;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }
		
        if(u32FontCount > MPP_VOSD_LINE_MAXCHAR)
            u32FontCount = MPP_VOSD_LINE_MAXCHAR;
		
		s32Ret = mpp_font_UnicodeToARGB1555(szUnicode, u32FontCount,enScale, pu16Buf, &stBmpSize, &u32Stride);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_font_StringToARGB1555 failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }
        
        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_1X;
                stRgnRect.s32X &= 0xfffffffe;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_1X : MPP_VOSD_TIME_Y_1X;
                }
                else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_1X : MPP_VOSD_STATE_LOW_Y_1X;
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_TIME_Y_1X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_2X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_2X;
                stRgnRect.s32X &= 0xfffffffe;
                //stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_2X : MPP_VOSD_TIME_Y_2X;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_2X : MPP_VOSD_TIME_Y_2X;
                }
                else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_2X : MPP_VOSD_STATE_LOW_Y_2X;
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_TIME_Y_2X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_3X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_3X;
                //stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_3X : MPP_VOSD_TIME_Y_3X;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_3X : MPP_VOSD_TIME_Y_3X;
                }
                else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_3X : MPP_VOSD_STATE_LOW_Y_3X;
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_TIME_Y_3X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_4X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_4X;
                //stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_4X : MPP_VOSD_TIME_Y_4X;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_4X : MPP_VOSD_TIME_Y_4X;
                }
                else if (MPP_VOSD_POS_DOWN == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_4X : MPP_VOSD_STATE_LOW_Y_4X;
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_TIME_Y_4X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        stRgnRect.u32Height = stBmpSize.u32Height;
        s32Ret = mpp_vosd_RecreateChnNameOverlayRgn(s32Chn, stRgnRect);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_RecreateChnNameOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }

        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        stBitmap.ePixelFormat = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stBitmap.stSize.u32Width = stBmpSize.u32Width;
        stBitmap.stSize.u32Height = stBmpSize.u32Height;
        stBitmap.pData = pu16Buf;
        s32Ret = MI_RGN_SetBitMap(u32Handle, &stBitmap);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_RGN_SetBitMap failed! [err=%#x]\n", s32Ret);
        }
    }

#if (BOARD != BOARD_WFTR20S3)	
    	m_stVosdInfo.stChnName.bUpdate = SV_FALSE;
#endif
    free(pu16Buf);

    return SV_SUCCESS;
}

sint32 mpp_vosd_UpdateChnName2Bmp()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    MPP_VOSD_SIZE_E enCharSize;
    MPP_FONT_SCALE_E enScale;
    SV_SIZE_S stBmpSize = {0}; 
    SV_RECT_S stRgnRect = {0};
    uint32 u32Stride = 0;
    uint16 *pu16Buf = NULL;
    uint32 u32Handle = 0;
	MI_RGN_Bitmap_t stBitmap;
    MI_RGN_ChnPort_t stChn;
    MI_RGN_ChnPortParam_t stChnAttr;
	uint32 u32CharCount;
	uint32 szUnicode[128];

	if (!m_stVosdInfo.stChnName.bUpdate)
	{
	    return SV_SUCCESS;
	}

    pu16Buf = (uint16 *)malloc(MPP_VOSD_TEMPBMP_BUFSIZE);
    if (NULL == pu16Buf)
    {
        return ERR_NOMEM;
    }

    u32CharCount = mpp_utf8ToUnicode(m_stVosdInfo.stChnName.szOverStr,szUnicode);

    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i;
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.bCreated)
        {
            continue;
        }
		
        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                enScale = MPP_FONT_SCALE_1X;
				
                break;

            case MPP_VOSD_SIZE_2X:
                enScale = MPP_FONT_SCALE_2X;
				
                break;

            case MPP_VOSD_SIZE_3X:
                enScale = MPP_FONT_SCALE_3X;
			
                break;

            case MPP_VOSD_SIZE_4X:
                enScale = MPP_FONT_SCALE_4X;
				
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

		stChn.eModId  = E_MI_RGN_MODID_VPE;
		stChn.s32DevId = 0;
		stChn.s32ChnId = 0;
		stChn.s32OutputPortId = s32Chn;
		u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.u32Handle;
		s32Ret = MI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
		if(MI_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
			return SV_FAILURE;
		}

		if(u32CharCount < MPP_VOSD_LINE_MAXCHAR)
		{
			stChnAttr.bShow = SV_FALSE;
		}
		else
		{
			stChnAttr.bShow = SV_TRUE;
		}
		
		s32Ret = MI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
		if(MI_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
			return SV_FAILURE;
		}

		if(u32CharCount < MPP_VOSD_LINE_MAXCHAR) 
		{
			continue;
		}
		
		s32Ret = mpp_font_UnicodeToARGB1555(szUnicode + MPP_VOSD_LINE_MAXCHAR , u32CharCount - MPP_VOSD_LINE_MAXCHAR ,enScale, pu16Buf, &stBmpSize, &u32Stride);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_font_StringToARGB1555 failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }
        
        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
			case MPP_VOSD_SIZE_1X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_1X;
                stRgnRect.s32X &= 0xfffffffe;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME2_Y_1X : MPP_VOSD_CHNNAME_Y_1X;
                }
                else
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_1X : MPP_VOSD_STATE_LOW_Y_1X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_2X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_2X;
                stRgnRect.s32X &= 0xfffffffe;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME2_Y_2X : MPP_VOSD_CHNNAME_Y_2X;
                }
                else
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_2X : MPP_VOSD_STATE_LOW_Y_2X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_3X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_3X;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME2_Y_3X : MPP_VOSD_CHNNAME_Y_3X;
                }
                else
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_3X : MPP_VOSD_STATE_LOW_Y_3X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_4X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_4X;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME2_Y_4X : MPP_VOSD_CHNNAME_Y_4X;
                }
                else
                {
                    stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bShow ? MPP_VOSD_CHNNAME_LOW_Y_4X : MPP_VOSD_STATE_LOW_Y_4X;
                }
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        stRgnRect.u32Height = stBmpSize.u32Height;
        s32Ret = mpp_vosd_RecreateChnName2OverlayRgn(s32Chn, stRgnRect);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_RecreateChnNameOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }

        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnName2Rgn.u32Handle;
        stBitmap.ePixelFormat = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stBitmap.stSize.u32Width = stBmpSize.u32Width;
        stBitmap.stSize.u32Height = stBmpSize.u32Height;
        stBitmap.pData = pu16Buf;
        s32Ret = MI_RGN_SetBitMap(u32Handle, &stBitmap);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_RGN_SetBitMap failed! [err=%#x]\n", s32Ret);
        }
    }
	
    m_stVosdInfo.stChnName.bUpdate = SV_FALSE;
    free(pu16Buf);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新状态信息位图
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateStateBmp()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    MPP_VOSD_SIZE_E enCharSize;
    MPP_FONT_SCALE_E enScale;
    SV_SIZE_S stBmpSize = {0}; 
    SV_RECT_S stRgnRect = {0};
    uint32 u32Stride = 0;
    uint16 *pu16Buf = NULL;
    uint32 u32Handle = 0;
	MI_RGN_Bitmap_t stBitmap;

	if (!m_stVosdInfo.stState.bUpdate)
	{
	    return SV_SUCCESS;
	}

    pu16Buf = (uint16 *)malloc(MPP_VOSD_STATE_BUFSIZE);
    if (NULL == pu16Buf)
    {
        return ERR_NOMEM;
    }

    print_level(SV_INFO, "OSD update State: %s\n", m_stVosdInfo.stState.szOverStr);
    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i;
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.bCreated)
        {
            continue;
        }

        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                enScale = MPP_FONT_SCALE_1X;
                break;

            case MPP_VOSD_SIZE_2X:
                enScale = MPP_FONT_SCALE_2X;
                break;

            case MPP_VOSD_SIZE_3X:
                enScale = MPP_FONT_SCALE_3X;
                break;

            case MPP_VOSD_SIZE_4X:
                enScale = MPP_FONT_SCALE_4X;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        s32Ret = mpp_font_StringToARGB1555(m_stVosdInfo.stState.szOverStr, enScale, pu16Buf, &stBmpSize, &u32Stride);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_font_StringToARGB1555 failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }
        
        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                stRgnRect.s32X = MPP_VOSD_STATE_X_1X;
                stRgnRect.s32X &= 0xfffffffe;

                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_STATE_Y_1X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_TIME_Y_1X;
                    }
                    else
                    {
                        stRgnRect.s32Y = MPP_VOSD_CHNNAME_Y_1X;
                    }
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_STATE_LOW_Y_1X;
                }
                break;

            case MPP_VOSD_SIZE_2X:
                stRgnRect.s32X = MPP_VOSD_STATE_X_2X;
                stRgnRect.s32X &= 0xfffffffe;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_STATE_Y_2X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_TIME_Y_2X;
                    }
                    else
                    {
                        stRgnRect.s32Y = MPP_VOSD_CHNNAME_Y_2X;
                    }
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_STATE_LOW_Y_2X;
                }
                break;

            case MPP_VOSD_SIZE_3X:
                stRgnRect.s32X = MPP_VOSD_STATE_X_3X;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_STATE_Y_3X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_TIME_Y_3X;
                    }
                    else
                    {
                        stRgnRect.s32Y = MPP_VOSD_CHNNAME_Y_3X;
                    }
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_STATE_LOW_Y_3X;
                }
                break;

            case MPP_VOSD_SIZE_4X:
                stRgnRect.s32X = MPP_VOSD_STATE_X_4X;
                if (MPP_VOSD_POS_UP == m_stVosdInfo.enOsdPosition)
                {
                    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_STATE_Y_4X;
                    }
                    else if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow && !m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow)
                    {
                        stRgnRect.s32Y = MPP_VOSD_TIME_Y_4X;
                    }
                    else
                    {
                        stRgnRect.s32Y = MPP_VOSD_CHNNAME_Y_4X;
                    }        
                }
                else
                {
                    stRgnRect.s32Y = MPP_VOSD_STATE_LOW_Y_4X;
                }
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        stRgnRect.u32Width = stBmpSize.u32Width;
        stRgnRect.u32Height = stBmpSize.u32Height;
        s32Ret = mpp_vosd_RecreateStateOverlayRgn(s32Chn, stRgnRect);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_RecreateStateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }

        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stStateRgn.u32Handle;
        stBitmap.ePixelFormat = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stBitmap.stSize.u32Width = stBmpSize.u32Width;
        stBitmap.stSize.u32Height = stBmpSize.u32Height;
        stBitmap.pData = pu16Buf;
        s32Ret = MI_RGN_SetBitMap(u32Handle, &stBitmap);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_SetBitMap failed! [err=%#x]\n", s32Ret);
        }
    }

    m_stVosdInfo.stState.bUpdate = SV_FALSE;
    free(pu16Buf);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 更新摄像头标识字符位图
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateCameraBmp()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    MPP_VOSD_SIZE_E enCharSize;
    MPP_FONT_SCALE_E enScale;
    SV_SIZE_S stBmpSize = {0}; 
    SV_RECT_S stRgnRect = {0};
    uint32 u32Stride = 0;
    uint16 *pu16Buf = NULL;
    uint32 u32Handle = 0;
	MI_RGN_Bitmap_t stBitmap;

	if (!m_stVosdInfo.stCameraStr.bUpdate)
	{
	    return SV_SUCCESS;
	}

    pu16Buf = (uint16 *)malloc(MPP_VOSD_TEMPBMP_BUFSIZE);
    if (NULL == pu16Buf)
    {
        return ERR_NOMEM;
    }

    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i;
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.bCreated)
        {
            continue;
        }

        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                enScale = MPP_FONT_SCALE_1X;
                break;

            case MPP_VOSD_SIZE_2X:
                enScale = MPP_FONT_SCALE_2X;
                break;

            case MPP_VOSD_SIZE_3X:
                enScale = MPP_FONT_SCALE_3X;
                break;

            case MPP_VOSD_SIZE_4X:
                enScale = MPP_FONT_SCALE_4X;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        s32Ret = mpp_font_StringToARGB1555(m_stVosdInfo.stCameraStr.szOverStr, enScale, pu16Buf, &stBmpSize, &u32Stride);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_font_StringToARGB1555 failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }

        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                stRgnRect.s32X = MPP_VOSD_CAMERA_X_1X;
                stRgnRect.s32X &= 0xfffffffe;
                stRgnRect.s32Y = MPP_VOSD_CAMERA_Y_1X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_2X:
                stRgnRect.s32X = MPP_VOSD_CAMERA_X_2X;
                stRgnRect.s32X &= 0xfffffffe;
                stRgnRect.s32Y = MPP_VOSD_CAMERA_Y_2X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_3X:
                stRgnRect.s32X = MPP_VOSD_CAMERA_X_3X;
                stRgnRect.s32Y = MPP_VOSD_CAMERA_Y_3X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_4X:
                stRgnRect.s32X = MPP_VOSD_CAMERA_X_4X;
                stRgnRect.s32Y = MPP_VOSD_CAMERA_Y_4X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        stRgnRect.u32Height = stBmpSize.u32Height;
        s32Ret = mpp_vosd_RecreateCameraOverlayRgn(s32Chn, stRgnRect);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_RecreateChnNameOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }

        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stCameraRgn.u32Handle;
        stBitmap.ePixelFormat = E_MI_RGN_PIXEL_FORMAT_ARGB1555;
        stBitmap.stSize.u32Width = stBmpSize.u32Width;
        stBitmap.stSize.u32Height = stBmpSize.u32Height;
        stBitmap.pData = pu16Buf;
        s32Ret = MI_RGN_SetBitMap(u32Handle, &stBitmap);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_RGN_SetBitMap failed! [err=%#x]\n", s32Ret);
        }
    }

    m_stVosdInfo.stCameraStr.bUpdate = SV_FALSE;
    free(pu16Buf);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 更新电池电量位图
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateBatteryBmp(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    MI_RGN_CanvasInfo_t stCanvasInfo = {0};
    OSD_SURFACE_S Surface = {0};
    char szFilePath[64];
    char *pszSize = "2x";

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 3)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32Chn];
    switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
    {
        case MPP_VOSD_SIZE_1X:
            pszSize = "1x";
            break;
        case MPP_VOSD_SIZE_2X:
            pszSize = "2x";
            break;
        case MPP_VOSD_SIZE_3X:
            pszSize = "3x";
            break;
        case MPP_VOSD_SIZE_4X:
            pszSize = "4x";
            break;
        default:
            return ERR_ILLEGAL_PARAM;
    }    

    print_level(SV_INFO, "enBatteryStat: %d\n", pstChnOverlay->enBatteryStat);
    switch (pstChnOverlay->enBatteryStat)
    {
        case BATTERY_CHARGEING:
            sprintf(szFilePath, "/root/bmp/bc_%s.bmp", pszSize);
            break;
        case BATTERY_CHARGE_FULL:
            sprintf(szFilePath, "/root/bmp/bcf_%s.bmp", pszSize);
            break;
        case BATTERY_EMPTY:
            sprintf(szFilePath, "/root/bmp/ble_%s.bmp", pszSize);
            break;
        case BATTERY_LEVEL1:
            sprintf(szFilePath, "/root/bmp/bl1_%s.bmp", pszSize);
            break;
        case BATTERY_LEVEL2:
            sprintf(szFilePath, "/root/bmp/bl2_%s.bmp", pszSize);
            break;
        case BATTERY_LEVEL3:
            sprintf(szFilePath, "/root/bmp/bl3_%s.bmp", pszSize);
            break;
        case BATTERY_LEVEL4:
            sprintf(szFilePath, "/root/bmp/bl4_%s.bmp", pszSize);
            break;
        default:
            print_level(SV_ERROR, "enBatteryStat is invalied!\n");
            return ERR_ILLEGAL_PARAM;
    }

    u32Handle = pstChnOverlay->stBatteryRgn.u32Handle;
    s32Ret = MI_RGN_GetCanvasInfo(u32Handle, &stCanvasInfo);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_GetCanvasInfo failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }

    Surface.enColorFmt = OSD_COLOR_FMT_RGB1555;
    CreateSurfaceByCanvas(szFilePath, &Surface, (uint8*)(stCanvasInfo.virtAddr), stCanvasInfo.stSize.u32Width, stCanvasInfo.stSize.u32Height, stCanvasInfo.u32Stride);

    s32Ret = MI_RGN_UpdateCanvas(u32Handle);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_UpdateCanvas failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新信号量位图
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateSingnalBmp(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    MI_RGN_CanvasInfo_t stCanvasInfo = {0};
    OSD_SURFACE_S Surface = {0};
    char szFilePath[64];
    char *pszSize = "2x";

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 3)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32Chn];
    switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
    {
        case MPP_VOSD_SIZE_1X:
            pszSize = "1x";
            break;
        case MPP_VOSD_SIZE_2X:
            pszSize = "2x";
            break;
        case MPP_VOSD_SIZE_3X:
            pszSize = "3x";
            break;
        case MPP_VOSD_SIZE_4X:
            pszSize = "4x";
            break;
        default:
            return ERR_ILLEGAL_PARAM;
    }    

    print_level(SV_INFO, "enSingnalStat: %d\n", pstChnOverlay->enSingnalStat);
    switch (pstChnOverlay->enSingnalStat)
    {
        case SINGNAL_LEVEL1:
            sprintf(szFilePath, "/root/bmp/wifi1_%s.bmp", pszSize);
            break;
        case SINGNAL_LEVEL2:
            sprintf(szFilePath, "/root/bmp/wifi2_%s.bmp", pszSize);
            break;
        case SINGNAL_LEVEL3:
            sprintf(szFilePath, "/root/bmp/wifi3_%s.bmp", pszSize);
            break;
        case SINGNAL_LEVEL4:
            sprintf(szFilePath, "/root/bmp/wifi4_%s.bmp", pszSize);
            break;
        default:
            print_level(SV_ERROR, "enBatteryStat is invalied!\n");
            return ERR_ILLEGAL_PARAM;
    }

    u32Handle = pstChnOverlay->stSingnalRgn.u32Handle;
    s32Ret = MI_RGN_GetCanvasInfo(u32Handle, &stCanvasInfo);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_GetCanvasInfo failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }

    Surface.enColorFmt = OSD_COLOR_FMT_RGB1555;
    CreateSurfaceByCanvas(szFilePath, &Surface, (uint8*)(stCanvasInfo.virtAddr), stCanvasInfo.stSize.u32Width, stCanvasInfo.stSize.u32Height, stCanvasInfo.u32Stride);

    s32Ret = MI_RGN_UpdateCanvas(u32Handle);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_UpdateCanvas failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }
    
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 更新旋转图标位图
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateRoIconBmp(sint32 s32Chn, uint32 u32PicNum)
{
    //if(BOARD_IsCustomer(BOARD_C_IPCR20S3_LUIS))
    //{
        sint32 s32Ret = 0;
        uint32 u32Handle = 0, i;
        MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
        MI_RGN_CanvasInfo_t stCanvasInfo = {0};
        OSD_SURFACE_S Surface = {0};
        char szFilePath[64];

        
        if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 3)
        {
            return ERR_ILLEGAL_PARAM;
        }

        if (!m_stVosdInfo.astChnOverlay[s32Chn].stRoIconRgn.bShow)
    	{
    	    return SV_SUCCESS;
    	}

        pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32Chn];

        u32Handle = pstChnOverlay->stRoIconRgn.u32Handle;
        s32Ret = MI_RGN_GetCanvasInfo(u32Handle, &stCanvasInfo);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_GetCanvasInfo failed! s32Ret: 0x%x.\n", s32Ret);
            return s32Ret;
        }

        //sprintf(szFilePath, VOSD_BMP_ICON4, u32PicNum);
        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                sprintf(szFilePath, VOSD_BMP_ICON1, u32PicNum);
                break;
            case MPP_VOSD_SIZE_2X:
                sprintf(szFilePath, VOSD_BMP_ICON2, u32PicNum);
                break;
            case MPP_VOSD_SIZE_3X:
                sprintf(szFilePath, VOSD_BMP_ICON3, u32PicNum);
                break;
            case MPP_VOSD_SIZE_4X:
                sprintf(szFilePath, VOSD_BMP_ICON4, u32PicNum);
                break;
            default:
                return ERR_ILLEGAL_PARAM;
        }
        
        Surface.enColorFmt = OSD_COLOR_FMT_RGB1555;
        
        CreateSurfaceByCanvas(szFilePath, &Surface, (uint8*)(stCanvasInfo.virtAddr), stCanvasInfo.stSize.u32Width, stCanvasInfo.stSize.u32Height, stCanvasInfo.u32Stride);

        s32Ret = MI_RGN_UpdateCanvas(u32Handle);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_RGN_UpdateCanvas failed! s32Ret: 0x%x.\n", s32Ret);
            return s32Ret;
        }
    //}

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 设置通道名名字
 * 输入参数: szChnName --- 通道名字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_SetChnName(sint32 s32Chn, char *szChnName)
{
    sint32 s32Ret = 0;
    
    if (NULL == szChnName)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = pthread_mutex_lock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    strcpy(m_stVosdInfo.stChnName.szOverStr, szChnName);
    m_stVosdInfo.stChnName.bUpdate = SV_TRUE;

    s32Ret = pthread_mutex_unlock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS; 
}

/******************************************************************************
 * 函数功能: 设置状态信息
 * 输入参数: szState --- 通道名字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_SetState(sint32 s32Chn, char *szState)
{
    sint32 s32Ret = 0;
    
    if (NULL == szState)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = pthread_mutex_lock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    strcpy(m_stVosdInfo.stState.szOverStr, szState);
    m_stVosdInfo.stState.bUpdate = SV_TRUE;

    s32Ret = pthread_mutex_unlock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    return SV_SUCCESS; 
}


/******************************************************************************
 * 函数功能: 设置通道字符叠加的样式
 * 输入参数: s32Chn --- Vi输入通道
             s32OsdChn --- OSD叠加通道
             enCharSize --- 通道叠加的字符大小尺寸
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_SetChnCharStyle(sint32 s32Chn, sint32 s32OsdChn, MPP_VOSD_SIZE_E enCharSize)
{
    sint32 s32Ret = 0;
    sint32 s32RealChn = 0;
    
    if (s32OsdChn < 0 || s32OsdChn >= VIODE_MAX_CHN * 3 || enCharSize >= MPP_VOSD_SIZE_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    //ssc335RGN叠加到vpe端口需转换
    switch(s32OsdChn)
    {
        case 0:
            s32RealChn = 1;
            break;
            
        case 1:
            s32RealChn = 2;
            break;
            
        case 2:
            s32RealChn = 0;    
            break;
    }

    s32Ret = pthread_mutex_lock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

//    print_level(SV_INFO, "set [CH%d] OSD CharSize: %dX\n", s32RealChn, enCharSize + 1);
    if (m_stVosdInfo.astChnOverStyle[s32RealChn].nCharSize != enCharSize)
    {
        m_stVosdInfo.astChnOverStyle[s32RealChn].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[s32RealChn].bUpdate = SV_TRUE;
        m_stVosdInfo.stChnName.bUpdate = SV_TRUE;
        m_stVosdInfo.stState.bUpdate = SV_TRUE;
        m_stVosdInfo.stCameraStr.bUpdate = SV_TRUE;
    }

    s32Ret = pthread_mutex_unlock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS; 
}

/******************************************************************************
 * 函数功能: 获取通道字符叠加的尺寸
 * 输入参数: s32Chn --- Vi输入通道
             s32OsdChn --- OSD叠加通道
 * 输出参数: penCharSize --- 通道叠加的字符大小尺寸
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_GetChnCharSize(sint32 s32Chn, sint32 s32OsdChn, MPP_VOSD_SIZE_E *penCharSize)
{
    sint32 s32RealChn = 0;
    
    if (s32OsdChn < 0 || s32OsdChn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == penCharSize)
    {
        return ERR_NULL_PTR;
    }

    //ssc335RGN叠加到vpe端口需转换
    switch(s32OsdChn)
    {
        case 0:
            s32RealChn = 1;
            break;
            
        case 1:
            s32RealChn = 2;
            break;
            
        case 2:
            s32RealChn = 0;    
            break;
    }

    *penCharSize = m_stVosdInfo.astChnOverStyle[s32RealChn].nCharSize;

    return SV_SUCCESS; 
}

sint32 mpp_vosd_FeedBsdOverlay(uint32 u32TimeMs)
{
    return SV_SUCCESS; 
}

