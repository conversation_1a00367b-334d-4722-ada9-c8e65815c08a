#include <stdlib.h>
#include "sv_bb_posalarm.h"
#include "sv_bb_mdvr.h"
#include "sv_bb_interface.h"
#include "sv_bb_header.h"
#include "utils.h"
#include "sv_bb_common.h"

SV_BB_POSALARM::SV_BB_POSALARM()
{
	m_u32Alarm = 0;
	m_u32State = 0;
	m_PosBase = (BB_POS_BASE_S *)malloc(sizeof(BB_POS_BASE_S));
	m_u8ClickReport = 0; //正常
	
	memset(m_PosBase, 0, sizeof(BB_POS_BASE_S));
}

SV_BB_POSALARM::~SV_BB_POSALARM()
{
	if(NULL != m_PosBase)
	{
		free(m_PosBase);
		m_PosBase = NULL;
	}
}

/* 从磁盘读取gps数据 */
int SV_BB_POSALARM::GetPosBaseFromDisk(BB_POS_BASE_S *posBase, sv_data_st *pGpsData, sint32 s32GpsState)
{
	if(SV_NULL == posBase)
	{
		return SV_FAILURE;
	}

	//posBase->AlarmFlag = GetPosAlarm();

	/* 这里可能要做下处理，有数据要一直读 */
	//if(SV_SUCCESS == sv_bb_get_gps_upload(&stGpsData))
	{
		if(1 == (s32GpsState>>1 & 0x01))
		{
			setbit(&posBase->state, BB_POS_STATE_00);			
		}

		sv_config_main_timezone_st stTz = { 0 };
		float fLat = 0.0f;
		float flon = 0.0f;
		float fLat_convert = 0.0f;
		float fLon_convert = 0.0f;			
		sscanf(pGpsData->lan, "%f", &fLat);
		sscanf(pGpsData->lon, "%f", &flon);

		/* 南纬 */
		if(fLat < 0.0f)
		{
			setbit(&posBase->state, BB_POS_STATE_02);						
		}
		
		/* 西经 */
		if(flon < 0.0f)
		{			
			setbit(&posBase->state, BB_POS_STATE_03);						
		}
		fLat_convert = fabs(fLat);
		fLon_convert = fabs(flon);

		posBase->lat = (u32)(fLat_convert * 1000000);
		posBase->lon = (u32)(fLon_convert * 1000000);		
		print_level(SV_INFO,"lan:%s lon:%s fLan:%f flon:%f lat:%u lon:%u time:%s fLat_convert:%f fLon_convert:%f \n",
			pGpsData->lan, pGpsData->lon, fLat, flon, posBase->lat, posBase->lon, pGpsData->time, fLat_convert, fLon_convert);
		struct tm stm;
		struct tm tm_stm_local;
		memset(&stm, 0, sizeof(struct tm));
		memset(&tm_stm_local, 0, sizeof(struct tm));

		int nRet = sscanf(pGpsData->time, "%4d-%02d-%02d %02d:%02d:%02d", &stm.tm_year,
										 &stm.tm_mon,
										 &stm.tm_mday,
										 &stm.tm_hour,
										 &stm.tm_min,
										 &stm.tm_sec);
		if(nRet != 6)
		{
			print_level(SV_ERROR,"prase gps time failed!");
			return SV_FAILURE;
		}
		print_level(SV_INFO,"year:%d mon:%d day:%d hour:%d min:%d sec:%d \n", stm.tm_year, stm.tm_mon, stm.tm_mday,
		stm.tm_hour, stm.tm_min, stm.tm_sec);
		stm.tm_year -= 1900;
		stm.tm_mon -= 1;

		#if 0
		//COMMON_UTC2Local
		sv_bb_get_timezone(&stTz);
		sint32 s32GpsUtcTime = mktime(&stm);
		sint32 s32GpsLocalTime = s32GpsUtcTime + (stTz.s8_hour * 3600);
		time_t_2_bcd(s32GpsLocalTime, posBase->bcdtime);
		
		print_level(SV_INFO,"stTz.s8_hour:%d stTz.u8_min:%u s32GpsUtcTime:%d s32GpsLocalTime:%d \n",
			stTz.s8_hour, stTz.u8_min, s32GpsUtcTime, s32GpsLocalTime);
		#endif

		COMMON_UTC2Local(&stm, &tm_stm_local);
		tm_stm_local.tm_year += 1900;
		tm_stm_local.tm_mon += 1;
		time_to_bcd(&tm_stm_local, posBase->bcdtime);
		print_level(SV_INFO,"local [%d/%d/%d:%d:%d:%d] bcd:[%x %x %x %x %x %x] alt:%u speed:%d course:%d \n", tm_stm_local.tm_year, tm_stm_local.tm_mon,
			tm_stm_local.tm_mday,tm_stm_local.tm_hour, tm_stm_local.tm_min, tm_stm_local.tm_sec, posBase->bcdtime[0], posBase->bcdtime[1],
			posBase->bcdtime[2], posBase->bcdtime[3], posBase->bcdtime[4], posBase->bcdtime[5],posBase->altitude ,pGpsData->speed,
			pGpsData->course);

		//posBase->altitude = s32GpsState>>2;		
		posBase->altitude = 0;//海拔写死成0
		posBase->speed = pGpsData->speed * 10;
		posBase->direction = pGpsData->course;
		setbit(&posBase->state, BB_POS_STATE_01);
		
		/* 使用gps卫星进行定位 */
		setbit(&posBase->state, BB_POS_STATE_18);
		
		/* 使用北斗卫星进行定位 */
		setbit(&posBase->state, BB_POS_STATE_19);
	}

	return SV_SUCCESS;
}


int SV_BB_POSALARM::GetPosBase(BB_POS_BASE_S *posBase)
{
	if(SV_NULL == posBase)
	{
		return SV_FAILURE;
	}
#if 1
	sv_gps_state_coord_st st_gps_state = { 0 };	
	sv_gps_state_speed_st st_gps_speed = { 0 };	
	sv_gps_state_time_st st_gps_time = { 0 };
	sv_state_main_mcu_acc_st st_acc = { 0 };
	//sv_config_main_timezone_st stTz = { 0 };	
	double dLat_convert = 0.0;
	double dLon_convert = 0.0;
	
	sv_bb_get_gps_coord(&st_gps_state);	
	sv_bb_get_gps_speed(&st_gps_speed);	
	sv_bb_get_gps_time(&st_gps_time);
	sv_bb_get_main_mcu_acc(&st_acc);	
	//sv_bb_get_timezone(&stTz);

	posBase->AlarmFlag = GetPosAlarm();	//需要补充
		
	if(SV_STATE_MCU_ACC_ON == st_acc.u8_acc)
	{
		setbit(&posBase->state, BB_POS_STATE_00);
	}
	print_level(SV_WARN,"GetPosBase gps state:%d speed actice:%d time active:%d GetPosBase acc:%d\n", 
		st_gps_state.e_state, st_gps_speed.b_active, st_gps_time.b_active,st_acc.u8_acc);

	if(GPS_STATE_CONNECTED == st_gps_state.e_state)
	{	
		dLat_convert = fabs(st_gps_state.d_latitude);
		dLon_convert = fabs(st_gps_state.d_longtitude);
		posBase->lat = (u32)(dLat_convert * 1000000);
		posBase->lon = (u32)(dLon_convert * 1000000);
		print_level(SV_INFO,"d_latitude:%f d_longtitude:%f dLat_convert:%f dLon_convert:%f lat:%u lon:%u speed:%u dir:%u \n",
			 st_gps_state.d_latitude, st_gps_state.d_longtitude, dLat_convert, dLon_convert, posBase->lat, posBase->lon,
			 posBase->speed, posBase->direction);

		 struct tm stTime = {0};
        struct timeval tvNow;
        struct timezone tz;
        gettimeofday(&tvNow, &tz);
        tvNow.tv_sec += (tz.tz_minuteswest * 60);
        gmtime_r((time_t *)&tvNow.tv_sec, &stTime);
			
		struct tm stm;		
		struct tm tm_stm_local;
		memset(&stm, 0, sizeof(struct tm));
		stm.tm_year = stTime.tm_year;//st_gps_time.s32_year - 1900;
		stm.tm_mon =  stTime.tm_mon;//st_gps_time.s32_month -1;
		stm.tm_mday = stTime.tm_mday;//st_gps_time.s32_day;
		stm.tm_hour = stTime.tm_hour;//st_gps_time.s32_hour;
		stm.tm_min = stTime.tm_min;///st_gps_time.s32_min;
		stm.tm_sec = stTime.tm_sec;//st_gps_time.s32_sec;

		COMMON_UTC2Local(&stm, &tm_stm_local);
		tm_stm_local.tm_year += 1900;
		tm_stm_local.tm_mon += 1;
		time_to_bcd(&tm_stm_local, posBase->bcdtime);
		
		//posBase->altitude = st_gps_speed.d_altitude;		
		posBase->altitude = 0; //海拔写死成0
		posBase->speed = st_gps_speed.d_speed * 10;
		posBase->direction = st_gps_speed.d_course;
		setbit(&posBase->state, BB_POS_STATE_01);

		print_level(SV_DEBUG,"######## lat:%f lon:%f altitude:%f speed:%f direction:%f [%u]:[%u]:[%u]:[%u]:[%u]:[%u] \n",
			st_gps_state.d_latitude, st_gps_state.d_longtitude, st_gps_speed.d_altitude,
			st_gps_speed.d_speed, st_gps_speed.d_course, stTime.tm_year+1900, stTime.tm_mon+1,
			stTime.tm_mday,stTime.tm_hour, stTime.tm_min, stTime.tm_sec);

		
		if(st_gps_state.d_latitude < 0)
		{
			setbit(&posBase->state, BB_POS_STATE_02);
		}

		if(st_gps_state.d_longtitude < 0)
		{
			setbit(&posBase->state, BB_POS_STATE_03);
		}

		/* 使用gps卫星进行定位 */
		setbit(&posBase->state, BB_POS_STATE_18);
		
		/* 使用北斗卫星进行定位 */
		setbit(&posBase->state, BB_POS_STATE_19);
	}
	else
	{
		time_t timeNow = time(NULL);
		time_t_2_bcd(timeNow, posBase->bcdtime);
	}
#endif

	return SV_SUCCESS;
}


void SV_BB_POSALARM::SetBitPosAlarm(BB_POS_ALM_E ePosAlm)
{
	m_AlarmLock.lock();
	setbit(&m_u32Alarm, ePosAlm);
	m_AlarmLock.unlock();	
}

void SV_BB_POSALARM::ClrBitPosAlarm(BB_POS_ALM_E ePosAlm)
{
	m_AlarmLock.lock();
	clrbit(&m_u32Alarm, ePosAlm);
	m_AlarmLock.unlock();		
}

u32 SV_BB_POSALARM::GetPosAlarm()
{
	m_AlarmLock.lock();
	u32 u32PosAlarm = m_u32Alarm;
	m_AlarmLock.unlock();

	return u32PosAlarm;
}

void SV_BB_POSALARM::SetPosAlarm(u32 u32Alarm)
{
	m_AlarmLock.lock();
	m_u32Alarm = u32Alarm;
	m_AlarmLock.unlock();
}

u32 SV_BB_POSALARM::GetPosState()
{
	return m_u32State;
}

void SV_BB_POSALARM::SetClickReport(uint8 u8State)
{
	m_ClickReportLock.lock();
	m_u8ClickReport = u8State;
	m_ClickReportLock.unlock();
}

uint8 SV_BB_POSALARM::GetClickReport()
{
	m_ClickReportLock.lock();
	uint8 u8State = m_u8ClickReport;
	m_ClickReportLock.unlock();

	return u8State;
}

void SV_BB_POSALARM::PlatConfirmAlarm(u32 u32AlarmType)
{
	if(isset(&u32AlarmType, BB_POS_ALM_00))
	{
		ClrBitPosAlarm(BB_POS_ALM_00);
	}

	if(isset(&u32AlarmType, BB_POS_ALM_03))
	{
		ClrBitPosAlarm(BB_POS_ALM_03);		
	}

	if(isset(&u32AlarmType, BB_POS_ALM_04))
	{
		//收到平台应答，清掉一键报案状态
		SetClickReport(0);
	}

	if(isset(&u32AlarmType, BB_POS_ALM_20))
	{
		ClrBitPosAlarm(BB_POS_ALM_20);		
	}

	if(isset(&u32AlarmType, BB_POS_ALM_21))
	{
		ClrBitPosAlarm(BB_POS_ALM_21);		
	}

	if(isset(&u32AlarmType, BB_POS_ALM_27))
	{
		ClrBitPosAlarm(BB_POS_ALM_27);
	}
	
	if(isset(&u32AlarmType, BB_POS_ALM_28))
	{
		ClrBitPosAlarm(BB_POS_ALM_28);
	}

}

int SV_BB_POSALARM::AddPosExtData(OUT BB_POS_EXT_DATA_S extData[], int maxExtNum)
{
	int nExtNum = 0;
	u32 u32Tmp = 0;

	if(nExtNum < maxExtNum)
	{
		AddExtData(BB_POS_EXT_01, 0, 0, u32Tmp, 0, NULL , 0, &extData[nExtNum++]);
	}

	if(nExtNum < maxExtNum)
	{
		AddExtData(BB_POS_EXT_02, 0, 0, u32Tmp, 0, NULL, 0, &extData[nExtNum++]);
	}

	if(nExtNum < maxExtNum)
	{
		AddExtData(BB_POS_EXT_03, 0, 0, u32Tmp, 0, NULL, 0, &extData[nExtNum++]);
	}

	if(nExtNum < maxExtNum)
	{
		AddExtData(BB_POS_EXT_04, 0, 0, u32Tmp, 0, NULL, 0, &extData[nExtNum++]);
	}

	if(nExtNum < maxExtNum)
	{
		sv_state_media_hub_get_vi_mask_st stMask = { 0 }; //保留位，用于摄像头丢失报警，bit置位表示摄像头丢失
		if(SV_SUCCESS == sv_bb_get_media_hub(&stMask)) 
		{
			AddExtData(BB_POS_EXT_15, 0, 0, (u32)stMask.u16_mask, 
					0, NULL, 0, &extData[nExtNum++]);
		}
	}

	if(nExtNum < maxExtNum)
	{
		sv_cellular_state_connect_st st_connect = { 0 };
		if(SV_SUCCESS == sv_bb_get_cell_connect(&st_connect))
		{
			//print_level(SV_INFO,"cell rssi:%d \n", st_connect.s8_rssi);
			AddExtData(BB_POS_EXT_30, st_connect.s8_rssi, 0, 0, 0, NULL, 0, &extData[nExtNum++]);
		}
	}

	if(nExtNum < maxExtNum)
	{
		sv_gps_state_sate_st stGpsState = { 0 };
		if(SV_SUCCESS == sv_bb_get_gps_sate(&stGpsState))
		{
			AddExtData(BB_POS_EXT_31, (u8)stGpsState.s32_used, 0, 0, 0, NULL, 0, &extData[nExtNum++]);
		}
	}

	if(nExtNum < maxExtNum)
	{
		STYiliExtData stYiliExtData = { 0 };
		memset(&stYiliExtData, 0, sizeof(STYiliExtData));

		sv_state_mcu_cycle_upload_st stVol = { 0 };
		if(SV_SUCCESS == sv_bb_get_acc_vol(&stVol))
		{
			stYiliExtData.stVolData.u16NeedReport = 1;
			u16 u16Vol = stVol.f_cur_vol * 100;
			stYiliExtData.stVolData.stVolHead.u16length = 0x04;
			stYiliExtData.stVolData.stVolHead.u16Order = 0x08;
			
			stYiliExtData.stVolData.u8VolInteger = u16Vol/100;
			stYiliExtData.stVolData.u8VolDecimal = u16Vol%100;
			//print_level(SV_INFO,"u8VolInteger:%u u8VolDecimal:%u stVol.f_curVol:%f u16Vol:%u \n",
			//	stYiliExtData.stVolData.u8VolInteger, stYiliExtData.stVolData.u8VolDecimal,
			//	stVol.f_cur_vol, u16Vol);
		}

		stYiliExtData.stReportData.u16NeedReport = 1;
		stYiliExtData.stReportData.stReporHead.u16length = 0x03;
		stYiliExtData.stReportData.stReporHead.u16Order = 0x0A;
		stYiliExtData.stReportData.u8ReportState = m_u8ClickReport;
		
		AddExtData(BB_POS_EXT_EC, 0, 0, 0, 0, (u8 *)&stYiliExtData, sizeof(STYiliExtData), &extData[nExtNum++]);
	}

	return nExtNum;	
}


int SV_BB_POSALARM::AddExtData(BB_POS_EXT_ID ext_id, u8 ext_val1, u16 ext_val2, u32 ext_val3, 
					u64 ext_val4, u8 *ext_val5, int ext_val5_len, OUT BB_POS_EXT_DATA_S *pExtData)
{
	pExtData->ExtId = ext_id;
	switch(ext_id)
	{
		case BB_POS_EXT_01:			
		case BB_POS_EXT_25:
		case BB_POS_EXT_2B:
		case BB_POS_EXT_15:
		{
			pExtData->ExtDatalen = sizeof(u32);
			SETBE32(pExtData->ExtData, ext_val3);
		}
		break;
		case BB_POS_EXT_02:
		case BB_POS_EXT_03:
		case BB_POS_EXT_04:			
		case BB_POS_EXT_2A:
		{
			pExtData->ExtDatalen = sizeof(u16);
			SETBE16(pExtData->ExtData, ext_val2);
		}
		break;
		case BB_POS_EXT_11:
		case BB_POS_EXT_12:
		case BB_POS_EXT_13:
		{
			pExtData->ExtDatalen = ext_val5_len;
			memcpy(pExtData->ExtData, ext_val5, ext_val5_len);
		}
		break;
		case BB_POS_EXT_30:
		case BB_POS_EXT_31:
		{
			pExtData->ExtDatalen = sizeof(u8);
			SET8(pExtData->ExtData, ext_val1);
		}
		break;
		case BB_POS_EXT_EC:
		{
			pExtData->ExtDatalen = 0;
			STYiliExtData *pYiliExtDat = (STYiliExtData *)ext_val5;

			if(0 == pYiliExtDat->stObdData.u16NeedReport && 
				0 == pYiliExtDat->stVolData.u16NeedReport &&
				0 == pYiliExtDat->stReportData.u16NeedReport)
			{
				break;
			}

			if(1 == pYiliExtDat->stObdData.u16NeedReport)
			{
				/* 油量 */
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stObdData.stOilHead.u16length);
				pExtData->ExtDatalen += 2;
				
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stObdData.stOilHead.u16Order);
				pExtData->ExtDatalen += 2;
				
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stObdData.u16OilInteger);			
				pExtData->ExtDatalen += 2;
				
				SET8(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stObdData.u8OilDecimal);
				pExtData->ExtDatalen += 1;

				/* 转速 */
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stObdData.stRoateHead.u16length);
				pExtData->ExtDatalen += 2;
				
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stObdData.stRoateHead.u16Order);
				pExtData->ExtDatalen += 2;

				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stObdData.u16Rotating);
				pExtData->ExtDatalen += 2;				
			}

			if(1 == pYiliExtDat->stVolData.u16NeedReport)
			{
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stVolData.stVolHead.u16length);
				pExtData->ExtDatalen += 2;
				
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stVolData.stVolHead.u16Order);
				pExtData->ExtDatalen += 2;
				
				SET8(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stVolData.u8VolInteger);			
				pExtData->ExtDatalen += 1;
				
				SET8(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stVolData.u8VolDecimal);
				pExtData->ExtDatalen += 1;				
			}

			if(1 == pYiliExtDat->stReportData.u16NeedReport)
			{
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stReportData.stReporHead.u16length);
				pExtData->ExtDatalen += 2;
				
				SETBE16(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stReportData.stReporHead.u16Order);
				pExtData->ExtDatalen += 2;
				
				SET8(pExtData->ExtData + pExtData->ExtDatalen, pYiliExtDat->stReportData.u8ReportState);			
				pExtData->ExtDatalen += 1;				
			}
		}
		break;
		default:
		{
			print_level(SV_WARN,"no find Ext Id:0x%x \n", ext_id);
		}
		break;
	}

	return SV_SUCCESS;
}

int SV_BB_POSALARM::GpsDataHandle(char *gpsData, int gpsDatalen)
{
	return SV_SUCCESS;
}

