/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file:       mp4.cpp
author:     lyn
version:    1.0.0
date:       2023-12-06
function:   recorder mp4 format source file
notice:     none
*******************************************************************************/

#include <sys/stat.h>
#include "r_mp4.h"
#include "print.h"

namespace recorder
{
RMP4::RMP4()
{
    int i;
    memset(&stAVFormatCtx, 0, sizeof(stAVFormatCtx));
    memset(&stStreamVideo, 0, sizeof(stStreamVideo));
    memset(&stStreamAudio, 0, sizeof(stStreamAudio));
    memset(&stMp4Ctx, 0, sizeof(stMp4Ctx));
    memset(&stAvMetaData, 0, sizeof(stAvMetaData));
    memset(&stVCodeCtx, 0, sizeof(stVCodeCtx));
    memset(&stACodeCtx, 0, sizeof(stACodeCtx));
    memset(&elems, 0, sizeof(elems));
    memset(&stOutputFormat, 0, sizeof(stOutputFormat));
    memset(&astAvFormatInternal, 0, sizeof(astAvFormatInternal));
    memset(&stVCodeCtxDeprecated, 0 , sizeof(AVCodecContext));
    memset(&stACodeCtxDeprecated, 0 , sizeof(AVCodecContext));
    
    for(i=0; i<3; i++)
    {
        streams[i] = NULL;
        memset(&stMovTrack[i], 0 , sizeof(MOVTrack));
        memset(&stMovCodecPara[i], 0 , sizeof(AVCodecParameters));
    }

    pstFaacHandle               = 0;
    pu8PCMBuf                   = NULL;
    pu8AACBuf                   = NULL;
    u32HeadSize                 = 0;
    u32InputSamples             = 0;
    u32MaxOutputBytes           = 0;
    u32PCMBufSize               = 0;
    s32AACSize                  = 0;
    memset(au8FaacHead, 0, sizeof(au8FaacHead));

    s64CurPts                   = 0;
    s32Pos                      = 0;
}
RMP4::~RMP4()
{

}
/******************************************************************************
 * 函数功能: 初始化faac库
 * 输入参数: 无
 * 输出参数: m_stRecorderInfo.stFaacInfo 相关信息
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMP4::FaacInit(void)
{
    sint32 s32Ret = 0;
    uint32 u32SampleRate = 16000, u32AudioChnnel = 1, lu32InputSamples = 0, lu32MaxOutputBytes = 0, u32DecSpeInfSize = 0;
    uint32 lu32PCMBufSize = 0;
    uint32 u32PCMBitSize = 16;      // PCM数据单样本采样深度
    uint8 *lpu8PCMBuf = NULL;       // 输入pcm数据缓存
    uint8 *lpu8AACBuf = NULL;       // 输出aac数据缓存    
    uint8 *pu8FaacBuf = NULL;
    static faacEncHandle pstFaacEncHandle = {0};
    static faacEncConfigurationPtr pstFaacEncConfig = {0};

    pstFaacEncHandle = faacEncOpen(u32SampleRate, u32AudioChnnel, (unsigned long *)&lu32InputSamples, (unsigned long *)&lu32MaxOutputBytes);  
    if(NULL == pstFaacEncHandle)
    {
        print_level(SV_ERROR, "faacEncOpen error \n");
        return SV_FAILURE;
    }

    lu32PCMBufSize = lu32InputSamples * (u32PCMBitSize / 8);
    if (lu32PCMBufSize <= 0 || lu32PCMBufSize > 1024*512 
        || lu32MaxOutputBytes <=0 || lu32MaxOutputBytes > 512*1024)
    {
        print_level(SV_ERROR, "FaacInit size error [%d] [%d]\n",lu32PCMBufSize,lu32MaxOutputBytes);
        return ERR_ILLEGAL_PARAM;
    }
        
    lpu8PCMBuf = (uint8 *)malloc(lu32PCMBufSize);
    if (NULL == lpu8PCMBuf)
    {
        print_level(SV_ERROR, "malloc pu8PCMBuf failed.\n");
        return SV_FAILURE;
    }

    lpu8AACBuf = (uint8 *)malloc(lu32MaxOutputBytes);
    if (NULL == lpu8AACBuf)
    {
        free(lpu8PCMBuf);
        lpu8PCMBuf = NULL;
        print_level(SV_ERROR, "malloc pu8AACBuf failed.\n");
        return SV_FAILURE;
    }
    
    pstFaacEncConfig = faacEncGetCurrentConfiguration(pstFaacEncHandle); 
    pstFaacEncConfig->inputFormat   = FAAC_INPUT_16BIT;
    pstFaacEncConfig->outputFormat  = 0;
    pstFaacEncConfig->useTns        = 0;
    pstFaacEncConfig->useLfe        = 0;
    pstFaacEncConfig->allowMidside  = 0;
    pstFaacEncConfig->aacObjectType = LOW;
    pstFaacEncConfig->shortctl      = SHORTCTL_NORMAL;
    pstFaacEncConfig->quantqual     = 100;
    pstFaacEncConfig->bandWidth     = u32SampleRate >> 1;
    pstFaacEncConfig->bitRate       = 0;
    faacEncSetConfiguration(pstFaacEncHandle, pstFaacEncConfig); 
    s32Ret = faacEncGetDecoderSpecificInfo(pstFaacEncHandle, &pu8FaacBuf, (unsigned long *)&u32DecSpeInfSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "faacEncGetDecoderSpecificInfo error=%x \n", s32Ret);
        faacEncClose(pstFaacEncHandle);
        pstFaacEncHandle = NULL;
        free(lpu8PCMBuf);
        free(lpu8AACBuf);
        lpu8PCMBuf = NULL;
        lpu8AACBuf = NULL;
        return SV_FAILURE;
    }
    
    //print_level(SV_WARN, "u32DecSpeInfSize=%u u32MaxOutputBytes=%u u32SampleRate=%u u32InputSamples=%u u32PCMBufSize=%u\n",\
                            u32DecSpeInfSize,u32MaxOutputBytes,u32SampleRate,u32InputSamples,u32PCMBufSize);
    memcpy(this->au8FaacHead, pu8FaacBuf, u32DecSpeInfSize);
    this->u32HeadSize = u32DecSpeInfSize;
    this->pstFaacHandle = pstFaacEncHandle;
    this->u32InputSamples = lu32InputSamples;
    this->u32MaxOutputBytes = lu32MaxOutputBytes;
    this->u32PCMBufSize = lu32PCMBufSize;
    this->pu8AACBuf = lpu8AACBuf;
    this->pu8PCMBuf = lpu8PCMBuf;
    return SV_SUCCESS;
}

sint32 RMP4::FaacFini(void)
{
    if (NULL != pu8AACBuf)
    {
        free(pu8AACBuf);
        pu8AACBuf = NULL;
    }
    if (NULL != pu8PCMBuf)
    {
        free(pu8PCMBuf);
        pu8PCMBuf = NULL;
    }
    if (NULL != pstFaacHandle)
    {
        faacEncClose(pstFaacHandle);
    }

    return SV_SUCCESS;
}
sint32 RMP4::Open()
{
    sint32                          s32Ret = 0, i;
    static uint8_t                  h264_extradata[] = {0x00, 0x00, 0x00, 0x01, 0x67, 0x42, 0x00, 0x2a, 0x96, 0x35,\
                                                        0x40, 0xf0, 0x04, 0x4f, 0xcb, 0x37, 0x01, 0x01, 0x01, 0x02,\
                                                        0x00, 0x00, 0x00, 0x01, 0x68, 0xce, 0x31, 0xb2};

    if (stMediaCfg.u32Width < 352 || stMediaCfg.u32Width > 3840
        || stMediaCfg.u32Height < 240 || stMediaCfg.u32Height > 2160
        || stMediaCfg.u32Framerate < 2 || stMediaCfg.u32Framerate > 30
        || stMediaCfg.u32Bitrate < 64 || stMediaCfg.u32Bitrate > 12*1024)
    {
        print_level(SV_ERROR, "(%dx%d) framerate:%d, bitrate:%d\n", \
                    stMediaCfg.u32Width, stMediaCfg.u32Height, stMediaCfg.u32Framerate, stMediaCfg.u32Bitrate);
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = FaacInit();
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }
    elems.key     = "ISFT";
    elems.value = "Lavf58.20.100";
    memset(&stStreamVideo, 0, sizeof(AVStream));
    memset(&stStreamAudio, 0, sizeof(AVStream));
    memset(&stMp4Ctx, 0, sizeof(MOVMuxContext));
    memset(&stAvMetaData, 0, sizeof(AVDictionary));
    memset(&stVCodeCtx, 0, sizeof(AVCodecParameters));
    memset(&stACodeCtx, 0, sizeof(AVCodecParameters));
    memset(&stOutputFormat, 0, sizeof(AVOutputFormat));
    memset(&astAvFormatInternal, 0, sizeof(AVFormatInternal));

    for (i=0; i<3; i++)
    {
        memset(&stMovTrack[i], 0, sizeof(MOVTrack));
        memset(&stMovCodecPara[i], 0, sizeof(AVCodecParameters));
        stMovTrack[i].flags = MOV_TRACK_ENABLED;
    }
    streams[0]                                  = &stStreamVideo;
    streams[1]                                  = &stStreamAudio;
    stMp4Ctx.tracks                             = stMovTrack;
    (stMovTrack[0]).st                          = &stStreamVideo;
    (stMovTrack[1]).st                          = &stStreamAudio;
    (stMovTrack[0]).par                         = &stVCodeCtx;
    (stMovTrack[1]).par                         = &stACodeCtx;
    stAVFormatCtx.oformat                       = &stOutputFormat;
    stAVFormatCtx.internal                      = &astAvFormatInternal;
    
    //video
    stStreamVideo.nb_frames                     = 0;
    stStreamVideo.codecpar                      = &stVCodeCtx;
    stStreamVideo.id                            = 0;
    stStreamVideo.codecpar->codec_type          = AVMEDIA_TYPE_VIDEO;
    stStreamVideo.codecpar->codec_id            = (stMediaCfg.enEncodeType == ENCODE_H264) ? AV_CODEC_ID_H264 : AV_CODEC_ID_HEVC;
    stStreamVideo.codecpar->width               = stMediaCfg.u32Width;
    stStreamVideo.codecpar->height              = stMediaCfg.u32Height;
    stStreamVideo.codecpar->bit_rate            = stMediaCfg.u32Bitrate*1024;
    stStreamVideo.codecpar->frame_size          = 0;
    stStreamVideo.codecpar->sample_rate         = 0;
    stStreamVideo.codecpar->block_align         = 0;
    stStreamVideo.codecpar->extradata_size      = 0;
    stStreamVideo.codecpar->codec_tag           = (stMediaCfg.enEncodeType == ENCODE_H264) ? (('a') | (('v') << 8) | (('c') << 16) | ((unsigned)('1') << 24)):(('h') | (('e') << 8) | (('v') << 16) | ((unsigned)('1') << 24));
    stStreamVideo.codecpar->bits_per_coded_sample         = 0;
    stStreamVideo.time_base.num                 = 1;
    stStreamVideo.time_base.den                 = 1000000;
    stStreamVideo.metadata                      = NULL;
    stStreamVideo.sample_aspect_ratio.den       = 1;
    stStreamVideo.sample_aspect_ratio.num       = 0;
    stStreamVideo.codecpar->extradata           = h264_extradata;
    stStreamVideo.codecpar->extradata_size      = (stMediaCfg.enEncodeType == ENCODE_H264) ? sizeof(h264_extradata) : 0;
    stAvMetaData.count                          = 1;
    stAvMetaData.elems                          = &elems;
    stStreamVideo.codec                         = &stVCodeCtxDeprecated;
    /* Audio */
    stStreamAudio.nb_frames                     = 0;
    stStreamAudio.codecpar                      = &stACodeCtx;
    stStreamAudio.id                            = 1;
    stStreamAudio.codecpar->extradata_size      = (sint32) u32HeadSize;
    stStreamAudio.codecpar->extradata           = (uint8 *)au8FaacHead;
    stStreamAudio.codecpar->codec_id            = AV_CODEC_ID_AAC;
    stStreamAudio.codecpar->codec_type          = AVMEDIA_TYPE_AUDIO;
    stStreamAudio.codecpar->channels            = 1;
    stStreamAudio.codecpar->profile             = FF_PROFILE_AAC_LOW;
    stStreamAudio.codecpar->codec_tag           = (('m') | (('p') << 8) | (('4') << 16) | ((unsigned)('a') << 24));
    stStreamAudio.time_base.num                 = 1;
    stStreamAudio.codecpar->sample_rate         = 1000000;
    stStreamAudio.codecpar->channels            = 1;
    stStreamAudio.time_base.den                 = 1000000;
    stStreamAudio.codec                         = &stACodeCtxDeprecated;

    snprintf(stAVFormatCtx.filename, sizeof(stAVFormatCtx.filename), "%s", fileName);
    stAVFormatCtx.url = stAVFormatCtx.filename;
    if (avio_open(&stAVFormatCtx.pb, stAVFormatCtx.filename, AVIO_FLAG_READ_WRITE) < 0) 
    {
        print_level(SV_ERROR, "url_fopen file %s failed.\n", stAVFormatCtx.filename);
        return SV_FAILURE;
    }
    stMp4Ctx.mode = MODE_MP4;

#if (defined(BOARD_ADA47V1))
    stMp4Ctx.nb_streams = 2;
#elif (defined(BOARD_ADA32IR))
    stMp4Ctx.nb_streams = 1;
#else
    stMp4Ctx.nb_streams = (stMediaCfg.bAudioEnable) ? 2 : 1;
#endif

    stMp4Ctx.flags = FF_MOV_FLAG_EMPTY_MOOV | FF_MOV_FLAG_FRAGMENT | FF_MOV_FLAG_DEFAULT_BASE_MOOF | FF_MOV_FLAG_DASH;
    stMp4Ctx.iods_skip = SV_TRUE;
    
    stAVFormatCtx.priv_data           = &stMp4Ctx;
    stAVFormatCtx.start_time          = 0;
    stAVFormatCtx.streams             = streams;
    
#if (defined(BOARD_ADA47V1))
    stAVFormatCtx.nb_streams = 2;
#elif (defined(BOARD_ADA32IR))
    stAVFormatCtx.nb_streams = 1;
#else
    stAVFormatCtx.nb_streams = (stMediaCfg.bAudioEnable) ? 2 : 1;
#endif
    stAVFormatCtx.metadata            = &stAvMetaData;
    if (mov_write_header(&stAVFormatCtx) != 0) 
    {
        print_level(SV_ERROR, "mov_write_header failed.\n");
        avio_close(stAVFormatCtx.pb);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 RMP4::EncodeAAC(REC_D_PACK_S *pstPacket)
{

    sint32          s32Ret              = 0;
    char            u8AudioPCM[1024]    = {0};
    uint32          u32Size             = 640;

    G711A2PCM((pstPacket->data).get(), u8AudioPCM, pstPacket->size, 0);

    if(u32PCMBufSize - s32Pos > u32Size)
    {
        memcpy(pu8PCMBuf + s32Pos, u8AudioPCM, u32Size);
        s32Pos += u32Size;
        return SV_FAILURE;
    }               
    else
    {
        /* Buffer长度不足，存储此packet数据的一部分写满缓存 */
        memcpy(pu8PCMBuf + s32Pos, u8AudioPCM, u32PCMBufSize - s32Pos);
        s32Ret = faacEncEncode(pstFaacHandle, (int*)pu8PCMBuf, u32InputSamples, pu8AACBuf, u32MaxOutputBytes);
        if(s32Ret > 0)
        {
            s32AACSize = s32Ret;
        }
        else
        {
            s32AACSize = 0;
        }
        memcpy(pu8PCMBuf, u8AudioPCM + (u32PCMBufSize - s32Pos), u32Size - (u32PCMBufSize - s32Pos));
        s32Pos = u32Size - (u32PCMBufSize - s32Pos);
    }

    return SV_SUCCESS;
}

sint32 RMP4::Write(REC_D_PACK_S *pstPacket)
{
    sint32                  s32Ret = 0;
    sint32                  s32Stream = 0;
    AVPacket                stAvPkt = {0};
    static uint32           u32WriteCache = {0};
    //print_level(SV_DEBUG, "MP4 write type[%d] pts[%lld]\n",pstPacket->type, pstPacket->pts);
    if (NULL == pstPacket)
    {
        return ERR_NULL_PTR;
    }

    if (pstPacket->size > 512*1024 || pstPacket->type > 3)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (pstPacket->type != 0 && pstPacket->type != 1 && pstPacket->type != 2)
    {
        return SV_SUCCESS;
    }

    /* aac音频编码 */
    if (pstPacket->type == 2)
    {
        s32Ret = EncodeAAC(pstPacket);
        if (SV_SUCCESS != s32Ret)
        {
            return s32Ret;
        }
    }

    s64CurPts = pstPacket->pts;
    
    switch (pstPacket->type)
    {
        case 0:
        case 1:
            s32Stream = 0;
            break;
        case 2:
            s32Stream = 1;
            break;
        case 3:
            s32Stream = 2;
            break;
    }

    if (0 == stAVFormatCtx.streams[s32Stream]->nb_frames)
    {
        stAVFormatCtx.streams[s32Stream]->start_time = pstPacket->pts;
        if ((0 == s32Stream) || (1 == s32Stream))
        {
            stAVFormatCtx.start_time = pstPacket->pts;
            u32WriteCache = 0;
        }
    }

    if (1 == s32Stream)
    {
        u32WriteCache += s32AACSize;
    }
    else
    {
        u32WriteCache += pstPacket->size;
    }

    avio_flush(stAVFormatCtx.pb);

    stAVFormatCtx.streams[s32Stream]->nb_frames++;
    if (1 == s32Stream)
    {
        stAvPkt.data = pu8AACBuf;
        stAvPkt.size = s32AACSize;
    }
    else
    {
        stAvPkt.data        = (pstPacket->data).get();
        stAvPkt.size        = pstPacket->size;
    }

    stAvPkt.flags           = (pstPacket->type == 1 || pstPacket->type == 2) ? AV_PKT_FLAG_KEY : 0;
    stAvPkt.stream_index    = s32Stream;
    stAvPkt.pts             = pstPacket->pts - stAVFormatCtx.streams[s32Stream]->start_time;
    stAvPkt.dts             = stAvPkt.pts;

    s32Ret = mov_write_packet(&stAVFormatCtx, &stAvPkt);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "mov_write_packet failed. [err=%d]\n", s32Ret);
        return ERR_BADADDR;
    }

    return SV_SUCCESS;
}

sint32 RMP4::Close()
{
    sint32                  s32Ret          = 0;
    sint32                  s32Duration     = 0;
    sint32                  s32FileSize     = 0;
    struct stat             stStat          = {0};
    char                    szNewFilePath[256];
    char                    szDuration[16];
    char                    szFileSize[16];
    char                    *pszFileName = NULL;
    char                    *pszEventType = NULL;

    s32Ret = FaacFini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "FaacFini failed\n");
    }
    s32Duration = (s64CurPts - stAVFormatCtx.start_time + 500) / 1000;
    print_level(SV_DEBUG, "%lld -> %lld | %d\n", stAVFormatCtx.start_time, s64CurPts, s32Duration);
    
    s32Ret = mov_write_trailer(&stAVFormatCtx);
    if (s32Ret < 0) 
    {
        print_level(SV_ERROR, "mov_write_trailer failed. [ret=%d]\n", s32Ret);
    }

    s32Ret = avio_close(stAVFormatCtx.pb);
    if (s32Ret < 0) 
    {
        print_level(SV_ERROR, "url_fclose failed. [ret=%d]\n", s32Ret);
    }

    strcpy(szNewFilePath, stAVFormatCtx.filename);
    s32Ret = stat(stAVFormatCtx.filename, &stStat);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "stat file:%s failed.\n", stAVFormatCtx.filename);
    }
    else
    {
        s32FileSize = stStat.st_size;
    }

    pszFileName = strrchr(szNewFilePath, '/');
    if (NULL == pszFileName)
    {
        print_level(SV_ERROR, "strrchr failed.\n");
    }
    else
    {
        sprintf(szDuration, "%07d", s32Duration);
        sprintf(szFileSize, "%010d", s32FileSize);
        memcpy(&pszFileName[19], szDuration, 7);
        memcpy(&pszFileName[27], szFileSize, 10);
        if (isNeedRename)
        {
            pszEventType = TransNumber2EventType(eventType);
            memcpy(&pszFileName[16], pszEventType, 2);
        }
        print_level(SV_DEBUG, "rename: %s \n -> %s\n", stAVFormatCtx.filename, szNewFilePath);
        rename(stAVFormatCtx.filename, szNewFilePath);
    }
    
    fileDuration = s32Duration;
    fileSize = s32FileSize;
    memcpy(fileName, szNewFilePath, REC_FILE_NAME_LEN);
    

    return SV_SUCCESS;
}
sint32 RMP4::Close(SV_BOOL bUserDefine)
{
    sint32                  s32Ret          = 0;
    sint32                  s32Duration     = 0;
    sint32                  s32FileSize     = 0;
    struct stat             stStat          = {0};
    char                    szNewFilePath[256];
    char                    szDuration[16];
    char                    szFileSize[16];
    char                    *pszFileName = NULL;

    s32Ret = FaacFini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "FaacFini failed\n");
    }
    s32Duration = (s64CurPts - stAVFormatCtx.start_time + 500) / 1000;
    print_level(SV_DEBUG, "%lld -> %lld | %d\n", stAVFormatCtx.start_time, s64CurPts, s32Duration);
    
    s32Ret = mov_write_trailer(&stAVFormatCtx);
    if (s32Ret < 0) 
    {
        print_level(SV_ERROR, "mov_write_trailer failed. [ret=%d]\n", s32Ret);
    }

    s32Ret = avio_close(stAVFormatCtx.pb);
    if (s32Ret < 0) 
    {
        print_level(SV_ERROR, "url_fclose failed. [ret=%d]\n", s32Ret);
    }

    strcpy(szNewFilePath, stAVFormatCtx.filename);
    s32Ret = stat(stAVFormatCtx.filename, &stStat);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "stat file:%s failed.\n", stAVFormatCtx.filename);
    }
    else
    {
        s32FileSize = stStat.st_size;
    }
    
    fileDuration = s32Duration;
    fileSize = s32FileSize;
    memcpy(fileName, szNewFilePath, REC_FILE_NAME_LEN);

    return SV_SUCCESS;

}

}


