// PHZ
// 2018-9-30

#include <time.h>
#include <stdio.h>
#include "RtpPureConnection.h"
#include "RtspConnection.h"
#include "net/SocketUtil.h"
#include "RtpPureConnection.h"

using namespace std;
using namespace xop;

RTP_CHANNEL_INFO_S m_rtpPureChannelInfo[MAX_MEDIA_CHANNEL];

RtpPureConnection::RtpPureConnection():RtpConnection()
{
    std::random_device rd;

    for(int chn=0; chn<MAX_MEDIA_CHANNEL; chn++) {
        rtpfd_[chn] = 0;
        rtcpfd_[chn] = 0;
        memset(&media_channel_info_[chn], 0, sizeof(media_channel_info_[chn]));
        media_channel_info_[chn].packet_seq = rd()&0xffff;
        media_channel_info_[chn].packet_count = 0;
        media_channel_info_[chn].octet_count = 0;
        media_channel_info_[chn].rtp_header.version = RTP_VERSION;
        media_channel_info_[chn].rtp_header.seq = 0; //htons(1);
        media_channel_info_[chn].rtp_header.ts = htonl(rd());
        media_channel_info_[chn].rtp_header.ssrc = htonl(rd());
        if (!m_rtpPureChannelInfo[chn].is_playing)
        {
            m_rtpPureChannelInfo[chn].timestampBase = htonl(rd());
            m_rtpPureChannelInfo[chn].is_tsPreseted = false;
#if RTSP_DEBUG_MIME
            printf("chn[%d] timestampBase: %u\n", chn, m_rtpPureChannelInfo[chn].timestampBase);
#endif
        }

        media_channel_info_[chn].rtcp_pkt_info.sr_pack.csrc = 0;
        media_channel_info_[chn].rtcp_pkt_info.sr_pack.padding = 0;
        media_channel_info_[chn].rtcp_pkt_info.sr_pack.version = RTP_VERSION;
        media_channel_info_[chn].rtcp_pkt_info.sr_pack.payload = 200;
        media_channel_info_[chn].rtcp_pkt_info.sr_pack.length = htons(6);
        media_channel_info_[chn].rtcp_pkt_info.sr_pack.ssrc = media_channel_info_[chn].rtp_header.ssrc;

        media_channel_info_[chn].rtcp_pkt_info.sdes_pack.cssc = 1;
        media_channel_info_[chn].rtcp_pkt_info.sdes_pack.padding = 0;
        media_channel_info_[chn].rtcp_pkt_info.sdes_pack.version = RTP_VERSION;
        media_channel_info_[chn].rtcp_pkt_info.sdes_pack.payload = 202;
        media_channel_info_[chn].rtcp_pkt_info.sdes_pack.pktLength = htons(4);
        media_channel_info_[chn].rtcp_pkt_info.sdes_pack.ssrc = media_channel_info_[chn].rtp_header.ssrc;
    }
}

RtpPureConnection::~RtpPureConnection()
{
    for(int chn=0; chn<MAX_MEDIA_CHANNEL; chn++) {
        if(rtpfd_[chn] > 0) {
            SocketUtil::Close(rtpfd_[chn]);
        }

        if(rtcpfd_[chn] > 0) {
            SocketUtil::Close(rtcpfd_[chn]);
        }
    }
}

uint64_t RtpPureConnection::htonll(uint64_t val)
{
    return (((uint64_t) htonl(val)) << 32) + htonl(val >> 32);
}

int RtpPureConnection::GetId() const
{
    return 1;
}


bool RtpPureConnection::SetupRtpOverUdp(MediaChannelId channel_id, uint16_t rtp_port, uint16_t rtcp_port)
{
    media_channel_info_[channel_id].rtp_port = rtp_port;
    media_channel_info_[channel_id].rtcp_port = rtcp_port;

    std::random_device rd;
    for (int n = 0; n <= 10; n++) {
        if (n == 10) {
            return false;
        }
        local_rtp_port_[channel_id] = rtp_port;
        local_rtcp_port_[channel_id] =local_rtp_port_[channel_id] + 1;

        rtpfd_[channel_id] = ::socket(AF_INET, SOCK_DGRAM, 0);
        if(!SocketUtil::Bind(rtpfd_[channel_id], "0.0.0.0",  local_rtp_port_[channel_id])) {
            SocketUtil::Close(rtpfd_[channel_id]);
            continue;
        }

        rtcpfd_[channel_id] = ::socket(AF_INET, SOCK_DGRAM, 0);
        if(!SocketUtil::Bind(rtcpfd_[channel_id], "0.0.0.0", local_rtcp_port_[channel_id])) {
            SocketUtil::Close(rtpfd_[channel_id]);
            SocketUtil::Close(rtcpfd_[channel_id]);
            continue;
        }

        break;

    }

    SocketUtil::SetSendBufSize(rtpfd_[channel_id], 50*1024);

    peer_rtp_addr_[channel_id].sin_family = AF_INET;
    peer_rtp_addr_[channel_id].sin_addr.s_addr = peer_addr_.sin_addr.s_addr;
    peer_rtp_addr_[channel_id].sin_port = htons(media_channel_info_[channel_id].rtp_port);

    peer_rtcp_sddr_[channel_id].sin_family = AF_INET;
    peer_rtcp_sddr_[channel_id].sin_addr.s_addr = peer_addr_.sin_addr.s_addr;
    peer_rtcp_sddr_[channel_id].sin_port = htons(media_channel_info_[channel_id].rtcp_port);

    media_channel_info_[channel_id].is_setup = true;
    transport_mode_ = RTP_OVER_UDP;

    return true;
}

bool RtpPureConnection::SetupRtpOverMulticast(MediaChannelId channel_id, std::string ip, uint16_t port)
{
    std::random_device rd;
    
    for (int n = 0; n <= 10; n++) {
        if (n == 10) {
            return false;
        }
       
        local_rtp_port_[channel_id] = rd() & 0xfffe;
        rtpfd_[channel_id] = ::socket(AF_INET, SOCK_DGRAM, 0);
#if 1//(!defined(BOARD_ADA32V2) && !defined(BOARD_ADA32C4))    // 多播
        if (!SocketUtil::Bind(rtpfd_[channel_id], "0.0.0.0", local_rtp_port_[channel_id])) {
          SocketUtil::Close(rtpfd_[channel_id]);
          continue;
        }
#else  // 广播
        int opt  = 1;
        setsockopt(rtpfd_[channel_id], SOL_SOCKET, SO_BROADCAST, &opt, sizeof(opt));
#endif
        break;
    }

    media_channel_info_[channel_id].rtp_port = port;

    peer_rtp_addr_[channel_id].sin_family = AF_INET;
    peer_rtp_addr_[channel_id].sin_addr.s_addr = inet_addr(ip.c_str());
    peer_rtp_addr_[channel_id].sin_port = htons(port);

    media_channel_info_[channel_id].is_setup = true;
    transport_mode_ = RTP_OVER_MULTICAST;
    is_multicast_ = true;
    return true;
}

void RtpPureConnection::Play()
{
    int ret = 0;
    for(int chn=0; chn<MAX_MEDIA_CHANNEL; chn++) {
        if (media_channel_info_[chn].is_setup) {

            presetTimestamp(chn);
            media_channel_info_[chn].is_play = true;
            m_rtpPureChannelInfo[chn].is_playing = true;
            m_rtpPureChannelInfo[chn].clientNum++;
        }
    }
}

void RtpPureConnection::Record()
{
    for (int chn=0; chn<MAX_MEDIA_CHANNEL; chn++) {
        if (media_channel_info_[chn].is_setup) {
            media_channel_info_[chn].is_record = true;
            media_channel_info_[chn].is_play = true;
            m_rtpPureChannelInfo[chn].is_playing = true;
        }
    }
}

void RtpPureConnection::Teardown()
{
    if(!is_closed_) {
        is_closed_ = true;
        for(int chn=0; chn<MAX_MEDIA_CHANNEL; chn++) {
            media_channel_info_[chn].is_play = false;
            media_channel_info_[chn].is_record = false;
            media_channel_info_[chn].packet_count = 0;
            media_channel_info_[chn].octet_count = 0;
            m_rtpPureChannelInfo[chn].clientNum--;
            if (0 == m_rtpPureChannelInfo[chn].clientNum)
                m_rtpPureChannelInfo[chn].is_playing = false;
        }
    }
}

string RtpPureConnection::GetMulticastIp(MediaChannelId channel_id) const
{
    return std::string(inet_ntoa(peer_rtp_addr_[channel_id].sin_addr));
}

string RtpPureConnection::GetRtpInfo(const std::string& rtsp_url)
{
    
    char buf[2048] = { 0 };
    snprintf(buf, 1024, "RTP-Info: ");

    int num_channel = 0;

    auto time_point = chrono::time_point_cast<chrono::milliseconds>(chrono::steady_clock::now());
    auto ts = time_point.time_since_epoch().count();
    for (int chn = 0; chn<MAX_MEDIA_CHANNEL; chn++) {
        uint32_t rtpTime = (uint32_t)(ts*media_channel_info_[chn].clock_rate / 1000);
        if (media_channel_info_[chn].is_setup) {
            if (num_channel != 0) {
                snprintf(buf + strlen(buf), sizeof(buf) - strlen(buf), ",");
            }           

            snprintf(buf + strlen(buf), sizeof(buf) - strlen(buf),
                    "url=%s/track%d;seq=0;rtptime=%u",
                    rtsp_url.c_str(), chn, rtpTime);
            num_channel++;
        }
    }

    return std::string(buf);
}

uint32_t RtpPureConnection::convertToRTPTimestamp(MediaChannelId channel_id, struct timeval tv)
{
    uint32_t timestampIncrement = (media_channel_info_[channel_id].clock_rate*tv.tv_sec);
    timestampIncrement += (uint32_t)(media_channel_info_[channel_id].clock_rate*(tv.tv_usec/1000000.0)+0.5);
    
    if (m_rtpPureChannelInfo[channel_id].is_tsPreseted)
    {
        m_rtpPureChannelInfo[channel_id].timestampBase -= timestampIncrement;   //第一帧来到时的RTP时间戳为预设的基准值
        m_rtpPureChannelInfo[channel_id].is_tsPreseted = false;
    }

    uint32_t const rtpTimestamp = m_rtpPureChannelInfo[channel_id].timestampBase + timestampIncrement;

    return rtpTimestamp;
}

uint32_t RtpPureConnection::presetTimestamp(MediaChannelId channel_id)
{
    struct timeval timeNow;
    gettimeofday(&timeNow, NULL);

    //用当前时间预设RTP时间戳基准值
    uint32_t tsNow = convertToRTPTimestamp(channel_id, timeNow);
    if (!m_rtpPureChannelInfo[channel_id].is_playing)
    {
        m_rtpPureChannelInfo[channel_id].timestampBase = tsNow;
        m_rtpPureChannelInfo[channel_id].is_tsPreseted = true;
#if RTSP_DEBUG_MIME
        printf("presetTimestamp timeNow_sec: %ld timeNow_usec: %ld\n", timeNow.tv_sec, timeNow.tv_usec);
        printf("presetTimestamp timestampBase: %u\n", m_rtpPureChannelInfo[channel_id].timestampBase);
#endif
    }

    return tsNow;
}

void RtpPureConnection::SetFrameType(uint8_t frame_type)
{
    frame_type_ = frame_type;
    if(!has_key_frame_ && (frame_type == 0 || frame_type == VIDEO_FRAME_I)) {
        has_key_frame_ = true;
    }
}

void RtpPureConnection::SetRtpHeader(MediaChannelId channel_id, RtpPacket pkt)
{
    if((media_channel_info_[channel_id].is_play || media_channel_info_[channel_id].is_record) && has_key_frame_) {
        media_channel_info_[channel_id].rtp_header.marker = pkt.last;
        media_channel_info_[channel_id].rtp_header.ts = htonl(pkt.timestamp);
        media_channel_info_[channel_id].rtp_header.seq = htons(media_channel_info_[channel_id].packet_seq++);
        memcpy(pkt.data.get()+TCP_HEAD_SIZE, &media_channel_info_[channel_id].rtp_header, RTP_HEADER_SIZE);
    }
}

int RtpPureConnection::SendRtpPacket(MediaChannelId channel_id, RtpPacket pkt)
{
    if (is_closed_) {
      return -1;
    }
   
    //auto conn = rtsp_connection_.lock();
    //if (!conn) {
    //  return -1;
    //}
    pkt.timestamp = convertToRTPTimestamp(channel_id, pkt.presentationTime);

    this->SetFrameType(pkt.type);
    this->SetRtpHeader(channel_id, pkt);
    if((media_channel_info_[channel_id].is_play || media_channel_info_[channel_id].is_record) && has_key_frame_ ) {
        //if(transport_mode_ == RTP_OVER_UDP) 
        SendRtpOverUdp(channel_id, pkt);
               
        media_channel_info_[channel_id].octet_count  += pkt.size;
        media_channel_info_[channel_id].packet_count += 1;
    }

    return 1;
}

int RtpPureConnection::SendRtpOverUdp(MediaChannelId channel_id, RtpPacket pkt)
{
    media_channel_info_[channel_id].octet_count  += pkt.size;
    media_channel_info_[channel_id].packet_count += 1;

    int ret = sendto(rtpfd_[channel_id], (const char*)pkt.data.get()+TCP_HEAD_SIZE, pkt.size-TCP_HEAD_SIZE, 0, 
                    (struct sockaddr *)&(peer_rtp_addr_[channel_id]),
                    sizeof(struct sockaddr_in));
                   
    if(ret < 0) {
        Teardown();
        return -1;
    }

    return ret;
}

void RtpPureConnection::addSR(MediaChannelId channel_id, RtcpPacket *pkt)
{
    struct timeval timeNow;
    gettimeofday(&timeNow, NULL);

    media_channel_info_[channel_id].rtcp_pkt_info.sr_pack.ntpTimestampMSW = htonl((uint32_t)(timeNow.tv_sec + 0x83AA7E80)); 
    double fractionalPart = (timeNow.tv_usec/15625.0)*0x04000000;
    media_channel_info_[channel_id].rtcp_pkt_info.sr_pack.ntpTimestampLSW = htonl((uint32_t)(fractionalPart+0.5));

    uint32_t rtpTimestampNow = convertToRTPTimestamp(channel_id, timeNow);
    media_channel_info_[channel_id].rtcp_pkt_info.sr_pack.rtpTimestamp = htonl(rtpTimestampNow);
    
    media_channel_info_[channel_id].rtcp_pkt_info.sr_pack.packet_count = htonl(media_channel_info_[channel_id].packet_count);
    media_channel_info_[channel_id].rtcp_pkt_info.sr_pack.octet_count = htonl(media_channel_info_[channel_id].octet_count);

    pkt->size = TCP_HEAD_SIZE + RTCP_SR_SIZE;
    memcpy(pkt->data.get()+pkt->offset, &media_channel_info_[channel_id].rtcp_pkt_info.sr_pack, RTCP_SR_SIZE);
    pkt->offset = pkt->size;
}

void RtpPureConnection::addSDES(MediaChannelId channel_id, RtcpPacket *pkt)
{
    media_channel_info_[channel_id].rtcp_pkt_info.sdes_pack.cname = 1;
    media_channel_info_[channel_id].rtcp_pkt_info.sdes_pack.udnLength = 6;
    media_channel_info_[channel_id].rtcp_pkt_info.sdes_pack.udn = htonll(0x286e6f6e65290000);   //默认设为(none) 0x28,0x6e,0x6f,0x6e,0x65,0x29
    media_channel_info_[channel_id].rtcp_pkt_info.sdes_pack.type = htonl(0);

    pkt->size += RTCP_SDES_SIZE;
    memcpy(pkt->data.get()+pkt->offset, &media_channel_info_[channel_id].rtcp_pkt_info.sdes_pack, RTCP_SDES_SIZE);
    pkt->offset = pkt->size;
}


int RtpPureConnection::SendReport(MediaChannelId channel_id)
{
    int ret = 0;
    if (is_closed_) {
        return -1;
    }
    
    RtcpPacket pkt;
    addSR(channel_id, &pkt);
    addSDES(channel_id, &pkt);
    if(transport_mode_ == RTP_OVER_UDP)
        ret = SendRtcpOverUdp(channel_id, pkt);

    if (ret <= 0)
        return -1;
    
    return ret;
}


int RtpPureConnection::SendRtcpOverUdp(MediaChannelId channel_id, RtcpPacket pkt)
{
    int ret = sendto(rtcpfd_[channel_id], (const char*)pkt.data.get()+TCP_HEAD_SIZE, pkt.size-TCP_HEAD_SIZE, 0, 
                    (struct sockaddr *)&(peer_rtcp_sddr_[channel_id]),
                    sizeof(struct sockaddr_in));
    if(ret < 0)
    {
        Teardown();
        return -1;
    }
#if RTSP_DEBUG_MIME
    printf("SendRtcpOverUdp chn[%d] send rtcp packet size: %d\n", channel_id, ret);
#endif  
    return ret;
}


