﻿/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：mpp_sys.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2021-04-21

文件功能描述: 封装RK MPP系统控制模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <linux/fb.h>
#include <error.h>
#include <sys/ioctl.h>
#include <sys/prctl.h>
#include <fcntl.h>
#include <time.h>
#include <pthread.h>
#include <errno.h>

#include "common.h"
#include "print.h"
#include "../../include/board.h"
#include "rkmedia_api.h"
#include "mpp_sys.h"
#include "media.h"

/* 系统控制模块控制信息 */
typedef struct tagMppSysInfo_S
{
    pthread_mutex_t callbackLock;   /* 数据回调线程互斥锁 (视频编码回调和音频编码回调的互斥) */
    MPP_BIND_INFO_S stBindInfo;     /* 通道绑定信息 */
    SV_BOOL     bNightMode;         /* 是否启用夜晚模式 */
    uint32      u32IRcutMode;       /* IRCUT模式(0:白天,1:自动,2:夜晚) */
    SV_BOOL     bUpdataIRcutMode;   /* IRCUT模式是否发生变化 */
    uint32      u32LedBright;       /* 红外灯亮度 */
    WDR_MODE_EE enSysWDRMode;		/* 是否使能WDR */
    uint64      u64DiffPts;         /* 系统时间戳与开机时间的差值 */
    uint32      u32TID;             /* 系统控制线程ID */
    SV_BOOL     bRunning;           /* 线程是否正在运行 */
    SV_BOOL     bException;         /* 线程是否出现异常 */
} MPP_SYS_INFO_S;

MPP_SYS_INFO_S m_stSysInfo = {0};   /* 系统控制模块信息 */

extern MPP_PHY_CHN g_astViPhyChn[VIM_MAX_CHN_NUM];
extern MPP_PHY_CHN g_astAiPhyChn[MPP_AIO_MAX_CHN_NUM];

extern void * mpp_sys_Body(MPP_SYS_INFO_S *pstSysInfo);
static uint64 mpp_sys_GetSysTimeByUsec(void)
{
    struct timeval stTime;

    gettimeofday(&stTime, NULL);

    return  (stTime.tv_sec * 1000000LLU) + stTime.tv_usec;
}

/******************************************************************************
 * 函数功能: 初始化HiFB，图像层绑定VO设备
 * 输入参数: stSrcSize --- 视频源画面大小 (宽高)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_initHiFB(SV_SIZE_S stSrcSize)
{
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 配置并初始化MPP系统
 * 输入参数: pstVbConf --- 视频公共缓存池配置信息 u32MaxPoolCnt: [1, MPP_SYS_MAX_COMM_POOLS]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_ILLEGAL_PARAM - 参数错误
             ERR_SYS_NOTREADY - 系统未初始化
             ERR_NOMEM - 内存不足
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Init(MPP_VB_CONF_S *pstVbConf)
{
    RK_S32 s32Ret = 0;

    memset (&m_stSysInfo, 0x0, sizeof(MPP_SYS_INFO_S));
    s32Ret = RK_MPI_SYS_Init();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Init failed! [err=%#x]\n", s32Ret);
    }

    LOG_LEVEL_CONF_S szlogconf = {0};
    strcpy(szlogconf.cModName, "all");
    szlogconf.s32Level = 0;
    s32Ret = RK_MPI_LOG_SetLevelConf(&szlogconf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_LOG_SetLevelConf failed! [err=%#x]\n", s32Ret);
    }

    s32Ret = pthread_mutex_init(&m_stSysInfo.callbackLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化MPP系统
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_BUSY - 系统忙碌
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Fini()
{
    sint32 s32Ret = 0, i;

    s32Ret = pthread_mutex_destroy(&m_stSysInfo.callbackLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_destroy failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动SYS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Start()
{
    sint32 s32Ret = 0;
    uint32 u32TID = 0;
    pthread_attr_t 	attr;    

    m_stSysInfo.bRunning = SV_TRUE;
    m_stSysInfo.bException = SV_FALSE;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程    
    s32Ret = pthread_create(&u32TID, &attr, mpp_sys_Body, &m_stSysInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stSysInfo.u32TID = u32TID;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止SYS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Stop()
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;

    m_stSysInfo.bRunning = SV_FALSE;
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for VENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
   
    return SV_SUCCESS;
}

typedef enum tagMppSysMode_S
{
    MPP_SYS_DAY = 0, 
    MPP_SYS_NIGHT,

    MPP_SYS_BUTT
} MPP_SYS_MODE;

/******************************************************************************
 * 函数功能: SYS模块线程体
 * 输入参数: pstSysInfo --- MPP系统控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_sys_Body(MPP_SYS_INFO_S *pstSysInfo)
{
    /* A38默认先切为白天,后面跟进实际场景进行白天黑夜的切换 */

    sint32 s32Ret = 0, i;
    uint32 u32Value = MPP_SYS_DAY, u32CdsStat = MPP_SYS_DAY;
    uint32 u32ChnNum = 1;
    SV_BOOL bOpenIrLED = SV_FALSE;              /* 当前LED是否打开标志位 */
    uint32 u32LedBright = 0;                    /* 当前LED亮度 */

    s32Ret = prctl(PR_SET_NAME, "mpp_sys_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }


    s32Ret = BOARD_InitIrCut();
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_InitIrCut failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = BOARD_SetIrLED(SV_FALSE, u32LedBright);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_SetIrLED failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = BOARD_SetIrCutStatue(SV_FALSE);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
    }

    if (BOARD_ADA47V1_V2 == BOARD_GetVersion() && (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_ADA47V1_G_6M)))
    {
        s32Ret = mpp_vi_Color2GreyEnable();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vi_Color2GreyEnable failed. [err=%#x]\n", s32Ret);
        }
    }
    else
    {
        s32Ret = mpp_vi_Color2GreyDisable();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vi_Color2GreyDisable failed. [err=%#x]\n", s32Ret);
        }
    }

    for (i = 0; i < 30; i++)
    {
        if (pstSysInfo->bUpdataIRcutMode)
        {
            break;
        }
        sleep_ms(100);
    }

    print_level(SV_INFO, "bUpdataIRcutMode:%d, u32IRcutMode:%d\n", pstSysInfo->bUpdataIRcutMode, pstSysInfo->u32IRcutMode);

    while (pstSysInfo->bRunning)
    {

        sleep_ms(50);
        u32Value = MPP_SYS_DAY;
        
        if (u32LedBright != pstSysInfo->u32LedBright)
        {
            u32LedBright = pstSysInfo->u32LedBright;
            if (bOpenIrLED)
            {
                s32Ret = BOARD_SetIrLED(bOpenIrLED, u32LedBright);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "BOARD_SetIrLED failed. [err=%#x]\n", s32Ret);
                }
            }
        }

        if(0 == pstSysInfo->u32IRcutMode)   // 配置为白天模式
        {
            if (!pstSysInfo->bUpdataIRcutMode)
            {
                continue;
            }
            
            print_level(SV_INFO, "setting as day mode.\n");
            pstSysInfo->bUpdataIRcutMode = SV_FALSE;
            pstSysInfo->bNightMode = SV_FALSE;
            s32Ret = BOARD_SetIrCutStatue(SV_FALSE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = mpp_vi_Color2GreyDisable();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vi_Color2GreyDisable failed. [err=%#x]\n", s32Ret);
            }

            /* 白天模式下，红外灯常关闭 */
            s32Ret = BOARD_SetIrLED(SV_FALSE, u32LedBright);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrLED failed. [err=%#x]\n", s32Ret);
            }

            bOpenIrLED = SV_FALSE;
            u32CdsStat = 0;
            continue;
        }
        else if (2 == pstSysInfo->u32IRcutMode) // 配置为夜晚模式
        {
            if (!pstSysInfo->bUpdataIRcutMode)
            {
                continue;
            }

            print_level(SV_INFO, "setting as night mode.\n");
            pstSysInfo->bUpdataIRcutMode = SV_FALSE;
            pstSysInfo->bNightMode = SV_TRUE;
            if (BOARD_ADA47V1_V1 != BOARD_GetVersion())
            {
                s32Ret = mpp_vi_Color2GreyEnable();
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "mpp_vi_Color2GreyEnable failed. [err=%#x]\n", s32Ret);
                }

                s32Ret = BOARD_SetIrCutStatue(SV_TRUE);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
                }
            }
            
            /* 夜晚模式下，红外灯常开 */
            s32Ret = BOARD_SetIrLED(SV_TRUE, u32LedBright);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrLED failed. [err=%#x]\n", s32Ret);
            }

            bOpenIrLED = SV_TRUE;
            u32CdsStat = 1;
            continue;
        }
        else
        {
            pstSysInfo->bUpdataIRcutMode = SV_FALSE;
        }

        for (i = 0; i < 10; i++)
        {
            s32Ret = BOARD_CDS_DETECT(&u32Value);
            if (SV_SUCCESS == s32Ret)
            {
                break;
            }
            print_level(SV_WARN, "BOARD_CDS_DETECT failed.\n");
            sleep_ms(50);
        }

        if (u32CdsStat == u32Value)
        {
            continue;
        }

        u32CdsStat = u32Value;
        if (MPP_SYS_DAY == u32CdsStat)
        {
            print_level(SV_INFO, "switch to day mode.\n");
            pstSysInfo->bNightMode = SV_FALSE;
            
            s32Ret = BOARD_SetIrCutStatue(SV_FALSE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = mpp_vi_Color2GreyDisable();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vi_Color2GreyDisable failed. [err=%#x]\n", s32Ret);
            }
            s32Ret = BOARD_SetIrLED(SV_FALSE, u32LedBright);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrLED failed. [err=%#x]\n", s32Ret);
            }
            
            bOpenIrLED = SV_FALSE;
        }
        else if (MPP_SYS_NIGHT == u32CdsStat)
        {
            print_level(SV_INFO, "switch to night mode.\n");
            pstSysInfo->bNightMode = SV_TRUE;
            sleep_ms(50);
            
            s32Ret = mpp_vi_Color2GreyEnable();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vi_Color2GreyEnable failed. [err=%#x]\n", s32Ret);
            }
            
            s32Ret = BOARD_SetIrCutStatue(SV_TRUE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = BOARD_SetIrLED(SV_TRUE, u32LedBright);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrLED failed. [err=%#x]\n", s32Ret);
            }

            bOpenIrLED = SV_TRUE;
        }
    }
    return NULL;
}

/******************************************************************************
 * 函数功能: 获取MPP时间戳
 * 输入参数: 无
 * 输出参数: pu64Pts --- 时间戳
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_GetPts(uint64 *pu64Pts)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 同步系统时间戳
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_UpdatePts()
{
    struct timespec tvNow = {0, 0};
    struct timeval tv = {0};
    uint64 u64TimeUs = 0ll;
    uint64 u64PTS = 0ll;

    clock_gettime(CLOCK_MONOTONIC, &tvNow);
    gettimeofday(&tv, NULL);
    u64TimeUs = (uint64)tvNow.tv_sec*1000000ll + tvNow.tv_nsec/1000;
    u64PTS = tv.tv_sec*1000000ll + tv.tv_usec;
    if (llabs(m_stSysInfo.u64DiffPts - (u64PTS-u64TimeUs)) > 1000)
    {
        m_stSysInfo.u64DiffPts = u64PTS-u64TimeUs;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 系统时间戳与开机时间的差值(us)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
uint64 mpp_sys_GetDiffPts()
{
    return m_stSysInfo.u64DiffPts;
}

/******************************************************************************
 * 函数功能: 视频输入通道绑定到视频编码通道组
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VoChn --- 视频输出通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_INVALID_CHNID - 通道号无效
             ERR_MEDIA_INVALID_DEVID - 无效设备号
             ERR_INVALID_CHNID - 无效通道号
             ERR_UNEXIST - 通道组不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViGroupBind(sint32 s32ViChn, sint32 s32VeGroup)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道到视频编码通道组解绑定
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VoChn --- 视频输出通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道组不存在
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViGroupUnBind(sint32 s32ViChn, sint32 s32VeGroup)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道绑定到视频处理通道组
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViVpssBind(sint32 s32ViChn, sint32 s32VpssGrp)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32ViChn >= VIM_MAX_CHN_NUM || s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }
    printf("vi vpss bind, input vi channel: %d, vpss group:%d\n", s32ViChn, s32VpssGrp);
    stSrcChn.enModId = RK_ID_VI;
    stSrcChn.s32DevId = g_astViPhyChn[s32ViChn].DevId;
    stSrcChn.s32ChnId = g_astViPhyChn[s32ViChn].ChnId;

    for(int i=0; i < MPP_VPSS_CHN_BUTT; i++)
    {
#if (BOARD != BOARD_ADA32IR)
        if (i == MPP_VPSS_CHN_VO)
            continue;

        if (i == MPP_VPSS_CHN_SEC)
            continue;
#endif

        stDestChn.enModId = RK_ID_RGA;
        stDestChn.s32DevId = 0;
        stDestChn.s32ChnId = i + s32VpssGrp * MPP_VPSS_CHN_BUTT;
        s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_SYS_Bind failed! [err=%d]\n", s32Ret);
            continue;
        }
        printf("bind channel type %d\n", stDestChn.enModId);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道到视频处理通道组解绑定
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViVpssUnBind(sint32 s32ViChn, sint32 s32VpssGrp)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32ViChn >= VIM_MAX_CHN_NUM || s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = RK_ID_VI;
    stSrcChn.s32DevId = g_astViPhyChn[s32ViChn].DevId;
    stSrcChn.s32ChnId = g_astViPhyChn[s32ViChn].ChnId;

    for(int i=0; i < MPP_VPSS_CHN_BUTT; i++)
    {
#if (BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1  || BOARD == BOARD_ADA32C4)
        if (i == MPP_VPSS_CHN_VO)
            continue;
#endif

#if (BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1  || BOARD == BOARD_ADA32C4)
        if (i == MPP_VPSS_CHN_SEC)
            continue;
#endif

        stDestChn.enModId = RK_ID_RGA;
        stDestChn.s32DevId = 0;
        stDestChn.s32ChnId = i + s32VpssGrp * MPP_VPSS_CHN_BUTT;
        s32Ret = RK_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_SYS_UnBind(%d %d %d) failed! [err=%d]\n", s32ViChn, s32VpssGrp, i, s32Ret);
            continue;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道绑定到视频处理通道组的相应通道
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViVpssChnBind(sint32 s32ViChn, sint32 s32VpssGrp, sint32 s32VpssChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32ViChn >= VIM_MAX_CHN_NUM || s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = RK_ID_VI;
    stSrcChn.s32DevId = g_astViPhyChn[s32ViChn].DevId;
    stSrcChn.s32ChnId = g_astViPhyChn[s32ViChn].ChnId;

    stDestChn.enModId = RK_ID_RGA;
    stDestChn.s32DevId = s32VpssGrp;
    stDestChn.s32ChnId = s32VpssChn;

    s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道解绑到视频处理通道组的相应通道
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无

 *****************************************************************************/
sint32 mpp_sys_ViVpssChnUnBind(sint32 s32ViChn, sint32 s32VpssGrp, sint32 s32VpssChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32ViChn >= VIM_MAX_CHN_NUM || s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = RK_ID_VI;
    stSrcChn.s32DevId = g_astViPhyChn[s32ViChn].DevId;
    stSrcChn.s32ChnId = g_astViPhyChn[s32ViChn].ChnId;

    stDestChn.enModId = RK_ID_RGA;
    stDestChn.s32DevId = s32VpssGrp;
    stDestChn.s32ChnId = s32VpssChn;


    s32Ret = RK_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_UnBind failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频处理通道绑定到视频编码通道
 * 输入参数: s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道号 [0, 1]
             s32VencChn --- 视频编码通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssVencBind(sint32 s32VpssGrp, sint32 s32VpssChn, sint32 s32VencChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32VpssGrp >= VPS_MAX_GRP_NUM || s32VencChn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = RK_ID_RGA;
    stSrcChn.s32DevId = s32VpssGrp;
    stSrcChn.s32ChnId = s32VpssChn;

    stDestChn.enModId = RK_ID_VENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32VencChn;
    s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频处理通道到视频编码通道解绑定
 * 输入参数: s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道号 [0, 1]
             s32VencChn --- 视频编码通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssVencUnBind(sint32 s32VpssGrp, sint32 s32VpssChn, sint32 s32VencChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32VpssGrp >= VPS_MAX_GRP_NUM || s32VencChn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = RK_ID_RGA;
    stSrcChn.s32DevId = s32VpssGrp;
    stSrcChn.s32ChnId = s32VpssChn;

    stDestChn.enModId = RK_ID_VENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32VencChn;
    s32Ret = RK_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_UnBind failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频处理通道绑定到视频处理通道组
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssVoBind(sint32 s32VpssGrp, sint32 s32VpssChn, sint32 s32VoChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if(s32VpssGrp >= VPS_MAX_GRP_NUM || s32VoChn >= MPP_VO_MAX_CHN_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId =  RK_ID_RGA;
    stSrcChn.s32DevId = s32VpssGrp;
    stSrcChn.s32ChnId = s32VpssChn;

    stDestChn.enModId = RK_ID_VO;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32VoChn;
    
    s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Bind failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频处理通道到视频处理通道组解绑定
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssVoUnBind(sint32 s32VpssGrp, sint32 s32VpssChn, sint32 s32VoChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if(s32VpssGrp >= VPS_MAX_GRP_NUM || s32VoChn >= MPP_VO_MAX_CHN_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = RK_ID_RGA;
    stSrcChn.s32DevId = s32VpssGrp;
    stSrcChn.s32ChnId = s32VoChn;

    stDestChn.enModId = RK_ID_VO;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32VoChn;

    s32Ret = RK_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频处理通道输出回调函数注册
 * 输入参数: s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道号
             szFunc     --- 输出回调函数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssRegisterOut(sint32 s32VpssGrp, sint32 s32VpssChn, void *szFunc)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stDestChn;
    OutCbFunc cb = (OutCbFunc)szFunc;
    
    if(s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }
    
    if(szFunc == NULL)
    {
        return ERR_BADADDR;
    }

    stDestChn.enModId = RK_ID_RGA;
    stDestChn.s32DevId = s32VpssGrp;
    stDestChn.s32ChnId = s32VpssChn;

    s32Ret = RK_MPI_SYS_RegisterOutCb(&stDestChn, cb);
    if(s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_RegisterOutCb failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    return s32Ret;
}

/******************************************************************************
 * 函数功能: 视频处理通道输出回调函数注销
 * 输入参数: s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssUnRegisterOut(sint32 s32VpssGrp, sint32 s32VpssChn)
{
    RK_S32 s32Ret = RK_SUCCESS;
    MPP_CHN_S stDestChn;
    
    if(s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stDestChn.enModId = RK_ID_RGA;
    stDestChn.s32DevId = s32VpssGrp;
    stDestChn.s32ChnId = s32VpssChn;

    s32Ret = RK_MPI_SYS_RegisterOutCb(&stDestChn, NULL);
    if(s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_RegisterOutCb failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    return s32Ret;
}

sint32 mpp_sys_AiAoBind(sint32 s32AiChn, sint32 s32AoChn)
{
#if (BOARD == BOARD_ADA900V1 || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 || BOARD == BOARD_ADA32C4)
    RK_S32 s32Ret = 0;
    MPP_CHN_S stSrcChn,stDestChn;

    if (s32AiChn > MPP_AENC_MAX_CHN_NUM || s32AoChn > MPP_AENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stSrcChn.enModId = RK_ID_AI;
    stSrcChn.s32DevId = g_astAiPhyChn[s32AiChn].DevId;
    stSrcChn.s32ChnId = g_astAiPhyChn[s32AiChn].ChnId;
    stDestChn.enModId = RK_ID_AO;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32AoChn;
    s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].s32Chn = s32AiChn;
    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].enChnType = MPP_CHN_AI;
    m_stSysInfo.stBindInfo.astAoChn[s32AoChn].s32Chn = s32AoChn;
    m_stSysInfo.stBindInfo.astAoChn[s32AoChn].enChnType = MPP_CHN_AO;
#endif  
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 音频输入通道到音频输出通道解绑定
 * 输入参数: s32AiChn --- 音频输入通道号
             s32AencChn --- 音频输出通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_AiAoUnBind(sint32 s32AiChn, sint32 s32AoChn)
{
#if (BOARD == BOARD_ADA900V1 || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 || BOARD == BOARD_ADA32C4)
    RK_S32 s32Ret = 0;
    MPP_CHN_S stSrcChn,stDestChn;

    if (s32AiChn > MPP_AIO_MAX_CHN_NUM || s32AoChn > MPP_AIO_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stSrcChn.enModId = RK_ID_AI;
    stSrcChn.s32DevId = g_astAiPhyChn[s32AiChn].DevId;
    stSrcChn.s32ChnId = g_astAiPhyChn[s32AiChn].ChnId;
    stDestChn.enModId = RK_ID_AO;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32AoChn;
    s32Ret = RK_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_UnBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].s32Chn = 0;
    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].enChnType = MPP_CHN_INVALID;
    m_stSysInfo.stBindInfo.astAencChn[s32AoChn].s32Chn = 0;
    m_stSysInfo.stBindInfo.astAencChn[s32AoChn].enChnType = MPP_CHN_INVALID;
#endif   
    return SV_SUCCESS;
}



sint32 mpp_sys_AiAencBind(sint32 s32AiChn, sint32 s32AencChn)
{
#if (BOARD == BOARD_ADA900V1 || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 || BOARD == BOARD_ADA32C4)
    RK_S32 s32Ret = 0;
    MPP_CHN_S stSrcChn,stDestChn;

    if (s32AiChn > MPP_AENC_MAX_CHN_NUM || s32AencChn > MPP_AENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stSrcChn.enModId = RK_ID_AI;
    stSrcChn.s32DevId = g_astAiPhyChn[s32AiChn].DevId;
    stSrcChn.s32ChnId = g_astAiPhyChn[s32AiChn].ChnId;
    stDestChn.enModId = RK_ID_AENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32AencChn;
    s32Ret = RK_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].s32Chn = s32AiChn;
    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].enChnType = MPP_CHN_AI;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].s32Chn = s32AencChn;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].enChnType = MPP_CHN_AENC;
#endif  
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 音频输入通道到音频编码通道解绑定
 * 输入参数: s32AiChn --- 音频输入通道号
             s32AencChn --- 音频编码通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_AiAencUnBind(sint32 s32AiChn, sint32 s32AencChn)
{
#if (BOARD == BOARD_ADA900V1 || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 || BOARD == BOARD_ADA32C4)
    RK_S32 s32Ret = 0;
    MPP_CHN_S stSrcChn,stDestChn;

    if (s32AiChn > MPP_AIO_MAX_CHN_NUM || s32AencChn > MPP_AENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stSrcChn.enModId = RK_ID_AI;
    stSrcChn.s32DevId = g_astAiPhyChn[s32AiChn].DevId;
    stSrcChn.s32ChnId = g_astAiPhyChn[s32AiChn].ChnId;
    stDestChn.enModId = RK_ID_AENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32AencChn;
    s32Ret = RK_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_UnBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].s32Chn = 0;
    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].enChnType = MPP_CHN_INVALID;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].s32Chn = 0;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].enChnType = MPP_CHN_INVALID;
#endif   
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 查询通道绑定信息
 * 输入参数: 无
 * 输出参数: pstBindInfo --- 通道绑定信息指针
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_QueryBindInfo(MPP_BIND_INFO_S *pstBindInfo)
{
    if (NULL == pstBindInfo)
    {
        return ERR_NULL_PTR;
    }

    memcpy(pstBindInfo, &m_stSysInfo.stBindInfo, sizeof(MPP_BIND_INFO_S));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 加锁音视频编码回调互斥锁
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_CallbackLock()
{
    sint32 s32Ret = 0;
    
    s32Ret = pthread_mutex_lock(&m_stSysInfo.callbackLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解锁音视频编码回调互斥锁
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_CallbackUnlock()
{
    sint32 s32Ret = 0;

    s32Ret = pthread_mutex_unlock(&m_stSysInfo.callbackLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 当前摄像头是否为夜晚模式
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 是
             SV_FALSE - 否
 * 注意    : 无
 *****************************************************************************/
SV_BOOL mpp_sys_IsNightMode()
{
    return m_stSysInfo.bNightMode;
}

/******************************************************************************
 * 函数功能: 更新IRCUT状态
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void mpp_sys_UpdateIRCUT_Status(uint32 u32IRcutMode)
{
    print_level(SV_INFO, "u32IRcutMode:%d\n", u32IRcutMode);

    /* DMS定焦相机强制设置为黑夜模式 */
    if ((BOARD_ADA47V1_V2 == BOARD_GetVersion() && (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_ADA47V1_G_6M)))
        ||(BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M)&& BOARD_GetVersion() == BOARD_ADA32N1_V2))
    {
        m_stSysInfo.u32IRcutMode = 2;
    }
    else
    {
        m_stSysInfo.u32IRcutMode = u32IRcutMode;
    }
    
    m_stSysInfo.bUpdataIRcutMode = SV_TRUE;
}

/******************************************************************************
 * 函数功能: 更新Led亮度
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void mpp_sys_UpdateLED_Status(uint32 u32LedBright)
{
    m_stSysInfo.u32LedBright = u32LedBright;
}

