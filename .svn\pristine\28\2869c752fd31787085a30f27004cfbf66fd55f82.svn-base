#
#Setup common used command
#
export SHELL	= /bin/bash
export ECHO 	= /bin/echo
export RM		= /bin/rm -rf
export CP		= /bin/cp -uvp
export MV		= /bin/mv
export LN		= /bin/ln
export MKDIR	= /bin/mkdir
export FIND		= /usr/bin/find
export SVN		= /usr/bin/svn --non-interactive
export PRINTF	= /usr/bin/printf
export TOUCH	= /usr/bin/touch
export MD5SUM	= /usr/bin/md5sum
export EXIT		= exit
export PUSHD	= pushd >/dev/null
export POPD		= popd  >/dev/null
export SOURCE 	= source
export CHMOD	= /bin/chmod
export CC		= no_default_cc
export CROSS_COMPILE	= no_default_cross_compile_

export CROSS_ARM_HISI_V100_NPTL		=arm-hisiv100nptl-linux-
export CROSS_ARM_HISI_V200			=arm-hisiv200-linux-
export CROSS_ARM_HISI_V300			=arm-hisiv300-linux-
export CROSS_ARM_HISI_V500			=arm-hisiv500-linux-
export CROSS_ARM_HIMI_X100			=arm-himix100-linux-
export CROSS_ARM_HISMALL			=arm-hismall-linux-
export CROSS_ARM_SIGM_STAR			=arm-buildroot-linux-uclibcgnueabihf-
export CROSS_ARM_ROCK_CHIP			=arm-linux-gnueabihf-

ifeq ($(SV_QUIET), y)
export MAKE		= /usr/bin/make --no-print-directory
else
export MAKE		= /usr/bin/make 
endif
# vim:noet:sw=4:ts=4
