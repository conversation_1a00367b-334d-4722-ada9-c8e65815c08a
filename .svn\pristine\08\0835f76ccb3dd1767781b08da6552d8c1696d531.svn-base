/******************************************************************************
Copyright (C) 2021-2023 广州敏视数码科技有限公司版权所有.

文件名：dmm.c

日期: 2021-08-03

文件功能描述: 定义DMS算法功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <ctype.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <dirent.h>

#include "print.h"
#include "../../../include/common.h"
#include "safefunc.h"

//#include "DSM.h"
//#include "stateAnalyzer.h"
//#include "FRAnalyzer.h"

#include <opencv2/opencv.hpp>
#include "dmsAlg.h"
#include "dmsAnalyzer.h"
#include "dmsCommon.h"
using namespace dmsalg;

#include "op.h"
#include "msg.h"
#include "uuid.h"
#include "cJSON.h"
#include "board.h"
#include "utils.h"
#include "thpool.h"
#include "alarm.h"
#include "dmm.h"
#include "media.h"
#include "media_sem.h"
#include "media_shm.h"
#include "alg_sem.h"
#include "alg.h"
//#include "YOLOpostprocess.h"
#include "gpio_pwm.h"
#include "button_detect.h"

#include <list>
#include <mutex>
#include <condition_variable>
#include <thread>

/* 模型路径定义 */
#define DMM_MODEL_PATH_FD                   "%s/centerface_output.rknn"
#define DMM_MODEL_PATH_FA                   "%s/HG_output.rknn"
#define DMM_MODEL_PATH_FR                   "%s/FR_output.rknn"
#define DMM_MODEL_PATH_EYE_STATE            "%s/eyeNet_output.rknn"
#define DMM_MODEL_PATH_HELMET               "%s/helmetNet_output.rknn"
#define DMM_MODEL_PATH_MASK                 "%s/mask_output.rknn"
#define DMM_MODEL_PATH_SHELTER              "%s/RGB_256_256_CAD.rknn"
#define DMM_MODEL_PATH_MULTIFUNC            "%s/phone_drink_smoke.rknn"
#define DMM_MODEL_PATH_SEATBELT             "%s/seatbelt_output.rknn"
#define DMM_MODEL_PATH_SMOKET1              "%s/smoket1_output.rknn"
#define DMM_MODEL_PATH_SUNGLASSES           "%s/glassnet_output.rknn"
#define DMM_MODEL_PATH_GAZE                 "%s/gazeNet_output.rknn"
#define DMM_MODEL_PATH_FAS                  "%s/GRAY_256_256_FAS.rknn"
#define DMM_MODEL_PATH_PEOPLE               "%s/RGB_384_224_PERSON.rknn"

#define DMM_MODEL_UNCRYPT_PATH_FD           "%s/centerface_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_FA           "%s/HG_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_FR           "%s/FR_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_EYE_STATE    "%s/eyeNet_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_HELMET       "%s/helmetNet_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_MASK         "%s/mask_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_SHELTER      "%s/RGB_256_256_CAD_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_MULTIFUNC    "%s/phone_drink_smoke_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_SEATBELT     "%s/seatbelt_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_SMOKET1      "%s/smoket1_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_SUNGLASSES   "%s/glassnet_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_GAZE         "%s/gazeNet_output_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_FAS          "%s/GRAY_256_256_FAS_unencrypt.rknn"
#define DMM_MODEL_UNCRYPT_PATH_PEOPLE       "%s/RGB_384_224_PERSON_unencrypt.rknn"

/* DDAW报警指示灯GPIO3_B7 */
#define DMM_DDAW_ALARM_BAND         3
#define DMM_DDAW_ALARM_PIN          15
/* DDAW失效指示灯GPIO2_D0 */
#define DMM_DDAW_FAULT_BAND         2
#define DMM_DDAW_FAULT_PIN          24
#define DMM_DDAW_FAULT_ON           0
#define DMM_DDAW_FAULT_OFF          1

#define BUTTON_DEVICE    "/dev/stonkam-button"

#if defined(BOARD_DMS31V2)
/*****************************
* old alarm out --> GPIO1_B7
* new alarm out --> GPIO1_C5
******************************/
#define DMM_ALARM_PIN_BAND 1
#define DMM_ALARM_OLD_PIN  15
#define DMM_ALARM_NEW_PIN  21
#else
/*****************************
* old alarm out --> GPIO2_A6
* new alarm out --> GPIO2_A7
******************************/
#define DMM_ALARM_PIN_BAND 2
#define DMM_ALARM_OLD_PIN  6
#define DMM_ALARM_NEW_PIN  7
#endif

/* FHD685壳体DMS的alarmout引脚和A32的一致 */
#define DMM_ALARM_FHD685_PIN_BAND 3
#define DMM_ALARM_FHD685_PIN_NUM  20

/* LED灯控制口 -- SPI1_CSN0_M1 -- GPIO1_C7 */
#define DMM_ALARM_LED_BAND 1
#define DMM_ALARM_LED_PIN  23

#define DMM_SAMPLE_NUM              55              /* 校准采样点数 */
#define DMM_MAX_ANGLE               35              /* 允许中心角正向移植值 */
#define DMM_MIN_ANGLE               -35             /* 允许中心角负向移植值 */
#define DMM_IMAGE_WIDTH             1280            /* 算法图像帧宽度 */
#define DMM_IMAGE_HEIGHT            720             /* 算法图像帧高度 */
#define DMM_FACE_CAP_TIMEOUT        90000           /* CREARE客户人脸捕获超时时间 */
#define DMM_NO_FACE_TIME_MS         10000           /* CREARE客户连续无人脸时间 */
#define DMM_NO_FACE_TIME_MS_EX      5000            /* 展会专用版连续无人脸时间 */
#define DMM_AUTOCALIB_MIN_SPEED     40              /* 自动标定所需要的最小速度 */

#define DMM_DEBUG_TIME          0               /* 测试每一步的延时 */

typedef struct tagDmmKSSLevelThres_S
{
    sint32  s32KssLevel7Thres;                  /* KSS七级阈值 */
    sint32  s32KssLevel8Thres;                  /* KSS八级阈值 */
    sint32  s32KssLevel9Thres;                  /* KSS九级阈值 */
} DMM_KSS_LEVEL_THRES;

typedef struct tagDmmSyncInfo_S
{
    ALARM_TYPE_E                enAlarmType;                    /* 报警行为类型 */
    STDmsDetectResult           stDmsResult;                    /* DMS结果 */
    ALG_GPS_RES                 stGpsRes;                       /* GPS结果 */
    SV_BOOL                     bAlarm[ALARM_DMS_BUFF];         /* 是否产生报警 */
    sint32                      s32AlarmTimeMs[ALARM_DMS_BUFF]; /* 算法检测间隔时间 */
    sint32                      s32FatigueLast;                 /* Fatigue 报警后的持续时间,针对L2等级配置 */
    uint64                      u64TimeStamp;                   /* 当前帧时间戳 */
    sint64                      s64ForwardMs;                   /* 前推耗时 */
}DMM_SYNC_INFO_S;

typedef struct tagDmmDDAWInfo_S
{
    std::condition_variable cv_ddaw;                /* DDAW音频播放条件变量 */
    std::mutex              mutex_ddaw;             /* DDAW音频播放互斥锁 */
    SV_BOOL                 bPlayDDAWAlarm;         /* 是否需要播放DDAW报警音频 */
    CLICK_TYPE_E            enClickType;            /* 按键按击类型 */
    DMM_KSS_LEVEL_THRES     stKssLevelThres;        /* KSS量表等级阈值 */
    ALARM_TYPE_E            enDDAWAlarmType;        /* DDAW报警等级 */
    MSG_DDAW_ALARM_CFG      stVirDDAWAlarm;         /* 虚拟DDAW报警 */
} DMM_DDAW_INFO_S;

/* 模块控制信息 */
typedef struct tagDmmInfo_S
{
    SV_BOOL                 bKeyAuth;               /* 密钥是否有效 */
#if ALG_MUTLIT_BUFFER
    sint32                  s32MediaBufFd[3];       /* 媒体通道Media Buffer的文件描述，三buf */
#else
    sint32                  s32MediaBufFd;         /* 媒体通道Media Buffer的文件描述符，单buf */
#endif
    sint32                  s32Chn;                 /* DMS算法媒体通道号 */
    sint32                  s32PwmFd;               /* alarmout模拟输出pwm波驱动设备fd */
    DMM_RUN_E               enRunStat;              /* 算法当前运行状态 */
    DMM_RES_E               enRunResult;            /* 上次次执行(标定,注册,登陆)的结果 */
    USER_INFO_S             stUserInfo;             /* 最近一次登陆或添加的用户信息 */
    CFG_ALG_PARAM           stCfgParam;             /* 算法配置参数 */

    CDmsAlg                 *pcsDmsAlg;             /* DMS算法类 */
    CDmsAnalyzer            *pcsDmsAnalyzer;        /* DMS行为分析器 */
    CFrAnalyzer             *pcsFrAnalyzer;         /* 人脸识别分析器 */
    STCalibrateParam        stCalibrateParam;       /* 标定角度参数 */

    char                    szUserDir[64];          /* 当前注册用户目录路径 */
    DMM_SYNC_INFO_S         stDmmSyncInfo;          /* 算法双线程同步信息 */
    uint32                  u32TidAlg;              /* 算法线程ID */
    uint32                  u32TidPlay;             /* 播放音频线程ID */
    uint32                  u32TidButtonDet;        /* 检测按键线程ID */
    uint32                  u32TidPlayDDAW;         /* 播放DDAW音频线程ID */
    SV_BOOL                 bRunning;               /* 线程是否正在运行 */
    SV_BOOL                 bDetectRunning;         /* 行为检测线程是否正在运行 */
    pthread_mutex_t         mutexRunStat;           /* 算法运行状态互斥锁 */
    SV_BOOL                 bFirstFace;             /* 是否为上电后第一次出现的人脸 */
    SV_BOOL                 bNeedFaceCap;           /* 本次启动是否还需要进行人脸捕获 */
    SV_BOOL                 bStartFaceCap;          /* 本次启动是否开始进行人脸捕获 */
    SV_BOOL                 bPlayLoginAudio;        /* 是否播放登录音频 */
    SV_BOOL                 bOnlyFr;                /* 是否只进行人脸识别 */
    DUMP_DMM_S              stDumpDmmInfo;          /* dump信息 */
    DMM_DDAW_INFO_S         stDDAWInfo;             /* DDAW 相关变量*/
} DMM_INFO_S;

DMM_INFO_S m_stDmmInfo = {0};           /* 模块控制信息 */


typedef struct tagDmmConf_S
{
    sint32  s32WorkSpeed;               /* 算法工作速度 */
    sint32  s32DectectInterval;         /* 算法检测间隔 */
    bool    bIntervalChanged;           /* 算法检测间隔是否被修改 */
    SV_BOOL bDmsAlarmOut;               /* 是否使能报警输出 */
    SV_BOOL bDmsAudioEnable;            /* 是否使能报警声音 */
}DMM_CONF_S;

DMM_CONF_S m_stDmmConf[ALARM_DMS_BUFF] = {0};


#define DMM_PALY_LIST_NUM   5           /* 播放列表数量 */

/* 音频播放消息 */
typedef struct tagDmmPlayMsg
{
    ALARM_TYPE_E    enAlarmType;        /* 报警类型 */
    sint64          tSubmitTime;        /* 报警产生时间 */
} DMM_PLAY_MSG;


/* 报警音频信息 */
typedef struct tagDmmPlayInfo_S
{
    SV_BOOL                     bPlaying;               /* 是否正在播放 */
    sint64                      tLastPlay;              /* 最后一个音频播放的时间点 */
    sint64                      tPlaying;               /* 最后一个音频播放的时间点 */
    ALARM_TYPE_E                enAlarmTypePlaying;     /* 正在播放的音频类型 */
    ALARM_TYPE_E                enAlarmTypeLast;        /* 上一个播放的音频类型 */
    std::list<DMM_PLAY_MSG>     listDmmPlay;            /* 音频播放列表 */
    std::condition_variable     list_cv;                /* 音频播放列表条件变量 */
    std::mutex                  list_mutex;             /* 音频播放列表互斥锁 */
}DMM_PLAY_INFO_S;

DMM_PLAY_INFO_S m_stDmmPlayInfo = {0};

static inline void dmm_CutLineBreak(char *pszStr)
{
    char *pcTmp = NULL;

    pcTmp = strchr(pszStr, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(pszStr, '\n');
    if(NULL != pcTmp)
        *pcTmp = '\0';
}

sint64 dmm_GetTimeTickMs()
{
	struct timespec time = {0, 0};

	clock_gettime(CLOCK_MONOTONIC, &time);
	return ((sint64)time.tv_sec) * 1000 + time.tv_nsec/1000000;
}

static inline void dmm_Debug__Test_Time(char *pszFunc, sint32 s32Line, sint64 *ps64Last)
{
#if DMM_DEBUG_TIME
    sint64 s64Now = 0;
    s64Now = dmm_GetTimeTickMs();
    print_level(SV_DEBUG, "%s:%d interval: %lld...\n", pszFunc, s32Line, s64Now - *ps64Last);
    *ps64Last = s64Now;
#endif
}

SV_BOOL dmm_GetOsdShowAllTime()
{
    return BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT) ? SV_TRUE : SV_FALSE;
}


/* MS_V回调操作函数
  说明: 算法ForwardGroupDSM/ForwardGroupFR 内部将图像数据拷贝完后的回调
       用于快速释放锁
 */
inline void dmm_MS_V()
{
    MS_V(0);    // 退出临界区
    ALG_Calculate_unLock();
}

sint32 dmm_Pwm_Init(sint32 *s32PwmFd)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    char szCmd[128] = {0};
    PWM_DUTY_CYCLE_S stPwmDutyCycle = {0};

    s32Ret = BOARD_RK_SetGPIO(DMM_ALARM_PIN_BAND, DMM_ALARM_NEW_PIN, 0);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
        return s32Ret;
    }

    sprintf(szCmd, "rmmod gpio_pwm");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "exec cmd: %s failed.\n", szCmd);
    }

    stPwmDutyCycle.ton = 2500 * m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsPwmDutyCycle;	// 客户要求PWM频率为4kHz，也就是周期为250000ns
    sprintf(szCmd, "insmod /root/ko/extdrv/gpio_pwm.ko ton=%d", stPwmDutyCycle.ton);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "exec cmd: %s failed.\n", szCmd);
    }

    s32Fd = open(PWM_DEVICE_PATH, O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "can't open %s!\n", PWM_DEVICE_PATH);
        return SV_FAILURE;
    }

    *s32PwmFd = s32Fd;
    return SV_SUCCESS;
}

sint32 dmm_AlarmOut_SetLevel(uint8 u8Value)
{
    sint32 s32Ret, i = 0;
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202032) && BOARD_DMS31V2_V1 == BOARD_GetVersion())
    {
        for (i = 0; i < 2; i++)
        {
            s32Ret = ioctl(m_stDmmInfo.s32PwmFd, (u8Value == 1) ? PWM_START_WORKING : PWM_STOP_WORKING);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "pwm_ioctl %d error[%d], try again...\n", (u8Value == 1) ? _IOC_NR(PWM_START_WORKING) :_IOC_NR(PWM_STOP_WORKING), s32Ret);
                continue;
            }
            else
            {
                break;
            }
        }
        if (i >= 2)
        {
            print_level(SV_ERROR, "pwm_ioctl %d error[%d]\n", (u8Value == 1) ? _IOC_NR(PWM_START_WORKING) :_IOC_NR(PWM_STOP_WORKING), s32Ret);
            return s32Ret;
        }
    }
    else
    {
        if (BOARD_DMS31V2_V1 == BOARD_GetVersion())
        {
            s32Ret = BOARD_RK_SetGPIO(DMM_ALARM_PIN_BAND, DMM_ALARM_OLD_PIN, u8Value);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                return s32Ret;
            }

            s32Ret = BOARD_RK_SetGPIO(DMM_ALARM_PIN_BAND, DMM_ALARM_NEW_PIN, u8Value);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                return s32Ret;
            }
        }
        else if (BOARD_DMS31V2_V2 == BOARD_GetVersion())
        {
            s32Ret = BOARD_RK_SetGPIO(DMM_ALARM_FHD685_PIN_BAND, DMM_ALARM_FHD685_PIN_NUM, u8Value);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                return s32Ret;
            }
        }
    }
#endif

    return SV_SUCCESS;
}

/* 触发线使能 */
void dmm_Alarm_Out(void *pvArg)
{
    sint32 s32Ret;
    sint32 s32Cnt = 0;
    const sint32 s32HighLevel = 1;
    const sint32 s32LowLevel = 0;
    static uint8 u8PinValue = 0;
    static SV_BOOL bTrigger = SV_FALSE;
    static sint64 s64StartTime = 0;
    static sint64 s64NowTime = 0;
    static ALARM_TYPE_E enAlarmTypeLast = ALARM_NOTHING;
    ALARM_TYPE_E enAlarmType = *((ALARM_TYPE_E *)pvArg);

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
    {
        if (ALARM_SHELTER == enAlarmType || ALARM_SUNGLASS == enAlarmType)
        {
            print_level(SV_INFO, "fault type: %d!!!\n", enAlarmType);
            s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_ON);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
            }
            enAlarmTypeLast = enAlarmType;
            goto exit;
        }
        else if (ALARM_SHELTER == enAlarmTypeLast || ALARM_SUNGLASS == enAlarmTypeLast)
        {
            print_level(SV_INFO, "no fault, reset normal!!! %d\n", enAlarmType);
            s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_OFF);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
            }
        }
        enAlarmTypeLast = enAlarmType;
    }

    if (SV_FALSE == m_stDmmConf[enAlarmType].bDmsAlarmOut)
    {
        goto exit;
    }
    //print_level(SV_INFO, "alarm out enAlarmType: %d, bTrigger: %d\n", enAlarmType, bTrigger);

    if (bTrigger)
    {
        s64StartTime = dmm_GetTimeTickMs();
        pthread_exit(NULL);
        goto exit;
    }

    bTrigger = SV_TRUE;
    s64StartTime = dmm_GetTimeTickMs();
    s64NowTime = s64StartTime;

    dmm_AlarmOut_SetLevel(s32HighLevel);
    while (s64NowTime - s64StartTime < m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsAlarmOutInterval)
    {
        sleep_ms(10);
        s64NowTime = dmm_GetTimeTickMs();
        if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
        {
            if (s32Cnt++ % 25 == 0)
            {
                u8PinValue = !u8PinValue;
                BOARD_RK_SetGPIO(DMM_ALARM_LED_BAND, DMM_ALARM_LED_PIN, u8PinValue);
            }
        }
    }
    dmm_AlarmOut_SetLevel(s32LowLevel);
    bTrigger = SV_FALSE;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        s32Cnt = 0;
        u8PinValue = 0;
        BOARD_RK_SetGPIO(DMM_ALARM_LED_BAND, DMM_ALARM_LED_PIN, u8PinValue);
    }

exit:
    return;
}

sint32 dmm_Alarm_Post(ALARM_TYPE_E enAlarmType)
{
    sint32 s32Ret = 0;
    static ALARM_TYPE_E enAlarmTypePost = ALARM_NOTHING;
    enAlarmTypePost = enAlarmType;

    if (m_stDmmInfo.stDDAWInfo.enClickType == CLICK_TYPE_DOUBLE)
    {
        print_level(SV_WARN, "double click status, skip alarm out!\n");
        return SV_SUCCESS;
    }

    pthread_t thread_trigger;
    pthread_attr_t 	attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    s32Ret = pthread_create(&thread_trigger, &attr, dmm_Alarm_Out, (void *)&enAlarmTypePost);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create  failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
    pthread_attr_destroy(&attr);

    return SV_SUCCESS;
}

/* 提交报警事件给到control和http */
sint32 dmm_Alarm_Submit(ALARM_EVENT_S stAlarmEvent)
{
    sint32 s32Ret, i = 0;
    MSG_PACKET_S stMsgPkt = {0};
    struct timeval tvAlarm;
    struct timezone tz;
    DUMP_GPS_S stGpsInfo = {0};
    DUMP_GPS_S *pstGpsInfo = &stGpsInfo;
    uuid_t au8AlarmUuid;
	char szAlarmUuid[64] = {0};

    gettimeofday(&tvAlarm, &tz);
    stAlarmEvent.s64TimeStamp = (sint64)tvAlarm.tv_sec;
    stAlarmEvent.u16UsrId = atoi(m_stDmmInfo.stDumpDmmInfo.u8UsrId);
    strcpy(stAlarmEvent.u8UsrName, m_stDmmInfo.stDumpDmmInfo.u8UsrName);
    s32Ret = dump_GetGPSInfo(&pstGpsInfo);
    if (SV_SUCCESS == s32Ret)
    {
        stAlarmEvent.stGpsInfo = stGpsInfo;
    }

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        uuid_generate(au8AlarmUuid);
        uuid_unparse(au8AlarmUuid, szAlarmUuid);
        if (0 != strlen(szAlarmUuid))
        {
            strcpy(stAlarmEvent.u8AlarmUuid, szAlarmUuid);
        }
    }

    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    stMsgPkt.stMsg.u16OpCode = OP_EVENT_ALG_ALARM;
    stMsgPkt.pu8Data = (uint8 *)&stAlarmEvent;
    stMsgPkt.u32Size = sizeof(stAlarmEvent);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_ALG_ALARM, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = Msg_submitEvent(EP_HTTPSERVER, OP_EVENT_ALG_ALARM, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/* 提交眼睑闭合度数据给到control */
sint32 dmm_SubmitEyelid(DMM_EYELID_S stDmmEyelid)
{
    sint32 s32Ret, i = 0;
    MSG_PACKET_S stMsgPkt = {0};
    struct timeval tvTime;
    struct timezone tz;
    DUMP_GPS_S stGpsInfo = {0};
    DUMP_GPS_S *pstGpsInfo = &stGpsInfo;
    uuid_t au8AlarmUuid;
	char szAlarmUuid[64] = {0};
	static sint64 s64TsLast = 0;
	sint64 s64Interval = 0;

    if (0 != s64TsLast)
    {
        s64Interval = stDmmEyelid.u64TimeStamp - s64TsLast;
    }
    stDmmEyelid.s64Interval = s64Interval;

    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    stMsgPkt.stMsg.u16OpCode = OP_EVENT_DMM_EYELID_DATA;
    stMsgPkt.pu8Data = (uint8 *)&stDmmEyelid;
    stMsgPkt.u32Size = sizeof(stDmmEyelid);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_DMM_EYELID_DATA, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    s64TsLast = stDmmEyelid.u64TimeStamp;

    return SV_SUCCESS;
}


sint32 dmm_DumpInfo(DUMP_DMM_S *pstDumpDmmInfo)
{
    sint32 s32Ret = -1, s32Fd = -1, i = 0;
    char szBuf[DUMP_BUF_SIZE] = {0};
    cJSON *pstJson = NULL;
    cJSON *pstUsrId = NULL;
    cJSON *pstFrUserName = NULL;
    cJSON *pstHeadPos = NULL;
    cJSON *pstGaze = NULL;
    cJSON *pstEyeScore = NULL;
    cJSON *pstGlassScore = NULL;
    cJSON *pstTmp = NULL;
    uint8  u8EyeScore[4][24] = {"openEyeLeft", "closeEyeLeft", "openEyeRight", "closeEyeRight"};
    uint8  u8GlassScore[2][24] = {"normalglass", "sunglass"};
    uint8  u8HeadPose[3][24] = {"pitch", "yaw", "roll"};
    uint8  u8Gaze[2][24] = {"pitch", "yaw"};
    uint8  u8UsrId[12] = {0};

    if (NULL == pstDumpDmmInfo)
    {
        return SV_FAILURE;
    }

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        goto error_exit;
    }

    pstTmp = cJSON_CreateNumber(pstDumpDmmInfo->enAlarmType);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber %d failed.\n", pstDumpDmmInfo->enAlarmType);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "alarmType", pstTmp);

    pstTmp = cJSON_CreateBool(pstDumpDmmInfo->bImageMirror);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateBool %d failed.\n", pstDumpDmmInfo->bImageMirror);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "bImageMirror", pstTmp);

    pstTmp = cJSON_CreateBool(pstDumpDmmInfo->bDetectFace);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateBool %d failed.\n", pstDumpDmmInfo->bDetectFace);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "bDetectFace", pstTmp);

    pstTmp = cJSON_CreateBool(pstDumpDmmInfo->bYawn);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateBool %d failed.\n", pstDumpDmmInfo->bYawn);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "bYawn", pstTmp);

    pstTmp = cJSON_CreateBool(pstDumpDmmInfo->bNoMask);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateBool %d failed.\n", pstDumpDmmInfo->bNoMask);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "bNoMask", pstTmp);

    pstTmp = cJSON_CreateBool(pstDumpDmmInfo->bShelter);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateBool %d failed.\n", pstDumpDmmInfo->bShelter);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "bShelter", pstTmp);

    pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8SmokeScore);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8SmokeScore);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "smokeScore", pstTmp);

    pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8PhoneScore);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8PhoneScore);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "phoneScore", pstTmp);

    pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8DrinkEatScore);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8DrinkEatScore);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "drinkEatScore", pstTmp);

    pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8SeatbeltScore);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8SeatbeltScore);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "seatbeltScore", pstTmp);

    pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8HelmetScore);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8HelmetScore);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "helmetScore", pstTmp);

    pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8Ecr[0]);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8Ecr[0]);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "ecrLeft", pstTmp);

    pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8Ecr[1]);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8Ecr[1]);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "ecrRight", pstTmp);

    pstEyeScore = cJSON_CreateObject();
    if (NULL == pstEyeScore)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "eyeScore", pstEyeScore);
    for (i = 0; i < 4; i++)
    {
        pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8EyeScore[i]);
        if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8EyeScore[i]);
            goto error_exit;
        }
        cJSON_AddItemToObject(pstEyeScore, u8EyeScore[i], pstTmp);
    }

    pstGlassScore = cJSON_CreateObject();
    if (NULL == pstGlassScore)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "glassScore", pstGlassScore);
    for (i = 0; i < 2; i++)
    {
        pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8GlassScore[i]);
        if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8GlassScore[i]);
            goto error_exit;
        }
        cJSON_AddItemToObject(pstGlassScore, u8GlassScore[i], pstTmp);
    }

    pstHeadPos = cJSON_CreateObject();
    if (NULL == pstHeadPos)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "headPos", pstHeadPos);
    for (i = 0; i < 3; i++)
    {
        pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8HeadPose[i]);
        if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8HeadPose[i]);
            goto error_exit;
        }
        cJSON_AddItemToObject(pstHeadPos, u8HeadPose[i], pstTmp);
    }

    pstGaze = cJSON_CreateObject();
    if (NULL == pstGaze)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "gazePos", pstGaze);
    for (i = 0; i < 2; i++)
    {
        pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8Gaze[i]);
        if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8Gaze[i]);
            goto error_exit;
        }
        cJSON_AddItemToObject(pstGaze, u8Gaze[i], pstTmp);
    }

    pstUsrId = cJSON_CreateString(pstDumpDmmInfo->u8UsrId);
    if (NULL == pstUsrId)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8UsrId);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "usrId", pstUsrId);

    pstFrUserName = cJSON_CreateString(pstDumpDmmInfo->u8UsrName);
    if (NULL == pstFrUserName)
    {
        print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8UsrName);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "usrName", pstFrUserName);

    memset(szBuf, 0, DUMP_BUF_SIZE);
    cJSON_PrintPreallocated(pstJson, szBuf, DUMP_BUF_SIZE, 0);
    s32Fd = open("/var/info/dmm-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        goto error_exit;
    }

    s32Ret = write(s32Fd, szBuf, strlen(szBuf)+1);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        close(s32Fd);
        goto error_exit;
    }

    close(s32Fd);
    rename("/var/info/dmm-tmp", DUMP_INFO_DMM);

    cJSON_Delete(pstJson);
    return SV_SUCCESS;

error_exit:
    if (NULL != pstJson)
    {
        cJSON_Delete(pstJson);
    }
    return SV_FAILURE;
}

sint32 dmm_DumpInfo_Mini(DUMP_DMM_S *pstDumpDmmInfo)
{
    sint32 s32Ret = -1, s32Fd = -1, i = 0;
    char szBuf[DUMP_BUF_SIZE] = {0};
    cJSON *pstJson = NULL;
    cJSON *pstUsrId = NULL;
    cJSON *pstFrUserName = NULL;
    cJSON *pstHeadPos = NULL;
    cJSON *pstGaze = NULL;
    cJSON *pstEyeScore = NULL;
    cJSON *pstGlassScore = NULL;
    cJSON *pstTmp = NULL;
    uint8  u8EyeScore[2][24] = {"openEye", "closeEye"};
    uint8  u8GlassScore[2][24] = {"normalglass", "sunglass"};
    uint8  u8HeadPose[3][24] = {"pitch", "yaw", "roll"};
    uint8  u8Gaze[2][24] = {"pitch", "yaw"};

    if (NULL == pstDumpDmmInfo)
    {
        return SV_FAILURE;
    }

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        goto error_exit;
    }

    pstTmp = cJSON_CreateBool(pstDumpDmmInfo->bImageMirror);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateBool %d failed.\n", pstDumpDmmInfo->bImageMirror);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "bImageMirror", pstTmp);

    pstTmp = cJSON_CreateBool(pstDumpDmmInfo->bDetectFace);
    if (NULL == pstTmp)
    {
        print_level(SV_ERROR, "cJSON_CreateBool %d failed.\n", pstDumpDmmInfo->bDetectFace);
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "bDetectFace", pstTmp);

    pstHeadPos = cJSON_CreateObject();
    if (NULL == pstHeadPos)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        goto error_exit;
    }
    cJSON_AddItemToObject(pstJson, "headPos", pstHeadPos);
    for (i = 0; i < 3; i++)
    {
        pstTmp = cJSON_CreateString(pstDumpDmmInfo->u8HeadPose[i]);
        if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "cJSON_CreateString %s failed.\n", pstDumpDmmInfo->u8HeadPose[i]);
            goto error_exit;
        }
        cJSON_AddItemToObject(pstHeadPos, u8HeadPose[i], pstTmp);
    }

    memset(szBuf, 0, DUMP_BUF_SIZE);
    cJSON_PrintPreallocated(pstJson, szBuf, DUMP_BUF_SIZE, 0);
    s32Fd = open("/var/info/dmm-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        goto error_exit;
    }

    s32Ret = write(s32Fd, szBuf, strlen(szBuf)+1);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        close(s32Fd);
        goto error_exit;
    }

    close(s32Fd);
    rename("/var/info/dmm-tmp", DUMP_INFO_DMM);

    cJSON_Delete(pstJson);
    return SV_SUCCESS;

error_exit:
    if (NULL != pstJson)
    {
        cJSON_Delete(pstJson);
    }
    return SV_FAILURE;
}

void dmm_HandleDumpInfo(void *pvArg)
{
    sint32 s32Ret = 0;
    DUMP_DMM_S *pstDumpDmmInfo = (DUMP_DMM_S *)pvArg;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_GBS))
    {
        s32Ret = dmm_DumpInfo_Mini(pstDumpDmmInfo);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "dmm_DumpInfo failed!\n");
        }
    }
    else
    {
        s32Ret = dmm_DumpInfo(pstDumpDmmInfo);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "dmm_DumpInfo failed!\n");
        }
    }

    return;
}

sint32 dmm_DumpInfoThread(DUMP_DMM_S *pstDumpInfo)
{
    sint32 s32Ret;
    static DUMP_DMM_S stDumpDmmInfo = {0};
    memcpy(&stDumpDmmInfo, pstDumpInfo, sizeof(DUMP_DMM_S));

    pthread_t thread_trigger;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    s32Ret = pthread_create(&thread_trigger, &attr, dmm_HandleDumpInfo, (void *)&stDumpDmmInfo);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create  failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
    pthread_attr_destroy(&attr);

    return SV_SUCCESS;
}

/* 报警OSD */
sint32 dmm_PostDmmGui(MEDIA_GUI_ALARM_DMM_S *pstGuiAlarmDmm)
{
    sint32 s32Ret = 0;
    uint16 u16mask;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_NULL_S stGuiNull;

    if (NULL == pstGuiAlarmDmm)
    {
        return SV_FAILURE;
    }

    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
    stMsgPkt.u32Size = sizeof(MEDIA_GUI_DRAW_S);
    u16mask = MEDIA_GUI_GET_MASK(m_stDmmInfo.s32Chn, 1, MEDIA_GUI_OP_CLEAR);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }

    u16mask = MEDIA_GUI_GET_MASK(m_stDmmInfo.s32Chn, 1, MEDIA_GUI_OP_ALARM_DMM);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, *pstGuiAlarmDmm);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 向 Control 发送进度条
 * 输入参数: string --- 进度条的字符串说明
             fprocess --- 进度条进度
             bClose --- 是否关闭进度条
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 无
 ******************************************************************************/
sint32 dmm_PostProcessBar(char *string, float fprocess, SV_BOOL bClose)
{
    sint32 s32Ret;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_PROCESS_BAR_S stGuiProcessBar = {0};
    MEDIA_GUI_NULL_S stGuiNull;
    uint16 u16mask;

    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    memset(&stMediaGuiDraw, 0, sizeof(stMediaGuiDraw));
    stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
    stMsgPkt.u32Size = sizeof(MEDIA_GUI_DRAW_S);

    u16mask = MEDIA_GUI_GET_MASK(m_stDmmInfo.s32Chn, 1, MEDIA_GUI_OP_CLEAR);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }

    if(!bClose && string != NULL)
    {
        strcpy(stGuiProcessBar.string, string);
        stGuiProcessBar.fprocess = fprocess;
        stGuiProcessBar.bShowAllTime = dmm_GetOsdShowAllTime();

        u16mask = MEDIA_GUI_GET_MASK(m_stDmmInfo.s32Chn, 1, MEDIA_GUI_OP_PROCESS_BAR);
        s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiProcessBar);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
        }
    }

    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 向 Control 发送自动标定结果
 * 输入参数: pstCalibRes --- 自动标定结果
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 无
 ******************************************************************************/
sint32 dmm_PostAutoCalibration(STCalibrateResult *pstCalibRes)
{
    sint32 s32Ret;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_AUTO_CALIB_S stGuiAutoCalib = {0};
    MEDIA_GUI_NULL_S stGuiNull;
    uint16 u16mask;
    static SV_BOOL bCaliSleepLast = SV_FALSE;
    static float fProcessLast = -1;
    static sint8 s8CalibStateLast = -1;
    static sint32 s32Cnt = 0;
    SV_BOOL bSubmitPrint = SV_FALSE;

    if (E_DMS_CALIBRATE_UPDATE == pstCalibRes->eCalibrateState
        || E_DMS_CALIBRATE_FAIL == pstCalibRes->eCalibrateState
        || (E_DMS_FIRST_CALIBRATING == s8CalibStateLast && E_DMS_CALIBRATE_SUCCESS == pstCalibRes->eCalibrateState))
    {
        bSubmitPrint = SV_TRUE;
        goto submit_osd;
    }
    else
    {
        if ((bCaliSleepLast && pstCalibRes->bCaliSleep)
            || (E_DMS_FIRST_CALIBRATING == pstCalibRes->eCalibrateState && fProcessLast == pstCalibRes->fCalibrateProgress)
            || (E_DMS_CALIBRATE_SUCCESS == s8CalibStateLast && E_DMS_CALIBRATE_SUCCESS == pstCalibRes->eCalibrateState)
            || (m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsCalibrated && E_DMS_CALIBRATE_SUCCESS == pstCalibRes->eCalibrateState))
        {
            return SV_SUCCESS;
        }
    }

submit_osd:
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    memset(&stMediaGuiDraw, 0, sizeof(stMediaGuiDraw));
    stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
    stMsgPkt.u32Size = sizeof(MEDIA_GUI_DRAW_S);

    u16mask = MEDIA_GUI_GET_MASK(m_stDmmInfo.s32Chn, 1, MEDIA_GUI_OP_CLEAR);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }

    stGuiAutoCalib.fprocess = pstCalibRes->fCalibrateProgress;
    stGuiAutoCalib.u8CaliState = pstCalibRes->eCalibrateState;
    stGuiAutoCalib.u8ReasonCode = pstCalibRes->eCalibrateFailReason;
    stGuiAutoCalib.bShowAllTime = dmm_GetOsdShowAllTime();
    stGuiAutoCalib.bCaliSleep = (SV_BOOL)pstCalibRes->bCaliSleep;

    u16mask = MEDIA_GUI_GET_MASK(m_stDmmInfo.s32Chn, 1, MEDIA_GUI_OP_AUTO_CALIBRATION);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiAutoCalib);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
    }

    if (bSubmitPrint || (0 == s32Cnt++ % 30) || (!bCaliSleepLast && pstCalibRes->bCaliSleep))
    {
        print_level(SV_INFO, "state: %d, reason: %d, progress: %f, bCaliSleep: %d\n", pstCalibRes->eCalibrateState, \
            pstCalibRes->eCalibrateFailReason, pstCalibRes->fCalibrateProgress, pstCalibRes->bCaliSleep);
    }

    bCaliSleepLast = stGuiAutoCalib.bCaliSleep;
    s8CalibStateLast = pstCalibRes->eCalibrateState;
    fProcessLast = pstCalibRes->fCalibrateProgress;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取GPS信息数据
 * 输入参数: 无
 * 输出参数: pszMcuPort -- mcu对应的设备文件
 * 返回值  : SV_FAILURE -- 失败
 			 SV_SUCCESS -- 成功
 * 注意      : 无
 *****************************************************************************/
sint32 dmm_GetGpsResults(sint32 &s32status, sint32 &s32speed)
{
    sint32 s32Ret;
    char *tmpBuf = NULL;
    char szCmd[32] = {0};
    char szBuf[256] = {0};
    char szMcuPort[32] = {0};
    char szJsonRet[1024] = {0};
    cJSON *pstJson = NULL, *pstStatus = NULL, *pstSpeed = NULL;

    s32Ret = cJSON_GetJson(GPS_DUMP_INFO_FILE, szJsonRet);
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }

    pstJson = cJSON_Parse(szJsonRet);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return SV_FAILURE;
    }

    pstStatus = cJSON_GetObjectItemCaseSensitive(pstJson, "Status");
    if (NULL == pstStatus)
    {
        print_level(SV_ERROR, "keyword Status is not exist.\n");
        return SV_FAILURE;
    }
    s32status = pstStatus->valueint;

    pstSpeed = cJSON_GetObjectItemCaseSensitive(pstJson, "spk");
    if (NULL == pstSpeed)
    {
        print_level(SV_ERROR, "keyword spk is not exist.\n");
        return SV_FAILURE;
    }
    s32speed = pstSpeed->valueint;


    cJSON_Delete(pstJson);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 计算角度平均值
 * 输入参数: ps32Angles --- 角度采样集指针
             u32Num --- 采样集数
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 将角度集按10度间隔分15类，取出现类别最频繁的点类前30个点的角度平均值
 *****************************************************************************/
sint32 dmm_CalcAnglesAverage(sint32 *pas32Angles, uint32 u32Num)
{
    /* 角度分类 */
    typedef struct tag_AngleClass_S
    {
        sint32 s32ClassAngle;                   /* 属于哪个角度类(-70 ~ 0 ~ 70) */
        uint32 u32PointNum;                     /* 该类的点集数 */
    } DMM_ANG_CLASS;

    sint32 i, j;
    sint32 s32Class;
    sint32 s32AngleSum = 0, s32AngleNum = 0;
    DMM_ANG_CLASS stAngleTmp;
    DMM_ANG_CLASS astAngleClass[15] = {{-70, 0}, {-60, 0}, {-50, 0}, {-40, 0}, {-30, 0}, {-20, 0}, {-10, 0}, \
                                       {0, 0}, {10, 0}, {20, 0}, {30, 0}, {40, 0}, {50, 0}, {60, 0}, {70, 0}};
    /* 统计各类出现点数 */
    for (i = 0; i < u32Num; i++)
    {
        s32Class = pas32Angles[i];
        s32Class += (s32Class > 0) ? 5 : -5;
        s32Class = s32Class / 10 * 10;
        //print_level(SV_DEBUG, "%d->%d\n", pas32Angles[i], s32Class);
        for (j = 0; j < 15; j++)
        {
            if (astAngleClass[j].s32ClassAngle == s32Class)
            {
                astAngleClass[j].u32PointNum++;
                break;
            }
        }
    }

    /* 按各类点数由高到低排序 */
    for (i = 0; i < 14; i++)
    {
        for (j = 0; j < 14-i; j++)
        {
            if (astAngleClass[j].u32PointNum < astAngleClass[j+1].u32PointNum)
            {
                stAngleTmp = astAngleClass[j];
                astAngleClass[j] = astAngleClass[j+1];
                astAngleClass[j+1] = stAngleTmp;
            }
        }
    }


    /* 计算类别最频繁的点类前30个点的角度平均值 */
    for (i = 0; i < 15; i++)
    {
        //print_level(SV_DEBUG, "%d->%d\n", astAngleClass[i].s32ClassAngle, astAngleClass[i].u32PointNum);
        for (j = 0; j < u32Num; j++)
        {
            s32Class = pas32Angles[j];
            s32Class += (s32Class > 0) ? 5 : -5;
            s32Class = s32Class / 10 * 10;
            if (astAngleClass[i].s32ClassAngle == s32Class)
            {
                s32AngleSum += pas32Angles[j];
                s32AngleNum++;
                if (s32AngleNum >= 30)
                {
                    break;
                }
            }
        }

        if (s32AngleNum >= 30)
        {
            break;
        }
    }

    if (s32AngleNum == 0)
    {
        return -1000;
    }

    return s32AngleSum / s32AngleNum;
}


/******************************************************************************
 * 函数功能: 计算视线方向投影在图像的终点
 * 输入参数: fPointStartX --- 投影方向起点x坐标
             fPointStartY --- 投影方向起点y坐标
             fPitch --- 视线方向俯仰角，单位为角度
             fYaw --- 视线方向水平角，单位为角度
 * 输出参数: fPointEndX --- 投影方向终点x坐标
             fPointEndY，投影方向终点y坐标
 * 返回值  : 无
 * 说明    : 用于计算坐标转换的视线旋转角度
 *****************************************************************************/
void dmm_GazeVecotrCompute(float fPointStartX, float fPointStartY, float fPitch, float fYaw, float& fPointEndX, float& fPointEndY)
{
    int32_t s32VectorLength = 112;
    fPointEndX = ((fPointStartX*1280.0) - s32VectorLength * sin(fYaw * (M_PI / 180.0)) * cos(fPitch * (M_PI / 180.0))) / 1280.0;
    fPointEndY = ((fPointStartY*720.0) - s32VectorLength * sin(fPitch * (M_PI / 180.0))) / 720.0;
}

sint32 dmm_Sem_Init()
{
    sint32 s32Ret = 0;

    s32Ret = AS_Init();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "AS_Init failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    if (AS_GetValue_Forward() <= 0)
    {
        AS_Forward_V();
    }

    return SV_SUCCESS;
}

sint32 dmm_Shm_Init()
{
    sint32 s32Ret = 0;

    MS_Fini();
#if ALG_MUTLIT_BUFFER
    MH_Fini();
#endif

    s32Ret = MS_Init();
    if(s32Ret != NULL)
    {
        return SV_FAILURE;
    }

#if ALG_MUTLIT_BUFFER
    s32Ret = MH_Init();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Init failed!\n");
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

sint32 dmm_get_readIdx(sint32 s32Chn, uint64 *pu64Pts)
{
    sint32 s32Ret, s32Idx = -1;
    uint64 u64Pts = 0, u64PtsTmp = 0;
    int try_time = 30;
    int i;
    static uint64 u64PtsLast = 0;

    if (m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
    {
        try_time = 60;
    }

    while (try_time--)
    {
        /* P操作进入MediaBuffer临界区 */
        s32Ret = MS_P(s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MS_P failed. [err=%d]\n", s32Ret);
            return s32Idx;
        }

        for (i = 0; i < ALG_MULTI_BUF_NUM; i++)
        {
            if (MH_IsRead(s32Chn, i))
            {
                MH_GetPts(s32Chn, i, &u64PtsTmp);
                //print_level(SV_INFO, "s32Idx: %d, u64PtsTmp: %lld\n", i, u64PtsTmp);

                if (0 == u64Pts)
                {
                    u64Pts = u64PtsTmp;
                    s32Idx = i;
                }
                else if (u64PtsTmp > u64Pts)
                {
                    //print_level(SV_INFO, "s32Idx: %d, u64PtsTmp: %lld, u64Pts: %lld\n", i, u64PtsTmp, u64Pts);
                    u64Pts = u64PtsTmp;
                    s32Idx = i;
                }

                if (0 != u64PtsLast && u64Pts < u64PtsLast)
                {
                    //print_level(SV_INFO, "timestamp fallback, s32Idx: %d, u64Pts: %lld, u64PtsLast: %lld\n", s32Idx, u64Pts, u64PtsLast);
                    u64Pts = 0;
                    s32Idx = -1;
                }
            }
        }

        if (s32Idx >= 0)
        {
            MH_SetRead(s32Chn, s32Idx);
            MH_GetPts(s32Chn, s32Idx, &u64Pts);
            *pu64Pts = u64Pts;
            MS_V(s32Chn);
            break;
        }
        else
        {
            MS_V(s32Chn);
            sleep_ms(1);
            //print_level(SV_WARN, "not readable! %d\n", try_time);
            //MH_PrintState(s32Chn);
        }
    }

    //print_level(SV_INFO, "s32Idx: %d, pts: %lld, interval: %lld\n", s32Idx, *pu64Pts, *pu64Pts - u64PtsLast);
    u64PtsLast = *pu64Pts;

    return s32Idx;
}

sint32 dmm_release_readIdx(sint32 s32Chn, sint32 s32Idx)
{
    sint32 s32Ret;
    /* P操作进入MediaBuffer临界区 */
    s32Ret = MS_P(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MS_P failed. [err=%d]\n", s32Ret);
        return s32Idx;
    }

    s32Ret = MH_SetWrite(s32Chn, s32Idx);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_SetWrite failed. [err=%d]\n", s32Ret);
        MS_V(s32Chn);
        return s32Ret;
    }

    MS_V(s32Chn);
    return s32Ret;
}

sint32 dmm_mmap(void **apvBuf)
{
    sint32 s32Ret, i = 0;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;

#if ALG_MUTLIT_BUFFER
    uint32 u32BufCnt = ALG_MULTI_BUF_NUM;
    if (0)
    {
        u32BufCnt = 1;
    }

    for (i = 0; i < u32BufCnt; i++)
    {
        apvBuf[i] = mmap(NULL, u32BufLen, PROT_READ, MAP_SHARED, m_stDmmInfo.s32MediaBufFd[i], 0);
        if (MAP_FAILED == apvBuf[i])
        {
            print_level(SV_ERROR, "mmap failed: %d.\n", i);
            return SV_FAILURE;
        }
    }
#else
    *apvBuf = mmap(NULL, u32BufLen, PROT_READ, MAP_SHARED, m_stDmmInfo.s32MediaBufFd, 0);
    if (MAP_FAILED == *apvBuf)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

sint32 dmm_munmap(void **apvBuf)
{
    sint32 s32Ret, i = 0;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;

#if ALG_MUTLIT_BUFFER
    uint32 u32BufCnt = ALG_MULTI_BUF_NUM;
    if (0)
    {
        u32BufCnt = 1;
    }

    for (i = 0; i < u32BufCnt; i++)
    {
        if (MAP_FAILED != apvBuf[i])
        {
            munmap(apvBuf[i], u32BufLen);
            apvBuf[i] = MAP_FAILED;
        }
        else
        {
            print_level(SV_WARN, "skip munmap null apvBuf[%d]\n", i);
        }
    }
#else
    if (MAP_FAILED != *apvBuf)
    {
        munmap(*apvBuf, u32BufLen);
        *apvBuf = MAP_FAILED;
    }
#endif

    return SV_SUCCESS;
}

void *dmm_getDstBuf(void **apvBuf, sint32 s32Idx)
{
#if ALG_MUTLIT_BUFFER
    if (0)
    {
        return apvBuf[0];
    }
    else
    {
        return apvBuf[s32Idx];
    }
#else
    return *apvBuf;
#endif
}

sint32 dmm_Forward(CDmsAlg      *pcsDmsAlg, void **apvBuf, STDmsDetectResult **ppstDmmDetRes, DMM_ALG_TYPE_E enDmmAlgType)
{
    sint32 s32Ret, i = 0;

    if (NULL == pcsDmsAlg || NULL == apvBuf || NULL == ppstDmmDetRes)
    {
        print_level(SV_ERROR, "null pointer!\n");
        return SV_FAILURE;
    }

    switch (enDmmAlgType)
    {
        case DMM_ALG_TYPE_CALIBRATION_PRE:
        case DMM_ALG_TYPE_CALIBRATION:
        case DMM_ALG_TYPE_DETECTION:
        case DMM_ALG_TYPE_CHANGE_DRIVER:
            s32Ret = pcsDmsAlg->AlgModelDetectDms((uint8 **)apvBuf, ppstDmmDetRes, DMM_IMAGE_WIDTH, DMM_IMAGE_HEIGHT);
            break;

        case DMM_ALG_TYPE_REGISTER:
        case DMM_ALG_TYPE_RECOGNITION:
            s32Ret = pcsDmsAlg->AlgModelDetectFr((uint8 **)apvBuf, ppstDmmDetRes, DMM_IMAGE_WIDTH, DMM_IMAGE_HEIGHT);
            break;

        default:
            print_level(SV_ERROR, "invalid dmm alg type!\n");
            return SV_FAILURE;
    }

    return s32Ret;
}

sint32 dmm_PBuf(sint32 s32Chn, sint32 &s32Idx, uint64 *pu64Pts)
{
    sint32 s32Ret;
    sint32 s32Id = 0;
    uint64 u64Pts = 0;
    static uint64 u64PtsLast = 0;

#if ALG_MUTLIT_BUFFER
    if (0)
    {
        s32Ret = MS_P(s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            return SV_FAILURE;
        }

        #if 0
        if (MH_IsRead(s32Chn, s32Id))
        {
            MH_SetRead(s32Chn, s32Id);
            MH_GetPts(s32Chn, s32Id, &u64Pts);
            print_level(SV_INFO, "s32Idx: %d, pts: %lld, interval: %lld\n", s32Id, u64Pts, u64Pts - u64PtsLast);
            u64PtsLast = u64Pts;
        }
        #endif
    }
    else
    {
        uint64 u64Pts = 0;
        s32Idx = dmm_get_readIdx(s32Chn, &u64Pts);
        if (s32Idx < 0)
        {
            //print_level(SV_ERROR, "pd_get_readIdx failed!\n");
            return SV_FAILURE;
        }
        *pu64Pts = u64Pts;
    }
#else
    /* P操作进入MediaBuffer临界区 */
    s32Ret = MS_P(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

void dmm_VBuf(sint32 s32Chn, sint32 s32Idx)
{
    sint32 s32Ret = 0;
    sint32 s32Id = 0;
#if ALG_MUTLIT_BUFFER
    if (0)
    {
        s32Ret = MH_SetWrite(s32Chn, s32Id);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "MH_SetWrite failed. [err=%d]\n", s32Ret);
            MS_V(s32Chn);
            return s32Ret;
        }
        MS_V(s32Chn);
    }
    else
    {
        dmm_release_readIdx(s32Chn, s32Idx);
    }
#else
    MS_V(s32Chn);
#endif
}

void dmm_setFuncSwitch(ALARM_TYPE_E enAlarmType, bool bSwitchState)
{
    STDMSFuncSwitch stDmsFuncSwitch = m_stDmmInfo.pcsDmsAnalyzer->GetAlgSwitch();

    switch (enAlarmType)
    {
        case ALARM_FATIGUE:
            if (stDmsFuncSwitch.bFatigueSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bFatigueSW = bSwitchState;
            break;

        case ALARM_DISTRACTION:
            if (stDmsFuncSwitch.bDistractSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bDistractSW = bSwitchState;
            break;

        case ALARM_NO_DRIVER:
            if (stDmsFuncSwitch.bNoDriverSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bNoDriverSW = bSwitchState;
            break;

        case ALARM_SMOKE:
            if (stDmsFuncSwitch.bSmokeSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bSmokeSW = bSwitchState;
            break;

        case ALARM_PHONE:
            if (stDmsFuncSwitch.bPhoneSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bPhoneSW = bSwitchState;
            break;

        case ALARM_YAWN:
            if (stDmsFuncSwitch.bYawnSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bYawnSW = bSwitchState;
            break;

        case ALARM_NO_MASK:
            if (stDmsFuncSwitch.bNoMaskSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bNoMaskSW = bSwitchState;
            break;

        case ALARM_SUNGLASS:
            if (stDmsFuncSwitch.bSunglassesSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bSunglassesSW = bSwitchState;
            break;

        case ALARM_NO_SEATBELT:
            if (stDmsFuncSwitch.bSeatbeltSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bSeatbeltSW = bSwitchState;
            break;

        case ALARM_SHELTER:
            if (stDmsFuncSwitch.bOcclusionSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bOcclusionSW = bSwitchState;
            break;

        case ALARM_FATIGUE_L2:
            if (stDmsFuncSwitch.bFatigueL2SW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bFatigueL2SW = bSwitchState;
            break;

        case ALARM_DRINK_EAT:
            if (stDmsFuncSwitch.bDrinkSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bDrinkSW = bSwitchState;
            break;

        case ALARM_NO_HELMET:
            if (stDmsFuncSwitch.bHelmetSW == bSwitchState)
            {
                goto exit;
            }
            stDmsFuncSwitch.bHelmetSW = bSwitchState;
            break;

        default:
            goto exit;
    }

    m_stDmmInfo.pcsDmsAnalyzer->SetALgSwitch(stDmsFuncSwitch);

exit:
    return;
}

/* 报警音频过滤 */
void dmm_Alarm_PlayFilter(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32TimeoutCnt = 400;
    ALARM_TYPE_E enAlarmType = *((ALARM_TYPE_E *)pvArg);
    DMM_PLAY_INFO_S *pstDmmPlayInfo = &m_stDmmPlayInfo;
    sint64 tNow = dmm_GetTimeTickMs();

    if (m_stDmmInfo.stDDAWInfo.enClickType == CLICK_TYPE_DOUBLE)
    {
        print_level(SV_WARN, "double click status, skip play audio!\n");
        goto exit;
    }

    if (pstDmmPlayInfo->listDmmPlay.size() > DMM_PALY_LIST_NUM)
    {
        print_level(SV_WARN, "playlist is full!!!!\n");
        goto exit;
    }

    if (enAlarmType == NOTIFY_LNGIN_SUCCESS)
    {
        sleep_ms(1500);
    }

    #if 0
    /* 和正在播放的音频是同一个，过滤掉 */
    if (!pstDmmPlayInfo->listDmmPlay.empty() && pstDmmPlayInfo->bPlaying && enAlarmType == pstDmmPlayInfo->enAlarmTypePlaying)
    {
        print_level(SV_WARN, "aplay is playing the same alarm%d, return\n", enAlarmType);
        return;
    }
    #endif

    /* 对于音频类型播报，短时间内连续触发的情况 */
    if (ALARM_AUD_VOICE == m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.enAudioType)
    {
        sint64 s64TimeInterLast = llabs(tNow - pstDmmPlayInfo->tLastPlay);
        sint64 s64TimeInterPlaying = llabs(tNow - pstDmmPlayInfo->tPlaying);
        if (enAlarmType == pstDmmPlayInfo->enAlarmTypeLast && s64TimeInterPlaying < 2000)
        {
            /* 短时间内同一报警连续触发，但当前播放的音频并不是这个，继续添加到列表中 */
            if (!pstDmmPlayInfo->listDmmPlay.empty() && pstDmmPlayInfo->bPlaying && enAlarmType != pstDmmPlayInfo->enAlarmTypePlaying)
            {
                print_level(SV_INFO, "another alarm%d is playing, this alarm should be played later, add to list now\n", pstDmmPlayInfo->enAlarmTypePlaying);
            }
            else
            {
                /* 短时间内同一报警连续触发，但当前播放的音频就是这一个警报的，过滤掉，避免短时间大量播放同一个音频 */
                print_level(SV_WARN, "continuous alarm%d, playing alarm%d now, skip!!! timeIntervalLast: %lld, timeInterPlaying: %lld\n", enAlarmType, pstDmmPlayInfo->enAlarmTypePlaying, s64TimeInterLast, s64TimeInterPlaying);
                return;
            }
        }
    }

    uint32 u32Cnt = 0, u32PrintCnt = 0;
    std::unique_lock<std::mutex> lock(pstDmmPlayInfo->list_mutex, std::defer_lock);
    while (!lock.try_lock())
    {
        sleep_ms(10);
        if (++u32Cnt >= s32TimeoutCnt)
        {
            print_level(SV_WARN, "alarm%d waiting for aplay timeout, return\n", enAlarmType);
            goto exit;
        }

        if (++u32PrintCnt >= 100)
        {
            u32PrintCnt = 0;
            print_level(SV_WARN, "alarm%d is waiting for alarm%d!!!\n", enAlarmType, pstDmmPlayInfo->enAlarmTypePlaying);
        }

        if (enAlarmType == pstDmmPlayInfo->enAlarmTypePlaying)
        {
            print_level(SV_WARN, "alarm%d is playing, skip this same alarm!\n", pstDmmPlayInfo->enAlarmTypePlaying);
            goto exit;
        }
    }

add:
    DMM_PLAY_MSG stDmmPlayMsg = {0};
    stDmmPlayMsg.enAlarmType = enAlarmType;
    stDmmPlayMsg.tSubmitTime = dmm_GetTimeTickMs();
    pstDmmPlayInfo->listDmmPlay.push_back(stDmmPlayMsg);
    pstDmmPlayInfo->list_cv.notify_one();

exit:
    return;
}

sint32 dmm_Alarm_PlayAudio(ALARM_TYPE_E enAlarmType)
{
    sint32 s32Ret = 0;
    static ALARM_TYPE_E enAlarmTypePlay = ALARM_NOTHING;
    enAlarmTypePlay = enAlarmType;

    pthread_t thread_trigger;
    pthread_attr_t 	attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    s32Ret = pthread_create(&thread_trigger, &attr, dmm_Alarm_PlayFilter, (void *)&enAlarmTypePlay);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create  failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
    pthread_attr_destroy(&attr);

    return SV_SUCCESS;
}

void * dmm_playAlarm_Body(void *pvArg)
{
    DMM_INFO_S *pstDmmInfo = (DMM_INFO_S *)pvArg;
    DMM_PLAY_INFO_S *pstDmmPlayInfo = &m_stDmmPlayInfo;
    DMM_PLAY_MSG *pstDmmPlayMsg = NULL;
    std::list<DMM_PLAY_MSG>::iterator list_iterator;

    while (pstDmmInfo->bRunning)
    {
        std::unique_lock<std::mutex> lock(pstDmmPlayInfo->list_mutex);
        pstDmmPlayInfo->list_cv.wait(lock, [pstDmmPlayInfo](){ return !pstDmmPlayInfo->listDmmPlay.empty(); });

        list_iterator = pstDmmPlayInfo->listDmmPlay.begin();
        pstDmmPlayMsg = &(*list_iterator);

        ALARM_EnableSpk(SV_TRUE);

        pstDmmPlayInfo->tPlaying = pstDmmPlayMsg->tSubmitTime;
        pstDmmPlayInfo->enAlarmTypePlaying = pstDmmPlayMsg->enAlarmType;
        pstDmmPlayInfo->bPlaying = SV_TRUE;

        ALARM_PlayAudio(pstDmmPlayMsg->enAlarmType);

        pstDmmPlayInfo->enAlarmTypeLast = pstDmmPlayMsg->enAlarmType;
        pstDmmPlayInfo->tLastPlay = dmm_GetTimeTickMs();

        pstDmmPlayInfo->listDmmPlay.erase(list_iterator);
        pstDmmPlayInfo->enAlarmTypePlaying = ALARM_NOTHING;
        pstDmmPlayInfo->bPlaying = SV_FALSE;

        if (pstDmmPlayInfo->listDmmPlay.empty() && !COMMON_IsAplaying())
        {
            ALARM_EnableSpk(SV_FALSE);
        }
    }

    return NULL;
}

/******************************************************************************
 * 函数功能: 安装预标定执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsAlgStatus --- 算法检测状态
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 用于动态指示当前设备预安装位置的合适程度
 *****************************************************************************/
void dmm_InitBody()
{
    sint32 s32Ret = 0, i;
    SV_BOOL bValidFd = SV_TRUE;

    while (m_stDmmInfo.bRunning)
    {
#if ALG_MUTLIT_BUFFER
        bValidFd = SV_TRUE;
        for (i = 0; i < ALG_MULTI_BUF_NUM; i++)
        {
            if (m_stDmmInfo.s32MediaBufFd[i] <= 0)
            {
                bValidFd = SV_FALSE;
                break;
            }
        }

        if (!bValidFd)
        {
            sleep_ms(200);
            continue;
        }
#else
        if (m_stDmmInfo.s32MediaBufFd < 0)
        {
            sleep_ms(200);
            continue;
        }
#endif

        if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_CREARE))
        {
            s32Ret = dmm_Alarm_PlayAudio(NOTIFY_ALGRUNNING);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_Alarm_PlayAudio failed.\n");
            }
        }
        else
        {
            /* creare播放带人脸捕获的欢迎语时延迟到算法初始化完成后再进行 */
            if (m_stDmmInfo.bStartFaceCap)
            {
                s32Ret = ALARM_PlayWelcome();
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "ALARM_PlayWelcome failed. [err=%#x]\n", s32Ret);
                }
            }
        }

        print_level(SV_INFO, "all media fd is valid, enter to dection\n");
        m_stDmmInfo.enRunStat = DMM_RUN_DETECTION;
        if (E_LOGIN_OFF != m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.enDmsLoginMode)
        {
            m_stDmmInfo.bPlayLoginAudio = SV_TRUE;
            if (E_LOGIN_BOOT == m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.enDmsLoginMode)
            {
                m_stDmmInfo.enRunStat = DMM_RUN_RECOGNITION;
            }
        }

        if (m_stDmmInfo.bOnlyFr)
        {
            m_stDmmInfo.bPlayLoginAudio = SV_TRUE;
            m_stDmmInfo.enRunStat = DMM_RUN_RECOGNITION;
        }

        break;
    }
}

/******************************************************************************
 * 函数功能: 安装预标定执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsAlgStatus --- 算法检测状态
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 用于动态指示当前设备预安装位置的合适程度
 *****************************************************************************/
void dmm_CalibrationPreBody(DMM_INFO_S *pstDmmInfo)
{
    sint32 s32Ret = 0, i;
    sint32 s32SemChn = 0;
    cv::Rect referROi(390,145,500,375); /* x,y,width,height */
    sint32 s32PoseCenterAngle = 0;  // 头部姿势距中心角的偏移量
    sint32 s32PosePitchAngle = 0;   // 头部俯仰角
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_FACE_S stGuiFace = {0};
    MEDIA_GUI_RECT_S stGuiRect = {0};
    MEDIA_GUI_NULL_S stGuiNull;
    MEDIA_GUI_LINE_S stGuiLine = {0};
    uint16 u16mask;
#if ALG_MUTLIT_BUFFER
    void *apvBuf[3] = {NULL};
#else
    void *apvBuf = NULL;
#endif
    void *apvDstBuf = NULL;
    sint32 s32Idx;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;
    time_t tEnterTime = 0;
    DMM_RUN_E enNextRunStat = DMM_RUN_DETECTION;
    if (m_stDmmInfo.bOnlyFr)
    {
        enNextRunStat = DMM_RUN_RECOGNITION;
    }
    uint64 u64Pts = 0;

    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return ;
    }

    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   *pstDmsResult = &stDmsResult;

    s32Ret = dmm_mmap((void **)&apvBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return ;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32SemChn = 0;
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion() && BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))
    {
        s32SemChn = 0;
    }
    else
    {
        s32SemChn = 1;
    }
#else
    s32SemChn = 1;
#endif

    print_level(SV_INFO, "enter CalibrationPre.\n");
    tEnterTime = time(NULL);
    while (pstDmmInfo->bRunning)
    {
        if (pstDmmInfo->enRunStat != DMM_RUN_CALIBRATION_PRE)
        {
            print_level(SV_INFO, "exit CalibrationPre.\n");
            break;
        }

        /* 预标定状态最多保持3分钟（避免误操作无法回到检测状态）*/
        if (time(NULL) > tEnterTime + 180)
        {
            pstDmmInfo->enRunStat = enNextRunStat;
        }

        /* 锁住计算资源,保证同一时刻只跑一个算法*/
        s32Ret = ALG_Calculate_Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }

        /* P操作进入MediaBuffer临界区 */
        u64Pts = 0;
        s32Ret = dmm_PBuf(s32SemChn, s32Idx, &u64Pts);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "dmm_PBuf failed. [err=%d]\n", s32Ret);
            ALG_Calculate_unLock();
            sleep_ms(1);
            continue;
        }
        apvDstBuf = dmm_getDstBuf((void **)&apvBuf, s32Idx);

        /* 模型向前推断函数 */
        s32Ret = dmm_Forward(pstDmmInfo->pcsDmsAlg, &apvDstBuf, &pstDmsResult, DMM_ALG_TYPE_CALIBRATION_PRE);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ForwardGroupDSM failed. [err=%d]\n", s32Ret);
            dmm_VBuf(s32SemChn, s32Idx);
            ALG_Calculate_unLock();
            sleep_ms(10);
            continue;
        }
        dmm_VBuf(s32SemChn, s32Idx);
        ALG_Calculate_unLock();

        s32PoseCenterAngle = (sint32)stDmsResult.pfpHeadPose[1];
        s32PosePitchAngle = (sint32)stDmsResult.pfGaze[0];          // 从姿态俯仰角改为视线俯仰角，视线俯仰角单位是弧度，要转为角度
        print_level(SV_DEBUG, "detectFace:%d, center:%d, pitch:%d\n", stDmsResult.bDetectFace, s32PoseCenterAngle, s32PosePitchAngle);
        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
        stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
        stMsgPkt.u32Size = sizeof(MEDIA_GUI_DRAW_S);
        memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));
        u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_CLEAR);
        s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);

        if (stDmsResult.bDetectFace)
        {
            s32PoseCenterAngle = (s32PoseCenterAngle + 5) / 10 * 10;

            for (i = 0; i < 3; i++)
            {
                stGuiFace.angle[i] = (int)(stDmsResult.pfpHeadPose[i] + 2.5 * ((stDmsResult.pfpHeadPose[i] > 0) * 2 - 1))/5*5;
    			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] > 85 ? 90 : stGuiFace.angle[i];
    			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] < -85 ? -90 : stGuiFace.angle[i];
            }
            stGuiFace.stFaceRect.color = GUI_COLOR_RED;
            stGuiFace.stFaceRect.x1 = 1.0 * (float)referROi.x / 1280.0;
            stGuiFace.stFaceRect.y1 = 1.0 * (float)referROi.y / 720.0;
            stGuiFace.stFaceRect.x2 = 1.0 * (float)(referROi.x+referROi.width) / 1280.0;
            stGuiFace.stFaceRect.y2 = 1.0 * (float)(referROi.y+referROi.height) / 720.0;
            stGuiFace.u32PointNum = LANDMARK_POINT_NUM;
            for (i = 0; i < LANDMARK_POINT_NUM; i++)
            {
                stGuiFace.astFacePoints[i].x = stDmsResult.pfLandmarkPoints[i*2];
                stGuiFace.astFacePoints[i].y = stDmsResult.pfLandmarkPoints[i*2+1];
            }

            //if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
                //stGuiFace.bDisablePoint = SV_TRUE;

            float fLEyeEndPoint[2] = {0};
            float fREyeEndPoint[2] = {0};
            uint8 u8LPointNum = 26;
            uint8 u8RPointNum = 34;
            dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8LPointNum], stDmsResult.pfLandmarkPoints[u8LPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fLEyeEndPoint[0], fLEyeEndPoint[1]);
            dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8RPointNum], stDmsResult.pfLandmarkPoints[u8RPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fREyeEndPoint[0], fREyeEndPoint[1]);

            stGuiFace.stGazeLine[0].x1 = stDmsResult.pfLandmarkPoints[u8LPointNum];
            stGuiFace.stGazeLine[0].y1 = stDmsResult.pfLandmarkPoints[u8LPointNum+1];
            stGuiFace.stGazeLine[0].x2 = fLEyeEndPoint[0];
            stGuiFace.stGazeLine[0].y2 = fLEyeEndPoint[1];
            stGuiFace.stGazeLine[0].color = GUI_COLOR_BLUE;
            stGuiFace.stGazeLine[0].stick = 5;

            stGuiFace.stGazeLine[1].x1 = stDmsResult.pfLandmarkPoints[u8RPointNum];
            stGuiFace.stGazeLine[1].y1 = stDmsResult.pfLandmarkPoints[u8RPointNum+1];
            stGuiFace.stGazeLine[1].x2 = fREyeEndPoint[0];
            stGuiFace.stGazeLine[1].y2 = fREyeEndPoint[1];
            stGuiFace.stGazeLine[1].color = GUI_COLOR_BLUE;
            stGuiFace.stGazeLine[1].stick = 5;
            #if 0
            printf("line1 point1(%f, %f)---(%f, %f), point2(%f, %f)---(%f, %f)\n", stGuiFace.stGazeLine[0].x1, stGuiFace.stGazeLine[0].y1, stGuiFace.stGazeLine[0].x2, stGuiFace.stGazeLine[0].y2,
                    stGuiFace.stGazeLine[1].x1, stGuiFace.stGazeLine[1].y1, stGuiFace.stGazeLine[1].x2, stGuiFace.stGazeLine[1].y2);
            #endif
            if (SV_FALSE == pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsGazeTracking)
                stGuiFace.bDisableGaze = SV_TRUE;


            /* 图像中心参考区域与人脸区域的交叠区达到一定比例 */
            cv::Rect showFaceRoi(stDmsResult.stFaceBoundBox.fX1*1280,stDmsResult.stFaceBoundBox.fY1*720,(stDmsResult.stFaceBoundBox.fX2-stDmsResult.stFaceBoundBox.fX1)*1280,(stDmsResult.stFaceBoundBox.fY2-stDmsResult.stFaceBoundBox.fY1)*720);
            cv::Rect interRoi = referROi & showFaceRoi;
            if (interRoi.area() > (0.15*referROi.area()) && (interRoi.area() > (0.8*showFaceRoi.area())))
            {
                if (s32PoseCenterAngle <= 30 && s32PoseCenterAngle >= -30)
                {
                    stGuiFace.stFaceRect.color = GUI_COLOR_GREEN;
                }
                else
                {
                    stGuiFace.stFaceRect.color = GUI_COLOR_YELLOW;
                }
            }
            stGuiFace.bShowAllTime = dmm_GetOsdShowAllTime();
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_DRAW_FACE);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiFace);
        }
        else
        {
            stGuiRect.stick = 4;
            stGuiRect.color = GUI_COLOR_RED;
            stGuiRect.x1 = 1.0 * (float)referROi.x / 1280.0;
            stGuiRect.y1 = 1.0 * (float)referROi.y / 720.0;
            stGuiRect.x2 = 1.0 * (float)(referROi.x+referROi.width) / 1280.0;
            stGuiRect.y2 = 1.0 * (float)(referROi.y+referROi.height) / 720.0;
            stGuiRect.bShowAllTime = dmm_GetOsdShowAllTime();
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_DRAW_RECT);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiRect);
        }

        s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        }
    }

    if (pstDmmInfo->enRunStat != DMM_RUN_CALIBRATION)
    {
        memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));
        u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_CLEAR);
        s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
        s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        }
    }

    dmm_munmap((void **)&apvBuf);
}

/******************************************************************************
 * 函数功能: 安装正式标定执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsAlgStatus --- 算法检测状态
             pcsFRAnalyzer --- 人脸识别分析器
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 用于确认安装位置后,采集多点确认司机凝视角度
 *****************************************************************************/
void dmm_CalibrationBody(DMM_INFO_S *pstDmmInfo)
{
    sint32 s32Ret = 0, i;
    sint32 s32SemChn = 0;
    cv::Rect referROi(390,145,500,375);
    sint32 s32PoseCenterDeg = 0;
    sint32 ps32HeadPosAverage[2];
    sint32 ps32GazeAverage[2];
    sint32 as32HeadPosAngles[2][DMM_SAMPLE_NUM];    // 中心角采样集
    sint32 as32GazeAngles[2][DMM_SAMPLE_NUM];       // 俯仰角采样集
    uint32 u32SampleCnt = 0;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_FACE_S stGuiFace = {0};
    MEDIA_GUI_RECT_S stGuiRect = {0};
    MEDIA_GUI_NULL_S stGuiNull;
    MEDIA_GUI_LINE_S stGuiLine = {0};
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
    uint16 u16mask;
#if ALG_MUTLIT_BUFFER
    void *apvBuf[3] = {NULL};
#else
    void *apvBuf = NULL;
#endif
    void *apvDstBuf = NULL;
    sint32 s32Idx;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;
    DMM_RUN_E enNextRunStat = DMM_RUN_DETECTION;
    if (m_stDmmInfo.bOnlyFr)
    {
        enNextRunStat = DMM_RUN_RECOGNITION;
    }
    uint64 u64Pts = 0;

    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg || NULL == pstDmmInfo->pcsDmsAnalyzer || NULL == pstDmmInfo->pcsFrAnalyzer)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return ;
    }

    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   *pstDmsResult = &stDmsResult;

    s32Ret = dmm_mmap((void **)&apvBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return ;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32SemChn = 0;
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion() && BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))
    {
        s32SemChn = 0;
    }
    else
    {
        s32SemChn = 1;
    }
#else
    s32SemChn = 1;
#endif

    clock_gettime(CLOCK_MONOTONIC, &tvLast);

    print_level(SV_INFO, "enter Calibration.\n");
    dmm_Alarm_PlayAudio(NOTIFY_CALIBRATION_START);
    //sleep_ms(2000);   // 延时2s移动到下面线程体内进行计算

    while (pstDmmInfo->bRunning)
    {
        if (pstDmmInfo->enRunStat != DMM_RUN_CALIBRATION)
        {
            print_level(SV_INFO, "exit Calibration.\n");
            break;
        }

        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        if (tvNow.tv_sec - tvLast.tv_sec > 12)
        {
            print_level(SV_INFO, "Register timeout!\n");
            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = enNextRunStat;
            pstDmmInfo->enRunResult = DMM_RES_TIMEOUT;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
            continue;
        }

        /* 锁住计算资源,保证同一时刻只跑一个算法*/
        s32Ret = ALG_Calculate_Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }

        /* P操作进入MediaBuffer临界区 */
        u64Pts = 0;
        s32Ret = dmm_PBuf(s32SemChn, s32Idx, &u64Pts);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "dmm_PBuf failed. [err=%d]\n", s32Ret);
            ALG_Calculate_unLock();
            sleep_ms(1);
            continue;
        }
        apvDstBuf = dmm_getDstBuf((void **)&apvBuf, s32Idx);

        /* 模型向前推断函数 */
        s32Ret = dmm_Forward(pstDmmInfo->pcsDmsAlg, &apvDstBuf, &pstDmsResult, DMM_ALG_TYPE_CALIBRATION);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ForwardGroupDSM failed. [err=%d]\n", s32Ret);
            dmm_VBuf(s32SemChn, s32Idx);
            ALG_Calculate_unLock();
            sleep_ms(10);
            continue;
        }
        dmm_VBuf(s32SemChn, s32Idx);
        ALG_Calculate_unLock();


        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
        stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
        stMsgPkt.u32Size = sizeof(MEDIA_GUI_DRAW_S);
        memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));
        u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_CLEAR);
        s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);

        print_level(SV_DEBUG, "detectFace: %d, headPitch: %f, headYaw: %f, gazePitch: %f, gazeYaw: %f\n", stDmsResult.bDetectFace, stDmsResult.pfpHeadPose[0], stDmsResult.pfpHeadPose[1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1]);
        if (stDmsResult.bDetectFace)
        {
            s32PoseCenterDeg = stDmsResult.pfpHeadPose[1];
            s32PoseCenterDeg += (s32PoseCenterDeg > 0) ? 5 : -5;
			s32PoseCenterDeg = (s32PoseCenterDeg + 2.5 * ((s32PoseCenterDeg > 0) * 2 - 1)) / 10 * 10;

            for (i = 0; i < 3; i++)
            {
                stGuiFace.angle[i] = (int)(stDmsResult.pfpHeadPose[i] + 2.5 * ((stDmsResult.pfpHeadPose[i] > 0) * 2 - 1))/5*5;
    			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] > 85 ? 90 : stGuiFace.angle[i];
    			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] < -85 ? -90 : stGuiFace.angle[i];
            }
            stGuiFace.stFaceRect.color = GUI_COLOR_RED;
            stGuiFace.stFaceRect.x1 = 1.0 * (float)referROi.x / 1280.0;
            stGuiFace.stFaceRect.y1 = 1.0 * (float)referROi.y / 720.0;
            stGuiFace.stFaceRect.x2 = 1.0 * (float)(referROi.x+referROi.width) / 1280.0;
            stGuiFace.stFaceRect.y2 = 1.0 * (float)(referROi.y+referROi.height) / 720.0;
            stGuiFace.u32PointNum = LANDMARK_POINT_NUM;
            for (i = 0; i < LANDMARK_POINT_NUM; i++)
            {
                stGuiFace.astFacePoints[i].x = stDmsResult.pfLandmarkPoints[i*2];
                stGuiFace.astFacePoints[i].y = stDmsResult.pfLandmarkPoints[i*2+1];
            }

            //if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
                //stGuiFace.bDisablePoint = SV_TRUE;

            float fLEyeEndPoint[2] = {0};
            float fREyeEndPoint[2] = {0};
            uint8 u8LPointNum = 26;
            uint8 u8RPointNum = 34;
            dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8LPointNum], stDmsResult.pfLandmarkPoints[u8LPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fLEyeEndPoint[0], fLEyeEndPoint[1]);
            dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8RPointNum], stDmsResult.pfLandmarkPoints[u8RPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fREyeEndPoint[0], fREyeEndPoint[1]);

            stGuiFace.stGazeLine[0].x1 = stDmsResult.pfLandmarkPoints[u8LPointNum];
            stGuiFace.stGazeLine[0].y1 = stDmsResult.pfLandmarkPoints[u8LPointNum+1];
            stGuiFace.stGazeLine[0].x2 = fLEyeEndPoint[0];
            stGuiFace.stGazeLine[0].y2 = fLEyeEndPoint[1];
            stGuiFace.stGazeLine[0].color = GUI_COLOR_BLUE;
            stGuiFace.stGazeLine[0].stick = 5;

            stGuiFace.stGazeLine[1].x1 = stDmsResult.pfLandmarkPoints[u8RPointNum];
            stGuiFace.stGazeLine[1].y1 = stDmsResult.pfLandmarkPoints[u8RPointNum+1];
            stGuiFace.stGazeLine[1].x2 = fREyeEndPoint[0];
            stGuiFace.stGazeLine[1].y2 = fREyeEndPoint[1];
            stGuiFace.stGazeLine[1].color = GUI_COLOR_BLUE;
            stGuiFace.stGazeLine[1].stick = 5;
            #if 0
            printf("line1 point1(%f, %f)---(%f, %f), point2(%f, %f)---(%f, %f)\n", stGuiFace.stGazeLine[0].x1, stGuiFace.stGazeLine[0].y1, stGuiFace.stGazeLine[0].x2, stGuiFace.stGazeLine[0].y2,
                    stGuiFace.stGazeLine[1].x1, stGuiFace.stGazeLine[1].y1, stGuiFace.stGazeLine[1].x2, stGuiFace.stGazeLine[1].y2);
            #endif
            if (SV_FALSE == pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsGazeTracking)
                stGuiFace.bDisableGaze = SV_TRUE;


            /* 图像中心参考区域与人脸区域的交叠区达到一定比例 */
            cv::Rect showFaceRoi(stDmsResult.stFaceBoundBox.fX1*1280,stDmsResult.stFaceBoundBox.fY1*720,(stDmsResult.stFaceBoundBox.fX2-stDmsResult.stFaceBoundBox.fX1)*1280,(stDmsResult.stFaceBoundBox.fY2-stDmsResult.stFaceBoundBox.fY1)*720);
            cv::Rect interRoi = referROi & showFaceRoi;
            if (interRoi.area() > (0.15*referROi.area()) && (interRoi.area() > (0.8*showFaceRoi.area())))
            {
                if (-30 <= s32PoseCenterDeg && s32PoseCenterDeg <= 30)
                {
                    stGuiFace.stFaceRect.color = GUI_COLOR_GREEN;
                }
                else
                {
                    stGuiFace.stFaceRect.color = GUI_COLOR_YELLOW;
                }
            }
            stGuiFace.bShowAllTime = dmm_GetOsdShowAllTime();
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_DRAW_FACE);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiFace);
        }
        else
        {
            stGuiRect.stick = 4;
            stGuiRect.color = GUI_COLOR_RED;
            stGuiRect.x1 = 1.0 * (float)referROi.x / 1280.0;
            stGuiRect.y1 = 1.0 * (float)referROi.y / 720.0;
            stGuiRect.x2 = 1.0 * (float)(referROi.x+referROi.width) / 1280.0;
            stGuiRect.y2 = 1.0 * (float)(referROi.y+referROi.height) / 720.0;
            stGuiRect.bShowAllTime = dmm_GetOsdShowAllTime();
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_DRAW_RECT);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiRect);
        }

        s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        }

        if(stGuiFace.stFaceRect.color != GUI_COLOR_GREEN && stGuiFace.stFaceRect.color != GUI_COLOR_YELLOW)
        {
            //print_level(SV_DEBUG, "color:%#x\n", stGuiFace.stFaceRect.color);
            continue;
        }

        /****************************************************************************************************************
         *  把上面的sleep_ms(2000)移到此处计算，解决NOTIFY_CALIBRATION_START语音播放后，直接sleep导致有2s的画框消失问题
         *  2s内一直会进行画框的操作，2s后再开始统计人脸角度
         ****************************************************************************************************************/
        if (tvNow.tv_sec - tvLast.tv_sec < 2)
        {
            continue;
        }

        for (i = 0; i < 2; i++)
        {
            as32HeadPosAngles[i][u32SampleCnt] = (sint32)stDmsResult.pfpHeadPose[i];
            as32GazeAngles[i][u32SampleCnt] = (sint32)stDmsResult.pfGaze[i];
        }

        u32SampleCnt++;
        //print_level(SV_DEBUG, "u32SampleCnt:%d\n", u32SampleCnt);
        if (u32SampleCnt < DMM_SAMPLE_NUM)
        {
            continue;
        }

        for (i = 0; i < 2; i++)
        {
            ps32HeadPosAverage[i] = dmm_CalcAnglesAverage(as32HeadPosAngles[i], u32SampleCnt);
            ps32GazeAverage[i] = dmm_CalcAnglesAverage(as32GazeAngles[i], u32SampleCnt);
            print_level(SV_INFO, "ps32HeadPosAverage[%d]: %d, ps32GazeAverage[%d]: %d\n", i, ps32HeadPosAverage[i], i, ps32GazeAverage[i]);
        }
        if (ps32HeadPosAverage[1] == -1000 || ps32GazeAverage[i] == -1000)
        {
            print_level(SV_ERROR, "dmm_CalcAnglesAverage exception.\n");
            u32SampleCnt = 0;
            continue;
        }

        if (ps32HeadPosAverage[1] < DMM_MIN_ANGLE)
        {
            pstDmmInfo->stCfgParam.bImageMirror ? dmm_Alarm_PlayAudio(NOTIFY_CALIBRATION_TOORIGHT) : dmm_Alarm_PlayAudio(NOTIFY_CALIBRATION_TOOLEFT);
            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = DMM_RUN_CALIBRATION_PRE;
            pstDmmInfo->enRunResult = DMM_RES_FAILURE;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
        }
        else if (ps32HeadPosAverage[1] > DMM_MAX_ANGLE)
        {
            pstDmmInfo->stCfgParam.bImageMirror ? dmm_Alarm_PlayAudio(NOTIFY_CALIBRATION_TOOLEFT) : dmm_Alarm_PlayAudio(NOTIFY_CALIBRATION_TOORIGHT);
            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = DMM_RUN_CALIBRATION_PRE;
            pstDmmInfo->enRunResult = DMM_RES_FAILURE;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
        }
        else
        {
            s32Ret = CONFIG_ReloadFile();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "CONFIG_ReloadFile failed. [err=%#x]\n", s32Ret);
            }

            memcpy(pstDmmInfo->stCalibrateParam.ps32CalibrateHeadAngle, ps32HeadPosAverage, sizeof(pstDmmInfo->stCalibrateParam.ps32CalibrateHeadAngle));
            memcpy(pstDmmInfo->stCalibrateParam.ps32CalibrateGazeAngle, ps32GazeAverage, sizeof(pstDmmInfo->stCalibrateParam.ps32CalibrateGazeAngle));
            pstDmmInfo->pcsDmsAnalyzer->CalibrateParamChange(pstDmmInfo->stCalibrateParam);     //把标定结果回写给算法DMM
            pstDmmInfo->pcsFrAnalyzer->SetCalibrateAngle(pstDmmInfo->stCalibrateParam);         //把标定结果回写给算法FR

            pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsCalibrated = SV_TRUE;
            memcpy(pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle, ps32HeadPosAverage, sizeof(pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle));
            memcpy(pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.ps32CalibrateGazeAngle, ps32GazeAverage, sizeof(pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.ps32CalibrateGazeAngle));
            s32Ret = CONFIG_SetAlgParam(&pstDmmInfo->stCfgParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "CONFIG_SetAlgParam failed. [err=%#x]\n", s32Ret);
            }
            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = enNextRunStat;
            pstDmmInfo->enRunResult = DMM_RES_SUCCESS;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);

            dmm_Alarm_PlayAudio(NOTIFY_CALIBRATION_SUCCESS);
        }
    }

    memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));
    u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_CLEAR);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
    }

    dmm_munmap((void **)&apvBuf);
}

/******************************************************************************
 * 函数功能: 注册人脸执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsFRAnalyzer --- 人脸识别分析器
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 用于人脸识别登陆功能
 *****************************************************************************/
void dmm_RegisterBody(DMM_INFO_S *pstDmmInfo)
{
    sint32 s32Ret = 0, i;
    sint32 s32Fd = -1;
    sint32 s32SemChn = 0;
    sint32 s32Progress = 0;
    float fProgress = 0;
    MSG_PACKET_S stMsgPkt = {0};
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
#if ALG_MUTLIT_BUFFER
    void *apvBuf[3] = {NULL};
#else
    void *apvBuf = NULL;
#endif
    void *apvDstBuf = NULL;
    sint32 s32Idx;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;
    char szUserNamePath[128];
    char szUserName[1024];
    DMM_RUN_E enNextRunStat = DMM_RUN_DETECTION;
    if (pstDmmInfo->bOnlyFr)
    {
        enNextRunStat = DMM_RUN_RECOGNITION;
    }
    uint64 u64Pts = 0;

    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg || NULL == pstDmmInfo->pcsFrAnalyzer)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return ;
    }

    if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
    {
        goto skip;
    }

    STFaceIdInfo        stFaceIdInfo;
    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   *pstDmsResult = &stDmsResult;
    CFrAnalyzer         *pcsFrAnalyzer = pstDmmInfo->pcsFrAnalyzer;

    s32Ret = dmm_mmap((void **)&apvBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return ;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32SemChn = 0;
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion() && BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))
    {
        s32SemChn = 0;
    }
    else
    {
        s32SemChn = 1;
    }
#else
    s32SemChn = 1;
#endif

    print_level(SV_INFO, "enter Register.\n");
	s32Ret = dmm_PostProcessBar("REGISTER", 0.0, SV_FALSE);
	if(s32Ret != SV_SUCCESS)
	{
		print_level(SV_ERROR, "dmm_PostProcessBar failed.\n");
	}
    dmm_Alarm_PlayAudio(NOTIFY_REGISTER_START);

    clock_gettime(CLOCK_MONOTONIC, &tvLast);
    while (pstDmmInfo->bRunning)
    {
        if (pstDmmInfo->enRunStat != DMM_RUN_REGISTER)
        {
            print_level(SV_INFO, "exit Register.\n");
            break;
        }

        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        if (tvNow.tv_sec - tvLast.tv_sec > 10)
        {
            print_level(SV_INFO, "Register timeout!\n");
            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = enNextRunStat;
            pstDmmInfo->enRunResult = DMM_RES_FAILURE;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
            continue;
        }

        /* 锁住计算资源,保证同一时刻只跑一个算法*/
        s32Ret = ALG_Calculate_Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }

        /* P操作进入MediaBuffer临界区 */
        u64Pts = 0;
        s32Ret = dmm_PBuf(s32SemChn, s32Idx, &u64Pts);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "dmm_PBuf failed. [err=%d]\n", s32Ret);
            ALG_Calculate_unLock();
            sleep_ms(1);
            continue;
        }
        apvDstBuf = dmm_getDstBuf((void **)&apvBuf, s32Idx);

        /* 模型向前推断函数 */
        s32Ret = dmm_Forward(pstDmmInfo->pcsDmsAlg, &apvDstBuf, &pstDmsResult, DMM_ALG_TYPE_REGISTER);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ForwardGroupDSM failed. [err=%d]\n", s32Ret);
            dmm_VBuf(s32SemChn, s32Idx);
            ALG_Calculate_unLock();
            sleep_ms(10);
            continue;
        }
        dmm_VBuf(s32SemChn, s32Idx);
        ALG_Calculate_unLock();

        //print_level(SV_INFO, "stDmsResult.bFeatureVal: %d\n", stDmsResult.bFeatureVal);
        if (stDmsResult.bFeatureVal)
        {
            fProgress = pcsFrAnalyzer->FrRegister(stDmsResult);
            s32Progress = fProgress * 100;
            print_level(SV_INFO, "progress: %d%%\n", s32Progress);
			s32Ret = dmm_PostProcessBar("REGISTER", fProgress, SV_FALSE);
			if(s32Ret != SV_SUCCESS)
			{
				print_level(SV_ERROR, "dmm_PostProcessBar failed.\n");
			}
        }

        if (s32Progress >= 100)
        {
            print_level(SV_INFO, "Register successful.\n");

            strcpy(stDmsResult.pu8UserPath, pstDmmInfo->szUserDir);
            pcsFrAnalyzer->WriteInfo(stDmsResult);
            dmm_Alarm_PlayAudio(NOTIFY_REGISTRE_SUCCESS);

            /* 实时更新用户名字 */
            do
            {
                stFaceIdInfo = pcsFrAnalyzer->GetFrIdInfo();
                snprintf(pstDmmInfo->stUserInfo.szDir, DMM_MAX_DIRLEN, "%s/%s", FRS_USERINFOS_PATH, stFaceIdInfo.ps8UserName);
                sprintf(szUserNamePath, "%s/username", pstDmmInfo->stUserInfo.szDir);
                s32Fd = open(szUserNamePath, O_RDONLY);
                if (s32Fd < 0)
                {
                    print_level(SV_ERROR, "open file: %s failed. [err:%s]\n", szUserNamePath, strerror(errno));
                    break;
                }
                else
                {
                    s32Ret = read(s32Fd, szUserName, 1024);
                    if (s32Ret < 0)
                    {
                        print_level(SV_ERROR, "read file: %s failed. [err:%s]\n", szUserNamePath, strerror(errno));
                    }
                }
                close(s32Fd);
                dmm_CutLineBreak(szUserName);

                strcpy(pstDmmInfo->stUserInfo.szName, &szUserName[strlen("username=")]);
                strcpy(pstDmmInfo->stDumpDmmInfo.u8UsrId, stFaceIdInfo.ps8UserName+strlen("user"));
                strcpy(pstDmmInfo->stDumpDmmInfo.u8UsrName, pstDmmInfo->stUserInfo.szName);
                print_level(SV_INFO, "usrId: %s, userName: %s\n", pstDmmInfo->stDumpDmmInfo.u8UsrId, pstDmmInfo->stDumpDmmInfo.u8UsrName);
            }while (0);

            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = enNextRunStat;
            pstDmmInfo->enRunResult = DMM_RES_SUCCESS;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
        }
    }

	s32Ret = dmm_PostProcessBar("REGISTER", 0.0, SV_TRUE);
	if(s32Ret != SV_SUCCESS)
	{
		print_level(SV_ERROR, "dmm_PostProcessBar failed.\n");
	}

    dmm_munmap((void **)&apvBuf);
    return ;

skip:
    pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
    pstDmmInfo->enRunStat = enNextRunStat;
    pstDmmInfo->enRunResult = DMM_RES_SUCCESS;
    pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
    return ;
}

/******************************************************************************
 * 函数功能: 人脸识别执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsFRAnalyzer --- 人脸识别分析器
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 用于人脸识别登陆功能
 *****************************************************************************/
void dmm_RecognitionBody(DMM_INFO_S *pstDmmInfo)
{
    sint32 s32Ret = 0, i;
    sint32 s32SemChn = 0;
    uint16 warnState, u16UsrId;
    sint32 s32Fd = -1;
    sint32 s32Progress = 0;
    float fProgress = 0;
    char szUserNamePath[128];
    char szUserName[1024];
    MSG_PACKET_S stMsgPkt = {0};
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
#if ALG_MUTLIT_BUFFER
    void *apvBuf[3] = {NULL};
#else
    void *apvBuf = NULL;
#endif
    void *apvDstBuf = NULL;
    sint32 s32Idx;
    struct timeval tvAlarm;
    struct timezone tz;
    ALARM_EVENT_S stAlarmEvent = {0};
    ALARM_TYPE_E enAlarmType = ALARM_NOTHING;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;
    DUMP_DMM_S stDumpDmmInfo = {0};
    SV_BOOL bFinish = SV_FALSE;
    SV_BOOL bSleep = SV_FALSE;
    pstDmmInfo->enRunResult = DMM_RES_RUNNING;
    uint64 u64Pts = 0;

    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg || NULL == pstDmmInfo->pcsFrAnalyzer)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return ;
    }

    if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
    {
        goto skip;
    }

    STFaceIdInfo        stFaceIdInfo;
    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   *pstDmsResult = &stDmsResult;
    CFrAnalyzer         *pcsFrAnalyzer = pstDmmInfo->pcsFrAnalyzer;

    s32Ret = dmm_mmap((void **)&apvBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return ;
    }
    pstDmmInfo->bFirstFace = SV_FALSE;
    memset(&pstDmmInfo->stDumpDmmInfo, 0, sizeof(DUMP_DMM_S));

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32SemChn = 0;
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion() && BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))
    {
        s32SemChn = 0;
    }
    else
    {
        s32SemChn = 1;
    }
#else
    s32SemChn = 1;
#endif

    print_level(SV_INFO, "enter Recognition. bPlayLoginAudio: %d\n", pstDmmInfo->bPlayLoginAudio);
	s32Ret = dmm_PostProcessBar("LOGIN", 0.0, SV_FALSE);
	if(s32Ret != SV_SUCCESS)
	{
		print_level(SV_ERROR, "dmm_PostProcessBar failed.\n");
	}

    if (pstDmmInfo->bPlayLoginAudio)
    {
        dmm_Alarm_PlayAudio(NOTIFY_LOGIN_START);
        //sleep_ms(2000);     // 等待音频播放完成
    }

    clock_gettime(CLOCK_MONOTONIC, &tvLast);
    pcsFrAnalyzer->ReadFeature(FRS_USERINFOS_PATH);
    while (pstDmmInfo->bRunning)
    {
        if (pstDmmInfo->enRunStat != DMM_RUN_RECOGNITION || bFinish)
        {
            print_level(SV_INFO, "exit Recognition.\n");
            if (!bFinish)
            {
                print_level(SV_INFO, "Recognition is interrupted!\n");
                goto exit;
            }

            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            if (pstDmmInfo->bOnlyFr)
            {
                if (pstDmmInfo->enRunResult == DMM_RES_SUCCESS)
                {
                    pstDmmInfo->enRunStat = DMM_RUN_IDEL;
                }
                else
                {
                    pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                    bSleep = SV_TRUE;
                }
            }
            else
            {
                pstDmmInfo->enRunStat = DMM_RUN_DETECTION;
            }
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
            break;
        }

        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        if (tvNow.tv_sec - tvLast.tv_sec > 10)
        {
            bFinish = SV_TRUE;
            pstDmmInfo->enRunResult = DMM_RES_TIMEOUT;
            if (pstDmmInfo->bPlayLoginAudio)
            {
                dmm_Alarm_PlayAudio(NOTIFY_LNGIN_FAILED);
            }

            dmm_DumpInfo(&pstDmmInfo->stDumpDmmInfo);
            print_level(SV_INFO, "Recognition timeout!\n");
            continue;
        }

        /* 锁住计算资源,保证同一时刻只跑一个算法*/
        s32Ret = ALG_Calculate_Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }

        /* P操作进入MediaBuffer临界区 */
        u64Pts = 0;
        s32Ret = dmm_PBuf(s32SemChn, s32Idx, &u64Pts);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "dmm_PBuf failed. [err=%d]\n", s32Ret);
            ALG_Calculate_unLock();
            sleep_ms(1);
            continue;
        }
        apvDstBuf = dmm_getDstBuf((void **)&apvBuf, s32Idx);

        /* 模型向前推断函数 */
        s32Ret = dmm_Forward(pstDmmInfo->pcsDmsAlg, &apvDstBuf, &pstDmsResult, DMM_ALG_TYPE_RECOGNITION);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ForwardGroupDSM failed. [err=%d]\n", s32Ret);
            dmm_VBuf(s32SemChn, s32Idx);
            ALG_Calculate_unLock();
            sleep_ms(10);
            continue;
        }
        dmm_VBuf(s32SemChn, s32Idx);
        ALG_Calculate_unLock();

        if (stDmsResult.bFeatureVal)
        {
            fProgress = pcsFrAnalyzer->FrRecognition(stDmsResult);
            s32Progress = fProgress * 100;
            print_level(SV_INFO, "progress: %d%%\n", s32Progress);
			s32Ret = dmm_PostProcessBar("LOGIN", fProgress, SV_FALSE);
			if(s32Ret != SV_SUCCESS)
			{
				print_level(SV_ERROR, "dmm_PostProcessBar failed.\n");
			}
        }

        if (s32Progress >= 100)
        {
            print_level(SV_INFO, "Face Recognition end\n");

            stFaceIdInfo = pcsFrAnalyzer->GetFrIdInfo();
            print_level(SV_INFO, "Face Recognition ID: %d\n", stFaceIdInfo.s32Id);
            if (stFaceIdInfo.s32Id > 0)
            {
                print_level(SV_INFO, "Face Recognition name: %s\n", stFaceIdInfo.ps8UserName);
                pstDmmInfo->enRunResult = DMM_RES_SUCCESS;
                if (pstDmmInfo->bPlayLoginAudio)
                {
                    dmm_Alarm_PlayAudio(NOTIFY_LNGIN_SUCCESS);
                }
                snprintf(pstDmmInfo->stUserInfo.szDir, DMM_MAX_DIRLEN, "%s/%s", FRS_USERINFOS_PATH, stFaceIdInfo.ps8UserName);
                sprintf(szUserNamePath, "%s/username", pstDmmInfo->stUserInfo.szDir);
                s32Fd = open(szUserNamePath, O_RDONLY);
                if (s32Fd < 0)
                {
                    print_level(SV_ERROR, "open file: %s failed. [err:%s]\n", szUserNamePath, strerror(errno));
                }
                else
                {
                    s32Ret = read(s32Fd, szUserName, 1024);
                    if (s32Ret < 0)
                    {
                        print_level(SV_ERROR, "read file: %s failed. [err:%s]\n", szUserNamePath, strerror(errno));
                    }
                }
                dmm_CutLineBreak(szUserName);

                strcpy(pstDmmInfo->stUserInfo.szName, &szUserName[strlen("username=")]);
                strcpy(pstDmmInfo->stDumpDmmInfo.u8UsrId, stFaceIdInfo.ps8UserName+strlen("user"));
                strcpy(pstDmmInfo->stDumpDmmInfo.u8UsrName, pstDmmInfo->stUserInfo.szName);
                print_level(SV_INFO, "usrId: %s, userName: %s\n", pstDmmInfo->stDumpDmmInfo.u8UsrId, pstDmmInfo->stDumpDmmInfo.u8UsrName);
            }
            else
            {
				pstDmmInfo->enRunResult = DMM_RES_FAILURE;
                if (pstDmmInfo->bPlayLoginAudio)
                {
                    dmm_Alarm_PlayAudio(NOTIFY_LNGIN_FAILED);
                }
            }
            bFinish = SV_TRUE;
            dmm_DumpInfo(&pstDmmInfo->stDumpDmmInfo);
        }
    }

	if (pstDmmInfo->enRunResult == DMM_RES_SUCCESS)
		enAlarmType = NOTIFY_LNGIN_SUCCESS;
	else if (pstDmmInfo->enRunResult == DMM_RES_TIMEOUT)
		enAlarmType = NOTIFY_LNGIN_TIMEOUT;
	else
		enAlarmType = NOTIFY_LNGIN_FAILED;

    if (NOTIFY_LNGIN_SUCCESS == enAlarmType)
    {
        s32Ret = dmm_Alarm_Post(NOTIFY_LNGIN_SUCCESS);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "dmm_Alarm_Post failed.\n");
        }
    }
    else
    {
        s32Ret = dmm_Alarm_Post(NOTIFY_LNGIN_FAILED);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "dmm_Alarm_Post failed.\n");
        }
    }

    // 提交登录报警事件
    memset(&stAlarmEvent, 0, sizeof(stAlarmEvent));
    stAlarmEvent.enAlarmEvent = ALARM_EVENT_FR;
    stAlarmEvent.enAlarmType = enAlarmType;

    s32Ret = dmm_Alarm_Submit(stAlarmEvent);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "dmm_Alarm_Submit failed!\n");
    }
    print_level(SV_DEBUG, "u16UsrId:%03u, warnState:%d\n", atoi(pstDmmInfo->stDumpDmmInfo.u8UsrId), enAlarmType);

exit:
    s32Ret = dmm_PostProcessBar("LOGIN", 0.0, SV_TRUE);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "dmm_PostProcessBar failed.\n");
    }

    dmm_munmap((void **)&apvBuf);

    if (bSleep)
    {
        sleep_ms(5000);
    }
    return ;

skip:
    dmm_Alarm_PlayAudio(NOTIFY_LNGIN_FAILED);
    pstDmmInfo->enRunResult = DMM_RES_FAILURE;
    if (pstDmmInfo->bOnlyFr)
    {
        if (pstDmmInfo->enRunResult == DMM_RES_SUCCESS)
        {
            pstDmmInfo->enRunStat = DMM_RUN_IDEL;
        }
        else
        {
            pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
            bSleep = SV_TRUE;
        }
    }
    else
    {
        pstDmmInfo->enRunStat = DMM_RUN_DETECTION;
    }
    return ;
}

/******************************************************************************
 * 函数功能: 更换司机检测执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsFRAnalyzer --- 人脸识别分析器
 * 输出参数: 无
 * 返回值  : 无
 * 说明    : 用于人脸识别登陆功能
 *****************************************************************************/
void dmm_ChangeDriverBody(DMM_INFO_S *pstDmmInfo)
{
    sint32 s32Ret = 0, i;
    sint32 s32SemChn = 0;
    uint16 warnState, u16UsrId;
    sint32 s32Fd = -1;
    sint32 s32Progress = 0;
    float fProgress = 0;
    char szUserNamePath[128];
    char szUserName[1024];
    MSG_PACKET_S stMsgPkt = {0};
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
#if ALG_MUTLIT_BUFFER
    void *apvBuf[3] = {NULL};
#else
    void *apvBuf = NULL;
#endif
    void *apvDstBuf = NULL;
    sint32 s32Idx;
    struct timeval tvAlarm;
    struct timezone tz;
    ALARM_EVENT_S stAlarmEvent = {0};
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;
    MEDIA_GUI_ALARM_DMM_S stGuiAlarmDmm = {0};
    uint64 u64Pts = 0;

    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg || NULL == pstDmmInfo->pcsFrAnalyzer)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return ;
    }

    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   *pstDmsResult = &stDmsResult;
    CFrAnalyzer         *pcsFrAnalyzer = pstDmmInfo->pcsFrAnalyzer;

    /* CREARE客户要求关掉更换司机检测功能 */
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        pstDmmInfo->enRunStat = DMM_RUN_DETECTION;
        return;
    }

    s32Ret = dmm_mmap((void **)&apvBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return ;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32SemChn = 0;
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion() && BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))
    {
        s32SemChn = 0;
    }
    else
    {
        s32SemChn = 1;
    }
#else
    s32SemChn = 1;
#endif

    print_level(SV_INFO, "enter FRMatch.\n");

    clock_gettime(CLOCK_MONOTONIC, &tvLast);
    while (pstDmmInfo->bRunning)
    {
        if (pstDmmInfo->enRunStat != DMM_RUN_CHANGE_DRIVER)
        {
            print_level(SV_INFO, "exit Recognition.\n");
            break;
        }

        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        if (tvNow.tv_sec - tvLast.tv_sec > 30)
        {
            print_level(SV_INFO, "FRMatch timeout!\n");
            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = DMM_RUN_DETECTION;
            pstDmmInfo->enRunResult = DMM_RES_TIMEOUT;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
            continue;
        }

        /* 锁住计算资源,保证同一时刻只跑一个算法*/
        s32Ret = ALG_Calculate_Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }

        /* P操作进入MediaBuffer临界区 */
        u64Pts = 0;
        s32Ret = dmm_PBuf(s32SemChn, s32Idx, &u64Pts);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "dmm_PBuf failed. [err=%d]\n", s32Ret);
            ALG_Calculate_unLock();
            sleep_ms(1);
            continue;
        }
        apvDstBuf = dmm_getDstBuf((void **)&apvBuf, s32Idx);

        /* 模型向前推断函数 */
        s32Ret = dmm_Forward(pstDmmInfo->pcsDmsAlg, &apvDstBuf, &pstDmsResult, DMM_ALG_TYPE_CHANGE_DRIVER);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ForwardGroupDSM failed. [err=%d]\n", s32Ret);
            dmm_VBuf(s32SemChn, s32Idx);
            ALG_Calculate_unLock();
            sleep_ms(10);
            continue;
        }
        dmm_VBuf(s32SemChn, s32Idx);
        ALG_Calculate_unLock();

        if (stDmsResult.bFeatureVal)
        {
            fProgress = pcsFrAnalyzer->FRMatch(stDmsResult);
            s32Progress = fProgress * 100;
            print_level(SV_INFO, "progress: %d%%\n", s32Progress);
        }

        if (s32Progress >= 100)
        {
            print_level(SV_INFO, "Face FRMatch end\n");


            /* 获取司机检测结果,         0为同一个人, -1不是同一个人 */
            if (0 == pcsFrAnalyzer->GetMatchResult())
            {
                pstDmmInfo->enRunResult = DMM_RES_SUCCESS;

            }
            else
            {
                pstDmmInfo->enRunResult = DMM_RES_FAILURE;
            }

            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = DMM_RUN_DETECTION;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
        }
    }

    /* 不通结果产生不同的事件 */
    if (pstDmmInfo->enRunResult == DMM_RES_FAILURE)
    {
        print_level(SV_INFO, "different drivers.\n");
        warnState = NOTIFY_LNGIN_CHANGE_GUARD;
    }
    else if (pstDmmInfo->enRunResult == DMM_RES_TIMEOUT)
    {
        print_level(SV_INFO, "time out.\n");
        warnState = NOTIFY_LNGIN_TIMEOUT;
    }
    else
    {
        pstDmmInfo->bFirstFace = SV_FALSE;
        print_level(SV_INFO, "same driver\n");
        goto exit;
    }

    // 提交登录报警事件
    memset(&stAlarmEvent, 0, sizeof(stAlarmEvent));
    stAlarmEvent.enAlarmEvent = ALARM_EVENT_FR;
    stAlarmEvent.enAlarmType = warnState;

    s32Ret = dmm_Alarm_Submit(stAlarmEvent);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "dmm_Alarm_Submit failed!\n");
    }
    print_level(SV_DEBUG, "warnState:%u\n", warnState);

    stGuiAlarmDmm.enAlarmType = warnState;
    stGuiAlarmDmm.enAlarmLevel = ALARM_LEVEL_HIGH;
    stGuiAlarmDmm.contime = 2;  //持续两秒钟
    s32Ret = dmm_PostDmmGui(&stGuiAlarmDmm);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "dmm_PostDmmGui failed. [err=%#x]\n", s32Ret);
    }
exit:

    dmm_munmap((void **)&apvBuf);
}

/******************************************************************************
 * 函数功能: DMS算法检测执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsAlgStatus --- 算法检测状态
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void dmm_DetctionBody(DMM_INFO_S *pstDmmInfo)
{
    sint32 s32Ret = 0, i = 0, j = 0;
    sint32 s32SemChn = 0;
    sint32 s32FatigueLast = 0;   // Fatigue 报警后的持续时间,针对L2等级配置
    sint32 s32AlarmTimeMs[ALARM_DMS_BUFF] = {0};
    sint32 s32AlarmTimeMsLast[ALARM_DMS_BUFF] = {0};       /* 上一次设置的检测检测 */
    SV_BOOL bAlarm[ALARM_DMS_BUFF] = {0};
    SV_BOOL bOverspeed = SV_FALSE;
    float fSensitivity;
    uint32 u32StepTimeMs = 0;
    uint32 u32NoFaceTimeMs = 0;   /* 连续无司机的时间 */
    uint32 u32FaceCapTimeMs = 0;  /* 抓取首张人脸所用的时间 */
    uint32 u32FaceCnt = 0;        /* creare客户连续无司机10s后，人脸再次出现的帧数，人脸再次出现达到u32FaceCntMax帧才会跳转司机换岗检测 */
    uint32 u32FaceCntMax = 5;
    SV_BOOL bNoface = SV_FALSE;
    uint32 u32FaceDetectTimeMs = 0;     /* 人脸检测的时间计时，北京速力需求，计时到一分钟，进行一次人脸检测 */
    SV_BOOL bAngleOut = SV_FALSE;       /* 人脸角度是否超出合理范围，北京速力需求，能正常检测到人脸才跳转到人脸识别 */
    SV_BOOL bStartDetAngle = SV_FALSE;  /* 是否开始检测人脸角度 */
    uint32 u32CorrectAngleCnt = 0;      /* 人脸自动识别时，角度连续三次不超出合理范围才认为可以进行人脸识别 */
    static SV_BOOL s_bPlayGpsConn = SV_FALSE;
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
    struct timespec tvBegin, tvEnd;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_FACE_S stGuiFace = {0};
    MEDIA_GUI_ALARM_DMM_S stGuiAlarmDmm = {0};
    MEDIA_GUI_NULL_S stGuiNull;
    uint16 u16mask;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;
    sint32 s32GpsStatus, s32GpsSpeed;     /* GPS状态, GPS速度 */
    ALARM_TYPE_E enAlarmType = ALARM_NOTHING, enAlarmTypeLast = ALARM_NOTHING;
    sint32 s32RepeatTime = 0;
    ALARM_EVENT_S stAlarmEvent = {0};
    DMM_EYELID_S stDmmEyelid = {0};
    SV_BOOL bWorkingLowSpeed = SV_FALSE;
    DUMP_DMM_S stDumpDmmInfo = {0};
    SV_BOOL bGetShelter = SV_FALSE;
    uint32 u32ShelterNum = 0;
    uint32 u32DumpNum = 0;
    uint32 u32WorkSpeedIndex = 0;
    SV_BOOL bOsd = SV_FALSE;
    sint32 s32Cnt = 0;
    sint32 s32Total = 0;
#if ALG_MUTLIT_BUFFER
    void *apvBuf[3] = {NULL};
#else
    void *apvBuf = NULL;
#endif
    void *apvDstBuf = NULL;
    sint32 s32Idx;
    sint64 s64Now = 0, s64Last = 0;
    sint64 s64TimeBegin = 0, s64TimeNow = 0;        /* 遮挡和太阳眼镜时间统计 */
    uint32 u32TotolFrameCnt = 0, u32FaultCnt = 0;
    uint32 u32ShelterCnt = 0, u32SunglassCnt = 0;
    uint32 u32DDAWL7Cnt = 0, u32DDAWL8Cnt = 0, u32DDAWL9Cnt = 0, u32DDAWNoAlarmCnt = 0;
    const uint32 u32FaultStartCnt = 6;
    float fFaultRate = 0.0;
    SV_BOOL bGetShelterStartTime = SV_FALSE;
    SV_BOOL bGetSunglassStartTime = SV_FALSE;
    SV_BOOL bFault = SV_FALSE;
    bool bDetectFaceLast = false;
    sint64 s64ForwardMs = 0, s64ProcessMs = 0, s64PBufMs = 0;
    uint64 u64Pts = 0, u64Last = 0;
    SV_BOOL bVirDDAWAlarm = SV_FALSE;
    sint64 s64DDAWAlarmOffBeginTime = 0, s64DDAWAlarmOffEndTime = 0;

    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg || NULL == pstDmmInfo->pcsDmsAnalyzer)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return ;
    }

    STDMSResult         stDmsAnalysisRes;
    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   *pstDmsResult = &stDmsResult;
    STDmsDetectResult   stDmsResultLast;
    STDDAWResult        stDDAWResult;
    EDmsAlgStateCode    enWarnState = E_STATE_NORMAL;
    CDmsAnalyzer        *pcsDmsAnalyzer = pstDmmInfo->pcsDmsAnalyzer;

    for (i = 0; i < ALARM_DMS_BUFF; i++)
    {
        if (i != ALARM_OVERSPEED)
        {
            s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
        }
        s32AlarmTimeMsLast[i] = m_stDmmConf[i].s32DectectInterval;
    }

    s32Ret = dmm_mmap((void **)&apvBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return ;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32SemChn = 0;
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion() && BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))
    {
        s32SemChn = 0;
    }
    else
    {
        s32SemChn = 1;
    }
#else
    s32SemChn = 1;
#endif

    print_level(SV_INFO, "enter DMS detection.\n");
    clock_gettime(CLOCK_MONOTONIC, &tvLast);
    s64Last = dmm_GetTimeTickMs();
    while (pstDmmInfo->bRunning)
    {
        if (pstDmmInfo->enRunStat != DMM_RUN_DETECTION)
        {
            print_level(SV_INFO, "exit DMS detection.\n");
            break;
        }

        if ((BOARD_ADA32N1_V2 != BOARD_GetVersion()) || (!BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M)))
        {
            s32Ret = dmm_GetGpsResults(s32GpsStatus, s32GpsSpeed);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_GetGpsResults fail.\n");
                sleep_ms(1);
                continue;
            }
        }
#if 0
        if (s32GpsStatus != 0)
        {
            if (!s_bPlayGpsConn)
            {
                dmm_Alarm_PlayAudio(NOTIFY_GPS_CONNECT);
                s_bPlayGpsConn = SV_TRUE;
            }
        }
        else
        {
            s_bPlayGpsConn = SV_FALSE;
        }
#endif

        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        u32StepTimeMs = ((tvNow.tv_sec*1000 + tvNow.tv_nsec/1000000) - (tvLast.tv_sec*1000 + tvLast.tv_nsec/1000000));
        tvLast = tvNow;


#if 0 // Calculate the delay time per 100 times
        s32Cnt++;
        s32Total += u32StepTimeMs;
        if (s32Cnt >= 100)
        {
            print_level(SV_DEBUG, "dmm_DetctionBody delay: %d...\n", s32Total / s32Cnt);
            s32Cnt = 0;
            s32Total = 0;
        }
#endif
        //print_level(SV_INFO, "u32StepTimeMs: %d\n", u32StepTimeMs);

        for (i = 0; i < ALARM_DMS_BUFF; i++)
        {
            if (m_stDmmConf[i].bIntervalChanged)
            {
                print_level(SV_INFO, "dectectInterval[%d] %d change to %d\n", i, s32AlarmTimeMsLast[i], m_stDmmConf[i].s32DectectInterval);
                if (s32AlarmTimeMsLast[i] >= 0)
                {
                    bAlarm[i] = SV_TRUE;
                    s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
                }
                s32AlarmTimeMsLast[i] = m_stDmmConf[i].s32DectectInterval;

                if (i == NOTIFY_LNGIN_CHANGE_GUARD)
                {
                    bNoface = SV_FALSE;
                    u32NoFaceTimeMs = 0;
                }

                m_stDmmConf[i].bIntervalChanged = false;
            }

            switch (i)
            {
                case ALARM_FATIGUE:
                    if (m_stDmmConf[i].s32DectectInterval >= 0)
                    {
                        if (bAlarm[i])
                        {
                            bool bSwitch = true;
                            s32AlarmTimeMs[i] -= u32StepTimeMs;
                            bSwitch = (s32AlarmTimeMs[i] <= 0);
                            if (m_stDmmConf[ALARM_FATIGUE_L2].s32DectectInterval >= 0)  // 针对二级疲劳使能
                            {
                                if(s32FatigueLast >= 0)
                                {
                                    s32FatigueLast -= u32StepTimeMs;
                                    bSwitch = (s32FatigueLast >= 0);
                                }
                            }
                            dmm_setFuncSwitch(i, bSwitch);
                        }
                        else
                        {
                            dmm_setFuncSwitch(i, true);
                        }

                        if (bAlarm[i] && s32AlarmTimeMs[i] <= 0)  /* 报警触发超过一定时间后重新复位 */
                        {
                            bAlarm[i] = SV_FALSE;
                            bAlarm[ALARM_FATIGUE_L2] = SV_FALSE;
                            s32FatigueLast = -1;
                            s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
                        }
                    }
                    else
                    {
                        dmm_setFuncSwitch(i, false);
                    }
                    break;

                case ALARM_DISTRACTION:
                case ALARM_NO_DRIVER:
                case ALARM_SMOKE:
                case ALARM_PHONE:
                case ALARM_YAWN:
                case ALARM_NO_MASK:
                case ALARM_SUNGLASS:
                case ALARM_NO_SEATBELT:
                case ALARM_SHELTER:
                case ALARM_FATIGUE_L2:
                case ALARM_DRINK_EAT:
                case ALARM_NO_HELMET:
                    if (m_stDmmConf[i].s32DectectInterval >= 0)
                    {
                        if (bAlarm[i])
                        {
                            s32AlarmTimeMs[i] -= u32StepTimeMs;
                            dmm_setFuncSwitch(i, (s32AlarmTimeMs[i] <= 0));
                        }
                        else
                        {
                            dmm_setFuncSwitch(i, true);
                        }

                        if (bAlarm[i] && s32AlarmTimeMs[i] <= 0)
                        {
                            bAlarm[i] = SV_FALSE;
                            s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
                        }
                    }
                    else
                    {
                        dmm_setFuncSwitch(i, false);
                    }
                    break;

                case ALARM_OVERSPEED:
                    if (m_stDmmConf[i].s32DectectInterval >= 0)
                    {
                        if (s32AlarmTimeMs[i] <= 0)
                        {
                            if (s32GpsStatus != 0)
                            {
                                if (s32GpsSpeed > pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsOverspeedLimit)
                                {
                                    enAlarmType = ALARM_OVERSPEED;
                                    goto submit_msg;
                                }
                                else
                                {
                                    s32AlarmTimeMs[i] = 0;
                                }
                            }
                            else
                            {
                                s32AlarmTimeMs[i] = 0;
                            }
                        }

                        if (bAlarm[i])
                        {
                            s32AlarmTimeMs[i] -= u32StepTimeMs;
                        }
                        if (bAlarm[i] && s32AlarmTimeMs[i] <= 0)
                        {
                            bAlarm[i] = SV_FALSE;
                        }
                    }
                    break;

                default:
                    continue;
            }
        }

        /* 锁住计算资源,保证同一时刻只跑一个算法*/
        s32Ret = ALG_Calculate_Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);
        clock_gettime(CLOCK_MONOTONIC, &tvBegin);
        u64Pts = 0;
        s32Ret = dmm_PBuf(s32SemChn, s32Idx, &u64Pts);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "dmm_PBuf failed. [err=%d]\n", s32Ret);
            ALG_Calculate_unLock();
            sleep_ms(1);
            continue;
        }
        apvDstBuf = dmm_getDstBuf((void **)&apvBuf, s32Idx);
        clock_gettime(CLOCK_MONOTONIC, &tvEnd);
		s64PBufMs = (tvEnd.tv_sec*1000 + tvEnd.tv_nsec/1000000) - (tvBegin.tv_sec*1000 + tvBegin.tv_nsec/1000000);

		dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

#if 0 // for debug
        char *pszFileName = NULL;
        pszFileName = "/mnt/nfs/dmm.rgb";
        FILE *fp = fopen(pszFileName, "wb+");
        if (NULL != fp)
        {
#if ALG_MUTLIT_BUFFER
            fwrite((char *)apvBuf[s32Idx], 1, u32BufLen, fp);
#else
            fwrite((char *)apvBuf, 1, u32BufLen, fp);
#endif
            fclose(fp);
            print_level(SV_INFO, "write finish!\n");
            //MS_V(s32SemChn);
            //ALG_Calculate_unLock();
            //exit(1);
        }
#endif

        clock_gettime(CLOCK_MONOTONIC, &tvBegin);
        /* 模型向前推断函数 */
        s32Ret = dmm_Forward(pstDmmInfo->pcsDmsAlg, &apvDstBuf, &pstDmsResult, DMM_ALG_TYPE_DETECTION);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ForwardGroupDSM failed. [err=%d]\n", s32Ret);
            dmm_VBuf(s32SemChn, s32Idx);
            ALG_Calculate_unLock();
            sleep_ms(10);
            continue;
        }
        clock_gettime(CLOCK_MONOTONIC, &tvEnd);
        s64ForwardMs = (tvEnd.tv_sec*1000 + tvEnd.tv_nsec/1000000) - (tvBegin.tv_sec*1000 + tvBegin.tv_nsec/1000000);
#if DMM_DEBUG_TIME
        print_level(SV_INFO, "forward eclipse: %dms\n", s64ForwardMs);
#endif
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        /* 退出临界区 */
        dmm_VBuf(s32SemChn, s32Idx);
        ALG_Calculate_unLock();

        clock_gettime(CLOCK_MONOTONIC, &tvBegin);

        /* 过滤掉全图检测时导致的一帧数据无效的情况 */
        if (!stDmsResult.bDetectFace && bDetectFaceLast)
        {
            bDetectFaceLast = stDmsResult.bDetectFace;
            stDmsResult = stDmsResultLast;
        }
        else
        {
            bDetectFaceLast = stDmsResult.bDetectFace;
            stDmsResultLast = stDmsResult;
        }

        /* 设置检测灵敏度 */
        switch (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsSensitivity)
        {
            case -1:    /* 自动模式 */
                if(s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsMiddleSpeedThr)
                    fSensitivity = 1.0;
                else if(s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsHighSpeedThr)
                    fSensitivity = 0.66;
                else
                    fSensitivity = 0.33;
                break;
            case 0:
                fSensitivity = 1.0;
                break;
            case 1:
                fSensitivity = 0.66;
                break;
            case 2:
                fSensitivity = 0.33;
                break;
            default:
                fSensitivity = 1.0;
        }

        /* 行为分析 */
        stDmsAnalysisRes = pcsDmsAnalyzer->StateAnalysis(stDmsResult, fSensitivity);
        enWarnState = stDmsAnalysisRes.eDmsAlgStateCode;
        //print_level(SV_INFO, "enWarnState: %d\n", enWarnState);
        if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
            enWarnState = E_STATE_NORMAL;

        do {
            switch (enWarnState)
            {
                case E_STATE_NORMAL:
                    enAlarmType          = ALARM_NOTHING;
                    break;
                case E_STATE_FATIGUE:
                    enAlarmType          = ALARM_FATIGUE;
                    break;
                case E_STATE_FATIGUE_L2:
                    enAlarmType          = ALARM_FATIGUE_L2;
                    break;
                case E_STATE_DISTRACTION:
                    enAlarmType          = ALARM_DISTRACTION;
                    break;
                case E_STATE_NO_DRIVER:
                    enAlarmType          = ALARM_NO_DRIVER;
                    break;
                case E_STATE_SMOKE:
                    enAlarmType          = ALARM_SMOKE;
                    break;
                case E_STATE_PHONE:
                    enAlarmType          = ALARM_PHONE;
                    break;
                case E_STATE_YAWN:
                    enAlarmType          = ALARM_YAWN;
                    break;
                case E_STATE_NO_MASK:
                    enAlarmType          = ALARM_NO_MASK;
                    break;
                case E_STATE_SUNGLASSES:
                    enAlarmType          = ALARM_SUNGLASS;
                    break;
                case E_STATE_NO_SEATBELT:
                    enAlarmType          = ALARM_NO_SEATBELT;
                    break;
                case E_STATE_OCCLUSION:
                    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
                    {
                        enAlarmType      = ALARM_NO_DRIVER;
                    }
                    else
                    {
                        enAlarmType      = ALARM_SHELTER;
                    }
                    bGetShelter = SV_TRUE;
                    break;
                case E_STATE_DRINK:
                    enAlarmType          = ALARM_DRINK_EAT;
                    break;
                case E_STATE_NO_HELMET:
                    enAlarmType          = ALARM_NO_HELMET;
                    break;
                default:
                    break;
            }

            if (stDmsResult.bDetectFace)
            {
                float angleDiff = abs(stDmsResult.pfpHeadPose[1] - pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle[1]);
                if (angleDiff >= 30)
                {
                    u32CorrectAngleCnt = 0;
                }
                else
                {
                    u32CorrectAngleCnt++;
                    //print_level(SV_INFO, "u32CorrectAngleCnt: %d\n", u32CorrectAngleCnt);
                }

                if (u32CorrectAngleCnt >= 10)
                {
                    bAngleOut = SV_FALSE;
                }
                else
                {
                    bAngleOut = SV_TRUE;
                }
            }
            else
            {
                u32CorrectAngleCnt = 0;
            }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32N1))
            /* 定时人脸识别处理，展会专用版不使用该功能，展会专用版无人脸大于5s后自动进行一次人脸识别 */
            if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_EXHIBITION))
            {
                if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsFrInterval <= 0)
                {
                    goto interval_exit;
                }

                u32FaceDetectTimeMs += u32StepTimeMs;
                if (u32FaceDetectTimeMs > pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsFrInterval * 1000)
                {
                    if (!bStartDetAngle)
                    {
                        bStartDetAngle = SV_TRUE;
                        u32CorrectAngleCnt = 0;
                        bAngleOut = SV_TRUE;
                    }
                }
                else
                {
                    bStartDetAngle = SV_FALSE;
                }

                if (bStartDetAngle && !bAngleOut)
                {
                    pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                    pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                    pstDmmInfo->bPlayLoginAudio = SV_FALSE;
                    pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                    break;
                }
            }
            else
            {
                if (pstDmmInfo->bFirstFace)
                {
                    if (stDmsResult.bDetectFace)
                    {
                        if (!bStartDetAngle)
                        {
                            bStartDetAngle = SV_TRUE;
                            u32CorrectAngleCnt = 0;
                            bAngleOut = SV_TRUE;
                        }

                        if (bStartDetAngle && !bAngleOut)
                        {
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                            pstDmmInfo->bPlayLoginAudio = SV_FALSE;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "recognition first face.\n");
                            break;
                        }
                    }
                    else
                    {
                        bStartDetAngle = SV_FALSE;
                    }
                }
                else
                {
                    if (!bNoface && !stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs += u32StepTimeMs;
                        if (u32NoFaceTimeMs >= DMM_NO_FACE_TIME_MS_EX)
                        {
                            print_level(SV_INFO, "no face time exceeds %dms!!!\n", DMM_NO_FACE_TIME_MS_EX);
                            memset(pstDmmInfo->stDumpDmmInfo.u8UsrId, 0, sizeof(pstDmmInfo->stDumpDmmInfo.u8UsrId));
                            memset(pstDmmInfo->stDumpDmmInfo.u8UsrName, 0, sizeof(pstDmmInfo->stDumpDmmInfo.u8UsrName));
                            bNoface = SV_TRUE;
                        }
                    }
                    else if (stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs = 0;
                    }

                    if (bNoface)
                    {
                        if (stDmsResult.bDetectFace)
                        {
                            if (!bStartDetAngle)
                            {
                                bStartDetAngle = SV_TRUE;
                                u32CorrectAngleCnt = 0;
                                bAngleOut = SV_TRUE;
                            }
                        }
                        else
                        {
                            bStartDetAngle = SV_FALSE;
                        }

                        if (bStartDetAngle && !bAngleOut)
                        {
                            bNoface = SV_FALSE;
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                            pstDmmInfo->bPlayLoginAudio = SV_FALSE;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "face is not angle out, enter DMM_RUN_RECOGNITION....\n");
                            break;
                        }
                    }
                }
            }

interval_exit:

            /* CREARE客户，连续一段时间无司机后，若人脸再次出现，则识别一次人脸，检测是否换司机 */
            if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
            {
                if (m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval < 0
                    && (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFaceCapture || (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFaceCapture && !pstDmmInfo->bNeedFaceCap)))
                {
                    //print_level(SV_INFO, "bNeedFaceCap: %d, bStartFaceCap: %d\n", pstDmmInfo->bNeedFaceCap, pstDmmInfo->bStartFaceCap);
                    goto creare_exit;
                }

                if (pstDmmInfo->bNeedFaceCap && !pstDmmInfo->bStartFaceCap)
                {
                    if (!COMMON_IsPathExist(DMM_START_FACE_CAP))
                    {
                        if (m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval >= 0)
                        {
                            goto faceCap_exit;
                        }
                        else
                        {
                            goto creare_exit;
                        }
                    }
                    print_level(SV_INFO, "play welcome and start face capture!\n");
                    pstDmmInfo->bStartFaceCap = SV_TRUE;
                    s32Ret = ALARM_PlayWelcome();
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "ALARM_PlayWelcome failed. [err=%#x]\n", s32Ret);
                    }
                    remove(DMM_START_FACE_CAP);
                }

                if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFaceCapture && pstDmmInfo->bStartFaceCap)
                {
                    u32FaceCapTimeMs += u32StepTimeMs;
                    //print_level(SV_INFO, "u32FaceCapTimeMs: %d\n", u32FaceCapTimeMs);
                    if (u32FaceCapTimeMs >= DMM_FACE_CAP_TIMEOUT)
                    {
                        print_level(SV_INFO, "face capture timeout!\n");
                        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
                        stMsgPkt.stMsg.u8Param = 2;
                        s32Ret = Msg_submitEvent(EP_CAN, OP_EVENT_CAN_DVR_FACE_CAPTURE, &stMsgPkt);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_INFO, "Msg_submitEvent failed!\n");
                        }
                        pstDmmInfo->bNeedFaceCap = SV_FALSE;
                        pstDmmInfo->bStartFaceCap = SV_FALSE;
                    }
                }
faceCap_exit:

                if (pstDmmInfo->bFirstFace || pstDmmInfo->bStartFaceCap)
                {
                    if (stDmsResult.bDetectFace)
                    {
                        if (!bStartDetAngle)
                        {
                            bStartDetAngle = SV_TRUE;
                            u32CorrectAngleCnt = 0;
                            bAngleOut = SV_TRUE;
                        }
                    }
                    else
                    {
                        bStartDetAngle = SV_FALSE;
                    }
                }

                if (pstDmmInfo->bStartFaceCap)
                {
                    if (bStartDetAngle && !bAngleOut)
                    {
                        print_level(SV_INFO, "face capture!!!\n");
                        pstDmmInfo->bNeedFaceCap = SV_FALSE;
                        pstDmmInfo->bStartFaceCap = SV_FALSE;
                        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
                        stMsgPkt.stMsg.u8Param = 1;
                        s32Ret = Msg_submitEvent(EP_CAN, OP_EVENT_CAN_DVR_FACE_CAPTURE, &stMsgPkt);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_INFO, "Msg_submitEvent failed!\n");
                        }
                    }
                }

                /* 上电后跳到人脸识别线程记录第一张出现的人脸 */
                if (pstDmmInfo->bFirstFace)
                {
                    if (bStartDetAngle && !bAngleOut)
                    {
                        if (m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval >= 0)
                        {
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_CHANGE_DRIVER;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "record first face.\n");
                            break;
                        }
                    }
                }
                else
                {
                    if (!bNoface && !stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs += u32StepTimeMs;
                        if (u32NoFaceTimeMs >= DMM_NO_FACE_TIME_MS)
                        {
                            print_level(SV_INFO, "no face time exceeds %dms!!!\n", DMM_NO_FACE_TIME_MS);
                            bNoface = SV_TRUE;
                        }
                    }
                    else if (stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs = 0;
                    }

                    if (bNoface)
                    {
                        if (stDmsResult.bDetectFace)
                        {
                            if (!bStartDetAngle)
                            {
                                bStartDetAngle = SV_TRUE;
                                u32CorrectAngleCnt = 0;
                                bAngleOut = SV_TRUE;
                            }
                        }
                        else
                        {
                            bStartDetAngle = SV_FALSE;
                        }

                        if (bStartDetAngle && !bAngleOut)
                        {
                            bNoface = SV_FALSE;
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_CHANGE_DRIVER;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "face is not angle out, enter DMM_RUN_CHANGE_DRIVER....\n");
                            break;
                        }
                    }
                }
            }
creare_exit:;
#endif

            /* 如果检测到无司机，则准备跳到登录阶段 */
            if (E_LOGIN_AUTO == pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.enDmsLoginMode && stDmsResult.bDetectFace && ((pstDmmInfo->bFirstFace || ALARM_NO_DRIVER == enAlarmTypeLast || ALARM_SHELTER == enAlarmTypeLast)))
            {
                if (!bAngleOut)
                {
                    pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                    print_level(SV_INFO, "ready to login....\n");
                    pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                    pstDmmInfo->bPlayLoginAudio = SV_TRUE;
                    pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                    break;
                }
                else
                {
                    print_level(SV_WARN, "waiting for center angle to fr....\n");
                    continue;
                }
            }

submit_msg:
			bOsd = SV_FALSE;
            if (enAlarmType == ALARM_NOTHING)
            {
                if (enAlarmTypeLast == ALARM_NO_DRIVER && s32RepeatTime > 1 && stDmsResult.bDetectFace)
                {
                    s32AlarmTimeMs[ALARM_NO_DRIVER] = pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32NoDriverInterval * 1000;
                    enAlarmTypeLast = ALARM_NOTHING;
                    s32RepeatTime = 0;
                }

                if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[enAlarmType] <= 0
                    || (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[enAlarmType] > 0 && s32GpsStatus != 0 && s32GpsSpeed >= pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[enAlarmType])
                    || pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS)
                {
                    bOsd = SV_TRUE;
                }
                break;
            }
            else if (enAlarmType == ALARM_OVERSPEED)
            {
                bOverspeed = SV_TRUE;
                s32AlarmTimeMs[ALARM_OVERSPEED] = pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32OverspeedInterval * 1000;
            }

            /* 工作速度判断 */
            if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
                u32WorkSpeedIndex = enAlarmType;
            else
                u32WorkSpeedIndex = 0;

            bWorkingLowSpeed = SV_FALSE;
            if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex] > 0)
            {
                if (s32GpsStatus == 0)
                {
                    if (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS)
                    {
                        enAlarmTypeLast = enAlarmType;
                        sleep_ms(30);
                        break;
                    }
                    else
                    {
                        if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
                        {
                            bWorkingLowSpeed = SV_TRUE;
                        }
                    }
                }
                else if (s32GpsStatus != 0 && s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex])
                {
                    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
                    {
                        bWorkingLowSpeed = SV_TRUE;
                    }
                    else
                    {
                        pcsDmsAnalyzer->SetTimerZero();
                        enAlarmTypeLast = enAlarmType;
                        sleep_ms(30);
                        break;
                    }
                }
            }
            bOsd = SV_TRUE;

            bAlarm[enAlarmType] = SV_TRUE;
            s32AlarmTimeMs[enAlarmType] = m_stDmmConf[enAlarmType].s32DectectInterval;

            if (enAlarmType == ALARM_FATIGUE)
            {
                if(m_stDmmConf[ALARM_FATIGUE_L2].s32DectectInterval >= 0)
                {
                    s32FatigueLast = 5 * 1000;
                }
                else
                {
                    s32FatigueLast = -1;
                }
            }

            /* 低速状态下触发的报警不推送报警事件 */
            if (!bWorkingLowSpeed)
            {
                /* 通知外部的结果统一使用ALARM_TYPE_E宏，函数内使用WarnState宏 */
                s32Ret = dmm_Alarm_Post((sint32)enAlarmType);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "dmm_Alarm_Post failed.\n");
                }

                memset(&stAlarmEvent, 0, sizeof(stAlarmEvent));
                stAlarmEvent.enAlarmEvent = ALARM_EVENT_DMM;
                stAlarmEvent.enAlarmType = enAlarmType;

                s32Ret = dmm_Alarm_Submit(stAlarmEvent);
                if (SV_SUCCESS != s32Ret)
                {
                	print_level(SV_ERROR, "dmm_Alarm_Submit failed!\n");
                }
            }

            stGuiAlarmDmm.enAlarmType = enAlarmType;
            stGuiAlarmDmm.enAlarmLevel = bWorkingLowSpeed ? ALARM_LEVEL_LOW : ALARM_LEVEL_HIGH;
            stGuiAlarmDmm.contime = 2;  //持续两秒钟
            s32Ret = dmm_PostDmmGui(&stGuiAlarmDmm);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_PostDmmGui failed. [err=%#x]\n", s32Ret);
            }

            print_level(SV_DEBUG, "enWarnState: %d\n", enWarnState);
            if (enAlarmType == ALARM_NO_DRIVER && s32RepeatTime >= 5);            /* 多次无司机报警屏蔽 */
            else if (enAlarmType == ALARM_SHELTER && s32RepeatTime >= 5);         /* 多次遮挡报警屏蔽 */
            else
            {
                print_level(SV_DEBUG, "enAlarmType: %d\n", enAlarmType);
                if (m_stDmmConf[ALARM_NOTHING].bDmsAudioEnable && m_stDmmConf[enAlarmType].bDmsAudioEnable && !bWorkingLowSpeed)
                {
                    s32Ret = dmm_Alarm_PlayAudio(enAlarmType);
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "dmm_Alarm_PlayAudio failed. [type:%d]\n", enAlarmType);
                    }
                }
            }

            if (enAlarmType != enAlarmTypeLast)
            {
                s32RepeatTime = 1;
                enAlarmTypeLast = enAlarmType;
                break;
            }

            /* case enAlarmType == enAlarmTypeLast */
            s32RepeatTime++;
            if(BOARD_IsNotCustomer(BOARD_C_DMS31V2_SHIQI))
            {
                break;
            }

            if(enAlarmType == ALARM_NO_DRIVER)
            {
                if(s32RepeatTime % 9 == 0)
                {
                    s32AlarmTimeMs[ALARM_NO_DRIVER] = 30 * 60 * 1000; // 延时30分钟
                }
                else if(s32RepeatTime % 3 == 0)
                {
                    s32AlarmTimeMs[ALARM_NO_DRIVER] = 3 * 60 * 1000; // 延时3分钟
                }
            }
            else if(enAlarmType == ALARM_PHONE)
            {
                if(s32RepeatTime % 9 == 0)
                {
                    s32AlarmTimeMs[ALARM_PHONE] = 10 * 60 * 1000; // 延时10分钟
                }
                else if(s32RepeatTime % 3 == 0)
                {
                    s32AlarmTimeMs[ALARM_PHONE] = 3 * 60 * 1000; // 延时3分钟
                }
            }
            else if(enAlarmType == ALARM_SMOKE)
            {
                if(s32RepeatTime % 9 == 0)
                {
                    s32AlarmTimeMs[ALARM_SMOKE] = 10 * 60 * 1000; // 延时10分钟
                }
                else if(s32RepeatTime % 3 == 0)
                {
                    s32AlarmTimeMs[ALARM_SMOKE] = 3 * 60 * 1000; // 延时3分钟
                }
            }
        } while(0);
		dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        do{
            stDDAWResult = pcsDmsAnalyzer->DDAWAnalysis(stDmsResult);
            dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);
            if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_DDAW))
            {
                break;
            }

            #if 0
            print_level(SV_INFO, "%f, %f, %f, %f, %f\n", stDDAWResult.pfEcr[0], stDDAWResult.pfEcr[1], stDDAWResult.pfHeadPose[0], stDDAWResult.pfHeadPose[1], stDDAWResult.pfHeadPose[2]);
            print_level(SV_INFO, "%d, %d\n", stDDAWResult.s32BlinkPerMin, stDDAWResult.s32YawnPerMin);
            print_level(SV_INFO, "%f, %f, %f, %f\n", stDDAWResult.fEcrGrad, stDDAWResult.fGazeGrad, stDDAWResult.fPoseYawGrad, stDDAWResult.fPosePitchGrad);
            print_level(SV_INFO, "%d\n", stDDAWResult.s32DDAWScore);
            #endif
            //print_level(SV_INFO, "bYawn: %d, fOcclusionScore: %f, bOcclusion: %d, bSunglasses: %d\n", stDmsResult.bYawn, stDmsResult.fOcclusionScore, stDDAWResult.bOcclusion, stDDAWResult.bSunglasses);

            /* 3s内遮挡率达到一定比例判定为遮挡，要发生一次遮挡后才会开始计时 */
            if (!bGetShelterStartTime)
            {
                if (stDDAWResult.bOcclusion)
                {
                    u32ShelterCnt++;
                    if (u32ShelterCnt > u32FaultStartCnt)
                    {
reset_shelter:
                        s64TimeBegin = dmm_GetTimeTickMs();
                        u32TotolFrameCnt = 0;
                        u32FaultCnt = 0;
                        bGetShelterStartTime = SV_TRUE;
                        bGetSunglassStartTime = SV_FALSE;
                        print_level(SV_INFO, "reset shelter time begin!\n");
                    }
                }
                else
                {
                    u32ShelterCnt = 0;
                }
            }
            else
            {
                s64TimeNow = dmm_GetTimeTickMs();
                if (s64TimeNow - s64TimeBegin < 3000)
                {
                    u32TotolFrameCnt++;
                    if (stDDAWResult.bOcclusion)
                    {
                        u32FaultCnt++;
                    }
                }
                else
                {
                    fFaultRate = u32FaultCnt*1.0 / u32TotolFrameCnt;
                    print_level(SV_INFO, "shelter rate: %f\n", fFaultRate);
                    if (fFaultRate > 0.8)
                    {
                        bFault = SV_TRUE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_ON);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "shelter!!!!!\n");
                        goto reset_shelter;
                    }
                    else
                    {
                        bFault = SV_FALSE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_OFF);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "no shelter!!!!!\n");
                    }

                    u32ShelterCnt = 0;
                    u32FaultCnt = 0;
                    u32TotolFrameCnt = 0;
                    s64TimeBegin = s64TimeNow;
                    bGetShelterStartTime = SV_FALSE;
                    printf("\n");
                }
            }

            if (!bGetSunglassStartTime)
            {
                if (stDDAWResult.bSunglasses)
                {
                    u32SunglassCnt++;
                    if (u32SunglassCnt > u32FaultStartCnt)
                    {
reset_sunglasses:
                        s64TimeBegin = dmm_GetTimeTickMs();
                        u32TotolFrameCnt = 0;
                        u32FaultCnt = 0;
                        bGetSunglassStartTime = SV_TRUE;
                        bGetShelterStartTime = SV_FALSE;
                        print_level(SV_INFO, "reset sunglass time begin!\n");
                    }
                }
                else
                {
                    u32SunglassCnt = 0;
                }
            }
            else
            {
                s64TimeNow = dmm_GetTimeTickMs();
                if (s64TimeNow - s64TimeBegin < 3000)
                {
                    u32TotolFrameCnt++;
                    if (stDDAWResult.bSunglasses)
                    {
                        u32FaultCnt++;
                    }
                }
                else
                {
                    fFaultRate = u32FaultCnt*1.0 / u32TotolFrameCnt;
                    print_level(SV_INFO, "sunglass rate: %f\n", fFaultRate);
                    if (fFaultRate > 0.8)
                    {
                        bFault = SV_TRUE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_ON);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "sunglass!!!!!\n");
                        goto reset_sunglasses;
                    }
                    else
                    {
                        bFault = SV_FALSE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_OFF);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "no sunglass!!!!!\n");
                    }

                    u32SunglassCnt = 0;
                    u32FaultCnt = 0;
                    u32TotolFrameCnt = 0;
                    s64TimeBegin = s64TimeNow;
                    bGetSunglassStartTime = SV_FALSE;
                    printf("\n");
                }
            }

            if (bFault)
            {
                goto ddaw_exit;
            }

            if (pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable)
            {
                bVirDDAWAlarm = pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable;

                if (pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmStatus && !pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    print_level(SV_INFO, "start virtual alarm\n");
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_TRUE;
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 1);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }
                else if (!pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmStatus && pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    print_level(SV_INFO, "stop virtual alarm\n");
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }

                goto ddaw_exit;
            }
            else if (bVirDDAWAlarm && !pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable)
            {
                print_level(SV_INFO, "disable virtual alarm\n");
                bVirDDAWAlarm = pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable;
                if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    print_level(SV_INFO, "stop virtual alarm\n");
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }
            }

            u32WorkSpeedIndex = 0;
            if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex] > 0)
            {
                if ((s32GpsStatus == 0 && !pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS)
                    || (s32GpsStatus != 0 && s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex]))
                {
                    if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                    {
                        if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                        if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                        if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_NOTHING;

                        print_level(SV_INFO, "speed is not reachable, stop ddaw alarm~~~~\n");
                        ALARM_EnableSpk(SV_FALSE);
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                        if(s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                    }
                    goto ddaw_exit;
                }
            }
            #if 1
            //print_level(SV_INFO, "s32DDAWScore: %d, u32DDAWL9Cnt: %d, u32DDAWL8Cnt: %d, u32DDAWL7Cnt: %d, u32DDAWNoAlarmCnt: %d\n", stDDAWResult.s32DDAWScore, u32DDAWL9Cnt, u32DDAWL8Cnt, u32DDAWL7Cnt, u32DDAWNoAlarmCnt);
            if (CLICK_TYPE_DOUBLE != pstDmmInfo->stDDAWInfo.enClickType)
            {
                if (stDDAWResult.s32DDAWScore >= pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel9Thres)
                {
                    if (++u32DDAWL9Cnt >= 3)
                    {
                        if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                        if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_DDAW_KSS9;
                        //print_level(SV_INFO, "KSS9!!!\n");
                    }
                }
                else if (stDDAWResult.s32DDAWScore >= pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel8Thres)
                {
                    if (++u32DDAWL8Cnt >= 3)
                    {
                        if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                        if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_DDAW_KSS8;
                        //print_level(SV_INFO, "KSS8!!!\n");
                    }
                }
                else if (stDDAWResult.s32DDAWScore >= pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel7Thres)
                {
                    if (++u32DDAWL7Cnt >= 3)
                    {
                        if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                        if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_DDAW_KSS7;
                        //print_level(SV_INFO, "KSS7!!!\n");
                    }
                }
                else if (stDDAWResult.s32DDAWScore < pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel7Thres)
                {
                    if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                    {
                        if (0 == s64DDAWAlarmOffBeginTime)
                        {
                            s64DDAWAlarmOffBeginTime = dmm_GetTimeTickMs();
                        }

                        s64DDAWAlarmOffEndTime = dmm_GetTimeTickMs();
                        if (s64DDAWAlarmOffEndTime - s64DDAWAlarmOffBeginTime >= 2000)
                        {
                            if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                            if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                            if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                            if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                            pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                            pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_NOTHING;

                            print_level(SV_INFO, "it's been 2 seconds, stop ddaw alarm~~~~\n");
                            ALARM_EnableSpk(SV_FALSE);
                            s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                            if(s32Ret != SV_SUCCESS)
                            {
                                print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                            }
                        }
                    }
                }

                dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

                if (ALARM_NOTHING != pstDmmInfo->stDDAWInfo.enDDAWAlarmType && !pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    if (0 != ALARM_GetVolume())
                    {
                        ALARM_EnableSpk(SV_TRUE);
                    }
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_TRUE;
                    pstDmmInfo->stDDAWInfo.cv_ddaw.notify_one();

                    print_level(SV_INFO, "start ddaw alarm~~~~\n");
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 1);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }

                dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);
            }
            else
            {
                if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                    if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                    if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                    if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                    pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_NOTHING;

                    print_level(SV_INFO, "stop ddaw alarm~~~~\n");
                    ALARM_EnableSpk(SV_FALSE);
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }
            }
            #endif

ddaw_exit:
            bOsd = SV_TRUE;
        } while(0);
		dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        pstDmmInfo->stDumpDmmInfo.enAlarmType = enAlarmType;

        /* 速度比较快，这里需要对遮挡手动添加多几帧dump数据 */
        if (enAlarmType == ALARM_NOTHING)
        {
            if (u32DumpNum <= 5)
            {
                u32DumpNum++;
                pstDmmInfo->stDumpDmmInfo.enAlarmType = enAlarmTypeLast;
            }
        }
        else
        {
            u32DumpNum = 0;
        }

        pstDmmInfo->stDumpDmmInfo.bImageMirror = pstDmmInfo->stCfgParam.bImageMirror;
        pstDmmInfo->stDumpDmmInfo.bDetectFace = stDmsResult.bDetectFace ? SV_TRUE : SV_FALSE;
        pstDmmInfo->stDumpDmmInfo.bYawn = stDmsResult.bYawn ? SV_TRUE : SV_FALSE;
        pstDmmInfo->stDumpDmmInfo.bNoMask = stDmsResult.bNoMask ? SV_TRUE : SV_FALSE;
        pstDmmInfo->stDumpDmmInfo.bShelter = (stDmsResult.fOcclusionScore > 50.0) ? SV_TRUE : SV_FALSE;
        sprintf(pstDmmInfo->stDumpDmmInfo.u8SmokeScore, "%f", stDmsResult.fSmokeScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8PhoneScore, "%f", stDmsResult.fPhoneScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8DrinkEatScore, "%f", stDmsResult.fDrinkScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8SeatbeltScore, "%f", stDmsResult.fSeatbeltScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8HelmetScore, "%f", stDmsResult.fHelmetScore);
        for (j = 0; j < 2; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8Ecr[j], "%f", stDmsAnalysisRes.fAbsEcr[j]);
        for (j = 0; j < 4; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8EyeScore[j], "%f", stDmsResult.pfpEyeStateScore[j]);
        for (j = 0; j < 2; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8GlassScore[j], "%f", stDmsResult.pfpSunglassesScore[j]);
        for (j = 0; j < 3; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8HeadPose[j], "%f", stDmsResult.pfpHeadPose[j]);
        for (j = 0; j < 2; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8Gaze[j], "%f", stDmsResult.pfGaze[j]);  // 弧度转角度

        memcpy(&stDumpDmmInfo, &pstDmmInfo->stDumpDmmInfo, sizeof(DUMP_DMM_S));
        s32Ret = dmm_DumpInfoThread(&stDumpDmmInfo);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "dmm_DumpInfoThread failed. [err: %s]\n", strerror(errno));
            return SV_FAILURE;
        }
		dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT) && pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
        {
            dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);
            clock_gettime(CLOCK_MONOTONIC, &tvEnd);
            s64ProcessMs = (tvEnd.tv_sec*1000 + tvEnd.tv_nsec/1000000) - (tvBegin.tv_sec*1000 + tvBegin.tv_nsec/1000000);

            //print_level(SV_INFO, "bDetectFace: %d, fEyelidLeft: %f, fEyelidRight: %f\n", stDmsResult.bDetectFace, stDmsResult.pfEyeCloseRate[0], stDmsResult.pfEyeCloseRate[1]);
            memset(&stDmmEyelid, 0, sizeof(stDmmEyelid));
            stDmmEyelid.u64TimeStamp = u64Pts;
            stDmmEyelid.s64PBufMs = s64PBufMs;
            stDmmEyelid.s64ForwardMs = s64ForwardMs;
            stDmmEyelid.s64ProcessMs = s64ProcessMs;
            stDmmEyelid.fEyelidLeft = stDmsResult.pfEyeCloseRate[0];
            stDmmEyelid.bEyelidLeftValid = (SV_BOOL)stDmsResult.bDetectFace;
            stDmmEyelid.fEyelidRight = stDmsResult.pfEyeCloseRate[1];
            stDmmEyelid.bEyelidRightValid = (SV_BOOL)stDmsResult.bDetectFace;
            dmm_SubmitEyelid(stDmmEyelid);
            u64Last = u64Pts;
        }

        if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) && enAlarmType == ALARM_NOTHING)
        {
            bOsd = SV_TRUE;
        }

        /* 发送检测结果OSD信息到ipsys进行叠加 */
        if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsOsdEnable && bOsd)
        {
            memset(&stMsgPkt, 0, sizeof(stMsgPkt));
            stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
            stMsgPkt.u32Size = sizeof(MEDIA_GUI_DRAW_S);
            memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));

            //清空原来的画板
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_CLEAR);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
            }

            memset(&stGuiFace, 0, sizeof(MEDIA_GUI_FACE_S));
            if (stDmsResult.bDetectFace)
            {
                for (i = 0; i < 3; i++)
                {
                    stGuiFace.angle[i] = (int)(stDmsResult.pfpHeadPose[i] + 2.5 * ((stDmsResult.pfpHeadPose[i] > 0) * 2 - 1))/5*5;
        			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] > 85 ? 90 : stGuiFace.angle[i];
        			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] < -85 ? -90 : stGuiFace.angle[i];
                }
                stGuiFace.stFaceRect.color = GUI_COLOR_YELLOW;
                stGuiFace.stFaceRect.x1 = stDmsResult.stFaceBoundBox.fX1;
                stGuiFace.stFaceRect.y1 = stDmsResult.stFaceBoundBox.fY1;
                stGuiFace.stFaceRect.x2 = stDmsResult.stFaceBoundBox.fX2;
                stGuiFace.stFaceRect.y2 = stDmsResult.stFaceBoundBox.fY2;
                stGuiFace.u32PointNum = LANDMARK_POINT_NUM;
                for (i = 0; i < LANDMARK_POINT_NUM; i++)
                {
                    stGuiFace.astFacePoints[i].x = stDmsResult.pfLandmarkPoints[i*2];
                    stGuiFace.astFacePoints[i].y = stDmsResult.pfLandmarkPoints[i*2+1];
                }

                if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
                    stGuiFace.bDisablePoint = SV_TRUE;

                float fLEyeEndPoint[2] = {0};
                float fREyeEndPoint[2] = {0};
                uint8 u8LPointNum = 26;
                uint8 u8RPointNum = 34;
                dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8LPointNum], stDmsResult.pfLandmarkPoints[u8LPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fLEyeEndPoint[0], fLEyeEndPoint[1]);
                dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8RPointNum], stDmsResult.pfLandmarkPoints[u8RPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fREyeEndPoint[0], fREyeEndPoint[1]);

                stGuiFace.stGazeLine[0].x1 = stDmsResult.pfLandmarkPoints[u8LPointNum];
                stGuiFace.stGazeLine[0].y1 = stDmsResult.pfLandmarkPoints[u8LPointNum+1];
                stGuiFace.stGazeLine[0].x2 = fLEyeEndPoint[0];
                stGuiFace.stGazeLine[0].y2 = fLEyeEndPoint[1];
                stGuiFace.stGazeLine[0].color = GUI_COLOR_BLUE;
                stGuiFace.stGazeLine[0].stick = 5;

                stGuiFace.stGazeLine[1].x1 = stDmsResult.pfLandmarkPoints[u8RPointNum];
                stGuiFace.stGazeLine[1].y1 = stDmsResult.pfLandmarkPoints[u8RPointNum+1];
                stGuiFace.stGazeLine[1].x2 = fREyeEndPoint[0];
                stGuiFace.stGazeLine[1].y2 = fREyeEndPoint[1];
                stGuiFace.stGazeLine[1].color = GUI_COLOR_BLUE;
                stGuiFace.stGazeLine[1].stick = 5;
                #if 0
                printf("line1 point1(%f, %f)---(%f, %f), point2(%f, %f)---(%f, %f)\n", stGuiFace.stGazeLine[0].x1, stGuiFace.stGazeLine[0].y1, stGuiFace.stGazeLine[0].x2, stGuiFace.stGazeLine[0].y2,
                        stGuiFace.stGazeLine[1].x1, stGuiFace.stGazeLine[1].y1, stGuiFace.stGazeLine[1].x2, stGuiFace.stGazeLine[1].y2);
                #endif
                if (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsGazeTracking || pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
                    stGuiFace.bDisableGaze = SV_TRUE;

                if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
                {
                    for (j = 0; j < 2; j++)
                        stGuiFace.stDDawData.s32Ecr[j] = (sint32)(stDDAWResult.pfEcr[j]*100.0);
                    for (j = 0; j < 3; j++)
                        stGuiFace.stDDawData.s32HeadPose[j] = (sint32)stDDAWResult.pfHeadPose[j];
                    stGuiFace.stDDawData.s32BlinkPerMin = stDDAWResult.s32BlinkPerMin;
                    stGuiFace.stDDawData.s32YawnPerMin = stDDAWResult.s32YawnPerMin;
                    stGuiFace.stDDawData.s32DDAWScore = stDDAWResult.s32DDAWScore;
                    stGuiFace.stDDawData.fEcrGrad = stDDAWResult.fEcrGrad;
                    stGuiFace.stDDawData.fGazeGrad = stDDAWResult.fGazeGrad;
                    stGuiFace.stDDawData.fPoseYawGrad = stDDAWResult.fPoseYawGrad;
                    stGuiFace.stDDawData.fPosePitchGrad = stDDAWResult.fPosePitchGrad;
                    stGuiFace.stDDawData.bOcclusion = (SV_BOOL)stDDAWResult.bOcclusion;
                    stGuiFace.bDdawMode = SV_FALSE;
                }
            }
            else
            {
                stGuiFace.stFaceRect.color = GUI_COLOR_YELLOW;
                stGuiFace.u32PointNum = 0;
            }

            //添加人脸绘制操作
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_DRAW_FACE);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiFace);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
            }
        }
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);
    }

    dmm_munmap((void **)&apvBuf);
}

/******************************************************************************
 * 函数功能: DMS算法检测执行体
 * 输入参数: pcsDmmDetector --- DMS算法检测器
             pcsAlgStatus --- 算法检测状态
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void dmm_Det_ForwardBody(DMM_INFO_S *pstDmmInfo)
{
    sint32 s32Ret = 0, i = 0, j = 0;
    sint32 s32SemChn = 0;
    sint32 s32Cnt = 0;
    sint32 s32Total = 0;
    uint32 u32StepTimeMs = 0;
    uint32 u32NoFaceTimeMs = 0;   /* 连续无司机的时间 */
    SV_BOOL bNoface = SV_FALSE;
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
    struct timespec tvBegin, tvEnd;
    struct timespec tvBeginAll, tvEndAll;
    sint32 s32GpsStatus, s32GpsSpeed;     /* GPS状态, GPS速度 */
    ALARM_TYPE_E enAlarmType = ALARM_NOTHING;
#if ALG_MUTLIT_BUFFER
    void *apvBuf[3] = {NULL};
#else
    void *apvBuf = NULL;
#endif
    void *apvDstBuf = NULL;
    sint32 s32Idx;
    sint64 s64Last = 0, s64Now = 0;
    uint32 u32BufLen = DMM_IMAGE_WIDTH*DMM_IMAGE_HEIGHT*1.5;
    uint64 u64Pts = 0;

    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg || NULL == pstDmmInfo->pcsDmsAnalyzer)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return ;
    }

    DMM_SYNC_INFO_S *pstDmmSyncInfo = (DMM_SYNC_INFO_S *)&pstDmmInfo->stDmmSyncInfo;
    //pstDmmSyncInfo->bPostResReady = SV_TRUE;
    sint32 *s32AlarmTimeMs = pstDmmSyncInfo->s32AlarmTimeMs;
    SV_BOOL *bAlarm = pstDmmSyncInfo->bAlarm;
    sint32 s32AlarmTimeMsLast[ALARM_DMS_BUFF] = {0};                            /* 上一次设置的检测检测 */

    for (i = 0; i < ALARM_DMS_BUFF; i++)
    {
        if (i != ALARM_OVERSPEED)
        {
            s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
        }
        s32AlarmTimeMsLast[i] = m_stDmmConf[i].s32DectectInterval;
    }

    CDmsAlg             *pcsDmsAlg = pstDmmInfo->pcsDmsAlg;                     /* DMS算法类 */
    CDmsAnalyzer        *pcsDmsAnalyzer = pstDmmInfo->pcsDmsAnalyzer;           /* DMS行为分析器 */
    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   *pstDmsResult = &stDmsResult;
    memset(stDmsResult.pfLandmarkPoints, 0, sizeof(float)*LANDMARK_POINT_NUM*2);

    s32Ret = dmm_mmap((void **)&apvBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mmap failed.\n");
        return ;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32SemChn = 0;
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion() && BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))
    {
        s32SemChn = 0;
    }
    else
    {
        s32SemChn = 1;
    }
#else
    s32SemChn = 1;
#endif

    print_level(SV_INFO, "enter dmm_Det_ForwardBody.\n");
    clock_gettime(CLOCK_MONOTONIC, &tvLast);
    s64Last = dmm_GetTimeTickMs();
    while (pstDmmInfo->bRunning)
    {
        if (pstDmmInfo->enRunStat != DMM_RUN_DETECTION || CLICK_TYPE_PRESS_OFF == pstDmmInfo->stDDAWInfo.enClickType)
        {
            //print_level(SV_INFO, "enRunStat change: %d -> %d\n", DMM_RUN_DETECTION, pstDmmInfo->enRunStat);
            break;
        }

#if DMM_DEBUG_TIME
        clock_gettime(CLOCK_MONOTONIC, &tvBeginAll);
#endif
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        if ((BOARD_ADA32N1_V2 != BOARD_GetVersion()) || (!BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M)))
        {
            s32Ret = dmm_GetGpsResults(s32GpsStatus, s32GpsSpeed);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_GetGpsResults fail.\n");
                sleep_ms(1);
                continue;
            }
        }

        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        u32StepTimeMs = ((tvNow.tv_sec*1000 + tvNow.tv_nsec/1000000) - (tvLast.tv_sec*1000 + tvLast.tv_nsec/1000000));
        tvLast = tvNow;
        enAlarmType = ALARM_NOTHING;

#if 0 // Calculate the delay time per 100 times
        s32Cnt++;
        s32Total += u32StepTimeMs;
        if (s32Cnt >= 100)
        {
            print_level(SV_DEBUG, "dmm_Det_ForwardBody delay: %d...\n", s32Total / s32Cnt);
            s32Cnt = 0;
            s32Total = 0;
        }
#endif

        //print_level(SV_INFO, "dmm_Det_ForwardBody u32StepTimeMs: %d\n", u32StepTimeMs);
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        for (i = 0; i < ALARM_DMS_BUFF; i++)
        {
            if (m_stDmmConf[i].bIntervalChanged)
            {
                print_level(SV_INFO, "dectectInterval[%d] %d change to %d\n", i, s32AlarmTimeMsLast[i], m_stDmmConf[i].s32DectectInterval);
                if (s32AlarmTimeMsLast[i] >= 0)
                {
                    bAlarm[i] = SV_TRUE;
                    s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
                }
                s32AlarmTimeMsLast[i] = m_stDmmConf[i].s32DectectInterval;

                if (i == NOTIFY_LNGIN_CHANGE_GUARD)
                {
                    bNoface = SV_FALSE;
                    u32NoFaceTimeMs = 0;
                }

                m_stDmmConf[i].bIntervalChanged = false;
            }

            switch (i)
            {
                case ALARM_FATIGUE:
                    if (m_stDmmConf[i].s32DectectInterval >= 0)
                    {
                        if (bAlarm[i])
                        {
                            bool bSwitch = true;
                            s32AlarmTimeMs[i] -= u32StepTimeMs;
                            bSwitch = (s32AlarmTimeMs[i] <= 0);

                            if (m_stDmmConf[ALARM_FATIGUE_L2].s32DectectInterval >= 0)  // 针对二级疲劳使能
                            {
                                if(pstDmmSyncInfo->s32FatigueLast >= 0)
                                {
                                    pstDmmSyncInfo->s32FatigueLast -= u32StepTimeMs;
                                    bSwitch = (pstDmmSyncInfo->s32FatigueLast >= 0);
                                }
                            }
                            dmm_setFuncSwitch(i, bSwitch);
                        }
                        else
                        {
                            dmm_setFuncSwitch(i, true);
                        }

                        if (bAlarm[i] && s32AlarmTimeMs[i] <= 0)  /* 报警触发超过一定时间后重新复位 */
                        {
                            bAlarm[i] = SV_FALSE;
                            bAlarm[ALARM_FATIGUE_L2] = SV_FALSE;
                            pstDmmSyncInfo->s32FatigueLast = -1;
                            s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
                        }
                    }
                    else
                    {
                        dmm_setFuncSwitch(i, false);
                    }
                    break;

                case ALARM_DISTRACTION:
                case ALARM_NO_DRIVER:
                case ALARM_SMOKE:
                case ALARM_PHONE:
                case ALARM_YAWN:
                case ALARM_NO_MASK:
                case ALARM_SUNGLASS:
                case ALARM_NO_SEATBELT:
                case ALARM_SHELTER:
                case ALARM_FATIGUE_L2:
                case ALARM_DRINK_EAT:
                case ALARM_NO_HELMET:
                    if (m_stDmmConf[i].s32DectectInterval >= 0)
                    {
                        if (bAlarm[i])
                        {
                            s32AlarmTimeMs[i] -= u32StepTimeMs;
                            dmm_setFuncSwitch(i, (s32AlarmTimeMs[i] <= 0));
                        }
                        else
                        {
                            dmm_setFuncSwitch(i, true);
                        }

                        if (bAlarm[i] && s32AlarmTimeMs[i] <= 0)
                        {
                            bAlarm[i] = SV_FALSE;
                            s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
                        }
                    }
                    else
                    {
                        dmm_setFuncSwitch(i, false);
                    }
                    break;

                case ALARM_OVERSPEED:
                    if (m_stDmmConf[i].s32DectectInterval >= 0)
                    {
                        if (s32AlarmTimeMs[i] <= 0)
                        {
                            if (s32GpsStatus != 0)
                            {
                                if (s32GpsSpeed > pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsOverspeedLimit)
                                {
                                    enAlarmType = ALARM_OVERSPEED;
                                    bAlarm[i] = SV_TRUE;
                                    s32AlarmTimeMs[i] = m_stDmmConf[i].s32DectectInterval;
                                    goto det_finish;
                                }
                                else
                                {
                                    s32AlarmTimeMs[i] = 0;
                                }
                            }
                            else
                            {
                                s32AlarmTimeMs[i] = 0;
                            }
                        }

                        if (bAlarm[i])
                        {
                            s32AlarmTimeMs[i] -= u32StepTimeMs;
                        }
                        if (bAlarm[i] && s32AlarmTimeMs[i] <= 0)
                        {
                            bAlarm[i] = SV_FALSE;
                        }
                    }
                    break;

                default:
                    continue;
            }
        }

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        /* 锁住计算资源,保证同一时刻只跑一个算法*/
        s32Ret = ALG_Calculate_Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        u64Pts = 0;
        s32Ret = dmm_PBuf(s32SemChn, s32Idx, &u64Pts);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "dmm_PBuf failed. [err=%d]\n", s32Ret);
            ALG_Calculate_unLock();
            sleep_ms(1);
            continue;
        }
        apvDstBuf = dmm_getDstBuf((void **)&apvBuf, s32Idx);
        pstDmmSyncInfo->u64TimeStamp = u64Pts;

#if 0 // for debug
        char *pszFileName = NULL;
        pszFileName = "/mnt/nfs/dmm.rgb";
        FILE *fp = fopen(pszFileName, "wb+");
        if (NULL != fp)
        {
            fwrite(apvDstBuf, 1, u32BufLen, fp);
            fclose(fp);
            print_level(SV_INFO, "write finish!\n");
            //MS_V(s32SemChn);
            //ALG_Calculate_unLock();
            //exit(1);
        }
#endif

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);
        clock_gettime(CLOCK_MONOTONIC, &tvBegin);

        /* 模型向前推断函数 */
        s32Ret = dmm_Forward(pcsDmsAlg, &apvDstBuf, &pstDmsResult, DMM_ALG_TYPE_DETECTION);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "dmm_Forward failed. [err=%d]\n", s32Ret);
            dmm_VBuf(s32SemChn, s32Idx);
            ALG_Calculate_unLock();
            sleep_ms(10);
            continue;
        }
        clock_gettime(CLOCK_MONOTONIC, &tvEnd);
        pstDmmSyncInfo->s64ForwardMs = (tvEnd.tv_sec*1000 + tvEnd.tv_nsec/1000000) - (tvBegin.tv_sec*1000 + tvBegin.tv_nsec/1000000);

#if DMM_DEBUG_TIME
        print_level(SV_INFO, "forward eclipse: %dms\n", pstDmmSyncInfo->s64ForwardMs);
#endif

        /* 退出临界区 */
        dmm_VBuf(s32SemChn, s32Idx);
        ALG_Calculate_unLock();

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

det_finish:

        //上次的结果还没处理完，再把此次前推的结果丢出去
        AS_Forward_P();

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        pstDmmSyncInfo->stDmsResult = stDmsResult;
        pstDmmSyncInfo->enAlarmType = enAlarmType;
        pstDmmSyncInfo->stGpsRes.s32GpsStatus = s32GpsStatus;
        pstDmmSyncInfo->stGpsRes.s32GpsSpeed = s32GpsSpeed;

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        AS_PostProcess_V();

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

#if DMM_DEBUG_TIME
        clock_gettime(CLOCK_MONOTONIC, &tvEndAll);
        print_level(SV_INFO, "forward all eclipse: %dms\n", (tvEndAll.tv_sec*1000 + tvEndAll.tv_nsec/1000000) - (tvBeginAll.tv_sec*1000 + tvBeginAll.tv_nsec/1000000));
#endif
    }
    print_level(SV_INFO, "exit dmm_Det_ForwardBody.\n");

    dmm_munmap((void **)&apvBuf);
    if (AS_GetValue_PostProcessing() <= 0)
    {
        AS_PostProcess_V();
    }
}

void *dmm_Det_ProcessBody(void *pvArg)
{
    DMM_INFO_S *pstDmmInfo = (DMM_INFO_S *)pvArg;
    if (NULL == pstDmmInfo || NULL == pstDmmInfo->pcsDmsAlg || NULL == pstDmmInfo->pcsDmsAnalyzer)
    {
        print_level(SV_ERROR, "input null ptr.\n");
        return NULL;
    }

    sint32 s32Ret = 0, i = 0, j = 0;
    sint32 s32Cnt = 0;
    sint32 s32Total = 0;
    float fSensitivity;
    uint32 u32StepTimeMs = 0;
    uint32 u32NoFaceTimeMs = 0;         /* 连续无司机的时间 */
    uint32 u32FaceCapTimeMs = 0;        /* 抓取首张人脸所用的时间 */
    SV_BOOL bNoface = SV_FALSE;
    uint32 u32FaceDetectTimeMs = 0;     /* 人脸检测的时间计时，北京速力需求，计时到一分钟，进行一次人脸检测 */
    SV_BOOL bAngleOut = SV_FALSE;       /* 人脸角度是否超出合理范围，北京速力需求，能正常检测到人脸才跳转到人脸识别 */
    SV_BOOL bStartDetAngle = SV_FALSE;  /* 是否开始检测人脸角度 */
    uint32 u32CorrectAngleCnt = 0;      /* 人脸自动识别时，角度连续三次不超出合理范围才认为可以进行人脸识别 */
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
    struct timespec tvBegin, tvEnd;
    MSG_PACKET_S stMsgPkt = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_FACE_S stGuiFace = {0};
    MEDIA_GUI_ALARM_DMM_S stGuiAlarmDmm = {0};
    MEDIA_GUI_NULL_S stGuiNull;
    uint16 u16mask;
    sint32 s32GpsStatus, s32GpsSpeed;     /* GPS状态, GPS速度 */
    ALARM_TYPE_E enAlarmType = ALARM_NOTHING, enAlarmTypeLast = ALARM_NOTHING;
    sint32 s32RepeatTime = 0;
    ALARM_EVENT_S stAlarmEvent = {0};
    DMM_EYELID_S stDmmEyelid = {0};
    SV_BOOL bWorkingLowSpeed = SV_FALSE;
    SV_BOOL bGetShelter = SV_FALSE;
    uint32 u32DumpNum = 0;
    uint32 u32WorkSpeedIndex = 0;
    SV_BOOL bOsd = SV_FALSE;
    DUMP_DMM_S stDumpDmmInfo = {0};
    sint64 s64Last = 0, s64Now = 0;
    sint64 s64TimeBegin = 0, s64TimeNow = 0;        /* 遮挡和太阳眼镜时间统计 */
    uint32 u32TotolFrameCnt = 0, u32FaultCnt = 0;
    uint32 u32ShelterCnt = 0, u32SunglassCnt = 0;
    uint32 u32DDAWL7Cnt = 0, u32DDAWL8Cnt = 0, u32DDAWL9Cnt = 0, u32DDAWNoAlarmCnt = 0;
    const uint32 u32FaultStartCnt = 6;
    float fFaultRate = 0.0;
    SV_BOOL bGetShelterStartTime = SV_FALSE;
    SV_BOOL bGetSunglassStartTime = SV_FALSE;
    SV_BOOL bFault = SV_FALSE;
    bool bDetectFaceLast = false;
    SV_BOOL bVirDDAWAlarm = SV_FALSE;
    sint64 s64DDAWAlarmOffBeginTime = 0, s64DDAWAlarmOffEndTime = 0;
    CalibrateState enCalibStateLast = E_DMS_FIRST_CALIBRATING;
    sint32 ps32CalibrateHeadAngleNow[2];
    SV_BOOL bAutoCalibStateChanged = SV_FALSE;
    SV_BOOL bAutoCalibAngleChanged = SV_FALSE;

    STDMSResult         stDmsAnalysisRes;
    STDmsDetectResult   stDmsResult;
    STDmsDetectResult   stDmsResultLast;
    STDDAWResult        stDDAWResult;
    STCalibrateResult   stCalibResult = {0};
    EDmsAlgStateCode    enWarnState = E_STATE_NORMAL;

    s32Ret = prctl(PR_SET_NAME, "dmm_Det_ProcessBody");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    s32Ret = pthread_detach(pthread_self());
    if (s32Ret != 0)
    {
        pthread_exit(NULL);
    }

    DMM_SYNC_INFO_S *pstDmmSyncInfo = (DMM_SYNC_INFO_S *)&pstDmmInfo->stDmmSyncInfo;
    sint32 *s32AlarmTimeMs = pstDmmSyncInfo->s32AlarmTimeMs;
    SV_BOOL *bAlarm = pstDmmSyncInfo->bAlarm;
    CDmsAnalyzer *pcsDmsAnalyzer = pstDmmInfo->pcsDmsAnalyzer;
    bool bClear = false;

    clock_gettime(CLOCK_MONOTONIC, &tvLast);
    s64Last = dmm_GetTimeTickMs();

    print_level(SV_INFO, "enter dmm_Det_ProcessBody.\n");
    while (pstDmmInfo->bRunning)
    {
        if (!pstDmmInfo->bDetectRunning && !bClear)
        {
            pcsDmsAnalyzer->SetTimerZero();

            u32NoFaceTimeMs = 0;
            u32FaceCapTimeMs = 0;
            u32FaceDetectTimeMs = 0;
            bNoface = SV_FALSE;
            bAngleOut = SV_FALSE;
            bStartDetAngle = SV_FALSE;
            u32CorrectAngleCnt = 0;
            enAlarmType = ALARM_NOTHING;
            enAlarmTypeLast = ALARM_NOTHING;
            s32RepeatTime = 0;
            bWorkingLowSpeed = SV_FALSE;
            bGetShelter = SV_FALSE;
            u32DumpNum = 0;
            u32WorkSpeedIndex = 0;
            bOsd = SV_FALSE;
            u32TotolFrameCnt = 0;
            u32FaultCnt = 0;
            u32ShelterCnt = 0;
            u32SunglassCnt = 0;
            u32DDAWL7Cnt = 0;
            u32DDAWL8Cnt = 0;
            u32DDAWL9Cnt = 0;
            u32DDAWNoAlarmCnt = 0;
            bGetShelterStartTime = SV_FALSE;
            bGetSunglassStartTime = SV_FALSE;
            bFault = SV_FALSE;
            bDetectFaceLast = false;
            bVirDDAWAlarm = SV_FALSE;
            s64DDAWAlarmOffBeginTime = 0;
            s64DDAWAlarmOffEndTime = 0;

            bClear = true;
        }

        clock_gettime(CLOCK_MONOTONIC, &tvBegin);
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        /* P操作进入临界区 */
        s32Ret = AS_PostProcess_P();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "AS_PostProcess_P failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }
        bClear = false;

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        s32GpsStatus = pstDmmSyncInfo->stGpsRes.s32GpsStatus;
        s32GpsSpeed = pstDmmSyncInfo->stGpsRes.s32GpsSpeed;
        stDmsResult = pstDmmSyncInfo->stDmsResult;
        /* 过滤掉全图检测时导致的一帧数据无效的情况 */
        if (!stDmsResult.bDetectFace && bDetectFaceLast)
        {
            stDmsResult = stDmsResultLast;
        }
        bDetectFaceLast = pstDmmSyncInfo->stDmsResult.bDetectFace;
        stDmsResultLast = pstDmmSyncInfo->stDmsResult;

        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        u32StepTimeMs = ((tvNow.tv_sec*1000 + tvNow.tv_nsec/1000000) - (tvLast.tv_sec*1000 + tvLast.tv_nsec/1000000));
        tvLast = tvNow;
        //print_level(SV_DEBUG, "u32StepTimeMs: %d...\n", u32StepTimeMs);

#if 0 // Calculate the delay time per 100 times
        s32Cnt++;
        s32Total += u32StepTimeMs;
        if (s32Cnt >= 50)
        {
            print_level(SV_DEBUG, "dmm_Det_ProcessBody delay: %d...\n", s32Total / s32Cnt);
            s32Cnt = 0;
            s32Total = 0;
            #if 0
            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
            pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
            pstDmmInfo->bPlayLoginAudio = SV_FALSE;
            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
            goto exit;
            #endif
        }
#endif

        if (ALARM_NOTHING != pstDmmSyncInfo->enAlarmType)
        {
            enAlarmType = pstDmmSyncInfo->enAlarmType;
            goto submit_msg;
        }

        /* 设置检测灵敏度 */
        switch (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsSensitivity)
        {
            case -1:    /* 自动模式 */
                if(s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsMiddleSpeedThr)
                    fSensitivity = 1.0;
                else if(s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsHighSpeedThr)
                    fSensitivity = 0.66;
                else
                    fSensitivity = 0.33;
                break;
            case 0:
                fSensitivity = 1.0;
                break;
            case 1:
                fSensitivity = 0.66;
                break;
            case 2:
                fSensitivity = 0.33;
                break;
            default:
                fSensitivity = 1.0;
        }

        /* 行为分析 */
        stDmsAnalysisRes = pcsDmsAnalyzer->StateAnalysis(stDmsResult, fSensitivity);
        enWarnState = stDmsAnalysisRes.eDmsAlgStateCode;
        //print_level(SV_INFO, "enWarnState: %d\n", enWarnState);
        if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
            enWarnState = E_STATE_NORMAL;

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        do {
            switch (enWarnState)
            {
                case E_STATE_NORMAL:
                    enAlarmType          = ALARM_NOTHING;
                    break;
                case E_STATE_FATIGUE:
                    enAlarmType          = ALARM_FATIGUE;
                    if (bAlarm[enAlarmType] && s32AlarmTimeMs[enAlarmType] > 0)
                    {
                        print_level(SV_WARN, "fatigue time: %d, skip this alarm!\n", s32AlarmTimeMs[enAlarmType]);
                        enAlarmType = ALARM_NOTHING;
                    }
                    break;
                case E_STATE_FATIGUE_L2:
                    enAlarmType          = ALARM_FATIGUE_L2;
                    break;
                case E_STATE_DISTRACTION:
                    enAlarmType          = ALARM_DISTRACTION;
                    break;
                case E_STATE_NO_DRIVER:
                    enAlarmType          = ALARM_NO_DRIVER;
                    break;
                case E_STATE_SMOKE:
                    enAlarmType          = ALARM_SMOKE;
                    break;
                case E_STATE_PHONE:
                    enAlarmType          = ALARM_PHONE;
                    break;
                case E_STATE_YAWN:
                    enAlarmType          = ALARM_YAWN;
                    break;
                case E_STATE_NO_MASK:
                    enAlarmType          = ALARM_NO_MASK;
                    break;
                case E_STATE_SUNGLASSES:
                    enAlarmType          = ALARM_SUNGLASS;
                    break;
                case E_STATE_NO_SEATBELT:
                    enAlarmType          = ALARM_NO_SEATBELT;
                    break;
                case E_STATE_OCCLUSION:
                    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
                    {
                        enAlarmType      = ALARM_NO_DRIVER;
                    }
                    else
                    {
                        enAlarmType      = ALARM_SHELTER;
                    }
                    bGetShelter = SV_TRUE;
                    break;
                case E_STATE_DRINK:
                    enAlarmType          = ALARM_DRINK_EAT;
                    break;
                case E_STATE_NO_HELMET:
                    enAlarmType          = ALARM_NO_HELMET;
                    break;
                default:
                    break;
            }

            if (stDmsResult.bDetectFace)
            {
                float angleDiff = abs(stDmsResult.pfpHeadPose[1] - pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle[1]);
                if (angleDiff >= 30)
                {
                    u32CorrectAngleCnt = 0;
                }
                else
                {
                    u32CorrectAngleCnt++;
                    //print_level(SV_INFO, "u32CorrectAngleCnt: %d\n", u32CorrectAngleCnt);
                }

                if (u32CorrectAngleCnt >= 10)
                {
                    bAngleOut = SV_FALSE;
                }
                else
                {
                    bAngleOut = SV_TRUE;
                }
            }
            else
            {
                u32CorrectAngleCnt = 0;
            }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32N1))
            /* 定时人脸识别处理，展会专用版不使用该功能，展会专用版无人脸大于5s后自动进行一次人脸识别 */
            if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_EXHIBITION))
            {
                if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsFrInterval <= 0)
                {
                    goto interval_exit;
                }

                u32FaceDetectTimeMs += u32StepTimeMs;
                if (u32FaceDetectTimeMs > pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsFrInterval * 1000)
                {
                    if (!bStartDetAngle)
                    {
                        bStartDetAngle = SV_TRUE;
                        u32CorrectAngleCnt = 0;
                        bAngleOut = SV_TRUE;
                    }
                }
                else
                {
                    bStartDetAngle = SV_FALSE;
                }

                if (bStartDetAngle && !bAngleOut)
                {
                    pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                    pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                    pstDmmInfo->bPlayLoginAudio = SV_FALSE;
                    pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                    goto exit;
                }
            }
            else
            {
                if (pstDmmInfo->bFirstFace)
                {
                    if (stDmsResult.bDetectFace)
                    {
                        if (!bStartDetAngle)
                        {
                            bStartDetAngle = SV_TRUE;
                            u32CorrectAngleCnt = 0;
                            bAngleOut = SV_TRUE;
                        }

                        if (bStartDetAngle && !bAngleOut)
                        {
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                            pstDmmInfo->bPlayLoginAudio = SV_FALSE;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "recognition first face.\n");
                            goto exit;
                        }
                    }
                    else
                    {
                        bStartDetAngle = SV_FALSE;
                    }
                }
                else
                {
                    if (!bNoface && !stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs += u32StepTimeMs;
                        if (u32NoFaceTimeMs >= DMM_NO_FACE_TIME_MS_EX)
                        {
                            print_level(SV_INFO, "no face time exceeds %dms!!!\n", DMM_NO_FACE_TIME_MS_EX);
                            memset(pstDmmInfo->stDumpDmmInfo.u8UsrId, 0, sizeof(pstDmmInfo->stDumpDmmInfo.u8UsrId));
                            memset(pstDmmInfo->stDumpDmmInfo.u8UsrName, 0, sizeof(pstDmmInfo->stDumpDmmInfo.u8UsrName));
                            bNoface = SV_TRUE;
                        }
                    }
                    else if (stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs = 0;
                    }

                    if (bNoface)
                    {
                        if (stDmsResult.bDetectFace)
                        {
                            if (!bStartDetAngle)
                            {
                                bStartDetAngle = SV_TRUE;
                                u32CorrectAngleCnt = 0;
                                bAngleOut = SV_TRUE;
                            }
                        }
                        else
                        {
                            bStartDetAngle = SV_FALSE;
                        }

                        if (bStartDetAngle && !bAngleOut)
                        {
                            bNoface = SV_FALSE;
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                            pstDmmInfo->bPlayLoginAudio = SV_FALSE;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "face is not angle out, enter DMM_RUN_RECOGNITION....\n");
                            goto exit;
                        }
                    }
                }
            }

interval_exit:

            /* CREARE客户，连续一段时间无司机后，若人脸再次出现，则识别一次人脸，检测是否换司机 */
            if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
            {
                if (m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval < 0
                    && (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFaceCapture || (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFaceCapture && !pstDmmInfo->bNeedFaceCap)))
                {
                    //print_level(SV_INFO, "bNeedFaceCap: %d, bStartFaceCap: %d\n", pstDmmInfo->bNeedFaceCap, pstDmmInfo->bStartFaceCap);
                    goto creare_exit;
                }

                if (pstDmmInfo->bNeedFaceCap && !pstDmmInfo->bStartFaceCap)
                {
                    if (!COMMON_IsPathExist(DMM_START_FACE_CAP))
                    {
                        if (m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval >= 0)
                        {
                            goto faceCap_exit;
                        }
                        else
                        {
                            goto creare_exit;
                        }
                    }
                    print_level(SV_INFO, "play welcome and start face capture!\n");
                    pstDmmInfo->bStartFaceCap = SV_TRUE;
                    s32Ret = ALARM_PlayWelcome();
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "ALARM_PlayWelcome failed. [err=%#x]\n", s32Ret);
                    }
                    remove(DMM_START_FACE_CAP);
                }

                if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFaceCapture && pstDmmInfo->bStartFaceCap)
                {
                    u32FaceCapTimeMs += u32StepTimeMs;
                    //print_level(SV_INFO, "u32FaceCapTimeMs: %d\n", u32FaceCapTimeMs);
                    if (u32FaceCapTimeMs >= DMM_FACE_CAP_TIMEOUT)
                    {
                        print_level(SV_INFO, "face capture timeout!\n");
                        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
                        stMsgPkt.stMsg.u8Param = 2;
                        s32Ret = Msg_submitEvent(EP_CAN, OP_EVENT_CAN_DVR_FACE_CAPTURE, &stMsgPkt);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_INFO, "Msg_submitEvent failed!\n");
                        }
                        pstDmmInfo->bNeedFaceCap = SV_FALSE;
                        pstDmmInfo->bStartFaceCap = SV_FALSE;
                    }
                }
faceCap_exit:

                if (pstDmmInfo->bFirstFace || pstDmmInfo->bStartFaceCap)
                {
                    if (stDmsResult.bDetectFace)
                    {
                        if (!bStartDetAngle)
                        {
                            bStartDetAngle = SV_TRUE;
                            u32CorrectAngleCnt = 0;
                            bAngleOut = SV_TRUE;
                        }
                    }
                    else
                    {
                        bStartDetAngle = SV_FALSE;
                    }
                }

                if (pstDmmInfo->bStartFaceCap)
                {
                    if (bStartDetAngle && !bAngleOut)
                    {
                        print_level(SV_INFO, "face capture!!!\n");
                        pstDmmInfo->bNeedFaceCap = SV_FALSE;
                        pstDmmInfo->bStartFaceCap = SV_FALSE;
                        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
                        stMsgPkt.stMsg.u8Param = 1;
                        s32Ret = Msg_submitEvent(EP_CAN, OP_EVENT_CAN_DVR_FACE_CAPTURE, &stMsgPkt);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_INFO, "Msg_submitEvent failed!\n");
                        }
                    }
                }

                /* 上电后跳到人脸识别线程记录第一张出现的人脸 */
                if (pstDmmInfo->bFirstFace)
                {
                    if (bStartDetAngle && !bAngleOut)
                    {
                        if (m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval >= 0)
                        {
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_CHANGE_DRIVER;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "record first face.\n");
                            goto exit;
                        }
                    }
                }
                else
                {
                    if (!bNoface && !stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs += u32StepTimeMs;
                        if (u32NoFaceTimeMs >= DMM_NO_FACE_TIME_MS)
                        {
                            print_level(SV_INFO, "no face time exceeds %dms!!!\n", DMM_NO_FACE_TIME_MS);
                            bNoface = SV_TRUE;
                        }
                    }
                    else if (stDmsResult.bDetectFace)
                    {
                        u32NoFaceTimeMs = 0;
                    }

                    if (bNoface)
                    {
                        if (stDmsResult.bDetectFace)
                        {
                            if (!bStartDetAngle)
                            {
                                bStartDetAngle = SV_TRUE;
                                u32CorrectAngleCnt = 0;
                                bAngleOut = SV_TRUE;
                            }
                        }
                        else
                        {
                            bStartDetAngle = SV_FALSE;
                        }

                        if (bStartDetAngle && !bAngleOut)
                        {
                            bNoface = SV_FALSE;
                            pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                            pstDmmInfo->enRunStat = DMM_RUN_CHANGE_DRIVER;
                            pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                            print_level(SV_INFO, "face is not angle out, enter DMM_RUN_CHANGE_DRIVER....\n");
                            goto exit;
                        }
                    }
                }
            }
creare_exit:;
#endif
            dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

            /* 如果检测到无司机，则准备跳到登录阶段 */
            if (E_LOGIN_AUTO == pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.enDmsLoginMode && stDmsResult.bDetectFace && ((pstDmmInfo->bFirstFace || ALARM_NO_DRIVER == enAlarmTypeLast || ALARM_SHELTER == enAlarmTypeLast)))
            {
                if (!bAngleOut)
                {
                    pthread_mutex_lock(&pstDmmInfo->mutexRunStat);
                    print_level(SV_INFO, "ready to login....\n");
                    pstDmmInfo->enRunStat = DMM_RUN_RECOGNITION;
                    pstDmmInfo->bPlayLoginAudio = SV_TRUE;
                    pthread_mutex_unlock(&pstDmmInfo->mutexRunStat);
                    goto exit;
                }
                else
                {
                    print_level(SV_WARN, "waiting for center angle to fr....\n");
                    AS_Forward_V();
                    continue;
                }
            }

submit_msg:
			bOsd = SV_FALSE;
            if (enAlarmType == ALARM_NOTHING)
            {
                if (enAlarmTypeLast == ALARM_NO_DRIVER && s32RepeatTime > 1 && stDmsResult.bDetectFace)
                {
                    s32AlarmTimeMs[ALARM_NO_DRIVER] = pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32NoDriverInterval * 1000;
                    enAlarmTypeLast = ALARM_NOTHING;
                    s32RepeatTime = 0;
                }

                if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[enAlarmType] <= 0
                    || (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[enAlarmType] > 0 && s32GpsStatus != 0 && s32GpsSpeed >= pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[enAlarmType])
                    || pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS)
                {
                    bOsd = SV_TRUE;
                }
                break;
            }

            /* 工作速度判断 */
            if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
                u32WorkSpeedIndex = enAlarmType;
            else
                u32WorkSpeedIndex = 0;

            bWorkingLowSpeed = SV_FALSE;
            if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex] > 0)
            {
                if (s32GpsStatus == 0)
                {
                    if (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS)
                    {
                        enAlarmTypeLast = enAlarmType;
                        sleep_ms(30);
                        break;
                    }
                    else
                    {
                        if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
                        {
                            bWorkingLowSpeed = SV_TRUE;
                        }
                    }
                }
                else if (s32GpsStatus != 0 && s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex])
                {
                    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
                    {
                        bWorkingLowSpeed = SV_TRUE;
                    }
                    else
                    {
                        pcsDmsAnalyzer->SetTimerZero();
                        enAlarmTypeLast = enAlarmType;
                        sleep_ms(30);
                        break;
                    }
                }
            }
            bOsd = SV_TRUE;

            bAlarm[enAlarmType] = SV_TRUE;
            s32AlarmTimeMs[enAlarmType] = m_stDmmConf[enAlarmType].s32DectectInterval;

            if (enAlarmType == ALARM_FATIGUE)
            {
                if(m_stDmmConf[ALARM_FATIGUE_L2].s32DectectInterval >= 0)
                {
                    pstDmmSyncInfo->s32FatigueLast = 5 * 1000;
                }
                else
                {
                    pstDmmSyncInfo->s32FatigueLast = -1;
                }
            }

            /* 低速状态下触发的报警不推送报警事件 */
            if (!bWorkingLowSpeed)
            {
                /* 通知外部的结果统一使用ALARM_TYPE_E宏，函数内使用WarnState宏 */
                s32Ret = dmm_Alarm_Post((sint32)enAlarmType);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "dmm_Alarm_Post failed.\n");
                }

                memset(&stAlarmEvent, 0, sizeof(stAlarmEvent));
                stAlarmEvent.enAlarmEvent = ALARM_EVENT_DMM;
                stAlarmEvent.enAlarmType = enAlarmType;

                s32Ret = dmm_Alarm_Submit(stAlarmEvent);
                if (SV_SUCCESS != s32Ret)
                {
                	print_level(SV_ERROR, "dmm_Alarm_Submit failed!\n");
                }
            }

            stGuiAlarmDmm.enAlarmType = enAlarmType;
            stGuiAlarmDmm.enAlarmLevel = bWorkingLowSpeed ? ALARM_LEVEL_LOW : ALARM_LEVEL_HIGH;
            stGuiAlarmDmm.contime = 2;  //持续两秒钟
            s32Ret = dmm_PostDmmGui(&stGuiAlarmDmm);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_PostDmmGui failed. [err=%#x]\n", s32Ret);
            }

            print_level(SV_DEBUG, "enWarnState: %d\n", enWarnState);
            if (enAlarmType == ALARM_NO_DRIVER && s32RepeatTime >= 5);            /* 多次无司机报警屏蔽 */
            else if (enAlarmType == ALARM_SHELTER && s32RepeatTime >= 5);         /* 多次遮挡报警屏蔽 */
            else
            {
                print_level(SV_DEBUG, "enAlarmType: %d\n", enAlarmType);
                if (m_stDmmConf[ALARM_NOTHING].bDmsAudioEnable && m_stDmmConf[enAlarmType].bDmsAudioEnable && !bWorkingLowSpeed)
                {
                    s32Ret = dmm_Alarm_PlayAudio(enAlarmType);
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "dmm_Alarm_PlayAudio failed. [type:%d]\n", enAlarmType);
                    }
                }
            }

            if (enAlarmType != enAlarmTypeLast)
            {
                s32RepeatTime = 1;
                enAlarmTypeLast = enAlarmType;
                break;
            }

            /* case enAlarmType == enAlarmTypeLast */
            s32RepeatTime++;
            if(BOARD_IsNotCustomer(BOARD_C_DMS31V2_SHIQI))
            {
                break;
            }

            if(enAlarmType == ALARM_NO_DRIVER)
            {
                if(s32RepeatTime % 9 == 0)
                {
                    s32AlarmTimeMs[ALARM_NO_DRIVER] = 30 * 60 * 1000; // 延时30分钟
                }
                else if(s32RepeatTime % 3 == 0)
                {
                    s32AlarmTimeMs[ALARM_NO_DRIVER] = 3 * 60 * 1000; // 延时3分钟
                }
            }
            else if(enAlarmType == ALARM_PHONE)
            {
                if(s32RepeatTime % 9 == 0)
                {
                    s32AlarmTimeMs[ALARM_PHONE] = 10 * 60 * 1000; // 延时10分钟
                }
                else if(s32RepeatTime % 3 == 0)
                {
                    s32AlarmTimeMs[ALARM_PHONE] = 3 * 60 * 1000; // 延时3分钟
                }
            }
            else if(enAlarmType == ALARM_SMOKE)
            {
                if(s32RepeatTime % 9 == 0)
                {
                    s32AlarmTimeMs[ALARM_SMOKE] = 10 * 60 * 1000; // 延时10分钟
                }
                else if(s32RepeatTime % 3 == 0)
                {
                    s32AlarmTimeMs[ALARM_SMOKE] = 3 * 60 * 1000; // 延时3分钟
                }
            }
        } while(0);
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        do{
            if (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsAutoCalibration)
            {
                break;
            }

            if (s32GpsSpeed >= DMM_AUTOCALIB_MIN_SPEED)
            {
                pcsDmsAnalyzer->AutoCalibrate(s32GpsSpeed, stDmsResult);
            }

            stCalibResult = pcsDmsAnalyzer->GetCalibrateResult();
            #if 0
            print_level(SV_INFO, "state: %d, reason: %d, headpos: %f %f %f, progress: %f, bCaliSleep: %d\n", stCalibResult.eCalibrateState, \
                stCalibResult.eCalibrateFailReason, stCalibResult.pfCalibrateHeadPose[0], stCalibResult.pfCalibrateHeadPose[1], \
                stCalibResult.pfCalibrateHeadPose[2], stCalibResult.fCalibrateProgress, stCalibResult.bCaliSleep);
            #endif

            dmm_PostAutoCalibration(&stCalibResult);

            if (E_DMS_CALIBRATE_SUCCESS == stCalibResult.eCalibrateState || E_DMS_CALIBRATE_UPDATE == stCalibResult.eCalibrateState)
            {
                bAutoCalibStateChanged = SV_FALSE;
                bAutoCalibAngleChanged = SV_FALSE;

                /* 标定成功提示语只会在设备没标定过的时候播放一次音频 */
                if (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsCalibrated && E_DMS_CALIBRATE_SUCCESS == stCalibResult.eCalibrateState)
                {
                    dmm_Alarm_PlayAudio(NOTIFY_AUTO_CALIBRATION_SUCCESS);
                    bAutoCalibStateChanged = SV_TRUE;
                }

                if (E_DMS_CALIBRATE_UPDATE == stCalibResult.eCalibrateState)
                {
                    dmm_Alarm_PlayAudio(NOTIFY_AUTO_CORRECTION_SUCCESS);
                    bAutoCalibStateChanged = SV_TRUE;
                }

                for (i = 0; i < 2; i++)
                {
                    ps32CalibrateHeadAngleNow[i] = (sint32)(stCalibResult.pfCalibrateHeadPose[i] + (stCalibResult.pfCalibrateHeadPose[i] > 0) - 0.5);   // 四舍五入
                }
                if (STRUCT_Not_EQUAL(ps32CalibrateHeadAngleNow, pstDmmInfo->stCalibrateParam.ps32CalibrateHeadAngle))
                {
                    print_level(SV_INFO, "headAngle change: %d -> %d, %d -> %d\n", pstDmmInfo->stCalibrateParam.ps32CalibrateHeadAngle[0], ps32CalibrateHeadAngleNow[0], pstDmmInfo->stCalibrateParam.ps32CalibrateHeadAngle[1], ps32CalibrateHeadAngleNow[1]);
                    bAutoCalibAngleChanged = SV_TRUE;
                }

                if (bAutoCalibStateChanged || bAutoCalibAngleChanged)
                {
                    s32Ret = CONFIG_ReloadFile();
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "CONFIG_ReloadFile failed. [err=%#x]\n", s32Ret);
                    }

                    memcpy(pstDmmInfo->stCalibrateParam.ps32CalibrateHeadAngle, ps32CalibrateHeadAngleNow, sizeof(ps32CalibrateHeadAngleNow));
                    memcpy(pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle, ps32CalibrateHeadAngleNow, sizeof(ps32CalibrateHeadAngleNow));
                    pstDmmInfo->pcsDmsAnalyzer->CalibrateParamChange(pstDmmInfo->stCalibrateParam);     // 把标定结果回写给算法DMM
                    pstDmmInfo->pcsFrAnalyzer->SetCalibrateAngle(pstDmmInfo->stCalibrateParam);         // 把标定结果回写给算法FR

                    pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsCalibrated = SV_TRUE;
                    s32Ret = CONFIG_SetAlgParam(&pstDmmInfo->stCfgParam);
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "CONFIG_SetAlgParam failed. [err=%#x]\n", s32Ret);
                    }
                }
            }
            enCalibStateLast = stCalibResult.eCalibrateState;
        } while(0);

        do{
            stDDAWResult = pcsDmsAnalyzer->DDAWAnalysis(stDmsResult);
            if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_DDAW))
            {
                break;
            }

            #if 0
            print_level(SV_INFO, "%f, %f, %f, %f, %f\n", stDDAWResult.pfEcr[0], stDDAWResult.pfEcr[1], stDDAWResult.pfHeadPose[0], stDDAWResult.pfHeadPose[1], stDDAWResult.pfHeadPose[2]);
            print_level(SV_INFO, "%d, %d\n", stDDAWResult.s32BlinkPerMin, stDDAWResult.s32YawnPerMin);
            print_level(SV_INFO, "%f, %f, %f, %f\n", stDDAWResult.fEcrGrad, stDDAWResult.fGazeGrad, stDDAWResult.fPoseYawGrad, stDDAWResult.fPosePitchGrad);
            print_level(SV_INFO, "%d\n", stDDAWResult.s32DDAWScore);
            #endif
            //print_level(SV_INFO, "bYawn: %d, fOcclusionScore: %f, bOcclusion: %d, bSunglasses: %d\n", stDmsResult.bYawn, stDmsResult.fOcclusionScore, stDDAWResult.bOcclusion, stDDAWResult.bSunglasses);

            /* 3s内遮挡率达到一定比例判定为遮挡，要发生一次遮挡后才会开始计时 */
            if (!bGetShelterStartTime)
            {
                if (stDDAWResult.bOcclusion)
                {
                    u32ShelterCnt++;
                    if (u32ShelterCnt > u32FaultStartCnt)
                    {
reset_shelter:
                        s64TimeBegin = dmm_GetTimeTickMs();
                        u32TotolFrameCnt = 0;
                        u32FaultCnt = 0;
                        bGetShelterStartTime = SV_TRUE;
                        bGetSunglassStartTime = SV_FALSE;
                        print_level(SV_INFO, "reset shelter time begin!\n");
                    }
                }
                else
                {
                    u32ShelterCnt = 0;
                }
            }
            else
            {
                s64TimeNow = dmm_GetTimeTickMs();
                if (s64TimeNow - s64TimeBegin < 3000)
                {
                    u32TotolFrameCnt++;
                    if (stDDAWResult.bOcclusion)
                    {
                        u32FaultCnt++;
                    }
                }
                else
                {
                    fFaultRate = u32FaultCnt*1.0 / u32TotolFrameCnt;
                    print_level(SV_INFO, "shelter rate: %f\n", fFaultRate);
                    if (fFaultRate > 0.8)
                    {
                        bFault = SV_TRUE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_ON);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "shelter!!!!!\n");
                        goto reset_shelter;
                    }
                    else
                    {
                        bFault = SV_FALSE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_OFF);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "no shelter!!!!!\n");
                    }

                    u32ShelterCnt = 0;
                    u32FaultCnt = 0;
                    u32TotolFrameCnt = 0;
                    s64TimeBegin = s64TimeNow;
                    bGetShelterStartTime = SV_FALSE;
                    printf("\n");
                }
            }

            if (!bGetSunglassStartTime)
            {
                if (stDDAWResult.bSunglasses)
                {
                    u32SunglassCnt++;
                    if (u32SunglassCnt > u32FaultStartCnt)
                    {
reset_sunglasses:
                        s64TimeBegin = dmm_GetTimeTickMs();
                        u32TotolFrameCnt = 0;
                        u32FaultCnt = 0;
                        bGetSunglassStartTime = SV_TRUE;
                        bGetShelterStartTime = SV_FALSE;
                        print_level(SV_INFO, "reset sunglass time begin!\n");
                    }
                }
                else
                {
                    u32SunglassCnt = 0;
                }
            }
            else
            {
                s64TimeNow = dmm_GetTimeTickMs();
                if (s64TimeNow - s64TimeBegin < 3000)
                {
                    u32TotolFrameCnt++;
                    if (stDDAWResult.bSunglasses)
                    {
                        u32FaultCnt++;
                    }
                }
                else
                {
                    fFaultRate = u32FaultCnt*1.0 / u32TotolFrameCnt;
                    print_level(SV_INFO, "sunglass rate: %f\n", fFaultRate);
                    if (fFaultRate > 0.8)
                    {
                        bFault = SV_TRUE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_ON);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "sunglass!!!!!\n");
                        goto reset_sunglasses;
                    }
                    else
                    {
                        bFault = SV_FALSE;
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_OFF);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                        print_level(SV_INFO, "no sunglass!!!!!\n");
                    }

                    u32SunglassCnt = 0;
                    u32FaultCnt = 0;
                    u32TotolFrameCnt = 0;
                    s64TimeBegin = s64TimeNow;
                    bGetSunglassStartTime = SV_FALSE;
                    printf("\n");
                }
            }

            if (bFault)
            {
                goto ddaw_exit;
            }

            if (pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable)
            {
                bVirDDAWAlarm = pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable;

                if (pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmStatus && !pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    print_level(SV_INFO, "start virtual alarm\n");
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_TRUE;
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 1);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }
                else if (!pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmStatus && pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    print_level(SV_INFO, "stop virtual alarm\n");
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }

                goto ddaw_exit;
            }
            else if (bVirDDAWAlarm && !pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable)
            {
                print_level(SV_INFO, "disable virtual alarm\n");
                bVirDDAWAlarm = pstDmmInfo->stDDAWInfo.stVirDDAWAlarm.bAlarmEnable;
                if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    print_level(SV_INFO, "stop virtual alarm\n");
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }
            }

            u32WorkSpeedIndex = 0;
            if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex] > 0)
            {
                if ((s32GpsStatus == 0 && !pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsWorkspeed_almNoGPS)
                    || (s32GpsStatus != 0 && s32GpsSpeed < pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[u32WorkSpeedIndex]))
                {
                    if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                    {
                        if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                        if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                        if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_NOTHING;

                        print_level(SV_INFO, "speed is not reachable, stop ddaw alarm~~~~\n");
                        ALARM_EnableSpk(SV_FALSE);
                        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                        if(s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        }
                    }
                    goto ddaw_exit;
                }
            }

            #if 1
            //print_level(SV_INFO, "s32DDAWScore: %d, u32DDAWL9Cnt: %d, u32DDAWL8Cnt: %d, u32DDAWL7Cnt: %d, u32DDAWNoAlarmCnt: %d\n", stDDAWResult.s32DDAWScore, u32DDAWL9Cnt, u32DDAWL8Cnt, u32DDAWL7Cnt, u32DDAWNoAlarmCnt);
            if (CLICK_TYPE_DOUBLE != pstDmmInfo->stDDAWInfo.enClickType)
            {
                if (stDDAWResult.s32DDAWScore >= pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel9Thres)
                {
                    if (++u32DDAWL9Cnt >= 3)
                    {
                        if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                        if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_DDAW_KSS9;
                        //print_level(SV_INFO, "KSS9!!!\n");
                    }
                }
                else if (stDDAWResult.s32DDAWScore >= pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel8Thres)
                {
                    if (++u32DDAWL8Cnt >= 3)
                    {
                        if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                        if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_DDAW_KSS8;
                        //print_level(SV_INFO, "KSS8!!!\n");
                    }
                }
                else if (stDDAWResult.s32DDAWScore >= pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel7Thres)
                {
                    if (++u32DDAWL7Cnt >= 3)
                    {
                        if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                        if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                        if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                        if (0 != s64DDAWAlarmOffBeginTime) s64DDAWAlarmOffBeginTime = 0;
                        pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_DDAW_KSS7;
                        //print_level(SV_INFO, "KSS7!!!\n");
                    }
                }
                else if (stDDAWResult.s32DDAWScore < pstDmmInfo->stDDAWInfo.stKssLevelThres.s32KssLevel7Thres)
                {
                    if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                    {
                        if (0 == s64DDAWAlarmOffBeginTime)
                        {
                            s64DDAWAlarmOffBeginTime = dmm_GetTimeTickMs();
                        }

                        s64DDAWAlarmOffEndTime = dmm_GetTimeTickMs();
                        if (s64DDAWAlarmOffEndTime - s64DDAWAlarmOffBeginTime >= 2000)
                        {
                            if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                            if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                            if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                            if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                            pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                            pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_NOTHING;

                            print_level(SV_INFO, "it's been 2 seconds, stop ddaw alarm~~~~\n");
                            ALARM_EnableSpk(SV_FALSE);
                            s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                            if(s32Ret != SV_SUCCESS)
                            {
                                print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                            }
                        }
                    }
                }

                dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

                if (ALARM_NOTHING != pstDmmInfo->stDDAWInfo.enDDAWAlarmType && !pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    if (0 != ALARM_GetVolume())
                    {
                        ALARM_EnableSpk(SV_TRUE);
                    }
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_TRUE;
                    pstDmmInfo->stDDAWInfo.cv_ddaw.notify_one();

                    print_level(SV_INFO, "start ddaw alarm~~~~\n");
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 1);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }

                dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);
            }
            else
            {
                if (pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm)
                {
                    if (0 != u32DDAWL9Cnt) u32DDAWL9Cnt = 0;
                    if (0 != u32DDAWL8Cnt) u32DDAWL8Cnt = 0;
                    if (0 != u32DDAWL7Cnt) u32DDAWL7Cnt = 0;
                    if (0 != u32DDAWNoAlarmCnt) u32DDAWNoAlarmCnt = 0;
                    pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm = SV_FALSE;
                    pstDmmInfo->stDDAWInfo.enDDAWAlarmType = ALARM_NOTHING;

                    print_level(SV_INFO, "stop ddaw alarm~~~~\n");
                    ALARM_EnableSpk(SV_FALSE);
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_ALARM_BAND, DMM_DDAW_ALARM_PIN, 0);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    }
                }
            }
#endif

ddaw_exit:
            bOsd = SV_TRUE;
        } while(0);
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        pstDmmInfo->stDumpDmmInfo.enAlarmType = enAlarmType;

        /* 速度比较快，这里需要对遮挡手动添加多几帧dump数据 */
        if (enAlarmType == ALARM_NOTHING)
        {
            if (u32DumpNum <= 5)
            {
                u32DumpNum++;
                pstDmmInfo->stDumpDmmInfo.enAlarmType = enAlarmTypeLast;
            }
        }
        else
        {
            u32DumpNum = 0;
        }

        pstDmmInfo->stDumpDmmInfo.bImageMirror = pstDmmInfo->stCfgParam.bImageMirror;
        pstDmmInfo->stDumpDmmInfo.bDetectFace = stDmsResult.bDetectFace ? SV_TRUE : SV_FALSE;
        pstDmmInfo->stDumpDmmInfo.bYawn = stDmsResult.bYawn ? SV_TRUE : SV_FALSE;
        pstDmmInfo->stDumpDmmInfo.bNoMask = stDmsResult.bNoMask ? SV_TRUE : SV_FALSE;
        pstDmmInfo->stDumpDmmInfo.bShelter = (stDmsResult.fOcclusionScore > 50.0) ? SV_TRUE : SV_FALSE;
        sprintf(pstDmmInfo->stDumpDmmInfo.u8SmokeScore, "%f", stDmsResult.fSmokeScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8PhoneScore, "%f", stDmsResult.fPhoneScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8DrinkEatScore, "%f", stDmsResult.fDrinkScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8SeatbeltScore, "%f", stDmsResult.fSeatbeltScore);
        sprintf(pstDmmInfo->stDumpDmmInfo.u8HelmetScore, "%f", stDmsResult.fHelmetScore);
        for (j = 0; j < 2; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8Ecr[j], "%f", stDmsAnalysisRes.fAbsEcr[j]);
        for (j = 0; j < 4; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8EyeScore[j], "%f", stDmsResult.pfpEyeStateScore[j]);
        for (j = 0; j < 2; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8GlassScore[j], "%f", stDmsResult.pfpSunglassesScore[j]);
        for (j = 0; j < 3; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8HeadPose[j], "%f", stDmsResult.pfpHeadPose[j]);
        for (j = 0; j < 2; j++)
            sprintf(pstDmmInfo->stDumpDmmInfo.u8Gaze[j], "%f", stDmsResult.pfGaze[j]);  // 弧度转角度

        memcpy(&stDumpDmmInfo, &pstDmmInfo->stDumpDmmInfo, sizeof(DUMP_DMM_S));
        s32Ret = dmm_DumpInfoThread(&stDumpDmmInfo);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "dmm_DumpInfoThread failed. [err: %s]\n", strerror(errno));
        }

        if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT) && pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
        {
            memset(&stDmmEyelid, 0, sizeof(stDmmEyelid));
            stDmmEyelid.u64TimeStamp = pstDmmSyncInfo->u64TimeStamp;
            stDmmEyelid.fEyelidLeft = stDmsResult.pfEyeCloseRate[0];
            stDmmEyelid.bEyelidLeftValid = (SV_BOOL)stDmsResult.bDetectFace;
            stDmmEyelid.fEyelidRight = stDmsResult.pfEyeCloseRate[1];
            stDmmEyelid.bEyelidRightValid = (SV_BOOL)stDmsResult.bDetectFace;
            dmm_SubmitEyelid(stDmmEyelid);
        }
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) && enAlarmType == ALARM_NOTHING)
        {
            bOsd = SV_TRUE;
        }

        /* 发送检测结果OSD信息到ipsys进行叠加 */
        if (pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsOsdEnable && bOsd)
        {
            memset(&stMsgPkt, 0, sizeof(stMsgPkt));
            stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
            stMsgPkt.u32Size = sizeof(MEDIA_GUI_DRAW_S);
            memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));

            //清空原来的画板
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_CLEAR);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
            }

            memset(&stGuiFace, 0, sizeof(MEDIA_GUI_FACE_S));
            if (stDmsResult.bDetectFace)
            {
                for (i = 0; i < 3; i++)
                {
                    stGuiFace.angle[i] = (int)(stDmsResult.pfpHeadPose[i] + 2.5 * ((stDmsResult.pfpHeadPose[i] > 0) * 2 - 1))/5*5;
        			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] > 85 ? 90 : stGuiFace.angle[i];
        			stGuiFace.angle[i] = stDmsResult.pfpHeadPose[i] < -85 ? -90 : stGuiFace.angle[i];
                }
                stGuiFace.stFaceRect.color = GUI_COLOR_YELLOW;
                stGuiFace.stFaceRect.x1 = stDmsResult.stFaceBoundBox.fX1;
                stGuiFace.stFaceRect.y1 = stDmsResult.stFaceBoundBox.fY1;
                stGuiFace.stFaceRect.x2 = stDmsResult.stFaceBoundBox.fX2;
                stGuiFace.stFaceRect.y2 = stDmsResult.stFaceBoundBox.fY2;
                stGuiFace.u32PointNum = LANDMARK_POINT_NUM;
                for (i = 0; i < LANDMARK_POINT_NUM; i++)
                {
                    stGuiFace.astFacePoints[i].x = stDmsResult.pfLandmarkPoints[i*2];
                    stGuiFace.astFacePoints[i].y = stDmsResult.pfLandmarkPoints[i*2+1];
                    if (stGuiFace.astFacePoints[i].x == -1 && stGuiFace.astFacePoints[i].y == -1)
                    {
                        stGuiFace.bDisablePoint = SV_TRUE;
                        break;
                    }
                }

                if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
                    stGuiFace.bDisablePoint = SV_TRUE;

                float fLEyeEndPoint[2] = {0};
                float fREyeEndPoint[2] = {0};
                uint8 u8LPointNum = 26;
                uint8 u8RPointNum = 34;
                dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8LPointNum], stDmsResult.pfLandmarkPoints[u8LPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fLEyeEndPoint[0], fLEyeEndPoint[1]);
                dmm_GazeVecotrCompute(stDmsResult.pfLandmarkPoints[u8RPointNum], stDmsResult.pfLandmarkPoints[u8RPointNum+1], stDmsResult.pfGaze[0], stDmsResult.pfGaze[1], fREyeEndPoint[0], fREyeEndPoint[1]);

                stGuiFace.stGazeLine[0].x1 = stDmsResult.pfLandmarkPoints[u8LPointNum];
                stGuiFace.stGazeLine[0].y1 = stDmsResult.pfLandmarkPoints[u8LPointNum+1];
                stGuiFace.stGazeLine[0].x2 = fLEyeEndPoint[0];
                stGuiFace.stGazeLine[0].y2 = fLEyeEndPoint[1];
                stGuiFace.stGazeLine[0].color = GUI_COLOR_BLUE;
                stGuiFace.stGazeLine[0].stick = 5;

                stGuiFace.stGazeLine[1].x1 = stDmsResult.pfLandmarkPoints[u8RPointNum];
                stGuiFace.stGazeLine[1].y1 = stDmsResult.pfLandmarkPoints[u8RPointNum+1];
                stGuiFace.stGazeLine[1].x2 = fREyeEndPoint[0];
                stGuiFace.stGazeLine[1].y2 = fREyeEndPoint[1];
                stGuiFace.stGazeLine[1].color = GUI_COLOR_BLUE;
                stGuiFace.stGazeLine[1].stick = 5;
                #if 0
                printf("line1 point1(%f, %f)---(%f, %f), point2(%f, %f)---(%f, %f)\n", stGuiFace.stGazeLine[0].x1, stGuiFace.stGazeLine[0].y1, stGuiFace.stGazeLine[0].x2, stGuiFace.stGazeLine[0].y2,
                        stGuiFace.stGazeLine[1].x1, stGuiFace.stGazeLine[1].y1, stGuiFace.stGazeLine[1].x2, stGuiFace.stGazeLine[1].y2);
                #endif
                if (!pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsGazeTracking || pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
                    stGuiFace.bDisableGaze = SV_TRUE;

                if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
                {
                    for (j = 0; j < 2; j++)
                        stGuiFace.stDDawData.s32Ecr[j] = (sint32)(stDDAWResult.pfEcr[j]*100.0);
                    for (j = 0; j < 3; j++)
                        stGuiFace.stDDawData.s32HeadPose[j] = (sint32)stDDAWResult.pfHeadPose[j];
                    stGuiFace.stDDawData.s32BlinkPerMin = stDDAWResult.s32BlinkPerMin;
                    stGuiFace.stDDawData.s32YawnPerMin = stDDAWResult.s32YawnPerMin;
                    stGuiFace.stDDawData.s32DDAWScore = stDDAWResult.s32DDAWScore;
                    stGuiFace.stDDawData.fEcrGrad = stDDAWResult.fEcrGrad;
                    stGuiFace.stDDawData.fGazeGrad = stDDAWResult.fGazeGrad;
                    stGuiFace.stDDawData.fPoseYawGrad = stDDAWResult.fPoseYawGrad;
                    stGuiFace.stDDawData.fPosePitchGrad = stDDAWResult.fPosePitchGrad;
                    stGuiFace.stDDawData.bOcclusion = (SV_BOOL)stDDAWResult.bOcclusion;
                    stGuiFace.bDdawMode = SV_FALSE;
                }
            }
            else
            {
                stGuiFace.stFaceRect.color = GUI_COLOR_YELLOW;
                stGuiFace.u32PointNum = 0;
            }

            //添加人脸绘制操作
            u16mask = MEDIA_GUI_GET_MASK(pstDmmInfo->s32Chn, 0, MEDIA_GUI_OP_DRAW_FACE);
            s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiFace);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
            }
        }

exit:
        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

        AS_Forward_V();

        dmm_Debug__Test_Time(__FUNCTION__, __LINE__, &s64Last);

#if DMM_DEBUG_TIME
        clock_gettime(CLOCK_MONOTONIC, &tvEnd);
        print_level(SV_INFO, "process eclipse: %dms\n", (tvEnd.tv_sec*1000 + tvEnd.tv_nsec/1000000) - (tvBegin.tv_sec*1000 + tvBegin.tv_nsec/1000000));
#endif
    }

    print_level(SV_INFO, "exit dmm_Det_ProcessBody.\n");
    return NULL;
}

/* DMS算法驱动线程 */
void * dmm_alg_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    DMM_RUN_E enRunStat;
    DMM_INFO_S *pstDmmInfo = (DMM_INFO_S *)pvArg;
    sint32 s32SleepTime = 500;
    bool bFault = false;

    s32Ret = prctl(PR_SET_NAME, "dmm_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    enRunStat = pstDmmInfo->enRunStat;
    while (pstDmmInfo->bRunning)
    {
        //print_level(SV_DEBUG, "dmm_alg_Body running, enRunStat: %d...\n", enRunStat);
        if (enRunStat != pstDmmInfo->enRunStat)
        {
            print_level(SV_INFO, "dmm running status change: %d -> %d\n", enRunStat, pstDmmInfo->enRunStat);
            enRunStat = pstDmmInfo->enRunStat;
            sleep_ms(100);
        }
        else
        {
            sleep_ms(s32SleepTime);
        }

        if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
        {
            if (CLICK_TYPE_PRESS_OFF == pstDmmInfo->stDDAWInfo.enClickType)
            {
                if (!bFault)
                {
                    s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_ON);
                    if(s32Ret != SV_SUCCESS)
                    {
                        print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                        continue;
                    }
                    bFault = true;
                }

                print_level(SV_WARN, "long press off alg!\n");
                continue;
            }
            else if (CLICK_TYPE_PRESS_ON == pstDmmInfo->stDDAWInfo.enClickType && bFault)
            {
                s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_OFF);
                if(s32Ret != SV_SUCCESS)
                {
                    print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    continue;
                }
                bFault = false;
                enRunStat = DMM_RUN_DETECTION;
                print_level(SV_INFO, "long press on alg!\n");
            }
        }

run_stat:
        switch (enRunStat)
        {
            case DMM_RUN_INIT:
                dmm_InitBody();
                s32SleepTime = 1000;
                enRunStat = pstDmmInfo->enRunStat;
                 goto run_stat;
                break;

            case DMM_RUN_IDEL:
                if (CLICK_TYPE_PRESS_OFF == pstDmmInfo->stDDAWInfo.enClickType)
                    sleep_ms(500);
                else
                    sleep_ms(3000);
                continue;

            case DMM_RUN_CALIBRATION_PRE:
                dmm_CalibrationPreBody(pstDmmInfo);
                break;

            case DMM_RUN_CALIBRATION:
                dmm_CalibrationBody(pstDmmInfo);
                break;

            case DMM_RUN_REGISTER:
                dmm_RegisterBody(pstDmmInfo);
                break;

            case DMM_RUN_RECOGNITION:
                dmm_RecognitionBody(pstDmmInfo);
                break;

            case DMM_RUN_DETECTION:
                if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT) && pstDmmInfo->stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)  /* 单线程处理 */
                {
                    dmm_DetctionBody(pstDmmInfo);
                }
                else
                {
                    pstDmmInfo->bDetectRunning = SV_TRUE;
                    dmm_Det_ForwardBody(pstDmmInfo);
                    pstDmmInfo->bDetectRunning = SV_FALSE;
                }
                break;

            case DMM_RUN_CHANGE_DRIVER:
                dmm_ChangeDriverBody(pstDmmInfo);
                break;

            default:
                print_level(SV_WARN, "invalid running status: %d\n", enRunStat);
                sleep_ms(500);
                continue;
        }
    }

    return NULL;
}

void * dmm_PlayDDAW_Body(void *pvArg)
{
    sint32 s32Ret;
    ALARM_TYPE_E enDDAWAlarmType = ALARM_NOTHING;
    MEDIA_GUI_ALARM_DMM_S stGuiAlarmDmm = {0};
    DMM_INFO_S *pstDmmInfo = (DMM_INFO_S *)pvArg;

    while (pstDmmInfo->bRunning)
    {
        std::unique_lock<std::mutex> lock(pstDmmInfo->stDDAWInfo.mutex_ddaw);
        pstDmmInfo->stDDAWInfo.cv_ddaw.wait(lock, [pstDmmInfo](){ return pstDmmInfo->stDDAWInfo.bPlayDDAWAlarm; });

        if (pstDmmInfo->stDDAWInfo.enClickType == CLICK_TYPE_DOUBLE)
        {
            print_level(SV_WARN, "double click status, skip play audio!\n");
            sleep_ms(100);
            continue;
        }

        enDDAWAlarmType = pstDmmInfo->stDDAWInfo.enDDAWAlarmType;
        if (ALARM_NOTHING != pstDmmInfo->stDDAWInfo.enDDAWAlarmType)
        {
            switch (enDDAWAlarmType)
            {
                case ALARM_DDAW_KSS7:
                    ALARM_SetTmpVolume(30);
                    break;

                case ALARM_DDAW_KSS8:
                    ALARM_SetTmpVolume(70);
                    break;

                case ALARM_DDAW_KSS9:
                    ALARM_SetTmpVolume(100);
                    break;

                default:
                    break;
            }

            stGuiAlarmDmm.enAlarmType = enDDAWAlarmType;
            stGuiAlarmDmm.enAlarmLevel = ALARM_LEVEL_HIGH;
            stGuiAlarmDmm.contime = 1;
            s32Ret = dmm_PostDmmGui(&stGuiAlarmDmm);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_PostDmmGui failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = ALARM_PlayAudio(enDDAWAlarmType);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_Alarm_PlayAudio failed. [type:%d]\n", enDDAWAlarmType);
            }

            switch (enDDAWAlarmType)
            {
                case ALARM_DDAW_KSS7:
                case ALARM_DDAW_KSS8:
                case ALARM_DDAW_KSS9:
                    ALARM_RecoverVolume();
                    break;

                default:
                    break;
            }

            if (0 == ALARM_GetVolume())
            {
                sleep_ms(1000);
            }
        }
    }

    return NULL;
}

void * dmm_ButtonDet_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32ButtonFd = -1, s32MaxFd = -1;
    fd_set fdSet = {0};
    struct timeval timeout;
    CLICK_TYPE_E enClickType = CLICK_TYPE_NONE;
    char szCmd[64] = {0};
    DMM_INFO_S *pstDmmInfo = (DMM_INFO_S *)pvArg;

    sprintf(szCmd, "insmod /root/ko/extdrv/button_detect.ko");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "exec cmd: %s failed!\n", szCmd);
        //return NULL;
    }

    while (pstDmmInfo->bRunning)
    {
        while (s32ButtonFd <= 0)
        {
            s32ButtonFd = open(BUTTON_DEVICE, O_RDWR);
            if (s32ButtonFd <= 0)
            {
                print_level(SV_WARN, "open %s failed!\n", BUTTON_DEVICE);
                sleep_ms(1000);
                continue;
            }

            s32Ret = ioctl(s32ButtonFd, BUTTON_GET_BUTTON_TYPE, &enClickType);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "button_ioctl error[%d]\n", s32Ret);
                sleep_ms(1000);
                continue;
            }

            pstDmmInfo->stDDAWInfo.enClickType = enClickType;
            print_level(SV_INFO, "init click type: %d\n", pstDmmInfo->stDDAWInfo.enClickType);
        }

        while (pstDmmInfo->bRunning)
        {
            FD_ZERO(&fdSet);
            FD_SET(s32ButtonFd, &fdSet);
            s32MaxFd = s32ButtonFd + 1;
            timeout.tv_sec = 0;
            timeout.tv_usec = 100000;
            s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout);
            if (s32Ret < 0)
            {
                print_level(SV_WARN, "select failed!\n");
                break;
            }
            else if (s32Ret == 0)
            {
                //print_level(SV_WARN, "select timeout!\n");
            }
            else
            {
                if (FD_ISSET(s32ButtonFd, &fdSet))
                {
                    s32Ret = read(s32ButtonFd, &enClickType, sizeof(CLICK_TYPE_E));
                    if (s32Ret != sizeof(CLICK_TYPE_E))
                    {
                        print_level(SV_ERROR, "read failed!\n");
                        break;
                    }

                    /* 系统处于长按关闭算法状态时，必须先长按恢复算法，否则不识别其他的按键状态 */
                    if (CLICK_TYPE_PRESS_OFF == pstDmmInfo->stDDAWInfo.enClickType)
                    {
                        if (CLICK_TYPE_PRESS_ON != enClickType)
                        {
                            print_level(SV_INFO, "skip clickType: %d, reason: alg has been closed, must be opened first!\n", enClickType);
                            continue;
                        }
                        else
                        {
                            pstDmmInfo->enRunStat = DMM_RUN_DETECTION;
                        }
                    }

                    pstDmmInfo->stDDAWInfo.enClickType = enClickType;
                    switch (enClickType)
                    {
                        case CLICK_TYPE_SINGLE:
                            print_level(SV_WARN, "---single click---\n");
                            break;

                        case CLICK_TYPE_DOUBLE:
                            print_level(SV_WARN, "---double click---\n");
                            break;

                        case CLICK_TYPE_PRESS_OFF:
                            print_level(SV_WARN, "---long press off click---\n");
                            break;

                        case CLICK_TYPE_PRESS_ON:
                            print_level(SV_WARN, "---long press on click---\n");
                            break;

                        default:
                            print_level(SV_ERROR, "unsupport click type: %d\n", enClickType);
                            enClickType = CLICK_TYPE_NONE;
                            break;
                    }

                    /* 系统长按关闭算法状态时，直接把运行算法类型改为空闲，正在运行的算法线程直接跳出 */
                    if (CLICK_TYPE_PRESS_OFF == enClickType)
                    {
                        pstDmmInfo->enRunStat = DMM_RUN_IDEL;
                    }
                }
            }
        }

        if (s32ButtonFd > 0)
        {
            close(s32ButtonFd);
            s32ButtonFd = -1;
        }
        sleep_ms(1000);
    }

    sprintf(szCmd, "rmmod /root/ko/extdrv/button_detect.ko");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "exec cmd: %s failed!\n", szCmd);
        //return NULL;
    }

    return NULL;
}


sint32 dmm_CheckCallback(uint8_t* au8SourceAddr, uint8_t* au8ResultAddr)
{
    sint32 s32Ret, i;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    KEY_AUTH_S stAuthSrc = {0}, stAuthDes = {0};

    stAuthSrc.s32Len = 16;
    memcpy(stAuthSrc.au8Buf, au8SourceAddr, 16);
    stMsgPkt.pu8Data = (uint8 *)&stAuthSrc;
    stMsgPkt.u32Size = sizeof(KEY_AUTH_S);
    stRetPkt.pu8Data = (uint8 *)&stAuthDes;
    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_KEY_AUTH, &stMsgPkt, &stRetPkt, sizeof(KEY_AUTH_S));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_KEY_AUTH failed. [err=%#x]\n", s32Ret);
    }

    m_stDmmInfo.bKeyAuth = stRetPkt.stMsg.s32Param;
    memcpy(au8ResultAddr, stAuthDes.au8Buf, 16);
    print_level(SV_DEBUG, "bKeyAuth:%d\n", m_stDmmInfo.bKeyAuth);

    return 0;
}

void dmm_SetAlgParam()
{
    sint32 i = 0;

    m_stDmmConf[ALARM_FATIGUE].s32DectectInterval             =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32FatigueInterval * 1000;
    m_stDmmConf[ALARM_DISTRACTION].s32DectectInterval         =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DistractionInterval * 1000;
    m_stDmmConf[ALARM_NO_DRIVER].s32DectectInterval           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32NoDriverInterval * 1000;
    m_stDmmConf[ALARM_SMOKE].s32DectectInterval               =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32SmokeInterval * 1000;
    m_stDmmConf[ALARM_PHONE].s32DectectInterval               =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32PhoneInterval * 1000;
    m_stDmmConf[ALARM_YAWN].s32DectectInterval                =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32YawnInterval * 1000;
    m_stDmmConf[ALARM_NO_MASK].s32DectectInterval             =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32NoMaskInterval * 1000;
    m_stDmmConf[ALARM_SUNGLASS].s32DectectInterval            =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32SunGlassInterval * 1000;
    m_stDmmConf[ALARM_NO_SEATBELT].s32DectectInterval         =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32SeatBeltInterval * 1000;
    m_stDmmConf[ALARM_SHELTER].s32DectectInterval             =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32ShelterInterval * 1000;
    m_stDmmConf[ALARM_FATIGUE_L2].s32DectectInterval          =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32FatigueL2Interval * 1000;
    m_stDmmConf[ALARM_DRINK_EAT].s32DectectInterval           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DrinkEatInterval * 1000;
    m_stDmmConf[ALARM_OVERSPEED].s32DectectInterval           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32OverspeedInterval * 1000;
    m_stDmmConf[ALARM_NO_HELMET].s32DectectInterval           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32NoHelmetInterval * 1000;
    m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32ChangeGuardInterval * 1000;

    /* CREARE客户要求关掉更换司机检测功能 */
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval = -1;
    }

    m_stDmmConf[ALARM_FATIGUE].bDmsAlarmOut             =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutFatigue;
    m_stDmmConf[ALARM_DISTRACTION].bDmsAlarmOut         =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutDistraction;
    m_stDmmConf[ALARM_NO_DRIVER].bDmsAlarmOut           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutNoDriver;
    m_stDmmConf[ALARM_SMOKE].bDmsAlarmOut               =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutSmoke;
    m_stDmmConf[ALARM_PHONE].bDmsAlarmOut               =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutPhone;
    m_stDmmConf[ALARM_YAWN].bDmsAlarmOut                =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutYawn;
    m_stDmmConf[ALARM_NO_MASK].bDmsAlarmOut             =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutNoMask;
    m_stDmmConf[ALARM_SUNGLASS].bDmsAlarmOut            =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutSunGlass;
    m_stDmmConf[ALARM_NO_SEATBELT].bDmsAlarmOut         =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutSeatBelt;
    m_stDmmConf[ALARM_SHELTER].bDmsAlarmOut             =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutShelter;
    m_stDmmConf[ALARM_FATIGUE_L2].bDmsAlarmOut          =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutFatigueL2;
    m_stDmmConf[ALARM_DRINK_EAT].bDmsAlarmOut           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutDrinkEat;
    m_stDmmConf[NOTIFY_LNGIN_FAILED].bDmsAlarmOut       =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutLoginFail;
    m_stDmmConf[NOTIFY_LNGIN_SUCCESS].bDmsAlarmOut      =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutLoginSuccess;
    m_stDmmConf[ALARM_OVERSPEED].bDmsAlarmOut           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutOverspeed;
    m_stDmmConf[ALARM_NO_HELMET].bDmsAlarmOut           =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAlarmOutNoHelmet;

    m_stDmmConf[ALARM_NOTHING].bDmsAudioEnable          =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioEnable;
    m_stDmmConf[ALARM_FATIGUE].bDmsAudioEnable          =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioFatigue;
    m_stDmmConf[ALARM_DISTRACTION].bDmsAudioEnable      =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioDistraction;
    m_stDmmConf[ALARM_NO_DRIVER].bDmsAudioEnable        =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioNoDriver;
    m_stDmmConf[ALARM_SMOKE].bDmsAudioEnable            =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioSmoke;
    m_stDmmConf[ALARM_PHONE].bDmsAudioEnable            =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioPhone;
    m_stDmmConf[ALARM_YAWN].bDmsAudioEnable             =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioYawn;
    m_stDmmConf[ALARM_NO_MASK].bDmsAudioEnable          =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioNoMask;
    m_stDmmConf[ALARM_SUNGLASS].bDmsAudioEnable         =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioSunGlass;
    m_stDmmConf[ALARM_NO_SEATBELT].bDmsAudioEnable      =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioSeatBelt;
    m_stDmmConf[ALARM_SHELTER].bDmsAudioEnable          =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioShelter;
    m_stDmmConf[ALARM_FATIGUE_L2].bDmsAudioEnable       =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioFatigueL2;
    m_stDmmConf[ALARM_DRINK_EAT].bDmsAudioEnable        =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioDrinkEat;
    m_stDmmConf[ALARM_OVERSPEED].bDmsAudioEnable        =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioOverspeed;
    m_stDmmConf[ALARM_NO_HELMET].bDmsAudioEnable        =  m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsAudioNoHelmet;

    STDMSAlgParam stDmsAlgParam;
    stDmsAlgParam.fEcrThr = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsEyelidClosure*1.0/100;
    stDmsAlgParam.fFatigueTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsFatigueTimelimit;
    stDmsAlgParam.fDictThrUp = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleUp;
    stDmsAlgParam.fDictThrDown = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleDown;
    stDmsAlgParam.fDictThrLeft = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleLeft;
    stDmsAlgParam.fDictThrRight = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsDistractionAngleRight;
    stDmsAlgParam.fDictTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsDistractionTimelimit;
    stDmsAlgParam.fNoDriverTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsNodriverTimelimit;
    stDmsAlgParam.fSmThr = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsSmokeThreshold*1.0/100;
    stDmsAlgParam.fSmTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsSmokeTimelimit;
    stDmsAlgParam.fPhoneThr = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsPhoneThreshold*1.0/100;
    stDmsAlgParam.fPhoneTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsPhoneTimelimit;
    stDmsAlgParam.fSeatbeltThr = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsSeatBeltThreshold*1.0/100;
    stDmsAlgParam.fSeatbeltTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsSeatBeltTimelimit;
    stDmsAlgParam.fOcclusionTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsShelterTimelimit;
    stDmsAlgParam.fDrinkThr = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsDrinkEatThreshold*1.0/100;
    stDmsAlgParam.fDrinkTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsDrinkEatTimelimit;
    stDmsAlgParam.fYawnTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsYawnTimelimit;
    stDmsAlgParam.fNoMaskTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsNoMaskTimelimit;
    stDmsAlgParam.fSunGlassesTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsSunGlassTimelimit;
    stDmsAlgParam.fHelmetThr = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsNoHelmetThreshold*1.0/100;
    stDmsAlgParam.fHelmetTime = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsNoHelmetTimelimit;
    stDmsAlgParam.fFatigueSumTimeLength = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsFatigueSumTime;
    stDmsAlgParam.fClosePercent = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsFatigueClosePercent;
    stDmsAlgParam.fPoseTimeLength = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32AutoCalibPoseTimeLength*1.0;
    m_stDmmInfo.pcsDmsAnalyzer->SetAlgParam(stDmsAlgParam);

    STDDAWParam stDDAWParam;
    memcpy(&stDDAWParam, &m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.stDDAWParam, sizeof(STDDAWParam));
    m_stDmmInfo.pcsDmsAnalyzer->SetDDAWParam(stDDAWParam);
    #if 0
    printf("DDAW param: ");
    float *pfDDAWParam = (float *)&stDDAWParam;
    for (i = 0; i < 9; i++)
    {
        printf("%f ", *(pfDDAWParam+i));
    }
    printf("\n");
    #endif

    memcpy(&m_stDmmInfo.stDDAWInfo.stKssLevelThres, &m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.stDDAWParam.s32KssLevel7Thres, sizeof(m_stDmmInfo.stDDAWInfo.stKssLevelThres));
    sint32 *ps32DDAWParam = (sint32 *)&m_stDmmInfo.stDDAWInfo.stKssLevelThres;
    #if 0
    for (i = 0; i < 3; i++)
    {
        printf("%d ", *(ps32DDAWParam+i));
    }
    printf("\n");
    #endif
}

sint32 DMM_Init(DMM_CFG_PARAM_S *pstInitParam)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32PwmFd = -1;
    sint32 s32ChnNum = 1;
    sint32 s32CenterGaze = 0;
    sint32 s32PitchAngleGaze = 0;
    char szCmd[128] = {0};
    char *pszModelPath = "/root/model/model_unencrypted";
    char *pszUnencryptedModelPath = "/mnt/nfs/model_unencrypted";
    char *pszModelDir = NULL;
    char szModelPathFD[256] = {0};
    char szModelPathFA[256] = {0};
    char szModelPathFR[256] = {0};
    char szModelPathEyeState[256] = {0};
    char szModelPathHelmet[256] = {0};
    char szModelPathMask[256] = {0};
    char szModelPathOcclusion[256] = {0};
    char szModelPathMultifunction[256] = {0};
    char szModelPathSeatbelt[256] = {0};
    char szModelPathSmokeT1[256] = {0};
    char szModelPathSunglasses[256] = {0};
    char szModelPathGaze[256] = {0};
    char szModelPathFAS[256] = {0};
    char szModelPathPeople[256] = {0};
    bool bEncrypt = true;

    CDmsAlg             *pcsDmsAlg = NULL;
    CDmsAnalyzer        *pcsDmsAnalyzer = NULL;
    CFrAnalyzer         *pcsFrAnalyzer = NULL;
    STAlgModelPath      stModelPath;
    STCalibrateParam    stCalibrateParam;
    STDMSFuncSwitch     stDmsFuncSwitch;

    if (NULL == pstInitParam)
    {
        return ERR_NULL_PTR;
    }

    memset(&m_stDmmInfo, 0, sizeof(DMM_INFO_S));
    s32Ret = pthread_mutex_init(&m_stDmmInfo.mutexRunStat, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    if (COMMON_IsPathExist(pszUnencryptedModelPath) && BOARD_IsNotCustomer(BOARD_C_DMS31V2_111371))
    {
        print_level(SV_INFO, "%s is exist, use unencrypted model to debug!\n", pszUnencryptedModelPath);
        sprintf(szCmd, "umount %s 2> /dev/null", pszModelPath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);

        sprintf(szCmd, "mount --bind %s %s", pszUnencryptedModelPath, pszModelPath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);

        bEncrypt = false;
    }

    s32Ret = dmm_Sem_Init();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "dmm_Shm_Init failed!\n");
        return SV_FAILURE;
    }

    s32Ret = dmm_Shm_Init();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "dmm_Shm_Init failed!\n");
        return SV_FAILURE;
    }

#if ALG_MUTLIT_BUFFER
    for (i = 0; i < 3; i++)
    {
        m_stDmmInfo.s32MediaBufFd[i] = pstInitParam->s32MediaBufFd[i];
    }
#else
    m_stDmmInfo.s32MediaBufFd = pstInitParam->s32MediaBufFd;
#endif
    m_stDmmInfo.s32Chn = pstInitParam->s32Chn;
    m_stDmmInfo.stCfgParam = pstInitParam->stAlgParam;

    if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Ret = dmm_Alarm_PlayAudio(NOTIFY_ALGSTART);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "dmm_Alarm_PlayAudio failed.\n");
        }
    }

    if (m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsCalibrated)
    {
        memcpy(stCalibrateParam.ps32CalibrateHeadAngle, pstInitParam->stAlgParam.stAlgCh2.stDmsParam.ps32CalibrateHeadAngle, sizeof(stCalibrateParam.ps32CalibrateHeadAngle));
        memcpy(stCalibrateParam.ps32CalibrateGazeAngle, pstInitParam->stAlgParam.stAlgCh2.stDmsParam.ps32CalibrateGazeAngle, sizeof(stCalibrateParam.ps32CalibrateGazeAngle));

        for (i = 0; i < 2; i++)
        {
            print_level(SV_INFO, "ps32HeadPosAverage[%d]: %d, ps32GazeAverage[%d]: %d\n", i, stCalibrateParam.ps32CalibrateHeadAngle[i], i, stCalibrateParam.ps32CalibrateGazeAngle[i]);
        }
        memcpy(&m_stDmmInfo.stCalibrateParam, &stCalibrateParam, sizeof(STCalibrateParam));
    }

    stDmsFuncSwitch.bNoDriverSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32NoDriverInterval >= 0;
    stDmsFuncSwitch.bFatigueSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32FatigueInterval >= 0;
    stDmsFuncSwitch.bFatigueL2SW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32FatigueL2Interval >= 0;
    stDmsFuncSwitch.bDistractSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32DistractionInterval >= 0;
    stDmsFuncSwitch.bSmokeSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32SmokeInterval >= 0;
    stDmsFuncSwitch.bPhoneSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32PhoneInterval >= 0;
    stDmsFuncSwitch.bYawnSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32YawnInterval >= 0;
    stDmsFuncSwitch.bNoMaskSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32NoMaskInterval >= 0;
    stDmsFuncSwitch.bSunglassesSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32SunGlassInterval >= 0;
    stDmsFuncSwitch.bSeatbeltSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32SeatBeltInterval >= 0;
    stDmsFuncSwitch.bOcclusionSW  = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32ShelterInterval >= 0;
    stDmsFuncSwitch.bDrinkSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32DrinkEatInterval >= 0;
    stDmsFuncSwitch.bHelmetSW = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.s32NoHelmetInterval >= 0;
    m_stDmmInfo.bFirstFace = SV_TRUE;
    m_stDmmInfo.bNeedFaceCap = pstInitParam->stAlgParam.stAlgCh2.stDmsParam.bDmsFaceCapture;
    m_stDmmInfo.bStartFaceCap = SV_FALSE;

    stModelPath.pu8ModelFD              = nullptr;
    stModelPath.pu8ModelFA              = nullptr;
    stModelPath.pu8ModelFR              = nullptr;
    stModelPath.pu8ModelEyeState        = nullptr;
    stModelPath.pu8ModelHelmet          = nullptr;
    stModelPath.pu8ModelMask            = nullptr;
    stModelPath.pu8ModelCameraAnomaly   = nullptr;
    stModelPath.pu8ModelMultifunction   = nullptr;
    stModelPath.pu8ModelSeatbelt        = nullptr;
    stModelPath.pu8ModelSmokeT1         = nullptr;
    stModelPath.pu8ModelSunglasses      = nullptr;
    stModelPath.pu8ModelGaze            = nullptr;
    stModelPath.pu8ModelFAS             = nullptr;
    stModelPath.pu8ModelPeople          = nullptr;

    /* 填充模型路径，无需加载的模型直接置空 */
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111371))
    {
        pszModelDir = "/root/model/model_unencrypted";
        sprintf(szModelPathFD, DMM_MODEL_UNCRYPT_PATH_FD, pszModelDir);
        sprintf(szModelPathFA, DMM_MODEL_UNCRYPT_PATH_FA, pszModelDir);
        sprintf(szModelPathFR, DMM_MODEL_UNCRYPT_PATH_FR, pszModelDir);
        sprintf(szModelPathFAS, DMM_MODEL_UNCRYPT_PATH_FAS, pszModelDir);

        stModelPath.pu8ModelFD                  = szModelPathFD;
        stModelPath.pu8ModelFA                  = szModelPathFA;
        stModelPath.pu8ModelFR                  = szModelPathFR;
        stModelPath.pu8ModelFAS                 = szModelPathFAS;
        m_stDmmInfo.bOnlyFr = SV_TRUE;
        bEncrypt = false;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT) && m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly)
    {
        pszModelDir = "/root/model";
        sprintf(szModelPathFD, DMM_MODEL_PATH_FD, pszModelDir);
        sprintf(szModelPathFA, DMM_MODEL_PATH_FA, pszModelDir);
        sprintf(szModelPathEyeState, DMM_MODEL_PATH_EYE_STATE, pszModelDir);
        sprintf(szModelPathOcclusion, DMM_MODEL_PATH_SHELTER, pszModelDir);
        sprintf(szModelPathFAS, DMM_MODEL_PATH_FAS, pszModelDir);

        stModelPath.pu8ModelFD                  = szModelPathFD;
        stModelPath.pu8ModelFA                  = szModelPathFA;
        stModelPath.pu8ModelEyeState            = szModelPathEyeState;
        stModelPath.pu8ModelCameraAnomaly       = szModelPathOcclusion;
        stModelPath.pu8ModelFAS                 = szModelPathFAS;
    }
    else
    {
        if (bEncrypt)
        {
            pszModelDir = "/root/model";
            sprintf(szModelPathFD, DMM_MODEL_PATH_FD, pszModelDir);
            sprintf(szModelPathFA, DMM_MODEL_PATH_FA, pszModelDir);
            sprintf(szModelPathFR, DMM_MODEL_PATH_FR, pszModelDir);
            sprintf(szModelPathEyeState, DMM_MODEL_PATH_EYE_STATE, pszModelDir);
            sprintf(szModelPathHelmet, DMM_MODEL_PATH_HELMET, pszModelDir);
            sprintf(szModelPathMask, DMM_MODEL_PATH_MASK, pszModelDir);
            sprintf(szModelPathOcclusion, DMM_MODEL_PATH_SHELTER, pszModelDir);
            sprintf(szModelPathMultifunction, DMM_MODEL_PATH_MULTIFUNC, pszModelDir);
            sprintf(szModelPathSeatbelt, DMM_MODEL_PATH_SEATBELT, pszModelDir);
            sprintf(szModelPathSmokeT1, DMM_MODEL_PATH_SMOKET1, pszModelDir);
            sprintf(szModelPathSunglasses, DMM_MODEL_PATH_SUNGLASSES, pszModelDir);
            sprintf(szModelPathGaze, DMM_MODEL_PATH_GAZE, pszModelDir);
            sprintf(szModelPathFAS, DMM_MODEL_PATH_FAS, pszModelDir);
            sprintf(szModelPathPeople, DMM_MODEL_PATH_PEOPLE, pszModelDir);

            stModelPath.pu8ModelFD              = szModelPathFD;
            stModelPath.pu8ModelFA              = szModelPathFA;
            stModelPath.pu8ModelFR              = szModelPathFR;
            stModelPath.pu8ModelEyeState        = szModelPathEyeState;
            stModelPath.pu8ModelHelmet          = szModelPathHelmet;
            stModelPath.pu8ModelMask            = szModelPathMask;
            stModelPath.pu8ModelCameraAnomaly   = szModelPathOcclusion;
            stModelPath.pu8ModelMultifunction   = szModelPathMultifunction;
            stModelPath.pu8ModelSeatbelt        = szModelPathSeatbelt;
            stModelPath.pu8ModelSmokeT1         = szModelPathSmokeT1;
            stModelPath.pu8ModelSunglasses      = szModelPathSunglasses;
            stModelPath.pu8ModelGaze            = szModelPathGaze;
            stModelPath.pu8ModelFAS             = szModelPathFAS;
            stModelPath.pu8ModelPeople          = szModelPathPeople;
        }
        else
        {
            pszModelDir = "/mnt/nfs/model_unencrypted";
            sprintf(szModelPathFD, DMM_MODEL_UNCRYPT_PATH_FD, pszModelDir);
            sprintf(szModelPathFA, DMM_MODEL_UNCRYPT_PATH_FA, pszModelDir);
            sprintf(szModelPathFR, DMM_MODEL_UNCRYPT_PATH_FR, pszModelDir);
            sprintf(szModelPathEyeState, DMM_MODEL_UNCRYPT_PATH_EYE_STATE, pszModelDir);
            sprintf(szModelPathHelmet, DMM_MODEL_UNCRYPT_PATH_HELMET, pszModelDir);
            sprintf(szModelPathMask, DMM_MODEL_UNCRYPT_PATH_MASK, pszModelDir);
            sprintf(szModelPathOcclusion, DMM_MODEL_UNCRYPT_PATH_SHELTER, pszModelDir);
            sprintf(szModelPathMultifunction, DMM_MODEL_UNCRYPT_PATH_MULTIFUNC, pszModelDir);
            sprintf(szModelPathSeatbelt, DMM_MODEL_UNCRYPT_PATH_SEATBELT, pszModelDir);
            sprintf(szModelPathSmokeT1, DMM_MODEL_UNCRYPT_PATH_SMOKET1, pszModelDir);
            sprintf(szModelPathSunglasses, DMM_MODEL_UNCRYPT_PATH_SUNGLASSES, pszModelDir);
            sprintf(szModelPathGaze, DMM_MODEL_UNCRYPT_PATH_GAZE, pszModelDir);
            sprintf(szModelPathFAS, DMM_MODEL_UNCRYPT_PATH_FAS, pszModelDir);
            sprintf(szModelPathPeople, DMM_MODEL_UNCRYPT_PATH_PEOPLE, pszModelDir);

            stModelPath.pu8ModelFD              = szModelPathFD;
            stModelPath.pu8ModelFA              = szModelPathFA;
            stModelPath.pu8ModelFR              = szModelPathFR;
            stModelPath.pu8ModelEyeState        = szModelPathEyeState;
            stModelPath.pu8ModelHelmet          = szModelPathHelmet;
            stModelPath.pu8ModelMask            = szModelPathMask;
            stModelPath.pu8ModelCameraAnomaly   = szModelPathOcclusion;
            stModelPath.pu8ModelMultifunction   = szModelPathMultifunction;
            stModelPath.pu8ModelSeatbelt        = szModelPathSeatbelt;
            stModelPath.pu8ModelSmokeT1         = szModelPathSmokeT1;
            stModelPath.pu8ModelSunglasses      = szModelPathSunglasses;
            stModelPath.pu8ModelGaze            = szModelPathGaze;
            stModelPath.pu8ModelFAS             = szModelPathFAS;
            stModelPath.pu8ModelPeople          = szModelPathPeople;
        }

        if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
        {
            pszModelDir = "/root/model";
            sprintf(szModelPathFD, DMM_MODEL_PATH_FD, pszModelDir);
            sprintf(szModelPathFA, DMM_MODEL_PATH_FA, pszModelDir);
            sprintf(szModelPathEyeState, DMM_MODEL_PATH_EYE_STATE, pszModelDir);
            sprintf(szModelPathOcclusion, DMM_MODEL_PATH_SHELTER, pszModelDir);
            sprintf(szModelPathSunglasses, DMM_MODEL_PATH_SUNGLASSES, pszModelDir);
            sprintf(szModelPathGaze, DMM_MODEL_PATH_GAZE, pszModelDir);
            sprintf(szModelPathFAS, DMM_MODEL_PATH_FAS, pszModelDir);

            stModelPath.pu8ModelFD              = szModelPathFD;
            stModelPath.pu8ModelFA              = szModelPathFA;
            stModelPath.pu8ModelEyeState        = szModelPathEyeState;
            stModelPath.pu8ModelCameraAnomaly   = szModelPathOcclusion;
            stModelPath.pu8ModelSunglasses      = szModelPathSunglasses;
            stModelPath.pu8ModelGaze            = szModelPathGaze;
            stModelPath.pu8ModelFAS             = szModelPathFAS;
        }
    }

    #if 1
    print_level(SV_INFO, "FDModelPath: %s\n", stModelPath.pu8ModelFD);
    print_level(SV_INFO, "FAModelPath: %s\n", stModelPath.pu8ModelFA);
    print_level(SV_INFO, "FRModelPath: %s\n", stModelPath.pu8ModelFR);
    print_level(SV_INFO, "EyeStateModelPath: %s\n", stModelPath.pu8ModelEyeState);
    print_level(SV_INFO, "HelmetModelPath: %s\n", stModelPath.pu8ModelHelmet);
    print_level(SV_INFO, "MaskModelPath: %s\n", stModelPath.pu8ModelMask);
    print_level(SV_INFO, "OcclusionModelPath: %s\n", stModelPath.pu8ModelCameraAnomaly);
    print_level(SV_INFO, "MultifunctionModelPath: %s\n", stModelPath.pu8ModelMultifunction);
    print_level(SV_INFO, "SeatbeltModelPath: %s\n", stModelPath.pu8ModelSeatbelt);
    print_level(SV_INFO, "SmokeT1ModelPath: %s\n", stModelPath.pu8ModelSmokeT1);
    print_level(SV_INFO, "SunglassesModelPath: %s\n", stModelPath.pu8ModelSunglasses);
    print_level(SV_INFO, "GazeModelPath: %s\n", stModelPath.pu8ModelGaze);
    print_level(SV_INFO, "FASModelPath: %s\n", stModelPath.pu8ModelFAS);
    print_level(SV_INFO, "PeopleModelPath: %s\n", stModelPath.pu8ModelPeople);
    #endif

    /* 创建DMS算法类 */
    pcsDmsAlg = new CDmsAlg(s32ChnNum, false);
    if (NULL == pcsDmsAlg)
    {
        print_level(SV_ERROR, "new CDmsAlg failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    /* DMS算法初始化 */
    s32Ret = pcsDmsAlg->AlgInit(stModelPath, dmm_CheckCallback);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "AlgInit fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    m_stDmmInfo.pcsDmsAlg = pcsDmsAlg;

    /* 创建DMS行为分析类 */
    pcsDmsAnalyzer = new CDmsAnalyzer(stCalibrateParam, stDmsFuncSwitch, false, BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW));
    if (NULL == pcsDmsAnalyzer)
    {
        print_level(SV_ERROR, "new CDmsAnalyzer failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    m_stDmmInfo.pcsDmsAnalyzer = pcsDmsAnalyzer;

    /* 创建人脸识别分析类 */
    pcsFrAnalyzer = new CFrAnalyzer(stCalibrateParam, FRS_USERINFOS_PATH);
    if (NULL == pcsFrAnalyzer)
    {
        print_level(SV_ERROR, "new CFrAnalyzer failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    m_stDmmInfo.pcsFrAnalyzer = pcsFrAnalyzer;

    /* 算法初始化完成，关闭DDAW失效灯 */
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
    {
        s32Ret = BOARD_RK_SetGPIO(DMM_DDAW_FAULT_BAND, DMM_DDAW_FAULT_PIN, DMM_DDAW_FAULT_OFF);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    for (i = 0; i < ALARM_DMS_BUFF; i++)
    {
        m_stDmmConf[i].s32WorkSpeed = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[i];
        /* CREARE客户遮挡当做无司机来处理 */
        if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) && i == ALARM_SHELTER)
        {
            m_stDmmConf[i].s32WorkSpeed = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[ALARM_NO_DRIVER];
        }
    }

	dmm_SetAlgParam();

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202032))
    {
        s32Ret = dmm_Pwm_Init(&s32PwmFd);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "dmm_Pwm_Init failed\n");
            //return SV_FAILURE;
        }
        m_stDmmInfo.s32PwmFd = s32PwmFd;
    }

    return SV_SUCCESS;
}

sint32 DMM_Fini()
{
    AS_Fini();
    delete m_stDmmInfo.pcsDmsAlg;
    delete m_stDmmInfo.pcsDmsAnalyzer;
    delete m_stDmmInfo.pcsFrAnalyzer;
    pthread_mutex_destroy(&m_stDmmInfo.mutexRunStat);

    return SV_SUCCESS;
}

sint32 DMM_Start()
{
    sint32 s32Ret = 0, i = 0;
    pthread_t thread, thread1, thread2, thread3, thread4;
    SV_BOOL bValidFd = SV_TRUE;

    pthread_attr_t attr;
    size_t stack_size = 10 * 1024 * 1024; // 设置栈大小为 10 MB
    pthread_attr_init(&attr);
    pthread_attr_setstacksize(&attr, stack_size);

#if ALG_MUTLIT_BUFFER
    bValidFd = SV_TRUE;
    for (i = 0; i < 3; i++)
    {
        if (m_stDmmInfo.s32MediaBufFd[i] <= 0)
        {
            bValidFd = SV_FALSE;
            break;
        }
    }
#else
    if (m_stDmmInfo.s32MediaBufFd < 0)
    {
        bValidFd = SV_FALSE;
    }
#endif

    if (!bValidFd)
    {
        m_stDmmInfo.enRunStat = DMM_RUN_INIT;
    }
    else
    {
        if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_CREARE))
        {
            s32Ret = dmm_Alarm_PlayAudio(NOTIFY_ALGRUNNING);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "dmm_Alarm_PlayAudio failed.\n");
            }
        }
        else
        {
            /* creare播放带人脸捕获的欢迎语时延迟到算法初始化完成后再进行 */
            if (m_stDmmInfo.bStartFaceCap)
            {
                s32Ret = ALARM_PlayWelcome();
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "ALARM_PlayWelcome failed. [err=%#x]\n", s32Ret);
                }
            }
        }

        m_stDmmInfo.enRunStat = DMM_RUN_DETECTION;
        if (E_LOGIN_OFF != m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.enDmsLoginMode)
        {
            m_stDmmInfo.bPlayLoginAudio = SV_TRUE;
            if (E_LOGIN_BOOT == m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.enDmsLoginMode)
            {
                m_stDmmInfo.enRunStat = DMM_RUN_RECOGNITION;
            }
        }

        if (m_stDmmInfo.bOnlyFr)
        {
            m_stDmmInfo.bPlayLoginAudio = SV_TRUE;
            m_stDmmInfo.enRunStat = DMM_RUN_RECOGNITION;
        }
    }

    m_stDmmInfo.bRunning = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
    {
        s32Ret = pthread_create(&thread2, NULL, dmm_ButtonDet_Body, &m_stDmmInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
            if (EAGAIN == s32Ret)
            {
                return ERR_SYS_NOTREADY;
            }
            else
            {
                return SV_FAILURE;
            }
        }

        s32Ret = pthread_create(&thread3, NULL, dmm_PlayDDAW_Body, &m_stDmmInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
            if (EAGAIN == s32Ret)
            {
                return ERR_SYS_NOTREADY;
            }
            else
            {
                return SV_FAILURE;
            }
        }
    }

    if (!m_stDmmInfo.bOnlyFr)
    {
        s32Ret = pthread_create(&thread4, NULL, dmm_Det_ProcessBody, &m_stDmmInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
            if (EAGAIN == s32Ret)
            {
                return ERR_SYS_NOTREADY;
            }
            else
            {
                return SV_FAILURE;
            }
        }
    }

    s32Ret = pthread_create(&thread, NULL, dmm_alg_Body, &m_stDmmInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    s32Ret = pthread_create(&thread1, NULL, dmm_playAlarm_Body, &m_stDmmInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    m_stDmmInfo.u32TidAlg = thread;
    m_stDmmInfo.u32TidPlay = thread1;
    m_stDmmInfo.u32TidButtonDet = thread2;
    m_stDmmInfo.u32TidPlayDDAW = thread3;

    pthread_attr_destroy(&attr);

    return SV_SUCCESS;
}

sint32 DMM_Stop()
{
    sint32 s32Ret = 0;
    pthread_t thread = m_stDmmInfo.u32TidAlg;
    pthread_t thread1 = m_stDmmInfo.u32TidPlay;
    pthread_t thread2 = m_stDmmInfo.u32TidButtonDet;
    pthread_t thread3 = m_stDmmInfo.u32TidPlayDDAW;
    void *pvRetval = NULL;

    m_stDmmInfo.bDetectRunning = SV_FALSE;
    m_stDmmInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_DDAW))
    {
        s32Ret = pthread_join(thread2, &pvRetval);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
            return SV_FAILURE;
        }

        s32Ret = pthread_join(thread3, &pvRetval);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
            return SV_FAILURE;
        }
    }

    s32Ret = pthread_join(thread1, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = pthread_join(thread1, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 DMM_ConfigSet(CFG_ALG_PARAM *pstCfgParam)
{
    sint32 s32Ret = 0, i = 0;
    PWM_DUTY_CYCLE_S stPwmDutyCycle = {0};

    if (NULL == pstCfgParam)
    {
        return ERR_NULL_PTR;
    }

    pstCfgParam->stAlgCh2.stDmsParam.bDmsCalibrated = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsCalibrated;

    if (STRUCT_EQUAL(m_stDmmInfo.stCfgParam, *pstCfgParam))
    {
        print_level(SV_WARN, "dmm config is the same as last time!\n");
        return SV_SUCCESS;
    }

    pthread_mutex_lock(&m_stDmmInfo.mutexRunStat);

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202032) && m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsPwmDutyCycle != pstCfgParam->stAlgCh2.stDmsParam.s32DmsPwmDutyCycle)
    {
        m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsPwmDutyCycle = pstCfgParam->stAlgCh2.stDmsParam.s32DmsPwmDutyCycle;
        stPwmDutyCycle.ton = 2500 * m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsPwmDutyCycle;
        s32Ret = ioctl(m_stDmmInfo.s32PwmFd, PWM_SET_DUTY_CYCLE, &stPwmDutyCycle);  // 客户要求PWM频率为4kHz，也就是周期为250000ns
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "pwm_ioctl %d error[%d]\n", _IOC_NR(PWM_SET_DUTY_CYCLE), s32Ret);
        }
    }

    m_stDmmConf[ALARM_FATIGUE].bIntervalChanged = (m_stDmmConf[ALARM_FATIGUE].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32FatigueInterval * 1000);
    m_stDmmConf[ALARM_DISTRACTION].bIntervalChanged = (m_stDmmConf[ALARM_DISTRACTION].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32DistractionInterval * 1000);
    m_stDmmConf[ALARM_NO_DRIVER].bIntervalChanged = (m_stDmmConf[ALARM_NO_DRIVER].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32NoDriverInterval * 1000);
    m_stDmmConf[ALARM_SMOKE].bIntervalChanged = (m_stDmmConf[ALARM_SMOKE].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32SmokeInterval * 1000);
    m_stDmmConf[ALARM_PHONE].bIntervalChanged = (m_stDmmConf[ALARM_PHONE].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32PhoneInterval * 1000);
    m_stDmmConf[ALARM_YAWN].bIntervalChanged = (m_stDmmConf[ALARM_YAWN].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32YawnInterval * 1000);
    m_stDmmConf[ALARM_NO_MASK].bIntervalChanged = (m_stDmmConf[ALARM_NO_MASK].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32NoMaskInterval * 1000);
    m_stDmmConf[ALARM_SUNGLASS].bIntervalChanged = (m_stDmmConf[ALARM_SUNGLASS].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32SunGlassInterval * 1000);
    m_stDmmConf[ALARM_NO_SEATBELT].bIntervalChanged = (m_stDmmConf[ALARM_NO_SEATBELT].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32SeatBeltInterval * 1000);
    m_stDmmConf[ALARM_SHELTER].bIntervalChanged = (m_stDmmConf[ALARM_SHELTER].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32ShelterInterval * 1000);
    m_stDmmConf[ALARM_FATIGUE_L2].bIntervalChanged = (m_stDmmConf[ALARM_FATIGUE_L2].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32FatigueL2Interval * 1000);
    m_stDmmConf[ALARM_DRINK_EAT].bIntervalChanged = (m_stDmmConf[ALARM_DRINK_EAT].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32DrinkEatInterval * 1000);
    m_stDmmConf[ALARM_OVERSPEED].bIntervalChanged = (m_stDmmConf[ALARM_OVERSPEED].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32OverspeedInterval * 1000);
    m_stDmmConf[ALARM_NO_HELMET].bIntervalChanged = (m_stDmmConf[ALARM_NO_HELMET].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32NoHelmetInterval * 1000);

    /* CREARE客户要求关掉更换司机检测功能 */
    //m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].bIntervalChanged = (m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].s32DectectInterval != pstCfgParam->stAlgCh2.stDmsParam.s32ChangeGuardInterval * 1000);
    m_stDmmConf[NOTIFY_LNGIN_CHANGE_GUARD].bIntervalChanged = false;

    m_stDmmInfo.stCfgParam = *pstCfgParam;

    for (i = 0; i < ALARM_DMS_BUFF; i++)
    {
        m_stDmmConf[i].s32WorkSpeed = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[i];
        /* CREARE客户遮挡当做无司机来处理 */
        if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) && i == ALARM_SHELTER)
        {
            m_stDmmConf[i].s32WorkSpeed = m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.s32DmsWorkspeed[ALARM_NO_DRIVER];
        }
    }

	dmm_SetAlgParam();

    pthread_mutex_unlock(&m_stDmmInfo.mutexRunStat);

    return SV_SUCCESS;
}

sint32 DMM_SetRunStatus(DMM_RUN_E enRunStat)
{
    pthread_mutex_lock(&m_stDmmInfo.mutexRunStat);
    if (DMM_RUN_CALIBRATION == enRunStat
        || DMM_RUN_REGISTER == enRunStat
        || DMM_RUN_RECOGNITION == enRunStat)
    {
        m_stDmmInfo.enRunResult = DMM_RES_RUNNING;
    }
    m_stDmmInfo.enRunStat = enRunStat;
    if (DMM_RUN_RECOGNITION == enRunStat)
    {
        m_stDmmInfo.bPlayLoginAudio = SV_TRUE;
    }
    print_level(SV_INFO, "DMM_SetRunStatus:%d\n", m_stDmmInfo.enRunStat);
    pthread_mutex_unlock(&m_stDmmInfo.mutexRunStat);

    return SV_SUCCESS;
}

DMM_RUN_E DMM_GetRunStatus()
{
    return m_stDmmInfo.enRunStat;
}

DMM_RES_E DMM_GetRunResult(USER_INFO_S *pstUserInfo)
{
    if(NULL != pstUserInfo)
    {
        *pstUserInfo = m_stDmmInfo.stUserInfo;
    }
    return m_stDmmInfo.enRunResult;
}

SV_BOOL DMM_IsFatigueOnly()
{
    return m_stDmmInfo.stCfgParam.stAlgCh2.stDmsParam.bDmsFatigueOnly;
}

sint32 DMM_RegisterUser(char *pszUserName)
{
    sint32 s32Ret = 0, i;
    USER_LIST_S stUserList = {0};
    char szDirOldFmt[DMM_MAX_DIRLEN];
    char szDir[DMM_MAX_DIRLEN];
    char szDirPath[128];
    char szCmd[128];

    if (NULL == pszUserName)
    {
        return ERR_NULL_PTR;
    }

    if (strlen(pszUserName) >= DMM_MAX_NAMELEN-1)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = DMM_GetUserList(&stUserList);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "DMM_GetUserList failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    for (i = 0; i < stUserList.u32Num; i++)
    {
        if (0 == strcmp(stUserList.astUserList[i].szName, pszUserName))
        {
            print_level(SV_ERROR, "user name: %s is eixst.\n", pszUserName);
            return ERR_EXIST;
        }
    }

    for (i = 0; i < DMM_MAX_FR_NUM; i++)
    {
        sprintf(szDirOldFmt, "ID/user%02d", i);
		sprintf(szDir, "ID/user%03d", i);
        if (0 != strcmp(stUserList.astUserList[i].szDir, szDirOldFmt)
            && 0 != strcmp(stUserList.astUserList[i].szDir, szDir))    // 找到没有被占用的编号
        {
			sprintf(szDir, "user%03d", i);
            break;
        }
    }
    if (i >= DMM_MAX_FR_NUM)
    {
        print_level(SV_ERROR, "no free user buffer!\n");
        return ERR_BUF_FULL;
    }

    print_level(SV_INFO, "szDir: %s\n", szDir);
    sprintf(szDirPath, "%s/%s", FRS_USERINFOS_PATH, szDir);
    print_level(SV_INFO, "szDirPath: %s\n", szDirPath);
    s32Ret = mkdir(szDirPath, 0755);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "mkdir: %s failed.", szDirPath);
        return SV_FAILURE;
    }

    sprintf(szCmd, "echo \"username=%s\" > %s/username", pszUserName, szDirPath);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    strcpy(m_stDmmInfo.szUserDir, szDir);
    print_level(SV_INFO, "m_stDmmInfo.szUserDir: %s\n", m_stDmmInfo.szUserDir);

    return SV_SUCCESS;
}

sint32 DMM_DeleteUser(char *pszUserName)
{
    sint32 s32Ret = 0, i;
    USER_LIST_S stUserList = {0};
    char szCmd[128];

    if (NULL == pszUserName)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = DMM_GetUserList(&stUserList);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }

    for (i = 0; i < stUserList.u32Num; i++)
    {
        if (0 == strcmp(stUserList.astUserList[i].szName, pszUserName))
        {
            break;
        }
    }
    if (i >= stUserList.u32Num)
    {
        print_level(SV_ERROR, "not found user name:%s\n", pszUserName);
        return ERR_UNEXIST;
    }

    sprintf(szCmd, "rm -rf /root/%s", stUserList.astUserList[i].szDir);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 DMM_DeleteUserBatch(USER_LIST_S *pstDelUserList)
{
    sint32 s32Ret = 0, i, j;
    USER_LIST_S stUserList = {0};
    char szCmd[128];

    if (NULL == pstDelUserList)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = DMM_GetUserList(&stUserList);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }

    for (i = 0; i < pstDelUserList->u32Num; i++)
    {
        for (j = 0; j < stUserList.u32Num; j++)
        {
            if (0 == strcmp(stUserList.astUserList[j].szName, pstDelUserList->astUserList[i].szName))
            {
                break;
            }
        }
        if (j >= stUserList.u32Num)
        {
            print_level(SV_ERROR, "not found user name: %s\n", pstDelUserList->astUserList[i].szName);
            continue;
        }

        sprintf(szCmd, "rm -rf /root/%s", stUserList.astUserList[j].szDir);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if(0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

sint32 DMM_ModifyUser(char *pszUserName)
{
    sint32 s32Ret = 0, i;
    USER_LIST_S stUserList = {0};
    char szCmd[128];

    if (NULL == pszUserName)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = DMM_GetUserList(&stUserList);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }

	printf("!!!!!!!!!! %s\n",pszUserName);
	char* tmp = strchr(pszUserName,'/');
	if(!tmp){
		print_level(SV_ERROR, "wrong name:%s with no / \n", pszUserName);
        return SV_FAILURE;
	}
	*tmp='\0';
	char* pszNewUserName = tmp+1;
	printf("~~~~~~~~ %s %s\n",pszUserName,pszNewUserName);

    for (i = 0; i < stUserList.u32Num; i++)
    {
        if (0 == strcmp(stUserList.astUserList[i].szName, pszUserName))
        {
            break;
        }
    }
    if (i >= stUserList.u32Num)
    {
        print_level(SV_ERROR, "not found user name:%s\n", pszUserName);
        return ERR_UNEXIST;
    }

	sprintf(szCmd, "echo \"username=%s\" > /root/%s/username", pszNewUserName, stUserList.astUserList[i].szDir);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}


sint32 DMM_GetUserList(USER_LIST_S *pstUserList)
{
    sint32 s32Ret = 0, i;
    sint32 s32Num = 0;
    sint32 s32Fd = -1;
    uint32 u32UserCnt = 0;
    struct dirent **pastFileList;
    char szUserNamePath[128];
    char szUserName[1024];

    if (NULL == pstUserList)
    {
        return ERR_NULL_PTR;
    }

    s32Num = scandir(FRS_USERINFOS_PATH, &pastFileList, 0, alphasort);
    if (s32Num < 0)
    {
        mkdir(FRS_USERINFOS_PATH, 0755);
        pstUserList->u32Num = 0;
        return SV_SUCCESS;
    }

    for (i = 0; i < s32Num; i++)
    {
        if (!(pastFileList[i]->d_type & DT_DIR) || NULL == strstr(pastFileList[i]->d_name, "user"))
        {
            free(pastFileList[i]);
            continue;
        }

        sprintf(szUserNamePath, "%s/%s/username", FRS_USERINFOS_PATH, pastFileList[i]->d_name);
        s32Fd = open(szUserNamePath, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: %s failed. [err:%s]\n", szUserNamePath, strerror(errno));
            free(pastFileList[i]);
            continue;
        }

        s32Ret = read(s32Fd, szUserName, 1024);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "read file: %s failed. [err:%s]\n", szUserNamePath, strerror(errno));
            free(pastFileList[i]);
            close(s32Fd);
            continue;
        }
        close(s32Fd);

        dmm_CutLineBreak(szUserName);
        //print_level(SV_DEBUG, "szName:%s, szDir:%s\n", szUserName, pastFileList[i]->d_name);
        strcpy(pstUserList->astUserList[u32UserCnt].szName, &szUserName[strlen("username=")]);
        snprintf(pstUserList->astUserList[u32UserCnt].szDir,DMM_MAX_DIRLEN,"%s/%s",FRS_USERINFOS_DIR,pastFileList[i]->d_name);
        u32UserCnt++;
        free(pastFileList[i]);
    }

    free(pastFileList);
    pstUserList->u32Num = u32UserCnt;

    return SV_SUCCESS;
}

sint32 DMM_GetUserListDelFormJson(USER_LIST_S *pstUserList)
{
    sint32 s32Ret = 0, i;
    sint32 s32Fd = -1;
    uint32 u32UserCnt = 0;
    uint32 u32Size = 0;
    char szBuf[25*1024] = {0};
    cJSON *pstJson, *pstTmp, *pstDeleteUserList = NULL;


    if (access(DUMP_INFO_DEL_USER_LIST, F_OK) != 0)
    {
        print_level(SV_ERROR, "file %s is not exist!\n", DUMP_INFO_DEL_USER_LIST);
        goto error_exit;
    }

    s32Fd = open(DUMP_INFO_DEL_USER_LIST, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file %s error!\n", DUMP_INFO_DEL_USER_LIST);
        goto error_exit;
    }

    s32Ret = read(s32Fd, szBuf, sizeof(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "read file %s error!\n", DUMP_INFO_DEL_USER_LIST);
        goto error_exit;
    }

    pstJson = cJSON_Parse(szBuf);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        goto error_exit;
    }

    pstDeleteUserList = cJSON_GetObjectItemCaseSensitive(pstJson, "userList");
    if (NULL != pstDeleteUserList)
    {
        u32Size = cJSON_GetArraySize(pstDeleteUserList);
        for (i = 0; i < u32Size; i++)
        {
            pstTmp = cJSON_GetArrayItem(pstDeleteUserList, i);
            if (NULL != pstTmp)
            {
                strcpy(pstUserList->astUserList[u32UserCnt].szName, pstTmp->valuestring);
                u32UserCnt++;
            }
        }
    }

    close(s32Fd);
    cJSON_Delete(pstJson);
    pstUserList->u32Num = u32UserCnt;
    return SV_SUCCESS;

error_exit:
    if (-1 != s32Fd)
    {
        close(s32Fd);
    }
    return SV_FAILURE;
}

sint32 DMM_StartFaceCapture()
{
    sint32 s32Ret = 0;

    if (NULL == m_stDmmInfo.pcsDmsAlg)
    {
        print_level(SV_WARN, "dmm init is not finished!\n");
        return SV_FAILURE;
    }

    if (!m_stDmmInfo.bStartFaceCap && m_stDmmInfo.bNeedFaceCap)
    {
        print_level(SV_INFO, "play welcome and start face capture!\n");
        m_stDmmInfo.bStartFaceCap = SV_TRUE;
        s32Ret = ALARM_PlayWelcome();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALARM_PlayWelcome failed. [err=%#x]\n", s32Ret);
        }
    }

    return SV_SUCCESS;
}

void DMM_SetVirDDAWAlarm(MSG_DDAW_ALARM_CFG *pstDDAWAlarm)
{
    memcpy(&m_stDmmInfo.stDDAWInfo.stVirDDAWAlarm, pstDDAWAlarm, sizeof(MSG_DDAW_ALARM_CFG));
}

#if ALG_MUTLIT_BUFFER
sint32 DMM_SetMediaBufFd(int (*ps32MediaBufFd)[4][3])
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32Arr = 0;

    for (i = 0; i < ALG_MULTI_BUF_NUM; i++)
    {
        if ((*ps32MediaBufFd)[s32Arr][i] <= 0)
        {
            print_level(SV_ERROR, "invalid media buf fd[%d]: %d\n", i, (*ps32MediaBufFd)[s32Arr][i]);
            return SV_FAILURE;
        }
        else
        {
            m_stDmmInfo.s32MediaBufFd[i] = (*ps32MediaBufFd)[s32Arr][i];
            print_level(SV_INFO, "valid media buf fd[%d]: %d\n", i, (*ps32MediaBufFd)[s32Arr][i]);
        }
    }
    return SV_SUCCESS;
}
#else
sint32 DMM_SetMediaBufFd(int (*ps32MediaBufFd)[4])
{
    sint32 s32Ret = 0, i = 0;
    if ((*ps32MediaBufFd)[0] < 0)
    {
        print_level(SV_ERROR, "invalid media buf fd: %d\n", (*ps32MediaBufFd)[0]);
        return SV_FAILURE;
    }
    m_stDmmInfo.s32MediaBufFd = (*ps32MediaBufFd)[0];
    print_level(SV_INFO, "valid media buf fd: %d\n", (*ps32MediaBufFd)[0]);
    return SV_SUCCESS;
}
#endif

