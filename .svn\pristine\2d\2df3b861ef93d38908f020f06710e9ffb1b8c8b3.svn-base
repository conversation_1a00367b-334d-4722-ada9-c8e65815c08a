/******************************************************************************
Copyright (C) 2020-2022 广州敏视数码科技有限公司版权所有.

文件名：storage.c

作者: 许家铭       版本: v1.0.0(初始版本号)   日期: 2020-06-17

文件功能描述: 定义块存储设备管理功能

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/types.h>
#include <sys/prctl.h>
#include <sys/stat.h>
#include <sys/vfs.h>
#include <sys/mount.h>
#include <linux/fs.h>
#include <linux/hdreg.h>
#include <error.h>
#include <linux/netlink.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <fnmatch.h>
#include <pthread.h>
#include <errno.h>
#include <sys/socket.h>
#include <sys/statvfs.h>

#include "print.h"
#include "alarm.h"
#include "utils.h"
#include "common.h"
#include "alarm.h"
#include "storage.h"
#include "board.h"
#include "safefunc.h"
#include "msg.h"
#include "cJSON.h"

typedef enum tagStorageErrCode_E
{
    STORAGE_ERR_CODE_NO_DISK            = 2010000,          /* 无可用的存储设备 */
    STORAGE_ERR_CODE_DISK_DROP          = 2010001,          /* 使用中掉卡 */
    STORAGE_ERR_CODE_TOO_MANY_PART      = 2010002,          /* 磁盘太多分区 */
    STORAGE_ERR_CODE_NOT_FAT32          = 2010003,          /* 文件系统不是FAT32格式 */
    STORAGE_ERR_CODE_NO_FORMAT_FLAG     = 2010004,          /* 没有格式化标志（可能是因为文件丢失导致的） */
    STORAGE_ERR_CODE_DISK_READONLY      = 2010005,          /* 磁盘只读了 */
    STORAGE_ERR_CODE_DISK_REPAIR_FAIL   = 2010006,          /* 磁盘修复失败 */
    STORAGE_ERR_CODE_STO_THR_EXCEPTION  = 2010007           /* 存储线程异常 */
} STORAGE_ERR_CODE;

/* LED灯控制口 -- SPI1_CSN0_M1 -- GPIO1_C7 */
#define STORAGE_LED_BAND 1
#define STORAGE_LED_PIN  23

/* 存储设备数 */
#if (defined(BOARD_ADA47V1))
#define MAX_SD_CARD_NUM                 1                    /* 最大SD卡数 */
#define MAX_STORAGE_NUM                 3                    /* 最大存储设备数 */
#else
#define MAX_SD_CARD_NUM                 1                    /* 最大SD卡数 */
#define MAX_STORAGE_NUM                 2                    /* 最大存储设备数 */
#endif
#define MOUNT_PATH_NUM                  (STORAGE_MEMORY+1)   /* 挂载路径数 */
#define MAX_PARTIRION_NUM               2                    /* 最大分区数 */

/* 错误码定义 */
#define STORAGE_ERR_FAILURE             (1)                  /* 设备操作的失败 */
#define STORAGE_ERR_FORBID_MOUNT        (2)                  /* 设备被禁止挂载 */
#define STORAGE_ERR_EXCEPT_UNMOUT       (3)                  /* 设备出现异常卸载 */
#define STORAGE_ERR_REPAIR_FAILED       (4)                  /* 设备修复出错 */

#define MOUNT_NOMOUNT 0
#define MOUNT_SUCCESS 1
#define MOUNT_EXCEPT 2

#define FILE_SYS_FAT_SIZE_THRESHOLD     (2*384*1024)         /* 单位(KB） */
#define FILE_SYS_EXT_SIZE_THRESHOLD     (2*384*1024)         /* 单位(KB） */
#define STO_MAX(a,b)            (((a)>(b))?(a):(b))

/* 热插拔音频文件路径 */
#define STORAGE_INSERT_SOUND_FILE       "/root/sound/other/deviceinsert.wav"
#define STORAGE_REMOVE_SOUND_FILE       "/root/sound/other/deviceremove.wav"

/* 设备文件 */
#if (defined(BOARD_IPCR20S4))
#define STORAGE_BLKDEV_PATH             "/dev/mmcblk0"
#define STORAGE_BLKDEV_NAME             "/mmcblk0"
#elif (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) \
        || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) \
        || defined(BOARD_HDW845V1) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || (defined(BOARD_ADA32V4)))
#define STORAGE_BLKDEV_PATH             "/dev/mmcblk2"
#define STORAGE_BLKDEV_NAME             "/mmcblk2"
#else
#define STORAGE_BLKDEV_PATH             "/dev/mmcblk2"
#define STORAGE_BLKDEV_NAME             "/mmcblk2"
#endif

/* u盘设备 */
#define USB_BLKDEV_PATH                 "/dev/sda"
#define USB_BLKDEV_NAME                 "/sda"
/* EMMC设备 */
#define EMMC_BLKDEV_PATH                "/dev/mmcblk0"
#define EMMC_BLKDEV_NAME                "/mmcblk0"

#define STORAGE_SOUND_VOL               80

#if (defined(BOARD_IPCR20S4) || defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) \
    || defined(BOARD_ADA900V1))
    #define     STO_REMAIN_SIZE         (584 + 6)    /* SD卡要保留的空间(MB),与录像的REC_REMAIN_SIZE大一点 */
#else
    #define     STO_REMAIN_SIZE         (256 + 3)
#endif


/* 卡挂载路径 */
#define MOUNT_PATH_NUM                  6
const char* mountPathArray[MOUNT_PATH_NUM] = {"/mnt/sdcard","/mnt/sdcard2","/mnt/sdcard3","/userdata","/mnt/udisk","/tmp/sdcard"};
const char* mountMediaPath[MOUNT_PATH_NUM] = {"/mnt/media","/mnt/media2","/mnt/media3","/userdata","/mnt/mediaU","/tmp/sdcard"};

/* 存储设备系统状态 */
typedef struct tagDeviceStat_S
{
    SINFO_DEV_E enDevStat;           /* 存储设备可访问性状态  - Notice : Backup rely on it - */
    SINFO_DEV_E enMediaMountStat;    /* MEDIA 存储设备可访问性状态  */
    SINFO_FS_E  enMediaFs;           /* 媒体存储文件系统类型 */
} STOR_STAT_S;

/* 模块控制信息 */
typedef struct tagStorManInfo_S
{
    SV_BOOL             bEnable;                                /* 模块是否使能运作 */
	SV_BOOL			    bAutoTesting;		                    /* 是否正在进行自动化测试 */
    SV_BOOL             bAudioAlarm;                            /* 模块是否使能SD丢失音频报警 */
    SV_BOOL             bLedAlarm;                              /* 模块是否使能SD丢失LED闪烁报警 */
    uint32              *pu32ErrCode;                           /* 错误码 */
    sint32              s32UTChour;                             /* UTC时 -12~14*/
    sint32              s32UTCminute;                           /* UTC分 0~59*/
    SV_BOOL             bConfig;                                /* 存储设备参数是否发生配置 */
    SV_BOOL             bException;                             /* 存储设备是否出现异常 */
    SV_BOOL             bResetException;                        /* 是否处于异常复位当中 */
    SV_BOOL             *pbPwrOff;                              /* 电源掉电标志 */
    SV_BOOL             bRepairing;                             /* 是否在修复卡状态 */
    SINFO_RUN_E         enRunStat;                              /* 模块当前运行状态 */
    SREC_STAT_E         enRecordStat;                           /* 当前录像状态 */
    STOR_CMD_S          stCommand;                              /* 外部命令 */
    STOR_STAT_S         astDevStatTab[STORAGE_MAX_BLK_NUM];     /* 存储设备系统状态列表 */
    SFS_INFO_S          astFsInfo[STORAGE_MAX_BLK_NUM];         /* 挂载的文件系统信息 */
    SINFO_REP_E         aRepairRes[STORAGE_MAX_BLK_NUM];        /* 最近一次修复结果 */
    SV_BOOL             abStorageEnable[STORAGE_MAX_BLK_NUM];   /* 单独存储设备使能开关 */
    SV_BOOL             bInsert[STORAGE_MAX_BLK_NUM];           /* 卡插拔状态 */
    uint32              u32Tid;                                 /* 监测线程ID */
    pthread_t           s32DevCheckTid;                         /* 设备监测线程id */
    pthread_t           s32SdAlarmTid;                          /* SD卡报警监测线程id */
    pthread_t           s32PowerTid;                            /* 电源监测线程 */
    SV_BOOL             bRunning;                               /* 线程是否正在运行 */
} STORAGE_INFO_S;

STORAGE_INFO_S m_stStorageInfo = {0};                   /* 模块控制信息 */

char * storage_TransDevStat(SINFO_DEV_E enDevStat)
{
    switch (enDevStat)
    {
        case SINFO_DEV_UNINSERT:
            return "No SD Card";
        case SINFO_DEV_ENABLE:
            return "Not Mounted";
        case SINFO_DEV_MOUNTED:
            return "Normal";
        case SINFO_DEV_FORBID:
            return "Forbid";
        case SINFO_DEV_EXCEPT:
            return "Exception";
        case SINFO_DEV_REPAIRING_FS:
            return "Repairing File System";
        case SINFO_DEV_REPAIRING_PART:
            return "Repairing Partion";
        case SINFO_DEV_FORMATTING:
            return "Formatting";
        case SINFO_DEV_CLEANING:
            return "Cleaning";
        default:
            return "Unknown";
    }
}

char * storage_TransRecStat(SREC_STAT_E enRecStat)
{
    switch (enRecStat)
    {
        case SREC_STAT_NO_RECORD:
            return "No Record";
        case SREC_STAT_RECORDING:
            return "Recording";
        case SREC_STAT_NO_ENOUGH_CAPACITY:
            return "No Enough Capacity";
        default:
            return "Unknown";
    }
}

/******************************************************************************
 * 函数功能: 设置存储错误码
 * 输入参数: u32ErrCode --- 错误码
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void storage_SetErrCode(uint32 u32ErrCode)
{
    if ((u32ErrCode == STORAGE_ERR_CODE_NO_DISK && *(m_stStorageInfo.pu32ErrCode) != STORAGE_ERR_CODE_DISK_DROP) || (u32ErrCode != STORAGE_ERR_CODE_NO_DISK))
    {
        *(m_stStorageInfo.pu32ErrCode) = u32ErrCode;
        //print_level(SV_INFO, "u32ErrCode: %d\n", u32ErrCode);
    }
    return;
}

/******************************************************************************
 * 函数功能: 将模块运行状态信息更新到 /var/info/storage 文件
 * 输入参数: pstStormanInfo --- 模块控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void storage_DumpModuleInfo(STORAGE_INFO_S *pstStormanInfo)
{
    uint8 i;
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    cJSON *pstJson = NULL;
    cJSON *pstEnable = NULL, *pstConfig = NULL, *pstPwrOff = NULL, *pstException = NULL, *pstRunStat = NULL, *pstCmd = NULL, *pstPos = NULL, *pstRecStat = NULL;
    cJSON *pstFull = NULL, *pstInsertOne = NULL;
    char szBuf[4096];
    SINFO_OUT_E stStoOsd = {0};

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return;
    }

    //模块信息
    pstEnable = cJSON_CreateBool(pstStormanInfo->bEnable);
    if (NULL == pstEnable)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "enable", pstEnable);

    pstConfig = cJSON_CreateBool(pstStormanInfo->bConfig);
    if (NULL == pstConfig)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "config", pstConfig);

    pstPwrOff = cJSON_CreateBool(*(pstStormanInfo->pbPwrOff));
    if (NULL == pstPwrOff)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "powerOff", pstPwrOff);

    pstRunStat = cJSON_CreateNumber(pstStormanInfo->enRunStat);
    if (NULL == pstRunStat)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "runStat", pstRunStat);

    pstCmd = cJSON_CreateNumber(pstStormanInfo->stCommand.enCmdType);
    if (NULL == pstCmd)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "command", pstCmd);

    pstPos = cJSON_CreateNumber(pstStormanInfo->stCommand.enStoragePos);
    if (NULL == pstPos)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "curPossition", pstPos);

    pstRecStat = cJSON_CreateString(storage_TransRecStat(pstStormanInfo->enRecordStat));
    if (NULL == pstRecStat)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "recStat", pstRecStat);

    s32Ret = STORAGE_GetState(&stStoOsd);
    pstException = cJSON_CreateBool(stStoOsd.bException);
    if (NULL == pstException)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "exception", pstException);

    pstInsertOne = cJSON_CreateBool(stStoOsd.bInsert);
    if (NULL == pstInsertOne)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "insertOne", pstInsertOne);

    pstFull = cJSON_CreateBool(stStoOsd.bFull);
    if (NULL == pstFull)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "full", pstFull);

    //设备信息
    cJSON *pstDevInfo = NULL;
    cJSON *pstFsType = NULL;
    cJSON *pstFsToatlSize = NULL;
    cJSON *pstFsRemainSize = NULL;
    cJSON *pstFsPercent = NULL;
    cJSON *pstInsert = NULL;

    cJSON *pstSdStatusList = cJSON_CreateArray();
    if (NULL == pstSdStatusList)
    {
        print_level(SV_ERROR, "cJSON_CreateArray failed.\n");
        goto exit;
    }

    for(i = 0; i<MAX_SD_CARD_NUM; i++)
    {
        cJSON *pstSdStatus = cJSON_CreateObject();
        if (NULL == pstSdStatus)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            goto exit;
        }

        cJSON_AddItemToObject(pstSdStatus, "pos", cJSON_CreateNumber(i));
        pstInsert = cJSON_CreateBool(pstStormanInfo->bInsert[i]);
        if (NULL == pstInsert)
        {
            print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "insert", pstInsert);

        pstDevInfo = cJSON_CreateNumber(pstStormanInfo->astDevStatTab[i].enDevStat);
        if (NULL == pstDevInfo)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "devStat", pstDevInfo);

        pstFsType = cJSON_CreateNumber(pstStormanInfo->astDevStatTab[i].enMediaFs);
        if (NULL == pstFsType)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "mediaFsType", pstFsType);


        pstFsToatlSize = cJSON_CreateNumber(pstStormanInfo->astFsInfo[i].totalSize);
        if (NULL == pstFsToatlSize)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "fsTotalSizeMB", pstFsToatlSize);

        pstFsRemainSize = cJSON_CreateNumber(pstStormanInfo->astFsInfo[i].remainSize);
        if (NULL == pstFsRemainSize)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "fsRemainSizeMB", pstFsRemainSize);

        pstFsPercent = cJSON_CreateNumber(pstStormanInfo->astFsInfo[i].totalSize == 0 ? 0 : pstStormanInfo->astFsInfo[i].remainSize * 100 / pstStormanInfo->astFsInfo[i].totalSize);
        if (NULL == pstFsPercent)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "fsRemainPercent", pstFsPercent);

        cJSON_AddItemToArray(pstSdStatusList, pstSdStatus);
    }

    /* U盘或内容EMMC设备 */
#if defined(BOARD_ADA47V1)
    i = STORAGE_INNER_EMMC;
#else
    i = STORAGE_EXTRA_SD;
#endif
    for(; i<=STORAGE_EXTRA_SD; i++)
    {
        cJSON *pstSdStatus = cJSON_CreateObject();
        if (NULL == pstSdStatus)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            goto exit;
        }

        cJSON_AddItemToObject(pstSdStatus, "pos", cJSON_CreateNumber(i));
        pstInsert = cJSON_CreateBool(pstStormanInfo->bInsert[i]);
        if (NULL == pstInsert)
        {
            print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "insert", pstInsert);

        pstDevInfo = cJSON_CreateNumber(pstStormanInfo->astDevStatTab[i].enDevStat);
        if (NULL == pstDevInfo)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "devStat", pstDevInfo);

        pstFsType = cJSON_CreateNumber(pstStormanInfo->astDevStatTab[i].enMediaFs);
        if (NULL == pstFsType)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "mediaFsType", pstFsType);


        pstFsToatlSize = cJSON_CreateNumber(pstStormanInfo->astFsInfo[i].totalSize);
        if (NULL == pstFsToatlSize)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "fsTotalSizeMB", pstFsToatlSize);

        pstFsRemainSize = cJSON_CreateNumber(pstStormanInfo->astFsInfo[i].remainSize);
        if (NULL == pstFsRemainSize)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "fsRemainSizeMB", pstFsRemainSize);

        pstFsPercent = cJSON_CreateNumber(pstStormanInfo->astFsInfo[i].totalSize == 0 ? 0 : pstStormanInfo->astFsInfo[i].remainSize * 100 / pstStormanInfo->astFsInfo[i].totalSize);
        if (NULL == pstFsPercent)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSdStatus, "fsRemainPercent", pstFsPercent);

        cJSON_AddItemToArray(pstSdStatusList, pstSdStatus);
    }
    cJSON_AddItemToObject(pstJson, "statusList", pstSdStatusList);

    //修复信息
    cJSON_AddItemToObject(pstJson, "aRepairRes", cJSON_CreateIntArray(pstStormanInfo->aRepairRes, MAX_STORAGE_NUM));

    memset(szBuf, 0, 4096);
    cJSON_PrintPreallocated(pstJson, szBuf, 4096, 0);
    s32Fd = open("/var/info/storage-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /var/info/storage-tmp failed. [err:%s]\n", strerror(errno));
        goto exit;
    }

    s32Ret = write(s32Fd, szBuf, strlen(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write file: /var/info/storage-tmp failed. [err:%s]\n", strerror(errno));
        close(s32Fd);
        goto exit;
    }

    close(s32Fd);
    rename("/var/info/storage-tmp", "/var/info/storage");

exit:
    cJSON_Delete(pstJson);
}

/******************************************************************************
 * 函数功能: 向Log模块发送SD卡格式化操作日志
 * 输入参数: u32SizeGB --- SD卡大小
             pszResult --- 格式化结果
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void storage_LOG_FormatDevice(STORAGE_POS_E enStoragePos, uint32 u32SizeGB, char *pszResult)
{
    char szLogBuf[1024];

    sprintf(szLogBuf, "type=\"sdCardFormat\"\tsdCardPos=%d\tsdCardSizeGB=%d\tresult=\"%s\"", \
                        enStoragePos, u32SizeGB, pszResult);


    //print_level(SV_DEBUG, "%s\n", szLogBuf);
    LOG_Submit(-1, szLogBuf);
}

 /******************************************************************************
 * 函数功能: 向Log模块发送SD status 日志
 * 输入参数: pszType --- 操作类型
             enStoragePos --- SD卡位置
             pszReason --- 原因描述
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void storman_LOG_DeviceStat(STOR_STAT_S* pstatArray)
{
    char szLogBuf[1024];
    sint32 statArray[STORAGE_MAX_BLK_NUM];
    static int logCnt=1;
    int i;
    for(i = 0;i < MAX_SD_CARD_NUM; i++)
    {
        statArray[i] = 0;
        statArray[i] = (((long)pstatArray[i].enDevStat)<<8)+(((long)pstatArray[i].enMediaMountStat)<<4)+pstatArray[i].enMediaFs;
    }
#if defined(BOARD_ADA47V1)
    statArray[STORAGE_INNER_EMMC] = 0;
    statArray[STORAGE_INNER_EMMC] = (((long)pstatArray[STORAGE_INNER_EMMC].enDevStat)<<8)+(((long)pstatArray[STORAGE_INNER_EMMC].enMediaMountStat)<<4)+pstatArray[STORAGE_INNER_EMMC].enMediaFs;
#endif
    statArray[STORAGE_EXTRA_SD] = 0;
    statArray[STORAGE_EXTRA_SD] = (((long)pstatArray[STORAGE_EXTRA_SD].enDevStat)<<8)+(((long)pstatArray[STORAGE_EXTRA_SD].enMediaMountStat)<<4)+pstatArray[STORAGE_EXTRA_SD].enMediaFs;
    sprintf(szLogBuf, "type=\"sdStatus\"\tsd1=%x\tsd2=%x\tsd3=%x\tsd4=%x\tusb=%x\tn=%d", statArray[0],statArray[1],statArray[2],statArray[3],statArray[4],logCnt++);
    //print_level(SV_DEBUG, "%s\n", szLogBuf);
    LOG_Submit(-1, szLogBuf);
}

/******************************************************************************
 * 函数功能: 向Log模块发送SD卡普通操作日志
 * 输入参数: pszType --- 操作类型
             pszReason --- 原因描述
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void storage_LOG_OperateDevice(char *pszType, STORAGE_POS_E enStoragePos, char *pszReason)
{
    char szLogBuf[1024];

    if (NULL == pszReason)
    {
        sprintf(szLogBuf, "type=\"%s\"\tpos=%d", pszType, enStoragePos);
    }
    else
    {
        sprintf(szLogBuf, "type=\"%s\"\tpos=%d\treason=\"%s\"", pszType, enStoragePos, pszReason);
    }
    //print_level(SV_DEBUG, "%s\n", szLogBuf);
    LOG_Submit(-1, szLogBuf);
}

/******************************************************************************
 * 函数功能: 检查目录是否存在，不存在就创建
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_CheckDir(void)
{
    sint32 s32Ret = -1, i;

    CONFIG_FlashProtection(SV_FALSE);
    if (0 != access("/var/info/", F_OK))
    {
        s32Ret = mkdir("/var/info", 0777);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "mkdir /var/info/ failed. [err=%#x]\n", errno);
        }
    }

    if (0 != access("/var/mounting/", F_OK))
    {
        s32Ret = mkdir("/var/mounting", 0777);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "mkdir /var/mounting/ failed. [err=%#x]\n", errno);
        }
    }

    if (0 != access("/var/repairing/", F_OK))
    {
        s32Ret = mkdir("/var/repairing", 0777);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "mkdir /var/repairing/ failed. [err=%#x]\n", errno);
        }
    }

    for(i=0; i<MOUNT_PATH_NUM; i++)
    {
        if (0 != access(mountPathArray[i], F_OK))
        {
            s32Ret = mkdir(mountPathArray[i], 0666);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "mkdir %s failed. [err=%#x]\n", mountPathArray[i], errno);
            }
        }
    }

    for(i=0; i<MOUNT_PATH_NUM; i++)
    {
        if (0 != access(mountMediaPath[i], F_OK))
        {
            s32Ret = mkdir(mountMediaPath[i], 0666);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "mkdir %s failed. [err=%#x]\n", mountMediaPath[i], errno);
            }
        }
    }

    CONFIG_FlashProtection(SV_TRUE);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取存储设备块设备地址
 * 输入参数: 无
 * 输出参数: pszBlockPath --- 设备块对应的设备文件路径 (如: /dev/sda)
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败

 * 注意    : 无
 *****************************************************************************/
sint32 storage_GetBlockDevPath(STORAGE_POS_E enStoragePos, char *pszBlockDevPath)
{
    if (enStoragePos >= STORAGE_MEMORY || NULL == pszBlockDevPath)
    {
        print_level(SV_ERROR, "param is invalid.\n");
        return SV_FAILURE;
    }

    //print_level(SV_DEBUG, "enStoragePos = %d.\n", enStoragePos);

#if (defined(BOARD_IPCR20S4) || defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
    if ((STORAGE_MAIN_SD1 == enStoragePos) && (0 == access(STORAGE_BLKDEV_PATH, F_OK)))
    {
        strcpy(pszBlockDevPath, STORAGE_BLKDEV_PATH);
    }
#if (defined(BOARD_ADA47V1))
    else if((STORAGE_INNER_EMMC == enStoragePos) && (0 == access(EMMC_BLKDEV_PATH, F_OK)))
    {
        strcpy(pszBlockDevPath, EMMC_BLKDEV_PATH);
    }
#endif
    else if((STORAGE_EXTRA_SD == enStoragePos) && (0 == access(USB_BLKDEV_PATH, F_OK)))
    {
        strcpy(pszBlockDevPath, USB_BLKDEV_PATH);
    }
    else
    {
        return SV_FAILURE;
    }
#else
    return SV_FAILURE;
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 指定复位某个存储设备
 * 输入参数: enStoragePos --- 设备块存储位置
 * 输出参数: pszDevPath --- 复位成功后设备的文件路径
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_ResetDevice(STORAGE_POS_E enStoragePos, char *pszDevPath)
{
    sint32 s32Ret = 0, i;
    char szDevPath[64];

    m_stStorageInfo.astDevStatTab[enStoragePos].enDevStat = SINFO_DEV_FORBID;
    m_stStorageInfo.astDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_FORBID;
    storage_DumpModuleInfo(&m_stStorageInfo);
    print_level(SV_INFO, "begin reset sd%d.\n", enStoragePos + 1);
    s32Ret = BOARD_ResetSDCard();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_ResetSDCard failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    sleep_ms(1000);
    for (i = 0; i < 12; i++)
    {
        s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
        if (SV_SUCCESS == s32Ret)
        {
            break;
        }

        print_level(SV_WARN, "wait for sd%d wake up %ds\n", enStoragePos + 1, i + 1);
        sleep_ms(1000);
    }

    if (i >= 12)
    {
        print_level(SV_ERROR, "wait for sd%d wake up failed. wait time>12s\n", enStoragePos+1);
        return SV_FAILURE;
    }

    print_level(SV_INFO, "reset sd%d successful.\n", enStoragePos + 1);
    if (NULL != pszDevPath)
    {
        strcpy(pszDevPath, szDevPath);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能:获取sd卡容量
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 容量值 (Byte)
 * 注意    : 无
 *****************************************************************************/
sint64 storage_BlockCapacity(const char* blockDevPath)
{
    int s32SectorSize,s32SectorNum;
    sint64 s64Capcaciy = 0;
    int s32Ret,s32Fd = open(blockDevPath, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open %s failed. [err=%#x]\n", blockDevPath, errno);
        return 0;
    }

    s32Ret = ioctl(s32Fd, BLKSSZGET, &s32SectorSize);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "%s ioctl BLKSSZGET failed. [err=%#x]\n", blockDevPath, errno);
        close(s32Fd);
        return 0;
    }

    s32Ret = ioctl(s32Fd, BLKGETSIZE, &s32SectorNum);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "%s ioctl BLKSSZGET failed. [err=%#x]\n", blockDevPath, errno);
        close(s32Fd);
        return 0;
    }

    close(s32Fd);
    s64Capcaciy = ((uint64)s32SectorSize) * ((uint64)s32SectorNum) ;
    //print_level(SV_INFO,"s32SectorSize:%d s32SectorNum:%d s64Capcaciy:%lld\n",s32SectorSize,s32SectorNum,s64Capcaciy);
    return s64Capcaciy;
}

/******************************************************************************
 * 函数功能: 触发存储设备更新生成分区设备文件
 * 输入参数: pszDevPath --- 存储设备文件路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_UpdatePartion(char *pszDevPath)
{
    char partionDevPath[128];
    char szcmd[128];
    int cmd,s32Ret,s32Fd;
    int i;
    cmd = 10;
    while(cmd > 0)
    {
        s32Fd = open(pszDevPath, O_RDWR);
        if(s32Fd > 0)
            break;
        cmd--;
        usleep(100000);
    }
    if(cmd<=0)
    {
        print_level(SV_ERROR,"open %s fail\n",pszDevPath);
        return SV_FAILURE;
    }
    cmd = 0;
    s32Ret = ioctl(s32Fd, BLKRRPART,(int*)NULL);
    if(s32Ret!=0)
    {
        print_level(SV_WARN,"IOCTL fail %d %s\n",s32Ret,strerror(errno));
        close(s32Fd);
        return SV_SUCCESS;
    }
    close(s32Fd);

    for(i = 1; i <= MAX_PARTIRION_NUM; i++)
    {
        sprintf(partionDevPath, (NULL != strstr(pszDevPath, "mmcblk")) ? "%sp%d" : "%s%d", pszDevPath, i);
        sprintf(szcmd,"fdisk -l %s|grep %s",pszDevPath,partionDevPath);
        if(SAFE_System(szcmd,NORMAL_WAIT_TIME)!=0)
            if(SAFE_System(szcmd,NORMAL_WAIT_TIME)!=0)
                continue;
        cmd = 0;
        while(access(partionDevPath,F_OK)!=0)
        {
            if(cmd>20)
            {
                print_level(SV_ERROR,"%s disappear\n",partionDevPath);
                return SV_FAILURE;
            }
            cmd++;
            sleep_ms(100);
        }
    }
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能:
 *         update partition table;
 *         获取某个存储设备的容量
 * 输入参数: enStoragePos --- 设备块存储位置
 * 输出参数: 无
 * 返回值  : 分区容量值 (Byte)
 * 注意    : 无
 *****************************************************************************/
sint64 storage_Scan(STORAGE_POS_E enStoragePos)
{
    sint32 s32Ret = 0;
    char szDevPath[64];

    if (enStoragePos > STORAGE_EXTRA_SD)
    {
        return 0;
    }

    s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "storage_GetBlockDevPath failed. [pos=%d, err=%#x]\n", enStoragePos, s32Ret);
        return 0;
    }
    if(storage_UpdatePartion(szDevPath)!=SV_SUCCESS)
        return 0;

    return storage_BlockCapacity(szDevPath);
}

/******************************************************************************
 * 函数功能: 获取挂载的文件路径
 * 输入参数: pos --- 设备位置
 * 输出参数: 无
 * 返回值  : 路径字符串或者NULL
 *****************************************************************************/
const char* STORAGE_GetMountPath(STORAGE_POS_E pos)
{
    if(pos >= MOUNT_PATH_NUM)
        return NULL;
    return mountPathArray[pos];
}

/******************************************************************************
 * 函数功能: 获取挂载的文件路径
 * 输入参数: pos --- 设备位置
 * 输出参数: 无
 * 返回值  : 路径字符串或者NULL
 *****************************************************************************/
const char* STORAGE_GetMountExt(STORAGE_POS_E pos)
{
    if(pos >= MOUNT_PATH_NUM)
        return NULL;
    return mountMediaPath[pos];
}

/******************************************************************************
 * 函数功能: 判断存储设备是否存在 mounted 标志文件
 * 输入参数: enStoragePos --- 存储设备位置
              enFsType --- 指定检查文件系统类型
 * 输出参数: 无
 * 返回值  :挂载状态
 *****************************************************************************/
long storage_CatMountStatus(STORAGE_POS_E enStoragePos, SINFO_FS_E enFsType)
{
    sint32 s32Ret = 0;
    char szProcMount[4096];
    char szMountedFile[64];
    char szSearchString[64];
    const char *pszMountPath = NULL;
    char szCmd[128] = {0};

    if ((enStoragePos >= MAX_STORAGE_NUM)
#if defined(BOARD_ADA47V1)
        && STORAGE_INNER_EMMC != enStoragePos
#endif
        && STORAGE_EXTRA_SD != enStoragePos)
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d]\n", enStoragePos);
        return MOUNT_NOMOUNT;
    }

    if(SAFE_CAT("/proc/mounts",szProcMount,4096)!=0)
    {
        print_level(SV_ERROR,"could not open /proc/mounts\n");
        return MOUNT_NOMOUNT;
    }

    if (enFsType==SINFO_FS_FAT32)
    {
        pszMountPath = STORAGE_GetMountPath(enStoragePos);
        sprintf(szSearchString,"%s ",pszMountPath);
        if (NULL != pszMountPath)
        {
            sprintf(szMountedFile, "%s/mounted", pszMountPath);
            if(strstr(szProcMount,szSearchString)!=NULL)
            {
                if(0 == access(szMountedFile, F_OK))
                {
                    sprintf(szCmd, "touch %s/mounted", pszMountPath);
                    s32Ret = SAFE_SV_System(szCmd);
                    if (0 == s32Ret)
                    {
                        return MOUNT_SUCCESS;
                    }
                    else
                    {
                        return MOUNT_EXCEPT;
                    }
                }
                else
                {
                    return MOUNT_EXCEPT;
                }
            }
            else
                return MOUNT_NOMOUNT;
        }
    }

    if (enFsType==SINFO_FS_EXT4)
    {
        pszMountPath = STORAGE_GetMountExt(enStoragePos);
        sprintf(szSearchString,"%s ",pszMountPath);
        if (NULL != pszMountPath)
        {
            sprintf(szMountedFile, "%s/mounted", pszMountPath);
            if(strstr(szProcMount,szSearchString)!=NULL)
            {
                if(0 == access(szMountedFile, F_OK))
                    return MOUNT_SUCCESS;
                else
                    return MOUNT_EXCEPT;
            }
            else
                return MOUNT_NOMOUNT;
        }
    }

    return MOUNT_NOMOUNT;
}

/******************************************************************************
 * 函数功能: 根据分区信息识别出媒体数据存储的文件系统类型
 * 输入参数: enStoragePos --- 存储设备位置
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SINFO_FS_E storage_GetMediaFsType(STORAGE_POS_E enStoragePos)
{
    sint32 s32Ret = 0, i;
    char szDevPath[64];
    char szPartionPath[64];
    sint32 s32Fd;
    char szCmd[128];
    char szBuf[2048];
    sint32 as32SizeKB[3] = {0};
    char szStr[256] = {0};
    char szTmp[64];
    struct hd_geometry stGeometry;

    if (enStoragePos > STORAGE_EXTRA_SD)
    {
        print_level(SV_ERROR, "invalied storage pos=%d\n", enStoragePos);
        return SINFO_FS_BUTT;
    }

    if (STORAGE_INNER_EMMC == enStoragePos)
    {
        return SINFO_FS_EXT4;
    }

    for (i = 0; i < 3; i++)
    {
        s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
        if (SV_SUCCESS == s32Ret)
        {
            break;
        }
        //print_level(SV_WARN, "storage_GetBlockDevPath failed. [pos=%d, err=%#x]\n", enStoragePos, s32Ret);
    }
    if (i >= 3)
    {
        //print_level(SV_ERROR, "storage_GetBlockDevPath failed 3 times. [pos=%d, err=%#x]\n", enStoragePos, s32Ret);
        return SINFO_FS_UNKNOWN;
    }

    s32Fd = open(szDevPath, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_WARN, "open %s failed. [err=%#x]\n", szDevPath, errno);
    }
    else
    {
        s32Ret = ioctl(s32Fd, HDIO_GETGEO, &stGeometry);
        if (s32Ret < 0)
        {
            printf("%s ioctl HDIO_GETGEO failed. [err=%#x]\n", szDevPath, errno);
        }
        else
        {
            sprintf(szStr, "%d heads, %d sectors/track, %d cylinders | ", stGeometry.heads, stGeometry.sectors, stGeometry.cylinders);
        }
    }
    close(s32Fd);
    //获取总的卡容量
    //多分区的情况下，第一分区文件系统类型一定是FAT32
    for (i = 0; i < MAX_PARTIRION_NUM; i++)
    {
        sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp%d" : "%s%d", szDevPath, i+1);
        if (0 != access(szPartionPath, F_OK))
        {
            //print_level(SV_DEBUG,"%s could not got %s\n",szPartionPath,strerror(errno));
            continue;
        }
        as32SizeKB[i] = (sint32)(storage_BlockCapacity(szPartionPath)/1024);
        sprintf(szTmp, "%s:%dKB ", szPartionPath, as32SizeKB[i]);
        strcat(szStr, szTmp);
    }

    //print_level(SV_INFO, "[sd%d partinfo] %s\n", enStoragePos+1, szStr);

    //大于该阈值，只有FAT格式的文件系统
    if (as32SizeKB[0] > FILE_SYS_FAT_SIZE_THRESHOLD)
    {
        //print_level(SV_INFO, "[sd%d] media storage filesystem type is FAT32\n", enStoragePos+1);
        return SINFO_FS_FAT32;
    }
    //大于该阈值，存在FAT+EXT文件系统格式
    else if (as32SizeKB[1] > FILE_SYS_EXT_SIZE_THRESHOLD)
    {
        if (NULL != strstr(szDevPath, "mmcblk"))
        {
            sprintf(szCmd, "blkid %sp2 > /var/partinfo -c /dev/null -w /dev/null", szDevPath);
        }
        else
        {
            sprintf(szCmd, "blkid %s2 > /var/partinfo -c /dev/null -w /dev/null", szDevPath);
        }
        s32Ret = SAFE_SV_System(szCmd);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed. [pos=%d, err=%d]\n", szCmd, enStoragePos, s32Ret);
            return SINFO_FS_UNKNOWN;
        }
        s32Fd = open("/var/partinfo", O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: /var/partinfo failed. [pos=%d, err=%#x]\n", enStoragePos, errno);
            return SINFO_FS_UNKNOWN;
        }

        memset(szBuf, 0x0, sizeof(szBuf));
        s32Ret = read(s32Fd, szBuf, 2048);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "read file: /var/partinfo failed. [pos=%d, err=%#x]\n", enStoragePos, errno);
            close(s32Fd);
            return SINFO_FS_UNKNOWN;
        }
        close(s32Fd);

        if (NULL != strstr(szBuf, "ext4"))
        {
            return SINFO_FS_EXT4;
        }
        else
        {
            print_level(SV_WARN, "[sd%d] media storage partion as linux type, but filesystem maybe destory!\n", enStoragePos+1);
            return SINFO_FS_UNKNOWN;
        }
    }
    else
    {
        print_level(SV_WARN, "[sd%d] unknow media storage filesystem type!\n", enStoragePos+1);
        return SINFO_FS_UNKNOWN;
    }
}

/******************************************************************************
 * 函数功能: 检查块设备是否存在
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  :  SV_TRUE - 存在
           SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL storage_IsBlockExist(const char *Partition)
{
    if (NULL == Partition)
    {
        return SV_FALSE;
    }

    if(access(Partition, F_OK)==0)
    {
        return SV_TRUE;
    }
    return SV_FALSE;
}

/******************************************************************************
 * 函数功能: 获取块设备状态
 * 输入参数: 无
 * 输出参数: paDevStatTab --- 设备挂载状态列表
             pau64DevSize --- 设备容量列表 (Byte)
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_GetBlockDevStatus(BLK_DEV_INFO_S *pstBlockDevStat)
{
    uint8 i = 0;
    char aPath[64] = {0};
    SV_BOOL bIsInsert = SV_FALSE;
    if(NULL == pstBlockDevStat)
    {
        return SV_FAILURE;
    }
#if (defined(BOARD_IPCR20S4) || defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
    pstBlockDevStat->u32BlkNum = MAX_SD_CARD_NUM;
    for (i=0; i<pstBlockDevStat->u32BlkNum; i++)
    {
        memset(aPath, 0, sizeof(aPath));
        sprintf(aPath, "%s", STORAGE_BLKDEV_PATH);
        bIsInsert = storage_IsBlockExist(aPath);
        if(SV_TRUE == bIsInsert)
        {
            pstBlockDevStat->aBlkStat[i] = SINFO_BLK_RUNNING;
        }
        else
        {
            pstBlockDevStat->aBlkStat[i] = SINFO_BLK_UNEXIST;
            storage_SetErrCode(STORAGE_ERR_CODE_NO_DISK);
        }
    }

#else
    return ERR_NOT_SURPPORT;
#endif
    /* U盘设备 */
    bIsInsert = storage_IsBlockExist(USB_BLKDEV_PATH);
    if(SV_TRUE == bIsInsert)
    {
        pstBlockDevStat->aBlkStat[STORAGE_EXTRA_SD] = SINFO_BLK_RUNNING;
    }
    else
    {
        pstBlockDevStat->aBlkStat[STORAGE_EXTRA_SD] = SINFO_BLK_UNEXIST;
    }

#if defined(BOARD_ADA47V1)
    pstBlockDevStat->aBlkStat[STORAGE_INNER_EMMC] = SINFO_BLK_RUNNING;
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始块设备调度方式
 * 输入参数: type --- 文件系统类型
             enStoragePos --- 存储设备位置
 * 输出参数: 无
 * 返回值  : SV_TRUE -- 成功
              SV_FALSE -- 失败
 * 注意    : 无
 *****************************************************************************/
SV_BOOL storage_InitBlockDevice(STORAGE_POS_E enStoragePos, SINFO_FS_E type)
{
    char szTmpPath[128];
    char szDevPath[64];
    const char* devName;
    sint32 s32Ret = 0;
    print_level(SV_INFO,"init storage %d %d\n",enStoragePos,type);
    if(SINFO_FS_UNKNOWN == type)
        return SV_FALSE;
    s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
    if (SV_SUCCESS != s32Ret)
        return SV_FALSE;
    devName = szDevPath+ 5;

    print_level(SV_INFO,"use deadline scheduler\n");
    sprintf(szTmpPath,"/sys/block/%s/queue/scheduler",devName);
    SAFE_ECHO(szTmpPath,"deadline");
    sprintf(szTmpPath,"/sys/block/%s/device/max_sectors",devName);
    SAFE_ECHO(szTmpPath,"240");

    return SV_TRUE;
}
/******************************************************************************
 * 函数功能: 获取打开的文件
 * 输入参数: pcMouthPath --- 挂载目录
 * 输出参数: 无
 * 返回值  : SV_TRUE -- 成功
              SV_FALSE -- 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_GetOpenFile(char *pcMouthPath)
{
    sint32 s32Ret = 0;
    char szBuf[10240] = {0};
    char szCmd[256] = {0};
    sprintf(szCmd, "lsof | grep -E \"%s*\"", pcMouthPath);
    s32Ret = SAFE_System_Recv(szCmd, szBuf, sizeof(szBuf));
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }
    print_level(SV_WARN, "busy file %s\n",szBuf);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 对挂载目录进行强制卸载
 * 输入参数: pszMountPath --- 挂载点目录
             enStoragePos --- 存储设备位置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 只有在尝试多次卸载失败后才可以用该函数卸载
 *****************************************************************************/
sint32 storage_ForceUnmount(char *pszMountPath, STORAGE_POS_E enStoragePos)
{
    sint32 s32Ret = 0;
    char szCmd[64];
    char szDevPath[64];

    if (NULL == pszMountPath)
    {
        print_level(SV_ERROR, "param ptr is null.\n");
        return SV_FAILURE;
    }

    sprintf(szCmd, "touch  %s/mounted &", pszMountPath);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    sleep_ms(10);
    sprintf(szCmd, "umount -lf %s 1>/dev/null 2>/dev/null", pszMountPath);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    s32Ret = storage_ResetDevice(enStoragePos, szDevPath);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "storman_ResetDevice failed. [pos=%d]\n", enStoragePos);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 对挂载目录进行快速卸载
 * 输入参数: pszMountPath --- 挂载点目录
             enStoragePos --- 存储设备位置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_QuickUnmount(char *pszMountPath, STORAGE_POS_E enStoragePos)
{
    sint32 s32Ret = 0, i;
    char szCmd[64] = {0};

    if (NULL == pszMountPath)
    {
        print_level(SV_ERROR, "param ptr is null.\n");
        return SV_FAILURE;
    }

    for (i = 0; i < 20; i++)
    {
        s32Ret = umount2(pszMountPath, MNT_FORCE);
        if (0 == s32Ret)
        {
            break;
        }
        storage_GetOpenFile((char*)pszMountPath);
        sprintf(szCmd, "cat /proc/mounts | grep \"%s \" >> /dev/null", pszMountPath);
        s32Ret = SAFE_System(szCmd, 30000);
        if (1 == s32Ret)
        {
            break;
        }

        print_level(SV_WARN, "umount path: %s failed. [err=%#x]\n", pszMountPath, errno);
        sleep_ms(100);
    }

    if (i >= 20)
    {
        print_level(SV_ERROR, "umount path: %s 20 times failed. force umount it now. [err=%#x]\n", pszMountPath, errno);
        sprintf(szCmd, "ps > %s/fuser.txt", pszMountPath);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            SAFE_System("ps > /var/ps.txt", NORMAL_WAIT_TIME);
        }

        sprintf(szCmd, "fuser %s -m >> %s/fuser.txt", pszMountPath, pszMountPath);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            sprintf(szCmd, "fuser %s -m >> /var/fuser.txt", pszMountPath);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
        }
        storage_ForceUnmount(pszMountPath, enStoragePos);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 指定卸载某个存储设备
 * 输入参数: enStoragePos --- 设备块存储位置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_UnmountDevice(STORAGE_POS_E enStoragePos)
{
    sint32 s32Ret = 0, i, j;
    SV_BOOL bFailed = SV_FALSE;
    sint32 umountCmd = 0;
    char szCmd[64];
    const char *pszMountPath = NULL;

    if (enStoragePos > STORAGE_EXTRA_SD)
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d]\n", enStoragePos);
        return SV_FAILURE;
    }


    print_level(SV_INFO, "try to umount SD%d.\n", enStoragePos+1);
    for (i = 0; i < 2; i++)
    {
        if (0 == i)
        {
            if(storage_CatMountStatus(enStoragePos,SINFO_FS_FAT32) != MOUNT_NOMOUNT)
                pszMountPath = STORAGE_GetMountPath(enStoragePos);
            else
                continue;
        }
        else
        {
            if(storage_CatMountStatus(enStoragePos,SINFO_FS_EXT4) != MOUNT_NOMOUNT)
                pszMountPath = STORAGE_GetMountExt(enStoragePos);
            else
                continue;
        }

        if (NULL == pszMountPath)
        {
            print_level(SV_ERROR, "get mount path failed. [pos=%#x]\n", enStoragePos);
            bFailed = SV_TRUE;
            continue;
        }
        umountCmd++;
        sprintf(szCmd, "rm %s/mounted", pszMountPath);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        }
        sync();
        for (j = 0; j < 5; j++)
        {
            sprintf(szCmd, "umount %s", pszMountPath);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (0 == s32Ret)
            {
                break;
            }
            print_level(SV_WARN, "umount cmd %s failed. [err=%#x]\n", szCmd, s32Ret);
            storage_GetOpenFile((char*)pszMountPath);
            sprintf(szCmd, "cat /proc/mounts | grep \"%s \" >> /dev/null", pszMountPath);
            s32Ret = SAFE_System(szCmd, 30000);
            if (1 == s32Ret)
            {
                break;
            }
            print_level(SV_WARN, "umount path: %s failed. [err=%#x]\n", pszMountPath, errno);
            sleep_ms(*(m_stStorageInfo.pbPwrOff) ? 20 : 1000);
        }
        if (j >= 5)
        {
            print_level(SV_ERROR, "umount path: %s 15 times failed. force umount it now. [err=%#x]\n", pszMountPath, errno);
            sprintf(szCmd, "ps > %s/fuser.txt", pszMountPath);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                SAFE_System("ps > /var/ps.txt", NORMAL_WAIT_TIME);
            }

            sprintf(szCmd, "fuser -m %s >> %s/fuser.txt", pszMountPath, pszMountPath);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                sprintf(szCmd, "fuser %s -m >> /var/fuser.txt", pszMountPath);
                SAFE_System(szCmd, NORMAL_WAIT_TIME);
            }
            storage_ForceUnmount((char*)pszMountPath, enStoragePos);
            bFailed = SV_TRUE;
            continue;
        }
    }

    if(umountCmd==0)
    {
        print_level(SV_ERROR, "SD%d  no mount !!!\n", enStoragePos+1);
        return SV_FAILURE;
    }

    if (bFailed)
    {
        print_level(SV_ERROR, "SD%d umount failure!!!\n", enStoragePos+1);
        return SV_FAILURE;
    }
    print_level(SV_INFO, "SD%d umount successful!!!\n", enStoragePos+1);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取文件状态
 * 输入参数: pFilename -- 文件名
 * 输出参数: pstFileStat -- 文件状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_GetFileStat(const char* pFilename, struct stat *pstFileStat)
{
    sint32 s32Ret = 0;
    struct stat fileStat = {0};
    if ((NULL == pFilename) || (NULL == pstFileStat))
    {
        return ERR_NULL_PTR;
    }

    s32Ret = stat(pFilename,&fileStat);
    if (0 != s32Ret )
    {
        print_level(SV_ERROR, "stat failed error = %x\n", s32Ret);
        memset(pstFileStat, 0, sizeof(struct stat));
        return s32Ret;
    }
    memcpy(pstFileStat, &fileStat, sizeof(struct stat));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 扫描指定卡里的录像文件，获取最新修改时间
 * 输入参数: pszRootPath -- 文件路径
 * 输出参数: ps32Mtime -- 最新修改时间
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 子节点不扫描图片节点目录，避免删除文件时找到图片文件删除
 *****************************************************************************/
sint32 storage_ScanNodeFile(const char *pszRootPath, sint32 *ps32Mtime)
{
    sint32 s32Ret = 0, i;
    uint32 u32DirNum = 3;
    DIR *pDirRoot = NULL;
    DIR *pDirDate = NULL;
    DIR *pDirHour = NULL;
    DIR *pDirMinute = NULL;
    struct dirent *pstDirentRoot = NULL;
    struct dirent *pstDirentDate = NULL;
    struct dirent *pstDirentHour = NULL;
    struct dirent *pstDirentMinute = NULL;
    char szDirDate[64];
    char szDirHour[64];
    char szDirMinute[64];
    sint32 s32Mtime = 0;
    struct stat stFileStat = {0};

    if (NULL == pszRootPath)
    {
        return ERR_ILLEGAL_PARAM;
    }
    //打开类型根目录
    pDirRoot = opendir(pszRootPath);
    if (NULL == pDirRoot)
    {
        print_level(SV_ERROR, "open sdroot: %s failed. [err: %s]\n", pszRootPath, strerror(errno));
        return ERR_UNEXIST;
    }

    while ((pstDirentRoot = readdir(pDirRoot)) != NULL)
    {
        if (pstDirentRoot->d_type != 4)
            continue;
        if (strcmp(pstDirentRoot->d_name, ".")==0)
            continue;
        if (strcmp(pstDirentRoot->d_name, "..")==0)
            continue;
        s32Ret = fnmatch("[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]", pstDirentRoot->d_name, FNM_PATHNAME|FNM_PERIOD);
        if (0 != s32Ret)
        {
            continue;
        }
        //打开时目录
        sprintf(szDirDate, "%s/%s", pszRootPath, pstDirentRoot->d_name);
        pDirDate = opendir(szDirDate);
        if (NULL == pDirDate)
        {
            continue;
        }
        //循环遍历目录及其文件
        while ((pstDirentDate = readdir(pDirDate)) != NULL)
        {
            if (pstDirentDate->d_type != 4)
                continue;
            if (strcmp(pstDirentDate->d_name, ".")==0)
                continue;
            if (strcmp(pstDirentDate->d_name, "..")==0)
                continue;

            sprintf(szDirHour, "%s/%s", szDirDate, pstDirentDate->d_name);
            pDirHour = opendir(szDirHour);
            if (NULL == pDirHour)
            {
                continue;
            }

            while ((pstDirentHour = readdir(pDirHour)) != NULL)
            {
                if (pstDirentHour->d_type != 4)
                    continue;
                if (strcmp(pstDirentHour->d_name, ".")==0)
                    continue;
                if (strcmp(pstDirentHour->d_name, "..")==0)
                    continue;

                sprintf(szDirMinute, "%s/%s", szDirHour, pstDirentHour->d_name);
                s32Ret = storage_GetFileStat(szDirMinute, &stFileStat);
                if (SV_SUCCESS == s32Ret)
                {
                    s32Mtime = STO_MAX(stFileStat.st_mtime, s32Mtime);
                    *ps32Mtime = s32Mtime;
                }
            }
            closedir(pDirHour);
        }
        closedir(pDirDate);
    }
    closedir(pDirRoot);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 根据录像文件路径更新时间
 * 输入参数: pszMountPath -- 挂载路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_UpdateSysTime(char *pszMountPath)
{
    sint32 s32Ret = 0;
    char szCmd[128] = {0};
    static sint32 s32Mtime = 0;
    sint32 s32GetTime = 0;
    static SV_BOOL bUpdateTime = SV_FALSE; //时间更新标志，避免热插拔时候又更新时间
    struct stat stFileStat = {0};
    struct timeval tvTime;
    struct timezone tz;

    snprintf(szCmd, 128, "%s/normal", pszMountPath);
    if ((0 == access(szCmd, F_OK)))
    {
        s32Ret = storage_ScanNodeFile(szCmd, &s32GetTime);
        s32Mtime = STO_MAX(s32Mtime, s32GetTime);
    }
    snprintf(szCmd, 128, "%s/alarm", pszMountPath);
    if ((0 == access(szCmd, F_OK)))
    {
        s32Ret = storage_ScanNodeFile(szCmd, &s32GetTime);
        s32Mtime = STO_MAX(s32Mtime, s32GetTime);
    }
    snprintf(szCmd, 128, "%s/picture", pszMountPath);
    if ((0 == access(szCmd, F_OK)))
    {
        s32Ret = storage_ScanNodeFile(szCmd, &s32GetTime);
        s32Mtime = STO_MAX(s32Mtime, s32GetTime);
    }

    gettimeofday(&tvTime, &tz);
    if (!bUpdateTime && s32Mtime > tvTime.tv_sec)
    {
        print_level(SV_INFO, "start sync time by file\n");
        m_stStorageInfo.s32UTCminute = (m_stStorageInfo.s32UTChour > 0) ? m_stStorageInfo.s32UTCminute : -m_stStorageInfo.s32UTCminute;
        tz.tz_minuteswest = m_stStorageInfo.s32UTChour*60 + m_stStorageInfo.s32UTCminute;
        tvTime.tv_sec = s32Mtime;//UTC时间
        tvTime.tv_usec = 0;
        if (settimeofday(&tvTime, &tz) < 0)//设置UTC时间
        {
            print_level(SV_ERROR, "settimeofday failed.\n");
            return SV_FAILURE;
        }
        bUpdateTime = SV_TRUE;
        print_level(SV_INFO, "finsih sync time by file\n");
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查某个 EXT 存储设备状态
 * 输入参数: pvStoragePos --- 设备块存储位置参数指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 设备正常
             SV_FAILURE - 检查失败
             STORAGE_ERR_FORBID_MOUNT - 设备被禁止
             STORAGE_ERR_EXCEPT_UNMOUT - 设备出现异常卸载
 * 注意    : 无
 *****************************************************************************/
slng32 storage_CheckExtDevice(void *pvStoragePos)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    uint64 u64FreeBytes = 0;
    struct statvfs stStatvfs = {0};
    char szDevPath[64];
    char szPartionPath[64];
    char szTestFile[64];
    STORAGE_POS_E enStoragePos;
    const char *pszFsType = NULL;

    if (SIG_ERR == signal(SIGPIPE, SIG_IGN))
    {
        print_level(SV_ERROR, "catch signal SIGPIPE Error: %d, %s\n", errno, strerror(errno));
    }

    if (NULL == pvStoragePos)
    {
        print_level(SV_ERROR, "param prt is null.\n");
        goto fail_exit;
    }

    enStoragePos = *(STORAGE_POS_E *)pvStoragePos;
    print_level(SV_INFO, "[sd%d] check begin...\n", enStoragePos+1);
    if (enStoragePos > STORAGE_EXTRA_SD)
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d]\n", enStoragePos);
        goto fail_exit;
    }
    umount("/var/mounting");

    pszFsType = "ext4";

    if((s32Ret=storage_GetBlockDevPath(enStoragePos, szDevPath))!=SV_SUCCESS)
    {
        print_level(SV_ERROR, "BOARD_GetBlockDevPath failed. [pos=%d, err=%#x]\n", enStoragePos, s32Ret);
        goto fail_exit;
    }

#if defined(BOARD_ADA47V1)
    if (enStoragePos == STORAGE_INNER_EMMC)
    {
        sprintf(szPartionPath, "%sp4", szDevPath);
    }
    else
    {
        sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp2" : "%s2", szDevPath);
    }
#else
    sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp2" : "%s2", szDevPath);
#endif

    if (0 != access(szPartionPath, F_OK))
    {
        print_level(SV_ERROR, "storman_UpdatePartion: %s failed. [pos=%d, err=%#x]\n", szPartionPath, enStoragePos, s32Ret);
        goto fail_exit;
    }

    s32Ret = mount(szPartionPath, "/var/mounting", pszFsType, MS_SYNCHRONOUS, NULL);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "first mount %s /var/mounting failed. [pos=%d, type=%s, err=%#x]\n", szPartionPath, enStoragePos, pszFsType, errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "mount command failed.");
        goto fail_exit;
    }

    /* 获取容量 */
    s32Ret = statvfs("/var/mounting", &stStatvfs);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "statvfs /var/mounting failed. [pos=%d, type=%s, err=%#x]\n", enStoragePos, pszFsType, errno);
        storage_QuickUnmount("/var/mounting", enStoragePos);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "statvfs command failed.");
        goto fail_exit;
    }
    u64FreeBytes = (uint64)stStatvfs.f_bfree * (uint64)stStatvfs.f_bsize;

    if (u64FreeBytes < 10 * 1024 * 1024)
    {
        print_level(SV_ERROR, "[pos=%d, type=%s, %s] remain size %lld < 10MB\n", enStoragePos, pszFsType, szPartionPath, u64FreeBytes);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "remain size less than 10MB");
        storage_QuickUnmount("/var/mounting", enStoragePos);
        goto fail_exit;
    }

    if (0 != access("/var/mounting/testDir", F_OK))
    {
        s32Ret = mkdir("/var/mounting/testDir", 0777);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "mkdir /var/mounting/testDir failed. [pos=%d, type=%s, err=%#x]\n", enStoragePos, pszFsType, errno);
            storage_LOG_OperateDevice("mountFail", enStoragePos, "try to create direction failed.");
            storage_QuickUnmount("/var/mounting", enStoragePos);
            goto fail_exit;
        }
    }

    SAFE_System("rm /var/mounting/testDir/* 1>/dev/null 2>/dev/null", NORMAL_WAIT_TIME);
    sprintf(szTestFile, "/var/mounting/testDir/%ld", time(NULL));
    s32Fd = open(szTestFile, O_RDWR|O_CREAT);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [pos=%d, type=%s, err=%#x]\n", szTestFile, enStoragePos, pszFsType, errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "try to create file failed.");
        storage_QuickUnmount("/var/mounting", enStoragePos);
        goto fail_exit;
    }

    close(s32Fd);
    storage_QuickUnmount("/var/mounting", enStoragePos);

    /* 第二次挂载确认文件可写入 */
    s32Ret = mount(szPartionPath, "/var/mounting", pszFsType, MS_SYNCHRONOUS, NULL);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "second mount %s /var/mounting failed. [pos=%d, type=%s, err=%#x]\n", szPartionPath, enStoragePos, pszFsType, errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "second mount command failed.");
        goto fail_exit;
    }

    if (0 != access(szTestFile, F_OK))
    {
        print_level(SV_ERROR, "second mount for access file: %s failed. [pos=%d, type=%s, err=%#x]\n", szTestFile, enStoragePos, pszFsType, errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "try second mount to confirm writable failed.");
        storage_QuickUnmount("/var/mounting", enStoragePos);
        goto fail_exit;
    }

    remove(szTestFile);
    s32Ret = storage_QuickUnmount("/var/mounting", enStoragePos);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "storman_QuickUnmount failed. [pos=%d, type=%s, err=%#x]\n", enStoragePos, pszFsType, s32Ret);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "umount fail after check device finish.");
        goto fail_exit;
    }

    print_level(SV_INFO, "[sd%d] check normal.\n", enStoragePos+1);
    return SV_SUCCESS;

fail_exit:
    return STORAGE_ERR_FAILURE;
}

/******************************************************************************
 * 函数功能: 检查某个FAT 存储设备状态
 * 输入参数: pvStoragePos --- 设备块存储位置参数指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 设备正常
             SV_FAILURE - 检查失败
             STORAGE_ERR_FORBID_MOUNT - 设备被禁止
             STORAGE_ERR_EXCEPT_UNMOUT - 设备出现异常卸载
 * 注意    : 无
 *****************************************************************************/
slng32 storage_CheckFatDevice(void *pvStoragePos)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    uint64 u64FreeBytes = 0;
    struct statvfs stStatvfs = {0};
    char szDevPath[64];
    char szPartionPath[64];
    char szTestFile[64];
    STORAGE_POS_E enStoragePos;


    if (SIG_ERR == signal(SIGPIPE, SIG_IGN))
    {
        print_level(SV_ERROR, "catch signal SIGPIPE Error: %d, %s\n", errno, strerror(errno));
    }

    if (NULL == pvStoragePos)
    {
        print_level(SV_ERROR, "param prt is null.\n");
        goto fail_exit;
    }

    enStoragePos = *(STORAGE_POS_E *)pvStoragePos;
    print_level(SV_INFO, "[sd%d] check begin...\n", enStoragePos+1);
    if (enStoragePos > STORAGE_EXTRA_SD)
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d]\n", enStoragePos);
        goto fail_exit;
    }

    umount("/var/mounting");

    if((s32Ret=storage_GetBlockDevPath(enStoragePos, szDevPath))!=SV_SUCCESS)
    {
        print_level(SV_ERROR, "BOARD_GetBlockDevPath failed. [pos=%d, err=%#x]\n", enStoragePos, s32Ret);
        goto fail_exit;
    }

    sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp1" : "%s1", szDevPath);
    if (0 != access(szPartionPath, F_OK))
    {
        print_level(SV_ERROR, "storman_UpdatePartion: %s failed. [pos=%d, err=%#x]\n", szPartionPath, enStoragePos, s32Ret);
        goto fail_exit;
    }

    s32Ret = mount(szPartionPath, "/var/mounting", "vfat", MS_SYNCHRONOUS, NULL);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "first mount %s /var/mounting failed. [pos=%d, type=%s, err=%#x]\n", szPartionPath, enStoragePos, "vfat", errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "mount command failed.");
        goto fail_exit;
    }

    /* 获取容量 */
    s32Ret = statvfs("/var/mounting", &stStatvfs);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "statvfs /var/mounting failed. [pos=%d, type=%s, err=%#x]\n", enStoragePos, "vfat", errno);
        storage_QuickUnmount("/var/mounting", enStoragePos);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "statvfs command failed.");
        goto fail_exit;
    }
    u64FreeBytes = (uint64)stStatvfs.f_bfree * (uint64)stStatvfs.f_bsize;

    if (u64FreeBytes < 10 * 1024 * 1024)
    {
        print_level(SV_ERROR, "[pos=%d, type=%s, %s] remain size %lld < 10MB\n", enStoragePos, "vfat", szPartionPath, u64FreeBytes);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "remain size less than 10MB");
        storage_QuickUnmount("/var/mounting", enStoragePos);
        goto fail_exit;
    }

    if (0 != access("/var/mounting/testDir", F_OK))
    {
        s32Ret = mkdir("/var/mounting/testDir", 0777);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "mkdir /var/mounting/testDir failed. [pos=%d, type=%s, err=%#x]\n", enStoragePos, "vfat", errno);
            storage_LOG_OperateDevice("mountFail", enStoragePos, "try to create direction failed.");
            storage_QuickUnmount("/var/mounting", enStoragePos);
            goto fail_exit;
        }
    }

    SAFE_System("rm /var/mounting/testDir/* 1>/dev/null 2>/dev/null", NORMAL_WAIT_TIME);
    sprintf(szTestFile, "/var/mounting/testDir/%ld", time(NULL));
    s32Fd = open(szTestFile, O_RDWR|O_CREAT);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [pos=%d, type=%s, err=%#x]\n", szTestFile, enStoragePos, "vfat", errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "try to create file failed.");
        storage_QuickUnmount("/var/mounting", enStoragePos);
        goto fail_exit;
    }

    close(s32Fd);
    storage_QuickUnmount("/var/mounting", enStoragePos);

    /* 第二次挂载确认文件可写入 */
    s32Ret = mount(szPartionPath, "/var/mounting", "vfat", MS_SYNCHRONOUS, NULL);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "second mount %s /var/mounting failed. [pos=%d, type=%s, err=%#x]\n", szPartionPath, enStoragePos, "vfat", errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "second mount command failed.");
        goto fail_exit;
    }

    if (0 != access(szTestFile, F_OK))
    {
        print_level(SV_ERROR, "second mount for access file: %s failed. [pos=%d, type=%s, err=%#x]\n", szTestFile, enStoragePos, "vfat", errno);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "try second mount to confirm writable failed.");
        storage_QuickUnmount("/var/mounting", enStoragePos);
        goto fail_exit;
    }

    remove(szTestFile);

    if (0 == access("/var/mounting/mounted", F_OK))
    {
        storage_QuickUnmount("/var/mounting", enStoragePos);
        print_level(SV_DEBUG, "last time except unmount\n");
        return STORAGE_ERR_EXCEPT_UNMOUT;
    }

    s32Ret = storage_QuickUnmount("/var/mounting", enStoragePos);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "storman_QuickUnmount failed. [pos=%d, type=%s, err=%#x]\n", enStoragePos, "vfat", s32Ret);
        storage_LOG_OperateDevice("mountFail", enStoragePos, "umount fail after check device finish.");
        goto fail_exit;
    }

    print_level(SV_INFO, "[sd%d] check normal.\n", enStoragePos+1);
    return SV_SUCCESS;

fail_exit:
    storage_SetErrCode(STORAGE_ERR_CODE_DISK_READONLY);
    return STORAGE_ERR_FAILURE;
}


/******************************************************************************
 * 函数功能: 指定修复某个存储设备的文件系统
 * 输入参数:
 *         enStoragePos --- 设备块存储位置
 *         type        --- the type of filesystem
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
             STORAGE_ERR_REPAIR_FAILED - 出现修复失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_RepairFileSys(STORAGE_POS_E enStoragePos,SINFO_FS_E enFsType)
{
    sint32 s32Ret = 0, i, k;
    sint32 s32Num = 0;
    SV_BOOL bFailed = SV_FALSE;
    char szCmd[128];
    char szDevPath[64];
    char szPartionPath[64];
    BLK_DEV_INFO_S stBlockStat = {0};
    const char *pszFsType = NULL;
    const char *pszExtTool = NULL;

    if (enStoragePos > STORAGE_EXTRA_SD && STORAGE_ALL != enStoragePos)
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d]\n", enStoragePos);
        return SV_FAILURE;
    }

    s32Ret = storage_GetBlockDevStatus(&stBlockStat);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_GetBlockDevStatus failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (STORAGE_ALL == enStoragePos)
    {
        i = 0;
        s32Num = MAX_STORAGE_NUM;
    }
    else
    {
        i = (sint32)enStoragePos;
        s32Num = (sint32)enStoragePos + 1;
    }

    for (; i < s32Num; i++)
    {
        /* 最后位置的设备为U盘 */
        if ((i == (MAX_STORAGE_NUM - 1)))
        {
            i = STORAGE_EXTRA_SD;
        }

        if (SINFO_BLK_RUNNING != stBlockStat.aBlkStat[i])
        {
            continue;
        }

        enStoragePos = (STORAGE_POS_E)i;
        print_level(SV_INFO, "begin try to repair sd%d...\n", enStoragePos + 1);
        storage_LOG_OperateDevice("repairStart", enStoragePos, NULL);
        s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);

        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "BOARD_GetBlockDevPath pos=%d failed. [err=%#x]\n", enStoragePos, s32Ret);
            storage_LOG_OperateDevice("repairFail", enStoragePos, "can not get device path.");
            bFailed = SV_TRUE;
            continue;
        }

        if(storage_UpdatePartion(szDevPath)!=SV_SUCCESS)
        {
            print_level(SV_ERROR, "storman_UpdatePartion pos=%d failed. \n", enStoragePos);
            storage_LOG_OperateDevice("repairFail", enStoragePos, "partion could not found.");
            bFailed = SV_TRUE;
            continue;
        }

        if(enFsType == SINFO_FS_FAT32)
        {
            sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp1" : "%s1", szDevPath);
            SAFE_System("rm /var/core*", NORMAL_WAIT_TIME);
            SAFE_System("killall -9 fsck", NORMAL_WAIT_TIME);
            pszFsType = "vfat";
            sprintf(szCmd, "fsck.fat %s -w -f -a -v", szPartionPath);
            m_stStorageInfo.astDevStatTab[i].enDevStat = SINFO_DEV_REPAIRING_FS;
            storage_DumpModuleInfo(&m_stStorageInfo);
        }
        else if(enFsType==SINFO_FS_EXT4)
        {
            sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp2" : "%s2", szDevPath);
            pszExtTool = "fsck.ext4";
            pszFsType = "ext4";
            SAFE_System("rm /var/core*", NORMAL_WAIT_TIME);
            sprintf(szCmd, "killall -9 %s", pszExtTool);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
            sprintf(szCmd, "%s -a %s", pszExtTool, szPartionPath);
            m_stStorageInfo.astDevStatTab[i].enMediaMountStat = SINFO_DEV_REPAIRING_FS;
            storage_DumpModuleInfo(&m_stStorageInfo);
        }
        else
            continue;

        print_level(SV_INFO,"cmd %s\n",szCmd);
        m_stStorageInfo.bRepairing = SV_TRUE;
        print_level(SV_INFO, "start repairing sd%d.\n", enStoragePos + 1);
        storage_LOG_OperateDevice("repairStart", enStoragePos, NULL);
        s32Ret = SAFE_System(szCmd, 0);
        m_stStorageInfo.bRepairing = SV_FALSE;
        if (0 == s32Ret)
        {
            print_level(SV_INFO, "no error need to repair in sd%d.\n", enStoragePos + 1);
            m_stStorageInfo.aRepairRes[i] = SINFO_REP_NULL;
            storage_DumpModuleInfo(&m_stStorageInfo);
        }
        else if (s32Ret < 0)
        {
            print_level(SV_ERROR, "cmd: %s to repair sd%d failed.\n", szCmd, enStoragePos + 1);
            storage_LOG_OperateDevice("repairFail", enStoragePos, "dosfsck command failed.");
            bFailed = SV_TRUE;
            m_stStorageInfo.aRepairRes[i] = SINFO_REP_FAILURE;
            storage_DumpModuleInfo(&m_stStorageInfo);
            continue;
        }
        else if (s32Ret != 1)
        {
            print_level(SV_ERROR, "there is error in sd%d, repair done.\n", enStoragePos + 1);
            print_level(SV_INFO, "check to repair sd%d again.\n", enStoragePos + 1);
            bFailed = SV_TRUE;
            m_stStorageInfo.aRepairRes[i] = SINFO_REP_FAILURE;
            storage_DumpModuleInfo(&m_stStorageInfo);
            for (k = 0; k < 3; k++)
            {
                s32Ret = SAFE_System(szCmd, 0);
                if (0 == s32Ret)
                {
                    bFailed = SV_FALSE;
                    print_level(SV_INFO, "check to repair sd%d finish.\n", enStoragePos + 1);
                    break;
                }
                print_level(SV_WARN, "check sd%d %d time still have error.\n", enStoragePos + 1, k+1);
            }

            if (k >=3)
            {
                print_level(SV_ERROR, "check to repair sd%d exceed 3 times.\n", enStoragePos + 1);
            }
        }

        umount("/var/repairing");
        print_level(SV_DEBUG,"szPartionPath %s\n",szPartionPath);
        s32Ret = mount(szPartionPath, "/var/repairing", pszFsType, MS_SYNCHRONOUS, NULL);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "mount %d /var/reparing fail %d\n", enStoragePos, s32Ret);
            storage_LOG_OperateDevice("repairFail", enStoragePos, "fail to mount and partition is not existed after reset device.");
            bFailed = SV_TRUE;
            continue;
        }

        remove("/var/repairing/mounted");
        SAFE_System("rm /var/repairing/*.rec 1>/dev/null 2>/dev/null", NORMAL_WAIT_TIME);
        SAFE_System("rm /var/repairing/*.REC 1>/dev/null 2>/dev/null", NORMAL_WAIT_TIME);
        SAFE_System("rm /var/repairing/*.ren 1>/dev/null 2>/dev/null ", NORMAL_WAIT_TIME);
        SAFE_System("rm /var/repairing/*.REN 1>/dev/null 2>/dev/null", NORMAL_WAIT_TIME);
        SAFE_System("rm /var/repairing/found.* 1>/dev/null 2>/dev/null", NORMAL_WAIT_TIME);
        storage_QuickUnmount("/var/repairing", enStoragePos);
        print_level(SV_INFO, "repair sd%d successful.\n", enStoragePos + 1);
        storage_LOG_OperateDevice("repairFinish", enStoragePos, NULL);

    }

    storage_DumpModuleInfo(&m_stStorageInfo);

    if (bFailed)
    {
        storage_SetErrCode(STORAGE_ERR_CODE_DISK_REPAIR_FAIL);
        return STORAGE_ERR_REPAIR_FAILED;
    }
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 指定修复某个存储设备的分区
 * 输入参数:
 *      enStoragePos --- 设备块存储位置
 *      enFsType     --- Original Format Fs Type
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
             STORAGE_ERR_REPAIR_FAILED - 出现修复失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_RepairPartion(STORAGE_POS_E enStoragePos,SINFO_FS_E enFsType)
{
    sint32 s32Ret = 0, i, j;
    sint32 s32Num = 0;
    SV_BOOL bFailed = SV_FALSE;
    char szCmd[128];
    char szDevPath[64];
    BLK_DEV_INFO_S stBlockStat = {0};
    const char *pszFsType = NULL;

    if (enStoragePos > STORAGE_EXTRA_SD && STORAGE_ALL != enStoragePos)
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d]\n", enStoragePos);
        return SV_FAILURE;
    }

    s32Ret = storage_GetBlockDevStatus(&stBlockStat);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_GetBlockDevStatus failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (STORAGE_ALL == enStoragePos)
    {
        i = 0;
        s32Num = MAX_STORAGE_NUM;
    }
    else
    {
        i = (sint32)enStoragePos;
        s32Num = (sint32)enStoragePos + 1;
    }

    for (; i < s32Num; i++)
    {
        /* TODO:2022/04/07最后位置的设备为U盘 */
        if ((i == (MAX_STORAGE_NUM - 1)))
        {
            i = STORAGE_EXTRA_SD;
        }

        if (SINFO_BLK_RUNNING != stBlockStat.aBlkStat[i])
        {
            continue;
        }

        enStoragePos = (STORAGE_POS_E)i;
        print_level(SV_INFO, "begin reparation sd%d...\n", enStoragePos + 1);
        storage_LOG_OperateDevice("repairStart", enStoragePos, NULL);
        m_stStorageInfo.astDevStatTab[i].enDevStat = SINFO_DEV_REPAIRING_PART;
        m_stStorageInfo.astDevStatTab[i].enMediaMountStat = SINFO_DEV_REPAIRING_PART;
        storage_DumpModuleInfo(&m_stStorageInfo);

        s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "BOARD_GetBlockDevPath pos=%d failed. [err=%#x]\n", enStoragePos, s32Ret);
            storage_LOG_OperateDevice("repairFail", enStoragePos, "can not get device path.");
            bFailed = SV_TRUE;
            continue;
        }

        if (SINFO_FS_FAT32 == enFsType || SINFO_FS_EXT4 == enFsType)
        {
            pszFsType = (SINFO_FS_FAT32 == enFsType) ? "fat" : "ext";
            sprintf(szCmd, "/root/fdisk.sh %s %s >> /dev/null ", szDevPath, pszFsType);
            s32Ret = SAFE_System(szCmd, 30000);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                print_level(SV_INFO, "try to reset sd%d for fdisk...\n", enStoragePos + 1);
                storage_ResetDevice(enStoragePos, szDevPath);
                sprintf(szCmd, "/root/fdisk.sh %s %s >> /dev/null ", szDevPath, pszFsType);
                s32Ret = SAFE_System(szCmd, 30000);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s still failed.\n", szCmd);
                    storage_LOG_OperateDevice("repairFail", enStoragePos, "fdisk command failed.");
                    bFailed = SV_TRUE;
                    continue;
                }
            }
        }
        else
        {
            for (j = 0; j < 2; j++)
            {
                if (0 == j)
                {
                    pszFsType = "ext";
                }
                else
                {
                    pszFsType = "fat";
                }

                print_level(SV_INFO, "try to repair sd%d as %s filesystem.\n", enStoragePos + 1, pszFsType);
                sprintf(szCmd, "/root/fdisk.sh %s %s >> /dev/null ", szDevPath, pszFsType);
                s32Ret = SAFE_System(szCmd, 30000);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                    print_level(SV_INFO, "try to reset sd%d for fdisk...\n", enStoragePos + 1);
                    storage_ResetDevice(enStoragePos, szDevPath);
                    sprintf(szCmd, "/root/fdisk.sh %s %s >> /dev/null ", szDevPath, pszFsType);
                    s32Ret = SAFE_System(szCmd, 30000);
                    if (0 != s32Ret)
                    {
                        print_level(SV_ERROR, "cmd: %s still failed.\n", szCmd);
                        storage_LOG_OperateDevice("repairFail", enStoragePos, "fdisk command failed.");
                        continue;
                    }
                }
                else if (0 == j)
                {
                    enFsType = storage_GetMediaFsType(enStoragePos);
                    if (SINFO_FS_EXT4 == enFsType)
                    {
                        print_level(SV_INFO, "repair sd%d as ext successful.\n", enStoragePos + 1);
                        break;
                    }
                }
                else
                {
                    print_level(SV_INFO, "repair sd%d as fat32 successful.\n", enStoragePos + 1);
                    break;
                }
            }
            if (j >= 2)
            {
                print_level(SV_ERROR, "try repair sd%d as fat32 and ext both failure.\n", enStoragePos + 1);
                bFailed = SV_TRUE;
                continue;
            }
        }

        print_level(SV_INFO, "reparation sd%d successful.\n", enStoragePos + 1);
        storage_LOG_OperateDevice("repairFinish", enStoragePos, NULL);
    }

    storage_DumpModuleInfo(&m_stStorageInfo);
    if (bFailed)
    {
        return STORAGE_ERR_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 指定格式化某个存储设备
 * 输入参数: enStoragePos --- 设备块存储位置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_FormatDevice(STORAGE_POS_E enStoragePos, SINFO_FS_E enMediaFs, SINFO_PARTITION_E enPartition)
{
    sint32 s32Ret = 0, i;
    sint32 s32Num = 0;
    SV_BOOL bFailed = SV_FALSE;
    uint32 u32GB = 0;
    char szCmd[128];
    char szDevPath[64];
    char szPartionPath[64];
    const char *pszExtTool = NULL;
    const char *pszFsType = NULL;
    BLK_DEV_INFO_S stBlockStat = {0};
    SINFO_FS_E curStorageFs;
    SINFO_PARTITION_E needFormatPar;

    if ((enStoragePos > STORAGE_EXTRA_SD && STORAGE_ALL != enStoragePos) \
        || (enMediaFs >= SINFO_FS_BUTT) || (enPartition >= SINFO_PAR_BUTT))
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d, fs=%d, partion=%d]\n", enStoragePos, enMediaFs, enPartition);
        return SV_FAILURE;
    }

    m_stStorageInfo.astDevStatTab[enStoragePos].enDevStat = SINFO_DEV_FORMATTING;
    m_stStorageInfo.astDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_FORMATTING;
    m_stStorageInfo.astDevStatTab[enStoragePos].enMediaFs = SINFO_FS_UNKNOWN;
#if (defined(BOARD_ADA47V1))
    m_stStorageInfo.astDevStatTab[STORAGE_INNER_EMMC].enDevStat = SINFO_DEV_FORMATTING;
    m_stStorageInfo.astDevStatTab[STORAGE_INNER_EMMC].enMediaMountStat = SINFO_DEV_FORMATTING;
    m_stStorageInfo.astDevStatTab[STORAGE_INNER_EMMC].enMediaFs = SINFO_FS_UNKNOWN;
#endif
    storage_DumpModuleInfo(&m_stStorageInfo);

    //寻找相应的块设备开始格式化
    s32Ret = storage_GetBlockDevStatus(&stBlockStat);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "storage_GetBlockDevStatus failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (STORAGE_ALL == enStoragePos)
    {
        i = 0;
        s32Num = MAX_STORAGE_NUM;
    }
    else
    {
        i = (sint32)enStoragePos;
        s32Num = (sint32)enStoragePos + 1;
#if defined(BOARD_ADA47V1)
        if (STORAGE_INNER_EMMC == i)
        {
            enMediaFs = SINFO_FS_EXT4;
        }
#endif
    }

    if (SINFO_FS_EXT4 == enMediaFs)
    {
        pszFsType = "ext";
    }
    else
    {
        pszFsType = "fat";
    }

    for (; i < s32Num; i++)
    {
#if defined(BOARD_ADA47V1)
        if (i == MAX_SD_CARD_NUM)
        {
            i = STORAGE_INNER_EMMC;
        }
#endif
        /* TODO:2022/04/07最后位置的设备为U盘 */
        if ((i == (MAX_STORAGE_NUM - 1)))
        {
            i = STORAGE_EXTRA_SD;
        }

        if (SINFO_BLK_RUNNING != stBlockStat.aBlkStat[i])
        {
            continue;
        }

        if(enMediaFs == SINFO_FS_FAT32 && enPartition == SINFO_PAR_MEDIA)
            needFormatPar = SINFO_PAR_FAT32;

        curStorageFs = storage_GetMediaFsType((STORAGE_POS_E)i);
        if(curStorageFs == SINFO_FS_UNKNOWN || curStorageFs != enMediaFs)
            needFormatPar = SINFO_PAR_ALL;
        else
            needFormatPar = enPartition;

        m_stStorageInfo.astDevStatTab[i].enDevStat = SINFO_DEV_FORMATTING;
        m_stStorageInfo.astDevStatTab[i].enMediaMountStat = SINFO_DEV_FORMATTING;
        m_stStorageInfo.astDevStatTab[i].enMediaFs = enMediaFs;
        storage_DumpModuleInfo(&m_stStorageInfo);

        enStoragePos = (STORAGE_POS_E)i;
        s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "BOARD_GetBlockDevPath pos=%d failed. [err=%#x]\n", enStoragePos, s32Ret);
            storage_LOG_FormatDevice(enStoragePos, 0, "failuer. reason: can not get device path.");
            bFailed = SV_TRUE;
            continue;
        }

        print_level(SV_INFO, "begin format sd%d...\n", enStoragePos + 1);
#if defined(BOARD_ADA47V1)
        if (i == STORAGE_INNER_EMMC)
        {
            enMediaFs = SINFO_FS_EXT4;
            needFormatPar = SINFO_PAR_MEDIA;
            u32GB = 7;
            goto mkfs_ext4;
        }
#endif

        //重新分区条件：当格式所有分区或格式成另一种文件系统
        if(needFormatPar == SINFO_PAR_ALL)
        {
            sprintf(szCmd, "/root/fdisk.sh %s %s >> /dev/null ", szDevPath, pszFsType);
            s32Ret = SAFE_System(szCmd, 30000);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                print_level(SV_INFO, "try to reset sd%d for fdisk...\n", enStoragePos + 1);
                storage_ResetDevice(enStoragePos, szDevPath);
                storage_UpdatePartion(szDevPath);
                sleep(3);
                sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp1" : "%s1", szDevPath);
                sprintf(szCmd, "/root/fdisk.sh %s %s >> /dev/null ", szDevPath, pszFsType);
                print_level(SV_DEBUG, "cmd: %s\n", szCmd);
                s32Ret = SAFE_System(szCmd, 30000);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s still failed.\n", szCmd);
                    storage_LOG_FormatDevice(enStoragePos, 0, "failuer. reason: can not repartition.");
                    bFailed = SV_TRUE;
                    continue;
                }
            }
            else
                storage_UpdatePartion(szDevPath);
        }
        u32GB = (uint32)(storage_BlockCapacity(szDevPath) / (1024 * 1024 * 1024)) + 1;
        sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp1" : "%s1", szDevPath);
        if(access(szPartionPath,F_OK)!=0)
        {
            print_level(SV_ERROR,"%s not exist !!!\n",szPartionPath);
        }
        //格式化 FAT32 分区
        if(needFormatPar == SINFO_PAR_ALL || needFormatPar == SINFO_PAR_FAT32)
        {
            if (SINFO_FS_FAT32 == enMediaFs)
            {
                if (u32GB >= 8)
                {
                    sprintf(szCmd, "mkdosfs -s 128 %s", szPartionPath);
                }
                else if (u32GB >= 4)
                {
                    sprintf(szCmd, "mkdosfs -s 64 %s", szPartionPath);
                }
                else if (u32GB >= 2)
                {
                    sprintf(szCmd, "mkdosfs -s 32 %s", szPartionPath);
                }
                else
                {
                    sprintf(szCmd, "mkdosfs %s", szPartionPath);
                }
            }
            else
            {
                sprintf(szCmd, "mkdosfs %s", szPartionPath);
            }

            SAFE_System("killall -9 mkdosfs", NORMAL_WAIT_TIME);
            print_level(SV_INFO,"%s\n",szCmd);
            s32Ret = SAFE_System(szCmd, 240000);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                storage_LOG_FormatDevice(enStoragePos, u32GB, "failuer. reason: can not format part1 fat32 filesystem.");
                bFailed = SV_TRUE;
                continue;
            }

        }

mkfs_ext4:
        //格式化 EXT 分区
        if(needFormatPar == SINFO_PAR_ALL || needFormatPar == SINFO_PAR_MEDIA)
        {
            if (SINFO_FS_EXT4 == enMediaFs)
            {
                pszExtTool = "mkfs.ext4";
#if defined(BOARD_ADA47V1)
                if (i == STORAGE_INNER_EMMC)
                {
                    sprintf(szPartionPath, "%sp4", szDevPath);
                }
                else
                {
                    sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp2" : "%s2", szDevPath);
                }
#else
                sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp2" : "%s2", szDevPath);
#endif
                sprintf(szCmd, "killall -9 %s", pszExtTool);
                SAFE_System(szCmd, NORMAL_WAIT_TIME);
                sprintf(szCmd, "%s %s -i 102400 -m 0 -b 4096 -j -J size=4 -F", pszExtTool, szPartionPath);
                print_level(SV_INFO,"%s\n",szCmd);
                s32Ret = SAFE_System(szCmd, 2400000);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                    storage_LOG_FormatDevice(enStoragePos, u32GB, "failuer. reason: can not format part2 ext filesystem.");
                    bFailed = SV_TRUE;
                    continue;
                }

                sprintf(szCmd, "tune2fs -i 0 -c 0 -O ^huge_file %s ",  szPartionPath);
                print_level(SV_INFO,"%s\n",szCmd);
                s32Ret = SAFE_System(szCmd, 60000);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                    storage_LOG_FormatDevice(enStoragePos, u32GB, "failuer. reason: tune2fs fail.");
#if (!defined(BOARD_ADA47V1))
                    bFailed = SV_TRUE;
                    continue;
#endif
                }

                sprintf(szCmd, "e2fsck -p %s ",  szPartionPath);
                print_level(SV_INFO,"%s\n",szCmd);
                s32Ret = SAFE_System(szCmd, 60000);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                    storage_LOG_FormatDevice(enStoragePos, u32GB, "failuer. reason: e2fsck fail.");
#if (!defined(BOARD_ADA47V1))
                    bFailed = SV_TRUE;
                    continue;
#endif
                }
            }
        }

        print_level(SV_INFO, "format sd%d [%dGB] successful.\n", enStoragePos + 1, u32GB);
        storage_LOG_FormatDevice(enStoragePos, u32GB, "success.");
    }

    storage_DumpModuleInfo(&m_stStorageInfo);
    if (bFailed)
    {
        return STORAGE_ERR_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 指定挂载某个存储设备
 * 输入参数:
 *             enStoragePos --- 设备块存储位置
 *             enFsType    ---- the type of filesystem
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_MountDevice(STORAGE_POS_E enStoragePos,SINFO_FS_E enFsType)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    char szDevPath[64];
    char szPartionPath[64];
    char szMountFile[64];
    const char *pszMountPath = NULL;
    const char *pszFsType = NULL;
    const char *option = NULL;
    char szCmd[128] = {0};
    SV_BOOL bRechecked = SV_FALSE;

    if (enStoragePos > STORAGE_EXTRA_SD)
    {
        print_level(SV_ERROR, "param is invalid. [pos=%d]\n", enStoragePos);
        return SV_FAILURE;
    }

    print_level(SV_DEBUG,"start mount %d, file sys type %d\n",enStoragePos,enFsType);

recheck:
    //检查文件系统
    if(enFsType == SINFO_FS_FAT32)
        s32Ret = storage_CheckFatDevice(&enStoragePos);
    else if(enFsType == SINFO_FS_EXT4)
        s32Ret = storage_CheckExtDevice(&enStoragePos);
    else
    {
        print_level(SV_ERROR, "type is invalid. [pos=%d]\n", enStoragePos);
        return SV_FAILURE;
    }

    if (STORAGE_ERR_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "check device fail. [pos=%d]\n", enStoragePos);
#if defined(BOARD_ADA47V1)
        if (STORAGE_INNER_EMMC == enStoragePos && 0 != access("/etc/emmc_format", F_OK))  // 程序烧录第1次启动对EMMC分区进行格式化
        {
            STOR_CMD_S stCommand = {0};

            SAFE_ECHO("/etc/emmc_format", "1\n");
            stCommand.bValid = SV_TRUE;
            stCommand.enCmdType = SINFO_CMD_FORMAT_DEVICE;
            stCommand.enStoragePos = STORAGE_INNER_EMMC;
            stCommand.enFsType = SINFO_FS_EXT4;
            stCommand.enPartition = SINFO_PAR_MEDIA;
            STORAGE_FormatDeviceIO(stCommand);
        }
#elif defined(BOARD_DMS31V2)    // 修复文件系统后重新检查一次
        if (SV_FALSE == bRechecked)
        {
            s32Ret = storage_RepairFileSys(enStoragePos,enFsType);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "storman_RepairFileSys pos=%d failed. [err=%#x]\n", enStoragePos, s32Ret);
                return SV_FAILURE;
            }
            bRechecked = SV_TRUE;
            goto recheck;
        }
#endif
        return SV_FAILURE;
    }
    else if (STORAGE_ERR_EXCEPT_UNMOUT == s32Ret)
    {
        s32Ret = storage_RepairFileSys(enStoragePos,enFsType);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "storman_RepairFileSys pos=%d failed. [err=%#x]\n", enStoragePos, s32Ret);
            return SV_FAILURE;
        }
    }

    //挂载文件系统
    print_level(SV_INFO, "try to mount SD%d %d...\n", enStoragePos+1,enFsType);
    s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_GetBlockDevPath failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if(enFsType == SINFO_FS_FAT32)
    {
        pszMountPath = STORAGE_GetMountPath(enStoragePos);
        pszFsType = "vfat";
        sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp1" : "%s1", szDevPath);
    }
    else
    {
        pszMountPath = STORAGE_GetMountExt(enStoragePos);
        pszFsType = "ext4";
        option = "data=writeback,barrier=0,journal_checksum,journal_async_commit,noauto_da_alloc,delalloc";
#if defined(BOARD_ADA47V1)
    if (enStoragePos == STORAGE_INNER_EMMC)
    {
        sprintf(szPartionPath, "%sp4", szDevPath);
    }
    else
    {
        sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp2" : "%s2", szDevPath);
    }
#else
        sprintf(szPartionPath, (NULL != strstr(szDevPath, "mmcblk")) ? "%sp2" : "%s2", szDevPath);
#endif
    }

    print_level(SV_DEBUG,"%s\n",szPartionPath);
    if (NULL == pszMountPath)
    {
        print_level(SV_ERROR, "pszMountPath invalid pos %d\n", enStoragePos);
        return SV_FAILURE;
    }

    s32Ret = mount(szPartionPath, pszMountPath, pszFsType, MS_NOATIME, option);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "mount %s %s failed. [err=%#x]\n", szPartionPath, pszMountPath, errno);
        return SV_FAILURE;
    }


    sprintf(szMountFile, "%s/mounted", pszMountPath);
    s32Fd = open(szMountFile, O_RDWR|O_CREAT);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err=%#x]\n", szMountFile, errno);
        return SV_FAILURE;
    }

    close(s32Fd);
    snprintf(szCmd, 128, "find %s -maxdepth 8 -iname \"FSCK*.*\" -type f | xargs rm -rf &", pszMountPath);
    SAFE_System(szCmd, 0);
    print_level(SV_INFO, "SD%d mount successful!!!\n", enStoragePos+1);
    storage_SetErrCode(0);

#if !USING_RTC_TIME
    /* 更新系统时间 */
    s32Ret = storage_UpdateSysTime(pszMountPath);
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 指定设备进行初始化挂载操作
 * 输入参数: 无
 * 输出参数: paDevStatTab --- 设备挂载状态列表
             pau64DevSize --- 设备容量列表 (Byte)
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_MountInitByPos(STORAGE_POS_E enStoragePos, STOR_STAT_S *pastDevStatTab, uint64 *pau64DevSize)
{
    sint32 s32cmd,s32Ret = 0;
    uint64 u64TotalSize = 0;
    uint64 u64DevSize = 0;
    SINFO_FS_E enFsType;
    BLK_DEV_INFO_S stBlockStat = {0};

    //检查插入情况
    s32Ret = storage_GetBlockDevStatus(&stBlockStat);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_GetBlockDevStatus failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32cmd = 0;

    pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_UNINSERT;
    pastDevStatTab[enStoragePos].enMediaFs = SINFO_FS_UNKNOWN;
    pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_UNINSERT;
    //卡槽检测状态下是否已经挂载
    if(SINFO_BLK_PROBING == stBlockStat.aBlkStat[enStoragePos])
    {
        if(storage_CatMountStatus(enStoragePos,SINFO_FS_FAT32)!=MOUNT_NOMOUNT)
        {
            pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_UNINSERT;
            pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_UNINSERT;
            goto skip_check;
        }
    }
    //没有挂载的情况下
    while(SINFO_BLK_PROBING == stBlockStat.aBlkStat[enStoragePos])
    {
        //获取卡插入情况
        s32Ret = storage_GetBlockDevStatus(&stBlockStat);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "BOARD_GetBlockDevStatus pos[%d] failed. [err=%#x]\n", enStoragePos, s32Ret);
            return SV_FAILURE;
        }
        if(s32cmd>150)
        {
            print_level(SV_WARN,"pos %d probing fail\n",enStoragePos);
            break;
        }
        sleep_ms(100);
        s32cmd++;
    }

    print_level(SV_DEBUG,"storage scan %d \n",enStoragePos);
    if(stBlockStat.aBlkStat[enStoragePos]!=SINFO_BLK_RUNNING)
    {
        if(SINFO_BLK_PROBING == stBlockStat.aBlkStat[enStoragePos])
        {
            pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_EXCEPT;
            pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_EXCEPT;
        }
        else
        {
            pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_UNINSERT;
            pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_UNINSERT;
        }
        goto skip_check;
    }
    else
    {
        pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_ENABLE;
        pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_ENABLE;
    }
    //获取分区容量大小
    u64DevSize = storage_Scan(enStoragePos);
    print_level(SV_DEBUG, "pos[%d] DevSize: %lld\n", enStoragePos, u64DevSize);
    if(u64DevSize==0)
    {
        if(enStoragePos != STORAGE_EXTRA_SD
#if defined(BOARD_ADA47V1)
           && enStoragePos != STORAGE_INNER_EMMC
#endif
          )
        {
            print_level(SV_WARN, "try to repair SD%d through reset\n",enStoragePos);
            storage_ResetDevice(enStoragePos, NULL);
            sleep(2);
            if(0 == storage_Scan(enStoragePos))
            {
                print_level(SV_ERROR,"not medium on %d \n",enStoragePos);
                pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_FORBID;
                pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_FORBID;
                goto skip_check;
            }
        }
        else
        {
            sleep(2);
            u64DevSize = storage_Scan(enStoragePos);
            if(u64DevSize==0)
            {
                print_level(SV_ERROR,"not medium on %d \n",enStoragePos);
                pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_FORBID;
                pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_FORBID;
                goto skip_check;
            }
        }
    }

    //获取文件系统类型
    enFsType = storage_GetMediaFsType(enStoragePos);
    if(enFsType==SINFO_FS_UNKNOWN)
    {
        print_level(SV_ERROR,"pos %d fs type could not recognize\n",enStoragePos);
        pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_FORBID;
        pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_FORBID;
        goto skip_check;
    }

    u64TotalSize += u64DevSize;
    pau64DevSize[enStoragePos] = u64DevSize;
    pastDevStatTab[enStoragePos].enMediaFs = enFsType;
    if(SV_TRUE == m_stStorageInfo.abStorageEnable[enStoragePos])
    {
        storage_InitBlockDevice(enStoragePos,pastDevStatTab[enStoragePos].enMediaFs);
        if(storage_CatMountStatus(enStoragePos,SINFO_FS_FAT32)==MOUNT_SUCCESS)
        {
            print_level(SV_INFO,"storage devices%d have mounted\n",enStoragePos);
            if(pastDevStatTab[enStoragePos].enMediaFs==SINFO_FS_FAT32)
            {
                pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_MOUNTED;
                pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_MOUNTED;
            }
            else
                pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_MOUNTED;
        }
        else
        {
            pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_ENABLE;
        }

        if(pastDevStatTab[enStoragePos].enMediaFs==SINFO_FS_EXT4)
        {
            if(storage_CatMountStatus(enStoragePos,pastDevStatTab[enStoragePos].enMediaFs)==MOUNT_SUCCESS)
            {
                print_level(SV_INFO,"%d media have mounted\n",enStoragePos);
                pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_MOUNTED;
            }
            else
                pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_ENABLE;
        }
    }
    else
    {
        pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_FORBID;
        pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_FORBID;
    }

skip_check:

    storage_DumpModuleInfo(&m_stStorageInfo);

    /* 尝试挂载文件系统 */
    /* 已挂载 */
    if((pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_MOUNTED) \
        &&(pastDevStatTab[enStoragePos].enMediaMountStat==SINFO_DEV_MOUNTED))
        goto skip_finish;

    //设备异常时，卸载该设备并跳过处理
    if((pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_FORBID) \
        ||(pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_UNINSERT) \
        ||(pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_EXCEPT))
    {
        if((s32Ret=storage_CatMountStatus(enStoragePos,SINFO_FS_FAT32)) != MOUNT_NOMOUNT)
        {
            print_level(SV_ERROR,"storage_CatMountStatus cat %d %d\n",enStoragePos,s32Ret);
            storage_UnmountDevice(enStoragePos);
        }
        goto skip_finish;
    }

    //ext文件系统类型异常时，卸载并跳过处理
    if(pastDevStatTab[enStoragePos].enMediaFs==SINFO_FS_EXT4)
    {
        if((pastDevStatTab[enStoragePos].enMediaMountStat==SINFO_DEV_FORBID) \
            ||(pastDevStatTab[enStoragePos].enMediaMountStat==SINFO_DEV_UNINSERT) \
            ||(pastDevStatTab[enStoragePos].enMediaMountStat==SINFO_DEV_EXCEPT))
        {
            if((s32Ret=storage_CatMountStatus(enStoragePos,SINFO_FS_EXT4)) != MOUNT_NOMOUNT)
            {
                print_level(SV_ERROR,"storage_CatMountStatus cat %d %d\n",enStoragePos,s32Ret);
                storage_UnmountDevice(enStoragePos);
            }
            goto skip_finish;
        }
    }

    //开始挂载，如果挂载异常，卸载设备
    print_level(SV_DEBUG,"try mount %d %d %d\n",enStoragePos,pastDevStatTab[enStoragePos].enDevStat,pastDevStatTab[enStoragePos].enMediaFs);
    if(enStoragePos != STORAGE_INNER_EMMC && storage_CatMountStatus(enStoragePos,SINFO_FS_FAT32) == MOUNT_EXCEPT)
    {
        print_level(SV_ERROR,"mount point %d except ... umount it ... \n",enStoragePos);
        storage_UnmountDevice(enStoragePos);
    }

    if(pastDevStatTab[enStoragePos].enMediaFs==SINFO_FS_EXT4)
    {
        if(storage_CatMountStatus(enStoragePos,SINFO_FS_EXT4)==MOUNT_EXCEPT)
        {
            print_level(SV_ERROR,"mount point %d except ... umount it ... \n",enStoragePos);
            storage_UnmountDevice(enStoragePos);
        }
    }

    if(enStoragePos != STORAGE_INNER_EMMC && storage_CatMountStatus(enStoragePos,SINFO_FS_FAT32)==MOUNT_NOMOUNT)
    {
        if(pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_ENABLE)
        {
            if(pastDevStatTab[enStoragePos].enMediaFs==SINFO_FS_FAT32)
            {
                s32Ret = storage_MountDevice(enStoragePos,SINFO_FS_FAT32);
                if(s32Ret==0)
                {
                    pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_MOUNTED;
                    pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_MOUNTED;
                }
                else
                {
                    pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_EXCEPT;
                    pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_EXCEPT;
                }
            }
            else
            {
                s32Ret = storage_MountDevice(enStoragePos,SINFO_FS_FAT32);
                if(s32Ret==0)
                    pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_MOUNTED;
                else
                    pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_EXCEPT;
            }
        }
    }

    if((pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_ENABLE) \
        ||(pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_MOUNTED) \
        ||(pastDevStatTab[enStoragePos].enDevStat==SINFO_DEV_EXCEPT))
    {
        if(pastDevStatTab[enStoragePos].enMediaFs==SINFO_FS_EXT4)
        {
            if(storage_CatMountStatus(enStoragePos,pastDevStatTab[enStoragePos].enMediaFs)==MOUNT_NOMOUNT)
            {
                if(pastDevStatTab[enStoragePos].enMediaMountStat==SINFO_DEV_ENABLE)
                {
                    s32Ret = storage_MountDevice(enStoragePos,pastDevStatTab[enStoragePos].enMediaFs);
                    if(s32Ret==0)
                    {
#if (defined(BOARD_ADA47V1))
                        pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_MOUNTED;
#endif
                        pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_MOUNTED;
                    }
                    else
                    {
#if (defined(BOARD_ADA47V1))
                        pastDevStatTab[enStoragePos].enDevStat = SINFO_DEV_EXCEPT;
#endif
                        pastDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_EXCEPT;
                    }
                }
            }
        }
    }
skip_finish:
    storage_DumpModuleInfo(&m_stStorageInfo);
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 对所有存储设备进行初始化挂载操作
 * 输入参数: 无
 * 输出参数: paDevStatTab --- 设备挂载状态列表
             pau64DevSize --- 设备容量列表 (Byte)
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 storage_MountInit(STOR_STAT_S *pastDevStatTab, uint64 *pau64DevSize)
{
    sint32 s32cmd,s32Ret = 0, i;
    uint64 u64TotalSize = 0;
    uint64 u64DevSize = 0;
    SINFO_FS_E enFsType;
    BLK_DEV_INFO_S stBlockStat = {0};

    if (NULL == pastDevStatTab || NULL == pau64DevSize)
    {
        print_level(SV_ERROR, "param ptr is null.\n");
        return SV_FAILURE;
    }
    umount("/var/mounting");
    umount("/var/repairing");

    //检查插入情况
    s32Ret = storage_GetBlockDevStatus(&stBlockStat);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_GetBlockDevStatus failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32cmd = 0;
    for(i=0; i<MAX_SD_CARD_NUM; i++)
    {
        pastDevStatTab[i].enDevStat = SINFO_DEV_UNINSERT;
        pastDevStatTab[i].enMediaFs = SINFO_FS_UNKNOWN;
        pastDevStatTab[i].enMediaMountStat = SINFO_DEV_UNINSERT;
        //卡槽检测状态下是否已经挂载
        if(SINFO_BLK_PROBING == stBlockStat.aBlkStat[i])
        {
            if(storage_CatMountStatus((STORAGE_POS_E)i,SINFO_FS_FAT32)!=MOUNT_NOMOUNT)
            {
                pastDevStatTab[i].enDevStat = SINFO_DEV_UNINSERT;
                pastDevStatTab[i].enMediaMountStat = SINFO_DEV_UNINSERT;
                continue;
            }
        }
        //没有挂载的情况下
        while(SINFO_BLK_PROBING == stBlockStat.aBlkStat[i])
        {
            //获取卡插入情况
            s32Ret = storage_GetBlockDevStatus(&stBlockStat);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_GetBlockDevStatus pos[%d] failed. [err=%#x]\n", i, s32Ret);
                return SV_FAILURE;
            }
            if(s32cmd>150)
            {
                print_level(SV_WARN,"pos %d probing fail\n",i);
                break;
            }
            sleep_ms(100);
            s32cmd++;
        }

        print_level(SV_DEBUG,"storage scan %d \n",i);
        if(stBlockStat.aBlkStat[i]!=SINFO_BLK_RUNNING)
        {
            if(SINFO_BLK_PROBING == stBlockStat.aBlkStat[i])
            {
                pastDevStatTab[i].enDevStat = SINFO_DEV_EXCEPT;
                pastDevStatTab[i].enMediaMountStat = SINFO_DEV_EXCEPT;
            }
            else
            {
                pastDevStatTab[i].enDevStat = SINFO_DEV_UNINSERT;
                pastDevStatTab[i].enMediaMountStat = SINFO_DEV_UNINSERT;
            }
            continue;
        }
        else
        {
            pastDevStatTab[i].enDevStat = SINFO_DEV_ENABLE;
            pastDevStatTab[i].enMediaMountStat = SINFO_DEV_ENABLE;
        }
        //获取分区容量大小
        u64DevSize = storage_Scan((STORAGE_POS_E)i);
        if(u64DevSize==0)
        {
            if(i != STORAGE_EXTRA_SD
#if defined(BOARD_ADA47V1)
                && i != STORAGE_INNER_EMMC
#endif
            )
            {
                print_level(SV_WARN, "try to repair SD%d through reset\n",i);
                storage_ResetDevice((STORAGE_POS_E)i, NULL);
                sleep(2);
                if(0 == storage_Scan((STORAGE_POS_E)i))
                {
                    print_level(SV_ERROR,"not medium on %d \n",i);
                    pastDevStatTab[i].enDevStat = SINFO_DEV_FORBID;
                    pastDevStatTab[i].enMediaMountStat = SINFO_DEV_FORBID;
                    continue;
                }
            }
            else
            {
                sleep(2);
                u64DevSize = storage_Scan((STORAGE_POS_E)i);
                if(u64DevSize==0)
                {
                    print_level(SV_ERROR,"not medium on %d \n",i);
                    pastDevStatTab[i].enDevStat = SINFO_DEV_FORBID;
                    pastDevStatTab[i].enMediaMountStat = SINFO_DEV_FORBID;
                    continue;
                }
            }
        }

        //获取文件系统类型
        enFsType = storage_GetMediaFsType((STORAGE_POS_E)i);
        if(enFsType==SINFO_FS_UNKNOWN)
        {
            print_level(SV_ERROR,"pos %d fs type could not recognize\n",i);
            pastDevStatTab[i].enDevStat = SINFO_DEV_FORBID;
            pastDevStatTab[i].enMediaMountStat = SINFO_DEV_FORBID;
            continue;
        }

#if defined(BOARD_DMS31V2)
        if(enFsType != SINFO_FS_FAT32)
        {
            storage_SetErrCode(STORAGE_ERR_CODE_NOT_FAT32);
        }
#endif

        u64TotalSize += u64DevSize;
        pau64DevSize[i] = u64DevSize;
        pastDevStatTab[i].enMediaFs = enFsType;
        if(SV_TRUE == m_stStorageInfo.abStorageEnable[i])
        {
            storage_InitBlockDevice((STORAGE_POS_E)i,pastDevStatTab[i].enMediaFs);
            if(storage_CatMountStatus((STORAGE_POS_E)i,SINFO_FS_FAT32)==MOUNT_SUCCESS)
            {
                print_level(SV_INFO,"storage devices%d have mounted\n",i);
                if(pastDevStatTab[i].enMediaFs==SINFO_FS_FAT32)
                {
                    pastDevStatTab[i].enDevStat = SINFO_DEV_MOUNTED;
                    pastDevStatTab[i].enMediaMountStat = SINFO_DEV_MOUNTED;
                }
                else
                    pastDevStatTab[i].enDevStat = SINFO_DEV_MOUNTED;
            }
            else
            {
                pastDevStatTab[i].enDevStat = SINFO_DEV_ENABLE;
            }

            if(pastDevStatTab[i].enMediaFs==SINFO_FS_EXT4)
            {
                if(storage_CatMountStatus((STORAGE_POS_E)i,pastDevStatTab[i].enMediaFs)==MOUNT_SUCCESS)
                {
                    print_level(SV_INFO,"%d media have mounted\n",i);
                    pastDevStatTab[i].enMediaMountStat = SINFO_DEV_MOUNTED;
                }
                else
                    pastDevStatTab[i].enMediaMountStat = SINFO_DEV_ENABLE;
            }
        }
        else
        {
            pastDevStatTab[i].enDevStat = SINFO_DEV_FORBID;
            pastDevStatTab[i].enMediaMountStat = SINFO_DEV_FORBID;
        }
    }

    storage_DumpModuleInfo(&m_stStorageInfo);

    //尝试挂载文件系统
    for (i = 0; i <MAX_SD_CARD_NUM; i++)
    {
        //已挂载
        if((pastDevStatTab[i].enDevStat==SINFO_DEV_MOUNTED) \
            &&(pastDevStatTab[i].enMediaMountStat==SINFO_DEV_MOUNTED))
            continue;

        //设备异常时，卸载该设备并跳过处理
        if((pastDevStatTab[i].enDevStat==SINFO_DEV_FORBID) \
            ||(pastDevStatTab[i].enDevStat==SINFO_DEV_UNINSERT) \
            ||(pastDevStatTab[i].enDevStat==SINFO_DEV_EXCEPT))
        {
            if((s32Ret=storage_CatMountStatus((STORAGE_POS_E)i,SINFO_FS_FAT32)) != MOUNT_NOMOUNT)
            {
                print_level(SV_ERROR,"storage_CatMountStatus cat %d %d\n",i,s32Ret);
                storage_UnmountDevice((STORAGE_POS_E)i);
            }
            continue;
        }

        //ext文件系统类型异常时，卸载并跳过处理
        if(pastDevStatTab[i].enMediaFs==SINFO_FS_EXT4)
        {
            if((pastDevStatTab[i].enMediaMountStat==SINFO_DEV_FORBID) \
                ||(pastDevStatTab[i].enMediaMountStat==SINFO_DEV_UNINSERT) \
                ||(pastDevStatTab[i].enMediaMountStat==SINFO_DEV_EXCEPT))
            {
                if((s32Ret=storage_CatMountStatus((STORAGE_POS_E)i,SINFO_FS_EXT4)) != MOUNT_NOMOUNT)
                {
                    print_level(SV_ERROR,"storage_CatMountStatus cat %d %d\n",i,s32Ret);
                    storage_UnmountDevice((STORAGE_POS_E)i);
                }
                continue;
            }
        }

        //开始挂载，如果挂载异常，卸载设备
        print_level(SV_DEBUG,"try mount %d %d %d\n",(STORAGE_POS_E)i,pastDevStatTab[i].enDevStat,pastDevStatTab[i].enMediaFs);
        if(storage_CatMountStatus((STORAGE_POS_E)i,SINFO_FS_FAT32) == MOUNT_EXCEPT)
        {
            print_level(SV_ERROR,"mount point %d except ... umount it ... \n",i);
            storage_UnmountDevice((STORAGE_POS_E)i);
        }

        if(pastDevStatTab[i].enMediaFs==SINFO_FS_EXT4)
        {
            if(storage_CatMountStatus((STORAGE_POS_E)i,SINFO_FS_EXT4)==MOUNT_EXCEPT)
            {
                print_level(SV_ERROR,"mount point %d except ... umount it ... \n",i);
                storage_UnmountDevice((STORAGE_POS_E)i);
            }
        }

        if(storage_CatMountStatus((STORAGE_POS_E)i,SINFO_FS_FAT32)==MOUNT_NOMOUNT)
        {
            if(pastDevStatTab[i].enDevStat==SINFO_DEV_ENABLE)
            {
                if(pastDevStatTab[i].enMediaFs==SINFO_FS_FAT32)
                {
                    s32Ret = storage_MountDevice((STORAGE_POS_E)i,SINFO_FS_FAT32);
                    if(s32Ret==0)
                    {
                        pastDevStatTab[i].enDevStat = SINFO_DEV_MOUNTED;
                        pastDevStatTab[i].enMediaMountStat = SINFO_DEV_MOUNTED;
                    }
                    else
                    {
                        pastDevStatTab[i].enDevStat = SINFO_DEV_EXCEPT;
                        pastDevStatTab[i].enMediaMountStat = SINFO_DEV_EXCEPT;
                    }
                }
                else
                {
                    s32Ret = storage_MountDevice((STORAGE_POS_E)i,SINFO_FS_FAT32);
                    if(s32Ret==0)
                        pastDevStatTab[i].enDevStat = SINFO_DEV_MOUNTED;
                    else
                        pastDevStatTab[i].enDevStat = SINFO_DEV_EXCEPT;
                }
            }
        }

        if((pastDevStatTab[i].enDevStat==SINFO_DEV_ENABLE) \
            ||(pastDevStatTab[i].enDevStat==SINFO_DEV_MOUNTED) \
            ||(pastDevStatTab[i].enDevStat==SINFO_DEV_EXCEPT))
        {
            if(pastDevStatTab[i].enMediaFs==SINFO_FS_EXT4)
            {
                if(storage_CatMountStatus((STORAGE_POS_E)i,pastDevStatTab[i].enMediaFs)==MOUNT_NOMOUNT)
                {
                    if(pastDevStatTab[i].enMediaMountStat==SINFO_DEV_ENABLE)
                    {
                        s32Ret = storage_MountDevice((STORAGE_POS_E)i,pastDevStatTab[i].enMediaFs);
                        if(s32Ret==0)
                            pastDevStatTab[i].enMediaMountStat = SINFO_DEV_MOUNTED;
                        else
                            pastDevStatTab[i].enMediaMountStat = SINFO_DEV_EXCEPT;
                    }
                }
            }
        }
    }

    storage_DumpModuleInfo(&m_stStorageInfo);
#if defined(BOARD_ADA47V1)
    s32Ret = storage_MountInitByPos(STORAGE_INNER_EMMC, pastDevStatTab, pau64DevSize);
#else
    s32Ret = storage_MountInitByPos(STORAGE_EXTRA_SD, pastDevStatTab, pau64DevSize);
#endif

    return s32Ret;

}

/******************************************************************************
 * 函数功能: 监测电源模块初始化
 * 输入参数: 输入文件描述符
 * 输出参数: 打开的设备描述符
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
SV_BOOL storage_MonitorPowerInit(int *powerfd)
{
    int powerfdTemp = 0;
    SV_BOOL s32Ret = SV_FALSE;
    struct stat sbuf = {0};
    mode_t modes;
    char szCmd[128] = {0};

    powerfdTemp = open("/dev/power", O_RDONLY);
    if(powerfdTemp < 0)
    {
        close(powerfdTemp);
        s32Ret = stat("/dev/power",&sbuf);
        modes = sbuf.st_mode;
        //检测驱动是否存在
        if(S_ISBLK(modes))
        {
            powerfdTemp = open("/dev/power", O_RDONLY);
            if(powerfdTemp < 0)
            {
                print_level(SV_ERROR,"open /dev/power error\n");
                return SV_FALSE;
            }
        }
        else
        {
            //驱动不存在，加载驱动
            sprintf(szCmd, "insmod /root/ko/extdrv/power_detect.ko 1>/dev/null  2>&1");
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if(0 == s32Ret)
            {
                powerfdTemp = open("/dev/power", O_RDONLY);
                if(powerfdTemp < 0)
                {
                    print_level(SV_ERROR,"open /dev/power error\n");
                    return SV_FALSE;
                }
            }
            else
            {
                return SV_FALSE;
            }
        }
    }
    *powerfd = powerfdTemp;
    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: 初始化监控sd卡动态插拔
 * 输入参数: 接收消息的buf和大小
 * 输出参数: 创建的sockfd
 * 返回值  : 初始化套接字结果成功
 * 注意    : 无
 *****************************************************************************/
SV_BOOL storage_MointorSDCardInit(int *sockfd, char *buf, uint32 bufSize)
{
    struct sockaddr_nl sa;
    SV_BOOL bRet = SV_FALSE;
    int sockfdTemp = 0;
    sint32 s32Ret = 0;
    int enable = 1;

    sa.nl_family = AF_NETLINK;
    sa.nl_pad = 0;
    sa.nl_pid = pthread_self();
    sa.nl_groups = NETLINK_KOBJECT_UEVENT;

    //创建socket
    sockfdTemp = socket( PF_NETLINK, SOCK_DGRAM, NETLINK_KOBJECT_UEVENT );
    if(-1 == sockfdTemp)
    {
        print_level(SV_ERROR,"socket creat error\n");
        goto fail_exit;
    }
    //修改socket选项
    setsockopt( sockfdTemp, SOL_SOCKET, SO_RCVBUF, buf, bufSize );
    setsockopt(sockfdTemp, SOL_SOCKET, SO_REUSEADDR,  &enable, sizeof(int));
    //绑定
    s32Ret = bind(sockfdTemp, (struct sockaddr*)&sa, sizeof(sa));
    if(-1 == s32Ret)
    {
        print_level(SV_WARN,"socket bind error, perhaps it is already bound\n");
        goto fail_exit;
    }
    *sockfd = sockfdTemp;
    bRet = SV_TRUE;
fail_exit:
    return bRet;
}

/******************************************************************************
 * 函数功能: 检查块设备是否存在
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  :  SV_TRUE - 存在
           SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL storage_IsSDCardInsert(STORAGE_POS_E enStoragePos)
{
    sint32 s32Ret = -1;
    SV_BOOL bInsert = SV_FALSE;
    char szDevPath[64];

    s32Ret = storage_GetBlockDevPath(enStoragePos, szDevPath);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FALSE;
    }
    bInsert = storage_IsBlockExist(szDevPath);
    return bInsert;
}

/******************************************************************************
 * 函数功能: 获取指音频的时长
 * 输入参数: filename --- 音频路径
 * 输出参数: 无
 * 返回值  : 音频时长(ms)
             NULL - 其它错误
 * 注意    :
 *****************************************************************************/

int storage_Wav_Time3(char *filename)
{
    float flen = 0;
    if (filename != NULL)
    {
        FILE* fp;
        fp = fopen(filename, "rb");
        if (fp != NULL)
        {
            int i;
            int j;

            fseek(fp, 28, SEEK_SET);
            fread(&i, sizeof(4), 1, fp);
            fseek(fp ,0 ,SEEK_END);
            j = ftell(fp) - 44;
            fclose(fp);
            fp = NULL;
            flen = (float)j/(float)i;
        }
    }

    return (int)(flen * 1000);
}


/* 播放音频 */
void storage_PlayAudio(void *pvArg)
{
#if !defined(BOARD_WFCR20S2)
    sint32 s32Ret = 0;
    char szCmd[128] = {0};
	char szFilePath[256] = {0};
    static SV_BOOL bPlaying = SV_FALSE;
    static SV_BOOL bInsertLast = SV_FALSE;
    SV_BOOL bInsert = *((SV_BOOL *)pvArg);

    if (bPlaying && bInsertLast == bInsert)
    {
        return;
    }

    if (COMMON_IsPathExist(ALARM_WELCOME_PLAY_FILE))
    {
        return;
    }

    bPlaying = SV_TRUE;
    bInsertLast = bInsert;
    ALARM_EnableSpk(SV_TRUE);

    memset(szCmd, 0, sizeof(szCmd));
    if (bInsert)
    {
        strncpy(szFilePath, STORAGE_INSERT_SOUND_FILE, 255);
    }
    else
    {
        strncpy(szFilePath, STORAGE_REMOVE_SOUND_FILE, 255);
    }
    sprintf(szCmd, "aplay -Dplug:dmix_vol %s > /dev/null &", szFilePath);
    s32Ret = SAFE_System(szCmd, 30000);

    sleep_ms(storage_Wav_Time3(szFilePath));   /* 休眠等长的时间 */
    bPlaying = SV_FALSE;
    if (bInsertLast == bInsert)
    {
        if (!COMMON_IsAplaying())
        {
            ALARM_EnableSpk(SV_FALSE);
        }
    }
#endif
    return;
}


sint32 storage_AudioAlarm(SV_BOOL bInsert)
{
    sint32 s32Ret = 0;
    pthread_t thread_trigger;
    static SV_BOOL bInsertType = SV_FALSE;
    pthread_attr_t 	attr;
	char szFilePath[256] = {0};

    if (bInsert)
    {
        strncpy(szFilePath, STORAGE_INSERT_SOUND_FILE, 255);
    }
    else
    {
        strncpy(szFilePath, STORAGE_REMOVE_SOUND_FILE, 255);
    }
	if (access(szFilePath, F_OK) != SV_SUCCESS)
	{
		print_level(SV_ERROR, "%s not exit\n", szFilePath);
		return SV_FAILURE;
	}

    bInsertType = bInsert;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    s32Ret = pthread_create(&thread_trigger, &attr, storage_PlayAudio, (void *)&bInsertType);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 存储设备监管执行体
 * 输入参数: pstStormanInfo --- 模块控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void storage_MonitorBody(STORAGE_INFO_S *pstStormanInfo)
{
    sint32 s32Ret = 0, i, s32WaitRecCnt;
    uint32 u32Cnt = 0;
    uint64 u64TotalSize = 0;
    uint64 u64MountSize = 0;
    SV_BOOL mountPointError = SV_FALSE;
    STORAGE_POS_E enStoragePos;
    BLK_DEV_INFO_S stBlockStat = {0};
    STOR_STAT_S astDevStatTmp[STORAGE_MAX_BLK_NUM];
    uint64 au64DevSize[STORAGE_MAX_BLK_NUM] = {0};
    SV_BOOL buMountFail = SV_FALSE;
    uint32 u32PowerValue = 0;

    char sdLostReason[4][128] = {"Sd card detects line status changes",\
                                 "Sd card lost without remove SD card Manually",\
                                 "normal running???",\
                                 "unknown error"};

    pstStormanInfo->enRunStat = SINFO_RUN_MOUNTING;
    pstStormanInfo->bException = SV_FALSE;
    pstStormanInfo->bResetException = SV_FALSE;
    pstStormanInfo->bConfig = SV_FALSE;

    storage_DumpModuleInfo(pstStormanInfo);

remount:
    u64TotalSize =  0;
    u64MountSize =  0;
    while(pstStormanInfo->bResetException)
    {
        sleep(1);
    }

    print_level(SV_INFO,"mount init ... \n");
    s32WaitRecCnt = 5;
    /* 1s等待录像结束 */
    while ((SREC_STAT_RECORDING == pstStormanInfo->enRecordStat)
            && (s32WaitRecCnt > 0))
    {
        sleep_ms(200);
        s32WaitRecCnt--;
    }
    s32Ret = storage_MountInit(pstStormanInfo->astDevStatTab, au64DevSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "storage_MountInit failed. [err=%#x]\n", s32Ret);
        return;
    }
    print_level(SV_INFO,"mount init finish ... \n");
    pstStormanInfo->bException = SV_FALSE;
    for (i = 0; i < STORAGE_MAX_BLK_NUM; i++)
    {
        u64TotalSize += au64DevSize[i];
        u64MountSize += ((pstStormanInfo->astDevStatTab[i].enDevStat == SINFO_DEV_MOUNTED) ? au64DevSize[i] : 0);
    }
    pstStormanInfo->enRunStat = SINFO_RUN_MOUNTED;
    storage_DumpModuleInfo(pstStormanInfo);
    storman_LOG_DeviceStat(pstStormanInfo->astDevStatTab);
    print_level(SV_INFO, "enter block devices watching status.\n");

    if (0 != access("/sdcard/log/uploaded", F_OK))
    {
        CONFIG_FlashProtection(SV_FALSE);
        SAFE_System("mkdir -p /sdcard/log/uploaded", 10000);
        CONFIG_FlashProtection(SV_TRUE);
    }

    //挂载完成，监测设备状态
    while(pstStormanInfo->bEnable)
    {
        u32Cnt++;
        //更新信息
        if (0 == u32Cnt % 5)
        {
            storage_DumpModuleInfo(pstStormanInfo);
        }
        //外部配置
        if (SINFO_CMD_IDLE != pstStormanInfo->stCommand.enCmdType)
        {
            print_level(SV_INFO, "recive operation cmdType=%d.\n", pstStormanInfo->stCommand.enCmdType);
            break;
        }

        if (pstStormanInfo->bConfig)
        {
            print_level(SV_INFO, "recive config.\n");
            break;
        }

        if (pstStormanInfo->bException)
        {
            print_level(SV_INFO, "recive hdd error event.\n");
            break;
        }

        if (NULL != pstStormanInfo->pbPwrOff && *(pstStormanInfo->pbPwrOff))
        {
            print_level(SV_INFO, "exception power off.\n");
            while(SREC_STAT_RECORDING == pstStormanInfo->enRecordStat)
            {
                sleep_ms(5);
            }
            break;
        }

        s32Ret = storage_GetBlockDevStatus(&stBlockStat);
        if(SV_SUCCESS != s32Ret)
        {
            print_level(SV_DEBUG, "storage get block dev status failed [err=%#x]\n", s32Ret);
            continue;
        }
        /* SD卡检测 */
        for(i = 0; i < MAX_SD_CARD_NUM; i++)
        {
            enStoragePos = (STORAGE_POS_E)i;
            STORAGE_GetMediaStorageInfo(enStoragePos, &pstStormanInfo->astFsInfo[enStoragePos]);
#if 0
            print_level(SV_DEBUG,"card:%d aBlkStat:%d enDevStat:%d totalsize:%ld remainsize:%d\n",\
                        i+1,stBlockStat.aBlkStat[i], pstStormanInfo->astDevStatTab[i].enDevStat,\
                        pstStormanInfo->astFsInfo[enStoragePos].totalSize,pstStormanInfo->astFsInfo[enStoragePos].remainSize);
#endif
            if ((SINFO_BLK_RUNNING == stBlockStat.aBlkStat[i]) &&
                (SINFO_DEV_UNINSERT == pstStormanInfo->astDevStatTab[i].enDevStat))
                goto remount;

            mountPointError = SV_FALSE;
            if((SINFO_BLK_RUNNING != stBlockStat.aBlkStat[i]))
            {
                if((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[i].enDevStat)||(SINFO_DEV_MOUNTED==pstStormanInfo->astDevStatTab[i].enMediaMountStat))
                {
                    print_level(SV_ERROR, "sd%d lost.\n", enStoragePos+1);
                    mountPointError = SV_TRUE;
                }
                pstStormanInfo->astDevStatTab[i].enDevStat = SINFO_DEV_UNINSERT;
                pstStormanInfo->astDevStatTab[i].enMediaMountStat = SINFO_DEV_UNINSERT;
            }

            if((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[i].enDevStat)&&(storage_CatMountStatus(enStoragePos, SINFO_FS_FAT32)!=MOUNT_SUCCESS))
            {
                print_level(SV_ERROR, "some error on mount point.%d \n", enStoragePos+1);
                mountPointError = SV_TRUE;
            }

            if(pstStormanInfo->astDevStatTab[i].enMediaFs!=SINFO_FS_FAT32)
            {
                if((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[i].enMediaMountStat)&&(storage_CatMountStatus(enStoragePos, pstStormanInfo->astDevStatTab[i].enMediaFs)!=MOUNT_SUCCESS))
                {
                    print_level(SV_ERROR, "some error on mount point.%d \n", enStoragePos+1);
                    mountPointError = SV_TRUE;
                }
            }

            if (mountPointError)
            {
                pstStormanInfo->astDevStatTab[i].enMediaMountStat = SINFO_DEV_EXCEPT;
                print_level(SV_ERROR, "sd%d lost.\n", enStoragePos+1);
                storage_LOG_OperateDevice("sdLost", enStoragePos, sdLostReason[stBlockStat.aBlkStat[i]]);
                goto remount;
            }
        }

#if defined(BOARD_ADA47V1)
        /* EMMC信息统计 */
        STORAGE_GetMediaStorageInfo(STORAGE_INNER_EMMC, &pstStormanInfo->astFsInfo[STORAGE_INNER_EMMC]);
#endif
        /* U盘检测 */
        enStoragePos = STORAGE_EXTRA_SD;
        STORAGE_GetMediaStorageInfo(enStoragePos, &pstStormanInfo->astFsInfo[enStoragePos]);
#if 0
        print_level(SV_DEBUG,"card:%d aBlkStat:%d enDevStat:%d totalsize:%ld remainsize:%d\n",\
                    enStoragePos,stBlockStat.aBlkStat[enStoragePos], pstStormanInfo->astDevStatTab[enStoragePos].enDevStat,\
                    pstStormanInfo->astFsInfo[enStoragePos].totalSize,pstStormanInfo->astFsInfo[enStoragePos].remainSize);
#endif
        if ((SINFO_BLK_RUNNING == stBlockStat.aBlkStat[enStoragePos]) &&
            (SINFO_DEV_UNINSERT == pstStormanInfo->astDevStatTab[enStoragePos].enDevStat))
            goto remount;

        mountPointError = SV_FALSE;
        if((SINFO_BLK_RUNNING != stBlockStat.aBlkStat[enStoragePos]))
        {
            if((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[enStoragePos].enDevStat)||(SINFO_DEV_MOUNTED==pstStormanInfo->astDevStatTab[enStoragePos].enMediaMountStat))
            {
                print_level(SV_ERROR, "udisk lost.\n");
                mountPointError = SV_TRUE;
            }
            pstStormanInfo->astDevStatTab[enStoragePos].enDevStat = SINFO_DEV_UNINSERT;
            pstStormanInfo->astDevStatTab[enStoragePos].enMediaMountStat = SINFO_DEV_UNINSERT;
        }

        if((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[enStoragePos].enDevStat)&&(storage_CatMountStatus(enStoragePos, SINFO_FS_FAT32)!=MOUNT_SUCCESS))
        {
            print_level(SV_ERROR, "some error on mount point.%d \n", enStoragePos+1);
            mountPointError = SV_TRUE;
        }

        if(pstStormanInfo->astDevStatTab[enStoragePos].enMediaFs!=SINFO_FS_FAT32)
        {
            if((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[enStoragePos].enMediaMountStat)&&(storage_CatMountStatus(enStoragePos, pstStormanInfo->astDevStatTab[enStoragePos].enMediaFs)!=MOUNT_SUCCESS))
            {
                print_level(SV_ERROR, "some error on mount point.%d \n", enStoragePos+1);
                mountPointError = SV_TRUE;
            }
        }

        if (mountPointError)
        {
            print_level(SV_ERROR, "udisk lost.\n");
            storage_LOG_OperateDevice("udiskLost", enStoragePos, sdLostReason[stBlockStat.aBlkStat[enStoragePos]]);
            goto remount;
        }

        for(i=0; i<40; i++)
        {
            if (NULL != pstStormanInfo->pbPwrOff && *(pstStormanInfo->pbPwrOff))
            {
                sleep_ms(5);
                break;
            }
            sleep_ms(5);
        }
    }

    //退出监测
    print_level(SV_INFO, "exit block devices watching status.\n");
    pstStormanInfo->enRunStat = SINFO_RUN_UNMOUNTING;
    storage_DumpModuleInfo(pstStormanInfo);
    memcpy(astDevStatTmp, pstStormanInfo->astDevStatTab, sizeof(astDevStatTmp));
    /* SD卡设备 */
    for (i = 0; i < MAX_SD_CARD_NUM; i++)
    {
        enStoragePos = (STORAGE_POS_E)i;
        if (!pstStormanInfo->bEnable  || (NULL != pstStormanInfo->pbPwrOff && *(pstStormanInfo->pbPwrOff)) ||
            SINFO_CMD_FORMAT_DEVICE == pstStormanInfo->stCommand.enCmdType ||
            SINFO_CMD_REPAIR_PARTION == pstStormanInfo->stCommand.enCmdType ||
            SINFO_CMD_CELAN_BACKUPZONE == pstStormanInfo->stCommand.enCmdType)
        {
            astDevStatTmp[i].enDevStat = SINFO_DEV_FORBID;
            astDevStatTmp[i].enMediaMountStat = SINFO_DEV_FORBID;
        }
        else
        {
            astDevStatTmp[i].enDevStat = (SINFO_DEV_MOUNTED == astDevStatTmp[i].enDevStat) ? SINFO_DEV_ENABLE : astDevStatTmp[i].enDevStat;
            astDevStatTmp[i].enMediaMountStat = (SINFO_DEV_MOUNTED == astDevStatTmp[i].enMediaMountStat) ? SINFO_DEV_ENABLE : astDevStatTmp[i].enMediaMountStat;
            astDevStatTmp[i].enMediaFs = storage_GetMediaFsType(enStoragePos);
        }
    }

#if defined(BOARD_ADA47V1)
    /* EMMC设备 */
    if (!pstStormanInfo->bEnable  || (NULL != pstStormanInfo->pbPwrOff && *(pstStormanInfo->pbPwrOff)) ||
        SINFO_CMD_FORMAT_DEVICE == pstStormanInfo->stCommand.enCmdType ||
        SINFO_CMD_REPAIR_PARTION == pstStormanInfo->stCommand.enCmdType ||
        SINFO_CMD_CELAN_BACKUPZONE == pstStormanInfo->stCommand.enCmdType)
    {
        astDevStatTmp[STORAGE_INNER_EMMC].enDevStat = SINFO_DEV_FORBID;
        astDevStatTmp[STORAGE_INNER_EMMC].enMediaMountStat = SINFO_DEV_FORBID;
    }
    else
    {
        astDevStatTmp[STORAGE_INNER_EMMC].enDevStat = (SINFO_DEV_MOUNTED == astDevStatTmp[STORAGE_INNER_EMMC].enDevStat) ? SINFO_DEV_ENABLE : astDevStatTmp[STORAGE_INNER_EMMC].enDevStat;
        astDevStatTmp[STORAGE_INNER_EMMC].enMediaMountStat = (SINFO_DEV_MOUNTED == astDevStatTmp[STORAGE_INNER_EMMC].enMediaMountStat) ? SINFO_DEV_ENABLE : astDevStatTmp[STORAGE_INNER_EMMC].enMediaMountStat;
        astDevStatTmp[STORAGE_INNER_EMMC].enMediaFs = storage_GetMediaFsType(enStoragePos);
    }
#endif

    /* U盘设备 */
    if (!pstStormanInfo->bEnable  || (NULL != pstStormanInfo->pbPwrOff && *(pstStormanInfo->pbPwrOff)) ||
        SINFO_CMD_FORMAT_DEVICE == pstStormanInfo->stCommand.enCmdType ||
        SINFO_CMD_REPAIR_PARTION == pstStormanInfo->stCommand.enCmdType ||
        SINFO_CMD_CELAN_BACKUPZONE == pstStormanInfo->stCommand.enCmdType)
    {
        astDevStatTmp[STORAGE_EXTRA_SD].enDevStat = SINFO_DEV_FORBID;
        astDevStatTmp[STORAGE_EXTRA_SD].enMediaMountStat = SINFO_DEV_FORBID;
    }
    else
    {
        astDevStatTmp[STORAGE_EXTRA_SD].enDevStat = (SINFO_DEV_MOUNTED == astDevStatTmp[STORAGE_EXTRA_SD].enDevStat) ? SINFO_DEV_ENABLE : astDevStatTmp[STORAGE_EXTRA_SD].enDevStat;
        astDevStatTmp[STORAGE_EXTRA_SD].enMediaMountStat = (SINFO_DEV_MOUNTED == astDevStatTmp[STORAGE_EXTRA_SD].enMediaMountStat) ? SINFO_DEV_ENABLE : astDevStatTmp[STORAGE_EXTRA_SD].enMediaMountStat;
        astDevStatTmp[STORAGE_EXTRA_SD].enMediaFs = storage_GetMediaFsType(enStoragePos);
    }

    for (i=0; i < 600; i++)
    {
        if (SREC_STAT_RECORDING != pstStormanInfo->enRecordStat)
        {
            print_level(SV_DEBUG, "loopRec storage stop success [i=%d]\n", i);
            break;
        }
        sleep_ms(5);
    }

    if (i >= 600)
    {
        print_level(SV_ERROR, "waiting for loopRec stop exceed 3s.\n");
    }

    print_level(SV_INFO, "begin sync.\n");
    sync();
    print_level(SV_INFO, "finish sync.\n");

    /* 卸载SD卡 */
    for (i = 0; i < MAX_SD_CARD_NUM; i++)
    {
        if ((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[i].enDevStat)||(SINFO_DEV_MOUNTED==pstStormanInfo->astDevStatTab[i].enMediaMountStat))
        {
            pstStormanInfo->astDevStatTab[i].enDevStat = astDevStatTmp[i].enDevStat;
            pstStormanInfo->astDevStatTab[i].enMediaMountStat = astDevStatTmp[i].enMediaMountStat;
            enStoragePos = (STORAGE_POS_E)i;
            if(storage_UnmountDevice(enStoragePos)!=SV_SUCCESS)
                buMountFail = SV_TRUE;
            u64MountSize -= au64DevSize[i];
        }
    }

#if defined(BOARD_ADA47V1)
    //卸载EMMC
    if (((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[STORAGE_INNER_EMMC].enDevStat)||(SINFO_DEV_MOUNTED==pstStormanInfo->astDevStatTab[STORAGE_INNER_EMMC].enMediaMountStat)))
    {
        pstStormanInfo->astDevStatTab[STORAGE_INNER_EMMC].enDevStat = SINFO_DEV_FORBID;
        pstStormanInfo->astDevStatTab[STORAGE_INNER_EMMC].enMediaMountStat = SINFO_DEV_FORBID;
        pstStormanInfo->astDevStatTab[STORAGE_INNER_EMMC].enMediaFs = SINFO_FS_UNKNOWN;
        storage_UnmountDevice(STORAGE_INNER_EMMC);
        u64MountSize -= au64DevSize[STORAGE_INNER_EMMC];
    }
#endif

    //卸载U盘
    if (((SINFO_DEV_MOUNTED == pstStormanInfo->astDevStatTab[STORAGE_EXTRA_SD].enDevStat)||(SINFO_DEV_MOUNTED==pstStormanInfo->astDevStatTab[STORAGE_EXTRA_SD].enMediaMountStat)))
    {
        pstStormanInfo->astDevStatTab[STORAGE_EXTRA_SD].enDevStat = SINFO_DEV_FORBID;
        pstStormanInfo->astDevStatTab[STORAGE_EXTRA_SD].enMediaMountStat = SINFO_DEV_FORBID;
        pstStormanInfo->astDevStatTab[STORAGE_EXTRA_SD].enMediaFs = SINFO_FS_UNKNOWN;
        storage_UnmountDevice(STORAGE_EXTRA_SD);
        u64MountSize -= au64DevSize[STORAGE_EXTRA_SD];
    }

    print_level(SV_INFO, "umount all block devices finish.\n");
    pstStormanInfo->enRunStat = SINFO_RUN_UNINIT;
    storage_DumpModuleInfo(pstStormanInfo);

}

/******************************************************************************
 * 函数功能: 存储设备监测线程
 * 输入参数: pvArg --- 传递参数
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * storage_Watch_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    SINFO_FS_E enType;
    STORAGE_INFO_S *pstStorageInfo = (STORAGE_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "storage_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while(pstStorageInfo->bRunning)
    {
        sleep_ms(200);
        storage_DumpModuleInfo(pstStorageInfo);
        if (!pstStorageInfo->bEnable || (NULL != pstStorageInfo->pbPwrOff && *(pstStorageInfo->pbPwrOff)))
        {
            sleep_ms(500);
            continue;
        }

        //处理用户命令
        switch(pstStorageInfo->stCommand.enCmdType)
        {
            case SINFO_CMD_FORMAT_DEVICE:
                s32Ret = storage_FormatDevice(pstStorageInfo->stCommand.enStoragePos, pstStorageInfo->stCommand.enFsType, pstStorageInfo->stCommand.enPartition);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "storage_FormatDevice pos=%d failed. [err=%#x]\n", pstStorageInfo->stCommand.enStoragePos, s32Ret);
                }
                memset(&pstStorageInfo->stCommand, 0x0, sizeof(STOR_CMD_S));
                break;
            case SINFO_CMD_REPAIR_FILESYS:
                s32Ret = storage_RepairFileSys(pstStorageInfo->stCommand.enStoragePos,SINFO_FS_FAT32);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "storage_RepairFileSys pos=%d failed. [err=%#x]\n", pstStorageInfo->stCommand.enStoragePos, s32Ret);
                }
                enType= storage_GetMediaFsType(pstStorageInfo->stCommand.enStoragePos);
                if(enType==SINFO_FS_EXT4)
                {
                    s32Ret = storage_RepairFileSys(pstStorageInfo->stCommand.enStoragePos,enType);
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "storage_RepairFileSys pos=%d failed. [err=%#x]\n", pstStorageInfo->stCommand.enStoragePos, s32Ret);
                    }
                }
                memset(&pstStorageInfo->stCommand, 0x0, sizeof(STOR_CMD_S));
                break;
            case SINFO_CMD_REPAIR_PARTION:
                enType= storage_GetMediaFsType(pstStorageInfo->stCommand.enStoragePos);
                if(enType!=SINFO_FS_UNKNOWN)
                    if(enType!=pstStorageInfo->stCommand.enFsType)
                    {
                        print_level(SV_ERROR,"fs Type not match %d %d !!! \n",enType,pstStorageInfo->stCommand.enFsType);
                        memset(&pstStorageInfo->stCommand, 0x0, sizeof(STOR_CMD_S));
                        break;
                    }
                s32Ret = storage_RepairPartion(pstStorageInfo->stCommand.enStoragePos,pstStorageInfo->stCommand.enFsType);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "storage_RepairPartion pos=%d failed. [err=%#x]\n",pstStorageInfo->stCommand.enStoragePos, s32Ret);
                }
                memset(&pstStorageInfo->stCommand, 0x0, sizeof(STOR_CMD_S));
                break;
            default :
                pstStorageInfo->stCommand.enCmdType = SINFO_CMD_IDLE;
                break;
        }

        storage_DumpModuleInfo(pstStorageInfo);
        storage_MonitorBody(pstStorageInfo);
    }

    return NULL;
}

void * storage_DevCheckBody(void *pvArg)
{
    sint32 s32Ret = 0, i;
    STORAGE_INFO_S *pstStorageInfo = (STORAGE_INFO_S *)pvArg;
    sint32 s32SdCardFd = 0, s32MaxFd = 0;
    char recvBuf[1024] = {0};
    fd_set fdSet = {0};
    struct timeval timeout;
    char *pszFileName = NULL;
    char szCmd[128];
    SV_BOOL bIsPlay = SV_FALSE;

    s32Ret = prctl(PR_SET_NAME, "storage_Restart_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    //初始化sd卡监测套接字
    s32Ret = storage_MointorSDCardInit(&s32SdCardFd, recvBuf,sizeof(recvBuf));
    if(SV_FALSE == s32Ret)
    {
        print_level(SV_WARN,"sd card mointor socket init failed \n");
    }

    for (i=0; i<MAX_SD_CARD_NUM; i++)
    {
        pstStorageInfo->bInsert[i] = storage_IsSDCardInsert(i);
    }

#if defined(BOARD_ADA47V1)
    pstStorageInfo->bInsert[STORAGE_INNER_EMMC] = SV_TRUE;
#endif
    pstStorageInfo->bInsert[STORAGE_EXTRA_SD] = storage_IsSDCardInsert(STORAGE_EXTRA_SD);

    while(pstStorageInfo->bRunning)
    {
        /* 卡热插拔检测 */
        FD_ZERO(&fdSet);
        FD_SET(s32SdCardFd,&fdSet);
        s32MaxFd = s32SdCardFd + 1;
        timeout.tv_sec = 0;
        timeout.tv_usec = 200000;
        s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout);
        if(s32Ret < 0)
        {
            if(errno != EINTR)
            {
                print_level(SV_ERROR, "select failed\n");
            }
        }
        else if(0 == s32Ret)
        {
            //print_level(SV_INFO, "select timeout\n");
            bIsPlay = SV_FALSE;
        }
        else
        {
            if(FD_ISSET(s32SdCardFd,&fdSet))
            {
                recv(s32SdCardFd, &recvBuf, sizeof(recvBuf), 0);
                pszFileName =  strrchr(recvBuf, '/');
#if (defined(BOARD_IPCR20S4) || defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
                if( 0 == memcmp( recvBuf,"add@",4) && 0 == strcmp(pszFileName,STORAGE_BLKDEV_NAME))
                {
                    print_level(SV_WARN,"insert sd card\n");
                    if (!bIsPlay && (pstStorageInfo->bAutoTesting || (pstStorageInfo->bAudioAlarm && BOARD_IsNotCustomer(BOARD_C_DMS31V2_ACR))))
                    {
                        storage_AudioAlarm(SV_TRUE);
                        bIsPlay = SV_TRUE;
                    }
                    pstStorageInfo->bInsert[STORAGE_MAIN_SD1] = SV_TRUE;
                }
                else if( 0 == memcmp( recvBuf,"remove@",7) && 0 == strcmp(pszFileName,STORAGE_BLKDEV_NAME))
                {
                    print_level(SV_WARN,"remove sd card\n");
                    if (!bIsPlay && (pstStorageInfo->bAutoTesting || pstStorageInfo->bAudioAlarm))
                    {
                        storage_AudioAlarm(SV_FALSE);
                        bIsPlay = SV_TRUE;
                    }
                    pstStorageInfo->bInsert[STORAGE_MAIN_SD1] = SV_FALSE;

                    storage_SetErrCode(STORAGE_ERR_CODE_DISK_DROP);
                }
#endif
                /* ACR客户拔插U盘不响 */
                if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_ACR) || pstStorageInfo->bAutoTesting)
                {
                    if( 0 == memcmp( recvBuf,"add@",4) && 0 == strcmp(pszFileName,USB_BLKDEV_NAME))
                    {
                        print_level(SV_WARN,"insert U disk\n");
                        if (!bIsPlay && (pstStorageInfo->bAutoTesting || pstStorageInfo->bAudioAlarm))
                        {
                            storage_AudioAlarm(SV_TRUE);
                            bIsPlay = SV_TRUE;
                        }
                        pstStorageInfo->bInsert[STORAGE_EXTRA_SD] = SV_TRUE;
                    }
                    else if( 0 == memcmp( recvBuf,"remove@",7) && 0 == strcmp(pszFileName,USB_BLKDEV_NAME))
                    {
                        print_level(SV_WARN,"remove U disk\n");
                        if ((pstStorageInfo->bAutoTesting || pstStorageInfo->bAudioAlarm) && !bIsPlay)
                        {
                            storage_AudioAlarm(SV_FALSE);
                            bIsPlay = SV_TRUE;
                        }
                        pstStorageInfo->bInsert[STORAGE_EXTRA_SD] = SV_FALSE;
                    }
                }
            }
        }
    }

    return NULL;
}

void * storage_SdAlarm_Body(void *pvArg)
{
	sint16 u16SumTime;
    sint32 s32Ret = 0, i;
	struct timeval startTime = {0}, endTime = {0};
    STORAGE_INFO_S *pstStorageInfo = (STORAGE_INFO_S *)pvArg;
    SV_BOOL bLedOff = SV_TRUE;
    uint8 u8PinValue = 0;

    while (pstStorageInfo->bRunning)
	{
        if (BOARD_IsCustomer(BOARD_C_DMS31V2_ACR))
        {
            sleep_ms(1000);
            if (!pstStorageInfo->bAudioAlarm)
                continue;

            if (!pstStorageInfo->bInsert[STORAGE_MAIN_SD1])
            {
            	gettimeofday(&endTime, NULL);
            	u16SumTime = (endTime.tv_sec - startTime.tv_sec) * 1000 + (endTime.tv_usec - startTime.tv_usec) / 1000;
                if (u16SumTime >= 14000)
            	{
                    storage_AudioAlarm(SV_FALSE);
                    u16SumTime = 0;
            		gettimeofday(&startTime, NULL);
                }
            }
            else
            {
                gettimeofday(&startTime, NULL);
            	gettimeofday(&endTime, NULL);
            }
        }
        else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202116))
        {
            if (!pstStorageInfo->bLedAlarm)
            {
                if (!bLedOff)
                {
                    bLedOff = SV_TRUE;
                    u8PinValue = 0;
                    BOARD_RK_SetGPIO(STORAGE_LED_BAND, STORAGE_LED_PIN, u8PinValue);
                }
                sleep_ms(500);
                continue;
            }

            if (!pstStorageInfo->bInsert[STORAGE_MAIN_SD1])
            {
                bLedOff = SV_FALSE;
                u8PinValue = !u8PinValue;
                BOARD_RK_SetGPIO(STORAGE_LED_BAND, STORAGE_LED_PIN, u8PinValue);
                sleep_ms(500);
            }
            else
            {
                if (!bLedOff)
                {
                    bLedOff = SV_TRUE;
                    u8PinValue = 0;
                    BOARD_RK_SetGPIO(STORAGE_LED_BAND, STORAGE_LED_PIN, u8PinValue);
                }
                sleep_ms(1000);
            }
        }
    }
}

/******************************************************************************
 * 函数功能: 模块初始化
 * 输入参数: pstSysParam --- 配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 STORAGE_Init(CFG_SYS_PARAM *pstSysParam)
{

#if defined(BOARD_WFCR20S2)
	return SV_SUCCESS;
#endif

    sint32 i = 0;
    if (NULL == pstSysParam)
    {
        return ERR_NULL_PTR;
    }
    memset(&m_stStorageInfo, 0, sizeof(STORAGE_INFO_S));
    m_stStorageInfo.bEnable = pstSysParam->bEnableStorage;
    m_stStorageInfo.bAudioAlarm = pstSysParam->bEnableSDAlarm;
    m_stStorageInfo.bLedAlarm = pstSysParam->bEnableSDLedAlarm;
    m_stStorageInfo.s32UTChour = pstSysParam->s32UTChour;
    m_stStorageInfo.s32UTCminute = pstSysParam->s32UTCminute;
    if (0 == access(AUTOTEST_IP, F_OK))
    {
		m_stStorageInfo.bAutoTesting = SV_TRUE;
        m_stStorageInfo.bEnable = SV_TRUE;
    }
	else
	{
		m_stStorageInfo.bAutoTesting = SV_FALSE;
	}

    if(SV_TRUE == m_stStorageInfo.bEnable)
    {
        for(i=0; i<MAX_SD_CARD_NUM; i++)
        {
            m_stStorageInfo.abStorageEnable[i] = SV_TRUE;
        }
#if defined(BOARD_ADA47V1)
        m_stStorageInfo.abStorageEnable[STORAGE_INNER_EMMC] = SV_TRUE;
#endif
        m_stStorageInfo.abStorageEnable[STORAGE_EXTRA_SD] = SV_TRUE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 模块去初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 STORAGE_Fini()
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 STORAGE_Start()
{
#if defined(BOARD_WFCR20S2)
		return SV_SUCCESS;
#endif
    sint32 s32Ret = 0;
    pthread_t thread;
    pthread_attr_t attr;
    struct sched_param param;
    m_stStorageInfo.bRunning = SV_TRUE;

    /* 设置线程优先级和时间片实时调度策略 */
    s32Ret = pthread_attr_init(&attr);
    param.sched_priority = 50;
    pthread_attr_setschedpolicy(&attr,SCHED_RR);
    pthread_attr_setschedparam(&attr,&param);
    pthread_attr_setinheritsched(&attr,PTHREAD_EXPLICIT_SCHED);
    s32Ret = storage_CheckDir();
    s32Ret = pthread_create(&thread, NULL, storage_Watch_Body, &m_stStorageInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    m_stStorageInfo.u32Tid = thread;

    s32Ret = pthread_create(&thread, NULL, storage_DevCheckBody, &m_stStorageInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    m_stStorageInfo.s32DevCheckTid = thread;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_ACR) || BOARD_IsCustomer(BOARD_C_DMS31V2_202116))
    {
        s32Ret = pthread_create(&thread, NULL, storage_SdAlarm_Body, &m_stStorageInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
            return ERR_SYS_NOTREADY;
        }
        m_stStorageInfo.s32SdAlarmTid = thread;
    }
    pthread_attr_destroy(&attr);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 STORAGE_Stop(SV_BOOL bJoin)
{
#if defined(BOARD_WFCR20S2)
		return SV_SUCCESS;
#endif
    sint32 s32Ret = 0;
    pthread_t thread = m_stStorageInfo.u32Tid;
    void *pvRetval = NULL;

    m_stStorageInfo.bRunning = SV_FALSE;
    if (!bJoin)
    {
        goto exit;
    }

    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    thread = m_stStorageInfo.s32DevCheckTid;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_ACR))
    {
        thread = m_stStorageInfo.s32SdAlarmTid;
        s32Ret = pthread_join(thread, &pvRetval);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
            return SV_FAILURE;
        }
    }
#if (!(defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) \
    || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) \
    || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4)))
    thread = m_stStorageInfo.s32PowerTid;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif

exit:
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 存储配置
 * 输入参数: pstSysParam -- 配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 STORAGE_SetConfig(CFG_SYS_PARAM *pstSysParam)
{
    if (NULL == pstSysParam)
    {
        return ERR_NULL_PTR;
    }

    m_stStorageInfo.bEnable = pstSysParam->bEnableStorage;
    m_stStorageInfo.bAudioAlarm = pstSysParam->bEnableSDAlarm;
    m_stStorageInfo.bLedAlarm = pstSysParam->bEnableSDLedAlarm;
    if (m_stStorageInfo.bAutoTesting)
    {
        m_stStorageInfo.bEnable = SV_TRUE;
    }

    if(SV_TRUE == m_stStorageInfo.bEnable)
    {
#if (defined(BOARD_IPCR20S4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) \
    || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_HDW845V1)\
    || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32V4))
        m_stStorageInfo.abStorageEnable[STORAGE_MAIN_SD1] = SV_TRUE;
        m_stStorageInfo.abStorageEnable[STORAGE_EXTRA_SD] = SV_TRUE;
#elif (defined(BOARD_ADA47V1))
        m_stStorageInfo.abStorageEnable[STORAGE_MAIN_SD1] = SV_TRUE;
        m_stStorageInfo.abStorageEnable[STORAGE_INNER_EMMC] = SV_TRUE;
        m_stStorageInfo.abStorageEnable[STORAGE_EXTRA_SD] = SV_TRUE;
#else
        return ERR_NOT_SURPPORT;
#endif
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取存储状态信息(对外)
 * 输入参数: 存储信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 STORAGE_GetState(SINFO_OUT_E *pstSOut)
{
    sint32 i;
    SV_BOOL bInsert = SV_FALSE;
    SV_BOOL bException = SV_TRUE;
    SV_BOOL bFull = SV_FALSE;
    long totalSize = 0;
    long remainSize = 0;

    /* 卡插入状态 */
    for (i=0; i<MAX_SD_CARD_NUM; i++)
    {
       bInsert =  m_stStorageInfo.bInsert[i];
       if (SV_TRUE == bInsert)
       {
            break;
       }
    }
    if (SV_FALSE == bInsert)
    {
        bInsert = m_stStorageInfo.bInsert[STORAGE_EXTRA_SD];
    }
#if defined(BOARD_ADA47V1)
    if (SV_FALSE == bInsert)
    {
        bInsert = m_stStorageInfo.bInsert[STORAGE_INNER_EMMC];
    }
#endif
    /* 异常状态 */
    for (i=0; i<MAX_SD_CARD_NUM; i++)
    {
       bException =  (SINFO_DEV_MOUNTED == m_stStorageInfo.astDevStatTab[i].enDevStat) ? SV_FALSE : SV_TRUE;
       if (SV_FALSE == bException)
       {
            break;
       }
    }
    if (SV_TRUE == bException)
    {
        bException = (SINFO_DEV_MOUNTED == m_stStorageInfo.astDevStatTab[STORAGE_EXTRA_SD].enDevStat) ? SV_FALSE : SV_TRUE;
    }
#if defined(BOARD_ADA47V1)
    if (SV_TRUE == bException)
    {
        bException = (SINFO_DEV_MOUNTED == m_stStorageInfo.astDevStatTab[STORAGE_INNER_EMMC].enDevStat) ? SV_FALSE : SV_TRUE;
    }
#endif
    /* 容量百分比 */
    for (i=0; i<MAX_SD_CARD_NUM; i++)
    {
        totalSize += m_stStorageInfo.astFsInfo[i].totalSize;
        remainSize += m_stStorageInfo.astFsInfo[i].remainSize;
    }
#if defined(BOARD_ADA47V1)
    totalSize += m_stStorageInfo.astFsInfo[STORAGE_INNER_EMMC].totalSize;
    remainSize += m_stStorageInfo.astFsInfo[STORAGE_INNER_EMMC].remainSize;
#endif
    totalSize += m_stStorageInfo.astFsInfo[STORAGE_EXTRA_SD].totalSize;
    remainSize += m_stStorageInfo.astFsInfo[STORAGE_EXTRA_SD].remainSize;

    pstSOut->bInsert    = bInsert;
    pstSOut->bException = bException;
    pstSOut->bFull      = ((SV_FALSE == bException) && (0 != remainSize) && (remainSize <= STO_REMAIN_SIZE) ? SV_TRUE : SV_FALSE);
    pstSOut->bEnable    = m_stStorageInfo.bEnable;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取设备数据大小
 * 输入参数: pos -- 设备位置
 * 输出参数: pstInfo -- 容量信息
 * 返回值  : 获取是否成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 STORAGE_GetMediaStorageInfo(STORAGE_POS_E pos, SFS_INFO_S *pstInfo)
{
    sint32 s32Ret = 0;
    const char* storageMountPath;
    unsigned long long  usedBytes = 0;
    unsigned long long freeBytes = 0;
    unsigned long long totalBytes = 0;
    unsigned long long availBytes = 0;
    SINFO_FS_E eFileSysType = SINFO_FS_UNKNOWN;
    struct statvfs stStatvfs = {0};

    if(pstInfo == NULL)
    {
        print_level(SV_ERROR,"info == NULL\n");
        return SV_FAILURE;
    }

    eFileSysType = storage_GetMediaFsType(pos);
    if(SINFO_FS_FAT32 == eFileSysType)
    {
        storageMountPath = STORAGE_GetMountPath(pos);
        if(storageMountPath == NULL)
        {
            pstInfo->remainSize     = 0;
            pstInfo->totalSize        = 0;
            print_level(SV_ERROR,"card not mount!!\n");
            return SV_FAILURE;
        }

        /* 获取容量 */
        s32Ret = statvfs((storageMountPath), &stStatvfs);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR,"statvfs failed for path->[%s]\n", storageMountPath);
            pstInfo->remainSize = 0;
            pstInfo->totalSize = 0;
            return SV_FAILURE;
        }
        totalBytes = stStatvfs.f_blocks * stStatvfs.f_bsize;
        freeBytes = stStatvfs.f_bfree * stStatvfs.f_bsize;
        usedBytes = (stStatvfs.f_blocks - stStatvfs.f_bavail) * stStatvfs.f_bsize;
        availBytes = stStatvfs.f_bavail * stStatvfs.f_bsize;

        pstInfo->remainSize = freeBytes/1024/1024;
        pstInfo->totalSize    = totalBytes/1024/1024;
        if(pstInfo->totalSize == 0)
        {
            print_level(SV_WARN,"storage pos %s  have  not mounted or meet other problem \n",storageMountPath);
            return SV_FAILURE;
        }
    }
    else if(SINFO_FS_EXT4 == eFileSysType)
    {
        storageMountPath = STORAGE_GetMountPath(pos);
        if(storageMountPath == NULL)
        {
            pstInfo->remainSize     = 0;
            pstInfo->totalSize        = 0;
            print_level(SV_ERROR,"card not mount!!\n");
            return SV_FAILURE;
        }

        /* 获取容量 */
        s32Ret = statvfs((storageMountPath), &stStatvfs);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR,"statvfs failed for path->[%s]\n", storageMountPath);
            pstInfo->remainSize = 0;
            pstInfo->totalSize = 0;
            return SV_FAILURE;
        }
        totalBytes = stStatvfs.f_blocks * stStatvfs.f_bsize;
        freeBytes = stStatvfs.f_bfree * stStatvfs.f_bsize;
        usedBytes = (stStatvfs.f_blocks - stStatvfs.f_bavail) * stStatvfs.f_bsize;
        availBytes = stStatvfs.f_bavail * stStatvfs.f_bsize;

        storageMountPath = STORAGE_GetMountExt(pos);
        if(storageMountPath == NULL)
        {
            pstInfo->remainSize     = 0;
            pstInfo->totalSize        = 0;
            print_level(SV_ERROR,"card not mount!!\n");
            return SV_FAILURE;
        }

        /* 获取容量 */
        s32Ret = statvfs((storageMountPath), &stStatvfs);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR,"statvfs failed for path->[%s]\n", storageMountPath);
            pstInfo->remainSize = 0;
            pstInfo->totalSize = 0;
            return SV_FAILURE;
        }
        totalBytes = stStatvfs.f_blocks * stStatvfs.f_bsize;
        freeBytes = stStatvfs.f_bfree * stStatvfs.f_bsize;
        usedBytes = (stStatvfs.f_blocks - stStatvfs.f_bavail) * stStatvfs.f_bsize;
        availBytes = stStatvfs.f_bavail * stStatvfs.f_bsize;

        pstInfo->remainSize = freeBytes/1024/1024;
        pstInfo->totalSize    = totalBytes/1024/1024;
        if(pstInfo->totalSize == 0)
        {
            print_level(SV_WARN,"storage pos %s  have  not mounted or meet other problem \n",storageMountPath);
            return SV_FAILURE;
        }
    }
    else
    {
        pstInfo->remainSize     = 0;
        pstInfo->totalSize        = 0;
        //print_level(SV_ERROR, "unknown file sys type \n");
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 当前存储设备是否可写入
 * 输入参数: pos  -- 设备位置
 * 输出参数: 无
 * 返回值  : 是否可写入
 * 注意    :
 *****************************************************************************/
SV_BOOL STORAGE_IsDeviceWritable(STORAGE_POS_E pos)
{
    sint32 i,s32Cnt;
    SV_BOOL bIsWritable = SV_FALSE;

    if ((NULL != m_stStorageInfo.pbPwrOff && SV_TRUE == *(m_stStorageInfo.pbPwrOff)) \
        || SV_TRUE == m_stStorageInfo.bException \
        || SINFO_RUN_MOUNTED != m_stStorageInfo.enRunStat)
    {
        return SV_FALSE;
    }

    if(pos == STORAGE_NONE)
        return SV_FALSE;

    if (STORAGE_MAIN_ALL == pos )
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD - 1;
    }
    else if( STORAGE_ALL == pos)
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD ;
    }
    else
    {
        i = pos;
        s32Cnt = pos;
    }

    //检查全部卡时，只要有一张不可读写就返回不可读
    for (; i <= s32Cnt; i++)
    {
        if(SINFO_DEV_MOUNTED == m_stStorageInfo.astDevStatTab[i].enDevStat)
        {
            bIsWritable = SV_TRUE;
        }
        else
        {
            bIsWritable = SV_FALSE;
            break;
        }
    }

    return bIsWritable;
}

/******************************************************************************
 * 函数功能: 当前存储文件系统是否可写入
 * 输入参数: pos  -- 设备位置
 * 输出参数: 无
 * 返回值  : 是否可写入
 * 注意    :
 *****************************************************************************/
SV_BOOL STORAGE_IsMeidaWritable(STORAGE_POS_E pos)
{
    sint32 i,s32Cnt;
    SV_BOOL bIsWritable = SV_FALSE;

    if((pos == STORAGE_NONE) || (pos >= STORAGE_UNKNOWN))
    {
        return SV_FALSE;
    }

    if ((NULL != m_stStorageInfo.pbPwrOff && SV_TRUE == *(m_stStorageInfo.pbPwrOff)) \
        || SV_TRUE == m_stStorageInfo.bException \
        || SINFO_RUN_MOUNTED != m_stStorageInfo.enRunStat
        || SINFO_CMD_IDLE != m_stStorageInfo.stCommand.enCmdType)
    {
        return SV_FALSE;
    }

    if (STORAGE_MAIN_ALL == pos )
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD - 1;
    }
    else if( STORAGE_ALL == pos)
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD ;
    }
    else
    {
        i = pos;
        s32Cnt = pos;
    }

    //检查全部卡时，只要有一张不可读写就返回不可读
    for (; i <= s32Cnt; i++)
    {
        if ((SINFO_DEV_MOUNTED == m_stStorageInfo.astDevStatTab[i].enMediaMountStat)
            &&(SV_TRUE == m_stStorageInfo.bInsert[i]))
        {
            bIsWritable = SV_TRUE;
        }
        else
        {
            bIsWritable = SV_FALSE;
            break;
        }
    }

    return bIsWritable;
}

/******************************************************************************
 * 函数功能: 当前存储设备挂载
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 是否可写入
 * 注意    :   比STORAGE_IsWritable慢，掉电不会立刻返回
 *****************************************************************************/
SV_BOOL STORAGE_IsMount(STORAGE_POS_E pos)
{
    sint32 i,s32Cnt;
    SV_BOOL bIsMount = SV_FALSE;

    if((pos == STORAGE_NONE) || (pos >= STORAGE_UNKNOWN))
    {
        return SV_FALSE;
    }

    if(SINFO_RUN_MOUNTED != m_stStorageInfo.enRunStat)
    {
        return SV_FALSE;
    }

    if (STORAGE_MAIN_ALL == pos )
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD - 1;
    }
    else if( STORAGE_ALL == pos)
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD ;
    }
    else
    {
        i = pos;
        s32Cnt = pos;
    }

    for (; i <= s32Cnt; i++)
    {
        if ((SINFO_DEV_MOUNTED == m_stStorageInfo.astDevStatTab[i].enMediaMountStat))
        {
            bIsMount = SV_TRUE;
            break;
        }
        else
        {
            bIsMount = SV_FALSE;
        }
    }
    return bIsMount;

}


/******************************************************************************
 * 函数功能: 当前存储设备是否可写入
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 是否可写入
 * 注意    :   无
 *****************************************************************************/
SV_BOOL STORAGE_IsWritable(STORAGE_POS_E pos)
{
    return STORAGE_IsMeidaWritable(pos);
}

/******************************************************************************
 * 函数功能: 存储设备锁，挂载下防止卸载
 * 输入参数: pos -- 位置
 * 输出参数: 无
 * 返回值  : 文件描述符
 * 注意    :   无
 *****************************************************************************/
int STORAGE_Lock(STORAGE_POS_E pos)
{
    char mountedFilePath[PATH_LEN];
    const char* mountedPath;
    memset(mountedFilePath,0,PATH_LEN);
    if(pos==STORAGE_MEMORY)
        return 0;
    mountedPath = STORAGE_GetMountPath(pos);
    if(mountedPath==NULL)
        return -1;
    sprintf(mountedFilePath,"%s/mounted",mountedPath);
    return open(mountedFilePath,O_RDONLY);
}

/******************************************************************************
 * 函数功能: 解锁
 * 输入参数: fd -- 文件描述符
 * 输出参数: 无
 * 返回值  : 无
 * 注意    :   无
 *****************************************************************************/
void STORAGE_UnLock(int fd)
{
    if(fd > 0)
        close(fd);
}

/******************************************************************************
 * 函数功能: 获取挂载状态FAT32
 * 输入参数: pos -- 设备位置
 * 输出参数: 无
 * 返回值  : SV_TRUE -- 已挂载
              SV_FALSE -- 未挂载
 * 注意    :   无
 *****************************************************************************/
SV_BOOL STORAGE_IsStorageMounted(STORAGE_POS_E pos)
{
    char mountedFilePath[PATH_LEN];
    char szProcMount[4096];
    char szSearchString[64];
    const char* mountedPath;

    memset(mountedFilePath,0,PATH_LEN);
    mountedPath = STORAGE_GetMountPath(pos);
    if(mountedPath==NULL)
        return SV_FALSE;
    sprintf(mountedFilePath,"%s/mounted",mountedPath);
    if(access(mountedFilePath,F_OK)!=0)
        return SV_FALSE;
    if(SAFE_CAT("/proc/mounts",szProcMount,4096)!=0)
    {
        print_level(SV_ERROR,"could not open /proc/mounts\n");
        return SV_FALSE;
    }
    sprintf(szSearchString,"%s ",mountedPath);
    if(strstr(szProcMount,szSearchString)==NULL)
        return SV_FALSE;


    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: 获取挂载状态EXT
 * 输入参数: pos -- 设备位置
 * 输出参数: 无
 * 返回值  : SV_TRUE -- 已挂载
              SV_FALSE -- 未挂载
 * 注意    :   无
 *****************************************************************************/
SV_BOOL STORAGE_IsMediaStorageMounted(STORAGE_POS_E pos)
{
    char mountedFilePath[PATH_LEN];
    char szProcMount[4096];
    char szSearchString[64];
    const char* mountedPath;

    memset(mountedFilePath,0,PATH_LEN);
    mountedPath = STORAGE_GetMountExt(pos);
    if(mountedPath==NULL)
        return SV_FALSE;
    sprintf(mountedFilePath,"%s/mounted",mountedPath);
    if(access(mountedFilePath,F_OK)!=0)
        return SV_FALSE;
    if(SAFE_CAT("/proc/mounts",szProcMount,4096)!=0)
    {
        print_level(SV_ERROR,"could not open /proc/mounts\n");
        return SV_FALSE;
    }
    sprintf(szSearchString,"%s ",mountedPath);
    if(strstr(szProcMount,szSearchString)==NULL)
        return SV_FALSE;

    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: 获取设备卡是否挂载2025最新
 * 输入参数: pos -- 设备位置
 * 输出参数: 无
 * 返回值  : SV_TRUE -- 已挂载
              SV_FALSE -- 未挂载
 * 注意    :   无
 *****************************************************************************/
SV_BOOL STORAGE_IsExternalMounted(STORAGE_POS_E pos)
{
    sint32 s32Ret = 0;
    sint32 i = 0;
    sint32 s32Cnt = 0;

    if((pos == STORAGE_NONE) || (pos >= STORAGE_UNKNOWN))
    {
        return SV_FALSE;
    }
    /* 传入全部存储器，依次遍历SD卡1 --> U盘的挂载目录 */
    if (STORAGE_ALL == pos)
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD;
        for (; i <= s32Cnt; i++)
        {
            if (STORAGE_IsStorageMounted(i))
            {
                /* 只要有一个还是挂载，就退出循环并记录TRUE */
                print_level(SV_INFO, "STORAGE IS MOUNT\n");
                s32Ret = SV_TRUE;
                break;
            }
            else
            {
                print_level(SV_INFO, "STORAGE IS NOT MOUNT\n");
                s32Ret = SV_FALSE;
            }
        }
    }
    else
    {
        s32Ret = STORAGE_IsStorageMounted(pos);
        return s32Ret;
    }
}

/******************************************************************************
 * 函数功能: 获取文件系统类型
 * 输入参数: pos -- 设备位置
 * 输出参数: 无
 * 返回值  : 文件系统类型
 * 注意    :   无
 *****************************************************************************/
SINFO_FS_E STORAGE_MediaFilesystemType(STORAGE_POS_E pos)
{
    return storage_GetMediaFsType(pos);
}

/******************************************************************************
 * 函数功能: 获取挂载路径
 * 输入参数: pos --- 设备位置
 * 输出参数: 无
 * 返回值  : 路径字符串或者NULL
 *****************************************************************************/
const char* STORAGE_GetMediaPath(STORAGE_POS_E pos)
{
    SINFO_FS_E enFsType;

    if(pos > MOUNT_PATH_NUM)
        return NULL;

    if(pos == STORAGE_MEMORY)
        return mountPathArray[pos];

    enFsType = STORAGE_MediaFilesystemType(pos);
    if (SINFO_FS_FAT32 == enFsType)
    {
        return mountPathArray[pos];
    }
    else if (SINFO_FS_EXT4 == enFsType)
    {
        return mountMediaPath[pos];
    }
    else
    {
        print_level(SV_WARN, "STORAGE_MediaFilesystemType unknown filesystem pos=%d type=%d.\n", pos, enFsType);
        return NULL;
    }
}

/******************************************************************************
 * 函数功能: 格式化SD卡
 * 输入参数: stCommand -- 命令
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败

 * 注意    :   无
 *****************************************************************************/
sint32 STORAGE_FormatDeviceIO(STOR_CMD_S stCommand)
{
    stCommand.enCmdType = SINFO_CMD_FORMAT_DEVICE;
    memcpy(&m_stStorageInfo.stCommand, &stCommand, sizeof(STOR_CMD_S));
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 修复分区
 * 输入参数: stCommand -- 命令
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败

 * 注意    :   无
 *****************************************************************************/
sint32 STORAGE_RepairPartionIO(STOR_CMD_S stCommand)
{
    stCommand.enCmdType = SINFO_CMD_REPAIR_PARTION;
    memcpy(&m_stStorageInfo.stCommand, &stCommand, sizeof(STOR_CMD_S));
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 修复文件系统
 * 输入参数: stCommand -- 命令
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败

 * 注意    :   无
 *****************************************************************************/
sint32 STORAGE_RepairFileSysIO(STOR_CMD_S stCommand)
{
    stCommand.enCmdType = SINFO_CMD_REPAIR_FILESYS;
    memcpy(&m_stStorageInfo.stCommand, &stCommand, sizeof(STOR_CMD_S));
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置录像状态
 * 输入参数: 无
 * 输出参数: 存储模块录像状态
 * 返回值  : 状态
 * 注意    : 无
 *****************************************************************************/
sint32 STORAGE_SetRecordStat(SREC_STAT_E enRecordStat)
{
    m_stStorageInfo.enRecordStat = enRecordStat;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取录像分区的剩余容量
 * 输入参数: bReflash -- 是否刷新容量 path -- 录像分区挂载路径
 * 输出参数: 无
 * 返回值  : 录像剩余容量
 * 注意    : 无
 *****************************************************************************/
uint32 STORAGE_GetRemainSize(STORAGE_POS_E pos, SV_BOOL bRefresh)
{
    sint32 i,s32Cnt;
    sint32 s32Ret = -1;
    uint32 u32TotalSize = 0, u32RemainSize = 0;
    if(pos == STORAGE_NONE)
        return 0;

    if (STORAGE_MAIN_ALL == pos )
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD - 1;
    }
    else if( STORAGE_ALL == pos)
    {
        i = STORAGE_MAIN_SD1;
        s32Cnt = STORAGE_EXTRA_SD ;
    }
    else
    {
        i = pos;
        s32Cnt = pos;
    }

    for (; i <= s32Cnt; i++)
    {
        if(bRefresh)
        {
            s32Ret = STORAGE_GetMediaStorageInfo(pos,&(m_stStorageInfo.astFsInfo[pos]));
            if(SV_FAILURE == s32Ret)
            {
                continue;
            }
        }

        u32RemainSize += m_stStorageInfo.astFsInfo[i].remainSize;
        u32TotalSize += m_stStorageInfo.astFsInfo[i].totalSize;
    }
    return u32RemainSize;

}

/******************************************************************************
 * 函数功能: 注册掉电标志
 * 输入参数: pbPowerFlag -- 掉电标志指针
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 将模块的掉电指针指向control的掉电标志，只读
 *****************************************************************************/
sint32 STORAGE_RegisterPowerFlag(const SV_BOOL *pbPowerFlag)
{
    if (NULL == pbPowerFlag)
    {
        return ERR_NULL_PTR;
    }
    m_stStorageInfo.pbPwrOff = pbPowerFlag;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 注册错误码
 * 输入参数: pu32ErrCode -- 错误码
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 将模块的掉电指针指向control的掉电标志，只读
 *****************************************************************************/
sint32 STORAGE_RegisterErrCode(const uint32 *pu2ErrCode)
{
    if (NULL == pu2ErrCode)
    {
        return ERR_NULL_PTR;
    }
    m_stStorageInfo.pu32ErrCode = pu2ErrCode;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 注册掉电标志
 * 输入参数: pbPowerFlag -- 掉电标志指针
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 将模块的掉电指针指向control的掉电标志，只读
 *****************************************************************************/
sint32 STORAGE_GetRepairStat(SV_BOOL *pbRepair)
{
    if (NULL == pbRepair)
    {
        return ERR_NULL_PTR;
    }
    *pbRepair = m_stStorageInfo.bRepairing;
    return SV_SUCCESS;
}


