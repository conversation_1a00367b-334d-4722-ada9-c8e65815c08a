/******************************************************************************
Copyright (C) 2020-2022 广州敏视数码科技有限公司版权所有.

文件名：gps.c

作者: lyn       版本: v1.0.0(初始版本号)   日期: 2021-08-17

文件功能描述: 定义GPS管理功能,从外接GPS模块或4G模块获取GPS数据

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <errno.h>
#include <time.h>
#include <fcntl.h>
#include <termios.h>
#include <sys/stat.h>


#include "../../../include/board.h"
#include "safefunc.h"
#include "cJSON.h"
#include "op.h"
#include "msg.h"
#include "cellular.h"
#include "gps.h"
#include "include/nmea/tok.h"
#include "include/nmea/parse.h"
#include "include/nmea/sentence.h"
#include "fence.h"
#include "print.h"
#include "alarm.h"
#include "utils.h"


#define min(x, y) (((x) < (y))? (x) : (y))

#if (defined(BOARD_DMS31V2))
#define GPS_SERIAL_PORT        "/dev/ttyS4"              /* 内置GPS模块 */
#else
#define GPS_SERIAL_PORT        "/dev/ttyS4"              /* 默认GPS串口 */
#endif

#define GPS_RS232_PORT         "/dev/ttyS0"              /* rs232外接GPS模块 */
#define GPS_EC25_PORT          "/dev/ttyUSB1"            /* ec25模块GPS功能端口 */
#define GPS_SIM7600_PORT       "/dev/ttyUSB1"            /* sim7600模块GPS功能端口 */
#define GPS_GEMALTO_PORT       "/dev/ttyACM2"            /* 金雅拓模块GPS功能端口 */

#if (defined(BOARD_DMS31V2))
#define EC25_GPS_PORT_PATH     "/sys/devices/platform/ffe00000.usb/usb1/1-1/1-1.2/1-1.2:1.1"
#define SIM7600_GPS_PORT_PATH  "/sys/devices/platform/ffe00000.usb/usb1/1-1/1-1.2/1-1.2:1.1"

#else
#endif

/* 4G模块厂商ID */
#define VID_GEMATO          "0061"
#define VID_SERRIA          "1199"
#define VID_QUECTEL         "05c6"
#define VID_HUAWEI          "12d1"
#define VID_TELIT           "0036"
#define VID_TELIT910_C1     "1201"
#define VID_YIYUAN          "2c7c"
#define VID_FIBOCOM         "1508:1001"

/* 4G模块厂商和设备ID */
#define VPID_HUAWEI_MU709S_6         "12d1:1c25"
#define VPID_HUAWEI_ME909S_120       "12d1:15c1"
#define VPID_YIYUAN_EC25             "2c7c:0125"
#define VPID_EC200T                  "2c7c:6026"
#define VPID_EC25                    "2c7c:0125"
#define VPID_SIM7600                 "1e0e:9001"
#define VPID_EC200U                  "2c7c:0901"

#define DATA_ERR_CNT                 3

typedef struct tag_GPSv_s
{
    char satelliteId;                      /* 1~32 PRN */
    char used;                             /* 0 not used; 1 used */
    char snr;                              /* 0~99 dBHz */
    char elevation;                        /* 0~99 degrees */
    short azimuth;                         /* 0~359 degrees */
} __attribute__((packed))GPSSv_S;

typedef struct tag_GPSSvInfo_s
{
    char sat_visible;                      /* 0~24 */
    GPSSv_S stGPSSv[GPS_VIS_MAX_NUM];
} __attribute__((packed))GPS_SVINFO_S;

typedef struct tag_DVRGPSData_S
{
    sint32 status;                  /* 状态 */
    sint32 signal;                  /* 信号值[0,3] */
    sint32 satinuse;                /* 使用的卫星数 */
    double latitude;                /* 纬度 */
    double longitude;               /* 经度 */
    double elv;                     /* 天线高度,相对地平线，对应altitude，海拔高度 */
    double spk;                     /* 速度(km/h) */
}DVR_GPS_DATA_S;

/* GPS 模块控制信息 */
typedef struct tag_GPSInfo_S
{
    SV_BOOL                    bEnable;             /* 是否使能GPS模块 */
    SV_BOOL                    bConfig;             /* 是否需要配置参数 */
    SV_BOOL                    bException;          /* gps是否监测到异常 */
    SV_BOOL                    bSendData;           /* 是否发送数据给UI界面 */
    SV_BOOL                    bIsUseExtendGPS;     /* 是否使用外部GPS设备 */
    SV_BOOL                    bTimeSync;           /* 时间同步标志 */
    SV_BOOL                    bRecvData;           /* 是否接收到数据 */
    SV_BOOL                    bUseVTGSpeed;        /* 是否使用地面速度信息中的速度信息，兼容没有该信息的模块 */
    sint32                     s32GPSDevFd;         /* 设备fd */
    CELLULAR_MODULE_E          eModuleType;         /* 4G模块类型 */
    nmeaGPRMC*                 pstGPRMC;            /* 推荐定位信息 */
    nmeaGPGSA*                 pstGPGSA;            /* 当前卫星信息 */
    nmeaGPGGA*                 pstGPGGA;            /* 定位信息 */
    nmeaGPGSV*                 pstGPGSV;            /* 可见卫星信息 */
    nmeaGPVTG*                 pstGPVTG;            /* 地面速度信息    */
    GPATT*                     pstGPATT;            /* SKM-4DU私有协议，包含惯导信息*/
    GPS_CONFIG_S               stGPSConfig;         /* 用户配置参数 */
    GPS_SRC_E                  eLastGPSSrc;         /* 记录过去gps数据源 */
    GPS_DATA_S                 stGPSData;           /* GPS数据 */
    GPS_VIR_INFO_S             stGPSVirInfo;        /* GPS 虚拟设备信息 */
    DVR_GPS_DATA_S             stDvrGPSData;        /* DVR发送过来的数据 */
    pthread_mutex_t            mutexLock;           /* 参数设置线程互斥锁 */
    SV_BOOL                    bRunning;            /* 线程运行状态 */
    sint32                     s32DvrRecvCnt;       /* DVR数据源运行计数器，在DVR超过规定时间没数据更新时切换数据源 */
    sint32                     s32MainLifeCnt;      /* 主线程运行计数器，避免突然掉电导致GPS read阻塞 */
    uint32                     u32Tid;              /* 线程ID */
    pthread_t                  pMonitorTid;         /* 监控线程id */
}GPS_INFO_S;

GPS_INFO_S m_stGPSInfo = {0};

sint32 file_size2(char* filename)
{
    struct stat statbuf;
    stat(filename,&statbuf);
    int size = statbuf.st_size;

    return size;
}

/******************************************************************************
 * 函数功能: 根据设备文件查找对应端口号
 * 输入参数: u32Size -- 输出数据长度 path -- 端口文件路径
 * 输出参数: port -- 返回端口
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_SearchDev(char *port, const char *path, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[128] = {0};
    char szCmd[128] = {0};
    char *pcTmp = NULL;

    sprintf(szCmd, "ls %s | grep tty", path);
    s32Ret = SAFE_System_Recv(szCmd, szBuf, sizeof(szBuf));

    /* 去换行符 */
    pcTmp = strchr(szBuf, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szBuf, '\n');

    if(NULL != pcTmp)
        *pcTmp = '\0';

    if((SV_SUCCESS == s32Ret) && (strstr(szBuf, "ttyUSB") != NULL))
    {
        snprintf(port, u32Size, "/dev/%s", szBuf);
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}


/******************************************************************************
 * 函数功能: 外接GPS通过串口传输数据
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_SerialOpen(sint32 *ps32Fd, const char* pcPort, int Baudrate)
{
    uint8 i = 0;
    sint32 s32Ret = -1;
    sint32 s32Fd = -1;
    struct termios stTermAttr;

    if(NULL == ps32Fd || NULL== pcPort)
    {
        return SV_FAILURE;
    }

    //打开端口
    for(i=0; i<5; i++)
    {
        s32Fd = open(pcPort, O_RDWR|O_NOCTTY);
        if(s32Fd > 0)
        {
            *ps32Fd = s32Fd;
            break;
        }

        if(4 == i)
        {
            *ps32Fd = -1;
            return SV_FAILURE;
        }

        sleep_ms(1000);
    }

    //获取端口属性
    tcgetattr(s32Fd, &stTermAttr);
    bzero(&stTermAttr, sizeof(stTermAttr));
    //设置端口属性
    switch(Baudrate)
    {
        case 9600:
            s32Ret = cfsetispeed(&stTermAttr, B9600);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }

            s32Ret = cfsetospeed(&stTermAttr, B9600);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }
            break;

        case 19200:
            s32Ret = cfsetispeed(&stTermAttr, B19200);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }

            s32Ret = cfsetospeed(&stTermAttr, B19200);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }
            break;

        case 38400:
            s32Ret = cfsetispeed(&stTermAttr, B38400);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }

            s32Ret = cfsetospeed(&stTermAttr, B38400);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }
            break;

        case 115200:
            s32Ret = cfsetispeed(&stTermAttr, B115200);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }

            s32Ret = cfsetospeed(&stTermAttr, B115200);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }
            break;
        default:
            s32Ret = cfsetispeed(&stTermAttr, B115200);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }

            s32Ret = cfsetospeed(&stTermAttr, B115200);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "cfsetispeed failed. [err=%#x]\n", errno);
            }
            break;
    }

    stTermAttr.c_cflag |= CLOCAL | CREAD;
    stTermAttr.c_cflag &= ~CSIZE;
    stTermAttr.c_cflag |= CS8;
    stTermAttr.c_cflag &= ~PARENB;
    stTermAttr.c_cflag &= ~CSTOPB;
    stTermAttr.c_cflag &= ~CRTSCTS;
    stTermAttr.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    stTermAttr.c_oflag &= ~OPOST;
    s32Ret = tcflush(s32Fd, TCIFLUSH);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "tcflush failed. [err=%#x]\n", errno);
    }

    stTermAttr.c_cc[VTIME] = 255;
    stTermAttr.c_cc[VMIN] = 0;
    s32Ret = tcsetattr(s32Fd, TCSANOW, &stTermAttr);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "tcsetattr failed. [err=%#x]\n", errno);
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 关闭串口
 * 输入参数: s32Fd -- 串口文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_SerialClose(sint32 s32Fd)
{
    sint32 s32Ret = -1;
    tcflush(s32Fd, TCIOFLUSH);
    s32Ret = close(s32Fd);
    if(s32Ret < 0)
    {
        print_level(SV_ERROR, "close serial error [err=%#x]\n", errno);
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读取串口数据长度，循环读取防止一次没读完
 * 输入参数: s32Fd -- 文件描述符 pc -- 数据地址 pResult -- 返回的结果
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_SerialReadRaw(sint32 s32Fd, char *pc, uint32 u32Len, uint32 *pResult)
{
    sint32 s32Context = 0;
    uint32 u32Processed = 0;
    time_t start = time(NULL);
    if(NULL == pc || NULL == pResult)
    {
        return SV_FAILURE;
    }

    while(u32Processed < u32Len)
    {
        s32Context = read(s32Fd, (pc+u32Processed), (u32Len - u32Processed));
        if(-1 == s32Context)
        {
#if (!defined(BOARD_ADA32V2) && !defined(BOARD_ADA32V3) && !defined(BOARD_ADA32IR) && !defined(BOARD_ADA47V1))
            //print_level(SV_ERROR, "read byte error %s\n", strerror(errno));
#endif
            sleep_ms(100);
            break;
        }
        else
        {
            u32Processed += s32Context;
        }

        if(difftime(time(NULL), start) > 1)
        {
            break;
        }
    }

    *pResult = u32Processed;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取字符
 * 输入参数: s32Fd -- 文件描述符 pc -- 数据地址
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_SerialGetChar(sint32 s32Fd, char* pc)
{
    sint32 s32Ret = -1;
    uint32 u32Result = 0;
    uint8 i = 2;
    if(NULL == pc)
    {
        return SV_FAILURE;
    }

    while(i>1)
    {
        s32Ret = gps_SerialReadRaw(s32Fd, pc, 1, &u32Result);
        if(1 == u32Result && SV_SUCCESS == s32Ret)
        {
            break;
        }
        i--;
        sleep_ms(500);
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: gps数据单位转换
 * 输入参数: dSrc -- 输入数据 dResult -- 输出数据存储区
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_NDeg2Degree(double dSrc , double *dResult)
{
    if(NULL == dResult)
    {
        return SV_FAILURE;
    }
    double deg = ((sint32)(dSrc / 100));
    dSrc = deg + (dSrc - deg * 100) / 60;

    *dResult = dSrc;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读取GPS数据
 * 输入参数: s32Fd -- 文件描述符 szBuf -- 数据缓存区 u32Size -- 缓冲区大小 pStartPos -- 起始位置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_GetData(sint32 s32Fd, char* szBuf, uint32 u32Size, sint32 *pStartPos)
{
    char c;
    uint8 u8Count = 0;
    sint32 s32Pos = *pStartPos;
    time_t tStart = time(NULL);
    time_t tEnd = 0;
    double dTimeDiff = 0;
    if(NULL == szBuf)
    {
        return SV_FAILURE;
    }

    do
    {
        gps_SerialGetChar(s32Fd, &c);
        if(c != '\r' && c != '\n')
        {
            if(s32Pos >= u32Size)
            {
                s32Pos = 0;
                memcpy(szBuf+(*pStartPos), szBuf+s32Pos, u32Size-(*pStartPos));
                s32Pos = u32Size-(*pStartPos);
                *pStartPos = 0;
            }
            *(szBuf+s32Pos) = c;
            s32Pos++;
        }

        if((u8Count++) > 3)
        {
            usleep(1);
            u8Count = 0;
        }
        tEnd= time(NULL);
        dTimeDiff = tEnd - tStart;
        if (dTimeDiff > 3)
        {
            return SV_FAILURE;
        }

    }while(c != '\n');

    if(*pStartPos != s32Pos)
    {
        *(szBuf+s32Pos) = '\0';
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: GPS从DVR获取数据
 * 输入参数: pstMsgPkt -- 消息包 pstRetPkt -- 回包
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
static sint32 gps_GetDvrGpsData(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    GPS_DATA_S *pstGpsData = (GPS_DATA_S *)pstMsgPkt->pu8Data;

    m_stGPSInfo.stDvrGPSData.status = pstGpsData->status;
    m_stGPSInfo.stDvrGPSData.signal = pstGpsData->signal;
    m_stGPSInfo.stDvrGPSData.satinuse = pstGpsData->satinuse;
    m_stGPSInfo.stDvrGPSData.latitude = pstGpsData->latitude;
    m_stGPSInfo.stDvrGPSData.longitude = pstGpsData->longitude;
    m_stGPSInfo.stDvrGPSData.elv = pstGpsData->elv;
    m_stGPSInfo.stDvrGPSData.spk = pstGpsData->spk;

    m_stGPSInfo.stGPSData.status = pstGpsData->status;
    m_stGPSInfo.stGPSData.signal = pstGpsData->signal;
    m_stGPSInfo.stGPSData.satinuse = pstGpsData->satinuse;
    m_stGPSInfo.stGPSData.spk = pstGpsData->spk;
    if(BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        m_stGPSInfo.stGPSData.spk = pstGpsData->spk / 1.6093;   // kmh转为mph单位
    }

    m_stGPSInfo.stGPSData.latitude = pstGpsData->latitude;
    m_stGPSInfo.stGPSData.longitude = pstGpsData->longitude;
    m_stGPSInfo.stGPSData.elv = pstGpsData->elv;
    m_stGPSInfo.stGPSData.bHadData = SV_TRUE;
    m_stGPSInfo.stGPSConfig.eGPSSrc = GPS_SRC_DVR;
    m_stGPSInfo.bRecvData = SV_TRUE;
    m_stGPSInfo.s32DvrRecvCnt = 0;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 配置gps输入数据源
 * 输入参数: eGPSSrc -- 数据源
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_ConfigSrc(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    GPS_SRC_E eGPSSrc = (GPS_SRC_E)pstMsgPkt->stMsg.s32Param;

    if (eGPSSrc >= GPS_SRC_BUTT)
    {
        return ERR_NULL_PTR;
    }
    if(SV_TRUE == m_stGPSInfo.bException)
    {
        return ERR_SYS_NOTREADY;
    }
    m_stGPSInfo.stGPSConfig.eGPSSrc = eGPSSrc;
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: GPS设置系统时间
 * 输入参数: 推荐定位信息指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_SetTime(nmeaGPRMC *pstGPRMC)
{
    sint32 s32Ret = -1;
    struct tm stGPSTime = {0};
    time_t tGPSSec = -1;
    sint32 s32TzHour = m_stGPSInfo.stGPSConfig.s32TzHour;
    sint32 s32TzMin = m_stGPSInfo.stGPSConfig.s32TzMin;
    char szCmd[64] = {0};
    struct timeval tvNow;
    struct timezone tz;
    struct timeval tvTime;
    sint32 s32UTCminute = 0;

    if(NULL == pstGPRMC)
    {
        return SV_FAILURE;
    }
    print_level(SV_DEBUG, "UTC time: \"%d-%d-%d %d:%d:%d\"\n",pstGPRMC->utc.year+2000, pstGPRMC->utc.mon, pstGPRMC->utc.day, pstGPRMC->utc.hour, pstGPRMC->utc.min, pstGPRMC->utc.sec);
    print_level(SV_DEBUG, "s32TzHour=%d, s32TzMin=%d.\n", s32TzHour, s32TzMin);

    //设置时间
    stGPSTime.tm_year = pstGPRMC->utc.year + 2000 - 1900; //GPS + 2000,struct tm -1900
    stGPSTime.tm_mon = pstGPRMC->utc.mon - 1;
    stGPSTime.tm_mday = pstGPRMC->utc.day;
    stGPSTime.tm_hour = pstGPRMC->utc.hour;
    stGPSTime.tm_min = pstGPRMC->utc.min;
    stGPSTime.tm_sec = pstGPRMC->utc.sec;
    //转换为至1970 1月1日以来秒数
    tGPSSec = mktime(&stGPSTime);

    //设置系统时间
    s32Ret = stime(&tGPSSec);
    if(s32Ret <0)
    {
        print_level(SV_ERROR, "set system time error %s\n", strerror(errno));
        return SV_FAILURE;
    }

    if (gettimeofday(&tvTime, &tz) < 0)//UTC时间
    {
        print_level(SV_ERROR, "gettimeofday failed.\n");
		return SV_FAILURE;
    }
    else
    {
        s32UTCminute = (m_stGPSInfo.stGPSConfig.s32TzHour > 0) ? m_stGPSInfo.stGPSConfig.s32TzMin : -m_stGPSInfo.stGPSConfig.s32TzMin;
        tz.tz_minuteswest = m_stGPSInfo.stGPSConfig.s32TzHour*60 + s32UTCminute;

        print_level(SV_INFO, "tv_sec:%d, tz_minuteswest:%d\n",tvTime.tv_sec, tz.tz_minuteswest);
        if (settimeofday(&tvTime, &tz) < 0)//UTC时间
        {
            print_level(SV_ERROR, "settimeofday failed.\n");
            return SV_FAILURE;
        }
    }

    //更新硬件时间
    sprintf(szCmd, "hwclock -w 1>/dev/null  2>&1");
    s32Ret = SAFE_System(szCmd, 10000);
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        SAFE_System(szCmd, 10000);
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

//TODO: 2021/08/19GPS发送数据给ui尚未实现
/******************************************************************************
 * 函数功能: 通过GPS发送数据给UI界面
 * 输入参数: pstData -- GPS数据
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_SendData(GPS_DATA_S *pstData)
{
    sint32 s32Ret;
    MSG_PACKET_S stMsgPkt = {0};

    if (NULL ==  pstData)
    {
        print_level(SV_ERROR, "null pointer.\n");
        return SV_FAILURE;
    }

    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    stMsgPkt.stMsg.u16OpCode = OP_EVENT_GPS_DATA;
    stMsgPkt.pu8Data = (uint8 *)pstData;
    stMsgPkt.u32Size = sizeof(GPS_DATA_S);
    s32Ret = Msg_submitEvent(EP_ALG, OP_EVENT_GPS_DATA, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查4G模块是否存在
 * 输入参数: 无
 * 输出参数:
 * 返回值  : SV_SUCCESS - 成功
 * 修改时间: 2022-3-15
 * 注意    : DMS31的GPS数据源优先级为 DVR > 4G > 内置gps
           ADA32的GPS数据源只有mcu
 *****************************************************************************/
sint32 gps_IsCellExist(SV_BOOL *bIsCellExist)
{
    sint32 s32RetryTimes = 3;
    sint32 i = 0;
    sint8 szBuf[512]={0};

    *bIsCellExist = SV_FALSE;

    for (i = 0; i < s32RetryTimes; i++)
    {
        SAFE_System_Recv("lsusb", szBuf, 512);

        if (NULL != strstr(szBuf, VPID_YIYUAN_EC25) || NULL != strstr(szBuf, VPID_EC200T)
            || NULL != strstr(szBuf, VPID_EC25) || NULL != strstr(szBuf, VPID_SIM7600)
            || NULL != strstr(szBuf, VPID_EC200U)
            )
        {
            *bIsCellExist = SV_TRUE;
            break;
        }
    }

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 超过规定时间接受不到数据，或者连续3次初始化失败，切换当前GPS数据源
 * 输入参数: pstGPSInfo -- 主模块参数
 * 输出参数:
 * 返回值  : SV_SUCCESS - 成功
 * 修改时间: 2022-3-15
 * 注意    : DMS31的GPS数据源优先级为 DVR > 4G > 内置gps
           ADA32的GPS数据源只有mcu
 *****************************************************************************/
sint32 gps_ChangeGPSScr(GPS_INFO_S *pstGPSInfo)
{
    SV_BOOL bIsCellExist = SV_FALSE;
#if (defined(BOARD_DMS31V2))
    gps_IsCellExist(&bIsCellExist);

    if (GPS_SRC_232 == pstGPSInfo->stGPSConfig.eGPSSrc && bIsCellExist)
    {
        pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_4G;
    }
    else if (GPS_SRC_232 == pstGPSInfo->stGPSConfig.eGPSSrc && !bIsCellExist)
    {
        pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_SERIAL;
    }
    else if (GPS_SRC_DVR == pstGPSInfo->stGPSConfig.eGPSSrc && bIsCellExist)
    {
        pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_4G;
    }
    else if (GPS_SRC_DVR == pstGPSInfo->stGPSConfig.eGPSSrc && !bIsCellExist)
    {
        pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_SERIAL;
    }
    else if (GPS_SRC_SERIAL == pstGPSInfo->stGPSConfig.eGPSSrc && bIsCellExist)
    {
        pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_4G;
    }
    else
    {
        pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_SERIAL;
    }
#elif (defined(BOARD_ADA32V2)|| defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA47V1))
    pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_MCU;
#else
    pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_4G;
#endif

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 打印gps接收到的数据
 * 输入参数:        s32Cnt -- 第几次
                s32Num -- 每几次打印一次
                ps8RecvData -- 数据指针
 * 输出参数:
 * 返回值  : SV_SUCCESS - 成功
 * 修改时间: 2022-5-18
 *****************************************************************************/
sint32 gps_PrintRecvData(sint32 s32Cnt, sint32 s32Num, sint8 *ps8RecvData)
{
    sint32 s32Mod = -1;

    if (NULL == ps8RecvData)
    {
        print_level(SV_ERROR, "gps_PrintRecvData uses NULL.\n", ps8RecvData);
        return SV_FAILURE;
    }

    s32Mod = s32Cnt % s32Num;
    if (0 == s32Mod)
    {
        print_level(SV_INFO, "gps data:%s\n", ps8RecvData);
    }

    return SV_SUCCESS;
}

int64_t gps_GetBootTickMs()
{
    struct timespec ts;

    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (int64_t)ts.tv_sec * 1000 + (int64_t)ts.tv_nsec/1000000;
}


/******************************************************************************
 * 函数功能: 查找USB口的gps模块是否存在
 * 输入参数:        s32Cnt -- 第几次
                s32Num -- 每几次打印一次
                ps8RecvData -- 数据指针
 * 输出参数:
 * 返回值  : SV_SUCCESS - 成功
 * 修改时间: 2022-5-18
 *****************************************************************************/
sint32 gps_DectectUSB()
{
    sint32 s32Ret = -1;
    sint8 szCmd[128] = {0};
    sint8 szPath[128] = {0};
    char szBuf[128] = {0};
    char *pcTmp = NULL;

    sprintf(szCmd, "find /sys/devices/platform/ -name \"*-1.1\:1.0\"");

    s32Ret = SAFE_System_Recv(szCmd, szPath, sizeof(szPath));

    /* 去换行符 */
    pcTmp = strchr(szPath, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szPath, '\n');

    if(NULL != pcTmp)
        *pcTmp = '\0';

    memset(szCmd, 0, sizeof(szCmd));

    sprintf(szCmd, "ls %s | grep tty", szPath);
    s32Ret = SAFE_System_Recv(szCmd, szBuf, sizeof(szBuf));

    /* 去换行符 */
    pcTmp = strchr(szBuf, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szBuf, '\n');

    if(NULL != pcTmp)
        *pcTmp = '\0';

    if((SV_SUCCESS == s32Ret) && (strstr(szBuf, "ttyUSB") != NULL))
    {
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}



/* 播放音频 */
void gps_PlayAudio_body(void *pvArg)
{
#if !defined(BOARD_WFCR20S2)
    sint32 s32Ret = 0;
    char szCmd[128] = {0};
	char szFilePath[256] = {0};
    static SV_BOOL bPlaying = SV_FALSE;
    static SV_BOOL bInsertLast = SV_FALSE;
    ALARM_TYPE_E eAlarmType = *((ALARM_TYPE_E *)pvArg);

    if (bPlaying)
    {
        return;
    }

    if (COMMON_IsPathExist(ALARM_WELCOME_PLAY_FILE))
    {
        return;
    }

    bPlaying = SV_TRUE;
    ALARM_EnableSpk(SV_TRUE);
    ALARM_PlayAudio(eAlarmType);
    bPlaying = SV_FALSE;

    if (!COMMON_IsAplaying())
    {
        ALARM_EnableSpk(SV_FALSE);
    }
#endif
    return;
}

sint32 gps_Alarm_PlayAudio(ALARM_TYPE_E enAlarmType)
{
    sint32 s32Ret;
    pthread_t thread_trigger;
    static ALARM_TYPE_E enAlarmTypePlay = ALARM_NOTHING;

    enAlarmTypePlay = enAlarmType;
    s32Ret = pthread_create(&thread_trigger, NULL, gps_PlayAudio_body, (void *)&enAlarmTypePlay);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create  failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

void gps_State_Update_Alarm()
{
    static SV_BOOL s_bPlayGpsConn = SV_FALSE;
    static SV_BOOL s_bPlayGpsIns = SV_FALSE;

    if (3 == m_stGPSInfo.stGPSData.status)
    {
        if (!s_bPlayGpsConn)
        {
            printf("gps_State_Update_Alarm. NOTIFY_GPS_CONNECT\n");
            gps_Alarm_PlayAudio(NOTIFY_GPS_CONNECT);
            s_bPlayGpsConn = SV_TRUE;
        }
    }
    else
    {
        s_bPlayGpsConn = SV_FALSE;
    }

    if (3 == m_stGPSInfo.stGPSData.s32INS_state || 4 == m_stGPSInfo.stGPSData.s32INS_state)
    {
        if (!s_bPlayGpsIns)
        {
            gps_Alarm_PlayAudio(NOTIFY_GPS_INS_FINISH);
            s_bPlayGpsIns = SV_TRUE;
        }
    }
    else
    {
        s_bPlayGpsIns = SV_FALSE;
    }

}

void gps_FenceCheck()
{

	if (3 == m_stGPSInfo.stGPSData.status)
	{
		FENCE_Check(m_stGPSInfo.stGPSData.longitude,m_stGPSInfo.stGPSData.latitude);
	}

}

/******************************************************************************
 * 函数功能: 将模块信息更新进文件/var/info/gps
 * 输入参数: pstGPSInfo -- 主模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DumpModuleInfo(GPS_INFO_S *pstGPSInfo)
{
    sint32 s32Ret = -1;
    sint32 s32Fd = 0;
    cJSON *pstJson = NULL;
    char szBuf[2048];
    uint8 i = 0;

    cJSON *pstGPSSrcType = NULL;
    cJSON *pst4GModType = NULL;
    cJSON *pstException = NULL;
    cJSON *pstStatus = NULL;
    cJSON *pstSignal = NULL;
    cJSON *pstHadData = NULL;
    cJSON *pstSatinuse = NULL;
    cJSON *pstSatCount = NULL;
    cJSON *pstLatitude = NULL;
    cJSON *pstLongitude = NULL;
    cJSON *pstDirection = NULL;
    cJSON *pstPDOP = NULL;
    cJSON *pstHDOP = NULL;
    cJSON *pstVDOP = NULL;
    cJSON *pstElv = NULL;
    cJSON *pstSpK = NULL;
    cJSON *pstSnrSingle = NULL;

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return SV_FAILURE;
    }

    //异常位
    pstException = cJSON_CreateBool(pstGPSInfo->bException);
    if (NULL == pstException)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Exception", pstException);

    pst4GModType = cJSON_CreateNumber(pstGPSInfo->eModuleType);
    if (NULL == pst4GModType)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "4GType", pst4GModType);

    pstGPSSrcType = cJSON_CreateNumber(pstGPSInfo->stGPSConfig.eGPSSrc);
    if (NULL == pstGPSSrcType)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "GPSSrc", pstGPSSrcType);

    pstStatus = cJSON_CreateNumber(pstGPSInfo->stGPSVirInfo.bEnable ? 3 : pstGPSInfo->stGPSData.status); // 优先虚拟设备
    if (NULL == pstStatus)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Status", pstStatus);

    pstSignal = cJSON_CreateNumber(pstGPSInfo->stGPSVirInfo.bEnable ? 3 : pstGPSInfo->stGPSData.signal); // 优先虚拟设备
    if (NULL == pstSignal)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Signal", pstSignal);

    pstHadData = cJSON_CreateBool(pstGPSInfo->stGPSVirInfo.bEnable ? SV_TRUE : pstGPSInfo->stGPSData.bHadData); // 优先虚拟设备
    if (NULL == pstHadData)
    {
        print_level(SV_ERROR, "cJSON_CreateBool failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "HadData", pstHadData);

    pstSatinuse = cJSON_CreateNumber(pstGPSInfo->stGPSData.satinuse);
    if (NULL == pstSatinuse)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "Satinuse", pstSatinuse);

    pstSatCount = cJSON_CreateNumber(pstGPSInfo->stGPSData.sat_count);
    if (NULL == pstSatCount)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "sat_count", pstSatCount);

    pstLatitude = cJSON_CreateNumber(pstGPSInfo->stGPSVirInfo.bEnable ? 23.123456 : pstGPSInfo->stGPSData.latitude);
    if (NULL == pstLatitude)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "latitude", pstLatitude);

    pstLongitude = cJSON_CreateNumber(pstGPSInfo->stGPSVirInfo.bEnable ? -113.123456 : pstGPSInfo->stGPSData.longitude);
    if (NULL == pstLongitude)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "longitude", pstLongitude);

    pstDirection = cJSON_CreateNumber(pstGPSInfo->stGPSData.direction);
    if (NULL == pstDirection)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "direction", pstDirection);

    pstPDOP = cJSON_CreateNumber(pstGPSInfo->stGPSData.PDOP);
    if (NULL == pstPDOP)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "PDOP", pstPDOP);

    pstHDOP = cJSON_CreateNumber(pstGPSInfo->stGPSData.HDOP);
    if (NULL == pstHDOP)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "HDOP", pstHDOP);

    pstVDOP = cJSON_CreateNumber(pstGPSInfo->stGPSData.VDOP);
    if (NULL == pstVDOP)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "VDOP", pstVDOP);

    pstElv = cJSON_CreateNumber(pstGPSInfo->stGPSVirInfo.bEnable ? 100.888888 : pstGPSInfo->stGPSData.elv);
    if (NULL == pstElv)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "elv", pstElv);

    pstSpK = cJSON_CreateNumber(pstGPSInfo->stGPSVirInfo.bEnable ? pstGPSInfo->stGPSVirInfo.Speed : pstGPSInfo->stGPSData.spk);
    //pstSpK = cJSON_CreateNumber(pstGPSInfo->stGPSData.spk);
    if (NULL == pstSpK)
    {
        print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
        goto exit;
    }
    cJSON_AddItemToObject(pstJson, "spk", pstSpK);

    cJSON *pastSnrList = cJSON_CreateArray();
    if (NULL == pastSnrList)
    {
        print_level(SV_ERROR, "cJSON_CreateArray failed.\n");
        goto exit;
    }

    for(i=0; i<GPS_VIS_MAX_NUM; i++)
    {
        cJSON *pstSnr = cJSON_CreateObject();
        if (NULL == pstSnr)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            goto exit;
        }

        pstSnrSingle = cJSON_CreateNumber(pstGPSInfo->stGPSData.snr[i]);
        if (NULL == pstSnrSingle)
        {
            print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
            goto exit;
        }
        cJSON_AddItemToObject(pstSnr, "snr", pstSnrSingle);
        cJSON_AddItemToArray(pastSnrList, pstSnr);
    }

    cJSON_AddItemToObject(pstJson, "snrList", pastSnrList);

    //写文件
    memset(szBuf, 0, 2048);
    cJSON_PrintPreallocated(pstJson, szBuf, 2048, 0);
    s32Fd = open("/var/info/gps-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        goto exit;
    }

    s32Ret = write(s32Fd, szBuf, strlen(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "write file: /var/info/cellular-tmp failed. [err:%s]\n", strerror(errno));
        close(s32Fd);
        goto exit;
    }

    close(s32Fd);
    rename("/var/info/gps-tmp", "/var/info/gps");

exit:
    cJSON_Delete(pstJson);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 处理GPRMC推荐定位信息数据
 * 输入参数: pstGPSInfo -- GPS模块指针 szBuf -- 数据所在模块存储区
 *             pstGPRMC -- 推荐定位信息模块数据存储区 u32szBufSize -- szBuf 的大小
 *             bIsGPS -- GPS还是北斗 SV_TRUE:GPS SV_FALSE :北斗+GPS
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_GPRMCProcess(GPS_INFO_S *pstGPSInfo, const char *szBuf, nmeaGPRMC* pstGPRMC, uint32 u32szBufSize, SV_BOOL bIsGPS)
{
    sint32 s32Ret = -1;
    static sint32 s32RMCErrCnt = 0;
    double latitude, longitude;
    double dTmpSpk = 0.0;
    if(NULL == pstGPSInfo || NULL == szBuf || NULL == pstGPRMC)
    {
        return SV_FAILURE;
    }

    //北斗和GPS区分，获取经纬度
    if(SV_TRUE == bIsGPS)
    {
        s32Ret = nmea_parse_GPRMC(szBuf, u32szBufSize, pstGPRMC);
    }
    else
    {
        s32Ret = nmea_parse_GNRMC(szBuf, u32szBufSize, pstGPRMC);
    }

    if(0 == s32Ret && s32RMCErrCnt < DATA_ERR_CNT)
    {
        s32RMCErrCnt++;
        return SV_FAILURE;

    }
    s32RMCErrCnt = 0;
    s32Ret = gps_NDeg2Degree(pstGPRMC->lat, &latitude);
    s32Ret = gps_NDeg2Degree(pstGPRMC->lon, &longitude);
    s32Ret = gps_NDeg2Degree(pstGPRMC->direction, &(pstGPSInfo->stGPSData.direction));
    pstGPSInfo->stGPSData.status = (pstGPRMC->status == 'A')? 3 : 0;
    if (NULL != pstGPRMC->ns)
    {
        pstGPSInfo->stGPSData.latitude = (pstGPRMC->ns == 'S')? -latitude : latitude;
    }
    else
    {
        pstGPSInfo->stGPSData.latitude = 0;
    }

    if (NULL != pstGPRMC->ew)
    {
        pstGPSInfo->stGPSData.longitude = (pstGPRMC->ew == 'W')? -longitude : longitude;
    }
    else
    {
        pstGPSInfo->stGPSData.longitude = 0;
    }

    /* 如果没有接收到地面速度信息，则使用推荐定位信息的速度，需要进行单位转换 1kn = 1.852km/h */
    if (!pstGPSInfo->bUseVTGSpeed)
    {
        dTmpSpk = pstGPRMC->speed * 1.852;
        if(BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
        {
            dTmpSpk = pstGPRMC->speed * 1.852 / 1.6093;   // kmh转为mph单位
        }

        /* 速度滤波 */
        if ((dTmpSpk - pstGPSInfo->stGPSData.spk) <= 150)
        {
            pstGPSInfo->stGPSData.spk = dTmpSpk;
        }
    }

    if (GPS_SRC_DVR == pstGPSInfo->stGPSConfig.eGPSSrc)
    {
        pstGPSInfo->stGPSData.status = pstGPSInfo->stDvrGPSData.status;
        pstGPSInfo->stGPSData.spk = pstGPSInfo->stDvrGPSData.spk;
        pstGPSInfo->stGPSData.longitude = pstGPSInfo->stDvrGPSData.longitude;
        pstGPSInfo->stGPSData.latitude = pstGPSInfo->stDvrGPSData.latitude;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 处理GPGSA当前可见卫星信息数据
 * 输入参数: pstGPSInfo -- GPS模块指针 szBuf -- 数据所在模块存储区
 *             pstGPGSA -- 当前可见卫星信息模块数据存储区 u32szBufSize -- szBuf 的大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_GPGSAProcess(GPS_INFO_S *pstGPSInfo, const char *szBuf, nmeaGPGSA* pstGPGSA, uint32 u32szBufSize)
{
    sint32 s32Ret = -1;
    static sint32 s32GSAErrCnt = 0;
    if(NULL == pstGPSInfo || NULL == szBuf || NULL == pstGPGSA)
    {
        return SV_FAILURE;
    }

    s32Ret = nmea_parse_GPGSA(szBuf, u32szBufSize, pstGPGSA);
    if(0 == s32Ret && s32GSAErrCnt < DATA_ERR_CNT)
    {
        s32GSAErrCnt++;
        return SV_FAILURE;

    }

    s32GSAErrCnt = 0;
    pstGPSInfo->stGPSData.PDOP = pstGPGSA->PDOP;
    pstGPSInfo->stGPSData.HDOP = pstGPGSA->HDOP;
    pstGPSInfo->stGPSData.VDOP = pstGPGSA->VDOP;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 处理GPATT
 * 输入参数: pstGPSInfo -- GPS模块指针 szBuf -- 数据所在模块存储区
 *           pstGPATT -- 惯性导航等数据存储区
             u32szBufSize -- szBuf 的大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : SKM-4DU 模块定义的一组通信协议GPATT，主要用来判断惯导功能有没有打开
 *****************************************************************************/
sint32 gps_GPATTProcess(GPS_INFO_S *pstGPSInfo, const char *szBuf, GPATT* pstGPATT, uint32 u32szBufSize)
{
    sint32 s32Ret = -1;
    static sint32 s32GSAErrCnt = 0;
    if(NULL == pstGPSInfo || NULL == szBuf || NULL == pstGPATT)
    {
        print_level(SV_ERROR, "use null point\n");
        return SV_FAILURE;
    }

    s32Ret = parse_GPATT(szBuf, u32szBufSize, pstGPATT);
    if(0 == s32Ret && s32GSAErrCnt < DATA_ERR_CNT)
    {
        s32GSAErrCnt++;
        print_level(SV_ERROR, "parse_GPATT failed\n");
        return SV_FAILURE;
    }

    s32GSAErrCnt = 0;
    pstGPSInfo->stGPSData.s32INS = pstGPATT->INS;
    pstGPSInfo->stGPSData.s32INS_state = atoi(pstGPATT->State_Flag);
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 处理GPGGA定位信息数据
 * 输入参数: pstGPSInfo -- GPS模块指针 szBuf -- 数据所在模块存储区
 *             pstGPGGA -- 定位信息模块数据存储区 u32szBufSize -- szBuf 的大小
 *             bIsGPS -- GPS还是北斗 SV_TRUE:GPS SV_FALSE :北斗
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_GPGGAProcess(GPS_INFO_S *pstGPSInfo, const char *szBuf, nmeaGPGGA* pstGPGGA, uint32 u32szBufSize, SV_BOOL bIsGPS)
{
    sint32 s32Ret = -1;
    static sint32 s32GGAErrCnt = 0;
    if(NULL == pstGPSInfo || NULL == szBuf || NULL == pstGPGGA)
    {
        return SV_FAILURE;
    }

    if(SV_TRUE == bIsGPS)
    {
        s32Ret = nmea_parse_GPGGA(szBuf, u32szBufSize, pstGPGGA);
    }
    else
    {
        s32Ret = nmea_parse_GNGGA(szBuf, u32szBufSize, pstGPGGA);
    }

    if(0 == s32Ret && s32GGAErrCnt < DATA_ERR_CNT)
    {
        s32GGAErrCnt++;
        return SV_FAILURE;

    }

    s32GGAErrCnt = 0;
    pstGPSInfo->stGPSData.satinuse = pstGPGGA->satinuse;
    pstGPSInfo->stGPSData.elv = pstGPGGA->elv;
    pstGPSInfo->stGPSData.signal = min((pstGPGGA->satinuse+3-1)/3, 3); /* 通过使用卫星数衡量GPS信号, 每三个卫星作为一个刻度*/

    if (GPS_SRC_DVR == pstGPSInfo->stGPSConfig.eGPSSrc)
    {
        pstGPSInfo->stGPSData.signal = pstGPSInfo->stDvrGPSData.signal;
        pstGPSInfo->stGPSData.satinuse = pstGPSInfo->stDvrGPSData.satinuse;
        pstGPSInfo->stGPSData.elv = pstGPSInfo->stDvrGPSData.elv;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 处理GPGSV可见卫星信息
 * 输入参数: pstGPSInfo -- GPS模块指针 szBuf -- 数据所在模块存储区
 *             pstGPGSV -- 可见卫星信息数据存储区 u32szBufSize -- szBuf 的大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_GPGSVProcess(GPS_INFO_S *pstGPSInfo, const char *szBuf, nmeaGPGSV* pstGPGSV, GPS_SVINFO_S* pstSvInfo,  uint32 u32szBufSize)
{
    sint32 i = 0;
    sint32 s32Ret = -1;
    static sint32 s32PackCount = 1;
    sint32 s32LeftNum = 0;
    static uint32 u32Index = 0;
    sint32 s32SatVisble = 0;
    static sint32 s32GPGSVErrCnt = 0;

    if(NULL == pstGPSInfo || NULL == szBuf || NULL == pstGPGSV || NULL == pstSvInfo)
    {
        return SV_FAILURE;
    }

    s32Ret = nmea_parse_GPGSV(szBuf, u32szBufSize, pstGPGSV);
    if(0 == s32Ret && s32GPGSVErrCnt < DATA_ERR_CNT)
    {
        s32GPGSVErrCnt++;
        return SV_FAILURE;

    }

    s32GPGSVErrCnt = 0;
    pstGPSInfo->stGPSData.sat_count = pstGPGSV->sat_count;
    s32SatVisble = pstGPGSV->sat_count;
    if((s32PackCount < pstGPGSV->pack_count) && (u32Index < (GPS_VIS_MAX_NUM - 1)))
    {
        for(i=0; i<4; i++)
        {
            if(u32Index >= GPS_VIS_MAX_NUM)
            {
                print_level(SV_ERROR, "u32Index:%d >= GPS_VIS_MAX_NUM:%d\n", u32Index, GPS_VIS_MAX_NUM);
                continue;
            }
            pstSvInfo->stGPSSv[u32Index].satelliteId = pstGPGSV->sat_data[i].id;
            pstSvInfo->stGPSSv[u32Index].elevation =  pstGPGSV->sat_data[i].elv;
            pstSvInfo->stGPSSv[u32Index].azimuth = pstGPGSV->sat_data[i].azimuth;
            pstSvInfo->stGPSSv[u32Index].snr= pstGPGSV->sat_data[i].sig;
            pstGPSInfo->stGPSData.snr[u32Index] = pstGPGSV->sat_data[i].sig;
            u32Index++;
        }
        s32PackCount++;
    }
    else
    {
        s32LeftNum = (s32SatVisble - (s32PackCount-1)*4);
        for(i=0; i<s32LeftNum; i++)
        {
            if(u32Index >= GPS_VIS_MAX_NUM)
            {
                print_level(SV_ERROR, "u32Index:%d >= GPS_VIS_MAX_NUM:%d\n", u32Index, GPS_VIS_MAX_NUM);
                continue;
            }
            pstSvInfo->stGPSSv[u32Index].satelliteId = pstGPGSV->sat_data[i].id;
            pstSvInfo->stGPSSv[u32Index].elevation =  pstGPGSV->sat_data[i].elv;
            pstSvInfo->stGPSSv[u32Index].azimuth = pstGPGSV->sat_data[i].azimuth;
            pstSvInfo->stGPSSv[u32Index].snr= pstGPGSV->sat_data[i].sig;
            pstGPSInfo->stGPSData.snr[u32Index] = pstGPGSV->sat_data[i].sig;
            u32Index++;
        }
        u32Index = 0;
        s32PackCount = 1;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 处理GPVTG地面位置信息
 * 输入参数: pstGPSInfo -- GPS模块指针 szBuf -- 数据所在模块存储区
 *             pstGPVTG -- 地面位置信息存储区 u32szBufSize -- szBuf 的大小
 *             bIsGPS -- GPS还是北斗 SV_TRUE:GPS SV_FALSE :北斗
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_GPVTGProcess(GPS_INFO_S *pstGPSInfo, const char *szBuf, nmeaGPVTG* pstGPVTG, uint32 u32szBufSize, SV_BOOL bIsGPS)
{
    sint32 s32Ret = -1;
    static sint32 s32VTGErrCnt = 0;
    double dTmpSpk = 0.0;

    if(NULL == pstGPSInfo || NULL == szBuf || NULL == pstGPVTG)
    {
        return SV_FAILURE;
    }

    if(SV_TRUE == bIsGPS)
    {
        s32Ret = nmea_parse_GPVTG(szBuf, u32szBufSize, pstGPVTG);
    }
    else
    {
        s32Ret = nmea_parse_GNVTG(szBuf, u32szBufSize, pstGPVTG);
    }

    if(0 == s32Ret && s32VTGErrCnt < DATA_ERR_CNT)
    {
        s32VTGErrCnt++;
        return SV_FAILURE;

    }

    s32VTGErrCnt = 0;
    dTmpSpk = pstGPVTG->spn * 1.852;
    if(BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        dTmpSpk = pstGPVTG->spn * 1.852 / 1.6093; // kmh转为mph单位
    }

    /* 速度滤波 */
    if ((dTmpSpk - pstGPSInfo->stGPSData.spk) <= 150)
    {
        pstGPSInfo->stGPSData.spk = dTmpSpk;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化4G模块的gps端口
 * 输入参数: 无
 * 输出参数: ps32Fd -- 设备文件描述符 peModuleType -- 模块类型
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevInit4G(sint32 *ps32Fd, CELLULAR_MODULE_E *peModuleType)
{
#if (!defined(BOARD_ADA32V2) && !defined(BOARD_ADA32V3) && !defined(BOARD_ADA32IR) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA900V1) && !defined(BOARD_DMS51V1))
    sint32 s32Ret = -1, s32Fd = -1;
    char *pGPSPortPath = NULL;
    char szGPSPort[32] = {0};
    char szCmd[128] = {0};
    char szPath[128] = {0};
    char *pcTmp = NULL;
    CELLULAR_MODULE_E eModuleType = CELLULAR_UNKNOWN;

    s32Ret = Cellular_GetModTypeforCheck(&eModuleType);
    if(CELLULAR_UNKNOWN != eModuleType)
    {
        switch(eModuleType)
        {
            case CELLULAR_EC25:
			case CELLULAR_SIM7600:
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
                sprintf(szCmd, "find /sys/devices/platform/ -name \"ttyUSB*\" 2>/dev/null | head -n 1 | sed 's|/ttyUSB[^/]*.*||' | sed 's/[0-9]$/1/'");
                //printf(szCmd, "find /sys/devices/platform/ -name \"*-1.2\:1.1\"");
#elif defined(BOARD_WFCR20S2)
				sprintf(szCmd, "find /sys/devices/soc0/ -name \"*-1.1\:1.1\"");
#endif
			break;

		    case CELLULAR_EC200U:
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
				sprintf(szCmd, "find /sys/devices/platform/ -name \"*-1.2\:1.8\"");
#endif
                break;
            case CELLULAR_GEMATO:
            default :
                pGPSPortPath = NULL;
                break;
        }

		s32Ret = GetInsContext(szCmd, szPath, sizeof(szPath));
		pcTmp = strchr(szPath, '\r');
		if(NULL == pcTmp)
			pcTmp = strchr(szPath, '\n');
		if(NULL != pcTmp)
			*pcTmp = '\0';
		pGPSPortPath = szPath;

        if(NULL != pGPSPortPath)
        {

            s32Ret = gps_SearchDev(szGPSPort, pGPSPortPath, sizeof(szGPSPort));
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "search GPS Port error\n");
                return SV_FAILURE;
            }
        }
        else
        {
            return SV_FAILURE;
        }

        s32Ret = gps_SerialOpen(&s32Fd, szGPSPort, 9600);
        if(SV_FAILURE == s32Ret || s32Fd < 0)
        {
            print_level(SV_ERROR, "open serial %s error\n", szGPSPort);
            return SV_FAILURE;
        }

        *peModuleType = eModuleType;
        *ps32Fd = s32Fd;
        return SV_SUCCESS;
    }
#endif

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 去初始化4G模块的gps端口
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevFini4G(sint32 s32Fd)
{
    sint32 s32Ret = -1;
    s32Ret = close(s32Fd);
    if (s32Ret < 0 )
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化内置gps模块
 * 输入参数: 无
 * 输出参数: ps32Fd -- 设备文件描述
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevInitSerial(sint32 *ps32Fd)
{
    sint32 s32Ret = -1, s32Fd = -1;
    char* pGPSPort = NULL;
    pGPSPort = GPS_SERIAL_PORT;

    s32Ret = gps_SerialOpen(&s32Fd, pGPSPort, 9600);
    if((SV_FAILURE == s32Ret)|| s32Fd < 0)
    {
        print_level(SV_ERROR, "open serial error\n");
        return SV_FAILURE;
    }
    *ps32Fd = s32Fd;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化内置gps模块
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevFiniSerial(sint32 s32Fd)
{
    sint32 s32Ret = -1;
    s32Ret = close(s32Fd);
    if (s32Ret < 0 )
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化MCU透传GPS模块
 * 输入参数: 无
 * 输出参数: ps32Fd -- 设备文件描述
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevInitMcu(sint32 *ps32Fd)
{
    sint32 s32Fd = open(GPS_FIFO_FILE, O_RDONLY | O_NONBLOCK);
    if (s32Fd < 0)
    {
        return SV_FAILURE;
    }
    *ps32Fd = s32Fd;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化MCU透传GPS模块
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevFiniMcu(sint32 s32Fd)
{
    sint32 s32Ret = -1;
    s32Ret = close(s32Fd);
    if (s32Ret < 0 )
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化USB GPS模块
 * 输入参数: 无
 * 输出参数: ps32Fd -- 设备文件描述
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevInitUSB(sint32 *ps32Fd)
{
    sint32 s32Ret = -1;
    sint32 s32Fd = -1;
    sint8 szCmd[128] = {0};
    sint8 szPath[128] = {0};
    sint8 *ps8Tmp = NULL;
    sint8 s8GPSPort[32] = {0};
    char szBuf[128] = {0};
    char *pcTmp = NULL;

#if 0
    sprintf(szCmd, "find /sys/devices/platform/ -name \"*ttyUSB*\"");
    s32Ret = SAFE_System_Recv(szCmd, szPath, sizeof(szPath));
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "gps_DevInitUSB: SAFE_System_Recv failed. s32Ret=%d\n", s32Ret);
        return SV_FAILURE;
    }

    ps8Tmp = strchr(szPath, '\r');
    if(NULL == ps8Tmp)
        ps8Tmp = strchr(szPath, '\n');
    if(NULL != ps8Tmp)
        *ps8Tmp = '\0';

    ps8Tmp = strrchr(szPath, '/');
    if(NULL != ps8Tmp)
    {
        snprintf(s8GPSPort, sizeof(s8GPSPort), "/dev/%s", ps8Tmp + 1);
    }
    else
    {
        print_level(SV_ERROR, "not find ttyUSB\n");
        return SV_FAILURE;
    }
#else

    sprintf(szCmd, "find /sys/devices/platform/ -name \"*-1.1\:1.0\"");

    s32Ret = SAFE_System_Recv(szCmd, szPath, sizeof(szPath));

    /* 去换行符 */
    pcTmp = strchr(szPath, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szPath, '\n');

    if(NULL != pcTmp)
        *pcTmp = '\0';

    memset(szCmd, 0, sizeof(szCmd));

    sprintf(szCmd, "ls %s | grep tty", szPath);
    s32Ret = SAFE_System_Recv(szCmd, szBuf, sizeof(szBuf));

    /* 去换行符 */
    pcTmp = strchr(szBuf, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szBuf, '\n');

    if(NULL != pcTmp)
        *pcTmp = '\0';

    if((SV_SUCCESS == s32Ret) && (strstr(szBuf, "ttyUSB") != NULL))
    {
        snprintf(s8GPSPort, sizeof(s8GPSPort), "/dev/%s", szBuf);
    }
    else
    {
        print_level(SV_ERROR, "not find ttyUSB\n");
        return SV_FAILURE;
    }
#endif

    s32Ret = gps_SerialOpen(&s32Fd, s8GPSPort, 9600);
    if(SV_FAILURE == s32Ret || s32Fd < 0)
    {
        print_level(SV_ERROR, "open serial %s error\n", s8GPSPort);
        return SV_FAILURE;
    }

    *ps32Fd = s32Fd;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化USB GPS模块
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevFiniUSB(sint32 s32Fd)
{
    sint32 s32Ret = -1;
    s32Ret = close(s32Fd);
    if (s32Ret < 0 )
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化232外接的GPS模块
 * 输入参数: 无
 * 输出参数: ps32Fd -- 设备文件描述
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevInit232(sint32 *ps32Fd)
{
    sint32 s32Ret = -1, s32Fd = -1;
    char* pGPSPort = NULL;
    pGPSPort = GPS_RS232_PORT;

    s32Ret = gps_SerialOpen(&s32Fd, pGPSPort, 9600);
    if((SV_FAILURE == s32Ret)|| s32Fd < 0)
    {
        print_level(SV_ERROR, "open serial error\n");
        return SV_FAILURE;
    }
    *ps32Fd = s32Fd;
}

/******************************************************************************
 * 函数功能: 去初始化232外接的GPS模块
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevFini232(sint32 s32Fd)
{
    sint32 s32Ret = -1;
    s32Ret = close(s32Fd);
    if (s32Ret < 0 )
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化DVR直接传输gps数据模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevInitDVR(void)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化DVR直接传输gps数据模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevFiniDVR(void)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: GPS硬件设备初始化
 * 输入参数: pstGPSInfo -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevInit(GPS_INFO_S *pstGPSInfo)
{
    sint32 s32Ret = -1;
    if (NULL == pstGPSInfo)
    {
        return ERR_NULL_PTR;
    }


    pthread_mutex_lock(&m_stGPSInfo.mutexLock);
    switch(pstGPSInfo->stGPSConfig.eGPSSrc)
    {
        case GPS_SRC_4G:
            s32Ret = gps_DevInit4G(&pstGPSInfo->s32GPSDevFd, &pstGPSInfo->eModuleType);
            break;
        case GPS_SRC_MCU:
            s32Ret = gps_DevInitMcu(&pstGPSInfo->s32GPSDevFd);
//            print_level(SV_ERROR,"gps_DevInitMcu :%d \n",s32Ret);
            break;
        case GPS_SRC_SERIAL:
            s32Ret = gps_DevInitSerial(&pstGPSInfo->s32GPSDevFd);
            break;
        case GPS_SRC_DVR:
            s32Ret = gps_DevInitDVR();
            break;
        case GPS_SRC_USB:
            s32Ret = gps_DevInitUSB(&pstGPSInfo->s32GPSDevFd);
            break;
        case GPS_SRC_232:
            s32Ret = gps_DevInit232(&pstGPSInfo->s32GPSDevFd);
            break;
        default :
            print_level(SV_ERROR, "gps device doesnt support\n");
            s32Ret = SV_FAILURE;
            break;
    }
    pthread_mutex_unlock(&m_stGPSInfo.mutexLock);

    return s32Ret;
}

/******************************************************************************
 * 函数功能: GPS硬件设备去初始化
 * 输入参数: pstGPSInfo -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 gps_DevFini(GPS_INFO_S *pstGPSInfo)
{
    sint32 s32Ret = -1;
    if ((NULL == pstGPSInfo) || (pstGPSInfo->s32GPSDevFd < 0) || (pstGPSInfo->eLastGPSSrc >= GPS_SRC_BUTT))
    {
        return ERR_ILLEGAL_PARAM;
    }

    pthread_mutex_lock(&m_stGPSInfo.mutexLock);
    switch (pstGPSInfo->eLastGPSSrc)
    {
        case GPS_SRC_4G:
            s32Ret = gps_DevFini4G(pstGPSInfo->s32GPSDevFd);
            pstGPSInfo->eModuleType = CELLULAR_UNKNOWN;
            break;
        case GPS_SRC_MCU:
            s32Ret = gps_DevFiniMcu(pstGPSInfo->s32GPSDevFd);
            break;
        case GPS_SRC_SERIAL:
            s32Ret = gps_DevFiniSerial(pstGPSInfo->s32GPSDevFd);
            break;
        case GPS_SRC_DVR:
            s32Ret = gps_DevFiniDVR();
            break;
        case GPS_SRC_USB:
            s32Ret = gps_DevFiniUSB(pstGPSInfo->s32GPSDevFd);
            break;
        case GPS_SRC_232:
            s32Ret = gps_DevFini232(&pstGPSInfo->s32GPSDevFd);
            break;
        default :
            print_level(SV_ERROR, "gps device doesnt support\n");
            s32Ret = SV_FAILURE;
            break;
    }
    pthread_mutex_unlock(&m_stGPSInfo.mutexLock);

    return s32Ret;
}

/******************************************************************************
 * 函数功能: GPS主进程
 * 输入参数: pvArg -- 用户传入参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void* gps_Watch_Body(void *pvArg)
{
    SV_BOOL bIsGPS = SV_FALSE;
    SV_BOOL bHadInit = SV_FALSE;
    sint32 i = 0, s32Ret = -1, s32StartPost = 0, s32MaxFd = -1, s32GetDataErrCnt = 0;
    sint32 s32InitErr = 0;
    sint32 s32TimeoutCnt = 0;
    sint32 s32PrintCnt = 1;         /* 第几轮 */
    sint32 s32PrintPerRound = 10;   /* 每几轮打印一次接收的数据 */
    char szGPSBuf[10*1024] = {0};
    GPS_SVINFO_S stGPSSvInfo = {0};
    GPS_INFO_S *pstGPSInfo = (GPS_INFO_S *)pvArg;
    fd_set stFdSet = {0};
    SV_BOOL bHadChangeSrc = SV_FALSE;
    struct timeval stTimeout;
    nmeaGPRMC* pstGPRMC = pstGPSInfo->pstGPRMC;    //推荐定位信息
    nmeaGPGSA* pstGPGSA = pstGPSInfo->pstGPGSA;    //当前卫星信息
    nmeaGPGGA* pstGPGGA = pstGPSInfo->pstGPGGA;    //定位信息
    nmeaGPGSV* pstGPGSV = pstGPSInfo->pstGPGSV;    //可见卫星信息
    nmeaGPVTG* pstGPVTG = pstGPSInfo->pstGPVTG;    //地面速度信息
    GPATT* pstGPATT = pstGPSInfo->pstGPATT;        //惯性导航、俯仰角等信息
    uint32 u32TestCnt = 0;

	double lat = 0.0;
	double lon = 0.0;
    s32Ret = prctl(PR_SET_NAME, "GPS_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstGPSInfo->bRunning)
    {
        s32Ret = gps_DumpModuleInfo(pstGPSInfo);

        pstGPSInfo->s32MainLifeCnt++;

#if (!defined(BOARD_ADA32C4))  //gps 状态报警只有中文语音，暂不开放此功能
        /* 进行状态报警 */
        gps_State_Update_Alarm();
#endif

#if (defined(BOARD_ADA32C4))
		//gps_FenceCheck();
#endif
        /* 处理外部配置gps */
        if (SV_TRUE == pstGPSInfo->bConfig)
        {
            pstGPSInfo->bConfig = SV_FALSE;
        }

        /* 处理异常 */
        if (SV_TRUE == pstGPSInfo->bException)
        {
            bHadInit = SV_FALSE;
            pstGPSInfo->bException = SV_FALSE;
            print_level(SV_WARN, "gps get exception\n");
        }
        /* 判断4G模块的gps功能是否打开 */
        if (SV_SUCCESS ==  access("/var/info/cell_gps_open", F_OK) && GPS_SRC_DVR != pstGPSInfo->stGPSConfig.eGPSSrc \
             && GPS_SRC_232 != pstGPSInfo->stGPSConfig.eGPSSrc)
        {
            SAFE_System_Not_Print("rm /var/info/cell_gps_open", 500);
            bHadChangeSrc = SV_TRUE;
            pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_4G;
        }

#if (defined(BOARD_DMS31V2))
        /* 判断RS232是否接gps模块 */
        if (SV_SUCCESS ==  access("/var/info/rs232_gps_open", F_OK) && GPS_SRC_DVR != pstGPSInfo->stGPSConfig.eGPSSrc)
        {
            SAFE_System_Not_Print("rm /var/info/rs232_gps_open", 500);
            bHadChangeSrc = SV_TRUE;
            pstGPSInfo->stGPSConfig.eGPSSrc = GPS_SRC_232;
        }
#endif

        /* 切换数据源 */
        if (pstGPSInfo->eLastGPSSrc != pstGPSInfo->stGPSConfig.eGPSSrc)
        {
            bHadInit = SV_FALSE;
            s32Ret = gps_DevFini(pstGPSInfo);
            print_level(SV_INFO, "gps date src change [%d]->[%d] \n",pstGPSInfo->eLastGPSSrc, pstGPSInfo->stGPSConfig.eGPSSrc);
        }

        /* 初始化GPS设备 */
        if (SV_FALSE == bHadInit)
        {
            s32Ret = gps_DevInit(pstGPSInfo);
            if (SV_SUCCESS ==s32Ret)
            {
                bHadInit = SV_TRUE;
                s32InitErr = 0;
                pstGPSInfo->eLastGPSSrc = pstGPSInfo->stGPSConfig.eGPSSrc;
            }
        }

        if (SV_FALSE == bHadInit)
        {
            pstGPSInfo->bRecvData = SV_FALSE;
            memset(&pstGPSInfo->stGPSData, 0, sizeof(pstGPSInfo->stGPSData));
            s32InitErr++;
            if (s32InitErr > 3)
            {
                s32InitErr = 0;
                gps_ChangeGPSScr(pstGPSInfo);
            }
            sleep_ms(1000);
            continue;
        }

        if (GPS_SRC_DVR == pstGPSInfo->stGPSConfig.eGPSSrc)
        {
            pstGPSInfo->stGPSData.status = pstGPSInfo->stDvrGPSData.status;
            pstGPSInfo->stGPSData.signal = pstGPSInfo->stDvrGPSData.signal;
            pstGPSInfo->stGPSData.satinuse = pstGPSInfo->stDvrGPSData.satinuse;
            pstGPSInfo->stGPSData.latitude = pstGPSInfo->stDvrGPSData.latitude;
            pstGPSInfo->stGPSData.longitude = pstGPSInfo->stDvrGPSData.longitude;
            pstGPSInfo->stGPSData.elv = pstGPSInfo->stDvrGPSData.elv;
            pstGPSInfo->stGPSData.spk = pstGPSInfo->stDvrGPSData.spk;
            sleep_ms(500);
            continue;
        }

        FD_ZERO(&stFdSet);
        FD_SET(pstGPSInfo->s32GPSDevFd, &stFdSet);
        s32MaxFd = pstGPSInfo->s32GPSDevFd + 1;
        stTimeout.tv_sec = 2;
        stTimeout.tv_usec = 0;
        s32Ret = select(s32MaxFd, &stFdSet, NULL, NULL, &stTimeout);
        if (s32Ret < 0)
        {
            pstGPSInfo->bRecvData = SV_FALSE;
            print_level(SV_ERROR, "select failed. [err=%#x]\n", errno);
        }
        else if (0 == s32Ret)
        {
            pstGPSInfo->bRecvData = SV_FALSE;

            if (GPS_SRC_SERIAL == pstGPSInfo->stGPSConfig.eGPSSrc && !bHadChangeSrc)
            {
                s32TimeoutCnt = 0;
            }
            s32TimeoutCnt++;
            if (s32TimeoutCnt > 3)
            {
                s32TimeoutCnt = 0;
                pstGPSInfo->stGPSData.signal = 0;
                gps_ChangeGPSScr(pstGPSInfo);
            }
        }
        else
        {
            s32TimeoutCnt = 0;
            if (s32PrintCnt > s32PrintPerRound)
            {
                s32PrintCnt = 1;
            }

            if (FD_ISSET(pstGPSInfo->s32GPSDevFd, &stFdSet))
            {
                s32Ret = gps_GetData(pstGPSInfo->s32GPSDevFd, szGPSBuf, sizeof(szGPSBuf), &s32StartPost);

                //print_level(SV_DEBUG, " %d gps data %s \n", s32Ret, szGPSBuf);

                if (SV_SUCCESS == s32Ret)
                {
                    s32GetDataErrCnt = 0;
                    pstGPSInfo->bRecvData = SV_TRUE;
                    //推荐定位信息
                    if (NULL != strstr(szGPSBuf,"$GPRMC")|| NULL != strstr(szGPSBuf, "$GNRMC"))
                    {
                        s32PrintCnt++;
                        gps_PrintRecvData(s32PrintCnt, s32PrintPerRound, szGPSBuf);
                        //获取GPS状态
                        if (strstr(szGPSBuf, "$GPRMC"))
                        {
                            bIsGPS = SV_TRUE;
                        }
                        else
                        {
                            bIsGPS = SV_FALSE;
                        }

                        //处理GPRMC数据包
                        s32Ret = gps_GPRMCProcess(pstGPSInfo, szGPSBuf, pstGPRMC, sizeof(szGPSBuf), bIsGPS);
                        if (SV_FAILURE == s32Ret)
                        {
                            continue;
                        }
                        //通过GPS获取系统时间
                        if ((3 == pstGPSInfo->stGPSData.status) && (SV_TRUE == pstGPSInfo->bTimeSync))
                        {
                            s32Ret = gps_SetTime(pstGPRMC);
                            if (SV_SUCCESS == s32Ret)
                            {
                                pstGPSInfo->bTimeSync = SV_FALSE;
                            }
                        }
                        //发送数据给GUI
                        if(SV_TRUE == pstGPSInfo->bSendData)
                        {
                            s32Ret = gps_SendData(&(pstGPSInfo->stGPSData));
                        }
                    }
                    //当前卫星信息
                    else if (NULL != strstr(szGPSBuf, "$GPGSA"))
                    {
                        gps_PrintRecvData(s32PrintCnt, s32PrintPerRound, szGPSBuf);
                        s32Ret = gps_GPGSAProcess(pstGPSInfo, szGPSBuf, pstGPGSA, sizeof(szGPSBuf));
                        if(SV_FAILURE == s32Ret)
                        {
                            //print_level(SV_ERROR, "GPGGA process error\n");
                            continue;
                        }
                    }
                    //定位信息
                    else if (NULL != strstr(szGPSBuf, "$GPGGA") || NULL != strstr(szGPSBuf, "$GNGGA"))
                    {
                        gps_PrintRecvData(s32PrintCnt, s32PrintPerRound, szGPSBuf);
                        if (strstr(szGPSBuf, "$GPGGA"))
                        {
                            bIsGPS = SV_TRUE;
                        }
                        else
                        {
                            bIsGPS = SV_FALSE;
                        }

                        s32Ret = gps_GPGGAProcess(pstGPSInfo, szGPSBuf, pstGPGGA, sizeof(szGPSBuf), bIsGPS);
                        if (SV_FAILURE == s32Ret)
                        {
                            //print_level(SV_ERROR, "GPGGA process error\n");
                            continue;
                        }
                    }
                    //可见卫星信息
                    else if (NULL != strstr(szGPSBuf, "$GPGSV"))
                    {
                        gps_PrintRecvData(s32PrintCnt, s32PrintPerRound, szGPSBuf);
                        s32Ret = gps_GPGSVProcess(pstGPSInfo, szGPSBuf, pstGPGSV, &stGPSSvInfo, sizeof(szGPSBuf));
                        if (SV_FAILURE == s32Ret)
                        {
                            //print_level(SV_ERROR, "GPGSV process error\n");
                            continue;
                        }
                    }
                    //地面位置信息
                    else if (NULL != strstr(szGPSBuf, "$GPVTG") || NULL != strstr(szGPSBuf, "$GNVTG"))
                    {
                        pstGPSInfo->bUseVTGSpeed = SV_TRUE;
                        gps_PrintRecvData(s32PrintCnt, s32PrintPerRound, szGPSBuf);
                        if (strstr(szGPSBuf, "$GPVTG"))
                        {
                            bIsGPS = SV_TRUE;
                        }
                        else
                        {
                            bIsGPS = SV_FALSE;
                        }

                        s32Ret = gps_GPVTGProcess(pstGPSInfo, szGPSBuf, pstGPVTG, sizeof(szGPSBuf), bIsGPS);
                        if (SV_FAILURE == s32Ret)
                        {
                            //print_level(SV_ERROR, "GPVTG process error\n");
                            continue;
                        }
                    }
                    else if (NULL != strstr(szGPSBuf, "$GPATT"))
                    {
                        gps_PrintRecvData(s32PrintCnt, s32PrintPerRound, szGPSBuf);
                        s32Ret = gps_GPATTProcess(pstGPSInfo, szGPSBuf, pstGPATT, sizeof(szGPSBuf));
                        if (SV_FAILURE == s32Ret)
                        {
                            print_level(SV_ERROR, "$GPATT process error\n");
                            continue;
                        }
                    }
                }
                else
                {
                    s32GetDataErrCnt ++;
                }

                /* USB HUB掉电异常处理 */
                if (s32GetDataErrCnt > 2)
                {
                    s32GetDataErrCnt = 0;
                    pstGPSInfo->bException = SV_TRUE;
                    print_level(SV_INFO, "gps exception.n");
                }

                memset(szGPSBuf, 0, sizeof(szGPSBuf));
                //print_level(SV_DEBUG, "status :%d ; satinuse :%d sat_count:%d; latitude:%f; longitude %f; direction :%f; PDOP:%f;spk:%f\n",pstGPSInfo->stGPSData.status,pstGPSInfo->stGPSData.satinuse,pstGPSInfo->stGPSData.sat_count,pstGPSInfo->stGPSData.latitude,pstGPSInfo->stGPSData.longitude,pstGPSInfo->stGPSData.direction,pstGPSInfo->stGPSData.PDOP,pstGPSInfo->stGPSData.spk);
            }
        }
    }

    return NULL;
}

/******************************************************************************
 * 函数功能: GPS监控线程
 * 输入参数: pvArg -- 用户传入参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void* gps_MonitorBody(void *pvArg)
{
    sint32 s32Ret = -1, s32ErrCnt = 0;
    GPS_INFO_S *pstGPSInfo = (GPS_INFO_S *)pvArg;
    sint32 s32MainLifeCnt = pstGPSInfo->s32MainLifeCnt;
    sint32 s32NoGPSDataCnt = 0;

    s32Ret = prctl(PR_SET_NAME, "GPS_MonitorBody");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstGPSInfo->bRunning)
    {
        sleep_ms(1000);

        if (GPS_SRC_DVR == pstGPSInfo->stGPSConfig.eGPSSrc)
        {
            pstGPSInfo->s32DvrRecvCnt++;
            if (pstGPSInfo->s32DvrRecvCnt > 6)
            {
                pstGPSInfo->s32DvrRecvCnt = 0;
                memset(&m_stGPSInfo.stDvrGPSData, 0, sizeof(DVR_GPS_DATA_S));
                pstGPSInfo->bRecvData = SV_FALSE;
                pstGPSInfo->stGPSData.signal = 0;
                gps_ChangeGPSScr(pstGPSInfo);
            }
        }

        if (SV_FALSE == pstGPSInfo->bRecvData)
        {
            s32NoGPSDataCnt++;
        }
        else
        {
            s32NoGPSDataCnt = 0;
            pstGPSInfo->stGPSData.bHadData = SV_TRUE;
        }
        /* 5s内连续接受不到数据，不显示GPS图标 */
        if (s32NoGPSDataCnt >= 3 && SV_FALSE == pstGPSInfo->stGPSVirInfo.bEnable)
        {
            s32NoGPSDataCnt = 0;
            pstGPSInfo->stGPSData.bHadData = SV_FALSE;
            print_level(SV_INFO, "gps can't receive data.\n");
        }

        if (s32MainLifeCnt == pstGPSInfo->s32MainLifeCnt)
        {
            s32ErrCnt++;
        }
        else
        {
            s32ErrCnt = 0;
        }
        /* 3S内gps都没有更新，认为线程阻塞 */
        if(s32ErrCnt > 3)
        {
            s32ErrCnt = 0;
            print_level(SV_WARN, "gps send exception\n");
            pstGPSInfo->bException = SV_TRUE;
            s32Ret = gps_DevFini(pstGPSInfo);
        }
        s32MainLifeCnt = pstGPSInfo->s32MainLifeCnt;
    }

}


sint32 GPS_Init(GPS_CONFIG_S *pstGpsConfig)
{
    sint32 s32Ret = SV_FAILURE;
    GPS_VIR_INFO_S stGpsVirInfo = {0};

    memset(&m_stGPSInfo, 0, sizeof(GPS_INFO_S));
    m_stGPSInfo.pstGPRMC = (nmeaGPRMC* )calloc(1, sizeof(nmeaGPRMC));    //推荐定位信息
    m_stGPSInfo.pstGPGSA = (nmeaGPGSA* )calloc(1, sizeof(nmeaGPGSA));    //当前卫星信息
    m_stGPSInfo.pstGPGGA = (nmeaGPGGA* )calloc(1, sizeof(nmeaGPGGA));    //定位信息
    m_stGPSInfo.pstGPGSV = (nmeaGPGSV* )calloc(1, sizeof(nmeaGPGSV));    //可见卫星信息
    m_stGPSInfo.pstGPVTG = (nmeaGPVTG* )calloc(1, sizeof(nmeaGPVTG));    //地面速度信息
    m_stGPSInfo.pstGPATT = (GPATT* )calloc(1, sizeof(GPATT));            //惯性导航信息

    if(NULL == m_stGPSInfo.pstGPRMC || NULL == m_stGPSInfo.pstGPGSA || NULL == m_stGPSInfo.pstGPGGA || NULL == m_stGPSInfo.pstGPGSV || NULL == m_stGPSInfo.pstGPVTG
        || NULL == m_stGPSInfo.pstGPATT)
    {
        print_level(SV_ERROR, "allocate memory error\n");
        exit(-1);
    }
    memcpy(&m_stGPSInfo.stGPSConfig, pstGpsConfig, sizeof(GPS_CONFIG_S));

    /* 上电是否使能GPS时间同步 */
    if (m_stGPSInfo.stGPSConfig.bEnableGPSTime)
    {
        m_stGPSInfo.bTimeSync = SV_TRUE;
    }

    s32Ret = pthread_mutex_init(&m_stGPSInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

#if (defined(BOARD_DMS31V2))
    m_stGPSInfo.stGPSConfig.eGPSSrc = GPS_SRC_SERIAL;
    m_stGPSInfo.eLastGPSSrc = GPS_SRC_SERIAL;
#elif (defined(BOARD_ADA32V2)|| defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1))
    m_stGPSInfo.stGPSConfig.eGPSSrc = GPS_SRC_MCU;
    m_stGPSInfo.eLastGPSSrc = GPS_SRC_MCU;
#else
    m_stGPSInfo.stGPSConfig.eGPSSrc = GPS_SRC_4G;
    m_stGPSInfo.eLastGPSSrc = GPS_SRC_4G;
#endif

    if (pstGpsConfig->s32VirSpeedConfig >= 0)
    {
        stGpsVirInfo.bEnable = SV_TRUE;
        stGpsVirInfo.Speed = pstGpsConfig->s32VirSpeedConfig;

        s32Ret = GPS_SetVirInfo(&stGpsVirInfo);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "GPS_SetVirInfo failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

sint32 GPS_Fini(void)
{
    free(m_stGPSInfo.pstGPRMC);
    free(m_stGPSInfo.pstGPGSA);
    free(m_stGPSInfo.pstGPGGA);
    free(m_stGPSInfo.pstGPGSV);
    free(m_stGPSInfo.pstGPVTG);
    free(m_stGPSInfo.pstGPATT);

    return SV_SUCCESS;
}

sint32 GPS_Start(void)
{
    sint32 s32Ret = 0;
    pthread_t thread;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程

    s32Ret = MSG_ReciverStart(EP_GPS);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_GPS, OP_EVENT_DVR_GPS_DATA, gps_GetDvrGpsData);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_GPS, OP_EVENT_GPS_CHANGE_SRC, gps_ConfigSrc);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stGPSInfo.bRunning = SV_TRUE;
    s32Ret = pthread_create(&thread, &attr, gps_Watch_Body, &m_stGPSInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    m_stGPSInfo.u32Tid = thread;

    s32Ret = pthread_create(&thread, &attr, gps_MonitorBody, &m_stGPSInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    m_stGPSInfo.pMonitorTid = thread;
    pthread_attr_destroy(&attr);


    return SV_SUCCESS;
}

sint32 GPS_Stop(void)
{
    sint32 s32Ret = 0;
    pthread_t thread = m_stGPSInfo.u32Tid;
    void *pvRetval = NULL;

    m_stGPSInfo.bRunning = SV_FALSE;
    //s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    thread = m_stGPSInfo.pMonitorTid;
    //s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 配置gps参数
 * 输入参数: pstSysParam -- 系统配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 GPS_SetConfig(CFG_SYS_PARAM *pstSysParam)
{
    if (NULL == pstSysParam)
    {
        return ERR_NULL_PTR;
    }

    if (SV_TRUE == pstSysParam->bEnableGPStime && SV_FALSE == m_stGPSInfo.stGPSConfig.bEnableGPSTime)
    {
        m_stGPSInfo.bTimeSync = SV_TRUE;
    }
    else
    {
        m_stGPSInfo.bTimeSync = SV_FALSE;
    }

    m_stGPSInfo.bConfig = SV_TRUE;
    m_stGPSInfo.stGPSConfig.bEnableGPSTime = pstSysParam->bEnableGPStime;
    m_stGPSInfo.stGPSConfig.s32TzHour = pstSysParam->s32UTChour;
    m_stGPSInfo.stGPSConfig.s32TzMin = pstSysParam->s32UTCminute;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取GPS信息
 * 输入参数: pstGPSData --- GPS数据信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 GPS_GetInfo(GPS_DATA_S* pstGPSData)
{
    if(NULL == pstGPSData)
    {
        print_level(SV_INFO, "GPS_GetInfo failed.\n");
        return SV_FAILURE;
    }

    pstGPSData->status = m_stGPSInfo.stGPSData.status;
    pstGPSData->signal = m_stGPSInfo.stGPSData.signal;
    pstGPSData->satinuse = m_stGPSInfo.stGPSData.satinuse;
    pstGPSData->sat_count = m_stGPSInfo.stGPSData.sat_count;
    pstGPSData->latitude = m_stGPSInfo.stGPSData.latitude;
    pstGPSData->longitude = m_stGPSInfo.stGPSData.longitude;
    pstGPSData->PDOP = m_stGPSInfo.stGPSData.PDOP;
    pstGPSData->HDOP = m_stGPSInfo.stGPSData.HDOP;
    pstGPSData->VDOP = m_stGPSInfo.stGPSData.VDOP;
    pstGPSData->elv = m_stGPSInfo.stGPSData.elv;
    pstGPSData->spk = m_stGPSInfo.stGPSData.spk;
    pstGPSData->s32INS_state =m_stGPSInfo.stGPSData.s32INS_state;
    pstGPSData->bHadData = m_stGPSInfo.stGPSData.bHadData;

    if(m_stGPSInfo.stGPSVirInfo.bEnable)
    {
        pstGPSData->status = 3;
        pstGPSData->signal = 3;
        pstGPSData->bHadData = SV_TRUE;
        pstGPSData->spk = m_stGPSInfo.stGPSVirInfo.Speed;
    }

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 设置GPS虚拟设备信息
 * 输入参数: pstGPSData --- GPS虚拟设备信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 GPS_SetVirInfo(GPS_VIR_INFO_S *pstGpsVirInfo)
{
    if(NULL == pstGpsVirInfo)
    {
        return SV_FAILURE;
    }

    m_stGPSInfo.stGPSVirInfo.bEnable = pstGpsVirInfo->bEnable;
    m_stGPSInfo.stGPSVirInfo.Speed   = pstGpsVirInfo->Speed;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取GPS虚拟设备信息
 * 输入参数: pstGPSData --- GPS虚拟设备信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 GPS_GetVirInfo(GPS_VIR_INFO_S *pstGpsVirInfo)
{
    if(NULL == pstGpsVirInfo)
    {
        return SV_FAILURE;
    }

    pstGpsVirInfo->bEnable = m_stGPSInfo.stGPSVirInfo.bEnable;
    pstGpsVirInfo->Speed   = m_stGPSInfo.stGPSVirInfo.Speed;

    return SV_SUCCESS;
}

