/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_venc.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-11-22

文件功能描述: 封装海思MPP视频编码模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _MPP_VO_H_
#define _MPP_VO_H_

#include "common.h"
#include "mpp_com.h"
#include "media.h"

#ifdef __cplusplus
#if _cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

typedef struct tagVOCfg_S
{
    uint32              u32ChnNum;      /* 输入通道数目 */
    SV_ROT_ANGLE_E      enRotateAngle;  /* 画面旋转角度 */
    SV_SIZE_S           stInSize;       /* 输入尺寸 */
    SV_SIZE_S           stOutSize;      /* 输出尺寸 */
    uint32              u32Fps;         /* 输出帧率 */
    sint32              s32AlgType;     /* 算法类型 */
    SV_BOOL             bRoiGreen;      /* 区域使能 绿色 */
    SV_BOOL             bRoiYellow;     /* 区域使能 黄色 */
    SV_BOOL             bRoiRed;        /* 区域使能 红色*/
    SV_BOOL             bOsdEnable;     /* ROI区域使能 */
    sint32              enRoiGui;       /* 区域排布方式(0: 红区在底部, 1:红区在左边, 2:红区在右边, 3:圆形ROI区域) */
    sint32              enRoiStyle;     /* 行人算法排布样式 */
    SV_POINT2_S         astPdPoints[8]; /* 标定线 */
    SPLIT_MODE          enSplitMode;    /* 分割模式 */
    VALG_EXTERN_S       stAlgExtern[MEDIA_CHNNUM];  /* 算法扩展参数 */
} MPP_VO_CONF_S;

/******************************************************************************
 * 函数功能: 初始化VO模块
 * 输入参数: pstVoConf --- 视频输出配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_Init(MPP_VO_CONF_S *pstVoConf);

/******************************************************************************
 * 函数功能: 去初始化VO模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_Fini();

/******************************************************************************
 * 函数功能: 启动VO模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_Start();

/******************************************************************************
 * 函数功能: 停止VO模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_Stop();

/******************************************************************************
 * 函数功能: 暂停VO模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
#if 0
extern sint32 mpp_vo_Interrupt(SV_BOOL bInterrupt, VIDEO_VO_S *pstVoAttr);
#else
extern sint32 mpp_vo_Interrupt(SV_BOOL bInterrupt);
#endif

/******************************************************************************
 * 函数功能: 配置Primary画板的属性
 * 输入参数: bGreen     绿色GUI区域使能
             bYellow    黄色GUI区域使能
             bRed       红色GUI区域使能
             s32PdGui   GUI绘制样式
             s32PdRoi   画布排布样式
             pdPoint    标定线(八个点)
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_SetPrimaryStr(VALG_EXTERN_S *pstAlgExtern);

/******************************************************************************
 * 函数功能: HDW845配置行人检测报警区域的属性
 * 输入参数: stAlarmRoi     “回”型行人检测区域
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_SetAlarmRoi(ZOOM_ALARM_ROI       stZoomAlarmRoi);


/******************************************************************************
 * 函数功能: 设置通道画面顺时针旋转角度
 * 输入参数: s32Chn --- VPSS通道号
             s32Rotate --- 旋转角度[0-3]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_SetChnRotate(sint32 s32Chn, SV_ROT_ANGLE_E enAngle);

/******************************************************************************
 * 函数功能: 重新配置VO的分辨率
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_ReCreateChannel(VIDEO_VO_S *pstVoAttr);

/******************************************************************************
 * 函数功能: Primary画板的属性更新
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vo_Primary_Update();


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif

#endif

