/******************************************************************************
Copyright (C) 2018-2020 广州敏视数码科技有限公司版权所有.

文件名：wifi.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2018-05-30

文件功能描述: wifi模块功能函数

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <errno.h>
#include <regex.h>
#include <linux/wireless.h>
#include <linux/in.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <error.h>


#include "print.h"
#include "op.h"
#include "msg.h"
#include "../../../include/board.h"
#include "safefunc.h"
#include "wifi.h"
#include "rtl8811.h"
#include "wf61.h"
#include "mt7601u.h"
#include "rtl8723.h"
//#include "mpi_sys.h"
#include "safefunc.h"
#include "cjson/cJSON.h"

const char *m_aszCountryCodes[229] = {
"AD",   /* Andorra */
"AE",   /* United Arab Emirates */
"AF",   /* Afghanistan */
"AG",   /* Antigua & Barbuda */
"AI",   /* Anguilla(UK) */
"AL",   /* Albania */
"AM",   /* Armenia */
"AO",   /* Angola */
"AQ",   /* Antarctica */
"AR",   /* Argentina */
"AS",   /* American Samoa */
"AT",   /* Austria */
"AU",   /* Australia */
"AW",   /* Aruba */
"AZ",   /* Azerbaijan */
"BA",   /* Bosnia & Herzegovina */
"BB",   /* Barbados */
"BD",   /* Bangladesh */
"BE",   /* Belgium */
"BF",   /* Burkina Faso */
"BG",   /* Bulgaria */
"BH",   /* Bahrain */
"BI",   /* Burundi */
"BJ",   /* Benin */
"BN",   /* Brunei */
"BO",   /* Bolivia */
"BR",   /* Brazil */
"BS",   /* Bahamas */
"BW",   /* Botswana */
"BY",   /* Belarus */
"BZ",   /* Belize */
"CA",   /* Canada */
"CC",   /* Cocos (Keeling) Islands (Australia) */
"CD",   /* Congo, Republic of the */
"CF",   /* Central African Republic */
"CG",   /* Congo, Democratic Republic of the. Zaire */
"CH",   /* Switzerland */
"CI",   /* Cote d'Ivoire */
"CK",   /* Cook Islands */
"CL",   /* Chile */
"CM",   /* Cameroon */
"CN",   /* China */
"CO",   /* Colombia */
"CR",   /* Costa Rica */
"CV",   /* Cape Verde */
"CX",   /* Christmas Island (Australia) */
"CY",   /* Cyprus */
"CZ",   /* Czech Republic */
"DE",   /* Germany */
"DJ",   /* Djibouti */
"DK",   /* Denmark */
"DM",   /* Dominica */
"DO",   /* Dominican Republic */
"DZ",   /* Algeria */
"EC",   /* Ecuador */
"EE",   /* Estonia */
"EG",   /* Egypt */
"EH",   /* Western Sahara */
"ER",   /* Eritrea */
"ES",   /* Spain, Canary Islands, Ceuta, Melilla */
"ET",   /* Ethiopia */
"FI",   /* Finland */
"FJ",   /* Fiji */
"FK",   /* Falkland Islands (Islas Malvinas) (UK) */
"FM",   /* Micronesia, Federated States of (USA) */
"FO",   /* Faroe Islands (Denmark) */
"FR",   /* France */
"GA",   /* Gabon */
"GB",   /* Great Britain (United Kingdom; England) */
"GD",   /* Grenada */
"GE",   /* Georgia */
"GF",   /* French Guiana */
"GG",   /* Guernsey (UK) */
"GH",   /* Ghana */
"GI",   /* Gibraltar (UK) */
"GL",   /* Greenland (Denmark) */
"GM",   /* Gambia */
"GN",   /* Guinea */
"GP",   /* Guadeloupe (France) */
"GQ",   /* Equatorial Guinea */
"GR",   /* Greece */
"GS",   /* South Georgia and the Sandwich Islands (UK) */
"GT",   /* Guatemala */
"GU",   /* Guam (USA) */
"GW",   /* Guinea-Bissau */
"GY",   /* Guyana */
"HK",   /* Hong Kong */
"HM",   /* Heard and McDonald Islands (Australia) */
"HN",   /* Honduras */
"HR",   /* Croatia */
"HT",   /* Haiti */
"HU",   /* Hungary */
"ID",   /* Indonesia */
"IE",   /* Ireland */
"IL",   /* Israel */
"IM",   /* Isle of Man (UK) */
"IN",   /* India */
"IQ",   /* Iraq */
"IR",   /* Iran */
"IS",   /* Iceland */
"IT",   /* Italy */
"JE",   /* Jersey (UK) */
"JM",   /* Jamaica */
"JO",   /* Jordan */
"JP",   /* Japan- Telec */
"KE",   /* Kenya */
"KG",   /* Kyrgyzstan */
"KH",   /* Cambodia */
"KI",   /* Kiribati */
"KN",   /* Saint Kitts and Nevis */
"KR",   /* South Korea */
"KW",   /* Kuwait */
"KY",   /* Cayman Islands (UK) */
"KZ",   /* Kazakhstan */
"LA",   /* Laos */
"LB",   /* Lebanon */
"LC",   /* Saint Lucia */
"LI",   /* Liechtenstein */
"LK",   /* Sri Lanka */
"LR",   /* Liberia */
"LS",   /* Lesotho */
"LT",   /* Lithuania */
"LU",   /* Luxembourg */
"LV",   /* Latvia */
"LY",   /* Libya */
"MA",   /* Morocco */
"MC",   /* Monaco */
"MD",   /* Moldova */
"ME",   /* Montenegro */
"MF",   /* Saint Martin */
"MG",   /* Madagascar */
"MH",   /* Marshall Islands (USA) */
"MK",   /* Republic of Macedonia (FYROM) */
"ML",   /* Mali */
"MM",   /* Burma (Myanmar) */
"MN",   /* Mongolia */
"MO",   /* Macau */
"MP",   /* Northern Mariana Islands (USA) */
"MQ",   /* Martinique (France) */
"MR",   /* Mauritania */
"MS",   /* Montserrat (UK) */
"MT",   /* Malta */
"MU",   /* Mauritius */
"MV",   /* Maldives */
"MW",   /* Malawi */
"MX",   /* Mexico */
"MY",   /* Malaysia */
"MZ",   /* Mozambique */
"NA",   /* Namibia */
"NC",   /* New Caledonia */
"NE",   /* Niger */
"NF",   /* Norfolk Island (Australia) */
"NG",   /* Nigeria */
"NI",   /* Nicaragua */
"NL",   /* Netherlands */
"NO",   /* Norway */
"NP",   /* Nepal */
"NR",   /* Nauru */
"NU",   /* Niue */
"NZ",   /* New Zealand */
"OM",   /* Oman */
"PA",   /* Panama */
"PE",   /* Peru */
"PF",   /* French Polynesia (France) */
"PG",   /* Papua New Guinea */
"PH",   /* Philippines */
"PK",   /* Pakistan */
"PL",   /* Poland */
"PM",   /* Saint Pierre and Miquelon (France) */
"PR",   /* Puerto Rico */
"PT",   /* Portugal */
"PW",   /* Palau */
"PY",   /* Paraguay */
"QA",   /* Qatar */
"RE",   /* Reunion (France) */
"RO",   /* Romania */
"RS",   /* Serbia, Kosovo */
"RU",   /* Russia(fac/gost), Kaliningrad */
"RW",   /* Rwanda */
"SA",   /* Saudi Arabia */
"SB",   /* Solomon Islands */
"SC",   /* Seychelles */
"SE",   /* Sweden */
"SG",   /* Singapore */
"SH",   /* Saint Helena (UK) */
"SI",   /* Slovenia */
"SJ",   /* Svalbard (Norway) */
"SK",   /* Slovakia */
"SL",   /* Sierra Leone */
"SM",   /* San Marino */
"SN",   /* Senegal */
"SO",   /* Somalia */
"SR",   /* Suriname */
"ST",   /* Sao Tome and Principe */
"SV",   /* El Salvador */
"SX",   /* Sint Marteen */
"SZ",   /* Swaziland */
"TC",   /* Turks and Caicos Islands (UK) */
"TD",   /* Chad */
"TF",   /* French Southern and Antarctic Lands (FR Southern Territories) */
"TG",   /* Togo */
"TH",   /* Thailand */
"TJ",   /* Tajikistan */
"TK",   /* Tokelau */
"TM",   /* Turkmenistan */
"TN",   /* Tunisia */
"TO",   /* Tonga */
"TR",   /* Turkey, Northern Cyprus */
"TT",   /* Trinidad & Tobago */
"TW",   /* Taiwan */
"TZ",   /* Tanzania */
"UA",   /* Ukraine */
"UG",   /* Uganda */
"US",   /* United States of America (USA) */
"UY",   /* Uruguay */
"UZ",   /* Uzbekistan */
"VA",   /* Holy See (Vatican City) */
"VC",   /* Saint Vincent and the Grenadines */
"VE",   /* Venezuela */
"VI",   /* United States Virgin Islands (USA) */
"VN",   /* Vietnam */
"VU",   /* Vanuatu */
"WF",   /* Wallis and Futuna (France) */
"WS",   /* Samoa */
"YE",   /* Yemen */
"YT",   /* Mayotte (France) */
"ZA",   /* South Africa */
"ZM",   /* Zambia */
"ZW",   /* Zimbabwe */
};

/* wifi模块初始化状态 */
typedef enum tagWifiInitStat_E
{
    WIFI_NOT_INIT = 0,
    WIFI_INITING,
    WIFI_INITED,
    WIFI_INIT_FAIL,

    WIFI_INIT_BUTT
} WIFI_INIT_STAT;

/* wifi 模块控制信息 */
typedef struct tag_WifiComInfo_S
{
    WIFI_CONF_S stWifiConf;     /* 工作参数 */
    WIFI_ID_E   enWifiId;       /* 型号ID */
    uint32      u32TID;         /* 守护线程ID */
    SV_BOOL     bRunning;       /* 线程是否正在运行 */
    SV_BOOL     bAutoTesting;   /* 是否正在进行自动化测试 */
	SV_BOOL		bApConfUpdate;  /* WIFI AP 参数是否改变 */
	SV_BOOL		bStaConfUpdate; /* WIFI STA 参数是否改变 */
	SV_BOOL		bStaUpdate;     /* WIFI STA IP和网关参数是否改变 */
	SV_BOOL		bSkipDetecting;	/* 线程是否正在监测wifi模块状态 */
    pthread_mutex_t mutexLock;  /* 参数设置线程互斥锁 */
} WIFI_COM_INFO_S;

WIFI_COM_INFO_S m_stWifiInfo;   /* 模块控制信息 */

SV_BOOL m_WifiException = SV_FALSE;     /* 是否出现WiFi模块异常 */

sint32 wifi_SubmitNetworkStat(SV_BOOL bHasWifi)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    NETWORK_STAT_S stNetworkStat = {0};

    stNetworkStat.enNetworkType = NETWORK_TYPE_WIFI;
    stNetworkStat.bExist = bHasWifi;
    stMsgPkt.pu8Data = (uint8*)&stNetworkStat;
    stMsgPkt.u32Size = sizeof(NETWORK_STAT_S);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_NETWORK_STAT, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
    }

#if (defined(BOARD_WFCR20S2))
	s32Ret = Msg_submitEvent(EP_SOMEIPSERVER, OP_EVENT_NETWORK_STAT, &stMsgPkt);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "Msg_submitEvent EP_SOMEIPSERVER failed. [err=%#x]\n", s32Ret);
	}
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解析16进制字符串转换为整型数值
 * 输入参数: pszStr --- 16进制字符串
 * 输出参数: 无
 * 返回值  : 整型数值
             -1 - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 wifi_htoi(char *pszStr)
{
    sint32 s32Val = 0, i;
    sint32 s32Len = 0;

    if (NULL == pszStr)
    {
        return -1;
    }

    s32Len = strlen(pszStr);
    if (s32Len <= 0)
    {
        print_level(SV_ERROR, "invaled number string.\n");
        return -1;
    }

    for (i = 0; i < s32Len; i++)
    {
        if (pszStr[i] == '\r' || pszStr[i] == '\n')
        {
            break;
        }

        if (pszStr[i] == '0' && (pszStr[i+1] == 'x' || pszStr[i+1] == 'X'))
        {
            i++;
            continue;
        }

        if (pszStr[i] >= '0' && pszStr[i] <= '9')
        {
            s32Val = s32Val * 16 + (pszStr[i] - '0');
        }
        else if (pszStr[i] >= 'a' && pszStr[i] <= 'f')
        {
            s32Val = s32Val * 16 + (pszStr[i] - 'a') + 10;
        }
        else if (pszStr[i] >= 'A' && pszStr[i] <= 'F')
        {
            s32Val = s32Val * 16 + (pszStr[i] - 'A') + 10;
        }
        else
        {
            print_level(SV_ERROR, "invaled number string.\n");
            return -1;
        }
    }

    return s32Val;
}

/******************************************************************************
 * 函数功能: 复位WIFI模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 wifi_ResetPower()
{
    BOARD_SetWifiPower(SV_FALSE);
    sleep_ms(1000);
    BOARD_SetWifiPower(SV_TRUE);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取wifi模块的VID
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 模块的VID
             -1 - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 wifi_GetModuleVid()
{
    sint32 s32Len = 0;
    sint32 s32Fd = 0;
    sint32 s32Vid = 0;
	char szCmd[128] = {0};
	char szVid[32] = {0};

#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))
    s32Fd = open(USB_ModuleVid, O_RDONLY);
    if (s32Fd < 0)
    {
        s32Fd = open(SDIO_ModuleVid, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open idVendor file failed. [err=%#x]\n", errno);
            return -1;
        }
    }

    s32Len = read(s32Fd, szVid, 31);
    if (s32Len <= 0)
    {
        print_level(SV_ERROR, "read idVendor file failed. [err=%#x]\n", errno);
        close(s32Fd);
        return -1;
    }
    close(s32Fd);
#else
	sprintf(szCmd, "lsusb | grep -E '%s|%s|%s|%s|%s|%s|%s'| awk '{printf $(NF)}' | awk -F ':' '{printf $1}'", MT7601_PID, RTL8811_PID, RTL8821_PID, CVTE_8188_PID, FU_8188_PID, EU_8188_PID, DU_8723_PID);
	SAFE_System_Recv(szCmd, szVid, 32);
	if (strlen(szVid) == 0)
	{
		print_level(SV_WARN, "wifi module is not exist.\n");
		return -1;
	}
	s32Len = 31;
#endif

    szVid[s32Len] = '\0';
    s32Vid = wifi_htoi(szVid);

    return s32Vid;
}

/******************************************************************************
 * 函数功能: 获取wifi模块的PID
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 模块的PID
             -1 - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 wifi_GetModulePid()
{
    sint32 s32Len = 0;
    sint32 s32Fd = 0;
    sint32 s32Pid = 0;
	char szCmd[128] = {0};
	char szPid[32] = {0};
	static sint32 errCnt = 0;

#if 0
    s32Fd = open(USB_ModulePid, O_RDONLY);
    if (s32Fd < 0)
    {
        s32Fd = open(SDIO_ModulePid, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open idProduct file failed. [err=%#x]\n", errno);
            return -1;
        }
    }

    s32Len = read(s32Fd, szPid, 31);
    if (s32Len <= 0)
    {
        print_level(SV_ERROR, "read idProduct file failed. [err=%#x]\n", errno);
        close(s32Fd);
        return -1;
    }
    close(s32Fd);
#else
	sprintf(szCmd, "lsusb | grep -E '%s|%s|%s|%s|%s|%s|%s|%s' | awk '{printf $(NF)}' | awk -F ':' '{printf $2}'", MT7601_PID, RTL8811_PID, RTL8821_PID, CVTE_8188_PID, FU_8188_PID, EU_8188_PID, DU_8723_PID,FU_8192_PID);
	SAFE_System_Recv(szCmd, szPid, 32);
	if (strlen(szPid) == 0)
	{
		if (++errCnt > 10)
		{
			errCnt = 0;
			print_level(SV_WARN, "wifi module is not exist.\n");
		}
		return -1;
	}
	s32Len = 31;
#endif

    szPid[s32Len] = '\0';
    s32Pid = wifi_htoi(szPid);

    return s32Pid;
}

SV_BOOL wifi_IsValidIPAddress(const char *ip)
{
    sint32 i = 0, s32Num, s32Dots = 0;
    char *ptr;
    char *pszSaveptr;  // strtok_r 的上下文指针

    // 如果字符串为空
    if (ip == NULL)
        return SV_FALSE;

    // 创建字符串的副本，以避免 strtok_r 修改原字符串
    char *pszIpbak = strdup(ip);
    if (pszIpbak == NULL)
        return SV_FALSE;

    // 使用 strtok_r 逐段分析，每个段必须是 0-255 之间的整数
    ptr = strtok_r(pszIpbak, ".", &pszSaveptr);
    while (NULL != ptr)
    {
        // 检查是否是纯数字
        for (i = 0; ptr[i] != '\0'; i++)
        {
            if (!isdigit(ptr[i]))
            {
                free(pszIpbak); // 释放内存
                return SV_FALSE;
            }
        }

        // 转换成数字并检查范围
        s32Num = atoi(ptr);
        if (s32Num < 0 || s32Num > 255)
        {
            free(pszIpbak); // 释放内存
            return SV_FALSE;
        }

        // 检查点数目
        ptr = strtok_r(NULL, ".", &pszSaveptr);
        s32Dots++;
    }

    // 有效的 IPv4 地址应该有 4 个部分，并且 3 个点
    free(pszIpbak); // 释放内存
    return (s32Dots == 4);
}

/******************************************************************************
 * 函数功能: 判断wifi模块是否存在
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL wifi_IsModuleExist()
{
#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))
    if (0 == access(USB_ModulePid, F_OK))
#else
    if (-1 != wifi_GetModulePid())
#endif
        return SV_TRUE;
    else if(0 == access(SDIO_ModulePid, F_OK))
        return SV_TRUE;
    else
        return SV_FALSE;
}

/******************************************************************************
 * 函数功能: 判断wifi是否正在运行
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL wifi_IsRunning()
{
	sint32 s32Ret;
	char szCmd[64] = {0};
	char szbuf[1024] = {0};

	strcpy(szCmd, "iwconfig 2> /dev/null");
	s32Ret = SAFE_System_Recv(szCmd, szbuf, 1024);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "SAFE_System_Recv %s failed.\n", szCmd);
		return SV_FALSE;
	}

	if (strstr(szbuf, "ESSID") != NULL)
		return SV_TRUE;
	else
		return SV_FALSE;
}

/******************************************************************************
 * 函数功能: 判断wifi驱动是否插入
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL wifi_IsKoInsert()
{
    if (0 == access(RTL8821Proc, F_OK))
        return SV_TRUE;
    else if(0 == access(RTL8811Proc, F_OK))
        return SV_TRUE;
    else if(0 == access(RTL8188FuProc, F_OK))
        return SV_TRUE;
	else if(0 == access(RTL8188GtvuProc, F_OK))
        return SV_TRUE;
	else if(0 == access(RTL8723Proc, F_OK))
        return SV_TRUE;
    else
        return SV_FALSE;
}

/******************************************************************************
 * 函数功能: 检测wifi设备型号
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : wifi设备型号
 * 注意    : 无
 *****************************************************************************/
WIFI_ID_E wifi_ProbeDevice()
{
    sint32 i;
    sint32 s32Pid = 0;
    char *pszModuleName = NULL;

    for (i = 0; i < 10; i++)
    {
        s32Pid = wifi_GetModulePid();
        if (s32Pid != -1)
        {
            break;
        }
        print_level(SV_WARN, "wifi device is unexist.\n");
        sleep_ms(1000);
    }
    if (i >= 10)
    {
        print_level(SV_ERROR, "can not probe wifi module during 10 second.\n");
        return WIFI_ID_UNKNOWN;
    }

    if (0x0811 == s32Pid)
    {
        return WIFI_ID_RTL8811;
    }
    else if (0x2281 == s32Pid)
    {
        return WIFI_ID_WF61;
    }
    else if (0x1022 == s32Pid || 0x9374 == s32Pid)
    {
        return WIFI_ID_AR1021;
    }
	else if (0xc820 == s32Pid || 0xb820 == s32Pid || 0xc811 == s32Pid)
    {
        return WIFI_ID_RTL8821;
    }
    else if (0x7601 == s32Pid)
    {
        return WIFI_ID_MT7601;
    }
	else if (0xf179 == s32Pid)
	{
		return WIFI_ID_RTL8188FU;
	}
	else if (0x018c == s32Pid)
	{
		return WIFI_ID_RTL8188CVTE;
	}
    else if (0x8179 == s32Pid)
	{
		return WIFI_ID_RTL8188EU;
	}
    else if (0xd723 == s32Pid)
	{
	    print_level(SV_INFO, "wifi device is RTL8723.\n");
		return WIFI_ID_RTL8723DU;
	}
	else if(0xf192 == s32Pid)
	{
		return WIFI_ID_RTL8192FU;
	}
    else
    	return WIFI_ID_UNKNOWN;
}

/******************************************************************************
 * 函数功能: 获取自动化测试的STA SSID、STA password、ip
 * 输入参数: pszInterface --- 网卡名字
 * 输出参数: pszGatewayIp --- 网关IP地址
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 wifi_GetAutoTestIpAddr(char *pszStaSsid, char *pszStaPwd, char *pszStaIpAddr)
{
    sint32 s32Ret = 0;
    SV_BOOL bException = SV_FALSE;
	char szJsonRet[1024] = {0};
    cJSON *pstJson = NULL, *pstWifi = NULL;
	cJSON *pstStaSsid = NULL;
	cJSON *pstStaPwd = NULL;
	cJSON *pstStaIpAddr = NULL;

    /* 解析JSON字段 */
    s32Ret = cJSON_GetJson(AUTOTEST_IP, szJsonRet);
	if (SV_SUCCESS != s32Ret)
	{
		return s32Ret;
	}

    pstJson = cJSON_Parse(szJsonRet);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return SV_FAILURE;
    }

    pstWifi = cJSON_GetObjectItemCaseSensitive(pstJson, "wifi");
    if (NULL == pstWifi)
    {
        print_level(SV_ERROR, "keyword wifi is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }

    pstStaSsid = cJSON_GetObjectItemCaseSensitive(pstWifi, "sta_ssid");
    if (NULL == pstStaSsid)
    {
        print_level(SV_ERROR, "keyword sta_ssid is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }

    pstStaPwd = cJSON_GetObjectItemCaseSensitive(pstWifi, "sta_pwd");
    if (NULL == pstStaPwd)
    {
        print_level(SV_ERROR, "keyword sta_pwd is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }

    pstStaIpAddr = cJSON_GetObjectItemCaseSensitive(pstWifi, "sta_ip");
    if (NULL == pstStaIpAddr)
    {
        print_level(SV_ERROR, "keyword sta_ip is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }


    print_level(SV_INFO, "sta_ssid=%s\n", pstStaSsid->valuestring);
    print_level(SV_INFO, "sta_pwd=%s\n", pstStaPwd->valuestring);
    print_level(SV_INFO, "sta_ip=%s\n", pstStaIpAddr->valuestring);


    snprintf(pszStaSsid, 32, "%s", pstStaSsid->valuestring);
    snprintf(pszStaPwd, 32, "%s", pstStaPwd->valuestring);
    snprintf(pszStaIpAddr, 32, "%s", pstStaIpAddr->valuestring);

exit:
    cJSON_Delete(pstJson);
    return SV_SUCCESS;
}

sint32 wifi_GetStaIpaddress(char *pszStaIp)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    struct ifreq stIfr;
    struct sockaddr_in stAddr;
    uint32 u32IpAddr = 0;
    char szStaIpaddr[32] = {0};

    s32Fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "socket init failed.\n");
        return SV_FAILURE;
    }

    strcpy(stIfr.ifr_name, "wlan1");
    s32Ret = ioctl(s32Fd, SIOCGIFADDR, &stIfr);
    close(s32Fd);
    if (s32Ret < 0)
    {
        return SV_FAILURE;
    }
    memcpy(&stAddr, &stIfr.ifr_addr, sizeof(stAddr));
    u32IpAddr = (uint32)stAddr.sin_addr.s_addr;

    char *pcIpaddr = (char *)&u32IpAddr;
    sprintf(szStaIpaddr, "%d.%d.%d.%d", pcIpaddr[0], pcIpaddr[1], pcIpaddr[2], pcIpaddr[3]);
    strcpy(pszStaIp, szStaIpaddr);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检测维护设备运行
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/

void * wifi_Daemon_Body(void *pvArg)
{
    sint32 s32Ret = 0, i;
    SV_BOOL bFirstLoop = SV_TRUE;
    SV_BOOL bWifiInited = SV_FALSE;
    SV_BOOL bAutoTestStaIpSet = SV_FALSE;
    uint32 u32ResetCnt = 0;
    uint32 u32ExceptionCnt = 0;
	long lSetChannel = 0;
	SV_BOOL bhostapd = SV_FALSE;
    char szCmd[64];
    char szTmp[64];
    char szRecvBuf[256];
    char szLogBuf[1024];
    char szGateway[32] = {0};
    WIFI_COM_INFO_S *pstWifiInfo = (WIFI_COM_INFO_S *)pvArg;

    char szStaSsid[32] = {0};
    char szStaPwd[32] = {0};
    char szStaIpAddr[32] = {0};

    s32Ret = prctl(PR_SET_NAME, "wifi_daemon_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstWifiInfo->bRunning)
    {
        //print_level(SV_DEBUG, "wifi_Daemon_Body running...\n");
        if (!bFirstLoop)
        {
            sleep_ms(1000);
        }

        if (pstWifiInfo->bSkipDetecting)
        {
            sleep_ms(500);
            print_level(SV_INFO, "wifi module is restarting, waiting for detecting...\n");
            continue;
        }

        if (wifi_IsModuleExist() && !m_WifiException)
        {
            if (!bWifiInited)
            {
                print_level(SV_INFO, "detected wifi module.\n");
                #if defined(PLATFORM_RV1106)
                wifi_SubmitNetworkStat(SV_TRUE);
                #endif
                pstWifiInfo->enWifiId = wifi_ProbeDevice();
                if (WIFI_ID_UNKNOWN == pstWifiInfo->enWifiId)
                {
                    print_level(SV_ERROR, "wifi_ProbeDevice failed.\n");
                    bFirstLoop = SV_FALSE;
                    continue;
                }

                switch (pstWifiInfo->enWifiId)
                {
                    case WIFI_ID_RTL8811:
                        s32Ret = RTL8811_Init(&pstWifiInfo->stWifiConf);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "RTL8811_Init failed. [err=%#x]\n", s32Ret);
                        }
                        break;

                    case WIFI_ID_WF61:
                        s32Ret = wf61_Init(&pstWifiInfo->stWifiConf);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "wf61_Init failed. [err=%#x]\n", s32Ret);
                        }
                        break;

                    case WIFI_ID_AR1021:
                        s32Ret = AR1021_Init(&pstWifiInfo->stWifiConf);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "AR1021_Init failed. [err=%#x]\n", s32Ret);
                        }
                        break;

                    case WIFI_ID_RTL8821:
                        s32Ret = RTL8821_Init(&pstWifiInfo->stWifiConf);
                        if(SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "RTL8821_Init failed. [err=%#x]\n", s32Ret);
                        }
                        break;

                    case WIFI_ID_MT7601:
                        s32Ret = MT7601U_Init(&pstWifiInfo->stWifiConf);
                        if(SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "MT7601U_Init failed. [err=%#x]\n", s32Ret);
                        }
                    	break;

                    case WIFI_ID_RTL8188FU:
                    case WIFI_ID_RTL8188CVTE:
                    case WIFI_ID_RTL8188EU:
                        s32Ret = RTL8188_Init(&pstWifiInfo->stWifiConf, pstWifiInfo->enWifiId);
                        if(SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "RTL8188FU_Init failed. [err=%#x]\n", s32Ret);
                        }
                        break;

                    case WIFI_ID_RTL8723DU:
                        s32Ret = RTL8723_Init(&pstWifiInfo->stWifiConf, pstWifiInfo->enWifiId);
                        if(SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "RTL8723DU_Init failed. [err=%#x]\n", s32Ret);
                        }
                        break;
					case WIFI_ID_RTL8192FU:
						s32Ret = RTL8192_Init(&pstWifiInfo->stWifiConf, pstWifiInfo->enWifiId);
                        if(SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "RTL8723DU_Init failed. [err=%#x]\n", s32Ret);
                        }
                        break;
                    default:
                        print_level(SV_WARN, "unknown wifi module.\n");
                        break;
                }

                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "RTL Init failed. [err=%#x]\n", s32Ret);
                    continue;
                }

                bWifiInited = SV_TRUE;
				pstWifiInfo->bApConfUpdate = SV_FALSE;
				pstWifiInfo->bStaConfUpdate = SV_FALSE;

#if (defined(BOARD_WFCR20S2))
				wifi_SubmitNetworkStat(SV_TRUE);
#endif

                if(bWifiInited && !wifi_IsKoInsert() && ++u32ExceptionCnt > 3)
                {
                    m_WifiException = SV_TRUE;
                    u32ExceptionCnt = 0;
                }


                if (pstWifiInfo->bAutoTesting && !bAutoTestStaIpSet)
                {
                    s32Ret = wifi_GetAutoTestIpAddr(szStaSsid, szStaPwd, szStaIpAddr);
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "wifi_GetAutoTestIpAddr failed.\n");
                    }
                    else
                    {
                        print_level(SV_INFO, "wifi_GetAutoTestIpAddr success.\n");
                        pstWifiInfo->bStaConfUpdate = SV_TRUE;
                        pstWifiInfo->stWifiConf.bWifiStaEnable = SV_TRUE;

                        strncpy(pstWifiInfo->stWifiConf.szWifiStaSsid, szStaSsid, 32);
                        strncpy(pstWifiInfo->stWifiConf.szWifiStaPwd, szStaPwd, 32);
                        strncpy(pstWifiInfo->stWifiConf.szWifiStaIpAddr, szStaIpAddr, 32);
                    }
                }
            }
			else
			{
				bhostapd = SV_FALSE;
				if (pstWifiInfo->bApConfUpdate)
				{
                    print_level(SV_INFO, "ap config has been changed.\n");
					switch (pstWifiInfo->enWifiId)
			        {
			            case WIFI_ID_RTL8811:
			                lSetChannel = pstWifiInfo->stWifiConf.enWifiFreq == WIFI_FREQ_2_4G ? pstWifiInfo->stWifiConf.lSet2GChannel : pstWifiInfo->stWifiConf.lSet5GChannel;
			                s32Ret = rtl8811_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApIpAddr , pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq, lSetChannel);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "rtl8811_AP_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
			            case WIFI_ID_RTL8821:
			                lSetChannel = pstWifiInfo->stWifiConf.enWifiFreq == WIFI_FREQ_2_4G ? pstWifiInfo->stWifiConf.lSet2GChannel : pstWifiInfo->stWifiConf.lSet5GChannel;
			                s32Ret = rtl8821_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApIpAddr, pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq, lSetChannel);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "rtl8821_AP_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
			            case WIFI_ID_WF61:
			                s32Ret = wf61_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "wf61_AP_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
			            case WIFI_ID_AR1021:
			                s32Ret = ar1021_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "rtl8811_AP_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
			            case WIFI_ID_MT7601:
			                lSetChannel = pstWifiInfo->stWifiConf.lSet2GChannel;
			                s32Ret = mt7601_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq, lSetChannel);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "mt7601_AP_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
						case WIFI_ID_RTL8188FU:
                        case WIFI_ID_RTL8188CVTE:
                        case WIFI_ID_RTL8188EU:
							RTL8188_SetWifiId(pstWifiInfo->enWifiId);
							s32Ret = rtl8188_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApIpAddr, pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq, lSetChannel);
						    if (SV_SUCCESS != s32Ret)
						    {
						        print_level(SV_ERROR, "rtl8188_AP_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
						    }
							break;
                        case WIFI_ID_RTL8723DU:
                            RTL8723_SetWifiId(pstWifiInfo->enWifiId);
                            s32Ret = rtl8723_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApIpAddr , pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq, lSetChannel);
                            if (SV_SUCCESS != s32Ret)
                            {
                                print_level(SV_ERROR, "rtl8723_AP_Config failed. [err=%#x]\n", s32Ret);
                                continue;
                            }
                            break;
						case WIFI_ID_RTL8192FU:
	                        RTL8192_SetWifiId(pstWifiInfo->enWifiId);
							lSetChannel = pstWifiInfo->stWifiConf.enWifiFreq == WIFI_FREQ_2_4G ? pstWifiInfo->stWifiConf.lSet2GChannel : pstWifiInfo->stWifiConf.lSet5GChannel;
	                        s32Ret = rtl8192_AP_Config(pstWifiInfo->stWifiConf.enWifiAuth, pstWifiInfo->stWifiConf.szWifiApSsid, pstWifiInfo->stWifiConf.szWifiApIpAddr , pstWifiInfo->stWifiConf.szWifiApPwd, pstWifiInfo->stWifiConf.enWifiFreq, lSetChannel);
	                        if (SV_SUCCESS != s32Ret)
	                        {
	                            print_level(SV_ERROR, "rtl8192_AP_Config failed. [err=%#x]\n", s32Ret);
	                            continue;
	                        }
	                        break;

                        default:
                            print_level(SV_WARN, "unknown wifi module.\n");
                            break;
			        }

					pstWifiInfo->bApConfUpdate = SV_FALSE;
				}

                /* 直接使用静态IP的方式，需要静态IP和网关 */
                do
                {
                    if (BOARD_IsCustomer(BOARD_C_DMS31V2_XJKJ)
                        && pstWifiInfo->stWifiConf.bWifiStaEnable
                        && pstWifiInfo->bStaUpdate)
                    {
                        if (0 == strlen(pstWifiInfo->stWifiConf.szWifiStaIpAddr) || 0 == strlen(pstWifiInfo->stWifiConf.szWifiStaGateway) || 0 == strlen(pstWifiInfo->stWifiConf.szWifiStaSubmask))
                        {
                            pstWifiInfo->bStaUpdate = SV_FALSE;
                            break;
                        }

                        switch (pstWifiInfo->enWifiId)
    			        {
    						case WIFI_ID_RTL8821:
    							break;
                            case WIFI_ID_RTL8188FU:
                            case WIFI_ID_RTL8188CVTE:
                            case WIFI_ID_RTL8188EU:
    							RTL8188_SetStaStaticInfo(pstWifiInfo->bStaConfUpdate, pstWifiInfo->stWifiConf.szWifiStaIpAddr, pstWifiInfo->stWifiConf.szWifiStaGateway, pstWifiInfo->stWifiConf.szWifiStaSubmask);
    							break;

                            default:
                                print_level(SV_WARN, "unknown wifi module.\n");
                                break;
    			        }

                        pstWifiInfo->bStaUpdate = SV_FALSE;
                    }
                }while(0);


				if (pstWifiInfo->bStaConfUpdate)
				{
                    print_level(SV_INFO, "sta config has been changed.\n");
					switch (pstWifiInfo->enWifiId)
			        {
			            case WIFI_ID_RTL8811:
			                s32Ret = rtl8811_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "rtl8811_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
						case WIFI_ID_RTL8821:
							s32Ret = rtl8821_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
							if(SV_SUCCESS != s32Ret)
							{
								print_level(SV_ERROR, "rtl8821_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
							}
							break;
			            case WIFI_ID_WF61:
			                s32Ret = wf61_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "wf61_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
			            case WIFI_ID_AR1021:
			                s32Ret = ar1021_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "ar1021_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
			            case WIFI_ID_MT7601:
			                s32Ret = mt7601_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "mt7601_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }

			                s32Ret = mt7601_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
			                if (SV_SUCCESS != s32Ret)
			                {
			                    print_level(SV_ERROR, "mt7601_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
			                }
			                break;
                        case WIFI_ID_RTL8188FU:
                        case WIFI_ID_RTL8188CVTE:
                        case WIFI_ID_RTL8188EU:
							RTL8188_SetWifiId(pstWifiInfo->enWifiId);
							RTL8188_SetStaStaticInfo(pstWifiInfo->bStaConfUpdate, pstWifiInfo->stWifiConf.szWifiStaIpAddr, pstWifiInfo->stWifiConf.szWifiStaGateway, pstWifiInfo->stWifiConf.szWifiStaSubmask);
							s32Ret = rtl8188_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
						    if (SV_SUCCESS != s32Ret)
						    {
						        print_level(SV_ERROR, "rtl8188_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
						    }
							break;
                        case WIFI_ID_RTL8723DU:
							RTL8723_SetWifiId(pstWifiInfo->enWifiId);
							s32Ret = rtl8723_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
						    if (SV_SUCCESS != s32Ret)
						    {
						        print_level(SV_ERROR, "rtl8723_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
						    }
                            break;
						 case WIFI_ID_RTL8192FU:
							RTL8192_SetWifiId(pstWifiInfo->enWifiId);
							s32Ret = rtl8192_STA_Config(pstWifiInfo->stWifiConf.bWifiStaEnable, pstWifiInfo->stWifiConf.szWifiStaSsid, pstWifiInfo->stWifiConf.szWifiStaPwd);
						    if (SV_SUCCESS != s32Ret)
						    {
						        print_level(SV_ERROR, "rtl8192_STA_Config failed. [err=%#x]\n", s32Ret);
			                    continue;
						    }
                            break;

                        default:
                            print_level(SV_WARN, "unknown wifi module.\n");
                            break;
			        }

					pstWifiInfo->bStaConfUpdate = SV_FALSE;
				}

                /* 先分配IP再修改的方式 */
                do
                {
                    if ((pstWifiInfo->bAutoTesting || BOARD_IsCustomer(BOARD_C_DMS31V2_111334))
                        && pstWifiInfo->stWifiConf.bWifiStaEnable
                        && pstWifiInfo->bStaUpdate)
                    {
                        if (0 == strlen(pstWifiInfo->stWifiConf.szWifiStaIpAddr))
                        {
                            pstWifiInfo->bStaUpdate = SV_FALSE;
                            break;
                        }

                        SV_BOOL bIpSame = SV_TRUE;
                        SV_BOOL bSegMatch = SV_TRUE;
                        SV_BOOL bNeedUdhcpc = SV_TRUE;
                        char szStaIpaddr[32] = {0};
                        s32Ret = WIFI_CheckStaIpaddr(pstWifiInfo->stWifiConf.szWifiStaIpAddr, &bIpSame, &bSegMatch);
                        if (SV_SUCCESS != s32Ret)
                        {
                            //print_level(SV_WARN, "WIFI_CheckStaIpaddr failed!\n");
                            break;
                        }

                        if (!bSegMatch || bIpSame)
                        {
                            print_level(SV_INFO, "segment match: %d, ip same: %d\n", bSegMatch, bIpSame);
                            pstWifiInfo->bStaUpdate = SV_FALSE;
                            break;
                        }

                        strcpy(szCmd, "/root/ip route show dev wlan1 | grep default | awk '{print $3}'");
                        memset(szRecvBuf, 0, sizeof(szRecvBuf));
                        s32Ret = SAFE_System_Recv(szCmd, szRecvBuf, 256);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "SAFE_System_Recv %s failed!\n", szCmd);
                            break;
                        }
                        COMMON_CutLineBreak(szRecvBuf);
                        strncpy(szGateway, szRecvBuf, 32);

                        print_level(SV_INFO, "the sta ipaddr is not the same as %s, config now! get sta gateway: %s\n", pstWifiInfo->stWifiConf.szWifiStaIpAddr, szGateway);
                        sprintf(szCmd, "ifconfig wlan1 %s", pstWifiInfo->stWifiConf.szWifiStaIpAddr);
                        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "exec cmd: %s failed! [err=%x]\n", szCmd, s32Ret);
                            break;
                        }
                        bAutoTestStaIpSet = SV_TRUE;

                        if (pstWifiInfo->bAutoTesting)
                        {
                            break;
                        }

                        /* 手动修改了wlan1的IP之后wlan1的默认网关会丢失，需要重新配置网关才可以上网 */
                        for (i = 0; i < 5; i++)
                        {
                            strcpy(szCmd, "/root/ip route show | grep default");
                            memset(szRecvBuf, 0, sizeof(szRecvBuf));
                            s32Ret = SAFE_System_Recv(szCmd, szRecvBuf, 256);
                            if (SV_SUCCESS != s32Ret)
                            {
                                print_level(SV_ERROR, "SAFE_System_Recv %s failed!\n", szCmd);
                                break;
                            }

                            sleep_ms(100);

                            if (strstr(szRecvBuf, "default"))
                            {
                                strcpy(szCmd, "/root/ip route del default");
                                s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                                if (s32Ret != SV_SUCCESS)
                                {
                                    print_level(SV_ERROR, "exec cmd: %s failed! [err=%x]\n", szCmd, s32Ret);
                                }
                            }
                            else
                            {
                                break;
                            }
                        }

                        sprintf(szCmd, "/root/ip route add default via %s dev wlan1", szGateway);
                        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                        if (s32Ret != SV_SUCCESS)
                        {
                            print_level(SV_ERROR, "exec cmd: %s failed! [err=%x]\n", szCmd, s32Ret);
                            break;
                        }
                    }
                }while (0);
			}

            u32ResetCnt = 0;
        }
        else
        {
            if (bWifiInited)
            {
                print_level(SV_ERROR, "lost wifi module.\n");
                #if defined(PLATFORM_RV1106)
                wifi_SubmitNetworkStat(SV_FALSE);
                #endif
                switch (pstWifiInfo->enWifiId)
                {
                    case WIFI_ID_RTL8811:
                        s32Ret = RTL8811_Fini();
                        break;

                    case WIFI_ID_WF61:
                        s32Ret = wf61_Fini();
                        break;

                    case WIFI_ID_AR1021:
                        s32Ret = AR1021_Fini();
                        break;
                    case WIFI_ID_RTL8821:
                        s32Ret = RTL8821_Fini(SV_TRUE);
                        break;
                    case WIFI_ID_MT7601:
                        s32Ret = MT7601U_Fini();
                        break;
					case WIFI_ID_RTL8188FU:
					case WIFI_ID_RTL8188CVTE:
                    case WIFI_ID_RTL8188EU:
						s32Ret = RTL8188_Fini(SV_TRUE);
						break;
                    case WIFI_ID_RTL8723DU:
                        s32Ret = RTL8723_Fini(SV_TRUE);
                        break;
					case WIFI_ID_RTL8192FU:
                        s32Ret = RTL8192_Fini(SV_TRUE);
                        break;

                    default:
                        print_level(SV_WARN, "unknown wifi module.\n");
                }
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "Wifi_Fini failed. [err=%#x]\n", s32Ret);
                }
                bWifiInited = SV_FALSE;
                pstWifiInfo->enWifiId = WIFI_ID_UNKNOWN;
            }

#if defined(BOARD_DMS31V2)
            if (u32ResetCnt < 3)
            {
                u32ResetCnt++;
                print_level(SV_INFO, "try to reset wifi module [%d].\n", u32ResetCnt);
                wifi_ResetPower();
                sleep_ms(2000);
            }
#elif (!defined(BOARD_ADA32V2) && !defined(BOARD_ADA32V3) && !defined(BOARD_ADA32IR) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32E1) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA900V1) && !defined(BOARD_HDW845V1) && !defined(BOARD_ADA32C4))
            if (u32ResetCnt < 3)
            {
                u32ResetCnt++;
                print_level(SV_INFO, "try to reset wifi module [%d].\n", u32ResetCnt);
                wifi_ResetPower();
                sleep_ms(2000);
            }
            else if (u32ResetCnt == 3)
            {
                u32ResetCnt++;
                sprintf(szLogBuf, "type=\"wifiException\"\twifiModuleId=%d", pstWifiInfo->enWifiId);
                print_level(SV_DEBUG, "%s\n", szLogBuf);
                LOG_Submit(-1, szLogBuf);
                print_level(SV_ERROR, "reset wifi module excess 3 times, try to reboot now.\n");
                s32Ret = pstWifiInfo->stWifiConf.pfExcCallback();
            }
#endif

            m_WifiException = SV_FALSE;
        }
		//SHECMD_BackgroundRunCmd(10, "task o");
        bFirstLoop = SV_FALSE;
    }

    return NULL;
}

sint32 WIFI_Init(WIFI_CONF_S *pstWifiParam, SV_BOOL *bWifi)
{
    sint32 s32Ret = 0;
    char szStaSsid[32] = {0};
    char szStaPwd[32] = {0};
    char szStaIpAddr[32] = {0};
    char szCmd[64] = {0};

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    if (NULL == pstWifiParam)
    {
        return ERR_NULL_PTR;
    }

    memset(&m_stWifiInfo, 0x0, sizeof(WIFI_COM_INFO_S));
    s32Ret = pthread_mutex_init(&m_stWifiInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    if (0 == access(AUTOTEST_IP, F_OK))
        m_stWifiInfo.bAutoTesting = SV_TRUE;
    else
        m_stWifiInfo.bAutoTesting = SV_FALSE;

    m_stWifiInfo.stWifiConf = *pstWifiParam;

#if (defined(BOARD_DMS31V2))
    if (m_stWifiInfo.bAutoTesting)
    {
        s32Ret = wifi_GetAutoTestIpAddr(szStaSsid, szStaPwd, szStaIpAddr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "wifi_GetAutoTestIpAddr failed.\n");
        }
        else
        {
            print_level(SV_ERROR, "wifi_GetAutoTestIpAddr success.\n");
            m_stWifiInfo.bStaConfUpdate = SV_TRUE;
            m_stWifiInfo.stWifiConf.bWifiStaEnable = SV_TRUE;

            strncpy(m_stWifiInfo.stWifiConf.szWifiStaSsid, szStaSsid, 32);
            strncpy(m_stWifiInfo.stWifiConf.szWifiStaPwd, szStaPwd, 32);
            strncpy(m_stWifiInfo.stWifiConf.szWifiStaIpAddr, szStaIpAddr, 32);
        }
    }
    m_stWifiInfo.bStaUpdate = m_stWifiInfo.stWifiConf.bWifiStaEnable;
#endif

	print_level(SV_INFO, "apIpaddr: %s, staIpaddr: %s\n", m_stWifiInfo.stWifiConf.szWifiApIpAddr, m_stWifiInfo.stWifiConf.szWifiStaIpAddr);

    if (m_stWifiInfo.stWifiConf.bWifiStaEnable)
    {
        strcpy(szCmd, "ip addr flush dev wlan1 &");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
        }
    }

    *bWifi = wifi_IsModuleExist();
    return SV_SUCCESS;
}

sint32 WIFI_Fini()
{
    sint32 s32Ret = 0;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
            s32Ret = RTL8811_Fini();
            break;
        case WIFI_ID_WF61:
            s32Ret = wf61_Fini();
            break;
        case WIFI_ID_AR1021:
            s32Ret = AR1021_Fini();
            break;
		case WIFI_ID_RTL8821:
			s32Ret = RTL8821_Fini(SV_TRUE);
			break;
		case WIFI_ID_MT7601:
            s32Ret = MT7601U_Fini();
            break;
		case WIFI_ID_RTL8188FU:
		case WIFI_ID_RTL8188CVTE:
        case WIFI_ID_RTL8188EU:
			s32Ret = RTL8188_Fini(SV_TRUE);
			break;
        case WIFI_ID_RTL8723DU:
            s32Ret = RTL8723_Fini(SV_TRUE);
            break;
		case WIFI_ID_RTL8192FU:
            s32Ret = RTL8192_Fini(SV_TRUE);
            break;

        default:
            print_level(SV_WARN, "unknown wifi module.\n");
            break;
    }
    pthread_mutex_destroy(&m_stWifiInfo.mutexLock);
    memset(&m_stWifiInfo, 0x0, sizeof(WIFI_COM_INFO_S));

    return SV_SUCCESS;
}

sint32 WIFI_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread = 0;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    m_stWifiInfo.bRunning = SV_TRUE;
    pthread_attr_t  attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程

    s32Ret = pthread_create(&thread, &attr, wifi_Daemon_Body, &m_stWifiInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
    pthread_attr_destroy(&attr);
    m_stWifiInfo.u32TID = thread;

    return SV_SUCCESS;
}

sint32 WIFI_Stop()
{
    sint32 s32Ret = 0;
    void *pvRetval = NULL;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    m_stWifiInfo.bRunning = SV_FALSE;
    //s32Ret = pthread_join(m_stWifiInfo.u32TID, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

sint32 WIFI_SetPararm(WIFI_CONF_S *pstWifiParam)
{
    sint32 s32Ret = 0;
    long lSetChannel = 0;
	SV_BOOL bhostapd = SV_FALSE;
	SV_BOOL bApConfUpdate = SV_FALSE;
	SV_BOOL bStaConfUpdate = SV_FALSE;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    if (NULL == pstWifiParam)
    {
        return ERR_NULL_PTR;
    }

	if (m_stWifiInfo.stWifiConf.enWifiAuth != pstWifiParam->enWifiAuth
        || m_stWifiInfo.stWifiConf.lSet2GChannel != pstWifiParam->lSet2GChannel
        || m_stWifiInfo.stWifiConf.lSet5GChannel != pstWifiParam->lSet5GChannel
        || 0 != strcmp(m_stWifiInfo.stWifiConf.szWifiApSsid, pstWifiParam->szWifiApSsid)
        || 0 != strcmp(m_stWifiInfo.stWifiConf.szWifiApIpAddr, pstWifiParam->szWifiApIpAddr)
        || 0 != strcmp(m_stWifiInfo.stWifiConf.szWifiApPwd, pstWifiParam->szWifiApPwd)
        || m_stWifiInfo.stWifiConf.enWifiFreq != pstWifiParam->enWifiFreq
        || (m_stWifiInfo.stWifiConf.bWifiStaEnable && !pstWifiParam->bWifiStaEnable)
		)
    {
		m_stWifiInfo.stWifiConf.enWifiAuth = pstWifiParam->enWifiAuth;
		m_stWifiInfo.stWifiConf.lSet2GChannel = pstWifiParam->lSet2GChannel;
		m_stWifiInfo.stWifiConf.lSet5GChannel = pstWifiParam->lSet5GChannel;
		strcpy(m_stWifiInfo.stWifiConf.szWifiApSsid, pstWifiParam->szWifiApSsid);
		strcpy(m_stWifiInfo.stWifiConf.szWifiApIpAddr, pstWifiParam->szWifiApIpAddr);
		strcpy(m_stWifiInfo.stWifiConf.szWifiApPwd, pstWifiParam->szWifiApPwd);
		m_stWifiInfo.stWifiConf.enWifiFreq = pstWifiParam->enWifiFreq;
		bApConfUpdate = SV_TRUE;
    }

    if (m_stWifiInfo.stWifiConf.bWifiStaEnable != pstWifiParam->bWifiStaEnable
        || 0 != strcmp(m_stWifiInfo.stWifiConf.szWifiStaSsid, pstWifiParam->szWifiStaSsid)
        || 0 != strcmp(m_stWifiInfo.stWifiConf.szWifiStaPwd, pstWifiParam->szWifiStaPwd)
		|| (0 != strlen(m_stWifiInfo.stWifiConf.szWifiStaIpAddr) && 0 == strlen(pstWifiParam->szWifiStaIpAddr))
		|| (0 != strlen(m_stWifiInfo.stWifiConf.szWifiStaGateway) && 0 == strlen(pstWifiParam->szWifiStaGateway))
		|| (0 != strlen(m_stWifiInfo.stWifiConf.szWifiStaSubmask) && 0 == strlen(pstWifiParam->szWifiStaSubmask))
		|| (0 == strlen(m_stWifiInfo.stWifiConf.szWifiStaIpAddr) && 0 != strlen(pstWifiParam->szWifiStaIpAddr))
		|| (0 == strlen(m_stWifiInfo.stWifiConf.szWifiStaGateway) && 0 != strlen(pstWifiParam->szWifiStaGateway))
		|| (0 == strlen(m_stWifiInfo.stWifiConf.szWifiStaSubmask) && 0 != strlen(pstWifiParam->szWifiStaSubmask))
		)
    {
		m_stWifiInfo.stWifiConf.bWifiStaEnable = pstWifiParam->bWifiStaEnable;
		strcpy(m_stWifiInfo.stWifiConf.szWifiStaSsid, pstWifiParam->szWifiStaSsid);
		strcpy(m_stWifiInfo.stWifiConf.szWifiStaPwd, pstWifiParam->szWifiStaPwd);
		bStaConfUpdate = SV_TRUE;
    }

    if (0 != strcmp(m_stWifiInfo.stWifiConf.szWifiStaIpAddr, pstWifiParam->szWifiStaIpAddr)
        || 0 != strcmp(m_stWifiInfo.stWifiConf.szWifiStaGateway, pstWifiParam->szWifiStaGateway)
        || 0 != strcmp(m_stWifiInfo.stWifiConf.szWifiStaSubmask, pstWifiParam->szWifiStaSubmask)
       )
    {
        strcpy(m_stWifiInfo.stWifiConf.szWifiStaIpAddr, pstWifiParam->szWifiStaIpAddr);
        strcpy(m_stWifiInfo.stWifiConf.szWifiStaGateway, pstWifiParam->szWifiStaGateway);
        strcpy(m_stWifiInfo.stWifiConf.szWifiStaSubmask, pstWifiParam->szWifiStaSubmask);
        m_stWifiInfo.bStaUpdate = m_stWifiInfo.stWifiConf.bWifiStaEnable;
    }

    m_stWifiInfo.stWifiConf = *pstWifiParam;
	m_stWifiInfo.bApConfUpdate = m_stWifiInfo.bApConfUpdate ? m_stWifiInfo.bApConfUpdate : bApConfUpdate;
	m_stWifiInfo.bStaConfUpdate = m_stWifiInfo.bStaConfUpdate ? m_stWifiInfo.bStaConfUpdate : bStaConfUpdate;

    return SV_SUCCESS;
}

sint32 WIFI_GetPararm(WIFI_CONF_S *pstWifiParam)
{
    if (NULL == pstWifiParam)
    {
        return ERR_NULL_PTR;
    }

    *pstWifiParam = m_stWifiInfo.stWifiConf;

    return SV_SUCCESS;
}

sint32 WIFI_SetPararm_Reboot(WIFI_CONF_S *pstWifiParam, SV_BOOL bReboot)
{
    sint32 s32Ret = 0;
    long lSetChannel = 0;
	int _count = 0;
    char szCmd[40];
    char readstr[10];
    FILE *fp;
#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))
    char *pszHostapConfName = "rtl8821_hostapd.conf";
#else
    char *pszHostapConfName = "hostapd.conf";
#endif

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    if (NULL == pstWifiParam)
    {
        return ERR_NULL_PTR;
    }

    if (m_stWifiInfo.enWifiId == WIFI_ID_RTL8821 || m_stWifiInfo.enWifiId == WIFI_ID_RTL8188FU || m_stWifiInfo.enWifiId == WIFI_ID_RTL8188CVTE || m_stWifiInfo.enWifiId == WIFI_ID_RTL8188EU || m_stWifiInfo.enWifiId == WIFI_ID_RTL8723DU || m_stWifiInfo.enWifiId == WIFI_ID_RTL8192FU)
    {
    	m_stWifiInfo.bSkipDetecting = SV_TRUE;

#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))
        SAFE_System("killall -9 hostapd2", NORMAL_WAIT_TIME);
#else
        SAFE_System("killall -9 hostapd", NORMAL_WAIT_TIME);
#endif
        SAFE_KillallProcess("udhcpd /etc/udhcpd.conf");

        BOARD_SetWifiPower(SV_FALSE);
        sleep_ms(1000);
        BOARD_SetWifiPower(SV_TRUE);
        sleep_ms(2000);

        sprintf(szCmd, "ifconfig wlan0 %s &", pstWifiParam->szWifiApIpAddr);

        //快速配置wlan0
        for (_count = 0; _count < 20; _count++)
        {
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
            sleep_ms(200);
            SAFE_System_Recv("ifconfig | grep wlan0", readstr, 6);
            print_level(SV_INFO,"%s\n",readstr);

            if (0 == strcmp(readstr, "wlan0"))
                break;
        }
        sleep_ms(100);
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
            lSetChannel = pstWifiParam->enWifiFreq == WIFI_FREQ_2_4G ? pstWifiParam->lSet2GChannel : pstWifiParam->lSet5GChannel;
            s32Ret = rtl8811_AP_Config(pstWifiParam->enWifiAuth, pstWifiParam->szWifiApSsid, pstWifiParam->szWifiApIpAddr , pstWifiParam->szWifiApPwd, pstWifiParam->enWifiFreq, lSetChannel);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "rtl8811_AP_Config failed. [err=%#x]\n", s32Ret);
                return s32Ret;
            }
            m_WifiException = SV_TRUE;
            break;
        case WIFI_ID_RTL8821:

            s32Ret = RTL8821_Fini(SV_FALSE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RTL8821_Fini failed.\n");
            }

            s32Ret = RTL8821_Init(pstWifiParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RTL8821_Init failed.\n");
            }

			m_stWifiInfo.bSkipDetecting = SV_FALSE;

            break;
        case WIFI_ID_WF61:
            s32Ret = wf61_AP_Config(pstWifiParam->enWifiAuth, pstWifiParam->szWifiApSsid, pstWifiParam->szWifiApPwd, pstWifiParam->enWifiFreq);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "wf61_AP_Config failed. [err=%#x]\n", s32Ret);
                return s32Ret;
            }
            break;
        case WIFI_ID_AR1021:
            s32Ret = ar1021_AP_Config(pstWifiParam->enWifiAuth, pstWifiParam->szWifiApSsid, pstWifiParam->szWifiApPwd, pstWifiParam->enWifiFreq);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "rtl8811_AP_Config failed. [err=%#x]\n", s32Ret);
                return s32Ret;
            }
            break;
        case WIFI_ID_MT7601:
            lSetChannel = pstWifiParam->lSet2GChannel;
            s32Ret = mt7601_AP_Config(pstWifiParam->enWifiAuth, pstWifiParam->szWifiApSsid, pstWifiParam->szWifiApPwd, pstWifiParam->enWifiFreq, lSetChannel);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mt7601_AP_Config failed. [err=%#x]\n", s32Ret);
                return s32Ret;
            }
            break;

        case WIFI_ID_RTL8188FU:
        case WIFI_ID_RTL8188CVTE:
        case WIFI_ID_RTL8188EU:
            s32Ret = RTL8188_Fini(SV_FALSE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RTL8821_Fini failed.\n");
            }

            s32Ret = RTL8188_Init(pstWifiParam, m_stWifiInfo.enWifiId);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RTL8821_Init failed.\n");
            }

			m_stWifiInfo.bSkipDetecting = SV_FALSE;
            break;

        case WIFI_ID_RTL8723DU:
            s32Ret = RTL8723_Fini(SV_FALSE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RTL8723_Fini failed.\n");
            }

            s32Ret = RTL8723_Init(pstWifiParam, m_stWifiInfo.enWifiId);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RTL8723_Init failed.\n");
            }

			m_stWifiInfo.bSkipDetecting = SV_FALSE;
            break;
		case WIFI_ID_RTL8192FU:
			s32Ret = RTL8192_Fini(SV_FALSE);
			if (SV_SUCCESS != s32Ret)
			{
				print_level(SV_ERROR, "RTL8912_Fini failed.\n");
			}

			s32Ret = RTL8192_Init(pstWifiParam, m_stWifiInfo.enWifiId);
			if (SV_SUCCESS != s32Ret)
			{
				print_level(SV_ERROR, "RTL8192_Init failed.\n");
			}

			m_stWifiInfo.bSkipDetecting = SV_FALSE;
			break;

        default:
            print_level(SV_WARN, "unknown wifi module.\n");
            break;
    }

    m_stWifiInfo.stWifiConf = *pstWifiParam;

    return SV_SUCCESS;
}

sint32 WIFI_CheckStaIpaddr(char *pszStaIpAddr, SV_BOOL *bSame, SV_BOOL *bSegMatch)
{
    sint32 s32Ret = 0;
    char szStaIpaddr[32] = {0};
    uint8 u8SegSrc = 0;
    uint8 u8SegDts = 0;

    s32Ret = wifi_GetStaIpaddress(szStaIpaddr);
    if (SV_SUCCESS != s32Ret)
    {
        //print_level(SV_WARN, "wifi_GetStaIpaddress failed!\n");
        return s32Ret;
    }
    print_level(SV_INFO, "sta ip: %s\n", szStaIpaddr);

    if (0 == strcmp(szStaIpaddr, pszStaIpAddr))
    {
        *bSame = SV_TRUE;
    }
    else
    {
        *bSame = SV_FALSE;
    }

    sscanf(szStaIpaddr, "%*d.%*d.%d.%*d", &u8SegSrc);
    sscanf(pszStaIpAddr, "%*d.%*d.%d.%*d", &u8SegDts);
    if (u8SegSrc != u8SegDts)
    {
        print_level(SV_WARN, "u8SegSrc: %d, u8SegDts: %d is not the same\n", u8SegSrc, u8SegDts);
        *bSegMatch = SV_FALSE;
    }
    else
    {
        *bSegMatch = SV_TRUE;
    }

    return s32Ret;
}

sint32 WIFI_GetStaGateway(char *pszStaGateway)
{
    sint32 s32Ret = 0;
    char szCmd[128] = {0};
    char szStaGateway[32] = {0};
    char szRecvBuf[256] = {0};

    strcpy(szCmd, "/root/ip route show dev wlan1 | grep default | awk '{print $3}'");
    s32Ret = SAFE_System_Recv(szCmd, szRecvBuf, 256);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SAFE_System_Recv %s failed!\n", szCmd);
        return SV_FAILURE;
    }
    COMMON_CutLineBreak(szRecvBuf);

    if (0 == strlen(szRecvBuf) || !wifi_IsValidIPAddress(szRecvBuf))
    {
        return SV_FAILURE;
    }

    strncpy(pszStaGateway, szRecvBuf, 32);
    return SV_SUCCESS;
}

sint32 WIFI_Set_TxPower(SV_BOOL bSet, uint32 u32Db)
{
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8821:
            return RTL8821_Set_TxPower(bSet, u32Db);

        case WIFI_ID_RTL8188FU:
            return RTL8188_Set_TxPower(bSet, u32Db);

        case WIFI_ID_RTL8723DU:
            return RTL8723_Set_TxPower(bSet, u32Db);

		case WIFI_ID_RTL8192FU:
            return RTL8192_Set_TxPower(bSet, u32Db);

        default:
            print_level(SV_WARN, "unknown wifi module.\n");
    }

    return SV_SUCCESS;
}

sint32 WIFI_Query_Status(WIFI_STAT_S *pstStatus)
{
    sint32 s32Ret = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
            s32Ret = RTL8811_Query_Status(pstStatus);
            break;

        case WIFI_ID_RTL8821:
            s32Ret = RTL8821_Query_Status(pstStatus);
            break;

        case WIFI_ID_AR1021:
            s32Ret = AR1021_Query_Status(pstStatus);
            break;

        case WIFI_ID_MT7601:
            s32Ret = MT7601_Query_Status(pstStatus);
            break;

        case WIFI_ID_RTL8188FU:
        case WIFI_ID_RTL8188CVTE:
        case WIFI_ID_RTL8188EU:
            s32Ret = RTL8188_Query_Status(pstStatus);
            break;

        case WIFI_ID_RTL8723DU:
            s32Ret = RTL8723_Query_Status(pstStatus);
            break;

		case WIFI_ID_RTL8192FU:
			s32Ret = RTL8192_Query_Status(pstStatus);
            break;

        default:
            return SV_FAILURE;
    }

    char szStaIpaddr[32] = {0};
    if (SV_SUCCESS == wifi_GetStaIpaddress(szStaIpaddr))
    {
        strcpy(pstStatus->szStaIpAddr, szStaIpaddr);
    }

    return s32Ret;
}

uint32 WIFI_GetSignalLevel(uint32 u32SteamKbps)
{
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
            return RTL8811_GetSignalLevel(u32SteamKbps);

        case WIFI_ID_RTL8821:
            return RTL8821_GetSignalLevel(u32SteamKbps);

        case WIFI_ID_WF61:
            return wf61_GetSignalLevel(u32SteamKbps);

        case WIFI_ID_AR1021:
            return AR1021_GetSignalLevel(u32SteamKbps);

        case WIFI_ID_MT7601:
            return MT7601_GetSignalLevel(u32SteamKbps);

        case WIFI_ID_RTL8723DU:
            return RTL8723_GetSignalLevel(u32SteamKbps);

		case WIFI_ID_RTL8192FU:
            return RTL8192_GetSignalLevel(u32SteamKbps);

		case WIFI_ID_RTL8188CVTE:
		case WIFI_ID_RTL8188EU:
		case WIFI_ID_RTL8188FU:
			return RTL8188_GetSignalLevel(u32SteamKbps);

        default:
            return 0;
    }
}

uint32 WIFI_GetBandwidthLevel()
{
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
            return RTL8811_GetBandwidthLevel();

        case WIFI_ID_RTL8821:
            return RTL8821_GetBandwidthLevel();

        case WIFI_ID_WF61:
            return wf61_GetBandwidthLevel();

        case WIFI_ID_AR1021:
            return AR1021_GetBandwidthLevel();

        case WIFI_ID_MT7601:
            return MT7601_GetBandwidthLevel();

        case WIFI_ID_RTL8723DU:
            return RTL8723_GetBandwidthLevel();

		case WIFI_ID_RTL8192FU:
            return RTL8192_GetBandwidthLevel();

		case WIFI_ID_RTL8188CVTE:
		case WIFI_ID_RTL8188EU:
		case WIFI_ID_RTL8188FU:
			return RTL8188_GetBandwidthLevel();

        default:
            return 0;
    }
}

uint32 WIFI_GetDriverBlockLevel()
{
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
            return RTL8811_GetDriverBlockLevel();

        default:
            return 1;
    }
}

sint32 WIFI_GetAvailableChannel(const sint32 **pps32Channel, sint32 *ps32Number)
{
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
            return RTL8811_GetAvailableChannel(pps32Channel, ps32Number);

        case WIFI_ID_RTL8821:
            return RTL8821_GetAvailableChannel(pps32Channel, ps32Number);

        case WIFI_ID_WF61:
            return SV_SUCCESS;

        case WIFI_ID_AR1021:
            return SV_SUCCESS;

        case WIFI_ID_MT7601:
            return MT7601_GetAvailableChannel(pps32Channel, ps32Number);

        case WIFI_ID_RTL8188FU:
        case WIFI_ID_RTL8188CVTE:
        case WIFI_ID_RTL8188EU:
            return RTL8188_GetAvailableChannel(pps32Channel, ps32Number);

        case WIFI_ID_RTL8723DU:
            return RTL8723_GetAvailableChannel(pps32Channel, ps32Number);

		case WIFI_ID_RTL8192FU:
			return RTL8192_GetAvailableChannel(pps32Channel, ps32Number);

        default:
            return SV_FAILURE;
    }
}

SV_BOOL WIFI_IsDualBand()
{
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111323))
    {
        return SV_SUCCESS;
    }

    switch (m_stWifiInfo.enWifiId)
    {
        case WIFI_ID_RTL8811:
        case WIFI_ID_RTL8821:
        case WIFI_ID_WF61:
        case WIFI_ID_AR1021:
            return SV_TRUE;

        case WIFI_ID_MT7601:
        case WIFI_ID_RTL8188FU:
        case WIFI_ID_RTL8188CVTE:
        case WIFI_ID_RTL8188EU:
        case WIFI_ID_RTL8723DU:
		case WIFI_ID_RTL8192FU:
            return SV_FALSE;

        default:
            return SV_FALSE;
    }
}

