<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="./css/jquery.mobile.css">
        <link rel="stylesheet" href="./css/jquery.extra-themes.css">
        <!--link rel="stylesheet" href="./css/common.css"-->
        <script src="./js/jquery.min.js"></script>
        <script src="./js/jquery.mobile.js"></script>
        <title data-desc="config">Configure</title>
        <style>
            #networkConfig {
                display: none;
            }

            #sysConfig {
                display: none;
            }

            #popup-show-info {
                min-width: 200px;
                max-width: 400px;
            }

            #popup-ask-for-sure {
                min-width: 200px;
                max-width: 400px;
            }

            #popup-form-input2 {
                min-width: 200px;
                max-width: 400px;
            }

            #popup-form-input3 {
                min-width: 200px;
                max-width: 400px;
            }

            #tab-chn-1 {
                display: none;
            }

            #tab-chn-2 {
                display: none;
            }

            .cfg-chn-1 {
                display: none;
            }

            .cfg-chn-2 {
                display: none;
            }

            .cfg-chn-3 {
                display: none;
            }

            .alg-chn-1 {
                display: none;
            }

            .alg-chn-2 {
                display: none;
            }

            #goto-popup {
                display: none;
            }

            #popup {
                min-width: 200px;
                max-width: 400px;
            }

            .hide-anchor {
                display: none;
            }

            .ui-icon-global:after {
                background-image: url("./css/images/icons-png/global-white.png");
                background-size: 14px 14px;
            }

            .ui-icon-slider:after {
                background-image: url("./css/images/icons-png/slider-white.png");
                background-size: 14px 14px;
            }

            .scroll-downwarp {
                position: relative;
                width: 100%;
                height: 0;
                overflow: hidden;
                text-align: center;
            }

            .scroll-hardware {
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }

            .scroll-downwarp-reset {
                -webkit-transition: height 300ms;
                transition: height 300ms;
            }

            .downwarp-content {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                min-height: 30px;
                padding: 10px 0;
            }

            .downwarp-tip {
                display: inline-block;
                font-size: 12px;
                color: gray;
                vertical-align: middle;
            }

            .downwarp-progress {
                display: inline-block;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                border: 1px solid gray;
                margin-right: 8px;
                border-bottom-color: transparent;
                vertical-align: middle;
            }

            .ui-selectmenu.ui-popup .ui-input-search {
                margin-left: .5em;
                margin-right: .5em;
            }

            .ui-selectmenu.ui-dialog .ui-content {
                padding-top: 0;
            }

            .ui-selectmenu.ui-dialog .ui-selectmenu-list {
                margin-top: 0;
            }

            .ui-selectmenu.ui-popup .ui-selectmenu-list li.ui-first-child .ui-btn {
                border-top-width: 1px;
                -webkit-border-radius: 0;
                border-radius: 0;
            }

            .ui-selectmenu.ui-dialog .ui-header {
                border-bottom-width: 1px;
            }

            #TestConfig {
                height: 100%;
                width: 20%;
                position: absolute;
                left: 40%;
                top: -18px;
                font-size: 18px;
                line-height: 52px;
                text-align: center;
                font-weight: var(--theme_title_font_weight);
                text-transform: var(--theme_title_text_transform);
                color: var(--theme_title_color);
                text-decoration: none;
            }

            input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }

            input[type=number] {
                -moz-appearance: textfield;
            }
        </style>
    </head>
    <body>
        <div id="page-config" data-role="none" style="overflow-x:hidden;background:#fff;min-height: 887px;">
            <div class='title index_hide_group'>
                <a class="title_back" href="./index.html" data-ajax="false"></a>
                <p id="TestConfig" data-desc="config">Configure</p>
                <a class="btn-refresh title_refresh"></a>
            </div>
            <div class='title_hide index_hide_group'></div>
            <div id="main" role="main" class="ui-content" style="min-height: 810px;overflow: hidden;"></div>
            <div data-role="footer" data-position="fixed" data-fullscreen="false">
                <a style="visibility:hidden;"></a>
                <a href="#" id="btn-submit" data-role="button" class="href-btn" data-desc="confirm">确定</a>
                <a href="#" id="btn-cancel" data-role="button" class="href-btn" data-desc="cancel">取消</a>
            </div>
            <a id="show-info" href="#popup-show-info" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
            <div data-role="popup" id="popup-show-info" data-dismissible="false">
                <div data-role="header">
                    <h1></h1>
                </div>
                <div role="main" class="ui-content">
                    <h3></h3>
                    <p></p>
                    <a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
                </div>
            </div>
            <a id="ask-for-sure" href="#popup-ask-for-sure" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
            <div data-role="popup" id="popup-ask-for-sure" data-dismissible="false" data-popup-title="">
                <div data-role="header">
                    <h1></h1>
                </div>
                <div role="main" class="ui-content">
                    <h3></h3>
                    <p></p>
                    <a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
                    <a id="btn-ask-for-sure" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
                </div>
            </div>
            <a id="form-input2" href="#popup-form-input2" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
            <div data-role="popup" id="popup-form-input2" data-dismissible="false" data-popup-title="">
                <div data-role="header">
                    <h1></h1>
                </div>
                <div role="main" class="ui-content">
                    <h3></h3>
                    <form>
                        <div>
                            <label for="form-input2-1" class="ui-hidden-accessible"></label>
                            <input id="form-input2-1" value="" placeholder="" type="text">
                            <label for="form-input2-2" class="ui-hidden-accessible"></label>
                            <input id="form-input2-2" value="" placeholder="" type="text">
                            <p class="form-input-note">&nbsp;</p>
                            <a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
                            <a id="btn-form-input2" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-desc="confirm">确定</a>
                        </div>
                    </form>
                </div>
            </div>
            <a id="form-input3" href="#popup-form-input3" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
            <div data-role="popup" id="popup-form-input3" data-dismissible="false" data-popup-title="">
                <div data-role="header">
                    <h1></h1>
                </div>
                <div role="main" class="ui-content">
                    <h3></h3>
                    <form>
                        <div>
                            <label for="form-input3-1" class="ui-hidden-accessible"></label>
                            <input id="form-input3-1" value="" placeholder="" type="text">
                            <label for="form-input3-2" class="ui-hidden-accessible"></label>
                            <input id="form-input3-2" value="" placeholder="" type="text">
                            <label for="form-input3-3" class="ui-hidden-accessible"></label>
                            <input id="form-input3-3" value="" placeholder="" type="text">
                            <p class="form-input-note">&nbsp;</p>
                            <a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
                            <a id="btn-form-input3" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-desc="confirm">确定</a>
                        </div>
                    </form>
                </div>
            </div>
            <script id="ssid-list-template" type="text/x-handlebars-template">
                
                                        
			<ul data-role="listview" data-split-icon="delete" data-inset="true">
				<li data-role="list-divider">SSID<i>/Password</i><span data-desc="list">列表</span></li>
				{{#each this}}
				<li><a href="#" class="btn-popup" data-popup-type="form-input2" data-popup-title="edit-ssid" data-list-index="{{@index}}"><img src="../css/images/icons-png/edit-white.png" alt="Edit" class="ui-li-icon ui-corner-all"><h2 id="networkConfig-wifi-staMode-apList-{{@index}}-ssid">{{ssid}}</h2><i id="networkConfig-wifi-staMode-apList-{{@index}}-password">{{password}}</i></a><a href="#" class="btn-popup" data-popup-type="ask-for-sure" data-popup-title="delete-ssid"  data-list-index="{{@index}}"></a></li>
				{{/each}}
				<li data-icon="plus"><a href="#" class="btn-popup" data-popup-type="form-input2" data-popup-title="add-ssid">&nbsp;</a></li>
			</ul>           
            
            
            </script>
            <script id="configs-template" type="text/x-handlebars-template">
                                                   
			<div class="index_hide_group" style="position: fixed;z-index: 1000;border-bottom: 2px;top:52px;width:100%;height:52px">
				<table class="menu" rules="none" cellspacing="5%">
	            	<td><li><a href="#" class="xbtn-nav menu_checked" data-goto-id="mediaConfig" data-desc="media-config" id="media"style="background-color: #fff;">媒体配置</a></li></td>
					<td {{#isNotSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isNotSupportAlg}}><li><a href="#" class="xbtn-nav" data-goto-id="algConfig" data-desc="alg-config" id="alg"style="background-color: #fff;">算法配置</a></li></td>
					<td class="Usr-Install" {{{hideUSBCam}}}><li><a href="#" class="xbtn-nav" data-goto-id="networkConfig" data-desc="network-config" id="network"style="background-color: #fff;">网络配置</a></li></td>
					<td class="Usr-Install" {{{hideNotWebuiFull "ADA32IR"}}} {{{hideHardware "IPTR20S1"}}}><li><a href="#" class="xbtn-nav" data-goto-id="sysConfig" data-desc="sys-config" id="sys"style="background-color: #fff;">系统配置</a></li></td>
			</table>
			</div>
			<div id="mediaConfig" class="tab-configs">
				<div class="configmenu">
					<h1><p>{{getKeyLang "video"}}</p></h1>
					<div style="margin-top:30px;width:100%;{{#unequal ipcIdentification.hardware "ADA32IR"}}display: none;{{/unequal}}">
						<table class="menu menu_a" rules="none" cellspacing="5%">
							<td><li><a id="media-ch1" class="sel-cfg-chn menu_checked" value="ch1" data-tabchn-id="cfg-chn-0" data-desc="chn1" data-chn-idx="0">CH1</a></li></td>
							<td><li><a id="media-ch2" class="sel-cfg-chn" value="ch2" data-tabchn-id="cfg-chn-1" data-desc="chn2" data-chn-idx="1">CHN2</a></li></td>
						</table>
					</div>
					<div id="cfg-chn-0" class="media-chn-conf" {{{hideHardware "WFTR20S3"}}}>
					<div class="item">
						<h1><p data-desc="image-process">图像处理</p></h1>
						<div class="config_checkbox" {{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-imageProcess-removeFishEyeEnable" {{#if mediaConfig.imageProcess.removeFishEyeEnable}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-imageProcess-removeFishEyeEnable"></label>
							<p data-desc="image-removeFishEyeEnable">去鱼眼使能</p>	
						</div>

						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-imageProcess-mirrorEnable" {{#if mediaConfig.imageProcess.mirrorEnable}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-imageProcess-mirrorEnable"></label>
							<p data-desc="image-mirror">画面镜像</p>	
						</div>

						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-imageProcess-flipEnable" {{#if mediaConfig.imageProcess.flipEnable}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-imageProcess-flipEnable"></label>
							<p data-desc="image-flip">画面翻转</p>	
						</div>

						<div class="config_checkbox" {{{hideNotHardware "DMS31V2"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-imageProcess-refSceneEnable" {{#if mediaConfig.imageProcess.refSceneEnable}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-imageProcess-refSceneEnable"></label>
							<p data-desc="image-refSceneEnable">反光场景</p>	
						</div>
						
						<div class="custom-select-box" {{{hideCustomer "201739"}}}{{#isNotSupportIrcut ipcIdentification.hardware ipcIdentification.board}}style="display: none;"{{/isNotSupportIrcut}}>
						<label class="single_option_text" for="mediaConfig-imageProcess-ircutMode" data-desc="image-ircut">滤光片</label>
							<div><select id="mediaConfig-imageProcess-ircutMode" data-role="none" class="custom-select" value="{{mediaConfig.imageProcess.ircutMode}}" placeholder="{{mediaConfig.imageProcess.ircutMode}}">
								<option value="0" {{#equal mediaConfig.imageProcess.ircutMode 0}}selected="selected"{{/equal}} data-desc="ircut-color">color</option>
								<option value="1" {{#equal mediaConfig.imageProcess.ircutMode 1}}selected="selected"{{/equal}} data-desc="auto">auto</option>
								<option value="2" {{#equal mediaConfig.imageProcess.ircutMode 2}}selected="selected"{{/equal}} data-desc="ircut-BW">black-white</option>
							</select></div>
						</div>
						
						<div data-role="none" class="rangeinput" {{{hideNotHardware "MN234"}}}>
							<p class="rangeinput_title" data-desc="ledBrightness">LED 亮度</p>
							<label data-role="none" for="mediaConfig-imageProcess-ledBrightness"></label>
							<input data-role="none" class="rangeinput_input" type="range" id="mediaConfig-imageProcess-ledBrightness" min="0" max="10" value="{{mediaConfig.imageProcess.ledBrightness}}">
							<p class="rangeinput_value">{{mediaConfig.imageProcess.ledBrightness}}<p>	
						</div>

						<div class="config_checkbox" {{{hideHardware "WFTR20S3 IPTR20S1"}}} {{#isNotWFdev ipcIdentification.hardware}}style="display: none;"{{/isNotWFdev}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-bitrateAdaptive" {{#if mediaConfig.bitrateAdaptive}}checked="checked" {{/if}}/><label
									data-role="none" for="mediaConfig-bitrateAdaptive"></label>
							<p data-desc="bitrate-adaptive">码流自适应</p>	
						</div>
						<div class="custom-select-box" {{{hideNotHardware "IPCR20S3 IPCR20S4"}}} >
						<label class="single_option_text" for="mediaConfig-imageProcess-videoMode" data-desc="video-mode">视频制式</label>
			    		<select id="mediaConfig-imageProcess-videoMode" data-role="none" class="custom-select" value="{{mediaConfig.imageProcess.videoMode}}" placeholder="{{mediaConfig.imageProcess.videoMode}}">
								<option value="NTSC" {{#equal mediaConfig.imageProcess.videoMode "NTSC"}}selected="selected"{{/equal}}>NTSC</option>
								<option value="PAL" {{#equal mediaConfig.imageProcess.videoMode "PAL"}}selected="selected"{{/equal}}>PAL</option>					
					    </select>	
						</div>
						<div class="custom-select-box" {{{hideNotSupportRotate}}}>
						<label class="single_option_text" for="mediaConfig-imageProcess-rotateAngle" data-desc="image-rotate">画面旋转</label>
							<div><select id="mediaConfig-imageProcess-rotateAngle" data-role="none" class="custom-select" value="{{mediaConfig.imageProcess.rotateAngle}}" placeholder="{{mediaConfig.imageProcess.rotateAngle}}">
								<option value="0" {{#equal mediaConfig.imageProcess.rotateAngle 0}}selected="selected"{{/equal}}>0</option>
								<option value="1" {{#equal mediaConfig.imageProcess.rotateAngle 1}}selected="selected"{{/equal}}>90,CW</option>
								<option value="3"  {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4"}}} {{#equal mediaConfig.imageProcess.rotateAngle 3}}selected="selected"{{/equal}}>90,CCW</option>
							</select></div>
						</div>
						
						<div data-role="none" class="rangeinput" style="display:none;">
							<p class="rangeinput_title">{{getKeyLang "image-fps"}}</p>
							<label data-role="none" for="mediaConfig-imageProcess-viFramerate"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="mediaConfig-imageProcess-viFramerate" min="25" max="30" value="{{mediaConfig.imageProcess.viFramerate}}">
							<p class="rangeinput_value">{{mediaConfig.imageProcess.viFramerate}}</p>
						</div>

					</div>
					<div class="item" style="display:none;">
						<label class="single_option_text" for="mediaConfig-imageProcess-WDRMode" data-desc="image-wdrmode">宽动态</label>
			    		<select id="mediaConfig-imageProcess-WDRMode" data-role="none" class="custom-select" value="{{mediaConfig.imageProcess.WDRMode}}" placeholder="{{mediaConfig.imageProcess.WDRMode}}">
								<option value="DISABLE" {{#equal mediaConfig.imageProcess.WDRMode "DISABLE"}}selected="selected"{{/equal}}>DISABLE</option>
								<option value="ENABLE" {{#equal mediaConfig.imageProcess.WDRMode "ENABLE"}}selected="selected"{{/equal}}>ENABLE</option>
								<option value="AUTO" {{#equal mediaConfig.imageProcess.WDRMode "AUTO"}}selected="selected"{{/equal}}>AUTO</option>						
					    </select>
				    </div>
				    <div class="item" {{{hideUSBCam}}}>
						<div style="margin-top:30px;width:100%;">
							<table class="menu menu_a" rules="none" cellspacing="5%">
								<td {{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA900V1 ADA32IR DMS31V2 HDW845V1"}}}><li>
									<a id="sel-ch4" class="xsel-chn menu_checked" value="ch4" data-tabchn-id="tab-chn-3" data-desc="vo-stream">显示制式</a>
								</li></td>
				            	<td class="sel-td 1106-td" {{{hideNotWebuiFull "ADA32V2 AICB046V1 ADA32V3 ADA32IR HDW845V1"}}}><li>
				            		<a id="sel-ch1" class="xsel-chn" class="xsel-chn menu_checked" value="ch1" data-tabchn-id="tab-chn-0" data-desc="main-stream">主码流</a>
				            	</li></td>
								
								<td class="sel-td 1106-td" {{{hideNotWebuiFull "ADA32V2 AICB046V1 ADA32V3 ADA32IR HDW845V1"}}} {{#isWFdev ipcIdentification.hardware}}style="display:none"{{/isWFdev}}{{#isNotSupportSubStream ipcIdentification.hardware}}style="display:none"{{/isNotSupportSubStream}}><li>
									<a id="sel-ch2" class="xsel-chn" value="ch2" data-tabchn-id="tab-chn-1" data-desc="sub-stream">子码流</a>
								</li></td>

								<td class="sel-td sel-ada32ir-td Usr-Install" {{{hideNotWebuiFull "ADA32V2 AIC046V1 ADA32IR HDW845V1"}}} {{#isWFTdev ipcIdentification.hardware}}style="display:none"{{/isWFTdev}}  {{{hideHardware "ADA32V3s IPTR20S1"}}}><li>
									<a id="sel-ch3" class="xsel-chn" value="ch3" data-tabchn-id="tab-chn-2" data-desc="pic-stream">图片流</a>
								</li></td>
								
				            	<td style="display:none"><li>
				            		<a id="sel-ch5" class="xsel-chn" class="xsel-chn menu_checked" value="ch5" data-tabchn-id="tab-chn-4" data-desc="ir-stream">红外流</a>
				            	</li></td>
				    		</table>
			    		</div>
			    		<div id="tab-chn-0" class="tab-chn-conf" {{{hideHardware "ADA32V4 ADA32V2 AIC046V1 ADA32V3 ADA32IR ADA900V1 DMS31V2 DMS885N ADA47V1 HDW845V1"}}}> <!-- 主码流 -->

			    			<div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-mainStream-enEncode" data-desc="enEncode">编码格式</label>
					    		<div><select class="custom-select" id="mediaConfig-mainStream-enEncode" data-role="none" value="{{mediaConfig.mainStream.enEncode}}">
							        <option value="H264" {{#equal mediaConfig.mainStream.enEncode "H264"}}selected="selected"{{/equal}}>H264</option>
									<option value="H265" {{#equal mediaConfig.mainStream.enEncode "H265"}}selected="selected"{{/equal}} {{{hideHardware "WFTR20S3 IPTR20S1"}}}>H265</option>
									<option {{#isNotSupportMJpeg ipcIdentification.hardware}}style="display: none;"{{/isNotSupportMJpeg}} value="MJPEG" {{#equal mediaConfig.mainStream.enEncode "MJPEG"}}selected="selected"{{/equal}}>MJPEG</option>
							    </select></div>
						    </div>

						    <div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-mainStream-resolution" data-desc="resolution">分辨率</label>
					    		<div><select class="custom-select" id="mediaConfig-mainStream-resolution" data-role="none" value="{{mediaConfig.mainStream.resolution}}">
							        <option value="CIF" {{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}}{{#equal mediaConfig.mainStream.resolution "CIF"}}selected="selected"{{/equal}}>CIF</option>
									<option value="D1"  {{#equal mediaConfig.mainStream.resolution "D1"}}selected="selected"{{/equal}}>D1</option>
									<option value="SVGA"{{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1" }}}{{#equal mediaConfig.mainStream.resolution "SVGA"}}selected="selected"{{/equal}}>SVGA</option>
									<option value="HVGA"  {{#equal mediaConfig.mainStream.resolution "HVGA"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>HVGA</option>
									<option value="VGA"  {{#equal mediaConfig.mainStream.resolution "VGA"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>VGA</option>
									<option {{#isNotIPC ipcIdentification.hardware}}style="display:none;"{{/isNotIPC}} value="720P(16:9)" {{#equal mediaConfig.mainStream.resolution "720P(16:9)"}}selected="selected"{{/equal}}>720P(16:9)</option>
									<option {{#isIPCdev ipcIdentification.hardware}}style="display:none;"{{/isIPCdev}} value="720P" {{#equal mediaConfig.mainStream.resolution "720P"}}selected="selected"{{/equal}}>720P</option>
									<option value="720P(16:10)" {{#equal mediaConfig.mainStream.resolution "720P(16:10)"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>720P(16:10)</option>
									<option {{#isNotIPC ipcIdentification.hardware}}style="display:none;"{{/isNotIPC}} value="960P"  {{#equal mediaConfig.mainStream.resolution "960P"}}selected="selected"{{/equal}}>960P</option>
									
									<option {{#isNotSupportFHD ipcIdentification.hardware}}hide{{/isNotSupportFHD}}{{#equal mediaConfig.videoMode "720P"}}hide{{/equal}}
										value="1080P"  {{#equal mediaConfig.mainStream.resolution "1080P"}}selected="selected"{{/equal}}>1080P</option>
									<option value="480x272"  {{#equal mediaConfig.mainStream.resolution "480x272"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>480x272</option>
									<option value="576x320"  {{#equal mediaConfig.mainStream.resolution "576x320"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>576x320</option>
									<option value="592x336"  {{#equal mediaConfig.mainStream.resolution "592x336"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>592x336</option>
									<option value="640x400"  {{#equal mediaConfig.mainStream.resolution "640x400"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>640x400</option>
									<option value="800x448"  {{#equal mediaConfig.mainStream.resolution "800x448"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>800x448</option>
									<option value="848x480"  {{#equal mediaConfig.mainStream.resolution "848x480"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>848x480</option>
									<option value="1040x576"  {{#equal mediaConfig.mainStream.resolution "1040x576"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>1040x576</option>
									<option value="1408x800"  {{#equal mediaConfig.mainStream.resolution "1408x800"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>1408x800</option>
									<option {{{hideNotBoard "32 38 42"}}} value="5MP" class="br_hide3" {{#equal mediaConfig.mainStream.resolution "5MP"}}selected="selected"{{/equal}}>5MP(2688x1944)</option>
							    </select></div>
						    </div>

						    <div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-mainStream-framerate" data-desc="framerate">帧率</label>
					    		<div><select class="custom-select" id="mediaConfig-mainStream-framerate" data-role="none" value="{{mediaConfig.mainStream.framerate}}">
							       	<option value="2" {{#equal mediaConfig.mainStream.framerate 2}}selected="selected"{{/equal}}>2fps</option>
									<option value="5" {{#equal mediaConfig.mainStream.framerate 5}}selected="selected"{{/equal}}>5fps</option>
									<option value="6" {{#equal mediaConfig.mainStream.framerate 6}}selected="selected"{{/equal}}>6fps</option>
									<option value="10" {{#equal mediaConfig.mainStream.framerate 10}}selected="selected"{{/equal}}>10fps</option>
									<option value="12" {{#equal mediaConfig.mainStream.framerate 12}}selected="selected"{{/equal}}>12fps</option>
									<option value="20" {{#equal mediaConfig.mainStream.framerate 20}}selected="selected"{{/equal}}>20fps</option>
									<option value="24" {{#equal mediaConfig.mainStream.framerate 24}}selected="selected"{{/equal}}>24fps</option>
									<option value="25" {{#equal mediaConfig.mainStream.framerate 25}}selected="selected"{{/equal}}>25fps</option>
									<option {{#isCustomer "000025"}}style="display:none;"{{/isCustomer}} value="30" {{#equal mediaConfig.mainStream.framerate 30}}selected="selected"{{/equal}}>30fps</option>
							    </select></div>
						    </div>

						    <div class="custom-select-box Usr-Install">
								<label class="single_option_text" for="mediaConfig-mainStream-bitrate" data-desc="bitrate">码率</label>
					    		<div><select class="custom-select" id="mediaConfig-mainStream-bitrate" data-role="none" value="{{mediaConfig.mainStream.bitrate}}">
							        <option value="64" {{#equal mediaConfig.mainStream.bitrate 64}}selected="selected"{{/equal}}{{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32V3"}}}>64Kbps</option>
									<option value="128" {{#equal mediaConfig.mainStream.bitrate 128}}selected="selected"{{/equal}}{{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32V3"}}}>128Kbps</option>
									<option value="256" {{#equal mediaConfig.mainStream.bitrate 256}}selected="selected"{{/equal}}{{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32V3"}}}>256Kbps</option>
									<option value="512" {{#equal mediaConfig.mainStream.bitrate 512}}selected="selected"{{/equal}}{{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32V3"}}}>512Kbps</option>
									<option value="1024" {{#equal mediaConfig.mainStream.bitrate 1024}}selected="selected"{{/equal}}>1Mbps</option>
									<option value="2048" {{#equal mediaConfig.mainStream.bitrate 2048}}selected="selected"{{/equal}}>2Mbps</option>
									<option value="3072" {{#equal mediaConfig.mainStream.bitrate 3072}}selected="selected"{{/equal}}>3Mbps</option>
									<option value="4096" {{#equal mediaConfig.mainStream.bitrate 4096}}selected="selected"{{/equal}}>4Mbps</option>
									<option value="5120" {{#equal mediaConfig.mainStream.bitrate 5120}}selected="selected"{{/equal}}{{#isNotSupportFHD ipcIdentification.hardware}}style="display: none;"{{/isNotSupportFHD}}>5Mbps</option>
									<option value="6144" {{#equal mediaConfig.mainStream.bitrate 6144}}selected="selected"{{/equal}}{{#isNotSupportFHD ipcIdentification.hardware}}style="display: none;"{{/isNotSupportFHD}}>6Mbps</option>
									<option value="8192" {{#equal mediaConfig.mainStream.bitrate 8192}}selected="selected"{{/equal}}{{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32N1 ADA32E1 ADA32V3 ADA900V1 MN234 ADA38N1"}}}>8Mbps</option>
									<option value="10240" {{#equal mediaConfig.mainStream.bitrate 10240}}selected="selected"{{/equal}}{{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32N1 ADA32E1 ADA32V3 ADA900V1 MN234 IPTR20S1 ADA38N1"}}}>10Mbps</option>
									<option {{#isNotCustomer "202023 200032"}}style="display:none;"{{/isNotCustomer}} value="10240" {{#equal mediaConfig.mainStream.bitrate 10240}}selected="selected"{{/equal}}{{{hideNotHardware "IPCR20S3"}}}>10Mbps</option>
									<option value="15360" class="br_hide1" {{#equal mediaConfig.mainStream.bitrate 15360}}selected="selected"{{/equal}}{{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32N1 ADA32E1 IPCR20S4 ADA900V1 MN234 IPTR20S1 ADA38N1"}}}>15Mbps</option>
									<option value="20480" class="br_hide1" {{#equal mediaConfig.mainStream.bitrate 20480}}selected="selected"{{/equal}}{{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32N1 ADA32E1 IPCR20S4 ADA900V1 MN234 IPTR20S1 ADA38N1"}}}>20Mbps</option>
									<option value="35840" class="br_hide2" {{#equal mediaConfig.mainStream.bitrate 35840}}selected="selected"{{/equal}}{{{hideNotHardware "IPCR20S4"}}}>35Mbps</option>
									<option value="51200" class="br_hide2" {{#equal mediaConfig.mainStream.bitrate 51200}}selected="selected"{{/equal}}{{{hideNotHardware "IPCR20S4"}}}>50Mbps</option>
							   </select></div>
						    </div>

							<div class="custom-select-box Usr-Install">
								<label class="single_option_text" for="mediaConfig-mainStream-rcMode" data-desc="rc-mode">码率控制模式</label>
					    		<div><select class="custom-select" id="mediaConfig-mainStream-rcMode" data-role="none" value="{{mediaConfig.mainStream.rcMode}}">
							        <option class="mainstream_rc1" value="VBR" {{#equal mediaConfig.mainStream.rcMode "VBR"}}selected="selected"{{/equal}}>VBR</option>
									<option value="CBR" {{#equal mediaConfig.mainStream.rcMode "CBR"}}selected="selected"{{/equal}}>CBR</option>
									<option class="mainstream_rc1" value="ABR" {{#equal mediaConfig.mainStream.rcMode "ABR"}}selected="selected"{{/equal}}>ABR</option>
									<option class="mainstream_rc2" value="FIXQP" {{#equal mediaConfig.mainStream.rcMode "FIXQP"}}selected="selected"{{/equal}}{{{hideNotHardware "IPCR20S4 IPTR20S1"}}}>FIXQP</option>
							    </select></div>
							</div>

							<div data-role="none" class="input-text-box Usr-Install">
								<label data-role="none" id="tab-mainifrm-interval" for="mediaConfig-mainStream-ifrmInterval" data-desc="ifrm-interval">I帧间隔</label>
								<input data-role="none" type="number" id="mediaConfig-mainStream-ifrmInterval" class="ifrm-limit" value="{{mediaConfig.mainStream.ifrmInterval}}">
						    </div>

							<div data-role="none" class="input-text-box" style="display: none;" id="div-mainQ-value">
								<label data-role="none" for="mediaConfig-mainStream-mainQ" data-desc="q-factor">Q(1-99)</label>
								<input data-role="none" type="text" id="mediaConfig-mainStream-mainQ" value="{{mediaConfig.mainStream.mainQ}}">
						    </div>

						</div>
						<div id="tab-chn-1" class="tab-chn-conf"> <!-- 子码流 -->
							<div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-subStream-enEncode" data-desc="enEncode">编码格式</label>
					    		<div><select class="custom-select" id="mediaConfig-subStream-enEncode" data-role="none" value="{{mediaConfig.subStream.enEncode}}">
							        <option value="H264" {{#equal mediaConfig.subStream.enEncode "H264"}}selected="selected"{{/equal}}>H264</option>
									<option value="H265" {{{hideHardware "IPTR20S1 ADA32C4"}}} {{#equal mediaConfig.subStream.enEncode "H265"}}selected="selected"{{/equal}}>H265</option>
							    </select></div>
						    </div>
						    <div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-subStream-resolution" data-desc="resolution">分辨率</label>
					    		<div><select class="custom-select" id="mediaConfig-subStream-resolution" data-role="none" value="{{mediaConfig.subStream.resolution}}">
									<option value="CIF" {{{hideHardware "ADA32N1 ADA900V1 ADA38N1"}}} {{#equal mediaConfig.subStream.resolution "CIF"}}selected="selected"{{/equal}}>CIF</option>
									<option value="D1" {{#equal mediaConfig.subStream.resolution "D1"}}selected="selected"{{/equal}}>D1</option>
									<option value="540P"{{{hideNotBoard "32 42"}}}{{#equal mediaConfig.subStream.resolution "540P"}}selected="selected"{{/equal}}>540P</option>
							    </select></div>
						    </div>
							<div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-subStream-framerate" data-desc="framerate">帧率</label>
					    		<div><select class="custom-select" id="mediaConfig-subStream-framerate" data-role="none" value="{{mediaConfig.subStream.framerate}}">
							        <option value="2" {{#equal mediaConfig.subStream.framerate 2}}selected="selected"{{/equal}}>2fps</option>
									<option value="6" {{#equal mediaConfig.subStream.framerate 6}}selected="selected"{{/equal}}>6fps</option>
									<option value="12" {{#equal mediaConfig.subStream.framerate 12}}selected="selected"{{/equal}}>12fps</option>
									<option value="20" {{#equal mediaConfig.subStream.framerate 20}}selected="selected"{{/equal}}>20fps</option>
									<option value="25" {{#equal mediaConfig.subStream.framerate 25}}selected="selected"{{/equal}}>25fps</option>
									<option {{{hideHardware "ADA32N1 ADA38N1"}}}{{#isCustomer "000025"}}style="display:none;"{{/isCustomer}} value="30" {{#equal mediaConfig.subStream.framerate 30}}selected="selected"{{/equal}}>30fps</option>

							    </select></div>
						    </div>
						    <div class="custom-select-box Usr-Install">
								<label class="single_option_text" for="mediaConfig-subStream-bitrate" data-desc="bitrate">码率</label>
					    		<div><select class="custom-select" id="mediaConfig-subStream-bitrate" data-role="none" value="{{mediaConfig.subStream.bitrate}}">
									<option value="128" {{#equal mediaConfig.subStream.bitrate 128}}selected="selected"{{/equal}}>128Kbps</option>
									<option value="256" {{#equal mediaConfig.subStream.bitrate 256}}selected="selected"{{/equal}}>256Kbps</option>
									<option value="512" {{#equal mediaConfig.subStream.bitrate 512}}selected="selected"{{/equal}}>512Kbps</option>
									<option value="1024" {{#equal mediaConfig.subStream.bitrate 1024}}selected="selected"{{/equal}}>1Mbps</option>
									<option value="2048" {{#equal mediaConfig.subStream.bitrate 2048}}selected="selected"{{/equal}}>2Mbps</option>

							    </select></div>
						    </div>		
							<div class="custom-select-box Usr-Install">
								<label class="single_option_text" for="mediaConfig-subStream-rcMode" data-desc="rc-mode">码率控制模式</label>
					    		<div><select class="custom-select" id="mediaConfig-subStream-rcMode" data-role="none" value="{{mediaConfig.subStream.rcMode}}">
							       <option value="VBR" {{#equal mediaConfig.subStream.rcMode "VBR"}}selected="selected"{{/equal}}>VBR</option>
									<option value="CBR" {{#equal mediaConfig.subStream.rcMode "CBR"}}selected="selected"{{/equal}}>CBR</option>
									<option value="ABR" {{#equal mediaConfig.subStream.rcMode "ABR"}}selected="selected"{{/equal}}>ABR</option>

							    </select></div>
						    </div>
							<div data-role="none" class="input-text-box Usr-Install">
								<label data-role="none" id="tab-subifrm-interval" for="mediaConfig-subStream-ifrmInterval" data-desc="ifrm-interval">I帧间隔</label>
								<input data-role="none" type="number" id="mediaConfig-subStream-ifrmInterval" class="ifrm-limit" value="{{mediaConfig.subStream.ifrmInterval}}">
						    </div>
						</div>
						<div id="tab-chn-2" class="tab-chn-conf Usr-Install"> <!-- 图片流 -->
							<div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-jpegStream-resolution" data-desc="resolution">分辨率</label>
					    		<div><select class="custom-select" id="mediaConfig-jpegStream-resolution" data-role="none" value="{{mediaConfig.jpegStream.resolution}}">
									<option value="CIF" {{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}} {{#equal mediaConfig.jpegStream.resolution "CIF"}}selected="selected"{{/equal}}>CIF</option>
									<option value="HVGA"  {{#equal mediaConfig.jpegStream.resolution "HVGA"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>HVGA</option>
									<option {{#isNotIPC ipcIdentification.hardware}}style="display:none;"{{/isNotIPC}} value="VGA" {{#equal mediaConfig.jpegStream.resolution "VGA"}}selected="selected"{{/equal}}{{{hideCustomer "111597"}}}>VGA</option>
									<option value="D1" {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA900V1 ADA32IR " }}} {{#equal mediaConfig.jpegStream.resolution "D1"}}selected="selected"{{/equal}}>D1</option>
									<option {{#isNotIPC ipcIdentification.hardware}}style="display:none;"{{/isNotIPC}} value="720P(16:9)" {{#equal mediaConfig.jpegStream.resolution "720P(16:9)"}}selected="selected"{{/equal}}>720P(16:9)</option>
									<option {{#isIPCdev ipcIdentification.hardware}}style="display:none;"{{/isIPCdev}} value="720P" {{#equal mediaConfig.jpegStream.resolution "720P"}}selected="selected"{{/equal}}>720P</option>
									<option {{#isNotSupportFHD ipcIdentification.hardware}}hide{{/isNotSupportFHD}} value="1080P" {{#equal mediaConfig.jpegStream.resolution "1080P"}}selected="selected"{{/equal}}>1080P</option>
									<option value="640x400"  {{#equal mediaConfig.jpegStream.resolution "640x400"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>640x400</option>
									<option value="848x480"  {{#equal mediaConfig.jpegStream.resolution "848x480"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>848x480</option>
									<option value="1040x576"  {{#equal mediaConfig.jpegStream.resolution "1040x576"}}selected="selected"{{/equal}}{{{hideNotCustomer "201894"}}}>1040x576</option>
							    </select></div>
						    </div>
						</div>
						<div id="tab-chn-3" class="tab-chn-conf" {{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA900V1 ADA32IR DMS31V2 DMS885N ADA47V1 HDW845V1"}}}>
							<div class="custom-select-box"  {{{hideHardware "ADA32V4"}}}>
								<label class="single_option_text" for="mediaConfig-voStream-resolution" data-desc="resolution">分辨率</label>
					    		<div><select class="custom-select" id="mediaConfig-voStream-resolution" data-role="none" value="{{mediaConfig.voStream.resolution}}">
									<option value="720P" {{#equal mediaConfig.voStream.resolution "720P"}}selected="selected"{{/equal}}>720P{{{display720PScale}}}</option>
									<option value="1080P" {{#equal mediaConfig.voStream.resolution "1080P"}}selected="selected"{{/equal}} {{{hideHardware "ADA32V3"}}}>1080P</option>
							    </select></div>
						    </div>
							<div class="custom-select-box"  {{{hideHardware "ADA32V4"}}}>
								<label class="single_option_text" for="mediaConfig-voStream-framerate" data-desc="framerate">帧率</label>
					    		<div><select class="custom-select" id="mediaConfig-voStream-framerate" data-role="none" value="{{mediaConfig.voStream.framerate}}">
									<option value="25" {{#equal mediaConfig.voStream.framerate 25}}selected="selected"{{/equal}}>25fps</option>
									<option value="30" {{#equal mediaConfig.voStream.framerate 30}}selected="selected"{{/equal}}>30fps</option>

							    </select></div>
							</div>
							<div class="custom-select-box" {{{hideNotBoard "25"}}}>
								<label class="single_option_text" for="mediaConfig-voStream-split" data-desc="split">显示模式</label>
								<div><select class="custom-select" id="mediaConfig-voStream-split" data-role="none" value="{{mediaConfig.voStream.split}}">
									<option value="0" {{#equal mediaConfig.voStream.split 0}}selected="selected"{{/equal}}>VISCAM</option>
									<option value="1" {{#equal mediaConfig.voStream.split 1}}selected="selected"{{/equal}}>DIV2</option>
									<option value="2" {{#isIRCam ipcIdentification.board}}style="display: none"{{/isIRCam}}{{#equal mediaConfig.voStream.split 2}}selected="selected"{{/equal}}>DIV4</option>
									<option value="5" {{#equal mediaConfig.voStream.split 5}}selected="selected"{{/equal}}>IRCAM</option>
									<option value="6" {{#equal mediaConfig.voStream.split 6}}selected="selected"{{/equal}}>IRCAM_OSD</option>
									<option value="7" {{#equal mediaConfig.voStream.split 7}}selected="selected"{{/equal}}>IRCAM_OSD2</option>
									<!--
									<option value="8" {{#equal mediaConfig.voStream.split 8}}selected="selected"{{/equal}}>IRCAM_VIS</option>
									<option value="9" {{#equal mediaConfig.voStream.split 9}}selected="selected"{{/equal}}>IRCAM_IR</option>
									-->
							    </select></div>
						    </div>
							<div class="custom-select-box Usr-Install" {{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 RCT16947V2 ADA32IR DMS31V2"}}}>
							<!-- <label class="single_option_text" for="mediaConfig-extscreenStream-mode" data-desc="CVBSStrem-mode">模式</label> -->
                                <label class="single_option_text" for="mediaConfig-extscreenStream-mode">{{#isHardware "ADA32V4"}}{{getKeyLang "AHDorCVBS-mode"}}{{/isHardware}}{{#isNotHardware "ADA32V4"}}{{getKeyLang "CVBSStrem-mode"}}{{/isNotHardware}}</label>
								<div><select class="custom-select" id="mediaConfig-extscreenStream-mode" data-role="none" value="{{mediaConfig.extscreenStream.mode}}">
								   <option value="NTSC" {{#equal mediaConfig.extscreenStream.mode "NTSC"}}selected="selected"{{/equal}}>NTSC</option>
								   <option value="PAL"  {{#equal mediaConfig.extscreenStream.mode "PAL"}}selected="selected"{{/equal}}>PAL</option>
								</select></div>
							</div>
						</div>
			    		<div id="tab-chn-4" class="tab-chn-conf" style="display:none"> <!-- 红外流 -->
			    			<div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-irStream-enEncode" data-desc="enEncode">编码格式</label>
					    		<div><select class="custom-select" id="mediaConfig-irStream-enEncode" data-role="none" value="{{mediaConfig.irStream.enEncode}}">
							        <option value="H264" {{#equal mediaConfig.irStream.enEncode "H264"}}selected="selected"{{/equal}}>H264</option>
									<option value="H265" {{#equal mediaConfig.irStream.enEncode "H265"}}selected="selected"{{/equal}}>H265</option>
							    </select></div>
						    </div>

						    <div class="custom-select-box">
								<label class="single_option_text" for="mediaConfig-irStream-resolution" data-desc="resolution">分辨率</label>
					    		<div><select class="custom-select" id="mediaConfig-irStream-resolution" data-role="none" value="{{mediaConfig.irStream.resolution}}">
									<option value="D1"    {{#equal mediaConfig.irStream.resolution "D1"}}selected="selected"{{/equal}}>D1</option>
									<option value="720P"  {{#equal mediaConfig.irStream.resolution "720P"}}selected="selected"{{/equal}}>720P</option>
									<option	value="1080P" {{#equal mediaConfig.irStream.resolution "1080P"}}selected="selected"{{/equal}}>1080P</option>
							    </select></div>
						    </div>

						    <div class="custom-select-box Usr-Install">
								<label class="single_option_text" for="mediaConfig-irStream-bitrate" data-desc="bitrate">码率</label>
					    		<div><select class="custom-select" id="mediaConfig-irStream-bitrate" data-role="none" value="{{mediaConfig.irStream.bitrate}}">
							        <option value="64" {{#equal mediaConfig.irStream.bitrate 64}}selected="selected"{{/equal}}>64Kbps</option>
									<option value="128" {{#equal mediaConfig.irStream.bitrate 128}}selected="selected"{{/equal}}>128Kbps</option>
									<option value="256" {{#equal mediaConfig.irStream.bitrate 256}}selected="selected"{{/equal}}>256Kbps</option>
									<option value="512" {{#equal mediaConfig.irStream.bitrate 512}}selected="selected"{{/equal}}>512Kbps</option>
									<option value="1024" {{#equal mediaConfig.irStream.bitrate 1024}}selected="selected"{{/equal}}>1Mbps</option>
									<option value="2048" {{#equal mediaConfig.irStream.bitrate 2048}}selected="selected"{{/equal}}>2Mbps</option>
							   </select></div>
						    </div>

							<div class="custom-select-box Usr-Install">
								<label class="single_option_text" for="mediaConfig-irStream-rcMode" data-desc="rc-mode">码率控制模式</label>
					    		<div><select class="custom-select" id="mediaConfig-irStream-rcMode" data-role="none" value="{{mediaConfig.irStream.rcMode}}">
							        <option class="irstream_rc1" value="VBR" {{#equal mediaConfig.irStream.rcMode "VBR"}}selected="selected"{{/equal}}>VBR</option>
									<option value="CBR" {{#equal mediaConfig.irStream.rcMode "CBR"}}selected="selected"{{/equal}}>CBR</option>
									<option class="irstream_rc1" value="ABR" {{#equal mediaConfig.irStream.rcMode "ABR"}}selected="selected"{{/equal}}>ABR</option>
							    </select></div>
							</div>
						</div>						
		    		</div>	
				</div>
					<div id="cfg-chn-1" class="media-chn-conf" style="display:none">
						<div class="item">
							<h1><p data-desc="image-process">图像处理</p></h1>
							<div class="custom-select-box" style="display:none;">
								<label class="single_option_text" for="mediaConfig-irMediaConfig-resolution" data-desc="resolution">分辨率</label>
								<div><select class="custom-select" id="mediaConfig-irMediaConfig-resolution" data-role="none" value="{{mediaConfig.irMediaConfig.resolution}}">
									<option value="256x192" {{#equal mediaConfig.irMediaConfig.resolution "256x192"}}selected="selected"{{/equal}}>256X192</option>
									<option value="384x288" {{#equal mediaConfig.irMediaConfig.resolution "384x288"}}selected="selected"{{/equal}} style="display: none">384X288</option>									
								</select></div>
							</div>							
							<div class="config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-irMediaConfig-mirrorEnable" {{#if mediaConfig.irMediaConfig.mirrorEnable}}checked="checked"{{/if}}/>
								<label data-role="none" for="mediaConfig-irMediaConfig-mirrorEnable"></label>
								<p data-desc="image-mirror">画面镜像</p>	
							</div>
							<div class="config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-irMediaConfig-flipEnable" {{#if mediaConfig.irMediaConfig.flipEnable}}checked="checked"{{/if}}/>
								<label data-role="none" for="mediaConfig-irMediaConfig-flipEnable"></label>
								<p data-desc="image-flip">画面翻转</p>	
							</div>	
						</div>						
					</div>
				</div>
				<div class="configmenu" {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR ADA47V1 HDW845V1 ADA32C4"}}} {{#isNotCustomer "202615"}}{{{hideNotWebuiFull "DMS31V2 DMS885N"}}}{{/isNotCustomer}}{{#isA38}}style="display: none;"{{/isA38}}>
					<h1><p>{{getKeyLang "audio"}}</p></h1>
					<div class="item" {{{hideDoubleAudioSourceDevice}}}>
						<div data-role="none" class="input-switch-box">
							<p {{#isNotHardware "DMS31V2 DMS885N"}}data-desc="audio-enable"{{/isNotHardware}} {{#isHardware "DMS31V2 DMS885N"}}data-desc="dms-audio-enable"{{/isHardware}}>音频使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="mediaConfig-audio-enable" {{#if mediaConfig.audio.enable}}checked="checked"{{/if}}><label
										data-role="none" for="mediaConfig-audio-enable"></label>
						</div>
					</div>
					<div class="item" {{{hideNotDoubleAudioSourceDevice}}}>
						<div data-role="none" class="input-switch-box">
							<p data-desc="micAudio-enable">MIC音频使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="mediaConfig-audio-micEnable" {{#if mediaConfig.audio.micEnable}}checked="checked"{{/if}}><label
										data-role="none" for="mediaConfig-audio-micEnable"></label>
						</div>
					</div>
					<div class="item" {{#NotAudioSetting ipcIdentification.hardware}}style="display: none;"{{/NotAudioSetting}}>
						<div class="custom-select-box">
							<label class="single_option_text" for="mediaConfig-audio-encType" data-desc="aud-fmt">音频格式</label>
				    		<div><select class="custom-select" id="mediaConfig-audio-encType" data-role="none" value="{{mediaConfig.audio.encType}}">
								<option value="G.711A" {{#equal mediaConfig.audio.encType "G.711A"}}selected="selected"{{/equal}}>G.711A</option>
								<option{{#isWFdev ipcIdentification.hardware}}hide{{/isWFdev}} {{{hideHardware "ADA32N1 ADA32E1 ADA32C4 ADA900V1 MN234 ADA38N1"}}} value="G.711U" {{#equal mediaConfig.audio.encType "G.711U"}}selected="selected"{{/equal}}>G.711U</option>
								<option{{#isWFdev ipcIdentification.hardware}}hide{{/isWFdev}} {{{hideHardware "ADA32N1 ADA32E1 ADA32C4 ADA900V1 MN234 ADA38N1"}}} value="LPCM" {{#equal mediaConfig.audio.encType "LPCM"}}selected="selected"{{/equal}}>LPCM</option>
						    </select></div>
					    </div>
						<div class="custom-select-box">
							<label class="single_option_text" for="mediaConfig-audio-sampleRate" data-desc="aud-sr">音频采样率</label>
				    		<div><select class="custom-select" id="mediaConfig-audio-sampleRate" data-role="none" value="{{mediaConfig.audio.sampleRate}}">
								<option value="8KHz" {{#equal mediaConfig.audio.sampleRate "8KHz"}}selected="selected"{{/equal}}>8KHz</option>
								<option value="16KHz" {{#equal mediaConfig.audio.sampleRate "16KHz"}}selected="selected"{{/equal}}>16KHz</option>
								<option {{#isNotSupport32K ipcIdentification.hardware}}style="display:none;"{{/isNotSupport32K}} value="32KHz" {{#equal mediaConfig.audio.sampleRate "32KHz"}}selected="selected"{{/equal}}>32KHz</option>
						    </select></div>
					    </div>
					</div>	
					<div class="item" {{#NotAudioSetting ipcIdentification.hardware}}style="display: none;"{{/NotAudioSetting}}>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title" data-desc="volume">音量</p>
							<label data-role="none" for="mediaConfig-audio-volume"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="mediaConfig-audio-volume" min="1" max="30" value="{{mediaConfig.audio.volume}}">
							<p class="rangeinput_value">{{mediaConfig.audio.volume}}</p>
						</div>
					</div>
				</div>	
				<div class="configmenu" {{{hideUSBCam}}} >
					<h1><p>{{getKeyLang "osd display"}}</p></h1>
					<div class="item">
						<h1><p data-desc="switch">Switch</p></h1>
						{{#isNotSupportAlg ipcIdentification.hardware}}<!--{{/isNotSupportAlg}}
						{{#isNotArray mediaConfig.osdConf.showGuiMask}}
						<div class="config_checkbox " {{{hideHardware "ADA42PTZV1 ADA32N1 ADA32E1 ADA32C4 MN234 ADA38N1"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-3" {{#mask mediaConfig.osdConf.showGuiMask 3}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-3"></label>
							<p data-desc="vo-stream">输出流</p>
						</div>
						<div class="sel-td 1106-td config_checkbox" {{{hideNotWebuiFull "ADA32V2 AICB046V1 ADA32V3 ADA32IR"}}} {{{hideHardware "ADA42PTZV1"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-0" {{#mask mediaConfig.osdConf.showGuiMask 0}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-0"></label>
							<p data-desc="main-stream">主码流</p>	
						</div>
						<div class="sel-td 1106-td config_checkbox" {{{hideNotWebuiFull "ADA32V2 AICB046V1  ADA32V3 ADA32IR"}}} {{{hideHardware "ADA42PTZV1"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-1" {{#mask mediaConfig.osdConf.showGuiMask 1}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-1"></label>
							<p data-desc="sub-stream">子码流</p>
						</div>
						<div class="sel-td config_checkbox" {{{hideNotWebuiFull "ADA32V2 AICB046V1  ADA32IR"}}} {{{hideHardware "ADA42PTZV1 ADA32V3"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-2" {{#mask mediaConfig.osdConf.showGuiMask 2}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-2"></label>
							<p data-desc="pic-stream">图片流</p>
						</div>

						<div class="config_checkbox " {{{hideNotHardware "ADA42PTZV1"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-4" {{#mask mediaConfig.osdConf.showGuiMask 1}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-4"></label>
							<p data-desc="vo-stream">输出流</p>
						</div>
						<div class="config_checkbox" {{{hideNotHardware "ADA42PTZV1"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-5" {{#mask mediaConfig.osdConf.showGuiMask 0}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-5"></label>
							<p data-desc="main-stream">主码流</p>
						</div>
						{{/isNotArray}}
						{{#isArray mediaConfig.osdConf.showGuiMask}}
						{{#each mediaConfig.osdConf.showGuiMask}}
						<div class="config_checkbox cfg-chn-{{@index}} multi-chn-cfg">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-{{@index}}-0" {{#mask this 0}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-{{@index}}-0"></label>
							<p data-desc="main-stream">主码流</p>
						</div>
						<div class="config_checkbox cfg-chn-{{@index}} multi-chn-cfg">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-{{@index}}-1" {{#mask this 1}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-{{@index}}-1"></label>
							<p data-desc="sub-stream">子码流</p>
						</div>
						<div class="config_checkbox cfg-chn-{{@index}} multi-chn-cfg">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-{{@index}}-2" {{#mask this 2}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-{{@index}}-2"></label>
							<p data-desc="pic-stream">图片流</p>
						</div>
						<div class="config_checkbox cfg-chn-{{@index}} multi-chn-cfg">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showGuiMask-{{@index}}-3" {{#mask this 3}}checked="checked"{{/mask}}/>
							<label data-role="none" for="mediaConfig-osdConf-showGuiMask-{{@index}}-3"></label>
							<p data-desc="vo-stream">输出流</p>
						</div>
						{{/each}}
						{{/isArray}}
						{{#isNotSupportAlg ipcIdentification.hardware}}-->{{/isNotSupportAlg}}


						<div class="config_checkbox" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showTime" {{#if mediaConfig.osdConf.showTime}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-osdConf-showTime"></label>
							<p data-desc="show-time">显示时间</p>	
						</div>
	

						<div class="config_checkbox" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR DMS31V2 DMS885N ADA47V1"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showChnName" {{#if mediaConfig.osdConf.showChnName}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-osdConf-showChnName"></label>
							<p data-desc="show-chnname">显示通道名</p>	
						</div>
						<div class="custom-select-box" {{{hideNotHardware "IPCR20S3"}}}{{{hideNotCustomer "111597" }}}>
							<label class="single_option_text" for="mediaConfig-osdConf-osdPosition" data-desc="osd-position">OSD位置</label>
							<div><select class="custom-select" id="mediaConfig-osdConf-osdPosition" data-role="none" value="{{mediaConfig.osdConf.osdPosition}}">
								<option value="0" {{#equal mediaConfig.osdConf.osdPosition 0}}selected="selected"{{/equal}} data-desc="Above">Above</option>
								<!--<option value="1" {{#equal mediaConfig.osdConf.osdPosition 1}}selected="selected"{{/equal}} data-desc="Below">Below</option>-->
								<option value="2" {{#equal mediaConfig.osdConf.osdPosition 2}}selected="selected"{{/equal}} data-desc="Separated">Separated</option>
							</select></div>
						</div>
						<div class="config_checkbox" {{#isNotCustomer "200032"}}style="display:none;"{{/isNotCustomer}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showRoIcon" {{#if mediaConfig.osdConf.showRoIcon}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-osdConf-showRoIcon"></label>
							<p data-desc="show-roicon">显示旋转图标</p>	
						</div>
						<div class="config_checkbox" {{{hideNotCustomer "202615" }}} {{{hideNotHardware "DMS31V2 DMS885N"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showCsrIcon" {{#if mediaConfig.osdConf.showCsrIcon}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-osdConf-showCsrIcon"></label>
							<p data-desc="show-csricon">显示图标</p>	
						</div>
						<div class="config_checkbox" {{{AlarmIconEnable}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showAlarmIcon" {{#if mediaConfig.osdConf.showAlarmIcon}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-osdConf-showAlarmIcon"></label>
							<p data-desc="show-alarmicon">显示报警图标</p>	
						</div>
						<div class="config_checkbox" {{{hideNotHardware "HDW845V1" }}} >
							<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-osdConf-showZoom" {{#if mediaConfig.osdConf.showZoom}}checked="checked"{{/if}}/>
							<label data-role="none" for="mediaConfig-osdConf-showZoom"></label>
							<p data-desc="show-zoom">显示放大倍数</p>	
						</div>
						</div>
					</div>
					<div class="item">
						<div class="custom-select-box" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR"}}}>
							<label class="single_option_text" for="mediaConfig-osdConf-timeFormat" data-desc="time-format">时间格式</label>
				    		<div><select class="custom-select" id="mediaConfig-osdConf-timeFormat" data-role="none" value="{{mediaConfig.osdConf.timeFormat}}">
								<option value="0" {{#equal mediaConfig.osdConf.timeFormat 0}}selected="selected"{{/equal}} data-desc="YYYY-MM-DD">YYYY-MM-DD</option>
								<option value="1" {{#equal mediaConfig.osdConf.timeFormat 1}}selected="selected"{{/equal}} data-desc="MM-DD-YYYY">MM-DD-YYYY</option>
								<option value="2" {{#equal mediaConfig.osdConf.timeFormat 2}}selected="selected"{{/equal}} data-desc="DD-MM-YYYY">DD-MM-YYYY</option>
							</select></div>
					    </div>
						<div data-role="none" class="input-text-box" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR DMS31V2 DMS885N ADA47V1"}}}>
							<label data-role="none" for="mediaConfig-osdConf-channelName" data-desc="channel-name">通道名</label>
							<input data-role="none" type="text" user-select="none" id="mediaConfig-osdConf-channelName" oninput="handleInput(event)" class="empty-limit char-normal" value="{{mediaConfig.osdConf.channelName}}">
					    </div>
					</div>
				</div>				
			</div>
			<div id="algConfig" class="tab-configs" {{{hideHardware "ADA32C4"}}}>
				<div class="configmenu" {{{hideHardware "HDW845V1 MN234 ADA38N1 ADA32C4"}}}>
					<h1><p>{{getKeyLang "audio-settings"}}</p></h1>
				</div>
				<div data-role="none" class="rangeinput" {{{hideHardware "HDW845V1 ADA38N1 ADA32C4"}}}>
					<p class="rangeinput_title">{{getKeyLang "volume"}}</p>
					<label data-role="none" for="algConfig-audioVolume"></label>
					<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-audioVolume" min="0" max="100" value="{{algConfig.audioVolume}}">
					<p class="rangeinput_value">{{algConfig.audioVolume}}</p>
				</div>
				<div data-role="none" class="rangeinput" {{{hideUSBCam}}} {{{hideHardware "ADA900V1 ADA32N1 ADA32C4 ADA32E1 DMS31V2 DMS885N HDW845V1 MN234 ADA38N1"}}} {{{hideBoard "26"}}} >
					<p class="rangeinput_title">{{getKeyLang "alarmervolume"}}</p>
					<label data-role="none" for="algConfig-pdsAlarmerVolume"></label>
					<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsAlarmerVolume" min="0" max="8" value="{{algConfig.pdsAlarmerVolume}}">
					<p class="rangeinput_value">{{algConfig.pdsAlarmerVolume}}</p>
				</div>
				<div {{{hideNotHardware "ADA32IR HDW845V1"}}}>
					<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-top: 35px">
						<td><li><a id="alg-ch1" class="sel-alg-chn menu_checked" value="ch1" data-tabchn-id="alg-chn-0" data-desc="chn1" data-chn-idx="0">CH1</a></li></td>
						<td ><li><a id="alg-ch2" class="sel-alg-chn" value="ch2" data-tabchn-id="alg-chn-1" data-desc="chn2" data-chn-idx="1">CH2</a></li></td>
					</table>
				</div>
				<div class="configmenu alg-conf alg-audio-conf Usr-Intg Usr-Install" {{#isNotSupportPD ipcIdentification.hardware}}style="display: none;"{{/isNotSupportPD}} {{{hideHardware "HDW845V1 ADA32C4"}}}>
					{{#each algConfig.pdsConf}}
					{{#if @first}}
					<div class="alg-conf pd-chn-{{@index}}">
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-audioType" data-desc="audio-type">audio</label>
								<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-audioType" data-role="none" value="{{audioType}}">
									<option value="1" {{#equal audioType 1}}selected="selected"{{/equal}} data-desc="DULU">DULU</option>
									<option value="2" {{#equal audioType 2}}selected="selected"{{/equal}} data-desc="TRAIN">TRAIN</option>
									<option value="3" {{#equal audioType 3}}selected="selected"{{/equal}} data-desc="DO">DO</option>
									<option value="4" {{#equal audioType 4}}selected="selected"{{/equal}} data-desc="PHONE">PHONE</option>
									<option value="5" {{#equal audioType 5}}selected="selected"{{/equal}} data-desc="DIDU">DIDU</option>
									<option value="6" {{#equal audioType 6}}selected="selected"{{/equal}} data-desc="DING">DING</option>

									<option value="7"  {{#equal audioType  7}}selected="selected"{{/equal}} {{{hideNotHardware "ADA32V2 AICB046V1"}}} {{{hideNotCustomer "201570"}}}>Caution</option>
									<option value="8"  {{#equal audioType  8}}selected="selected"{{/equal}} {{{hideNotHardware "ADA32V2 AICB046V1"}}} {{{hideNotCustomer "201570"}}}>Check ahead</option>
									<option value="9"  {{#equal audioType  9}}selected="selected"{{/equal}} {{{hideNotHardware "ADA32V2 AICB046V1"}}} {{{hideNotCustomer "201570"}}}>Check behind</option>
									<option value="10" {{#equal audioType 10}}selected="selected"{{/equal}} {{{hideNotHardware "ADA32V2 AICB046V1"}}} {{{hideNotCustomer "201570"}}}>Check left</option>
									<option value="11" {{#equal audioType 11}}selected="selected"{{/equal}} {{{hideNotHardware "ADA32V2 AICB046V1"}}} {{{hideNotCustomer "201570"}}}>Check right</option>
	
								</select></div>
							</div>
					</div>
					{{/if}}
					{{/each}}			
				</div>
				
				<div class="configmenu alg-conf" id="tab-adas-conf" {{#isNotSupportADAS ipcIdentification.hardware}}style="display: none;"{{/isNotSupportADAS}}>
						<h1><p>{{getKeyLang "adas-conf"}}</p></h1>
						<div class="item">
							<h1><p>{{getKeyLang "audio"}}</p></h1>
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-adasConf-audioType" data-desc="audio-type">audio</label>
								<div><select class="custom-select" id="algConfig-adasConf-audioType" data-role="none" value="{{algConfig.adasConf.audioType}}">
									<option value="1" {{#equal audioType 1}}selected="selected"{{/equal}} data-desc="DULU">DULU</option>
									<option value="2" {{#equal audioType 2}}selected="selected"{{/equal}} data-desc="TRAIN">TRAIN</option>
									<option value="3" {{#equal audioType 3}}selected="selected"{{/equal}} data-desc="DO">DO</option>
									<option value="4" {{#equal audioType 4}}selected="selected"{{/equal}} data-desc="PHONE">PHONE</option>
									<option value="5" {{#equal audioType 5}}selected="selected"{{/equal}} data-desc="DIDU">DIDU</option>
									<option value="6" {{#equal audioType 6}}selected="selected"{{/equal}} data-desc="DING">DING</option>
								</select></div>
							</div>
							<div class="config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-adasConf-fcwEnable" {{#if algConfig.adasConf.fcwEnable}}checked="checked"{{/if}}/>
								<label data-role="none" for="algConfig-adasConf-fcwEnable"></label>
								<p data-desc="enable-fcw">Enable FCW</p>
							</div>
							<div class="config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-adasConf-ldwEnable" {{#if algConfig.adasConf.ldwEnable}}checked="checked"{{/if}}/>
								<label data-role="none" for="algConfig-adasConf-fcwEnable"></label>
								<p data-desc="enable-ldw">Enable LDW</p>
							</div>
							<div class="config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-adasConf-pdsEnable" {{#if algConfig.adasConf.pdsEnable}}checked="checked"{{/if}}/>
								<label data-role="none" for="algConfig-adasConf-fcwEnable"></label>
								<p data-desc="enable-pds">Enable PDS</p>
							</div>
						</div>
				</div>
				<div class="configmenu alg-conf" id="tab-dms-conf" {{#isNotSupportDMS ipcIdentification.hardware}}style="display: none;"{{/isNotSupportDMS}}>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-audioType" data-desc="audio-type">audio</label>
							<div><select class="custom-select" id="algConfig-dmsConf-audioType" data-role="none" value="{{algConfig.dmsConf.audioType}}">
								<option value="0" {{#equal algConfig.dmsConf.audioType 0}}selected="selected"{{/equal}}>{{getKeyLang "Voice"}}</option>
								<option value="1" {{#equal algConfig.dmsConf.audioType 1}}selected="selected"{{/equal}}>{{getKeyLang "DULU"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.audioType 2}}selected="selected"{{/equal}}>{{getKeyLang "TRAIN"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.audioType 3}}selected="selected"{{/equal}}>{{getKeyLang "DO"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.audioType 4}}selected="selected"{{/equal}}>{{getKeyLang "PHONE"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.audioType 5}}selected="selected"{{/equal}}>{{getKeyLang "DIDU"}}</option>
								<option value="6" {{#equal algConfig.dmsConf.audioType 6}}selected="selected"{{/equal}}>{{getKeyLang "DING"}}</option>
								<option value="12" {{#equal algConfig.dmsConf.audioType 12}}selected="selected"{{/equal}} {{{hideNotCustomer "202018"}}}>{{getKeyLang "Concentrate"}}</option>
							</select></div>
						</div>
						<div {{{hideNotCustomer "100249"}}}>
							<div data-role="none" class="input-switch-box">
								<p data-desc="dmsAudio-enable">报警音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioEnable"></label>
							</div>
						</div>
						<div {{{hideNotCustomer "202018"}}}>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioDisclamer-enable">免责声明音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioWelcomeEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioWelcomeEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioWelcomeEnable"></label>
							</div>
						</div>
						<div {{{hideNotCustomer "201581"}}}>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioFatigue-enable">疲劳音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioFatigueEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioFatigueEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioFatigueEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioFatigueL2-enable">二级疲劳音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioFatigueL2Enable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioFatigueL2Enable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioFatigueL2Enable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioDistraction-enable">分心音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioDistractionEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioDistractionEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioDistractionEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioNoDriver-enable">无司机音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioNoDriverEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioNoDriverEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioNoDriverEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioSmoke-enable">吸烟音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioSmokeEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioSmokeEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioSmokeEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioPhone-enable">打电话音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioPhoneEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioPhoneEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioPhoneEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioYawn-enable">打哈欠音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioYawnEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioYawnEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioYawnEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioNoMask-enable">无口罩音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioNoMaskEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioNoMaskEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioNoMaskEnable"></label>
							</div>
                            <div data-role="none" class="input-switch-box">
								<p data-desc="audioSunGlass-enable">太阳眼镜音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioSunGlassEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioSunGlassEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioSunGlassEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioSeatBelt-enable">安全带音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioSeatBeltEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioSeatBeltEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioSeatBeltEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioShelter-enable">遮挡音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioShelterEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioShelterEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioShelterEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioDrinkEat-enable">喝东西音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioDrinkEatEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioDrinkEatEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioDrinkEatEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioOverspeed-enable">超速音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioOverspeedEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioOverspeedEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioOverspeedEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="audioNoHelmet-enable">无安全帽音频使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAudioSettings-dmsAudioNoHelmetEnable" {{#if algConfig.dmsConf.dmsAudioSettings.dmsAudioNoHelmetEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsAudioSettings-dmsAudioNoHelmetEnable"></label>
							</div>
						</div>
					<h1><p>{{getKeyLang "work-settings"}}</p></h1>
						<div {{{hideNotCustomer "201581"}}}>
							<div data-role="none" class="input-switch-box">
								<p data-desc="dms-face-capture">捕获人脸</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsFaceCapture" {{#if algConfig.dmsConf.dmsFaceCapture}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-dmsConf-dmsFaceCapture"></label>
							</div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsLoginMode" data-desc="dms-login-mode">Face Recognition</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsLoginMode" data-role="none" value="{{algConfig.dmsConf.dmsLoginMode}}">
								<option value="0" {{#equal algConfig.dmsConf.dmsLoginMode 0}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="1" {{#equal algConfig.dmsConf.dmsLoginMode 1}}selected="selected"{{/equal}}>{{getKeyLang "boot"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsLoginMode 2}}selected="selected"{{/equal}}>{{getKeyLang "auto"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideCustomer "999999"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsFrInterval" data-desc="dms-fr-interval">人脸识别间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsFrInterval" data-role="none" value="{{algConfig.dmsConf.dmsFrInterval}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsFrInterval -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsFrInterval 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsFrInterval 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsFrInterval 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="900" {{#equal algConfig.dmsConf.dmsFrInterval 900}}selected="selected"{{/equal}}>{{getKeyLang "15min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsFrInterval 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{showHWandCustomer "DMS31V2" "111550"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsNoDriverStrategy" data-desc="dms-noDriver-strategy">No Driver Strategy</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsNoDriverStrategy" data-role="none" value="{{algConfig.dmsConf.dmsNoDriverStrategy}}">
								<option value="0" {{#equal algConfig.dmsConf.dmsNoDriverStrategy 0}}selected="selected"{{/equal}}>{{getKeyLang "body"}}</option>
								<option value="1" {{#equal algConfig.dmsConf.dmsNoDriverStrategy 1}}selected="selected"{{/equal}}>{{getKeyLang "face"}}</option>
							</select></div>
						</div>
                        <div {{{hideCustomer "201581"}}} {{{hideHardware "DMS885N"}}}>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{#isNotCustomer "201851"}}{{getKeyLang "work-speed+unit"}}{{/isNotCustomer}} {{#isCustomer "201851"}}{{getKeyLang "work-speed+unit2"}}{{/isCustomer}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-speed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-speed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.speed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.speed}}</p>
                            </div>
                        </div>
                        <div {{{hideNotCustomer "201581"}}}>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-fatigue"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsFatigueWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsFatigueWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsFatigueWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsFatigueWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-fatigueL2"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsFatigueL2WorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsFatigueL2WorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsFatigueL2WorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsFatigueL2WorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-distraction"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsDistractionWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsDistractionWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsDistractionWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsDistractionWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-noDriver"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsNoDriverWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsNoDriverWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsNoDriverWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsNoDriverWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-smoke"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsSmokeWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsSmokeWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsSmokeWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsSmokeWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-phone"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsPhoneWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsPhoneWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsPhoneWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsPhoneWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-yawn"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsYawnWorkspeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsYawnWorkspeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsYawnWorkspeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsYawnWorkspeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-noMask"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsNoMaskWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsNoMaskWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsNoMaskWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsNoMaskWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-sunglasses"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsSunGlassWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsSunGlassWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsSunGlassWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsSunGlassWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-seatbelt"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsSeatBeltWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsSeatBeltWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsSeatBeltWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsSeatBeltWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-drinkEat"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsDrinkEatWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsDrinkEatWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsDrinkEatWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsDrinkEatWorkSpeed}}</p>
                            </div>
                            <div data-role="none" class="rangeinput">
                                <p class="rangeinput_title">{{getKeyLang "work-speed-noHelmet"}}</p>
                                <label data-role="none" for="algConfig-dmsConf-workSpeed-dmsNoHelmetWorkSpeed"></label>
                                <input data-role="none" class="rangeinput_input" type="range" id="algConfig-dmsConf-workSpeed-dmsNoHelmetWorkSpeed" min="0" max="150" step="1" value="{{algConfig.dmsConf.workSpeed.dmsNoHelmetWorkSpeed}}">
                                <p class="rangeinput_value">{{algConfig.dmsConf.workSpeed.dmsNoHelmetWorkSpeed}}</p>
                            </div>
                        </div>
						<div class="config_checkbox" {{{hideHardware "DMS885N"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-workSpeed-almNoGPS" {{#if algConfig.dmsConf.workSpeed.almNoGPS}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-workSpeed-almNoGPS"></label>
							<p data-desc="alarming-no-GPS">无GPS信号时保持警报</p>
						</div>
						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-dmsGazeTracking" {{#if algConfig.dmsConf.dmsGazeTracking}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-dmsGazeTracking"></label>
							<p data-desc="dms-gaze-tracking">视线跟踪</p>
						</div>
						<div class="config_checkbox" {{{showHWandCustomer "DMS31V2" "202615"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-dmsFatigueOnly" {{#if algConfig.dmsConf.dmsFatigueOnly}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-dmsFatigueOnly"></label>
							<p data-desc="dms-fatigue-only">仅检测疲劳</p>
						</div>
						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAutoCalibration" {{#if algConfig.dmsConf.dmsAutoCalibration}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-dmsAutoCalibration"></label>
							<p data-desc="dms-autoCalibration">自动标定</p>
						</div>

						<!--div class="config_checkbox" {{{showHWandCustomer "DMS31V2" "111550"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-dmsReadPilotState" {{#if algConfig.dmsConf.dmsReadPilotState}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-dmsReadPilotState"></label>
							<p data-desc="dms-readPilotState">读取先导状态</p>
						</div-->

						<div class="config_checkbox" {{{showHWandCustomer "DMS31V2" "111550"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-dmsReadPilotState" {{#if algConfig.dmsConf.dmsReadPilotState}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-dmsReadPilotState"></label>
							<p data-desc="dms-readPilotState">读取先导状态</p>
						</div>
						<div class="config_checkbox" {{{showHWandCustomer "DMS31V2" "111550"}}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-dmsReadEngineRPM" {{#if algConfig.dmsConf.dmsReadEngineRPM}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-dmsReadEngineRPM"></label>
							<p data-desc="dms-readEngineRPM">读取发动机转速</p>
						</div>
						<div class="config_checkbox" {{#isNotDMS31P}}style="display: none;"{{/isNotDMS31P}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="algConfig-dmsConf-ddawRegulationEnable" {{#if algConfig.dmsConf.ddawRegulationEnable}}checked="checked"{{/if}}/><label
								data-role="none" for="algConfig-dmsConf-ddawRegulationEnable"></label>
							<p data-desc="ddaw-RegulatoryButton">DDAW法规</p>
						</div>
				<div id="ddawhide1">
					<h1><p>{{getKeyLang "alarming-interval"}}</p></h1>
						<div class="custom-select-box" style="display: none;">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-mildFatigue" data-desc="mild-fatigue-interval">轻度疲劳检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-mildFatigue" data-role="none" value="{{algConfig.dmsConf.dmsInterval.mildFatigue}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.mildFatigue 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" style="display: none;">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-moderateFatigue" data-desc="moderate-fatigue-interval">中度疲劳检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-moderateFatigue" data-role="none" value="{{algConfig.dmsConf.dmsInterval.moderateFatigue}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.moderateFatigue 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" style="display: none;">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-severeFatigue" data-desc="severe-fatigue-interval">重度疲劳检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-severeFatigue" data-role="none" value="{{algConfig.dmsConf.dmsInterval.severeFatigue}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.severeFatigue 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-fatigue" data-desc="fatigue-interval">疲劳检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-fatigue" data-role="none" value="{{algConfig.dmsConf.dmsInterval.fatigue}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.fatigue -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.fatigue 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.fatigue 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.fatigue 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.fatigue 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.fatigue 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.fatigue 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.fatigue 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.fatigue 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.fatigue 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.fatigue 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.fatigue 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.fatigue 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.fatigue 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.fatigue 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.fatigue 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{#isDMS31P}}style="display: none;"{{/isDMS31P}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-fatigueL2" data-desc="fatigueL2-interval">二级疲劳检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-fatigueL2" data-role="none" value="{{algConfig.dmsConf.dmsInterval.fatigueL2}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.fatigueL2 -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.fatigueL2 0}}selected="selected"{{/equal}}>{{getKeyLang "on"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-distraction" data-desc="distraction-interval">分心检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-distraction" data-role="none" value="{{algConfig.dmsConf.dmsInterval.distraction}}">
							<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.distraction -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.distraction 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.distraction 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.distraction 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.distraction 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.distraction 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.distraction 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.distraction 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.distraction 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.distraction 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.distraction 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.distraction 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.distraction 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.distraction 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.distraction 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.distraction 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-noDriver">{{#isNotCustomer "111396"}}{{getKeyLang "noDriver-interval"}}{{/isNotCustomer}}{{#isCustomer "111396"}}{{getKeyLang "noDutyOfficer-interval"}}{{/isCustomer}}</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-noDriver" data-role="none" value="{{algConfig.dmsConf.dmsInterval.noDriver}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.noDriver -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.noDriver 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.noDriver 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.noDriver 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.noDriver 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.noDriver 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.noDriver 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.noDriver 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.noDriver 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.noDriver 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.noDriver 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.noDriver 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.noDriver 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.noDriver 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.noDriver 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.noDriver 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-smoke" data-desc="smoke-interval">抽烟检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-smoke" data-role="none" value="{{algConfig.dmsConf.dmsInterval.smoke}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.smoke -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.smoke 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.smoke 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.smoke 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.smoke 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.smoke 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.smoke 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.smoke 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.smoke 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.smoke 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.smoke 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.smoke 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.smoke 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.smoke 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.smoke 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.smoke 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-phone" data-desc="phone-interval">打电话检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-phone" data-role="none" value="{{algConfig.dmsConf.dmsInterval.phone}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.phone -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.phone 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.phone 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.phone 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.phone 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.phone 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.phone 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.phone 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.phone 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.phone 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.phone 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.phone 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.phone 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.phone 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.phone 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.phone 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-yawn" data-desc="yawn-interval">打哈欠检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-yawn" data-role="none" value="{{algConfig.dmsConf.dmsInterval.yawn}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.yawn -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.yawn 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.yawn 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.yawn 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.yawn 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.yawn 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.yawn 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.yawn 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.yawn 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.yawn 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.yawn 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.yawn 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.yawn 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.yawn 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.yawn 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.yawn 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideCustomer "111396"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-noMask" data-desc="noMask-interval">无口罩检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-noMask" data-role="none" value="{{algConfig.dmsConf.dmsInterval.noMask}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.noMask -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.noMask 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.noMask 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.noMask 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.noMask 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.noMask 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.noMask 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.noMask 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.noMask 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.noMask 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.noMask 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.noMask 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.noMask 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.noMask 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.noMask 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.noMask 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideCustomer "111396"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-sunGlass" data-desc="sunGlass-interval">太阳镜检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-sunGlass" data-role="none" value="{{algConfig.dmsConf.dmsInterval.sunGlass}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.sunGlass -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.sunGlass 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideCustomer "111396"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-seatBelt" data-desc="seatBelt-interval">安全带检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-seatBelt" data-role="none" value="{{algConfig.dmsConf.dmsInterval.seatBelt}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.seatBelt -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.seatBelt 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-shelter" data-desc="shelter-interval">遮挡检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-shelter" data-role="none" value="{{algConfig.dmsConf.dmsInterval.shelter}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.shelter -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.shelter 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.shelter 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.shelter 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.shelter 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.shelter 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.shelter 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.shelter 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.shelter 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.shelter 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.shelter 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.shelter 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.shelter 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.shelter 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.shelter 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.shelter 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideBoard "29"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-drinkEat" data-desc="drinkEat-interval">喝东西检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-drinkEat" data-role="none" value="{{algConfig.dmsConf.dmsInterval.drinkEat}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.drinkEat -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.drinkEat 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideCustomer "111396"}}} {{{hideBoard "29"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-overspeed" data-desc="overspeed-interval">超速检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-overspeed" data-role="none" value="{{algConfig.dmsConf.dmsInterval.overspeed}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.overspeed -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.overspeed 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.overspeed 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="15" {{#equal algConfig.dmsConf.dmsInterval.overspeed 15}}selected="selected"{{/equal}}>{{getKeyLang "15s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.overspeed 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.overspeed 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.overspeed 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.overspeed 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.overspeed 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.overspeed 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.overspeed 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.overspeed 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.overspeed 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideBoard "29"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-noHelmet" data-desc="noHelmet-interval">无安全帽检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-noHelmet" data-role="none" value="{{algConfig.dmsConf.dmsInterval.noHelmet}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.noHelmet -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 0}}selected="selected"{{/equal}}>{{getKeyLang "0s"}}</option>
								<option value="2" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 2}}selected="selected"{{/equal}}>{{getKeyLang "2s"}}</option>
								<option value="3" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="4" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 4}}selected="selected"{{/equal}}>{{getKeyLang "4s"}}</option>
								<option value="5" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="30" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="60" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
								<option value="90" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 90}}selected="selected"{{/equal}}>{{getKeyLang "90s"}}</option>
								<option value="120" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 120}}selected="selected"{{/equal}}>{{getKeyLang "2min"}}</option>
								<option value="180" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 180}}selected="selected"{{/equal}}>{{getKeyLang "3min"}}</option>
								<option value="300" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 300}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="600" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="1200" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 1200}}selected="selected"{{/equal}}>{{getKeyLang "20min"}}</option>
								<option value="1800" {{#equal algConfig.dmsConf.dmsInterval.noHelmet 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
							</select></div>
						</div>
				</div>
						<!-- <div class="custom-select-box" {{{hideNotCustomer "201581"}}}>
							<label class="single_option_text" for="algConfig-dmsConf-dmsInterval-changeGuard" data-desc="changeGuard-interval">司机换岗检测间隔</label>
							<div><select class="custom-select" id="algConfig-dmsConf-dmsInterval-changeGuard" data-role="none" value="{{algConfig.dmsConf.dmsInterval.changeGuard}}">
								<option value="-1" {{#equal algConfig.dmsConf.dmsInterval.changeGuard -1}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="0" {{#equal algConfig.dmsConf.dmsInterval.changeGuard 0}}selected="selected"{{/equal}}>{{getKeyLang "on"}}</option>
							</select></div>
						</div> -->
				<div id="ddawhide2">
					<h1  {{{hideHardware "DMS885N"}}}><p>{{getKeyLang "alarmingOut-settings"}}</p></h1>
						<div data-role="none" class="rangeinput" {{{hideNotCustomer "202032"}}} {{{hideHardware "DMS885N"}}} >
							<p class="rangeinput_title">{{getKeyLang "dmsPwm-DutyCycle"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsPwmDutyCycle"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsPwmDutyCycle" min="0" max="100" step="1" value="{{algConfig.dmsConf.dmsAlarmOutSettings.dmsPwmDutyCycle}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAlarmOutSettings.dmsPwmDutyCycle}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p class="rangeinput_title">{{getKeyLang "dmsAlarmOut-Interval"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutInterval"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutInterval" {{#isNotCustomer "202032"}}min="0" max="10000" step="10"{{/isNotCustomer}} {{#isCustomer "202032"}}min="500" max="10000" step="100"{{/isCustomer}} value="{{algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutInterval}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutInterval}}<p>
						</div>
						<div  data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutFatigue-enable">疲劳警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutFatigueEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutFatigueEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutFatigueEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutDistraction-enable">分心警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutDistractionEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutDistractionEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutDistractionEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p>{{#isNotCustomer "111396"}}{{getKeyLang "alarmOutNoDriver-enable"}}{{/isNotCustomer}}{{#isCustomer "111396"}}{{getKeyLang "alarmOutNoDutyOfficer-enable"}}{{/isCustomer}}</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutNoDriverEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutNoDriverEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutNoDriverEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutSmoke-enable">吸烟警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutSmokeEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutSmokeEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutSmokeEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutPhone-enable">手机警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutPhoneEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutPhoneEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutPhoneEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutYawn-enable">打哈欠警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutYawnEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutYawnEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutYawnEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideCustomer "111396"}}} {{{hideBoard "29"}}}>
							<p data-desc="alarmOutNoMask-enable">无口罩警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutNoMaskEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutNoMaskEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutNoMaskEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideCustomer "111396"}}} {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutSunglasses-enable">太阳眼镜警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutSunGlassEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutSunGlassEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutSunGlassEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideCustomer "111396"}}} {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutSeatBelt-enable">安全带警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutSeatBeltEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutSeatBeltEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutSeatBeltEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutShelter-enable">遮挡警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutShelterEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutShelterEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutShelterEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutDrinkEat-enable">喝东西警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutDrinkEatEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutDrinkEatEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutDrinkEatEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideCustomer "111396"}}} {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutOverspeed-enable">超速警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutOverspeedEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutOverspeedEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutOverspeedEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideCustomer "111371"}}} {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutLoginFail-enable">认证失败警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutLoginFailEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutLoginFailEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutLoginFailEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutLoginSuccess-enable">认证成功警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutLoginSuccessEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutLoginSuccessEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutLoginSuccessEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideBoard "29"}}} {{{hideHardware "DMS885N"}}}>
							<p data-desc="alarmOutNoHelmet-enable">无安全帽警报输出使能</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutNoHelmetEnable" {{#if algConfig.dmsConf.dmsAlarmOutSettings.dmsAlarmOutNoHelmetEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-dmsConf-dmsAlarmOutSettings-dmsAlarmOutNoHelmetEnable"></label>
						</div>
				</div>
				<div>
					<h1><p>{{getKeyLang "advanced-settings"}}</p></h1>
						<div data-role="none" class="rangeinput" {{#isDMS31P}}style="display: none;"{{/isDMS31P}}>
							<p class="rangeinput_title">{{getKeyLang "eyelid-closure"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsEyelidClosure"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsEyelidClosure" min="20" max="70" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsEyelidClosure}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsEyelidClosure}}<p>
						</div>
						<div id="fg_TimeLimit" data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "fatigue-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueTimelimit" {{#isNotCustomer "111314"}}min="1" max="5"{{/isNotCustomer}} {{#isCustomer "111314"}}min="1" max="1800" step="1"{{/isCustomer}} value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{#isDMS31P}}style="display: none;"{{/isDMS31P}} >
							<p class="rangeinput_title">{{getKeyLang "fatigue-sumTime"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueSumTime"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueSumTime" {{#isNotCustomer "111314"}}min="30" max="120" step="5"{{/isNotCustomer}} {{#isCustomer "111314"}}min="30" max="1800" step="10"{{/isCustomer}} value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueSumTime}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueSumTime}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{#isDMS31P}}style="display: none;"{{/isDMS31P}}>
							<p class="rangeinput_title">{{getKeyLang "fatigue-closePercent"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueClosePercent"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueClosePercent" min="10" max="100" step="5" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueClosePercent}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueClosePercent}}<p>
						</div>
						<div data-role="none" id="ddawhide4" class="rangeinput"{{#isNotDMS31P}}style="display: none;"{{/isNotDMS31P}}>
							<p class="rangeinput_title" style="display:block"> {{getKeyLang "ddaw-KSSLevel7"}}  </p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueMildThres"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueMildThres" min="20" max="50" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueMildThres}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueMildThres}}<p>
						</div>
						<div data-role="none" id="ddawhide5" class="rangeinput"{{#isNotDMS31P}}style="display: none;"{{/isNotDMS31P}}>
							<p class="rangeinput_title" style="display:block"> {{getKeyLang "ddaw-KSSLevel8"}}  </p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueModerateThres"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueModerateThres" min="40" max="70" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueModerateThres}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueModerateThres}}<p>
						</div>
						<div data-role="none" id="ddawhide6" class="rangeinput"{{#isNotDMS31P}}style="display: none;"{{/isNotDMS31P}}>
							<p class="rangeinput_title" style="display:block"> {{getKeyLang "ddaw-KSSLevel9"}}  </p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueSevereThres"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsFatigueSevereThres" min="60" max="90" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueSevereThres}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsFatigueSevereThres}}<p>
						</div>
						<div data-role="none" id="ddawL2" style="display:none" class="rangeinput"{{#isNotDMS31P}}style="display: none;"{{/isNotDMS31P}}>
							<p class="rangeinput_title"> {{getKeyLang "ddaw-KSSLevel7Thres"}}  </p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsKSSLevel7Thres"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsKSSLevel7Thres" min="10" max="100" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsKSSLevel7Thres}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsKSSLevel7Thres}}<p>
						</div>
						<div data-role="none" id="ddawL3" style="display:none" class="rangeinput"{{#isNotDMS31P}}style="display: none;"{{/isNotDMS31P}}>
							<p class="rangeinput_title">{{getKeyLang "ddaw-KSSLevel8Thres"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsKSSLevel8Thres"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsKSSLevel8Thres" min="10" max="100" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsKSSLevel8Thres}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsKSSLevel8Thres}}<p>
						</div>
						<div  data-role="none" id="ddawL4" style="display:none" class="rangeinput"{{#isNotDMS31P}}style="display: none;"{{/isNotDMS31P}}>
							<p class="rangeinput_title">{{getKeyLang "ddaw-KSSLevel9Thres"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsKSSLevel9Thres"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsKSSLevel9Thres" min="10" max="100" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsKSSLevel9Thres}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsKSSLevel9Thres}}<p>
						</div>
				</div>
				<div id="ddawhide3">
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "distraction-angle-up"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleUp"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleUp" min="20" max="50" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleUp}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleUp}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "distraction-angle-down"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleDown"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleDown" min="20" max="50" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleDown}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleDown}}<p>
						</div>
						<div  data-role="none" class="rangeinput" >
							<p class="rangeinput_title">{{getKeyLang "distraction-angle-left"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleLeft"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleLeft" min="20" max="75" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleLeft}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleLeft}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "distraction-angle-right"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleRight"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionAngleRight" min="20" max="75" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleRight}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleRight}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "distraction-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsDistractionTimelimit" {{#isNotCustomer "111314"}}min="1" max="9"{{/isNotCustomer}} {{#isCustomer "111314"}}min="1" max="1800" step="1"{{/isCustomer}} value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{#isNotCustomer "111396"}}{{getKeyLang "nodriver-timelimit"}}{{/isNotCustomer}}{{#isCustomer "111396"}}{{getKeyLang "noDutyOfficer-timelimit"}}{{/isCustomer}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsNodriverTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsNodriverTimelimit" {{#isNotCustomer "111314"}}min="5" max="30"{{/isNotCustomer}} {{#isCustomer "111314"}}min="5" max="1800" step="5"{{/isCustomer}} value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsNodriverTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsNodriverTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "smoke-threshold"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsSmokeThreshold"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsSmokeThreshold" min="40" max="60" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsSmokeThreshold}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsSmokeThreshold}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "smoke-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsSmokeTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsSmokeTimelimit" min="1" max="5" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsSmokeTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsSmokeTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "phone-threshold"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsPhoneThreshold"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsPhoneThreshold" min="45" max="65" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsPhoneThreshold}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsPhoneThreshold}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "phone-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsPhoneTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsPhoneTimelimit" min="1" max="5" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsPhoneTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsPhoneTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "yawn-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsYawnTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsYawnTimelimit" min="1" max="5" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsYawnTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsYawnTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideCustomer "111396"}}}>
							<p class="rangeinput_title">{{getKeyLang "noMask-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsNoMaskTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsNoMaskTimelimit" min="2" max="10" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsNoMaskTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsNoMaskTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideCustomer "111396"}}}>
							<p class="rangeinput_title">{{getKeyLang "sunGlass-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsSunGlassTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsSunGlassTimelimit" min="3" max="10" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsSunGlassTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsSunGlassTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideCustomer "111396"}}}>
							<p class="rangeinput_title">{{getKeyLang "seatBelt-threshold"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsSeatbeltThreshold"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsSeatbeltThreshold" min="40" max="60" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsSeatbeltThreshold}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsSeatbeltThreshold}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideCustomer "111396"}}}>
							<p class="rangeinput_title">{{getKeyLang "seatBelt-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsSeatbeltTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsSeatbeltTimelimit" min="5" max="15" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsSeatbeltTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsSeatbeltTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "shelter-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsShelterTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsShelterTimelimit" min="5" max="30" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsShelterTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsShelterTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideBoard "29"}}}>
							<p class="rangeinput_title">{{getKeyLang "DrinkEat-threshold"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsDrinkEatThreshold"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsDrinkEatThreshold" min="40" max="60" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsDrinkEatThreshold}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsDrinkEatThreshold}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideBoard "29"}}}>
							<p class="rangeinput_title">{{getKeyLang "DrinkEat-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsDrinkEatTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsDrinkEatTimelimit" min="1" max="5" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsDrinkEatTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsDrinkEatTimelimit}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideCustomer "111396"}}} {{{hideBoard "29"}}}>
							<p class="rangeinput_title">{{#isNotCustomer "201851"}}{{getKeyLang "overspeed-limit-kmh"}}{{/isNotCustomer}} {{#isCustomer "201851"}}{{getKeyLang "overspeed-limit-mph"}}{{/isCustomer}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsOverspeedLimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsOverspeedLimit" min="1" max="150" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsOverspeedLimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsOverspeedLimit}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideBoard "29"}}}>
							<p class="rangeinput_title">{{getKeyLang "noHelmet-threshold"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsNoHelmetThreshold"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsNoHelmetThreshold" min="40" max="60" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsNoHelmetThreshold}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsNoHelmetThreshold}}<p>
						</div>
						<div data-role="none" class="rangeinput" {{{hideBoard "29"}}}>
							<p class="rangeinput_title">{{getKeyLang "noHelmet-timelimit"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-dmsNoHelmetTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsAdvancedSettings-dmsNoHelmetTimelimit" min="1" max="30" value="{{algConfig.dmsConf.dmsAdvancedSettings.dmsNoHelmetTimelimit}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsAdvancedSettings.dmsNoHelmetTimelimit}}<p>
						</div>
				</div>

					<div id="ddawL1" style="display:none">
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsBlinkScorer" data-desc="ddaw-frequentBlinkScore">频繁眨眼分值</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsBlinkScorer" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsBlinkScorer}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsBlinkThres" data-desc="ddaw-frequentBlinkThreshold">频繁眨眼阈值</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsBlinkThres" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsBlinkThres}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsYawnScorer" data-desc="ddaw-yawnScore">打哈欠分值</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsYawnScorer" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsYawnScorer}}">
						</div>
						<!-- <div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsGazeScorer" data-desc="ddaw-dullGazeScore">视线呆滞分值</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsGazeScorer" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsGazeScorer}}">
						</div> -->
						<div data-role="none" class="input-text-box" style="display:none">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsGazeThres" data-desc="ddaw-dullGazeThres">视线呆滞阈值</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsGazeThres" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsGazeThres}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatigueYawRadius" data-desc="ddaw-fatigueYawDevt">疲劳水平角偏差</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatigueYawRadius" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsFatigueYawRadius}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatigueEcrGradThres" data-desc="ddaw-fatigueECRGradThres">疲劳ECR变化率阈值</label>
							<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatigueEcrGradThres" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsFatigueEcrGradThres}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatigueYawGradThres" data-desc="ddaw-fatigueYawGradThres">疲劳水平角变化率阈值</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatigueYawGradThres" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsFatigueYawGradThres}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatiguePitchGradThres" data-desc="ddaw-fatiguePitchGradThres">疲劳俯仰角变化率阈值</label>
							<input data-role="none" type="text" oninput="if(value>100)value=100;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsFatiguePitchGradThres" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsFatiguePitchGradThres}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsShelterThres" data-desc="ddaw-shelterThres">遮挡阈值</label>
							<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-dmsConf-dmsAdvancedSettings-DDAWParam-dmsShelterThres" value="{{algConfig.dmsConf.dmsAdvancedSettings.DDAWParam.dmsShelterThres}}">
						</div>
					</div>
					<!-- <div class="custom-select-box" {{{hideNotWebuiFull "DMS31V2"}}}>
						<label class="single_option_text" for="algConfig-dmsConf-dmsSensitivity">{{getKeyLang "detection-sensitivity"}}</label>
						<div><select class="custom-select" id="algConfig-dmsConf-dmsSensitivity" data-role="none" value="{{algConfig.dmsConf.dmsSensitivity}}">
							<option value="2">{{getKeyLang "sensitivity-high"}}</option>
							<option value="1">{{getKeyLang "sensitivity-medium"}}</option>
							<option value="0">{{getKeyLang "sensitivity-low"}}</option>
							<option value="-1">{{getKeyLang "sensitivity-auto"}}</option>
						</select></div>
					</div> -->
					<div id="algConfig-dmsConf-dmsSpeedLimit" {{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}}{{#unequal algConfig.dmsConf.dmsSensitivity -1}}style="display:none;"{{/unequal}}>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "sensitivity-low"}} / {{getKeyLang "sensitivity-medium"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsMiddleSpeedThr"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsMiddleSpeedThr" min="0" max="100" value="{{algConfig.dmsConf.dmsMiddleSpeedThr}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsMiddleSpeedThr}}<p>
						</div>
						<div data-role="none" class="rangeinput"{{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}}>
							<p class="rangeinput_title">{{getKeyLang "sensitivity-medium"}} / {{getKeyLang "sensitivity-high"}}<p>
							<label data-role="none" for="algConfig-dmsConf-dmsHighSpeedThr"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-dmsConf-dmsHighSpeedThr" min="0" max="100" value="{{algConfig.dmsConf.dmsHighSpeedThr}}">
							<p class="rangeinput_value">{{algConfig.dmsConf.dmsMiddleSpeedThr}}<p>
						</div>
					</div>
				</div>	
				<div class="configmenu alg-conf" id="tab-pds-conf" {{#isNotSupportPD ipcIdentification.hardware}}style="display: none;"{{/isNotSupportPD}}>
					{{#each algConfig.pdsConf}}
					{{#if @first}}
					<div class="alg-conf pd-chn-{{@index}}">
						<h1><p>{{getKeyLang "pd-conf"}}</p></h1>
						<div class="item" {{#isNotSupportLedBuzzer}}style="display: none;"{{/isNotSupportLedBuzzer}}>
							<h1><p>{{getKeyLang "led-settings"}}</p></h1>
							<div data-role="none" class="multi_selectbox" {{{hideHardware "ADA32N1 ADA32E1 HDW845V1"}}}>
								<p class="multi_selectbox_title">{{getKeyLang "Lightup-display"}}</p>
								<table rules="none" class="multi_selectbox_table">
									<td>
										<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-Redred"{{#if RedLedConfig.RedSwitch }}checked="checked"{{/if}}disabled="disabled">
										<label for="algConfig-Redred"></label>
									</td>
									<td>
										<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-Redyellow"{{#if YellowLedConfig.YellowSwitch}}checked="checked"{{/if}}disabled="disabled">
										<label for="algConfig-Redyellow"></label>	
									</td>
									<td>
										<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-Redgreen"{{#if GreenLedConfig.GreenSwitch}}checked="checked"{{/if}}disabled="disabled">
										<label for="algConfig-Redgreen"></label>
									</td>
								</table>
							</div>
							<div style="width:100%; margin-top:20px">
								<table class="menu menu_a" rules="none" cellspacing="5%">
									<td class="sel-td"><li>
										<a  class="xsel-light menu_checked" data-tablight-id="tab-light-red" data-desc="red-light">红色灯</a>
									</li></td>
									
									<td class="sel-td"><li>
										<a  class="xsel-light" data-tablight-id="tab-light-yellow" data-desc="yellow-light">黄色灯</a>
									</li></td>
	
									<td class="sel-td"><li>
										<a  class="xsel-light" data-tablight-id="tab-light-green" data-desc="green-light">绿色灯</a>
									</li></td>
								</table>
							</div>
							<div id="tab-light-red" class="tab-alg-light"> 
								<div data-role="none" class="input-switch-box">
									<p data-desc="RedLight-Switch">红色灯开关</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedLedConfig-RedSwitch" {{#if RedLedConfig.RedSwitch}}checked="checked"{{/if}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedLedConfig-RedSwitch"></label>
								</div>
				
								<div data-role="none" class="rangeinput" >
									<p class="rangeinput_title">{{getKeyLang "Light-Flicker-Rate"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedLedConfig-LightFlickerRate"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-RedLedConfig-LightFlickerRate" min="0" max="100" value="{{RedLedConfig.LightFlickerRate}}" step="1">
									<p class="rangeinput_value">{{RedLedConfig.LightFlickerRate}}<p>
								</div>
								<div data-role="none" class="rangeinput" style="display: none;">
									<p class="rangeinput_title">{{getKeyLang "Light-Pwm-Cyclical"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedLedConfig-LightPwmCyclical"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-RedLedConfig-LightPwmCyclical" min="0" max="100" value="{{RedLedConfig.LightPwmCyclical}}" step="1">
									<p class="rangeinput_value">{{RedLedConfig.LightPwmCyclical}}<p>
								</div>
								<div data-role="none" class="rangeinput" {{{hideHardware "ADA32C4"}}}>
									<p class="rangeinput_title">{{getKeyLang "Light-Pwm-DutyCycle"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedLedConfig-LightPwmDutyCycle"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-RedLedConfig-LightPwmDutyCycle" min="0" max="100" value="{{RedLedConfig.LightPwmDutyCycle}}" step="1">
									<p class="rangeinput_value">{{RedLedConfig.LightPwmDutyCycle}}<p>
								</div>
								<div data-role="none" class="rangeinput" >
									<p class="rangeinput_title">{{getKeyLang "audio-type"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedLedConfig-BuzzerPwmCyclical"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-RedLedConfig-BuzzerPwmCyclical" min="0" max="100" value="{{RedLedConfig.BuzzerPwmCyclical}}" step="1">
									<p class="rangeinput_value">{{RedLedConfig.BuzzerPwmCyclical}}<p>
								</div>
								<div data-role="none" class="rangeinput" >
									<p class="rangeinput_title">{{getKeyLang "Buzzer-Pwm-DutyCycle"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedLedConfig-BuzzerPwmDutyCycle"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-RedLedConfig-BuzzerPwmDutyCycle" min="0" max="100" value="{{RedLedConfig.BuzzerPwmDutyCycle}}" step="1">
									<p class="rangeinput_value">{{RedLedConfig.BuzzerPwmDutyCycle}}<p>
								</div>
							</div>

							<div id="tab-light-yellow" class="tab-alg-light" style="display: none;"> 

								<div data-role="none" class="input-switch-box">
									<p data-desc="YellowLight-Switch">黄色灯开关</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowLedConfig-YellowSwitch"{{#if YellowLedConfig.YellowSwitch}}checked="checked"{{/if}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowLedConfig-YellowSwitch"></label>
								</div>
								<div data-role="none" class="rangeinput" >
									<p class="rangeinput_title">{{getKeyLang "Light-Flicker-Rate"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowLedConfig-LightFlickerRate"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-YellowLedConfig-LightFlickerRate" min="0" max="100" value="{{YellowLedConfig.LightFlickerRate}}" step="1">
									<p class="rangeinput_value">{{YellowLedConfig.LightFlickerRate}}<p>
								</div>
								<div data-role="none" class="rangeinput" style="display: none;">
									<p class="rangeinput_title">{{getKeyLang "Light-Pwm-Cyclical"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowLedConfig-LightPwmCyclical"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-YellowLedConfig-LightPwmCyclical" min="0" max="100" value="{{YellowLedConfig.LightPwmCyclical}}" step="1">
									<p class="rangeinput_value">{{YellowLedConfig.LightPwmCyclical}}<p>
								</div>
								<div data-role="none" class="rangeinput" {{{hideHardware "ADA32C4"}}}>
									<p class="rangeinput_title">{{getKeyLang "Light-Pwm-DutyCycle"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowLedConfig-LightPwmDutyCycle"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-YellowLedConfig-LightPwmDutyCycle" min="0" max="100" value="{{YellowLedConfig.LightPwmDutyCycle}}" step="1">
									<p class="rangeinput_value">{{YellowLedConfig.LightPwmDutyCycle}}<p>
								</div>
								<div data-role="none" class="rangeinput" >
									<p class="rangeinput_title">{{getKeyLang "audio-type"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowLedConfig-BuzzerPwmCyclical"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-YellowLedConfig-BuzzerPwmCyclical" min="0" max="100" value="{{YellowLedConfig.BuzzerPwmCyclical}}" step="1">
									<p class="rangeinput_value">{{YellowLedConfig.BuzzerPwmCyclical}}<p>
								</div>
								<div data-role="none" class="rangeinput" >
									<p class="rangeinput_title">{{getKeyLang "Buzzer-Pwm-DutyCycle"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowLedConfig-BuzzerPwmDutyCycle"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-YellowLedConfig-BuzzerPwmDutyCycle" min="0" max="100" value="{{YellowLedConfig.BuzzerPwmDutyCycle}}" step="1">
									<p class="rangeinput_value">{{YellowLedConfig.BuzzerPwmDutyCycle}}<p>
								</div>
							</div>

							<div id="tab-light-green" class="tab-alg-light" style="display: none;"> 
								<div data-role="none" class="input-switch-box">
									<p data-desc="GreenLight-Switch">绿色灯开关</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenLedConfig-GreenSwitch"{{#if GreenLedConfig.GreenSwitch}}checked="checked"{{/if}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenLedConfig-GreenSwitch"></label>
								</div>
								<div data-role="none" class="rangeinput" >
									<p class="rangeinput_title">{{getKeyLang "Light-Flicker-Rate"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenLedConfig-LightFlickerRate"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-GreenLedConfig-LightFlickerRate" min="0" max="100" value="{{GreenLedConfig.LightFlickerRate}}" step="1">
									<p class="rangeinput_value">{{GreenLedConfig.LightFlickerRate}}<p>
								</div>
								<div data-role="none" class="rangeinput" style="display: none;">
									<p class="rangeinput_title">{{getKeyLang "Light-Pwm-Cyclical"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenLedConfig-LightPwmCyclical"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-GreenLedConfig-LightPwmCyclical" min="0" max="100" value="{{GreenLedConfig.LightPwmCyclical}}" step="1">
									<p class="rangeinput_value">{{GreenLedConfig.LightPwmCyclical}}<p>
								</div>
								<div data-role="none" class="rangeinput" {{{hideHardware "ADA32C4"}}}>
									<p class="rangeinput_title">{{getKeyLang "Light-Pwm-DutyCycle"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenLedConfig-LightPwmDutyCycle"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-GreenLedConfig-LightPwmDutyCycle" min="0" max="100" value="{{GreenLedConfig.LightPwmDutyCycle}}" step="1">
									<p class="rangeinput_value">{{GreenLedConfig.LightPwmDutyCycle}}<p>
								</div>
								<div data-role="none" class="rangeinput" {{{hideHardware "ADA32C4"}}}>
									<p class="rangeinput_title">{{getKeyLang "audio-type"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenLedConfig-BuzzerPwmCyclical"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-GreenLedConfig-BuzzerPwmCyclical" min="0" max="100" value="{{GreenLedConfig.BuzzerPwmCyclical}}" step="1">
									<p class="rangeinput_value">{{GreenLedConfig.BuzzerPwmCyclical}}<p>
								</div>
								<div data-role="none" class="rangeinput" {{{hideHardware "ADA32C4"}}}>
									<p class="rangeinput_title">{{getKeyLang "Buzzer-Pwm-DutyCycle"}}<p>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenLedConfig-BuzzerPwmDutyCycle"></label>
									<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-GreenLedConfig-BuzzerPwmDutyCycle" min="0" max="100" value="{{GreenLedConfig.BuzzerPwmDutyCycle}}" step="1">
									<p class="rangeinput_value">{{GreenLedConfig.BuzzerPwmDutyCycle}}<p>
								</div>
							</div>		
						</div>
						<div class="item" {{{showHardware "ADA32V4 ADA32V2 ADA32V3 AICB046V1 ADA32IR"}}}>
							<h1><p>{{getKeyLang "MultiTriggerLine"}}</p></h1>
							<div style="margin-top:30px;width:100%;">
								<table class="menu menu_a" rules="none" cellspacing="5%">
									<td class="sel-td" ><li>
										<a  class="xsel-wire" class="xsel-wire menu_checked" value="ch1" data-tabwire-id="tab-wire-red" data-desc="red-wire">红色区域</a>
									</li></td>
									
									<td class="sel-td"><li>
										<a  class="xsel-wire" value="ch2" data-tabwire-id="tab-wire-yellow" data-desc="yellow-wire">黄色区域</a>
									</li></td>
	
									<td class="sel-td"><li>
										<a  class="xsel-wire" value="ch3" data-tabwire-id="tab-wire-green" data-desc="green-wire">绿色区域</a>
									</li></td>
								</table>
							</div>
							<div id="tab-wire-red" class="tab-alg-wire"> 
								<div data-role="none" class="input-switch-box">
									<p data-desc="pdRedLine-Switch">红色线输出开关</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-bAlarmOutSwitch" {{#if RedWireAlarmOut.bAlarmOutSwitch}}checked="checked"{{/if}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-bAlarmOutSwitch"></label>
								</div>
								<div data-role="none" class="multi_selectbox" {{{hideHardware "ADA32N1 ADA32E1 HDW845V1 ADA32C4"}}}>
									<p class="multi_selectbox_title">{{getKeyLang "pdAlarmOut-Zone"}}</p>
									<table rules="none" class="multi_selectbox_table">
										<td>
											<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-zonemask-0"{{#mask RedWireAlarmOut.zonemask 0}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-zonemask-0"></label>
										</td>
										<td>
											<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-zonemask-1"{{#mask RedWireAlarmOut.zonemask 1}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-zonemask-1"></label>	
										</td>
										<td>
											<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-zonemask-2"{{#mask RedWireAlarmOut.zonemask 2}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-zonemask-2"></label>
										</td>
									</table>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install">
									<p data-desc="personModel-enable">支持行人检测报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-0" {{#mask RedWireAlarmOut.modelMask 0}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-0"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Intg Usr-Install">
									<p data-desc="carModel-enable">支持车检测报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-1" {{#mask RedWireAlarmOut.modelMask 1}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-1"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install" {{{hideHardware "ADA32N1 ADA32IR"}}}>
									<p data-desc="shelter-enable">支持遮挡报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-2" {{#mask RedWireAlarmOut.modelMask 2}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-2"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install" {{{showHWandCustomer "ADA32V2" "202661 201338"}}}>
									<p data-desc="signModel-enable">支持启用标志模型</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-3" {{#mask RedWireAlarmOut.modelMask 3}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-3"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install" {{{showHWandCustomer "ADA32V2 ADA32V3" "200055"}}}>
									<p data-desc="heartBeatSignal-enable">支持心跳检测信号输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-4" {{#mask RedWireAlarmOut.modelMask 4}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-4"></label>
								</div>	
								<div data-role="none" class="input-switch-box Usr-Intg Usr-Install">
									<p data-desc="motion-enable">支持运动方向过滤</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-5" {{#mask RedWireAlarmOut.modelMask 5}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-RedWireAlarmOut-modelMask-5"></label>
								</div>							
							</div>
							<div id="tab-wire-yellow" class="tab-alg-wire" style="display: none;"> 
								<div data-role="none" class="input-switch-box">
									<p data-desc="pdYellowLine-Switch">黄色线输出开关</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-bAlarmOutSwitch" {{#if YellowWireAlarmOut.bAlarmOutSwitch}}checked="checked"{{/if}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-bAlarmOutSwitch"></label>
								</div>
								<div data-role="none" class="multi_selectbox" {{{hideHardware "ADA32N1 ADA32E1 HDW845V1 ADA32C4"}}}>
									<p class="multi_selectbox_title">{{getKeyLang "pdAlarmOut-Zone"}}</p>
									<table rules="none" class="multi_selectbox_table">
										<td>
											<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-zonemask-0"{{#mask YellowWireAlarmOut.zonemask 0}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-zonemask-0"></label>
										</td>
										<td>
											<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-zonemask-1"{{#mask YellowWireAlarmOut.zonemask 1}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-zonemask-1"></label>	
										</td>
										<td>
											<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-zonemask-2"{{#mask YellowWireAlarmOut.zonemask 2}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-zonemask-2"></label>
										</td>
									</table>
								</div>
								<div data-role="none" class="input-switch-box Usr-Install">
									<p data-desc="personModel-enable">支持行人检测报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-0" {{#mask YellowWireAlarmOut.modelMask 0}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-0"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Intg Usr-Install">
									<p data-desc="carModel-enable">支持车检测报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-1" {{#mask YellowWireAlarmOut.modelMask 1}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-1"></label>
								</div>
								<div data-role="none" class="input-switch-box Usr-Install" {{{hideHardware "ADA32N1 ADA32IR"}}}>
									<p data-desc="shelter-enable">支持遮挡报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-2" {{#mask YellowWireAlarmOut.modelMask 2}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-2"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install" {{{showHWandCustomer "ADA32V2" "202661 201338"}}}>
									<p data-desc="signModel-enable">支持启用标志模型</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-3" {{#mask YellowWireAlarmOut.modelMask 3}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-3"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install" {{{showHWandCustomer "ADA32V2 ADA32V3" "200055"}}}>
									<p data-desc="heartBeatSignal-enable">支持心跳检测信号输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-4" {{#mask YellowWireAlarmOut.modelMask 4}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-4"></label>
								</div>	
								<div data-role="none" class="input-switch-box Usr-Intg Usr-Install">
									<p data-desc="motion-enable">支持运动方向过滤</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-5" {{#mask YellowWireAlarmOut.modelMask 5}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-YellowWireAlarmOut-modelMask-5"></label>
								</div>
							</div>
							<div id="tab-wire-green" class="tab-alg-wire" style="display: none;"> 
								<div data-role="none" class="input-switch-box">
									<p data-desc="pdGreenLine-Switch">绿色线输出开关</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-bAlarmOutSwitch" {{#if GreenWireAlarmOut.bAlarmOutSwitch}}checked="checked"{{/if}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-bAlarmOutSwitch"></label>
								</div>
								<div data-role="none" class="multi_selectbox" {{{hideHardware "ADA32N1 ADA32E1 HDW845V1 ADA32C4"}}}>
									<p class="multi_selectbox_title">{{getKeyLang "pdAlarmOut-Zone"}}</p>
									<table rules="none" class="multi_selectbox_table">
										<td>
											<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-zonemask-0"{{#mask GreenWireAlarmOut.zonemask 0}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-zonemask-0"></label>
										</td>
										<td>
											<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-zonemask-1"{{#mask GreenWireAlarmOut.zonemask 1}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-zonemask-1"></label>	
										</td>
										<td>
											<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-zonemask-2"{{#mask GreenWireAlarmOut.zonemask 2}}checked="checked"{{/mask}}>
											<label for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-zonemask-2"></label>
										</td>
									</table>
								</div>
								<div data-role="none" class="input-switch-box Usr-Install">
									<p data-desc="personModel-enable">支持行人检测报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-0" {{#mask GreenWireAlarmOut.modelMask 0}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-0"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Intg Usr-Install">
									<p data-desc="carModel-enable">支持车检测报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-1" {{#mask GreenWireAlarmOut.modelMask 1}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-1"></label>
								</div>
								<div data-role="none" class="input-switch-box Usr-Install" {{{hideHardware "ADA32N1 ADA32IR"}}}>
									<p data-desc="shelter-enable">支持遮挡报警输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-2" {{#mask GreenWireAlarmOut.modelMask 2}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-2"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install" {{{showHWandCustomer "ADA32V2" "202661 201338"}}}>
									<p data-desc="signModel-enable">支持启用标志模型</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-3" {{#mask GreenWireAlarmOut.modelMask 3}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-3"></label>
								</div>
								<div data-role="none" class="input-switch-box  Usr-Install" {{{showHWandCustomer "ADA32V2 ADA32V3" "200055"}}}>
									<p data-desc="heartBeatSignal-enable">支持心跳检测信号输出</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-4" {{#mask GreenWireAlarmOut.modelMask 4}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-4"></label>
								</div>
								<div data-role="none" class="input-switch-box Usr-Intg Usr-Install">
									<p data-desc="motion-enable">支持运动方向过滤</p>
									<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-5" {{#mask GreenWireAlarmOut.modelMask 5}}checked="checked"{{/mask}}>
									<label data-role="none" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-modelMask-5"></label>
								</div>					
								<div class="custom-select-box" {{{showHWandCustomer "ADA32V4 ADA32V2 AICB046V1 ADA32IR" "202661"}}}>
									<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-event" data-desc="GreenWireAlarmEvent">绿色线报警事件</label>
									<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-GreenWireAlarmOut-event" data-role="none" value="{{GreenWireAlarmOut.event}}">
										<option value="0" {{#equal GreenWireAlarmOut.event 0}}selected="selected"{{/equal}} data-desc="greenwire-alarm">行人检测报警</option>
										<option value="1" {{#equal GreenWireAlarmOut.event 1}}selected="selected"{{/equal}} data-desc="greenwire-diagnostic">开机诊断·</option>
									</select></div>
								</div>
							</div>		
						</div>	
						<div class="item" {{{hideHardware "ADA32E1 ADA32C4 MN234 ADA32N1 HDW845V1"}}}>
							<h1><p>{{getKeyLang "SingleTriggerLine"}}</p></h1>
							<div data-role="none" class="multi_selectbox">
								<p class="multi_selectbox_title">{{getKeyLang "pdAlarmOut-Switch"}}</p>
								<table rules="none" class="multi_selectbox_table">
									<td>
										<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdAlarmOutEnable-red"{{#if pdAlarmOutEnable.red}}checked="checked"{{/if}}>
										<label for="algConfig-pdsConf-{{@index}}-pdAlarmOutEnable-red"></label>
									</td>
									<td>
										<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdAlarmOutEnable-yellow"{{#if pdAlarmOutEnable.yellow}}checked="checked"{{/if}}>
										<label for="algConfig-pdsConf-{{@index}}-pdAlarmOutEnable-yellow"></label>	
									</td>
									<td>
										<input data-role="none" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdAlarmOutEnable-green"{{#if pdAlarmOutEnable.green}}checked="checked"{{/if}}>
										<label for="algConfig-pdsConf-{{@index}}-pdAlarmOutEnable-green"></label>
									</td>
								</table>
							</div>
							<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32V2" "202883"}}}>
								<p data-desc="pd-AlarmOutPWMmode">alarm triggers output pwm mode</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdAlarmOutPWMmode" {{#if pdAlarmOutPWMmode}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdAlarmOutPWMmode"></label>
							</div>
							<div data-role="none" class="input-switch-box" {{{hideHardware "ADA32IR"}}}>
								<p data-desc="pd-AlarmOutShelter">shelter alarm triggers output</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdAlarmOutShelter" {{#if pdAlarmOutShelter}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdAlarmOutShelter"></label>
							</div>
						</div>
						<div class="custom-select-box Usr-Intg Usr-Install">
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdsModel" data-desc="pd-Model">检测模型</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdsModel" data-role="none" value="{{pdsModel}}">
								<option value="0" {{#equal pdsModel 0}}selected="selected"{{/equal}}{{#isA32OW}}style="display: none;"{{/isA32OW}}>{{getKeyLang "pd-Model-Person"}}</option>
								<option value="7" {{#equal pdsModel 7}}selected="selected"{{/equal}}{{#isA32OW}}style="display: none;"{{/isA32OW}} {{{hideHardware "HDW845V1 AICB046V1"}}}>
									{{#isCustomer "202319"}}{{getKeyLang "pd-Model-Car2"}}{{/isCustomer}}
									{{#isNotCustomer "202319"}}{{getKeyLang "pd-Model-Car"}}{{/isNotCustomer}}
								</option>
								<option value="1" {{#equal pdsModel 1}}selected="selected"{{/equal}}{{#isA32OW}}style="display: none;"{{/isA32OW}} {{{hideHardware "HDW845V1 AICB046V1"}}}>
									{{#isCustomer "202319"}}{{getKeyLang "pd-Model-PersonCar2"}}{{/isCustomer}}
									{{#isNotCustomer "202319"}}{{getKeyLang "pd-Model-PersonCar"}}{{/isNotCustomer}}
								</option>
								<option value="13" {{#equal pdsModel 13}}selected="selected"{{/equal}}{{#isA32OW}}style="display: none;"{{/isA32OW}}{{{hideNotCustomer "100394"}}} {{{hideHardware "ADA32IR"}}}>{{getKeyLang "pd-Model-SafetyHat"}}</option>
								<option value="18" {{#equal pdsModel 18}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661 200598-93852"}}}>{{getKeyLang "pd-Model-Sign"}}</option>
								<option value="19" {{#equal pdsModel 19}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661 200598-93852"}}}>{{getKeyLang "pd-Model-PersonSign"}}</option>
								<option value="20" {{#equal pdsModel 20}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661 200598-93852"}}}>{{getKeyLang "pd-Model-CarSign"}}</option>
								<option value="21" {{#equal pdsModel 21}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661 200598-93852"}}}>{{getKeyLang "pd-Model-PersonCarSign"}}</option>

								<option value="2" {{#equal pdsModel 2}}selected="selected"{{/equal}}{{#isNotA32OW}}style="display: none;"{{/isNotA32OW}}>{{getKeyLang "pd-Model-Person"}}</option>
								<option value="8" {{#equal pdsModel 8}}selected="selected"{{/equal}}{{#isNotA32OW}}style="display: none;"{{/isNotA32OW}} {{{hideHardware "ADA32V3"}}}>{{getKeyLang "pd-Model-Car"}}</option>
								<option value="3" {{#equal pdsModel 3}}selected="selected"{{/equal}}{{#isNotA32OW}}style="display: none;"{{/isNotA32OW}} {{{hideHardware "ADA32V3"}}}>{{getKeyLang "pd-Model-PersonCar"}}</option>

								<option value="5" {{#equal pdsModel 5}}selected="selected"{{/equal}}{{{hideNotCustomer "201266A"}}}>{{getKeyLang "pd-Model-ManHole"}}</option>
								<option value="17" {{#equal pdsModel 17}}selected="selected"{{/equal}}{{{hideNotCustomer "201207"}}}>{{getKeyLang "pd-Model-Bear"}}</option>
								<option value="22" {{#equal pdsModel 22}}selected="selected"{{/equal}}{{{showHWandCustomer "ADA32V2" "202958"}}}>{{getKeyLang "pd-PC-DCD"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box Usr-Install">
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdSensitivity" data-desc="pd-Sensitivity">PDS灵敏度</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdSensitivity" data-role="none" value="{{pdSensitivity}}">
								<option value="0" {{#equal pdSensitivity 0}}selected="selected"{{/equal}} data-desc="sensitivity-low">Low</option>
								<option value="1" {{#equal pdSensitivity 1}}selected="selected"{{/equal}} data-desc="sensitivity-medium">Medium</option>
								<option value="2" {{#equal pdSensitivity 2}}selected="selected"{{/equal}} data-desc="sensitivity-high">High</option>
							</select></div>
						</div>
						<div class="custom-select-box Usr-Intg Usr-Install" {{{hideHardware "HDW845V1"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdOsdFontSize" data-desc="pd-OsdFontSize">置信值字体</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdOsdFontSize" data-role="none" value="{{pdOsdFontSize}}">
								<option value="0" {{#equal pdOsdFontSize 0}}selected="selected" {{/equal}} data-desc="off">OFF</option>
								<option value="1" {{#equal pdOsdFontSize 1}}selected="selected"{{/equal}}>1X</option>
								<option value="2" {{#equal pdOsdFontSize 2}}selected="selected"{{/equal}}>2X</option>
								<option value="3" {{#equal pdOsdFontSize 3}}selected="selected"{{/equal}}>3X</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{showHWandCustomer "ADA32V2" "201851"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdAlarmIconSize" data-desc="pd-AlarmIconSize">行人/车辆图标大小</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdAlarmIconSize" data-role="none" value="{{pdAlarmIconSize}}">
								<option value="1" {{#equal pdAlarmIconSize 1}}selected="selected"{{/equal}}>X1</option>
								<option value="3" {{#equal pdAlarmIconSize 3}}selected="selected"{{/equal}}>X3</option>
								<option value="6" {{#equal pdAlarmIconSize 6}}selected="selected"{{/equal}}>X6</option>
							</select></div>
						</div>
						<div data-role="none" class="input-switch-box Usr-Install">
							<p data-desc="pd-Alg-Enable">算法开关</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-algEnable" {{{isCheckAlgEnable}}}>
							<label data-role="none" for="algConfig-algEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideNotHardware "AICB046V1"}}}>
							<p data-desc="pd-R151-reg">R151法规</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-R151RegulationEnable" {{#if R151RegulationEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-R151RegulationEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideHardware "HDW845V1 ADA32C4 MN234"}}}>
							<p data-desc="pd-Alarm-In">触发输入</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdAlarmIn" {{#if pdAlarmIn}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdAlarmIn"></label>
						</div>
						<div data-role="none" class="input-switch-box Usr-Intg Usr-Install" {{{hideHardware "HDW845V1"}}}>
							<p data-desc="pd-test-mode">测试模式</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdTestMode" {{#if pdTestMode}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdTestMode"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32V2" "201933"}}}>
							<p data-desc="pd-red-flashScreen">画面红色闪烁</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdRedFlashScreen" {{#if pdRedFlashScreen}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdRedFlashScreen"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32V4 ADA32V2 AICB046V1 ADA32IR" "202613"}}}>
							<p data-desc="CrosshairIcon">十字准星图标开关</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdCrosshairIcon" {{#if pdCrosshairIcon}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdCrosshairIcon"></label>
						</div>
						<div data-role="none" class="input-switch-box">
							<p data-desc="display-pdRectPerson">行人检测框</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdRectPerson" {{#if pdRectPerson}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdRectPerson"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32V4 ADA32V2 ADA32V3" "200055 200055A"}}}>
							<p data-desc="pd-BorderAlarm">警告边框</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdBorderAlarm" {{#if pdBorderAlarm}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdBorderAlarm"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1"}}}>
							<p data-desc="mosaicEnable">开启人脸马赛克</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-bMosaic" {{#if bMosaic}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-bMosaic"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32V2 ADA32V3" "200001"}}}>
							<p data-desc="BG-gifEnable">开启GIF效果</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-{{@index}}-bGif" {{#if bGif}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-bGif"></label>
						</div>
						<div class="custom-select-box Usr-Install" {{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1"}}}>
							<label class="single_option_text"   for="algConfig-pdsConf-{{@index}}-pdBlkSize" data-desc="mosaicSize" >马赛克大小</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdBlkSize" data-role="none" value="{{pdBlkSize}}">		
								<option value=5 {{#equal pdBlkSize 5}}selected="selected"{{/equal}}>5</option>
								<option value=10 {{#equal pdBlkSize 10}}selected="selected"{{/equal}}>10</option>
								<option value=15 {{#equal pdBlkSize 15}}selected="selected"{{/equal}}>15</option>
							</select></div>
						</div>
						<div class="custom-select-box Usr-Install" {{{hideHardware "HDW845V1 ADA32C4"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdInterval-red" data-desc="pdRed-interval">红区检测间隔</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdInterval-red" data-role="none" value="{{pdInterval.red}}">
								<option value="-1" {{#equal pdInterval.red -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
								<option value="0" {{#equal pdInterval.red 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
								<option value="2" {{#equal pdInterval.red 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
								<option value="3" {{#equal pdInterval.red 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
								<option value="4" {{#equal pdInterval.red 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
								<option value="5" {{#equal pdInterval.red 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
								<option value="10" {{#equal pdInterval.red 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
								<option value="30" {{#equal pdInterval.red 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
								<option value="60" {{#equal pdInterval.red 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
								<option value="90" {{#equal pdInterval.red 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
								<option value="120" {{#equal pdInterval.red 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
								<option value="180" {{#equal pdInterval.red 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
								<option value="300" {{#equal pdInterval.red 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
							</select></div>
						</div>
						<div class="custom-select-box pds-det-interval Usr-Install" {{{hideHardware "HDW845V1 ADA32C4"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdInterval-yellow">
								{{#isCustomer "200055 200055A"}}
									{{getKeyLang "pdOrange-interval"}}									
								{{/isCustomer}}
								{{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdYellow-interval"}}{{/isNotCustomer}}
							</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdInterval-yellow" data-role="none" value="{{pdInterval.yellow}}">
								<option value="-1" {{#equal pdInterval.yellow -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
								<option value="0" {{#equal pdInterval.yellow 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
								<option value="2" {{#equal pdInterval.yellow 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
								<option value="3" {{#equal pdInterval.yellow 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
								<option value="4" {{#equal pdInterval.yellow 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
								<option value="5" {{#equal pdInterval.yellow 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
								<option value="10" {{#equal pdInterval.yellow 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
								<option value="30" {{#equal pdInterval.yellow 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
								<option value="60" {{#equal pdInterval.yellow 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
								<option value="90" {{#equal pdInterval.yellow 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
								<option value="120" {{#equal pdInterval.yellow 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
								<option value="180" {{#equal pdInterval.yellow 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
								<option value="300" {{#equal pdInterval.yellow 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
							</select></div>
						</div>
						<div class="custom-select-box pds-det-interval Usr-Install" {{{hideHardware "HDW845V1 ADA32C4"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdInterval-green">
								{{#isCustomer "200055 200055A"}}
									{{getKeyLang "pdPurple-interval"}}									
								{{/isCustomer}}
								{{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdGreen-interval"}}{{/isNotCustomer}}
							</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdInterval-green" data-role="none" value="{{pdInterval.green}}">
								<option value="-1" {{#equal pdInterval.green -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
								<option value="0" {{#equal pdInterval.green 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
								<option value="2" {{#equal pdInterval.green 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
								<option value="3" {{#equal pdInterval.green 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
								<option value="4" {{#equal pdInterval.green 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
								<option value="5" {{#equal pdInterval.green 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
								<option value="10" {{#equal pdInterval.green 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
								<option value="30" {{#equal pdInterval.green 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
								<option value="60" {{#equal pdInterval.green 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
								<option value="90" {{#equal pdInterval.green 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
								<option value="120" {{#equal pdInterval.green 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
								<option value="180" {{#equal pdInterval.green 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
								<option value="300" {{#equal pdInterval.green 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
							</select></div>
						</div>
						<div data-role="none" class="rangeinput" {{{hideHardware "HDW845V1 ADA32C4 MN234"}}}>
							<p class="rangeinput_title">{{getKeyLang "pdAlarmOut-Interval"}}<p>
							<div class="config_checkbox2">
								<p data-desc="auto">AUTO</p>
								<input data-role="none" class="checkBtn custom" id="pdalarmout-auto-enable" type="checkbox" />
								<label data-role="none" for="pdalarmout-auto-enable"></label>
							</div>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-pdAlarmOutInterval"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-pdAlarmOutInterval" min="0" max="10000" step="50" value="{{pdAlarmOutInterval}}">
							<p  id="pdalarm_interval_value" class="rangeinput_value">{{pdAlarmOutInterval}}<p>
						</div>
						<div data-role="none" class="multi_selectbox" {{{hideHardware "ADA32V3 ADA32IR HDW845V1"}}} {{{hideNotCustomer "100394"}}} >
							<p class="multi_selectbox_title">{{getKeyLang "pdSafetyHelmet-NoAlarm"}}</p>
							<table rules="none" class="multi_selectbox_table2">
								<td>
									<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipRedHelmet"{{#if pdSafetyHelmet.pdSkipRedHelmet}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipRedHelmet"></label>
								</td>
								<td>
									<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipYellowHelmet"{{#if pdSafetyHelmet.pdSkipYellowHelmet}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipYellowHelmet"></label>
								</td>
								<td>
									<input data-role="none" class="checkBtnBlue" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipBlueHelmet"{{#if pdSafetyHelmet.pdSkipBlueHelmet}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipBlueHelmet"></label>
								</td>
								<td>
									<input data-role="none" class="checkBtnWhite" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipWhiteHelmet"{{#if pdSafetyHelmet.pdSkipWhiteHelmet}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdSafetyHelmet-pdSkipWhiteHelmet"></label>
								</td>
							</table>
						</div>
						<div data-role="none" class="multi_selectbox" {{{showHWandCustomer "ADA32V2" "200598"}}}>
							<p class="multi_selectbox_title">{{getKeyLang "Single-Trigger-Line-Switch"}}</p>
							<table rules="none" class="multi_selectbox_table">
								<td>
									<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSTLEnable-red" {{#if pdSTLEnable.red}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdSTLEnable-red"></label>
								</td>
								<td>
									<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSTLEnable-yellow" {{#if pdSTLEnable.yellow}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdSTLEnable-yellow"></label>								
								</td>
								<td>
									<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSTLEnable-green" {{#if pdSTLEnable.green}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdSTLEnable-green"></label>								
								</td>
							</table>
						</div>
						<div data-role="none" class="multi_selectbox" {{{hideHardware "HDW845V1"}}}>
							<p class="multi_selectbox_title">{{getKeyLang "pdRoi-Switch"}}</p>
							<table rules="none" class="multi_selectbox_table">
								<td>
									<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdRoiEnable-red" {{#if pdRoiEnable.red}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdRoiEnable-red"></label>
								</td>
								<td>
									<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdRoiEnable-yellow" {{#if pdRoiEnable.yellow}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdRoiEnable-yellow"></label>								
								</td>
								<td>
									<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdRoiEnable-green" {{#if pdRoiEnable.green}}checked="checked"{{/if}}>
									<label for="algConfig-pdsConf-{{@index}}-pdRoiEnable-green"></label>								
								</td>
							</table>
						</div>
						<div class="custom-select-box" {{{hideHardware "MN234"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-{{@index}}-pdRoiGui" data-desc="pdroigui">GUI绘制模式</label>
							<div><select class="custom-select" id="algConfig-pdsConf-{{@index}}-pdRoiGui" data-role="none" value="{{pdRoiGui}}">
								<option value="0" {{#equal pdRoiGui 0}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-hide"}}</option>
								<option value="1" {{#equal pdRoiGui 1}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-line"}}</option>
								<option value="2" {{#equal pdRoiGui 2}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-fill"}}</option>
							</select></div>
						</div>
						<div data-role="none" class="rangeinput Usr-Intg Usr-Install" {{{hideUSBCam}}} {{{hideHardware "ADA32N1 ADA32E1 HDW845V1 MN234"}}}>
							<p class="rangeinput_title">{{getKeyLang "min-work-speed+unit"}}<p>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-minWorkSpeed"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-minWorkSpeed" min="0" max="150" value="{{minWorkSpeed}}">
							<p class="rangeinput_value">{{minWorkSpeed}}<p>
						</div>		
						<div data-role="none" class="rangeinput Usr-Intg Usr-Install" {{{hideUSBCam}}} {{{hideHardware "ADA32N1 ADA32E1 HDW845V1 MN234"}}}>
							<p class="rangeinput_title">{{getKeyLang "max-work-speed+unit"}}<p>
							<label data-role="none" for="algConfig-pdsConf-{{@index}}-maxWorkSpeed"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-{{@index}}-maxWorkSpeed" min="0" max="150" value="{{maxWorkSpeed}}">
							<p class="rangeinput_value">{{maxWorkSpeed}}<p>
						</div>
					</div>
					{{/if}}
					{{/each}}
					<h1 {{{hideHardware "HDW845V1"}}}><p>{{getKeyLang "advanced-settings"}}</p></h1>
						<div class="item" {{{hideNotHardware "AICB046V1"}}} {{{hideNotWebuiFull "AICB046V1"}}}>
							<h1><p data-desc="pdOvertaking">超车警报</p></h1>
							<div data-role="none" class="input-switch-box">
								<p data-desc="overtaking-enable">超车警报使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-0-bOvertakingEnable" {{#if algConfig.pdsConf.[0].bOvertakingEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pdsConf-0-bOvertakingEnable"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="opticalFlow-enable">光流使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-0-bOpticalFlowEnable" {{#if algConfig.pdsConf.[0].bOpticalFlowEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pdsConf-0-bOpticalFlowEnable"></label>
							</div>


							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pdsConf-0-bIntervalFrameWay" data-desc="IntervalFrame-way" >IntervalFrame Way</label>
								<div><select class="custom-select" id="algConfig-pdsConf-0-bIntervalFrameWay" data-role="none" value="{{algConfig.pdsConf.[0].bIntervalFrameWay}}">
									<option value="0" {{#equal algConfig-pdsConf-0-bIntervalFrameWay 0}}selected="selected"{{/equal}} data-desc="IntervalFrame-time">IntervalFrameTime</option>
									<option value="1" {{#equal algConfig-pdsConf-0-bIntervalFrameWay 1}}selected="selected"{{/equal}} data-desc="IntervalFrame-gap">IntervalFrameGap</option>
								</select></div>
							</div>

							<div data-role="none" class="rangeinput">
								<p class="rangeinput_title">{{getKeyLang "opticalFlow-FrmInt"}}<p>
								<label data-role="none" for="algConfig-pdsConf-0-overtakingDetFrmCnt"></label>
								<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-0-opticalFlowInterval" min="0" max="3" step="1" value="{{algConfig.pdsConf.[0].opticalFlowInterval}}">
								<p class="rangeinput_value">{{algConfig.pdsConf.[0].opticalFlowInterval}}<p>
							</div>
							<div data-role="none" class="rangeinput">
								<p class="rangeinput_title">{{getKeyLang "overtaking-DetFrmCnt"}}<p>
								<label data-role="none" for="algConfig-pdsConf-0-overtakingDetFrmCnt"></label>
								<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-0-overtakingDetFrmCnt" min="1" max="9" step="2" value="{{algConfig.pdsConf.[0].overtakingDetFrmCnt}}">
								<p class="rangeinput_value">{{algConfig.pdsConf.[0].overtakingDetFrmCnt}}<p>
							</div>


							<div data-role="none" class="rangeinput">
								<p class="rangeinput_title">{{getKeyLang "overtaking-PointNumLim"}}</p>
								<label data-role="none" for="algConfig-pdsConf-0-OpPointNumLim"></label>
								<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-0-OpPointNumLim" min="10" max="300" step="1" value="{{algConfig.pdsConf.[0].OpPointNumLim}}">
								<p class="rangeinput_value">{{algConfig.pdsConf.[0].OpPointNumLim}}</p>
							</div>		
							<div data-role="none" class="input-text-box">
								<label data-role="none" for="algConfig-pdsConf-0-fMiniFrameIntervalTime" data-desc="overtaking-intervalTime">间隔时间(MS)</label>
								<input data-role="none" type="text" oninput="if(value>2000)value=2000;if(value<0)value=0;if(value.length>7)value=value.slice(0,7)" id="algConfig-pdsConf-0-fMiniFrameIntervalTime" value="{{algConfig.pdsConf.[0].fMiniFrameIntervalTime}}">
							</div>


							<div data-role="none" class="input-text-box">
								<label data-role="none" for="algConfig-pdsConf-0-overtakingThres" data-desc="overtaking-threshold">阈值</label>
								<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-pdsConf-0-overtakingThres" value="{{algConfig.pdsConf.[0].overtakingThres}}">
							</div>
							<div data-role="none" class="input-text-box">
								<label data-role="none" for="algConfig-pdsConf-0-overtakeRefRate" data-desc="overtaking-refRate">参考帧比例</label>
								<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>4)value=value.slice(0,4);if(value<0)value=0" id="algConfig-pdsConf-0-overtakeRefRate" value="{{algConfig.pdsConf.[0].overtakeRefRate}}">
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="opFlowFreeShake-enable">光流防抖使能</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-0-bOpticalFreeShake" {{#if algConfig.pdsConf.[0].bOpticalFreeShake}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pdsConf-0-bOpticalFreeShake"></label>
							</div>
						</div>
						<div class="custom-select-box Usr-Install" {{{hideHardware "ADA32N1 ADA32E1 HDW845V1 ADA32C4 MN234 ADA38N1"}}}>
							<label class="single_option_text" for="algConfig-algTrigger" data-desc="pdAlarmOut-Trigger">报警输出方式</label>
							<div><select class="custom-select" id="algConfig-algTrigger" data-role="none" value="{{algConfig.algTrigger}}">
								<option value="0" {{#equal algConfig.algTrigger 0}}selected="selected"{{/equal}} data-desc="High-Level">高电平</option>
								<option value="1"  {{#equal algConfig.algTrigger 1}} selected="selected"{{/equal}} data-desc="Low-Level">低电平</option>
							</select></div>
						</div>
						<div class="custom-select-box Usr-Install" {{{hideHardware "ADA32E1 ADA32N1 HDW845V1 ADA32C4 MN234 ADA38N1"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-0-pdAlarmInTrigger" data-desc="pdAlarmIn-Trigger">报警输入方式</label>
							<div><select class="custom-select" id="algConfig-pdsConf-0-pdAlarmInTrigger" data-role="none" value="{{algConfig.pdsConf.[0].pdAlarmInTrigger}}">
								<option value="0" {{#equal algConfig.pdsConf.[0].pdAlarmInTrigger 0}}selected="selected"{{/equal}} data-desc="High-Level">高电平</option>
								<option value="1"  {{#equal algConfig.pdsConf.[0].pdAlarmInTrigger 1}} selected="selected"{{/equal}} data-desc="Low-Level">低电平</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{showHWandCustomer "ADA32V4 ADA32V2" "201306"}}}>
							<label class="single_option_text" for="algConfig-pdsConf-0-pdCamPos" data-desc="pdCamPos">摄像头安装位置</label>
							<div><select class="custom-select" id="algConfig-pdsConf-0-pdCamPos" data-role="none" value="{{algConfig.pdsConf.[0].pdCamPos}}">
								<option value="0" {{#equal algConfig.pdsConf.[0].pdCamPos 0}}selected="selected"{{/equal}} data-desc="pos-front">前</option>
								<option value="1"  {{#equal algConfig.pdsConf.[0].pdCamPos 1}} selected="selected"{{/equal}} data-desc="pos-back">后</option>
								<option value="2" {{#equal algConfig.pdsConf.[0].pdCamPos 2}}selected="selected"{{/equal}} data-desc="pos-left">左</option>
								<option value="3"  {{#equal algConfig.pdsConf.[0].pdCamPos 3}} selected="selected"{{/equal}} data-desc="pos-right">右</option>
								<option value="4"  {{#equal algConfig.pdsConf.[0].pdCamPos 4}} selected="selected"{{/equal}} data-desc="off">关闭</option>
							</select></div>
						</div>
						<div data-role="none" class="input-switch-box Usr-Install" {{{hideNotHardware "ADA32V4 ADA32V2 ADA32V3 AICB046V1"}}}>
							<p data-desc="shelterAlarm" id="label-shelterEnable">遮挡报警开关</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-0-shelterEnable" {{#if algConfig.pdsConf.[0].shelterEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-0-shelterEnable"></label>
						</div>
						<div data-role="none" class="rangeinput Usr-Install" {{{hideNotHardware "ADA32V4 ADA32V2 AICB046V1"}}}>
							<p class="rangeinput_title" data-desc="shelter-timelimit"><p>
							<label data-role="none" for="algConfig-pdsConf-0-ShelterTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-0-ShelterTimelimit" min="1" max="300" step="1" value="{{algConfig.pdsConf.[0].ShelterTimelimit}}">
							<p class="rangeinput_value">{{algConfig.pdsConf.[0].ShelterTimelimit}}<p>
						</div>
						<div data-role="none" class="input-switch-box Usr-Install" {{{hideNotHardware "ADA32V4 ADA32V2 ADA32V3 AICB046V1"}}}>
							<p data-desc="shelterAudio">遮挡报警声音开关</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pdsConf-0-shelterAudio" {{#if algConfig.pdsConf.[0].shelterAudio}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-pdsConf-0-shelterAudio"></label>
						</div>
						<div data-role="none" class="rangeinput Usr-Install" {{{showHWandCustomer "ADA32V2" "200585"}}}>
							<p class="rangeinput_title" data-desc="Detect3-Time">标志3检测时间</p>
							<label data-role="none" for="algConfig-pdsConf-0-pdDetectTimelimit"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pdsConf-0-pdDetectTimelimit" min="3" max="120" step="1" value="{{algConfig.pdsConf.[0].pdDetectTimelimit}}">
							<p class="rangeinput_value">{{algConfig.pdsConf.[0].pdDetectTimelimit}}<p>
						</div>
						<div {{{hideHardware "HDW845V1"}}} class="Usr-Intg Usr-Install">
							<div data-role="none" class="input-text-box">
								<label data-role="none" data-desc="RedSensitivity">RedSensitivity</label>	
								<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
									<td><li>
										<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdRedSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.pdsConf.[0].pdRedSensitivity.[0]}}">
									</li></td>
									<td><li>
										<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdRedSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.pdsConf.[0].pdRedSensitivity.[1]}}">
									</li></td>
									<td><li>
										<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdRedSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.pdsConf.[0].pdRedSensitivity.[2]}}">
									</li></td>
								</table>
							</div>
							<div data-role="none" class="input-text-box">
								<label data-role="none">
									{{#isCustomer "200055 200055A"}}
										{{getKeyLang "pdOrangeSensitivity"}}								
									{{/isCustomer}}
								    {{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdYellowSensitivity"}}{{/isNotCustomer}}
								</label>	
								<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
									<td><li>
										<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdYellowSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.pdsConf.[0].pdYellowSensitivity.[0]}}">
									</li></td>
									<td><li>
										<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdYellowSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.pdsConf.[0].pdYellowSensitivity.[1]}}">
									</li></td>
									<td><li>
										<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdYellowSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.pdsConf.[0].pdYellowSensitivity.[2]}}">
									</li></td>
								</table>
							</div>
							<div data-role="none" class="input-text-box">
								<label data-role="none">
									{{#isCustomer "200055 200055A"}}
										{{getKeyLang "pdPurpleSensitivity"}}								
									{{/isCustomer}}
								    {{#isNotCustomer "200055 200055A"}}{{getKeyLang "pdGreenSensitivity"}}{{/isNotCustomer}}
								</label>	
								<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
									<td><li>
										<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdGreenSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.pdsConf.[0].pdGreenSensitivity.[0]}}">
									</li></td>
									<td><li>
										<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdGreenSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.pdsConf.[0].pdGreenSensitivity.[1]}}">
									</li></td>
									<td><li>
										<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdGreenSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.pdsConf.[0].pdGreenSensitivity.[2]}}">
									</li></td>
								</table>
							</div>
						</div>
						<div data-role="none" class="input-text-box" {{{showHWandCustomer "ADA32V2" "201933"}}}>
							<label data-role="none" data-desc="RectangleX">Rectangle_X</label>	
							<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
								<td><li>
									<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdRectangleX" class="char-normal" value="{{algConfig.pdsConf.[0].pdRectangleX}}">
								</li></td>
							</table>
						</div>
						<div data-role="none" class="input-text-box" {{{showHWandCustomer "ADA32V2" "201933"}}}>
							<label data-role="none" data-desc="RectangleY">Rectangle_Y</label>	
							<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
								<td><li>
									<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pdsConf-0-pdRectangleY" class="char-normal" value="{{algConfig.pdsConf.[0].pdRectangleY}}">
								</li></td>
							</table>
						</div>
						<div class="custom-select-box Usr-Install" {{{hideNotHardware "ADA32V2 ADA32V3 AICB046V1" }}}>
							<label class="single_option_text" for="algConfig-pdsConf-0-pdDetectPart" data-desc="pdDetectPart">GUI绘制模式</label>
							<div><select class="custom-select" id="algConfig-pdsConf-0-pdDetectPart" data-role="none" value="{{algConfig.pdsConf.[0].pdDetectPart}}">
								<option value="0" {{#equal algConfig.pdsConf.[0].pdDetectPart 0}}selected="selected"{{/equal}}>{{getKeyLang "all"}}</option>
								<option value="1" {{#equal algConfig.pdsConf.[0].pdDetectPart 1}}selected="selected"{{/equal}}>{{getKeyLang "bottom"}}</option>
							</select></div>
						</div>
						<h1 {{{hideNotHardware "none"}}}><p>{{getKeyLang "gengeral-sign"}}</p></h1>
						<div {{{hideNotHardware "none"}}}>
							<div class="custom-select-box">
								<img  src="./css/images/sign/Pic1.jpg" style="width:auto; height:70%;" />
								<div><select class="custom-select" id="algConfig-pdsConf-0-pdSign1" data-role="none" value="{{algConfig.pdsConf.[0].pdSign1}}">
									<option value="0" {{#equal algConfig.pdsConf.[0].pdSign1 0}}selected="selected"{{/equal}} data-desc="off">关</option>
									<option value="1"  {{#equal algConfig.pdsConf.[0].pdSign1 1}} selected="selected"{{/equal}} data-desc="on">开</option>
									<option value="2" {{#equal algConfig.pdsConf.[0].pdSign1 2}}selected="selected"{{/equal}} data-desc="disable">失能</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<img  src="./css/images/sign/Pic2.jpg" style="width:auto; height:70%;" />
								<div><select class="custom-select" id="algConfig-pdsConf-0-pdSign2" data-role="none" value="{{algConfig.pdsConf.[0].pdSign2}}">
									<option value="0" {{#equal algConfig.pdsConf.[0].pdSign2 0}}selected="selected"{{/equal}} data-desc="off">关</option>
									<option value="1"  {{#equal algConfig.pdsConf.[0].pdSign2 1}} selected="selected"{{/equal}} data-desc="on">开</option>
									<option value="2" {{#equal algConfig.pdsConf.[0].pdSign2 2}}selected="selected"{{/equal}} data-desc="disable">失能</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<img  src="./css/images/sign/Pic3.jpg" style="width:auto; height:70%;" />
								<div><select class="custom-select" id="algConfig-pdsConf-0-pdSign3" data-role="none" value="{{algConfig.pdsConf.[0].pdSign3}}">
									<option value="0" {{#equal algConfig.pdsConf.[0].pdSign3 0}}selected="selected"{{/equal}} data-desc="off">关</option>
									<option value="1"  {{#equal algConfig.pdsConf.[0].pdSign3 1}} selected="selected"{{/equal}} data-desc="on">开</option>
									<option value="2" {{#equal algConfig.pdsConf.[0].pdSign3 2}}selected="selected"{{/equal}} data-desc="disable">失能</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<img  src="./css/images/sign/Pic4.jpg" style="width:auto; height:70%;" />
								<div><select class="custom-select" id="algConfig-pdsConf-0-pdSign4" data-role="none" value="{{algConfig.pdsConf.[0].pdSign4}}">
									<option value="0" {{#equal algConfig.pdsConf.[0].pdSign4 0}}selected="selected"{{/equal}} data-desc="off">关</option>
									<option value="1"  {{#equal algConfig.pdsConf.[0].pdSign4 1}} selected="selected"{{/equal}} data-desc="on">开</option>
									<option value="2" {{#equal algConfig.pdsConf.[0].pdSign4 2}}selected="selected"{{/equal}} data-desc="disable">失能</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<img  src="./css/images/sign/Pic5.jpg" style="width:auto; height:70%;" />
								<div><select class="custom-select" id="algConfig-pdsConf-0-pdSign5" data-role="none" value="{{algConfig.pdsConf.[0].pdSign5}}">
									<option value="0" {{#equal algConfig.pdsConf.[0].pdSign5 0}}selected="selected"{{/equal}} data-desc="off">关</option>
									<option value="1"  {{#equal algConfig.pdsConf.[0].pdSign5 1}} selected="selected"{{/equal}} data-desc="on">开</option>
									<option value="2" {{#equal algConfig.pdsConf.[0].pdSign5 2}}selected="selected"{{/equal}} data-desc="disable">失能</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<img  src="./css/images/sign/Pic6.jpg" style="width:auto; height:70%;" />
								<div><select class="custom-select" id="algConfig-pdsConf-0-pdSign6" data-role="none" value="{{algConfig.pdsConf.[0].pdSign6}}">
									<option value="0" {{#equal algConfig.pdsConf.[0].pdSign6 0}}selected="selected"{{/equal}} data-desc="off">关</option>
									<option value="1"  {{#equal algConfig.pdsConf.[0].pdSign6 1}} selected="selected"{{/equal}} data-desc="on">开</option>
									<option value="2" {{#equal algConfig.pdsConf.[0].pdSign6 2}}selected="selected"{{/equal}} data-desc="disable">失能</option>
								</select></div>
							</div>										
						</div>
				</div>
				<div class="configmenu alg-conf" id="tab-apc-conf" {{#isNotSupportAPC ipcIdentification.hardware}}style="display: none;"{{/isNotSupportAPC}}>
					<h1><p>{{getKeyLang "ui-conf"}}</p></h1>
					<div class="custom-select-box">
						<label class="single_option_text" for="algConfig-apcConf-apcRoiGui" data-desc="pdroigui">GUI绘制模式</label>
						<div><select class="custom-select" id="algConfig-apcConf-apcRoiGui" data-role="none" value="{{algConfig.apcConf.apcRoiGui}}">
							<option value="1" {{#equal algConfig.apcConf.apcRoiGui 1}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-line"}}</option>
							<option value="2" {{#equal algConfig.apcConf.apcRoiGui 2}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-fill"}}</option>
						</select></div>
					</div>
					<div class="custom-select-box">
						<label class="single_option_text" for="algConfig-apcConf-apcUiStyle" data-desc="ui-style">UI Style</label>
						<div><select class="custom-select" id="algConfig-apcConf-apcUiStyle" data-role="none" value="{{algConfig.apcConf.apcUiStyle}}">
							<option value="0" {{#equal algConfig.apcConf.apcUiStyle 0}}selected="selected"{{/equal}}>ICON</option>
							<option value="1" {{#equal algConfig.apcConf.apcUiStyle 1}}selected="selected"{{/equal}}>TXT</option>
						</select></div>
					</div>
					<div data-role="none" class="input-switch-box">
						<p data-desc="total-Num">总人数</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-apcConf-apcUiTotalEnable" {{#if algConfig.apcConf.apcUiTotalEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-apcConf-apcUiTotalEnable"></label>
					</div>
					<div data-role="none" class="input-switch-box">
						<p data-desc="real-Num">实时数</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-apcConf-apcUiRealEnable" {{#if algConfig.apcConf.apcUiRealEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-apcConf-apcUiRealEnable"></label>
					</div>
					<div data-role="none" class="input-switch-box">
						<p data-desc="in-Num">进入人数</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-apcConf-apcUiInEnable" {{#if algConfig.apcConf.apcUiInEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-apcConf-apcUiInEnable"></label>
					</div>
					<div data-role="none" class="input-switch-box">
						<p data-desc="Out-Num">出去人数</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-apcConf-apcUiOutEnable" {{#if algConfig.apcConf.apcUiOutEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-apcConf-apcUiOutEnable"></label>
					</div>
					<h1><p>{{getKeyLang "alarm-header-conf"}}</p></h1>
					<div data-role="none" class="input-switch-box">
						<p data-desc="apc-OverloadAlarm-Enable">超载报警开关</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-apcConf-apcOverloadAlarmEnable" {{#if algConfig.apcConf.apcOverloadAlarmEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-apcConf-apcOverloadAlarmEnable"></label>
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="algConfig-apcConf-apcOverloadAlarmNum" data-desc="apc-OverloadAlarm-Num">核载人数</label>
						<input data-role="none" type="number" id="algConfig-apcConf-apcOverloadAlarmNum" value="{{algConfig.apcConf.apcOverloadAlarmNum}}">
					</div>
					<div data-role="none" class="input-switch-box">
						<p data-desc="apc-CrowdedAlarm-Enable">拥挤报警开关</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-apcConf-apcCrowdedAlarmEnable" {{#if algConfig.apcConf.apcCrowdedAlarmEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-apcConf-apcCrowdedAlarmEnable"></label>
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="algConfig-apcConf-apcCrowdedAlarmNum" data-desc="apc-CrowdedAlarm-Num">拥挤人数</label>
						<input data-role="none" type="number" id="algConfig-apcConf-apcCrowdedAlarmNum" value="{{algConfig.apcConf.apcCrowdedAlarmNum}}">
					</div>
				</div>
				<div id="alg-chn-1" class="pd-chn-ir" style="display:none">					
					<div class="custom-select-box">
						<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-audioType" data-desc="audio-type">audio</label>
						<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-audioType" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.audioType}}">
							<option value="1" {{#equal algConfig.irAlgConfig.pdsConf.audioType 1}}selected="selected"{{/equal}} data-desc="DULU">DULU</option>
							<option value="2" {{#equal algConfig.irAlgConfig.pdsConf.audioType 2}}selected="selected"{{/equal}} data-desc="TRAIN">TRAIN</option>
							<option value="3" {{#equal algConfig.irAlgConfig.pdsConf.audioType 3}}selected="selected"{{/equal}} data-desc="DO">DO</option>
							<option value="4" {{#equal algConfig.irAlgConfig.pdsConf.audioType 4}}selected="selected"{{/equal}} data-desc="PHONE">PHONE</option>
							<option value="5" {{#equal algConfig.irAlgConfig.pdsConf.audioType 5}}selected="selected"{{/equal}} data-desc="DIDU">DIDU</option>
							<option value="6" {{#equal algConfig.irAlgConfig.pdsConf.audioType 6}}selected="selected"{{/equal}} data-desc="DING">DING</option>
						</select></div>
					</div>
					<div class="alg-conf pd-chn-ir">
						<div class="configmenu">
							<h1><p>{{getKeyLang "pd-conf"}}</p></h1>
						</div>										
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-pdsModel" data-desc="pd-Model">检测模型</label>
							<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-pdsModel" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.pdsModel}}" disabled="disabled">
								<option value="14" {{#equal algConfig.irAlgConfig.pdsConf.pdsModel 14}}selected="selected"{{/equal}}>{{getKeyLang "pd-Model-Person"}}</option>
								<option value="9" {{#equal algConfig.irAlgConfig.pdsConf.pdsModel 9}}selected="selected"{{/equal}}>{{getKeyLang "pd-Model-Car"}}</option>
								<option value="4" {{#equal algConfig.irAlgConfig.pdsConf.pdsModel 4}}selected="selected"{{/equal}}>{{getKeyLang "pd-Model-PersonCar"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-pdSensitivity" data-desc="pd-Sensitivity">PDS灵敏度</label>
							<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-pdSensitivity" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.pdSensitivity}}">
								<option value="0" {{#equal algConfig.irAlgConfig.pdsConf.pdSensitivity 0}}selected="selected"{{/equal}} data-desc="sensitivity-low">Low</option>
								<option value="1" {{#equal algConfig.irAlgConfig.pdsConf.pdSensitivity 1}}selected="selected"{{/equal}} data-desc="sensitivity-medium">Medium</option>
								<option value="2" {{#equal algConfig.irAlgConfig.pdsConf.pdSensitivity 2}}selected="selected"{{/equal}} data-desc="sensitivity-high">High</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-pdOsdFontSize" data-desc="pd-OsdFontSize">置信值字体</label>
							<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-pdOsdFontSize" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.pdOsdFontSize}}">
								<option value="0" {{#equal algConfig.irAlgConfig.pdsConf.pdOsdFontSize 0}}selected="selected" {{/equal}} data-desc="off">OFF</option>
								<option value="1" {{#equal algConfig.irAlgConfig.pdsConf.pdOsdFontSize 1}}selected="selected"{{/equal}}>1X</option>
								<option value="2" {{#equal algConfig.irAlgConfig.pdsConf.pdOsdFontSize 2}}selected="selected"{{/equal}}>2X</option>
								<option value="3" {{#equal algConfig.irAlgConfig.pdsConf.pdOsdFontSize 3}}selected="selected"{{/equal}}>3X</option>
							</select></div>
						</div>
						<div data-role="none" class="input-switch-box">
							<p data-desc="pd-Alg-Enable">算法开关</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-irAlgConfig-pdsConf-algEnable" {{#if algConfig.irAlgConfig.pdsConf.algEnable}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-algEnable"></label>
						</div>
						<div data-role="none" class="input-switch-box">
							<p data-desc="pd-Alarm-In">触发输入</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdAlarmIn" {{#if algConfig.irAlgConfig.pdsConf.pdAlarmIn}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-pdAlarmIn"></label>
						</div>
						<div data-role="none" class="input-switch-box">
							<p data-desc="pd-test-mode">测试模式</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdTestMode" {{#if algConfig.irAlgConfig.pdsConf.pdTestMode}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-pdTestMode"></label>
						</div>
						<div data-role="none" class="input-switch-box" {{{showHWandCustomer "ADA32IR" "202613"}}} >
							<p data-desc="CrosshairIcon">十字准星图标开关</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdCrosshairIcon" {{#if algConfig.irAlgConfig.pdsConf.pdCrosshairIcon}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-pdCrosshairIcon"></label>
						</div>
						<div data-role="none" class="input-switch-box">
							<p data-desc="display-pdRectPerson">行人检测框</p>
							<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdRectPerson" {{#if algConfig.irAlgConfig.pdsConf.pdRectPerson}}checked="checked"{{/if}}>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-pdRectPerson"></label>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-pdInterval-red" data-desc="pdRed-interval">红区检测间隔</label>
							<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-pdInterval-red" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.pdInterval.red}}">
								<option value="-1" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
								<option value="0" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
								<option value="2" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
								<option value="3" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
								<option value="4" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
								<option value="5" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
								<option value="10" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
								<option value="30" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
								<option value="60" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
								<option value="90" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
								<option value="120" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
								<option value="180" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
								<option value="300" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.red 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-pdInterval-yellow" data-desc="pdYellow-interval">黄区检测间隔</label>
							<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-pdInterval-yellow" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.pdInterval.yellow}}">
								<option value="-1" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
								<option value="0" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
								<option value="2" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
								<option value="3" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
								<option value="4" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
								<option value="5" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
								<option value="10" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
								<option value="30" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
								<option value="60" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
								<option value="90" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
								<option value="120" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
								<option value="180" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
								<option value="300" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.yellow 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-pdInterval-green" data-desc="pdGreen-interval">绿区检测间隔</label>
							<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-pdInterval-green" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.pdInterval.green}}">
								<option value="-1" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
								<option value="0" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
								<option value="2" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
								<option value="3" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
								<option value="4" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
								<option value="5" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
								<option value="10" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
								<option value="30" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
								<option value="60" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
								<option value="90" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
								<option value="120" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
								<option value="180" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
								<option value="300" {{#equal algConfig.irAlgConfig.pdsConf.pdInterval.green 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
							</select></div>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "pdAlarmOut-Interval"}}<p>
							<div class="config_checkbox2">
								<p data-desc="auto">AUTO</p>
								<input data-role="none" class="checkBtn custom" id="ir-pdalarmout-auto-enable" type="checkbox" />
								<label data-role="none" for="ir-pdalarmout-auto-enable"></label>
							</div>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-pdAlarmOutInterval"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-irAlgConfig-pdsConf-pdAlarmOutInterval" min="0" max="10000" step="10" value="{{algConfig.irAlgConfig.pdsConf.pdAlarmOutInterval}}">
							<p  id="ir_pdalarm_interval_value" class="rangeinput_value">{{algConfig.irAlgConfig.pdsConf.pdAlarmOutInterval}}<p>
						</div>
						<div data-role="none" class="multi_selectbox">
							<p class="multi_selectbox_title">{{getKeyLang "pdAlarmOut-Switch"}}</p>
							<table rules="none" class="multi_selectbox_table">
								<td>
									<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdAlarmOutEnable-red"{{#if algConfig.irAlgConfig.pdsConf.pdAlarmOutEnable.red}}checked="checked"{{/if}}>
									<label for="algConfig-irAlgConfig-pdsConf-pdAlarmOutEnable-red"></label>
								</td>
								<td>
									<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdAlarmOutEnable-yellow"{{#if algConfig.irAlgConfig.pdsConf.pdAlarmOutEnable.yellow}}checked="checked"{{/if}}>
									<label for="algConfig-irAlgConfig-pdsConf-pdAlarmOutEnable-yellow"></label>
									
								</td>
								<td>
									<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdAlarmOutEnable-green"{{#if algConfig.irAlgConfig.pdsConf.pdAlarmOutEnable.green}}checked="checked"{{/if}}>
									<label for="algConfig-irAlgConfig-pdsConf-pdAlarmOutEnable-green"></label>
								</td>
							</table>
						</div>
						<div data-role="none" class="multi_selectbox">
							<p class="multi_selectbox_title">{{getKeyLang "pdRoi-Switch"}}</p>
							<table rules="none" class="multi_selectbox_table">

								<td>
									<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdRoiEnable-red" {{#if algConfig.irAlgConfig.pdsConf.pdRoiEnable.red}}checked="checked"{{/if}}>
									<label for="algConfig-irAlgConfig-pdsConf-pdRoiEnable-red"></label>
								</td>

								<td>
									<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdRoiEnable-yellow" {{#if algConfig.irAlgConfig.pdsConf.pdRoiEnable.yellow}}checked="checked"{{/if}}>
									<label for="algConfig-irAlgConfig-pdsConf-pdRoiEnable-yellow"></label>
									
								</td>
								<td>
									<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-irAlgConfig-pdsConf-pdRoiEnable-green" {{#if algConfig.irAlgConfig.pdsConf.pdRoiEnable.green}}checked="checked"{{/if}}>
									<label for="algConfig-irAlgConfig-pdsConf-pdRoiEnable-green"></label>
								</td>
							</table>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="algConfig-irAlgConfig-pdsConf-pdRoiGui" data-desc="pdroigui">GUI绘制模式</label>
							<div><select class="custom-select" id="algConfig-irAlgConfig-pdsConf-pdRoiGui" data-role="none" value="{{algConfig.irAlgConfig.pdsConf.pdRoiGui}}">
								<option value="0" {{#equal algConfig.irAlgConfig.pdsConf.pdRoiGui 0}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-hide"}}</option>
								<option value="1" {{#equal algConfig.irAlgConfig.pdsConf.pdRoiGui 1}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-line"}}</option>
								<option value="2" {{#equal algConfig.irAlgConfig.pdsConf.pdRoiGui 2}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-fill"}}</option>
							</select></div>
						</div>
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "min-work-speed+unit"}}<p>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-minWorkSpeed"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-irAlgConfig-pdsConf-minWorkSpeed" min="0" max="150" value="{{algConfig.irAlgConfig.pdsConf.minWorkSpeed}}">
							<p class="rangeinput_value">{{minWorkSpeed}}<p>
						</div>		
						<div data-role="none" class="rangeinput">
							<p class="rangeinput_title">{{getKeyLang "max-work-speed+unit"}}<p>
							<label data-role="none" for="algConfig-irAlgConfig-pdsConf-maxWorkSpeed"></label>
							<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-irAlgConfig-pdsConf-maxWorkSpeed" min="0" max="150" value="{{algConfig.irAlgConfig.pdsConf.maxWorkSpeed}}">
							<p class="rangeinput_value">{{maxWorkSpeed}}<p>
						</div>					
					</div>
					<div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" data-desc="RedSensitivity">RedSensitivity</label>	
							<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
								<td><li>
									<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdRedSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdRedSensitivity.[0]}}">
								</li></td>
								<td><li>
									<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdRedSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdRedSensitivity.[1]}}">
								</li></td>
								<td><li>
									<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdRedSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdRedSensitivity.[2]}}">
								</li></td>
							</table>
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" data-desc="pdYellowSensitivity">pdYellowSensitivity</label>	
							<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
								<td><li>
									<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdYellowSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdYellowSensitivity.[0]}}">
								</li></td>
								<td><li>
									<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdYellowSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdYellowSensitivity.[1]}}">
								</li></td>
								<td><li>
									<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdYellowSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdYellowSensitivity.[2]}}">
								</li></td>
							</table>
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" data-desc="pdGreenSensitivity">pdGreenSensitivity</label>	
							<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
								<td><li>
									<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdGreenSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdGreenSensitivity.[0]}}">
								</li></td>
								<td><li>
									<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdGreenSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdGreenSensitivity.[1]}}">
								</li></td>
								<td><li>
									<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-irAlgConfig-pdsConf-pdGreenSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.irAlgConfig.pdsConf.pdGreenSensitivity.[2]}}">
								</li></td>
							</table>
						</div>
					</div>						
				</div>
				<div class="configmenu alg-conf" id="tab-zoom-conf" style="display: none;">
					<div class="alg-conf">
						<h1><p>{{getKeyLang "zoom-conf"}}</p></h1>
					</div>
					<div class="custom-select-box">
						<label class="single_option_text" for="algConfig-zoomConf-sensitivity" data-desc="zoom-sensitivity">跟踪算法灵敏度</label>
						<div><select class="custom-select" id="algConfig-zoomConf-sensitivity" data-role="none" value="{{algConfig.zoomConf.sensitivity}}">
							<option value="0" {{#equal algConfig.zoomConf.sensitivity 0}}selected="selected"{{/equal}} data-desc="sensitivity-low">Low</option>
							<option value="1" {{#equal algConfig.zoomConf.sensitivity 1}}selected="selected"{{/equal}} data-desc="sensitivity-medium">Medium</option>
							<option value="2" {{#equal algConfig.zoomConf.sensitivity 2}}selected="selected"{{/equal}} data-desc="sensitivity-high">High</option>
						</select></div>
					</div>
					<div class="custom-select-box" {{{hideHardware "HDW845V1"}}}>
						<label class="single_option_text" for="algConfig-zoomConf-osdFontSize" data-desc="pd-OsdFontSize">置信值字体</label>
						<div><select class="custom-select" id="algConfig-zoomConf-osdFontSize" data-role="none" value="{{algConfig.zoomConf.osdFontSize}}">
							<option value="0" {{#equal algConfig.zoomConf.osdFontSize 0}}selected="selected" {{/equal}} data-desc="off">OFF</option>
							<option value="1" {{#equal algConfig.zoomConf.osdFontSize 1}}selected="selected"{{/equal}}>1X</option>
							<option value="2" {{#equal algConfig.zoomConf.osdFontSize 2}}selected="selected"{{/equal}}>2X</option>
							<option value="3" {{#equal algConfig.zoomConf.osdFontSize 3}}selected="selected"{{/equal}}>3X</option>
						</select></div>
					</div>
					<div data-role="none" class="input-switch-box">
						<p data-desc="zoom-alg-enable">算法开关</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-zoomConf-algEnable" {{#if algConfig.zoomConf.algEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-zoomConf-algEnable"></label>
					</div>
					<div data-role="none" class="input-switch-box" {{{hideHardware "HDW845V1"}}}>
						<p data-desc="pd-Alarm-In">触发输入</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-zoomConf-alarmInEnable" {{#if algConfig.zoomConf.alarmInEnable}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-zoomConf-alarmInEnable"></label>
					</div>
					<div data-role="none" class="input-switch-box" {{{hideHardware "HDW845V1"}}}>
						<p data-desc="pd-test-mode">测试模式</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-zoomConf-testMode" {{#if algConfig.zoomConf.testMode}}checked="checked"{{/if}}>
						<label data-role="none" for="algConfig-zoomConf-testMode"></label>
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" class="single_option_text" for="algConfig-zoomConf-threshold" data-desc="score-threshold">跟踪阈值</label>
						<input data-role="none" type="text" id="algConfig-zoomConf-threshold" class="char-normal" value="{{algConfig.zoomConf.threshold}}">
					</div>
					<div data-role="none" class="rangeinput" {{{hideHardware "HDW845V1"}}}>
						<p class="rangeinput_title">{{getKeyLang "pdAlarmOut-Interval"}}<p>
						<div class="config_checkbox2">
							<p data-desc="auto">AUTO</p>
							<input data-role="none" class="checkBtn custom" id="zoomalarmout-auto-enable" type="checkbox" />
							<label data-role="none" for="zoomalarmout-auto-enable"></label>
						</div>
						<label data-role="none" for="algConfig-zoomConf-alarmOutInterval"></label>
						<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-zoomConf-alarmOutInterval" min="0" max="10000" step="50" value="{{algConfig.zoomConf.alarmOutInterval}}">
						<p  id="zoomalarm_interval_value" class="rangeinput_value">{{algConfig.zoomConf.alarmOutInterval}}<p>
					</div>					
					<div data-role="none" class="multi_selectbox" {{{hideHardware "HDW845V1"}}}>
						<p class="multi_selectbox_title">{{getKeyLang "pdAlarmOut-Switch"}}</p>
						<table rules="none" class="multi_selectbox_table">
							<td>
								<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-zoomConf-alarmOutEnable-red"{{#if algConfig.zoomConf.alarmOutEnable.red}}checked="checked"{{/if}}>
								<label for="algConfig-zoomConf-alarmOutEnable-red"></label>
							</td>
						</table>
					</div>					
					<h1 {{{hideHardware "HDW845V1"}}}><p>{{getKeyLang "advanced-settings"}}</p></h1>
					<div class="custom-select-box" {{{hideHardware "HDW845V1"}}}>
						<label class="single_option_text" for="algConfig-zoomConf-alarmOutLv" data-desc="pdAlarmOut-Trigger">报警输出方式</label>
						<div><select class="custom-select" id="algConfig-zoomConf-alarmOutLv" data-role="none" value="{{algConfig.zoomConf.alarmOutLv}}">
							<option value="0" {{#equal algConfig.zoomConf.alarmOutLv 0}}selected="selected"{{/equal}} data-desc="High-Level">高电平</option>
							<option value="1"  {{#equal algConfig.zoomConf.alarmOutLv 1}} selected="selected"{{/equal}} data-desc="Low-Level">低电平</option>
						</select></div>
					</div>
					<div class="custom-select-box" {{{hideHardware "HDW845V1"}}}>
						<label class="single_option_text" for="algConfig-zoomConf-alarmInLv" data-desc="pdAlarmIn-Trigger">报警输入方式</label>
						<div><select class="custom-select" id="algConfig-zoomConf-alarmInLv" data-role="none" value="{{algConfig.zoomConf.alarmInLv}}">
							<option value="0" {{#equal algConfig.zoomConf.alarmInLv 0}}selected="selected"{{/equal}} data-desc="High-Level">高电平</option>
							<option value="1"  {{#equal algConfig.zoomConf.alarmInLv 1}} selected="selected"{{/equal}} data-desc="Low-Level">低电平</option>
						</select></div>
					</div>					
				</div>		
			</div>
			<div id="networkConfig" class="tab-configs Usr-Install">
				<div class="configmenu" {{#isWFdev ipcIdentification.hardware}}style="display: none;"{{/isWFdev}} {{{hideNotWebuiFull "ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR ADA32E1 DMS31V2 DMS885N HDW845V1"}}}>
					<h1><p>{{getKeyLang "dns-server"}}</p></h1>
					<div data-role="none" class="input-text-box">
						<label data-role="none"  for="networkConfig-dnsServer" data-desc="dns-server">DNS Server</label>
						<input data-role="none" type="text" id="networkConfig-dnsServer" class="empty-limit ipaddr-limit char-normal" value="{{networkConfig.dnsServer}}">
					</div>
				</div>

				<div class="configmenu" {{{showHardware "WFCR20S2"}}}>
					<h1><p>{{getKeyLang "gb28181-enable"}}</p></h1>
					<div class="config_checkbox">
						<input data-role="none" class="checkBtn custom" type="checkbox" id="serverConfig-GB28181Enable" {{#if serverConfig.GB28181Enable}}checked="checked"{{/if}}/>
						<label data-role="none" for="serverConfig-GB28181Enable"></label>
						<p data-desc="enable">GB28181Enable</p>
					</div>

					<div data-role="none" class="input-text-box">
						<label data-role="none"  for="serverConfig-SIPServerId" data-desc="sip-server-id">SIP Server ID</label>
						<input data-role="none" type="text" id="serverConfig-SIPServerId" class="char-normal" value="{{serverConfig.SIPServerId}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none"  for="serverConfig-SIPServerIp" data-desc="sip-server-ip">SIP Server IP</label>
						<input data-role="none" type="text" id="serverConfig-SIPServerIp" class="char-normal" value="{{serverConfig.SIPServerIp}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none"  for="serverConfig-DevSIPId" data-desc="dev-sip-id">Device SIP ID</label>
						<input data-role="none" type="text" id="serverConfig-DevSIPId" class="char-normal" value="{{serverConfig.DevSIPId}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="serverConfig-SIPDOMAIN" data-desc="sip-domain">SIP DOMAIN</label>
						<input data-role="none" type="text" id="serverConfig-SIPDOMAIN" class="char-normal" value="{{serverConfig.SIPDOMAIN}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="serverConfig-SIPPwd" data-desc="sip-password">SIP Password</label>
						<input data-role="none" type="text" id="serverConfig-SIPPwd" class="char-normal" value="{{serverConfig.SIPPwd}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="serverConfig-SIPServerPort" data-desc="sip-server-port">SIP Server Port</label>
						<input data-role="none" type="text" id="serverConfig-SIPServerPort" class="char-normal" value="{{serverConfig.SIPServerPort}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="serverConfig-DevSIPPort" data-desc="dev-sip-port">Device SIP Port</label>
						<input data-role="none" type="text" id="serverConfig-DevSIPPort" class="char-normal" value="{{serverConfig.DevSIPPort}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="serverConfig-KaPeriod" data-desc="keepalive-period">KeepAlive Period</label>
						<input data-role="none" type="text" id="serverConfig-KaPeriod" class="char-normal" value="{{serverConfig.KaPeriod}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="serverConfig-RegInterval" data-desc="regc-interval">Regcister interval</label>
						<input data-role="none" type="text" id="serverConfig-RegInterval" class="char-normal" value="{{serverConfig.RegInterval}}">
					</div>

				</div>


				
				<div class="configmenu" {{#isNotSupportServer ipcIdentification.hardware}}style="display: none;"{{/isNotSupportServer}} >
					<h1><p>{{getKeyLang "sys-server"}}</p></h1>
					<div class="config_checkbox">
						<input data-role="none" class="checkBtn custom" type="checkbox" id="serverConfig-enable" {{#if serverConfig.enable}}checked="checked"{{/if}}/>
						<label data-role="none" for="serverConfig-enable"></label>
						<p data-desc="enable">Enable</p>
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" id="tab-control-server" for="serverConfig-serverAddress" data-desc="control-server">URL</label>
						<input data-role="none" type="text" id="serverConfig-serverAddress" class="empty-limit char-normal" value="{{serverConfig.serverAddress}}">
					</div>

					<div style="height: 10px;"></div>
					<div class="custom-select-box">
						<label class="single_option_text" for="serverConfig-netType" data-desc="server-netType">netType</label>
						<div><select class="custom-select" id="serverConfig-netType" data-role="none" value="{{serverConfig.netType}}">
							<option value="0" {{#equal serverConfig.netType 0}}selected="selected"{{/equal}}{{{hideHardware "ADA32C4"}}}>LAN</option>
							<option value="1" {{#equal serverConfig.netType 1}}selected="selected"{{/equal}}{{{hideHardware "ADA32N1"}}}>WIFI</option>
							<option value="3" {{#equal serverConfig.netType 3}}selected="selected"{{/equal}}{{{hideHardware "ADA32N1"}}}>4G</option>
						</select></div>
					</div>
					<div class="custom-select-box" {{{hideHardware "ADA32N1"}}}>
						<label class="single_option_text" for="serverConfig-uploadFileOpts" data-desc="upload-file-options">Upload File Type</label>
						<div><select class="custom-select" id="serverConfig-uploadFileOpts" data-role="none" value="{{serverConfig.uploadFileOpts}}">
							<option value="0" {{#equal serverConfig.uploadFileOpts 0}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
							<option value="1" {{#equal serverConfig.uploadFileOpts 1}}selected="selected"{{/equal}}>{{getKeyLang "record-video"}}</option>
							<option value="2" {{#equal serverConfig.uploadFileOpts 2}}selected="selected"{{/equal}} {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR ADA900V1 HDW845V1"}}}>{{getKeyLang "record-pic"}}</option>
							<option value="3" {{#equal serverConfig.uploadFileOpts 3}}selected="selected"{{/equal}} {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR ADA900V1 HDW845V1"}}}>{{getKeyLang "record-video&pic"}}</option>
						</select></div>
					</div>
					<div class="custom-select-box" {{{hideNotWebuiFull "DMS31V2 DMS885N"}}} {{{hideHardware "ADA32N1"}}}>
						<label class="single_option_text" for="serverConfig-recordLevel" data-desc="rec-log-level">Record Log Level</label>
						<div><select class="custom-select" id="serverConfig-recordLevel" data-role="none" value="{{serverConfig.recordLevel}}">
							<option value="-1" {{#equal serverConfig.recordLevel -1}}selected="selected"{{/equal}}>Off</option>
							<option value="0" {{#equal serverConfig.recordLevel 0}}selected="selected"{{/equal}}>0</option>
							<option value="1" {{#equal serverConfig.recordLevel 1}}selected="selected"{{/equal}}>1</option>
							<option value="2" {{#equal serverConfig.recordLevel 2}}selected="selected"{{/equal}}>2</option>
							<option value="3" {{#equal serverConfig.recordLevel 3}}selected="selected"{{/equal}}>3</option>
						</select></div>
					</div>
					<div class="custom-select-box" style="display:none;">
						<label class="single_option_text" for="serverConfig-uploadLevel" data-desc="upload-log-level">Upload Log Level</label>
						<div><select class="custom-select" id="serverConfig-uploadLevel" data-role="none" value="{{serverConfig.uploadLevel}}">
							<option value="-1" {{#equal serverConfig.uploadLevel -1}}selected="selected"{{/equal}}>Off</option>
							<option value="0" {{#equal serverConfig.uploadLevel 0}}selected="selected"{{/equal}}>0</option>
							<option value="1" {{#equal serverConfig.uploadLevel 1}}selected="selected"{{/equal}}>1</option>
							<option value="2" {{#equal serverConfig.uploadLevel 2}}selected="selected"{{/equal}}>2</option>
							<option value="3" {{#equal serverConfig.uploadLevel 3}}selected="selected"{{/equal}}>3</option>
						</select></div>
					</div>
				</div>

				<div class="configmenu"{{{showHardware "DMS31V2"}}}>
					<h1><p>{{getKeyLang "bbConfig"}}</p></h1>
					<div class="config_checkbox">
						<input data-role="none" class="checkBtn custom" type="checkbox" id="serverConfig-bb808Config-bb808Enable" {{#if serverConfig.bb808Config.bb808Enable}}checked="checked"{{/if}}/>
						<label data-role="none" for="serverConfig-bb808Config-bb808Enable"></label>
						<p data-desc="enable">Enable</p>
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" " for="serverConfig-bb808Config-bb808ServerIP" data-desc="server-addr">IP</label>
						<input data-role="none" type="text" id="serverConfig-bb808Config-bb808ServerIP" class="char-normal" value="{{serverConfig.bb808Config.bb808ServerIP}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none"  for="serverConfig-bb808Config-bb808ServerPort" data-desc="bb808Port">Port</label>
						<input data-role="none" type="number" id="serverConfig-bb808Config-bb808ServerPort" value="{{serverConfig.bb808Config.bb808ServerPort}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none"  for="serverConfig-bb808Config-bb808DeviceID" data-desc="deviceID">deviceID</label>
						<input data-role="none" type="text" id="serverConfig-bb808Config-bb808DeviceID" class="char-normal" value="{{serverConfig.bb808Config.bb808DeviceID}}">
					</div>
					<div class="custom-select-box">
						<label class="single_option_text" for="serverConfig-bb808Config-bb808NetType" data-desc="server-netType">netType</label>
						<div><select class="custom-select" id="serverConfig-bb808Config-bb808NetType" data-role="none" value="{{serverConfig.bb808Config.bb808NetType}}">
							<option value="0" {{#equal serverConfig.bb808Config.bb808NetType 0}}selected="selected"{{/equal}}{{{hideHardware "ADA32C4"}}}>LAN</option>
							<option value="1" {{#equal serverConfig.bb808Config.bb808NetType 1}}selected="selected"{{/equal}}>WIFI</option>
							<option value="2" {{#equal serverConfig.bb808Config.bb808NetType 2}}selected="selected"{{/equal}}>4G</option>
						</select></div>
					</div>

				</div>
				
				<div class="configmenu"  {{#isNotSupportServer ipcIdentification.hardware}}style="display: none;"{{/isNotSupportServer}} {{{hideHardware "ADA32N1"}}}>
					<h1><p>{{getKeyLang "4gNetWork"}}</p></h1>
					<div data-role="none"  class="input-text-box">
						<label data-role="none" class="single_option_text" for="networkConfig-4g-apn" data-desc="APN">APN</label>
						<input data-role="none" type="text" id="networkConfig-4g-apn" class="char-normal" value="{{networkConfig.4g.apn}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" class="single_option_text" for="networkConfig-4g-apnUser"  data-desc="APNuser">APN UserName</label>
						<input data-role="none" type="text" id="networkConfig-4g-apnUser" class="char-normal" value="{{networkConfig.4g.apnUser}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" class="single_option_text" for="networkConfig-4g-apnPassword" data-desc="APNpassword">APN Password</label>
						<input data-role="none" type="text" id="networkConfig-4g-apnPassword" class="char-normal" value="{{networkConfig.4g.apnPassword}}">
					</div>
					<div class="custom-select-box">
						<label class="single_option_text" for="networkConfig-4g-LogTestMode" data-desc="4g-log-test">LogTestMode</label>
						<div><select class="custom-select" id="networkConfig-4g-LogTestMode" data-role="none" value="{{networkConfig.4g.LogTestMode}}">
							<option value="0" {{#equal networkConfig.4g.LogTestMode 0}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
							<option value="1" {{#equal networkConfig.4g.LogTestMode 1}}selected="selected"{{/equal}}>{{getKeyLang "on"}}</option>
						</select></div>
					</div>
				</div>
				<div class="configmenu" {{#isWFdev ipcIdentification.hardware}}style="display: none;"{{/isWFdev}}{{{hideHardware "ADA32C4"}}}>
					<h1><p>{{getKeyLang "ethernet"}}</p></h1>
					<div class="config_checkbox">
						<input data-role="none" class="checkBtn custom" type="checkbox" id="networkConfig-ethernet-enableDHCP" {{#if networkConfig.ethernet.enableDHCP}}checked="checked"{{/if}}>
						<label data-role="none" for="networkConfig-ethernet-enableDHCP"></label>
						<p data-desc="dhcp">DHCP</p>	
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="networkConfig-ethernet-dhcpTimeout" data-desc="dhcp-timeout">DHCP Timeout</label>
						<input data-role="none" type="number" id="networkConfig-ethernet-dhcpTimeout" value="{{networkConfig.ethernet.dhcpTimeout}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" id="tab-ipaddr" for="networkConfig-ethernet-ipAddress" data-desc="ipaddr">IP Address</label>
						<input data-role="none" type="text" id="networkConfig-ethernet-ipAddress" class="empty-limit ipaddr-limit char-normal" value="{{networkConfig.ethernet.ipAddress}}"{{#if networkConfig.ethernet.enableDHCP}}readonly="true" style="color:#aaa;" {{/if}}>
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" id="tab-subnet-mask" for="networkConfig-ethernet-subnetMask" data-desc="subnet-mask">Subnet Mask</label>
						<input data-role="none" type="text" id="networkConfig-ethernet-subnetMask" class="empty-limit subnetmask-limit char-normal" value="{{networkConfig.ethernet.subnetMask}}"{{#if networkConfig.ethernet.enableDHCP}}readonly="true" style="color:#aaa;" {{/if}}>
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" id="tab-gateway" for="networkConfig-ethernet-gateway" data-desc="gateway">Gateways</label>
						<input data-role="none" type="text" id="networkConfig-ethernet-gateway" class="empty-limit gateway-limit char-normal" value="{{networkConfig.ethernet.gateway}}"{{#if networkConfig.ethernet.enableDHCP}}readonly="true" style="color:#aaa;" {{/if}}>
					</div>

					<!--此处不再显示只读的MAC地址，如要查看可以在query处查看-->
					<!--<div data-role="none" class="input-text-box" {{{hideNotWebuiFull "DMS31V2 DMS885N ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR"}}}>
						<label data-role="none" id="tab-mac-address" for="networkConfig-ethernet-macAddress" data-desc="mac-address">MAC Address</label>
						<input data-role="none" type="text" id="networkConfig-ethernet-macAddress" class="empty-limit macaddr-limit char-normal" value="{{networkConfig.ethernet.macAddress}}"readonly="true" style="color:#aaa;">
					</div>-->

				</div>
				<div class="configmenu" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR HDW845V1"}}} {{#isIPCdev ipcIdentification.hardware}}style="display: none;"{{/isIPCdev}} {{{hideHardware "IPTR20S1"}}} {{{hideHardware "ADA32N1 MN234 ADA38N1"}}}>
					<h1><p>{{getKeyLang "wifi"}}</p></h1>
					<div class="custom-select-box">
						<label class="single_option_text" for="networkConfig-wifi-apMode-authMode" data-desc="auth-mode">认证模式</label>
						<div><select class="custom-select" id="networkConfig-wifi-apMode-authMode" data-role="none" value="{{networkConfig.wifi.apMode.authMode}}">
							<option value="NONE" {{#equal networkConfig.wifi.apMode.authMode "NONE"}}selected="selected"{{/equal}}>NONE</option>
							<option value="WPAPSK" {{#equal networkConfig.wifi.apMode.authMode "WPAPSK"}}selected="selected"{{/equal}}>WPAPSK</option>
						</select></div>
					</div>
					<div class="custom-select-box" {{{hideHardware "DMS31V2 DMS885N"}}}>
						<label class="single_option_text" for="networkConfig-wifi-apMode-frequency" data-desc="ap-frequency">AP频段</label>
			    		<div><select class="custom-select" id="networkConfig-wifi-apMode-frequency" data-role="none" value="{{networkConfig.wifi.apMode.frequency}}">
					        <option value="2.4G" {{#equal networkConfig.wifi.apMode.frequency "2.4G"}}selected="selected"{{/equal}}>2.4G</option>
							<option value="5G" {{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}} {{#equal networkConfig.wifi.apMode.frequency "5G"}}selected="selected"{{/equal}}>5G</option>
					    </select></div>
					</div>		

					<div class="custom-select-box" id="set-2G-channel" {{#unequal networkConfig.wifi.apMode.frequency "2.4G"}}style="display: none;"{{/unequal}} {{{hideHardware "DMS31V2 DMS885N"}}}>
						<label class="single_option_text" for="networkConfig-wifi-apMode-set2GChannel" data-desc="ap-channel">热点信道</label>
			    		<div><select class="custom-select" id="networkConfig-wifi-apMode-set2GChannel" data-role="none" value="0"> 
								<option value="0">AUTO</option>
							 <!-- {{#each networkConfig.wifi.apMode.availchannel}}
									{{#lessThan this 15}}
								< option value="{{this}}">{{this}}</option>
									{{/lessThan}}
								{{/each}}-->
							</select></div>
				    </div>
						
					<div class="custom-select-box" id="set-5G-channel" {{#unequal networkConfig.wifi.apMode.frequency "5G"}}style="display: none;"{{/unequal}} {{{hideHardware "DMS31V2 DMS885N"}}}>
						<label class="single_option_text" for="networkConfig-wifi-apMode-set5GChannel" data-desc="ap-channel">热点信道</label>
			    		<div><select class="custom-select" id="networkConfig-wifi-apMode-set5GChannel" data-role="none" value="0">
					        <option value="0">AUTO</option>
						 <!--	 {{#each networkConfig.wifi.apMode.availchannel}}
								{{#lessThan 15 this}}
							<option value="{{this}}">{{this}}</option>
								{{/lessThan}}
							{{/each}} -->
					    </select></div>
					</div>
					
					<div class="custom-select-box" {{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}}>
						<label class="single_option_text" for="networkConfig-wifi-apMode-countryCode" data-desc="ap-country">Country Code</label>
						<div><select class="custom-select" id="networkConfig-wifi-apMode-countryCode" data-role="none" value={{networkConfig.wifi.apMode.countryCode}}>
								<option value="AD" {{#equal networkConfig.wifi.apMode.countryCode "AD"}}selected="selected"{{/equal}}>AD (Andorra)</option>
                                <option value="AE" {{#equal networkConfig.wifi.apMode.countryCode "AE"}}selected="selected"{{/equal}}>AE (United Arab Emirates)</option>
                                <option value="AF" {{#equal networkConfig.wifi.apMode.countryCode "AF"}}selected="selected"{{/equal}}>AF (Afghanistan)</option>
                                <option value="AG" {{#equal networkConfig.wifi.apMode.countryCode "AG"}}selected="selected"{{/equal}}>AG (Antigua & Barbuda)</option>
                                <option value="AI" {{#equal networkConfig.wifi.apMode.countryCode "AI"}}selected="selected"{{/equal}}>AI (Anguilla(UK))</option>
                                <option value="AL" {{#equal networkConfig.wifi.apMode.countryCode "AL"}}selected="selected"{{/equal}}>AL (Albania)</option>
                                <option value="AM" {{#equal networkConfig.wifi.apMode.countryCode "AM"}}selected="selected"{{/equal}}>AM (Armenia)</option>
								<option value="AO" {{#equal networkConfig.wifi.apMode.countryCode "AO"}}selected="selected"{{/equal}}>AO (Angola)</option>
								<option value="AQ" {{#equal networkConfig.wifi.apMode.countryCode "AQ"}}selected="selected"{{/equal}}>AQ (Antarctica)</option>
								<option value="AR" {{#equal networkConfig.wifi.apMode.countryCode "AR"}}selected="selected"{{/equal}}>AR (Argentina)</option>
								<option value="AS" {{#equal networkConfig.wifi.apMode.countryCode "AS"}}selected="selected"{{/equal}}>AS (American Samoa)</option>
								<option value="AT" {{#equal networkConfig.wifi.apMode.countryCode "AT"}}selected="selected"{{/equal}}>AT (Austria)</option>
								<option value="AU" {{#equal networkConfig.wifi.apMode.countryCode "AU"}}selected="selected"{{/equal}}>AU (Australia)</option>
								<option value="AW" {{#equal networkConfig.wifi.apMode.countryCode "AW"}}selected="selected"{{/equal}}>AW (Aruba)</option>
								<option value="AZ" {{#equal networkConfig.wifi.apMode.countryCode "AZ"}}selected="selected"{{/equal}}>AZ (Azerbaijan)</option>
								<option value="BA" {{#equal networkConfig.wifi.apMode.countryCode "BA"}}selected="selected"{{/equal}}>BA (Bosnia & Herzegovina)</option>
								<option value="BB" {{#equal networkConfig.wifi.apMode.countryCode "BB"}}selected="selected"{{/equal}}>BB (Barbados)</option>
								<option value="BD" {{#equal networkConfig.wifi.apMode.countryCode "BD"}}selected="selected"{{/equal}}>BD (Bangladesh)</option>
								<option value="BE" {{#equal networkConfig.wifi.apMode.countryCode "BE"}}selected="selected"{{/equal}}>BE (Belgium)</option>
								<option value="BF" {{#equal networkConfig.wifi.apMode.countryCode "BF"}}selected="selected"{{/equal}}>BF (Burkina Faso)</option>
								<option value="BG" {{#equal networkConfig.wifi.apMode.countryCode "BG"}}selected="selected"{{/equal}}>BG (Bulgaria)</option>
								<option value="BH" {{#equal networkConfig.wifi.apMode.countryCode "BH"}}selected="selected"{{/equal}}>BH (Bahrain)</option>
								<option value="BI" {{#equal networkConfig.wifi.apMode.countryCode "BI"}}selected="selected"{{/equal}}>BI (Burundi)</option>
								<option value="BJ" {{#equal networkConfig.wifi.apMode.countryCode "BJ"}}selected="selected"{{/equal}}>BJ (Benin)</option>
								<option value="BN" {{#equal networkConfig.wifi.apMode.countryCode "BN"}}selected="selected"{{/equal}}>BN (Brunei)</option>
								<option value="BO" {{#equal networkConfig.wifi.apMode.countryCode "BO"}}selected="selected"{{/equal}}>BO (Bolivia)</option>
								<option value="BR" {{#equal networkConfig.wifi.apMode.countryCode "BR"}}selected="selected"{{/equal}}>BR (Brazil)</option>
								<option value="BS" {{#equal networkConfig.wifi.apMode.countryCode "BS"}}selected="selected"{{/equal}}>BS (Bahamas)</option>
								<option value="BW" {{#equal networkConfig.wifi.apMode.countryCode "BW"}}selected="selected"{{/equal}}>BW (Botswana)</option>
								<option value="BY" {{#equal networkConfig.wifi.apMode.countryCode "BY"}}selected="selected"{{/equal}}>BY (Belarus)</option>
								<option value="BZ" {{#equal networkConfig.wifi.apMode.countryCode "BZ"}}selected="selected"{{/equal}}>BZ (Belize)</option>
								<option value="CA" {{#equal networkConfig.wifi.apMode.countryCode "CA"}}selected="selected"{{/equal}}>CA (Canada)</option>
								<option value="CC" {{#equal networkConfig.wifi.apMode.countryCode "CC"}}selected="selected"{{/equal}}>CC (Cocos (Keeling) Islands (Australia))</option>
								<option value="CD" {{#equal networkConfig.wifi.apMode.countryCode "CD"}}selected="selected"{{/equal}}>CD (Congo, Republic of the)</option>
								<option value="CF" {{#equal networkConfig.wifi.apMode.countryCode "CF"}}selected="selected"{{/equal}}>CF (Central African Republic)</option>
								<option value="CG" {{#equal networkConfig.wifi.apMode.countryCode "CG"}}selected="selected"{{/equal}}>CG (Congo, Democratic Republic of the. Zaire)</option>
								<option value="CH" {{#equal networkConfig.wifi.apMode.countryCode "CH"}}selected="selected"{{/equal}}>CH (Switzerland)</option>
								<option value="CI" {{#equal networkConfig.wifi.apMode.countryCode "CI"}}selected="selected"{{/equal}}>CI (Cote d'Ivoire)</option>
								<option value="CK" {{#equal networkConfig.wifi.apMode.countryCode "CK"}}selected="selected"{{/equal}}>CK (Cook Islands)</option>
								<option value="CL" {{#equal networkConfig.wifi.apMode.countryCode "CL"}}selected="selected"{{/equal}}>CL (Chile)</option>
								<option value="CM" {{#equal networkConfig.wifi.apMode.countryCode "CM"}}selected="selected"{{/equal}}>CM (Cameroon)</option>
								<option value="CN" {{#equal networkConfig.wifi.apMode.countryCode "CN"}}selected="selected"{{/equal}}>CN (China)</option>
								<option value="CO" {{#equal networkConfig.wifi.apMode.countryCode "CO"}}selected="selected"{{/equal}}>CO (Colombia)</option>
								<option value="CR" {{#equal networkConfig.wifi.apMode.countryCode "CR"}}selected="selected"{{/equal}}>CR (Costa Rica)</option>
								<option value="CV" {{#equal networkConfig.wifi.apMode.countryCode "CV"}}selected="selected"{{/equal}}>CV (Cape Verde)</option>
								<option value="CX" {{#equal networkConfig.wifi.apMode.countryCode "CX"}}selected="selected"{{/equal}}>CX (Christmas Island (Australia))</option>
								<option value="CY" {{#equal networkConfig.wifi.apMode.countryCode "CY"}}selected="selected"{{/equal}}>CY (Cyprus)</option>
								<option value="CZ" {{#equal networkConfig.wifi.apMode.countryCode "CZ"}}selected="selected"{{/equal}}>CZ (Czech Republic)</option>
								<option value="DE" {{#equal networkConfig.wifi.apMode.countryCode "DE"}}selected="selected"{{/equal}}>DE (Germany)</option>
								<option value="DJ" {{#equal networkConfig.wifi.apMode.countryCode "DJ"}}selected="selected"{{/equal}}>DJ (Djibouti)</option>
								<option value="DK" {{#equal networkConfig.wifi.apMode.countryCode "DK"}}selected="selected"{{/equal}}>DK (Denmark)</option>
								<option value="DM" {{#equal networkConfig.wifi.apMode.countryCode "DM"}}selected="selected"{{/equal}}>DM (Dominica)</option>
								<option value="DO" {{#equal networkConfig.wifi.apMode.countryCode "DO"}}selected="selected"{{/equal}}>DO (Dominican Republic)</option>
								<option value="DZ" {{#equal networkConfig.wifi.apMode.countryCode "DZ"}}selected="selected"{{/equal}}>DZ (Algeria)</option>
								<option value="EC" {{#equal networkConfig.wifi.apMode.countryCode "EC"}}selected="selected"{{/equal}}>EC (Ecuador)</option>
								<option value="EE" {{#equal networkConfig.wifi.apMode.countryCode "EE"}}selected="selected"{{/equal}}>EE (Estonia)</option>
								<option value="EG" {{#equal networkConfig.wifi.apMode.countryCode "EG"}}selected="selected"{{/equal}}>EG (Egypt)</option>
								<option value="EH" {{#equal networkConfig.wifi.apMode.countryCode "EH"}}selected="selected"{{/equal}}>EH (Western Sahara)</option>
								<option value="ER" {{#equal networkConfig.wifi.apMode.countryCode "ER"}}selected="selected"{{/equal}}>ER (Eritrea)</option>
								<option value="ES" {{#equal networkConfig.wifi.apMode.countryCode "ES"}}selected="selected"{{/equal}}>ES (Spain, Canary Islands, Ceuta, Melilla)</option>
								<option value="ET" {{#equal networkConfig.wifi.apMode.countryCode "ET"}}selected="selected"{{/equal}}>ET (Ethiopia)</option>
								<option value="FI" {{#equal networkConfig.wifi.apMode.countryCode "FI"}}selected="selected"{{/equal}}>FI (Finland)</option>
								<option value="FJ" {{#equal networkConfig.wifi.apMode.countryCode "FJ"}}selected="selected"{{/equal}}>FJ (Fiji)</option>
								<option value="FK" {{#equal networkConfig.wifi.apMode.countryCode "FK"}}selected="selected"{{/equal}}>FK (Falkland Islands (Islas Malvinas) (UK))</option>
								<option value="FM" {{#equal networkConfig.wifi.apMode.countryCode "FM"}}selected="selected"{{/equal}}>FM (Micronesia, Federated States of (USA))</option>
								<option value="FO" {{#equal networkConfig.wifi.apMode.countryCode "FO"}}selected="selected"{{/equal}}>FO (Faroe Islands (Denmark))</option>
								<option value="FR" {{#equal networkConfig.wifi.apMode.countryCode "FR"}}selected="selected"{{/equal}}>FR (France)</option>
								<option value="GA" {{#equal networkConfig.wifi.apMode.countryCode "GA"}}selected="selected"{{/equal}}>GA (Gabon)</option>
								<option value="GB" {{#equal networkConfig.wifi.apMode.countryCode "GB"}}selected="selected"{{/equal}}>GB (Great Britain (United Kingdom; England))</option>
								<option value="GD" {{#equal networkConfig.wifi.apMode.countryCode "GD"}}selected="selected"{{/equal}}>GD (Grenada)</option>
								<option value="GE" {{#equal networkConfig.wifi.apMode.countryCode "GE"}}selected="selected"{{/equal}}>GE (Georgia)</option>
								<option value="GF" {{#equal networkConfig.wifi.apMode.countryCode "GF"}}selected="selected"{{/equal}}>GF (French Guiana)</option>
								<option value="GG" {{#equal networkConfig.wifi.apMode.countryCode "GG"}}selected="selected"{{/equal}}>GG (Guernsey (UK))</option>
								<option value="GH" {{#equal networkConfig.wifi.apMode.countryCode "GH"}}selected="selected"{{/equal}}>GH (Ghana)</option>
								<option value="GI" {{#equal networkConfig.wifi.apMode.countryCode "GI"}}selected="selected"{{/equal}}>GI (Gibraltar (UK))</option>
								<option value="GL" {{#equal networkConfig.wifi.apMode.countryCode "GL"}}selected="selected"{{/equal}}>GL (Greenland (Denmark))</option>
								<option value="GM" {{#equal networkConfig.wifi.apMode.countryCode "GM"}}selected="selected"{{/equal}}>GM (Gambia)</option>
								<option value="GN" {{#equal networkConfig.wifi.apMode.countryCode "GN"}}selected="selected"{{/equal}}>GN (Guinea)</option>
								<option value="GP" {{#equal networkConfig.wifi.apMode.countryCode "GP"}}selected="selected"{{/equal}}>GP (Guadeloupe (France))</option>
								<option value="GQ" {{#equal networkConfig.wifi.apMode.countryCode "GQ"}}selected="selected"{{/equal}}>GQ (Equatorial Guinea)</option>
								<option value="GR" {{#equal networkConfig.wifi.apMode.countryCode "GR"}}selected="selected"{{/equal}}>GR (Greece)</option>
								<option value="GS" {{#equal networkConfig.wifi.apMode.countryCode "GS"}}selected="selected"{{/equal}}>GS (South Georgia and the Sandwich Islands (UK))</option>
								<option value="GT" {{#equal networkConfig.wifi.apMode.countryCode "GT"}}selected="selected"{{/equal}}>GT (Guatemala)</option>
								<option value="GU" {{#equal networkConfig.wifi.apMode.countryCode "GU"}}selected="selected"{{/equal}}>GU (Guam (USA))</option>
								<option value="GW" {{#equal networkConfig.wifi.apMode.countryCode "GW"}}selected="selected"{{/equal}}>GW (Guinea-Bissau)</option>
								<option value="GY" {{#equal networkConfig.wifi.apMode.countryCode "GY"}}selected="selected"{{/equal}}>GY (Guyana)</option>
								<option value="HK" {{#equal networkConfig.wifi.apMode.countryCode "HK"}}selected="selected"{{/equal}}>HK (Hong Kong)</option>
								<option value="HM" {{#equal networkConfig.wifi.apMode.countryCode "HM"}}selected="selected"{{/equal}}>HM (Heard and McDonald Islands (Australia))</option>
								<option value="HN" {{#equal networkConfig.wifi.apMode.countryCode "HN"}}selected="selected"{{/equal}}>HN (Honduras)</option>
								<option value="HR" {{#equal networkConfig.wifi.apMode.countryCode "HR"}}selected="selected"{{/equal}}>HR (Croatia)</option>
								<option value="HT" {{#equal networkConfig.wifi.apMode.countryCode "HT"}}selected="selected"{{/equal}}>HT (Haiti)</option>
								<option value="HU" {{#equal networkConfig.wifi.apMode.countryCode "HU"}}selected="selected"{{/equal}}>HU (Hungary)</option>
								<option value="ID" {{#equal networkConfig.wifi.apMode.countryCode "ID"}}selected="selected"{{/equal}}>ID (Indonesia)</option>
								<option value="IE" {{#equal networkConfig.wifi.apMode.countryCode "IE"}}selected="selected"{{/equal}}>IE (Ireland)</option>
								<option value="IL" {{#equal networkConfig.wifi.apMode.countryCode "IL"}}selected="selected"{{/equal}}>IL (Israel)</option>
								<option value="IM" {{#equal networkConfig.wifi.apMode.countryCode "IM"}}selected="selected"{{/equal}}>IM (Isle of Man (UK))</option>
								<option value="IN" {{#equal networkConfig.wifi.apMode.countryCode "IN"}}selected="selected"{{/equal}}>IN (India)</option>
								<option value="IQ" {{#equal networkConfig.wifi.apMode.countryCode "IQ"}}selected="selected"{{/equal}}>IQ (Iraq)</option>
								<option value="IR" {{#equal networkConfig.wifi.apMode.countryCode "IR"}}selected="selected"{{/equal}}>IR (Iran)</option>
								<option value="IS" {{#equal networkConfig.wifi.apMode.countryCode "IS"}}selected="selected"{{/equal}}>IS (Iceland)</option>
								<option value="IT" {{#equal networkConfig.wifi.apMode.countryCode "IT"}}selected="selected"{{/equal}}>IT (Italy)</option>
								<option value="JE" {{#equal networkConfig.wifi.apMode.countryCode "JE"}}selected="selected"{{/equal}}>JE (Jersey (UK))</option>
								<option value="JM" {{#equal networkConfig.wifi.apMode.countryCode "JM"}}selected="selected"{{/equal}}>JM (Jamaica)</option>
								<option value="JO" {{#equal networkConfig.wifi.apMode.countryCode "JO"}}selected="selected"{{/equal}}>JO (Jordan)</option>
								<option value="JP" {{#equal networkConfig.wifi.apMode.countryCode "JP"}}selected="selected"{{/equal}}>JP (Japan- Telec)</option>
								<option value="KE" {{#equal networkConfig.wifi.apMode.countryCode "KE"}}selected="selected"{{/equal}}>KE (Kenya)</option>
								<option value="KG" {{#equal networkConfig.wifi.apMode.countryCode "KG"}}selected="selected"{{/equal}}>KG (Kyrgyzstan)</option>
								<option value="KH" {{#equal networkConfig.wifi.apMode.countryCode "KH"}}selected="selected"{{/equal}}>KH (Cambodia)</option>
								<option value="KI" {{#equal networkConfig.wifi.apMode.countryCode "KI"}}selected="selected"{{/equal}}>KI (Kiribati)</option>
								<option value="KN" {{#equal networkConfig.wifi.apMode.countryCode "KN"}}selected="selected"{{/equal}}>KN (Saint Kitts and Nevis)</option>
								<option value="KR" {{#equal networkConfig.wifi.apMode.countryCode "KR"}}selected="selected"{{/equal}}>KR (South Korea)</option>
								<option value="KW" {{#equal networkConfig.wifi.apMode.countryCode "KW"}}selected="selected"{{/equal}}>KW (Kuwait)</option>
								<option value="KY" {{#equal networkConfig.wifi.apMode.countryCode "KY"}}selected="selected"{{/equal}}>KY (Cayman Islands (UK))</option>
								<option value="KZ" {{#equal networkConfig.wifi.apMode.countryCode "KZ"}}selected="selected"{{/equal}}>KZ (Kazakhstan)</option>
								<option value="LA" {{#equal networkConfig.wifi.apMode.countryCode "LA"}}selected="selected"{{/equal}}>LA (Laos)</option>
								<option value="LB" {{#equal networkConfig.wifi.apMode.countryCode "LB"}}selected="selected"{{/equal}}>LB (Lebanon)</option>
								<option value="LC" {{#equal networkConfig.wifi.apMode.countryCode "LC"}}selected="selected"{{/equal}}>LC (Saint Lucia)</option>
								<option value="LI" {{#equal networkConfig.wifi.apMode.countryCode "LI"}}selected="selected"{{/equal}}>LI (Liechtenstein)</option>
								<option value="LK" {{#equal networkConfig.wifi.apMode.countryCode "LK"}}selected="selected"{{/equal}}>LK (Sri Lanka)</option>
								<option value="LR" {{#equal networkConfig.wifi.apMode.countryCode "LR"}}selected="selected"{{/equal}}>LR (Liberia)</option>
								<option value="LS" {{#equal networkConfig.wifi.apMode.countryCode "LS"}}selected="selected"{{/equal}}>LS (Lesotho)</option>
								<option value="LT" {{#equal networkConfig.wifi.apMode.countryCode "LT"}}selected="selected"{{/equal}}>LT (Lithuania)</option>
								<option value="LU" {{#equal networkConfig.wifi.apMode.countryCode "LU"}}selected="selected"{{/equal}}>LU (Luxembourg)</option>
								<option value="LV" {{#equal networkConfig.wifi.apMode.countryCode "LV"}}selected="selected"{{/equal}}>LV (Latvia)</option>
								<option value="LY" {{#equal networkConfig.wifi.apMode.countryCode "LY"}}selected="selected"{{/equal}}>LY (Libya)</option>
								<option value="MA" {{#equal networkConfig.wifi.apMode.countryCode "MA"}}selected="selected"{{/equal}}>MA (Morocco)</option>
								<option value="MC" {{#equal networkConfig.wifi.apMode.countryCode "MC"}}selected="selected"{{/equal}}>MC (Monaco)</option>
								<option value="MD" {{#equal networkConfig.wifi.apMode.countryCode "MD"}}selected="selected"{{/equal}}>MD (Moldova)</option>
								<option value="ME" {{#equal networkConfig.wifi.apMode.countryCode "ME"}}selected="selected"{{/equal}}>ME (Montenegro)</option>
								<option value="MF" {{#equal networkConfig.wifi.apMode.countryCode "MF"}}selected="selected"{{/equal}}>MF (Saint Martin)</option>
								<option value="MG" {{#equal networkConfig.wifi.apMode.countryCode "MG"}}selected="selected"{{/equal}}>MG (Madagascar)</option>
								<option value="MH" {{#equal networkConfig.wifi.apMode.countryCode "MH"}}selected="selected"{{/equal}}>MH (Marshall Islands (USA))</option>
								<option value="MK" {{#equal networkConfig.wifi.apMode.countryCode "MK"}}selected="selected"{{/equal}}>MK (Republic of Macedonia (FYROM))</option>
								<option value="ML" {{#equal networkConfig.wifi.apMode.countryCode "ML"}}selected="selected"{{/equal}}>ML (Mali)</option>
								<option value="MM" {{#equal networkConfig.wifi.apMode.countryCode "MM"}}selected="selected"{{/equal}}>MM (Burma (Myanmar))</option>
								<option value="MN" {{#equal networkConfig.wifi.apMode.countryCode "MN"}}selected="selected"{{/equal}}>MN (Mongolia)</option>
								<option value="MO" {{#equal networkConfig.wifi.apMode.countryCode "MO"}}selected="selected"{{/equal}}>MO (Macau)</option>
								<option value="MP" {{#equal networkConfig.wifi.apMode.countryCode "MP"}}selected="selected"{{/equal}}>MP (Northern Mariana Islands (USA))</option>
								<option value="MQ" {{#equal networkConfig.wifi.apMode.countryCode "MQ"}}selected="selected"{{/equal}}>MQ (Martinique (France))</option>
								<option value="MR" {{#equal networkConfig.wifi.apMode.countryCode "MR"}}selected="selected"{{/equal}}>MR (Mauritania)</option>
								<option value="MS" {{#equal networkConfig.wifi.apMode.countryCode "MS"}}selected="selected"{{/equal}}>MS (Montserrat (UK))</option>
								<option value="MT" {{#equal networkConfig.wifi.apMode.countryCode "MT"}}selected="selected"{{/equal}}>MT (Malta)</option>
								<option value="MU" {{#equal networkConfig.wifi.apMode.countryCode "MU"}}selected="selected"{{/equal}}>MU (Mauritius)</option>
								<option value="MV" {{#equal networkConfig.wifi.apMode.countryCode "MV"}}selected="selected"{{/equal}}>MV (Maldives)</option>
								<option value="MW" {{#equal networkConfig.wifi.apMode.countryCode "MW"}}selected="selected"{{/equal}}>MW (Malawi)</option>
								<option value="MX" {{#equal networkConfig.wifi.apMode.countryCode "MX"}}selected="selected"{{/equal}}>MX (Mexico)</option>
								<option value="MY" {{#equal networkConfig.wifi.apMode.countryCode "MY"}}selected="selected"{{/equal}}>MY (Malaysia)</option>
								<option value="MZ" {{#equal networkConfig.wifi.apMode.countryCode "MZ"}}selected="selected"{{/equal}}>MZ (Mozambique)</option>
								<option value="NA" {{#equal networkConfig.wifi.apMode.countryCode "NA"}}selected="selected"{{/equal}}>NA (Namibia)</option>
								<option value="NC" {{#equal networkConfig.wifi.apMode.countryCode "NC"}}selected="selected"{{/equal}}>NC (New Caledonia)</option>
								<option value="NE" {{#equal networkConfig.wifi.apMode.countryCode "NE"}}selected="selected"{{/equal}}>NE (Niger)</option>
								<option value="NF" {{#equal networkConfig.wifi.apMode.countryCode "NF"}}selected="selected"{{/equal}}>NF (Norfolk Island (Australia))</option>
								<option value="NG" {{#equal networkConfig.wifi.apMode.countryCode "NG"}}selected="selected"{{/equal}}>NG (Nigeria)</option>
								<option value="NI" {{#equal networkConfig.wifi.apMode.countryCode "NI"}}selected="selected"{{/equal}}>NI (Nicaragua)</option>
								<option value="NL" {{#equal networkConfig.wifi.apMode.countryCode "NL"}}selected="selected"{{/equal}}>NL (Netherlands)</option>
								<option value="NO" {{#equal networkConfig.wifi.apMode.countryCode "NO"}}selected="selected"{{/equal}}>NO (Norway)</option>
								<option value="NP" {{#equal networkConfig.wifi.apMode.countryCode "NP"}}selected="selected"{{/equal}}>NP (Nepal)</option>
								<option value="NR" {{#equal networkConfig.wifi.apMode.countryCode "NR"}}selected="selected"{{/equal}}>NR (Nauru)</option>
								<option value="NU" {{#equal networkConfig.wifi.apMode.countryCode "NU"}}selected="selected"{{/equal}}>NU (Niue)</option>
								<option value="NZ" {{#equal networkConfig.wifi.apMode.countryCode "NZ"}}selected="selected"{{/equal}}>NZ (New Zealand)</option>
								<option value="OM" {{#equal networkConfig.wifi.apMode.countryCode "OM"}}selected="selected"{{/equal}}>OM (Oman)</option>
								<option value="PA" {{#equal networkConfig.wifi.apMode.countryCode "PA"}}selected="selected"{{/equal}}>PA (Panama)</option>
								<option value="PE" {{#equal networkConfig.wifi.apMode.countryCode "PE"}}selected="selected"{{/equal}}>PE (Peru)</option>
								<option value="PF" {{#equal networkConfig.wifi.apMode.countryCode "PF"}}selected="selected"{{/equal}}>PF (French Polynesia (France))</option>
								<option value="PG" {{#equal networkConfig.wifi.apMode.countryCode "PG"}}selected="selected"{{/equal}}>PG (Papua New Guinea)</option>
								<option value="PH" {{#equal networkConfig.wifi.apMode.countryCode "PH"}}selected="selected"{{/equal}}>PH (Philippines)</option>
								<option value="PK" {{#equal networkConfig.wifi.apMode.countryCode "PK"}}selected="selected"{{/equal}}>PK (Pakistan)</option>
								<option value="PL" {{#equal networkConfig.wifi.apMode.countryCode "PL"}}selected="selected"{{/equal}}>PL (Poland)</option>
								<option value="PM" {{#equal networkConfig.wifi.apMode.countryCode "PM"}}selected="selected"{{/equal}}>PM (Saint Pierre and Miquelon (France))</option>
								<option value="PR" {{#equal networkConfig.wifi.apMode.countryCode "PR"}}selected="selected"{{/equal}}>PR (Puerto Rico)</option>
								<option value="PT" {{#equal networkConfig.wifi.apMode.countryCode "PT"}}selected="selected"{{/equal}}>PT (Portugal)</option>
								<option value="PW" {{#equal networkConfig.wifi.apMode.countryCode "PW"}}selected="selected"{{/equal}}>PW (Palau)</option>
								<option value="PY" {{#equal networkConfig.wifi.apMode.countryCode "PY"}}selected="selected"{{/equal}}>PY (Paraguay)</option>
								<option value="QA" {{#equal networkConfig.wifi.apMode.countryCode "QA"}}selected="selected"{{/equal}}>QA (Qatar)</option>
								<option value="RE" {{#equal networkConfig.wifi.apMode.countryCode "RE"}}selected="selected"{{/equal}}>RE (Reunion (France))</option>
								<option value="RO" {{#equal networkConfig.wifi.apMode.countryCode "RO"}}selected="selected"{{/equal}}>RO (Romania)</option>
								<option value="RS" {{#equal networkConfig.wifi.apMode.countryCode "RS"}}selected="selected"{{/equal}}>RS (Serbia, Kosovo)</option>
								<option value="RU" {{#equal networkConfig.wifi.apMode.countryCode "RU"}}selected="selected"{{/equal}}>RU (Russia(fac/gost), Kaliningrad)</option>
								<option value="RW" {{#equal networkConfig.wifi.apMode.countryCode "RW"}}selected="selected"{{/equal}}>RW (Rwanda)</option>
								<option value="SA" {{#equal networkConfig.wifi.apMode.countryCode "SA"}}selected="selected"{{/equal}}>SA (Saudi Arabia)</option>
								<option value="SB" {{#equal networkConfig.wifi.apMode.countryCode "SB"}}selected="selected"{{/equal}}>SB (Solomon Islands)</option>
								<option value="SC" {{#equal networkConfig.wifi.apMode.countryCode "SC"}}selected="selected"{{/equal}}>SC (Seychelles)</option>
								<option value="SE" {{#equal networkConfig.wifi.apMode.countryCode "SE"}}selected="selected"{{/equal}}>SE (Sweden)</option>
								<option value="SG" {{#equal networkConfig.wifi.apMode.countryCode "SG"}}selected="selected"{{/equal}}>SG (Singapore)</option>
								<option value="SH" {{#equal networkConfig.wifi.apMode.countryCode "SH"}}selected="selected"{{/equal}}>SH (Saint Helena (UK))</option>
								<option value="SI" {{#equal networkConfig.wifi.apMode.countryCode "SI"}}selected="selected"{{/equal}}>SI (Slovenia)</option>
								<option value="SJ" {{#equal networkConfig.wifi.apMode.countryCode "SJ"}}selected="selected"{{/equal}}>SJ (Svalbard (Norway))</option>
								<option value="SK" {{#equal networkConfig.wifi.apMode.countryCode "SK"}}selected="selected"{{/equal}}>SK (Slovakia)</option>
								<option value="SL" {{#equal networkConfig.wifi.apMode.countryCode "SL"}}selected="selected"{{/equal}}>SL (Sierra Leone)</option>
								<option value="SM" {{#equal networkConfig.wifi.apMode.countryCode "SM"}}selected="selected"{{/equal}}>SM (San Marino)</option>
								<option value="SN" {{#equal networkConfig.wifi.apMode.countryCode "SN"}}selected="selected"{{/equal}}>SN (Senegal)</option>
								<option value="SO" {{#equal networkConfig.wifi.apMode.countryCode "SO"}}selected="selected"{{/equal}}>SO (Somalia)</option>
								<option value="SR" {{#equal networkConfig.wifi.apMode.countryCode "SR"}}selected="selected"{{/equal}}>SR (Suriname)</option>
								<option value="ST" {{#equal networkConfig.wifi.apMode.countryCode "ST"}}selected="selected"{{/equal}}>ST (Sao Tome and Principe)</option>
								<option value="SV" {{#equal networkConfig.wifi.apMode.countryCode "SV"}}selected="selected"{{/equal}}>SV (El Salvador)</option>
								<option value="SX" {{#equal networkConfig.wifi.apMode.countryCode "SX"}}selected="selected"{{/equal}}>SX (Sint Marteen)</option>
								<option value="SZ" {{#equal networkConfig.wifi.apMode.countryCode "SZ"}}selected="selected"{{/equal}}>SZ (Swaziland)</option>
								<option value="TC" {{#equal networkConfig.wifi.apMode.countryCode "TC"}}selected="selected"{{/equal}}>TC (Turks and Caicos Islands (UK))</option>
								<option value="TD" {{#equal networkConfig.wifi.apMode.countryCode "TD"}}selected="selected"{{/equal}}>TD (Chad)</option>
								<option value="TF" {{#equal networkConfig.wifi.apMode.countryCode "TF"}}selected="selected"{{/equal}}>TF (French Southern and Antarctic Lands (FR Southern Territories))</option>
								<option value="TG" {{#equal networkConfig.wifi.apMode.countryCode "TG"}}selected="selected"{{/equal}}>TG (Togo)</option>
								<option value="TH" {{#equal networkConfig.wifi.apMode.countryCode "TH"}}selected="selected"{{/equal}}>TH (Thailand)</option>
								<option value="TJ" {{#equal networkConfig.wifi.apMode.countryCode "TJ"}}selected="selected"{{/equal}}>TJ (Tajikistan)</option>
								<option value="TK" {{#equal networkConfig.wifi.apMode.countryCode "TK"}}selected="selected"{{/equal}}>TK (Tokelau)</option>
								<option value="TM" {{#equal networkConfig.wifi.apMode.countryCode "TM"}}selected="selected"{{/equal}}>TM (Turkmenistan)</option>
								<option value="TN" {{#equal networkConfig.wifi.apMode.countryCode "TN"}}selected="selected"{{/equal}}>TN (Tunisia)</option>
								<option value="TO" {{#equal networkConfig.wifi.apMode.countryCode "TO"}}selected="selected"{{/equal}}>TO (Tonga)</option>
								<option value="TR" {{#equal networkConfig.wifi.apMode.countryCode "TR"}}selected="selected"{{/equal}}>TR (Turkey, Northern Cyprus)</option>
								<option value="TT" {{#equal networkConfig.wifi.apMode.countryCode "TT"}}selected="selected"{{/equal}}>TT (Trinidad & Tobago)</option>
								<option value="TW" {{#equal networkConfig.wifi.apMode.countryCode "TW"}}selected="selected"{{/equal}}>TW (Taiwan)</option>
								<option value="TZ" {{#equal networkConfig.wifi.apMode.countryCode "TZ"}}selected="selected"{{/equal}}>TZ (Tanzania)</option>
								<option value="UA" {{#equal networkConfig.wifi.apMode.countryCode "UA"}}selected="selected"{{/equal}}>UA (Ukraine)</option>
								<option value="UG" {{#equal networkConfig.wifi.apMode.countryCode "UG"}}selected="selected"{{/equal}}>UG (Uganda)</option>
								<option value="US" {{#equal networkConfig.wifi.apMode.countryCode "US"}}selected="selected"{{/equal}}>US (United States of America (USA))</option>
								<option value="UY" {{#equal networkConfig.wifi.apMode.countryCode "UY"}}selected="selected"{{/equal}}>UY (Uruguay)</option>
								<option value="UZ" {{#equal networkConfig.wifi.apMode.countryCode "UZ"}}selected="selected"{{/equal}}>UZ (Uzbekistan)</option>
								<option value="VA" {{#equal networkConfig.wifi.apMode.countryCode "VA"}}selected="selected"{{/equal}}>VA (Holy See (Vatican City))</option>
								<option value="VC" {{#equal networkConfig.wifi.apMode.countryCode "VC"}}selected="selected"{{/equal}}>VC (Saint Vincent and the Grenadines)</option>
								<option value="VE" {{#equal networkConfig.wifi.apMode.countryCode "VE"}}selected="selected"{{/equal}}>VE (Venezuela)</option>
								<option value="VI" {{#equal networkConfig.wifi.apMode.countryCode "VI"}}selected="selected"{{/equal}}>VI (United States Virgin Islands (USA))</option>
								<option value="VN" {{#equal networkConfig.wifi.apMode.countryCode "VN"}}selected="selected"{{/equal}}>VN (Vietnam)</option>
								<option value="VU" {{#equal networkConfig.wifi.apMode.countryCode "VU"}}selected="selected"{{/equal}}>VU (Vanuatu)</option>
								<option value="WF" {{#equal networkConfig.wifi.apMode.countryCode "WF"}}selected="selected"{{/equal}}>WF (Wallis and Futuna (France))</option>
								<option value="WS" {{#equal networkConfig.wifi.apMode.countryCode "WS"}}selected="selected"{{/equal}}>WS (Samoa)</option>
								<option value="YE" {{#equal networkConfig.wifi.apMode.countryCode "YE"}}selected="selected"{{/equal}}>YE (Yemen)</option>
								<option value="YT" {{#equal networkConfig.wifi.apMode.countryCode "YT"}}selected="selected"{{/equal}}>YT (Mayotte (France))</option>
								<option value="ZA" {{#equal networkConfig.wifi.apMode.countryCode "ZA"}}selected="selected"{{/equal}}>ZA (South Africa)</option>
								<option value="ZM" {{#equal networkConfig.wifi.apMode.countryCode "ZM"}}selected="selected"{{/equal}}>ZM (Zambia)</option>
								<option value="ZW" {{#equal networkConfig.wifi.apMode.countryCode "ZW"}}selected="selected"{{/equal}}>ZW (Zimbabwe)</option>
						</select></div>
					</div>
					
					<div data-role="none" class="input-text-box">
						<label data-role="none" class="single_option_text" for="networkConfig-wifi-apMode-apIpAddress" data-desc="ap-ipaddress">AP Ipaddress</label>
						<input data-role="none" type="text" id="networkConfig-wifi-apMode-apIpAddress" class="empty-limit char-normal ipaddr-limit" value="{{networkConfig.wifi.apMode.apIpAddress}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" class="single_option_text" for="networkConfig-wifi-apMode-ssid" data-desc="ap-ssid">AP SSID</label>
						<input data-role="none" type="text" id="networkConfig-wifi-apMode-ssid" class="prefix-limit empty-limit char-normal ssidlen-limit22" value="{{networkConfig.wifi.apMode.ssid}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" class="single_option_text" for="networkConfig-wifi-apMode-password" data-desc="ap-password">AP Password</label>
						<input data-role="none" type="text" id="networkConfig-wifi-apMode-password" class="empty-limit char-normal" value="{{networkConfig.wifi.apMode.password}}">
					</div>

					<div class="config_checkbox" {{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}} >
						<input data-role="none" class="checkBtn custom" type="checkbox" id="networkConfig-wifi-staMode-enable" {{#if networkConfig.wifi.staMode.enable}}checked="checked"{{/if}}/><label
								data-role="none" for="networkConfig-wifi-staMode-enable"></label>
						<p data-desc="sta-mode-enable">Enable STA Mode</p>	
					</div>
					
					<div id="sta-id" {{{showWIFISTA ipcIdentification.hardware serverConfig.enable serverConfig.netType serverConfig.bb808Config.bb808Enable serverConfig.bb808Config.bb808NetType}}}>
						<div data-role="none" class="input-text-box" {{{showHWandCustomer "DMS31V2" "111334 111500"}}}>
							<label data-role="none" class="single_option_text" for="networkConfig-wifi-staMode-staIpAddress" data-desc="sta-ipAddr">STA IP</label>
							<input data-role="none" type="text" id="networkConfig-wifi-staMode-staIpAddress" class="char-normal ipaddr-limit-without-empty" value="{{networkConfig.wifi.staMode.staIpAddress}}">
						</div>
						<div data-role="none" class="input-text-box" {{{showHWandCustomer "DMS31V2" "111334 111500"}}}>
							<label data-role="none" class="single_option_text" for="networkConfig-wifi-staMode-staGateway" data-desc="sta-gateway">STA Gateway</label>
							<input data-role="none" type="text" id="networkConfig-wifi-staMode-staGateway" class="char-normal ipaddr-limit-without-empty" value="{{networkConfig.wifi.staMode.staGateway}}">
						</div>
						<div data-role="none" class="input-text-box" {{{showHWandCustomer "DMS31V2" "111334 111500"}}}>
							<label data-role="none" class="single_option_text" for="networkConfig-wifi-staMode-staSubmask" data-desc="sta-submask">STA Subnet Mask</label>
							<input data-role="none" type="text" id="networkConfig-wifi-staMode-staSubmask" class="char-normal ipaddr-limit-without-empty" value="{{networkConfig.wifi.staMode.staSubmask}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" class="single_option_text" for="networkConfig-wifi-staMode-ssid" data-desc="sta-ssid">STA SSID</label>
							<input data-role="none" type="text" id="networkConfig-wifi-staMode-ssid" class="empty-limit char-normal ssidlen-limit" value="{{networkConfig.wifi.staMode.ssid}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" class="single_option_text" for="networkConfig-wifi-staMode-password" data-desc="sta-password">STA Password</label>
							<input data-role="none" type="text" id="networkConfig-wifi-staMode-password" class="char-normal" value="{{networkConfig.wifi.staMode.password}}">
						</div>
					</div>
				</div>

				<div class="configmenu" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR DMS31V2 DMS885N HDW845V1"}}}>
					<h1><p>{{getKeyLang "protocol"}}</p></h1>
					<div data-role="none" class="input-text-box">
						<label data-role="none" id="tab-rtspServPort" for="networkConfig-protocol-rtspServPort" data-desc="rtspServPort">RTSP端口号</label>
						<input data-role="none" type="text" id="networkConfig-protocol-rtspServPort" class="empty-limit char-normal" value="{{networkConfig.protocol.rtspServPort}}">
					</div>
					<div data-role="none" class="input-text-box"{{{hideNotCustomer "100394"}}}>
						<label data-role="none" id="tab-rtspServPubAddr" for="networkConfig-protocol-rtspServPubAddr" data-desc="rtspServPubAddr">RTSP公网地址</label>
						<input data-role="none" type="text" id="networkConfig-protocol-rtspServPubAddr" class="empty-limit char-normal" value="{{networkConfig.protocol.rtspServPubAddr}}">
					</div>
					<div data-role="none" class="input-text-box"  {{#isWFdev ipcIdentification.hardware}}style="display:none;"{{/isWFdev}}>
						<label data-role="none" id="tab-onvifServPort" for="networkConfig-protocol-onvifServPort" data-desc="onvifServPort">ONVIF端口号</label>
						<input data-role="none" type="text" id="networkConfig-protocol-onvifServPort" class="empty-limit char-normal" value="{{networkConfig.protocol.onvifServPort}}">
					</div>
					<div data-role="none" class="input-switch-box" {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR DMS31V2 DMS885N"}}}>
						<p data-desc="onvif-discoverable">ONVIF使能</p>	
						<input data-role="none" class="switchBtn custom" type="checkbox" id="networkConfig-protocol-discoverable" {{#if networkConfig.protocol.discoverable}}checked="checked"{{/if}}><label
									data-role="none" for="networkConfig-protocol-discoverable"></label>
					</div>
					<div data-role="none" class="input-switch-box" {{{supportRTPMulticast ipcIdentification.hardware ipcIdentification.customer}}}>
						<p data-desc="multicastEnable">开启RTP多播</p>	
						<input data-role="none" class="switchBtn custom" type="checkbox" id="networkConfig-protocol-multicastEnable" {{#if networkConfig.protocol.multicastEnable}}checked="checked"{{/if}}><label
									data-role="none" for="networkConfig-protocol-multicastEnable"></label>
					</div>
					<div data-role="none" class="input-text-box" {{{supportRTPMulticast ipcIdentification.hardware ipcIdentification.customer}}}>
						<label data-role="none" id="tab-multicastPort" for="networkConfig-protocol-multicastPort" data-desc="multicastPort">RTP多播端口号</label>
						<input data-role="none" type="text" id="networkConfig-protocol-multicastPort" class="empty-limit char-normal" value="{{networkConfig.protocol.multicastPort}}">
					</div>
					<div data-role="none" class="input-text-box" {{{hideNotHardware "ADA32E1"}}}>
						<label data-role="none" id="tab-avtpStreamID" for="networkConfig-protocol-avtpStreamID" data-desc="avtpStreamID">avtpStreamID</label>
						<input data-role="none" type="text" id="networkConfig-protocol-avtpStreamID" class="empty-limit char-normal" value="{{networkConfig.protocol.avtpStreamID}}">
					</div>
					<div data-role="none" class="input-text-box" {{{hideNotHardware "ADA32E1"}}}>
						<label data-role="none" id="tab-avtpDestMAC" for="networkConfig-protocol-avtpDestMAC" data-desc="avtpDestMAC">avtpDestMAC</label>
						<input data-role="none" type="text" id="networkConfig-protocol-avtpDestMAC" class="empty-limit char-normal" value="{{networkConfig.protocol.avtpDestMAC}}">
					</div>
					<div data-role="none" class="input-text-box" {{{showHardware "IPCR20S3 ADA32E1 WFCR20S2 ADA32N1"}}}>
						<label data-role="none" id="tab-udpComPort" for="networkConfig-protocol-udpComPort" data-desc="udpComPort">SOME/IP UDP端口号</label>
						<input data-role="none" type="text" id="networkConfig-protocol-udpComPort" class="empty-limit char-normal" value="{{networkConfig.protocol.udpComPort}}">
					</div>
					<div data-role="none" class="input-switch-box" {{{showHWandCustomer "IPCR20S3 ADA32N1" "201368"}}}>
						<p data-desc="noStreamAtBoot">启动后不自动推流</p>	
						<input data-role="none" class="switchBtn custom" type="checkbox" id="networkConfig-protocol-noStreamAtBoot" {{#if networkConfig.protocol.noStreamAtBoot}}checked="checked"{{/if}}><label
									data-role="none" for="networkConfig-protocol-noStreamAtBoot"></label>
					</div>
				</div>
			</div>
			<div id="sysConfig" class="tab-configs Usr-Install">
				<div class="configmenu" {{#unequal systemConfig.mcuType 1}}style="display: none;"{{/unequal}}>
					<h1><p>{{getKeyLang "pwd-crtl"}}</p></h1>
					<div class="custom-select-box">
						<label class="single_option_text" for="systemConfig-timeoutSleep" data-desc="timeout-sleep">Timeout to Sleep</label>
						<div><select class="custom-select" id="systemConfig-timeoutSleep" data-role="none" value="{{systemConfig.timeoutSleep}}">
							<option value="-1" {{#equal systemConfig.timeoutSleep -1}}selected="selected"{{/equal}}>Never</option>
							<option value="180" {{#equal systemConfig.timeoutSleep 180}}selected="selected"{{/equal}}>3min</option>
							<option value="300" {{#equal systemConfig.timeoutSleep 300}}selected="selected"{{/equal}}>5min</option>
							<option value="600" {{#equal systemConfig.timeoutSleep 600}}selected="selected"{{/equal}}>10min</option>
							<option value="1800" {{#equal systemConfig.timeoutSleep 1800}}selected="selected"{{/equal}}>30min</option>
						</select></div>
					</div>
				</div>

				<div class="item" {{{hideNotCustomer "201795"}}}>
					<div data-role="none" class="input-switch-box">
						<p data-desc="4g-enable">4g使能</p>	
						<input data-role="none" class="switchBtn custom" type="checkbox" id="systemConfig-enable4g" {{#if systemConfig.enable4g}}checked="checked"{{/if}}><label
									data-role="none" for="systemConfig-enable4g"></label>
					</div>
				</div>

				<div class="configmenu" {{{displayCustomer "201207"}}} {{#isNotSupportRecord ipcIdentification.board}}style="display: none;"{{/isNotSupportRecord}} {{{hideNotWebuiFull "ADA32V2 AICB046V1 ADA32V3 ADA32IR ADA900V1 HDW845V1"}}}>
					<h1><p>{{getKeyLang "storage-crtl"}}</p></h1>
						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="systemConfig-enableStorage" {{#if systemConfig.enableStorage}}checked="checked"{{/if}}/>
							<label data-role="none" for="systemConfig-enableStorage"></label>
							<p data-desc="enable-storage">Enable Storage</p>
						</div>
						<div class="config_checkbox" {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR ADA900V1 HDW845V1"}}}>
							<input data-role= "none" class="checkBtn custom" type="checkbox" id="systemConfig-enableSDAlarm" {{#if systemConfig.enableSDAlarm}}checked="checked"{{/if}}/>
							<label data-role="none" for="systemConfig-enableSDAlarm"></label>
							<p data-desc="enable-sdAlarm">Enable SDAlarm</p>
						</div>
						<div class="config_checkbox" {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR ADA900V1 HDW845V1"}}} {{{hideNotCustomer "202116"}}}>
							<input data-role= "none" class="checkBtn custom" type="checkbox" id="systemConfig-enableSDLedAlarm" {{#if systemConfig.enableSDLedAlarm}}checked="checked"{{/if}}/>
							<label data-role="none" for="systemConfig-enableSDLedAlarm"></label>
							<p data-desc="enable-sdLedAlarm">Enable SD LED Alarm</p>
						</div>
					<h1><p>{{getKeyLang "rec-crtl"}}</p></h1>
						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="systemConfig-loopOverwrite" {{#if systemConfig.loopOverwrite}}checked="checked"{{/if}}/>
							<label data-role="none" for="systemConfig-loopOverwrite"></label>
							<p data-desc="loop-overwrite">Loop Overwrite</p>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="systemConfig-normalRecord" data-desc="normal-record">Normal Record</label>
							<div><select class="custom-select" id="systemConfig-normalRecord" data-role="none" value="{{systemConfig.normalRecord}}">
								<option value="0" {{#equal systemConfig.normalRecord 0}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="1" {{#equal systemConfig.normalRecord 1}}selected="selected"{{/equal}}>{{getKeyLang "record-normal-con"}}</option>
								<option value="2" {{#equal systemConfig.normalRecord 2}}selected="selected"{{/equal}}>{{getKeyLang "record-normal-dis"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="systemConfig-alarmRecord" data-desc="warning-record">Warning Record</label>
							<div><select class="custom-select" id="systemConfig-alarmRecord" data-role="none" value="{{systemConfig.alarmRecord}}">
								<option value="0" {{#equal systemConfig.alarmRecord 0}}selected="selected"{{/equal}}>{{getKeyLang "off"}}</option>
								<option value="1" {{#equal systemConfig.alarmRecord 1}}selected="selected"{{/equal}}>{{getKeyLang "record-video"}}</option>
								<option value="2" {{#equal systemConfig.alarmRecord 2}}selected="selected"{{/equal}} {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR ADA900V1 HDW845V1"}}}>{{getKeyLang "record-pic"}}</option>
								<option value="3" {{#equal systemConfig.alarmRecord 3}}selected="selected"{{/equal}} {{{hideHardware "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR ADA900V1 HDW845V1"}}}>{{getKeyLang "record-video&pic"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideNotHardware "DMS31V2 DMS885N ADA32C4"}}}>
							<label class="single_option_text" for="systemConfig-preRecDuration" data-desc="pre-record-duration">Alarm Pre-recording Duration</label>
							<div><select class="custom-select" id="systemConfig-preRecDuration" data-role="none" value="{{systemConfig.preRecDuration}}">
								<option value="3" {{#equal systemConfig.preRecDuration 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="5" {{#equal systemConfig.preRecDuration 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal systemConfig.preRecDuration 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="20" {{#equal systemConfig.preRecDuration 20}}selected="selected"{{/equal}}>{{getKeyLang "20s"}}</option>
								<option value="30" {{#equal systemConfig.preRecDuration 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="40" {{#equal systemConfig.preRecDuration 40}}selected="selected"{{/equal}}>{{getKeyLang "40s"}}</option>
								<option value="50" {{#equal systemConfig.preRecDuration 50}}selected="selected"{{/equal}}>{{getKeyLang "50s"}}</option>
								<option value="60" {{#equal systemConfig.preRecDuration 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideNotHardware "DMS31V2 DMS885N ADA32C4"}}}>
							<label class="single_option_text" for="systemConfig-postRecDuration" data-desc="record-last-duration">Alarm Recording Last Duration</label>
							<div><select class="custom-select" id="systemConfig-postRecDuration" data-role="none" value="{{systemConfig.postRecDuration}}">
								<option value="3" {{#equal systemConfig.postRecDuration 3}}selected="selected"{{/equal}}>{{getKeyLang "3s"}}</option>
								<option value="5" {{#equal systemConfig.postRecDuration 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
								<option value="10" {{#equal systemConfig.postRecDuration 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
								<option value="20" {{#equal systemConfig.postRecDuration 20}}selected="selected"{{/equal}}>{{getKeyLang "20s"}}</option>
								<option value="30" {{#equal systemConfig.postRecDuration 30}}selected="selected"{{/equal}}>{{getKeyLang "30s"}}</option>
								<option value="40" {{#equal systemConfig.postRecDuration 40}}selected="selected"{{/equal}}>{{getKeyLang "40s"}}</option>
								<option value="50" {{#equal systemConfig.postRecDuration 50}}selected="selected"{{/equal}}>{{getKeyLang "50s"}}</option>
								<option value="60" {{#equal systemConfig.postRecDuration 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="systemConfig-recFileLen" data-desc="rec-filelen">Rec FileLen(min)</label>
							<div><select class="custom-select" id="systemConfig-recFileLen" data-role="none" value="{{systemConfig.recFileLen}}">
								<option value="5" {{#equal systemConfig.recFileLen 5}}selected="selected"{{/equal}}>{{getKeyLang "5min"}}</option>
								<option value="10" {{#equal systemConfig.recFileLen 10}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="15" {{#equal systemConfig.recFileLen 15}}selected="selected"{{/equal}}>{{getKeyLang "15min"}}</option>
							</select></div>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="systemConfig-recFileType" data-desc="rec-filetype">Rec FileType</label>
							<div><select class="custom-select" id="systemConfig-recFileType" data-role="none" value="{{systemConfig.recFileType}}">
								<option value="AVI" {{#equal systemConfig.recFileType "AVI"}}selected="selected"{{/equal}}>AVI</option>
								<option value="MP4" {{#equal systemConfig.recFileType "MP4"}}selected="selected"{{/equal}}>MP4</option>
							</select></div>
						</div>
						<div class="custom-select-box" {{{hideNotBoard "25"}}}>
							<label class="single_option_text" for="systemConfig-recChn" data-desc="rec-Chn">Recorder Channel</label>
							<div><select class="custom-select" id="systemConfig-recChn" data-role="none" value="{{systemConfig.recChn}}">
								<option value="0" {{#equal systemConfig.recChn 0}}selected="selected"{{/equal}}>CHN0</option>
								<option value="1" {{#equal systemConfig.recChn 1}}selected="selected"{{/equal}}>CHN1</option>
								<option value="5" {{#equal systemConfig.recChn 5}}selected="selected"{{/equal}}>ALL</option>
							</select></div>
						</div>
				</div>
				<div class="custom-select-box" {{{hideNotHardware "ADA32C4 DMS31V2"}}}>
					<label class="single_option_text" for="systemConfig-accDelayTime" data-desc="acc-delay-time">acc-delay-time</label>
					<div><select class="custom-select" id="systemConfig-accDelayTime" data-role="none" value="{{systemConfig.accDelayTime}}">
						<option value="5" {{#equal systemConfig.accDelayTime 5}}selected="selected"{{/equal}}>{{getKeyLang "5s"}}</option>
						<option value="10" {{#equal systemConfig.accDelayTime 10}}selected="selected"{{/equal}}>{{getKeyLang "10s"}}</option>
						<option value="60" {{#equal systemConfig.accDelayTime 60}}selected="selected"{{/equal}}>{{getKeyLang "60s"}}</option>						
						<option value="600" {{#equal systemConfig.time.interval 600}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
						<option value="1800" {{#equal systemConfig.accDelayTimel 1800}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
						<option value="3600" {{#equal systemConfig.accDelayTime 3600}}selected="selected"{{/equal}}>{{getKeyLang "1h"}}</option>
						<option value="18000" {{#equal systemConfig.accDelayTime 18000}}selected="selected"{{/equal}}>{{getKeyLang "5h"}}</option>
						<option value="86400" {{#equal systemConfig.accDelayTime 86400}}selected="selected"{{/equal}}>{{getKeyLang "24h"}}</option>
					</select></div>
				</div>
				<div class="configmenu" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32V3 ADA32IR HDW845V1"}}}>
					<h1><p>{{getKeyLang "time-conf"}}</p></h1>
					<div class="config_checkbox" {{{showHardware "DMS31V2 ADA32C4"}}}>
						<input data-role="none" class="checkBtn custom" type="checkbox" id="systemConfig-time-enableGPStime" {{#if systemConfig.time.enableGPStime}}checked="checked"{{/if}}/>
						<label data-role="none" for="systemConfig-time-enableGPStime"></label>
						<p data-desc="gpstime-enable">GPS时间</p>	
					</div>
					<div data-role="none" class="rangeinput">
						<p class="rangeinput_title" data-desc="utc-hour">UTC 时<p></p>
						<label data-role="none" for="systemConfig-time-UTChour"></label>
						<input data-role="none"  class="rangeinput_input" type="range" id="systemConfig-time-UTChour" min="-12" max="14" data-highlight="true" value="{{systemConfig.time.UTChour}}">
						<p class="rangeinput_value">{{systemConfig.time.UTChour}}<p>
					</div>	
					<div data-role="none" class="rangeinput">
						<p class="rangeinput_title" data-desc="utc-minute">UTC 分<p></p>
						<label data-role="none" for="systemConfig-time-UTCminute"></label>
						<input data-role="none"  class="rangeinput_input" type="range" id="systemConfig-time-UTCminute" min="0" max="45" step="15" data-highlight="true" value="{{systemConfig.time.UTCminute}}">
						<p class="rangeinput_value">{{systemConfig.time.UTCminute}}<p>
					</div>

					<br>
					<div class="configmenu" {{{hideNotWebuiFull "ADA32V4 ADA32V2 AICB046V1 ADA32C4 ADA32V3 ADA32IR"}}}>
						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="systemConfig-time-ntpEnable" {{#if systemConfig.time.ntpEnable}}checked="checked"{{/if}}/><label
								data-role="none" for="systemConfig-time-ntpEnable"></label>
							<p data-desc="ntp-enable">NTP Time Sync</p>
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="systemConfig-time-serverAddress" data-desc="ntp-server">NTP服务器</label>
							<input data-role="none" type="text" id="systemConfig-time-serverAddress" class="empty-limit char-normal" value="{{systemConfig.time.serverAddress}}" {{#unless systemConfig.time.ntpEnable}}readonly="true" style="color:#666;"{{/unless}}>
						</div>
						<div class="custom-select-box">
							<label class="single_option_text" for="systemConfig-time-interval" data-desc="ntp-interval-min">NTP同步间隔</label>
							<div><select class="custom-select" id="systemConfig-time-interval" data-role="none" value="{{systemConfig.time.interval}}">
								<option value="10" {{#equal systemConfig.time.interval 10}}selected="selected"{{/equal}}>{{getKeyLang "10min"}}</option>
								<option value="15" {{#equal systemConfig.time.interval 15}}selected="selected"{{/equal}}>{{getKeyLang "15min"}}</option>
								<option value="30" {{#equal systemConfig.time.interval 30}}selected="selected"{{/equal}}>{{getKeyLang "30min"}}</option>
								<option value="60" {{#equal systemConfig.time.interval 60}}selected="selected"{{/equal}}>{{getKeyLang "1h"}}</option>
								<option value="120" {{#equal systemConfig.time.interval 120}}selected="selected"{{/equal}}>{{getKeyLang "2h"}}</option>
								<option value="300" {{#equal systemConfig.time.interval 300}}selected="selected"{{/equal}}>{{getKeyLang "5h"}}</option>
								<option value="1440" {{#equal systemConfig.time.interval 1440}}selected="selected"{{/equal}}>{{getKeyLang "24h"}}</option>
							</select></div>
						</div>
						<div class="config_checkbox" {{#isNotIPC ipcIdentification.hardware}}style="display:none"{{/isNotIPC}}>
							<input data-role="none" class="checkBtn custom" type="checkbox" id="systemConfig-time-daylightTime" {{#if systemConfig.time.daylightTime}}checked="checked"{{/if}}/><label
								data-role="none" for="systemConfig-time-daylightTime"></label>
							<p data-desc="daylight-time">Daylight Saving Time</p>
						</div>
					</div>

				</div>
				<div class="configmenu" {{{showHardware "ADA32V4 ADA32V2 ADA900V1 AICB046V1"}}}>
					<h1><p>RS232</p></h1>
					<div data-role="none" class="input-switch-box">
						<p data-desc="RS232Enable">RS232Enable</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="systemConfig-bEnableRS232" {{#if systemConfig.bEnableRS232}}checked="checked"{{/if}}>
						<label data-role="none" for="systemConfig-bEnableRS232"></label>
					</div>
					<div class="custom-select-box">
						<label class="single_option_text" for="systemConfig-RS232baudrate" data-desc="RS232Baudrate">RS232Baudrate</label>
						<div><select class="custom-select" id="systemConfig-RS232baudrate" data-role="none" value="{{systemConfig.RS232baudrate}}">
							<option value="4800" {{#equal systemConfig.RS232baudrate 4800}}selected="selected"{{/equal}}>4800</option>
							<option value="9600" {{#equal systemConfig.RS232baudrate 9600}}selected="selected"{{/equal}}>9600</option>
							<option value="14400" {{#equal systemConfig.RS232baudrate 14400}}selected="selected"{{/equal}}>14400</option>
							<option value="19200" {{#equal systemConfig.RS232baudrate 19200}}selected="selected"{{/equal}}>19200</option>
							<option value="38400" {{#equal systemConfig.RS232baudrate 38400}}selected="selected"{{/equal}}>38400</option>
							<option value="43000" {{#equal systemConfig.RS232baudrate 43000}}selected="selected"{{/equal}}>43000</option>
							<option value="57600" {{#equal systemConfig.RS232baudrate 57600}}selected="selected"{{/equal}}>57600</option>
							<option value="76800" {{#equal systemConfig.RS232baudrate 76800}}selected="selected"{{/equal}}>76800</option>
							<option value="115200" {{#equal systemConfig.RS232baudrate 115200}}selected="selected"{{/equal}}>115200</option>
						</select></div>
					</div>			
				</div>
				<div class="configmenu" {{#isNotSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isNotSupportAlg}} {{{hideNotWebuiFull "ADA32IR"}}} {{{hideCAN}}}>
					<h1><p>CAN</p></h1>
					<div data-role="none" class="input-text-box" {{#isNotSupportDMS ipcIdentification.hardware}}style="display:none;"{{/isNotSupportDMS}}>
						<label data-role="none" id="tab-dmmCanid" for="systemConfig-dmmCanid" data-desc="DmmCanID">Dmm CAN ID</label>
						<input data-role="none" type="text" id="systemConfig-dmmCanid" {{#isSupportAlg ipcIdentification.hardware}}class="empty-limit char-normal"{{/isSupportAlg}} value="{{systemConfig.dmmCanid}}">
					</div>
					<div data-role="none" class="input-text-box" {{#isNotSupportDMS ipcIdentification.hardware}}style="display:none;"{{/isNotSupportDMS}}>
						<label data-role="none" id="tab-frsCanid" for="systemConfig-frsCanid" data-desc="FrsCanID">Frs CAN ID</label>
						<input data-role="none" type="text" id="systemConfig-frsCanid" {{#isSupportAlg ipcIdentification.hardware}}class="empty-limit char-normal"{{/isSupportAlg}} value="{{systemConfig.frsCanid}}">
					</div>
					<div data-role="none" class="input-text-box" {{#isNotSupportDMS ipcIdentification.hardware}}style="display:none;"{{/isNotSupportDMS}}>
						<label data-role="none" id="tab-heartCanid" for="systemConfig-heartCanid" data-desc="HeartCanID">Heart CAN ID</label>
						<input data-role="none" type="text" id="systemConfig-heartCanid" {{#isSupportAlg ipcIdentification.hardware}}class="empty-limit char-normal"{{/isSupportAlg}} value="{{systemConfig.heartCanid}}">
					</div>
					<div data-role="none" class="input-text-box" {{#isNotSupportDMS ipcIdentification.hardware}}style="display:none;"{{/isNotSupportDMS}}>
						<label data-role="none" id="tab-dmsExtCanid" for="systemConfig-dmsExtCanid" data-desc="ExtCanID">Ext CAN ID</label>
						<input data-role="none" type="text" id="systemConfig-dmsExtCanid" {{#isSupportAlg ipcIdentification.hardware}}class="empty-limit char-normal"{{/isSupportAlg}} value="{{systemConfig.dmsExtCanid}}">
					</div>
					<div data-role="none" class="input-text-box Usr-Install" {{#isNotSupportPD ipcIdentification.hardware}}style="display:none;"{{/isNotSupportPD}}>
						<label data-role="none" id="tab-pdsCanid" for="systemConfig-pdsCanid" data-desc="PdsCanID">Pds CAN ID</label>
						<input data-role="none" type="text" id="systemConfig-pdsCanid" {{#isSupportAlg ipcIdentification.hardware}}class="empty-limit char-normal"{{/isSupportAlg}} value="{{systemConfig.pdsCanid}}">
					</div>
					<div data-role="none" class="input-text-box Usr-Install" {{#isNotSupportPD ipcIdentification.hardware}}style="display:none;"{{/isNotSupportPD}}>
						<label data-role="none" id="tab-pdsExtCanid" for="systemConfig-pdsExtCanid" data-desc="ExtCanID">Ext CAN ID</label>
						<input data-role="none" type="text" id="systemConfig-pdsExtCanid" {{#isSupportAlg ipcIdentification.hardware}}class="empty-limit char-normal"{{/isSupportAlg}} value="{{systemConfig.pdsExtCanid}}">
					</div>
					<div data-role="none" class="input-text-box" {{#isNotSupportAPC ipcIdentification.hardware}}style="display:none;"{{/isNotSupportAPC}}>
						<label data-role="none" id="tab-apcCanid" for="systemConfig-apcCanid" data-desc="ApcCanID">APC CAN ID</label>
						<input data-role="none" type="text" id="systemConfig-apcCanid" {{#isSupportAlg ipcIdentification.hardware}}class="empty-limit char-normal"{{/isSupportAlg}} value="{{systemConfig.apcCanid}}">
					</div>
					<div class="custom-select-box Usr-Install">
						<label class="single_option_text" for="systemConfig-frameFormat" data-desc="can-frame-format">Frame Format</label>
						<div><select class="custom-select" id="systemConfig-frameFormat" data-role="none" value="{{systemConfig.frameFormat}}">
							<option value="1" {{#equal systemConfig.frameFormat 1}}selected="selected"{{/equal}} >{{getKeyLang "can-frame-standard"}}</option>
							<option value="0" {{#equal systemConfig.frameFormat 0}}selected="selected"{{/equal}} >{{getKeyLang "can-frame-extended"}}</option>
						</select></div>
					</div>
					<div class="custom-select-box Usr-Install">
						<label class="single_option_text" for="systemConfig-baudrate" data-desc="baudrate">Baudrate</label>
						<div><select class="custom-select" id="systemConfig-baudrate" data-role="none" value="{{systemConfig.baudrate}}">
							<option value="100" {{#equal systemConfig.baudrate 100}}selected="selected"{{/equal}}>100k</option>
							<option value="125" {{#equal systemConfig.baudrate 125}}selected="selected"{{/equal}}>125k</option>
							<option value="250" {{#equal systemConfig.baudrate 250}}selected="selected"{{/equal}}>250k</option>
							<option value="500" {{#equal systemConfig.baudrate 500}}selected="selected"{{/equal}}>500k</option>
							<option value="800" {{#equal systemConfig.baudrate 800}}selected="selected"{{/equal}}>800k</option>
							<option value="1000" {{{hideHardware "DMS31V2 DMS885N"}}} {{#equal systemConfig.baudrate 1000}}selected="selected"{{/equal}}>1000k</option>
						</select></div>
					</div>

				</div>
			</div>

			<div style="height: 200px;"></div>
		
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            </script>
        </div>
        <div>
            <button id="ScrollToTop" style="background-image: url('../../css/images/back-to-top.png');position: fixed; font-weight: normal;width:40px;height:40px; bottom: 50px; right: 8px; border: none;padding:8.75px 12.5px; z-index: 100; border-radius: 5px; cursor: pointer; display: none;border-radius: 50%;opacity: 0.7;"></button>
        </div>
        <script src="./js/webapp-language.js"></script>
        <script src="./js/handlebars.js"></script>
        <script src="./js/webapp-common.js"></script>
        <script src="./js/webapp-model.js"></script>
        <script src="./js/webapp-view.js"></script>
        <script src="./js/webapp-ctrller.js"></script>
        <script type="text/javascript">
            $(document).ready(function() {
                var customer;
                var itemCustomer = window.location.host + "-customer";
                window.localStorage && (customer = window.localStorage.getItem(itemCustomer));
                console.log(customer);
                var hardware;
                var itemHardware = window.location.host + "-hardware";
                window.localStorage && (hardware = window.localStorage.getItem(itemHardware));
                console.log(hardware);
                if (customer == "200032" && hardware.indexOf("HDW845") == -1) {
                    if ((navigator.userAgent.match(/(phone|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                        $("<link>").attr({
                            rel: "stylesheet",
                            type: "text/css",
                            href: "./css/luis-mobilecommon.css"
                        }).appendTo("head");
                    } else {
                        $("<link>").attr({
                            rel: "stylesheet",
                            type: "text/css",
                            href: "./css/luis-common.css"
                        }).appendTo("head");
                    }
                } else {
                    if ((navigator.userAgent.match(/(phone|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                        $("<link>").attr({
                            rel: "stylesheet",
                            type: "text/css",
                            href: "./css/mobilecommon.css"
                        }).appendTo("head");
                    } else {
                        $("<link>").attr({
                            rel: "stylesheet",
                            type: "text/css",
                            href: "./css/common.css"
                        }).appendTo("head");
                    }
                }
                window.onload = function() {
                    var scrollToTopButton = document.querySelector('#ScrollToTop');
                    window.addEventListener('scroll', () => {
                        if (window.scrollY > 200) {
                            scrollToTopButton.style.display = 'block';
                        } else {
                            scrollToTopButton.style.display = 'none';
                        }
                    }
                    );
                }
                ;
                switchCssStyle(hardware, customer);
                var model = new WebappModel();
				
				if (customer == "200055" || customer == "200055A") 
				{
				model.lang = vt_lang;
				}
                var view = new WebappView(model,"config");
                var controller = new WebappController(model,view);
                controller.refreshConfigs();
            });
        </script>
    </body>
</html>
