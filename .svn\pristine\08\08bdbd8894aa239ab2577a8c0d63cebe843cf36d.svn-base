﻿//
// Copyright (c) 2019-2022 yanggaofeng
//
#include <yangipc/YangIpcPublish.h>



void yang_ipcPub_startAudioDecoding(YangIpcPublishSession* session){
    session->decoder.start(&session->decoder.session);
}
void yang_ipcPub_startVideoEncoding(YangIpcPublishSession* session){
	session->encoder.start(&session->encoder.session);
}
void yang_ipcPub_stopAudioDecoding(YangIpcPublishSession* session){
    session->decoder.stop(&session->decoder.session);
}
void yang_ipcPub_stopVideoEncoding(YangIpcPublishSession* session){
	session->encoder.stop(&session->encoder.session);
}
void yang_ipcPub_startRtc(YangIpcPublishSession* session){
	session->rtc.start(&session->rtc.session);
}
void yang_ipcPub_stopRtc(YangIpcPublishSession* session){
	session->rtc.stop(&session->rtc.session);
}


int32_t yang_ipcPub_addPeer(YangIpcPublishSession* session,char* sdp,char* answer,char* remoteIp,int32_t localPort,int* phasplay){
	return session->rtc.addPeer(&session->rtc.session,sdp,answer,remoteIp,localPort,phasplay);
}
void yang_ipcpub_sendRequest(YangIpcPublishSession* session,int32_t puid,uint32_t ssrc,YangRequestType req){
	session->encoder.sendMsgToEncoder(&session->encoder.session,req);
}


void yang_ipcPub_init(YangIpcPublishSession* session){
    if(session){
		session->encoder.init(&session->encoder.session);
        //if(session->hasAudio)
#if (defined(BOARD_IPCR20S3) && defined(BOARD_IPCR20S4))
        session->decoder.init(&session->decoder.session);
#endif

    }
}


void yang_create_ipcPublish(YangIpcPublish* publish,YangAVInfo* avinfo){
	YangIpcPublishSession* session=&publish->session;

	yang_create_videoEncoder(&session->encoder);
	
#if (!defined(BOARD_IPCR20S3) && !defined(BOARD_IPCR20S4))
	yang_create_audioDecoder(&session->decoder);
	
	publish->startAudioDecoding=yang_ipcPub_startAudioDecoding;
	publish->stopAudioDecoding=yang_ipcPub_stopAudioDecoding;
#endif

	yang_create_p2prtc(&session->rtc,avinfo);

	publish->startVideoEncoding=yang_ipcPub_startVideoEncoding;
	publish->stopVideoEncoding=yang_ipcPub_stopVideoEncoding;

	publish->startRtc=yang_ipcPub_startRtc;
	publish->stopRtc=yang_ipcPub_stopRtc;

	publish->addPeer=yang_ipcPub_addPeer;
	publish->sendRequest=yang_ipcpub_sendRequest;
	
	publish->init=yang_ipcPub_init;
}

void yang_destroy_ipcPublish(YangIpcPublish* publish){
	YangIpcPublishSession* session=&publish->session;
	yang_destroy_videoEncoder(&session->encoder);
    yang_destroy_audioDecoder(&session->decoder);
	yang_destroy_p2prtc(&session->rtc);
}



