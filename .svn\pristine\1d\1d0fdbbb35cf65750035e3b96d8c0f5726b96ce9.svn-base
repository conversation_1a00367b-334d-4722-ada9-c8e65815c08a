root@ZhuoTK:/# dmesg
[   15.840000] qmi_wwan_q 1-1.3:1.4: cdc-wdm0: USB WDM device
[   15.860000] qmi_wwan_q 1-1.3:1.4: Quectel Android work on RawIP mode
[   15.860000] qmi_wwan_q 1-1.3:1.4: rx_urb_size = 1520
[   15.870000] qmi_wwan_q 1-1.3:1.4 wwan0: register 'qmi_wwan_q' at usb-101c0000.ehci-1.3, WWAN/QMI device, da:0b:ce:b2:db:21

root@ZhuoTK:/# quectel-CM -s cment &
[04-13_03:20:20:456] Quectel_QConnectManager_Linux_V1.6.0.25
[04-13_03:20:20:459] Find /sys/bus/usb/devices/1-1.3 idVendor=0x2c7c idProduct=0x125, bus=0x001, dev=0x003
[04-13_03:20:20:460] Auto find qmichannel = /dev/cdc-wdm0
[04-13_03:20:20:460] Auto find usbnet_adapter = wwan0
[04-13_03:20:20:461] netcard driver = qmi_wwan_q, driver version = V1.2.0.23
[04-13_03:20:20:461] Modem works in QMI mode
[04-13_03:20:20:469] cdc_wdm_fd = 7
[04-13_03:20:20:547] Get clientWDS = 4
[04-13_03:20:20:579] Get clientDMS = 1
[04-13_03:20:20:611] Get clientNAS = 4
[04-13_03:20:20:643] Get clientUIM = 1
[04-13_03:20:20:675] Get clientWDA = 1
[04-13_03:20:20:707] requestBaseBandVersion EC25EFAR06A11M4G
[04-13_03:20:20:836] requestGetSIMStatus SIMStatus: SIM_READY
[04-13_03:20:20:836] requestSetProfile[1] cment///0
[04-13_03:20:20:899] requestGetProfile[1] cment///0
[04-13_03:20:20:931] requestRegistrationState2 MCC: 460, MNC: 0, PS: Attached, DataCap: LTE
[04-13_03:20:20:963] requestQueryDataCall IPv4ConnectionStatus: DISCONNECTED
[04-13_03:20:20:963] ifconfig wwan0 0.0.0.0
[04-13_03:20:20:976] ifconfig wwan0 down
[04-13_03:20:21:186] requestSetupDataCall WdsConnectionIPv4Handle: 0x8723e780
[04-13_03:20:21:316] ifconfig wwan0 up
[04-13_03:20:21:329] you are use OpenWrt?
[04-13_03:20:21:330] should not calling udhcpc manually?
[04-13_03:20:21:330] should modify /etc/config/network as below?
[04-13_03:20:21:330] config interface wan
[04-13_03:20:21:330] 	option ifname	wwan0
[04-13_03:20:21:330] 	option proto	dhcp
[04-13_03:20:21:330] should use "/sbin/ifstaus wan" to check wwan0 's status?
[04-13_03:20:21:331] busybox udhcpc -f -n -q -t 5 -i wwan0
[04-13_03:20:21:341] udhcpc (v1.23.2) started
[04-13_03:20:21:353] Sending discover...
[04-13_03:20:21:362] Sending select for ***********...
[04-13_03:20:21:365] Lease of *********** obtained, lease time 7200
[04-13_03:20:21:370] udhcpc: ifconfig wwan0 *********** netmask *************** broadcast +
[04-13_03:20:21:380] udhcpc: setting default routers: ***********

root@ZhuoTK:/# ifconfig wwan0
wwan0     Link encap:Ethernet  HWaddr 00:CA:01:91:97:BA  
          inet addr:***********  Mask:***************
          inet6 addr: fe80::2ca:1ff:fe91:97ba/64 Scope:Link
          UP RUNNING NOARP  MTU:1500  Metric:1
          RX packets:38 errors:0 dropped:0 overruns:0 frame:0
          TX packets:46 errors:0 dropped:0 overruns:0 carrier:0
          collisions:0 txqueuelen:1000 
          RX bytes:5244 (5.1 KiB)  TX bytes:6964 (6.8 KiB)

root@ZhuoTK:/# ip ro show
default via *********** dev wwan0 
***********/30 dev wwan0  proto kernel  scope link  src *********** 
***********/24 dev br-lan  proto kernel  scope link  src ************* 

root@ZhuoTK:/# killall quectel-CM
[04-13_03:20:46:130] requestDeactivateDefaultPDP WdsConnectionIPv4Handle
[04-13_03:20:46:406] ifconfig wwan0 0.0.0.0
[04-13_03:20:46:418] ifconfig wwan0 down
[04-13_03:20:46:600] QmiWwanThread exit
[04-13_03:20:46:600] qmi_main exit
