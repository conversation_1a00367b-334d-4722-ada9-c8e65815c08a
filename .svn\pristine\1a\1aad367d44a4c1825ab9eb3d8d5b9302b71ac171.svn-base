/******************************************************************************
Copyright (C) 2022-2024 广州敏视数码科技有限公司版权所有.

文件名：rs485.c

作者: szp       版本: v1.0.0(初始版本号)   日期: 2022-12-15

文件功能描述: 定义485通讯接口

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <errno.h>
#include <time.h>
#include <fcntl.h>
#include <termios.h>

#include "print.h"
#include "../../../include/board.h"
#include "safefunc.h"
#include "op.h"
#include "msg.h"
#include "utils.h"
#include "rs485.h"
#include "cJSON.h"
#include "gps.h"

#define RS485_PORT              "/dev/ttyS3"              		/* RS485串口设备 */
#define RS485_FRAME_MAX_LEN     1024                           /* RS485帧最大长度 */
#define FIREDIR     "/root/mcu/ac601"
#define FIRENAMELEN 60
#define AC601_BOARD_NAME "ARTERYCBB1-"
/* RS485模块控制信息 */
typedef struct
{
	sint32						s32SerialFd;		/* 串口设备描述符 */
    sint32                      s32RS485Baudrate;   /* 串口波特率 */
    SV_BOOL                     bR151Regulation;    /* 是否为R151法规版本 */
    SV_BOOL                    	bRunning;           /* 线程运行状态 */
    uint8_t                     u8DeviceId;         /* 设备ID */
    uint8_t                     u8DeviceSep;        /* 同种设备编码 */
    pthread_t                   RecvTid;            /* 接收线程ID */
    PD_ROI_DATA_S               stPdRoiData;        /* 算法发过来的roi区域人数 */
    pthread_mutex_t				mutexSendData;      /* 发送数据互斥锁 */
    pthread_mutex_t				mutexAlgData;       /* 算法数据互斥锁 */
}
STRS485Info;

STRS485Info m_stRS485Info = {0};

// 打印年月日时分秒毫秒
static void print_time()
{
    struct timeval tv;
	int BEIJINGTIME = 8;
	int DAY = (60*60*24);
	int YEARFIRST = 2001;
	int YEARSTART = (365*(YEARFIRST-1970) + 8);
	int YEAR400 = (365*4*100 + (4*(100/4 - 1) + 1));
	int YEAR100 = (365*100 + (100/4 - 1));
	int YEAR004 = (365*4 + 1);
	int YEAR001 = 365;

    long sec = 0, usec = 0;
    int yy = 0, mm = 0, dd = 0, hh = 0, mi = 0, ss = 0, ms = 0;
    int ad = 0;
    int y400 = 0, y100 = 0, y004 = 0, y001 = 0;
    int m[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    int i;
    memset(&tv, 0, sizeof(struct timeval));
    gettimeofday(&tv, NULL);
    sec = tv.tv_sec;
    usec = tv.tv_usec;
    sec = sec + (60*60)*BEIJINGTIME;
    ad = sec/DAY;
    ad = ad - YEARSTART;
    y400 = ad/YEAR400;
    y100 = (ad - y400*YEAR400)/YEAR100;
    y004 = (ad - y400*YEAR400 - y100*YEAR100)/YEAR004;
    y001 = (ad - y400*YEAR400 - y100*YEAR100 - y004*YEAR004)/YEAR001;
    yy = y400*4*100 + y100*100 + y004*4 + y001*1 + YEARFIRST;
    dd = (ad - y400*YEAR400 - y100*YEAR100 - y004*YEAR004)%YEAR001;
    //月 日
    if(0 == yy%1000)
    {
        if(0 == (yy/1000)%4)
        {
            m[1] = 29;
        }
    }
    else
    {
        if(0 == yy%4)
        {
            m[1] = 29;
        }
    }
    for(i = 1; i <= 12; i++)
    {
        if(dd - m[i] < 0)
        {
            break;
        }
        else
        {
            dd = dd -m[i];
        }
    }
    mm = i;
    //小时
    hh = sec/(60*60)%24;
    //分
    mi = sec/60 - sec/(60*60)*60;
    //秒
    ss = sec - sec/60*60;
    ms = usec;
    printf("%d-%02d-%02d %02d:%02d:%02d.%06d ==> ", yy, mm, dd, hh, mi, ss, ms);
}

sint32 callbackRS485Send(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0;
    MSG_RS485_DATA_S *pstMsgRS485Data = (MSG_RS485_DATA_S *)pstMsgPkt->pu8Data;

    s32Ret = rs485_SendData(pstMsgRS485Data->szRS485Data, pstMsgRS485Data->s32DataLen);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "rs485_SendData failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

static sint32 callbackRs485PersonNum(MSG_PACKET_S * pstMsgPkt, MSG_PACKET_S * pstRetPkt)
{
    pthread_mutex_lock(&m_stRS485Info.mutexAlgData);
    PD_RESULT_S *pstPdResults = (PD_RESULT_S *)pstMsgPkt->pu8Data;
    m_stRS485Info.stPdRoiData.stPdResultNum.s64TimeStamp = pstPdResults->s64TimeStamp;
    m_stRS485Info.stPdRoiData.stPdResultNum.s32RedRoiNum = pstPdResults->s32RedRoiNum;
    m_stRS485Info.stPdRoiData.stPdResultNum.s32YellowRoiNum = pstPdResults->s32YellowRoiNum;
    m_stRS485Info.stPdRoiData.stPdResultNum.s32GreenRoiNum = pstPdResults->s32GreenRoiNum;
    m_stRS485Info.stPdRoiData.stPdResultNum.bGreenAlarmOut = pstPdResults->bGreenAlarmOut;
    m_stRS485Info.stPdRoiData.stPdResultNum.bYellowAlarmOut = pstPdResults->bYellowAlarmOut;
    m_stRS485Info.stPdRoiData.stPdResultNum.bRedAlarmOut = pstPdResults->bRedAlarmOut;
    m_stRS485Info.stPdRoiData.stPdResultNum.s32RedDelayTime = pstPdResults->s32RedDelayTime;
    m_stRS485Info.stPdRoiData.stPdResultNum.s32YellowDelayTime = pstPdResults->s32YellowDelayTime;
    m_stRS485Info.stPdRoiData.stPdResultNum.s32GreenDelayTime = pstPdResults->s32GreenDelayTime;
    m_stRS485Info.stPdRoiData.stPdResultNum.s32GpsSpeed = pstPdResults->s32GpsSpeed;    
    m_stRS485Info.stPdRoiData.stPdResultNum.bNightMode = pstPdResults->bNightMode;
    m_stRS485Info.stPdRoiData.bValid = SV_TRUE;
    pthread_mutex_unlock(&m_stRS485Info.mutexAlgData);
    print_level(SV_INFO, "+++get time: %lld, num: %d %d %d speed :%d\n", pstPdResults->s64TimeStamp, pstPdResults->s32RedRoiNum, pstPdResults->s32YellowRoiNum, pstPdResults->s32GreenRoiNum,pstPdResults->s32GpsSpeed);

    return SV_SUCCESS;
}

sint32 rs485_CtrlPinInit(uint8 u8Band, uint8 u8PinNum)
{
    sint32 s32Ret = 0;
    uint32_t u32GpioNum = 0;
    char szCmd[256];

    if(u8PinNum > 32)
    {
        return SV_FAILURE;
    }

    u32GpioNum = u8Band*32+u8PinNum;

    sprintf(szCmd, "echo %d > /sys/class/gpio/export", u32GpioNum);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    sprintf(szCmd, "echo out > /sys/class/gpio/gpio%d/direction", u32GpioNum);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 rs485_CtrlPinSetValue(uint32_t u32GpioNum, uint8_t u8Status)
{
	if (u8Status > 1)
	{
		print_level(SV_ERROR, "s32Edge is out of range!\n");
		return SV_FAILURE;
	}

	static char s_szBufStat[] = {'0', '1'};
	sint32 s32Fd = -1, s32Ret;
	char path[64] = {0};

	snprintf(path, 64, "/sys/class/gpio/gpio%d/value", u32GpioNum);
	s32Fd  = open(path, O_RDWR|O_CLOEXEC);
	if(s32Fd < 0)
	{
		print_level(SV_ERROR, "open[%s] fail error[%d]\n", path, errno);
		return SV_FAILURE;
	}

	s32Ret = write(s32Fd, &s_szBufStat[u8Status], sizeof(s_szBufStat[u8Status]));
	if(s32Ret < 0)
	{
		print_level(SV_ERROR, "write [%s] fail error[%d]\n", s_szBufStat[u8Status], errno);
		close(s32Fd);
		return SV_FAILURE;
	}

	close(s32Fd);
	return SV_SUCCESS;
}

sint32 rs485_ReadData(char *pszData, uint32 *pu32Len)
{
	sint32 s32Ret = 0;
	uint32 u32Count = 0;
    uint32 u32TimCnt = 0;
    uint32_t u32TimCntLimit = 0;

	if (pszData == NULL)
	{
		print_level(SV_ERROR, "null pointer!\n");
		return SV_FAILURE;
	}

    switch (m_stRS485Info.s32RS485Baudrate)
    {
        case 1200:
            u32TimCntLimit = 30;
            break;
        case 2400:
            u32TimCntLimit = 20;
            break;
        case 4800:
            u32TimCntLimit = 12;
            break;
        case 9600:
            u32TimCntLimit = 6;
            break;
        case 19200:
        case 38400:
        case 57600:
        case 115200:
            u32TimCntLimit = 3;
            break;
        default:
            u32TimCntLimit = 3;
            break;
    }

    while (u32Count < RS485_FRAME_MAX_LEN)
    {
        s32Ret = read(m_stRS485Info.s32SerialFd, pszData+u32Count, RS485_FRAME_MAX_LEN-u32Count);
        if (s32Ret < 0)
        {
            if (errno != EAGAIN && errno != EINTR)
            {
                print_level(SV_ERROR, "recv error\n");
                return SV_FAILURE;
            }

            //print_level(SV_INFO, "EAGAIN or EINTR: recv no data\n");

			u32TimCnt++;
			if (u32TimCnt > u32TimCntLimit)
			{
				//print_level(SV_INFO, "data end\n");
				break;
			}
            else
            {
                sleep_ms(5);
            }
        }
        else if(s32Ret == 0)
        {
			u32TimCnt++;
			if (u32TimCnt > u32TimCntLimit)
			{
				//print_level(SV_INFO, "data end\n");
				break;
			}
            else
            {
                sleep_ms(5);
            }
        }
        else
        {
            u32Count += s32Ret;
            u32TimCnt = 0;
        }
    }

    *pu32Len = u32Count;
	return SV_SUCCESS;
}

sint32 rs485_WriteData(char *pszData, uint32 u32Len)
{
    sint32 i = 0, s32Ret = 0;

    if (u32Len > RS485_FRAME_MAX_LEN)
    {
        print_level(SV_ERROR, "RS485 frame is too long\n");
        return SV_FAILURE;
    }

	if (!m_stRS485Info.bRunning)
	{
		return SV_SUCCESS;
	}
#if 0
    printf("rs485 send data: : %d ",u32Len);
    for (i=0; i<u32Len; i++)
    {
        printf("0x%x ", pszData[i]);
    }
    printf("\r\n");
#endif
    pthread_mutex_lock(&m_stRS485Info.mutexSendData);
    s32Ret = serialWriteRaw(m_stRS485Info.s32SerialFd, pszData, u32Len);
    if (s32Ret != u32Len)
	{
		print_level(SV_ERROR, "serialWriteRaw failed.\n");
		pthread_mutex_unlock(&m_stRS485Info.mutexSendData);
		return SV_FAILURE;
	}


#if 0
    for (i=0; i<u32Len; i++)
    {
        s32Ret = serialWriteChar(m_stRS485Info.s32SerialFd, pszData[i]);
		if (s32Ret < 0)
		{
			print_level(SV_ERROR, "serialWriteChar failed.\n");
			pthread_mutex_unlock(&m_stRS485Info.mutexSendData);
			return SV_FAILURE;
		}
    }
#endif
    pthread_mutex_unlock(&m_stRS485Info.mutexSendData);

	return SV_SUCCESS;
}

sint32 rs485_SendData(char *pszData, uint32 u32Len)
{
    sint32 s32Ret = 0;
    sint32 u32Bytes = 0;
    uint8 u8Times = 0;
    uint64 u64SleepTime = 0;
    uint8 i = 0;

    if (pszData == NULL || u32Len > RS485_FRAME_MAX_LEN)
    {
        print_level(SV_ERROR, "serial frame is too long\n");
        return SV_FAILURE;
    }

    /* 485发送 */

    if(BOARD_IsHardware(BOARD_S_ADA46V1_FRONT)||BOARD_IsHardware(BOARD_S_ADA46V1_REAR)||BOARD_IsADA38_R159())
    {

        s32Ret = rs485_CtrlPinSetValue(108, 1);
    }else
    {

        s32Ret = rs485_CtrlPinSetValue(116, 1);
    }
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_RK_SetGPIO failed.\n");
		return SV_FAILURE;
    }

    /* 串口数据写入 */
    s32Ret = rs485_WriteData(pszData, u32Len);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "rs485_WriteData failed.\n");
        return SV_FAILURE;
    }
#if 0
        print_level(SV_INFO, "sendsize: %d\n", u32Len);
        char *tmp = pszData;
        for (i = 0; i < u32Len; i++)
        {
            printf("%02X ", *tmp++);
        }
        printf("\n");
#endif

    /* 判断是否发送完成 */
    s32Ret = ioctl(m_stRS485Info.s32SerialFd, TIOCOUTQ, &u32Bytes);
   //print_level(SV_DEBUG, "wait send len:%d ret:%d\n", u32Bytes, s32Ret);

    while((u32Bytes > 0) && (u8Times < 5))
    {
        u8Times++;
        u64SleepTime = (double)u32Bytes * 11 / m_stRS485Info.s32RS485Baudrate * 1000000;
        print_level(SV_INFO, "bytes:[%d], sleep time:[%lf], baudrate:%d \n", u32Bytes, u64SleepTime, m_stRS485Info.s32RS485Baudrate);
        usleep(u64SleepTime);
        s32Ret = ioctl(m_stRS485Info.s32SerialFd, TIOCOUTQ, &u32Bytes);
    }

    /* 串口缓存发送完成,等待485发送完成 */
    u64SleepTime = (double)(u32Len*11*1000000/m_stRS485Info.s32RS485Baudrate);
    usleep(u64SleepTime);

    /* 485接收 */
	if(BOARD_IsHardware(BOARD_S_ADA46V1_FRONT)||BOARD_IsHardware(BOARD_S_ADA46V1_REAR)||BOARD_IsADA38_R159())
    {

        s32Ret = rs485_CtrlPinSetValue(108, 0);
    }else
    {
        s32Ret = rs485_CtrlPinSetValue(116, 0);
    }
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_RK_SetGPIO failed.\n");
		return SV_FAILURE;
    }

	return SV_SUCCESS;
}
sint64 rs485_GetTimeTickMs()
{
	struct timespec time = {0, 0};

	clock_gettime(CLOCK_MONOTONIC, &time);
	return ((sint64)time.tv_sec) * 1000 + time.tv_nsec/1000000;
}

/******************************************************************************
 * 函数功能: 获取行人检测结果
 * 输入参数: 无
 * 输出参数: pszMcuPort -- mcu对应的设备文件
 * 返回值  : SV_FAILURE -- 失败
 			 SV_SUCCESS -- 成功
 * 注意      : 无
 *****************************************************************************/
sint32 rs485_GetPdResults(PD_RESULT_S *pstPdResults)
{
	sint32 s32Ret, i = 0, s32PdTargetSize = 0;
    SV_BOOL bException = SV_FALSE;
	char *tmpBuf = NULL;
	char szCmd[32] = {0};
	char szBuf[256] = {0};
	char szMcuPort[32] = {0};
	char szJsonRet[1024] = {0};
	cJSON *pstJson = NULL, *pstTimeStamp = NULL;
	cJSON *pstRed = NULL, *pstYellow = NULL, *pstGreen = NULL, *pstPDWorkMode = NULL, *pstSpeed = NULL;
    cJSON *pstPdsChn = NULL, *pstPdsItem = NULL;
    cJSON *pstTmp = NULL;

	s32Ret = cJSON_GetJson(DUMP_INFO_PD, szJsonRet);
	if (SV_SUCCESS != s32Ret)
	{
		return s32Ret;
	}

	pstJson = cJSON_Parse(szJsonRet);
	if (NULL == pstJson)
	{
		print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return SV_FAILURE;
	}

    pstPdsChn = cJSON_GetObjectItemCaseSensitive(pstJson, "pdsChn");
    if (NULL == pstPdsChn)
    {
        print_level(SV_ERROR, "keyword pdsChn is not exist.\n");
        bException = SV_TRUE;
		goto exit;
    }

    pstPdsItem = cJSON_GetArrayItem(pstPdsChn, 0);
    if (NULL == pstPdsItem)
    {
        print_level(SV_ERROR, "keyword pstPdsItem is not exist.\n");
        bException = SV_TRUE;
		goto exit;
    }

	pstPDWorkMode = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "PDWorkMode");
	if (NULL == pstPDWorkMode)
	{
		print_level(SV_ERROR, "keyword PDWorkMode is not exist.\n");
        bException = SV_TRUE;
		goto exit;
	}

	pstPdResults->s32PDWorkMode = pstPDWorkMode->valueint;
	if ((pstPdResults->s32PDWorkMode == 1)&& BOARD_IsNotCustomer(BOARD_C_ADA32V2_SAFE))
	{
		goto exit;
	}
	pstSpeed = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "speed");
	if (NULL == pstSpeed)
	{
		print_level(SV_ERROR, "keyword speed is not exist.\n");
        bException = SV_TRUE;
		goto exit;
	}
	pstPdResults->s32GpsSpeed = pstSpeed->valueint;

	pstTimeStamp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "TimeStamp");
	if (NULL == pstTimeStamp)
	{
		print_level(SV_ERROR, "keyword TimeStamp is not exist.\n");
        bException = SV_TRUE;
		goto exit;
	}
	pstPdResults->s64TimeStamp = pstTimeStamp->valueint;

	pstRed = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "RedRoiNum");
	if (NULL == pstRed)
    {
		print_level(SV_ERROR, "keyword RedRoiNum is not exist.\n");
        bException = SV_TRUE;
		goto exit;
    }
	pstPdResults->s32RedRoiNum = pstRed->valueint;

	pstYellow = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "YellowRoiNum");
	if (NULL == pstYellow)
    {
		print_level(SV_ERROR, "keyword YellowRoiNum is not exist.\n");
        bException = SV_TRUE;
		goto exit;
    }
	pstPdResults->s32YellowRoiNum = pstYellow->valueint;

	pstGreen = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "GreenRoiNum");
	if (NULL == pstGreen)
    {
		print_level(SV_ERROR, "keyword GreenRoiNum is not exist.\n");
        bException = SV_TRUE;
		goto exit;
    }
	pstPdResults->s32GreenRoiNum = pstGreen->valueint;

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "bShelter");
    if (NULL != pstTmp)
    {
        pstPdResults->bShelter = pstTmp->valueint;
    }

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "bNightMode");
    if (NULL != pstTmp)
    {
        pstPdResults->bNightMode = pstTmp->valueint;
    }

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "bRedAlarmOut");
    if (NULL != pstTmp)
    {
        pstPdResults->bRedAlarmOut = pstTmp->valueint;
    }

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "bYellowAlarmOut");
    if (NULL != pstTmp)
    {
        pstPdResults->bYellowAlarmOut = pstTmp->valueint;
    }

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "bGreenAlarmOut");
    if (NULL != pstTmp)
    {
        pstPdResults->bGreenAlarmOut = pstTmp->valueint;
    }

#if defined(BOARD_ADA32IR)
    cJSON *pstIrPdsItem = NULL;
    cJSON *pstIrRed = NULL, *pstIrYellow = NULL, *pstIrGreen = NULL;

    pstIrPdsItem = cJSON_GetArrayItem(pstPdsChn, 1);
    if (NULL == pstIrPdsItem)
    {
        print_level(SV_ERROR, "keyword pstPdsItem is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }
    pstIrRed = cJSON_GetObjectItemCaseSensitive(pstIrPdsItem, "RedRoiNum");
    if (NULL == pstIrRed)
    {
        print_level(SV_ERROR, "keyword RedRoiNum is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }
    pstPdResults->s32RedRoiNum += pstIrRed->valueint;

    pstIrYellow = cJSON_GetObjectItemCaseSensitive(pstIrPdsItem, "YellowRoiNum");
    if (NULL == pstIrYellow)
    {
        print_level(SV_ERROR, "keyword YellowRoiNum is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }
    pstPdResults->s32YellowRoiNum += pstIrYellow->valueint;

    pstIrGreen = cJSON_GetObjectItemCaseSensitive(pstIrPdsItem, "GreenRoiNum");
    if (NULL == pstIrGreen)
    {
        print_level(SV_ERROR, "keyword GreenRoiNum is not exist.\n");
        bException = SV_TRUE;
        goto exit;
    }
    pstPdResults->s32GreenRoiNum += pstIrGreen->valueint;

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstIrPdsItem, "bRedAlarmOut");
    if (NULL != pstTmp)
    {
        if (pstPdResults->bRedAlarmOut == 0)
            pstPdResults->bRedAlarmOut = pstTmp->valueint;
    }

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstIrPdsItem, "bYellowAlarmOut");
    if (NULL != pstTmp)
    {
        if (pstPdResults->bYellowAlarmOut == 0)
            pstPdResults->bYellowAlarmOut = pstTmp->valueint;
    }

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstIrPdsItem, "bGreenAlarmOut");
    if (NULL != pstTmp)
    {
        if (pstPdResults->bGreenAlarmOut == 0)
            pstPdResults->bGreenAlarmOut = pstTmp->valueint;
    }
#endif

    pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "RedHelmetNum");
	if (NULL != pstTmp)
    {
        pstPdResults->s32RedHelmetNum = pstTmp->valueint;
        pstPdResults->bHasHelmetNum = SV_TRUE;
    }

    if (pstPdResults->bHasHelmetNum)
    {
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "YellowHelmetNum");
    	if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "keyword YellowHelmetNum is not exist.\n");
            bException = SV_TRUE;
    		goto exit;
        }
        pstPdResults->s32YellowHelmetNum = pstTmp->valueint;

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "WhiteHelmetNum");
    	if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "keyword WhiteHelmetNum is not exist.\n");
            bException = SV_TRUE;
    		goto exit;
        }
        pstPdResults->s32WhiteHelmetNum = pstTmp->valueint;

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "BlueHelmetNum");
    	if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "keyword BlueHelmetNum is not exist.\n");
            bException = SV_TRUE;
    		goto exit;
        }
        pstPdResults->s32BlueHelmetNum = pstTmp->valueint;

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdsItem, "NoHelmetNum");
    	if (NULL == pstTmp)
        {
            print_level(SV_ERROR, "keyword NoHelmetNum is not exist.\n");
            bException = SV_TRUE;
    		goto exit;
        }
        pstPdResults->s32NoHelmetNum = pstTmp->valueint;
    }

exit:
    cJSON_Delete(pstJson);
    return bException ? SV_FAILURE : SV_SUCCESS;
}

sint32 rs485_Response_Get_GpsData(STRs485PacketInfo *stRs485Packet)
{
	sint32 s32Ret = SV_FAILURE;
    cJSON *pstJson = NULL;
    cJSON *cjVaild = NULL;
    cJSON *cjLat = NULL;
    cJSON *cjLng = NULL;
    cJSON *cjSpeed = NULL;
	GPS_DATA_S stGpsData = {0};
    uint8_t u8array[100] = {0};
    MSG_PACKET_S stMsgPkt = {0};

    if (NULL == stRs485Packet)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }
    pstJson = cJSON_Parse(stRs485Packet->u8args);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed. get body:\n%s\n", stRs485Packet->u8args);
        return MSG_FORMAT_ERROR;
    }

    cjVaild  = cJSON_GetObjectItem(pstJson, "Vaild");
    if (NULL == cjVaild)
    {
        cJSON_Delete(pstJson);
        return SV_FAILURE;
    }

    if (SV_FALSE ==  (SV_BOOL)cjVaild->valueint)
    {
        stGpsData.status = 0;
        stGpsData.signal = 0;
    }
    else
    {
        stGpsData.status = 3;
        stGpsData.signal = 3;
    }
    cjLat = cJSON_GetObjectItem(pstJson, "Lat");
    if (cjLat)
    {
        memset(u8array, 0, 100);
        memcpy(u8array, cjLat->valuestring, strlen(cjLat->valuestring));
        stGpsData.latitude = atof(u8array);
        //print_level(SV_INFO,"type :%s\n",u8zLat);
    }

    cjLng = cJSON_GetObjectItem(pstJson, "Lng");
    if (cjLng)
    {
        memset(u8array, 0, 100);
        memcpy(u8array, cjLng->valuestring, strlen(cjLng->valuestring));
        stGpsData.longitude = atof(u8array);
    }

    cjSpeed = cJSON_GetObjectItem(pstJson, "Speed");
    if (cjSpeed)
    {
        memset(u8array, 0, 100);
        memcpy(u8array, cjSpeed->valuestring, strlen(cjSpeed->valuestring));
        stGpsData.spk = atof(u8array);
    }
    cJSON_Delete(pstJson);

//    print_level(SV_INFO, "get Gps  Vaild:%d speed:%f Lat :%f Lng :%f \n",stGpsData.status,stGpsData.spk,stGpsData.latitude,stGpsData.longitude);

    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    stMsgPkt.stMsg.u16OpCode = OP_EVENT_DVR_GPS_DATA;
    stMsgPkt.pu8Data = (uint8 *)&stGpsData;
    stMsgPkt.u32Size = sizeof(GPS_DATA_S);
    s32Ret = Msg_submitEvent(EP_GPS, OP_EVENT_DVR_GPS_DATA, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

	return SV_SUCCESS;
}

sint32 rs485_Response_Get_Data()
{
    sint32 s32Ret;
    STRs485PacketInfo stRs485PacketSend = {0};
	PD_RESULT_S stPdResults = {0};
    PD_DETECT_INFO_S stPdDectectInfo = {0};
    static PD_DETECT_INFO_S sstPdDectectInfo = {0};
    static sint64  stSubmitTime = 0;
    sint64 s64NowTime = 0;
    sint64 s64nowTime = 0;
    sint32 s32DelayTime = 0;
    SV_BOOL bSendAlgData = SV_FALSE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
        s32DelayTime = 1000;
    else if (BOARD_IsADA38_R159())
        s32DelayTime = 500;

    //print_level(SV_INFO,"s32DelayTime :%d \n",s32DelayTime);

    bSendAlgData = SV_FALSE;
    memset(&stPdResults, 0, sizeof(stPdResults));
    pthread_mutex_lock(&m_stRS485Info.mutexAlgData);

    s64nowTime = rs485_GetTimeTickMs();
    if (m_stRS485Info.stPdRoiData.bValid)
    {
        bSendAlgData = SV_TRUE;
        s64nowTime = rs485_GetTimeTickMs();
        stPdResults.s64TimeStamp = m_stRS485Info.stPdRoiData.stPdResultNum.s64TimeStamp;
        stPdResults.s32RedRoiNum = m_stRS485Info.stPdRoiData.stPdResultNum.s32RedRoiNum;
        stPdResults.s32YellowRoiNum = m_stRS485Info.stPdRoiData.stPdResultNum.s32YellowRoiNum;
        stPdResults.s32GreenRoiNum = m_stRS485Info.stPdRoiData.stPdResultNum.s32GreenRoiNum;
        stPdResults.bGreenAlarmOut = m_stRS485Info.stPdRoiData.stPdResultNum.bGreenAlarmOut;
        stPdResults.bYellowAlarmOut = m_stRS485Info.stPdRoiData.stPdResultNum.bYellowAlarmOut;
        stPdResults.bRedAlarmOut = m_stRS485Info.stPdRoiData.stPdResultNum.bRedAlarmOut;

        stPdResults.s32GreenDelayTime = m_stRS485Info.stPdRoiData.stPdResultNum.s32GreenDelayTime;
        stPdResults.s32YellowDelayTime = m_stRS485Info.stPdRoiData.stPdResultNum.s32YellowDelayTime;
        stPdResults.s32RedDelayTime = m_stRS485Info.stPdRoiData.stPdResultNum.s32RedDelayTime;
        stPdResults.bNightMode = m_stRS485Info.stPdRoiData.stPdResultNum.bNightMode;
        stPdResults.s32GpsSpeed = m_stRS485Info.stPdRoiData.stPdResultNum.s32GpsSpeed;
        print_level(SV_INFO, "!!!get time: %lld, num: %d %d %d\n", m_stRS485Info.stPdRoiData.stPdResultNum.s64TimeStamp, m_stRS485Info.stPdRoiData.stPdResultNum.s32RedRoiNum, m_stRS485Info.stPdRoiData.stPdResultNum.s32YellowRoiNum, m_stRS485Info.stPdRoiData.stPdResultNum.s32GreenRoiNum);
        print_level(SV_INFO, "!!!alarmout: %d %d %d\n", m_stRS485Info.stPdRoiData.stPdResultNum.bGreenAlarmOut, m_stRS485Info.stPdRoiData.stPdResultNum.bYellowAlarmOut, m_stRS485Info.stPdRoiData.stPdResultNum.bRedAlarmOut);
    }
    pthread_mutex_unlock(&m_stRS485Info.mutexAlgData);


    /* 直接发送算法那边提交过来的数据 */
    if (bSendAlgData)
    {
        goto send_data;
    }

    s32Ret = rs485_GetPdResults(&stPdResults);

    send_data:

    stPdDectectInfo.u8RedRoiNum = stPdResults.s32RedRoiNum;
    stPdDectectInfo.u8YellowRoiNum = stPdResults.s32YellowRoiNum;
    stPdDectectInfo.u8GreenRoiNum = stPdResults.s32GreenRoiNum;
    stPdDectectInfo.bShelter = stPdResults.bShelter;
    stPdDectectInfo.bNightMode = stPdResults.bNightMode;
    stPdDectectInfo.u8speed = (uint8)stPdResults.s32GpsSpeed;

    if(m_stRS485Info.bR151Regulation)
    {
        stPdDectectInfo.bNightMode = stPdDectectInfo.bNightMode|0x10;
    }

   // print_level(SV_INFO,"Red :%d Yellow :%d Green :%d \n",stPdDectectInfo.u8RedRoiNum, stPdDectectInfo.u8YellowRoiNum,stPdDectectInfo.u8GreenRoiNum);
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159() || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        if (0 != stPdDectectInfo.u8RedRoiNum || 0 != stPdDectectInfo.u8YellowRoiNum || 0 != stPdDectectInfo.u8GreenRoiNum)
        {
            //有数据，时间更新

            stSubmitTime = rs485_GetTimeTickMs();
            memcpy(&sstPdDectectInfo, &stPdDectectInfo, 3);
            //print_level(SV_INFO, "++r: %d, y: %d, g: %d\n", stPdDectectInfo.u8RedRoiNum, stPdDectectInfo.u8YellowRoiNum, stPdDectectInfo.u8GreenRoiNum);
        }
        else
        {
            s64NowTime = rs485_GetTimeTickMs();
            if (0 != stSubmitTime && llabs(s64NowTime - stSubmitTime) <= s32DelayTime)
            {
                //如果时间小于500ms，则数据更新
                //print_level(SV_INFO, "s64NowTime: %lld, stSubmitTime: %lld, %lld\n", s64NowTime, stSubmitTime, s64NowTime - stSubmitTime);
                //print_level(SV_INFO, "--r: %d, y: %d, g: %d\n", sstPdDectectInfo.u8RedRoiNum, sstPdDectectInfo.u8YellowRoiNum, sstPdDectectInfo.u8GreenRoiNum);
                memcpy(&stPdDectectInfo, &sstPdDectectInfo, 3);

            }

            else
            {
                memset(&sstPdDectectInfo, 0, sizeof(PD_DETECT_INFO_S));
            }
        }
    }
    usleep(3*1000);
    stRs485PacketSend.u16StartCode = 0xffaa;
    stRs485PacketSend.u8Opcode = RS485_RQ_GET_DATA|CMD_RSP;
    stRs485PacketSend.u8DeviceId = m_stRS485Info.u8DeviceId;
    stRs485PacketSend.u8DeviceSep = m_stRS485Info.u8DeviceSep;

    stRs485PacketSend.u16Lenght = sizeof(stPdDectectInfo);
    memcpy(stRs485PacketSend.u8args, &stPdDectectInfo, stRs485PacketSend.u16Lenght);
    rs485_SendData(&stRs485PacketSend,stRs485PacketSend.u16Lenght+UART_PACK_HEAD_LEN);

     if (m_stRS485Info.stPdRoiData.bValid && llabs(s64nowTime - m_stRS485Info.stPdRoiData.stPdResultNum.s64TimeStamp) >= s32DelayTime)
    {
        m_stRS485Info.stPdRoiData.bValid = SV_FALSE;
    }
}
sint32 rs485_Response_Get_Version()
{
    STRs485PacketInfo stRs485PacketSend = {0};
    struct dirent *entry;
    uint8_t u8fireName[FIRENAMELEN];
    char * szfireName = NULL;
    uint8_t u8fireNameTemp[FIRENAMELEN];

    // 打开当前目录
    DIR *dir = opendir(FIREDIR);
    if (dir == NULL) {
        print_level(SV_INFO,"Failed to open directory :%s \n",FIREDIR);
        return EXIT_FAILURE;
    }


    // 遍历目录中的文件
    while ((entry = readdir(dir)) != NULL)
    {
        // 检查文件名是否以 AC601 开头
        if (strncmp(entry->d_name, "AC601", 5) == 0)
        {
            memcpy(u8fireName,entry->d_name,sizeof(entry->d_name));
            szfireName = strstr(u8fireName,AC601_BOARD_NAME);
            if(szfireName == NULL)
                break;
            memcpy(u8fireNameTemp,szfireName+sizeof(AC601_BOARD_NAME),sizeof(szfireName)-sizeof(AC601_BOARD_NAME));
            break;
        }
    }
    // 关闭目录
    closedir(dir);

    stRs485PacketSend.u16StartCode = 0xffaa;
    stRs485PacketSend.u8Opcode = RS485_RQ_UPGRADE_GET_VERSION|CMD_RSP;
    stRs485PacketSend.u8DeviceId = m_stRS485Info.u8DeviceId;
    stRs485PacketSend.u8DeviceSep = m_stRS485Info.u8DeviceSep;

    stRs485PacketSend.u16Lenght = sizeof(u8fireNameTemp);
    memcpy(stRs485PacketSend.u8args, u8fireNameTemp, stRs485PacketSend.u16Lenght);
    rs485_SendData(&stRs485PacketSend,stRs485PacketSend.u16Lenght+UART_PACK_HEAD_LEN);


}
sint32 rs485_Response_Get_Head()
{
    struct stat stStat = {0};
    char szCmd[256];
    sint32 s32Fd = -1;
    sint32 s32Ret = 0;
    uint8 *pu8Buf = NULL;
    STRs485UpHeader stRS485UpHeader = {0};
    STRs485PacketInfo stRs485PacketSend = {0};
    struct dirent *entry;
    uint8_t u8fireName[FIRENAMELEN];


    // 打开当前目录
    DIR *dir = opendir(FIREDIR);
    if (dir == NULL) {
        print_level(SV_INFO,"Failed to open directory :%s \n",FIREDIR);
        return EXIT_FAILURE;
    }

    // 遍历目录中的文件
    while ((entry = readdir(dir)) != NULL)
    {
        // 检查文件名是否以 AC601 开头
        if (strncmp(entry->d_name, "AC601", 5) == 0)
        {
            //memcpy(u8fireName,entry->d_name,sizeof(entry->d_name));
            sprintf(u8fireName,"%s/%s",FIREDIR,entry->d_name);
            break;
        }
    }
#if 1
    if (0 != stat(u8fireName, &stStat))
    {
        print_level(SV_ERROR, "file: %s unexist.\n", u8fireName);
        return SV_FAILURE;
    }

     print_level(SV_INFO, "file size: %lld\n", stStat.st_size);
     if (0 == stStat.st_size || stStat.st_size > 256 * 1024)
     {
         print_level(SV_ERROR, "file size: %lld is invalid.\n", stStat.st_size);
         return SV_FAILURE;
     }
#endif
     // 关闭目录
     closedir(dir);

     pu8Buf = malloc(stStat.st_size+64);
    if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc failed.\n");
        return SV_FAILURE;
    }

    s32Fd = open(u8fireName, O_RDONLY);
    if (s32Fd <= 0)
    {
        print_level(SV_ERROR, "open file: %s failed.\n", u8fireName);
        free(pu8Buf);
        return SV_FAILURE;
    }

    s32Ret = read(s32Fd, pu8Buf, stStat.st_size);
    if (s32Ret < stStat.st_size)
    {
        print_level(SV_ERROR, "read file: %s failed.\n", u8fireName);
        close(s32Fd);
        free(pu8Buf);
        return SV_FAILURE;
    }

    stRS485UpHeader.u32allDateLen = stStat.st_size;
    stRS485UpHeader.u32crc = crc32_check(pu8Buf, stRS485UpHeader.u32allDateLen);

     print_level(SV_INFO, "dateLen :%lx  crc32_check :%lx \n",stRS485UpHeader.u32allDateLen, stRS485UpHeader.u32crc);

     usleep(3*1000);
    stRs485PacketSend.u16StartCode = 0xffaa;
    stRs485PacketSend.u8Opcode = RS485_RQ_UPGRADE_GET_HEADER|CMD_RSP;
    stRs485PacketSend.u8DeviceId = m_stRS485Info.u8DeviceId;
    stRs485PacketSend.u8DeviceSep = m_stRS485Info.u8DeviceSep;

    stRs485PacketSend.u16Lenght = sizeof(stRS485UpHeader);
    memcpy(stRs485PacketSend.u8args, &stRS485UpHeader, stRs485PacketSend.u16Lenght);
    rs485_SendData(&stRs485PacketSend,stRs485PacketSend.u16Lenght+UART_PACK_HEAD_LEN);

    close(s32Fd);
    free(pu8Buf);
}

sint32 rs485_Response_Get_UpDate(STRs485PacketInfo *stRs485Packet)
{
    struct stat stStat = {0};
    char szCmd[256];
    sint32 s32Fd = -1;
    sint32 s32Ret = 0;
    uint8 *pu8Buf = NULL;
    uint32 u32i = 0;

    STRs485UpPackage stRs485UpPackage = {0};
    STRs485UpPackage *pstRs485UpPackage = NULL;
    STRs485PacketInfo stRs485PacketSend = {0};

    struct dirent *entry;
    uint8_t u8fireName[FIRENAMELEN];

    if (NULL == stRs485Packet)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    // 打开当前目录
    DIR *dir = opendir(FIREDIR);
    if (dir == NULL) {
        print_level(SV_INFO,"Failed to open directory :%s \n",FIREDIR);
        return EXIT_FAILURE;
    }

    // 遍历目录中的文件
    while ((entry = readdir(dir)) != NULL)
    {
        // 检查文件名是否以 AC601 开头
        if (strncmp(entry->d_name, "AC601", 5) == 0)
        {
            //memcpy(u8fireName,entry->d_name,sizeof(entry->d_name));
            sprintf(u8fireName,"%s/%s",FIREDIR,entry->d_name);
            break;
        }
    }
    // 关闭目录
    closedir(dir);


    if (0 != stat(u8fireName, &stStat))
    {
        print_level(SV_ERROR, "file: %s unexist.\n", u8fireName);
        return SV_FAILURE;
    }

     if (0 == stStat.st_size || stStat.st_size > 256 * 1024)
     {
         print_level(SV_ERROR, "file size: %lld is invalid.\n", stStat.st_size);
         return SV_FAILURE;
     }


     pu8Buf = malloc(stStat.st_size+64);
    if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc failed.\n");
        return SV_FAILURE;
    }

    s32Fd = open(u8fireName, O_RDONLY);
    if (s32Fd <= 0)
    {
        print_level(SV_ERROR, "open file: %s failed.\n", u8fireName);
        free(pu8Buf);
        return SV_FAILURE;
    }

    s32Ret = read(s32Fd, pu8Buf, stStat.st_size);
    if (s32Ret < stStat.st_size)
    {
        print_level(SV_ERROR, "read file: %s failed.\n", u8fireName);
        close(s32Fd);
        free(pu8Buf);
        return SV_FAILURE;
    }


    pstRs485UpPackage = stRs485Packet->u8args;

    usleep(3*1000);
#if 0
    if(stStat.st_size > (pstRs485UpPackage->u32startaddress + pstRs485UpPackage->u16dateLen))
    {

        /* 防止数据越界 */
        memcpy(stRs485UpPackage.u8args,&pu8Buf[pstRs485UpPackage->u32startaddress],pstRs485UpPackage->u16dateLen);
    }else
    {
        pstRs485UpPackage->u16dateLen = 0;
    }
#endif
    memcpy(stRs485UpPackage.u8args,&pu8Buf[pstRs485UpPackage->u32startaddress],pstRs485UpPackage->u16dateLen);
    stRs485UpPackage.u16dateLen = pstRs485UpPackage->u16dateLen;
    stRs485UpPackage.u32startaddress = pstRs485UpPackage->u32startaddress;
   // pstRs485UpPackage->u16dateLen =127 ;
    stRs485UpPackage.u32crc = crc32_check(stRs485UpPackage.u8args, stRs485UpPackage.u16dateLen);
    print_level(SV_INFO,"staddr:0x%x DateLen:0x%x Crc:0x%x \n",stRs485UpPackage.u32startaddress,stRs485UpPackage.u16dateLen,stRs485UpPackage.u32crc);

    stRs485PacketSend.u16StartCode = 0xffaa;
    stRs485PacketSend.u8Opcode = RS485_RQ_UPGRADE_GET_PACKAGE|CMD_RSP;
    stRs485PacketSend.u8DeviceId = m_stRS485Info.u8DeviceId;
    stRs485PacketSend.u8DeviceSep = m_stRS485Info.u8DeviceSep;

    stRs485PacketSend.u16Lenght = UART_UP_DATE_HEAD_LED + stRs485UpPackage.u16dateLen;
    memcpy(stRs485PacketSend.u8args, &stRs485UpPackage, stRs485PacketSend.u16Lenght);
    rs485_SendData(&stRs485PacketSend,stRs485PacketSend.u16Lenght+UART_PACK_HEAD_LEN);

    close(s32Fd);
    free(pu8Buf);
}
void rs485_test(void)
{
    STRs485PacketInfo stRs485Packet;
    STRs485UpPackage *pstRs485UpPackage = NULL;

    rs485_Response_Get_Version();
    usleep(1000*1000);
    rs485_Response_Get_Head();

    usleep(1000*1000);

    pstRs485UpPackage = stRs485Packet.u8args;
    pstRs485UpPackage->u32startaddress = 0x100;
    pstRs485UpPackage->u16dateLen = 1024;

    rs485_Response_Get_UpDate(&stRs485Packet);
}
void* rs485_Recv_Body(void *pvArg)
{
    sint32 s32Ret;
    STRS485Info *pstRS485Info = (STRS485Info *)pvArg;
    MSG_RS485_DATA_S stRS485RecvData = {0};
    STRs485PacketInfo stRs485PacketInfo = {0};
    MSG_PACKET_S stMsgPkt = {0};
    uint32_t i;
    struct timeval timeout;

    uint64 u64SleepTime = 0;

    while (pstRS485Info->bRunning)
    {
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        fd_set UartFd_Set;
        FD_ZERO(&UartFd_Set);
        FD_SET(pstRS485Info->s32SerialFd, &UartFd_Set);

        s32Ret = select(pstRS485Info->s32SerialFd+1, &UartFd_Set, 0, 0, &timeout);
        if(s32Ret < 0)
        {
            print_level(SV_ERROR, "%s\n", strerror(errno));
        }
        else if(s32Ret == 0)
        {
            //print_level(SV_INFO, "485 no data\n");
            continue;
        }
        else
        {
            memset(&stRS485RecvData, 0, sizeof(MSG_RS485_DATA_S));
            s32Ret = rs485_ReadData(stRS485RecvData.szRS485Data, &stRS485RecvData.s32DataLen);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "rs485_ReadData failed. Len:%d\n", stRS485RecvData.s32DataLen);
                continue;
            }

#if 0
            printf("rs485 recv data: ");
            for (i=0; i<stRS485RecvData.s32DataLen; i++)
            {
                printf("0x%x ", stRS485RecvData.szRS485Data[i]);
            }
            printf("\r\n");
#endif

            if(BOARD_IsHardware(BOARD_S_ADA46V1_FRONT)||BOARD_IsHardware(BOARD_S_ADA46V1_REAR)||BOARD_IsADA38_R159())
            {

                memset(&stRs485PacketInfo,0,sizeof(stRs485PacketInfo));
                memcpy(&stRs485PacketInfo,&stRS485RecvData.szRS485Data,stRS485RecvData.s32DataLen);
                //起始码，设备编码，同种设备编码核验
                //此处仅校验长度和起始码，设备编码和CRC不进行校验
                if(stRS485RecvData.s32DataLen < UART_PACK_HEAD_LEN)
                {
                    print_level(SV_INFO,"s32DataLen error :%d \n",stRS485RecvData.s32DataLen);
                    continue;
                }
                if(stRs485PacketInfo.u16StartCode != 0xffaa)
                {

                    print_level(SV_INFO,"u16StartCode error :0x%x \n",stRs485PacketInfo.u16StartCode);
                    continue;
                }

                if(stRs485PacketInfo.u8ConnectType && ((stRs485PacketInfo.u8DeviceId != m_stRS485Info.u8DeviceId)||(stRs485PacketInfo.u8DeviceSep != m_stRS485Info.u8DeviceSep)))
                {
                    //如果连接类型是接线器，则需要进行设备校验

                   // print_level(SV_INFO," u8DeviceId u8DeviceSep error :0x%x 0x%x %d %d \n",stRs485PacketInfo.u8DeviceId,stRs485PacketInfo.u8DeviceSep,m_stRS485Info.u8DeviceId,m_stRS485Info.u8DeviceSep);
                    continue;
                }
                if(stRs485PacketInfo.u8ConnectType == 0x00 && (stRs485PacketInfo.u8Opcode < RS485_RQ_GET_GPS ))
                {
                    //单接设备，不可以获取数据

                    print_level(SV_INFO," u8ConnectType u8Opcode error :0x%x 0x%x\n",stRs485PacketInfo.u8ConnectType,stRs485PacketInfo.u8Opcode);
                    continue;
                }
                //无需回包命令不需要，不需要做设备校验
                //需要回包的命令需要校验

                switch(stRs485PacketInfo.u8Opcode)
                {
                    case RS485_RQ_GET_DATA :
                        s32Ret =rs485_Response_Get_Data();
                        break;
                    case RS485_RQ_UPGRADE_GET_VERSION:
                        s32Ret = rs485_Response_Get_Version();
                        break;
                    case RS485_RQ_UPGRADE_GET_HEADER:
                        s32Ret = rs485_Response_Get_Head();
                        break;
                    case RS485_RQ_UPGRADE_GET_PACKAGE:
                        s32Ret = rs485_Response_Get_UpDate(&stRs485PacketInfo);
                        break;

                    case RS485_RQ_GET_GPS :
                        s32Ret =rs485_Response_Get_GpsData(&stRs485PacketInfo);
                        break;
                }
            }else
            {

                stMsgPkt.pu8Data = &stRS485RecvData;
                stMsgPkt.u32Size = sizeof(MSG_RS485_DATA_S);
                s32Ret = Msg_submitEvent(EP_ALG, OP_EVENT_RS485_RECV, &stMsgPkt);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
                    return SV_FAILURE;
                }
            }



        }
    }
}

sint32 RS485_Init(RS485_CONFIG_S *pstRS485Config)
{
	sint32 s32Ret, s32SerialFd = -1;
    memset(&m_stRS485Info, 0, sizeof(STRS485Info));
    pthread_t sendThread;
	s32Ret = pthread_mutex_init(&m_stRS485Info.mutexSendData, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    s32Ret = pthread_mutex_init(&m_stRS485Info.mutexAlgData, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

	s32Ret = serialOpen(RS485_PORT, pstRS485Config->s32RS485Baudrate, SV_TRUE, &s32SerialFd);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "serial_open %s error! baud: %d\n", RS485_PORT, pstRS485Config->s32RS485Baudrate);
		return SV_FAILURE;
	}
    print_level(SV_INFO,"pstRS485Config->s32RS485Baudrate :%d \n",pstRS485Config->s32RS485Baudrate);

	m_stRS485Info.s32SerialFd = s32SerialFd;
	m_stRS485Info.s32RS485Baudrate = pstRS485Config->s32RS485Baudrate;
    m_stRS485Info.bR151Regulation = pstRS485Config->bR151Regulation;

    if(BOARD_IsHardware(BOARD_S_ADA46V1_FRONT))
    {
        m_stRS485Info.u8DeviceId = 0x01,m_stRS485Info.u8DeviceSep = 0x01;
    }
    if(BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        m_stRS485Info.u8DeviceId = 0x01,m_stRS485Info.u8DeviceSep = 0x02;
    }

    if (BOARD_IsADA38_R159())
    {
        print_level(SV_INFO,"ADA38_R159 Version\n");
        m_stRS485Info.u8DeviceId = 0x02,m_stRS485Info.u8DeviceSep = 0x01;
    }

    if(BOARD_IsHardware(BOARD_S_ADA46V1_FRONT)||BOARD_IsHardware(BOARD_S_ADA46V1_REAR)||BOARD_IsADA38_R159())
    {

        s32Ret = rs485_CtrlPinInit(3, 12);
    }else
    {

        s32Ret = rs485_CtrlPinInit(3, 20);
    }
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "rs485_CtrlPinInit failed! [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

sint32 RS485_Fini()
{
	if(m_stRS485Info.s32SerialFd > 0)
	{
        serialClose(m_stRS485Info.s32SerialFd);
	}

    pthread_mutex_destroy(&m_stRS485Info.mutexSendData);
    pthread_mutex_destroy(&m_stRS485Info.mutexAlgData);
    memset(&m_stRS485Info, 0, sizeof(STRS485Info));
    return SV_SUCCESS;
}

sint32 RS485_Start()
{
    sint32 s32Ret = 0;
    m_stRS485Info.bRunning = SV_TRUE;

    pthread_t RecvTid;

    s32Ret = pthread_create(&RecvTid, NULL, rs485_Recv_Body, &m_stRS485Info);
    if(s32Ret != 0)
    {
        printf("pthread_create failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = MSG_ReciverStart(EP_RS485);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RS485, OP_EVENT_RS485_SEND, callbackRS485Send);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback_ThreadExec failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RS485, OP_EVENT_PD_PERSON_NUM, callbackRs485PersonNum);
   if (SV_SUCCESS != s32Ret)
   {
       print_level(SV_ERROR, "Msg_registerOpCallback_ThreadExec failed. [err=%#x]\n", s32Ret);
       return s32Ret;
   }

    m_stRS485Info.RecvTid = RecvTid;

    return SV_SUCCESS;
}

sint32 RS485_Stop()
{
    sint32 s32Ret = 0;
    pthread_t RecvTid = m_stRS485Info.RecvTid;
    void *pvRetval = NULL;

    m_stRS485Info.bRunning = SV_FALSE;

    s32Ret = pthread_join(RecvTid, &pvRetval);
    if (0 != s32Ret)
    {
        printf("pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 RS485_GetConfig(RS485_CONFIG_S *pstRS485Config)
{
    if (NULL == pstRS485Config)
    {
        return ERR_NULL_PTR;
    }

    pstRS485Config->s32RS485Baudrate = m_stRS485Info.s32RS485Baudrate;
    pstRS485Config->bR151Regulation = m_stRS485Info.bR151Regulation;

    return SV_SUCCESS;
}

sint32 RS485_SetConfig(RS485_CONFIG_S *pstRS485Config)
{
    sint32 s32Ret = -1, s32SerialFd = -1;

    if (NULL == pstRS485Config)
    {
        return ERR_NULL_PTR;
    }

    if (m_stRS485Info.s32RS485Baudrate != pstRS485Config->s32RS485Baudrate)
    {
        if (m_stRS485Info.s32SerialFd > 0)
        {
            serialClose(m_stRS485Info.s32SerialFd);
        }

        s32Ret = serialOpen(RS485_PORT, pstRS485Config->s32RS485Baudrate, SV_TRUE, &s32SerialFd);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "serial_open %s error! baud: %d\n", RS485_PORT, pstRS485Config->s32RS485Baudrate);
            s32Ret = serialOpen(RS485_PORT, m_stRS485Info.s32RS485Baudrate, SV_TRUE, &s32SerialFd);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "serial_open %s error! baud: %d\n", RS485_PORT, m_stRS485Info.s32RS485Baudrate);
            }
            else
            {
                print_level(SV_ERROR, "serial_open %s success! baud: %d\n", RS485_PORT, m_stRS485Info.s32RS485Baudrate);
            }
            m_stRS485Info.s32SerialFd = s32SerialFd;
            return SV_FAILURE;
        }
        else
        {
            print_level(SV_INFO, "RS485 %s reinit success! baudrate: %d\n", RS485_PORT, pstRS485Config->s32RS485Baudrate);
            m_stRS485Info.s32SerialFd = s32SerialFd;
            m_stRS485Info.s32RS485Baudrate = pstRS485Config->s32RS485Baudrate;
        }
    }

    m_stRS485Info.bR151Regulation = pstRS485Config->bR151Regulation;

    return SV_SUCCESS;
}

