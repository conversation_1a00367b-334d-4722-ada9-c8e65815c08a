#include <stdlib.h>
#include <string>
#include <sstream>
#include <map>
#include <utility>
#include <signal.h>

#include "cmsServer.h"
#include "cms_devInfo.h"
#include "cms_protocol.h"
#include "cms_mediaThread.h"
#include "cms_controlThread.h"
#include "cms_link.h"
#include "cms_state.h"
#include "cms_filePrivateUpload.h"
#include "cms_fileTransfer.h"
#include "cms_httpUpgrade.h"
#include "cms_offlineInfo.h"
#include "jsonHandle.h"
#include "op.h"
#include "alarm.h"
#include "sharefifo.h"
#include "cms_voicetalk.h"
#include "cms_processfile.h"
#include "fence.h"

#define RECONNECT_TIME_MEDIA 30
#define RECONNECT_TIME_MAIN  30


using namespace std;
extern "C" int MyDnsQuery(char *name, char *interface, char *result);

class CMS_SERVER;

static CMS_SERVER* pNetWork = SV_NULL;
const int CMS_AUDIO_BufSize = sizeof(sint16) * 4000 * 1;

void signal_callback_handler(int signum)
{
	fprintf(stderr,"signal_callback_handler ===> Caught signal %d\n", signum);
}


class CMS_SERVER:public SV_COMMON_JOIN_THREAD
{

typedef string (SV_NETWORK_PROTOCOL::*FunSetConfig)(SK_HEADER header, char *pData);
typedef sint32 (SV_NETWORK_PROTOCOL::* FunCreateJson)(SK_HEADER header, char *pOut, uint32 *pSize);

public:
	CMS_SERVER();
	virtual ~CMS_SERVER();

public:
	sint32 test();
	void run();
	sint32 sendLog(const char *pszLog, uint8 u8Level);
	sint32 sendAlarm(uint8 u8AlarmType, const char *szAlarmId, const char *szContent);
	sint32 reconnect();
	SV_BOOL isOnline();
	void ftpStop();
	void showState();
	sint32 processControlRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	sint32 processHttpAuthority(char *pData, SV_BOOL bAuth);
	sint32 processJsonSetConfig(char *pData, SV_BOOL bAuth);
	sint32 processFormatDevice(char *pData, SV_BOOL bAuth);
	sint32 processConfigImport(char *pData, SV_BOOL bAuth);
	sint32 processConfigExport(char *pData, SV_BOOL bAuth);
	sint32 processConfigReset(char *pOut, uint32 *pSize);
	sint32 processGetSWType(char *pOut, uint32 *pSize);
	sint32 processGetLockStatus(char *pOut, uint32 *pSize);
	sint32 processGetSysStatus(char *pOut, uint32 *pSize);
	const char *getRegisterReason();
	sint32 createConfigJson(char *pOut, uint32 *pSize);
	sint32 createDvrIdentification(char *pOut, uint32 *pSize);
	sint32 JsonGetDeviceInfo(char *pOut, uint32 *pSize);
	sint32 JsonGetGPSInfo(char *pOut, uint32 *pSize);
	time_t getLastCommunicationTime();
	sint32 getNextMessage();
	void deInit();
	void changeConfig();
	void preDeInit();
	void setTalkQueId(sint32 s32QueId);
	sint32 Stop();

private:
	SV_NETWORK_LINK controlLink, mediaLink;
	SV_NETWORK_MEDIATHREAD * pMediaThread;
	SV_NETWORK_CONTROLTHREAD* pControlThread;
    SV_NETWORK_FILEPRIVATEUPLOAD* pFilePrivateUpload;
	SV_NETWORK_FILETRANSFER* pFileTransfer;
	SV_NETWORK_HTTPUPGRADE* pHttpUpgrade;
	SV_NETWORK_VOICETALK* pVoiceTalk;

	sint32 s32TalkQueId;
	SV_BOOL bRegisterFlag;
	map<string, FunSetConfig> mapSetConfig;
	map<string, FunCreateJson> mapCreateJson;
	uint32 u32ControlCount, u32MediaCount;
	char szMd5[64];
	char szUpdateFileName[64];
	uint32 u32FilePort;
	char szTempUpgradePath[256];
	uint32 u32InformId;  //服务器离线文件使用
	CMS_SERVER_STAT_S stCmsServerStat;
    PROCESS_FILE_TYPE_E enFileType;

private:
	sint32 processMediaRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	sint32 processRegisterRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	sint32 processConnectRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	sint32 processOpenStreamRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	sint32 processOpenTalkRsp(SK_HEADER header,char *pData,uint32 u32DataSize);
	sint32 processCloseStreamRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	sint32 processCloseTalkRsp(SK_HEADER header,char *pData,uint32 u32DataSize);
	sint32 processTalkData(SK_HEADER header,char *pData,uint32 u32DataSize);
	sint32 getFileList(const char *pDirPath,uint32 u32FileType,char *szBeginTime,char *szEndTime,cJSON* pFileList,uint32 *u32FileNumber);
	void processAliveRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	void dumpHeader(SK_HEADER header);
	void processGetConfig(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processSetConfig(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processOperate(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processOfflineInfo(SK_HEADER header, char *pData, uint32 u32DataSize);
	void sendJsonResultHeader(SK_HEADER header);
	void processFense(SK_HEADER header, char* pData, uint32 u32DataSize);
	void processGetServerSetconfig(SK_HEADER header, char* pData, uint32 u32DataSize);
	void sendJsonResultStr(SK_HEADER header, SV_BOOL bResult, string str);
	void sendOperateResultStr(SK_HEADER header, SV_BOOL bResult, string str);
	void processAlarmUploadRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processLogUploadRsp(SK_HEADER header, char *pData, uint32 u32DataSize);
    void processCarStateInfoRsp(SK_HEADER header, char *pData, uint32 u32DataSize);

	void processFirmwareData(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processFirmwareResult(SK_HEADER header, char *pData, uint32 u32DataSize);
    void processConfigData(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processConfigResult(SK_HEADER header, char *pData, uint32 u32DataSize);
    void processFaceIdData(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processFaceIdResult(SK_HEADER header, char *pData, uint32 u32DataSize);

    void processRemoteFile(SK_HEADER header, char *pData, uint32 u32DataSize);
    void processRemoteData(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processRemoteResult(SK_HEADER header, char *pData, uint32 u32DataSize);

	void processGetServerInfo(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processSnapPic(SK_HEADER header, char *pData, uint32 u32DataSize);
	void processFileList(SK_HEADER header, char *pData, uint32 u32DataSize);
	sint32 createFileDataRsp(cJSON *pstfileReq,cJSON*pstfileRsp);
	void processTransferFileDataReq(SK_HEADER header, char *pData, uint32 u32DataSize);

};

CMS_SERVER::CMS_SERVER()
{
	bRegisterFlag = SV_FALSE;
	pControlThread = new SV_NETWORK_CONTROLTHREAD(SV_NETWORK_DVRINFO::getInstance(), SV_NETWORK_PROTOCOL::getInstance(), &controlLink, SV_NETWORK_STATE::getInstance());
	pMediaThread = new SV_NETWORK_MEDIATHREAD(SV_NETWORK_DVRINFO::getInstance(),SV_NETWORK_PROTOCOL::getInstance(), &mediaLink, SV_NETWORK_STATE::getInstance());
    pFilePrivateUpload = new SV_NETWORK_FILEPRIVATEUPLOAD(SV_NETWORK_DVRINFO::getInstance(), SV_NETWORK_STATE::getInstance(), SV_NETWORK_PROTOCOL::getInstance(), &controlLink);
	pFileTransfer = new SV_NETWORK_FILETRANSFER(SV_NETWORK_DVRINFO::getInstance(),  SV_NETWORK_PROTOCOL::getInstance(), SV_NETWORK_STATE::getInstance());
#if defined(BOARD_ADA32C4)
	pVoiceTalk = new SV_NETWORK_VOICETALK(SV_NETWORK_PROTOCOL::getInstance(),&mediaLink,SV_NETWORK_STATE::getInstance());
#endif

	start();
	//SV_NETWORK_DVRINFO::getInstance()->start();
	pControlThread->start();
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
    pControlThread->startTimerRun();

    if (BOARD_IsNotCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        pControlThread->startCarStateRun();
    }
#endif

	pMediaThread->start();
	pFilePrivateUpload->start();
	pFileTransfer->start();

	//CMSOffline_StartBody();

	mapSetConfig[TAG_JPGSNAP] = &SV_NETWORK_PROTOCOL::setJpgSnap;
	mapSetConfig[TAG_JUSTTEST] = &SV_NETWORK_PROTOCOL::setJustTest;
	mapSetConfig[TAG_STARTRECORD] = &SV_NETWORK_PROTOCOL::setStartRecord;
	mapSetConfig[TAG_STOPRECORD] = &SV_NETWORK_PROTOCOL::setStopRecord;
	mapSetConfig[TAG_STREAMTYPE] = &SV_NETWORK_PROTOCOL::setStreamType;
	mapSetConfig[TAG_RECORD] = &SV_NETWORK_PROTOCOL::setRecord;
	mapSetConfig[TAG_DISPLAY] = &SV_NETWORK_PROTOCOL::setDisplay;
	mapSetConfig[TAG_SYSTEM] = &SV_NETWORK_PROTOCOL::setSystem;
	mapSetConfig[TAG_NETWORK] = &SV_NETWORK_PROTOCOL::setNetwork;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))
	RECORDER_CmsRegisterCallbak(CMS_Server_SetPrivateFileUpdate); //注册 报警文件已生成 函数
#endif

	mapCreateJson["All"] = &SV_NETWORK_PROTOCOL::createAllConfigJson;
    mapCreateJson["Version"] = &SV_NETWORK_PROTOCOL::createVersionJson;
	mapCreateJson["DvrStatus"] = &SV_NETWORK_PROTOCOL::createDevStatusJson;
	mapCreateJson["FaceId"] = &SV_NETWORK_PROTOCOL::createFaceIdJson;

	signal(SIGPIPE, signal_callback_handler);

	u32ControlCount = 0;
	u32MediaCount = 0;
	memset(szMd5, 0, 64);
	memset(szUpdateFileName, 0, 64);
	memset(szTempUpgradePath, 0, 256);

	u32FilePort = 9092;
	u32InformId = 0;

    pHttpUpgrade = NULL;

    stCmsServerStat.pszServerStat = "Enable";
    stCmsServerStat.pszRegStat = "Connecting";
    SV_NETWORK_STATE::getInstance()->dumpCmsServerInfo(&stCmsServerStat);

    enFileType = PRO_FILE_TYPE_UPGRADE;
}


void CMS_SERVER::deInit()
{
	printf("deInit\n");
	SV_NETWORK_STATE::getInstance()->setNetworkRunning(SV_FALSE);
	usleep(500000);
	pControlThread->stop();
	print_level(SV_DEBUG,"pControlThread join\n");
	pMediaThread->stop();
	print_level(SV_DEBUG,"pMediaThread join\n");
	pFilePrivateUpload->stop();
	print_level(SV_DEBUG,"pFilePrivateUpload join\n");
    pFileTransfer->stop();
	print_level(SV_DEBUG,"pFileTransfer join\n");
	if(pHttpUpgrade != NULL)
	{
	    pHttpUpgrade->stop();
	    print_level(SV_DEBUG,"pHttpUpgrade join\n");
	}
}

void CMS_SERVER::preDeInit()
{
	printf("preDeInit()\n");
	SV_NETWORK_STATE::getInstance()->setNetworkRunning(SV_FALSE);
	SV_NETWORK_STATE::getInstance()->clearRegisterFlag();
	pControlThread->cancel();
	pMediaThread->cancel();
	pFilePrivateUpload->cancel();
    pFilePrivateUpload->closeFd();
	pFileTransfer->cancel();
    pFileTransfer->closeFd();
	if(pHttpUpgrade != SV_NULL)
	{
        SV_NETWORK_HTTPUPGRADE::setUpdate(SV_FALSE);
		pHttpUpgrade->stopDownload();
		pHttpUpgrade->cancel();
	}
}

void CMS_SERVER::changeConfig()
{
	SV_NETWORK_STATE::getInstance()->clearRegisterFlag();
	SV_NETWORK_STATE::getInstance()->setConfigUpdate(true);
	if (pHttpUpgrade != SV_NULL)
	{
        SV_NETWORK_HTTPUPGRADE::setUpdate(SV_FALSE);
		pHttpUpgrade->stopDownload();
	}
}

sint32 CMS_SERVER::Stop()
{
	sint32 s32Ret;
	if(pNetWork == SV_NULL)
	{
		return SV_FAILURE;
	}
	SV_NETWORK_STATE::getInstance()->setNetworkRunning(SV_FALSE);
	pControlThread->stop();
	print_level(SV_DEBUG,"pControlThread join\n");
	pMediaThread->stop();
	print_level(SV_DEBUG,"pMediaThread join\n");
	pFilePrivateUpload->stop();
	print_level(SV_DEBUG,"pFilePrivateUpload join\n");
    pFileTransfer->stop();
	print_level(SV_DEBUG,"pFileTransfer join\n");
	if(pHttpUpgrade != SV_NULL)
	{
		SV_NETWORK_HTTPUPGRADE::setUpdate(SV_FALSE);
		pHttpUpgrade->stopDownload();
		print_level(SV_DEBUG,"httpUpgrade pthread join.\n");
		pHttpUpgrade->stop();
	}
	return SV_SUCCESS;

}

CMS_SERVER::~CMS_SERVER()
{

}

sint32 CMS_SERVER::test()
{

	return SV_SUCCESS;
}

void CMS_SERVER::run()
{
    bool bCtrlReconn = false;
    bool bMediaReconn = false;
	SK_HEADER header;
	char dataBuf[NETWORK_MAX_RECVSIZE];
	uint32 u32DataSize = 0;
	sint32 s32TempSocket = -1;
	uint32 u32CtrlRecvErrCnt = 0, u32MediaRecvErrCnt = 0;

	while(SV_NETWORK_STATE::getInstance()->getNetworkRunning())
	{
        if (bCtrlReconn)
        {
            bCtrlReconn = false;
            bMediaReconn = false;
            print_level(SV_WARN,"Control socket timeout close, timeout time: %ds\n", RECONNECT_TIME_MAIN*2);
			controlLink.closeSocket();
			mediaLink.closeSocket();
			u32ControlCount = 0;
			u32MediaCount = 0;
            SV_NETWORK_STATE::getInstance()->clearRegisterFlag();
        }

        if (bMediaReconn)
        {
            bMediaReconn = false;
            print_level(SV_WARN,"Media socket timeout close, timeout time: %ds\n", RECONNECT_TIME_MAIN*2);
			mediaLink.closeSocket();
			u32MediaCount = 0;
            SV_NETWORK_STATE::getInstance()->setMediaRegisterFlag(SV_FALSE);
        }

		fd_set read_set;
		FD_ZERO(&read_set);
		if( controlLink.isAvalible() )
		{
			//print_level(SV_DEBUG,"get controlLink success\n");
			s32TempSocket = controlLink.getSocket();
			if(s32TempSocket > 0)
			{
				//print_level(SV_INFO,"controlink socket %d \n",s32TempSocket);
				FD_SET(s32TempSocket, &read_set);
			}
			else
			{
				sleep(1);
				continue;
			}
		}

		if( mediaLink.isAvalible() )
		{
			//print_level(SV_DEBUG,"get mediaLink success\n");
			s32TempSocket = mediaLink.getSocket();
			if(s32TempSocket > 0)
			{
				FD_SET(s32TempSocket, &read_set);
			}
		}

		struct timeval tv;
		tv.tv_sec = 2;
		tv.tv_usec = 0;

		sint32 s32MaxFd = controlLink.getSocket() > mediaLink.getSocket() ? controlLink.getSocket() +1 : mediaLink.getSocket() + 1;
		sint32 s32Ret = select(s32MaxFd, &read_set, NULL, NULL, &tv);
		if(s32Ret < 0)
		{
			controlLink.closeSocket();
			mediaLink.closeSocket();
			print_level(SV_ERROR,"select error %s\n", strerror(errno));
		}
		else if(s32Ret == 0)
		{
			++u32ControlCount;
			++u32MediaCount;
			print_level(SV_WARN,"select timeout, u32ControlCount:%d, u32MediaCount: %d\n", u32ControlCount, u32MediaCount);

			if (u32ControlCount >= RECONNECT_TIME_MAIN)
			{
			    bCtrlReconn = true;
			}

			if (u32MediaCount >= RECONNECT_TIME_MEDIA)
			{
			    bMediaReconn = true;
			}
		}
		else
		{
			if( mediaLink.isAvalible() && FD_ISSET(mediaLink.getSocket(),&read_set) )
			{
			    //print_level(SV_DEBUG,"mediaLink data\n");
				if( mediaLink.recvFromServer(&header, dataBuf, &u32DataSize) < 0 )
				{
					print_level(SV_ERROR,"recv from media server error\n");
					u32MediaRecvErrCnt++;
					if (u32MediaRecvErrCnt >= 5)
					{
                        bMediaReconn = true;
					}
					continue;
				}
                u32MediaRecvErrCnt = 0;

				processMediaRsp(header, dataBuf, u32DataSize);
			}

			if( controlLink.isAvalible() && FD_ISSET(controlLink.getSocket(),&read_set) )
			{

				//print_level(SV_DEBUG,"controlLink data\n");
				if( controlLink.recvFromServer(&header, dataBuf, &u32DataSize) < 0 )
				{
					print_level(SV_ERROR,"recv from control server error\n");
					u32CtrlRecvErrCnt++;
					if (u32CtrlRecvErrCnt >= 5)
					{
                        bCtrlReconn = true;
					}
					continue;
				}
                u32CtrlRecvErrCnt = 0;

				//dumpHeader(header);
				processControlRsp(header, dataBuf, u32DataSize);
			}
		}
	}

	SV_NETWORK_STATE::getInstance()->clearRegisterFlag();
	controlLink.closeSocket();
    mediaLink.closeSocket();
    pFilePrivateUpload->exitFileprivate();
}

sint32 CMS_SERVER::processControlRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{

	SV_CHECK(pData);
	u32ControlCount = 0;

	//print_level(SV_DEBUG,"code:0x%x\n", header.usCode);
	switch(header.usCode)
	{

		case SK_DVR_TO_CMS_SERVER_REGISTER_RSP: processRegisterRsp(header, pData, u32DataSize); break;
		case SK_CLIENT_TO_CMS_SERVER_KEEP_ALIVE_RSP: processAliveRsp(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_GET_CONFIG_REQ: processGetConfig(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_SET_CONFIG_REQ: processSetConfig(header, pData, u32DataSize); break; //在线设置配置参数，dms31和wfc202参数不同，cms端不能解析json
		//case SK_CMS_SERVER_TO_DVR_UPDATE_CONFIG_REQ: processSetConfig(header, pData, u32DataSize); break; // 离线配置，目前用的也是上面的在线参数op
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_REMOTE_OPERATE_REQ: processOperate(header, pData, u32DataSize); break;
		case SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_UPLOAD_ALARM_RSP: processAlarmUploadRsp(header, pData, u32DataSize); break;
		case SK_DVR_TO_CMS_SERVER_UPLOAD_LOG_RSP: processLogUploadRsp(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_INFORM_DVR_REMOTE_UPDATE_REQ : processRemoteFile(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_DATA_REQ: processRemoteData(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_RESULT_INFORM: processRemoteResult(header, pData, u32DataSize); break;
		case SK_CLIENT_TO_CMS_SERVER_GET_CONFIG_INFO_RSP: processGetServerInfo(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_SNAP_PICTURE_REQ: processSnapPic(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_GET_FILE_LIST_REQ:processFileList(header,pData,u32DataSize);break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_PREPARE_TRANSFER_FILE_DATA_REQ:processTransferFileDataReq(header,pData,u32DataSize);break;
		case SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_UPLOAD_DEVICE_STATE_INFO_RSP:processCarStateInfoRsp(header,pData,u32DataSize);break;
        case SK_CMS_SERVER_TO_DVR_UPDATE_FW_REQ:
            print_level(SV_INFO, "get SK_CMS_SERVER_TO_DVR_UPDATE_FW_REQ: %#x, get server info again\n", SK_CMS_SERVER_TO_DVR_UPDATE_FW_REQ);
            pControlThread->getServerInfo();
            break;
		case SK_CMS_SERVER_TO_DVR_OFFLINE_INFORM:processOfflineInfo(header,pData,u32DataSize);break;
		case SK_DVR_TO_CMS_SERVER_GET_ALARM_FENSE_RSP: processFense(header, pData, u32DataSize);break;
		case SK_CMS_SERVER_TO_DVR_BASIC_CONFIG_CHANGE_INFORM_REQ: processGetServerSetconfig(header, pData, u32DataSize); break;
		default: break;
	}

	return SV_SUCCESS;
}

sint32 CMS_SERVER::processMediaRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	SV_CHECK(pData);
	u32MediaCount = 0;
    static sint32 s32PrintCnt = 0;

	//print_level(SV_DEBUG,"Get media Rspcode:0x%x\n", header.usCode);
	switch(header.usCode)
	{
		case SK_CLIENT_TO_CMS_SERVER_CONNECT_STREAM_SERVER_RSP: processConnectRsp(header, pData, u32DataSize); break;
		case SK_CLIENT_TO_CMS_SERVER_KEEP_ALIVE_RSP:
            if (s32PrintCnt++ % 8 == 0)
                print_level(SV_DEBUG,"Get media Alive!\n");
            break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_OPEN_STREAM_REQ: processOpenStreamRsp(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_CLOSE_STREAM_REQ: processCloseStreamRsp(header, pData, u32DataSize); break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_OPEN_TALK_REQ:processOpenTalkRsp(header,pData,u32DataSize);break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_CLOSE_TALK_REQ:processCloseTalkRsp(header,pData,u32DataSize);break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_TALK_DATA:processTalkData(header,pData,u32DataSize);
		default: break;
	}

	return SV_SUCCESS;
}

void CMS_SERVER::dumpHeader(SK_HEADER header)
{
	print_level(SV_DEBUG,"\nHeader code:0x%x, SrcId:%llu, DstId:%llu\n", header.usCode, header.ullSrcId, header.ullDstId);
	print_level(SV_DEBUG,"Header Len:%u\n\n", header.uiTotalLength);
}

sint32 CMS_SERVER::processRegisterRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
    sint32 s32Ret = SV_SUCCESS;
	SKRegisterRsp registerRsp;

    s32Ret = SV_NETWORK_PROTOCOL::getInstance()->analysisRegisterRsp(pData, &registerRsp);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_DEBUG,"analysisRegisterRsp fail\n");
		return SV_FAILURE;
	}

	if( strcmp((const char *)registerRsp.szTcpAddr, "127.0.0.1") == 0 )
	{
		mediaLink.setServerIp( controlLink.getServerIp() );
		print_level(SV_DEBUG,"Set media thraed ip:%s\n", (const char *)controlLink.getServerIp());
	}
	else
	{
		mediaLink.setServerIp( (const char *)registerRsp.szTcpAddr );
		print_level(SV_DEBUG,"Set media thraed ip:%s\n", (const char *)registerRsp.szTcpAddr);
	}
    print_level(SV_DEBUG,"Set media thraed port: %d\n", registerRsp.usTcpStreamPort);
	mediaLink.setPort( registerRsp.usTcpStreamPort);
	pMediaThread->setSessionId( registerRsp.ullGlobalSessionId);
	pFilePrivateUpload->setSessionId(registerRsp.ullGlobalSessionId);
	pFileTransfer->setSessionId(registerRsp.ullGlobalSessionId);
	SV_NETWORK_STATE::getInstance()->setControlRegisterFlag(SV_TRUE);

	print_level(SV_DEBUG,"register success, media ip:%s, port:%d, id:%llu\n",
		mediaLink.getServerIp(), registerRsp.usTcpStreamPort, registerRsp.ullGlobalSessionId);

	return s32Ret;
}

sint32 CMS_SERVER::processConnectRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	SKCommonRsp* pConnectRsp = (SKCommonRsp*)pData;
	if(pConnectRsp->ucResult == 0) // success
	{
		print_level(SV_DEBUG,"Media connect success\n");
		SV_NETWORK_STATE::getInstance()->setMediaRegisterFlag(SV_TRUE);
	}
	else
	{
		print_level(SV_ERROR,"Media connect fail, reason:%s\n", pConnectRsp->szReason);
	}

	return SV_SUCCESS;
}

sint32 CMS_SERVER::processOpenStreamRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	sint32 s32Num = 0;
	uint8 u8ChnMask = 0;
	SKRealTimeStreamReq* pStreamChn = SV_NULL;
	SKDeviceChannelNumber* pChnNum = (SKDeviceChannelNumber*)pData;
	s32Num = pChnNum->uiChannelNumber;
	print_level(SV_DEBUG,"DataSize:%u, s32Num:%d\n", u32DataSize, s32Num);
	for(int i=0; i<s32Num; ++i)
	{
		pStreamChn = (SKRealTimeStreamReq*)(pData + sizeof(SKDeviceChannelNumber) + i*sizeof(SKRealTimeStreamReq));
		u8ChnMask |= 1 << pStreamChn->usChannelId;
		print_level(SV_DEBUG,"chnId:%d\n", pStreamChn->usChannelId);
	}

	print_level(SV_DEBUG,"\n Get open chnMask:0x%x\n", u8ChnMask);
	pMediaThread->setMaskBit(u8ChnMask);

	SK_HEADER sendHeader;
	SKCommonRsp commonReq;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_OPEN_STREAM_RSP, sizeof(SKCommonRsp), header.ullSrcId);
	SV_NETWORK_PROTOCOL::getInstance()->createCommonRsp(&commonReq, 0, "success!");
	return mediaLink.sendToServer(&sendHeader, (char *)&commonReq, sizeof(SKCommonRsp));

}

sint32 CMS_SERVER::processOpenTalkRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	sint32 s32Ret = SV_SUCCESS;
#if defined(BOARD_ADA32C4)
	SK_HEADER sendHeader;
	SKCommonRsp commonReq;
	uint8 u8Buf[10240] = {0};

	ALARM_EnableSpk(SV_TRUE);

	pVoiceTalk->setTalkDestId(header.ullSrcId);
	pVoiceTalk->setPlayFlag(SV_TRUE);

	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_OPEN_TALK_RSP, sizeof(SKCommonRsp), header.ullSrcId);
	//SV_NETWORK_PROTOCOL::getInstance()->createCommonRsp(&commonReq, 0, "create RK audio buffer error!");
	SV_NETWORK_PROTOCOL::getInstance()->createCommonRsp(&commonReq, 0, "success!");
	s32Ret = mediaLink.sendToServer(&sendHeader, (char *)&commonReq, sizeof(SKCommonRsp));


#endif
	return s32Ret;

}


sint32 CMS_SERVER::processTalkData(SK_HEADER header,char *pData,uint32 u32DataSize)
{
#if defined(BOARD_ADA32C4)
	static uint32 count = 0;
	SK_HEADER recvHeader;
	static uint32	u32Time1 = 0;
	uint32			u32Time2;
	SFIFO_MDADDR  stTalkDataInfo;
	SFIFO_MSHEAD stPacketHead = {0};
	sint32 	s32Ret;


	SKRealTimeVoiceTalkData* pTalkData = (SKRealTimeVoiceTalkData*)pData;
	//print_level(SV_DEBUG, "sample = %d  data_size:%d talkmb:%d \n", pTalkData->uiSampleRate,pTalkData->uiFrameLen,u32AuidoBufSize);

	if(pTalkData->ucAudioType != 0 || pTalkData->uiSampleRate != 16000 || pTalkData->uiFrameLen > 3200)
	{
		print_level(SV_ERROR,"type:%d sample:%d Framelen:%d/%d \n", pTalkData->ucAudioType, pTalkData->uiSampleRate, pTalkData->uiFrameLen, u32DataSize);
		return SV_FAILURE;
	}


	stPacketHead.flag = MSHEAD_FLAG;
    stPacketHead.algorithm = 0;
    stPacketHead.type = 2;
    stPacketHead.width = 0;
    stPacketHead.height = 0;
    stPacketHead.serial = 0;
    stPacketHead.pts = 0;

	stTalkDataInfo.u32DataCnt = 1;
    stTalkDataInfo.pau8Addr[0] = pData+sizeof(SKRealTimeVoiceTalkData);
    stTalkDataInfo.au32Len[0] = 3200;

    s32Ret = SFIFO_WritePacket(s32TalkQueId, &stPacketHead, &stTalkDataInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_WritePacket failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

#endif
	return SV_SUCCESS;
}

sint32 CMS_SERVER::processCloseStreamRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	sint32 s32Num = 0;
	uint8 u8ChnMask = 0;
	SKRealTimeStreamReq* pStreamChn = SV_NULL;
	SKDeviceChannelNumber* pChnNum = (SKDeviceChannelNumber*)pData;
	s32Num = pChnNum->uiChannelNumber;
	//print_level(SV_DEBUG,"DataSize:%u, s32Num:%d\n", u32DataSize, s32Num);
	for(int i=0; i<s32Num; ++i)
	{
		pStreamChn = (SKRealTimeStreamReq*)(pData + sizeof(SKDeviceChannelNumber) + i*sizeof(SKRealTimeStreamReq));
		u8ChnMask |= 1 << pStreamChn->usChannelId;
		//print_level(SV_DEBUG,"chnId:%d\n", pStreamChn->usChannelId);
	}

//	print_level(SV_DEBUG,"Get close chnMask:0x%x\n", u8ChnMask);
	pMediaThread->clearMaskBit(u8ChnMask);

	SK_HEADER sendHeader;
	SKCommonRsp commonReq;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_CLOSE_STREAM_RSP, sizeof(SKCommonRsp), header.ullSrcId);
	SV_NETWORK_PROTOCOL::getInstance()->createCommonRsp(&commonReq, 0, "success!");
	return mediaLink.sendToServer(&sendHeader, (char *)&commonReq, sizeof(SKCommonRsp));
}

sint32 CMS_SERVER::processCloseTalkRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	sint32 s32Ret = SV_SUCCESS;
#if defined(BOARD_ADA32C4)
	SK_HEADER sendHeader;
	SKCommonRsp commonReq;

	pVoiceTalk->setPlayFlag(SV_FALSE);

	ALARM_EnableSpk(SV_FALSE);

	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_CLOSE_TALK_RSP, sizeof(SKCommonRsp), header.ullSrcId);
	SV_NETWORK_PROTOCOL::getInstance()->createCommonRsp(&commonReq, 0, "success!");
	s32Ret = mediaLink.sendToServer(&sendHeader, (char *)&commonReq, sizeof(SKCommonRsp));
#endif
	return s32Ret;
}

void CMS_SERVER::processAliveRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{

	sint32 s32Diff = 0;
	SV_NETWORK_STATE::getInstance()->reflashCommunicationTime();
	if(SV_NETWORK_STATE::getInstance()->getRemoteUpdateFlag())
	{
		time_t timeCur;
		time(&timeCur);
		s32Diff = timeCur - SV_NETWORK_STATE::getInstance()->getUpdateTime();
		print_level(SV_DEBUG,"timeCur:%d, state.getUpdateTime():%d s32Diff:%d\n", timeCur, SV_NETWORK_STATE::getInstance()->getUpdateTime(), s32Diff);

		if(s32Diff > 60 || s32Diff < 0)
		{
			print_level(SV_DEBUG,"no updateData 60s, quit update!\n");
			SV_NETWORK_STATE::getInstance()->setRemoteUpdateFlag(SV_FALSE);
		}
	}

}

void CMS_SERVER::processGetConfig(SK_HEADER header, char *pData, uint32 u32DataSize)
{

	print_level(SV_DEBUG,"Get SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_GET_CONFIG_REQ\n");
	SKGetConfig* pConfig = (SKGetConfig*)pData;
	print_level(SV_DEBUG,"config:%s\n", pConfig->szConfigType);

	string strConfig = (char *)pConfig->szConfigType;
	char buf[1024*10] = "0";
	uint32 u32Size = 0;

	map<string, FunCreateJson>::iterator iterTest = mapCreateJson.find(strConfig);
	if(iterTest != mapCreateJson.end())
	{
		(SV_NETWORK_PROTOCOL::getInstance()->*(mapCreateJson[strConfig]))(header, buf, &u32Size);
	}

	controlLink.addDataToList(buf, u32Size);

}

void CMS_SERVER::processSetConfig(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	print_level(SV_DEBUG, "Get usCode: 0x%x\n", header.usCode);
	char *pJson = pData;
    char pOut[1024];
	printf("Set Json:%s\n", pJson);

	cJSON *json, *module;
	ostringstream ostrReturn;
	SV_BOOL bResult = SV_FALSE, bReconnect = SV_FALSE;

	sint32 s32Ret = JSON_HDL_SetConfig(pData, pOut);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "JSON_HDL_SetConfig failed. [err: %d]\n", s32Ret);
		bResult=SV_FALSE;
    }else{
		bResult=SV_TRUE;
    }

    if (0 == strcmp(pOut, "{}"))
    {
        ostrReturn << "Success";
    }
    else
    {
        ostrReturn << pOut;
    }

    //if (SK_CMS_SERVER_TO_DVR_UPDATE_CONFIG_REQ == header.usCode)
    if (0)
    {
        sendJsonResultHeader(header);
    }
    else
    {
    	sendJsonResultStr(header, bResult, ostrReturn.str());
    }
	if(bReconnect)
	{
		sleep(2);
		print_level(SV_WARN,"Change Network config, reconncect server!\n");
		//SV_DVR_NETWORK_Reconnect();
	}

}

void CMS_SERVER::processOperate(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	print_level(SV_DEBUG,"Get SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_SET_CONFIG_REQ\n");
	char *pJson = pData;
    char pOut[1024];
	printf("get cmd:%s\n", pJson);

	cJSON *json, *module;
	ostringstream ostrReturn;
	SV_BOOL bResult = SV_FALSE, bReconnect = SV_FALSE;

	sint32 s32Ret = JSON_HDL_CmsOperate(pData, pOut);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "JSON_HDL_CMS_OPERATE failed. [err: %d]\n", s32Ret);
		bResult=SV_FALSE;
    }else{
		bResult=SV_TRUE;
    }
    ostrReturn << pOut;

	sendOperateResultStr(header, bResult, ostrReturn.str());
	if(bReconnect)
	{
		sleep(2);
		print_level(SV_WARN,"Change Network config, reconncect server!\n");
		//SV_DVR_NETWORK_Reconnect();
	}

}

void CMS_SERVER::sendJsonResultHeader(SK_HEADER header)
{
	SK_HEADER sendHeader = {0};
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_SERVER_TO_DVR_UPDATE_CONFIG_RSP, 0, header.ullSrcId);
	controlLink.addDataToList((char *)&sendHeader, sizeof(SK_HEADER));
}

void CMS_SERVER::sendJsonResultStr(SK_HEADER header, SV_BOOL bResult, string str)
{
	cJSON *root;
	char *out;
	root=cJSON_CreateObject();
	cJSON_AddItemToObject(root, "Result",   cJSON_CreateBool(bResult));
	cJSON_AddItemToObject(root, "Reason",   cJSON_CreateString(str.c_str()));
	out=cJSON_Print(root);
	print_level(SV_DEBUG,"%s\n",out);

	SK_HEADER sendHeader;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_SET_CONFIG_RSP, strlen(out)+1, header.ullSrcId);
	controlLink.addDataToList(&sendHeader, out, strlen(out)+1);

	cJSON_Delete(root);
	free(out);
}

void CMS_SERVER::sendOperateResultStr(SK_HEADER header, SV_BOOL bResult, string str)
{
	cJSON *root;
	char *out;
	root=cJSON_CreateObject();
	cJSON_AddItemToObject(root, "Result",   cJSON_CreateBool(bResult));
	cJSON_AddItemToObject(root, "Reason",   cJSON_CreateString(str.c_str()));
	out=cJSON_Print(root);
	print_level(SV_DEBUG,"%s\n",out);

	SK_HEADER sendHeader;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_REMOTE_OPERATE_RSP, strlen(out)+1, header.ullSrcId);
	controlLink.addDataToList(&sendHeader, out, strlen(out)+1);

	cJSON_Delete(root);
	free(out);
}

void CMS_SERVER::processAlarmUploadRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	SKAlarmUploadRsp* pAlarmUploadRsp = (SKAlarmUploadRsp*)pData;
	SV_NETWORK_STATE::getInstance()->setRcvAlarmId((const char *)pAlarmUploadRsp->szAlarmId);

	print_level(SV_DEBUG,"szAlarmId:%s\n", pAlarmUploadRsp->szAlarmId);
	SV_DVR_DelofflineInfo("test", OFFLINE_ALARM_TYPE);
}

void CMS_SERVER::processLogUploadRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	SKLogUploadRsp* pLogUploadRsp = (SKLogUploadRsp*)pData;

	print_level(SV_DEBUG, "uiLogId: %d\n", pLogUploadRsp->uiLogId);
}

void CMS_SERVER::processCarStateInfoRsp(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	//print_level(SV_DEBUG,"CMS Client FileData Rsp: 0x%x\n", header.usCode);
    SKDeviceInfoRsp *pstDeviceInfoRsp = (SKDeviceInfoRsp*)pData;
	//print_level(SV_DEBUG,"CMS Client FileData Rsp id: %lld\n", pstDeviceInfoRsp->ullDeviceInfoId);

    /* ID为1234时表示在线上传，不需要删除离线文件 */
    if (pstDeviceInfoRsp->ullDeviceInfoId == 1234)
    {
        return;
    }
    else
    {
	    SV_NETWORK_STATE::getInstance()->setRcvCarStateId(pstDeviceInfoRsp->ullDeviceInfoId);
        SV_DVR_DelOfflineCarStateInfo(OFFLINE_CARSTATE_TYPE);
    }
}

void CMS_SERVER::processFirmwareData(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	sint32 s32Fd = -1;
	char szFileName[COMMON_NAME_LEN] = {0};
	//print_level(SV_DEBUG,"path:%s\n", SV_DVR_STORAGE_getUpgradePath());
	snprintf(szFileName, COMMON_NAME_LEN, "%s/temp.update", szTempUpgradePath);
	UpdateFirmwareDataReq* FirmwareData = (UpdateFirmwareDataReq*)pData;
	sint32 s32Ret;
	static sint32 s32Count;
	s32Count++;
    if(s32Count%10 == 0)
    {
		print_level(SV_DEBUG,"usFileSeq:%u\n", FirmwareData->usFileSeq);
    }

	print_level(SV_DEBUG,"usFileSeq:%u offset:%u\n", FirmwareData->usFileSeq, FirmwareData->usBuffOffSet);


	SV_NETWORK_STATE::getInstance()->reflashUpdateTime();

	if (!SV_NETWORK_STATE::getInstance()->getPwrOffFlag() && SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
	{
		if(FirmwareData->usBuffOffSet == 0)
		{
			s32Fd = open(szFileName, O_RDWR|O_CREAT|O_TRUNC);
		}
		else
		{
			s32Fd = open(szFileName, O_RDWR|O_CREAT);
		}

		SV_DVR_COMMON_CloseEvec(s32Fd);
		if(s32Fd < 0)
		{
			print_level(SV_ERROR,"open file fail! fd:%d, %s\n", s32Fd, strerror(errno));
		}
		else
		{
			s32Ret=lseek(s32Fd, FirmwareData->usBuffOffSet, SEEK_CUR);
			s32Ret=write(s32Fd, FirmwareData->cData, FirmwareData->usDataLen);
			fsync(s32Fd);
			close(s32Fd);
		}
	}
    else
    {
        print_level(SV_WARN, "power off or no sdcard!!!\n");
        return;
    }
	SK_HEADER sendHeader;
	UpdateFirmwareDataRsp stUpdateRsp;
	stUpdateRsp.ullSessionId = FirmwareData->ullSessionId;
	stUpdateRsp.usFileSeq = FirmwareData->usFileSeq;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_DATA_RSP, sizeof(UpdateFirmwareDataRsp), header.ullSrcId);

//	print_level(SV_DEBUG,"before send!\n");
	controlLink.sendToServer(&sendHeader, (char *)&stUpdateRsp, sizeof(UpdateFirmwareDataRsp));
//	print_level(SV_DEBUG,"after send!\n");


}

void CMS_SERVER::processFirmwareResult(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	char szOldName[COMMON_NAME_LEN] = {0};
	char szNewName[COMMON_NAME_LEN] = {0};
	snprintf(szOldName, COMMON_NAME_LEN, "%s/temp.update", szTempUpgradePath);
	snprintf(szNewName, COMMON_NAME_LEN, "%s/%s", szTempUpgradePath, szUpdateFileName);
	UpdateFirmwareResultInform* pStResultInform = (UpdateFirmwareResultInform*)pData;
	SK_HEADER sendHeader;
	UpdateFirmwareResultRsp stResultRsp = {0};
	stResultRsp.ullSessionId = pStResultInform->ullSessionId;

	print_level(SV_DEBUG,"ucResult:%u\n", pStResultInform->common_rsp.ucResult);
	print_level(SV_DEBUG,"szOldName:%s szNewName:%s\n", szOldName, szNewName);
	SV_NETWORK_STATE::getInstance()->reflashUpdateTime();

	if( pStResultInform->common_rsp.ucResult == 0 ) //success
	{
		//check md5
		if( SV_COMMON_checkMd5(szOldName, szMd5) == 0 )
		{
			print_level(SV_DEBUG,"checkMd5 success!\n");
			//check md5 success
			if( rename(szOldName, szNewName) < 0 )
			{
			    print_level(SV_ERROR,"rename error :%d. :%s\n", errno, strerror(errno));
			}

			//send success to client
			stResultRsp.common_rsp.ucResult = 0;
			snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "md5 success!");
			SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_RESULT_RSP, sizeof(InformDvrRemoteUpdateRsp), header.ullSrcId);
			controlLink.addDataToList(&sendHeader, (char *)&stResultRsp, sizeof(InformDvrRemoteUpdateRsp));

			sleep(2);
			SAFE_SV_System("sync");
		}
		else
		{
		    print_level(SV_ERROR,"checkMd5 fail! szMd5:%s\n", szMd5);
			unlink(szOldName);
			//send fail to client
			stResultRsp.common_rsp.ucResult = 1;
			snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "Check md5 fail!");
		}
	}
	else
	{
		//取消删除，用于
		//delete update file
		unlink(szOldName);

		//send success to client
		stResultRsp.common_rsp.ucResult = 1;
		snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "Client cancel send file!");
	}

	SV_NETWORK_STATE::getInstance()->setRemoteUpdateFlag(SV_FALSE);
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_RESULT_RSP, sizeof(InformDvrRemoteUpdateRsp), header.ullSrcId);
	controlLink.addDataToList(&sendHeader, (char *)&stResultRsp, sizeof(InformDvrRemoteUpdateRsp));
	if(stResultRsp.common_rsp.ucResult == 0)
	{
		//inform gui reboot
		//_DVR_Reboot();
		sint32 s32Ret = SV_COMMON_system("safereboot");
		if (0 != s32Ret)
		{
		    print_level(SV_ERROR,"cmd: reboot failed.\n");
		}
	}
}

void CMS_SERVER::processConfigData(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	sint32 s32Ret = 0;
    sint32 s32Fd = -1;
	char szFileName[COMMON_NAME_LEN] = {0};
	UpdateFirmwareDataReq* FirmwareData = (UpdateFirmwareDataReq*)pData;

	SV_NETWORK_STATE::getInstance()->reflashUpdateTime();
    strcpy(szFileName, HTTP_CONFIG_STORE_TMP_PATH);
	print_level(SV_DEBUG, "path:%s\n", szFileName);
	print_level(SV_DEBUG, "usFileSeq:%u offset:%u\n", FirmwareData->usFileSeq, FirmwareData->usBuffOffSet);

	if (FirmwareData->usBuffOffSet == 0)
	{
		s32Fd = open(szFileName, O_RDWR|O_CREAT|O_TRUNC);
	}
	else
	{
		s32Fd = open(szFileName, O_RDWR|O_CREAT);
	}

	SV_DVR_COMMON_CloseEvec(s32Fd);
	if(s32Fd < 0)
	{
		print_level(SV_ERROR,"open file fail! fd:%d, %s\n", s32Fd, strerror(errno));
	}
	else
	{
		s32Ret = lseek(s32Fd, FirmwareData->usBuffOffSet, SEEK_CUR);
		s32Ret = write(s32Fd, FirmwareData->cData, FirmwareData->usDataLen);
		fsync(s32Fd);
		close(s32Fd);
	}

	SK_HEADER sendHeader;
	UpdateFirmwareDataRsp stUpdateRsp;
	stUpdateRsp.ullSessionId = FirmwareData->ullSessionId;
	stUpdateRsp.usFileSeq = FirmwareData->usFileSeq;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_DATA_RSP, sizeof(UpdateFirmwareDataRsp), header.ullSrcId);

	controlLink.sendToServer(&sendHeader, (char *)&stUpdateRsp, sizeof(UpdateFirmwareDataRsp));
}

void CMS_SERVER::processConfigResult(SK_HEADER header, char *pData, uint32 u32DataSize)
{
    sint32 s32Ret = 0, s32Fd = -1;
    char szCmd[128] = {0};
    char szJsonBody[10*1024] = {0};
    char szJsonOut[512];
	UpdateFirmwareResultInform* pStResultInform = (UpdateFirmwareResultInform*)pData;
	SK_HEADER sendHeader = {0};
	UpdateFirmwareResultRsp stResultRsp = {0};
	stResultRsp.ullSessionId = pStResultInform->ullSessionId;

    char szOldName[COMMON_NAME_LEN] = {0};
	char szNewName[COMMON_NAME_LEN] = {0};
    strcpy(szOldName, HTTP_CONFIG_STORE_TMP_PATH);
    strcpy(szNewName, HTTP_CONFIG_STORE_PATH);

	print_level(SV_DEBUG,"ucResult:%u\n", pStResultInform->common_rsp.ucResult);
	print_level(SV_DEBUG,"szOldName:%s szNewName:%s\n", szOldName, szNewName);
	SV_NETWORK_STATE::getInstance()->reflashUpdateTime();

	if (pStResultInform->common_rsp.ucResult == 0) //success
	{
		//check md5
		if (SV_COMMON_checkMd5(szOldName, szMd5) == 0)
		{
			print_level(SV_DEBUG,"checkMd5 success!\n");
			//check md5 success
			if (rename(szOldName, szNewName) < 0)
			{
			    print_level(SV_ERROR,"rename error :%d. :%s\n", errno, strerror(errno));
			}

			//send success to client
			stResultRsp.common_rsp.ucResult = 0;
			snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "md5 success!");
			SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_RESULT_RSP, sizeof(InformDvrRemoteUpdateRsp), header.ullSrcId);
			controlLink.addDataToList(&sendHeader, (char *)&stResultRsp, sizeof(InformDvrRemoteUpdateRsp));
			SAFE_SV_System("sync");
		}
		else
		{
		    print_level(SV_ERROR,"checkMd5 fail! szMd5:%s\n", szMd5);
			unlink(szOldName);
			//send fail to client
			stResultRsp.common_rsp.ucResult = 1;
			snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "Check md5 fail!");
		}
	}
	else
	{
		//取消删除，用于
		//delete update file
		unlink(szOldName);

		//send success to client
		stResultRsp.common_rsp.ucResult = 1;
		snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "Client cancel send file!");
	}

	SV_NETWORK_STATE::getInstance()->setRemoteUpdateFlag(SV_FALSE);
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_RESULT_RSP, sizeof(InformDvrRemoteUpdateRsp), header.ullSrcId);
	controlLink.addDataToList(&sendHeader, (char *)&stResultRsp, sizeof(InformDvrRemoteUpdateRsp));

    if (0 != stResultRsp.common_rsp.ucResult)
    {
        print_level(SV_ERROR, "import config file error!\n");
        return;
    }

    sprintf(szCmd, "tar -zxvf %s -C /var/config", HTTP_CONFIG_STORE_PATH);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: %s failed.\n", szCmd);
        goto exit;
    }

    s32Fd = open(HTTP_CONFIG_JSON_PATH, O_RDONLY);
    if (s32Fd <= 0)
    {
        print_level(SV_ERROR, "open %s failed.\n", HTTP_CONFIG_JSON_PATH);
        goto exit;
    }

    s32Ret = read(s32Fd, szJsonBody, 10240);
    if (s32Ret <= 0)
    {
        print_level(SV_ERROR, "read %s failed.\n", HTTP_CONFIG_JSON_PATH);
        goto exit;
    }
    print_level(SV_INFO, "import config:\n%s\n", szJsonBody);

    s32Ret = JSON_HDL_SetConfig(szJsonBody, szJsonOut);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "JSON_HDL_SetConfig failed.\n");
        goto exit;
    }
    else
    {
        print_level(SV_INFO, "import config success.\n");
    }

    close(s32Fd);
    s32Fd = -1;

exit:
    if (-1 != s32Fd)
    {
        close(s32Fd);
        s32Fd = -1;
    }
    unlink(HTTP_CONFIG_JSON_PATH);
    unlink(HTTP_CONFIG_STORE_PATH);
    return;
}

void CMS_SERVER::processFaceIdData(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	sint32 s32Ret = 0;
    sint32 s32Fd = -1;
	char szFileName[COMMON_NAME_LEN] = {0};
	UpdateFirmwareDataReq* FirmwareData = (UpdateFirmwareDataReq*)pData;

	SV_NETWORK_STATE::getInstance()->reflashUpdateTime();
    strcpy(szFileName, HTTP_FACEID_TMP_PATH);
	print_level(SV_DEBUG, "path:%s\n", szFileName);
	print_level(SV_DEBUG, "usFileSeq:%u offset:%u\n", FirmwareData->usFileSeq, FirmwareData->usBuffOffSet);

	if (FirmwareData->usBuffOffSet == 0)
	{
		s32Fd = open(szFileName, O_RDWR|O_CREAT|O_TRUNC);
	}
	else
	{
		s32Fd = open(szFileName, O_RDWR|O_CREAT);
	}

	SV_DVR_COMMON_CloseEvec(s32Fd);
	if(s32Fd < 0)
	{
		print_level(SV_ERROR,"open file fail! fd:%d, %s\n", s32Fd, strerror(errno));
	}
	else
	{
		s32Ret = lseek(s32Fd, FirmwareData->usBuffOffSet, SEEK_CUR);
		s32Ret = write(s32Fd, FirmwareData->cData, FirmwareData->usDataLen);
		fsync(s32Fd);
		close(s32Fd);
	}

	SK_HEADER sendHeader;
	UpdateFirmwareDataRsp stUpdateRsp;
	stUpdateRsp.ullSessionId = FirmwareData->ullSessionId;
	stUpdateRsp.usFileSeq = FirmwareData->usFileSeq;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_DATA_RSP, sizeof(UpdateFirmwareDataRsp), header.ullSrcId);

	controlLink.sendToServer(&sendHeader, (char *)&stUpdateRsp, sizeof(UpdateFirmwareDataRsp));
}

void CMS_SERVER::processFaceIdResult(SK_HEADER header, char *pData, uint32 u32DataSize)
{
    sint32 s32Ret = 0, s32Fd = -1;
    char szCmd[128] = {0};
    char szJsonBody[10*1024] = {0};
    char szJsonOut[512];
	UpdateFirmwareResultInform* pStResultInform = (UpdateFirmwareResultInform*)pData;
	SK_HEADER sendHeader = {0};
	UpdateFirmwareResultRsp stResultRsp = {0};
	stResultRsp.ullSessionId = pStResultInform->ullSessionId;

    char szOldName[COMMON_NAME_LEN] = {0};
	char szNewName[COMMON_NAME_LEN] = {0};
    strcpy(szOldName, HTTP_FACEID_TMP_PATH);
    strcpy(szNewName, HTTP_FACEID_PATH);

	print_level(SV_DEBUG,"ucResult:%u\n", pStResultInform->common_rsp.ucResult);
	print_level(SV_DEBUG,"szOldName:%s szNewName:%s\n", szOldName, szNewName);
	SV_NETWORK_STATE::getInstance()->reflashUpdateTime();

	if (pStResultInform->common_rsp.ucResult == 0) //success
	{
		//check md5
		if (SV_COMMON_checkMd5(szOldName, szMd5) == 0)
		{
			print_level(SV_DEBUG,"checkMd5 success!\n");
			//check md5 success
			if (rename(szOldName, szNewName) < 0)
			{
			    print_level(SV_ERROR,"rename error :%d. :%s\n", errno, strerror(errno));
			}

			//send success to client
			stResultRsp.common_rsp.ucResult = 0;
			snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "md5 success!");
			SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_RESULT_RSP, sizeof(InformDvrRemoteUpdateRsp), header.ullSrcId);
			controlLink.addDataToList(&sendHeader, (char *)&stResultRsp, sizeof(InformDvrRemoteUpdateRsp));
			SAFE_SV_System("sync");
		}
		else
		{
		    print_level(SV_ERROR,"checkMd5 fail! szMd5:%s\n", szMd5);
			unlink(szOldName);
			//send fail to client
			stResultRsp.common_rsp.ucResult = 1;
			snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "Check md5 fail!");
		}
	}
	else
	{
		//取消删除，用于
		//delete update file
		unlink(szOldName);

		//send success to client
		stResultRsp.common_rsp.ucResult = 1;
		snprintf((char*)stResultRsp.common_rsp.szReason, SK_EXTERN_FIELD_LENGTH, "Client cancel send file!");
	}

	SV_NETWORK_STATE::getInstance()->setRemoteUpdateFlag(SV_FALSE);
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_UPDATE_FIRMWARE_RESULT_RSP, sizeof(InformDvrRemoteUpdateRsp), header.ullSrcId);
	controlLink.addDataToList(&sendHeader, (char *)&stResultRsp, sizeof(InformDvrRemoteUpdateRsp));

    if (0 != stResultRsp.common_rsp.ucResult)
    {
        print_level(SV_ERROR, "import faceId file error!\n");
        return;
    }

    sprintf(szCmd, "tar -xzvf %s -C /root", HTTP_FACEID_PATH);
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: %s failed.\n", szCmd);
        goto exit;
    }

    print_level(SV_INFO, "import FaceId success.\n");

exit:
    unlink(HTTP_FACEID_PATH);
    return;
}


void CMS_SERVER::processRemoteFile(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	uint8 u8Count = 0;
	char szCmd[128] = {0};
    char szStorage[32] = {0};
	InformDvrRemoteUpdateReq* pRemoteUpdate = (InformDvrRemoteUpdateReq*)pData;

	print_level(SV_DEBUG,"ullSessionId:%llu\n", pRemoteUpdate->ullSessionId);
	print_level(SV_DEBUG,"ucFileType:%d\n", pRemoteUpdate->ucFileType);
	print_level(SV_DEBUG,"uiFileSize:%u\n", pRemoteUpdate->uiFileSize);
	print_level(SV_DEBUG,"filename:%s\n", pRemoteUpdate->szStrFilename);
	strncpy(szUpdateFileName, (char *)pRemoteUpdate->szStrFilename, 64);
	memcpy(szMd5, (char *)pRemoteUpdate->szStrCheckSum, 32);
	print_level(SV_DEBUG,"szMd5:%s\n", szMd5);

    if (NULL != strstr(szUpdateFileName, "config.tar.gz"))
    {
        enFileType = PRO_FILE_TYPE_CONFIG;
    }
    else if (NULL != strstr(szUpdateFileName, "FaceId.tar.gz"))
    {
        enFileType = PRO_FILE_TYPE_FACEID;
    }
    else
    {
        enFileType = PRO_FILE_TYPE_UPGRADE;
    }

	SK_HEADER sendHeader;
	InformDvrRemoteUpdateRsp stUpdateRsp = {0};
	stUpdateRsp.ullSessionId = pRemoteUpdate->ullSessionId;
	SV_NETWORK_STATE::getInstance()->setUpdateInquireStatus(UPDATE_INQUIRE_IDLE);

	if( SV_NETWORK_STATE::getInstance()->getRemoteUpdateFlag() )
	{
		strncpy((char *)stUpdateRsp.common_rsp.szReason, "Dvr ugrading!", SK_EXTERN_FIELD_LENGTH);
		stUpdateRsp.common_rsp.ucResult = 1;
	}
	else
	{
		switch (enFileType)
		{
            case PRO_FILE_TYPE_UPGRADE:
                if (SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStorage))
        		{
        			stUpdateRsp.common_rsp.ucResult = 0;
        			strncpy((char *)stUpdateRsp.common_rsp.szReason, "Ready to upgrade!", SK_EXTERN_FIELD_LENGTH);
        			SV_NETWORK_STATE::getInstance()->setRemoteUpdateFlag(SV_TRUE);
        			SV_NETWORK_STATE::getInstance()->reflashUpdateTime();
        			strncpy(szTempUpgradePath, szStorage, 256);
        			char szFileName[COMMON_NAME_LEN] = {0};
        			sint32 s32FileSize = -1;
        			snprintf(szFileName, COMMON_NAME_LEN, "%s/temp.update", szTempUpgradePath);
        			s32FileSize = SV_COMMON_getFileSize(szFileName);
        			if(s32FileSize > 0)
        			{
        				stUpdateRsp.uiHaveAcceptedFileSize = (s32FileSize < 480*1024) ? 0 : s32FileSize-480*1024;
        			}
        			print_level(SV_DEBUG,"stUpdateRsp.uiHaveAcceptedFileSize:%u, s32FileSize: %d\n", stUpdateRsp.uiHaveAcceptedFileSize, s32FileSize);

        		}
        		else
        		{
        			strncpy((char *)stUpdateRsp.common_rsp.szReason, "No space!", SK_EXTERN_FIELD_LENGTH);
        			stUpdateRsp.common_rsp.ucResult = 2;  //?????
        		}
                break;

            case PRO_FILE_TYPE_CONFIG:
        	    SV_NETWORK_STATE::getInstance()->setRemoteUpdateFlag(SV_TRUE);
                if (!COMMON_IsPathExist(HTTP_CONFIG_DIR))
                {
                    print_level(SV_WARN, "path: %s is not exist!\n", HTTP_CONFIG_DIR);
                    sprintf(szCmd, "mkdir -p %s", HTTP_CONFIG_DIR);
                    SAFE_SV_System(szCmd);
                }

                unlink(HTTP_CONFIG_STORE_TMP_PATH);
                unlink(HTTP_CONFIG_STORE_PATH);
                sprintf(szCmd, "rm -rf %s/*", HTTP_CONFIG_DIR);
                SAFE_SV_System(szCmd);

                stUpdateRsp.uiHaveAcceptedFileSize = 0;
                stUpdateRsp.common_rsp.ucResult = 0;
                strncpy((char *)stUpdateRsp.common_rsp.szReason, "Ready to import config!", SK_EXTERN_FIELD_LENGTH);
                break;

            case PRO_FILE_TYPE_FACEID:
                SV_NETWORK_STATE::getInstance()->setRemoteUpdateFlag(SV_TRUE);

                unlink(HTTP_FACEID_TMP_PATH);
                unlink(HTTP_FACEID_PATH);

                stUpdateRsp.uiHaveAcceptedFileSize = 0;
                stUpdateRsp.common_rsp.ucResult = 0;
                strncpy((char *)stUpdateRsp.common_rsp.szReason, "Ready to import faceID!", SK_EXTERN_FIELD_LENGTH);
                break;

            default:
                break;
        }
	}

	char *pSendData = (char *)malloc(sizeof(InformDvrRemoteUpdateRsp));
	memcpy(pSendData, &stUpdateRsp, sizeof(InformDvrRemoteUpdateRsp));
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_INFORM_DVR_REMOTE_UPDATE_RSP, sizeof(InformDvrRemoteUpdateRsp), header.ullSrcId);
	controlLink.addDataToList(&sendHeader, pSendData, sizeof(InformDvrRemoteUpdateRsp));

	free(pSendData);

	SV_NETWORK_STATE::getInstance()->setUpdateInquireStatus(UPDATE_INQUIRE_IDLE);

}

void CMS_SERVER::processRemoteData(SK_HEADER header, char *pData, uint32 u32DataSize)
{
    switch (enFileType)
	{
        case PRO_FILE_TYPE_UPGRADE:
            processFirmwareData(header, pData, u32DataSize);
            break;

        case PRO_FILE_TYPE_CONFIG:
            processConfigData(header, pData, u32DataSize);
            break;

        case PRO_FILE_TYPE_FACEID:
            processFaceIdData(header, pData, u32DataSize);
            break;

        default:
            print_level(SV_ERROR, "not support file type: %d\n", enFileType);
            break;
    }
}

void CMS_SERVER::processRemoteResult(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	switch (enFileType)
	{
        case PRO_FILE_TYPE_UPGRADE:
            processFirmwareResult(header, pData, u32DataSize);
            break;

        case PRO_FILE_TYPE_CONFIG:
            processConfigResult(header, pData, u32DataSize);
            break;

        case PRO_FILE_TYPE_FACEID:
            processFaceIdResult(header, pData, u32DataSize);
            break;

        default:
            print_level(SV_ERROR, "not support file type: %d\n", enFileType);
            break;
    }
}

void CMS_SERVER::processGetServerInfo(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	cJSON *json, *param, *pHttpUpdate;
	uint8 u8DeviceType = 0;
	char *pVersion = SV_NULL;
	sint32 s32IfboxType =0;
	print_level(SV_DEBUG,"processGetServerInfo:%s", pData);

	json = cJSON_Parse(pData);
	if (!json)
	{
	print_level(SV_ERROR,"Error before: [%s]\n",cJSON_GetErrorPtr());
	}
	else
	{
		param = cJSON_GetObjectItem(json, "FileTransferPort");
		if(param != SV_NULL)
		{
			u32FilePort = param->valueint;
			pFileTransfer->setPort(u32FilePort);
			print_level(SV_DEBUG,"Get filetransfter port:%d\n", u32FilePort);
		}

		param = cJSON_GetObjectItem(json, "FileSavePort");
		if(param != SV_NULL)
		{
			print_level(SV_DEBUG,"Get FileSave port:%d\n", param->valueint);
			pFilePrivateUpload->setPort(param->valueint);
			pFilePrivateUpload->setPort(param->valueint);
		}

		param = cJSON_GetObjectItem(json, "DriverIdVersion");
		if(param != SV_NULL)
		{
			print_level(SV_DEBUG,"Get DriverIdVersion:%d\n", param->valueint);
			SV_NETWORK_STATE::getInstance()->setDriveIdVersion(param->valueint);
		}

		pHttpUpdate = cJSON_GetObjectItem(json, "UpdateFileInfo");
		if(pHttpUpdate != SV_NULL)
		{
			param = cJSON_GetObjectItem(pHttpUpdate, "Version");
			if(param != SV_NULL)
			{
				print_level(SV_DEBUG,"Version:%s\n", param->valuestring);
				pVersion = param->valuestring;
				//SV_NETWORK_STATE::getInstance()->set_httpupgrade_version(pVersion);
			}

			param = cJSON_GetObjectItem(pHttpUpdate, "DeviceType");
			if(param != SV_NULL)
			{
				print_level(SV_DEBUG,"DeviceType:%d\n", param->valueint);
				u8DeviceType = param->valueint;
			}
		}

		param = cJSON_GetObjectItem(json, "AlarmFenseVersion");
		if(param != SV_NULL)
		{
			print_level(SV_DEBUG,"Get AlarmFenseVersion:%d\n", param->valueint);
			pControlThread->getFense(param->valueint);
		}
	}

	if(SV_NETWORK_HTTPUPGRADE::isNeedtoUpdate(pVersion, u8DeviceType))
	{
		pHttpUpgrade = new SV_NETWORK_HTTPUPGRADE(SV_NETWORK_DVRINFO::getInstance(), SV_NETWORK_STATE::getInstance(), pData);
	}
#if 0
	if( SV_NETWORK_HTTPUPGRADE::isIfboxNeedtoUpdate(pData, &s32IfboxType) )
	{
		print_level(SV_DEBUG,"s32IfboxType:%d need to update\n", s32IfboxType);
		pHttpUpgrade = new SV_NETWORK_HTTPUPGRADE(SV_NETWORK_DVRINFO::getInstance(), SV_NETWORK_STATE::getInstance(), pData, s32IfboxType);
	}
#endif
	cJSON_Delete(json);
	//pControlThread->getDriverIdList();
}

void CMS_SERVER::processSnapPic(SK_HEADER header, char *pData, uint32 u32DataSize)
{
    SKSnapPictureReq * pSnapPic = (SKSnapPictureReq *)pData;
    char *pSendData;
    char *szFilePic = "/var/snap/snap0.jpeg";
    char buf[200*1024] = {0};

    sint32 s32Fd = open(szFilePic, O_RDONLY);
    if (s32Fd < 0)
    {
     	print_level(SV_ERROR,"open file: %s failed! [err: %s]\n", szFilePic, strerror(errno));
		return;
    }

    sint32 s32Ret = read(s32Fd, buf, 200*1024);
    if(s32Ret < 0)
     	print_level(SV_ERROR,"read file: %s failed! [err: %s]\n", szFilePic, strerror(errno));
    else
        print_level(SV_DEBUG,"read file: %s size[%d]\n", szFilePic, s32Ret);

    SK_HEADER sendHeader;
	SKSnapPictureRsp picRsp = {0};
	picRsp.usChannelId = pSnapPic->usChannelId;
	picRsp.usHeight    = 704;//Adas_config_interface::GetInstance()->getJpegChnHeight(-1);
	picRsp.usWidth     = 480;//Adas_config_interface::GetInstance()->getJpegChnWidth(-1);
	picRsp.ullPts      = 0;
	picRsp.uiFileLen   = s32Ret;

	uint32 u32SendDataLen = sizeof(SKSnapPictureRsp) + picRsp.uiFileLen;
	pSendData = (char *)malloc(u32SendDataLen);
	memcpy(pSendData, &picRsp, sizeof(SKSnapPictureRsp));
    memcpy(pSendData+sizeof(SKSnapPictureRsp), buf, s32Ret);

	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_SNAP_PICTURE_RSP, u32SendDataLen, header.ullSrcId);
	controlLink.addDataToList(&sendHeader, pSendData, u32SendDataLen);

	free(pSendData);
    close(s32Fd);
}

sint32 CMS_SERVER::getFileList(const char *pDirPath,uint32 fileType,char *szBeginTime,char *szEndTime,cJSON* pfileList,uint32 *fileNumber)
{

	struct dirent* file = NULL;
	DIR *pDir;
	char szPath[STORAGE_FULLPATH_LEN] = {0};
	sint32 s32Ret = SV_FAILURE;
	SV_BOOL bHaveDir = SV_FALSE;
	cJSON *fileInfo;
	char szFileTime[20]={0};

	if(!(pDir=opendir(pDirPath)))
	{
		print_level(SV_WARN,"Open dir:%s error!\n", pDirPath);
		return SV_FAILURE;
	}
	else
	{
		//print_level(SV_INFO,"Open dir:%s successfully!\n", pDirPath);
	}

	while( (file = readdir(pDir)) != NULL )
	{
		if(strcmp(file->d_name,".")==0 || strcmp(file->d_name,"..")==0)
		{
			continue;
		}

		if(file->d_type & DT_DIR)
		{
			snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s/", pDirPath, file->d_name);
			print_level(SV_DEBUG,"dir:%s \n",szPath);
			if( getFileList(szPath, fileType,szBeginTime,szEndTime,pfileList,fileNumber) == SV_SUCCESS)
			{
				s32Ret = SV_SUCCESS;
				break;
			}
		}
		else
		{    //2022-04-28 23:59:59
			sint32 s32Year,s32Month,s32Day,s32Hour,s32Minute,s32Second;
			char szFileType[5];
            switch (fileType)
            {
                case 0:
                    sscanf(file->d_name, "log_%04d%02d%02d%02d%02d%02d.%s",&s32Year,&s32Month,&s32Day,&s32Hour,&s32Minute,&s32Second,szFileType);
    				sprintf(szFileTime,"%04d-%02d-%02d %02d:%02d:%02d",s32Year,s32Month,s32Day,s32Hour,s32Minute,s32Second);
    				snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", pDirPath, file->d_name);
                    break;
                case 2:
                case 3:
                    sscanf(file->d_name, "%04d%02d%02d%02d%02d%02d",&s32Year,&s32Month,&s32Day,&s32Hour,&s32Minute,&s32Second);
    				sprintf(szFileTime,"%04d-%02d-%02d %02d:%02d:%02d",s32Year,s32Month,s32Day,s32Hour,s32Minute,s32Second);
    				snprintf(szPath, STORAGE_FULLPATH_LEN, "%s", file->d_name);//服务器规定上传日志文件要文件的绝对路径，报警文件要文件名
                    break;

                default:
                    break;
            }

			if(strcmp(szFileTime,szBeginTime)>0 && strcmp(szFileTime,szEndTime)<0)
			{

				fileInfo = cJSON_CreateObject();
		        if (NULL == fileInfo)
		        {
		            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
		            return SV_FAILURE;
		        }
		        cJSON_AddItemToArray(pfileList, fileInfo);
		        cJSON_AddItemToObject(fileInfo, "FileName", cJSON_CreateString(szPath)); //绝对路径
		        cJSON_AddItemToObject(fileInfo, "FileType", cJSON_CreateNumber(fileType));
				(*fileNumber)++;
			 }
		}
	}
	closedir(pDir);
	return s32Ret;
}

void CMS_SERVER::processFileList(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	SKGetDeviceFileList *pDeviceFileListRsp = (SKGetDeviceFileList*)pData;
	print_level(SV_DEBUG,"filetype=%d channel=%d begintime=%s endtime=%s \n",pDeviceFileListRsp->usFileType,pDeviceFileListRsp->usChannelIdMask,\
		pDeviceFileListRsp->szBeginTime,pDeviceFileListRsp->szEndTime);

#if defined(BOARD_ADA32N1)
	return;
#endif

	sint32 s32Ret;
	cJSON *root;
	cJSON *pstFileList;
	cJSON *pstLogTarFile;
	cJSON *pstFaceIdFile;
	char *out;
	uint32 u32FileNumber=0;
	uint32 u32FileType = pDeviceFileListRsp->usFileType;  // 0:log	1:All Record File  2:common RecordFile 3:Alarm RecordType
	char* szBeginTime = pDeviceFileListRsp->szBeginTime;
	char* szEndTime = pDeviceFileListRsp->szEndTime;
	char dirPath[128]={0};
    char szStoragePath[32] = {0};
    char szCmd[128] = {0};
    char szLogTarPath[128] = {0};
    char szFaceIdPath[128] = {0};
    char *pszVarLogFileName = "log_var.tar.gz";
    char *pszTfLogFileName = "log_tf.tar.gz";
    char *pszFaceIdFileName = "FaceId.tar.gz";
    SV_BOOL bStorageWritable = SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);

#if defined(BOARD_ADA32N1)
	bStorageWritable = SV_FALSE;//不支持sd存储，升级时bStorageWritable必须为SV_TRUE

#endif

	switch(u32FileType)
	{
		case 0:
			if (!bStorageWritable)
			{
				strcpy(dirPath, "/var/log/");
			    sprintf(szLogTarPath, "/tmp/%s", pszVarLogFileName);
    		    sprintf(szCmd, "tar -czvf %s %s", szLogTarPath, dirPath);
			    print_level(SV_INFO, "cmd: %s\n", szCmd);
    		    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
                    goto list_file;
                }

    		    sprintf(szCmd, "ln -sf %s /var/%s", szLogTarPath, pszVarLogFileName);
			    print_level(SV_INFO, "cmd: %s\n", szCmd);
    		    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    		    if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
                    goto list_file;
                }
                sprintf(szLogTarPath, "/var/%s", pszVarLogFileName);
    		}
			else
			{
				sprintf(dirPath, "%s/log/", szStoragePath);
			    sprintf(szLogTarPath, "/tmp/%s", pszTfLogFileName);
				sprintf(szCmd, "tar -czvf %s %s", szLogTarPath, dirPath);
			    print_level(SV_INFO, "cmd: %s\n", szCmd);
    		    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    		    if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
                    goto list_file;
                }

                sprintf(szCmd, "ln -sf %s /var/%s", szLogTarPath, pszTfLogFileName);
			    print_level(SV_INFO, "cmd: %s\n", szCmd);
    		    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    		    if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
                    goto list_file;
                }
                sprintf(szLogTarPath, "/var/%s", pszTfLogFileName);
	        }
			break;
        case 2:
			sprintf(dirPath, "%s/normal/", szStoragePath);
            break;
		case 3:
			sprintf(dirPath, "%s/alarm/", szStoragePath);
			break;
		case 6:
		    /* 将/root/ID路径下的文件压缩到/tmp/中 */
			strcpy(dirPath, "/root/");
		    sprintf(szFaceIdPath, "/tmp/%s", pszFaceIdFileName);
		    sprintf(szCmd, "tar -czvf %s -C %s ID", szFaceIdPath, dirPath);
		    print_level(SV_INFO, "cmd: %s\n", szCmd);
		    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
                goto list_file;
            }
            sprintf(szFaceIdPath, "/tmp/%s", pszFaceIdFileName);
            break;
		default:
			break;
	}

	root = cJSON_CreateObject();
	if (NULL == root)
	{
		print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
		return ;
	}

	pstFileList = cJSON_CreateArray();
	if (NULL == pstFileList)
	{
		print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
		cJSON_Delete(root);
		return ;
	}

    if (0 == u32FileType && 0 != strlen(szLogTarPath))
    {
        pstLogTarFile = cJSON_CreateObject();
        if (NULL == pstLogTarFile)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            return SV_FAILURE;
        }
        cJSON_AddItemToObject(pstLogTarFile, "FileName", cJSON_CreateString(szLogTarPath)); //绝对路径
        cJSON_AddItemToObject(pstLogTarFile, "FileType", cJSON_CreateNumber(u32FileType));
        cJSON_AddItemToArray(pstFileList, pstLogTarFile);
        u32FileNumber++;
    }
    if (6 == u32FileType&& 0 != strlen(szFaceIdPath))
    {
        pstFaceIdFile = cJSON_CreateObject();
        if (NULL == pstFaceIdFile)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            return SV_FAILURE;
        }
        cJSON_AddItemToObject(pstFaceIdFile, "FileName", cJSON_CreateString(szFaceIdPath)); //绝对路径
        cJSON_AddItemToObject(pstFaceIdFile, "FileType", cJSON_CreateNumber(u32FileType));
        cJSON_AddItemToArray(pstFileList, pstFaceIdFile);
        u32FileNumber++;
    }
list_file:
	s32Ret = getFileList(dirPath,u32FileType,szBeginTime,szEndTime,pstFileList,&u32FileNumber);
	cJSON_AddItemToObject(root, "FileNumber",  cJSON_CreateNumber(u32FileNumber));
	cJSON_AddItemToObject(root, "FileList", pstFileList);
	out=cJSON_Print(root);
	print_level(SV_DEBUG,"%s\n",out);

	SK_HEADER sendHeader;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_GET_FILE_LIST_RSP, strlen(out)+1, header.ullSrcId);
	controlLink.addDataToList(&sendHeader, out, strlen(out)+1);

	cJSON_Delete(root);
	free(out);

}


sint32 CMS_SERVER::createFileDataRsp(cJSON *pstfileReq,cJSON*pstfileRsp)
{
    sint32 s32Ret;
	uint32 u32SessionId;
	char szFileName[256]={0};
	uint32 u32FileType;
	uint64 u64FileOffset;
	cJSON *pstTmp=NULL;
	char szCheckSum[64] = {0};
	char szCmd[128] = {0};
	uint32 u32FileSize = 0;
	char szAlarmFilePath[256]={0};
	char *pstType=NULL;
	SV_MEDIA_FILE_NAME_PARAMS_ST stFileNameParams;

	if (NULL == pstfileReq || NULL == pstfileRsp)
    {
		print_level(SV_ERROR,"pstfileReq cJSON NULL.\n");
		return SV_FAILURE;
    }

	pstTmp = cJSON_GetObjectItemCaseSensitive(pstfileReq, "FileTransferSessionId");
    if (NULL != pstTmp)
    {
        u32SessionId = pstTmp->valueint;
    }
    pstTmp = cJSON_GetObjectItemCaseSensitive(pstfileReq, "FileName");
    if (NULL != pstTmp)
    {
        strcpy(szFileName,pstTmp->valuestring);
    }
    pstTmp = cJSON_GetObjectItemCaseSensitive(pstfileReq, "FileType"); //0:log,1:RecordFile
    if (NULL != pstTmp)
    {
        u32FileType = pstTmp->valueint;
    }
    pstTmp = cJSON_GetObjectItemCaseSensitive(pstfileReq, "FileOffset");
    if (NULL != pstTmp)
    {
        u64FileOffset = pstTmp->valueint;
    }

    // 0：log  1：recorderFile
    switch (u32FileType)
    {
        case 1:
            sscanf(szFileName, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
					,&stFileNameParams.s32Year\
					,&stFileNameParams.s32Month\
					,&stFileNameParams.s32Day\
					,&stFileNameParams.s32Hour\
					,&stFileNameParams.s32Minute\
					,&stFileNameParams.s32Second\
					,&stFileNameParams.cEventType\
					,&stFileNameParams.s32Duration\
					,&stFileNameParams.s32Size\
					,&stFileNameParams.s32Width\
					,&stFileNameParams.s32Height\
					,&stFileNameParams.s32FrameRate\
					,&stFileNameParams.s32BitRate\
					,&stFileNameParams.cPlateNum\
					,&stFileNameParams.s32ChNum\
					,&stFileNameParams.u64DeviceID\
					,&stFileNameParams.u8Flag\
					,&stFileNameParams.s32Msec\
					,&stFileNameParams.s32PreRecordMsec\
					,&stFileNameParams.cVersionNum\
					,&stFileNameParams.cCustomerNum\
					,&stFileNameParams.cTimeZone\
					,&stFileNameParams.cDST\
					,&stFileNameParams.cFileType);

    		if (0 != strlen(stFileNameParams.cEventType) && 0 != strlen(stFileNameParams.cFileType))
    		{
                if (strstr(stFileNameParams.cFileType, "mp4") || strstr(stFileNameParams.cFileType, "avi"))
                {
                    if (0 == strcmp(stFileNameParams.cEventType, "NM"))
                        SV_COMMON_getAlarmFileDir(UPLOAD_FILE_NORMAL_VIDEO, szFileName, szAlarmFilePath);
                    else
    			        SV_COMMON_getAlarmFileDir(UPLOAD_FILE_ALARM_VIDEO, szFileName, szAlarmFilePath);
                }
                else if (strstr(stFileNameParams.cFileType, "jpg"))
                {
                    SV_COMMON_getAlarmFileDir(UPLOAD_FILE_ALARM_PICTURE, szFileName, szAlarmFilePath);
                }
    		}
    		strcpy(szFileName,szAlarmFilePath);
    		break;

        default:
            break;
    }
	u32FileSize = SV_COMMON_getFileSize(szFileName);
	getFileMd5(szFileName,szCheckSum);


	pstTmp = cJSON_CreateObject();
	if(NULL == pstTmp)
	{
		print_level(SV_ERROR,"crate cJSON failed.\n");
		return SV_FAILURE;
	}
	cJSON_AddItemToArray(pstfileRsp, pstTmp);

	if (!SV_DVR_COMMON_IsFileExist(szFileName))
	{
		cJSON_AddItemToObject(pstTmp, "Result", cJSON_CreateBool(false));
		cJSON_AddItemToObject(pstTmp, "Reason", cJSON_CreateString("File not exit!"));
	}
	else
	{
		cJSON_AddItemToObject(pstTmp, "Result", cJSON_CreateBool(true));
		cJSON_AddItemToObject(pstTmp, "Reason", cJSON_CreateString("Success!"));
	}

	cJSON_AddItemToObject(pstTmp, "FileName", cJSON_CreateString(szFileName));
	cJSON_AddItemToObject(pstTmp, "FileType", cJSON_CreateNumber(u32FileType));
	cJSON_AddItemToObject(pstTmp, "FileSize", cJSON_CreateNumber(u32FileSize));
	cJSON_AddItemToObject(pstTmp, "FileTransferSessionId", cJSON_CreateNumber(u32SessionId));
	cJSON_AddItemToObject(pstTmp, "FileOffset", cJSON_CreateNumber(u64FileOffset));
	cJSON_AddItemToObject(pstTmp, "CheckSum", cJSON_CreateString(szCheckSum));

	pFileTransfer->setSendFile(szFileName, u32FileType, u64FileOffset,u32SessionId);//添加文件队列之后，才开始登录fileTransfer服务器

	return SV_SUCCESS;

}

void CMS_SERVER::processTransferFileDataReq(SK_HEADER header, char *pData, uint32 u32DataSize)
{
	print_level(SV_DEBUG,"CMS Client FileData Req:%s \n",pData);
	cJSON *rootReq=NULL;
	cJSON *rootRsp=NULL;
	cJSON *clientID=NULL;
	cJSON *fileInfo=NULL;
	cJSON *fileElement=NULL;
	cJSON *pstTmp=NULL;
	cJSON *pstfileDataRsp;
	char *out;
	uint64 u64CmsClientId;
	sint32 s32Ret;
	sint32 i;

	rootReq = cJSON_Parse(pData);
	clientID = cJSON_GetObjectItemCaseSensitive(rootReq, "CmsClientId");
	if(NULL != clientID)
	{
		u64CmsClientId = clientID->valueint;
	}
	pFileTransfer->setCmsSessionId(u64CmsClientId);

	fileInfo = cJSON_GetObjectItemCaseSensitive(rootReq, "FileDataReq");
 	fileElement = cJSON_GetArrayItem(fileInfo, i);

	rootRsp=cJSON_CreateObject();
	pstfileDataRsp=cJSON_CreateArray();
	s32Ret = createFileDataRsp(fileElement,pstfileDataRsp);
	cJSON_AddItemToObject(rootRsp, "FileDataRsp", pstfileDataRsp);
	out=cJSON_Print(rootRsp);
	print_level(SV_DEBUG,"%s\n",out);

	SK_HEADER sendHeader;
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&sendHeader, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_GET_FILE_LIST_RSP, strlen(out)+1, header.ullSrcId);
	controlLink.addDataToList(&sendHeader, out, strlen(out)+1);


	cJSON_Delete(rootReq);
	cJSON_Delete(rootRsp);
	free(out);

}

void CMS_SERVER::setTalkQueId(sint32 s32QueId)
{
	s32TalkQueId = s32QueId;
}

void CMS_SERVER::processFense(SK_HEADER header, char* pData, uint32 u32DataSize)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
	if(pData == NULL)
		return;

	print_level(SV_INFO,"cms client set new rule[%s]\n", pData);
	FENCE_SetRule(pData);

#endif
	return;
}

void CMS_SERVER::processOfflineInfo(SK_HEADER header, char* pData, uint32 u32DataSize)
{
	print_level(SV_WARN,"pData:%s\n", pData);
	cJSON *json, *pMessNum, *pType, *pContent, *pInformId,*pTime, *pContentReq, *pItem, *pTemp;
	sint32 s32ArraySize = 0;
	static char szContent[256], szTime[32];	
	sint8 s8starttimeisdst = -1;
	sint8 s8endtimeisdst = -1;
	
	json=cJSON_Parse(pData);
	if (!json) 
	{
		print_level(SV_ERROR,"Error before: [%s]\n",cJSON_GetErrorPtr());
	}	
	else	
	{
		pType = cJSON_GetObjectItem(json, "InformType");
		if(pType != SV_NULL)
		{
			print_level(SV_DEBUG,"pType->string:%s \n", pType->valuestring);
			if( strncmp(pType->valuestring, SK_OFFLINE_INFOR_TYPE_MESSAGE, 20) == 0 )
			{
				pMessNum = cJSON_GetObjectItem(json, "MessageNumber");
				pContent = cJSON_GetObjectItem(json, "Content");
				pInformId = cJSON_GetObjectItem(json, "InformId");
				pTime = cJSON_GetObjectItem(json, "Time");		
			}
			else if(!strncmp(pType->valuestring, SK_OFFLINE_CLIP_CMD, 20))
			{
				
			}			
		}
	}
	
	cJSON_Delete(json);
	return;
}

void CMS_SERVER::processGetServerSetconfig(SK_HEADER header, char* pData, uint32 u32DataSize)
{
	SK_HEADER stSendheader;
	char szBuf[32] = {0};
	
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&stSendheader, SK_CMS_SERVER_TO_DVR_BASIC_CONFIG_CHANGE_INFROM_RSP,0,SK_DEFAULT_CONTROL_SERVER_DST_ID);
	controlLink.sendToServer(&stSendheader, szBuf, 0);
	SV_NETWORK_PROTOCOL::getInstance()->createHeader(&stSendheader, SK_CLIENT_TO_CMS_SERVER_GET_CONFIG_INFO_REQ, 0, SK_DEFAULT_CONTROL_SERVER_DST_ID);
	controlLink.sendToServer(&stSendheader, szBuf, 0);
	return;
}

/*******Interface************/
sint32 CMS_Server_Init()
{

	if(pNetWork == SV_NULL)
	{
		print_level(SV_INFO,"cms server init.\n");
		pNetWork = new CMS_SERVER();
	}

	if(pNetWork != SV_NULL)
	{
		return SV_SUCCESS;
	}
	else
	{
		return SV_FAILURE;
	}

}

/*
sint32 CMS_Server_Start()
{
	if(pNetWork!=SV_NULL)
	{
		print_level(SV_DEBUG,"cms server start.\n");
		pNetWork->start();
		pNetWork->pControlThread->start();
		pNetWork->pMediaThread->start();
		pNetWork->pFilePrivateUpload->start();
	}
	return SV_SUCCESS;
}*/

sint32 CMS_Server_DeInit()
{

	if(pNetWork == SV_NULL)
	{
		return SV_FAILURE;
	}

	pNetWork->deInit();

	return SV_SUCCESS;

}

sint32 CMS_Server_PredeInit()
{

	if(pNetWork == SV_NULL)
	{
		return SV_FAILURE;
	}

	pNetWork->preDeInit();

	return SV_SUCCESS;

}

sint32 CMS_Server_Reconnect(SV_BOOL lastSwitch, SV_BOOL nowSwitch)
{
    if (lastSwitch && nowSwitch)
    {
        pNetWork->changeConfig();
    }
    else if (!lastSwitch && nowSwitch)
	{
		SV_NETWORK_STATE::getInstance()->setNetworkRunning(SV_TRUE);
		CMS_Server_Init();
	}
    else if (lastSwitch && !nowSwitch)
	{
		pNetWork->Stop();
        pNetWork = SV_NULL;
	}

	return SV_SUCCESS;
}

void CMS_Server_SetConfigUpdate(SV_BOOL bUpdate)
{
	SV_NETWORK_STATE::getInstance()->setConfigUpdate(bUpdate ? true : false);
}

sint32 CMS_Server_DumpInfo()
{
    CMS_SERVER_STAT_S stCmsInfo = {0};
    stCmsInfo.pszServerStat = "Disable";
    stCmsInfo.pszRegStat = "Disable";
    SV_NETWORK_STATE::getInstance()->dumpCmsServerInfo(&stCmsInfo);
    return SV_SUCCESS;
}

sint32 CMS_Server_SetPrivateFileUpdate(SV_BOOL *flag)
{
	SV_NETWORK_STATE::getInstance()->setPrivateFileUpdate(*flag);
	return SV_SUCCESS;
}

sint32 CMS_Server_RegisterPowerFlag(const SV_BOOL *pbPowerFlag)
{
    if (NULL == pbPowerFlag)
    {
        return ERR_NULL_PTR;
    }
    SV_NETWORK_STATE::getInstance()->setPwrOffFlag(pbPowerFlag);

    return SV_SUCCESS;
}

sint32 CMS_Server_RegisterStorageErrCode(const uint32 *pu32ErrCode)
{
    if (NULL == pu32ErrCode)
    {
        return ERR_NULL_PTR;
    }
    SV_NETWORK_STATE::getInstance()->setStorageErrCode(pu32ErrCode);

    return SV_SUCCESS;
}

sint32 getFileMd5(char *pFileName, char *pMd5)
{

	char cmd[256];
	char buf[256] = {0};
	sprintf(cmd, "md5sum %s", pFileName);
	FILE *stream;

	if( (stream = popen( cmd, "r" )) == NULL )
	{
		printf("popen error!\n");
		return SV_FAILURE;
	}

	if( (fread( buf, sizeof(char), 128,  stream)) < 1 )
	{
		pclose( stream );
		return SV_FAILURE;
	}
	pclose( stream );

	for(int i=0; i<32; i++)
	{
		if(buf[i] == 0)
		{
			break;
		}

		if(buf[i] == '_')
		{
			continue;
		}

		*pMd5 = buf[i];
		pMd5++;
	}

	return SV_SUCCESS;
}

void CMS_Server_SetTalkQueId(sint32 s32QueId)
{
	pNetWork->setTalkQueId(s32QueId);
	return ;
}

