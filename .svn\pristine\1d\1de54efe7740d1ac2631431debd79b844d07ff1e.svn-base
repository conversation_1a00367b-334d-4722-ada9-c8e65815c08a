/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_vosd.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-11-22

文件功能描述: 封装海思MPP视频遮挡叠加模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#ifndef __HuaweiLite__ 
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <linux/fb.h>
#include <error.h>
#endif
#include <sys/prctl.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <pthread.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <time.h>
#include <errno.h>

#include "print.h"
#include "common.h"
#include "hi_common.h"
#include "mpi_region.h"
#include "mpp_com.h"
#include "mpp_font.h"
#include "mpp_bmp.h"
#include "mpp_vosd.h"
#include "media.h"

#define MPP_VOSD_TIMEBMP_BUFSIZE1 16 * 1024     /* 分配给时间位图的缓冲空间 (1x尺寸) */
#define MPP_VOSD_TIMEBMP_BUFSIZE2 32 * 1024     /* 分配给时间位图的缓冲空间 (2x尺寸) */
#define MPP_VOSD_TIMEBMP_BUFSIZE3 64 * 1024     /* 分配给时间位图的缓冲空间 (3x尺寸) */
#define MPP_VOSD_TIMEBMP_BUFSIZE4 120 * 1024    /* 分配给时间位图的缓冲空间 (4x尺寸) */
#define MPP_VOSD_TEMPBMP_BUFSIZE  120 * 1024    /* 分配给车牌号或通道名的位图缓冲空间 */

#define MPP_OVERLAY_NUM         4
#define MPP_OVERLAY_TIME        0
#define MPP_OVERLAY_CHNNAME     1
#define MPP_OVERLAY_BATTERY     2
#define MPP_OVERLAY_SINGNAL     3

/* 区域状态 */
typedef struct tagRgnStat_S
{
    SV_BOOL     bCreated;       /* 是否被创建 */
    uint32      u32Handle;      /* 区域句柄值 */
    SV_BOOL     bShow;          /* 区域是否显示 */
    SV_RECT_S   stRect;         /* 区域位置及大小 */
} MPP_VOSD_RGN_S;

/* 画面遮挡区域 */
typedef struct tagCoverRgn_S
{
    MPP_VOSD_RGN_S astRegion[VOSD_MAX_COVER_RECT];  /* 画面遮挡区域 */
} MPP_COVER_RGN_S;

/* 视频叠加区域 */
typedef struct tagOverlayRgn_S
{
    BATTERY_STAT   enBatteryStat;   /* 电池电量状态 */
    SINGNAL_STAT   enSingnalStat;   /* 信号值状态 */
    VOSD_TIME_FMT  enTimeFmt;   /* 时间日期格式 */
    MPP_VOSD_RGN_S stTimeRgn;   /* 时间叠加区域 */
    MPP_VOSD_RGN_S stChnNameRgn;/* 通道名叠加区域 */
    MPP_VOSD_RGN_S stBatteryRgn;/* 电池电量叠加区域 */
    MPP_VOSD_RGN_S stSingnalRgn;/* 信号值叠加区域 */
} MPP_OVERLAY_RGN_S;

/* 叠加字符串参数 */
typedef struct tagOverStr_S
{
    SV_BOOL     bUpdate;        /* 字符串是否有更新 */
    char        szOverStr[MPP_VOSD_MAX_OVERSTR_LEN];    /* 叠加的字符串内容 */
} MPP_OVER_STR_S;

/* 叠加字符位图样式 */
typedef struct tagOverStyle_S
{
    SV_BOOL     bUpdate;        /* 字符样式是否有更新 */
    MPP_VOSD_SIZE_E nCharSize;  /* 字符的尺寸 */
} MPP_OVER_STYLE_S;

/* 叠加字符串位图信息 */
typedef struct tagOverBmp_S
{
    char        szOverStr[MPP_VOSD_MAX_OVERSTR_LEN];    /* 叠加的字符串内容 */
    SV_SIZE_S   stBmpSize;      /* 位图的尺寸 */
    void       *pvBuf1;          /* 位图缓冲区指针 (1x尺寸) */
    void       *pvBuf2;          /* 位图缓冲区指针 (2x尺寸) */
    void       *pvBuf3;          /* 位图缓冲区指针 (3x尺寸) */
    void       *pvBuf4;          /* 位图缓冲区指针 (4x尺寸) */
} MPP_OVER_BMP_S;

/* 视频遮挡叠加模块控制信息 */
typedef struct tagVosdInfo_S
{
    uint32      u32ChnNum;      /* 视频源通道数目 */
    MPP_COVER_RGN_S astChnCover[VIODE_MAX_CHN*4];     /* 通道画面区域遮挡 */
    MPP_OVERLAY_RGN_S astChnOverlay[VIODE_MAX_CHN*4]; /* 通道视频叠加 */
    MPP_OVER_STR_S stTimeStr;   /* 叠加的时间字符串 */
    MPP_OVER_STR_S stChnName;   /* 叠加的通道名 */
    MPP_OVER_STYLE_S astChnOverStyle[VIODE_MAX_CHN*4];   /* 通道叠加字符的样式 */
    MPP_OVER_BMP_S stTimeBmp;   /* 叠加的时间位图 */
    uint32      u32TID;         /* 视频叠加线程ID */
    SV_BOOL     bRunning;       /* 线程是否正在运行 */
    SV_BOOL     bException;     /* 线程是否出现异常 */
    pthread_mutex_t mutexLock;  /* 参数设置线程互斥锁 */
} MPP_VOSD_INFO_S;

extern MPP_PHY_CHN g_astViPhyChn[VIM_MAX_CHN_NUM];

MPP_VOSD_INFO_S m_stVosdInfo = {0};       /* 视频遮挡叠加控制信息 */

sint32 mpp_vosd_CreateCoverRgn(sint32 s32Chn, VOSD_COVER_S *pstCover);
sint32 mpp_vosd_DestroyCoverRgn(sint32 s32Chn);
sint32 mpp_vosd_CreateOverlayRgn(sint32 s32Chn, VOSD_OVERLAY_S *pstOverlay);
sint32 mpp_vosd_DestroyOverlayRgn(sint32 s32Chn);
void * mpp_vosd_Body(void *pvArg);
sint32 mpp_vosd_UpdateOverRgn();
sint32 mpp_vosd_UpdateTimeBmp(char *pszTimeStr);
sint32 mpp_vosd_UpdateChnNameBmp();
sint32 mpp_vosd_UpdateBatteryBmp(sint32 s32Chn);
sint32 mpp_vosd_UpdateSingnalBmp(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 初始化VOSD模块
 * 输入参数: pstVosdConf --- 视频遮挡叠加配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Init(MPP_VOSD_CONF_S *pstVosdConf)
{
    sint32 s32Ret = 0, i;
    VENC_CHN VeChnId = 0;
    MPP_VOSD_SIZE_E enCharSize = MPP_VOSD_SIZE_1X;
    VOSD_COVER_S stCover = {0};
    void *pvBuf1 = NULL;
    void *pvBuf2 = NULL;
    void *pvBuf3 = NULL;
    void *pvBuf4 = NULL;

    if (NULL == pstVosdConf)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstVosdConf->u32ChnNum || pstVosdConf->u32ChnNum > VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    memset(&m_stVosdInfo, 0x0, sizeof(MPP_VOSD_INFO_S));
    m_stVosdInfo.u32ChnNum = pstVosdConf->u32ChnNum;  

    pvBuf1 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE1);
    if (NULL == pvBuf1)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        return ERR_NOMEM;
    }
    memset(pvBuf1, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE1);

    pvBuf2 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE2);
    if (NULL == pvBuf2)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        free(pvBuf1);
        return ERR_NOMEM;
    }
    memset(pvBuf2, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE2);

    pvBuf3 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE3);
    if (NULL == pvBuf3)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        free(pvBuf1);
        free(pvBuf2);
        return ERR_NOMEM;
    }
    memset(pvBuf3, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE3);

    pvBuf4 = malloc(MPP_VOSD_TIMEBMP_BUFSIZE4);
    if (NULL == pvBuf4)
    {
        print_level(SV_ERROR, "malloc for time bitmap buffer failed! \n");
        free(pvBuf1);
        free(pvBuf2);
        free(pvBuf3);
        return ERR_NOMEM;
    }
    memset(pvBuf4, 0x0, MPP_VOSD_TIMEBMP_BUFSIZE4);

    s32Ret = pthread_mutex_init(&m_stVosdInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        free(pvBuf1);
        free(pvBuf2);
        free(pvBuf3);
        free(pvBuf4);
        return ERR_SYS_NOTREADY;
    }

    stCover.bEnable = SV_FALSE;
    stCover.u32RectNum = VOSD_MAX_COVER_RECT;
    for (i = 0; i < stCover.u32RectNum; i++)
    {
        stCover.astRect[i].u32Width = 10;
        stCover.astRect[i].u32Height = 10;
    }
    
    for (i = 0; i < pstVosdConf->u32ChnNum; i++)
    {
        s32Ret = mpp_vosd_CreateCoverRgn(i, &stCover);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateCoverRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }

        /* 主码流 */
        VeChnId = i;
        if (pstVosdConf->u32PriHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
        }
        else if (pstVosdConf->u32PriHeight < 720)
        {
            enCharSize = MPP_VOSD_SIZE_2X;
        }
        else if (pstVosdConf->u32PriHeight < 1080)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_4X;
        }
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
        s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }

        /* 子码流 */
        VeChnId = m_stVosdInfo.u32ChnNum + i;
        if (pstVosdConf->u32SecHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_2X;
        }
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
        s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }

        /* 主图片流 */
        VeChnId = 2 * m_stVosdInfo.u32ChnNum + i;
        if (pstVosdConf->u32JpegHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
        }
        else if (pstVosdConf->u32JpegHeight < 720)
        {
            enCharSize = MPP_VOSD_SIZE_2X;
        }
        else if (pstVosdConf->u32JpegHeight < 1080)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_4X;
        }
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
        s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }

        /* 子图片流 */
        VeChnId = 3 * m_stVosdInfo.u32ChnNum + i;
        m_stVosdInfo.astChnOverStyle[VeChnId].nCharSize = MPP_VOSD_SIZE_1X;
        m_stVosdInfo.astChnOverStyle[VeChnId].bUpdate = SV_TRUE;
        //s32Ret = mpp_vosd_CreateOverlayRgn(VeChnId, &pstVosdConf->stOverlay);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_CreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pvBuf1);
            free(pvBuf2);
            free(pvBuf3);
            free(pvBuf4);
            return s32Ret;
        }  
    }

    strcpy(m_stVosdInfo.stChnName.szOverStr, pstVosdConf->szChnName);
    m_stVosdInfo.stChnName.bUpdate = SV_TRUE;
    m_stVosdInfo.stTimeBmp.pvBuf1 = pvBuf1;
    m_stVosdInfo.stTimeBmp.pvBuf2 = pvBuf2;
    m_stVosdInfo.stTimeBmp.pvBuf3 = pvBuf3;
    m_stVosdInfo.stTimeBmp.pvBuf4 = pvBuf4;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化VOSD模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Fini()
{
    sint32 s32Ret = 0, i;

    for (i = 0; i < m_stVosdInfo.u32ChnNum; i++)
    {
        s32Ret = mpp_vosd_DestroyCoverRgn(i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyCoverRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vosd_DestroyOverlayRgn(i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vosd_DestroyOverlayRgn(m_stVosdInfo.u32ChnNum + i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vosd_DestroyOverlayRgn(2 * m_stVosdInfo.u32ChnNum + i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        //s32Ret = mpp_vosd_DestroyOverlayRgn(3 * m_stVosdInfo.u32ChnNum + i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_DestroyOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        } 
    }

    s32Ret = pthread_mutex_destroy(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_destroy failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    free(m_stVosdInfo.stTimeBmp.pvBuf1);
    free(m_stVosdInfo.stTimeBmp.pvBuf2);
    free(m_stVosdInfo.stTimeBmp.pvBuf3);
    free(m_stVosdInfo.stTimeBmp.pvBuf4);
    m_stVosdInfo.stTimeBmp.pvBuf1 = NULL;
    m_stVosdInfo.stTimeBmp.pvBuf2 = NULL;
    m_stVosdInfo.stTimeBmp.pvBuf3 = NULL;
    m_stVosdInfo.stTimeBmp.pvBuf4 = NULL;

    return SV_SUCCESS;
}



/******************************************************************************
 * 函数功能: 启动VOSD模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread;

    m_stVosdInfo.bRunning = SV_TRUE;
    m_stVosdInfo.bException = SV_FALSE;
    s32Ret = pthread_create(&thread, NULL, mpp_vosd_Body, &m_stVosdInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VOSD failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stVosdInfo.u32TID = thread;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止VOSD模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_Stop()
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;

    m_stVosdInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(m_stVosdInfo.u32TID, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for VOSD failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VENC模块线程体
 * 输入参数: pstVencInfo --- 视频编码控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_vosd_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    SV_BOOL bException = 0;
    struct timeval tvLast, tvNow;
    struct timezone tz;
    struct tm stTmNow = {0};
    char szTimeStr[MPP_VOSD_MAX_OVERSTR_LEN];
    MPP_VOSD_INFO_S *pstVosdInfo = (MPP_VOSD_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "mpp_vosd_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }    

    while (pstVosdInfo->bRunning)
    {
        sleep_ms(10);
        s32Ret = gettimeofday(&tvNow, &tz);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "gettimeofday failed! [err=%#x]\n", errno);
            continue;
        }

        if ((tvNow.tv_usec > 50000 && tvNow.tv_usec < 950000) || (tvLast.tv_sec == tvNow.tv_sec)
            && (!m_stVosdInfo.astChnOverStyle[0].bUpdate && !m_stVosdInfo.astChnOverStyle[1].bUpdate))
		{
			continue;
		}

		tvLast.tv_sec = tvNow.tv_sec;
		tvLast.tv_usec = tvNow.tv_usec;
        tvNow.tv_sec += (tz.tz_minuteswest * 60);
        gmtime_r((time_t *)&tvNow.tv_sec, &stTmNow);
        
        switch (m_stVosdInfo.astChnOverlay[0].enTimeFmt)
        {
            case VOSD_TIME_FMT_YYYYMMDD:
                sprintf(szTimeStr, "%04d-%02d-%02d %02d:%02d:%02d", 1900 + stTmNow.tm_year, 1 + stTmNow.tm_mon, stTmNow.tm_mday, \
                                                                stTmNow.tm_hour, stTmNow.tm_min, stTmNow.tm_sec);
                break;

            case VOSD_TIME_FMT_MMDDYYYY:
                sprintf(szTimeStr, "%02d-%02d-%04d %02d:%02d:%02d", 1 + stTmNow.tm_mon, stTmNow.tm_mday, 1900 + stTmNow.tm_year, \
                                                                stTmNow.tm_hour, stTmNow.tm_min, stTmNow.tm_sec);
                break;

            case VOSD_TIME_FMT_DDMMYYYY:
                sprintf(szTimeStr, "%02d-%02d-%04d %02d:%02d:%02d", stTmNow.tm_mday, 1 + stTmNow.tm_mon, 1900 + stTmNow.tm_year, \
                                                                stTmNow.tm_hour, stTmNow.tm_min, stTmNow.tm_sec);
                break;
        }
        
        s32Ret = pthread_mutex_lock(&m_stVosdInfo.mutexLock);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
            continue;
        }

        s32Ret = mpp_vosd_UpdateOverRgn();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateOverRgn failed! [err=%#x]\n", s32Ret);
        }

        s32Ret = mpp_vosd_UpdateTimeBmp(szTimeStr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateTimeBmp failed! [err=%#x]\n", s32Ret);
        }

        s32Ret = mpp_vosd_UpdateChnNameBmp();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateChnNameBmp failed! [err=%#x]\n", s32Ret);
        }

        s32Ret = pthread_mutex_unlock(&m_stVosdInfo.mutexLock);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        }
    }

    return NULL;
}


/******************************************************************************
 * 函数功能: 创建通道的遮挡区域
 * 输入参数: s32Chn --- 通道号
             pstCover --- 画面遮挡区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_CreateCoverRgn(sint32 s32Chn, VOSD_COVER_S *pstCover)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁通道的遮挡区域
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_DestroyCoverRgn(sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
             pstOverlay --- 视频叠加区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_CreateOverlayRgn(sint32 s32Chn, VOSD_OVERLAY_S *pstOverlay)
{
    HI_S32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_CHN_S stChn;
    RGN_ATTR_S stRgnAttr;
    RGN_CHN_ATTR_S stChnAttr;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstOverlay)
    {
        return ERR_NULL_PTR;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;

    /* 时间区域 */
    u32Handle = MPP_OVERLAY_NUM * s32Chn;
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = MPP_VOSD_TIMEWID_2X;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = MPP_VOSD_HEIGHT_2X;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_Create(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = pstOverlay->bShowTime;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = pstOverlay->stTimePos.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstOverlay->stTimePos.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_TIME;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bQpDisable = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bAbsQp = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp  = 0;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Height = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Width  = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.u32LumThresh = 128;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.enChgMod     = LESSTHAN_LUM_THRESH;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.bInvColEn    = HI_FALSE;
    print_level(SV_DEBUG, "handle:%d, time(%d,%d)\n", u32Handle, pstOverlay->stTimePos.s32X, pstOverlay->stTimePos.s32Y);
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

    m_stVosdInfo.astChnOverlay[s32Chn].enTimeFmt = pstOverlay->enTimeFmt;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow = pstOverlay->bShowTime;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.s32X = pstOverlay->stTimePos.s32X;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.s32Y = pstOverlay->stTimePos.s32Y;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.u32Width = MPP_VOSD_TIMEWID_2X;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;

	/* 通道名区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 1;
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = 80;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = MPP_VOSD_HEIGHT_2X;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_Create(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = pstOverlay->bShowChnName;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = pstOverlay->stChnNamePos.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstOverlay->stChnNamePos.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_CHNNAME;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow = pstOverlay->bShowChnName;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.s32X = pstOverlay->stChnNamePos.s32X;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.s32Y = pstOverlay->stChnNamePos.s32Y;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.u32Width = 80;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;

#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS)  
#if 0
	/* 电池电量区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = MPP_VOSD_BATTERY_W_2X;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = MPP_VOSD_BATTERY_H_2X;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_Create(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = SV_FALSE;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = MPP_VOSD_BATTERY_X_2X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = MPP_VOSD_BATTERY_Y_2X;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 128;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   =  0;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_BATTERY;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp = (s32Chn <= 1) ? -30 : 0;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bShow = SV_FALSE;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.stRect.s32X = MPP_VOSD_BATTERY_X_2X;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.stRect.s32Y = MPP_VOSD_BATTERY_Y_2X;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.stRect.u32Width = 80;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;

	/* 信号值区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 3;
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = 80;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = MPP_VOSD_HEIGHT_2X;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_Create(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = SV_FALSE;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = MPP_VOSD_SINGNAL_X_2X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = MPP_VOSD_SINGNAL_Y_2X;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 128;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 0;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp =  (s32Chn <= 1) ? -30 : 0;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_SINGNAL;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bShow = SV_FALSE;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.stRect.s32X = MPP_VOSD_SINGNAL_X_2X;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.stRect.s32Y = MPP_VOSD_SINGNAL_Y_2X;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.stRect.u32Width = 80;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.stRect.u32Height = MPP_VOSD_HEIGHT_2X;
#endif
#endif	
	print_level(SV_DEBUG, "[ch%d]handle: time=%d, chName=%d\n", s32Chn, \
	                        m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle, \
	                        m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
             pstCover --- 画面遮挡区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_DestroyOverlayRgn(sint32 s32Chn)
{
    HI_S32 s32Ret = 0;
    SV_BOOL bFailure = SV_FALSE;
    uint32 u32Handle = 0;
    MPP_CHN_S stChn;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;

    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = HI_MPI_RGN_Destroy(u32Handle);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated = SV_FALSE;
    }

    if (m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = HI_MPI_RGN_Destroy(u32Handle);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated = SV_FALSE;
    }
    
#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS)  
#if 0
    if (m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.u32Handle;
        s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = HI_MPI_RGN_Destroy(u32Handle);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bCreated = SV_FALSE;
    }

    if (m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.u32Handle;
        s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    	}
    	
        s32Ret = HI_MPI_RGN_Destroy(u32Handle);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
    		bFailure = SV_TRUE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bCreated = SV_FALSE;
    }
#endif    
#endif

    if (bFailure)
    {
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新创建通道的各视频叠加区域
 * 输入参数: s32Chn --- 通道号
             stTimeRect --- 时间矩形区域
             stChnNameRect --- 通道名矩形区域
             stBatteryRect --- 电池电量矩形区域
             stSingnalRect --- 信号值矩形区域
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_RecreateOverlayRgn(sint32 s32Chn, SV_RECT_S stTimeRect, SV_RECT_S stChnNameRect, SV_RECT_S stBatteryRect, SV_RECT_S stSingnalRect)
{
    HI_S32 s32Ret = 0;
    uint32 u32Handle = 0;    
    MPP_CHN_S stChn;
    RGN_ATTR_S stRgnAttr;
    RGN_CHN_ATTR_S stChnAttr;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4
        || 0 == stTimeRect.u32Width || stTimeRect.u32Width > 2047
        || 0 == stTimeRect.u32Height || stTimeRect.u32Height >2047
        || 0 == stChnNameRect.u32Width || stChnNameRect.u32Width > 2047
        || 0 == stChnNameRect.u32Height || stChnNameRect.u32Height >2047)
    {
        return ERR_ILLEGAL_PARAM;
    }

    //mpp_vosd_DestroyOverlayRgn(s32Chn);
	stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;

    /* 时间区域 */
    u32Handle = MPP_OVERLAY_NUM * s32Chn;
    s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
	}
    
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = stTimeRect.u32Width;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = stTimeRect.u32Height;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_SetAttr(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = stTimeRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = stTimeRect.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_TIME;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bQpDisable = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bAbsQp = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp  = 0;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Height = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Width  = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.u32LumThresh = 128;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.enChgMod     = LESSTHAN_LUM_THRESH;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.bInvColEn    = HI_FALSE;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.stRect = stTimeRect;

	/* 通道名区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 1;
	s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
	}
	
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = stChnNameRect.u32Width;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = stChnNameRect.u32Height;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_SetAttr(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = stChnNameRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = stChnNameRect.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_CHNNAME;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect = stChnNameRect;

#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS)  
#if 0
	/* 电池电量区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 2;
	s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
	}
	
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = stBatteryRect.u32Width;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = stBatteryRect.u32Height;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_SetAttr(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bShow;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = stBatteryRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = stBatteryRect.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 128;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 0;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_BATTERY;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp =  (s32Chn <= 1) ? -30 : 0;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.stRect = stBatteryRect;

	/* 信号值区域 */
	u32Handle = MPP_OVERLAY_NUM * s32Chn + 3;
	s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
	}
	
    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = stSingnalRect.u32Width;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = stSingnalRect.u32Height;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_SetAttr(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bShow;;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = stSingnalRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = stSingnalRect.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 128;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 0;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp =  (s32Chn <= 1) ? -30 : 0;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_SINGNAL;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
	
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bCreated = SV_TRUE;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.stRect = stSingnalRect;
#endif	
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新创建通道的通道名视频叠加区域
 * 输入参数: stRgnRect --- 视频叠加区域参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_RecreateChnNameOverlayRgn(sint32 s32Chn, SV_RECT_S stRgnRect)
{
    HI_S32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_CHN_S stChn;
    RGN_ATTR_S stRgnAttr;
    RGN_CHN_ATTR_S stChnAttr;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 4
        || 0 == stRgnRect.u32Width || stRgnRect.u32Width > 2047
        || 0 == stRgnRect.u32Height || stRgnRect.u32Height >2047)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;

    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
    s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}
    	
    s32Ret = HI_MPI_RGN_Destroy(u32Handle);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Destroy failed! [err=%#x]\n", s32Ret);
		//return HI_FAILURE;
	}

    stRgnAttr.enType = OVERLAY_RGN;
    stRgnAttr.unAttr.stOverlay.enPixelFmt       = PIXEL_FORMAT_RGB_1555;
    stRgnAttr.unAttr.stOverlay.stSize.u32Width  = (stRgnRect.u32Width + 1) & 0xfffffffe;
    stRgnAttr.unAttr.stOverlay.stSize.u32Height = (stRgnRect.u32Height + 1) & 0xfffffffe;
    stRgnAttr.unAttr.stOverlay.u32BgColor       = 0;
#ifdef __HuaweiLite__
    stRgnAttr.unAttr.stOverlay.u32CanvasNum     = 1;
#endif
    s32Ret = HI_MPI_RGN_Create(u32Handle, &stRgnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_Create failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	stChnAttr.bShow  = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = stRgnRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = stRgnRect.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_CHNNAME;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bQpDisable = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bAbsQp = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp  = 0;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Height = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Width  = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.u32LumThresh = 128;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.enChgMod     = LESSTHAN_LUM_THRESH;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.bInvColEn    = HI_FALSE;
    print_level(SV_DEBUG, "handle:%d, plate(%d,%d)\n", u32Handle, stRgnRect.s32X, stRgnRect.s32Y);
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle = u32Handle;
	m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.stRect = stRgnRect;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 显示通道的遮挡区域 (默认创建后为隐藏)
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ShowCoverRgn(sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 隐藏通道的遮挡区域 (默认创建后为隐藏)
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_HideCoverRgn(sint32 s32Chn)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道的遮挡区域属性
 * 输入参数: s32Chn --- 通道号
             pstCoverAttr --- 画面遮挡区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ChnCoverAttrSet(sint32 s32Chn, VOSD_COVER_S *pstCoverAttr)
{
	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取通道的遮挡区域属性
 * 输入参数: s32Chn --- 通道号
             pstCoverAttr --- 画面遮挡区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ChnCoverAttrGet(sint32 s32Chn, VOSD_COVER_S *pstCoverAttr)
{
	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道的视频叠加属性
 * 输入参数: s32Chn --- Vi输入通道
             s32OsdChn --- OSD叠加通道
             pstOverlayAttr --- 视频叠加区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 视频画面叠加显示坐标值必需为偶数且X坐标为8的倍数
 *****************************************************************************/
sint32 mpp_vosd_ChnOverlayAttrSet(sint32 s32Chn, sint32 s32OsdChn, VOSD_OVERLAY_S *pstOverlayAttr)
{
    HI_S32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_CHN_S stChn;
    RGN_CHN_ATTR_S stChnAttr;
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    char szFilePath[64];
    char *pszSize = "2x";

    if (s32OsdChn < 0 || s32OsdChn >= VIODE_MAX_CHN * 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstOverlayAttr)
    {
        return ERR_NULL_PTR;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32OsdChn;
    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32OsdChn];
    if (pstChnOverlay->stTimeRgn.bShow != pstOverlayAttr->bShowTime)
    {
        u32Handle = pstChnOverlay->stTimeRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowTime;
        s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	pstChnOverlay->stTimeRgn.bShow = pstOverlayAttr->bShowTime;
    }

    if (pstChnOverlay->stChnNameRgn.bShow != pstOverlayAttr->bShowChnName)
    {
        u32Handle = pstChnOverlay->stChnNameRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowChnName;
        if (pstOverlayAttr->bShowChnName)
        {
            switch (m_stVosdInfo.astChnOverStyle[s32OsdChn].nCharSize)
            {
                case MPP_VOSD_SIZE_1X:
                    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_1X : MPP_VOSD_TIME_Y_1X;
                    break;
                case MPP_VOSD_SIZE_2X:
                    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_2X : MPP_VOSD_TIME_Y_2X;
                    break;
                case MPP_VOSD_SIZE_3X:
                    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_3X : MPP_VOSD_TIME_Y_3X;
                    break;
                case MPP_VOSD_SIZE_4X:
                    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstOverlayAttr->bShowTime ? MPP_VOSD_CHNNAME_Y_4X : MPP_VOSD_TIME_Y_4X;
                    break;
                default:
                    return ERR_ILLEGAL_PARAM;
            }
        }
        s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	pstChnOverlay->stChnNameRgn.bShow = pstOverlayAttr->bShowChnName;
    }

    pstChnOverlay->enTimeFmt = pstOverlayAttr->enTimeFmt;
    
#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS)  
#if 0
    if (pstChnOverlay->stBatteryRgn.bShow != pstOverlayAttr->bShowBattery)
    {
        u32Handle = pstChnOverlay->stBatteryRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowBattery;
        s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	pstChnOverlay->stBatteryRgn.bShow = pstOverlayAttr->bShowBattery;
    }

    if (pstChnOverlay->stSingnalRgn.bShow != pstOverlayAttr->bShowSingnal)
    {
        u32Handle = pstChnOverlay->stSingnalRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
        stChnAttr.bShow = pstOverlayAttr->bShowSingnal;
        s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	
    	pstChnOverlay->stSingnalRgn.bShow = pstOverlayAttr->bShowSingnal;
    }

    if (pstChnOverlay->enBatteryStat != pstOverlayAttr->enBatteryStat)
    {
        pstChnOverlay->enBatteryStat = pstOverlayAttr->enBatteryStat;
        s32Ret = mpp_vosd_UpdateBatteryBmp(s32Chn);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateBatteryBmp failed! s32Ret: 0x%x.\n", s32Ret);
            return s32Ret;
        }
    }

    if (pstChnOverlay->enSingnalStat != pstOverlayAttr->enSingnalStat)
    {
        pstChnOverlay->enSingnalStat = pstOverlayAttr->enSingnalStat;
        s32Ret = mpp_vosd_UpdateSingnalBmp(s32Chn);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateSingnalBmp failed! s32Ret: 0x%x.\n", s32Ret);
            return s32Ret;
        }
    }
#endif    
#endif
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取通道的视频叠加属性
 * 输入参数: s32Chn --- Vi输入通道
             s32OsdChn --- OSD叠加通道
             pstOverlayAttr --- 视频叠加区域属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_ChnOverlayAttrGet(sint32 s32Chn, sint32 s32OsdChn, VOSD_OVERLAY_S *pstOverlayAttr)
{
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    
    if (s32OsdChn < 0 || s32OsdChn >= VIODE_MAX_CHN * 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstOverlayAttr)
    {
        return ERR_NULL_PTR;
    }

    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32OsdChn];
    pstOverlayAttr->bShowTime = pstChnOverlay->stTimeRgn.bShow;
    pstOverlayAttr->bShowChnName = pstChnOverlay->stChnNameRgn.bShow;
    pstOverlayAttr->enTimeFmt = pstChnOverlay->enTimeFmt;
    pstOverlayAttr->stTimePos.s32X = pstChnOverlay->stTimeRgn.stRect.s32X;
    pstOverlayAttr->stTimePos.s32Y = pstChnOverlay->stTimeRgn.stRect.s32Y;
    pstOverlayAttr->stChnNamePos.s32X = pstChnOverlay->stChnNameRgn.stRect.s32X;
    pstOverlayAttr->stChnNamePos.s32Y = pstChnOverlay->stChnNameRgn.stRect.s32Y;
    pstOverlayAttr->bShowBattery = pstChnOverlay->stBatteryRgn.bShow;
    pstOverlayAttr->bShowSingnal = pstChnOverlay->stSingnalRgn.bShow;
    pstOverlayAttr->enBatteryStat = pstChnOverlay->enBatteryStat;
    pstOverlayAttr->enSingnalStat = pstChnOverlay->enSingnalStat;
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 显示通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 只对时间和车牌号区域有效
 *****************************************************************************/
sint32 mpp_vosd_ShowOverlayRgn(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle;
    MPP_CHN_S stChn;
    RGN_CHN_ATTR_S stChnAttr;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;

    /* 时间区域 */
    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_TRUE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow = SV_TRUE;
	}
    
	/* 通道名区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_TRUE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
        m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow = SV_TRUE;
    }

#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS) 
#if 0
    /* 电池电量区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_TRUE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
        m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bShow = SV_TRUE;
    }

    /* 信号值区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
        m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bShow = SV_TRUE;
    }
#endif    
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 隐藏通道的视频叠加区域
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 只对时间和车牌号区域有效
 *****************************************************************************/
sint32 mpp_vosd_HideOverlayRgn(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle;
    MPP_CHN_S stChn;
    RGN_CHN_ATTR_S stChnAttr;

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;

    /* 时间区域 */
    if (m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
    {
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow = SV_FALSE;
	}

	/* 通道名区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.bShow = SV_FALSE;
	}

#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS) 
#if 0
	/* 电池电量区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.bShow = SV_FALSE;
	}

	/* 信号值区域 */
	if (m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bCreated)
	{
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.u32Handle;
        s32Ret = HI_MPI_RGN_GetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}

    	stChnAttr.bShow = SV_FALSE;
    	s32Ret = HI_MPI_RGN_SetDisplayAttr(u32Handle, &stChn, &stChnAttr);
        if(HI_SUCCESS != s32Ret)
    	{
    		print_level(SV_ERROR, "HI_MPI_RGN_GetDisplayAttr failed! [err=%#x]\n", s32Ret);
    		return SV_FAILURE;
    	}
    	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.bShow = SV_FALSE;
	}
#endif	
#endif
	
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解除OSD叠加通道与编码通道的绑定关系
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 在销毁编码通道前应该先调该接口解绑
 *****************************************************************************/
sint32 mpp_vosd_OverlayRgnDetach(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_CHN_S stChn;
    
    if (s32Chn < 0 || s32Chn >= m_stVosdInfo.u32ChnNum)
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            s32Chn = s32Chn;
            break;

        case STREAM_TYPE_SEC:
            s32Chn = m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        case STREAM_TYPE_SNAP0:
            s32Chn = 2 * m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;
    u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
    s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}

	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
    s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}

#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS) 
#if 0
	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn.u32Handle;
    s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}

	u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn.u32Handle;
    s32Ret = HI_MPI_RGN_DetachFromChn(u32Handle, &stChn);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_DetachFromChn failed! [err=%#x]\n", s32Ret);
	}
#endif	
#endif	

	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 绑定OSD叠加通道与编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 解绑重新创建完编码通道号需调该接口重新绑定
 *****************************************************************************/
sint32 mpp_vosd_OverlayRgnAttach(STREAM_TYPE_E enStreamType, sint32 s32Chn)
{
    sint32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_CHN_S stChn;
    RGN_CHN_ATTR_S stChnAttr;
    MPP_VOSD_RGN_S *pstVosdRgn = NULL;
    
    if (s32Chn < 0 || s32Chn >= m_stVosdInfo.u32ChnNum)
    {
        return ERR_ILLEGAL_PARAM;
    }

    switch (enStreamType)
    {
        case STREAM_TYPE_PRI:
            s32Chn = s32Chn;
            break;

        case STREAM_TYPE_SEC:
            s32Chn = m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        case STREAM_TYPE_SNAP0:
            s32Chn = 2 * m_stVosdInfo.u32ChnNum + s32Chn;
            break;

        default : return ERR_ILLEGAL_PARAM;
    }

    stChn.enModId  = HI_ID_VENC;
    stChn.s32DevId = 0;
    stChn.s32ChnId = s32Chn;
    stChnAttr.enType = OVERLAY_RGN;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 64;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bQpDisable = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.bAbsQp = HI_FALSE;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp  = 0;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Height = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.stInvColArea.u32Width  = 16;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.u32LumThresh = 128;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.enChgMod     = LESSTHAN_LUM_THRESH;
    stChnAttr.unChnAttr.stOverlayChn.stInvertColor.bInvColEn    = HI_FALSE;

    pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn;
    u32Handle = pstVosdRgn->u32Handle;
    stChnAttr.bShow  = pstVosdRgn->bShow;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_TIME;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = pstVosdRgn->stRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstVosdRgn->stRect.s32Y;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
	}

	pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn;
    u32Handle = pstVosdRgn->u32Handle;
    stChnAttr.bShow  = pstVosdRgn->bShow;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_CHNNAME;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = pstVosdRgn->stRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstVosdRgn->stRect.s32Y;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
	}

#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS) 
#if 0
	pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stBatteryRgn;
    u32Handle = pstVosdRgn->u32Handle;
    stChnAttr.bShow  = pstVosdRgn->bShow;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_BATTERY;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = pstVosdRgn->stRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstVosdRgn->stRect.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 128;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 0;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp = (s32Chn <= 1) ? -30 : 0;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
	}

	pstVosdRgn = &m_stVosdInfo.astChnOverlay[s32Chn].stSingnalRgn;
    u32Handle = pstVosdRgn->u32Handle;
    stChnAttr.bShow  = pstVosdRgn->bShow;
    stChnAttr.unChnAttr.stOverlayChn.u32Layer     = MPP_OVERLAY_SINGNAL;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32X = pstVosdRgn->stRect.s32X;
    stChnAttr.unChnAttr.stOverlayChn.stPoint.s32Y = pstVosdRgn->stRect.s32Y;
    stChnAttr.unChnAttr.stOverlayChn.u32BgAlpha   = 128;
    stChnAttr.unChnAttr.stOverlayChn.u32FgAlpha   = 0;
    stChnAttr.unChnAttr.stOverlayChn.stQpInfo.s32Qp =  (s32Chn <= 1) ? -30 : 0;
    s32Ret = HI_MPI_RGN_AttachToChn(u32Handle, &stChn, &stChnAttr);
    if(HI_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "HI_MPI_RGN_AttachToChn failed! [err=%#x]\n", s32Ret);
	}
#endif	
#endif

	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查更新叠加区域
 * 输入参数: szTimeStr --- 时间字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateOverRgn()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    MPP_VOSD_SIZE_E enCharSize;
    SV_RECT_S stTimeRect = {0};
    SV_RECT_S stChnNameRect = {0};
    SV_RECT_S stBatteryRect = {0};
    SV_RECT_S stSingnalRect = {0};

    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i;
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated || 
            !m_stVosdInfo.astChnOverStyle[s32Chn].bUpdate)
        {
            continue;
        }

        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                stTimeRect.s32X = MPP_VOSD_TIME_X_1X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_1X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_1X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_1X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_1X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_1X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_1X;
                stBatteryRect.s32X = (i == 2) ? MPP_VOSD_BATTERY_X_2X : MPP_VOSD_BATTERY_X_1X;
                stBatteryRect.s32Y = MPP_VOSD_BATTERY_Y_1X;
                stBatteryRect.u32Width = MPP_VOSD_BATTERY_W_1X;
                stBatteryRect.u32Height = MPP_VOSD_BATTERY_H_1X;
                stSingnalRect.s32X = (i == 2) ? MPP_VOSD_SINGNAL_X_2X : MPP_VOSD_SINGNAL_X_1X;
                stSingnalRect.s32Y = MPP_VOSD_SINGNAL_Y_1X;
                stSingnalRect.u32Width = MPP_VOSD_SINGNAL_W_1X;
                stSingnalRect.u32Height = MPP_VOSD_SINGNAL_H_1X;
                break;

            case MPP_VOSD_SIZE_2X:
                stTimeRect.s32X = MPP_VOSD_TIME_X_2X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_2X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_2X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_2X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_2X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_2X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_2X;
                stBatteryRect.s32X = (i == 2) ? MPP_VOSD_BATTERY_X_3X : MPP_VOSD_BATTERY_X_2X;
                stBatteryRect.s32Y = MPP_VOSD_BATTERY_Y_2X;
                stBatteryRect.u32Width = MPP_VOSD_BATTERY_W_2X;
                stBatteryRect.u32Height = MPP_VOSD_BATTERY_H_2X;
                stSingnalRect.s32X = (i == 2) ? MPP_VOSD_SINGNAL_X_3X : MPP_VOSD_SINGNAL_X_2X;
                stSingnalRect.s32Y = MPP_VOSD_SINGNAL_Y_2X;
                stSingnalRect.u32Width = MPP_VOSD_SINGNAL_W_2X;
                stSingnalRect.u32Height = MPP_VOSD_SINGNAL_H_2X;
                break;

            case MPP_VOSD_SIZE_3X:
                stTimeRect.s32X = MPP_VOSD_TIME_X_3X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_3X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_3X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_3X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_3X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_3X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_3X;
                stBatteryRect.s32X = (i == 2) ? MPP_VOSD_BATTERY_X_4X : MPP_VOSD_BATTERY_X_3X;
                stBatteryRect.s32Y = MPP_VOSD_BATTERY_Y_3X;
                stBatteryRect.u32Width = MPP_VOSD_BATTERY_W_3X;
                stBatteryRect.u32Height = MPP_VOSD_BATTERY_H_3X;
                stSingnalRect.s32X = (i == 2) ? MPP_VOSD_SINGNAL_X_4X : MPP_VOSD_SINGNAL_X_3X;
                stSingnalRect.s32Y = MPP_VOSD_SINGNAL_Y_3X;
                stSingnalRect.u32Width = MPP_VOSD_SINGNAL_W_3X;
                stSingnalRect.u32Height = MPP_VOSD_SINGNAL_H_3X;
                break;

            case MPP_VOSD_SIZE_4X:
                stTimeRect.s32X = MPP_VOSD_TIME_X_4X;
                stTimeRect.s32Y = MPP_VOSD_TIME_Y_4X;
                stTimeRect.u32Width = MPP_VOSD_TIMEWID_4X;
                stTimeRect.u32Height = MPP_VOSD_HEIGHT_4X;
                stChnNameRect.s32X = MPP_VOSD_CHNNAME_X_4X;
                stChnNameRect.s32Y = MPP_VOSD_CHNNAME_Y_4X;
                stChnNameRect.u32Width = 80;
                stChnNameRect.u32Height = MPP_VOSD_HEIGHT_4X;
                stBatteryRect.s32X = MPP_VOSD_BATTERY_X_4X;
                stBatteryRect.s32Y = MPP_VOSD_BATTERY_Y_4X;
                stBatteryRect.u32Width = MPP_VOSD_BATTERY_W_4X;
                stBatteryRect.u32Height = MPP_VOSD_BATTERY_H_4X;
                stSingnalRect.s32X = MPP_VOSD_SINGNAL_X_4X;
                stSingnalRect.s32Y = MPP_VOSD_SINGNAL_Y_4X;
                stSingnalRect.u32Width = MPP_VOSD_SINGNAL_W_4X;
                stSingnalRect.u32Height = MPP_VOSD_SINGNAL_H_4X;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }
   
        s32Ret = mpp_vosd_RecreateOverlayRgn(s32Chn, stTimeRect, stChnNameRect, stBatteryRect, stSingnalRect);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_RecreateOverlayRgn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        
#if (BOARD == BOARD_WFCR10S1 || BOARD == BOARD_WFCR10S1LOS || BOARD == BOARD_WFCR20S1 || BOARD == BOARD_WFCR20S1LOS)
#if 0
        s32Ret = mpp_vosd_UpdateBatteryBmp(s32Chn);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateBatteryBmp failed! s32Ret: 0x%x.\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vosd_UpdateSingnalBmp(s32Chn);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_UpdateSingnalBmp failed! s32Ret: 0x%x.\n", s32Ret);
            return s32Ret;
        }
#endif        
#endif        

        m_stVosdInfo.astChnOverStyle[s32Chn].bUpdate = SV_FALSE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新时间位图
 * 输入参数: szTimeStr --- 时间字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateTimeBmp(char *pszTimeStr)
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    uint32 u32SizeIdx = 0;
    SV_SIZE_S *pstBmpSize = NULL;
    SV_SIZE_S astBmpSize[MPP_VOSD_SIZE_BUTT] = {0};
    SV_BOOL abUpSize[MPP_VOSD_SIZE_BUTT] = {SV_FALSE};
    uint32 u32Stride = 0;
    uint16 *pu16Buf = NULL;
    uint16 *apu16Buf[MPP_VOSD_SIZE_BUTT] = {m_stVosdInfo.stTimeBmp.pvBuf1, m_stVosdInfo.stTimeBmp.pvBuf2, \
                                            m_stVosdInfo.stTimeBmp.pvBuf3, m_stVosdInfo.stTimeBmp.pvBuf4};
    
    uint32 u32Handle = 0;
    uint32 u32StrLen = 0;
	BITMAP_S stBitmap;

    if (NULL == pszTimeStr)
    {
        return ERR_NULL_PTR;
    }

    for (i = 0; i < MPP_VOSD_SIZE_BUTT; i++)
    {
        if (NULL == apu16Buf[i])
        {
            return ERR_NULL_PTR;
        }
    }

    u32StrLen = strlen(pszTimeStr);
    if (u32StrLen > MPP_VOSD_MAX_OVERSTR_LEN - 1)
    {
        return ERR_ILLEGAL_PARAM;
    }

    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i; 
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
        {
            continue;
        }

        u32SizeIdx = m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize;
        if (u32SizeIdx >= MPP_VOSD_SIZE_BUTT)
        {
            return ERR_ILLEGAL_PARAM;
        }

        abUpSize[u32SizeIdx] = SV_TRUE; 
    }

    for (i = MPP_VOSD_SIZE_1X; i < MPP_VOSD_SIZE_BUTT; i++)
    {
        if (abUpSize[i])
        {
            s32Ret = mpp_font_UpdateStrToARGB1555(pszTimeStr, i, apu16Buf[i], &astBmpSize[i], &u32Stride, 0);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_font_UpdateStrToARGB1555 failed! [err=%#x]\n", s32Ret);
            	return s32Ret;
            }
        }
    }
   
    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i; 
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
        {
            continue;
        }

        u32SizeIdx = m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize;
        pu16Buf = apu16Buf[u32SizeIdx];
        pstBmpSize = &astBmpSize[u32SizeIdx];     
        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.u32Handle;
        stBitmap.enPixelFormat = PIXEL_FORMAT_RGB_1555;
        stBitmap.u32Width = pstBmpSize->u32Width;
        stBitmap.u32Height = pstBmpSize->u32Height;
        stBitmap.pData = pu16Buf;
        s32Ret = HI_MPI_RGN_SetBitMap(u32Handle, &stBitmap);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_RGN_SetBitMap failed! [err=%#x]\n", s32Ret);
        }
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新车牌号位图
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateChnNameBmp()
{
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    uint32 u32ChnNum = m_stVosdInfo.u32ChnNum;
    MPP_VOSD_SIZE_E enCharSize;
    MPP_FONT_SCALE_E enScale;
    SV_SIZE_S stBmpSize = {0}; 
    SV_RECT_S stRgnRect = {0};
    uint32 u32Stride = 0;
    uint16 *pu16Buf = NULL;
    uint32 u32Handle = 0;
	BITMAP_S stBitmap;

	if (!m_stVosdInfo.stChnName.bUpdate)
	{
	    return SV_SUCCESS;
	}

    pu16Buf = (uint16 *)malloc(MPP_VOSD_TEMPBMP_BUFSIZE);
    if (NULL == pu16Buf)
    {
        return ERR_NOMEM;
    }

    print_level(SV_INFO, "OSD update ChnName: %s\n", m_stVosdInfo.stChnName.szOverStr);
    for (i = 0; i < u32ChnNum * 3; i++)
    {
        s32Chn = i;
        if (!m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bCreated)
        {
            continue;
        }

        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                enScale = MPP_FONT_SCALE_1X;
                break;

            case MPP_VOSD_SIZE_2X:
                enScale = MPP_FONT_SCALE_2X;
                break;

            case MPP_VOSD_SIZE_3X:
                enScale = MPP_FONT_SCALE_3X;
                break;

            case MPP_VOSD_SIZE_4X:
                enScale = MPP_FONT_SCALE_4X;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        s32Ret = mpp_font_StringToARGB1555(m_stVosdInfo.stChnName.szOverStr, enScale, pu16Buf, &stBmpSize, &u32Stride);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_font_StringToARGB1555 failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }
        
        switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
        {
            case MPP_VOSD_SIZE_1X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_1X;
                stRgnRect.s32X &= 0xfffffffe;
                stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_1X : MPP_VOSD_TIME_Y_1X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_2X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_2X;
                stRgnRect.s32X &= 0xfffffffe;
                stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_2X : MPP_VOSD_TIME_Y_2X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_3X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_3X;
                stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_3X : MPP_VOSD_TIME_Y_3X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            case MPP_VOSD_SIZE_4X:
                stRgnRect.s32X = MPP_VOSD_CHNNAME_X_4X;
                stRgnRect.s32Y = m_stVosdInfo.astChnOverlay[s32Chn].stTimeRgn.bShow ? MPP_VOSD_CHNNAME_Y_4X : MPP_VOSD_TIME_Y_4X;
                stRgnRect.u32Width = stBmpSize.u32Width;
                break;

            default:
                return ERR_ILLEGAL_PARAM;
        }

        stRgnRect.u32Height = stBmpSize.u32Height;
        s32Ret = mpp_vosd_RecreateChnNameOverlayRgn(s32Chn, stRgnRect);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vosd_RecreateChnNameOverlayRgn failed! [err=%#x]\n", s32Ret);
            free(pu16Buf);
        	return s32Ret;
        }

        u32Handle = m_stVosdInfo.astChnOverlay[s32Chn].stChnNameRgn.u32Handle;
        stBitmap.enPixelFormat = PIXEL_FORMAT_RGB_1555;
        stBitmap.u32Width = stBmpSize.u32Width;
        stBitmap.u32Height = stBmpSize.u32Height;
        stBitmap.pData = pu16Buf;
        s32Ret = HI_MPI_RGN_SetBitMap(u32Handle, &stBitmap);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_RGN_SetBitMap failed! [err=%#x]\n", s32Ret);
        }
    }

    m_stVosdInfo.stChnName.bUpdate = SV_FALSE;
    free(pu16Buf);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新电池电量位图
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateBatteryBmp(sint32 s32Chn)
{
    HI_S32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    RGN_CANVAS_INFO_S stCanvasInfo = {0};
    OSD_SURFACE_S Surface = {0};
    char szFilePath[64];
    char *pszSize = "2x";

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 3)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32Chn];
    switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
    {
        case MPP_VOSD_SIZE_1X:
            pszSize = "1x";
            break;
        case MPP_VOSD_SIZE_2X:
            pszSize = "2x";
            break;
        case MPP_VOSD_SIZE_3X:
            pszSize = "3x";
            break;
        case MPP_VOSD_SIZE_4X:
            pszSize = "4x";
            break;
        default:
            return ERR_ILLEGAL_PARAM;
    }    

    print_level(SV_INFO, "enBatteryStat: %d\n", pstChnOverlay->enBatteryStat);
    switch (pstChnOverlay->enBatteryStat)
    {
        case BATTERY_CHARGEING:
            sprintf(szFilePath, VOSD_BMP_BC, pszSize);
            break;
        case BATTERY_CHARGE_FULL:
            sprintf(szFilePath, VOSD_BMP_BCF, pszSize);
            break;
        case BATTERY_EMPTY:
            sprintf(szFilePath, VOSD_BMP_BLE, pszSize);
            break;
        case BATTERY_LEVEL1:
            sprintf(szFilePath, VOSD_BMP_BL1, pszSize);
            break;
        case BATTERY_LEVEL2:
            sprintf(szFilePath, VOSD_BMP_BL2, pszSize);
            break;
        case BATTERY_LEVEL3:
            sprintf(szFilePath, VOSD_BMP_BL3, pszSize);
            break;
        case BATTERY_LEVEL4:
            sprintf(szFilePath, VOSD_BMP_BL4, pszSize);
            break;
        default:
            print_level(SV_ERROR, "enBatteryStat is invalied!\n");
            return ERR_ILLEGAL_PARAM;
    }

    u32Handle = pstChnOverlay->stBatteryRgn.u32Handle;
    s32Ret = HI_MPI_RGN_GetCanvasInfo(u32Handle, &stCanvasInfo);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_GetCanvasInfo failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }

    Surface.enColorFmt = OSD_COLOR_FMT_RGB1555;
    CreateSurfaceByCanvas(szFilePath, &Surface, (uint8*)(stCanvasInfo.u32VirtAddr), stCanvasInfo.stSize.u32Width, stCanvasInfo.stSize.u32Height, stCanvasInfo.u32Stride);

    s32Ret = HI_MPI_RGN_UpdateCanvas(u32Handle);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_UpdateCanvas failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 更新信号量位图
 * 输入参数: s32Chn --- 通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_UpdateSingnalBmp(sint32 s32Chn)
{
    HI_S32 s32Ret = 0;
    uint32 u32Handle = 0;
    MPP_OVERLAY_RGN_S *pstChnOverlay = NULL;
    RGN_CANVAS_INFO_S stCanvasInfo = {0};
    OSD_SURFACE_S Surface = {0};
    char szFilePath[64];
    char *pszSize = "2x";

    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 3)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pstChnOverlay = &m_stVosdInfo.astChnOverlay[s32Chn];
    switch (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize)
    {
        case MPP_VOSD_SIZE_1X:
            pszSize = "1x";
            break;
        case MPP_VOSD_SIZE_2X:
            pszSize = "2x";
            break;
        case MPP_VOSD_SIZE_3X:
            pszSize = "3x";
            break;
        case MPP_VOSD_SIZE_4X:
            pszSize = "4x";
            break;
        default:
            return ERR_ILLEGAL_PARAM;
    }    

    print_level(SV_INFO, "enSingnalStat: %d\n", pstChnOverlay->enSingnalStat);
    switch (pstChnOverlay->enSingnalStat)
    {
        case SINGNAL_LEVEL1:
            sprintf(szFilePath, VOSD_BMP_WIFI1, pszSize);
            break;
        case SINGNAL_LEVEL2:
            sprintf(szFilePath, VOSD_BMP_WIFI2, pszSize);
            break;
        case SINGNAL_LEVEL3:
            sprintf(szFilePath, VOSD_BMP_WIFI3, pszSize);
            break;
        case SINGNAL_LEVEL4:
            sprintf(szFilePath, VOSD_BMP_WIFI4, pszSize);
            break;
        default:
            print_level(SV_ERROR, "enBatteryStat is invalied!\n");
            return ERR_ILLEGAL_PARAM;
    }

    u32Handle = pstChnOverlay->stSingnalRgn.u32Handle;
    s32Ret = HI_MPI_RGN_GetCanvasInfo(u32Handle, &stCanvasInfo);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_GetCanvasInfo failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }

    Surface.enColorFmt = OSD_COLOR_FMT_RGB1555;
    CreateSurfaceByCanvas(szFilePath, &Surface, (uint8*)(stCanvasInfo.u32VirtAddr), stCanvasInfo.stSize.u32Width, stCanvasInfo.stSize.u32Height, stCanvasInfo.u32Stride);

    s32Ret = HI_MPI_RGN_UpdateCanvas(u32Handle);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_RGN_UpdateCanvas failed! s32Ret: 0x%x.\n", s32Ret);
        return s32Ret;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道名名字
 * 输入参数: szChnName --- 通道名字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_SetChnName(sint32 s32Chn, char *szChnName)
{
    sint32 s32Ret = 0;
    
    if (NULL == szChnName)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = pthread_mutex_lock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    strcpy(m_stVosdInfo.stChnName.szOverStr, szChnName);
    m_stVosdInfo.stChnName.bUpdate = SV_TRUE;

    s32Ret = pthread_mutex_unlock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS; 
}

/******************************************************************************
 * 函数功能: 设置通道字符叠加的样式
 * 输入参数: s32Chn --- 通道号
             enCharSize --- 通道叠加的字符大小尺寸
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_SetChnCharStyle(sint32 s32Chn, MPP_VOSD_SIZE_E enCharSize)
{
    sint32 s32Ret = 0;
    
    if (s32Chn < 0 || s32Chn >= VIODE_MAX_CHN * 3 || enCharSize >= MPP_VOSD_SIZE_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = pthread_mutex_lock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    print_level(SV_INFO, "set [CH%d] OSD CharSize: %dX\n", s32Chn, enCharSize + 1);
    if (m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize != enCharSize)
    {
        m_stVosdInfo.astChnOverStyle[s32Chn].nCharSize = enCharSize;
        m_stVosdInfo.astChnOverStyle[s32Chn].bUpdate = SV_TRUE;
        m_stVosdInfo.stChnName.bUpdate = SV_TRUE;
    }

    s32Ret = pthread_mutex_unlock(&m_stVosdInfo.mutexLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS; 
}

/******************************************************************************
 * 函数功能: 获取通道字符叠加的尺寸
 * 输入参数: s32Chn --- Vi输入通道
             s32OsdChn --- OSD叠加通道
 * 输出参数: penCharSize --- 通道叠加的字符大小尺寸
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_GetChnCharSize(sint32 s32Chn, sint32 s32OsdChn, MPP_VOSD_SIZE_E *penCharSize)
{
    if (s32OsdChn < 0 || s32OsdChn >= VIODE_MAX_CHN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == penCharSize)
    {
        return ERR_NULL_PTR;
    }

    *penCharSize = m_stVosdInfo.astChnOverStyle[s32OsdChn].nCharSize;

    return SV_SUCCESS; 
}

/******************************************************************************
 * 函数功能: BSD事件叠加喂狗时间
 * 输入参数: u32TimeMs --- 叠加保持时间(ms)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vosd_FeedBsdOverlay(uint32 u32TimeMs)
{
    return SV_SUCCESS; 
}

