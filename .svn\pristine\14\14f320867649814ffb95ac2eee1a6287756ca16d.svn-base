#!/bin/sh
# Useage: ./load3516cv300 [ -r|-i|-a ] [ sensor ]
#         -r : rmmod all modules
#         -i : insmod all modules
#         -a : rmmod all moules and then insmod them
#

####################MMZ config####################################

mem_total=256;              # 256M, total mem
mem_start=0x80000000;       # phy mem start
os_mem_size=64;             # 64M, os mem
mmz_start=0x84000000;       # mmz start addr
mmz_size=192M;              # 192M, mmz size

##################################################################

####################sensor config#################################

#sensor list: imx290, imx323, ov2718, ar0237
SNS_TYPE=imx290;            # sensor type

##################################################################

####################vo config#################################

#vou interface mode: default(cvbs), bt656, lcd 
vou_intf_mode="default";

##################################################################

####################clk config####################################

vedu_frequency=198000000;  # 198M, vedu clock frequency
vgs_frequency=297000000;  # 297M, vgs clock frequency
vpss_frequency=250000000;  # 250M, vpss clock frequency
ive_frequency=297000000;  # 297M, ive clock frequency

##################################################################

report_error()
{
	echo "******* Error: There's something wrong, please check! *****"
	exit 1
}

insert_audio()
{
	insmod hi3516cv300_aio.ko
	insmod hi3516cv300_ai.ko
	insmod hi3516cv300_ao.ko
	insmod hi3516cv300_aenc.ko
	insmod hi3516cv300_adec.ko
	insmod hi_acodec.ko
	#insmod extdrv/hi_tlv320aic31.ko 
	echo "insert audio"
}

remove_audio()
{
	#rmmod hi_tlv320aic31.ko
	rmmod hi3516cv300_adec
	rmmod hi3516cv300_aenc
	rmmod hi3516cv300_ao
	rmmod hi3516cv300_ai
	rmmod hi_acodec
	rmmod hi3516cv300_aio
	echo "remove audio"
}

bus_type="i2c";
pinmux_mode="i2c_mipi";
sensor_clk_freq=37125000;
intf_mode="default"	

####################sensor note#################################

#if you want to configure a new sensor, maybe you need to know the sensor parameters as follows
#(1)bus_type: i2c or ssp, write and read sensor register.
#(2)pinmux_mode: i2c_mipi/ssp_mipi/i2c_dc/ssp_dc. For example, if the bus_type is i2c and the sensor output DC sequence, the pinmux_mode would be i2c_dc.
#(3)sensor_clk_freq: sensor clock frequency, please read the datasheet about sensor.
#(4)intf_mode: if the interface mode of viu is bt1120, the value should be bt1120, else default.
#(5)viu_frequency: viu clock frequecny, you can adjust different frequency when the scene changed.
#(6)isp_div: isp clock divider. Input clock frequency is the same as viu clock frequency.

##################################################################
insert_sns()
{
    case $SNS_TYPE in
        imx290)
			bus_type="i2c";
			pinmux_mode="i2c_mipi";
			sensor_clk_freq=37125000;
			intf_mode="default";
			viu_frequency=198000000;  # 198M, viu clock frequency
			isp_div=2;                # isp div clk, freq = viu_clk_freq / div
            ;;
        imx323)
			bus_type="ssp";
			pinmux_mode="ssp_dc";
			sensor_clk_freq=37125000;
			intf_mode="default";
			viu_frequency=198000000;  # 198M, viu clock frequency
			isp_div=2;                # isp div clk, freq = viu_clk_freq / div
            ;;
        ov2718)
			bus_type="i2c";
			pinmux_mode="i2c_mipi";
			sensor_clk_freq=24000000;
			intf_mode="default";
			viu_frequency=198000000;  # 198M, viu clock frequency
			isp_div=2;                # isp div clk, freq = viu_clk_freq / div
            ;;
        ar0237)
			bus_type="i2c";
			pinmux_mode="i2c_mipi";
			sensor_clk_freq=27000000;
			intf_mode="default";
			viu_frequency=198000000;  # 198M, viu clock frequency
			isp_div=2;                # isp div clk, freq = viu_clk_freq / div
            ;;
        bt1120)
			intf_mode="bt1120";
			viu_frequency=198000000;  # 198M, viu clock frequency
			isp_div=2;                # isp div clk, freq = viu_clk_freq / div
            ;;
        bt656)                                                                           
                        intf_mode="default";                                                     
                        viu_frequency=198000000;  # 198M, viu clock frequency                     
                        isp_div=2;                # isp div clk, freq = viu_clk_freq / div
            ;;
        *)
            echo "xxxx Invalid sensor type $SNS_TYPE xxxx"
            report_error;;
    esac
}

insert_isp()
{
    case $SNS_TYPE in
        imx226 | imx117)
            insmod hi3516cv300_isp.ko update_pos=1 proc_param=30;
            ;;
        *)
            insmod hi3516cv300_isp.ko update_pos=0  proc_param=30;
            ;;
    esac
}

insert_ko()
{
	insert_sns
	insmod sys_config.ko vi_vpss_online=$b_arg_online

	# driver load
	insmod hi_osal.ko mmz=anonymous,0,$mmz_start,$mmz_size anony=1 || report_error
	insmod hi3516cv300_base.ko

	insmod hi3516cv300_sys.ko vi_vpss_online=$b_arg_online sensor=$SNS_TYPE

	insmod hi3516cv300_region.ko
	insmod hi3516cv300_vgs.ko vgs_clk_frequency=$vgs_frequency

	insmod hi3516cv300_viu.ko detect_err_frame=10 viu_clk_frequency=$viu_frequency isp_div=$isp_div input_mode=$intf_mode
	insert_isp;
	insmod hi3516cv300_vpss.ko vpss_clk_frequency=$vpss_frequency
	insmod hi3516cv300_vou.ko vou_mode=$vou_intf_mode
	#insmod hi3516cv300_vou.ko detectCycle=0 vou_mode=$vou_intf_mode #close dac detect
	#insmod hi3516cv300_vou.ko transparentTransmit=1 vou_mode=$vou_intf_mode #enable transparentTransmit

	insmod hi3516cv300_rc.ko
	insmod hi3516cv300_venc.ko
	insmod hi3516cv300_chnl.ko
	insmod hi3516cv300_vedu.ko vedu_clk_frequency=$vedu_frequency
	insmod hi3516cv300_h264e.ko
	insmod hi3516cv300_h265e.ko
	insmod hi3516cv300_jpege.ko
	insmod hi3516cv300_ive.ko save_power=1 ive_clk_frequency=$ive_frequency
	#insmod hi3516cv300_sensor.ko sensor_bus_type=$bus_type sensor_clk_frequency=$sensor_clk_freq sensor_pinmux_mode=$pinmux_mode
	
	insmod extdrv/hi_pwm.ko
	insmod extdrv/hi_piris.ko
	insmod extdrv/mtdex.ko
	insmod extdrv/peripheral.ko
	insert_audio

	insmod hi_mipi.ko

	echo "==== Your input Sensor type is $SNS_TYPE ===="

}

remove_ko()
{
	remove_audio

	rmmod hi_pwm
	rmmod hi_piris
	rmmod mtdex
	rmmod peripheral

	rmmod hi3516cv300_sensor

	rmmod hi3516cv300_ive

	rmmod hi3516cv300_rc
	rmmod hi3516cv300_jpege
	rmmod hi3516cv300_h264e
	rmmod hi3516cv300_h265e
	rmmod hi3516cv300_vedu
	rmmod hi3516cv300_chnl
	rmmod hi3516cv300_venc

	rmmod hi3516cv300_vou
	rmmod hi3516cv300_vpss
	rmmod hi3516cv300_isp
	rmmod hi3516cv300_viu
	rmmod hi_mipi
	
	rmmod hi3516cv300_vgs
	rmmod hi3516cv300_region

	rmmod hi3516cv300_sys
	rmmod hi3516cv300_base
	rmmod hi_osal
	rmmod sys_config
}

load_usage()
{
	echo "Usage:  ./load3516cv300 [-option] [sensor_name]"
	echo "options:"
	echo "    -i                       insert modules"
	echo "    -r                       remove modules"
	echo "    -a                       remove modules first, then insert modules"
	echo "    -sensor sensor_name      config sensor type [default: imx290]"
	echo "    -osmem os_mem_size       config os mem size [unit: M, default: 64]"
	echo "    -total_mem_size          config total mem size [unit: M, default: 256]"
	echo "    -offline                 vi/vpss offline"
	echo "    -h                       help information"
	echo -e "Available sensors: imx290 ov2718 imx323 ar0237"
	echo -e "notes: osmem option can't be used when mmz zone partition is enable\n\n"
	echo -e "for example online:   ./load3516cv300 -a -sensor imx290 -osmem 64 -total 256\n"
	echo -e "            offline:  ./load3516cv300 -a -sensor imx290 -osmem 64 -total 256 -offline\n"
}

calc_mmz_info()
{
	mmz_start=`echo "$mem_start $os_mem_size" | 
	awk 'BEGIN { temp = 0; }
	{
		temp = $1/1024/1024 + $2;
	} 
	END { printf("0x%x00000\n", temp); }'`

	mmz_size=`echo "$mem_total $os_mem_size" | 
	awk 'BEGIN { temp = 0; }
	{
		temp = $1 - $2;
	} 
	END { printf("%dM\n", temp); }'`
	echo "mmz_start: $mmz_start, mmz_size: $mmz_size"
}


######################parse arg###################################
b_arg_os_mem=0
b_arg_total_mem=0
b_arg_sensor=0
b_arg_insmod=0
b_arg_remove=0
b_arg_online=1
b_arg_restore=0

for arg in $@
do
	if [ $b_arg_total_mem -eq 1 ]; then
		b_arg_total_mem=0;
		mem_total=$arg;
		
		if [ -z $mem_total ]; then
			echo "[error] mem_total is null"
			exit;
		fi
	fi
	
	if [ $b_arg_os_mem -eq 1 ] ; then
		b_arg_os_mem=0;
		os_mem_size=$arg;

		if [ -z $os_mem_size ]; then
			echo "[error] os_mem_size is null"
			exit;
		fi
	fi

	if [ $b_arg_sensor -eq 1 ] ; then
		b_arg_sensor=0
		SNS_TYPE=$arg;
	fi

	case $arg in
		"-i")
			b_arg_insmod=1;
			;;
		"-r")
			b_arg_remove=1;
			;;
		"-a")			
			b_arg_insmod=1;
			b_arg_remove=1;
			;;
		"-h")
			load_usage;
			;;
		"-sensor")
			b_arg_sensor=1;
			;;
		"-osmem")
			b_arg_os_mem=1;
			;;
		"-total")
			b_arg_total_mem=1;
			;;
		"-restore")
			b_arg_restore=1;
			;;
		"-offline")
			b_arg_online=0;
			;;
	esac
done
#######################parse arg end########################

if [ $os_mem_size -ge $mem_total ] ; then
	echo "[err] os_mem[$os_mem_size], over total_mem[$mem_total]"
	exit;
fi

calc_mmz_info;

#######################Action###############################

if [ $# -lt 1 ]; then
    load_usage;
    exit 0;
fi

if [ $b_arg_remove -eq 1 ]; then
	remove_ko;
fi

if [ $b_arg_insmod -eq 1 ]; then
	insert_ko;
fi

if [ $b_arg_restore -eq 1 ]; then	
	sys_restore;
fi
