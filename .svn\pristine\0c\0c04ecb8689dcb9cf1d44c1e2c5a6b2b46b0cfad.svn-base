/******************************************************************************
Copyright (C) 2020-2022 广州敏视数码科技有限公司版权所有.

文件名：storage.h

作者: 许家铭       版本: v1.0.0(初始版本号)   日期: 2020-06-17

文件功能描述: 定义块存储设备管理功能

*******************************************************************************/
#ifndef _STORAGE_H
#define _STORAGE_H

#include <dirent.h>
#include "common.h"
#include "defines.h"
#include "config.h"
#include "op.h"

#ifdef __cplusplus
extern "C" {
#endif

#define STORAGE_MAX_BLK_NUM         9
#define STORAGE_PATH_SDCARD1        "/mnt/sdcard"
#define STORAGE_PATH_SDCARD2        "/mnt/sdcard2"
#define STORAGE_PATH_SDCARD3        "/mnt/sdcard3"
#define STORAGE_PATH_EMMC           "/userdata"
#define STORAGE_PATH_UDISK          "/mnt/udisk"
#define STORAGE_PATH_MEMORY         "/var"

/* 存储块设备挂载的文件系统信息 */
typedef struct tagStorageFsInfo_S
{
    long  totalSize;                /* 设备总容量(单位MB) */
    long  remainSize;               /* 设备当前剩余容量(单件MB) */
} SFS_INFO_S;

/* 存储录像状态 */
typedef enum tagStorageRecStat_E
{
    SREC_STAT_NO_RECORD = 0,              /* 无录像 */
    SREC_STAT_RECORDING,                  /* 录像中 */
    SREC_STAT_NO_ENOUGH_CAPACITY,         /* 容量不足暂时录像 */

    SREC_STAT_BUTT
} SREC_STAT_E;

#if 0
typedef struct FILESYSTEM_Info_tag
{
	SINFO_FS_E 			type;
	SINFO_PARTITION_E	partion;
}__attribute__((packed))FILESYSTEM_Info_t;
#endif
/* 存储块设备状态枚举 */
typedef enum tagBlkdevStatus
{
    SINFO_BLK_UNEXIST   = 0,              /* 未插卡 */
    SINFO_BLK_PROBING,                    /* 卡槽检测中 */
    SINFO_BLK_RUNNING,                    /* 正常运行 */
    SINFO_BLK_UNKNOWN
} SINFO_BLK_STATUS;

typedef struct tagBlkDevInfo_S
{
    uint32              u32BlkNum;                      /* 卡数目 */
    SINFO_BLK_STATUS    aBlkStat[STORAGE_MAX_BLK_NUM];  /* 状态数组 */
} BLK_DEV_INFO_S;

/* 模块当前运行状态
   运行状态名: runStatus
 */
typedef enum tagSInfoRunStat_E
{
    SINFO_RUN_UNINIT = 0,           /* 未初始化 */
    SINFO_RUN_MOUNTING,             /* 正在挂载状态 */
    SINFO_RUN_MOUNTED,              /* 完成挂载进行监测状态 */
    SINFO_RUN_UNMOUNTING,           /* 正在卸载退出状态 */

    SINFO_RUN_BUTT
} SINFO_RUN_E;

/* 存储设备状态(可读写性)
   存储设备名:
          deivcesStat: []
          deivcesMediaStat: []
 */
typedef enum tagSInfoDevStat_E
{
    SINFO_DEV_UNINSERT = 0,         /* 未插入设备(SD卡或U盘) */
    SINFO_DEV_ENABLE,               /* 可读写, 未挂载(可访问设备文件) */
    SINFO_DEV_MOUNTED,              /* 可读写, 已挂载(可访问文件系统) */
    SINFO_DEV_FORBID,               /* 被禁用, 存储设备被上层禁用或者电子锁开 */
    SINFO_DEV_EXCEPT,               /* 有异常 */
    SINFO_DEV_REPAIRING_FS,         /* 正在修复文件系统 */
    SINFO_DEV_REPAIRING_PART,       /* 正在修复分区 */
    SINFO_DEV_FORMATTING,           /* 正在格式化 */
    SINFO_DEV_CLEANING,             /* 正在清除备份区 */

    SINFO_STAT_BUTT
} SINFO_DEV_E;

/* 存储设备最近一次修复结果
   修复结果名: repairRes: []
 */
typedef enum tagSInfoRepairRes_E
{
    SINFO_REP_NULL,                 /* 无修复或未出现损坏 */
    SINFO_REP_SUCCESS,              /* 成功修复出现的损坏 */
    SINFO_REP_FAILURE,              /* 出现损坏但修复失败 */

    SINFO_REP_BUTT
} SINFO_REP_E;

/* 存储设备对外状态信息 */
typedef struct tagSInfoOut_E
{
    SV_BOOL bEnable;        /* 存储配置是否使能 */
    SV_BOOL bInsert;        /* 存储设备是否插入 */
    SV_BOOL bException;     /* 存储设备是否异常 */
    SV_BOOL bFull;          /* 存储设备是否已满 */
} SINFO_OUT_E;

/******************************************************************************
 * 函数功能: 初始化存储管理模块
 * 输入参数: pstSysParam --- 系统参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 STORAGE_Init(CFG_SYS_PARAM *pstSysParam);

/******************************************************************************
 * 函数功能: 去初始化存储管理模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 STORAGE_Fini();

/******************************************************************************
 * 函数功能: 启动存储管理模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
extern sint32 STORAGE_Start();

/******************************************************************************
 * 函数功能: 停止存储管理模块
 * 输入参数: bJoin --- 是否等待回收
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
extern sint32 STORAGE_Stop(SV_BOOL bJoin);

/******************************************************************************
 * 函数功能: 配置存储模块工作参数
 * 输入参数: pstSysParam --- 系统参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 STORAGE_SetConfig(CFG_SYS_PARAM *pstSysParam);

/******************************************************************************
 * 函数功能: 获取存储设备的信息
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
extern sint32 STORAGE_GetMediaStorageInfo(STORAGE_POS_E pos, SFS_INFO_S *pstInfo);

/******************************************************************************
 * 函数功能: 格式化存储设备
 * 输入参数: stCommand -- 格式化命令
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
extern sint32 STORAGE_FormatDeviceIO(STOR_CMD_S stCommand);

/******************************************************************************
 * 函数功能: 修复分区表
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
extern sint32 STORAGE_RepairPartionIO(STOR_CMD_S stCommand);

/******************************************************************************
 * 函数功能: 修复文件系统
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
extern sint32 STORAGE_RepairFileSysIO(STOR_CMD_S stCommand);

/******************************************************************************
 * 函数功能: 当前存储设备是否可写入
 * 输入参数: pos -- 存储设备
 * 输出参数: 无
 * 返回值  : 是否可写入
 * 注意    :   无
 *****************************************************************************/
extern SV_BOOL STORAGE_IsWritable(STORAGE_POS_E pos);

/******************************************************************************
 * 函数功能: 当前存储设备挂载
 * 输入参数: pos -- 存储设备
 * 输出参数: 无
 * 返回值  : 是否挂载
 * 注意    :   比STORAGE_IsWritable慢，掉电不会立刻返回
 *****************************************************************************/
extern SV_BOOL STORAGE_IsMount(STORAGE_POS_E pos);

/******************************************************************************
 * 函数功能: 当前存储设备是否可写入
 * 输入参数: pos  -- 设备位置
 * 输出参数: 无
 * 返回值  : 是否可写入
 * 注意    :
 *****************************************************************************/
extern SV_BOOL STORAGE_IsDeviceWritable(STORAGE_POS_E pos);

/******************************************************************************
 * 函数功能: 当前存储文件系统是否可写入
 * 输入参数: pos  -- 设备位置
 * 输出参数: 无
 * 返回值  : 是否可写入
 * 注意    :
 *****************************************************************************/
SV_BOOL STORAGE_IsMeidaWritable(STORAGE_POS_E pos);

/******************************************************************************
 * 函数功能: 设置当前录像状态
 * 输入参数: enRecordStat --- 录像状态
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
extern sint32 STORAGE_SetRecordStat(SREC_STAT_E enRecordStat);

/******************************************************************************
 * 函数功能: 获取当前SD卡剩余容量(MB)
 * 输入参数: pos -- 设备位置 bReflash --- 是否刷新即时容量值
 * 输出参数: 无
 * 返回值  : 剩余容量(MB)
 * 注意    :
 *****************************************************************************/
extern uint32 STORAGE_GetRemainSize(STORAGE_POS_E pos, SV_BOOL bRefresh);

/******************************************************************************
 * 函数功能: 获取挂载的文件路径
 * 输入参数: pos --- 设备位置
 * 输出参数: 无
 * 返回值  : 路径字符串或者NULL
 *****************************************************************************/
extern const char* STORAGE_GetMountPath();

/******************************************************************************
 * 函数功能: 获取挂载的文件路径
 * 输入参数: pos --- 设备位置
 * 输出参数: 无
 * 返回值  : 路径字符串或者NULL
 *****************************************************************************/
extern const char* STORAGE_GetMountExt();

/******************************************************************************
 * 函数功能: 存储设备锁，挂载下防止卸载
 * 输入参数: pos -- 位置
 * 输出参数: 无
 * 返回值  : 文件描述符
 * 注意    :   无
 *****************************************************************************/
extern int STORAGE_Lock(STORAGE_POS_E pos);

/******************************************************************************
 * 函数功能: 解锁
 * 输入参数: fd -- 文件描述符
 * 输出参数: 无
 * 返回值  : 无
 * 注意    :   无
 *****************************************************************************/
extern void STORAGE_UnLock(int fd);

/******************************************************************************
 * 函数功能: 获取挂载状态
 * 输入参数: pos -- 设备位置
 * 输出参数: 无
 * 返回值  : SV_TRUE -- 已挂载
              SV_FALSE -- 未挂载
 * 注意    :   无
 *****************************************************************************/
extern SV_BOOL STORAGE_IsStorageMounted(STORAGE_POS_E pos);


/******************************************************************************
 * 函数功能: 获取挂载状态EXT
 * 输入参数: pos -- 设备位置
 * 输出参数: 无
 * 返回值  : SV_TRUE -- 已挂载
              SV_FALSE -- 未挂载
 * 注意    :   无
 *****************************************************************************/
extern SV_BOOL STORAGE_IsMediaStorageMounted(STORAGE_POS_E pos);

/******************************************************************************
 * 函数功能: 获取文件系统类型
 * 输入参数: pos -- 设备位置
 * 输出参数: 无
 * 返回值  : 文件系统类型
 * 注意    :   无
 *****************************************************************************/
extern SINFO_FS_E STORAGE_MediaFilesystemType();

/******************************************************************************
 * 函数功能: 获取挂载路径
 * 输入参数: pos --- 设备位置
 * 输出参数: 无
 * 返回值  : 路径字符串或者NULL
 *****************************************************************************/
const char* STORAGE_GetMediaPath(STORAGE_POS_E pos);


/******************************************************************************
 * 函数功能: 获取存储状态信息(对外)
 * 输入参数: 存储信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 STORAGE_GetState(SINFO_OUT_E *pstSOut);

/******************************************************************************
 * 函数功能: 注册掉电标志
 * 输入参数: pbPowerFlag -- 掉电标志指针
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 将模块的掉电指针指向control的掉电标志，只读
 *****************************************************************************/
extern sint32 STORAGE_RegisterPowerFlag(const SV_BOOL *pbPowerFlag);

/******************************************************************************
 * 函数功能: 注册错误码
 * 输入参数: pu2ErrCode -- 错误码
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 将模块的掉电指针指向control的掉电标志，只读
 *****************************************************************************/
extern sint32 STORAGE_RegisterErrCode(const uint32 *pu2ErrCode);

/******************************************************************************
 * 函数功能: 获取修复状态位
 * 输入参数: pbRepair -- 修复状态位针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 STORAGE_GetRepairStat(SV_BOOL *pbRepair);


#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* _STORAGE_H */
