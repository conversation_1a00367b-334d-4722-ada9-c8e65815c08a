Index: include/config.h
===================================================================
--- include/config.h	(revision 6041)
+++ include/config.h	(working copy)
@@ -135,6 +135,7 @@
     uint32  u32RtpDstPort;           /* SOME/IP建立RTP连接的目标端口 */
     uint32  u32UdpComPort;           /* SOME/IP建立UDP连接的端口 */
     SV_BOOL bNoStreamAtBoot;         /* SOME/IP不直接向已记录的客户端发送媒体数据 */
+    sint32  phyTestMode;
 } CFG_NETWORK_PARAM;
 
 /* 媒体通道参数 */
Index: include/op.h
===================================================================
--- include/op.h	(revision 6041)
+++ include/op.h	(working copy)
@@ -206,7 +206,7 @@
     OP_EVENT_IR_ADAS_CALIBRATE,         // 0xa04a, TC639的ADAS测距功能
     OP_EVENT_DMS_ACC_CLOSE,             // 0xa04b, DMS的ACC关闭功能    
     OP_EVENT_REBOOT,                    // 0xa04C, 请求重启系统: NULL
-    
+    OP_EVENT_CHANGE_PHY_TEST_MODE,
     OP_BUTT = 0XAFFF
 } MSG_OPCODE_E;
 
@@ -303,6 +303,7 @@
     uint32  u32RtpDstPort;              /* SOME/IP建立RTP连接的目标端口 */
     uint32  u32UdpComPort;              /* SOME/IP建立UDP连接的端口 */
     SV_BOOL bNoStreamAtBoot;            /* SOME/IP不直接向已记录的客户端发送媒体数据 */
+    sint32  phyTestMode;
 } MSG_NETWORK_CFG;
 
 typedef struct tagTestWifiCfg_S
Index: src/control/control.c
===================================================================
--- src/control/control.c	(revision 6041)
+++ src/control/control.c	(working copy)
@@ -1020,7 +1020,28 @@
 
     return SV_SUCCESS;
 }
+sint32 callbackChangePhyTestMode(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
+{
+    MSG_NETWORK_CFG *pstNetworkCfg = (MSG_NETWORK_CFG *)pstMsgPkt->pu8Data;
+    char szCmd[128] = {0};
 
+    print_level(SV_INFO,"change phy test mode %d.\n", pstNetworkCfg->phyTestMode);
+    if (pstNetworkCfg->phyTestMode == 1) {
+        sprintf(szCmd,"/root/mdio_tool eth0 0x01 0x05 write 0x2000");
+    } else if (pstNetworkCfg->phyTestMode == 2) {
+        sprintf(szCmd,"/root/mdio_tool eth0 0x01 0x05 write 0x2000;/root/mdio_tool eth0 0x01 0x05 write 0x4000");
+    } else if (pstNetworkCfg->phyTestMode == 3) {
+        sprintf(szCmd,"/root/mdio_tool eth0 0x01 0x05 write 0x6000");
+    } else if (pstNetworkCfg->phyTestMode == 4) {
+        sprintf(szCmd,"/root/mdio_tool eth0 0x01 0x05 write 0x8000");
+    } else if (pstNetworkCfg->phyTestMode == 5) {
+        sprintf(szCmd,"/root/mdio_tool eth0 0x01 0x05 write 0xA000");
+    } else {
+        sprintf(szCmd,"/root/mdio_tool eth0 0x01 0x05 write 0x0");
+    }
+
+    SAFE_System(szCmd,NORMAL_WAIT_TIME);
+}
 sint32 callbackSetNetworkCfg(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
 {
     sint32 s32Ret = 0;
@@ -1155,7 +1176,6 @@
         }
         strcpy(szIpLast, pszEthIpaddr_tmp+1);
         print_level(SV_INFO, "change Router Setting in udhcpd_eth.conf, szIpLast: %s\n", szIpLast);
-
         sprintf(szCmd,"sed -i \"/^opt\trouter/copt\trouter\t%s\" %s", pstNetworkCfg->szGateway, UDHCPD_ETH_CONF);
         SAFE_System(szCmd,NORMAL_WAIT_TIME);
 
@@ -13436,7 +13456,12 @@
         print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
         return s32Ret;
     }
-
+    s32Ret = Msg_registerOpCallback(EP_CONTROL, OP_EVENT_CHANGE_PHY_TEST_MODE, callbackChangePhyTestMode);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
+        return s32Ret;
+    }
     s32Ret = Msg_registerOpCallback(EP_CONTROL, OP_REQ_GET_NETWORK_CFG, callbackGetNetworkCfg);
     if (SV_SUCCESS != s32Ret)
     {
Index: src/ipserver/http/jsonHandle.cpp
===================================================================
--- src/ipserver/http/jsonHandle.cpp	(revision 6041)
+++ src/ipserver/http/jsonHandle.cpp	(working copy)
@@ -1932,6 +1932,12 @@
     pstEthernet = cJSON_GetObjectItemCaseSensitive(pstNetwork, "ethernet");
     if (NULL != pstEthernet)
     {
+	pstTmp = cJSON_GetObjectItemCaseSensitive(pstEthernet, "phyTestMode");
+        if (NULL != pstTmp && !cJSON_IsNumber(pstTmp))
+        {
+			print_level(SV_ERROR,"phyTestMode Parameter is out of range!\n");
+            return SV_FALSE;
+        }
         pstTmp = cJSON_GetObjectItemCaseSensitive(pstEthernet, "enableDHCP");
         if (NULL != pstTmp && !cJSON_IsBool(pstTmp))
         {
@@ -4415,6 +4421,7 @@
         cJSON_Delete(pstNetwork);
         return MSG_DEFAULT_FAIL;
     }
+    cJSON_AddItemToObject(pstEthernet, "phyTestMode", cJSON_CreateNumber(stNetworkCfg.phyTestMode));
     cJSON_AddItemToObject(pstEthernet, "enableDHCP", cJSON_CreateBool(stNetworkCfg.bDHCPEnable));
 	cJSON_AddItemToObject(pstEthernet, "dhcpTimeout", cJSON_CreateNumber(stNetworkCfg.s32DHCPTimeout));
     cJSON_AddItemToObject(pstEthernet, "ipAddress", cJSON_CreateString(stNetworkCfg.szIpAddr));
@@ -7400,6 +7407,25 @@
         pstEthernet = cJSON_GetObjectItemCaseSensitive(pstNetwork, "ethernet");
         if (NULL != pstEthernet)
         {
+	    pstTmp = cJSON_GetObjectItemCaseSensitive(pstEthernet, "phyTestMode");
+            if (NULL != pstTmp)
+            {
+                if (stNetworkCfg.phyTestMode != pstTmp->valueint)
+                {
+                    bNetChanged = SV_TRUE;
+                    print_level(SV_INFO, "phyTestMode change to: %d\n", pstTmp->valueint);
+                }
+                stNetworkCfg.phyTestMode = pstTmp->valueint;
+                
+                stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
+                stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
+                s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_CHANGE_PHY_TEST_MODE, &stMsgPkt);
+                if (SV_SUCCESS != s32Ret)
+                {
+                    print_level(SV_ERROR, "OP_EVENT_CHANGE_PHY_TEST_MODE failed. [err=%#x]\n", s32Ret);
+                    return MSG_DEFAULT_FAIL;
+                }
+            }
             pstTmp = cJSON_GetObjectItemCaseSensitive(pstEthernet, "enableDHCP");
             if (NULL != pstTmp)
             {
Index: src/webui/config.html
===================================================================
--- src/webui/config.html	(revision 6041)
+++ src/webui/config.html	(working copy)
@@ -3609,6 +3609,20 @@
 									data-role="none" for="networkConfig-protocol-noStreamAtBoot"></label>
 					</div>
 				</div>
+                                <div class="configmenu">
+					<h1><p>{{getKeyLang "phy-test"}}</p></h1>
+					<div class="custom-select-box">
+						<label class="single_option_text" for="networkConfig-ethernet-phyTestMode" data-desc="testMode">testMode</label>
+						<div><select class="custom-select" id="networkConfig-ethernet-phyTestMode" data-role="none" value="{{networkConfig.ethernet.phyTestMode}}">
+							<option value="0" {{#equal testModeType 0}}selected="selected"{{/equal}}>mode_0</option>
+							<option value="1" {{#equal testModeType 1}}selected="selected"{{/equal}}>mode_1</option>
+							<option value="2" {{#equal testModeType 2}}selected="selected"{{/equal}}>mode_2</option>
+							<option value="3" {{#equal testModeType 2}}selected="selected"{{/equal}}>mode_3</option>
+							<option value="4" {{#equal testModeType 2}}selected="selected"{{/equal}}>mode_4</option>
+							<option value="5" {{#equal testModeType 2}}selected="selected"{{/equal}}>mode_5</option>
+						</select></div>
+					</div>
+				</div>
 			</div>
 			<div id="sysConfig" class="tab-configs Usr-Install">
 				<div class="configmenu" {{#unequal systemConfig.mcuType 1}}style="display: none;"{{/unequal}}>
Index: src/webui/js/webapp-language.js
===================================================================
--- src/webui/js/webapp-language.js	(revision 6041)
+++ src/webui/js/webapp-language.js	(working copy)
@@ -1024,6 +1024,7 @@
     "IntervalFrame-way":{"EN": "Interval Mode", "CN": "间隔方式", "JP": "インターバル方式", "ES": "Modo de Intervalo", "PT": "Modo de Intervalo", "RU": "Режим Интервала", "TUR": "Aralık Yöntemi", "DG": "Intervallmodus", "ITA": "Modalità Intervallo", "FRA": "Mode d'Intervalle"},
     "overtaking-PointNumLim":{"EN": "Max Optical Flow Points", "CN": "光流最大点数", "JP": "オプティカルフロー最大点数", "ES": "Puntos Máximos de Flujo Óptico", "PT": "Máx. Pontos de Fluxo Óptico", "RU": "Макс. точек оптического потока", "TUR": "Maks. Optik Akış Noktası", "DG": "Maximale Anzahl optischer Flusspunkte", "ITA": "Max Punti di Flusso Ottico", "FRA": "Points Max du Flux Optique"},
     "IntervalFrame-time":{"EN": "Time Interval", "CN": "时间间隔", "JP": "時間間隔", "ES": "Intervalo de Tiempo", "PT": "Intervalo de Tempo", "RU": "Временной Интервал", "TUR": "Zaman Aralığı", "DG": "Zeitintervall", "ITA": "Intervallo di Tempo", "FRA": "Intervalle de Temps"},
+	"phy-test":{"EN": "Phy Test", "CN": "PHY测试模式", "JP": "GIF", "ES": "GIF", "PT": "GIF", "RU": "GIF", "TUR": "GIF", "DG": "GIF", "ITA": "GIF", "FRA": "GIF"},
     "":{"EN": "", "CN": "", "JP": "", "ES": "", "PT": "", "RU": "", "TUR": "", "DG": "", "ITA": "", "FRA": ""}
 };
 
Index: src/webui/js/webapp-model.js
===================================================================
--- src/webui/js/webapp-model.js	(revision 6041)
+++ src/webui/js/webapp-model.js	(working copy)
@@ -461,6 +461,7 @@
             "dnsServer": "string",
             "ethernet": {
                 "enableDHCP": "boolean",
+	       "phyTestMode": "number",
                 "dhcpTimeout":"number",
                 "ipAddress": "string",
                 "subnetMask": "string",
