/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file:       r_udisk.h
author:     lyn
version:    1.0.0
date:       2023-12-08
function:   recorder udisk header file
notice:     none
*******************************************************************************/
#ifndef _R_UDISK_H_
#define _R_UDISK_H_

#include "common.h"
#include "r_pos.h"
namespace recorder{

class RUdisk : public RPos
{
private:
public:
    virtual sint32 GetRemainSize();        /* 获取剩余容量 */
    RUdisk(REC_POS_E pos, sint32 idx);
    ~RUdisk();
};

}

#endif /* _R_UDISK_H_ */
