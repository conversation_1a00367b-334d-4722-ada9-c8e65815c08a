#! /bin/sh
errCnt=0

while true
do
    sleep 5
    if [[ -f /tmp/AutoTest/ipsys && -f /tmp/AutoTest/factory ]]; then
	    killall -0 ipsys
	    if [ $? -ne 0 ]; then
		killall -9 ipsys
		killall -9 factory
		/tmp/AutoTest/ipsys >> /dev/null &
		sleep 3
		echo -e "reboot test ipsys" >> /mnt/sdcard/dms31_autoTestLog.txt
		errCnt=`expr $errCnt + 1`
	    else
		errCnt=0
	    fi
	    if [ $errCnt -ge 3 ]; then
	       reboot
	    fi
	    
	    killall -0 factory
	    if [ $? -ne 0 ]; then
		killall -9 factory
		/tmp/AutoTest/factory >> /dev/null &
		sleep 1
		echo -e "reboot test factory" >> /mnt/sdcard/dms31_autoTestLog.txt
	    fi
    else
	    killall -0 ipsys
	    if [ $? -ne 0 ]; then
		killall -9 ipsys
		killall -9 alg
		writeLog 0 "ipsys Not Running" > /dev/null
		/root/ipsys >> /dev/null &
		sleep 3
		errCnt=`expr $errCnt + 1`
	    else
		errCnt=0
	    fi
	    if [ $errCnt -ge 3 ]; then
	       reboot
	    fi
	    
	    killall -0 alg
	    if [ $? -ne 0 ]; then
		killall -9 alg
		writeLog 0 "alg Not Running" > /dev/null
		/root/alg >> /dev/null &
		sleep 1
	    fi
    fi
done
