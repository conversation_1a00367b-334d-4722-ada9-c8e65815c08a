/*
 * rk_aiq_types_amfnr_algo_v1.h
 *
 *  Copyright (c) 2019 Rockchip Corporation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#ifndef _RK_AIQ_TYPE_AMFNR_ALGO_V1_H_
#define _RK_AIQ_TYPE_AMFNR_ALGO_V1_H_
#include "amfnr/rk_aiq_types_amfnr_hw_v1.h"

RKAIQ_BEGIN_DECLARE

#define AMFNR_USE_XML_FILE_V1 (1)

#define AMFNR_USE_JSON_PARA_V1 (1)

#define AMFNR_FIX_VALUE_PRINTF_V1 (1)


RKAIQ_END_DECLARE

#endif

