/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file：      r_mediaManager.h
author:     lyn
version:    1.0.0
date:       2023-12-06
function:   recorder media data header file
notice:     none
*******************************************************************************/
#ifndef _R_MEDIA_MANAGER_H_
#define _R_MEDIA_MANAGER_H_

#include <memory>
#include "common.h"
#include <mutex>
#include <pthread.h>

#include "common.h"
#include "sharefifo.h"
#include "r_media.h"

using namespace std;
namespace recorder{

class RMediaManager{
private:
public:
    list<int> chnList;
    list<shared_ptr<RMedia>> mediaList;
    sint32 MediaInit(sint32 channel, char *path);                /* 创建媒体 */
    shared_ptr<RMedia> GetMedia(sint32 chn);                     /* 获取该通道对应的媒体队列 */
    sint32 Start();                                              /* 启动队列 */
    sint32 Stop();                                               /* 停止队列 */

};
}

#endif/* _R_MEDIA_MANAGER_H_ */
