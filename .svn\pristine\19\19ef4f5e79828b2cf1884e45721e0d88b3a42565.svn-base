Index: build/ada32v2/etc/config.xml
===================================================================
--- build/ada32v2/etc/config.xml	(版本 3144)
+++ build/ada32v2/etc/config.xml	(工作副本)
@@ -69,6 +69,10 @@
 
         <sharpness>50</sharpness>
 
+		<voFramerate>25</voFramerate>
+
+		<voResolution>1080P</voResolution>
+
     </Media>
 
     <Log> 
Index: build/ada32v2/etc/config_bak1.xml
===================================================================
--- build/ada32v2/etc/config_bak1.xml	(版本 3144)
+++ build/ada32v2/etc/config_bak1.xml	(工作副本)
@@ -69,6 +69,8 @@
 
         <sharpness>50</sharpness>
 
+		<voFramerate>25</voFramerate>
+
     </Media>
 
     <Log> 
Index: build/ada32v2/etc/config_bak2.xml
===================================================================
--- build/ada32v2/etc/config_bak2.xml	(版本 3144)
+++ build/ada32v2/etc/config_bak2.xml	(工作副本)
@@ -69,6 +69,8 @@
 
         <sharpness>50</sharpness>
 
+		<voFramerate>25</voFramerate>
+
     </Media>
 
     <Log> 
Index: build/ada32v2/etc/config_default.xml
===================================================================
--- build/ada32v2/etc/config_default.xml	(版本 3144)
+++ build/ada32v2/etc/config_default.xml	(工作副本)
@@ -69,6 +69,8 @@
 
         <sharpness>50</sharpness>
 
+		<voFramerate>25</voFramerate>
+
     </Media>
 
     <Log> 
Index: build/ada32v2/etc/config_default_bak.xml
===================================================================
--- build/ada32v2/etc/config_default_bak.xml	(版本 3144)
+++ build/ada32v2/etc/config_default_bak.xml	(工作副本)
@@ -69,6 +69,8 @@
 
         <sharpness>50</sharpness>
 
+		<voFramerate>25</voFramerate>
+
     </Media>
 
     <Log> 
Index: build/ada32v2/root/mcu_wtd.sh
===================================================================
--- build/ada32v2/root/mcu_wtd.sh	(不存在的)
+++ build/ada32v2/root/mcu_wtd.sh	(工作副本)
@@ -0,0 +1,37 @@
+#! /bin/sh
+
+# 之前和现在是否存在这个设备，假设不存在
+isPre=0
+isNow=0
+
+
+while true
+do
+	sleep 1
+	usbCmd=`lsusb | grep 2e3c:5740`
+	if [ "$usbCmd"x != ""x ]; then
+		# 存在这个设备
+		isNow=1
+		isPre=1
+	else
+		# 这个设备不存在
+		isNow=0
+	fi
+	
+	# 说明以前存在这个设备，现在这个设备消失了
+	if [ $isPre != $isNow ]; then
+		isPre=0
+		
+		# 重置这个xhci控制器
+		echo "reset xhci-hcd controler"
+		sleep 0.5
+		echo -n "xhci-hcd.0.auto" > /sys/bus/platform/drivers/xhci-hcd/unbind
+		sleep 0.5
+		echo -n "xhci-hcd.0.auto" > /sys/bus/platform/drivers/xhci-hcd/bind
+		sleep 10
+	fi
+done
+
+
+
+
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(版本 3144)
+++ src/alg/pd/pd.cpp	(工作副本)
@@ -652,6 +652,7 @@
     sint32 fd = -1;
     cJSON *pstJson = NULL, *pstList = NULL, *pstItem = NULL;
     cJSON *pstTimeStamp = NULL, *pstPdWorkMode = NULL, *pstGreenRoiNum = NULL, *pstYellowRoiNum = NULL, *pstRedRoiNum = NULL;
+	char szTimeStamp[1024];
     char szBuf[1024];
     char echo_szBuf[2048];
     CFG_PDS_PARAM *apstPdsParam[ALG_MAX_CHN] = {0};
@@ -706,7 +707,8 @@
         }
 
         cJSON_AddItemToArray(pstList, pstItem);
-        pstTimeStamp = cJSON_CreateNumber((double)astDumpInfo[i].s64TimeStamp);
+        sprintf(szTimeStamp, "%lld", (astDumpInfo[i].s64TimeStamp));
+        pstTimeStamp = cJSON_CreateRaw(szTimeStamp);
         if(NULL == pstTimeStamp)
         {
             print_level(SV_ERROR, "cJSON_CreateNumber failed.\n");
Index: src/peripheral/mcu/mcu.c
===================================================================
--- src/peripheral/mcu/mcu.c	(版本 3144)
+++ src/peripheral/mcu/mcu.c	(工作副本)
@@ -1211,7 +1211,7 @@
         if (SV_SUCCESS != s32Ret)
         {
             print_level(SV_ERROR, "mcu_GetMcuConfig failed.\n");
-            return SV_FAILURE;
+            goto error_exit;
         }
 
         if (m_stMcuInfo.enAlgTrigger != s8TriggerLevel)
@@ -1221,13 +1221,16 @@
             if (SV_SUCCESS != s32Ret)
             {
                 print_level(SV_ERROR, "mcu_GetMcuConfig failed.\n");
-                return SV_FAILURE;
+                goto error_exit;
             }
         }
     }
     pthread_mutex_unlock(&m_stMcuInfo.mutexRecvData);
+    return SV_SUCCESS;
 
-    return SV_SUCCESS;
+error_exit:
+    pthread_mutex_unlock(&m_stMcuInfo.mutexRecvData);
+    return SV_FAILURE;
 }
 
 sint32 mcu_OpenPort(const char *port, sint32 *s32McuFd)
@@ -1397,7 +1400,7 @@
 {
 	uint32 u32SleepTime;
     sint32 s32Count = -1;
-	sint32 s32Ret, i = 0, j = 0;
+	sint32 s32Ret, i = 0, j = 0, k = 0;
     sint32 s32DelayTime = 1000;
     sint32 s32PrintAlgWarn = 0;
 	sint32 s32McuFd = -1, s32Baudrate, s32CrcLen, s32SendSize;
@@ -1448,15 +1451,29 @@
 					continue;
 				}
 				pstMcuInfo->s32McuFd = s32McuFd;
-				pstMcuInfo->bMcuOpened = SV_TRUE;
-    
-                s32Ret = mcu_CheckConfig();
-                if (SV_SUCCESS != s32Ret)
+
+                for (k = 0; k < 5; k++)
                 {
-                    print_level(SV_ERROR, "mcu_CheckConfig failed.\n");
-					sleep_ms(1000);
+                    s32Ret = mcu_CheckConfig();
+                    if (SV_SUCCESS != s32Ret)
+                    {
+                        print_level(SV_ERROR, "mcu_CheckConfig failed.\n");
+    					sleep_ms(1000);
+                        continue;
+                    }
+                    else
+                    {
+                        break;
+                    }
+                }
+                if (k >= 5)
+                {
+                    close(pstMcuInfo->s32McuFd);
+                    sleep_ms(1000);
                     continue;
                 }
+
+				pstMcuInfo->bMcuOpened = SV_TRUE;
 			}
 		}
         else
