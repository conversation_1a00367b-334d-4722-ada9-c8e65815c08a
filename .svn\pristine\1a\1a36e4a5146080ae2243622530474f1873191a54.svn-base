#! /bin/sh
himm 0x100C0010 0x1100  # WiFi Power GPIO
himm 0x120B0400 0x10  # WiFi Power GPIO OUT
himm 0x120B0040 0x10  # WiFi Power GPIO H
cd /root/ko
./load3516ev300 -i -sensor0 imx307 -osmem 64M
fs_write_enable 0
#cd /root/ko/extdrv
#insmod cfg80211.ko
#insmod mac80211.ko
#insmod 8821cu.ko
#insmod 8821au.ko
cd /root
mount -t tmpfs -o size=5M tmpfs /var/ 
#ulimit -c 10240
#echo "/var/core.%e" > /proc/sys/kernel/core_pattern
touch /var/mainStream
touch /var/subStream
touch /var/audStream
mkdir /var/snap
mkdir /var/info
mkdir /var/log
mkdir /var/log/uploaded 
mkdir /var/run
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:.:/usr/lib/
cp /etc/config.xml /var/config.xml
tar -zxf /root/webui.tar.gz -C /var/
mv /dev/random /dev/random.orig
ln -s /dev/urandom /dev/random
if [ ! -f /etc/serialNumber ] && [ ! -f /etc/defaultIpaddr ];then
	telnetd &
fi
/root/ipsys >> /dev/null &
/root/wtd.sh >> /dev/null &
