1.下载iptables 源码，编译，安装；需要用到可执行文件 iptables及依赖的库
2.内核需要打开相关配置，可以使用rv1126_ada32c4_defconfig中的配置；
CONFIG_NF_CONNTRACK=y
CONFIG_NF_CONNTRACK_MARK=y
CONFIG_IP_NF_IPTABLES=y
CONFIG_IP_NF_FILTER=y
CONFIG_IP_NF_TARGET_REJECT=y
CONFIG_IP_NF_NAT=y
CONFIG_IP_NF_TARGET_MASQUERADE=y
CONFIG_IP_NF_TARGET_NETMAP=y
CONFIG_IP_NF_TARGET_REDIRECT=y
CONFIG_IP_NF_MANGLE=y

或者menuconfig打开：
Networking support-->Networking options --> Network packet filtering framework(Netfiler) --> Core Netfilter Configuration -->
打开这些配置: Netfilter connection tracking support/Connection mark tracking support/Netfilter Xtables support(required for ip_tables)

 Networking support-->Networking options --> Network packet filtering framework(Netfiler) --> IP:Netfilter Configuration-->
打开这些配置：IPv4 packet rejection/Ipv4 NAT/IP tables support(required for filtering/masq/NAT)/Packet filtering/REJECT target support
		iptables NAT support/MASQUERADE target support/NETMAP target support/Packet mangling

3.根据4g模块驱动编译指导，修改内核源码，打开相关内核配置；
4.编译出内核和新的4g模块驱动
5.重新编译wifi驱动，指定新编译的内核
6.在设备中：
ifconfig wlan0 192.168.602.1
udhcpd /var/udhcpd.conf &
iptables -t nat -A POSTROUTING -s ************/************* -o wwan0 -j MASQUERADE
echo 1 >/proc/sys/net/ipv4/ip_forward
hostapd -B /var/hostapd.conf

