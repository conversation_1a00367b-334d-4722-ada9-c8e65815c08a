Index: include/board.h
===================================================================
--- include/board.h	(revision 4451)
+++ include/board.h	(working copy)
@@ -360,6 +360,7 @@
 #define BOARD_C_ADA32V2_202264           "202264"   /* 202264客户 */
 #define BOARD_C_ADA32V2_100214           "100214"   /* 100214客户 */
 #define BOARD_C_ADA32V2_202613           "202613"   /* 202613客户 */
+#define BOARD_C_ADA32V2_111119           "111119"   /* 王瑞雷沃客户 */
 
 
 /* ADA32 版本文件路径 */
Index: include/canUtils.h
===================================================================
--- include/canUtils.h	(revision 4451)
+++ include/canUtils.h	(working copy)
@@ -22,7 +22,9 @@
 #define CAN_UTILS_DATA_RECV_TIMEOUT   (60*1000)   /* 数据接收超时时间 */
 #define CAN_UTILS_DATA_MAX_SIZE       10240       /* 数据缓冲区大小 */
 #define CAN_UTILS_DATA_BUF_MAX_NUM    5           /* 数据缓冲区个数 */
+#define CAN_UTILS_GPS_RECV_TIMEOUT   (5*1000)   /* 数据接收超时时间 */
 
+
 /* CAN数据头 */
 typedef struct tagCanUtilsDataHeader_S
 {
Index: include/config_factory.h
===================================================================
--- include/config_factory.h	(revision 4451)
+++ include/config_factory.h	(working copy)
@@ -43,6 +43,10 @@
     {
         pszAdminPassword = "installer";
     }
+    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
+    {
+        pszAdminPassword = "BVAdmin123";
+    }
 #endif
 #if (defined(BOARD_ADA32IR))
     else if(BOARD_IsCustomer(BOARD_C_ADA32IR_202461))
Index: include/mcu.h
===================================================================
--- include/mcu.h	(revision 4451)
+++ include/mcu.h	(working copy)
@@ -159,7 +159,19 @@
  *****************************************************************************/
 extern sint32 MCU_SendData(char *pszMcuCanData, sint32 s32DataLen, SV_BOOL bSingleFrame);
 
+/******************************************************************************
+* 函数功能: 封装CAN数据成USB包发给MCU，发送Trigger电平数据
+* 输入参数: pszMcuCanData - CAN数据
+         s32DataLen - 数据长度
+         bSingleFrame - 是否为单帧模式
+* 输出参数: 无
+* 返回值  : SV_SUCCESS - 成功
+         SV_FAILURE - 失败
+* 注意    : 无
+*****************************************************************************/
+extern sint32 MCU_SendTriggerData(PD_ROI_NUM_S *pszMsgIO, sint32 s32DataLen);
 
+
 #ifdef __cplusplus
 #if __cplusplus
 }
Index: include/op.h
===================================================================
--- include/op.h	(revision 4451)
+++ include/op.h	(working copy)
@@ -110,6 +110,9 @@
     OP_REQ_GET_ZOOM,                    // 0x38, 请求设置变焦配置参数
     OP_REQ_NEWJPEG,                     // 0x39, 请求一张最新的抓图: s32Param ([1-5]: 图片质量值), string(文件路径, 如果不指定则默认为/var/snap/snap0.jpeg)
     OP_REQ_GET_PLAYABCK,                // 0x3a, 请求回播参数
+    OP_REQ_CAN_SINGLE,                  // 0X3b, 请求获取CAN数据: MSG_CAN_DATA_S
+    OP_REQ_SET_IO_STATUS,               // 0X3c, 请求设置三线触发电平: PD_ROI_NUM_S
+    OP_REQ_GET_IO_STATUS,               // 0X3d, 请求获取Alarm IN电平: s32Param
 
     /* 事件类型，无回包 */
     OP_EVENT_BEGIN  = 0XA000,           /* 事件类型OP码起始值 */
@@ -587,6 +590,7 @@
 /* CAN数据消息包 */
 typedef struct tagMsgCanData_S
 {
+    uint32 u32CanId;
     SV_BOOL bSingleFrame;               /* 是否为单帧模式 */
     sint32 s32DataLen;                  /* 数据长度 */
     char szCanData[256];                /* 数据内容 */
@@ -628,6 +632,13 @@
     char stWsInfo[256];                 /* websocket信息 */
 } MSG_WEBSOCKET_INFO_S;
 
+/* 三线触发线状态 */
+typedef struct tagTrigger_S
+{
+    sint32      s32Red    :8;            /* 红线 */
+    sint32      s32Green  :8;               /* 黄线 */
+    sint32      s32Yellow :8;              /* 绿线 */
+} MSG_IO_STAT_S;
 #ifdef __cplusplus
 #if __cplusplus
 }
Index: make_sdk/make.sh
===================================================================
--- make_sdk/make.sh	(revision 4451)
+++ make_sdk/make.sh	(working copy)
@@ -33,7 +33,8 @@
     bIdx=0
     SVN_DATETIME=`svn info ../ | grep -E -m 1 '最后修改的时间: *|Last Changed Date: *' | grep -w "[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}" -o`
     SVN_VERSION=`svn info | grep -E -m 1 '版本: [0-9]*|Revision: [0-9]*' | grep -w "[0-9]*" -o`
-    DATE_TIME=`date -d "${SVN_DATETIME}" '+%y%m%d'`
+    DATE_TIME="240812"
+	#DATE_TIME=`date -d "${SVN_DATETIME}" '+%y%m%d'`
 	board=`echo ${1,,}`
 	TOP_DIR=$PWD/../
 	SDK_DIR=$PWD/../../HD900_20${DATE_TIME}.${SVN_VERSION}/
@@ -187,7 +188,8 @@
     print_help
     SVN_DATETIME=`svn info ../ | grep -E -m 1 '最后修改的时间: *|Last Changed Date: *' | grep -w "[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}" -o`
     SVN_VERSION=`svn info | grep -E -m 1 '版本: [0-9]*|Revision: [0-9]*' | grep -w "[0-9]*" -o`
-    DATE_TIME=`date -d "${SVN_DATETIME}" '+%y%m%d'`
+	DATE_TIME="240812"
+    #DATE_TIME=`date -d "${SVN_DATETIME}" '+%y%m%d'`
 	SDK_DIR=$PWD/../../HD900_20${DATE_TIME}.${SVN_VERSION}/
 	
     while [ 1 ]; do
Index: src/alg/alg.cpp
===================================================================
--- src/alg/alg.cpp	(revision 4451)
+++ src/alg/alg.cpp	(working copy)
@@ -631,6 +631,31 @@
     return SV_SUCCESS;
 }
 
+/******************************************************************************
+ * 函数功能: 读取到单帧CAN数据的回调函数
+ * 返回值  : SV_SUCCESS - 成功
+ 			 SV_FAILURE - 其它错误
+ * 说明    :   无 
+ *****************************************************************************/
+static sint32 callbackReadCanData(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
+{	
+
+    sint32 i, s32Ret = 0;
+	MSG_CAN_DATA_S stMsgCanData = {0};
+    memcpy(&stMsgCanData, pstMsgPkt->pu8Data, pstMsgPkt->u32Size);
+    
+    /* 打印can 数据 */
+    print_level(SV_DEBUG, "alg recv CANid[%#x] data: \n", stMsgCanData.u32CanId);
+    for (i=0; i < CAN_UTILS_START_FRAME_LEN; i++){
+        printf("%02X ", stMsgCanData.szCanData[i]);
+    }
+    printf("\n ");
+
+
+    return SV_SUCCESS;
+}
+
+
 sint32 alg_RegisterMsgCallBack()
 {
     sint32 s32Ret;
@@ -719,6 +744,13 @@
         return s32Ret;
     }
 
+    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_CAN_SINGLE, callbackReadCanData);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
+        return s32Ret;
+    }
+
 #if (defined(BOARD_DMS31V2) || defined(BOARD_ADA42V1) || defined(BOARD_ADA42PTZV1) || defined(BOARD_ADA47V1))
     s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_GPS_DATA, callbackUpdateGpsData);
     if (SV_SUCCESS != s32Ret)
@@ -733,6 +765,7 @@
         print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
         return s32Ret;
     }
+    
 #endif
 
 #if (defined(BOARD_ADA900V1))
@@ -1686,6 +1719,8 @@
             continue;
         }
         bGetMediaBuf = SV_TRUE;
+
+        
 #endif
     }
 
Index: src/alg/demo/media/main.cpp
===================================================================
--- src/alg/demo/media/main.cpp	(revision 4451)
+++ src/alg/demo/media/main.cpp	(working copy)
@@ -55,6 +55,7 @@
 #include "cJSON.h"
 #include "rs485.h"
 
+
 /* 行人检测输出信息 */
 typedef struct tagPdDumpInfo_S
 {
@@ -192,7 +193,31 @@
     return SV_SUCCESS;
 }
 
+/******************************************************************************
+ * 函数功能: 雷沃客户读取到单帧CAN数据的回调函数
+ * 返回值  : SV_SUCCESS - 成功
+ 			 SV_FAILURE - 其它错误
+ * 说明    :   无 
+ *****************************************************************************/
+static sint32 callbackReadCanData(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
+{	
 
+    sint32 i, s32Ret = 0;
+	MSG_CAN_DATA_S stMsgCanData = {0};
+    memcpy(&stMsgCanData, pstMsgPkt->pu8Data, pstMsgPkt->u32Size);
+    
+    /* 打印can 数据 */
+    print_level(SV_DEBUG, "alg recv CANid[%#x] data: \n", stMsgCanData.u32CanId);
+    for (i=0; i < CAN_UTILS_START_FRAME_LEN; i++){
+        printf("%02X ", stMsgCanData.szCanData[i]);
+    }
+    printf("\n ");
+
+
+    return SV_SUCCESS;
+}
+
+
 /******************************************************************************
  * 函数功能: 通过socket获取MediaBuf Fd / Get the MediaBuf Fd via socket
  * 输入参数: s32SocketFd --- socket fd
@@ -638,6 +663,8 @@
     MSG_CAN_DATA_S stMsgCanData = {0};
 
     memset(&stMsgCanData, 0, sizeof(stMsgCanData));
+    stMsgCanData.u32CanId = 0x18fada32;
+
     stMsgCanData.bSingleFrame = SV_TRUE;
     strcpy(stMsgCanData.szCanData, "Hello!");
     stMsgCanData.s32DataLen = strlen(stMsgCanData.szCanData);
@@ -959,6 +986,86 @@
     remove(szJpegPath);
 }
 
+
+/******************************************************************************
+ * 函数功能: 设置三触发线电平
+ * 输入参数: 
+ * 输出参数: 
+ * 返回值  : SV_SUCCESS - 成功
+             SV_FAILURE - 其它错误
+ * 说明    : 无
+ *****************************************************************************/
+void Media_SetIOStat()
+{
+    sint32 s32Ret = 0;
+    sint32 delayTime = 2000; //延时2秒
+    MSG_PACKET_S stMsgPkt = {0};
+
+    PD_ROI_NUM_S stIOSet = {0};
+
+    int cnt = 5; //循环5次
+    SV_BOOL trigger = 1;
+    while(cnt--) {
+        trigger = trigger ? 0 : 1;
+
+        stIOSet.s32RedRoiNum = !trigger;
+        stIOSet.s32YellowRoiNum = trigger;
+        stIOSet.s32GreenRoiNum = trigger;
+        
+        stMsgPkt.pu8Data = (uint8 *)&stIOSet;
+        stMsgPkt.u32Size = sizeof(stIOSet);
+        print_level(SV_DEBUG, "Send Alarm Out Status: [R:%d, Y:%d, G:%d]!\n", stIOSet.s32RedRoiNum, stIOSet.s32YellowRoiNum, stIOSet.s32GreenRoiNum);
+        
+        s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_SET_IO_STATUS, &stMsgPkt, NULL, 0);
+        if (SV_SUCCESS != s32Ret)
+        {
+            print_level(SV_ERROR, "OP_REQ_SET_IO_STATUS failed. [err=%#x]\n", s32Ret);
+            return;
+        }
+        print_level(SV_DEBUG, "wait %d ms! [last %d time]\n", delayTime, cnt);
+        sleep_ms(delayTime);
+    }
+    
+    stIOSet.s32RedRoiNum = stIOSet.s32YellowRoiNum = stIOSet.s32GreenRoiNum = 0;  //清空
+    stMsgPkt.pu8Data = (uint8 *)&stIOSet;
+    stMsgPkt.u32Size = sizeof(stIOSet);
+    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_SET_IO_STATUS, &stMsgPkt, NULL, 0);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "OP_REQ_SET_IO_STATUS failed. [err=%#x]\n", s32Ret);
+        return;
+    }
+}
+
+/******************************************************************************
+ * 函数功能: 读取Alarm IN电平信息
+ * 输入参数: 
+ * 输出参数: 
+ * 返回值  : SV_SUCCESS - 成功
+             SV_FAILURE - 其它错误
+ * 重要说明    : 需要Alarm IN接入高电平产生/var/info/mcu文件后才能正常调用该函数
+             读取电平，否则返回错误代码err=0xa0008005。
+ *****************************************************************************/
+void Media_GetIOStat()
+{
+    sint32 s32Ret = 0;
+    MSG_PACKET_S stRetPkt = {0};
+    sint32 s32AlarmIN =SV_FALSE;
+
+    stRetPkt.stMsg.s32Param = s32AlarmIN;
+    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_GET_IO_STATUS, NULL, &stRetPkt, sizeof(sint32));
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "OP_REQ_GET_IO_STATUS failed. [err=%#x]\n", s32Ret);
+        return;
+    }
+
+    s32AlarmIN = stRetPkt.stMsg.s32Param;
+    print_level(SV_DEBUG, "Read Alarm In Status: [%d]!\n", s32AlarmIN);   
+
+}
+
+
 void Media_Demo_Usage()
 {
     printf("Usage : \n");
@@ -975,6 +1082,9 @@
     //printf("\t  9) Test Switch In/Out function.\n");
     printf("\t  9) Send bluetooth data\n");
     printf("\t  A) Capture JPEG Form Camera.\n");
+    printf("\t  B) Set IO Alarm Out.\n");
+    printf("\t  C) Get Alarm In.\n");
+
     printf("\t  Q) Quit.\n");
     printf("Select Number:");
 
@@ -1204,6 +1314,13 @@
     }
 #endif
 
+    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_CAN_SINGLE, callbackReadCanData);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
+        return s32Ret;
+    }
+
     g_running = 1;
     s32Ret = pthread_create(&thread, NULL, media_Watch_BufferFd, NULL);
     Media_Demo_Usage();
@@ -1251,6 +1368,13 @@
             case 'A':
                 Media_CaptureJpeg();
                 break;
+            case 'B':
+                Media_SetIOStat();
+                break;
+            case 'C':
+                Media_GetIOStat();
+                break;
+
             default:
                 Media_Demo_Usage();
         }
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 4451)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -6401,9 +6401,6 @@
         case E_PDS_P:
             modefilelist[0] = PD_MODEL_RGB_P;
 
-            if (BOARD_IsCustomer(BOARD_C_ADA32V2_FTC))
-                modefilelist[0] = PD_MODEL_RGB_P_FTC;
-
             if (BOARD_IsCustomer(BOARD_C_ADA32V2_201266B) || BOARD_IsCustomer(BOARD_C_ADA32V2_201266C))
                 modefilelist[0] = PD_MODEL_RGB_P_201266B;
 
Index: src/control/control.c
===================================================================
--- src/control/control.c	(revision 4451)
+++ src/control/control.c	(working copy)
@@ -6212,6 +6212,63 @@
     return SV_SUCCESS;
 }
 
+sint32 callbackSetIOStat(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
+{
+#if (!defined(PLATFORM_SSC335) && !defined(BOARD_HDW845V1))
+    PD_ROI_NUM_S *pstRoiNum = {0};
+    sint32 s32Ret = 0;
+    pstRoiNum = (PD_ROI_NUM_S *)(pstMsgPkt->pu8Data);
+//    print_level(SV_DEBUG, "callback SET IO Stat [R:%d, Y:%d, G:%d]\n", pstRoiNum->s32RedRoiNum, pstRoiNum->s32YellowRoiNum, pstRoiNum->s32GreenRoiNum);
+    s32Ret = MCU_SendTriggerData(pstRoiNum, sizeof(pstRoiNum));
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "mcu_SendlData failed!\n");
+        return SV_FAILURE;
+    }
+#endif
+    return SV_SUCCESS;
+}
+
+sint32 callbackGetIOStat(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
+{
+    sint32 s32Ret = 0;
+    uint8 u8AlarmIn = SV_FALSE;
+
+    char szJsonRet[1024] = {0};
+    cJSON *pstJson = NULL, *pstTmp = NULL;
+
+    print_level(SV_DEBUG, "before get JSON");
+    s32Ret = cJSON_GetJson(DUMP_INFO_MCU, szJsonRet);
+    if (SV_SUCCESS != s32Ret)
+    {
+        return s32Ret;
+    }
+
+    pstJson = cJSON_Parse(szJsonRet);
+    if (NULL == pstJson)
+    {
+        print_level(SV_ERROR, "cJSON_Parse failed.\n");
+        return SV_FAILURE;
+    }
+
+    pstTmp = cJSON_GetObjectItemCaseSensitive(pstJson, "bAlarmIn");
+    if (NULL == pstTmp)
+    {
+        print_level(SV_ERROR, "keyword Status is not exist.\n");
+        return SV_FAILURE;
+    }
+
+    u8AlarmIn = pstTmp->valueint;
+
+    cJSON_Delete(pstJson);
+
+    print_level(SV_DEBUG, "read Alarm In GPIO: [%d]\n", u8AlarmIn);
+
+    pstRetPkt->stMsg.s32Param = u8AlarmIn;
+    return SV_SUCCESS;
+}
+
+
 #if (defined(BOARD_DMS31V2) || defined(BOARD_ADA42V1) || defined(BOARD_ADA42PTZV1) || defined(BOARD_WFCR20S2) || defined(BOARD_ADA47V1))
 sint32 callbackResetCell(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
 {
@@ -12480,7 +12537,21 @@
         print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
         return s32Ret;
     }
+    
+    s32Ret = Msg_registerOpCallback(EP_CONTROL, OP_REQ_SET_IO_STATUS, callbackSetIOStat);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
+        return s32Ret;
+    }
 
+    s32Ret = Msg_registerOpCallback(EP_CONTROL, OP_REQ_GET_IO_STATUS, callbackGetIOStat);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
+        return s32Ret;
+    }
+
 #if (defined(BOARD_DMS31V2) || defined(BOARD_ADA42V1) || defined(BOARD_ADA42PTZV1) || defined(BOARD_WFCR20S2) || defined(BOARD_ADA47V1))
     if (BOARD_DMS31V2_V2 == BOARD_GetVersion())
     {
Index: src/ipserver/rtsp2/librtsp2.cpp
===================================================================
--- src/ipserver/rtsp2/librtsp2.cpp	(revision 4451)
+++ src/ipserver/rtsp2/librtsp2.cpp	(working copy)
@@ -396,8 +396,18 @@
                         }
                         break;
                     case xop::VIDEO_FRAME_P:
-                        stMainFrame.size -= 4;
-                        memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
+                        if (stMainFrame.size <= MaxPacketSize)
+                        {
+                            if (stMainFrame.size > 4)
+                            {
+                                stMainFrame.size -= 4;
+                                memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
+                            }
+                            else
+                            {
+                                memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
+                            }
+                        }
                         break;
                     case xop::AUDIO_FRAME:
                         memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
Index: src/media/rockchip/com/mpp_vosd.cpp
===================================================================
--- src/media/rockchip/com/mpp_vosd.cpp	(revision 4451)
+++ src/media/rockchip/com/mpp_vosd.cpp	(working copy)
@@ -2276,6 +2276,12 @@
 {
     VOSD_BODY_TYPE enType = VOSD_BODY_TYPE_HARDWARE;
     m_stVosdInfo.stBodyAttr[enType].bShow = SV_TRUE;
+#if (defined(ADA32SDK) || defined(ADA32NSDK))
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_ARCURE))
+    {
+        m_stVosdInfo.stBodyAttr[enType].bShow = SV_FALSE;
+    }
+#endif
     strcpy(m_stVosdInfo.stBodyAttr[enType].hardware.hardware, pstVosdConf->szHardwareVer);
     strcpy(m_stVosdInfo.stBodyAttr[enType].hardware.firmware, pstVosdConf->szFirmwareVer);
     m_stVosdInfo.stBodyAttr[enType].hardware.u32color = GUI_COLOR_BLUE;
@@ -2430,6 +2436,12 @@
 {
     VOSD_BODY_TYPE enType = VOSD_BODY_TYPE_WIFI;
     m_stVosdInfo.stBodyAttr[enType].bShow = SV_TRUE;
+#if (defined(ADA32SDK) || defined(ADA32NSDK))
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_ARCURE))
+    {
+        m_stVosdInfo.stBodyAttr[enType].bShow = SV_FALSE;
+    }
+#endif
     strcpy(m_stVosdInfo.stBodyAttr[enType].wifi.wifi, pstVosdConf->szWifiName);
     m_stVosdInfo.stBodyAttr[enType].wifi.u32color = GUI_COLOR_BLUE;
     m_stVosdInfo.stBodyAttr[enType].wifi.xpos = 0.01;
@@ -2595,6 +2607,12 @@
 {
     VOSD_BODY_TYPE enType = VOSD_BODY_TYPE_AHD;
     m_stVosdInfo.stBodyAttr[enType].bShow = SV_TRUE;
+#if (defined(ADA32SDK) || defined(ADA32NSDK))
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_ARCURE))
+    {
+        m_stVosdInfo.stBodyAttr[enType].bShow = SV_FALSE;
+    }
+#endif
     strcpy(m_stVosdInfo.stBodyAttr[enType].ahd.ahd, pstVosdConf->szVoFormat);
     m_stVosdInfo.stBodyAttr[enType].ahd.u32color = GUI_COLOR_BLUE;
     m_stVosdInfo.stBodyAttr[enType].ahd.xpos = 0.99;
@@ -2683,6 +2701,12 @@
 {
     VOSD_BODY_TYPE enType = VOSD_BODY_TYPE_CVBS;
     m_stVosdInfo.stBodyAttr[enType].bShow = SV_TRUE;
+#if (defined(ADA32SDK) || defined(ADA32NSDK))
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_ARCURE))
+    {
+        m_stVosdInfo.stBodyAttr[enType].bShow = SV_FALSE;
+    }
+#endif
     strcpy(m_stVosdInfo.stBodyAttr[enType].cvbs.cvbs, pstVosdConf->szExternScreenFormat);
     m_stVosdInfo.stBodyAttr[enType].cvbs.u32color = GUI_COLOR_BLUE;
     m_stVosdInfo.stBodyAttr[enType].cvbs.xpos = 0.99;
Index: src/media/rockchip/rv1126/mpp_vi.c
===================================================================
--- src/media/rockchip/rv1126/mpp_vi.c	(revision 4451)
+++ src/media/rockchip/rv1126/mpp_vi.c	(working copy)
@@ -329,6 +329,12 @@
         sprintf(pszIqfilesPath, "%s/fisheye", tmpIqfilesPath);
     }
 
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_111119) && BOARD_IsSVersion(BOARD_S_2M3))
+    {
+        char szTmpIqfilesPath[128];
+        strcpy(szTmpIqfilesPath, pszIqfilesPath);
+        sprintf(pszIqfilesPath, "%s/fisheye", szTmpIqfilesPath);
+    }
 
 #elif (BOARD == BOARD_ADA47V1)
     if (SEN_TYPE_IMX415 == enSenType)
@@ -421,6 +427,10 @@
         print_level(SV_ERROR, "mpp_vi_Get_ISP_PATH failed! [err=%d]\n", s32Ret);
         return s32Ret;
     }
+    else
+    {
+        print_level(SV_INFO, "szIqfilesPath=%s\n", szIqfilesPath);
+    }
 
 #if ((BOARD == BOARD_ADA47V1))
     RK_ISP_Stop(ViPipe);
Index: src/peripheral/can/can.c
===================================================================
--- src/peripheral/can/can.c	(revision 4451)
+++ src/peripheral/can/can.c	(working copy)
@@ -3627,7 +3627,8 @@
 static sint32 callbackSendCanData(MSG_PACKET_S * pstMsgPkt, MSG_PACKET_S * pstRetPkt)
 {
     MSG_CAN_DATA_S *pstMsgCanData = (MSG_CAN_DATA_S *)pstMsgPkt->pu8Data;
-    sint32 s32Ret = CAN_SendUserData(pstMsgCanData->szCanData, pstMsgCanData->s32DataLen, pstMsgCanData->bSingleFrame);
+    sint32 s32Ret = CAN_SendUserDataMsg(pstMsgCanData->szCanData, pstMsgCanData->s32DataLen,\
+                    pstMsgCanData->bSingleFrame, pstMsgCanData->u32CanId);
     if (SV_SUCCESS != s32Ret)
     {
         print_level(SV_ERROR, "CAN_SendUserData failed!\n");
@@ -4167,6 +4168,142 @@
 	return SV_SUCCESS;
 }
 
+sint32 CAN_SendUserData(const char *pszCanData, const sint32 s32DataLen, SV_BOOL bSingleFrame)
+{
+    sint32 s32Ret, i, k;
+	uint32 u32Canid = 0;
+    sint32 s32BytesNum = 8;
+    struct can_frame stCanFrame;
+    uint8 u8DataCnt = 0, u8FrameOffset = 0;
+	uint8 s32ValidDataCnt = 0, s32CanDataFrameCnt = 0;
+    char *pszData = NULL;
+	char szCmd[128] = {0};
+	char szBuf[64] = {0};
+    sint32 s32DetectCnt = 5;    // 连续5次发送数据之后如果TX没有增长的话，认为没有外接CAN接收设备，重启一次CAN
+    sint32 s32CanLastTxCount = 0, s32CanTxCount = 0;
+    static sint32 s_s32SendCnt = 0;
+
+    if (NULL == pszCanData || 0 == s32DataLen || s32DataLen > CAN_MAX_DATA_LEN)
+    {
+        print_level(SV_ERROR, "data len is out of range, dataLen: %d, maxLen: %d\n", s32DataLen, CAN_MAX_DATA_LEN);
+        return SV_FAILURE;
+    }
+
+    if (bSingleFrame && s32DataLen > 8)
+    {
+        print_level(SV_ERROR, "single frame mode, but data len larger than 8, dataLen: %d\n", s32DataLen);
+        return SV_FAILURE;
+    }
+
+    s_s32SendCnt++;
+    if (s_s32SendCnt >= s32DetectCnt)
+    {
+        strncpy(szCmd, "ifconfig | grep can0 -A3 | grep TX | awk -F ':' '{print$2}' | awk -F ' ' '{print$1}'", 128);
+    	s32Ret = can_SafeSystem_Recv(szCmd, szBuf, 64);
+    	if (SV_SUCCESS == s32Ret && 0 != strlen(szBuf))
+    	{
+    	    s32CanLastTxCount = atoi(szBuf);
+    	}
+    }
+    
+    pszData = pszCanData;
+    s32ValidDataCnt = s32DataLen;
+	if (s32ValidDataCnt % s32BytesNum != 0) 	//s32BytesNum data bit
+		s32CanDataFrameCnt = (s32ValidDataCnt / s32BytesNum) + 1;  //有效数据帧数
+	else
+		s32CanDataFrameCnt = s32ValidDataCnt / s32BytesNum;
+	
+	/* can data init */
+    pthread_mutex_lock(&m_stCanInfo.mutexSetConfig);
+    u32Canid = strtol(m_stCanInfo.szPdsCanid, NULL, 16);
+    stCanFrame.can_id = can_GetCanid(u32Canid);
+	pthread_mutex_unlock(&m_stCanInfo.mutexSetConfig);
+    stCanFrame.can_dlc = 8;
+
+    if (!bSingleFrame)
+    {
+    	/* starting stCanFrame */
+    	k = 0;            
+        stCanFrame.data[k++] = 0xfe;
+        stCanFrame.data[k++] = s32ValidDataCnt;
+        stCanFrame.data[k++] = crc8_check((uint8 *)pszCanData, s32ValidDataCnt);
+        while (k < 8)
+    	{
+            stCanFrame.data[k++] = 0xfe;
+        }
+        s32Ret = can_WriteFrame(&stCanFrame);
+    	if (SV_SUCCESS != s32Ret)
+    	{
+    		print_level(SV_ERROR, "can_WriteFrame failed.\n");
+    		return SV_FAILURE;
+    	}
+    }
+
+	/* data stCanFrame */
+    u8FrameOffset = 0;
+    u8DataCnt = (s32ValidDataCnt < s32BytesNum) ? s32ValidDataCnt : s32BytesNum;
+	for (i = 0; i<s32CanDataFrameCnt; i++)
+	{
+        if (i == s32CanDataFrameCnt-1)
+        {
+            u8DataCnt = s32ValidDataCnt - u8FrameOffset;
+        }
+        
+		memcpy(stCanFrame.data, pszCanData+u8FrameOffset, u8DataCnt);        
+        stCanFrame.can_dlc = u8DataCnt;
+        s32Ret = can_WriteFrame(&stCanFrame);
+    	if (SV_SUCCESS != s32Ret)
+    	{
+    		break;
+    	}
+		u8FrameOffset += u8DataCnt;
+        
+	}
+    if (SV_SUCCESS != s32Ret)
+	{
+		print_level(SV_ERROR, "can_WriteFrame failed.\n");
+		return SV_FAILURE;
+	}
+    
+    if (!bSingleFrame)
+    {
+    	/* ending stCanFrame */
+    	k = 0;
+    	stCanFrame.data[k++] = 0xff;
+        while (k < 8)
+    	{
+            stCanFrame.data[k++] = 0xff;
+        }
+        stCanFrame.can_dlc = 8;
+        s32Ret = can_WriteFrame(&stCanFrame);
+    	if (SV_SUCCESS != s32Ret)
+    	{
+    		print_level(SV_ERROR, "can_WriteFrame failed.\n");
+    		return SV_FAILURE;
+    	}
+    }
+    
+    if (s_s32SendCnt >= s32DetectCnt)
+    {
+        strncpy(szCmd, "ifconfig | grep can0 -A3 | grep TX | awk -F ':' '{print$2}' | awk -F ' ' '{print$1}'", 128);
+    	s32Ret = can_SafeSystem_Recv(szCmd, szBuf, 64);
+    	if (SV_SUCCESS == s32Ret && 0 != strlen(szBuf))
+    	{
+    	    s32CanTxCount = atoi(szBuf);
+    	}
+        
+        if (s32CanLastTxCount == s32CanTxCount)
+    	{
+    		print_level(SV_WARN, "can device need to restart, last: %d, now: %d\n", s32CanLastTxCount, s32CanTxCount);
+
+            SAFE_SV_System("canconfig can0 stop >> /dev/null && canconfig can0 start >> /dev/null");
+    	}
+        s_s32SendCnt = 0;
+    }
+
+    return SV_SUCCESS;
+}
+
 /******************************************************************************
  * 函数功能: 发送任意CAN数据，目前是只给ADA32使用
  * 输入参数: pszCanData - CAN数据
@@ -4176,7 +4313,7 @@
              SV_FAILURE - 失败
  * 注意    : 无
  *****************************************************************************/
-sint32 CAN_SendUserData(const char *pszCanData, const sint32 s32DataLen, SV_BOOL bSingleFrame)
+sint32 CAN_SendUserDataMsg(const char *pszCanData, const sint32 s32DataLen, SV_BOOL bSingleFrame, uint32 u32msgCanid)
 {
     sint32 s32Ret, i, k;
 	uint32 u32Canid = 0;
@@ -4223,7 +4360,10 @@
 	
 	/* can data init */
     pthread_mutex_lock(&m_stCanInfo.mutexSetConfig);
-	u32Canid = strtol(m_stCanInfo.szPdsCanid, NULL, 16);
+    
+    if (u32msgCanid) u32Canid = u32msgCanid;
+    else u32Canid = strtol(m_stCanInfo.szPdsCanid, NULL, 16);
+
     stCanFrame.can_id = can_GetCanid(u32Canid);
 	pthread_mutex_unlock(&m_stCanInfo.mutexSetConfig);
     stCanFrame.can_dlc = 8;
Index: src/peripheral/mcu/mcu.c
===================================================================
--- src/peripheral/mcu/mcu.c	(revision 4451)
+++ src/peripheral/mcu/mcu.c	(working copy)
@@ -1,4 +1,4 @@
-﻿/******************************************************************************
+/******************************************************************************
 Copyright (C) 2020-2022 广州敏视数码科技有限公司版权所有.
 
 文件名：mcu.c
@@ -268,7 +268,8 @@
 
     MSG_CAN_DATA_S *pstMsgCanData = (MSG_CAN_DATA_S *)pstMsgPkt->pu8Data;
     //print_level(SV_INFO, "get user data len: %d, data: %s\n", pstMsgCanData->s32DataLen, pstMsgCanData->szCanData);
-    sint32 s32Ret = MCU_SendData(pstMsgCanData->szCanData, pstMsgCanData->s32DataLen, pstMsgCanData->bSingleFrame);
+    sint32 s32Ret = MCU_SendDataMsg(pstMsgCanData->szCanData, pstMsgCanData->s32DataLen,\
+                                pstMsgCanData->bSingleFrame, pstMsgCanData->u32CanId);
     if (SV_SUCCESS != s32Ret)
     {
         print_level(SV_ERROR, "MCU_SendData failed!\n");
@@ -1453,6 +1454,39 @@
     return SV_SUCCESS;
 }
 
+
+uint32 mcu_ProcessCan_SingleFrame(CAN_UTILS_DATA_BUF_S * pstMcuCanDataBuf){
+    sint32 s32Ret;
+    int i;
+    MSG_PACKET_S stMsgPkt = {0} ;
+	MSG_CAN_DATA_S stMsgCanData = {0};
+
+    if (NULL == pstMcuCanDataBuf)
+    {
+        print_level(SV_ERROR, "get null pointer.\n");
+        return SV_FAILURE;
+    }
+    print_level(SV_INFO, "MCU recv CAN data:  ");
+    for (i=0; i < CAN_UTILS_START_FRAME_LEN; i++){
+        printf( "%02X ", pstMcuCanDataBuf->u8CanData[i]);
+    }
+    printf("\n ");
+    stMsgCanData.s32DataLen = pstMcuCanDataBuf->u16CanDataLen;
+    memcpy(stMsgCanData.szCanData, pstMcuCanDataBuf->u8CanData, stMsgCanData.s32DataLen);
+    stMsgCanData.u32CanId = pstMcuCanDataBuf->u32CanId;
+    
+	stMsgPkt.pu8Data = (uint8 *)&stMsgCanData;
+    stMsgPkt.u32Size = sizeof(MSG_CAN_DATA_S);
+    s32Ret = Msg_execRequestBlock(EP_MCU, EP_ALG, OP_REQ_CAN_SINGLE, &stMsgPkt, NULL, 0);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "OP_REQ_CAN_SINGLE failed.[%d]\n", s32Ret);
+        return SV_FAILURE;
+    }
+    
+    return SV_SUCCESS;
+}
+
 sint32 mcu_Response_BroadCast()
 {
     sint32 s32Ret;
@@ -2165,6 +2199,7 @@
                 case CAN_UTILS_OPS_COM_REC_GPSDATA:
                     s32Ret = mcu_Response_Get_GpsData(&stCanData);
                     break;
+
                 default:
                     s32Ret = SV_FAILURE;
                     print_level(SV_ERROR, "recv unsupport can opcode %02X.\n", pstMcuCanDataBuf->stCanHeader.u8Opcode);
@@ -2209,15 +2244,17 @@
 
 	u32SetRoiCanid &= ~(0xffffff);
 	u32SetRoiCanid |= 0xFBD1EE;
-
 	memcpy(&stCanInfo, pstMcuSerialPackage->data, sizeof(stCanInfo));
-	print_level(SV_INFO, "recv canid: %#x, u32SetRoiCanid: %#x\n", stCanInfo.u32CanId, u32SetRoiCanid);
-	if ((u32SetRoiCanid != stCanInfo.u32CanId)&&(u32BroadcastCanid != stCanInfo.u32CanId))
-	{
-		print_level(SV_WARN, "recv canid: %#x, not our canid: %#x and broadcast canid :%#x, skip to process!\n", stCanInfo.u32CanId, u32SetRoiCanid,u32BroadcastCanid);
-		return SV_SUCCESS;
-	}
-
+    
+    if (BOARD_IsNotCustomer(BOARD_C_ADA32V2_111119))
+    {
+    	print_level(SV_INFO, "recv canid: %#x, u32SetRoiCanid: %#x\n", stCanInfo.u32CanId, u32SetRoiCanid);
+    	if ((u32SetRoiCanid != stCanInfo.u32CanId)&&(u32BroadcastCanid != stCanInfo.u32CanId))
+    	{
+    		print_level(SV_WARN, "recv canid: %#x, not our canid: %#x and broadcast canid :%#x, skip to process!\n", stCanInfo.u32CanId, u32SetRoiCanid,u32BroadcastCanid);
+    		return SV_SUCCESS;
+    	}
+    }
     u32CanDataLen = pstMcuSerialPackage->stMcuSerialHeader.u16Len - MCU_CAN_BASE_INFO_LEN;
 
     // 续传拼包
@@ -2249,7 +2286,17 @@
             break;
         }
     }
+    
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_111119))
+    {
+        memcpy(&pstMcuCanDataBuf->u8CanData, &pstMcuSerialPackage->data[MCU_CAN_BASE_INFO_LEN+u32BufIndex], CAN_UTILS_START_FRAME_LEN);
+        pstMcuCanDataBuf->u32CanId = stCanInfo.u32CanId;
+        pstMcuCanDataBuf->u16CanDataLen = CAN_UTILS_START_FRAME_LEN;
+        mcu_ProcessCan_SingleFrame(pstMcuCanDataBuf);
 
+        return s32Ret;
+    }
+
     // 第一次接收到该canid的包
     if (i >= CAN_UTILS_DATA_BUF_MAX_NUM || bRemain)
     {
@@ -3986,7 +4033,7 @@
     }
 
 	pthread_mutex_lock(&m_stMcuInfo.mutexSetConfig);
-	u32Canid = strtol(m_stMcuInfo.szPdsCanid, NULL, 16);
+    u32Canid = strtol(m_stMcuInfo.szPdsCanid, NULL, 16);
 	s32Baudrate = m_stMcuInfo.s32Baudrate;
     u8CanFrameFlag = (uint8)m_stMcuInfo.enFrameFormat;
 	pthread_mutex_unlock(&m_stMcuInfo.mutexSetConfig);
@@ -4049,3 +4096,189 @@
 }
 
 
+/******************************************************************************
+ * 函数功能: 封装CAN数据成USB包发给MCU，发送CAN数据
+ * 输入参数: pszMcuCanData - CAN数据
+             s32DataLen - 数据长度
+ * 输出参数: 无
+ * 返回值  : SV_SUCCESS - 成功
+             SV_FAILURE - 失败
+ * 注意    : 无
+ *****************************************************************************/
+sint32 MCU_SendDataMsg(char *pszMcuCanData, sint32 s32DataLen, SV_BOOL bSingleFrame, uint32 u32msgCanid)
+{
+    sint32 s32Ret, i, j;
+	sint32 s32Count = -1;
+    sint32 s32BytesNum = 8;
+	sint32 s32Baudrate, s32CrcLen, s32SendSize;
+    uint32 u32Canid = 0;
+    uint8 u8CanFrameFlag = 0;
+    MCU_SERIAL_PACKET_S stMcuSerialPacket = {0};
+	MCU_CAN_INFO_S stMcuCanPacket = {0};
+
+    if (NULL == pszMcuCanData || 0 == s32DataLen || s32DataLen > MCU_CAN_VALID_DATA_LEN)
+    {
+        print_level(SV_ERROR, "data len is out of range, dataLen: %d, maxLen: %d\n", s32DataLen, MCU_CAN_VALID_DATA_LEN);
+        return SV_FAILURE;
+    }
+
+    if (bSingleFrame && s32DataLen > 8)
+    {
+        print_level(SV_ERROR, "single frame mode, but data len larger than 8, dataLen: %d\n", s32DataLen);
+        return SV_FAILURE;
+    }
+
+	pthread_mutex_lock(&m_stMcuInfo.mutexSetConfig);
+
+    if (u32msgCanid) u32Canid = u32msgCanid;
+    else u32Canid = strtol(m_stMcuInfo.szPdsCanid, NULL, 16);
+    
+	s32Baudrate = m_stMcuInfo.s32Baudrate;
+    u8CanFrameFlag = (uint8)m_stMcuInfo.enFrameFormat;
+	pthread_mutex_unlock(&m_stMcuInfo.mutexSetConfig);
+
+    //can头数据填充
+	stMcuCanPacket.u32CanId = u32Canid;
+	stMcuCanPacket.u16BaudRate = s32Baudrate;
+	stMcuCanPacket.u8CanFrameFlag = u8CanFrameFlag;
+    stMcuCanPacket.u8CanDataLen = bSingleFrame ? s32DataLen : s32DataLen + 16;
+
+	stMcuSerialPacket.stMcuSerialHeader.u16Startcode = 0xAAAA;
+	stMcuSerialPacket.stMcuSerialHeader.u8Opcode = 0x2;  // 0x2表示CAN数据
+	stMcuSerialPacket.stMcuSerialHeader.u32PacketId = 0;
+	stMcuSerialPacket.stMcuSerialHeader.u16Len	= MCU_CAN_INFO_HEADER_SIZE + stMcuCanPacket.u8CanDataLen;  // can头数据 + can数据长度
+
+    if (!bSingleFrame)
+    {
+    	// 头帧
+    	for (i=0; i<8; i++)
+    		stMcuCanPacket.u8CanData[i] = 0xfe;
+        stMcuCanPacket.u8CanData[1] = s32DataLen;
+    	stMcuCanPacket.u8CanData[2] = crc8_check(pszMcuCanData, s32DataLen);
+
+        // 数据帧
+    	memcpy(stMcuCanPacket.u8CanData+8, pszMcuCanData, s32DataLen);
+
+        // 尾帧
+    	for (i=stMcuCanPacket.u8CanDataLen-8; i<stMcuCanPacket.u8CanDataLen; i++)
+    		stMcuCanPacket.u8CanData[i] = 0xff;
+    }
+    else
+    {
+        // 单帧发送只有数据帧
+    	memcpy(stMcuCanPacket.u8CanData, pszMcuCanData, s32DataLen);
+    }
+
+	s32CrcLen = MCU_SERIAL_HEADER_CRC_LEN + stMcuSerialPacket.stMcuSerialHeader.u16Len;	//7表示usb包头数据，从opcode开始算
+	memcpy(stMcuSerialPacket.data, &stMcuCanPacket, stMcuSerialPacket.stMcuSerialHeader.u16Len);
+	stMcuSerialPacket.stMcuSerialHeader.u32Crc = crc32_check((char *)&stMcuSerialPacket.stMcuSerialHeader.u8Opcode, s32CrcLen);
+
+	s32SendSize = MCU_SERIAL_HEADER_SIZE + stMcuSerialPacket.stMcuSerialHeader.u16Len;
+
+#if 0
+	char *tmp = &stMcuSerialPacket;
+	printf("sendSize: %d\n", s32SendSize);
+	for (int k=0; k<s32SendSize; k++)
+		printf("%02X ", *tmp++);
+	printf("\n");
+#endif
+    pthread_mutex_lock(&m_stMcuInfo.mutexSendData);
+    s32Ret = mcu_SendSerialData((char *)&stMcuSerialPacket, s32SendSize);
+	pthread_mutex_unlock(&m_stMcuInfo.mutexSendData);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "mcu_SendSerialData failed!\n");
+        return SV_FAILURE;
+    }
+
+    return SV_SUCCESS;
+}
+
+
+/******************************************************************************
+ * 函数功能: 封装CAN数据成USB包发给MCU，发送三线触发Trigger电平数据(demo用)
+ * 输入参数: pszMsgIO - 三线电平信息
+             s32DataLen - 数据长度
+ * 输出参数: 无
+ * 返回值  : SV_SUCCESS - 成功
+             SV_FAILURE - 失败
+ * 注意    : 无
+ *****************************************************************************/
+sint32 MCU_SendTriggerData(PD_ROI_NUM_S *pszMsgIO, sint32 s32DataLen)
+{
+    sint32 s32Ret, i;
+	sint32 s32Baudrate, s32CrcLen, s32SendSize;
+    sint32 s32DelayTime;
+    uint32 u32Canid = 0;
+    uint8 u8CanFrameFlag = 0;
+    MCU_SERIAL_PACKET_S stMcuSerialPacket = {0};
+	MCU_CAN_INFO_S stMcuCanPacket = {0};
+	PD_ROI_NUM_S stPdRoiNum = {0};
+
+
+    if (NULL == pszMsgIO || 0 == s32DataLen || s32DataLen > MCU_CAN_VALID_DATA_LEN)
+    {
+        print_level(SV_ERROR, "data len is out of range, dataLen: %d, maxLen: %d\n", s32DataLen, MCU_CAN_VALID_DATA_LEN);
+        return SV_FAILURE;
+    }
+
+	u32Canid = strtol(m_stMcuInfo.szPdsCanid, NULL, 16);
+	s32Baudrate = m_stMcuInfo.s32Baudrate;
+    u8CanFrameFlag = (uint8)m_stMcuInfo.enFrameFormat;
+
+    //can头数据填充
+	stMcuCanPacket.u32CanId = u32Canid;
+	stMcuCanPacket.u16BaudRate = s32Baudrate;
+	stMcuCanPacket.u8CanFrameFlag = u8CanFrameFlag;
+    stMcuCanPacket.u8CanDataLen = 24;
+
+	stMcuSerialPacket.stMcuSerialHeader.u16Startcode = 0xAAAA;
+	stMcuSerialPacket.stMcuSerialHeader.u8Opcode = MCU_OPS_ZONE_WARN;   // 0x3表示三路报警数据
+	stMcuSerialPacket.stMcuSerialHeader.u32PacketId = 0;
+	stMcuSerialPacket.stMcuSerialHeader.u16Len	= MCU_CAN_INFO_HEADER_SIZE + stMcuCanPacket.u8CanDataLen;  // can头数据 + can数据长度
+
+    //初始化值
+    for (i=0; i<8; i++)
+       stMcuCanPacket.u8CanData[i] = 0xfe;
+
+    for (i=8; i<16; i++)
+       stMcuCanPacket.u8CanData[i] = 0;
+
+    for (i=16; i<24; i++)
+       stMcuCanPacket.u8CanData[i] = 0xff;
+
+    stMcuCanPacket.u8CanData[1] = 3;
+    stMcuCanPacket.u8CanData[8] = 0x0f;
+    // 传入消息包中的数据
+    stPdRoiNum.s32RedRoiNum = pszMsgIO->s32RedRoiNum;
+    stPdRoiNum.s32YellowRoiNum = pszMsgIO->s32YellowRoiNum;
+    stPdRoiNum.s32GreenRoiNum = pszMsgIO->s32GreenRoiNum;
+
+    stMcuCanPacket.u8CanData[2] = crc8_check((uint8 *)&stPdRoiNum, 3);
+    memcpy(&stMcuCanPacket.u8CanData[9], &stPdRoiNum, 3);
+    s32DelayTime = 100;
+    memcpy(&stMcuCanPacket.u8CanData[12], &s32DelayTime, 4);
+
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "mcu_SendSerialData failed!\n");
+        return SV_FAILURE;
+    }
+
+
+	s32CrcLen = MCU_SERIAL_HEADER_CRC_LEN + stMcuSerialPacket.stMcuSerialHeader.u16Len;	//7表示usb包头数据，从opcode开始算
+	memcpy(stMcuSerialPacket.data, &stMcuCanPacket, stMcuSerialPacket.stMcuSerialHeader.u16Len);
+	stMcuSerialPacket.stMcuSerialHeader.u32Crc = crc32_check((char *)&stMcuSerialPacket.stMcuSerialHeader.u8Opcode, s32CrcLen);
+
+	s32SendSize = MCU_SERIAL_HEADER_SIZE + stMcuSerialPacket.stMcuSerialHeader.u16Len;
+    s32Ret = mcu_SendSerialData((char *)&stMcuSerialPacket, s32SendSize);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "mcu_SendSerialData failed!\n");
+        return SV_FAILURE;
+    }
+
+    return SV_SUCCESS;
+
+}
+
