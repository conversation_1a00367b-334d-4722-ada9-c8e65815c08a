/******************************************************************************
Copyright (C) 2018-2012 广州敏视数码科技有限公司版权所有.

文件名：rtl8821.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2018-05-30

文件功能描述: rtl8821模块功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>
#include "lwip/tcpip.h"
#include "lwip/netif.h"

#include "print.h"
#include "safefunc.h"
#include "rtl8821.h"
#include "wpa_supplicant.h"
#include "hostapd_if.h"
#include "rtw_intf.h"
#include "os_intfs.h"

#if 1

#include <net/ethernet.h>
#include <netpacket/packet.h>
#include <net/if.h>

#include <sys/time.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <netdb.h>
#include <setjmp.h>
#include <sys/select.h>
#endif

/* rtl8821模块控制信息 */
typedef struct tag_Rtl8188ComInfo_S
{
    SV_BOOL bInited;                        /* 模式是否已经初始化 */
    struct netif *pstWifi;                  /* WiFi网络接口指针 */
	SV_BOOL  bWifiStaEnable;				/* WiFi是否使用STA模式 */
    SV_BOOL  bStaConfUpdate;                /* 是否STA模式配置更新 */
    WIFI_CONN_E enConnStatus;               /* STA连接状态 */
    char     szUsedSsid[64];                /* STA正在连接的热点SSID */
    char     szIpaddr[32];                  /* STA获从热点获得的IP地址 */
    SV_BOOL  bCurUse5G;                     /* 当前AP热点使用5G频段 */
    uint32   u32CurChannel;                 /* 当前使用的信道 */
    sint32   s32SocketId;                   /* socket ID */
    uint32   u32MuxId;                      /* 互斥锁 */
    uint32   u32TID;                        /* 线程ID */
    SV_BOOL  bRunning;                      /* 线程是否正在运行 */
    SV_BOOL  bException;                    /* 线程是否出现异常 */
    char     szCountryCode[4];              /* 当前配置的国家码 */
} RTL8821_COM_INFO_S;

RTL8821_COM_INFO_S m_stRtl8821Info;   /* 模块控制信息 */

extern SV_BOOL m_WifiException;


/* 计算校验和的算法 */
static unsigned short cal_chksum(unsigned short *addr,int len)
{
    int sum=0;
    int nleft = len;
    unsigned short *w = addr;
    unsigned short answer = 0;
    /* 把ICMP报头二进制数据以2字节为单位累加起来 */
    while(nleft > 1){
        sum += *w++;
        nleft -= 2;
    }
    /*
     * 若ICMP报头为奇数个字节，会剩下最后一字节。
     * 把最后一个字节视为一个2字节数据的高字节，
     * 这2字节数据的低字节为0，继续累加
     */
    if(nleft == 1){
        *(unsigned char *)(&answer) = *(unsigned char *)w;
        sum += answer;    /* 这里将 answer 转换成 int 整数 */
    }
    sum = (sum >> 16) + (sum & 0xffff);        /* 高位低位相加 */
    sum += (sum >> 16);        /* 上一步溢出时，将溢出位也加到sum中 */
    answer = ~sum;             /* 注意类型转换，现在的校验和为16位 */
    return answer;
}

/******************************************************************************
 * 函数功能: 判断IP主机是否存活
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存活
             SV_FALSE - 不存活
 * 注意    : 无
 *****************************************************************************/
int isIpAlive(uint32 ip) 
{ 
    char    sendpacket[4096];    /* 发送的数据包 */
    char    recvpacket[4096];    /* 接收的数据包 */
    pid_t    pid;
    int    datalen = 56;    /* icmp数据包中数据的长度 */
    int sockfd;
    int size = 50*1024;
    if((sockfd = socket(AF_INET, SOCK_RAW, 1)) < 0) {   //协议1:ICMP协议
        perror("socket error\n");
    }
    setsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &size, sizeof(size) );
    
    struct sockaddr_in source_addr, dest_addr;
	bzero(&source_addr, sizeof(source_addr));
    bzero(&dest_addr, sizeof(dest_addr));
	source_addr.sin_family = AF_INET;
	source_addr.sin_port = htons(8192);
	source_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    dest_addr.sin_family = AF_INET;
    dest_addr.sin_addr.s_addr = ip;
    //send packet;
    int packsize;
    struct icmp *icmp;
    struct timeval *tval;
    icmp = (struct icmp*)sendpacket;
    icmp->icmp_type = ICMP_ECHO;    /* icmp的类型 */
    icmp->icmp_code = 0;            /* icmp的编码 */
    icmp->icmp_cksum = 0;           /* icmp的校验和 */
    icmp->icmp_seq = 1;       /* icmp的顺序号 */
    icmp->icmp_id = pid;            /* icmp的标志符 */
    packsize = 8 + datalen;   /* icmp8字节的头 加上数据的长度(datalen=56), packsize = 64 */
    tval = (struct timeval *)icmp->icmp_data;    /* 获得icmp结构中最后的数据部分的指针 */
    gettimeofday(tval, NULL); /* 将发送的时间填入icmp结构中最后的数据部分 */
    icmp->icmp_cksum = cal_chksum((unsigned short *)icmp, packsize);/*填充发送方的校验和*/
 
 	if(bind(sockfd, (struct sockaddr*)&source_addr, sizeof(source_addr)) < 0){
		perror("bind error\n");
		close(sockfd);
		return SV_FALSE;
	}
 
    if(sendto(sockfd, sendpacket, packsize, 0, (struct sockaddr *)&dest_addr, sizeof(dest_addr)) < 0){
        perror("sendto error\n");
		close(sockfd);
		return SV_FALSE;
    }
    //printf("send %d, send done\n",1 );
    int n;
    struct sockaddr_in from;
    int fromlen = sizeof(from);
    fcntl(sockfd, F_SETFL, O_NONBLOCK);
    struct timeval timeo = {1,0};
    fd_set set;
    FD_ZERO(&set);
    FD_SET(sockfd, &set);
    //read , write;
    int retval = select(sockfd+1, &set, NULL, NULL, &timeo);
    if(retval == -1) {
        perror("select error\n");
		close(sockfd);
		return SV_FALSE;
    }else if(retval == 0 ) {
        perror("timeout error\n");
		close(sockfd);
		return SV_FALSE;
    }else{
        if( FD_ISSET(sockfd, &set) ){
            //printf("host is live\n");
            //return SV_TRUE;
        }
    }
    
    n = recvfrom(sockfd, recvpacket,sizeof(recvpacket), 0, (struct sockaddr *)&from, (socklen_t *)&fromlen);
    if(n<0) {
        perror("recvfrom error");
    }else{
         printf("%d\n",n);
    }
	
	close(sockfd);
    return SV_TRUE;
}


struct arppacket
{
        unsigned char dest_mac[ETH_ALEN];//接收方MAC
        unsigned char src_mac[ETH_ALEN];//发送方MAC
        unsigned short type;         //0x0806是ARP帧的类型值
        unsigned short ar_hrd;//硬件类型 - 以太网类型值0x1
        unsigned short ar_pro;//上层协议类型 - IP协议(0x0800)
        unsigned char  ar_hln;//MAC地址长度
        unsigned char  ar_pln;//IP地址长度
        unsigned short ar_op;//操作码 - 0x1表示ARP请求包,0x2表示应答包
        unsigned char  ar_sha[ETH_ALEN];//发送方mac
        unsigned char ar_sip[4];//发送方ip
        unsigned char ar_tha[ETH_ALEN];//接收方mac
        unsigned char ar_tip[4];//接收方ip
		unsigned char padding[18];//空字节填充
} __attribute__ ((__packed__));

int isArpAlive(uint32 u32SrcIp, uint32 u32DestIp, char *pau8SrcMac, char *pau8DstMac)
{
    int sockfd = 0;
    struct sockaddr_ll sl = {0};
 	struct arppacket arp = {0};

	/*
	* ARP协议帧配置
	*/
	if(pau8SrcMac == NULL)
	{
		print_level(SV_ERROR, "pau8SrcMac == NULL!\n");
		return SV_TRUE;
	}

	memset(arp.dest_mac, 0xff, ETH_ALEN);
	memset(arp.ar_tha, 0x00, ETH_ALEN);
	memcpy(arp.src_mac, pau8SrcMac, ETH_ALEN);
	memcpy(arp.ar_sha, pau8SrcMac, ETH_ALEN);
	arp.type = htons(0x0806);
	arp.ar_hrd = htons(0x01);
	arp.ar_pro = htons(0x0800);
	arp.ar_hln = ETH_ALEN;
	arp.ar_pln = 4;
	arp.ar_op = htons(0x01);
	memcpy(arp.ar_sip, &u32SrcIp, 4);
	memcpy(arp.ar_tip, &u32DestIp, 4);

	
    sockfd = socket(AF_PACKET,SOCK_RAW,htons(ETH_P_ARP));
    if(sockfd < 0)
    {
        printf("socket error\n");
        return -1;
    }

    sl.sll_family = PF_PACKET;
    sl.sll_ifindex = if_nametoindex("wlan1");

	if(sendto(sockfd,&arp, sizeof(arp), 0, (struct sockaddr*)&sl, sizeof(sl)) <= 0)
	{
		print_level(SV_ERROR, "sendto error\n");
		close(sockfd);
		return SV_FALSE;
	}
	
    //printf("send %d, send done\n",1 );
    int n;
	char recvpacket[4096];    /* 接收的数据包 */
    struct sockaddr_in from;
    int fromlen = sizeof(from);
    fcntl(sockfd, F_SETFL, O_NONBLOCK);
    struct timeval timeo = {10,0};
    fd_set set;
    FD_ZERO(&set);
    FD_SET(sockfd, &set);
    //read , write;
    int retval = select(sockfd+1, &set, NULL, NULL, &timeo);
    if(retval == -1) {
        print_level(SV_ERROR, "select error\n");
		close(sockfd);
		return SV_FALSE;
    }else if(retval == 0 ) {
		printf("===========%d============\n", __LINE__);
		perror("timeout error\n");
        printf("-----------%d============\n", __LINE__);
		close(sockfd);
		return SV_FALSE;
    }else{
        if( FD_ISSET(sockfd, &set) ){
            //printf("host is live\n");
            //return SV_TRUE;
        }
    }
    
    n = recvfrom(sockfd, recvpacket,sizeof(recvpacket), 0, (struct sockaddr *)&from, (socklen_t *)&fromlen);
    if(n<0) {
        print_level(SV_ERROR, "recvfrom error");
    }else{
         //printf("%d\n",n);
    }
	
	close(sockfd);
    return SV_TRUE;

}


#if 0
/******************************************************************************
 * 函数功能: 统计当前环境信道信息
 * 输入参数: enWifiFreq --- 使用频段
 * 输出参数: ch --- 获取的最优信道
 * 返回值  : SV_TRUE - 成功
             SV_FALSE - 其他错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_Statistical_Channel(WIFI_FREQ enWifiFreq, long *ch)
{
    FILE * fp = NULL;
    long ch_optimal24G, ch_optimal5G;
    sint32 s32Ret = 0, i, getint, chmin;
    sint32 channel[165+1];//1-13为2.4G,其中1,6,11互不干扰(最好三选一);149,153,157,161,165为5G
    char szCmd[256];
    
    strcpy(szCmd, "ifconfig wlan0 up");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        //return SV_FAILURE;
    }

    strcpy(szCmd, "iwlist wlan0 scan | grep \"Cell **\" -A 1 | grep Channel* | cut -c 29- > /var/Statistical_channel.txt");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    fp = fopen("/var/Statistical_channel.txt", "rt+");
    if (fp == NULL)
    {
        print_level(SV_ERROR, "Open file failed!\n");
        return SV_FAILURE;
    }

    memset(channel,0,sizeof(channel));
    while((fgets(szCmd,5,fp))!=NULL)
    {
        getint = atoi(szCmd);
        if(getint>=1 && getint <= 165)
        {
            channel[getint]++;
        }
    }

    ch_optimal24G = 1;
    chmin = channel[1];
    for(i=1;i<=11;i+=5)
    {
        if(chmin > channel[i])
        {
            ch_optimal24G = i;
            chmin = channel[i];
        }
    }

    ch_optimal5G = 149;
    chmin = channel[149];
    for(i=149;i<=165;i+=4)
    {
        if(chmin > channel[i])
        {
            ch_optimal5G = i;
            chmin = channel[i];
        }
    }
    
    if(enWifiFreq == WIFI_FREQ_2_4G)
    {
        *ch = ch_optimal24G;
    }
    else if (enWifiFreq == WIFI_FREQ_5G)
    {
        *ch = ch_optimal5G;
    }
    else
    {
        print_level(SV_INFO, "channel24G=%d, u32ChnNum24G=%d, channel5G=%d, u32ChnNum5G=%d\n", ch_optimal24G, channel[ch_optimal24G], ch_optimal5G, channel[ch_optimal5G]);
        if (channel[ch_optimal24G] > 2 && channel[ch_optimal24G] > channel[ch_optimal5G])
        {
            *ch = ch_optimal5G;
        }
        else
        {
            *ch = ch_optimal24G;
        }
    }
    
    strcpy(szCmd, "ifconfig wlan0 down");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }
    print_level(SV_INFO, "ch_optimal=%d\n",*ch);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: AP模式获取当前信道
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 信道号
 * 注意    : 无
 *****************************************************************************/
long rtl8821_GetChannel(void)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    uint32 u32ErrCnt = 0;
    char *pcTmp = NULL;
    long lCurchannel = 0;
    char *pszCurchannel = "cur_channel=";
    char szBuf[4096] = {0};
    
    memset(szBuf, 0x0, 4096);
    while(s32Fd <= 0)
    {
        if(++u32ErrCnt >= 2)
            return lCurchannel;

        s32Fd = open("/proc/net/rtl8821cu/wlan0/ap_info", O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_WARN, "acess /proc/net/rtl8821cu/wlan0/ap_info failed. [err=%#x]\n", errno);
            sleep_ms(1000);
        }    
    }
    
    s32Ret = read(s32Fd, szBuf, 4096);
    if(s32Ret <= 0)
    {
        print_level(SV_WARN, "read /proc/net/rtl8821cu/wlan0/ap_info failed. [err=%#x]\n", errno);
    }
    else
    {
        pcTmp = strstr(szBuf, pszCurchannel);
        if (NULL == pcTmp)
        {
            print_level(SV_WARN, "cannot find keyword: %s\n", pszCurchannel);
        }
        else
        {
            pcTmp += strlen(pszCurchannel);
            lCurchannel = atoi(pcTmp);
        }
    }
    
    close(s32Fd);

    return lCurchannel;
}

 /******************************************************************************
 * 函数功能: AP模式设置当前信道
 * 输入参数: enWifiFreq --- WIFI热点频段
             lChannel --- 设置的信道号
 * 输出参数: 无
 * 返回值  : 信道号
 * 注意    : 无
 *****************************************************************************/
long rtl8821_SetChannel(WIFI_FREQ enWifiFreq, long lChannel)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    uint32 u32ErrCnt = 0;
    char szStr[64];
    char szCmd[256];
    char szBuf[4096] = {0};
    char *pcTmp = NULL;
    char *pszSelFreq = NULL;
    long channel24G = 0, channel5G = 0, selChannel = 0;
    char *pszBestChn24G = "best_channel_24G = ";
    char *pszBestChn5G = "best_channel_5G = ";

    strcpy(szCmd, "iwlist wlan0 scan >> /dev/null");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        m_WifiException = SV_TRUE;
    }

    memset(szBuf, 0x0, 4096);
    while(s32Fd <= 0)
    {
        if(++u32ErrCnt >= 3)
            return (enWifiFreq == WIFI_FREQ_2_4G) ? 6 : 44;
        
        s32Fd = open("/proc/net/rtl8821cu/wlan0/best_channel", O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open /proc/net/rtl8821au/wlan0/best_channel failed. [err=%#x]\n", errno);
            sleep_ms(10);
        }
    }

    s32Ret = read(s32Fd, szBuf, 4096);
    if(s32Ret <= 0 )
    {
        print_level(SV_ERROR, "read /proc/net/rtl8821cu/wlan0/best_channel failed. [err=%#x]\n", errno);
    }
    else
    {
        pcTmp = strstr(szBuf, pszBestChn24G);
        if (NULL == pcTmp)
        {
            print_level(SV_ERROR, "cannot find keyword: %s\n", pszBestChn24G);
        }
        else
        {
            pcTmp += strlen(pszBestChn24G);
            channel24G = atoi(pcTmp);
        }

        pcTmp = strstr(szBuf, pszBestChn5G);
        if (NULL == pcTmp)
        {
            print_level(SV_ERROR, "cannot find keyword: %s\n", pszBestChn5G);
        }
        else
        {
            pcTmp += strlen(pszBestChn5G);
            channel5G = atoi(pcTmp);
        }

        if (enWifiFreq == WIFI_FREQ_2_4G)
        {
            selChannel = lChannel? lChannel : channel24G;
            pszSelFreq = "2.4G";
            m_stRtl8821Info.bCurUse5G = SV_FALSE;
        }
        else if (enWifiFreq == WIFI_FREQ_5G)
        {
            selChannel = lChannel? lChannel : channel5G;
            pszSelFreq = "5G";
            m_stRtl8821Info.bCurUse5G = SV_TRUE;
        }
        else
        {
            uint32 u32ChnNum24G = 0, u32ChnNum5G = 0;
            
            sprintf(szStr, "The rx cnt of channel %3d = ", channel24G);
            pcTmp = strstr(szBuf, szStr);
            if (NULL == pcTmp)
            {
                print_level(SV_ERROR, "cannot find keyword: %s\n", szStr);
            }
            else
            {
                pcTmp += strlen(szStr);
                u32ChnNum24G = atoi(pcTmp);
            }

            sprintf(szStr, "The rx cnt of channel %3d = ", channel5G);
            pcTmp = strstr(szBuf, szStr);
            if (NULL == pcTmp)
            {
                print_level(SV_ERROR, "cannot find keyword: %s\n", szStr);
            }
            else
            {
                pcTmp += strlen(szStr);
                u32ChnNum5G = atoi(pcTmp);
            }
            print_level(SV_INFO, "channel24G=%d, u32ChnNum24G=%d, channel5G=%d, u32ChnNum5G=%d\n", channel24G, u32ChnNum24G, channel5G, u32ChnNum5G);
            if (u32ChnNum24G > 2 && u32ChnNum24G > u32ChnNum5G)
            {
                selChannel = channel5G;
                pszSelFreq = "5G";
                m_stRtl8821Info.bCurUse5G = SV_TRUE;
            }
            else
            {
                selChannel = channel24G;
                pszSelFreq = "2.4G";
                m_stRtl8821Info.bCurUse5G = SV_FALSE;
            }
        }
    }
    close(s32Fd);
    
    return selChannel;
}



/******************************************************************************
 * 函数功能: 判断当前STA模式是否连接有热点
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 连接
             SV_FALSE - 未连接
 * 注意    : 无
 *****************************************************************************/
SV_BOOL rtl8821_IsStaConnected()
{
    sint32 s32Ret = 0;
    struct ifreq stIfr;			//ifreq和iwreq是内核的结构体
    struct iwreq stWRQ = {0};
    char szCmd[64];
    char szSSID[64] = {0};

    //if (!BOARD_IsWifiExist())
    //{
    //    return SV_FALSE;
    //}

    strcpy(stIfr.ifr_name, "wlan1");
    s32Ret = ioctl(m_stRtl8821Info.s32SocketId, SIOCGIFFLAGS, &stIfr);		//SIOCGIFFLAGS是获取接口标志
    if (0 != s32Ret || !(stIfr.ifr_flags & IFF_UP))
    {
        print_level(SV_ERROR, "ioctl SIOCGIFFLAGS failed or wlan1 is down. [ret=%d, up=%d]\n", s32Ret, (stIfr.ifr_flags & IFF_UP) ? 1 : 0);
        strcpy(szCmd, "ifconfig wlan1 up");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        }
        sleep_ms(1000);
        
        return SV_FALSE;
    }

    memset(&stWRQ, 0x0, sizeof(stWRQ));
    strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
    stWRQ.u.essid.pointer = szSSID;
    stWRQ.u.essid.length = 64;
    stWRQ.u.essid.flags = 0;
    s32Ret = ioctl(m_stRtl8821Info.s32SocketId, SIOCGIWESSID, &stWRQ);		//获取SSID
    if (s32Ret < 0 || strlen(szSSID) == 0)
    {
        return SV_FALSE;
		
    }

    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: STA模式获取当前所连接热点的SSID、MAC地址和分配到的IP地址
 * 输入参数: 无
 * 输出参数: pau8Mac --- MAC 地址
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_StaGetCurSsidMacIp(char *pszSsid, uint8 *pau8Mac, uint32 *pu32IpAddr)
{
    sint32 s32Ret = 0, i;
    char szSSID[64] = {0};
    struct iwreq stWRQ = {0};
    struct sockaddr_in stAddr;
    struct ifreq stIfr;

    if (NULL == pau8Mac)
    {
        return ERR_NULL_PTR;
    }

    memset(&stWRQ, 0x0, sizeof(stWRQ));
    strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
    stWRQ.u.essid.pointer = szSSID;
    stWRQ.u.essid.length = 64;
    stWRQ.u.essid.flags = 0;
    ioctl(m_stRtl8821Info.s32SocketId, SIOCGIWESSID, &stWRQ);
    szSSID[63] = '\0';
    strcpy(pszSsid, szSSID);

    memset(&stWRQ, 0x0, sizeof(stWRQ));
    strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
    stWRQ.u.ap_addr.sa_family = 1;//ARPHRD_ETHER;
    s32Ret = ioctl(m_stRtl8821Info.s32SocketId, SIOCGIWAP, &stWRQ);		//获取AP的mac地址
    if (s32Ret >= 0)
    {
        for (i = 0; i < 6; i++)
        {
            pau8Mac[i] = stWRQ.u.ap_addr.sa_data[i];
        }
    }
    else
    {
        for (i = 0; i < 6; i++)
        {
            pau8Mac[i] = 0xff;
        }
    }

    strcpy(stIfr.ifr_name, "wlan1");
    s32Ret = ioctl(m_stRtl8821Info.s32SocketId, SIOCGIFADDR, &stIfr);		//获取接口地址
    if (s32Ret >= 0)
    {
        memcpy(&stAddr, &stIfr.ifr_addr, sizeof(stAddr));
        *pu32IpAddr = (uint32)stAddr.sin_addr.s_addr;
    }
    else
    {
        *pu32IpAddr = 0;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: STA模式获取当前所连接热点的信号强度值
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 信息强度值
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_StaGetSignal()
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    sint32 s32Signal = 0;
    char szBuf[256] = {0};
    char *pszTmp = NULL;

    s32Fd = open(PROC_NET_WIRELESS, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "file: %s is unexist.\n", PROC_NET_WIRELESS);
        return 0;
    }

    s32Ret = read(s32Fd, szBuf, 255);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "read file: %s failed. [err=%#x]\n", PROC_NET_WIRELESS, errno);
        close(s32Fd);
        return 0;
    }

    pszTmp = strstr(szBuf, "wlan1:");
    if (NULL == pszTmp)
    {
        close(s32Fd);
        return 0;
    }

    pszTmp += strlen("wlan1:");
    pszTmp = strtok(pszTmp, " ");
    pszTmp = strtok(NULL, " ");		//函数返回值是分隔符后的那个字符，这里其实是拿wlan1:后面的第二个字符串，即为link的强度
    if (NULL != pszTmp)
    {
        sscanf(pszTmp, "%d", &s32Signal);
    }

    close(s32Fd);
    
    return s32Signal;
}

/******************************************************************************
 * 函数功能: STA模式重新连接热点
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_StaReconnectAp()
{
    sint32 s32Ret = 0;
    char szCmd[64];

    strcpy(szCmd, "udhcpc -i wlan1 -x hostname:WiFi-Camera &");
    SAFE_KillallProcess(szCmd);

    strcpy(szCmd, "/root/wpa_supplicant -B -Dwext -iwlan1 -c /var/wpa.conf");
    SAFE_KillallProcess(szCmd);

    strcpy(szCmd, "/root/wpa_supplicant -B -Dwext -iwlan1 -c /var/wpa.conf");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: STA模式断开热点连接
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_StaDisconnectAp()
{
    sint32 s32Ret = 0;
    char szCmd[64];
    char szSSID[] = "invalid_ssid_7777777";
    struct iwreq stWRQ = {0};

    strcpy(szCmd, "udhcpc -i wlan1 -x hostname:WiFi-Camera &");
    SAFE_KillallProcess(szCmd);

    strcpy(szCmd, "/root/wpa_supplicant -B -Dwext -iwlan1 -c /var/wpa.conf");
    SAFE_KillallProcess(szCmd);

    strcpy(szCmd, "ip addr flush dev wlan1 &");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    if (strlen(m_stRtl8821Info.szUsedSsid) != 0)
    {
        memset(&stWRQ, 0x0, sizeof(stWRQ));
        strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
        stWRQ.u.essid.pointer = szSSID;
        stWRQ.u.essid.length = strlen(szSSID);
        stWRQ.u.data.flags = 1;
        s32Ret = ioctl(m_stRtl8821Info.s32SocketId, SIOCSIWESSID, &stWRQ);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ioctl SIOCSIWESSID failed. [err=%#x]\n", errno);
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: STA模式重新从热点分配IP
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_StaReallocIp()
{
    sint32 s32Ret = 0;
    char szCmd[64];

    strcpy(szCmd, "udhcpc -i wlan1 -x hostname:WiFi-Camera &");
    SAFE_KillallProcess(szCmd);

    strcpy(szCmd, "udhcpc -i wlan1 -x hostname:WiFi-Camera &");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

void * rtl8821_sta_body(void *pvArg)
{
    sint32 s32Ret = 0;
    RTL8821_COM_INFO_S *pstComInfo = (RTL8821_COM_INFO_S *)pvArg;
    char szCmd[64];
    char szSsid[64] = {0};
    uint8 au8Mac[6], au8MacOld[6] = {0xff, 0xff, 0xff, 0xaa, 0xbb, 0xcc};
    uint32 u32IpAddr = 0;

    s32Ret = prctl(PR_SET_NAME, "rtl8821_sta_body");			//PR_SET_NAME :把参数arg2作为调用进程的经常名字
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    
    while (pstComInfo->bRunning)
    {
        //print_level(SV_DEBUG, "rtl8188 model runnig... enConnStatus: %d\n", pstComInfo->enConnStatus);
        sleep_ms(1000);
        if (pstComInfo->bStaConfUpdate)
        {
            pstComInfo->enConnStatus = WIFI_CONN_CONNECTING;
            s32Ret = rtl8821_StaReconnectAp();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "rtl8821_StaReconnectAp failed. [err=%#x]\n", s32Ret);
                continue;
            }
                
            pstComInfo->bStaConfUpdate = SV_FALSE;
        }
        else if (!rtl8821_IsStaConnected())
        {
            if (WIFI_CONN_CONNECTING != pstComInfo->enConnStatus)
            {
                pstComInfo->enConnStatus = WIFI_CONN_CONNECTING;

                strcpy(szCmd, "ip addr flush dev wlan1 &");
                s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                    SAFE_System(szCmd, NORMAL_WAIT_TIME);
                }
            }
        }
        else
        {
            s32Ret = rtl8821_StaGetCurSsidMacIp(szSsid, au8Mac, &u32IpAddr);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "rtl8188_GetApSsidMac failed. [err=%#x]\n", s32Ret);
                continue;
            }

            if ((au8MacOld[0] != au8Mac[0] || au8MacOld[1] != au8Mac[1] || au8MacOld[2] != au8Mac[2] || \
                au8MacOld[3] != au8Mac[3] || au8MacOld[4] != au8Mac[4] || au8MacOld[5] != au8Mac[5] || \
                u32IpAddr == 0) && strlen(szSsid) != 0)
            {
                pstComInfo->enConnStatus = WIFI_CONN_CONNECTING;
                print_level(SV_INFO, "Connected to AP: %s [%02X:%02X:%02X:%02X:%02X:%02X]. try to allocate IP.\n", szSsid, \
                                    au8Mac[0], au8Mac[1], au8Mac[2], au8Mac[3], au8Mac[4], au8Mac[5]);
                strcpy(pstComInfo->szUsedSsid, szSsid);
                s32Ret = rtl8821_StaReallocIp();
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "rtl8821_StaReconnectAp failed. [err=%#x]\n", s32Ret);
                    continue;
                }
                
                memcpy(au8MacOld, au8Mac, 6);
                sleep_ms(2000);
            }
            else if (WIFI_CONN_CONNECTED != pstComInfo->enConnStatus)
            {
                char *pcIpaddr = (char *)&u32IpAddr;
                print_level(SV_INFO, "wlan1 allocated ipaddr: %d.%d.%d.%d\n", pcIpaddr[0], pcIpaddr[1], pcIpaddr[2], pcIpaddr[3]);
                pstComInfo->enConnStatus = WIFI_CONN_CONNECTED;
            }

            sleep_ms(3000);
        }
    }

    rtl8821_StaDisconnectAp();
    
    return NULL;
}

/******************************************************************************
 * 函数功能: 启动rtl8821模块STA模式
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
sint32 rtl8821_Sta_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread = 0;

    if (m_stRtl8821Info.bRunning)
    {
        return SV_SUCCESS;
    }
    
    m_stRtl8821Info.bRunning = SV_TRUE;
    m_stRtl8821Info.enConnStatus = WIFI_CONN_DISCONNECTED;
    s32Ret = pthread_create(&thread, NULL, rtl8821_sta_body, &m_stRtl8821Info);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    m_stRtl8821Info.u32TID = (uint32)thread;
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止rtl8821模块STA模式
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
sint32 rtl8821_Sta_Stop()
{
    sint32 s32Ret = 0;
    void *pvRetval = NULL;

    if (!m_stRtl8821Info.bRunning)
    {
        return SV_SUCCESS;
    }

    m_stRtl8821Info.bRunning = SV_FALSE;
    m_stRtl8821Info.enConnStatus = WIFI_CONN_DISCONNECTED;
    s32Ret = pthread_join(m_stRtl8821Info.u32TID, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
    }
    
    return SV_SUCCESS;
}
#endif
/******************************************************************************
 * 函数功能: 获取STA模式路由IP
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_FAILURE
             SV_SUCCESS
 * 注意    : 无
 *****************************************************************************/
SV_BOOL rtl8821_StaGetRouteIp(char *pauRouteIP)
{
    sint32 s32Ret = 0;
    ip_addr_t ipaddr, netmask, gw;
    struct in_addr stIn;
    char *tmp;
    s32Ret = netifapi_netif_get_addr(&m_stRtl8821Info.pstWifi[RTL8821_STA_MODE], &ipaddr, &netmask, &gw);
	if (0 != s32Ret)
	{
		return SV_FAILURE;
	}
    stIn.s_addr = gw.addr;
    tmp = inet_ntoa(stIn);
    strcpy(pauRouteIP, tmp);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 判断当前STA模式是否连接有热点
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 连接
             SV_FALSE - 未连接
 * 注意    : 无
 *****************************************************************************/
SV_BOOL rtl8821_IsStaConnected()
{
    sint32 s32Ret = 0;
    s32Ret = m_stRtl8821Info.enConnStatus==WIFI_CONN_CONNECTED ? SV_TRUE : SV_FALSE;

    return s32Ret;
}

/******************************************************************************
 * 函数功能: STA模式获取当前所连接热点的SSID、MAC地址、分配到的IP地址、网关的IP地址
 * 输入参数: 无
 * 输出参数: pau8Mac --- MAC 地址
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_StaGetCurSsidMacIp(char *pszSsid, char *pau8Mac, uint32 *pu32IpAddr, uint32 *pu32IpGw)
{
    sint32 s32Ret = 0, i;
	int len;
    char szSSID[64] = {0};
	char szMac[ETH_ALEN];
	
	ip_addr_t ipaddr, netmask, gw;
	char *ifname = "wlan1";

	s32Ret = rtw_get_essid(ifname, szSSID, &len);
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "rtw_get_essid failed.\n");
		return SV_FAILURE;
	}
	szSSID[len] = '\0';
	

	s32Ret = rtw_get_mac(ifname, szMac);
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "rtw_get_essid failed.\n");
		return SV_FAILURE;
	}

	//print_level(SV_ERROR, "mac:%x:%x:%x:%x:%x:%x\n", szMac[0], szMac[1], 
    //                                szMac[2], szMac[3],
    //                                szMac[4], szMac[5]);
    

	s32Ret = netifapi_netif_get_addr(&m_stRtl8821Info.pstWifi[RTL8821_STA_MODE], &ipaddr, &netmask, &gw);
	if (0 != s32Ret)
	{
		*pu32IpAddr = 0;
		return SV_FAILURE;
	}

	if(NULL != pszSsid)
		strcpy(pszSsid, szSSID);
	
	if(NULL != pau8Mac)
		memcpy(pau8Mac, szMac, ETH_ALEN);

	if(NULL != pu32IpAddr)
		*pu32IpAddr = ipaddr.addr;

	if(NULL != pu32IpGw)
		*pu32IpGw = gw.addr;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 判断当前STA模式是否正常
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 正常
             SV_FALSE - 异常
 * 注意    : 无
 *****************************************************************************/
SV_BOOL rtl8821_IsStaAlive()
{
    sint32 s32Ret = SV_TRUE;
    if (!rtl8821_IsStaConnected())
    {
        return SV_TRUE;
    }

	char puMac[ETH_ALEN];
	uint32 u32IpAddr, u32IpGw;
	s32Ret = rtl8821_StaGetCurSsidMacIp(NULL, puMac, &u32IpAddr, &u32IpGw);
    if (0 != s32Ret)
    {
        return SV_TRUE;
    }
    if(0 == u32IpGw)
    {
        return SV_TRUE;
    }

    //s32Ret = isIpAlive(u32IpGw);
	s32Ret = isArpAlive(u32IpAddr, u32IpGw, puMac, NULL);
    if (s32Ret != SV_TRUE)
    {
		print_level(SV_ERROR, "Exception Occurs\n");
		return SV_FALSE;
    }

	
    return SV_TRUE;
}


/******************************************************************************
 * 函数功能: STA模式获取当前所连接热点的信号强度值
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 信息强度值
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_StaGetSignal()
{
    sint32 s32Ret = 0;
    sint32 s32Signal = 0;
	char *ifname = "wlan1";

	s32Signal = rtw_get_rxsignal(ifname);
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "rtl8821_StaGetSignal failed.\n");
		return SV_FAILURE;
	}
    
    return s32Signal;
}

/******************************************************************************
 * 函数功能: 获取当前模块发射功率
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 发射功率值(dBm)
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_GetTxPower(void)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    uint32 u32ErrCnt = 0;
    char szBuf[512] = {0};
    char *pcTmp = NULL;
    char *pszTxPower = "TxPower: ";
    char *pszFile = "/jffs/lib/firmware/rtl8821cu/TXPWR_LMT.txt";
    static sint32 s32TxPower = 0;

    if(s32TxPower > 0)
        return s32TxPower;
    
    memset(szBuf, 0x0, 512);
    while(s32Fd <= 0)
    {
        if(++u32ErrCnt >= 3)
            return s32TxPower;
        
        s32Fd = open(pszFile, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_WARN, "open %s failed. [err=%#x]\n", pszFile, errno);
            sleep_ms(10);
        }
        
    }
    
    s32Ret = read(s32Fd, szBuf, 512);
    if(s32Ret <= 0 )
    {
        print_level(SV_WARN, "read %s failed. [err=%#x]\n", pszFile, errno);
    }
    else
    {
        pcTmp = strstr(szBuf, pszTxPower);
        if (NULL == pcTmp)
        {
            print_level(SV_WARN, "cannot find keyword: %s\n", pszTxPower);
        }
        else
        {
            pcTmp += strlen(pszTxPower);            
            s32TxPower = atoi(pcTmp);
        }
    }
    close(s32Fd);

    return s32TxPower;
}

/******************************************************************************
 * 函数功能: rtl8821 STA模式连接状态回调函数
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void rtl8821_STA_event_cb(enum wpa_event event)
{
	 switch(event) {
		 case WPA_EVT_SCAN_RESULTS:
			printf("WiFi: Scan results available\n");
            m_stRtl8821Info.enConnStatus = WIFI_CONN_CONNECTING;
			break;
		 case WPA_EVT_CONNECTED:
			printf("WiFi: Connected\n");
            m_stRtl8821Info.enConnStatus = WIFI_CONN_CONNECTED;
			netifapi_dhcp_start(&m_stRtl8821Info.pstWifi[RTL8821_STA_MODE]);
			break;
		 case WPA_EVT_DISCONNECTED:
			printf("WiFi: Disconnect\n");
            m_stRtl8821Info.enConnStatus = WIFI_CONN_DISCONNECTED;
			netifapi_dhcp_stop(&m_stRtl8821Info.pstWifi[RTL8821_STA_MODE]);
			break;
		 default:
			break;
	 }
}
/******************************************************************************
* 函数功能: STA维护线程
* 输入参数: 无
* 输出参数: 无
* 返回值:无
*******************************************************************************/
void * rtl8821_sta_body(void *pvArg)
{
	//extern SV_BOOL m_WifiException;
	sint32 s32Ret;
	WIFI_CONF_S *pstWifiConf = (WIFI_CONF_S*)pvArg;
	while(m_stRtl8821Info.bWifiStaEnable)
	{
		if(
		 (SV_TRUE == m_stRtl8821Info.bWifiStaEnable) &&
		 (SV_TRUE == rtl8821_IsStaConnected()) &&
		 (SV_FALSE == m_WifiException)
		 )
		{
			if(!rtl8821_IsStaAlive())
            {
                print_level(SV_ERROR, "reset STA\n");
				s32Ret = rtl8821_STA_Config(pstWifiConf->bWifiStaEnable,pstWifiConf->szWifiStaSsid, pstWifiConf->szWifiStaPwd);
				if (SV_SUCCESS != s32Ret)
			    {
			        print_level(SV_ERROR, "rtl8821_STA_Config failed. [err=%#x]\n", s32Ret);
			        //return;
			    }
            }
	 	}
		sleep(60);
	}
}




/******************************************************************************
 * 函数功能: 配置rtl8821模块STA模式工作参数
 * 输入参数: bEnable --- 是否使能STA模式
             pszSsid --- 访问热点的SSID
             pszPwd  --- 访问热点的密码
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
sint32 rtl8821_STA_Config(SV_BOOL bEnable, char *pszSsid, char *pszPwd)
{
    sint32 s32Ret = 0;
    char szCmd[64];
    struct wpa_assoc_request stWpaConf = {0};

    if(NULL == pszSsid || NULL == pszPwd)
        return ERR_NULL_PTR;

	#if 1
	//if (m_stRtl8821Info.bWifiStaEnable && !bEnable)
	if (SV_FALSE == bEnable)
	{
		print_level(SV_INFO, "====STA enable has changed...\n");
		if(0 == m_stRtl8821Info.bInited)
		{
			print_level(SV_WARN, "rtl8821_STA_Config no inited...\n");
            return SV_SUCCESS;
		}
		//hostapd_stop();
        //wpa_supplicant_stop();
        //netifapi_dhcp_stop(&m_stRtl8821Info.pstWifi[RTL8821_STA_MODE]);
        return SV_SUCCESS;
	}
	#endif

	m_stRtl8821Info.bWifiStaEnable = bEnable;
	
    s32Ret = wpa_supplicant_start("wlan1", "realtek", NULL);
    if(0 != s32Ret)
	{
		if (1 != s32Ret)
		{
        	print_level(SV_ERROR, "wpa_supplicant_start failed.\n");
			return SV_FAILURE;
		}
		else
		{
			print_level(SV_ERROR, "wpa_supplicant has started.\n");
			//return SV_SUCCESS;
		}
    }

    s32Ret = wpa_register_event_cb(rtl8821_STA_event_cb);
    if(0 != s32Ret)
	{
        print_level(SV_ERROR, "wpa_register_event_cb failed.\n");
        wpa_supplicant_stop();
        return SV_FAILURE;
    }

    strcpy(stWpaConf.ssid, pszSsid);
    strcpy(stWpaConf.key, pszPwd);
    stWpaConf.auth = strcmp(pszPwd, "") == 0 ? WPA_SECURITY_OPEN : WPA_SECURITY_WPA2PSK;
    s32Ret = wpa_cli_connect(&stWpaConf);
    if(0 != s32Ret){
        print_level(SV_ERROR, "wpa_Cli_connect failed.\n");
        wpa_supplicant_stop();
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 配置rtl8821 AP 热点参数
 * 输入参数: enWifiAuth --- 热点认证模式
             pszSsid --- AP热点SSID
             pszPassword --- AP登陆密码
             enWifiFreq --- AP使用频段
             lSetChannel --- 选择的信道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8821_AP_Config(WIFI_AUTH enWifiAuth, const char *pszSsid, const char *pszApIpAddr, const char *pszPassword, WIFI_FREQ enWifiFreq, long lSetChannel)
{
    sint32 s32Ret = 0;
    struct hostapd_conf stHapdConf = {0};
    ip_addr_t ipaddr, netmask, gw;
    char *pszSelFreq = NULL;

    if (NULL == pszSsid || NULL == pszApIpAddr || NULL == pszPassword)
    {
        print_level(SV_ERROR, "input pointer is null.\n");
        return ERR_NULL_PTR;
    }

    if (enWifiAuth >= WIFI_AUTH_BUTT || enWifiFreq >= WIFI_FREQ_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pszSelFreq = (enWifiFreq == WIFI_FREQ_2_4G) ? "2.4G" : "5G";
    print_level(SV_INFO, "Set AP: ipaddr:%s, frequency=%s, channel=%d\n", pszApIpAddr, pszSelFreq, lSetChannel);
#if 0
    hostapd_stop();
#endif
    memset(&stHapdConf, 0, sizeof(struct hostapd_conf));
    strcpy(stHapdConf.driver, "realtek");
    strcpy(stHapdConf.ssid, pszSsid);
    stHapdConf.channel_num = lSetChannel ? lSetChannel : (enWifiFreq == WIFI_FREQ_2_4G ? 4 : 44);
    stHapdConf.authmode = enWifiAuth == WIFI_AUTH_NONE ? HOSTAPD_SECURITY_OPEN : HOSTAPD_SECURITY_WPA2PSK;
    strcpy(stHapdConf.key, enWifiAuth == WIFI_AUTH_NONE ? "" : pszPassword);
    s32Ret = hostapd_start("rtl8821cu", &stHapdConf);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "hostapd_start failed.\n");
        return ERR_SYS_NOTREADY;
    }

    sint32 a,b,c,d;
	s32Ret = sscanf(pszApIpAddr, "%d.%d.%d.%d", &a, &b, &c, &d);
	if (s32Ret == 4 && a >=0 && a <=255 && b >=0 && b <=255 && c >=0 && c <=255 && d >=0 && d <=255)
	{
		IP4_ADDR(&gw, a, b, c, d);
        IP4_ADDR(&ipaddr, a, b, c, d);
	}
	else
	{
        IP4_ADDR(&gw, 192, 168, 60, 1);
        IP4_ADDR(&ipaddr, 192, 168, 60, 1);
    }
    IP4_ADDR(&netmask, 255, 255, 255, 0);
    sleep_ms(200);
	print_level(SV_INFO, "set server ipaddr to %"U16_F".%"U16_F".%"U16_F".%"U16_F"\n", 
				ip4_addr1_16(&ipaddr), ip4_addr2_16(&ipaddr), ip4_addr3_16(&ipaddr), ip4_addr4_16(&ipaddr));
    netifapi_netif_set_addr(&m_stRtl8821Info.pstWifi[RTL8821_AP_MODE], &ipaddr, &netmask, &gw);

    netifapi_dhcps_stop(&m_stRtl8821Info.pstWifi[RTL8821_AP_MODE]);
    netifapi_dhcps_start(&m_stRtl8821Info.pstWifi[RTL8821_AP_MODE], NULL, 0);
    m_stRtl8821Info.u32CurChannel = stHapdConf.channel_num;
    
    return SV_SUCCESS;
}

sint32 RTL8821_Init(WIFI_CONF_S *pstWifiParam)
{
    sint32 s32Ret = 0;
    uint32   u32MuxId;
    long lSetChannel = 0;
    char szCmd[64];

    if (NULL == pstWifiParam)
    {
        return ERR_NULL_PTR;
    }

    if (0 != access("/jffs/lib/firmware/rtl8821cu/TXPWR_LMT.txt", F_OK))
    {
        SAFE_System("fs_write_enable 1", NORMAL_WAIT_TIME);
        SAFE_CP("/jffs/lib/firmware/rtl8821cu/TXPWR_LMT_CN.txt", "/jffs/lib/firmware/rtl8821cu/TXPWR_LMT.txt");
        SAFE_System("fs_write_enable 0", NORMAL_WAIT_TIME);
    }
    
    memset(&m_stRtl8821Info, 0x0, sizeof(RTL8821_COM_INFO_S));
    if (0 != rtw_wifi_open(&m_stRtl8821Info.pstWifi))
    {
        print_level(SV_ERROR, "mt_wifi_start failed.\n");
        m_WifiException = SV_TRUE;
        return ERR_SYS_NOTREADY;
    }
    
    m_stRtl8821Info.s32SocketId = socket(AF_INET, SOCK_DGRAM, 0);
    if (m_stRtl8821Info.s32SocketId < 0)
    {
        print_level(SV_ERROR, "socket init failed.\n");
        return ERR_SYS_NOTREADY;
    }

    strncpy(m_stRtl8821Info.szCountryCode, pstWifiParam->szCountryCode, 4);
    m_stRtl8821Info.szCountryCode[3] = '\0';
    s32Ret = RTL8821_SetCountryCode();
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RTL8821_SetCountryCode failed.\n");
    }

    lSetChannel = pstWifiParam->enWifiFreq == WIFI_FREQ_2_4G ? pstWifiParam->lSet2GChannel : pstWifiParam->lSet5GChannel;
    s32Ret = rtl8821_AP_Config(pstWifiParam->enWifiAuth, pstWifiParam->szWifiApSsid, pstWifiParam->szWifiApIpAddr, pstWifiParam->szWifiApPwd, pstWifiParam->enWifiFreq, lSetChannel);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "rtl8821_AP_Config failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#if 1
	m_stRtl8821Info.bWifiStaEnable = pstWifiParam->bWifiStaEnable;
	print_level(SV_INFO, "STA enable: %d\n", pstWifiParam->bWifiStaEnable);
    s32Ret = rtl8821_STA_Config(pstWifiParam->bWifiStaEnable, pstWifiParam->szWifiStaSsid, pstWifiParam->szWifiStaPwd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "rtl8821_STA_Config failed. [err=%#x]\n", s32Ret);
        //return s32Ret;
    }

	if(0 == m_stRtl8821Info.u32TID)
	{
		uint32 uwTskHiID = 0;
		TSK_INIT_PARAM_S stInitParam;
		LOS_TaskLock();
		stInitParam.pfnTaskEntry = (TSK_ENTRY_FUNC)rtl8821_sta_body;
		stInitParam.usTaskPrio = 5;
		stInitParam.pcName = "rtl8821_sta_body";
		stInitParam.uwStackSize = 64*1024;
		stInitParam.auwArgs[0] = pstWifiParam;
		stInitParam.uwResved   = LOS_TASK_STATUS_DETACHED;
		LOS_TaskCreate(&uwTskHiID, &stInitParam);
		LOS_TaskUnlock();
		m_stRtl8821Info.u32TID = uwTskHiID;
	}
#endif
    s32Ret = LOS_MuxCreate(&u32MuxId);
    if (LOS_OK != s32Ret)
    {
        print_level(SV_ERROR, "LOS_MuxCreate failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    
    m_stRtl8821Info.bInited = SV_TRUE;
    m_stRtl8821Info.u32MuxId = u32MuxId;

    return SV_SUCCESS;
}

sint32 RTL8821_Fini(SV_BOOL bRmmod)
{
#if 0
    sint32 s32Ret = 0;

    rtl8821_Sta_Stop();
    if (m_stRtl8821Info.s32SocketId >= 0)
    {
        close(m_stRtl8821Info.s32SocketId);
    }
#endif
    hostapd_stop();
    netifapi_dhcps_stop(&m_stRtl8821Info.pstWifi[RTL8821_AP_MODE]);
#if 1
    wpa_supplicant_stop();
    netifapi_dhcps_stop(&m_stRtl8821Info.pstWifi[RTL8821_STA_MODE]);
#endif
    m_stRtl8821Info.bInited = SV_FALSE;
    LOS_MuxPend(m_stRtl8821Info.u32MuxId, LOS_WAIT_FOREVER);
    rtw_wifi_close();
    LOS_MuxPost(m_stRtl8821Info.u32MuxId);
    LOS_MuxDelete(m_stRtl8821Info.u32MuxId);
	LOS_TaskDelete(m_stRtl8821Info.u32TID);
    memset(&m_stRtl8821Info, 0x0, sizeof(RTL8821_COM_INFO_S));
    
    return SV_SUCCESS;
}

sint32 RTL8821_Query_Status(WIFI_STAT_S *pstStatus)
{
    if (NULL == pstStatus)
    {
        return COM_ERR_NULL_PTR;
    }

    pstStatus->enWifiId = WIFI_ID_RTL8821;
    pstStatus->enConnStat = m_stRtl8821Info.enConnStatus;
    pstStatus->u32Signal = (WIFI_CONN_CONNECTED == m_stRtl8821Info.enConnStatus) ? rtl8821_StaGetSignal() : 0;
    pstStatus->u32TxPower = rtl8821_GetTxPower();
    pstStatus->lCurChannel = m_stRtl8821Info.u32CurChannel;
    if(m_stRtl8821Info.szCountryCode[0] != '\0')
        memcpy(pstStatus->szCountryCode, m_stRtl8821Info.szCountryCode, sizeof(m_stRtl8821Info.szCountryCode));

    return SV_SUCCESS;
}

uint32 RTL8821_GetSignalLevel(uint32 u32SteamKbps)
{
    uint32 u32Signal = 0;

    if (!m_stRtl8821Info.bInited)
    {
        print_level(SV_WARN, "WiFi uninited.\n");
        return 100;
    }

    LOS_MuxPend(m_stRtl8821Info.u32MuxId, LOS_WAIT_FOREVER);
    u32Signal = (uint32)rtw_get_rxsignal("wlan0");
    LOS_MuxPost(m_stRtl8821Info.u32MuxId);
    //print_level(SV_DEBUG, "[1]u32Signal:%d\n", u32Signal);
    if (1 >= u32Signal)
    {
        return 100;     // 当没有手机连入的时候使码流按最高值进行编码
    }
    
    if (m_stRtl8821Info.bCurUse5G)
    {
        if (u32Signal < 30)
        {
            u32Signal = 10;
        }
        else
        {
            u32Signal = 10 + (uint32)((float)(u32Signal - 30) * 9 / 7);
        }
    }
    else
    {
        if (u32Signal < 60)
        {
            u32Signal = 10;
        }
        else
        {
            u32Signal = 10 + (uint32)((float)(u32Signal - 60) * 9 / 4);
        }
    }

    //print_level(SV_DEBUG, "[2]u32Signal:%d\n", u32Signal);
    
    return u32Signal;
}

uint32 RTL8821_GetBandwidthLevel()
{
    uint u32Bandwidth = 50;
    
    return u32Bandwidth;
}

sint32 RTL8821_SetCountryCode(void)
{
    dprintf("CountryCode: %s", rtw_get_country_code());
    rtw_set_country_code(m_stRtl8821Info.szCountryCode);
    return SV_SUCCESS;
}

sint32 RTL8821_GetAvailableChannel(const sint32 **s32Channel, sint32 *s32Number)
{
    static sint32 AvailableChannel[42] = {0};

    rtw_get_chan_plan(AvailableChannel, s32Number);
    *s32Channel = AvailableChannel;
    *s32Number = 26;

    return SV_SUCCESS;
}

sint32 RTL8821_Set_TxPower(SV_BOOL bSet, uint32 u32Db)
{
    return rtw_set_txpower(bSet, u32Db);
}

sint32 RTL8821_Normal_Run()
{
	return rtw_wifi_normal_run();
}

