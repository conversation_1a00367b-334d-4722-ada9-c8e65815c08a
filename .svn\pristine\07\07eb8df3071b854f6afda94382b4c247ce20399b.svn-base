#ifndef _SV_NETWORK_DVRINFO_H_
#define _SV_NETWORK_DVRINFO_H_

#include <string>

#include "cmsServer.h"
#include "cms_common.h"
#include "sensoropcode.h"

using namespace std;

typedef enum
{

	NETCARD_LAN,
	NETCARD_WIFI,
	NETCARD_CELL

}NETCARD_TYPE_E;

class SV_NETWORK_DVRINFO:public SV_COMMON_JOIN_THREAD
{

public:
	static SV_COMMON_Mutex stateMutex;
	static SV_NETWORK_DVRINFO *getInstance();  
	static SV_NETWORK_DVRINFO *pInstance;

	virtual ~SV_NETWORK_DVRINFO(){};

	void run();

	const string getNetCardName();
	const UPLOAD_FILE_OPTS_E getUploadFileType();
	const string getIpAddr();
	const uint32 getIpPort();
	const uint32 getNetType();
	const uint32 getLogUploadLevel();
	const uint64 getDeviceId();
	const uint64 getUniqueId();
	const uint8 getRegisterMode();
	const string getPathNo();
	const string getPlateNumber();
	const string getVersion();
	const string getCurTime();    
    const string getTimeString();
	const string getLongitude();
	const string getLatitude();
	const string getElv();
	const string getGSensorX();
	const string getGSensorY();
	const string getGSensorZ();
	const uint32 getCarSpeed();
	const sint16 getCourse();
	const uint16 getExDevSate();
	const uint16 getCarTemperature();
	const SV_BOOL getDiskState();
    const SV_BOOL getStorageStatus(char *pszStorageDir);
	sint32 getGPSInfo();

private:
	SV_NETWORK_DVRINFO();
	SV_NETWORK_DVRINFO(const SV_NETWORK_DVRINFO &);  
	SV_NETWORK_DVRINFO& operator = (const SV_NETWORK_DVRINFO &); 

	char szValidNetCard[12];
	uint8 u8NetType;
	CFG_SER_PARAM stSerParam = {0};
	CFG_DEV_INFO stDevInfo;
	CFG_SYS_PARAM stSysParam = {0};
    gpsData_t stGpsData;
};





















#endif

