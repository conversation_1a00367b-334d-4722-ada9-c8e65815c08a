/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file:       r_emmc.h
author:     lyn
version:    1.0.0
date:       2023-12-11
function:   recorder emmc header file
notice:     none
*******************************************************************************/
#ifndef _R_EMMC_H_
#define _R_EMMC_H_

#include "common.h"
#include "r_pos.h"
namespace recorder{

class REmmc : public RPos
{
private:
public:
    virtual sint32 GetRemainSize();        /* 获取剩余容量 */
    REmmc(REC_POS_E pos, sint32 idx);
    ~REmmc();
};

}

#endif /* _R_EMMC_H_ */
