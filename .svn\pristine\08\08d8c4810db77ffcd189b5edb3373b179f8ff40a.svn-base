#include <stdio.h>
#include <string.h>
#include <sys/time.h>
#include <string.h>
#include <stdarg.h>
#include <sys/mman.h>
#include "sv_bb_common.h"
#include "sv_subiao_data_def.h"
#include "iconv.h"

BB_ID_INDEX_S g_sBbIdIdex[] =
{
	{0, 		BB_ID_TE_RSP},						/* 终端通用应答 */
	{1,			BB_ID_BS_RSP},						/* 平台通用应答 */
	{2,			BB_ID_TE_HEART},					/* 终端心跳 */
	{3,			BB_ID_BS_PKT_REQ},					/* 补传分包请求 */
	{4,			BB_ID_TE_REGISTER},					/* 终端注册 */
	{5,			BB_ID_BS_REGISTER},					/* 终端注册应答 */
	{6,			BB_ID_TE_UNREGISTER},				/* 终端注销 */
	{7,			BB_ID_TE_AUTH},						/* 终端鉴权 */
	{8,			BB_ID_BS_SET_PARAM},				/* 设置终端参数 */
	{9,			BB_ID_BS_GET_PARAM},				/* 获取终端参数 */
	
	{10,		BB_ID_BS_GET_PART_PARAM},			/* 查询指定终端参数 */
	{11,		BB_ID_TE_GET_PARAM},				/* 终端参数应答 */
	{12,		BB_ID_TE_CTRL},						/* 终端控制 */
	{13,		BB_ID_BS_GET_TE_ATTR},				/* 查询终端属性 */
	{14,		BB_ID_TE_GET_BS_ATTR},				/* 查询终端属性应答 */
	{15,		BB_ID_BS_SET_UPGRADE_PKT},			/* 下发终端升级包 */
	{16,		BB_ID_TE_UPGRADE_RST},				/* 终端升级结果通知		 */
	{17,		BB_ID_TE_POS},						/* 位置信息上报 */
	{18,		BB_ID_BS_GET_POS},					/* 位置信息查询 */
	{19,		BB_ID_TE_GET_POS},					/* 位置信息查询应答		*/
	{20,		BB_ID_BS_TEMP_TRACK_CTRL},			/* 临时位置跟踪控制 */
	
	{21,		BB_ID_BS_CONFIRM_ALRM},				/* 报警确认	 */
	{22,		BB_ID_BS_TXT_INFO},					/* 文本信息下发 */
	{23,		BB_ID_BS_SET_EVENT},				/* 事件设置 */
	{24,		BB_ID_TE_EVENT_RPT},				/* 事件报告 */
	{25,		BB_ID_BS_SET_QUSTION},				/* 提问下发 */
	{26,		BB_ID_TE_QUSTION_RPT},				/* 提问应答 */
	{27,		BB_ID_BS_INFO_ON_DEMOND},			/* 信息点播菜单设置 */
	{28,		BB_ID_TE_ON_CANCEL_INFO},			/* 信息点播/取消*/
	{29,		BB_ID_BS_INFO_SERVICE},				/* 信息服务 */
	{30,		BB_ID_BS_DIAL_CALLBACK},			/* 电话回拨 */
	
	{31,		BB_ID_BS_SET_DIAL_BOOK},			/* 设置电话本 */
	{32,		BB_ID_BS_VEHICLE_CTRL},				/* 车辆控制 */
	{33,		BB_ID_TE_VEHICLE_CTRL},				/* 车辆控制应答 */
	{34,		BB_ID_BS_SET_AREA_ROUND},			/* 设置圆形区域 */
	{35,		BB_ID_BS_DEl_AREA_ROUND},			/* 删除圆形区域 */
	{36,		BB_ID_BS_SET_AREA_RECT},			/* 设置矩形区域 */
	{37,		BB_ID_BS_DEL_AREA_RECT},			/* 删除矩形区域 */
	{38,		BB_ID_BS_SET_AREA_POLY},			/* 设置多边形区域 */
	{39,		BB_ID_BS_DEL_AREA_POLY},			/* 删除多边形区域 */
	{40,		BB_ID_BS_SET_AREA_LINE},			/* 设置线路区域 */

	{41,		BB_ID_BS_DEL_AREA_LINE},			/* 删除线路区域 */
	{42,		BB_ID_BS_GET_DRV_DATA},				/* 行驶记录数据采集 */
	{43,		BB_ID_TE_DRV_DATA_UPLOAD},			/* 行驶记录数据上传         */
	{44,		BB_ID_BS_SET_DRV_PARAM},			/* 行驶记录参数下传 */
	{45,		BB_ID_TE_SET_DRV_PARAM_RSP},		/* 电子运单上报 */
	{46,		BB_ID_BS_DRVER_IDENTITY},			/* 上报驾驶员身份 */
	{47,		BB_ID_TE_DRVER_IDENTITY_RSP},		/* 驾驶员身份信息采集上报 */
	{48,		BB_ID_TE_BLIND_POSDATA_RSP},		/* 批量定位数据上传 */
	{49,		BB_ID_TE_CAN_DATA_RSP},				/* CAN总线数据上传 */
	{50,		BB_ID_TE_MEDIA_EVENT},				/* 多媒体事件信息上传 */

	{51,		BB_ID_TE_MEDIA_DATA},				/* 多媒体数据上传 */
	{52,		BB_ID_BS_MEDIA_DATA_RES},			/* 多媒体数据上传应答 */
	{53,		BB_ID_BS_SNAP},						/* 摄像头立即抓拍 */
	{54,		BB_ID_TE_SNAP_RES},					/* 摄像头立即抓拍命令应答 */
	{55,		BB_ID_BS_MEDIA_DATA_SEARCH},		/* 多媒体数据检索 */
	{56,		BB_ID_TE_MEDIA_DATA_SEARCH_RES},	/* 多媒体数据检索应答 */
	{57,		BB_ID_BS_MEDIA_DATA_UPLOAD},		/* 多媒体数据上传 */
	{58,		BB_ID_BS_START_AUDIO},				/* 录音开始命令 */
	{59,		BB_ID_BS_SINGAL_MEDIADATA_UPLOAD},	/* 单条存储多媒体数据检索上传 */
	{60,		BB_ID_BS_TRANSMIT_DOWN},			/* 数据下行透传 */

	{61,		BB_ID_TE_TRANSMIT_UP},				/* 数据上行透传 */
	{62,		BB_ID_TE_DATA_COMPRESS_UP},			/* 数据压缩上报 */
	{63,		BB_ID_BS_RSA},						/* 平台RSA公钥 */
	{64,		BB_ID_TE_RSA_RSP},					/* 终端RSA公钥 */
	{65,		BB_ID_BS_QUERY_STREAM_ATTR},		/* 查询音视频参数 */
	{66,		BB_ID_TE_QUERY_STREAM_ATTR_RES},	/* 查询音视频参数应答 */
	{67,		BB_ID_TE_PEOPLE_FLOW},				/* 终端上传人流量 */
	{68,		BB_ID_BS_REQUEST_REAL_STREAM},		/* 实时音视频传输请求 */
	{69,		BB_ID_BS_REAL_STREAM_CTRL},			/* 音视频传输实时控制 */
	{70,		BB_ID_BS_QUERY_STREAM_LIST},		/* 查询资源列表 */

	{71,		BB_ID_TE_QUERY_STREAM_LIST_RES},	/* 查询资源列表应答*/
	{72,		BB_ID_BS_REQUEST_PLAYBACK},			/* 下发远程回放录像请求 */
	{73,		BB_ID_BS_PLAYBACK_CTRL},			/* 远程录像回放控制 */
	{74,		BB_ID_BS_REQUEST_FILE_UPLOAD},		/* 文件上传请求 */
	{75,		BB_ID_BS_FILE_UPLOAD_FIN},			/* 文件上传完成通知 */
	{76,		BB_ID_BS_FILE_UPLOAD_CTRL},			/* 文件上传控制 */
};


BB_PARAM_ID_INDEX_S g_sBbParamIdIndex[] =
{
	{BB_PARAM_HEART	},					/* 终端心跳发送间隔，单位为秒（s） */
	{BB_PARAM_TCP_TIMEOUT},				/* TCP 消息应答超时时间，单位为秒（s）*/
	{BB_PARAM_TCP_RETRANSMISSION},		/* TCP 消息重传次数 */
	{BB_PARAM_UDP_TIMEOUT},				/* UDP 消息应答超时时间，单位为秒（s） */
	{BB_PARAM_UDP_RETRANSMISSION},		/* UDP 消息重传次数 */
	{BB_PARAM_SMS_TIMEOUT},				/* SMS 消息应答超时时间，单位为秒（s） */
	{BB_PARAM_SMS_RETRANSMISSION},		/* SMS 消息重传次数 */

	{BB_PARAM_MAIN_APN},					/* 主服务器APN，无线通信拨号访问点。若网络制式为CDMA，则该处为PPP 拨号号码 */	
	{BB_PARAM_MAIN_USER},				/* 主服务器无线通信拨号用户名 */
	{BB_PARAM_MAIN_PASSWORD},			/* 主服务器无线通信拨号密码 */
	{BB_PARAM_MAIN_IP},					/* 主服务器地址,IP 或域名*/
	{BB_PARAM_BACKUP_APN},				/* 备份服务器APN，无线通信拨号访问点 */
	{BB_PARAM_BACKUP_USER},				/* 备份服务器无线通信拨号用户名 */
	{BB_PARAM_BACKUP_PASSWORD},			/* 备份服务器无线通信拨号密码 */
	{BB_PARAM_BACKUP_IP},				/* 备份服务器地址,IP 或域名 */
	{BB_PARAM_TCP_PORT},					/* 服务器TCP 端口 */
	{BB_PARAM_UDP_PORT},					/* 服务器UDP 端口 */
	{BB_PARAM_ICCARD_MAIN_IP},			/* 道路运输证IC 卡认证主服务器IP 地址或域名 */
	{BB_PARAM_ICCARD_MAIN_TCP_PORT},		/* 道路运输证IC 卡认证主服务器TCP 端口 */
	{BB_PARAM_ICCARD_MAIN_UDP_PORT},		/* 道路运输证IC 卡认证主服务器UDP 端口 */
	{BB_PARAM_ICCARD_BACKUP_IP},			/* 道路运输证IC 卡认证备份服务器IP 地址或域名，端口同主服务器 */

	{BB_PARAM_MAIN_APN},				/* 主服务器APN，无线通信拨号访问点。若网络制式为CDMA，则该处为PPP 拨号号码 */
	{BB_PARAM_MAIN_USER},				/* 主服务器无线通信拨号用户名 */
	{BB_PARAM_MAIN_PASSWORD},			/* 主服务器无线通信拨号密码 */
	{BB_PARAM_MAIN_IP},					/* 主服务器地址,IP 或域名*/
	{BB_PARAM_BACKUP_APN},				/* 备份服务器APN，无线通信拨号访问点 */
	{BB_PARAM_BACKUP_USER},				/* 备份服务器无线通信拨号用户名 */
	{BB_PARAM_BACKUP_PASSWORD},			/* 备份服务器无线通信拨号密码 */
	{BB_PARAM_BACKUP_IP},				/* 备份服务器地址,IP 或域名 */
	{BB_PARAM_TCP_PORT},				/* 服务器TCP 端口 */
	{BB_PARAM_UDP_PORT},				/* 服务器UDP 端口 */
	{BB_PARAM_ICCARD_MAIN_IP},			/* 道路运输证IC 卡认证主服务器IP 地址或域名 */
	{BB_PARAM_ICCARD_MAIN_TCP_PORT},	/* 道路运输证IC 卡认证主服务器TCP 端口 */
	{BB_PARAM_ICCARD_MAIN_UDP_PORT},	/* 道路运输证IC 卡认证主服务器UDP 端口 */
	{BB_PARAM_ICCARD_BACKUP_IP},		/* 道路运输证IC 卡认证备份服务器IP 地址或域名，端口同主服务器 */

	/* 0x001E - 0x001F保留 */
	{BB_PARAM_POS_STRATEGY},			/* 位置汇报策略，0：定时汇报；1：定距汇报；2：定时和定距汇报*/
	{BB_PARAM_POS_SCHEME},				/* 位置汇报方案，0：根据ACC 状态； 1：根据登录状态和ACC 状态，先判断登录状态，若登录再根据ACC 状态*/
	{BB_PARAM_NO_LOGIN_TIME_INTERVAL},	/* 驾驶员未登录汇报时间间隔，单位为秒（s），>0 */


	/* 0x0023- 0x0026保留 */
	{BB_PARAM_SLEEP_INTERVAL},			/* 休眠时汇报时间间隔，单位为秒（s），>0 */
	{BB_PARAM_EMERGECY_INTERVAL},		/* 紧急报警时汇报时间间隔，单位为秒（s），>0 */
	{BB_PARAM_DEFAULT_INTERVAL},		/* 缺省时间汇报间隔，单位为秒（s），>0 */

	/* 0x002A - 0x002B保留 */
	{BB_PARAM_DISTANCE_INTERVAL},		/* 缺省距离汇报间隔，单位为米（m），>0 */
	{BB_PARAM_NO_LOGIN_DISTANCE_INTERVAL}, /* 驾驶员未登录汇报距离间隔，单位为米（m），>0 */
	{BB_PARAM_SLEEP_DISTANCE_INTERVAL},	/* 休眠时汇报距离间隔，单位为米（m），>0 */
	{BB_PARAM_EMERGENCY_DISTANCE_INTERVAL},/* 紧急报警时汇报距离间隔，单位为米（m），>0 */
	{BB_PARAM_INFLECT_ANGEL_POINT},		/* 拐点补传角度，<180 */
	{BB_PARAM_INFLECT_FENCE_RADIUS},		/* 电子围栏半径（非法位移阈值），单位为米 */

	/* 0x0032- 0x003F 保留 */
	{BB_PARAM_PLATFORM_NUMBER},			/* 监控平台电话号码 */
	{BB_PARAM_REBOOT_NUMBER},			/* 复位电话号码，可采用此电话号码拨打终端电话让终端复位 */
	{BB_PARAM_RESET},					/* 恢复出厂设置电话号码，可采用此电话号码拨打终端电话让终端恢复出厂设置 */
	{BB_PARAM_SMS_PHONENUMBER},			/* 监控平台SMS 电话号码 */
	{BB_PARAM_SMS_ALARM_PHONENUMBER},	/* 接收终端SMS 文本报警号码 */
	{BB_PARAM_SMS_ANSWER_PHONE_STRATEGY},	/* 终端电话接听策略，0：自动接听；1：ACC ON 时自动接听，OFF 时手动接听 */
	{BB_PARAM_ONCE_CALL_TIME},			/* 每次最长通话时间，单位为秒（s），0 为不允许通话，0xFFFFFFFF 为不限制 */
	{BB_PARAM_MONTHLY_CALL_TIME},		/* 当月最长通话时间，单位为秒（s），0 为不允许通话，0xFFFFFFFF 为不限制 */
	{BB_PARAM_MONITOR_PHONENUMBER},		/* 监听电话号码 */
	{BB_PARAM_PRIVILEGE_PHONENUMER},	/* 监管平台特权短信号码 */

	/* 0x004A- 0x004F 保留 */
	{BB_PARAM_SHIELD},					/* 报警屏蔽字，与位置信息汇报消息中的报警标志相对应，相应位为1则相应报警被屏蔽*/
	{BB_PARAM_ALARM_SMS_SWITCH},		/* 报警发送文本SMS 开关，与位置信息汇报消息中的报警标志相对应，相应位为1 则相应报警时发送文本SMS*/
	{BB_PARAM_ALARM_SNAP_SWITCH},		/* 报警拍摄开关，与位置信息汇报消息中的报警标志相对应，相应位为 1 则相应报警时摄像头拍摄*/
	{BB_PARAM_ALARM_STORE_FLAG},		/* 报警拍摄存储标志，与位置信息汇报消息中的报警标志相对应，相应位为1 则对相应报警时拍的照片进行存储，否则实时上传 */
	{BB_PARAM_ALARM_KEY_FLAG},			/* 关键标志，与位置信息汇报消息中的报警标志相对应，相应位为1 则对相应报警为关键报警*/
	{BB_PARAM_MAX_SPEED},				/* 最高速度，单位为公里每小时（km/h）*/
	{BB_PARAM_SPEEDING_DURATION},		/* 超速持续时间，单位为秒（s） */
	{BB_PARAM_CONTINUE_DRIVE_TIME_LIMIT},	/* 连续驾驶时间门限，单位为秒（s） */
	{BB_PARAM_DAY_DRIVE_TIME_LIMIT},	/* 当天累计驾驶时间门限，单位为秒（s） */
	{BB_PARAM_MIN_REST_TIME},			/* 最小休息时间，单位为秒（s） */
	{BB_PARAM_MAX_STOP_TIME},			/* 最长停车时间，单位为秒（s） */
	{BB_PARAM_OVER_SPEED_DIFF},			/* 超速报警预警差值，单位为1/10Km/h */
	{BB_PARAM_TIRED_DRIVE_DIFF},		/* 疲劳驾驶预警差值，单位为秒（s），>0*/
	{BB_PARAM_COLLSION_ALARM_PARAM},	/*碰撞报警参数设置：
											b7-b0： 碰撞时间，单位4ms；
											b15-b8：碰撞加速度，单位0.1g，设置范围在：0-79 之间，默认为10。*/
	{BB_PARAM_ROLLOVER_ALARM_PARAM},	/* 侧翻报警参数设置：侧翻角度，单位1 度，默认为30 度。*/

	/* 0x005F- 0x0063 保留 */
	{BB_PARAM_TIMING_SNAP_CTRL},		/* 定时拍照控制，见表13 */
	{BB_PARAM_DISTANCE_SNAP_CTRL},		/* 定距拍照控制，见表14 */

	/* 0x0066- 0x006F 保留*/
	{BB_PARAM_IMAGE_QUALITY},			/* 图像/视频质量，1-10，1 最好 */
	{BB_PARAM_BRIGHTNESS},				/* 亮度，0-255 */
	{BB_PARAM_CONTRAST}, 				/* 对比度，0-127 */
	{BB_PARAM_SATURATION},				/* 饱和度，0-127 */
	{BB_PARAM_CHROMA},					/* 色度，0-255 */

	/* 0x0075- 0x007F 保留*/
	{BB_PARAM_MILEAGE},					/* 车辆里程表读数，1/10km */
	{BB_PARAM_PROVINCE_ID},				/* 车辆所在的省域ID */
	{BB_PARAM_CITY_ID},					/* 车辆所在的市域ID */
	{BB_PAPAM_PLATE_NUMBER},			/* 公安交通管理部门颁发的机动车号牌 */
	{BB_PAPAM_PLATE_COLOR},				/* 车牌颜色，按照JT/T415-2006 的5.4.12 */

	/* 0x0085- 0x8F 保留 */
	{BB_PARAM_GNSS_MODE},				/*GNSS 定位模式，定义如下：
										bit0，0：禁用GPS 定位， 1：启用GPS 定位；
										bit1，0：禁用北斗定位， 1：启用北斗定位；
										bit2，0：禁用GLONASS 定位， 1：启用GLONASS 定位；
										bit3，0：禁用Galileo 定位， 1：启用Galileo 定位。*/
	{BB_PARAM_GNSS_BAUDRATE},			/*GNSS 波特率，定义如下：
										0x00：4800；0x01：9600；
										0x02：19200；0x03：38400；
										0x04：57600；0x05：115200。*/
	{BB_PARAM_GNSS_OUTPUT_FREQUENCY},	/*GNSS 模块详细定位数据输出频率，定义如下：
										0x00：500ms；0x01：1000ms（默认值）；
										0x02：2000ms；0x03：3000ms；
										0x04：4000ms。*/
	{BB_PARAM_GNSS_SAMPLE_FREQUENCY},	/* GNSS 模块详细定位数据采集频率，单位为秒，默认为1。 */
	{BB_PARAM_GNSS_DATA_UPLOAD_MODE},	/* GNSS 模块详细定位数据上传方式：
										0x00，本地存储，不上传（默认值）；
										0x01，按时间间隔上传；
										0x02，按距离间隔上传；
										0x0B，按累计时间上传，达到传输时间后自动停止上传；
										0x0C，按累计距离上传，达到距离后自动停止上传；
										0x0D，按累计条数上传，达到上传条数后自动停止上传。*/
	{BB_PARAM_GNSS_DATA_SETTING},		/*GNSS 模块详细定位数据上传设置：
										上传方式为0x01 时，单位为秒；
										上传方式为0x02 时，单位为米；
										上传方式为0x0B 时，单位为秒；
										上传方式为0x0C 时，单位为米；
										上传方式为0x0D 时，单位为条。*/
	
	{BB_PARAM_CAN1_COLLECT_MS},			/* CAN 总线通道1 采集时间间隔(ms)，0 表示不采集 */
	{BB_PARAM_CAN1_COLLECT_S},			/*CAN 总线通道1 上传时间间隔(s)，0 表示不上传 */
	{BB_PARAM_CAN2_COLLECT_MS},			/* CAN 总线通道1 上传时间间隔(s)，0 表示不上传*/
	{BB_PARAM_CAN2_COLLECT_S},			/*CAN 总线通道2 上传时间间隔(s)，0 表示不上传*/
	{BB_PARAM_CAN_SETTINGS},			/*CAN 总线ID 单独采集设置：
										bit63-bit32 表示此ID 采集时间间隔(ms)，0 表示不采集；
										bit31 表示CAN 通道号，0：CAN1，1：CAN2；
										bit30 表示帧类型，0：标准帧，1：扩展帧；
										bit29 表示数据采集方式，0：原始数据，1：采集区间的计算值；
										bit28-bit0 表示CAN 总线ID。*/
	{BB_PARAM_ACC_DELAY_TIMES},			/* 0x002A 设置ACC熄火延时时间 一立定制*/

	/* 0xF000- 0xFFFF 用户自定义 */
};

extern int g_ParamIdSize = ARR_SZIE(g_sBbParamIdIndex);


int get_bb_id_index(BB_ID_E bb_id)
{
	int i = 0;
	for(i = 0; i < sizeof(g_sBbIdIdex)/sizeof(BB_ID_INDEX_S); i++)
	{
		if(g_sBbIdIdex[i].id == bb_id)
		{
			break;
		}
	}

	return i;
}

u8 bcd2hex(u8 bcd )
{
	return ((bcd>>4)*10 + bcd%(0x1<<4));
}

u8 hex2bcd(u8 hex)
{
	return ((hex%10) + ((hex/10)*(0x1<<4)));
}

time_t bcd_2_time_t(u8 bcd[6])
{
	struct tm stm = { 0 };
	time_t retTime;
	stm.tm_isdst = - 1;
	stm.tm_year = bcd2hex(bcd[0]) + 2000 -1900;
	stm.tm_mon =  bcd2hex(bcd[1]) -1;
	stm.tm_mday = bcd2hex(bcd[2]);
	stm.tm_hour = bcd2hex(bcd[3]);
	stm.tm_min = bcd2hex(bcd[4]);
	stm.tm_sec = bcd2hex(bcd[5]);
	retTime = mktime(&stm);
	//print_level(SV_DEBUG,"-------------------------------------------%x %x %x %x %x %x %d \n",stm.tm_year,stm.tm_mon,stm.tm_mday,stm.tm_hour,stm.tm_min,stm.tm_sec,retTime);
	return retTime;
}

int time_t_2_bcd(time_t tim, u8 bcd[6])
{
	if(0 == tim)
	{
		tim = time(NULL);
	}

	struct tm tmpTim = { 0 };
	struct tm *ptm = localtime_r(&tim, &tmpTim);
	bcd[0] = hex2bcd((ptm->tm_year +1900 -2000));
	bcd[1] = hex2bcd((ptm->tm_mon + 1));
	bcd[2] = hex2bcd(ptm->tm_mday);
	bcd[3] = hex2bcd(ptm->tm_hour);
	bcd[4] = hex2bcd(ptm->tm_min);
	bcd[5] = hex2bcd(ptm->tm_sec);

	return 0;
}

u64 msec_count(u64 msec)
{
	struct timeval tv;
	u64 diff;

	gettimeofday(&tv, NULL);
	diff = (u64)tv.tv_sec * 1000 + tv.tv_usec / 1000;

	return diff - msec;
}


void SET8(u8 val[],u8 x)
{
	val[0] = x;
}

void SETBE16(u8 val[],u16 x)
{
	val[0] = (x >> 8) & 0xff;
	val[1] = x & 0xff;
}

void SETBE32(u8 val[], u32 x)
{
	val[0] = (x >> 24) & 0xff;
	val[1] = (x >> 16) & 0xff;
	val[2] = (x >> 8) & 0xff;
	val[3] = x & 0xff;
}
void SETBE64(u8 val[], u64 x)
{
	val[0] = (x >> 56) & 0xff;
	val[1] = (x >> 48) & 0xff;
	val[2] = (x >> 40) & 0xff;
	val[3] = (x >> 32) & 0xff;
	val[4] = (x >> 24) & 0xff;
	val[5] = (x >> 16) & 0xff;
	val[6] = (x >> 8) & 0xff;
	val[7] = x & 0xff;
}

u32 GET8(u8 val[])
{
	return (u32)val[0];
}

u32 GETBE16(u8 val[])
{
	return ((u32)(val[0] << 8) | (u32)val[1]);
}

u32 GETBE32(u8 val[])
{
	return (((u32)val[0] << 24) | ((u32)val[1] << 16) | ((u32)val[2] <<8) | (u32)val[3]);
}

u64 GETBE64(u8 val[])
{
	return (((u64)val[0] << 56) | 
			((u64)val[1] << 48) |
			((u64)val[2] << 40) |
			((u64)val[3] << 32) |
			((u64)val[4] << 24) |
			((u64)val[5] << 16) |
			((u64)val[6] << 8 ) | 
			(u64)val[7]);
}

void SETLE16(u8 val[], u16 x)
{
	val[0] = (x << 8) & 0xff;
	val[1] = x & 0xff;
}

void SETLE32(u8 val[], u32 x)
{
	val[0] = (x << 24) & 0xff;
	val[1] = (x << 16) & 0xff;
	val[2] = (x << 8) & 0xff;
	val[3] = x & 0xff;
}

void SETLE64(u8 val[], u64 x)
{
	val[0] = (x << 56) & 0xff;
	val[1] = (x << 48)& 0xff; 
	val[2] = (x << 40) & 0xff; 
	val[3] = (x << 32) & 0xff;
	val[4] = (x << 24) & 0xff;
	val[5] = (x << 16) & 0xff;
	val[6] = (x << 8) & 0xff;
	val[7] = 0xff;
}

u32 GETLE16(u8 val[])
{
	return (((u32)val[0] >> 8) | (u32)val[0]);
}

u32 GETLE32(u8 val[])
{
	return (((u32)val[0] >> 24) | ((u32)val[1] >> 16) | ((u32)val[2] >> 8) | (u32)val[3]);
}

u64 GETLE64(u8 val[])
{
	return (((u64)val[0] >> 56) |
			((u64)val[1] >> 48) |
			((u64)val[2] >> 40) |
			((u64)val[3] >> 32) |
			((u64)val[4] >> 24) |
			((u64)val[5] >> 16) |
			((u64)val[6] >> 8) |
			(u64)val[7] );
}

int bcd2str( u8 *bcd, int bcdlength, char *str)
{
	if(NULL == bcd || 0 == bcdlength)
	{
		return -1;
	}

	int i,j;
	for(i =0, j =0; i < bcdlength; i++, j+=2)
	{
		str[j] = (bcd[i]>>4) > 9? (bcd[i]>>4)-10+'A' : (bcd[i]>>4)+'0';
		str[j+1] = (bcd[i] & 0x0F) > 9? (bcd[i] & 0x0F)-10+'A' : (bcd[i] & 0x0F)+'0'; 
	}

	return 0;
}

int str2bcd(char *str, OUT u8 *bcd, int bcdlength)
{
	if(NULL == str || NULL == bcd)
	{
		return -1;
	}
	char Tmp[64] = { 0 };

	int strlength = strlen(str);

	memset(Tmp, '0', sizeof(Tmp));
	if(strlength > 2*bcdlength)
	{
		memcpy(Tmp, str, 2*bcdlength);
	}
	else
	{
		memcpy(Tmp + (2*bcdlength - strlength) , str, strlength);
	}
	Tmp[2*bcdlength] = '\0';

	for(int i = 0; i < bcdlength; i++)
	{
		bcd[i] = (Tmp[2*i] << 4) | (Tmp[2*i +1] & 0x0f);
	}

	return 0;
}

// 时间的转换
int time_to_bcd(struct tm * time, OUT u8 bcd[6])
{
	bcd[0] = hex2bcd(time->tm_year - 2000);
	bcd[1] = hex2bcd(time->tm_mon);
	bcd[2] = hex2bcd(time->tm_mday);
	bcd[3] = hex2bcd(time->tm_hour);
	bcd[4] = hex2bcd(time->tm_min);
	bcd[5] = hex2bcd(time->tm_sec);

	return 0;
}

u64 gettime_us()
{	
	struct timeval Cur_tv = { 0 };
	gettimeofday(&Cur_tv, 0);

	return (Cur_tv.tv_sec * 1000000ULL + Cur_tv.tv_usec);
}

int mkdirs(char *muldir)
{
	if(NULL == muldir)
	{
		return SV_FAILURE;
	}
	
	int Ret = SV_FAILURE;
    int i,len;
    char str[512] = { 0 };
    strncpy(str, muldir, 512);
    len = strlen(str);
    for(i = 0; i < len; i++ )  
    {
        if( str[i]=='/' )
        {            
            str[i] = '\0';
			if( access(str,0)!=0 )
			{
                Ret = mkdir( str, 0777 );
			}
		    str[i]='/';
		}
	}    

	if( len>0 && access(str,0) != 0 )
	{
        Ret = mkdir( str, 0777 );
	}
	
	return Ret;
}
//代码转换:从一种编码转为另一种编码
int code_convert(char *from_charset,char *to_charset,char *inbuf,int inlen,char *outbuf,int outlen)
{
	iconv_t cd;
	int rc = -1;
	char **pin = &inbuf;
	char **pout = &outbuf;

	//def _BB_FUNC_
	cd = iconv_open(to_charset,from_charset);
	if (cd == 0) 
	{
		printf("iconv_open failed! \n");
		return -1;
	}
	memset(outbuf,0,outlen);
	if ((rc = iconv(cd,pin,(size_t *)&inlen,pout,(size_t *)&outlen)) == -1) 
	{
		printf("iconv convert failed! :%s\n", strerror(errno));
		return -1;
	}
	iconv_close(cd);

	return rc;
}
//UNICODE码转为GB2312码
int u2g(char *inbuf,int inlen,char *outbuf,int outlen)
{
return code_convert("utf-8","gbk",inbuf,inlen,outbuf,outlen);
}
//GB2312码转为UNICODE码
int g2u(char *inbuf,size_t inlen,char *outbuf,size_t outlen)
{
	return code_convert("gbk","utf-8",inbuf,inlen,outbuf,outlen);
}


void WriteHexLog(char *pstTips, u8 *pstData,int nDatalen, int nlevel)
{
	if(NULL == pstData || nDatalen <= 0)
	{
		return;
	}

	#define HEX_BUF_SIZE 3072

	char cBuf[3072] = { 0 };
	int nCount = 0;
	if(nDatalen >= 1024)
	{
		nDatalen = 1024;
	}

	if(NULL != pstTips)
	{
		nCount += sprintf(cBuf, "%s_%d:", pstTips, nDatalen);
	}

	for(int i = 0; i < nDatalen; i++)
	{
		if(nCount < HEX_BUF_SIZE)
		{
			nCount += sprintf(cBuf + nCount, "%02x", pstData[i]);
		}
	}

	if(nCount < HEX_BUF_SIZE)
	{
		nCount += sprintf(cBuf + nCount, "\n");
	}

	if(SV_DEBUG == nlevel)
	{
		print_level(SV_DEBUG,"%s",cBuf);
	}
	else if(SV_ERROR == nlevel)
	{
		print_level(SV_ERROR,"%s",cBuf);
	}
	else if(SV_WARN == nlevel)
	{
		print_level(SV_WARN,"%s",cBuf);
	}
	else
	{
		print_level(SV_INFO,"%s",cBuf);		
	}
}

u8 BBCalCrc(u8 *bufptr, int buflen)
{
	u8 u8Crc = bufptr[0];
	for(int i = 1; i < buflen; i++)
	{
		u8Crc ^= bufptr[i];
	}

	return u8Crc;
}

u8 getSubiaoAlarmLevel(u16 u16Speed)
{
	if(u16Speed <= 30)
	{
		return SUBIAO_ALARM_LEVEL_1;
	}
	else
	{
		return SUBIAO_ALARM_LEVEL_2;
	}
}

u8 getSubiaoAlarmModuleType(sint32 s32PriAlarmType)
{
	u8 u8SbAlarmType = 0xffff;
	
	switch(s32PriAlarmType)
	{
		case RECORD_TYPE_BSD_PASSERBY_1:
		case RECORD_TYPE_BSD_PASSERBY_2:
		case RECORD_TYPE_BSD_PASSERBY_3:
		case RECORD_TYPE_BSD_PASSERBY_4:
		{
			u8SbAlarmType = SUBIAO_ALARM_BSD;
		}
		break;
		case RECORD_TYPE_FATIGUE:
		case RECORD_TYPE_DISTRACTION:
		case RECORD_TYPE_NO_DRIVER:
		case RECORD_TYPE_SMOKING:
		case RECORD_TYPE_CALLING:
		case RECORD_TYPE_NO_SEAT_BELT:
		case RECORD_TYPE_SUNGLASSES:
		{
			u8SbAlarmType = SUBIAO_ALARM_DMS;
		}
		break;
		case RECORD_TYPE_PASSERBY:
		case RECORD_TYPE_CRASH:
		case RECORD_TYPE_SKEWING:
		{
			u8SbAlarmType = SUBIAO_ALARM_ADAS;
		}
		break;
		default:
		break;		
	}

	return u8SbAlarmType;
}

u8 getSubiaoSubAlarmType(sint32 s32PriAlarmType)
{
	u8 u8SbSubAlarmType = 0xffff;
	
	switch(s32PriAlarmType)
	{
		/* bsd start */
		case RECORD_TYPE_BSD_PASSERBY_1: u8SbSubAlarmType = E_BSD_CLOSE_RIGHT_REAR; break;
		case RECORD_TYPE_BSD_PASSERBY_2: u8SbSubAlarmType = E_BSD_CLOSE_REAR; break;
		case RECORD_TYPE_BSD_PASSERBY_3: u8SbSubAlarmType = E_BSD_CLOSE_LEFT_REAR; break;
		case RECORD_TYPE_BSD_PASSERBY_4: u8SbSubAlarmType = E_BSD_CLOSE_REAR; break;
		/* bsd end */
			
		/* dms start */
		case RECORD_TYPE_FATIGUE:  		u8SbSubAlarmType = E_DMS_FATIGUE; 			break;
		case RECORD_TYPE_DISTRACTION: 	u8SbSubAlarmType = E_DMS_DISTRACTION;		break;
		case RECORD_TYPE_NO_DRIVER:		u8SbSubAlarmType = E_DMS_DRIVER_ABORMAL;	break;
		case RECORD_TYPE_SMOKING:		u8SbSubAlarmType = E_DMS_SMOKING;			break;
		case RECORD_TYPE_CALLING:		u8SbSubAlarmType = E_DMS_CALLING;			break;
		case RECORD_TYPE_NO_SEAT_BELT:	break; 
		case RECORD_TYPE_SUNGLASSES:	break;
		/* dms end */

		/* adas start */
		case RECORD_TYPE_PASSERBY:		u8SbSubAlarmType = E_ADAS_PASSERBY;			break;
		case RECORD_TYPE_CRASH:			u8SbSubAlarmType = E_ADAS_CRASH;			break;
		case RECORD_TYPE_SKEWING:		u8SbSubAlarmType = E_ADAS_SKEWING;			break;
		/* adas end */
		default: break;
	}

	return u8SbSubAlarmType;
}


