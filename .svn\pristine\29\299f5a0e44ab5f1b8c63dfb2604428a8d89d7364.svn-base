/usr/lib/libVSC.so
/usr/lib/libneuralnetworks.so
/usr/lib/libOvx12VXCBinary.so
/usr/lib/libexiv2.so.26.0.0
/usr/lib/libCLC.so
/usr/lib/libavfilter.so
/usr/lib/libavfilter.so.7
/usr/lib/libavfilter.so.7.40.101
/usr/lib/libavdevice.so
/usr/lib/libavdevice.so.58
/usr/lib/libavdevice.so.58.5.100
/root/RGB_MANHOLE.rknn
/root/RGB_NIR_PC.rknn
/root/RGB_P.rknn
/root/RGB_P_OW.rknn
/root/RGB_PC.rknn
/root/RGB_PC_90.rknn
/root/RGB_PC_OW.rknn
/root/RGB_SH.rknn
/root/RGB_CQQ_SIGN.rknn
/root/RGB_PF.rknn
/root/RGB_SSR.rknn
/root/TRAFFIC.rknn
/root/model/RGB_PC_specific_version.rknn
/root/model/TRAFFIC.rknn
/root/model/RGB_YOLOv5n_PLATE__DET_fb68d20e.rknn
/root/model/RGB_CQQ_SIGN.rknn
/root/model/RGB_SSR.rknn
/root/model/BGR_PLATE_REC_e1fb6090.rknn
/root/model/RGB_PC_111171.rknn
/root/model/RGB_PC_200001.rknn
/root/model/RGB_PC_201306.rknn
/root/model/RGB_P_FTC.rknn


