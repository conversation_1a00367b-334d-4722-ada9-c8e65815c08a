cmake_minimum_required(VERSION 3.16)
# project(IMS_linux)
project(IMS_Win)
set(NAME_BIN app_ims)

# set(CMAKE_BUILD_TYPE release)
# add_definitions(-std=c++11)

# SET(PLATFORM_TYPE sigmastar)
SET(PLATFORM_TYPE rv1126)
# SET(PLATFORM_TYPE linux_trt)
#SET(PLATFORM_TYPE win_trt)

if (PLATFORM_TYPE MATCHES "win_trt")
    # set(CMAKE_BUILD_TYPE Debug)
    set(CMAKE_BUILD_TYPE Release)
    # 打印编译类型
    message("CMAKE_BUILD_TYPE: " ${CMAKE_BUILD_TYPE})
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/builds")
    set(CMAKE_PDB_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/builds")
    set(<PERSON><PERSON><PERSON>_ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/builds")
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/builds")
endif()


#############编译工具链#################
if (PLATFORM_TYPE MATCHES "rv1126")
    SET(TOOLCHAIN_DIR /home/<USER>/TOOLCHAIN_for_All_Platform/1-RV1126/gcc-arm-8.3-2019.03-x86_64-arm-linux-gnueabihf/)
    SET(CMAKE_C_COMPILER ${TOOLCHAIN_DIR}/bin/arm-linux-gnueabihf-gcc)
    SET(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/bin/arm-linux-gnueabihf-g++)
    if (CMAKE_C_COMPILER MATCHES "aarch64")
        SET(LIB_ARCH lib64)
    else()
        SET(LIB_ARCH lib)
    endif()
endif()
if(PLATFORM_TYPE MATCHES "sigmastar")
    SET(CMAKE_SYSTEM_NAME Linux)
    SET(CMAKE_SYSTEM_PROCESSOR arm)
    SET(TOOLCHAIN_DIR /home/<USER>/TOOLCHAIN_for_All_Platform/2-SigmaStar/gcc-sigmastar-9.1.0-2020.07-x86_64_arm-linux-gnueabihf/)
    SET(CMAKE_C_COMPILER ${TOOLCHAIN_DIR}/bin/arm-linux-gnueabihf-sigmastar-9.1.0-gcc)
    SET(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/bin/arm-linux-gnueabihf-sigmastar-9.1.0-g++)
endif()

if ((PLATFORM_TYPE MATCHES "win_trt") OR (PLATFORM_TYPE MATCHES "linux_trt"))
    enable_language(CUDA)
else()
    SET(CMAKE_FIND_ROOT_PATH ${TOOLCHAIN_DIR})
    SET(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
    SET(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
    SET(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
    ###############编译选项#####################
    set(CMAKE_C_FLAGS "-fopenmp -O3 -mno-unaligned-access -fpermissive -Wfatal-errors -mfloat-abi=hard -mfpu=neon-vfpv4 -Werror=return-type -Werror=array-bounds -Werror=stringop-overflow -Wl,-z,now,-s" )
    set(CMAKE_CXX_FLAGS "-fopenmp -O3 -mno-unaligned-access -fpermissive -Wfatal-errors -mfloat-abi=hard -mfpu=neon-vfpv4 -Werror=return-type -Werror=array-bounds -Werror=stringop-overflow -Wl,-z,now,-s" )
endif()

############chip api#######################
if (PLATFORM_TYPE MATCHES "rv1126")
    INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/3rdparty/rv1126/rknn/include/)
    SET(RKNN_API_LIB ${CMAKE_SOURCE_DIR}/3rdparty/rv1126/rknn/lib/librknn_api.so)
    if(LIB_ARCH STREQUAL "lib")
        SET(OpenCV_DIR ${CMAKE_SOURCE_DIR}/3rdparty/rv1126/opencv_static/opencv-linux-armhf/share/OpenCV)
    else()
        SET(OpenCV_DIR ${CMAKE_SOURCE_DIR}/3rdparty/rv1126/opencv_static/opencv-linux-aarch64/share/OpenCV)
    endif()
    FIND_PACKAGE(OpenCV REQUIRED)
endif()
if (PLATFORM_TYPE MATCHES "sigmastar")    
    INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/include/kernel/include)
    INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/include/project/include/ipu)
    INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/include/project/include/ipu/tiramisu)
    INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/include/project/include)
    INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/include/opencv/include/opencv4)
    INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/include/opencv/include/opencv4/opencv2)
    LINK_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/lib/glibc/common_dynamic)
    LINK_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/lib/glibc/xvr_dynamic)
    LINK_DIRECTORIES(${CMAKE_SOURCE_DIR}/./3rdparty/sigmastar/lib/opencv/shared_lib_9.1.0)
endif ()
if (PLATFORM_TYPE MATCHES "linux_trt")
    # CUDA
    find_package(CUDA REQUIRED)
    message(STATUS "Find CUDA include at ${CUDA_INCLUDE_DIRS}")
    message(STATUS "Find CUDA libraries: ${CUDA_LIBRARIES}")

    # TensorRT
    set(TENSORRT_ROOT /usr/local/TensorRT-*******/)
    find_path(TENSORRT_INCLUDE_DIR NvInfer.h
            HINTS ${TENSORRT_ROOT} PATH_SUFFIXES include/)
    message(STATUS "Found TensorRT headers at ${TENSORRT_INCLUDE_DIR}")
    find_library(TENSORRT_LIBRARY_INFER nvinfer
            HINTS ${TENSORRT_ROOT} ${TENSORRT_BUILD} ${CUDA_TOOLKIT_ROOT_DIR}
            PATH_SUFFIXES lib lib64 lib/x64)
    find_library(TENSORRT_LIBRARY_ONNXPARSER nvonnxparser
            HINTS  ${TENSORRT_ROOT} ${TENSORRT_BUILD} ${CUDA_TOOLKIT_ROOT_DIR}
            PATH_SUFFIXES lib lib64 lib/x64)
    set(TENSORRT_LIBRARY ${TENSORRT_LIBRARY_INFER} ${TENSORRT_LIBRARY_ONNXPARSER})
    message(STATUS "Find TensorRT libs: ${TENSORRT_LIBRARY}")

    # OpenCV
    find_package( OpenCV REQUIRED )
    message(STATUS "Find OpenCV include at ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "Find OpenCV libraries: ${OpenCV_LIBRARIES}")

    include_directories(${CUDA_INCLUDE_DIRS} ${TENSORRT_INCLUDE_DIR} ${OpenCV_INCLUDE_DIRS})
    include_directories(${CMAKE_SOURCE_DIR}/../3rdparty/linux_trt/include/)
    set(TRT_API_LIB ${CMAKE_SOURCE_DIR}/../3rdparty/linux_trt/lib/libcuda_infer.so)
endif()

if (PLATFORM_TYPE MATCHES "win_trt")
    # 无参宏将 -D 定义标志添加到源文件的编译中
    # add_definitions (-D_USE_MATH_DEFINES)
    # add_definitions (-DNOMINMAX) # Win下避免std::max冲突
    # 更改编译器的代码页
    add_compile_options(/source-charset:utf-8 /execution-charset:gb2312)
    # CUDA
    find_package(CUDA REQUIRED)
    message(STATUS "Find CUDA include at ${CUDA_INCLUDE_DIRS}")
    message(STATUS "Find CUDA libraries: ${CUDA_LIBRARIES}")

    # TensorRT
    set(TENSORRT_ROOT "C:/local_install/TensorRT-*******/")
    find_path(TENSORRT_INCLUDE_DIR NvInfer.h
            HINTS ${TENSORRT_ROOT} PATH_SUFFIXES include/)
    message(STATUS "Found TensorRT headers at ${TENSORRT_INCLUDE_DIR}")
    find_library(TENSORRT_LIBRARY_INFER nvinfer
            HINTS ${TENSORRT_ROOT} ${TENSORRT_BUILD} ${CUDA_TOOLKIT_ROOT_DIR}
            PATH_SUFFIXES lib lib64 lib/x64)
    find_library(TENSORRT_LIBRARY_ONNXPARSER nvonnxparser
            HINTS  ${TENSORRT_ROOT} ${TENSORRT_BUILD} ${CUDA_TOOLKIT_ROOT_DIR}
            PATH_SUFFIXES lib lib64 lib/x64)
    set(TENSORRT_LIBRARY ${TENSORRT_LIBRARY_INFER} ${TENSORRT_LIBRARY_ONNXPARSER})
    message(STATUS "Find TensorRT libs: ${TENSORRT_LIBRARY}")

    # OpenCV
    set(OpenCV_DIR "C:/local_install/opencv/build")
    find_package( OpenCV REQUIRED )
    message(STATUS "Find OpenCV include at ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "Find OpenCV libraries: ${OpenCV_LIBRARIES}")

    include_directories(${CUDA_INCLUDE_DIRS} ${TENSORRT_INCLUDE_DIR} ${OpenCV_INCLUDE_DIRS})
    # 推理库
    include_directories(${CMAKE_SOURCE_DIR}/../3rdparty/win_trt/include)
    set(TRT_API_LIB ${CMAKE_SOURCE_DIR}/../3rdparty/win_trt/lib_dll/Release/cuda_infer.lib)
    # set(Proj_ROOT "C:/0-TKWW/CaiBingbing/IMS_test")
    # link_directories(${Proj_ROOT}/3rdparty/win_trt/lib_dll/Release)
    # link_directories(${Proj_ROOT}/3rdparty/win_trt/lib_dll/Debug)
    # link_directories(${Proj_ROOT}/3rdparty/win_trt/lib/Release)
    # link_directories(${Proj_ROOT}/3rdparty/win_trt/lib/Debug)
endif()
#####################################

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

if (PLATFORM_TYPE MATCHES "win_trt")
    set(IMS_API_LIB ${CMAKE_SOURCE_DIR}/lib_dll/Release/libims.lib)
    # link_directories(${CMAKE_SOURCE_DIR}/lib_dll/Release)
    # link_directories(${CMAKE_SOURCE_DIR}/lib_dll/Debug)
    # link_directories(${CMAKE_SOURCE_DIR}/lib/Release)
    # link_directories(${CMAKE_SOURCE_DIR}/lib/Debug)
else()
    #Linux
    set(IMS_API_LIB ${CMAKE_SOURCE_DIR}/lib/libims.so)
endif()

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/src DIR_SRCS)

add_executable(${NAME_BIN} ${DIR_SRCS})
target_link_libraries(${NAME_BIN} ${IMS_API_LIB})
################链接库############################
if (PLATFORM_TYPE MATCHES "rv1126")
    TARGET_LINK_LIBRARIES(${NAME_BIN} ${RKNN_API_LIB} ${OpenCV_LIBS})
endif()
if (PLATFORM_TYPE MATCHES "sigmastar")
    target_link_libraries(${NAME_BIN} mi_sys)
    target_link_libraries(${NAME_BIN} mi_ipu)
    #target_link_libraries(${NAME_BIN} mi_ive)
    #target_link_libraries(${NAME_BIN} mi_scl)
    target_link_libraries(${NAME_BIN} cam_fs_wrapper)
    target_link_libraries(${NAME_BIN} cam_os_wrapper)
    target_link_libraries(${NAME_BIN} pthread)
    #target_link_libraries(${NAME_BIN} rt)
    #target_link_libraries(${NAME_BIN} dl)
    
    target_link_libraries(${NAME_BIN} opencv_core)
    target_link_libraries(${NAME_BIN} opencv_imgproc)
    target_link_libraries(${NAME_BIN} opencv_imgcodecs)
    target_link_libraries(${NAME_BIN} opencv_highgui)
    #target_link_libraries(${NAME_BIN} opencv_video)
endif ()
if (PLATFORM_TYPE MATCHES "linux_trt")
    TARGET_LINK_LIBRARIES(${NAME_BIN} ${TRT_API_LIB})
    TARGET_LINK_LIBRARIES(${NAME_BIN} ${CUDA_LIBRARIES} ${TENSORRT_LIBRARY} ${OpenCV_LIBRARIES})
endif()

if (PLATFORM_TYPE MATCHES "win_trt")
    TARGET_LINK_LIBRARIES(${NAME_BIN} ${TRT_API_LIB})
    TARGET_LINK_LIBRARIES(${NAME_BIN} ${CUDA_LIBRARIES} ${TENSORRT_LIBRARY} ${OpenCV_LIBRARIES})

    # 根据编译类型设置
    set_target_properties(${NAME_BIN} PROPERTIES
        MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>"
    )
endif()
############################################




