TARGET_LIB  = libmcu.a

include ../../../Makefile.param

ifneq ($(BOARD),$(findstring $(BOARD),DMS31V2,<PERSON><PERSON>31SD<PERSON>,ADA32E1,ADA32E1,ADA32ESDK,ADA32V2,ADA32IR,ADA32N1,ADA32SDK,ADA32NSDK,ADA900V1,ADA47V1,ADA32V3,ADA32C4,ADA46V1,DMS51V1))
IGNORE_THIS_BUILD = mcu
endif

SRCS = $(wildcard *.c)

CFLAGS += -I$(INC_PATH)/cjson
CPPFLAGS = $(CFLAGS)
_TARGET_DIR_ = $(TOP_LIB)

include $(AUTO_DEP_MK) 