#
# Link all library, and build the final excutable file
#

include ../../Makefile.param

# Link SV Common library
SV_COM_LIBS = -lconfig -lmxml -llog -lmsg -lboard -lsafefunc -lcjson

# Link other SV libs
OTHER_SV_LIBS 	= -lcunit

ifeq ($(OSTYPE), linux)
SYSTEM_LIB	= -lpthread -lm 
ifeq ($(PLATFORM), SSC335)
CUNIT_PLATFORM = mstar
else ifeq ($(PLATFORM), RV1126)
CUNIT_PLATFORM = rk
else
CUNIT_PLATFORM = hisi
endif
endif

ifeq ($(OSTYPE), liteos)
SRCS    := $(wildcard *.c)
SRCS	+= ../liteos/$(PLATFORM_DIR)/app_init.c
SRCS	+= ../liteos/$(PLATFORM_DIR)/sdk_init.c
LD_FLAGS	+= $(OTHER_SV_LIBS)
SYSTEM_LIB	= -lshellcmd -lsharefifo -lssl -lcrypto -lm 
CUNIT_PLATFORM = $(PLATFORM_DIR)
endif

CFLAGS += -I../cunit/inc -I$(SRC_PATH)/msg
CPPFLAGS = $(CFLAGS)

TARGET_BIN	= test_config
LIB_DEPEND	= $(COMP_DEPEND)
LD_FLAGS	+= -L$(LIB_PATH) -L$(TOP_LIB) -L../cunit/lib/$(OSTYPE)/$(CUNIT_PLATFORM)
LD_FLAGS	+= $(SYSTEM_LIB) $(OTHER_SV_LIBS) $(SV_COM_LIBS)

COPY_TO_DIR = /work/nfs/
include $(BIN_AUTO_DEP_MK)

# vim:noet:sw=4:ts=4

