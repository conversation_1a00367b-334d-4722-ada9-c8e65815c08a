#ifndef DATA_QUEUE_H
#define DATA_QUEUE_H

#include <queue>
#include <mutex>
#include <condition_variable>
#include <iostream>
#include "media.h"

class DataQueue {
public:
    // 生产数据
    void produce(someip::MEDIA_FRAME *media_frame);
    
    // 消费数据
    void consume(someip::MEDIA_FRAME **media_frame);

    int get_size();

private:
    std::queue<someip::MEDIA_FRAME *> queue;
    std::mutex mtx;
};

#endif // DATA_QUEUE_H