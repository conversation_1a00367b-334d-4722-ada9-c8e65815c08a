<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="./css/jquery.mobile.css">
<link rel="stylesheet" href="./css/jquery.extra-themes.css">
<!--link rel="stylesheet" href="./css/common.css"-->
<script src="./js/jquery.min.js"></script>
<script src="./js/jquery.mobile.js"></script>
<script src="./js/FileSaver.js"></script>
<script src="./js/webapp-language.js"></script>
<title data-desc="system">System</title>
	<style>
		.ui-content {
			border: 0;
			padding: 0;
			margin: 0;
		}
		.uploader
		{
			width: 100%;
			color: #92AAB0;
			text-align: center;
			vertical-align: middle;
			padding: 0px 0px;
			margin-bottom: 10px;
			font-size: 200%;

			cursor: default;

			-webkit-touch-callout: none;
			-webkit-user-select: none;
			-khtml-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			user-select: none;
		}
		.uploader div.drag {
			padding: 60px 0px 60px 0px;
			border: 2px dotted #A5A5C7;
		}
		.uploader div.or {
			font-size: 50%;
			font-weight: bold;
			color: #C0C0C0;
			padding: 10px;
		}
		.uploader input {
			position: absolute;
			top: 0;
			right: 0;
			margin: 0;
			border: solid transparent;
			border-width: 0 0 100px 200px;
			opacity: .0;
			filter: alpha(opacity= 0);
			-o-transform: translate(250px,-50px) scale(1);
			-moz-transform: translate(-300px,0) scale(4);
			direction: ltr;
			cursor: pointer;
		}
		.upload-file {
			max-height: 100px;
			min-height: 40px;
			overflow: auto;
			cursor: default;
		}
		.file-size{
			font-style: italic;
			color: gray;
			font-size: 90%;
		}
		.file-status{
			color: gray;
		}
		.file-status-success{
			color: #009900;
		}
		.progress{
			height: 20px;  /* Can be anything */
			position: relative;
			background: #555;
			-moz-border-radius: 25px;
			-webkit-border-radius: 25px;
			border-radius: 25px;
			padding: 10px;
			box-shadow: inset 0 -1px 1px rgba(255,255,255,0.3);
		}
		.progress-bar{
			display: block;
			height: 100%;
			border-top-right-radius: 8px;
			border-bottom-right-radius: 8px;
			border-top-left-radius: 20px;
			border-bottom-left-radius: 20px;
			background-color: rgb(43,194,83);
			);
			box-shadow:
			inset 0 2px 9px  rgba(255,255,255,0.3),
			inset 0 -2px 6px rgba(0,0,0,0.4);
			position: relative;
			overflow: hidden;
		}
		#popup-show-upgrade, #popup-show-reboot {min-width:200px; max-width:400px;}
		#popup-show-upgrade .ui-content {margin: 20px;}
		#popup-show-reboot .ui-content {margin: 20px;}
		#popup-show-info {min-width:200px; max-width:400px;}
		#popup-show-info .ui-content {margin: 20px;}
		#popup-ask-for-sure {min-width:200px; max-width:400px;}
		#popup-ask-for-sure .ui-content {margin: 20px;}
		#popup-form-input3 {min-width:200px; max-width:400px;}
		#popup-form-input3 .ui-content {margin: 20px;}
	</style>
</head>
<body>
	<div id="page-system" data-role="none" style="overflow-x:hidden;background:#fff;margin-left: 10px;margin-right: 10px;">
		<div class='title index_hide_group'>
			<a class="title_back" href="./index.html" data-ajax="false"></a>
    		<p data-desc="system-maintenance">系统维护</p>	
		</div>
		<div class='title_hide index_hide_group'></div>
		
		<div role="main" class="ui-content">
			<div id="sys-info" style="margin-left: 20px;margin-right: 10px;"></div>
			<script id="sys-info-template" type="text/x-handlebars-template">	
				<div class="configmenu">
					<div data-role="none" class="input-text-box">
						<label data-role="none" id="tab-serial-num" for="ipcIdentification-serialNo" data-desc="serial-num">Serial Number</label>
						<input data-role="none" type="text" id="ipcIdentification-serialNo" readonly="true" value="{{ipcIdentification.serialNo}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" id="tab-sw-version" for="ipcIdentification-firmware" data-desc="sw-version">Software Version</label>
						<input data-role="none" type="text" id="ipcIdentification-firmware" readonly="true" value="{{ipcIdentification.firmware}}">
					</div>
					<div data-role="none" class="input-text-box Usr-Install">
						<label data-role="none" id="tab-hw-version" for="ipcIdentification-hardware" data-desc="hw-version">Hardware Version</label>
						<input data-role="none" type="text" id="ipcIdentification-hardware" readonly="true" value="{{{getSpecialHardware}}}">
					</div>
					<div data-role="none" class="input-text-box Usr-Install">
						<label data-role="none" id="tab-uuid" for="ipcIdentification-uuid" data-desc="uuid">UUID</label>
						<input data-role="none" type="text" id="ipcIdentification-uuid" readonly="true" value="{{ipcIdentification.uuid}}">
					</div>
					<div data-role="none" class="input-text-box" {{#unequal ipcIdentification.customer "200032"}}style="display:none;"{{/unequal}}>
						<label data-role="none" id="tab-luisSerial-num" for="ipcIdentification-spSerialNum" data-desc="luis-serial-num">LUIS S/N</label>
						<input data-role="none" type="text" id="ipcIdentification-spSerialNum" value="{{ipcIdentification.spSerialNum}}">
					</div>
					<div data-role="none" class="input-text-box" {{#unequal ipcIdentification.customer "200032"}}style="display:none;"{{/unequal}}>
						<label data-role="none" id="tab-article-num" for="ipcIdentification-articleNum">LUIS Article Number</label>
						<input data-role="none" type="text" id="ipcIdentification-articleNum" value="{{ipcIdentification.articleNum}}">
					</div>
				</div>
					
				<div class="configmenu">
					<h1><p>{{getKeyLang "upgrade"}}</p></h1>
					<div id="drag-and-drop-zone" class="uploadBtn">
						<div class="drag" data-desc="drag-drop-packet">Drag &amp; Drop Packet Here</div>
						<div class="or"></div>
						<div class="btn">
							<label class="input-label">
								<span data-desc="click-add-file">Click to open the file Browser</span>
								<input type="file" name="firmware" title='Click to add Files'>
							</label>
						</div>
					</div>
					<div class="alert alert-info">
						<p id="show-error">&nbsp;</p>
					</div>
					<div id="upload-status" class="upload-file"></div>
				</div>
				<div class="configmenu" {{#isActivate}}style="display:none;"{{/isActivate}}>
					<h1><p>{{getKeyLang "import-key"}}</p></h1>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="ipcIdentification-deviceId">Device ID</label>
						<input data-role="none" type="text" id="ipcIdentification-deviceId" readonly="true" value="{{ipcIdentification.deviceId}}">
					</div>
					<div data-role="none" class="input-text-box">
						<label data-role="none" for="ipcIdentification-keyAuth">Key Activated:</label>
						<input data-role="none" type="text" id="ipcIdentification-keyAuth" readonly="true" value="{{ipcIdentification.keyAuth}}">
					</div>
					<div id="click-import-key" class="uploadBtn">
						<div class="btn">
							<label class="input-label">
								<span data-desc="click-import-key">Import Key</span>
								<input type="file" id="import_key" name="importkey" title='open file' {{#isTrueVal ipcIdentification "keyAuth"}}disabled="disabled"{{/isTrueVal}}>
							</label>
						</div>
					</div>
					<div class="alert alert-info">
						<p id="show-error3">&nbsp;</p>
					</div>
					<div id="upload-status3" class="upload-file"></div>
				</div>

				<div class="configmenu" {{#isWFdev ipcIdentification.hardware}}style="display:none;"{{/isWFdev}}>
					<h1><p>{{getKeyLang "import-config"}}</p></h1>
					<div id="click-import-config" class="uploadBtn">
						<div class="btn">
							<label class="input-label">
								<span data-desc="click-import-config">Import Config</span>
								<input type="file" id="import_config" name="importconfig" title='open file'>
							</label>
						</div>
					</div>
				</div>
				<div class="configmenu" {{#isWFdev ipcIdentification.hardware}}style="display:none;"{{/isWFdev}}>
					<h1><p>{{getKeyLang "export-config"}}</p></h1>
					<div id="click-export-config" class="uploadBtn">
						<button id="export_config" style="background-color: var(--theme_color);border: none;">
								<label class="input-label">
									<span data-desc="click-export-config">Export Config</span>
								</label>
						</button>
					</div>
					<img id="loadingIcon" src="../../css/images/ajax-loader.gif" style="display:none;background-color: #e0e0e0;opacity: 0.2; position:fixed; top:50%; left:50%;transform: translate(-50%, -50%); width:46px;height:46px;border-radius: 50%; z-index:1000;" >
					<div id="save-config-status" class="save-config-file"></div>
				</div>
				<div class="configmenu" {{#isWFdev ipcIdentification.hardware}}style="display:none;"{{/isWFdev}}>
					<h1><p>{{getKeyLang "export-log"}}</p></h1>
					<div id="click-export-log" class="uploadBtn">
						<button id="export_logfile" style="background-color: var(--theme_color);border: none;">
								<label class="input-label">
									<span data-desc="click-export-log">Export Log</span>
								</label>
						</button>
					</div>
					<div id="save-log-status" class="save-log-file"></div>
				</div>
				<div class="configmenu" {{{hideHardware "ADA32V2 AICB046V1 ADA32C4 ADA32V3 RCT16947V2 ADA32IR ADA32N1 ADA32E1 MN234"}}}{{{hideNotWebuiFull "DMS31V2 DMS885N"}}}>
					<h1><p>{{getKeyLang "run-factory"}}</p></h1>
					<div id="run-factory" class="uploadBtn">
						<div class="btn">
							<label class="input-label">
								<span data-desc="click-run-factory">Run Factory</span>
								<input type="file" id="run_factory" name="factory" title='open file'>
							</label>
						</div>
					</div>
					<div class="alert alert-info">
						<p id="show-error2">&nbsp;</p>
					</div>
					<div id="upload-status2" class="upload-file"></div>
				</div>

				<div class="configmenu">
					<h1><p>{{getKeyLang "restore-factory"}}</p></h1>
					<div class="custom-select-box" {{{hideHardware "ADA32V2 AICB046V1 ADA32C4 ADA32IR ADA32N1 ADA32E1 ADA32V3 DMS31V2 DMS885N MN234" }}}>
						<label class="single_option_text" for="restore-type" data-desc="select-restore-type">Select restore type:</label>
			    		<div><select class="custom-select" id="restore-type" data-role="none" value="hard">
							<option value="hard">hard-restore</option>																
							<option value="soft"{{#isSupportAlg ipcIdentification.hardware}}style="display:none;"{{/isSupportAlg}}>soft-restore</option>
						</select></div>
				    </div>
					<div class="button-wrap">
						<a data-rel="popup" class="btn-popup popupbtn" data-popup-type="ask-for-sure" data-popup-title="restore" data-desc="action-restore">执行恢复</a>
					</div>
				</div>

				<div class="configmenu">
					<h1><p>{{getKeyLang "reboot"}}</p></h1>
					<a data-rel="popup" class="btn-popup popupbtn" data-popup-type="ask-for-sure" data-popup-title="reboot" data-desc="action-reboot">执行重启</a>	
				</div>
	
				<!---div class="configmenu">
					<h1><p>{{getKeyLang "resetPwd"}}</p></h1>
					<a data-rel="popup" class="btn-popup popupbtn" data-popup-type="ask-for-sure" data-popup-title="resetPwd" data-desc="action-resetPwd">重置密码</a>	
				</div --->

				<a id="form-input3" href="#popup-form-input3" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
				<div data-role="popup" id="popup-form-input3" data-dismissible="false" data-popup-title="">
					<div data-role="header">
						<h1></h1>
					</div>
					<div role="main" class="ui-content">
						<h3></h3>
						<form>
							<div>
								<label for="form-input3-0" class="ui-hidden-accessible"></label>
								<input class="char-normal" oninput="handleInput(event)" id="form-input3-0" value="" placeholder="" type="text">
								<label for="form-input3-1" class="ui-hidden-accessible"></label>
								<input class="pwd-limit char-normal" oninput="handleInput(event)" id="form-input3-1" value="" placeholder="" type="text">
								<label for="form-input3-2" class="ui-hidden-accessible"></label>
								<input class="pwd-limit char-normal" oninput="handleInput(event)" id="form-input3-2" value="" placeholder="" type="text">
								<label for="form-input3-3" class="ui-hidden-accessible"></label>
								<input class="pwd-limit char-normal" oninput="handleInput(event)" id="form-input3-3" value="" placeholder="" type="text">
								<p class="form-input-note">&nbsp;</p>
								<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
								<a id="btn-form-input3" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-desc="confirm">确定</a>
							</div>
						</form>
					</div>
				</div>
				<div class="configmenu">
					<h1><p>{{getKeyLang "change-pwd"}}</p></h1>
					<a href="#" data-rel="popup" data-position-to="window" class="btn-popup popupbtn" text-decoration:none data-popup-type="form-input3" data-popup-title="change-pwd" data-desc="change-pwd">修改密码</a>
				</div>
				
				<a id="form-input4" href="#popup-form-input4" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
				<div data-role="popup" id="popup-form-input4" data-dismissible="false" data-popup-title="">
					<div data-role="header">
						<h1></h1>
					</div>
					<div role="main" class="ui-content">
						<h3></h3>
						<form>
							<div>
								<p class="form-input-note">&nbsp;</p>
								<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
								<a id="btn-form-input4" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-desc="confirm">确定</a>
							</div>
						</form>
					</div>
				</div>
				<div class="configmenu">
					<h1><p>{{getKeyLang "devtime"}}</p></h1>	
						<div data-role="none" class="input-text-box">
							<label data-role="none" id="tab-devtime" for="systemConfig-changetime"" data-desc="devtime">DevTime</label>
							<input data-role="none" style="width: 150px;" type="text" id="systemConfig-changetime" readonly="true" value="">
						</div>
						<div>
							<a href="#" data-rel="popup" data-position-to="window" class="btn-popup popupbtn" data-popup-type="ask-for-sure" data-popup-title="change-time" data-desc="change-time">校准设备时间</a>
						</div>
					
				</div>
			</script>
		</div>
		<!-- div data-role="footer" data-position="fixed" data-fullscreen="false">
			<a style="visibility:hidden;"></a>
			<a href="#" id="btn-submit" data-role="button" data-desc="confirm">确定</a>
			<a href="#" id="btn-cancel" data-role="button" data-desc="cancel">取消</a>
		</div-->
		<a id="show-upgrade" href="#popup-show-upgrade" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-show-upgrade" data-dismissible="false" data-popup-title="">
			<div data-role="header">
				<h1 data-desc="updating">Updating...</h1>
			</div>
			<div role="main" class="ui-content">
				<h3 data-desc="keep-power-on">Please keep the device power on.</h3>
				<form>
					<div>
						<p style="display: inline-block;"data-desc="reconnect">will try reconnect after</p>&nbsp;<span style="display: inline-block;" id="wait-sec">180</span><p style="display: inline-block;">s.</p>
					</div>
				</form>
			</div>
		</div>
		<a id="show-reboot" href="#popup-show-reboot" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-show-reboot" data-dismissible="false" data-popup-title="">
			<div data-role="header">
				<h1 data-desc="rebooting">Rebooting...</h1>
			</div>
			<div role="main" class="ui-content">
				<h3 data-desc="keep-power-on">Please keep the device power on.</h3>
				<form>
					<div>
						<p style="display: inline-block;" data-desc="reconnect">will try reconnect after</p><span style="display: inline-block;" id="wait2-sec">20</span><p style="display: inline-block;">s.</p>
					</div>
				</form>
			</div>
		</div>
		<!-- div class="footer" data-role="footer" data-position="fixed" data-mini="true"></div -->
		<a id="show-info" href="#popup-show-info" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-show-info" data-dismissible="false">
			<div data-role="header">
				<h1></h1>
			</div>
			<div role="main" class="ui-content">
				<h3></h3>
				<p></p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inlin" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
			</div>
		</div>
		<a id="ask-for-sure" href="#popup-ask-for-sure" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-ask-for-sure" data-dismissible="false" data-popup-title="" align="center">
			<div data-role="header">
				<h1></h1>
			</div>
			<div role="main" class="ui-content">
				<h3></h3>
				<p></p>
				<a class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
				<a id="btn-ask-for-sure" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
			</div>
		</div>
	</div>
	<script src="./js/handlebars.js"></script>
	<script src="./js/dmuploader.js"></script>
	<script src="./js/spark-md5.min.js"></script>
	<script src="./js/webapp-model.js"></script>
	<script src="./js/webapp-common.js"></script>
	<script src="./js/webapp-view.js"></script>
	<script src="./js/webapp-ctrller.js"></script>
	<script type="text/javascript">
		$(document).ready(function() {
			var customer;
			var itemCustomer = window.location.host+"-customer";
			window.localStorage && (customer = window.localStorage.getItem(itemCustomer));
			console.log(customer);
			var hardware;
			var itemHardware = window.location.host+"-hardware";
			window.localStorage && (hardware = window.localStorage.getItem(itemHardware));
			if(customer=="200032" && hardware.indexOf("HDW845") == -1 ){
				if ((navigator.userAgent.match(/(phone|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-common.css"}).appendTo("head");
				}
			}
			else{
				if ((navigator.userAgent.match(/(phone|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/common.css"}).appendTo("head");
				}
			}

			switchCssStyle(hardware, customer);
			var model = new WebappModel();
			var view = new WebappView(model, "system");
			var controller = new WebappController(model, view);			
			controller.refreshSysInfo();                
			controller.startDeviceTime();
		});
	</script>
</body>
</html>