#include "stdlib.h"
#include <sys/sem.h>
#include <pthread.h>
#include "cms_controlThread.h"
#include "cms_offlineInfo.h"
#include "fence.h"

#define CONTROL_RECONNECT_TIME 5

const sint32 sendAliveTime = (3*1000*1000); //us
const sint32 sleepTime = (1000*100); //us
const sint32 updateCarStateTime = (6*1000*1000); //us

/******************************************************************************
 * 函数功能: 日志文件写入锁定 (互斥写入)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 cms_logWriteLock()
{
    sint32 s32Ret = 0;
    sint32 s32SemId = 0;
	s32SemId = semget(LOGFILE_SEM_KEY, 1, 0600);
    if (s32SemId < 0)
    {
        print_level(SV_ERROR, "semget failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }

    struct sembuf stSemOpt;
    stSemOpt.sem_num = 0;
    stSemOpt.sem_op = -1;
    stSemOpt.sem_flg = SEM_UNDO;
    s32Ret = semop(s32SemId, &stSemOpt, 1);
    if (s32Ret < 0)
    {
        if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 日志文件写入解锁 (互斥写入)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 cms_logWriteUnlock()
{
    sint32 s32Ret = 0;
    sint32 s32SemId = 0;
    s32SemId = semget(LOGFILE_SEM_KEY, 1, 0600);
    if (s32SemId < 0)
    {
        print_level(SV_ERROR, "semget failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }

    struct sembuf stSemOpt;
    stSemOpt.sem_num = 0;
    stSemOpt.sem_op = 1;
    stSemOpt.sem_flg = SEM_UNDO;
    s32Ret = semop(s32SemId, &stSemOpt, 1);
    if (s32Ret < 0)
    {
        if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解析读取 log/cache 的内容
 * 输入参数: pu8Log --- 消息包指针
 * 输出参数: stLogUploadReq --- 发送包指针
 			 contenstr --- 发送包log内容指针
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 LogFileHandle(uint8 *pu8Log,SKLogUploadReq *stLogUploadReq,char *contentstr)
{
	sint32 s32Ret = 0;
    char *pszTmp = NULL;
	char *pszTTmp = NULL;
	char *timestr = NULL;
	char *level = NULL;
	char *content = NULL;
	sint32 year;
	sint32 month;
	sint32 day;
	sint32 hour;
	sint32 min;
	sint32 second;

	static sint32 UploadLogNum = 0;
	if (NULL == pu8Log)
    {
		print_level(SV_ERROR,"pointer null.\n");
        return ERR_NULL_PTR;
    }
	//printf("pu8Log = %s \n",pu8Log);
	level = strstr((char *)pu8Log,"level=");
    if (NULL != level)
    	stLogUploadReq->ucLogLevel = atoi(level+strlen("level="));
	else
	{
		print_level(SV_ERROR,"pointer null ------%s .\n",pu8Log);
   		return ERR_NULL_PTR;
	}

	stLogUploadReq->uiLogId = ++UploadLogNum;

	content = strstr((char *)pu8Log,"file=");
	pszTmp = strstr((char *)pu8Log,"upLoadStatus");
	if(pszTmp!=NULL)
		*pszTmp = '\0';
	if(NULL == content)
	{
		print_level(SV_ERROR,"pointer null.\n");
		return ERR_NULL_PTR;
	}
	stLogUploadReq->usContentLength = strlen(content);
	strcpy(contentstr,content);

	timestr =  strtok_r((char *)pu8Log, "	", &pszTTmp);
	if(NULL == timestr)
	{
		print_level(SV_ERROR,"pointer null.\n");
		return ERR_NULL_PTR;
	}
	sscanf(timestr, "%04d%02d%02d%02d%02d%02d",&year,&month,&day,&hour,&min,&second );
	strcpy(stLogUploadReq->szTime,timestr);
	sprintf(stLogUploadReq->szTime,"%04d-%02d-%02d %2d:%02d:%02d",year,month,day,hour,min,second);
	return SV_SUCCESS;

}

SV_NETWORK_CONTROLTHREAD::SV_NETWORK_CONTROLTHREAD(SV_NETWORK_DVRINFO* pTempinfo,
 	SV_NETWORK_PROTOCOL* pTempProtocol, SV_NETWORK_LINK* pTempLink, SV_NETWORK_STATE* pTempState)
 	:pDvrinfo(pTempinfo),pProtocol(pTempProtocol),pLink(pTempLink),pState(pTempState)
{

}

SV_NETWORK_CONTROLTHREAD::~SV_NETWORK_CONTROLTHREAD()
{


}

void SV_NETWORK_CONTROLTHREAD::run()
{

//1.register to server
    uint32 u32StorageErrCodeLast = 0;
    uint32 u32StorageErrCodeNow = 0;
	sint32 s32Count = CONTROL_RECONNECT_TIME;
	uint32 u32SendAlivecount = 0;
	sint32 s32Ret = 0;
    sint32 s32CarStateInterval = 60;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111219))
    {
        s32CarStateInterval = 40;
    }

	while(pState->getNetworkRunning())
	{

		while( !pState->getControlRegisterFlag() && pState->getNetworkRunning())
		{
		    //print_level(SV_DEBUG,"getControlRegisterFlag is false\n");
			if(s32Count < CONTROL_RECONNECT_TIME)
			{
				++s32Count;
				sleep(1);
				continue;
			}
			else
			{
				s32Count = 0;
                //pProtocol->createCarStateOffline();
			}

            pLink->closeSocket();
            //pLink->setSocketBufSize(256*1024);

            pLink->setServerIp((const char *)pDvrinfo->getIpAddr().c_str());
			pLink->setDeviceName((const char *)pDvrinfo->getNetCardName().c_str());
			pLink->setPort(pDvrinfo->getIpPort());
			print_level(SV_DEBUG, "Control link port:%d ip:%s devicename:%s \n",pLink->getServerPort(),pLink->getServerIp(),pLink->getDevName());
			if( pLink->initConnect() != SV_SUCCESS )
			{
				print_level(SV_WARN, "Control link connect error.\n");
				pState->setRegisterReason( pLink->getConnectReson() );
				sleep(1);
				continue;
			}
			else
			{
				print_level(SV_DEBUG, "Control link connect success.\n");
			}

			SK_HEADER header;
			SKRegisterReq registerReq;
			pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_REGISTER_REQ, sizeof(SKRegisterReq), SK_DEFAULT_CONTROL_SERVER_DST_ID);
			pProtocol->createRegisterReq(&registerReq, 3);
			pLink->sendToServer(&header, (char *)&registerReq, sizeof(SKRegisterReq));

			sleep(2);
		}

		getServerInfo();

		while (pState->getNetworkRunning())
		{
		    if( !pState->getControlRegisterFlag() ) // reconnect
			{
				print_level(SV_WARN, "register flag false, break!\n");
				break;
			}

            if (pLink->sendListData() != SV_SUCCESS)
            {
                //print_level(SV_ERROR,"sendListData Empty.\n");
            }

#if !defined(BOARD_ADA32N1)
			/* 每秒检测一遍存储卡错误码是否有变化，变化的话，发送一条日志给CMS */
            if (0 == (u32SendAlivecount % 10))
    		{
    		    u32StorageErrCodeNow = pState->getStorageErrCode();
    			if (0 != u32StorageErrCodeNow && u32StorageErrCodeLast != u32StorageErrCodeNow)
    			{
                    s32Ret = sendErrorLog(u32StorageErrCodeNow);
                    if (SV_SUCCESS != s32Ret)
                    {
    					print_level(SV_ERROR, "sendErrorLog failed!\n");
                    }
                    else
                    {
                        //print_level(SV_INFO, "sendErrorLog success!\n");
                    }
    			}
                u32StorageErrCodeLast = u32StorageErrCodeNow;
    		}
#endif

            if (0 == (u32SendAlivecount % 30))  // 3 second 100 ms x 30= 3s
    		{
    			if (sendAlive() <= 0)
    			{
    				print_level(SV_ERROR,"ControlThread sendAlive err.\n");
    				pState->setControlRegisterFlag(SV_FALSE);
    				break;
    			}
    			else
    			{
    			    //print_level(SV_INFO, "sendAlive success!\n");
    			}
    		}

    		usleep(sleepTime); //100ms
			++u32SendAlivecount;
			if(u32SendAlivecount > 6000)  // 10min = 10 * 60 * 10 * 100 ms
			{
				u32SendAlivecount = 0;
			}
		}
	}
}

/* 和TF卡相关的操作放在这条线程里面去发送，避免存储卡读写慢时导致无法发送心跳包等 */
void* SV_NETWORK_CONTROLTHREAD::timerTaskRun(void *arg)
{
    sint32 s32Ret, i = 0;
    SV_NETWORK_CONTROLTHREAD *ptr = (SV_NETWORK_CONTROLTHREAD *)arg;

    uint32 u32StorageErrCodeLast = 0;
    uint32 u32StorageErrCodeNow = 0;
	sint32 s32Count = CONTROL_RECONNECT_TIME;
	uint32 u32SendAlivecount = 0;
    sint32 s32CarStateInterval = 60;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111219))
    {
        s32CarStateInterval = 40;
    }

#if (defined(BOARD_ADA32C4))
	s32CarStateInterval = 30;
#endif

    while (ptr->pState->getNetworkRunning())
    {
        if (!ptr->pState->getControlRegisterFlag())
        {
            //print_level(SV_WARN, "no register, continue!\n");
            sleep_ms(500);
            continue;
        }

        /* 6s一次GPS数据 */
		if (0 == (u32SendAlivecount % s32CarStateInterval))
		{
			if (ptr->sendCarStateJson() <= 0)  //wfcr20s2需要的数据待定(经纬度、姿态角)
			{
				print_level(SV_ERROR,"ControlThread sendCarState err\n");
				ptr->pState->setControlRegisterFlag(SV_FALSE);
                continue;
			}
			else
			{
				//print_level(SV_DEBUG,"sendCarState success!\n");
			}
		}
        else
        {
            s32Ret = ptr->sendOfflineCarStateFile();
            if (SV_SUCCESS != s32Ret)
            {
				print_level(SV_ERROR, "sendOfflineCarStateFile failed!\n");
            }
        }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4) )
		s32Ret = ptr->sendOfflineFile();
		if (SV_SUCCESS != s32Ret)
        {
			print_level(SV_ERROR, "sendOfflineFile failed!\n");
        }
#endif

		usleep(sleepTime); //100ms
		++u32SendAlivecount;
		if (u32SendAlivecount > 6000)  // 10min = 10 * 60 * 10 * 100 ms
		{
			u32SendAlivecount = 0;
		}
    }

    return NULL;
}

void* SV_NETWORK_CONTROLTHREAD::carStateRun(void *arg)
{
    sint32 i = 0;
    bool bRename = false;
    SV_NETWORK_CONTROLTHREAD *ptr = (SV_NETWORK_CONTROLTHREAD *)arg;

    sint32 s32OfflineCarStateInterval = 6;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111219))
    {
        s32OfflineCarStateInterval = 4;
    }

    while (ptr->pState->getNetworkRunning())
    {
        if (ptr->pState->getControlRegisterFlag())
        {
            if (!bRename)
            {
                bRename = true;
                ptr->pProtocol->renameCarStateOfflineFlie();
            }
            sleep_ms(1000);
            continue;
        }

        bRename = false;
        ptr->pProtocol->createCarStateOffline();
        for (i = 0; i < s32OfflineCarStateInterval; i++)
        {
            if (ptr->pState->getControlRegisterFlag())
            {
                break;
            }
            sleep_ms(1000);
        }
    }

    return NULL;
}

void SV_NETWORK_CONTROLTHREAD::startTimerRun()
{
    pthread_t pid;
    pthread_attr_t attr;
	pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    if (pthread_create(&pid, &attr, timerTaskRun, (void *)this) != 0)
    {
    	printf("create thread error\n");
        return;
    }

    pthread_attr_destroy(&attr);
    return;

}

void SV_NETWORK_CONTROLTHREAD::startCarStateRun()
{
    pthread_t pid;
    pthread_attr_t attr;
	pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    if (pthread_create(&pid, &attr, carStateRun, (void *)this) != 0)
    {
    	printf("create thread error\n");
        return;
    }

    pthread_attr_destroy(&attr);
    return;
}

sint32 SV_NETWORK_CONTROLTHREAD::sendErrorLog(uint32 u32ErrCode)
{
    sint32 s32Ret = 0;
    struct timeval tvNow;
    struct timezone tz;
    struct tm tmUtcTime;
    char szLogId[32] = {0};
    char szLogTime[32] = {0};
    SK_HEADER header;
    SKLogUploadReq stLogUpload = {0};
    char szErrCode[32] = {0};
	char szBuf[2048] = {0};
	sint32 s32SendSize = 0;
	cJSON *pstRoot = NULL, *pstErrCode = NULL, *pstReason = NULL;
	char *out = NULL;
	char *pJSON = szBuf+sizeof(SKLogUploadReq);

	gettimeofday(&tvNow, &tz);
    gmtime_r((time_t *)&tvNow.tv_sec, &tmUtcTime);  // 平台那边要求统一上传时间为UTC时间

    snprintf(szLogId, 32, "%02d%02d%02d%02d%02d",  (tmUtcTime.tm_mon+1),
		tmUtcTime.tm_mday, tmUtcTime.tm_hour, tmUtcTime.tm_min, tmUtcTime.tm_sec);
    print_level(SV_INFO, "szLogId: %s\n", szLogId);

	snprintf(szLogTime, 32, "%04d-%02d-%02d %02d:%02d:%02d", (tmUtcTime.tm_year+1900), (tmUtcTime.tm_mon+1),
		tmUtcTime.tm_mday, tmUtcTime.tm_hour, tmUtcTime.tm_min, tmUtcTime.tm_sec);

    stLogUpload.ucLogLevel = 0;
    stLogUpload.uiLogId = strtol(szLogId, NULL, 10);
	strncpy((char *)stLogUpload.szTime, szLogTime, SK_DATE_TIME_STRING_MAX_LENGTH);

	pstRoot = cJSON_CreateObject();
	if (NULL == pstRoot)
	{
        print_level(SV_ERROR, "cJSON_CreateObject failed!\n");
        return SV_FAILURE;
	}

    sprintf(szErrCode, "%d", u32ErrCode);
	cJSON_AddItemToObject(pstRoot, "ErrorCode", cJSON_CreateString(szErrCode));
	cJSON_AddItemToObject(pstRoot, "Reason", cJSON_CreateString(""));
	out  = cJSON_Print(pstRoot);

	stLogUpload.usContentLength = strlen(out)+1;
	s32SendSize = stLogUpload.usContentLength + sizeof(SKLogUploadReq);

 	pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_UPLOAD_LOG_REQ, s32SendSize, SK_DEFAULT_CONTROL_SERVER_DST_ID);
	memcpy(szBuf, &stLogUpload, sizeof(stLogUpload));
	memcpy(pJSON, out, stLogUpload.usContentLength);
	pLink->sendToServer(&header, szBuf, s32SendSize);
	print_level(SV_DEBUG, "time:%s uiLogId:%d, out: %s\n", stLogUpload.szTime, stLogUpload.uiLogId, out);

    free(out);
    cJSON_Delete(pstRoot);
	return SV_SUCCESS;
}

sint32 SV_NETWORK_CONTROLTHREAD::sendOfflineCarState()
{
    sint32 s32Ret = 0;
	cJSON *json = NULL;
	SK_HEADER header = {0};
    SKDeviceInfoReq stDeviceInfoReq = {0};
    char szBuf[30*1024] = {0}, szAlarmId[SK_ALARM_ID_LENGTH] = {0};
	sint32 s32SendSize = 0;
	char *pJSON = szBuf;

    s32Ret = SV_DVR_GetOfflineCarStateInfo(OFFLINE_CARSTATE_TYPE, pJSON, 30*1024, szAlarmId);
    if (SV_SUCCESS != s32Ret)
	{
		//print_level(SV_ERROR, "SV_DVR_GetofflineInfo fail!\n");
		return SV_FAILURE;
	}

	//print_level(SV_DEBUG,"len:%d pJSON:%s szAlarmId: %s\n", strlen(pJSON), pJSON, szAlarmId);
	json = cJSON_Parse(pJSON);
	if (NULL == json)
	{
		print_level(SV_ERROR,"Error before: [%s]\n", cJSON_GetErrorPtr());
		SV_DVR_DelOfflineCarStateInfo(OFFLINE_CARSTATE_TYPE);
		return SV_FAILURE;
	}

    stDeviceInfoReq.ullDeviceInfoId = strtoull(szAlarmId, NULL, 10);
    stDeviceInfoReq.uiDataLen = strlen(pJSON) + 1;

    s32SendSize = sizeof(SKDeviceInfoReq) + stDeviceInfoReq.uiDataLen;
 	pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_UPLOAD_DEVICE_STATE_INFO_REQ, s32SendSize, SK_DEFAULT_CONTROL_SERVER_DST_ID);
    pLink->sendJsonToServer(&header, &stDeviceInfoReq, pJSON, stDeviceInfoReq.uiDataLen);
	pState->setSendCarStateId(stDeviceInfoReq.ullDeviceInfoId);
	cJSON_Delete(json);
	return SV_SUCCESS;
}

sint32 SV_NETWORK_CONTROLTHREAD::sendOfflineCarStateFile()
{
    sint32 s32Ret = SV_SUCCESS;
    char szStoragePath[32] = {0};
    char szPath[128] = {0};

	if (pState->isSendCarStateSuccess())
	{
        SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);
        sprintf(szPath, "%s/%s", szStoragePath, CAR_STATE_PATH);
        if (COMMON_IsPathExist(CAR_STATE_PATH_MEM) || COMMON_IsPathExist(szPath))
        {
		    s32Ret = sendOfflineCarState();
        }
	}
	return s32Ret;
}

sint32 SV_NETWORK_CONTROLTHREAD::sendofflineAlarm()
{
	static sint32 s32Count;
	cJSON *json, *pOther, *pAlarmId, *pTime, *pType;
	char *out;
	SK_HEADER header;
	SKAlarmUploadReq stAlarmReq;
	char szBuf[2048] = {0}, szAlarmId[SK_ALARM_ID_LENGTH] = {0};
	sint32 s32SendSize = 0, s32JsonLen = 0 ;
	char *pJSON = szBuf+sizeof(SKAlarmUploadReq);

	if( SV_DVR_GetofflineInfo(OFFLINE_ALARM_TYPE, pJSON, 2000, SV_FALSE) != SV_SUCCESS)
	{
		//printf("SV_DVR_GetofflineInfo fail!\n");
		return SV_FAILURE;
	}

	if (strlen(pJSON) == 0)
	{
	    print_level(SV_WARN, "get json info failed!\n");
		SV_DVR_DelofflineInfo("test", OFFLINE_ALARM_TYPE);
	}

	//print_level(SV_DEBUG,"len:%d pTest:%s\n", strlen(pJSON), pJSON);
	json=cJSON_Parse(pJSON);
	if (!json)
	{
		print_level(SV_ERROR,"Error before: [%s]\n",cJSON_GetErrorPtr());
		SV_DVR_DelofflineInfo("test", OFFLINE_ALARM_TYPE);
		return SV_FAILURE;
	}
	else
	{
		pOther = cJSON_GetObjectItem(json, "Other");
		if(pOther != SV_NULL)
		{
			pType = cJSON_GetObjectItem(json, "Type");
			pAlarmId = cJSON_GetObjectItem(pOther, "AlarmId");
			pTime = cJSON_GetObjectItem(pOther, "Time");
			print_level(SV_DEBUG,"AlarmId:%s alarmType: %d, Time:%s\n", pAlarmId->valuestring, pType->valueint, pTime->valuestring);

			snprintf(szAlarmId, SK_ALARM_ID_LENGTH, "%s_%d", pAlarmId->valuestring, ++s32Count);
			strncpy((char *)stAlarmReq.szAlarmId, szAlarmId, SK_ALARM_ID_LENGTH);
			strncpy((char *)stAlarmReq.szTime, pTime->valuestring, SK_DATE_TIME_STRING_MAX_LENGTH);
			stAlarmReq.ucType = pType->valueint;
		}
		cJSON_DeleteItemFromObject(json, "Other");
	}

	out=cJSON_Print(json);
	stAlarmReq.usContentLength = strlen(out)+1;
	s32SendSize = stAlarmReq.usContentLength + sizeof(SKAlarmUploadReq);

 	pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_UPLOAD_ALARM_REQ, s32SendSize,SK_DEFAULT_CONTROL_SERVER_DST_ID);
	memcpy(szBuf, &stAlarmReq, sizeof(stAlarmReq));
	memcpy(pJSON, out, stAlarmReq.usContentLength);
	pLink->sendToServer(&header, szBuf, s32SendSize);
	pState->setSendAlarmId(szAlarmId);
	pState->setAlarmType(stAlarmReq.ucType);
	print_level(SV_DEBUG,"Id:%s time:%s type:%d, len:%d\n",
		stAlarmReq.szAlarmId, stAlarmReq.szTime, stAlarmReq.ucType, stAlarmReq.usContentLength);
	::free(out);
	cJSON_Delete(json);
	return SV_SUCCESS;

}

sint32 SV_NETWORK_CONTROLTHREAD::sendOfflineFile()
{
    char szOfflineAlarm[128] = {0};
    char szStoragePath[32] = {0};
    SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);
    sprintf(szOfflineAlarm, "%s/offlineAlarm", szStoragePath);

	if (pState->isSendAlarmSuccess() && (COMMON_IsPathExist("/tmp/offlineAlarm") || COMMON_IsPathExist(szOfflineAlarm)))
	{
        sendofflineAlarm();
	}
	return SV_SUCCESS;
}

sint32 SV_NETWORK_CONTROLTHREAD::sendAlive()
{

	SK_HEADER header;
	SKKeepAliveReq alive;

	pProtocol->createHeader(&header, SK_CLIENT_TO_CMS_SERVER_KEEP_ALIVE_REQ, sizeof(SKKeepAliveReq), SK_DEFAULT_CONTROL_SERVER_DST_ID);
	pProtocol->createKeepAlive(&alive, 0);
	return pLink->sendToServer(&header, (char *)&alive, sizeof(SKKeepAliveReq));

}

sint32 SV_NETWORK_CONTROLTHREAD::sendCarState()
{
    sint32 s32Ret = 0;
	SK_HEADER header;
	SKCarState stCarState;

	pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_CAR_STATE, sizeof(SKCarState), SK_DEFAULT_CONTROL_SERVER_DST_ID);
	s32Ret = pProtocol->createCarState(&stCarState);
    if (s32Ret != SV_SUCCESS)
    {
        return 1;
    }

    s32Ret = pLink->sendToServer(&header, (char *)&stCarState, sizeof(SKCarState));
	return s32Ret;

}

sint32 SV_NETWORK_CONTROLTHREAD::sendCarStateJson()
{
    sint32 s32Ret = 0;
	SK_HEADER header;
    SKDeviceInfoReq deviceInfoReq;
    uint32 u32DataLen = 0;
    char *pszDataJson = NULL;

	s32Ret = pProtocol->createCarStateJson(&header, &deviceInfoReq, &pszDataJson, &u32DataLen);
    if (SV_SUCCESS != s32Ret)
    {
        return 1;
    }
    s32Ret = pLink->sendJsonToServer(&header, &deviceInfoReq, pszDataJson, u32DataLen);
    free(pszDataJson);
	return s32Ret;
}

sint32 SV_NETWORK_CONTROLTHREAD::getServerInfo()
{
    sint32 s32Ret = 0;

	SK_HEADER header;
	pProtocol->createHeader(&header, SK_CLIENT_TO_CMS_SERVER_GET_CONFIG_INFO_REQ, 0, SK_DEFAULT_CONTROL_SERVER_DST_ID);
	s32Ret = pLink->sendToServer(&header, "test", 0);
	return s32Ret;

}

sint32 SV_NETWORK_CONTROLTHREAD::uploadCacheLog()
{
	sint32 s32Ret = 0;
    sint32 s32Level = 0;
    sint32 s32LastOffset;
    uint32 u32Len = 0;
    SV_BOOL bEOF = SV_FALSE;
    FILE *fp = NULL, *fpw = NULL;
    char szBuf[4096];
    char szNewPath[64];
	char szCachePath[64];
    char *pszTmp = NULL, *pszLevel = NULL;
    char szStoragePath[32] = {0};

	if (!SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath))
		strcpy(szCachePath, "/var/log/cache");
	else
		sprintf(szCachePath, "%s/log/cache", szStoragePath);

    fp = fopen(szCachePath, "r");
    if (NULL == fp)
    {
        print_level(SV_ERROR, "fopen file: %s failed.\n", szCachePath);
        return ERR_UNEXIST;
    }

    while (1)
    {
        s32LastOffset = ftell(fp);
        if (NULL == fgets(szBuf, 4096, fp))
        {
            bEOF = SV_TRUE;
            break;
        }

        pszTmp = strstr(szBuf, "upLoadStatus=0000");
        pszLevel = strstr(szBuf, "type=\"normalLog\"\tlevel=");
        if (NULL == pszTmp || NULL != strstr(pszTmp, "upLoadStatus=00000") ||
            (NULL != pszLevel && atoi(pszLevel+strlen("type=\"normalLog\"\tlevel=")) > pDvrinfo->getLogUploadLevel() ))
        {
            continue;
        }
        //printf("uploadCachdLog content : %s", szBuf);
        *((char *)(pszTmp-1)) = '\n';
        s32Ret = uploadLogContent((uint8 *)szBuf, pszTmp-szBuf);
		if(SV_SUCCESS != s32Ret)
		{
			continue;
		}
        u32Len = strlen("upLoadStatus=00000");
        memcpy(pszTmp, "upLoadStatus=00000", u32Len);

        s32Ret = cms_logWriteLock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "fty_WriteLock failed. [err=%#x]\n", s32Ret);
        }
        fpw = fopen(szCachePath, "r+");
        if (NULL == fpw)
        {
            print_level(SV_WARN, "fopen file: %s failed.\n", szCachePath);
            cms_logWriteUnlock();
            break;
        }
        if (fseek(fpw, s32LastOffset+pszTmp-szBuf, SEEK_SET) < 0)
        {
            print_level(SV_ERROR, "fseek to offset=%d failed. [err: %s]\n", s32LastOffset+pszTmp-szBuf, strerror(errno));
            cms_logWriteUnlock();
            break;
        }
        if (1 != fwrite(pszTmp, u32Len, 1, fpw))
        {
            print_level(SV_ERROR, "fwrite to offset=%d failed. [err: %s]\n", s32LastOffset+pszTmp-szBuf, strerror(errno));
            cms_logWriteUnlock();
            break;
        }
        fclose(fpw);
        s32Ret = cms_logWriteUnlock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "fty_WriteUnlock failed. [err=%#x]\n", s32Ret);
        }
    }
    fclose(fp);

    return SV_SUCCESS;


}

sint32 SV_NETWORK_CONTROLTHREAD::uploadLogContent(uint8 *pu8Log, uint32 u32Size)
{
	sint32 s32Ret = 0;
	char pstLogUploadReq[2048];
	char contentstr[256];
    SKLogUploadReq stLogUploadReq;
	sint32 s32SendSize = 0;
	SK_HEADER header;

	if (NULL == pu8Log)
    {
        return ERR_NULL_PTR;
    }

	s32Ret = LogFileHandle(pu8Log,&stLogUploadReq,contentstr);
	if(SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR,"logFileHandle failed. [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}

	s32SendSize = sizeof(SKLogUploadReq) + stLogUploadReq.usContentLength;
    memcpy(pstLogUploadReq, &stLogUploadReq, sizeof(SKLogUploadReq));
    memcpy(pstLogUploadReq + sizeof(SKLogUploadReq), contentstr, stLogUploadReq.usContentLength);

    pProtocol->createHeader(&header,SK_DVR_TO_CMS_SERVER_UPLOAD_LOG_REQ,s32SendSize,SK_DEFAULT_CONTROL_SERVER_DST_ID);
	pLink->sendToServer(&header, pstLogUploadReq, s32SendSize);
	//print_level(SV_INFO, "s32UploadLevel:%d u32LogID:%d szTime:%s szContent:%s u16ContentLength:%d\n", \
	//	stLogUploadReq.ucLogLevel, stLogUploadReq.uiLogId, stLogUploadReq.szTime, contentstr, stLogUploadReq.usContentLength);

    return SV_SUCCESS;

}

void SV_NETWORK_CONTROLTHREAD::getFense(uint32 u32Verion)
{
	SK_HEADER header;
	char szBuf[12] = {0};
	uint32	u32FenceVer;
#if (defined(BOARD_ADA32C4) || defined(BOARD_DMS31V2))
	if(FENCE_GetVerison(&u32FenceVer) != SV_SUCCESS)
	{
		print_level(SV_ERROR,"NetworkGetFenceVer fail\n");
		return;
	}

	print_level(SV_DEBUG,"Server version:%d local version:%d\n", u32Verion, u32FenceVer);
	if(u32Verion != u32FenceVer)
	{
		//发送请求	
		print_level(SV_DEBUG,"Get new Rule!\n");
		pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_GET_ALARM_FENSE_REQ, 0,SK_DEFAULT_CONTROL_SERVER_DST_ID); 
		pLink->sendToServer(&header, szBuf, 0);
	}
#endif

	return;
}


