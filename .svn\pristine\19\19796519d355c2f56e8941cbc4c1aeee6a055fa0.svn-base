#!/bin/bash

# 检查并修改 CMA 值脚本
# 参数 1: 将 5M 替换为 110M
# 参数 2: 将 110M 替换为 5M

# 定义日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 检查参数
if [ -z "$1" ]; then
    log "错误：请提供参数（1 或 2）"
    log "参数 1: 将 5M 替换为 110M"
    log "参数 2: 将 110M 替换为 5M"
    exit 1
fi

# 验证参数有效性
if [ "$1" != "1" ] && [ "$1" != "2" ]; then
    log "错误：无效参数！请输入 1 或 2"
    exit 1
fi

# 挂载临时文件系统（如果需要操作 /var）
mount -t tmpfs -o size=10M tmpfs /var/ 2>/dev/null || true
mkdir -p /var/lock 2>/dev/null || true

# 读取当前 sys_bootargs 环境变量
log "正在读取当前 sys_bootargs..."
current_bootargs=$(fw_printenv -n sys_bootargs 2>/dev/null)

if [ -z "$current_bootargs" ]; then
    log "错误：无法读取 sys_bootargs 环境变量！"
    exit 1
fi

log "当前 sys_bootargs: $current_bootargs"

# 根据参数执行不同的替换逻辑
need_reboot=0  # 默认不需要重启

if [ "$1" = "1" ]; then
    # 参数 1: 将 5M 替换为 110M
    if [[ $current_bootargs == *rk_dma_heap_cma=5M* ]]; then
        log "检测到 rk_dma_heap_cma=5M，准备修改为 110M..."
        new_bootargs=$(echo "$current_bootargs" | sed 's/rk_dma_heap_cma=5M/rk_dma_heap_cma=110M/')
        need_reboot=1  # 需要重启
    else
        log "未检测到 rk_dma_heap_cma=5M，无需修改。"
        
        # 删除升级标志文件
        if [ -f "/root/upgrade_flag" ]; then
            log "删除升级标志文件: /root/upgrade_flag"
            rm -f /root/upgrade_flag
        fi
        
        exit 0
    fi
else
    # 参数 2: 将 110M 替换为 5M
    if [[ $current_bootargs == *rk_dma_heap_cma=110M* ]]; then
        log "检测到 rk_dma_heap_cma=110M，准备修改为 5M..."
        new_bootargs=$(echo "$current_bootargs" | sed 's/rk_dma_heap_cma=110M/rk_dma_heap_cma=5M/')
    else
        log "未检测到 rk_dma_heap_cma=110M，无需修改。"
        exit 0
    fi
fi

# 更新环境变量
log "更新 sys_bootargs: $new_bootargs"
if ! fw_setenv sys_bootargs "$new_bootargs"; then
    log "错误：无法更新 sys_bootargs！"
    exit 1
fi

# 如果是参数1且需要重启，则创建升级标志文件
if [ "$1" = "1" ] && [ "$need_reboot" -eq 1 ]; then
    log "创建升级标志文件: /root/upgrade_flag"
    touch /root/upgrade_flag
fi

log "CMA 值已成功修改，准备重启系统..."
sync  # 确保文件系统同步

# 强制重启
reboot -f