/******************************************************************************
Copyright (C) 2018-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_aenc.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2021-04-26

文件功能描述: 封装RK MPP音频编码模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "common.h"
#include "mpp_com.h"
#include "mpp_sys.h"
#include "mpp_aenc.h"
#include "media.h"
#include "rk_common.h"


/* 音频编码通道状态信息 */
typedef struct tagAencChn_S
{
    VENC_CHN    AeChnId;        /* 编码通道ID */
    SV_BOOL     bCreated;       /* 是否已创建 */
    SV_BOOL     bEnable;        /* 是否使能编码 */
    sint32      s32AencFd;      /* 通道设备文件句柄 */
} MPP_AENC_CHN_S;

/* 音频编码模块控制信息 */
typedef struct tagAencInfo_S
{
    uint32      u32ChnNum;      /* 音频源通道数目 */
    MPP_AENC_CHN_S astAencChn[2];   /* 音频编码通道 */
    sint32      s32MaxAencFd;   /* 音频编码通道设备文件句柄最大值 */
    MEDIA_DATA_CALLBACK pfDataCallback; /* 媒体流数据回调函数指针 */
    uint32      u32TID;         /* 音频编码线程ID */
    SV_BOOL     bRunning;       /* 线程是否正在运行 */
    SV_BOOL     bException;     /* 线程是否出现异常 */
    pthread_mutex_t mutexLock;  /* 通道操作线程互斥锁 */
} MPP_AENC_INFO_S;

MPP_AENC_INFO_S m_stAencInfo = {0};     /* 音频频编码控制信息 */

void * mpp_aenc_Body(MPP_AENC_INFO_S *pstAencInfo);
sint32 mpp_aenc_GetStream(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 初始化AENC模块
 * 输入参数: pstAencConf --- 音频编码配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Init(MPP_AENC_CONF_S *pstAencConf)
{
#if 0
    HI_S32 s32Ret = 0, i;
    sint32 s32VencFd = 0;
    AENC_CHN AeChn;
    AENC_ATTR_G711_S stG711Attr;
    AENC_ATTR_G726_S stG726Attr;
    AENC_ATTR_ADPCM_S stADPCMAttr;
    AENC_ATTR_LPCM_S stAencLpcm;
    AENC_CHN_ATTR_S stAencAttr;

    if (NULL == pstAencConf || NULL == pstAencConf->pfDataCallback)
    {
        return ERR_NULL_PTR;
    }

    if (pstAencConf->u32ChnNum > MPP_AENC_MAX_CHN_NUM || pstAencConf->enAencType >= AUDIO_ENCODE_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = pthread_mutex_init(&m_stAencInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    m_stAencInfo.u32ChnNum = pstAencConf->u32ChnNum;
    m_stAencInfo.pfDataCallback = pstAencConf->pfDataCallback;
    stAencAttr.u32BufSize = 30;
    stAencAttr.u32PtNumPerFrm = 320;
    switch (pstAencConf->enAencType)
    {
        case AUDIO_ENCODE_G711A:
            stAencAttr.enType = PT_G711A;
            stAencAttr.pValue = (HI_VOID *)&stG711Attr;
            stG711Attr.resv = 0;
            break;

        case AUDIO_ENCODE_G711U:
            stAencAttr.enType = PT_G711U;
            stAencAttr.pValue = (HI_VOID *)&stG711Attr;
            stG711Attr.resv = 0;
            break;

        case AUDIO_ENCODE_ADPCM:
            stAencAttr.enType = PT_ADPCMA;
            stAencAttr.pValue = (HI_VOID *)&stADPCMAttr;
            stADPCMAttr.enADPCMType = ADPCM_TYPE_DVI4;
            break;

        case AUDIO_ENCODE_LPCM:
            stAencAttr.enType = PT_LPCM;
            stAencAttr.pValue = (HI_VOID *)&stAencLpcm;
            break;

        default: 
            return ERR_ILLEGAL_PARAM;
    }

    for (i = 0; i < pstAencConf->u32ChnNum; i++)
    {
        AeChn = i;
        s32Ret = HI_MPI_AENC_CreateChn(AeChn, &stAencAttr);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_AENC_CreateChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        m_stAencInfo.astAencChn[i].AeChnId = AeChn;
        m_stAencInfo.astAencChn[i].bCreated = SV_TRUE;
        m_stAencInfo.astAencChn[i].bEnable = SV_FALSE;
        s32VencFd = HI_MPI_AENC_GetFd(AeChn);
        if (s32VencFd <= 0)
        {
            print_level(SV_ERROR, "HI_MPI_AENC_GetFd failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        m_stAencInfo.astAencChn[i].s32AencFd = s32VencFd;
        if (m_stAencInfo.s32MaxAencFd < s32VencFd)
        {
            m_stAencInfo.s32MaxAencFd = s32VencFd;
        }
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化AENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Fini()
{
#if 0
    HI_S32 s32Ret = 0, i;
    AENC_CHN AeChn;

    for (i = 0; i < m_stAencInfo.u32ChnNum; i++)
    {
        AeChn = i;
        s32Ret = mpp_sys_AiAencUnBind(i, i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_AiAencUnBind failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        
        s32Ret = HI_MPI_AENC_DestroyChn(AeChn);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_AENC_DestroyChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }
    pthread_mutex_destroy(&m_stAencInfo.mutexLock);
    memset(&m_stAencInfo, 0x0, sizeof(MPP_AENC_INFO_S));
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动AENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Start()
{
#if 0
    sint32 s32Ret = 0;
    uint32 u32TID = 0;

    m_stAencInfo.bRunning = SV_TRUE;
    m_stAencInfo.bException = SV_FALSE;
    s32Ret = pthread_create(&u32TID, NULL, mpp_aenc_Body, &m_stAencInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for AENC failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    m_stAencInfo.u32TID = u32TID;
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止AENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Stop()
{
#if 0
    sint32 s32Ret = 0;
    void * pvRetval = NULL;

    m_stAencInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(m_stAencInfo.u32TID, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for AENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: AENC模块线程体
 * 输入参数: pstVencInfo --- 视频编码控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_aenc_Body(MPP_AENC_INFO_S *pstAencInfo)
{
#if 0
    sint32 s32Ret = 0, i;
    SV_BOOL bSelect = SV_FALSE;
    fd_set read_fds, static_fds;
    struct timeval timeout = {2, 0};

    s32Ret = prctl(PR_SET_NAME, "mpp_aenc_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    
    FD_ZERO(&static_fds);
    for (i = 0; i < pstAencInfo->u32ChnNum; i++)
    {
        if (pstAencInfo->astAencChn[i].bCreated)
        {
            FD_SET(pstAencInfo->astAencChn[i].s32AencFd, &static_fds);
        }
    }

    while (pstAencInfo->bRunning)
    {
        bSelect = SV_FALSE;
        for (i = 0; i < pstAencInfo->u32ChnNum; i++)
        {
            if (pstAencInfo->astAencChn[i].bCreated && pstAencInfo->astAencChn[i].bEnable)
            {
                bSelect = SV_TRUE;
            }
        }

        if (!bSelect)
        {
            sleep_ms(500);
            continue;
        }
    
        read_fds = static_fds;
        timeout.tv_sec = 2;
        s32Ret = select(pstAencInfo->s32MaxAencFd + 1, &read_fds, NULL, NULL, &timeout);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "aenc select timeout or err. [ret=%#x]\n", s32Ret);
            sleep_ms(100);
            continue;
        }

        pthread_mutex_lock(&pstAencInfo->mutexLock);
        for (i = 0; i < pstAencInfo->u32ChnNum; i++)
        {
            if (pstAencInfo->astAencChn[i].bCreated && pstAencInfo->astAencChn[i].bEnable)
            {
                if (FD_ISSET(pstAencInfo->astAencChn[i].s32AencFd, &read_fds))
                {
                    s32Ret = mpp_aenc_GetStream(i);
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "aenc get Stream ch:%d failed! [ret=%#x]\n", i, s32Ret);
                        pstAencInfo->bException = SV_TRUE;
                    }
                }
            }
        }
        pthread_mutex_unlock(&pstAencInfo->mutexLock);
    }
#endif
    return NULL;
}

/******************************************************************************
 * 函数功能: 获取编码码流数据
 * 输入参数: s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_GetStream(sint32 s32Chn)
{
#if 0
    HI_S32 s32Ret = 0;
    AENC_CHN AeChn;
    AUDIO_STREAM_S stStream;
    STREAM_FRAME_S stFrame;

    AeChn = m_stAencInfo.astAencChn[s32Chn].AeChnId;
    s32Ret = HI_MPI_AENC_GetStream(AeChn, &stStream, HI_FALSE);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_AENC_GetStream failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (NULL != m_stAencInfo.pfDataCallback)
    {
        stFrame.u64PTS = stStream.u64TimeStamp;
        stFrame.u32Sep = stStream.u32Seq;
        stFrame.u32PackCount = 1;
        stFrame.astPacks[0].enPackType = PACK_TYPE_AUDIO;
        stFrame.astPacks[0].pu8Addr = stStream.pStream;
        stFrame.astPacks[0].u32Len = stStream.u32Len;

        mpp_sys_CallbackLock();
        
        s32Ret = m_stAencInfo.pfDataCallback(s32Chn, STREAM_TYPE_AUDIO_AENC, &stFrame, sizeof(stFrame));

        mpp_sys_CallbackUnlock();
    }

    s32Ret = HI_MPI_AENC_ReleaseStream(AeChn, &stStream);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_AENC_ReleaseStream failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 使能某音频编码通道进行编码
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_AENC_MAX_CHN_NUM)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_EnableChn(sint32 s32Chn)
{
#if 0
    sint32 s32Ret = 0;
    
    if (s32Chn > MPP_AENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    s32Ret = mpp_sys_AiAencBind(s32Chn, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_AiAencBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (!m_stAencInfo.astAencChn[s32Chn].bCreated)
    {
        return ERR_UNEXIST;
    }

    pthread_mutex_lock(&m_stAencInfo.mutexLock);
    m_stAencInfo.astAencChn[s32Chn].bEnable = SV_TRUE;
    pthread_mutex_unlock(&m_stAencInfo.mutexLock);
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 禁止某音频编码通道进行编码
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_AENC_MAX_CHN_NUM)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_DisableChn(sint32 s32Chn)
{
#if 0
    sint32 s32Ret = 0;
    
    if (s32Chn > MPP_AENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stAencInfo.astAencChn[s32Chn].bCreated)
    {
        return ERR_UNEXIST;
    }

    pthread_mutex_lock(&m_stAencInfo.mutexLock);
    m_stAencInfo.astAencChn[s32Chn].bEnable = SV_FALSE;
    pthread_mutex_unlock(&m_stAencInfo.mutexLock);

    s32Ret = mpp_sys_AiAencUnBind(s32Chn, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_AiAencBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置音频通道编码参数
 * 输入参数: s32Chn --- 编码通道号
             enEncType --- 编码类型
             enAudioSampleRate --- 编码采样率
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 
 *****************************************************************************/
sint32 mpp_aenc_SetEncParam(sint32 s32Chn, AUDIO_ENCODE_TYPE_E enEncType, AUD_SR_E enAudioSampleRate)
{
#if 0
    sint32 s32Ret = 0;
    sint32 s32VencFd;
    AENC_CHN AeChn;
    AENC_ATTR_G711_S stG711Attr;
    AENC_ATTR_G726_S stG726Attr;
    AENC_ATTR_ADPCM_S stADPCMAttr;
    AENC_ATTR_LPCM_S stAencLpcm;
    AENC_CHN_ATTR_S stAencAttr;

    if (s32Chn > MPP_AENC_MAX_CHN_NUM || enEncType >= AUDIO_ENCODE_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = mpp_aenc_DisableChn(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_DisableChn failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    AeChn = s32Chn;
    s32Ret = HI_MPI_AENC_DestroyChn(AeChn);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_AENC_DestroyChn failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    stAencAttr.u32BufSize = 30;
    stAencAttr.u32PtNumPerFrm = 320;
    switch (enEncType)
    {
        case AUDIO_ENCODE_G711A:
            stAencAttr.enType = PT_G711A;
            stAencAttr.pValue = (HI_VOID *)&stG711Attr;
            stG711Attr.resv = 0;
            break;

        case AUDIO_ENCODE_G711U:
            stAencAttr.enType = PT_G711U;
            stAencAttr.pValue = (HI_VOID *)&stG711Attr;
            stG711Attr.resv = 0;
            break;

        case AUDIO_ENCODE_ADPCM:
            stAencAttr.enType = PT_ADPCMA;
            stAencAttr.pValue = (HI_VOID *)&stADPCMAttr;
            stADPCMAttr.enADPCMType = ADPCM_TYPE_DVI4;
            break;

        case AUDIO_ENCODE_LPCM:
            stAencAttr.enType = PT_LPCM;
            stAencAttr.pValue = (HI_VOID *)&stAencLpcm;
            break;

        default: 
            return ERR_ILLEGAL_PARAM;
    }

    s32Ret = HI_MPI_AENC_CreateChn(AeChn, &stAencAttr);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_AENC_CreateChn failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stAencInfo.astAencChn[s32Chn].AeChnId = AeChn;
    m_stAencInfo.astAencChn[s32Chn].bCreated = SV_TRUE;
    m_stAencInfo.astAencChn[s32Chn].bEnable = SV_FALSE;
    s32VencFd = HI_MPI_AENC_GetFd(AeChn);
    if (s32VencFd <= 0)
    {
        print_level(SV_ERROR, "HI_MPI_AENC_GetFd failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stAencInfo.astAencChn[s32Chn].s32AencFd = s32VencFd;
    if (m_stAencInfo.s32MaxAencFd < s32VencFd)
    {
        m_stAencInfo.s32MaxAencFd = s32VencFd;
    }

    s32Ret = mpp_aenc_EnableChn(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_EnableChn failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif    
    return SV_SUCCESS;
}

