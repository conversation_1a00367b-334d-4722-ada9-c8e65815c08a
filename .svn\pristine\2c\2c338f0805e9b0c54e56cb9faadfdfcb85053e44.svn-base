#include "cms_processfile.h"

#include <time.h>
#include <dirent.h> 
#include "string.h"
#include <stdlib.h>
#include <string>
#include <list>
//#include "sv_network_interface.h"
#include "state_api_2.h"
#include "sv_common_time.h"
#include "sv_common.h"
#include "print.h"

SV_BOOL isVideoInTimeRage(MEDIA_FILE_INFO_ST stFileInfo, time_t startLocalTime, time_t endLocalTime)
{
	sint32 s32FileEndTime = stFileInfo.stFileTime + stFileInfo.s32Duration;

	if(s32FileEndTime < startLocalTime || stFileInfo.stFileTime > endLocalTime)
	{
		 return SV_FALSE;
	}
	else
	{
		return SV_TRUE;
	}
}


//2018-01-01 15:21:21 -> 20180101152121
sint32 UtcNetworkTimetoLocalTime(const char* szNetworkTime, char* szLocalTime,sint8 *pisdst)
{
	if(szNetworkTime == SV_NULL || szLocalTime == SV_NULL)
	{
		return SV_FAILURE;
	}

	time_t utcTime;
	struct tm tmUtcNetworkTime, tmLocalTime;
	

	sscanf((const char*)szNetworkTime, "%04d-%02d-%02d %02d:%02d:%02d", 
		&tmUtcNetworkTime.tm_year, &tmUtcNetworkTime.tm_mon, &tmUtcNetworkTime.tm_mday, 
		&tmUtcNetworkTime.tm_hour, &tmUtcNetworkTime.tm_min, &tmUtcNetworkTime.tm_sec);	
	tmUtcNetworkTime.tm_year -= 1900;
	tmUtcNetworkTime.tm_mon -= 1;

	COMMON_UTC2Local(&tmUtcNetworkTime, &tmLocalTime);
	strftime(szLocalTime, SK_DATE_TIME_STRING_MAX_LENGTH, "%Y%m%d%H%M%S", &tmLocalTime); 
	*pisdst = tmLocalTime.tm_isdst;
	print_level(SV_DEBUG,"szLocalTime:%s isdst %d\n", szLocalTime,*pisdst);


	return SV_SUCCESS;
}

//20180101152121 -> 2018-01-01 15:21:21
sint32 LocalTimetoUtcNetworkTime(const char* szLocalTime, char* szNetworkTime)
{
	if(szNetworkTime == SV_NULL || szLocalTime == SV_NULL)
	{
		return SV_FAILURE;
	}

	time_t utcTime;
	struct tm tmLocalTime, tmUtcNetworkTime;

	sscanf((const char*)szLocalTime, "%04d%02d%02d%02d%02d%02d", 
		&tmLocalTime.tm_year, &tmLocalTime.tm_mon, &tmLocalTime.tm_mday, 
		&tmLocalTime.tm_hour, &tmLocalTime.tm_min, &tmLocalTime.tm_sec);	
	tmLocalTime.tm_year -= 1900;
	tmLocalTime.tm_mon -= 1;
	localtime_r(&utcTime, &tmUtcNetworkTime);
	strftime(szNetworkTime, SK_DATE_TIME_STRING_MAX_LENGTH, "%Y-%m-%d %H:%M:%S", &tmUtcNetworkTime); 
	print_level(SV_DEBUG,"szNetworkTime:%s\n", szNetworkTime);
	print_level(SV_DEBUG,"szLocalTime:%s\n", szLocalTime);
	return SV_SUCCESS;
}

SV_BOOL isTimerangevalid(const char* szLocalStartTime, char* szLocalEndTime, sint8 s8starttimeisdst, sint8 s8endtimeisdst)
{

	if(szLocalStartTime == SV_NULL || szLocalEndTime == SV_NULL)
	{
		return SV_FALSE;
	}

	struct tm tmLocalStartTime, tmLocalEndTime;
	time_t timeStart=0, timeEnd=0, timeNow=0;
	sscanf((const char*)szLocalStartTime, "%04d%02d%02d%02d%02d%02d", 
		&tmLocalStartTime.tm_year, &tmLocalStartTime.tm_mon, &tmLocalStartTime.tm_mday, 
		&tmLocalStartTime.tm_hour, &tmLocalStartTime.tm_min, &tmLocalStartTime.tm_sec);
	sscanf((const char*)szLocalEndTime, "%04d%02d%02d%02d%02d%02d", 
		&tmLocalEndTime.tm_year, &tmLocalEndTime.tm_mon, &tmLocalEndTime.tm_mday, 
		&tmLocalEndTime.tm_hour, &tmLocalEndTime.tm_min, &tmLocalEndTime.tm_sec);

	tmLocalStartTime.tm_isdst =  s8starttimeisdst;
	tmLocalEndTime.tm_isdst =  s8endtimeisdst;
	
	tmLocalStartTime.tm_year -= 1900;
	tmLocalStartTime.tm_mon -= 1;
	tmLocalEndTime.tm_year -= 1900;
	tmLocalEndTime.tm_mon -= 1;	

	timeNow = time(NULL);
	timeStart = mktime(&tmLocalStartTime);
	timeEnd = mktime(&tmLocalEndTime);
	print_level(SV_DEBUG,"timeNow:%d timeStart:%d timeEnd:%d s8starttimeisdst:%d s8endtimeisdst:%d\n", timeNow, timeStart, timeEnd,tmLocalStartTime.tm_isdst,tmLocalEndTime.tm_isdst);

	if(timeStart > timeNow)
	{
		return SV_FALSE;
	}

	if(timeStart >= timeEnd)
	{
		return SV_FALSE;
	}

	if(timeEnd - timeStart > 600) //10min
	{
		return SV_FALSE;
	}

	return SV_TRUE;
}


sint32 UtcNetworkTimeToLocalTime(const char* szLocalTime, char* szNetworkTime)
{
	if(szNetworkTime == SV_NULL || szLocalTime == SV_NULL)
	{
		return SV_FAILURE;
	}

	time_t utcTime;
	struct tm tmLocalTime, tmUtcNetworkTime;

	sscanf((const char*)szLocalTime, "%04d-%02d-%02d %02d:%02d:%02d", 
		&tmLocalTime.tm_year, &tmLocalTime.tm_mon, &tmLocalTime.tm_mday, 
		&tmLocalTime.tm_hour, &tmLocalTime.tm_min, &tmLocalTime.tm_sec);	
	tmLocalTime.tm_year -= 1900;
	tmLocalTime.tm_mon -= 1;

	localtime_r(&utcTime, &tmUtcNetworkTime);
	strftime(szNetworkTime, SK_DATE_TIME_STRING_MAX_LENGTH, "%Y-%m-%d %H:%M:%S", &tmUtcNetworkTime); 
	print_level(SV_DEBUG,"szNetworkTime:%s\n", szNetworkTime);
	print_level(SV_DEBUG,"szLocalTime:%s\n", szLocalTime);
	return SV_SUCCESS;
}


























