#include <sys/types.h>
#include <unistd.h>
#include <sys/time.h>

#include "cms_fileTransfer.h"

SV_NETWORK_FILETRANSFER::SV_NETWORK_FILETRANSFER(SV_NETWORK_DVRINFO* pTempinfo,
 	SV_NETWORK_PROTOCOL* pTempProtocol, SV_NETWORK_STATE* pTempState)
 	:pDvrinfo(pTempinfo),pProtocol(pTempProtocol),pState(pTempState)
{
	bRegister = SV_FALSE;
	u64SessionId = 0;
	u64CmsSessionId = 0;
	u32Port = 9092;
	pLink = new SV_NETWORK_LINK();
}

SV_NETWORK_FILETRANSFER::~SV_NETWORK_FILETRANSFER()
{
	//delete pLink;
}

void SV_NETWORK_FILETRANSFER::setPort( uint32 u32Temp )
{
	u32Port = u32Temp;
}

sint32 SV_NETWORK_FILETRANSFER::setSendFile(char *pFileName, uint8 u8FileType, uint32 u32Offest, uint32 u32SessionId)
{
	SV_CHECK(pFileName);
	SV_COMMON_ScopedLock lock(listMutex);
	char szFullPath[STORAGE_FULLPATH_LEN] = {0}, szFullDir[STORAGE_FULLPATH_LEN];

	//要实现
	//SV_DVR_STORAGE_GetFileDirctory(pFileName, szFullDir);
	snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s%s", szFullDir, pFileName);
	sendFileList.push_back(stFileTransfer(pFileName, u8FileType, u32Offest, u32SessionId, pFileName));
	print_level(SV_DEBUG,"add fileName:%s to list, list size: %d\n",pFileName, sendFileList.size());
	return SV_SUCCESS;
}

sint32 SV_NETWORK_FILETRANSFER::deleteSendFile(uint32 u32SessionId)
{

	SV_COMMON_ScopedLock lock(listMutex);

	list<stFileTransfer>::iterator iter;
	for( iter = sendFileList.begin(); iter != sendFileList.end(); )
	{
		if(iter->u32FileSessionId == u32SessionId)
		{
			print_level(SV_DEBUG,"iter->u32FileSessionId:%d\n", iter->u32FileSessionId);
			if(iter->s32Fd > 0)
			{
				close(iter->s32Fd);
				iter->s32Fd = -1;
			}
			sendFileList.erase(iter++);
			break;
		}
		else
		{
			iter++;
		}
	}

	return SV_SUCCESS;
}

void SV_NETWORK_FILETRANSFER::setSessionId( uint64 u64Id)
{
	u64SessionId = u64Id;
	print_level(SV_DEBUG,"u64SessionId:%llu\n", u64SessionId);

}

uint64 SV_NETWORK_FILETRANSFER::getSessionId()
{
	return u64SessionId;
}

void SV_NETWORK_FILETRANSFER::setCmsSessionId( uint64 u64Id)
{
	u64CmsSessionId = u64Id;
	print_level(SV_DEBUG,"u64CmsSessionId:%llu\n", u64CmsSessionId);

}

void SV_NETWORK_FILETRANSFER::closeFd()
{
    print_level(SV_INFO, "close all fd\n");
    SV_COMMON_ScopedLock lock(listMutex);

	list<stFileTransfer>::iterator iter;
	for (iter = sendFileList.begin(); iter != sendFileList.end(); iter++)
	{
		if (0 != iter->u32FileSessionId)
		{
			print_level(SV_DEBUG,"delete file sessionId: %d\n", iter->u32FileSessionId);
			if(iter->s32Fd > 0)
			{
				close(iter->s32Fd);
				iter->s32Fd = -1;
			}
			sendFileList.erase(iter++);
		}
	}
}

void SV_NETWORK_FILETRANSFER::run()
{
	SK_HEADER header;
	char dataBuf[NETWORK_MAX_RECVSIZE];
	uint32 u32DataSize = 0;

	while(pState->getNetworkRunning())
	{

		sleep(1);
		pLink->closeSocket();
		pLink->setServerIp((const char *)pDvrinfo->getIpAddr().c_str());
		pLink->setDeviceName((const char *)pDvrinfo->getNetCardName().c_str());
		pLink->setPort(u32Port);
		//print_level(SV_DEBUG,"Filetransfer link ip:%s, device:%s, port:%d \n",
							//pDvrinfo->getIpAddr().c_str(), pDvrinfo->getNetCardName().c_str(), u32Port);

        //等待cms客户端发起下载文件请求，再把文件加入队列
		if ( sendFileList.empty() || !pState->getControlRegisterFlag())
		{
			print_level(SV_DEBUG,"List empty or control link disconnect! list: %d, flag: %d\n", sendFileList.empty(), pState->getControlRegisterFlag());
			pLink->closeSocket();
			sleep(1);
		}
		else
		{
			while( !pLink->isAvalible() )
			{
				if( pLink->initConnect() != SV_SUCCESS )
				{
					print_level(SV_DEBUG,"Filetransfer link connect error \n");
					for(int i=0; i<5; i++)
					{
						sleep(1);
						if( !pState->getNetworkRunning() )
						{
							return;
						}
					}
					continue;
				}
				else
				{
				    print_level(SV_DEBUG,"Filetransfer link connect success\n");
				}
			}

			//regist
			sendRegister();

			while (pState->getNetworkRunning())
			{
				//recv ����
				fd_set read_set;
				FD_ZERO(&read_set);
				if( pLink->isAvalible() )
				{
				 	print_level(SV_DEBUG,"Filetransfer success\n");
					FD_SET(pLink->getSocket(), &read_set);
				}
				else
				{
				    print_level(SV_DEBUG,"Filetransfer break\n");
					break;
				}

				struct timeval tv;
				tv.tv_sec = 1;
				tv.tv_usec = 0;  //1ms
				sint32 s32MaxFd = pLink->getSocket() + 1;
				sint32 s32Ret = select(s32MaxFd, &read_set, NULL, NULL, &tv);
				if(s32Ret < 0)
				{
				    print_level(SV_ERROR,"Filetransfer select error\n");
				}
				else if(s32Ret == 0)
				{

					if( sendFileList.empty() )
					{
						bRegister = SV_FALSE;
						break;
					}

					list<stFileTransfer>::iterator iter;
					for( iter = sendFileList.begin(); iter != sendFileList.end();)
					{
						if(iter->u8TimeOutCnt++ > 15) //15s ��ʱ
						{
						    print_level(SV_DEBUG,"Time out erase SessionId:%u\n", iter->u32FileSessionId);
							if(iter->s32Fd > 0)
							{
								close(iter->s32Fd);
								iter->s32Fd = -1;
							}
							sendFileList.erase(iter++);
						}
						else
						{
						    print_level(SV_DEBUG,"Time out cnt:%d SessionId:%u\n", iter->u8TimeOutCnt, iter->u32FileSessionId);
							iter++;
						}
					}

					//send data 1s ���һ��
					sendListData();
				}
				else
				{
					//recev data
					if( pLink->recvFromServer(&header, dataBuf, &u32DataSize) < 0 )
					{
					    print_level(SV_ERROR,"Filetransfer recev server error\n");
						break;
					}

					//dumpHeader(header);
					processFileTransferRsp(&header, dataBuf, &u32DataSize);

					//send data �յ������̻ظ�
					sendListData();
				}

			}

			//���ȫ��
			if(!pDvrinfo->getStorageStatus(NULL))
			{
				for(list<stFileTransfer>::iterator iter = sendFileList.begin(); iter != sendFileList.end(); iter++)
				{
					if(iter->s32Fd > 0)
					{
						close(iter->s32Fd);
						iter->s32Fd = -1;
					}
				}
				sendFileList.clear();
			}
		}
	}

}

void SV_NETWORK_FILETRANSFER::createRegisterReq(SKConnectFileTransferServerReq *pServerReq)
{

	pServerReq->ullGlobalSessionId = u64SessionId;
	print_level(SV_DEBUG,"u64SessionId:%llu\n", pServerReq->ullGlobalSessionId);

}

void SV_NETWORK_FILETRANSFER::sendRegister()
{
	SK_HEADER header;
	SKConnectFileTransferServerReq registerReq;
	pProtocol->createHeader(&header, SK_CLIENT_TO_CMS_SERVER_CONNECT_FILE_TRANSFER_SERVER_REQ, sizeof(SKConnectFileTransferServerReq), SK_DEFAULT_FILE_TRANSFER_SERVER_ID);
	createRegisterReq(&registerReq);
	pLink->sendToServer(&header, (char *)&registerReq, sizeof(SKConnectFileTransferServerReq));
}

void SV_NETWORK_FILETRANSFER::processRegisterRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{

	SKCommonRsp *pRsp = (SKCommonRsp *)pData;

	print_level(SV_DEBUG,"pRsp->ucResult:%d, szReason:%s\n", pRsp->ucResult,pRsp->szReason);

	if( pRsp->ucResult == 0 )
	{
		bRegister = SV_TRUE;
	}
	else
	{
		pLink->closeSocket();
	}
}

void SV_NETWORK_FILETRANSFER::processFileRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKDvrTransferFileRsp * pFileRsp = (SKDvrTransferFileRsp *)pData;

	print_level(SV_DEBUG,"seq:%d, sessionid:%d\n", pFileRsp->uiFileSeq, pFileRsp->uiSessionId);

//	struct timeval tv;
 //   gettimeofday(&tv,NULL);
//	printf("rec sec:%u usec:%u\n", tv.tv_sec, tv.tv_usec);

	list<stFileTransfer>::iterator iter;
	for( iter = sendFileList.begin(); iter != sendFileList.end(); iter++ )
	{
		if(iter->u32FileSessionId == pFileRsp->uiSessionId)
		{
			iter->u32RecFileSeq = pFileRsp->uiFileSeq + 1;
			break;
		}
	}

}

void SV_NETWORK_FILETRANSFER::processFileResultRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKDvrTransferFileResultRsp * pResult = (SKDvrTransferFileResultRsp *)pData;

	print_level(SV_DEBUG,"pResult->uiSessionId:%d\n", pResult->uiSessionId);

	deleteSendFile(pResult->uiSessionId);

}

void SV_NETWORK_FILETRANSFER::processStopTransfer(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKInformDvrStopTransferFileReq * pStop = (SKInformDvrStopTransferFileReq *)pData;
	print_level(SV_DEBUG,"sessid:%d, reason:%s\n", pStop->uiSessionId,pStop->szReason);
	deleteSendFile(pStop->uiSessionId);

	SK_HEADER header;
	SKInformDvrStopTransferFileRsp stInfo = {0};
	pProtocol->createHeader(&header, SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_INFORM_STOP_TRANSFER_FILE_RSP, sizeof(SKInformDvrStopTransferFileRsp), pHeader->ullSrcId);
	stInfo.uiSessionId = pStop->uiSessionId;
	strcpy((char*)stInfo.szReason, (char*)pStop->szReason);
	pLink->sendToServer(&header, (char *)&stInfo, sizeof(SKInformDvrStopTransferFileRsp));

}


sint32 SV_NETWORK_FILETRANSFER::processFileTransferRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{

	SV_CHECK(pData);

	print_level(SV_DEBUG,"get rsp code:0x%x\n", pHeader->usCode);
	switch(pHeader->usCode)
	{
		case SK_CLIENT_TO_CMS_SERVER_CONNECT_FILE_TRANSFER_SERVER_RSP: processRegisterRsp(pHeader, pData, pDataSize); break;
		case SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_TRANSFER_FILE_RSP: processFileRsp(pHeader, pData, pDataSize);break;
		case SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_TRANSFER_FILE_RESULT_RSP: processFileResultRsp(pHeader, pData, pDataSize);break;
		case SK_CMS_CLIENT_TO_CMS_SERVER_TO_DVR_INFORM_STOP_TRANSFER_FILE_REQ: processStopTransfer(pHeader, pData, pDataSize);break;
		default: break;
	}

	return SV_SUCCESS;
}

sint32 SV_NETWORK_FILETRANSFER::sendFile(stFileTransfer* pstFile)
{

	sint32 s32Fd = -1;
	sint32 s32ReadNum = 0;
	char buf[FILETRANSFER_BUFFNUM + 100] = {0}, szFullPath[STORAGE_FULLPATH_LEN];
	SKDvrTransferFileReq stFileReq = {0};

	if((pstFile->u8FileType == 0)||(pstFile->u8FileType == 4))
	{
		snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s", (char*)pstFile->szFileName);
	}
	else if(pstFile->u8FileType == 1)
	{
		snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s", (char*)pstFile->szFullPath);
	}
	else if(pstFile->u8FileType == 6)
	{
		snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s", (char*)pstFile->szFullPath);
	}

	if(pstFile->s32Fd < 0)
	{
		if( ( pstFile->s32Fd = open(szFullPath, O_RDWR ) ) < 0)
		{
			print_level(SV_ERROR,"open error file:%s\n", szFullPath);
			return SV_FAILURE;
		}
        print_level(SV_INFO, "open file:%s\n", szFullPath);
		SV_DVR_COMMON_CloseEvec(pstFile->s32Fd);
		lseek(pstFile->s32Fd, pstFile->u32FileOffest, SEEK_SET);
	}

	stFileReq.uiSessionId = pstFile->u32FileSessionId;
	stFileReq.uiFileOffset = pstFile->u32FileOffest;
	stFileReq.uiFileSeq = pstFile->u32FileSeq++;

	if(pstFile->s32ReadLen <= 0)
	{
		//s32ReadNum = read(pstFile->s32Fd, buf+sizeof(SKDvrTransferFileReq), FILETRANSFER_BUFFNUM);
		s32ReadNum = read(pstFile->s32Fd, pstFile->cBufSend, FILETRANSFER_BUFFNUM);
		if(s32ReadNum <= 0)
		{
			print_level(SV_ERROR,"s32ReadNum:%d %s\n", s32ReadNum, strerror(errno) );
			SK_HEADER header;
			SKDvrTransferFileResultReq stResultReq = {0};
			stResultReq.uiSessionId = pstFile->u32FileSessionId;
			stResultReq.common_rsp.ucResult = 1;
			strcpy((char*)stResultReq.common_rsp.szReason, "Read error!");
			pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_TRANSFER_FILE_RESULT_REQ, sizeof(SKDvrTransferFileResultReq), u64CmsSessionId);
			pLink->sendToServer(&header, (char *)&stResultReq, sizeof(SKDvrTransferFileResultReq));
			return SV_FAILURE;
		}
		else
		{
			pstFile->s32ReadLen = s32ReadNum;
		}
	}

	pstFile->u32FileOffest_last = pstFile->u32FileOffest;
	pstFile->u32FileOffest += pstFile->s32ReadLen;
	stFileReq.uiDataSize = pstFile->s32ReadLen;
	memcpy(buf, (char *)&stFileReq, sizeof(SKDvrTransferFileReq));
	memcpy(buf+sizeof(SKDvrTransferFileReq), pstFile->cBufSend, pstFile->s32ReadLen);

	print_level(SV_DEBUG,"sessid:%d, offeset:%d, seq:%d, datasize:%d  cmssessionId;%d \n", stFileReq.uiSessionId, stFileReq.uiFileOffset, stFileReq.uiFileSeq, stFileReq.uiDataSize,u64CmsSessionId);

	SK_HEADER header;
	pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_TRANSFER_FILE_REQ, sizeof(SKDvrTransferFileReq)+pstFile->s32ReadLen, u64CmsSessionId);
	pLink->sendToServer(&header, buf, sizeof(SKDvrTransferFileReq)+stFileReq.uiDataSize);

	if(stFileReq.uiDataSize < FILETRANSFER_BUFFNUM)
	{
		//�������
		SK_HEADER header;
		SKDvrTransferFileResultReq stResultReq = {0};
		stResultReq.uiSessionId = pstFile->u32FileSessionId;
		stResultReq.common_rsp.ucResult = 0;
		strcpy((char*)stResultReq.common_rsp.szReason, "Finished");
		pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TO_CMS_CLIENT_TRANSFER_FILE_RESULT_REQ, sizeof(SKDvrTransferFileResultReq), u64CmsSessionId);
		pLink->sendToServer(&header, (char *)&stResultReq, sizeof(SKDvrTransferFileResultReq));
		print_level(SV_WARN,"Send finished!\n");
		/*
        print_level(SV_INFO, "szFullPath='%s', strcmp result=%d\n", szFullPath, strcmp(szFullPath, "/tmp/FaceId.tar.gz"));
        print_level(SV_INFO, "szFullPath='%s', length=%zu\n", szFullPath, strlen(szFullPath));
        print_level(SV_INFO, "/tmp/FaceId.tar.gz length=%zu\n", strlen("/tmp/FaceId.tar.gz"));
        */
		if (pstFile->u8FileType == 0 && (0 == strcmp(szFullPath, "/var/log_tf.tar.gz") || 0 == strcmp(szFullPath, "/var/log_var.tar.gz")))//log
		{
			unlink((const char*)szFullPath);  //删除文件
		}
		else if (pstFile->u8FileType == 6 && 0 == strcmp(szFullPath, "/tmp/FaceId.tar.gz"))//faceId
		{
			unlink((const char*)szFullPath);  //删除文件
		}

		return SV_FAILURE;
	}

	pstFile->s32ReadLen = read(pstFile->s32Fd, pstFile->cBufSend, FILETRANSFER_BUFFNUM);
	return SV_SUCCESS;
}


sint32 SV_NETWORK_FILETRANSFER::getFileRsp(char *pFileName, uint8 u8FileType, cJSON *json)
{
	if((u8FileType == 0)||(u8FileType == 4)) //log
	{
		char szFileName[COMMON_NAME_LEN], szFileuploadName[COMMON_NAME_LEN];
		char szCmd[COMMON_CMD_LEN] = {0}, szCheckSum[64] = {0};
		uint32 u32FileSize = 0;

		strncpy(szFileName, pFileName, COMMON_NAME_LEN);
		sprintf(szFileName + strlen(szFileName) - 3, "txt");
		strncpy(szFileuploadName, pFileName, COMMON_NAME_LEN);
		sprintf(szFileuploadName + strlen(szFileuploadName) - 3, "txt_upload");

		if( !SV_DVR_COMMON_IsFileExist(szFileName) && !SV_DVR_COMMON_IsFileExist(szFileuploadName) )
		{
			cJSON_AddFalseToObject(json, "Result");
			cJSON_AddStringToObject(json, "Reason", "File not exit!");
			return SV_FAILURE;
		}
		else
		{
			cJSON_AddTrueToObject(json, "Result");
			cJSON_AddStringToObject(json, "Reason", "Success!");
		}

		if(SV_DVR_COMMON_IsFileExist(szFileName))
		{
			snprintf(szCmd, COMMON_CMD_LEN, "zip %s %s", pFileName, szFileName);
		}
		else
		{
			snprintf(szCmd, COMMON_CMD_LEN, "zip %s %s", pFileName, szFileuploadName);
		}

		print_level(SV_DEBUG,"szCmd:%s\n", szCmd);
		SV_COMMON_system(szCmd);
		u32FileSize = SV_COMMON_getFileSize(pFileName);
		cJSON_AddStringToObject(json, "FileName", pFileName);
		cJSON_AddNumberToObject(json, "FileType", u8FileType);
		cJSON_AddNumberToObject(json, "FileSize", u32FileSize);
	}
	else if(u8FileType == 1)
	{
		char szDir[STORAGE_FULLPATH_LEN] = {0}, szFullPath[STORAGE_FULLPATH_LEN] = {0};
		uint32 u32FileSize = 0;


		//SV_DVR_STORAGE_GetFileDirctory(pFileName, szDir);
		snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s%s", szDir, pFileName);
		print_level(SV_DEBUG,"szFullPath:%s\n", szFullPath);

		if( !SV_DVR_COMMON_IsFileExist(szFullPath) )
		{
			cJSON_AddFalseToObject(json, "Result");
			cJSON_AddStringToObject(json, "Reason", "File not exit!");
			return SV_FAILURE;
		}
		else
		{
			cJSON_AddTrueToObject(json, "Result");
			cJSON_AddStringToObject(json, "Reason", "Success!");
		}

		//getFileMd5(szFullPath, (char*)szCheckSum);
		u32FileSize = SV_COMMON_getFileSize(szFullPath);

		cJSON_AddStringToObject(json, "FileName", pFileName);
		cJSON_AddNumberToObject(json, "FileType", u8FileType);
		cJSON_AddNumberToObject(json, "FileSize", u32FileSize);
		//cJSON_AddStringToObject(json, "CheckSum", szCheckSum);
	}

	return SV_SUCCESS;
}


sint32 SV_NETWORK_FILETRANSFER::sendListData()
{
	if(!bRegister)
	{
		return SV_FAILURE;
	}

	list<stFileTransfer>::iterator iter;
	for( iter = sendFileList.begin(); iter != sendFileList.end(); )
	{
		//print_level(SV_DEBUG,"sendReq:%d, recReq:%d SessionId:%u\n", iter->u32FileSeq, iter->u32RecFileSeq, iter->u32FileSessionId);

		if(iter->u32FileSeq != iter->u32RecFileSeq)
		{
			iter++;
			continue;
		}

		if( sendFile(&(*iter)) == SV_SUCCESS )
		{
			iter->u8TimeOutCnt = 0;
			iter++;
		}
		else
		{
			if(iter->s32Fd > 0)
			{
				close(iter->s32Fd);
				iter->s32Fd = -1;
			}
			sendFileList.erase(iter++);
		}

	}
	return SV_SUCCESS;

}

SV_BOOL	SV_NETWORK_FILETRANSFER::isUploading()
{
	return (SV_BOOL)!sendFileList.empty();
}






























