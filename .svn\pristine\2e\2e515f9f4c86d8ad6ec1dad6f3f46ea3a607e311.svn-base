#ifndef _REC_PROTOCOL_H_
#define _REC_PROTOCOL_H_

#include <unistd.h>
#include <stdio.h>


#define AMF_END_OF_OBJECT       AMF_DATA_TYPE_OBJECT_END
#define AVFMT_GLOBALHEADER  	0x0040
#define AVFMT_VARIABLE_FPS  	0x0400 
#define AVFMTCTX_NOHEADER      	0x0001
#define AV_DATA_LEN   			256

typedef enum {
    AMF_DATA_TYPE_NUMBER = 0x00,
    AMF_DATA_TYPE_BOOL = 0x01,
    AMF_DATA_TYPE_STRING = 0x02,
    AMF_DATA_TYPE_OBJECT = 0x03,
    AMF_DATA_TYPE_NULL = 0x05,
    AMF_DATA_TYPE_UNDEFINED = 0x06,
    AMF_DATA_TYPE_REFERENCE = 0x07,
    AMF_DATA_TYPE_MIXEDARRAY = 0x08,
    AMF_DATA_TYPE_OBJECT_END = 0x09,
    AMF_DATA_TYPE_ARRAY = 0x0a,
    AMF_DATA_TYPE_DATE = 0x0b,
    AMF_DATA_TYPE_LONG_STRING = 0x0c,
    AMF_DATA_TYPE_UNSUPPORTED = 0x0d,
} AMFDataType;

/* 录像其他模块信息 */
typedef enum tagRecModType
{
    REC_MOD_NONE = 0x00,
    REC_MOD_GPS,
    REC_MOD_GSENSOR,

    REC_MOD_BUTT
} REC_MOD_TYPE;

typedef struct tag_gpsData_t {
    int             mode;
    int             satellites_used;
    int             satellites_visible;
    double          lat;        //latitude
    double          lon;        //lontitude
    double          alt;        //altitude
    double          speed;      //speed over ground
    double          course;     //course over ground
    double          time;
    double          epx;
    double          epy;
    double          epv;
    double          pdop;
    double          hdop;
    double          vdop;
} __attribute__ ((packed)) gpsData_t;

typedef struct tag_gSensorData_t {
    float           x;
    float           y;
    float           z;
	float           gy_x;
    float           gy_y;
    float           gy_z;
} __attribute__ ((packed)) gSensorData_t;

typedef struct tag_otherSensorsData_t {
	double      	speed;
	double      	temperature;
}__attribute__ ((packed)) otherSensorsData_t;


unsigned char  *putbyte(unsigned char *s, unsigned int b);
unsigned char  *putle32(unsigned char *s, unsigned int val);
unsigned char  *putbe32(unsigned char *s, unsigned int val);
unsigned char  *putle16(unsigned char *s, unsigned int val);
unsigned char  *putbe16(unsigned char *s, unsigned int val);
unsigned char  *putle24(unsigned char *s, unsigned int val);
unsigned char  *putbe24(unsigned char *s, unsigned int val);
unsigned char  *puttag(unsigned char *s, const char *tag);
unsigned char  *putamf_bool(unsigned char *ptr, const int bVal);
unsigned char  *putamf_string(unsigned char *ptr, const char *str);
unsigned char  *putamf_string_content(unsigned char *ptr, const char *str);
unsigned char  *putamf_double(unsigned char *ptr, double d);

#endif/* _REC_PROTOCOL_H */
