#ifndef _SV_NET_WORK_STATE_H_
#define _SV_NET_WORK_STATE_H_

#include <atomic>


#include "sv_common.h"
#include "cms_common.h"
#include "cmsServer.h"
#include "pthread.h"
//#include "protocol_new.h"

typedef enum svNETWORK_STREAM_TYPE_E
{
	NETWORK_STREAM_NORMAL_PIC,
	NETWORK_STREAM_ALARM_PIC,
	NETWORK_STREAM_NORMAL_VIDEO,
	NETWORK_STREAM_ALARM_VIDEO,
	NETWORK_STREAM_BUTT
}SV_NETWORK_STREAM_TYPE_E;

/* CMS下发的文件类型 */
typedef enum tag_ProcessFileType_E
{
    PRO_FILE_TYPE_UPGRADE = 0,          /* 升级包  */
    PRO_FILE_TYPE_CONFIG,               /* 配置包 */
    PRO_FILE_TYPE_FACEID,               /* 人脸ID包 */
    PRO_FILE_TYPE_FACEID_DRIVER,        /* CMS下发人脸ID包 */

    PRO_FILE_TYPE_BUTT
}PROCESS_FILE_TYPE_E;


#define UPDATE_INQUIRE_TIME   		90
#define UPDATE_INQUIRE_IDLE   		0
#define UPDATE_INQUIRE_OK     		1
#define UPDATE_INQUIRE_REJECT 		2
#define UPDATE_INQUIRE_TIMEOUT      3

#define SK_ALARM_ID_LENGTH          32

class SV_NETWORK_STATE
{

public:
	virtual ~SV_NETWORK_STATE();
	static SV_COMMON_Mutex stateMutex;
	static SV_NETWORK_STATE *getInstance();
	static SV_NETWORK_STATE *pInstance;

    void dumpCmsServerInfo(CMS_SERVER_STAT_S *pstCmsServerStat);

	SV_BOOL isDiskAble();

    SV_BOOL getPwrOffFlag();
    void setPwrOffFlag(const SV_BOOL *pbPowerFlag);

    SV_BOOL getStorageErrCode();
    void setStorageErrCode(const uint32 *pu32ErrCode);

	SV_BOOL getControlRegisterFlag();
	void setControlRegisterFlag(SV_BOOL bTempFlag);

	SV_BOOL getMediaRegisterFlag();
	void setMediaRegisterFlag(SV_BOOL bTempFlag);

	void clearRegisterFlag();

	SV_NETWORK_STREAM_TYPE_E getStreamMode();
	void setStreamMode(SV_NETWORK_STREAM_TYPE_E mode);

	void setSendLogId(uint32 u32Id);
	void setRcvLogId(uint32 u32Id);
	SV_BOOL isSendLogSuccess();

	SV_BOOL getSendLogstats();
	SV_BOOL setSendLogStatus(SV_BOOL bStatus);

	void setSendInfoId(uint64 u64Id);
	void setRcvInfoId(uint64 u64Id);
	SV_BOOL isSendInfoSuccess();

	void setSendAlarmId(const char *pAlarmId);
	void setRcvAlarmId(const char *pAlarmId);
	SV_BOOL isSendAlarmSuccess();

    void setSendCarStateId(uint64 u64CarStateId);
	void setRcvCarStateId(uint64 u64CarStateId);
	SV_BOOL isSendCarStateSuccess();

	SV_BOOL getRemoteUpdateFlag();
	void setRemoteUpdateFlag(SV_BOOL bTempFlag);

	void reflashUpdateTime();
	time_t getUpdateTime();

	void showState();

	void setRegisterReason( const char *pStr);
	const char *getRegisterReason();

	sint32 getDriveIdVersion();
	void setDriveIdVersion(sint32 s32Id);

	void setUploadOfflineStatus(SV_BOOL bStatus);
	SV_BOOL getUploadOfflineStatus();

	void setUploadVideoFile(SV_BOOL bStatus);
	SV_BOOL getUploadVideoFile();

	bool getNetworkRunning();
	void setNetworkRunning(SV_BOOL bRunning);

	SV_BOOL reflashSendCandataTime();
	SV_BOOL isNeedtoSendCanData();

	void reflashCommunicationTime();
	time_t getCommunicationTime();

	void setSendCheckListId(sint32 s32Id);
	void setRcvCheckListId(sint32 s32Id);
	SV_BOOL isSendCheckListuccess();

	void setAlarmType(const uint32 u32Type);
	uint32 getAlarmType();

	uint8 getUpdateInquireStatus();
	void setUpdateInquireStatus(uint8 u8Status);

	void setSendSensorId(uint32 u32Id);
	void setRcvSensorId(uint32 u32Id);
	SV_BOOL isSendSensorSuccess();

	bool getPrivateFileUpdate();
	void setPrivateFileUpdate(bool bUpdate);

    bool getConfigUpdate();
	void setConfigUpdate(bool bUpdate);
	
	bool getFaceInfoUpdate();
	void setFaceInfoUpdate(bool bUpdate);

private:
	SV_NETWORK_STATE();
	SV_NETWORK_STATE(const SV_NETWORK_STATE &);
	SV_NETWORK_STATE& operator = (const SV_NETWORK_STATE &);

	SV_BOOL *bPwrOffFlag;  	//�������ӱ�־
	uint32  *u32StorageErrCode;
	SV_BOOL bControlRegisterFlag;  	//�������ӱ�־
	SV_BOOL bMediaRegisterFlag;     //ý�����ӱ�־
	uint32 u32SendLogId, u32RcvLogId;    //���ͽ�����־��ʾ
	uint64 u32SendInfoId, u32RcvInfoId;  //���ͽ���������Ϣ��־
	sint32 s32DriveIdVersion;
	SV_BOOL bRemoteUpdateFlag;           //Զ��������־
	time_t timeGetUpdateData;            //��ȡԶ����������ʱ��
	SV_NETWORK_STREAM_TYPE_E eStreamMode;
	uint32 u32SendCheckListId, u32RcvCheckListId;  //���ͽ���CheckListID
	uint32 u32SendSenorId, u32RcvSensorId;  //���ͽ���ifbox sensordata

	pthread_mutex_t ControlRegisterFlag_mutex;
	pthread_mutex_t bMediaRegisterFlag_mutex;

	SV_BOOL bIsSendLog;                  //�Ƿ���log��־
	SV_BOOL bIsUpLoadOffLine;            //�Ƿ��ϴ�������Ϣ��־
	SV_BOOL bIsUpLoadVideoFile;          //�Ƿ��ϴ�������Ƶ��־

	char szRegisterReason[128];
	char szSendAlarmId[SK_ALARM_ID_LENGTH],szRecAlarmId[SK_ALARM_ID_LENGTH];     //����ID
	uint32  u32AlarmType;  //�ϴ��ı�������

    uint64 u64SendCarStateId, u64RecCarStateId;

	std::atomic<bool> bNetworkRunning;  //��������״̬��ʾ��true ���С�false ������
	SV_COMMON_Mutex mutexRunning;

	SV_COMMON_Mutex mutexCandata;
	time_t timeSendCanDataTime;			//����Candata��ʱ��

	SV_COMMON_Mutex mutexComnunication;
	time_t timeLastCommunication;		//����������ͨ��ʱ��

	SV_COMMON_Mutex mutexCheckList;

	uint8 u8UpdateInquireStatus;

	std::atomic<bool> bIsPrivateFileUpdate;
	std::atomic<bool> bIsConfigUpdate;
	std::atomic<bool> bIsFaceInfoUpdate;
	SV_COMMON_Mutex mutexFileUpdate;

    CMS_SERVER_STAT_S stCmsServerStat;
};














#endif
