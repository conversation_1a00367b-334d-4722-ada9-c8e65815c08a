/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：mpp_com.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2014-09-17

文件功能描述: 封装海思MPP媒体公共数据结构定义

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明
  1. :
  2. :
  3. :
  4. :
  5. :

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _MPP_COM_H
#define _MPP_COM_H

#include "common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define MPP_TP2824_FILE     "/dev/tp2824dev"
#define MPP_PR1000_FILE     "/dev/pr1000_dev"
#define MPP_NVP6134_FILE    "/dev/nvp6134_ex"
#define MPP_MIPI_FILE       "/dev/hi_mipi"
#define MPP_FRAMEBUFFER_FD  "/dev/dri/card0"

/* 板级平台宏定义 */
#define BOARD_IPCR20S1          0       /* IPC:200W像素,16CV300方案 */
#define BOARD_IPCR10S1          1       /* IPC:100W像素,18EV200方案 */
#define BOARD_WFCR10S1          2       /* WiFi摄像头:100W像素,18EV200方案 */
#define BOARD_WFCR10S2          3       /* WiFi摄像头:100W像素,16EV200方案 */
#define BOARD_WFTR20S1          4       /* WiFi发射盒:200W像素,16CV300方案 */
#define BOARD_WFTR20S2          5       /* WiFi发射盒:200W像素,16EV300方案 */
#define BOARD_IPCR20S2          6       /* IPC:200W像素支持WDR功能,16EV300方案 */
#define BOARD_WFCR20S1          7       /* WiFi摄像头:200W像素支持WDR功能,16EV300方案 */
#define BOARD_WFCR10S1LOS       8       /* WiFi摄像头:100W像素,18EV200方案 LiteOS系统 */
#define BOARD_WFCR20S1LOS       9       /* WiFi摄像头:200W像素,16EV300方案 LiteOS系统 */
#define BOARD_IPCR20S3          10      /* IPC:200W像素,SSC335方案 */
#define BOARD_WFCR20S2          11      /* WFC:200W像素,SSC335DE方案 */
#define BOARD_IPCR20S4          12      /* IPC:200W像素,SSC335DE方案 */
#define BOARD_ADA42V1           13      /* AI Box 4CH,RV1126方案 */
#define BOARD_WFTR20S3          14      /* WiFi发射盒:200W像素,SSC335DE方案 */
#define BOARD_DMS31V2           15      /* DMS31: RV1126方案 */
#define BOARD_ADA32V2           16      /* ADA32: RV1126方案 */
#define BOARD_ADA32IR           17      /* ADA32: RV1126方案支持TC933红外摄像头双目拼接 */
#define BOARD_ADA42PTZV1        18      /* AI Box + PTZ,RV1126方案 */
#define BOARD_ADA47V1           19      /* ADA47: 变焦项目: RV1126+IMX415 / RV1126+GC2093 */
#define BOARD_ADA900V1          20      /* ADA900: RV1126 方案 */
#define BOARD_ADA32V3           21      /* ADA32: RV1106 方案 */
#define BOARD_HDW845V1          22      /* HDW845 RV1126 方案,变焦摄像头 */
#define BOARD_ADA32N1           23      /* ADA32N: RV1126+OS05A10, 只带网口输出 */
#define BOARD_IPTR20S1          24      /* IPC转换盒: SSC335DE方案 */
#define BOARD_ADA32C4			25      /* ADA32 4G */
#define BOARD_ADA32E1           26      /* ADA32E: RV1126+OS05A10, 车载以太网 */
#define BOARD_IPCR20S5          27      /* IPC: SSC335DE方案,   双系统快速启动 */
#define BOARD_ADA46V1           28      /* ADA46: RV1126方案，R151摄像头 */
#define BOARD_ADA32V4           29      /* 联咏NT98539方案，双目-ADAS摄像头 */
#define BOARD_DMS51V1           30      /* DMS: RV1106方案 */


/* 硬件平台宏定义 */
#define PLATFORM_HI3516C        0       /* Hi3516CV300平台 */
#define PLATFORM_HI3518E        1       /* Hi3518EV200平台 */
#define PLATFORM_HI3516E        2       /* Hi3516EV300平台 */
#define PLATFORM_SSC335         3       /* SSC335平台 */
#define PLATFORM_RV1126         4       /* RV1126平台 */
#define PLATFORM_RV1106         5       /* RV1106平台 */
#define PLATFORM_NT98539        6       /* NT98539平台 */
#define PLATFORM_RV1126B        7       /* RV1126B平台 */


#define MOUDLETYPE_TC933        0       /* ADA32IR的产品类型 */
#define MOUDLETYPE_TC639_T2     1       /* ADA32IR的产品类型 */
#define MOUDLETYPE_TC639_T3     2       /* ADA32IR的产品类型 */
#define MOUDLETYPE_TC639_T6     3       /* ADA32IR的产品类型 */

/* 视频输入模块最值定义 */
#if ((BOARD == BOARD_ADA32IR))
#define VIM_MAX_DEV_NUM         2       /* 最大视频输入设备数目 */
#else
#define VIM_MAX_DEV_NUM         1       /* 最大视频输入设备数目 */
#endif
#define VIM_MAX_CHN_NUM_PER_DEV 1       /* 每个输入设备最大支持的通道数目 */
#define VIM_MAX_CHN_NUM         4       /* 最大支持输入通道数目 */


/* 视频处理模块最值定义 */
#define VPS_MAX_GRP_NUM         32      /* 最大通道组数目 */
#define MPP_VPSS_MAX_CHN_NUM    6       /* 最大通道数据 */

/* 视频编码模块最值定义 */
#define MPP_VENC_MAX_CHN_NUM    64      /* 最大编码通道数目 */
#define MPP_VENC_MAX_GRP_NUM    64      /* 最大编码通道组数目 */

/* 视频解码模块最值定义 */
#define MPP_VDEC_MAX_CHN_NUM    64

/* 视频输出模块最值定义 */
#define MPP_VO_MAX_CHN_NUM      2

/* 音频模块最值定义 */
#define MPP_AIO_MAX_CHN_NUM      16
#define MPP_AENC_MAX_CHN_NUM     32
#define MPP_ADEC_MAX_CHN_NUM     32

/* 机器输入规格定义 */
typedef enum tagDevViSpec_E
{
    DEV_VI_SPEC_4_D1 = 0,
    DEV_VI_SPEC_8_D1,
    DEV_VI_SPEC_16_D1,

    DEV_VI_SPEC_4_720P,
    DEV_VI_SPEC_8_720P,
    DEV_VI_SPEC_2_1080P,
    DEV_VI_SPEC_4_1080P,

    DEV_VI_SPEC_BUTT,
} DEV_VI_SPEC_E;

/* 像素格式类型 */
typedef enum tagPixelFormat_E
{
    PIXEL_FMT_RGB_1BPP = 0,
    PIXEL_FMT_RGB_2BPP,
    PIXEL_FMT_RGB_4BPP,
    PIXEL_FMT_RGB_8BPP,
    PIXEL_FMT_RGB_444,
    PIXEL_FMT_RGB_4444,
    PIXEL_FMT_RGB_555,
    PIXEL_FMT_RGB_565,
    PIXEL_FMT_RGB_1555,

    /*  9 reserved */
    PIXEL_FMT_RGB_888,
    PIXEL_FMT_RGB_8888,
    PIXEL_FMT_RGB_PLANAR_888,
    PIXEL_FMT_RGB_BAYER,

    PIXEL_FMT_YUV_A422,
    PIXEL_FMT_YUV_A444,

    PIXEL_FMT_YUV_PLANAR_422,
    PIXEL_FMT_YUV_PLANAR_420,
    PIXEL_FMT_YUV_PLANAR_444,

    PIXEL_FMT_YUV_SEMIPLANAR_422,
    PIXEL_FMT_YUV_SEMIPLANAR_420,
    PIXEL_FMT_YUV_SEMIPLANAR_444,

    PIXEL_FMT_UYVY_PACKAGE_422,
    PIXEL_FMT_YUYV_PACKAGE_422,
    PIXEL_FMT_VYUY_PACKAGE_422,
    PIXEL_FMT_YCbCr_PLANAR,

    PIXEL_FMT_BUTT
} PIXEL_FMT_E;

/* 物理通道索引 */
typedef struct tagMppPhyChn_S
{
    sint32 DevId;       /* 设备ID号 */
    sint32 ChnId;       /* 通道ID号 */    
} MPP_PHY_CHN;

/* 通道类型定义 */
typedef enum tagMppChnType_E
{
    MPP_CHN_INVALID = 0,/* 无效类型 */
    MPP_CHN_VI,         /* 视频输入通道 */
    MPP_CHN_VO,         /* 视频输出通道 */
    MPP_CHN_VENC,       /* 视频编码通道 */
    MPP_CHN_VDEC,       /* 视频解码通道 */
    MPP_CHN_GROUP,      /* 视频编码通道组 */
    MPP_CHN_AI,         /* 音频输入通道 */
    MPP_CHN_AO,         /* 音频输出通道 */
    MPP_CHN_AENC,       /* 音频编码通道 */
    MPP_CHN_ADEC,       /* 音频解码通道 */

    MPP_CHN_BUTT
} MPP_CHN_TYPE_E;

#if (PLATFORM == PLATFORM_NT98539)

    #define VI_CHN1_WIDTH   960
    #define VI_CHN1_HEIGHT  540

    typedef enum tagViChnUse_S
    {
        MPP_VI_CHN_0 = 0,   /* VI通道0 */
        MPP_VI_CHN_1,       /* VI通道1 */
        MPP_VI_CHN_2,       /* VI通道2 */
        MPP_VI_CHN_3,       /* VI通道3 */

        MPP_VI_CHN_BUTT
    } VI_CHN_USE;

    typedef enum tagMppVpssChn_E
    {
        MPP_VPSS_CHN_PRI = 0, /* VENC 主码流 */
        MPP_VPSS_CHN_SEC,     /* VENC 子码流 */
        MPP_VPSS_CHN_JPEG,    /* VENC jpeg流 */
        MPP_VPSS_CHN_VO,      /* VO 出图通道 */
        MPP_VPSS_CHN_ALG,     /* ALG 算法通道 */

        MPP_VPSS_CHN_BUTT,

        MPP_VPSS_CHN_EXT      /* 保证一致性 */

    } MPP_VPSS_CHN_TYPE_E;

#endif

#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)

#define VI_CHN1_WIDTH   960
#define VI_CHN1_HEIGHT  540

/* VI通道定义 */
#if (PLATFORM == PLATFORM_RV1126)
typedef enum tagViChnUse_S
{
    MPP_VI_CHN_0 = 0,   /* VI通道0 */
    MPP_VI_CHN_1,       /* VI通道1 */
    MPP_VI_CHN_2,       /* VI通道2 */
    MPP_VI_CHN_3,       /* VI通道3 */

    MPP_VI_CHN_BUTT
} VI_CHN_USE;
#elif (PLATFORM == PLATFORM_RV1106)
typedef enum tagViChnUse_S
{
    MPP_VI_CHN_0 = 0,   /* VI通道0，给主码流用 */
    MPP_VI_CHN_1,       /* VI通道1，给子码流用 */
    MPP_VI_CHN_2,       /* VI通道2，连接VPSS之后给算法和VO用 */

    MPP_VI_CHN_BUTT,
    MPP_VI_CHN_3        /* 1106只有三通道可用 */
} VI_CHN_USE;
#elif (PLATFORM == PLATFORM_RV1126B)
typedef enum tagViChnUse_S
{
    MPP_VI_CHN_0 = 0,   /* VI通道0，给主码流用 */
    MPP_VI_CHN_1,       /* VI通道1，给子码流用 */
    MPP_VI_CHN_2,       /* VI通道2，连接VPSS之后给算法和VO用 */

    MPP_VI_CHN_BUTT,
    MPP_VI_CHN_3        /* 1106只有三通道可用 */
} VI_CHN_USE;
#endif


/* VPSS 通道映射定义 */
#if (BOARD == BOARD_ADA32V2)
typedef enum tagMppVpssChn_E
{
    MPP_VPSS_CHN_PRI = 0, /* VENC 主码流 */
    MPP_VPSS_CHN_SEC,     /* VENC 子码流 */
    MPP_VPSS_CHN_JPEG,    /* VENC jpeg流 */
    MPP_VPSS_CHN_VO,      /* VO 出图通道 */
    MPP_VPSS_CHN_EXT,     /* 扩展通道 */
    MPP_VPSS_CHN_ALG,     /* ALG 算法通道 */

    MPP_VPSS_CHN_BUTT,
} MPP_VPSS_CHN_TYPE_E;

#elif ((BOARD == BOARD_ADA32V3) || (BOARD == BOARD_DMS51V1) || (BOARD == BOARD_ADA32V4))
typedef enum tagMppVpssChn_E
{
    MPP_VPSS_CHN_PRI = 0, /* VENC 主码流 */
    MPP_VPSS_CHN_SEC,     /* VENC 子码流 */
    MPP_VPSS_CHN_ALG,     /* ALG 算法通道 */
    MPP_VPSS_CHN_VO,      /* VO 出图通道 */

    MPP_VPSS_CHN_BUTT,

    MPP_VPSS_CHN_JPEG,    /* VENC jpeg流，保证一致性 */
    MPP_VPSS_CHN_EXT      /* 扩展通道，保证一致性 */
} MPP_VPSS_CHN_TYPE_E;

#elif (BOARD == BOARD_DMS31V2)
typedef enum tagMppVpssChn_E
{
    MPP_VPSS_CHN_PRI = 0, /* VENC 主码流 */
    MPP_VPSS_CHN_SEC,     /* VENC 子码流 */
    MPP_VPSS_CHN_JPEG,    /* VENC jpeg流 */
    MPP_VPSS_CHN_VO,      /* VO 出图通道 */
    MPP_VPSS_CHN_EXT,     /* 扩展通道 */
    MPP_VPSS_CHN_ALG,     /* ALG 算法通道 */

    MPP_VPSS_CHN_BUTT,
} MPP_VPSS_CHN_TYPE_E;

#else
typedef enum tagMppVpssChn_E
{
    MPP_VPSS_CHN_PRI = 0, /* VENC 主码流 */
    MPP_VPSS_CHN_SEC,     /* VENC 子码流 */
    MPP_VPSS_CHN_JPEG,    /* VENC jpeg流 */
    MPP_VPSS_CHN_VO,      /* VO 出图通道 */
    MPP_VPSS_CHN_ALG,     /* ALG 算法通道 */

    MPP_VPSS_CHN_BUTT,

    MPP_VPSS_CHN_EXT      /* 保证一致性 */

} MPP_VPSS_CHN_TYPE_E;
#endif

#endif


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _MPP_COM_H */

