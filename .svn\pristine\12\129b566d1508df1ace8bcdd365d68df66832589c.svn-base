#! /bin/bash
#
# Interactive make menu script
#

ECHO="/bin/echo"
PRINTF="/usr/bin/printf"
MAKE="/usr/bin/make"
MAKE_LOG_FILE="make_log.txt"

# Tests whether *entire string* is numerical.
isdigit ()    
{
    [ $# -eq 1 ] || return 0

    case $1 in
    *[!0-9]*) return 1;;
    *) return 0;;
    esac
}


#$1     BOARD
#$2     command 
#r  release 
#d  debug 
#g  get 
#s  splint
issue_make_cmd()
{
    valid_board=0
    bIdx=0

    #Match against available target boards
    while [[ ${bIdx} -lt ${BOARD_NUM} ]]
    do
        #${ECHO} "issue_make_cmd, b=${TARGET_BOARDS[${bIdx}]} p1=$1 p2=$2"
        if [[ ${TARGET_BOARDS[${bIdx}]} == $1 ]]
        then
            valid_board=1
            break
        fi
        let bIdx+=1
    done
    if [[ ${valid_board} -eq 0 ]]
    then
        ${ECHO} "Invalid BOARD:$1"
        return 1
    fi

	case $2 in
	'd')
	${MAKE} BOARD=$1 DEBUG=y 2>&1 |tee ${MAKE_LOG_FILE}
    return
	;;

	'r')
	${MAKE} BOARD=$1 2>&1 |tee ${MAKE_LOG_FILE}
    return
	;;

	*)
    ${ECHO} "issue_make_cmd, invalid build type:$2"
    exit -1
	;;
	esac
}

#Append new board to this array
TARGET_BOARDS=("ADA32NSDK")
BOARD_NUM=${#TARGET_BOARDS[*]}

DEBUG_RELEASE_ADDON=100
RELEASE_START_CHOICE=1
RELEASE_END_CHOICE=`expr ${BOARD_NUM}`
DEBUG_START_CHOICE=`expr ${RELEASE_START_CHOICE} \* ${DEBUG_RELEASE_ADDON}`
DEBUG_END_CHOICE=`expr ${RELEASE_END_CHOICE} \* ${DEBUG_RELEASE_ADDON}`

print_help()
{
    idx=1
    debug_idx=0

	${PRINTF} "--------------------------------------\n"
	${PRINTF} "0   :\tmake all platform release\n"
	${PRINTF} "c0  :\tmake all platform clean\n"
    ${PRINTF} "--------------------------------------\n"
    while [[ ${idx} -le ${BOARD_NUM} ]]; do
        let debug_idx=${idx}*${DEBUG_RELEASE_ADDON}

        ${PRINTF} "%-3d :\tmake %-10s release\n" ${idx} ${TARGET_BOARDS[${idx}-1]}
        ${PRINTF} "%-3d :\tmake %-10s debug  \n" ${debug_idx} ${TARGET_BOARDS[${idx}-1]}
		${PRINTF} "c%-2d :\tmake clean\n" ${idx}
        ${PRINTF} "--------------------------------------\n"

        let idx=${idx}+1
    done
    
    ${PRINTF} "99 :\tmake platform_rv1126 release\n"
    ${PRINTF} "c99 :\tmake clean\n"
    ${PRINTF} "--------------------------------------\n"

    ${PRINTF} "%-3s :\tHelp!!\n" "h"
    ${PRINTF} "%-3s :\tQuit\n" "q"
}

#$1 input interactively or from CLI parameter
action_on_input()
{
    idx=$1
    debug="r"


    #Check enter only case
    if [[ ${idx} == "" ]]
    then
        ${ECHO} "Please make a choice"
        return 255
    fi

    #Check non-digital case
    if ! isdigit ${idx} 
    then
        ${ECHO} "${idx} contains non-digital char"
        return 255
    fi

    #Convert debug choice to release choice
    if   [[ ${idx} -ge ${RELEASE_START_CHOICE} && ${idx} -le ${RELEASE_END_CHOICE} ]] ;then
        debug="r"
    elif [[ ${idx} -ge ${DEBUG_START_CHOICE} && ${idx} -le ${DEBUG_END_CHOICE} ]] ;then
        debug="d"
        idx=`expr ${idx} / ${DEBUG_RELEASE_ADDON}`
    else
        ${ECHO} "${idx} is out of range"
        return 255
    fi

    #${ECHO} idx=${idx} debug=${debug} BAORD=${TARGET_BOARDS[${idx}]}
    issue_make_cmd ${TARGET_BOARDS[${idx}-1]} ${debug}
}

build_menu()
{
    print_help
    while [ 1 ]; do
        $ECHO -n "MainMenu:"
        read input

        case ${input} in
        "") #Filter enter key
        continue;
        ;;

        [Qq])
        break
        ;;

        [Hh])
        print_help
        continue
        ;;
		
		'c0')
		for ((i=0; i<${BOARD_NUM}; i++)); do
			${MAKE} BOARD=${TARGET_BOARDS[$i]} clean
		done
		continue
		;;
        
        'c99')
		for ((i=9; i<17; i++)); do
			${MAKE} BOARD=${TARGET_BOARDS[$i]} clean
		done
		continue
		;;
        
        c[1-9])
		idx=${input#c}
        ${MAKE} BOARD=${TARGET_BOARDS[${idx}-1]} clean
        continue
        ;;
		
		c1[0-9])
		idx=${input#c}
        ${MAKE} BOARD=${TARGET_BOARDS[${idx}-1]} clean
        continue
        ;;

        c2[0-9])
        idx=${input#c}
        ${MAKE} BOARD=${TARGET_BOARDS[${idx}-1]} clean
        continue
        ;;
				
		0)
		for ((i=0; i<${BOARD_NUM}; i++)); do
			${MAKE} BOARD=${TARGET_BOARDS[$i]} clean
			sleep 1
			${MAKE} BOARD=${TARGET_BOARDS[$i]} 2>&1 |tee ${MAKE_LOG_FILE}
		done
		continue
		;;
        
        99)
		for ((i=9; i<17; i++)); do
			${MAKE} BOARD=${TARGET_BOARDS[$i]} clean
			sleep 1
			${MAKE} BOARD=${TARGET_BOARDS[$i]} 2>&1 |tee ${MAKE_LOG_FILE}
		done
		continue
		;;

        T1-12)
        ${MAKE} BOARD=ADA32IR MOUDLETYPE=933 2>&1 |tee ${MAKE_LOG_FILE}	
        continue
        ;;

        T2-12)
        ${MAKE} BOARD=ADA32IR MOUDLETYPE=639T2 2>&1 |tee ${MAKE_LOG_FILE}	
        continue
        ;;

		T3-12)
        ${MAKE} BOARD=ADA32IR MOUDLETYPE=639T3 2>&1 |tee ${MAKE_LOG_FILE}	
        continue
        ;;
        esac
        action_on_input ${input}
    done
}

if [[ -z $1 && -z $2 ]]
then
build_menu
else
issue_make_cmd $1 $2
fi

# vim:et:sw=4:ts=4

