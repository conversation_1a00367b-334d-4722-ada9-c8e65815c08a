/******************************************************************************
Copyright (C) 2018-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_aenc.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2018-03-08

文件功能描述: 封装Sigmastar音频编码模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "common.h"
#include "mi_common.h"
#include "mi_ai.h"
#include "media.h"
#include "mpp_com.h"
#include "mpp_sys.h"
#include "mpp_aenc.h"

/* 音频编码通道状态信息 */
typedef struct tagAencChn_S
{
    MI_AI_CHN   AeChnId;        /* 编码通道ID */
    SV_BOOL     bCreated;       /* 是否已创建 */
    SV_BOOL     bEnable;        /* 是否使能编码 */
    sint32      s32AencFd;      /* 通道设备文件句柄 */
    AUDIO_ENCODE_TYPE_E enAencType;     /* 音频编码协议 */
    AUD_SR_E    enAudioSampleRate;      /* 音频采样率 */
} MPP_AENC_CHN_S;

/* 音频编码模块控制信息 */
typedef struct tagAencInfo_S
{
    uint32      u32ChnNum;      /* 音频源通道数目 */
    MPP_AENC_CHN_S astAencChn[2];   /* 音频编码通道 */
    sint32      s32MaxAencFd;   /* 音频编码通道设备文件句柄最大值 */
    MEDIA_DATA_CALLBACK pfDataCallback; /* 媒体流数据回调函数指针 */
    uint32      u32TID;         /* 音频编码线程ID */
    SV_BOOL     bRunning;       /* 线程是否正在运行 */
    SV_BOOL     bException;     /* 线程是否出现异常 */
    pthread_mutex_t mutexLock;  /* 通道操作线程互斥锁 */
} MPP_AENC_INFO_S;

MPP_AENC_INFO_S m_stAencInfo = {0};     /* 音频频编码控制信息 */

void * mpp_aenc_Body(MPP_AENC_INFO_S *pstAencInfo);
sint32 mpp_aenc_GetStream(sint32 s32Chn);

/******************************************************************************
 * 函数功能: 初始化AENC模块
 * 输入参数: pstAencConf --- 音频编码配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Init(MPP_AENC_CONF_S *pstAencConf)
{
    sint32 s32Ret = 0, i;
    sint32 s32VencFd = 0;
    MI_AI_CHN AiDevId = 0;
    MI_AI_CHN AeChn;
    MI_AI_AencConfig_t stAencAttr = {0};
    MI_SYS_ChnPort_t stChnPort = {0};

    if (NULL == pstAencConf || NULL == pstAencConf->pfDataCallback)
    {
        return ERR_NULL_PTR;
    }

    if (pstAencConf->u32ChnNum > MPP_AENC_MAX_CHN_NUM || pstAencConf->enAencType >= AUDIO_ENCODE_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = pthread_mutex_init(&m_stAencInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    m_stAencInfo.u32ChnNum = pstAencConf->u32ChnNum;
    m_stAencInfo.pfDataCallback = pstAencConf->pfDataCallback;
    
    switch (pstAencConf->enAencType)
    {
        case AUDIO_ENCODE_G711A:
        case AUDIO_ENCODE_LPCM:
            stAencAttr.eAencType = E_MI_AUDIO_AENC_TYPE_G711A;
            stAencAttr.stAencG711Cfg.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
            break;

        case AUDIO_ENCODE_G711U:
            stAencAttr.eAencType = E_MI_AUDIO_AENC_TYPE_G711U;
            stAencAttr.stAencG711Cfg.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
            break;  

        default: 
            return ERR_ILLEGAL_PARAM;
    }
    
    switch(pstAencConf->enAudioSampleRate)
    {
        case AUD_SR_8K:
            stAencAttr.stAencG711Cfg.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_8000;
            break;

        case AUD_SR_16K:
            stAencAttr.stAencG711Cfg.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_16000;
            break;
            
        case AUD_SR_32K:
            stAencAttr.stAencG711Cfg.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_32000;
            break;    

        default: 
            return ERR_ILLEGAL_PARAM;    
    }

    for (i = 0; i < pstAencConf->u32ChnNum; i++)
    {

        AeChn = i;
        
        s32Ret = MI_AI_SetAencAttr(AiDevId, AeChn, &stAencAttr);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_AI_SetAencAttr failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        s32Ret = MI_AI_EnableAenc(AiDevId, AeChn);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_AI_EnableAenc failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }


        stChnPort.eModId = E_MI_MODULE_ID_AI;
        stChnPort.u32DevId = AiDevId;
        stChnPort.u32ChnId = AeChn;
        stChnPort.u32PortId = 0;
        s32Ret = MI_SYS_GetFd(&stChnPort, &s32VencFd);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_SYS_GetFd failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        m_stAencInfo.astAencChn[i].AeChnId = AeChn;
        m_stAencInfo.astAencChn[i].bCreated = SV_TRUE;
        m_stAencInfo.astAencChn[i].bEnable = SV_TRUE;
        m_stAencInfo.astAencChn[i].s32AencFd = s32VencFd;
        m_stAencInfo.astAencChn[i].enAudioSampleRate = pstAencConf->enAudioSampleRate;
        m_stAencInfo.astAencChn[i].enAencType = pstAencConf->enAencType;
        if (m_stAencInfo.s32MaxAencFd < s32VencFd)
        {
            m_stAencInfo.s32MaxAencFd = s32VencFd;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化AENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Fini()
{
    sint32 s32Ret = 0, i;
    MI_AI_CHN AeChn;
    MI_AUDIO_DEV AiDevId = 0;

    for (i = 0; i < m_stAencInfo.u32ChnNum; i++)
    {
        AeChn = i;
        s32Ret = mpp_sys_AiAencUnBind(i, i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_AiAencUnBind failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        
        s32Ret = MI_AI_DisableAenc(AiDevId, AeChn);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_AENC_DestroyChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }
    pthread_mutex_destroy(&m_stAencInfo.mutexLock);
    memset(&m_stAencInfo, 0x0, sizeof(MPP_AENC_INFO_S));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动AENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Start()
{
    sint32 s32Ret = 0;
    uint32 u32TID = 0;

    m_stAencInfo.bRunning = SV_TRUE;
    m_stAencInfo.bException = SV_FALSE;
    s32Ret = pthread_create(&u32TID, NULL, mpp_aenc_Body, &m_stAencInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for AENC failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    m_stAencInfo.u32TID = u32TID;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止AENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_Stop()
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;

    m_stAencInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(m_stAencInfo.u32TID, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for AENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: AENC模块线程体
 * 输入参数: pstVencInfo --- 视频编码控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_aenc_Body(MPP_AENC_INFO_S *pstAencInfo)
{
    sint32 s32Ret = 0, i;
    SV_BOOL bSelect = SV_FALSE;
    fd_set read_fds, static_fds;
    struct timeval timeout = {2, 0};

    s32Ret = prctl(PR_SET_NAME, "mpp_aenc_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    
    while (pstAencInfo->bRunning)
    {
        bSelect = SV_FALSE;
        for (i = 0; i < pstAencInfo->u32ChnNum; i++)
        {
            if (pstAencInfo->astAencChn[i].bCreated && pstAencInfo->astAencChn[i].bEnable)
            {
                bSelect = SV_TRUE;
            }
        }

        if (!bSelect)
        {
            sleep_ms(500);
            continue;
        }

        FD_ZERO(&static_fds);
        for (i = 0; i < pstAencInfo->u32ChnNum; i++)
        {
            if (pstAencInfo->astAencChn[i].bCreated)
            {
                FD_SET(pstAencInfo->astAencChn[i].s32AencFd, &static_fds);
            }
        }
    
        read_fds = static_fds;
        timeout.tv_sec = 2;
        s32Ret = select(pstAencInfo->s32MaxAencFd + 1, &read_fds, NULL, NULL, &timeout);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "aenc select timeout or err. [ret=%#x]\n", s32Ret);
            sleep_ms(100);
            continue;
        }

        pthread_mutex_lock(&pstAencInfo->mutexLock);
        for (i = 0; i < pstAencInfo->u32ChnNum; i++)
        {
            if (pstAencInfo->astAencChn[i].bCreated && pstAencInfo->astAencChn[i].bEnable)
            {
                if (FD_ISSET(pstAencInfo->astAencChn[i].s32AencFd, &read_fds))
                {
                    s32Ret = mpp_aenc_GetStream(i);
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "aenc get Stream ch:%d failed! [ret=%#x]\n", i, s32Ret);
                        pstAencInfo->bException = SV_TRUE;
                    }
                }
            }
        }
        pthread_mutex_unlock(&pstAencInfo->mutexLock);
    }

    return NULL;
}

/******************************************************************************
 * 函数功能: 获取编码码流数据
 * 输入参数: s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_GetStream(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    MI_AI_CHN AiDevId = 0;
    MI_AI_CHN AeChn;
    MI_AUDIO_Frame_t stStream;
    STREAM_FRAME_S stFrame;

    AeChn = m_stAencInfo.astAencChn[s32Chn].AeChnId;
    s32Ret = MI_AI_GetFrame(AiDevId, AeChn, &stStream, NULL, 0);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MI_AI_GetFrame failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    if (NULL != m_stAencInfo.pfDataCallback)
    {
        stFrame.u64PTS = stStream.u64TimeStamp;
        stFrame.u32Sep = stStream.u32Seq;
        stFrame.u32PackCount = 1;
        stFrame.astPacks[0].enPackType = PACK_TYPE_AUDIO;
#if (BOARD == BOARD_IPCR20S5)
        if(m_stAencInfo.astAencChn[s32Chn].enAencType == AUDIO_ENCODE_LPCM)
        {
            stFrame.astPacks[0].pu8Addr = (uint8*)stStream.apSrcPcmVirAddr[0];
            stFrame.astPacks[0].u32Len = stStream.u32SrcPcmLen[0];
        }
        else
        {
            stFrame.astPacks[0].pu8Addr = (uint8*)stStream.apVirAddr[0];
            stFrame.astPacks[0].u32Len = stStream.u32Len[0];
        }
#else
        if(m_stAencInfo.astAencChn[s32Chn].enAencType == AUDIO_ENCODE_LPCM)
        {
            stFrame.astPacks[0].pu8Addr = (uint8*)stStream.apSrcPcmVirAddr[0];
            stFrame.astPacks[0].u32Len = stStream.u32SrcPcmLen;
        }
        else
        {
            stFrame.astPacks[0].pu8Addr = (uint8*)stStream.apVirAddr[0];
            stFrame.astPacks[0].u32Len = stStream.u32Len;
        }
#endif
        mpp_sys_CallbackLock();
        
        s32Ret = m_stAencInfo.pfDataCallback(s32Chn, STREAM_TYPE_AUDIO_AENC, &stFrame, sizeof(stFrame));

        mpp_sys_CallbackUnlock();
    }

    s32Ret = MI_AI_ReleaseFrame(AiDevId, AeChn, &stStream, NULL);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_AENC_ReleaseStream failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 使能某音频编码通道进行编码
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_AENC_MAX_CHN_NUM)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_EnableChn(sint32 s32Chn)
{
    sint32 s32Ret = 0, i;
    MI_AI_CHN AeChn;
    MI_AUDIO_DEV AiDevId = 0;
    
    for (i = 0; i < m_stAencInfo.u32ChnNum; i++)
    {
        AeChn = i;
        s32Ret = MI_AI_EnableAenc(AiDevId, AeChn);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MI_AI_EnableAenc failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 禁止某音频编码通道进行编码
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_AENC_MAX_CHN_NUM)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_aenc_DisableChn(sint32 s32Chn)
{
    sint32 s32Ret = 0, i;
    MI_AI_CHN AeChn;
    MI_AUDIO_DEV AiDevId = 0;
    
    for (i = 0; i < m_stAencInfo.u32ChnNum; i++)
    {
        AeChn = i;
        s32Ret = mpp_sys_AiAencUnBind(i, i);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_AiAencUnBind failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        
        s32Ret = MI_AI_DisableAenc(AiDevId, AeChn);
        if (MI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_AENC_DestroyChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置音频通道编码参数
 * 输入参数: s32Chn --- 编码通道号
             enEncType --- 编码类型
             enAudioSampleRate --- 编码采样率
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 
 *****************************************************************************/
sint32 mpp_aenc_SetEncParam(sint32 s32Chn, AUDIO_ENCODE_TYPE_E enEncType, AUD_SR_E enAudioSampleRate)
{
    sint32 s32Ret = 0, i;
    MI_AI_CHN AiDevId = 0;
    sint32 s32VencFd;
    MI_AI_AencConfig_t stAencAttr = {0};
    MI_SYS_ChnPort_t stChnPort = {0};

    if (s32Chn > MPP_AENC_MAX_CHN_NUM || enEncType >= AUDIO_ENCODE_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if(m_stAencInfo.astAencChn[s32Chn].enAencType == enEncType && m_stAencInfo.astAencChn[s32Chn].enAudioSampleRate == enAudioSampleRate)
        return SV_SUCCESS;

    mpp_aenc_DisableChn(s32Chn);

    switch (enEncType)
    {
        case AUDIO_ENCODE_G711A:
        case AUDIO_ENCODE_LPCM:
            stAencAttr.eAencType = E_MI_AUDIO_AENC_TYPE_G711A;
            stAencAttr.stAencG711Cfg.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
            break;

        case AUDIO_ENCODE_G711U:
            stAencAttr.eAencType = E_MI_AUDIO_AENC_TYPE_G711U;
            stAencAttr.stAencG711Cfg.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
            break;  

        default: 
            return ERR_ILLEGAL_PARAM;
    }

    switch(enAudioSampleRate)
    {
        case AUD_SR_8K:
            stAencAttr.stAencG711Cfg.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_8000;
            break;

        case AUD_SR_16K:
            stAencAttr.stAencG711Cfg.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_16000;
            break;
            
        case AUD_SR_32K:
            stAencAttr.stAencG711Cfg.eSamplerate = E_MI_AUDIO_SAMPLE_RATE_32000;
            break;    

        default: 
            return ERR_ILLEGAL_PARAM;    
    }

    s32Ret = MI_AI_SetAencAttr(AiDevId, s32Chn, &stAencAttr);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MI_AI_SetAencAttr failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = MI_AI_EnableAenc(AiDevId, s32Chn);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MI_AI_EnableAenc failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    stChnPort.eModId = E_MI_MODULE_ID_AI;
    stChnPort.u32DevId = AiDevId;
    stChnPort.u32ChnId = s32Chn;
    stChnPort.u32PortId = 0;
    s32Ret = MI_SYS_GetFd(&stChnPort, &s32VencFd);
    if (MI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MI_SYS_GetFd failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    pthread_mutex_lock(&m_stAencInfo.mutexLock);
    m_stAencInfo.astAencChn[s32Chn].AeChnId = s32Chn;
    m_stAencInfo.astAencChn[s32Chn].bCreated = SV_TRUE;
    m_stAencInfo.astAencChn[s32Chn].bEnable = SV_TRUE;
    m_stAencInfo.astAencChn[s32Chn].s32AencFd = s32VencFd;
    m_stAencInfo.astAencChn[s32Chn].enAudioSampleRate = enAudioSampleRate;
    m_stAencInfo.astAencChn[s32Chn].enAencType = enEncType;
    if (m_stAencInfo.s32MaxAencFd < s32VencFd)
    {
        m_stAencInfo.s32MaxAencFd = s32VencFd;
    }
    pthread_mutex_unlock(&m_stAencInfo.mutexLock);
    
    return SV_SUCCESS;
}

