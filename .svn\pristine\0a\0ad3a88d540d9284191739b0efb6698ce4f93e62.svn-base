/******************************************************************************
Copyright (C) 2020-2022 广州敏视数码科技有限公司版权所有.

文件名：ec200u.c

作者: lyn       版本: v1.0.0(初始版本号)   日期: 2021-07-17

文件功能描述: 定义块移远EC200U设备管理功能

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <termios.h>
#include <errno.h>
#include <fcntl.h>
#include <net/if.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <arpa/inet.h>
#include <sys/time.h>

#include "print.h"
#include "../include/ec200u.h"
#include "safefunc.h"
#include "ec200u.h"
#include "cellular.h"


#define EC200U_LOAD_DRI_PATH        "/root/scripts/ec200uload.sh"           /* EC200U模块驱动加载 */
#define EC200U_UNLOAD_DRI_PATH      "/root/scripts/ec200uunload.sh"         /* EC200U模块驱动卸载 */
#define EC200U_AT_PORT              "/dev/ttyUSB0"                        /* AT指令端口 */
#define EC200U_GPS_PORT             "/dev/ttyUSB6"                        /* GPS端口 */

#if defined(BOARD_DMS31V2)
#define EC200U_AT_PORT_PATH         "/sys/devices/platform/ffe00000.usb/usb1/1-1/1-1.2/1-1.2:1.2"
#define EC200U_GPS_PORT_PATH        "/sys/devices/platform/ffe00000.usb/usb1/1-1/1-1.2/1-1.2:1.1"
#elif (defined(BOARD_ADA900V1))
#define EC200U_AT_PORT_PATH         "/sys/devices/platform/ffe00000.usb/usb3/3-1/3-1.2/3-1.2:1.2"
#define EC200U_GPS_PORT_PATH        "/sys/devices/platform/ffe00000.usb/usb3/3-1/3-1.2/3-1.2:1.1"
#else
#endif


#define EC200U_AT_SET_ECHO          "ATE0\r"                              /* 回显指令 */
#define EC200U_AT_SET_ERROR         "AT+CMEE=2\r"                         /* 设置错误信息格式 */
#define EC200U_AT_SET_APN           "AT+QICSGP=1,1,\"%s\",\"%s\",\"%s\",%d\r" /* 设置APN */
#define EC200U_AT_GET_PDP           "AT+QIACT?\r"                         /* 查询PDP状态 */
#define EC200U_AT_SET_PDP           "AT+QIACT=1\r"                        /* 激活PDP上下文 */
#define EC200U_AT_GET_CALL          "AT$QCRMCALL?\r"
#define EC200U_AT_SET_CALL          "AT$QCRMCALL=1,1\r"
#define EC200U_AT_SET_CALL_OFF      "AT$QCRMCALL=0,1\r"                   /* 挂断 */
#define EC200U_AT_GET_SIGNAL        "AT+CSQ\r"                            /* 获取信号强度 */
#define EC200U_AT_SET_GNSS          "AT+QGPS=1\r"                         /* GPS开启 */
#define EC200U_AT_GET_GNSS          "AT+QGPS?\r"                          /* 读取GPS状态 */
#define EC200U_AT_GET_MANUFACTURE   "AT+GMI\r"                            /* 获取设备制造商 */
#define EC200U_AT_GET_MODEL         "AT+GMM\r"
#define EC200U_AT_GET_VERSION       "AT+GMR\r"                            /* 获取软件版本 */
#define EC200U_AT_GET_IMEI          "AT+GSN\r"
#define EC200U_AT_GET_IMSI          "AT+CIMI\r"                           /* 获取国际移动用户身份 */
#define EC200U_AT_GET_NETTYPE       "AT+COPS?\r"                          /* 获取网络类型0：GSM 2: UTRAN 100:CDMA*/
#define EC200U_AT_GET_ICCID         "AT+QCCID\r"                          /* 获取卡识别ID */
#define EC200U_AT_GET_SIM           "AT+CPIN?\r"                          /* 获取SIM卡是否插入 */
#define EC200U_AT_GET_CS            "AT+CREG?\r"                          /* 获取网络注册状态CS */
#define EC200U_AT_GET_PS            "AT+CGREG?\r"                         /* 获取网络注册状态PS */
#define EC200U_AT_GET_EPS           "AT+CEREG?\r"                         /* 获取网络注册状态 */
#define EC200U_AT_GET_CEER          "AT+CEER\r"
#define EC200U_AT_GET_TEMP          "AT+QTEMP\r"
#define EC200U_AT_SET_QCFG 		 	"AT+QCFG=\"USBNET\",1\r"              /* 配置 USB 网络端口协议 1:ECM 3:RNDIS */
#define EC200U_AT_SET_QNETDEVCTL    "AT+QNETDEVCTL=3,1,1\r"      		  /* 网卡拨号配置 3:自动连接网卡 1:PDP上下文索引号。1:开启URC上报 */
#define EC200U_AT_SET_CFUN          "AT+CFUN=1,1\r"                       /* 设置功能模式 1:全功能模式 1:重启 */

#define EC200U_AT_GET_GPRS          "AT+QCFG=\"gprsattach\"\r"
#define EC200U_AT_GET_BAND          "AT+QCFG=\"band\"\r"
#define EC200U_AT_SET_FTM           "AT+QRFTESTMODE=1\r"
#define EC200U_AT_EXIT_FTM          "AT+QRFTESTMODE=0\r"


#define EC200U_AT_BUFFER_SIZE       1024

const char ec200uDriList[][32] = 
{
    "usbserial",
    "usb_wwan",
    "option"
};

CELLULAR_MODULE_PARAM_S stEC200UModuleParam = {0};

/******************************************************************************
 * 函数功能: 根据设备文件查找对应端口号
 * 输入参数: u32Size -- 输出数据长度
 * 输出参数: port -- 返回端口
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_SearchDev(char *port, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[128] = {0};
    char szCmd[128] = {0};
    char szPath[128] = {0};
    char *pcTmp = NULL;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
    sprintf(szCmd, "find /sys/devices/platform/ -name \"*-1.2\:1.2\"");
#elif (defined(BOARD_WFCR20S2))
	sprintf(szCmd, "find /sys/devices/soc0/ -name \"*-1.1\:1.2\"");   //wfc : /sys/devices/soc0/soc/soc:Sstar-ehci-1/usb1/1-1/1-1.1/1-1.1:1.2/ttyUSB2/tty/ttyUSB2
#endif

	s32Ret = GetInsContext(szCmd, szPath, sizeof(szPath));
    /* 去换行符 */
    pcTmp = strchr(szPath, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szPath, '\n');
    
    if(NULL != pcTmp)
        *pcTmp = '\0';

    memset(szCmd, 0, sizeof(szCmd));
    
    sprintf(szCmd, "ls %s | grep tty", szPath);
    s32Ret = GetInsContext(szCmd, szBuf, sizeof(szBuf));
    /* 去换行符 */
    pcTmp = strchr(szBuf, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szBuf, '\n');
    
    if(NULL != pcTmp)
        *pcTmp = '\0';
    
    if((SV_SUCCESS == s32Ret) && (strstr(szBuf, "ttyUSB") != NULL))
    {
        snprintf(port, u32Size, "/dev/%s", szBuf);
        return SV_SUCCESS;
    }
    
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 打开AT指令设备文件
 * 输入参数: ps32Fd -- 返回文件描述符存储区
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_OpenATInterface(sint32 *ps32Fd)
{
    sint32 s32Fd = 0;
    sint32 s32Ret = -1;
    char szATPort[32] = {0};
    
    s32Ret = ec200u_SearchDev(szATPort, sizeof(szATPort));
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "search AT Port error\n");
        return SV_FAILURE;
    }

    s32Fd = open(szATPort, O_CLOEXEC|O_RDWR|O_NONBLOCK);
    if(s32Fd < 0)
    {
        print_level(SV_ERROR, "open %s error\n", szATPort);
        return SV_FAILURE;
    }

    *ps32Fd = s32Fd;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 关闭AT指令设备文件
 * 输入参数: ps32Fd -- 文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_CloseATInterface(sint32 s32Fd)
{
    sint32 s32Ret = 0;
    if(s32Fd <=0)
    {
        return SV_SUCCESS;
    }

    s32Ret = close(s32Fd);
    if(s32Ret != 0)
    {
        print_level(SV_ERROR, "close error\n");
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 去换行符\r \n
 * 输入参数: pBuf -- 数据指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32    ec200u_CutChar(char* pszBuf, sint32 s32Len)
{
    sint32 s32Cnt = 0;
    sint32 i = 0;
    char temp[EC200U_AT_BUFFER_SIZE] = {0};

    if(NULL == pszBuf)
    {
        return SV_FAILURE;
    }

    for(i = 0; i<s32Len; i++)
    {
        if(pszBuf[i] != '\n' && pszBuf[i] != '\r')
        {
            temp[s32Cnt] = pszBuf[i];
            s32Cnt++;
        }
    }
    strncpy(pszBuf, temp, EC200U_AT_BUFFER_SIZE);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 字符串分割符,提取后面字符
 * 输入参数: pszBuf -- 分割字符串 delim -- 分割符 u32Num -- 层次
 * 输出参数: 无
 * 返回值  : 指向pszBuf的指针地址
             NULL -- 失败
 * 注意    : 无
 *****************************************************************************/
char* ec200u_Strtok(const char* pszBuf, const char* delim, uint32 u32Num)
{
    uint8 i = 0;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pSave = NULL;
    char *pc = NULL;
    uint32 u32Offset = 0;
    
    if(NULL == pszBuf || NULL == delim)
    {
        return NULL;
    }

    strncpy(szBuf, pszBuf, EC200U_AT_BUFFER_SIZE-1);
    szBuf[EC200U_AT_BUFFER_SIZE-1] = '\0';
    pc = strtok_r(szBuf, delim, &pSave);
    for(i=1; i<u32Num && pc; i++)
    {
        pc = strtok_r(NULL, delim, &pSave);
    }
    if(NULL == pc)
    {
        return NULL;
    }
    u32Offset = pc - szBuf;
    return (char* )(pszBuf + u32Offset);
}

/******************************************************************************
 * 函数功能: 字符串分割符,提取字符':'和','之间的整数值
 * 输入参数: pszBuf -- 分割字符串 u32Result 返回的整数
 * 输出参数: 无
 * 返回值  : 指向pszBuf的指针地址
             NULL -- 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_Str2Data(const char* pszBuf, uint32* u32Result)
{
    sint32 s32Offset = 0;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pc = NULL;
    char *pSave = NULL;
    char szTemp[32] = {0};
    
    if(NULL == pszBuf || NULL == u32Result)
    {
        return SV_FAILURE;
    }
    //拷贝内存
    strncpy(szBuf, pszBuf, EC200U_AT_BUFFER_SIZE-1);
    szBuf[EC200U_AT_BUFFER_SIZE-1] = '\0';
    //获取内存地址偏移
    pc = strtok_r(szBuf, ":", &pSave);
    pc = strtok_r(NULL, ",", &pSave);
    s32Offset = pSave - pc;

    if(NULL == pc || NULL == pSave || s32Offset <= 0)
    {
        return SV_FAILURE;
    }

    memcpy(szTemp, pc, s32Offset);
    szTemp[s32Offset] = '\0';
    *u32Result = atoi(szTemp);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 往设备内写文件
 * 输入参数: s32Fd -- 端口设备文件 pcCmd -- 命令字符串指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_Write(sint32 s32Fd, const char* pcCmd)
{
    sint32 s32Ret = 0;
    if(s32Fd <= 0 || pcCmd == NULL)
    {
        return SV_FAILURE;
    }

    tcflush(s32Fd, TCIOFLUSH);

    s32Ret = write(s32Fd, pcCmd, strlen(pcCmd));
    if(s32Ret != strlen(pcCmd))
    {
        print_level(SV_ERROR, "write AT CMD %s error\n", pcCmd);
        return SV_FAILURE;
    }
    //print_level(SV_INFO, "AT Write: %s \n", pcCmd);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读设备返回的数据
 * 输入参数: s32Fd -- 端口设备文件 pBuf -- 缓存地址 u32Timeout -- 超时等待时间(s)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_Read(sint32 s32Fd, char* pszBuf, uint32 u32Timeout)
{
    sint32 s32Ret = -1;
    sint32 s32MaxFd = s32Fd + 1;
    sint32    s32Count = 0;
    char cTemp[EC200U_AT_BUFFER_SIZE] = {0};
    struct timeval stTimeval;
    fd_set read_fds;
    char* pc = NULL;

    if(s32Fd <= 0 || NULL == pszBuf)
    {
        return SV_FAILURE;
    }

    stTimeval.tv_usec = 0;
    stTimeval.tv_sec = u32Timeout;
    FD_ZERO(&read_fds);
    FD_SET(s32Fd, &read_fds);
Wait:
    s32Ret = select(s32MaxFd, &read_fds, NULL, NULL, &stTimeval);
    if(s32Ret == 0)
    {
        print_level(SV_DEBUG, "read timeout\n");
        return SV_FAILURE;
    }
    else if(s32Ret == -1)
    {
        if(errno == EAGAIN || errno == EWOULDBLOCK || errno == EINTR)
        {
            goto Wait;
        }
        else
        {
            print_level(SV_ERROR,"read fail error[%s]\n", strerror(errno));
            return SV_FAILURE;
        }
    }
    sleep_ms(100);

    //读取多行数据
    while(1)
    {
        s32Ret = read(s32Fd, cTemp, EC200U_AT_BUFFER_SIZE-s32Count);
        if(s32Ret > 0)
        {
            memcpy(pszBuf+s32Count, cTemp, s32Ret);
            s32Count += s32Ret;
        }
        else
        {
            break;
        }
    }
    //去换行符
    ec200u_CutChar(pszBuf, s32Count);
    //print_level(SV_INFO, "AT read: %s \n", pszBuf);
    //去掉OK字符
    pc = strstr(pszBuf, "OK");
    if(pc)
    {
        *pc = '\0';
        return SV_SUCCESS;
    }

    return SV_FAILURE;

}

/******************************************************************************
 * 函数功能: 发送命令并获取返回数据
 * 输入参数: s32Fd -- 文件描述符 pcCmd -- 命令字符串 
              pBuf -- 返回数据存储区 u32Timeout -- 超时时间
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_SendAndRecv(sint32 s32Fd, const char* pcCmd, char* pBuf, uint32 u32Timeout)
{
    sint32 s32Ret = -1;
	char szCmd[128] = {0};
	char szRetBuf[EC200U_AT_BUFFER_SIZE] = {0};
	
    if(s32Fd <= 0 || NULL == pcCmd || NULL == pBuf)
    {
        return SV_FAILURE;
    }
    
    s32Ret = ec200u_Write(s32Fd, pcCmd);
	if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }

	sleep_ms(200);
	
    s32Ret = ec200u_Read(s32Fd, pBuf, u32Timeout);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }
    
    if(g_TestMode == SV_TRUE)
    {
       print_level(SV_INFO, "AT Send cmd: %s \n", pcCmd);
       print_level(SV_INFO, "AT Read :%s \n",  pBuf);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 监测驱动加载情况
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_DriverProbe(SV_BOOL *pbIsExist)
{
    uint8  i = 0;
    sint32 s32Ret = SV_FAILURE;
    char szBuf[128] = {0};
    char szCmd[64] = {0};

    if(NULL == pbIsExist)
    {
        return SV_FAILURE;
    }

    for(i = 0; i< (sizeof(ec200uDriList) >> 5); i++)
    {
        sprintf(szCmd, "lsmod | grep %s", ec200uDriList[i]);
        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = GetInsContext(szCmd, szBuf, sizeof(szBuf));
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "get instruction context error\n");
            return SV_FAILURE;
        }
        if(strstr(szBuf, ec200uDriList[i]) != NULL)
        {
            *pbIsExist = SV_TRUE;
        }
        else
        {
            *pbIsExist = SV_FALSE;
            break;
        }
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 加载驱动模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_LoadDriver(void)
{
    sint32 s32Ret = -1;
    char szCmd[64];
    
    sprintf(szCmd, "%s  1>/dev/null 2>/dev/null", EC200U_LOAD_DRI_PATH);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 卸载驱动模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_UnLoadDriver(void)
{
    sint32 s32Ret = -1;
    char szCmd[64];
    
    sprintf(szCmd, "%s  1>/dev/null 2>/dev/null", EC200U_UNLOAD_DRI_PATH);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 获取卡识别ID
 * 输入参数: s32Fd -- AT端口文件描述符  pICCID -- 用户传递存储区buffer
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetICCID(sint32 s32Fd, char *pICCID, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pc = NULL;
    
    if(NULL == pICCID)
    {
        return SV_FAILURE;
    }

    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_ICCID, szBuf, 1);
    pc = strstr(szBuf, "+QCCID:");
    if(SV_SUCCESS == s32Ret && pc)
    {
        strncpy(pICCID, pc+strlen("+QCCID: "), u32Size);
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取国际移动用户身份id
 * 输入参数: s32Fd -- AT端口文件描述符  u8Buf -- 用户传递存储区buffer
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetIMSI(sint32 s32Fd, char *pIMSI, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};

    if(NULL == pIMSI)
    {
        return SV_FAILURE;
    }
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_IMSI, szBuf, 1);
    if(SV_SUCCESS == s32Ret)
    {
        strncpy(pIMSI, szBuf, u32Size);
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取设备制造商
 * 输入参数: s32Fd - 端口文件描述符 pManufacturer -- 保存地址 u32Size -- 缓存区大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetManufacturer(sint32 s32Fd, char *pManufacturer, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};

    if(NULL == pManufacturer)
    {
        return SV_FAILURE;
    }
    
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_MANUFACTURE, szBuf, 1);
    if(SV_SUCCESS == s32Ret)
    {
        strncpy(pManufacturer, szBuf, u32Size);
        return SV_SUCCESS;
    }
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取模块AT指令软件版本
 * 输入参数: s32Fd - 端口文件描述符 pVersion -- 保存地址 u32Size -- 缓存区大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetVersion(sint32 s32Fd, char *pVersion, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};

    if(NULL == pVersion)
    {
        return SV_FAILURE;
    }
    
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_VERSION, szBuf, 1);
    if(SV_SUCCESS == s32Ret)
    {
        strncpy(pVersion, szBuf, u32Size);
        return SV_SUCCESS;
    }
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取International Mobile Equipment Identity
 * 输入参数: s32Fd - 端口文件描述符 pIMEI -- 保存地址 u32Size -- 缓存区大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetIMEI(sint32 s32Fd, char *pIMEI, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};

    if(NULL == pIMEI)
    {
        return SV_FAILURE;
    }
    
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_IMEI, szBuf, 1);
    if(SV_SUCCESS == s32Ret)
    {
        strncpy(pIMEI, szBuf, u32Size);
        return SV_SUCCESS;
    }
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取网络类型
 * 输入参数: s32Fd - 端口文件描述符 pIMEI -- 保存地址 u32Size -- 缓存区大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetNetType(sint32 s32Fd, char *pNetType, uint32 u32Size)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pc = NULL;
    sint32 s32NetType = 0;
    uint32 u32CopyNum = 0;
    if(NULL == pNetType)
    {
        return SV_FAILURE;
    }
    
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_NETTYPE, szBuf, 1);
    if(SV_SUCCESS == s32Ret && (pc = strstr(szBuf,"COPS:")))
    {
        pc = ec200u_Strtok(szBuf, ",", 4);
        if(NULL != pc)
        {
            s32NetType = atoi(pc);
            switch(s32NetType)
            {
                case 0:
                    pc = "GSM";
                    break;
                case 2:
                    pc = "UTRAN";
                    break;
                case 7:
                    pc = "E-UTRAN";
                    break;
                case 100:
                    pc = "CDMA";
                    break;
                default:
                    pc = "UNKNOWN";
                    break;
            }
            u32CopyNum = ((u32Size > strlen(pc)) ? (strlen(pc)+1): u32Size);
            memcpy(pNetType, pc, u32CopyNum);
            
            return SV_SUCCESS;
        }
    }
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取信号强度
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetRxRSSI(sint32 s32Fd, uint32* pResult)
{
    sint32 s32Status = -1;
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};

    if(NULL == pResult)
    {
        return SV_FAILURE;
    }

    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_SIGNAL, szBuf, 1);
    char *pc = NULL;
    pc = strstr(szBuf, "+CSQ:");
    if(SV_SUCCESS == s32Ret && pc)
    {
        pc = ec200u_Strtok(pc + strlen("+CSQ:"), ",", 2);
        s32Ret = ec200u_Str2Data(szBuf, pResult);
        if(NULL != pc)
        {
            s32Status = atoi(pc);
            if(1 == s32Status || 5 == s32Status)
            {
                return SV_SUCCESS; 
            }
        }
    }
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 获取模块温度
 * 输入参数: s32Fd - 端口文件描述符 pTemperature -- 保存地址
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetTemperature(sint32 s32Fd, uint32 *pTemperature)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pc = NULL;
    if(NULL == pTemperature)
    {
        return SV_FAILURE;
    }
    
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_TEMP, szBuf, 1);
    if(SV_SUCCESS == s32Ret)
    {
        pc = strstr(szBuf, "+QTEMP:");
        pc = strstr(pc,",");
        if(NULL != pc)
        {
            *pTemperature = atoi(pc+strlen(","));
            return SV_SUCCESS;
        }    
    }
    *pTemperature = 0;
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 读取IP地址
 * 输入参数: pIPAddr -- 保存IP地址存储空间
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_GetIPAddr(char *pIPAddr, uint32 u32Size)
{
    int s32SocketFd;
    sint32 s32Ret = -1;
    struct ifreq ifr;
	
    if((NULL == pIPAddr)|| u32Size < 32)
    {
        return SV_FAILURE;
    }

    s32SocketFd = socket(AF_INET, SOCK_STREAM, 0);
    if(s32SocketFd < 0)
    {
        return SV_FAILURE;
    }
    
    strcpy(ifr.ifr_name, EC200U_NET_CARD);
    s32Ret = ioctl(s32SocketFd, SIOCGIFADDR, &ifr);
    if(s32Ret < 0)
    {
		print_level(SV_ERROR,"ioctl error\n");
        close(s32SocketFd);
        return SV_FAILURE;
    }
    close(s32SocketFd);
	 
    memcpy(pIPAddr, inet_ntoa(((struct sockaddr_in*)&ifr.ifr_addr)->sin_addr), u32Size);
    //print_level(SV_DEBUG, "IP address %s\n", pIPAddr);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查SIM卡是否出错
 * 输入参数: 无
 * 输出参数: s32Fd -- 文件描述符 bIsError -- 错误状态返回指针
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_CheckSIMStatus(sint32 s32Fd, SV_BOOL *bIsError)
{
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};

    if(NULL == bIsError)
    {
        return SV_FAILURE;
    }
    
    ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_SIM, szBuf, 1);
    if(strstr(szBuf, "SIM failure"))
    {
        *bIsError = SV_TRUE;
    }
    else
    {
        *bIsError = SV_FALSE;
    }
    return SV_SUCCESS;

}
/******************************************************************************
 * 函数功能: SIM卡插入检测
 * 输入参数: 无
 * 输出参数: bIsCardExist -- 卡插入情况（只能检测拔出） penModuleStat -- 模块状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_SimCardProbe(SV_BOOL *bIsCardExist, CELLULAR_MODULE_PARAM_S *pstMoudleParam)
{
    sint32 s32Ret = -1;
    uint8 i = 0;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    static SV_BOOL bIsCardExistLocal = SV_FALSE;

    if(NULL == bIsCardExist)
    {
        return SV_FAILURE;
    }
    //检测SIM卡是否存在
    for(i=0; i<3; i++)
    {
        memset(szBuf, 0 ,sizeof(szBuf));
        s32Ret = ec200u_SendAndRecv(pstMoudleParam->s32Fd, EC200U_AT_GET_SIM, szBuf, 1);
        if(SV_SUCCESS == s32Ret && strstr(szBuf, "+CPIN: READY"))
        {
            *bIsCardExist = SV_TRUE;
            pstMoudleParam->bException = SV_FALSE;
            pstMoudleParam->enModuleStatus = CELL_STAT_NORMAL;
            if(SV_FALSE == bIsCardExistLocal)
            {
                s32Ret = ec200u_GetICCID(pstMoudleParam->s32Fd, pstMoudleParam->szICCD, sizeof(pstMoudleParam->szICCD));
                if(SV_FAILURE == s32Ret)
                {
                    sleep_ms(1000);
                    continue;
                }
                
                s32Ret = ec200u_GetIMSI(pstMoudleParam->s32Fd, pstMoudleParam->szIMSI, sizeof(pstMoudleParam->szIMSI));
                if(SV_FAILURE == s32Ret)
                {
                    sleep_ms(1000);
                    continue;
                }                
            }
            bIsCardExistLocal = *bIsCardExist;
            break;
        }
        else
        {
            *bIsCardExist = SV_FALSE;
            pstMoudleParam->bException = SV_TRUE;
            pstMoudleParam->enModuleStatus = CELL_STAT_INIT;
            bIsCardExistLocal = *bIsCardExist;
            if(strstr(szBuf, "SIM failure"))
            {
                print_level(SV_ERROR, "SIM failure\n");
                pstMoudleParam->enModuleStatus = CELL_STAT_SIM_ERR;
            }
            if(strstr(szBuf, "SIM not inserted"))
            {
                print_level(SV_WARN, "SIM not inserted\n");
            }            
            sleep_ms(1000);
            break;
        }
    }

    if(CELL_STAT_NORMAL == pstMoudleParam->enModuleStatus)
    {
        return SV_SUCCESS;
    }
    
    return SV_FAILURE;
}
/******************************************************************************
 * 函数功能: 初始化模块信息
 * 输入参数: ppstModuleParam -- 主控模块传递的用来指向子模块的指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_APPInit(CELLULAR_MODULE_PARAM_S **ppstModuleParam)
{
    uint8 i = 0;
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    
    if(NULL == ppstModuleParam)
    {
        return SV_FAILURE;
    }
    
    //绑定指针，主控模块指针指向子模块结构体
    *ppstModuleParam = &stEC200UModuleParam;

    //打开AT指令端口
    for (i = 0; i < 5; i++)
    {
        s32Ret = ec200u_OpenATInterface(&stEC200UModuleParam.s32Fd);
        if (SV_SUCCESS == s32Ret)
        {
            break;
        }
        sleep_ms(2000);
    }
    if (SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "ec200u_OpenATInterface failed.\n");
        return SV_FAILURE;
    }
    
    sleep_ms(50);
    for(i=0; i < 5; i++)
    {
        //关闭命令回显
        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = ec200u_SendAndRecv(stEC200UModuleParam.s32Fd, EC200U_AT_SET_ECHO, szBuf, 1);
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "send %s error\n", EC200U_AT_SET_ECHO);
            sleep_ms(2000);
            continue;
        }
        
        //设置错误输出格式
        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = ec200u_SendAndRecv(stEC200UModuleParam.s32Fd, EC200U_AT_SET_ERROR, szBuf, 1);
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "send %s error\n", EC200U_AT_SET_ERROR);
            sleep_ms(2000);
            continue;
        }
        
        //设置GPS
        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = ec200u_SendAndRecv(stEC200UModuleParam.s32Fd, EC200U_AT_GET_GNSS, szBuf, 1);
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "send %s error\n", EC200U_AT_GET_GNSS);
            sleep_ms(2000);
            continue;
        }
        if(!strstr(szBuf, "+QGPS: 1"))
        {
            memset(szBuf, 0, sizeof(szBuf));
            s32Ret = ec200u_SendAndRecv(stEC200UModuleParam.s32Fd, EC200U_AT_SET_GNSS, szBuf, 2);
            if(SV_FAILURE == s32Ret)
            {
                print_level(SV_ERROR, "send %s error\n", EC200U_AT_SET_GNSS);
                sleep_ms(2000);
                continue;
            }
        }

        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = GetInsContext("touch /var/info/cell_gps_open", szBuf, sizeof(szBuf));
        if (SV_SUCCESS != s32Ret)
        {
            memset(szBuf, 0, sizeof(szBuf));
            s32Ret = GetInsContext("touch /var/info/cell_gps_open", szBuf, sizeof(szBuf));
        }
        
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 清除子模块信息
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_ClearModule(void)
{
    sint32 s32Ret = -1;
    s32Ret = ec200u_CloseATInterface(stEC200UModuleParam.s32Fd);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }
    memset(&stEC200UModuleParam, 0 , sizeof(stEC200UModuleParam));
    return SV_SUCCESS;
}



/******************************************************************************
 * 函数功能: Circuit Switch
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_CSConnect(sint32 s32Fd)
{
    sint32 s32Status = -1;
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pc = NULL;
    
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_CS, szBuf, 1);
    pc = strstr(szBuf, "+CREG:");
    if(SV_SUCCESS == s32Ret && pc)
    {
        pc = ec200u_Strtok(pc + strlen("+CREG:"), ",", 2);
        if(NULL != pc)
        {
            s32Status = atoi(pc);
            if(1 == s32Status || 5 == s32Status)
            {
                return SV_SUCCESS; 
            }
        }
    }    
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: Packet Switch连接，无论结果如何都继续往下继续
 * 输入参数: s32Fd -- 设备文件描述符
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_PSConnect(sint32 s32Fd)
{
    sint32 s32Status = -1;
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pc = NULL;
    
    /*
    1.     If <stat> of AT+CGREG?/AT+CEREG? equals to 1 or 5, 
        it means that the module has registered on PS domain service in UMTS/LTE network. 
    2.    Go to next step no matter whether it is registered on PS domain service or not in 60s.
    */
    
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_PS, szBuf, 1);
    pc = strstr(szBuf, "+CGREG:");
    if(SV_SUCCESS == s32Ret && pc)
    {
        pc = ec200u_Strtok(pc + strlen("+CGREG:"), ",", 2);
        if(NULL != pc)
        {
            s32Status = atoi(pc);
            if(1 == s32Status || 5 == s32Status)
            {
                return SV_SUCCESS; 
            }
        }
    }
#if 0
    else
    {
        return SV_FAILURE;
    }
#endif

    memset(szBuf, 0, sizeof(szBuf));
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_EPS, szBuf, 1);
    pc = strstr(szBuf, "+CEREG:");
    if(SV_SUCCESS == s32Ret && pc)
    {
        pc = ec200u_Strtok(pc + strlen("+CEREG:"), ",", 2);
        if(NULL != pc)
        {
            s32Status = atoi(pc);
            if(1 == s32Status || 5 == s32Status)
            {
                return SV_SUCCESS; 
            }
        }
    }
    
    return SV_FAILURE;
}
/******************************************************************************
 * 函数功能: PDP连接设置
 * 输入参数: pstMoudleParam -- 子模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_PDPConnect(CELLULAR_MODULE_PARAM_S *pstMoudleParam)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char szCmd[512] = {0};

    //查询是否激活了PDP
    s32Ret = ec200u_SendAndRecv(pstMoudleParam->s32Fd, EC200U_AT_GET_PDP, szBuf, 1);
    if(SV_SUCCESS == s32Ret && strstr(szBuf, "+QIACT: 1,1"))
    {
        print_level(SV_DEBUG, "PDP already active\n");
        return SV_SUCCESS;
    } 

    memset(szBuf, 0, sizeof(szBuf));
    //TODO: 2021/08/13 默认APN,账号和密码参数需要设置
    //memcpy(pstMoudleParam->stSetup.szApn, "3gnet", sizeof("3gnet"));
    //memcpy(pstMoudleParam->stSetup.szApn, "unim2m.njm2mapn", sizeof("unim2m.njm2mapn"));
    snprintf(szCmd, 512, EC200U_AT_SET_APN, pstMoudleParam->stSetup.szApn, pstMoudleParam->stSetup.szUserName, pstMoudleParam->stSetup.szPassWd, 3);

    s32Ret = ec200u_SendAndRecv(pstMoudleParam->s32Fd, szCmd, szBuf, 1);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_DEBUG, "%s error\n", szCmd);
    }

    /************************************************************************
                         巴西专用版需要支持4个运营商
    operator    MNC                apn                 user     password
    
    tim         02.03.04.08        timbrasil.br        tim      tim
    vivo        06.10.11.23        zap.vivo.com.br     vivo     vivo
    Claro       05                 claro.com.br        claro    claro
    Oi          16.24.31           gprs.oi.com.br      oi       oi
            
    ************************************************************************/
    //TODO:    2021/08/13 设置PDP连接容易读取出错,暂时未有何影响
    memset(szBuf, 0, sizeof(szBuf));
    s32Ret = ec200u_SendAndRecv(pstMoudleParam->s32Fd, EC200U_AT_SET_PDP, szBuf, 1);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_DEBUG, "AT %s error\n", EC200U_AT_SET_PDP);
        sleep_ms(3000);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 ec200u_Call(sint32 s32Fd)
{
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};

    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_GET_CALL, szBuf, 1);
    if (SV_SUCCESS == s32Ret && strstr(szBuf, "$QCRMCALL: 1"))
    {
        return SV_SUCCESS;
    }

    /* 先挂断,不用管结果 */
    memset(szBuf, 0, sizeof(szBuf));
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_SET_CALL_OFF, szBuf, 1);
    
    memset(szBuf, 0, sizeof(szBuf));
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_SET_CALL, szBuf, 3);
    if (SV_SUCCESS == s32Ret)
    {    
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 4G网络网卡设置
 * 输入参数: bSwitch -- 网卡拉起开关
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_SwitchNetWork(SV_BOOL bSwitch)
{
    int s32SocketFd;
    sint32 s32Ret = -1;
    struct ifreq ifr;
    //IPV4 TCP连接
    s32SocketFd = socket(AF_INET, SOCK_STREAM, 0);
    if(s32SocketFd < 0)
    {
        return SV_FAILURE;
    }
    strcpy(ifr.ifr_name, EC200U_NET_CARD);
    s32Ret = ioctl(s32SocketFd, SIOCGIFFLAGS, &ifr);
    if(s32Ret < 0)
    {
        close(s32SocketFd);
        return SV_FAILURE;
    }
    //设置参数
    if(SV_FALSE == bSwitch)
    {
        ifr.ifr_flags &= ~IFF_UP;
    }
    else
    {
        ifr.ifr_flags |= (IFF_UP | IFF_RUNNING);
    }

    s32Ret = ioctl(s32SocketFd, SIOCSIFFLAGS, &ifr);

    if(0 != s32Ret)
    {
        close(s32SocketFd);
        print_level(SV_ERROR, "set net param error\n");
        return SV_FAILURE;
    }

    close(s32SocketFd);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 设置wwan网络DHCP服务相关参数
 * 输入参数: pstMoudleParam -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_SetDHCPServer(CELLULAR_MODULE_PARAM_S *pstMoudleParam)
{
    uint8 i = 0;
    sint32 s32Ret = -1;
    char szCmd[128] = {0};

	sprintf(szCmd, "ifconfig %s up", EC200U_NET_CARD);
	SAFE_System(szCmd, 5000);

	sleep(1);
	
    //DHCP客户端请求,分配IP地址
    sprintf(szCmd, "udhcpc -i %s &", EC200U_NET_CARD);
    SAFE_System(szCmd, 5000);
    //获取IP地址
    for(i=0; i<5; i++)
    {
        s32Ret = ec200u_GetIPAddr(pstMoudleParam->szIPAddr, sizeof(pstMoudleParam->szIPAddr));   
		if(SV_SUCCESS == s32Ret)
    	{
            return SV_SUCCESS;
    	}
        sleep_ms(2000);
    }
	
    sprintf(szCmd, "killall -9 udhcpc");
    SAFE_System(szCmd, 5000);

    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 计算校验值，ICMP用
 * 输入参数: addr -- 地址 len -- 数据长度
 * 输出参数: 无
 * 返回值  : 校验值
 * 注意    : 无
 *****************************************************************************/
uint16 ec200u_CalCheckSum(uint16 *addr, sint32 len)  
{  
    int nleft=len;  
    int sum=0;  
    unsigned short *w=addr;  
    unsigned short answer=0;  
      
    while(nleft > 1)  
    {  
        sum += *w++;  
        nleft -= 2;  
    }  
      
    if( nleft == 1)  
    {         
        *(unsigned char *)(&answer) = *(unsigned char *)w;  
        sum += answer;  
    }  
      
    sum = (sum >> 16) + (sum & 0xffff);  
    sum += (sum >> 16);  
    answer = ~sum;  
      
    return answer;  
}

/******************************************************************************
 * 函数功能: 测试ping指定IP地址速度
 * 输入参数: pszInterface --- 网卡名字
             pszIpaddr --- 目标IP地址
             u32Timeout --- 等待回包超时时间(ms)
 * 输出参数: pfSpeedTime --- 网速时间(ping包返回的延时ms)
 * 返回值  : SV_SUCCESS - 访问成功
             <0 - 访问失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_PingIpaddr(char *pszInterface, char *pszIpaddr, uint32 u32Timeout, float *pfSpeedTime)
{
    sint32 s32Ret = 0, i;
    sint32 sockfd;
    SV_BOOL bTimeout = SV_FALSE;
    float fElapse = 0.0;
    fd_set readfds;
    struct timeval tvTimeout, *ptvTime = NULL;
    struct timeval tvStart, tvEnd;
    struct ifreq stIfreq = {0};
    struct sockaddr_in addr;
    struct sockaddr_in from;
    struct icmp *icmp = NULL;
    struct ip *iph = NULL; 
    char sendpacket[4096];
    char recvpacket[4096];

    if (NULL == pszInterface || NULL == pszIpaddr || NULL == pfSpeedTime)
    {
        print_level(SV_ERROR, "param ptr is null.\n");
        return SV_FAILURE;
    }
    //ICMP套接字
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = inet_addr(pszIpaddr);
    sockfd = socket(AF_INET, SOCK_RAW, IPPROTO_ICMP);
    if (sockfd < 0)
    {
        print_level(SV_ERROR, "socket failed. [err=%#x]\n", errno);
        return -1;
    }
    //获取接收超时值
    tvTimeout.tv_sec = u32Timeout / 1000;
    tvTimeout.tv_usec = (u32Timeout % 1000) * 1000;
    s32Ret = setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &tvTimeout, sizeof(tvTimeout));
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "setsockopt failed. [err=%#x]\n", errno);
        close(sockfd);
        return -1;
    }
    //绑定接口
    strcpy(stIfreq.ifr_name, pszInterface);
    s32Ret = setsockopt(sockfd, SOL_SOCKET, SO_BINDTODEVICE, &stIfreq, sizeof(stIfreq));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "set socket option[SO_BINDTODEVICE] failed. [ifr: %s, err=%#x]\n", pszInterface, errno);
        close(sockfd);
        return -2;
    }
    //发送包
    icmp=(struct icmp*)sendpacket;
    icmp->icmp_type = ICMP_ECHO;
    icmp->icmp_code = 0;
    icmp->icmp_cksum = 0;
    icmp->icmp_seq = 0;
    icmp->icmp_id = 0;
    ptvTime = (struct timeval *)icmp->icmp_data;
    gettimeofday(ptvTime, NULL);
    gettimeofday(&tvStart, NULL);
    icmp->icmp_cksum = ec200u_CalCheckSum((unsigned short *)icmp,sizeof(struct icmp));
    s32Ret = sendto(sockfd, (char *)&sendpacket, sizeof(struct icmp), 0, (struct sockaddr *)&addr, sizeof(addr));
    if (s32Ret < 1)
    {
        print_level(SV_ERROR, "sendto failed. [err=%#x]\n", errno);
        close(sockfd);
        return -1;
    }
    //获取回包
    bTimeout = SV_FALSE;
    for (i = 0; i < 10; i++)
    {
    
        FD_ZERO(&readfds);
        FD_SET(sockfd, &readfds); 
        tvTimeout.tv_sec = u32Timeout / 1000;
        tvTimeout.tv_usec = (u32Timeout % 1000) * 1000;
        s32Ret = select(sockfd+1, &readfds, NULL, NULL, &tvTimeout);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "select failed. [err=%#x]\n", s32Ret);
            sleep_ms(10);
            continue;
        }
        else if (0 == s32Ret)
        {
            if(strcmp(pszInterface, EC200U_NET_CARD) != 0)
                print_level(SV_WARN, "[%s] ping ip:%s select failed. [err=%#x]\n", pszInterface, pszIpaddr, s32Ret);
            bTimeout = SV_TRUE;
            break;
        }

        memset(recvpacket, 0, sizeof(recvpacket));
        int fromlen = sizeof(from);
        s32Ret = recvfrom(sockfd, recvpacket, sizeof(recvpacket), 0, (struct sockaddr *)&from, (socklen_t *)&fromlen);
        if (s32Ret < 1)
        {
            print_level(SV_ERROR, "recvfrom failed. [err=%#x]\n", errno);
            continue;
        }

        char *from_ip = (char *)inet_ntoa(from.sin_addr);
        if (strcmp(from_ip, pszIpaddr) != 0)
        {
            print_level(SV_WARN, "not the same ping ip. real[%s] expect[%s]\n", from_ip, pszIpaddr);
            continue;
        }

        iph = (struct ip *)recvpacket;
        icmp= (struct icmp *)(recvpacket + (iph->ip_hl<<2));
        if (icmp->icmp_type == ICMP_ECHOREPLY && icmp->icmp_id == 0)
        {
            gettimeofday(&tvEnd, NULL);
            fElapse = (float)((tvEnd.tv_sec * 1000000 + tvEnd.tv_usec) - (tvStart.tv_sec * 1000000 + tvStart.tv_usec)) / 1000.0;
            //print_level(SV_DEBUG, "[%s] ping ip:%s, icmp->icmp_type:%d ,icmp->icmp_id:%d, time:%.3fms\n", pszInterface, pszIpaddr, icmp->icmp_type, icmp->icmp_id, fElapse);
            break;
        }
    }

    close(sockfd);
    if (i >= 10 || bTimeout)
    {
        if(strcmp(pszInterface, EC200U_NET_CARD) != 0)
            print_level(SV_WARN, "[%s] ping ip:%s, wait for recive packet timeout.\n", pszInterface, pszIpaddr);
        return -1;
    }

    *pfSpeedTime = fElapse;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: ping指定IP地址
 * 输入参数: pszInterface --- 网卡名字
             pszIpaddr --- 目标IP地址
             u32Timeout --- 等待回包超时时间(ms),设置只能是1000的整数倍
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 访问成功
             <0 - 访问失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_CmdPingIpaddr(char *pszInterface, char *pszIpaddr, uint32 u32Timeout)
{
    sint32 s32Ret = -1;
    uint8 u8Cmd[128] = {0};
    uint8 u8RecvBuf[256] = {0};
    uint8 *pu8Pos = NULL;
    uint32 u32TimeoutSec = 0;

    if (NULL == pszInterface || NULL == pszIpaddr)
    {
        print_level(SV_ERROR, "param ptr is null.\n");
        return SV_FAILURE;
    }

    u32TimeoutSec = u32Timeout / 1000;
    snprintf(u8Cmd, sizeof(u8Cmd), "ping %s -I %s -c 1 -w %d", pszIpaddr, pszInterface, u32TimeoutSec);
    //print_level(SV_INFO, "cmd:%s\n", u8Cmd);
    
    s32Ret = SAFE_System_Recv(u8Cmd, u8RecvBuf, sizeof(u8RecvBuf));
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", u8Cmd);
        return SV_FAILURE;
    }

    if (NULL == strstr(u8RecvBuf, "packet loss") )
    {
        print_level(SV_ERROR, "ping failed.\n", u8Cmd);
        return SV_FAILURE;
    }

    pu8Pos = strstr(u8RecvBuf, "100% packet loss");
    if (NULL != pu8Pos)
    {
        print_level(SV_ERROR, "ping failed.\n", u8Cmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 设置模块网络连接相关的参数，其功能相当于ifconfig
 * 输入参数: pstMoudleParam -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_Connect(CELLULAR_MODULE_PARAM_S *pstMoudleParam)
{
    uint8 i = 0;
    sint32 s32Ret = -1;
    float fSpeedTime = 0;
    sint8 s8Cmd[512] = {0};

    pstMoudleParam->eConStatus = CELL_CONNECTING;
    //获取信号强度
    s32Ret = ec200u_GetRxRSSI(pstMoudleParam->s32Fd, &(pstMoudleParam->u32Signal));
    if (99 == pstMoudleParam->u32Signal)
    {
        print_level(SV_WARN, "module signal is weakless...\n");
        sleep_ms(3000);
    }
    //设置电路交换和分组交换参数
    //s32Ret = ec200u_CSConnect(pstMoudleParam->s32Fd);

    s32Ret = ec200u_PSConnect(pstMoudleParam->s32Fd);
    if (SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "ec200u_PSConnect error\n");
        strcpy(pstMoudleParam->u8Describe, "ps connect fail");
        return SV_FAILURE;
    }

#if 0
    s32Ret = ec200u_PDPConnect(pstMoudleParam);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "pdpconnect error\n");
        return SV_FAILURE;
    }

    s32Ret = ec200u_Call(pstMoudleParam->s32Fd);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "call error\n");
        return SV_FAILURE;
    }
    
    //设置网络参数
    s32Ret = ec200u_SetDHCPServer(pstMoudleParam);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "set DHCP server error\n");
        return SV_FAILURE;
    }
#endif

	s32Ret = ec200u_EcmCall(pstMoudleParam->s32Fd);
	if(SV_SUCCESS != s32Ret )
	{
		print_level(SV_ERROR,"ecm call error!\n");
		return SV_FAILURE;
	}

	sleep(2);
	
	s32Ret = ec200u_SetDHCPServer(pstMoudleParam);
	if(SV_SUCCESS != s32Ret )
	{
		print_level(SV_ERROR,"udhcpc error!\n");
		return SV_FAILURE;
	}
	
    //检查网络通断
    for (i = 0; i < 3; i++)
    {
        s32Ret = ec200u_CmdPingIpaddr(EC200U_NET_CARD, "*******", 3000);
        if(SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, " eth1 ping address ******* success\n");
            strcpy(pstMoudleParam->u8Describe, "get ip success");
            pstMoudleParam->eConStatus = CELL_CONNETCTED;
            return SV_SUCCESS;
        }
        
        s32Ret = ec200u_CmdPingIpaddr(EC200U_NET_CARD, "*******", 3000);
        if (SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, " eth1 ping address ******* success\n");
            strcpy(pstMoudleParam->u8Describe, "get ip success");
            pstMoudleParam->eConStatus = CELL_CONNETCTED;
            return SV_SUCCESS;
        }
        else
        {
            pstMoudleParam->eConStatus = CELL_CONNECTING;
            sleep_ms(1000);
        }
    }
    
    strcpy(pstMoudleParam->u8Describe, "get ip fail");
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 更新模块参数;
 * 输入参数: pstMoudleParam -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_Update(CELLULAR_MODULE_PARAM_S *pstMoudleParam)
{
    sint32 s32Ret = -1;
    static sint64 s64OldTimeStamp = 0, s64OldPingTimeStamp = 0;
    sint64 s64CurTimeStamp = 0;
    SV_BOOL bSIMStat = SV_FALSE;
    float fSpeedTime = 0;
    static sint32 s32ErrCnt = 0;
    static sint32 s32TmpErrCnt = 0;

    s64CurTimeStamp = SAFE_GetTimeTick();
    if ((s64CurTimeStamp - s64OldTimeStamp > 3000) || (0 == s64OldTimeStamp))
    {
        s64OldTimeStamp = s64CurTimeStamp;
        s32Ret = ec200u_GetManufacturer(pstMoudleParam->s32Fd, pstMoudleParam->szManufacturer, sizeof(pstMoudleParam->szManufacturer));
        s32Ret = ec200u_GetVersion(pstMoudleParam->s32Fd, pstMoudleParam->szVersion, sizeof(pstMoudleParam->szVersion));
        s32Ret = ec200u_GetIMEI(pstMoudleParam->s32Fd, pstMoudleParam->szIMEI, sizeof(pstMoudleParam->szIMEI));
        s32Ret = ec200u_GetNetType(pstMoudleParam->s32Fd, pstMoudleParam->szNetType, sizeof(pstMoudleParam->szNetType));
        s32Ret = ec200u_GetIPAddr(pstMoudleParam->szIPAddr, sizeof(pstMoudleParam->szIPAddr));
        s32Ret = ec200u_GetICCID(pstMoudleParam->s32Fd, pstMoudleParam->szICCD, sizeof(pstMoudleParam->szICCD));
        //SIM卡错误时调整状态
        if (SV_FAILURE == s32Ret)
        {
            ec200u_CheckSIMStatus(pstMoudleParam->s32Fd, &bSIMStat);
            if(SV_TRUE == bSIMStat)
            {
                print_level(SV_ERROR, "SIM card error.\n");
                strcpy(pstMoudleParam->u8Describe, "SIM error");
                pstMoudleParam->enModuleStatus = CELL_STAT_SIM_ERR;
                return SV_FAILURE;
            }

        }
        s32Ret = ec200u_GetRxRSSI(pstMoudleParam->s32Fd, &(pstMoudleParam->u32Signal));
        s32Ret = ec200u_GetIMSI(pstMoudleParam->s32Fd, pstMoudleParam->szIMSI, sizeof(pstMoudleParam->szIMSI));
        s32Ret = ec200u_GetTemperature(pstMoudleParam->s32Fd, &(pstMoudleParam->s32Temperature));
        if (SV_SUCCESS == s32Ret)
        {
            s32TmpErrCnt = 0;
            print_level(SV_INFO, "Signal=%d, Temp=%d.\n", pstMoudleParam->u32Signal, pstMoudleParam->s32Temperature);
        }
        else
        {
            s32TmpErrCnt++;
        }

#if 0
        print_level(SV_DEBUG, "manufacture :%s ;version :%s ;IMEI :%s ;nettype :%s ;ICCID :%s;signal %u;IMSI %s;temperature %d\n",\
                    pstMoudleParam->szManufacturer,pstMoudleParam->szVersion,pstMoudleParam->szIMEI,\
                    pstMoudleParam->szNetType,pstMoudleParam->szICCD,pstMoudleParam->u32Signal,pstMoudleParam->szIMSI,pstMoudleParam->u32Temperature);
#endif
    }
    
    if ((s64CurTimeStamp - s64OldPingTimeStamp > 5000) || (0 == s64OldPingTimeStamp))
    {
        s64OldPingTimeStamp = s64CurTimeStamp;
        if (CELL_CONNETCTED == pstMoudleParam->eConStatus)
        {
            s32Ret = ec200u_CmdPingIpaddr(EC200U_NET_CARD, "*******", 3000);
            if (SV_SUCCESS == s32Ret)
            {
                s32ErrCnt = 0;
            }
            else
            {
                s32Ret = ec200u_CmdPingIpaddr(EC200U_NET_CARD, "*******", 3000);
                if (SV_SUCCESS == s32Ret)
                {
                    s32ErrCnt = 0;
                }
                else
                {
                    s32ErrCnt++;
                }
            }
        }    
    }

    if (s32ErrCnt > 3)
    {
        s32ErrCnt = 0;
        s32TmpErrCnt = 0;
        pstMoudleParam->eConStatus = CELL_DISCONNECT;
        pstMoudleParam->bConnInterrupt = SV_TRUE;
        pstMoudleParam->enModuleStatus = CELL_STAT_INIT;
        strcpy(pstMoudleParam->u8Describe, "ping fail");
    }
    
    if (s32TmpErrCnt > 3)
    {
        s32ErrCnt = 0;
        s32TmpErrCnt = 0;
        pstMoudleParam->eConStatus = CELL_DISCONNECT;
        pstMoudleParam->bException = SV_TRUE;
        pstMoudleParam->enModuleStatus = CELL_STAT_RESET;
        strcpy(pstMoudleParam->u8Describe, "Interface error");
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 配置参数
 * 输入参数: pstMoudleParam -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_Config(CELLULAR_MODULE_PARAM_S *pstMoudleParam)
{
    sint32 s32Ret = -1;
    sint8 s8Buf[EC200U_AT_BUFFER_SIZE] = {0};
    sint8 s8Cmd[512] = {0};
    
    snprintf(s8Cmd, 512, EC200U_AT_SET_APN, pstMoudleParam->stSetup.szApn, pstMoudleParam->stSetup.szUserName, pstMoudleParam->stSetup.szPassWd, 3);
    s32Ret = ec200u_SendAndRecv(pstMoudleParam->s32Fd, s8Cmd, s8Buf, 1);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_DEBUG, "%s error\n", s8Cmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 ec200u_GetGPRSMode(void)
{
    return SV_SUCCESS;
}

sint32 ec200u_GetBand(void)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 配置参数
 * 输入参数: pstMoudleParam -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ec200u_EcmCall(sint32 s32Fd)
{
	sint32 s32Status = -1;
    sint32 s32Ret = -1;
    char szBuf[EC200U_AT_BUFFER_SIZE] = {0};
    char *pc = NULL;
	
    s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_SET_QCFG, szBuf, 1);
    if(SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }

	s32Ret = ec200u_SendAndRecv(s32Fd, EC200U_AT_SET_QNETDEVCTL, szBuf, 1);
    if(SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }
	
	return SV_SUCCESS;

}
