#	WS-typemap.dat
#
#-------------------------------------------------------------------------------
#gSOAP XML Web services tools
#Copyright (C) 2004-2008, <PERSON>, Genivia Inc. All Rights Reserved.
#This software is released under one of the following two licenses:
#GPL or Genivia's license for commercial use.
#-------------------------------------------------------------------------------
#GPL license.
#
#This program is free software; you can redistribute it and/or modify it under
#the terms of the GNU General Public License as published by the Free Software
#Foundation; either version 2 of the License, or (at your option) any later
#version.
#
#This program is distributed in the hope that it will be useful, but WITHOUT ANY
#WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A
#PARTICULAR PURPOSE. See the GNU General Public License for more details.
#
#You should have received a copy of the GNU General Public License along with
#this program; if not, write to the Free Software Foundation, Inc., 59 Temple
#Place, Suite 330, Boston, MA 02111-1307 USA
#
#Author contact information:
#<EMAIL> / <EMAIL>
#-------------------------------------------------------------------------------
#A commercial use license is available from Genivia, Inc., <EMAIL>
#-------------------------------------------------------------------------------

d		= "http://schemas.xmlsoap.org/ws/2005/04/discovery"
dn		= "http://www.onvif.org/ver10/network/wsdl"
dnrd	= "http://www.onvif.org/ver10/network/wsdl/RemoteDiscoveryBinding"
dndl	= "http://www.onvif.org/ver10/network/wsdl/DiscoveryLookupBinding"
dare	= "http://www.onvif.org/ver20/analytics/wsdl/RuleEngineBinding"
daae	= "http://www.onvif.org/ver20/analytics/wsdl/AnalyticsEngineBinding"
depps	= "http://www.onvif.org/ver10/events/wsdl/PullPointSubscriptionBinding"
dee  	= "http://www.onvif.org/ver10/events/wsdl/EventBinding"
desm	= "http://www.onvif.org/ver10/events/wsdl/SubscriptionManagerBinding"
denf	= "http://www.onvif.org/ver10/events/wsdl/NotificationProducerBinding"
denc	= "http://www.onvif.org/ver10/events/wsdl/NotificationConsumerBinding"
depp	= "http://www.onvif.org/ver10/events/wsdl/PullPointBinding"
decpp	= "http://www.onvif.org/ver10/events/wsdl/CreatePullPointBinding"
depsm	= "http://www.onvif.org/ver10/events/wsdl/PausableSubscriptionManagerBinding"

tt		= "http://www.onvif.org/ver10/schema"
tds		= "http://www.onvif.org/ver10/device/wsdl"
trt		= "http://www.onvif.org/ver10/media/wsdl"
tr2     = "http://www.onvif.org/ver20/media/wsdl"
tev		= "http://www.onvif.org/ver10/events/wsdl"
ter		= "http://www.onvif.org/ver10/error"
tns1	= "http://www.onvif.org/ver10/topics"
tan		= "http://www.onvif.org/ver20/analytics/wsdl"
timg	= "http://www.onvif.org/ver20/imaging/wsdl"
tptz	= "http://www.onvif.org/ver20/ptz/wsdl"
tad		= "http://www.onvif.org/ver10/analyticsdevice/wsdl"
tls		= "http://www.onvif.org/ver10/display/wsdl"
trv		= "http://www.onvif.org/ver10/receiver/wsdl"
trc		= "http://www.onvif.org/ver10/recording/wsdl"
trp		= "http://www.onvif.org/ver10/replay/wsdl"
tse		= "http://www.onvif.org/ver10/search/wsdl"
tmd		= "http://www.onvif.org/ver10/deviceIO/wsdl"

wsdl	= "http://schemas.xmlsoap.org/wsdl/"
wsoap12	= "http://schemas.xmlsoap.org/wsdl/soap12/"
http	= "http://schemas.xmlsoap.org/wsdl/http/"
wstop	= "http://docs.oasis-open.org/wsn/t-1"
wsnt	= "http://docs.oasis-open.org/wsn/b-2"
xop		= "http://www.w3.org/2004/08/xop/include"
wsbf2	= "http://docs.oasis-open.org/wsrf/bf-2"
wsr2	= "http://docs.oasis-open.org/wsrf/r-2"



#	Bindings for WS-* protocols:

wsrp	= "http://schemas.xmlsoap.org/rp/"
wsen	= "http://schemas.xmlsoap.org/ws/2004/09/enumeration"
wsp	= "http://schemas.xmlsoap.org/ws/2004/09/policy"

#	wsa bindings

wsa	= <http://schemas.xmlsoap.org/ws/2004/08/addressing>
wsa3	= <http://schemas.xmlsoap.org/ws/2003/03/addressing>
wsa4	= <http://schemas.xmlsoap.org/ws/2004/03/addressing>
wsa5	= <http://www.w3.org/2005/08/addressing>

#	wsa:AttributedQName is a QName element extensible with attributes.
#	We redeclare it as a QName string, assuming we don't need attributes:

wsa__AttributedQName		= | _QName
wsa3__AttributedQName		= | _QName
wsa4__AttributedQName		= | _QName
wsa5__AttributedQNameType	= | _QName

#	wsa:AttributedURI is an anyURI element extensible with attributes.
#	We redeclare it as a regular string, assuming we don't need attributes:

wsa__AttributedURI	= | char*
wsa3__AttributedURI	= | char*
wsa4__AttributedURI	= | char*
wsa5__AttributedURIType	= | char*

#	wsa:ReplyAfterType is an nonnegativeInteger extensible with attributes.
#	We redeclare it here, assuming we don't need attributes:

wsa__ReplyAfterType			= | unsigned int
_wsa__ReplyAfter			= | unsigned int
wsa4__ReplyAfterType			= | unsigned int
_wsa4__ReplyAfter			= | unsigned int
wsa5__AttributedUnsignedLongType	= | ULONG64

#	wsa:IsReferenceParameter is a boolean, redefined here to avoid clashes:

_wsa5__IsReferenceParameter	= typedef enum _wsa5__IsReferenceParameter { wsa5__false, wsa5__true } _wsa5__IsReferenceParameter; | _wsa5__IsReferenceParameter;

#	wsse, wsu, ds, and xenc bindings

wsse	= <http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd>
wsu	= <http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd>
ds	= <http://www.w3.org/2000/09/xmldsig#>
xenc	= <http://www.w3.org/2001/04/xmlenc#>

wsse2	= <http://schemas.xmlsoap.org/ws/2002/12/secext>

#	wsrm bindings

wsrm	= <http://docs.oasis-open.org/ws-rx/wsrm/200702>
wsrm4	= <http://docs.oasis-open.org/wsrm/2004/06/ws-reliability-1.1.xsd>
ref     = <http://docs.oasis-open.org/wsrm/2004/06/reference-1.1.xsd>

wsrm__MessageNumberType		= | ULONG64
wsrm__DetailType		= | struct SOAP_ENV__Detail
_wsrm__UnsupportedElement	= | _QName | _QName
_wsrm__Identifier		= | char*
_wsrm__Address			= | char*
_wsrm__Expires			= #import "custom/duration.h" | xsd__duration

################################################################################
#
#	wsu redefinitions
#
################################################################################

_wsu__Id		= | char*
_wsu__Created		= | char*
_wsu__Expires		= | char*

wsu__AttributedDateTime	= | time_t
wsu__AttributedURI	= | char*

wsu__TimestampType	=

_wsu__Timestamp =\
typedef struct _wsu__Timestamp\n\
{	@char*	wsu__Id;	// use qualified form to enable signature\n\
	char*	Created;\n\
	char*	Expires;\n\
} _wsu__Timestamp;

################################################################################
#
#	wsse redefinitions
#
################################################################################

# wsse:AttributedString has a wsu:Id we don't need
wsse__AttributedString = | char*

# wsse:EncodedString has an EncodingType attribute we don't need for Nonce
wsse__EncodedString = | char*

# Don't use global wsse:Nonce
_wsse__Nonce =

# Don't use wsse:tUsage and global wsse:Usage
wsse__tUsage =
_wsse__Usage =

# Don't use wsse:PasswordString
wsse__PasswordString =

# But use wsse:Password element with similar content
_wsse__Password =\
typedef struct _wsse__Password\n\
{	char*					__item;\n\
	@char*					Type;\n\
} _wsse__Password;

# Don't use wsse:UsernameTokenType
wsse__UsernameTokenType =

# But use wsse:UsernameToken element with similar content
_wsse__UsernameToken =\
typedef struct _wsse__UsernameToken\n\
{	char*					Username;\n\
	struct _wsse__Password*			Password;\n\
	char*					Nonce;\n\
	char*					wsu__Created;\n\
	@char*					wsu__Id;\n\
} _wsse__UsernameToken;

# Don't use wsse:BinarySecurityTokenType
wsse__BinarySecurityTokenType =

# But use wsse:BinarySecurityToken element with similar content
_wsse__BinarySecurityToken =\
typedef struct _wsse__BinarySecurityToken\n\
{	char*					__item;\n\
	@char*					wsu__Id;\n\
	@char*					ValueType;\n\
	@char*					EncodingType;\n\
} _wsse__BinarySecurityToken;

# Don't use wsse:SecurityTokenReferenceType
wsse__SecurityTokenReferenceType =

# But use wsse:SecurityTokenReference element
_wsse__SecurityTokenReference =\
typedef struct _wsse__SecurityTokenReference\n\
{	struct _wsse__Reference*		Reference;\n\
	struct _wsse__KeyIdentifier*		KeyIdentifier;\n\
	struct _wsse__Embedded*			Embedded;\n\
	@char*					wsu__Id;\n\
	@char*					Usage;\n\
} _wsse__SecurityTokenReference;

# Don't use wsse:ReferenceType
wsse__ReferenceType =

# But use wsse:Reference element
_wsse__Reference =\
typedef struct _wsse__Reference\n\
{	@char*					URI;\n\
	@char*					ValueType;\n\
} _wsse__Reference;

# Don't use wsse:KeyIdentifierType
wsse__KeyIdentifierType =

# But use wsse:KeyIdentifier
_wsse__KeyIdentifier =\
typedef struct _wsse__KeyIdentifier\n\
{	char*					__item;\n\
	@char*					wsu__Id;\n\
	@char*					ValueType;\n\
	@char*					EncodingType;\n\
} _wsse__KeyIdentifier;

# Don't use wsse:EmbeddedType
wsse__EmbeddedType =

# But use wsse:KeyIdentifier
_wsse__Embedded =\
typedef struct _wsse__Embedded\n\
{	/* Extensible with embedded tokens and assertions */\n\
	@char*					wsu__Id;\n\
	@char*					ValueType;\n\
} _wsse__Embedded;

# Don't use wsse:TransformationParametersType
wsse__TransformationParametersType =

# Don't use wsse:TransformationParameters
_wsse__TransformationParameters =

# Don't use wsse:SecurityHeaderType
wsse__SecurityHeaderType =

# But use wsse:Security element with additional content
_wsse__Security =\
#import "xenc.h"\n\
typedef struct _wsse__Security\n\
{	struct _wsu__Timestamp*			wsu__Timestamp;\n\
	struct _wsse__UsernameToken*		UsernameToken;\n\
	struct _wsse__BinarySecurityToken*	BinarySecurityToken;\n\
	struct xenc__EncryptedKeyType*		xenc__EncryptedKey;\n\
	struct _xenc__ReferenceList*		xenc__ReferenceList;\n\
	struct ds__SignatureType*		ds__Signature;\n\
	@char*					SOAP_ENV__actor;\n\
	@char*					SOAP_ENV__role;\n\
} _wsse__Security;

################################################################################
#
#	ds redefinitions
#
################################################################################

ds__CryptoBinary = | char*

ds__DigestValueType = | char*

ds__HMACOutputLengthType = | int

ds__TransformType = #import "c14n.h"\n\
typedef struct ds__TransformType\n\
{	_c14n__InclusiveNamespaces*		c14n__InclusiveNamespaces;\n\
        _XML					__any;\n\
	@char*					Algorithm;\n\
} ds__TransformType, _ds__Transform;

ds__KeyInfoType =\
typedef struct ds__KeyInfoType\n\
{	char*					KeyName;\n\
	struct ds__KeyValueType*		KeyValue;\n\
	struct ds__RetrievalMethodType*		RetrievalMethod;\n\
	struct ds__X509DataType*		X509Data;\n\
	struct _wsse__SecurityTokenReference*	wsse__SecurityTokenReference;\n\
	@char*					Id;\n\
} ds__KeyInfoType, _ds__KeyInfo;

ds__SignatureType =\
typedef struct ds__SignatureType\n\
{	struct ds__SignedInfoType*		SignedInfo;\n\
	char*					SignatureValue;\n\
	struct ds__KeyInfoType*			KeyInfo;\n\
	@char*					Id;\n\
} ds__SignatureType, _ds__Signature;

# Add c14n:InclusiveNamespaces element to ds:CanonicalizationMethodType
ds__CanonicalizationMethodType = $\
    _c14n__InclusiveNamespaces*		c14n__InclusiveNamespaces;

# ds:SignatureValueType has an Id we don't need
ds__SignatureValueType =

ds__ObjectType =

ds__ManifestType =

ds__SignaturePropertyType =

ds__SignaturePropertiesType =

ds__PGPDataType =

ds__SPKIDataType =

################################################################################
#
#	xenc redefinitions
#
################################################################################

xenc__EncryptionPropertyType =\
typedef struct xenc__EncryptionPropertyType\n\
{	@char*					Target;\n\
	@char*					Id;\n\
} xenc__EncryptionPropertyType;

xenc__KeySizeType = | int

################################################################################
#
#	Common definitions
#
################################################################################

#	Use regular char* strings for ID

xsd__ID			= | char*

#	Use regular char* strings for NCName

xsd__NCName		= | char*

#	Use regular char* strings for anyURI

xsd__anyURI		= | char*

#	Use regular char* strings for xsd:duration

xsd__duration		= | char*

#	Use built-in gSOAP _QName

xsd__QName		= | _QName | _QName

#	Map xsd:integer to int

xsd__integer		= | int

#	Map xsd:nonNegativeInteger to unsigned int

xsd__nonNegativeInteger	= | unsigned int

#	Map xsd:base64Binary to char* and manipulate base64 data internally

xsd__base64Binary	= | char*

#	Map xsd:boolean to xsd__boolean_ to avoid redefs

xsd__boolean		= enum xsd__boolean_ { _false, _true }; | enum xsd__boolean_

#	Redeclare wsrp:action_t

wsrp__action_USCOREt	= | char*

#	Redeclare wsrp:to_t

wsrp__to_USCOREt	= | char*

#	Redeclare wsrp:from_t

wsrp__from_USCOREt	= | char*

#	Redeclare wsrp:id_t

wsrp__id_USCOREt	= | char*

#	Redeclare wsrp:relatesTo_t

wsrp__relatesTo_USCOREt	= | char*

#	Redeclare wsrp:faultcode_t

wsrp__faultcode_USCOREt	= | char*

#	Redeclare wsrp:faultreason_t

wsrp__faultreason_USCOREt = | char*

# 解决：PullMessages收不到事件通知
_wsnt__NotificationMessageHolderType_Message = $ struct _tt__Message* tt__Message;

# 解决：CreatePullPointSubscription无法订阅感兴趣的主题
wsnt__FilterType = $ struct wsnt__TopicExpressionType* TopicExpression;

# 解决：GetEventProperties无法解析TopicSet字段
wstop__TopicSetType = $ _XML __mixed;
