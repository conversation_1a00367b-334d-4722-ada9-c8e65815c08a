#include <stdio.h>
#include <stdlib.h>
#include <stddef.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/fcntl.h>
#include <sys/un.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <ctype.h>

#include "common.h"
#include "print.h"
#include "op.h"
#include "msg.h"
#include "config.h"
#include "CUnit.h"
#include "cJSON.H"
#include "adas.h"
#include "alarm.h"
#include "unsocket.h"
#include "media_sem.h"
#include "alglib.h"

int g_s32MediaBufFd = -1;     // 通道的Media Buffer的文件描述符

sint32 ALG_Calculate_Lock(){return 0;}
sint32 ALG_Calculate_unLock(){return 0;}

/***************************************************************
*-# 用例编号: itest_ADAS_Init_001
*-# 测试标题: 
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_ADAS_Init_001()
{
    sint32 s32Ret = 0;
    ADAS_CFG_PARAM_S stInitParam = {0};

    s32Ret = ADAS_Init(&stInitParam);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    sleep_ms(1000);

    s32Ret = ADAS_Fini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_ADAS_Start_001
*-# 测试标题: 
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_ADAS_Start_001()
{
    sint32 s32Ret = 0;
    sint32 chChoice = 0;
    ADAS_CFG_PARAM_S stInitParam = {0};
    CFG_ALG_PARAM stAlgParam = {0};

    s32Ret = CONFIG_GetAlgParam(&stAlgParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetAlgParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    stInitParam.s32MediaBufFd = g_s32MediaBufFd;
    stInitParam.stAlgParam.stAlgCh1.stAdasParam.bFcwEnable = SV_TRUE;
    stInitParam.stAlgParam.stAlgCh1.stAdasParam.bLdwEnable = SV_TRUE;
    stInitParam.stAlgParam.stAlgCh1.stAdasParam.bPdsEnable = SV_TRUE;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.bPdTestMode = SV_FALSE;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.enRoiStyle = CFG_PDROI_BOTTOM;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[0].dX = 0.4;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[0].dY = 0.4;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[1].dX = 0.3;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[1].dY = 0.6;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[2].dX = 0.2;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[2].dY = 0.8;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[3].dX = 0.1;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[3].dY = 1.0;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[4].dX = 0.6;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[4].dY = 0.4;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[5].dX = 0.7;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[5].dY = 0.6;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[6].dX = 0.8;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[6].dY = 0.8;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[7].dX = 0.9;
    stInitParam.stAlgParam.stAlgCh1.stPdsParam.astPdCalibrationPoints[7].dY = 1.0;
    stInitParam.stAlgParam = stAlgParam;
    s32Ret = ADAS_Init(&stInitParam);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = ADAS_Start();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    sleep_ms(1000);
    while (1)
    {
        chChoice = toupper(getchar());
        if (chChoice == 'Q')
        {
            break;
        }
    }

    s32Ret = ADAS_Stop();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = ADAS_Fini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

int si_adas_test()
{
    sint32 s32Ret = 0, i;
    sint32 s32SocketFd = -1;
    SocketPacket stSocketPkt = {0};
    fd_set read_fds, write_fds;
    struct timeval timeout;
    ALARM_CFG_PARAM_S stAlarmParam = {0};
    char *pszConfigFile = CONFIG_XML;
    char *pszConfigBak1 = CONFIG_BAK1;
    char *pszConfigBak2 = CONFIG_BAK2;
    char *pszConfigDefault = CONFIG_DEFAULT;

    if (g_s32MediaBufFd > 0)
    {
        return 0;
    }

    s32Ret = CONFIG_Init(pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    stAlarmParam.enLanguage = LANG_CN;
    ALARM_Init(&stAlarmParam);
    for (i = 0; i < 5; i++)
    {
        s32SocketFd = cli_connect(CS_PATH_CAM_STREAM, 'c');
        if (s32SocketFd > 0)
        {
            break;
        }
        print_level(SV_WARN, "cli_connect %s failed.\n", CS_PATH_CAM_STREAM);
        sleep_ms(1000);
    }
    if (i >= 5)
    {
        print_level(SV_ERROR, "wait for cli_connect %s timeout.\n", CS_PATH_CAM_STREAM);
        return -1;
    }

    print_level(SV_INFO, "cli_connect successful. fd:%d\n", s32SocketFd);
    FD_ZERO(&write_fds);
    FD_SET(s32SocketFd, &write_fds);
    timeout.tv_sec=3;
	timeout.tv_usec=0;
	s32Ret = select(s32SocketFd + 1, NULL, &write_fds, NULL, &timeout);
	if (s32Ret <= 0)
	{
	    print_level(SV_WARN, "select write failed. [err=%d]\n", s32Ret);
	    close(s32SocketFd);
	    return -1;
	}

    stSocketPkt.header.startode = MSG_STARTCODE;
    stSocketPkt.header.opcode = SOCKET_OP_GET_FD;
    s32Ret = unsock_write(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "unsock_write failed. [err=%#x]\n", s32Ret);
        close(s32SocketFd);
        return -1;
    }

    print_level(SV_INFO, "unsock_write successful. fd:%d\n", s32SocketFd);
    FD_ZERO(&read_fds);
    for (i = 0; i < 5; i++)
    {
        timeout.tv_sec=1;
		timeout.tv_usec=0;
		FD_SET(s32SocketFd, &read_fds);
		s32Ret = select(s32SocketFd + 1, &read_fds, NULL, NULL, &timeout);
		if (s32Ret <= 0)
		{
		    print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
		    continue;
		}
		
        s32Ret = unsock_recvfd(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt), &g_s32MediaBufFd);
        if (s32Ret < 0 || g_s32MediaBufFd < 0)
        {
            print_level(SV_ERROR, "unsock_write failed. [err=%#x, fd=%d]\n", s32Ret, g_s32MediaBufFd);
            close(s32SocketFd);
            return -1;
        }
        else
        {
            break;
        }
    }
    if (i >= 5)
    {
        print_level(SV_ERROR, "wait for unsock_recvfd timeout.\n");
        close(s32SocketFd);
        return -1;
    }

    print_level(SV_INFO, "got media buffer fd:%d\n", g_s32MediaBufFd);
    close(s32SocketFd);
    
    return 0;
}

void Suite_adas_API()
{
    CU_pSuite pSuite;

    if (NULL == CU_get_registry())
    {
        printf("CUnit not registry!\n");
        return;
    }

    pSuite = CU_ADD_SUITE(si_adas_test, NULL);
    CU_ADD_TEST(pSuite, itest_ADAS_Init_001);
    CU_ADD_TEST(pSuite, itest_ADAS_Start_001);
}

void AddTests_itest_adas()
{
    Suite_adas_API();
}


