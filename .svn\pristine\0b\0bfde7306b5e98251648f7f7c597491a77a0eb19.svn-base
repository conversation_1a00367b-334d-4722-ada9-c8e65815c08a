CPPFLAGS = -Wall -std=c++11 -O2
CPPFLAGS += -I./include  -I$(shell pwd)
AR_FLAGS = -rc

HEADER_DIR = ../include
LIB_DIR = ../lib

TARGET_LIB  = libsomeip.a
EXTERNAL_HEADERS = data_queue.hpp deserializer.hpp media.h message_control.hpp message_header.hpp serializable.hpp \
					serializer.hpp someip.hpp type_define.hpp

SRC_DIRS := . $(shell find ./src -type d)

SRCS := $(foreach dir, $(SRC_DIRS), $(wildcard $(dir)/*.cpp))

OBJS := $(SRCS:.cpp=.o)

all: $(TARGET_LIB)

$(TARGET_LIB): $(OBJS)
	$(AR) $(AR_FLAGS) $@ $^
	mv $(TARGET_LIB) $(LIB_DIR)
	for file in $(EXTERNAL_HEADERS); do \
		cp include/$$file $(HEADER_DIR)/$$file; \
	done

%.o: %.cpp
	$(CXX) $(CPPFLAGS) -c -o $@ $<

clean:
	rm -f $(OBJS) $(TARGET_LIB)