#! /bin/sh

# Improve CPU priority
io -4 0xff110008 0x303

# Improve USB Host Disconnect Detection Value
#io -4 0xff3e0060 0x00000000
#io -4 0xff3e0068 0x000000e8
#io -4 0xff3e0064 0x000000e0

/root/ko/insmod_ko.sh
insmod /root/ko/extdrv/mtdex.ko

umount -lf /mnt/sdcard/ 1>/dev/null 2>/dev/null
rm /mnt/* -r
mount -t tmpfs -o size=512k tmpfs /mnt/
mkdir /mnt/sdcard
mkdir /mnt/udisk
cp -rf /var /tmp/
mount -t tmpfs -o size=10M tmpfs /var/
sleep 0.1
mv /tmp/var/* /var/
touch /var/mainStream
touch /var/subStream
touch /var/audStream
mkdir /var/nfs
mkdir /var/snap
mkdir /var/warn
mkdir /var/info
mkdir /var/lock
mkdir /var/log
mkdir /var/log/uploaded 
mkdir /var/run
mkdir /var/mounting

export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:.:/usr/lib/

cp /etc/config.xml /var/config.xml
tar -zxf /root/webui.tar.gz -C /var/

fw_printenv | grep hardwareVersion
if [ $? -ne 0 ];then
	fw_setenv hardwareVersion $(cat /etc/hardwareVersion)
fi

if [ ! -f /etc/serialNumber ];then
	telnetd &
fi

/root/mcu_wtd.sh >> /dev/null &

/etc/init.d/S49alsa start
/root/gpio_enable.sh
/root/ipsys >> /dev/null &
#sleep 3
/root/alg >> /dev/null &
/root/wtd.sh >> /dev/null &
/root/update.sh &
/root/sys_wtd.sh >> /dev/null &
