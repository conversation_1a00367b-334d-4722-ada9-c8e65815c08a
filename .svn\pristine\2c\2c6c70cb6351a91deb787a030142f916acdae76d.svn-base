
TARGET_LIB  = libhttp.a

include ../../../Makefile.param

SRCS = $(wildcard ./*.c)
FILES = $(wildcard ./*.cpp) 

ifneq ($(PLATFORM),$(findstring $(PLATFORM), RV1126 RV1126_REBUILD))
SRCPPS += $(filter-out ./AES.cpp, $(FILES))
else
SRCPPS += $(FILES)
endif

ifneq ($(BOARD),$(findstring $(BOARD), ADA47V1 ADA900V1 HDW845V1 ADA32N1 ADA32NSDK ADA32V3 ADA32E1 ADA32ESDK ADA32C4 ADA32IR))
CFLAGS += -DMG_ENABLE_OPENSSL=1 -DMG_ENABLE_MD5=1 -I$(INC_PATH)/opensslv1.0
else
ifeq ($(BOARD),$(findstring $(BOARD),ADA47V1 HDW845V1 ADA32N1 ADA32NSDK ADA32V3 ADA32C4 ADA32IR))
CFLAGS += -DMG_ENABLE_OPENSSL=1 -DMG_ENABLE_MD5=1 -I$(INC_PATH)/opensslv1.1
else
CFLAGS += -DMG_ENABLE_MD5=1 -I$(INC_PATH)/opensslv1.1
endif
endif

-mfloat-abi=hard

CFLAGS += -std=gnu99 -std=gnu++11 -I$(INC_PATH)/cjson
CPPFLAGS = $(CFLAGS)
_TARGET_DIR_ = $(TOP_LIB)

include $(AUTO_DEP_MK) 
