#include <dirent.h>
#include <stdlib.h>
#include <fnmatch.h>

#include "cms_filePrivateUpload.h"
#include "cms_offlineInfo.h"

#define FILETRANSFER_BUFFNUM 163840
#define FILEPATH_VIDEO  "/mnt/sdcard/alarm/"
#define FILEPATH_PICTURE  "/mnt/sdcard/picture/"

#include <map>
#include "../../src/recorder/inc/r_common.h"
using namespace recorder;
extern sint32 GetCurPosFileList(REC_TYPE_E enType, multimap<uint64, REC_FILE_S> **fileList);


/******************************************************************************
 * 函数功能: 扫描全部存储设备的录像文件生成列表信息
 * 输入参数: pstStorageInfo -- 存储信息
 * 输出参数: 文件列表信息
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 scanRecorderFile(UPLOAD_FILE_OPTS_E enUploadOpts, list<SV_STORAGE_FILE_PATH_S>& fileList, SV_BOOL bClear)
{
    sint32 s32Ret = 0, i = 0;
    DIR *pDirRoot = NULL;
    DIR *pDirDate = NULL;
    DIR *pDirHour = NULL;
    DIR *pDirMinute = NULL;
    struct dirent *pstDirentRoot = NULL;
    struct dirent *pstDirentDate = NULL;
    struct dirent *pstDirentHour = NULL;
    struct dirent *pstDirentMinute = NULL;
    char szDirDate[64];
    char szDirHour[64];
    char szDirMinute[64];
    char szFilePath[256];
	SV_STORAGE_FILE_PATH_S stFilePath = {0};
	REC_FILE_S stFileInfo = {0};
	SV_MEDIA_FILE_NAME_PARAMS_ST stFileNameParams;
    std::multimap<uint64, REC_FILE_S> *pstFileMap;
    std::multimap<uint64, REC_FILE_S>::iterator map_itor;
    std::multimap<uint64, REC_FILE_S> *pstFileMapPic;
    std::multimap<uint64, REC_FILE_S>::iterator map_itor_pic;
	char szFilter[114]={0};
    char szFileName[256] = {0};
	uint32 u32Flag;
    char *pszFileName = NULL;
    uint32 u32VideoSize = 0, u32PicSize = 0, u32Total = 0, u32Index = 0;
    SV_BOOL bOnly = SV_TRUE;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2)  || defined(BOARD_ADA32C4))
    switch (enUploadOpts)
    {
        case UPLOAD_FILE_ALARM_VIDEO:
            s32Ret = GetCurPosFileList(REC_TYPE_ALARM, &pstFileMap);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_ALARM);
                return SV_FAILURE;
            }
            u32VideoSize = (*pstFileMap).size();
            break;

        case UPLOAD_FILE_ALARM_PICTURE:
            s32Ret = GetCurPosFileList(REC_TYPE_PIC, &pstFileMap);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_PIC);
                return SV_FAILURE;
            }
            u32PicSize = (*pstFileMap).size();
            break;

        case UPLOAD_FILE_ALARM_ALL:
            bOnly = SV_FALSE;
            s32Ret = GetCurPosFileList(REC_TYPE_ALARM, &pstFileMap);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_ALARM);
                return SV_FAILURE;
            }
            u32VideoSize = (*pstFileMap).size();

            s32Ret = GetCurPosFileList(REC_TYPE_PIC, &pstFileMapPic);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_PIC);
                return SV_FAILURE;
            }
            u32PicSize = (*pstFileMapPic).size();
            break;

        default:
            break;
    }

    u32Total = u32VideoSize + u32PicSize;
    print_level(SV_INFO, "get tatol file list size: %d + %d = %d\n", u32VideoSize, u32PicSize, u32Total);

    if (bClear)
    {
        fileList.clear();
    }

    if (0 == u32Total)
    {
        goto exit;
    }

    if (bOnly)
    {
        for (map_itor = (*pstFileMap).begin(); map_itor != (*pstFileMap).end(); map_itor++)
    	{
    		stFileInfo = ((*map_itor).second);
            pszFileName = strrchr(stFileInfo.name.data(), '/');
            if (NULL != pszFileName)
            {
                memset(szFileName, 0, sizeof(szFileName));
                memcpy(szFileName, pszFileName+1, strlen(pszFileName)-1);
            }
            else
            {
                continue;
            }

            sscanf(szFileName, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
    					,&stFileNameParams.s32Year\
    					,&stFileNameParams.s32Month\
    					,&stFileNameParams.s32Day\
    					,&stFileNameParams.s32Hour\
    					,&stFileNameParams.s32Minute\
    					,&stFileNameParams.s32Second\
    					,&stFileNameParams.cEventType\
    					,&stFileNameParams.s32Duration\
    					,&stFileNameParams.s32Size\
    					,&stFileNameParams.s32Width\
    					,&stFileNameParams.s32Height\
    					,&stFileNameParams.s32FrameRate\
    					,&stFileNameParams.s32BitRate\
    					,&stFileNameParams.cPlateNum\
    					,&stFileNameParams.s32ChNum\
    					,&stFileNameParams.u64DeviceID\
    					,&stFileNameParams.u8Flag\
    					,&stFileNameParams.s32Msec\
    					,&stFileNameParams.s32PreRecordMsec\
    					,&stFileNameParams.cVersionNum\
    					,&stFileNameParams.cCustomerNum\
    					,&stFileNameParams.cTimeZone\
    					,&stFileNameParams.cDST\
    					,&stFileNameParams.cFileType);

    		if (stFileNameParams.s32Size != 0 && stFileNameParams.u8Flag == 2)
    		{
                if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI)
                    && strncmp(stFileNameParams.cEventType, "FT", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "YW", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "AF", 2) != 0)
                {
                    continue;
                }

    			strcpy(stFilePath.szFilePath, stFileInfo.name.data());
    			stFilePath.u32Duration = stFileInfo.duration;
    			printf("file path: %s\n", stFilePath.szFilePath);
    			fileList.push_back(stFilePath);
            }
        }
    }
    else
    {
        map_itor = (*pstFileMap).begin();
        map_itor_pic = (*pstFileMapPic).begin();
        while (0 != u32Total)
        {
            if (map_itor == (*pstFileMap).end() && map_itor_pic == (*pstFileMapPic).end())
            {
                break;
            }

            if (u32Index++ % 2 == 0)
            {
                if (map_itor != (*pstFileMap).end())
                {
                    stFileInfo = ((*map_itor).second);
                    map_itor++;
                }
                else
                {
                    continue;
                }
            }
            else
            {
                if (map_itor_pic != (*pstFileMapPic).end())
                {
                    stFileInfo = ((*map_itor_pic).second);
                    map_itor_pic++;
                }
                else
                {
                    continue;
                }
            }

            pszFileName = strrchr(stFileInfo.name.data(), '/');
            if (NULL != pszFileName)
            {
                memset(szFileName, 0, sizeof(szFileName));
                memcpy(szFileName, pszFileName+1, strlen(pszFileName)-1);
            }
            else
            {
                continue;
            }

            sscanf(szFileName, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
    					,&stFileNameParams.s32Year\
    					,&stFileNameParams.s32Month\
    					,&stFileNameParams.s32Day\
    					,&stFileNameParams.s32Hour\
    					,&stFileNameParams.s32Minute\
    					,&stFileNameParams.s32Second\
    					,&stFileNameParams.cEventType\
    					,&stFileNameParams.s32Duration\
    					,&stFileNameParams.s32Size\
    					,&stFileNameParams.s32Width\
    					,&stFileNameParams.s32Height\
    					,&stFileNameParams.s32FrameRate\
    					,&stFileNameParams.s32BitRate\
    					,&stFileNameParams.cPlateNum\
    					,&stFileNameParams.s32ChNum\
    					,&stFileNameParams.u64DeviceID\
    					,&stFileNameParams.u8Flag\
    					,&stFileNameParams.s32Msec\
    					,&stFileNameParams.s32PreRecordMsec\
    					,&stFileNameParams.cVersionNum\
    					,&stFileNameParams.cCustomerNum\
    					,&stFileNameParams.cTimeZone\
    					,&stFileNameParams.cDST\
    					,&stFileNameParams.cFileType);

    		if (stFileNameParams.s32Size != 0 && stFileNameParams.u8Flag == 2)
    		{
                if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI)
                    && strncmp(stFileNameParams.cEventType, "FT", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "YW", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "AF", 2) != 0)
                {
                    continue;
                }

    			strcpy(stFilePath.szFilePath, stFileInfo.name.data());
    			stFilePath.u32Duration = stFileInfo.duration;
    			printf("file path: %s\n", stFilePath.szFilePath);
    			fileList.push_back(stFilePath);
            }
        }
    }

exit:;
    print_level(SV_DEBUG, "scan recorder file success! need to upload list size: %d\n", fileList.size());
#endif
    return SV_SUCCESS;
}

SV_NETWORK_FILEPRIVATEUPLOAD::SV_NETWORK_FILEPRIVATEUPLOAD(SV_NETWORK_DVRINFO* pTempinfo,
				SV_NETWORK_STATE* pTempState, SV_NETWORK_PROTOCOL* pTempProtocal, SV_NETWORK_LINK* pTempLinkControl)
 	:pDvrinfo(pTempinfo),pState(pTempState),pProtocol(pTempProtocal),pLinkControl(pTempLinkControl)
{
	bRegister = SV_FALSE;
	u32Port = 9093;
	u64SessionId = 0;
	pLink = new SV_NETWORK_LINK();

	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		stFileupLoadArry[i].s32Fd = -1;
	}

	clearArry();
    m_enUploadFileOpts = pDvrinfo->getUploadFileType();
}


SV_NETWORK_FILEPRIVATEUPLOAD::~SV_NETWORK_FILEPRIVATEUPLOAD()
{
	//delete pLink;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setPort( uint32 u32Temp )
{
	u32Port = u32Temp;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setSessionId( uint64 u64Id)
{
	u64SessionId = u64Id;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isArryEmpty()
{
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( stFileupLoadArry[i].u8UploadStatus != STATUS_NOFILE )
		{
			return SV_FALSE;
		}
	}

	return SV_TRUE;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isArryFull()
{
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( stFileupLoadArry[i].u8UploadStatus == STATUS_NOFILE )
		{
			return SV_FALSE;
		}
	}

	return SV_TRUE;

}

void SV_NETWORK_FILEPRIVATEUPLOAD::clearArry()
{
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		stFileupLoadArry[i].u8UploadStatus = STATUS_NOFILE;
		if(stFileupLoadArry[i].s32Fd > 0)
		{
			close(stFileupLoadArry[i].s32Fd);
		}
		stFileupLoadArry[i].s32Fd = SV_FAILURE;
		deleSessionID(stFileupLoadArry[i].u32FileSessionId);
	}
}

uint8 SV_NETWORK_FILEPRIVATEUPLOAD::getAccessIndex()
{
	for (uint8 i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if (stFileupLoadArry[i].u8UploadStatus == STATUS_NOFILE)
		{
			return i;
		}
	}

    return SV_FAILURE;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::getFileIndex( char *pFileName)
{
	for(uint8 i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( strstr(stFileupLoadArry[i].szFileName, pFileName) != SV_NULL)
		{
			return i;
		}
	}

	print_level(SV_DEBUG,"Can not find index filename:%s\n", pFileName);
	return SV_FAILURE;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::addFile2Arry(SKDvrPrepareToUploadFileRsp* pRsp, char *pFileName)
{
	sint32 s32Index = -1;
	char szFullPath[STORAGE_FULLPATH_LEN] = {0};

	s32Index = getFileIndex(pFileName);
	if(s32Index < 0)
	{
        print_level(SV_ERROR, "not found file: %s in array.\n", pFileName);
		return SV_FAILURE;
	}

	//获取录像的目录，需要实现
	if( SV_COMMON_getAlarmFileDir(m_enUploadFileOpts, pFileName, szFullPath) != SV_SUCCESS )
	{
		print_level(SV_DEBUG,"SV_DVR_STORAGE_GetFileDirctory fail [%s]\n", pFileName);
		return SV_FAILURE;
	}
	sprintf(stFileupLoadArry[s32Index].szFullPath, "%s", szFullPath);

	print_level(SV_DEBUG,"add full path:%s\n", stFileupLoadArry[s32Index].szFullPath);
	if( !SV_DVR_COMMON_IsFileExist(stFileupLoadArry[s32Index].szFullPath) )
	{
		print_level(SV_ERROR,"Not exit!\n");
        deleIndex(s32Index);
        removeFileFromList(stFileupLoadArry[s32Index].szFullPath);
		return SV_FAILURE;
	}

	if(stFileupLoadArry[s32Index].s32Fd < 0)
	{
		stFileupLoadArry[s32Index].u32FileOffest = pRsp->uiUploadedSize;
		stFileupLoadArry[s32Index].u8TimeOutCnt = 0;
		stFileupLoadArry[s32Index].u32FileSeq = 0;
		stFileupLoadArry[s32Index].u32RecFileSeq = 0;
		stFileupLoadArry[s32Index].u32FileSessionId = pRsp->uiSessionId;
		stFileupLoadArry[s32Index].bSendEnd = SV_FALSE;
		stFileupLoadArry[s32Index].s32Fd = open(stFileupLoadArry[s32Index].szFullPath, O_RDWR);
        if (stFileupLoadArry[s32Index].s32Fd <= 0)
        {
            print_level(SV_ERROR, "open file: %s failed.\n", stFileupLoadArry[s32Index].szFullPath);
            deleIndex(s32Index);
            return SV_FAILURE;
        }
		SV_DVR_COMMON_CloseEvec(stFileupLoadArry[s32Index].s32Fd);
		lseek(stFileupLoadArry[s32Index].s32Fd, stFileupLoadArry[s32Index].u32FileOffest, SEEK_SET);
		stFileupLoadArry[s32Index].u8UploadStatus = STATUS_UPLOADING;
		stFileupLoadArry[s32Index].s32PrintCnt = 0;
	}
	else
	{
		print_level(SV_WARN,"File:%s has been opened!\n", stFileupLoadArry[s32Index].szFullPath);
	}

	return SV_SUCCESS;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setStatusPrepare(uint8 u8Index, char *pFileName, uint8 u8Type, uint64 u64FileSize)
{
	stFileupLoadArry[u8Index].u8UploadStatus = STATUS_PREPARE;
	//print_level(SV_DEBUG,"szFullPath:%s\n", pFileName);
	//strcpy(stFileupLoadArry[u8Index].szFullPath, pFileName);
	print_level(SV_DEBUG,"u8Index:%d szFileName:%s\n", u8Index, pFileName);
	strcpy(stFileupLoadArry[u8Index].szFileName, pFileName);
	stFileupLoadArry[u8Index].u8FileType = u8Type;
	stFileupLoadArry[u8Index].u64FileSize = u64FileSize;
	stFileupLoadArry[u8Index].s32PrintCnt = 0;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::sendRegsiter()
{
	SK_HEADER stHeader;
	SKConnectFileSaveServerReq stConnect;
	stConnect.ullGlobalSessionId = u64SessionId;

	pProtocol->createHeader(&stHeader, SK_DVR_TO_CMS_SERVER_CONNECT_FILE_SAVE_SERVER_REQ,
		sizeof(SKConnectFileSaveServerReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&stHeader, (char *)&stConnect, sizeof(SKConnectFileSaveServerReq));

	print_level(SV_DEBUG,"send register u64SessionId:%llu!\n", u64SessionId);

}


uint64 SV_NETWORK_FILEPRIVATEUPLOAD::sendFileInfo(const char *szFilePath)
{
	char szFileName[128] = {0};
	SK_HEADER stHeader;
	SKDvrPrepareToUploadFileReq stUploadFile;

	stUploadFile.uiFileLen = SV_COMMON_getFileSize(szFilePath);
	SV_COMMON_cutOutName((const char *)szFilePath, szFileName);

	cJSON *root;
	char *out;
	root=cJSON_CreateObject();
	cJSON_AddStringToObject(root, "fn", szFileName);

	out=cJSON_Print(root);
	//print_level(SV_DEBUG,"send file header:\n%s\n",out);
	//print_level(SV_DEBUG,"out len:%d\n", strlen(out)+1);

	stUploadFile.uiDataLen = strlen(out) + 1;
	uint32 u32DataLen = sizeof(SKDvrPrepareToUploadFileReq) + strlen(out) + 1;
	char *pTemp = (char *)malloc(u32DataLen);
	memcpy(pTemp, &stUploadFile, sizeof(SKDvrPrepareToUploadFileReq));
	memcpy(pTemp+sizeof(SKDvrPrepareToUploadFileReq), out, strlen(out) + 1);

	pProtocol->createHeader(&stHeader, SK_DVR_TO_CMS_SERVER_PREPARE_TO_UPLOAD_FILE_REQ,
		u32DataLen, SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&stHeader, pTemp, u32DataLen);

	cJSON_Delete(root);
	free(out);
	free(pTemp);

	return stUploadFile.uiFileLen;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::sendFileInArry()
{

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))

	for(int i=0; i <FILEUPLOAD_MAXNUM; i++)
	{

		if(stFileupLoadArry[i].u8UploadStatus == STATUS_UPLOADING)
		{
			if(stFileupLoadArry[i].u32FileSeq == stFileupLoadArry[i].u32RecFileSeq)
			{
				sendFile(i);
				usleep(20000);
			}
		}
	}

#endif
	return SV_SUCCESS;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::sendFile(uint8 u8SendIndex)
{
	sint32 s32ReadNum = 0;
	char buf[FILETRANSFER_BUFFNUM + 100] = {0};
	SKDvrTransferFileReq stFileReq;

	if( u8SendIndex >= FILEUPLOAD_MAXNUM ||  stFileupLoadArry[u8SendIndex].u8UploadStatus != STATUS_UPLOADING)
	{
		print_level(SV_DEBUG,"Index:%d error!\n", u8SendIndex);
		return SV_FAILURE;
	}

	s32ReadNum = read(stFileupLoadArry[u8SendIndex].s32Fd, buf+sizeof(SKDvrTransferFileReq), FILETRANSFER_BUFFNUM);
	if(s32ReadNum <= 0)
	{
		//print_level(SV_ERROR,"s32ReadNum:%d %s file:%s\n", s32ReadNum, strerror(errno), stFileupLoadArry[u8SendIndex].szFileName );
		SK_HEADER header;
		SKDvrTransferFileResultReq stResultReq = {0};
		stResultReq.uiSessionId = stFileupLoadArry[u8SendIndex].u32FileSessionId;

		if(s32ReadNum == 0)
		{
			stResultReq.common_rsp.ucResult = 0;
			strcpy((char*)stResultReq.common_rsp.szReason, "Finished!");
		}
		else if(s32ReadNum < 0)
		{
			stResultReq.common_rsp.ucResult = 1;
			strcpy((char*)stResultReq.common_rsp.szReason, "Read error!");
		}

		if(!stFileupLoadArry[u8SendIndex].bSendEnd)
		{
			pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RESULT_REQ, sizeof(SKDvrTransferFileResultReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
			pLink->sendToServer(&header, (char *)&stResultReq, sizeof(SKDvrTransferFileResultReq));
			stFileupLoadArry[u8SendIndex].bSendEnd = SV_TRUE;
			//sleep(2);
			return SV_SUCCESS;
		}
		else
		{
			return SV_SUCCESS;
		}
	}

	stFileReq.uiDataSize = s32ReadNum;
	stFileReq.uiFileOffset = stFileupLoadArry[u8SendIndex].u32FileOffest;
	stFileReq.uiSessionId = stFileupLoadArry[u8SendIndex].u32FileSessionId;
	stFileReq.uiFileSeq = stFileupLoadArry[u8SendIndex].u32FileSeq++;
	stFileupLoadArry[u8SendIndex].u32FileOffest += s32ReadNum;
	memcpy(buf, (char *)&stFileReq, sizeof(SKDvrTransferFileReq));
#if 1
	if (stFileupLoadArry[u8SendIndex].s32PrintCnt++ % 30 == 0)
	{
		print_level(SV_DEBUG,"sessid:%d, offeset:%d, seq:%d, datasize:%d fileName:%s\n",
		stFileReq.uiSessionId, stFileReq.uiFileOffset, stFileReq.uiFileSeq, stFileReq.uiDataSize,
		stFileupLoadArry[u8SendIndex].szFileName);
	}
#endif
	SK_HEADER header;
	pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_REQ, sizeof(SKDvrTransferFileReq)+s32ReadNum, SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&header, buf, sizeof(SKDvrTransferFileReq)+s32ReadNum);


	if(s32ReadNum < FILETRANSFER_BUFFNUM)
	{

		SK_HEADER header;
		SKDvrTransferFileResultReq stResultReq = {0};
		stResultReq.uiSessionId = stFileupLoadArry[u8SendIndex].u32FileSessionId;
		stResultReq.common_rsp.ucResult = 0;
		strcpy((char*)stResultReq.common_rsp.szReason, "Finished");
		if(!stFileupLoadArry[u8SendIndex].bSendEnd)
		{
			pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RESULT_REQ, sizeof(SKDvrTransferFileResultReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
			pLink->sendToServer(&header, (char *)&stResultReq, sizeof(SKDvrTransferFileResultReq));
			stFileupLoadArry[u8SendIndex].bSendEnd = SV_TRUE;
			//sleep(2);
		}
	}

	return SV_SUCCESS;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setRecordFileFlag(UPLOAD_FILE_OPTS_E enUploadFileOpts, char *pFileName)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))
    if (enUploadFileOpts < UPLOAD_FILE_ALARM_VIDEO || enUploadFileOpts > UPLOAD_FILE_ALARM_ALL)
    {
        return;
    }

    RECORDER_RenameFile(pFileName, strlen(pFileName));
#endif
}

void SV_NETWORK_FILEPRIVATEUPLOAD::removeFileFromList(char *pFileName)
{
    std::list<SV_STORAGE_FILE_PATH_S>::iterator list_itor;
    SV_STORAGE_FILE_PATH_S stStorageFilePath = {0};

    for (list_itor = sendWholeFileList.begin(); list_itor != sendWholeFileList.end(); list_itor++)
    {
        stStorageFilePath = (*list_itor);
        if (NULL != strstr(stStorageFilePath.szFilePath, pFileName))
        {
            print_level(SV_INFO, "erase file: %s\n", pFileName);
            sendWholeFileList.erase(list_itor);
            break;
        }
    }
}

void SV_NETWORK_FILEPRIVATEUPLOAD::processRegisterRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKCommonRsp *pRsp = (SKCommonRsp *)pData;
	print_level(SV_DEBUG,"pRsp->ucResult:%d, szReason:%s\n", pRsp->ucResult, pRsp->szReason);

	if( pRsp->ucResult == 0 )
	{
		bRegister = SV_TRUE;
	}
	else
	{
		clearArry();
		pLink->closeSocket();
	}
}

void SV_NETWORK_FILEPRIVATEUPLOAD::processUploadInfoRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKDvrPrepareToUploadFileRsp *pUploadRsp = (SKDvrPrepareToUploadFileRsp *)pData;
	//print_level(SV_DEBUG,"pRsp->ucResult:%d uiSessionId:%d uiUploadedSize:%u\n", pUploadRsp->ucResult, pUploadRsp->uiSessionId, pUploadRsp->uiUploadedSize);
	char *pJsonData = pData + sizeof(SKDvrPrepareToUploadFileRsp);
	char szFullPath[256] = {0};
	cJSON *json, *param;
	uint8 u8Type;

    json = cJSON_Parse(pJsonData);
	if (NULL == json)
	{
		print_level(SV_ERROR,"Error before: [%s]\n", cJSON_GetErrorPtr());
        return;
	}
	else
	{
		param = cJSON_GetObjectItem(json, "fn");
		if (param == SV_NULL)
		{
            print_level(SV_ERROR,"Error before: [%s]\n", cJSON_GetErrorPtr());
            return;
		}
	}

	sint32 s32Index  = getFileIndex(param->valuestring);
    if (s32Index < 0)
    {
        print_level(SV_ERROR, "not found file: %s in array.\n", param->valuestring);
        goto exit;
    }

	u8Type = stFileupLoadArry[s32Index].u8FileType;
	//print_level(SV_DEBUG,"get file in array[%d] type[%d]\n", s32Index, u8Type);

	if( pUploadRsp->ucResult == 0 ) //can upload
	{
		addFile2Arry(pUploadRsp, param->valuestring);
	}
	else if(pUploadRsp->ucResult == 1) // file have exit
	{
		//print_level(SV_WARN,"File:%s have exited!\n", param->valuestring);
		if (SV_COMMON_getAlarmFileDir(m_enUploadFileOpts, param->valuestring, szFullPath) != SV_SUCCESS )
		{
			print_level(SV_ERROR,"SV_DVR_STORAGE_GetFileDirctory fail [%s]\n", param->valuestring);
			return;
		}
        print_level(SV_INFO, "file is exist in cms, szFullPath: %s\n", szFullPath);
        setRecordFileFlag(m_enUploadFileOpts, szFullPath);
        deleIndex(s32Index);
        removeFileFromList(szFullPath);
	}
	else
	{
		print_level(SV_WARN,"File:%s can not upload for reason[%d]!\n", param->valuestring, pUploadRsp->ucResult);
        if( SV_COMMON_getAlarmFileDir(m_enUploadFileOpts, param->valuestring, szFullPath) != SV_SUCCESS )
		{
			print_level(SV_ERROR,"SV_DVR_STORAGE_GetFileDirctory fail [%s]\n", param->valuestring);
			return;
		}

		if(stFileupLoadArry[s32Index].u8TimeOutCnt > 1)
		{
		    print_level(SV_WARN, "clean timeout file!\n");
    		stFileupLoadArry[s32Index].u8TimeOutCnt = 0;
            setRecordFileFlag(m_enUploadFileOpts, szFullPath);
		}
        else if (pUploadRsp->ucResult == 4)
        {
            deleIndex(s32Index);
            removeFileFromList(szFullPath);
        }
		else if(pUploadRsp->ucResult != 1)
		{
		    ++stFileupLoadArry[s32Index].u8TimeOutCnt;
		    print_level(SV_WARN,"can not upload timeout[%d]!\n", stFileupLoadArry[s32Index].u8TimeOutCnt);
		}
	}

exit:
	cJSON_Delete(json);

}

void SV_NETWORK_FILEPRIVATEUPLOAD::processUploadFileRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{

	SKDvrTransferFileRsp* pFileRsp = (SKDvrTransferFileRsp*)pData;
	//print_level(SV_DEBUG,"SessionId:%d FileSeq:%d\n", pFileRsp->uiSessionId, pFileRsp->uiFileSeq);
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if(stFileupLoadArry[i].u32FileSessionId == pFileRsp->uiSessionId)
		{
			stFileupLoadArry[i].u32RecFileSeq = pFileRsp->uiFileSeq + 1;
		}
	}

}

void SV_NETWORK_FILEPRIVATEUPLOAD::processUploadResultRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKDvrTransferFileResultRsp * pResult = (SKDvrTransferFileResultRsp *)pData;
    std::list<SV_STORAGE_FILE_PATH_S>::iterator list_itor;
    SV_STORAGE_FILE_PATH_S stStorageFilePath = {0};
	print_level(SV_DEBUG,"SessionId:%d\n", pResult->uiSessionId);

	char szTempFileName[STORAGE_FULLPATH_LEN] = {0};
	uint8 u8Type = 0;
	if( getSessionIdFileName(pResult->uiSessionId, szTempFileName) == SV_SUCCESS )
	{
		getFileType(szTempFileName, &u8Type);
        print_level(SV_DEBUG, "upload file success! rename: %s\n",szTempFileName);
        setRecordFileFlag(m_enUploadFileOpts, szTempFileName);
        removeFileFromList(szTempFileName);
	}
	deleSessionID(pResult->uiSessionId);

}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::getFileType(char *pFileName, uint8* pu8Type)
{
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( strstr(stFileupLoadArry[i].szFileName, pFileName) )
		{
			*pu8Type = stFileupLoadArry[i].u8FileType;
			return SV_SUCCESS;
		}
	}

	return SV_FAILURE;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::getSessionIdFileName(uint32 u32SessionId, char *pFileName)
{
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( stFileupLoadArry[i].u32FileSessionId == u32SessionId )
		{
			//strncpy(pFileName, stFileupLoadArry[i].szFileName, STORAGE_FULLPATH_LEN);
			strncpy(pFileName, stFileupLoadArry[i].szFullPath, STORAGE_FULLPATH_LEN);
			return SV_SUCCESS;
		}
	}

	return SV_FAILURE;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::deleIndex(uint32 u32Index)
{
	if(stFileupLoadArry[u32Index].s32Fd > 0)
	{
		close(stFileupLoadArry[u32Index].s32Fd);
		stFileupLoadArry[u32Index].s32Fd = -1;
	}

	memset(stFileupLoadArry[u32Index].szFullPath, 0, 256);
	memset(stFileupLoadArry[u32Index].szFileName, 0, STORAGE_NAME_LEN);
	stFileupLoadArry[u32Index].u8UploadStatus = STATUS_NOFILE;
	stFileupLoadArry[u32Index].u32FileSessionId = 0;
	stFileupLoadArry[u32Index].u64FileSize = 0;
	stFileupLoadArry[u32Index].s32PrintCnt = 0;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::deleSessionID(uint32 u32SessionId)
{
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if (stFileupLoadArry[i].u32FileSessionId == u32SessionId)
		{
            if (0 != u32SessionId)
            {
                print_level(SV_INFO, "delete session id: %d, index: %d\n", u32SessionId, i);
            }
			deleIndex(i);
			break;
		}
	}
}

void SV_NETWORK_FILEPRIVATEUPLOAD::processStopUploadFile(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SK_HEADER stHeader;
	SKInformDvrStopTransferFileReq * pStopFile = (SKInformDvrStopTransferFileReq *)pData;
	print_level(SV_DEBUG,"uiSessionId:%d\n", pStopFile->uiSessionId);

	pProtocol->createHeader(&stHeader, SK_CMS_SERVER_INFORM_DVR_STOP_TRANSFER_FILE_RSP,
		sizeof(SKInformDvrStopTransferFileReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&stHeader, (char *)pStopFile, sizeof(SKInformDvrStopTransferFileReq));


}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::processFileTransferRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{

	SV_CHECK(pData);
	switch(pHeader->usCode)
	{
		print_level(SV_DEBUG,"opCode:%d\n", pHeader->usCode);

		case SK_DVR_TO_CMS_SERVER_CONNECT_FILE_SAVE_SERVER_RSP: processRegisterRsp(pHeader, pData, pDataSize); break;
		case SK_DVR_TO_CMS_SERVER_PREPARE_TO_UPLOAD_FILE_RSP: processUploadInfoRsp(pHeader, pData, pDataSize); break;
		case SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RSP: processUploadFileRsp(pHeader, pData, pDataSize);break;
		case SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RESULT_RSP: processUploadResultRsp(pHeader, pData, pDataSize);break;
		case SK_CMS_SERVER_INFORM_DVR_STOP_TRANSFER_FILE_REQ: processStopUploadFile(pHeader, pData, pDataSize);break;
		default: break;
	}

	return SV_SUCCESS;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isFilePrepare(const char *pFileName)
{
    for(uint8 i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if (strncmp(stFileupLoadArry[i].szFileName, pFileName, 256)  == 0)
		{
			return SV_TRUE;
		}
	}

	return SV_FALSE;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isFileSending(const char *pFilefullPath)
{
	for(uint8 i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( strncmp(stFileupLoadArry[i].szFullPath, pFilefullPath, 256)  == 0 )
		{
			return SV_TRUE;
		}
	}

	return SV_FALSE;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::sendFileInfoArry()
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))

    uint8 u8Type = TYPE_VIDEO;
	uint64 u64FileSzie = 0;
    uint8 u8AccessIndex = -1;
    char szFileName[128] = {0};

	if( !isArryFull() && !isSendListEmpty() )
	{
		list<SV_STORAGE_FILE_PATH_S>::iterator iter;
		for(iter = sendWholeFileList.begin(); iter != sendWholeFileList.end(); iter++ )
		{
            SV_COMMON_cutOutName(iter->szFilePath, szFileName);
			if (!isFilePrepare(szFileName))
			{
				u64FileSzie = sendFileInfo(iter->szFilePath);
                u8AccessIndex = getAccessIndex();
                if (SV_FAILURE != u8AccessIndex)
                {
                    if (NULL != strstr(szFileName, ".mp4") || NULL != strstr(szFileName, ".avi"))
                        u8Type = TYPE_VIDEO;
                    else if (NULL != strstr(szFileName, ".jpg"))
                        u8Type = TYPE_JPG;
				    setStatusPrepare(u8AccessIndex, szFileName, u8Type, u64FileSzie);
                }
            }

			if (isArryFull())
			{
				break;
			}
		}
	}
#endif
	return ;
}

sint32  SV_NETWORK_FILEPRIVATEUPLOAD::updateSendList()
{
    sint32 s32Ret = SV_SUCCESS;
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))
	clearArry();

	print_level(SV_DEBUG,"updateSendList type=%d\n", m_enUploadFileOpts);
    switch (m_enUploadFileOpts)
    {
        case UPLOAD_FILE_OFF:
            sendWholeFileList.clear();
            break;

        case UPLOAD_FILE_ALARM_VIDEO:
        case UPLOAD_FILE_ALARM_PICTURE:
        case UPLOAD_FILE_ALARM_ALL:
            s32Ret = scanRecorderFile(m_enUploadFileOpts, sendWholeFileList, SV_TRUE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "scanRecorderFile failed\n");
            }
            break;

        default:
            break;
    }
#endif
	return s32Ret;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isSendListEmpty()
{
 	if( sendWholeFileList.empty())
	{
		return SV_TRUE;
	}

	return SV_FALSE;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::closeFd()
{
    print_level(SV_INFO, "close all fd\n");
    clearArry();
}

void SV_NETWORK_FILEPRIVATEUPLOAD::run()
{
    sint32 s32Ret;
	SK_HEADER header;
	char dataBuf[NETWORK_MAX_RECVSIZE];
	uint32 u32DataSize = 0, u32TimeoutCount = 0;
	static uint32 u32Count;
    UPLOAD_FILE_OPTS_E enLastUploadFileOpts = SV_NETWORK_DVRINFO::getInstance()->getUploadFileType();


	while (pState->getNetworkRunning())
	{
 		while (!pState->getControlRegisterFlag() || !pDvrinfo->getStorageStatus(NULL))
		{
			if (!pState->getNetworkRunning())
			{
				return;
			}
            sendWholeFileList.clear();
			clearArry();
			sleep(1);
		}

        if (pState->getConfigUpdate())
        {
            m_enUploadFileOpts = SV_NETWORK_DVRINFO::getInstance()->getUploadFileType();
            pState->setConfigUpdate(false);

            if (m_enUploadFileOpts != enLastUploadFileOpts && pDvrinfo->getStorageStatus(NULL))
            {
                print_level(SV_INFO, "get now uploadFileOpts: %d, lastOpts: %d\n", m_enUploadFileOpts, enLastUploadFileOpts);
                pState->setPrivateFileUpdate(true);
            }
            enLastUploadFileOpts = m_enUploadFileOpts;
        }

        if (!isSendListEmpty() && pDvrinfo->getStorageStatus(NULL))
        {
            print_level(SV_WARN, "last upload file list is not empty, reload file list now!\n");
            pState->setPrivateFileUpdate(true);
        }

        if (pState->getPrivateFileUpdate())
        {
            s32Ret = updateSendList();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "updateSendList failed, try again now!\n");
                sleep_ms(3000);
                continue;
            }
            else
            {
                pState->setPrivateFileUpdate(false);
            }
        }

		if (isSendListEmpty())
		{
			if(u32Count++ > 90)
			{
 				u32Count = 0;
			}
			//print_level(SV_DEBUG,"File private upload Send List is empty!\n");
			for(int i=0; i<5; i++)
			{
				sleep(1);
				if( !pState->getNetworkRunning() )
				{
					return;
				}
			}
			continue;
		}
		else
		{
			u32Count = 0;
		}

		sleep(1);
		pLink->closeSocket();
		pLink->setServerIp((const char *)pDvrinfo->getIpAddr().c_str());
		pLink->setDeviceName((const char *)pDvrinfo->getNetCardName().c_str());
		pLink->setPort(u32Port);

		if (isSendListEmpty() || !pState->getControlRegisterFlag())
		{
			pLink->closeSocket();
			sleep(1);
		}
		else
		{
			while (!pLink->isAvalible())
			{
				pLink->setServerIp((const char *)pDvrinfo->getIpAddr().c_str());
				pLink->setDeviceName((const char *)pDvrinfo->getNetCardName().c_str());
				pLink->setPort(u32Port);
				if (pLink->initConnect() != SV_SUCCESS)
				{
					print_level(SV_WARN,"FileUpload link connect error \n");
					for(int i=0; i<5; i++)
					{
						sleep(1);
						if( !pState->getNetworkRunning() )
						{
							return;
						}
					}
					continue;
				}
				else
				{
					print_level(SV_DEBUG,"FileUpload link connect success\n");
				}
			}
			//register
			sendRegsiter();
			sleep(5);

			while (pState->getNetworkRunning())
			{
				if (!pDvrinfo->getStorageStatus(NULL) || !pState->getControlRegisterFlag() || pState->getConfigUpdate())
				{
					print_level(SV_WARN,"Filetransfer break\n");
					break;
				}

				fd_set read_set;
				FD_ZERO(&read_set);
				if (pLink->isAvalible())
				{
 					FD_SET(pLink->getSocket(), &read_set);
				}
				else
				{
					print_level(SV_WARN,"Filetransfer break\n");
					break;
				}

				struct timeval tv;
				tv.tv_sec = 1;
				tv.tv_usec = 0;  //1ms
				sint32 s32MaxFd = pLink->getSocket() + 1;
				sint32 s32Ret = select(s32MaxFd, &read_set, NULL, NULL, &tv);
				if (s32Ret < 0)
				{
					print_level(SV_ERROR,"Filetransfer select error\n");
				}
				else if (s32Ret == 0)
				{
	 				if (u32TimeoutCount++ > 30)
					{
                        u32TimeoutCount = 0;
						bRegister = SV_FALSE;
						print_level(SV_WARN,"Filetransfer break\n");
						break;
					}

					if (!bRegister)
					{
						continue;
					}

					if (isSendListEmpty() && isArryEmpty())
					{
					    if (pState->getPrivateFileUpdate())
					    {
                            print_level(SV_WARN, "updateSendList \n");
					        updateSendList();
					        pState->setPrivateFileUpdate(false);
					    }

                        #if 0
						if( isSendListEmpty() && isArryEmpty() )
						{
							bRegister = SV_FALSE;
							print_level(SV_WARN,"Filetransfer break\n");
							break;
						}
                        #endif
					}

					if (!isArryFull())
					{
                        if (!isSendListEmpty())
                        {
    						sendFileInfoArry();
                        }
                        else
                        {
                            bRegister = SV_FALSE;
                            print_level(SV_WARN, "array is no full but send list is empty, Filetransfer break.\n");
							break;
                        }
					}
					else
					{
						sendFileInArry();
					}
				}
				else
				{
					u32TimeoutCount = 0;
					//recev data
					if (pLink->recvFromServer(&header, dataBuf, &u32DataSize) < 0)
					{
						print_level(SV_ERROR,"Filetransfer recev server error\n");
						break;
					}

		 			processFileTransferRsp(&header, dataBuf, &u32DataSize);
		 			sendFileInArry();
					sendFileInfoArry();
				}
			}
		}
	}
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isUploading()
{
	return (SV_BOOL)!isArryEmpty();
}

void SV_NETWORK_FILEPRIVATEUPLOAD::exitFileprivate()
{
    clearArry();
    pLink->closeSocket();
}




















































