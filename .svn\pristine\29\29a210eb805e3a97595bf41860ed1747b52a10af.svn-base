/******************************************************************************
Copyright (C) 2021-2023 广州敏视数码科技有限公司版权所有.

文件名：mpp_ir.c

作者: lyn    版本: v1.0.0(初始版本号)   日期: 2022-03-11

文件功能描述: 定义usb红外摄像头管理模块

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

说明：
*******************************************************************************/

#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/sem.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <linux/videodev2.h>

#include "print.h"
#include "common.h"
#include "board.h"
#include "rkmedia_api.h"
#include "mpp_com.h"
#include "mpp_sys.h"
#include "media.h"
#include "config.h"
/* DRM */
#include "libdrm/xf86drm.h"
#include "libdrm/xf86drmMode.h"
#include "libdrm/drm/drm_fourcc.h"
#include "libdrm/drm/drm_mode.h"
/* RGA */
#include "drmrga.h"
#include "rga.h"
#include "RgaApi.h"
#include "im2d.h"

#include "guideusb2livestream.h"
#include "usb_ir.h"
#include "mpp_vpss.h"
#include "mpp_ir.h"
#include "safefunc.h"
#include "sharefifo.h"
#include "mpp_gui.h"
#if ALG_MUTLIT_BUFFER
#include "media_shm.h"
#endif

#define        IR_SHM_PATH              "/var/ir/irshm"
#define        IR_SNAP_PATH             "/var/snap/snap1.jpeg"  /* 红外抓图路径 */
/* 视频尺寸参数 */
#if ((MOUDLETYPE == MOUDLETYPE_TC933) || (MOUDLETYPE == MOUDLETYPE_TC639_T2))
#define        IR_RAW_WIDTH             256                     /* 红外原图宽 */
#define        IR_RAW_HEIGHT            192                     /* 红外原图高 */
#define        IR_USB_GD_PVID           "04b4:f7f7"             /* 高德红外设备PID和VID */
#define        IR_UVC_PVID              "0bda:5830"             /* 标准UVC机芯PID和VID */

#elif (MOUDLETYPE == MOUDLETYPE_TC639_T3)
#define        IR_RAW_WIDTH             384                     /* 红外原图宽 */
#define        IR_RAW_HEIGHT            288                     /* 红外原图高 */
#define        IR_USB_GD_PVID           "04b4:f7f7"             /* 高德红外设备PID和VID */
#define        IR_UVC_PVID              "0bda:5830"             /* 拉姆达的标准UVC机芯PID和VID，目前在T3中是不会出现的 */

#elif (MOUDLETYPE == MOUDLETYPE_TC639_T6)
#define        IR_RAW_WIDTH             640                     /* 红外原图宽 */
#define        IR_RAW_HEIGHT            512                     /* 红外原图高 */
#define        IR_USB_GD_PVID           "04b4:f7f7"             /* 高德红外设备PID和VID，目前在T6中是不会出现的 */
#define        IR_UVC_PVID              "04b4:00f8"             /* 睿创微纳的标准UVC机芯PID和VID */

#endif

#if ((MOUDLETYPE == MOUDLETYPE_TC933) || (MOUDLETYPE == MOUDLETYPE_TC639_T2) || (MOUDLETYPE == MOUDLETYPE_TC639_T3))
#define        IR_VO_WIDTH              512                     /* 红外显示宽 */
#define        IR_VO_HEIGHT             288                     /* 红外显示高 */    


#elif (MOUDLETYPE == MOUDLETYPE_TC639_T6)
#define        IR_VO_WIDTH              640                     /* 红外显示宽 */
#define        IR_VO_HEIGHT             512                     /* 红外显示高 */    

#endif

#define        IR_ALG_WIDTH             512                     /* 算法图像宽 */
#define        IR_ALG_HEIGHT            384                     /* 算法图像高 */

#define        IR_VENC_WIDTH            1280                    /* 编码图像宽 */
#define        IR_VENC_HEIGHT           720                     /* 编码图像高 */

#define        IR_VENC_JPEG_WIDTH       1280                    /* 编码图像宽 */
#define        IR_VENC_JPEG_HEIGHT      720                     /* 编码图像高 */

#define        IR_VMIX_WIDTH            1920                    /* 显示屏宽 */
#define        IR_VMIX_HEIGHT           1080                    /* 显示屏高 */

#define        IR_OSD_POS_BAIS_X        1350                    /* 红外osd叠图X位置偏移 */
#define        IR_OSD_POS_BAIS_Y        50                      /* 红外osd叠图Y位置偏移 */

#define        IR_OSD_WIDTH             512                     /* 红外osd分屏方式叠加在图像的宽 */
#define        IR_OSD_HEIGHT            288                     /* 红外osd分屏方式叠加在图像的高 */

#define        IR_DRM_BUF_NUM           3                       /* DRM buffer数量 */
#if ALG_MUTLIT_BUFFER
#define        IR_ALG_DRM_BUF_NUM       3                       /* 算法DRM   buffer数量 */
#else
#define        IR_ALG_DRM_BUF_NUM       1                       /* 算法DRM   buffer数量 */
#endif
#define        IR_HEAP_BUF_NUM          3                       /* 堆空间 轮换buffer数量 */
#define        IR_ALG_HEAP_BUF_NUM      IR_ALG_DRM_BUF_NUM
#define        IR_SHM_BUF_NUM           3                       /* 共享内存 轮换buffer 数量 */

#define        IR_VENC_CHN              5                       /* venc编码通道 */
#define        IR_VENC_JPEG_CHN         7                       /* 图片venc通道 */
#define        IR_VENC_VMIX_CHN         9
#define        IR_VMIX_LINE_WIDTH       4                       /* vmix线宽像素 */

#define        IR_ALG_SEM_CHN           1                       /* 红外算法占用信号量的通道 */
#define        IR_VOSD_CHN              1                       /* 红外占用OSD通道号 */
#define        IR_VMIX_CHN              1                       /* 红外占用VMIX通道号 */
#define        IR_ALG_CHN               1                       /* 红外占用算法通道号 */

#define        IR_TRSHOLD_YELLOW        80                      /* 黄色报警温度阈值(摄氏度) */
#define        IR_TRSHOLD_RED           100                     /* 红色报警温度阈值(摄氏度) */
#define        IR_TRSHOLD_DARKRED       120                     /* 暗红色报警温度阈值(摄氏度) */

#define        IR_ALARM_INTERVAL        2000000                 /* 报警间隔(微秒) */

#define        IR_GD_CMD_LEN            12                      /* 高德机芯命令长度 */

/* 高德机芯设置命令 */
typedef enum tagIrCamCmdType_E
{
    IR_CMD_SET_SAVE = 0x00,             /* 保存设置 */
    IR_CMD_SET_VIDEO_YUV422,            /* 设置视频格式YUV422+参数行 */
    IR_CMD_SET_VIDEO_Y16_YUV422,        /* 设置视频格式YUV422+Y16+参数行 */
    IR_CMD_SET_TMP_MODE,                /* 设置测温模式为最高点+最低点 */
    IR_CMD_SET_ANALYSIS_MODE,           /* 设置分析模式为全屏模式 */
    IR_CMD_GET_STAT_VIDEO,              /* 获取视频格式 */
    IR_CMD_GET_ANALYSIS_MODE,           /* 获取分析模式 */
    IR_CMD_GET_TEMP_MODE,               /* 获取测温模式 */

    IR_CMD_SET_TEST_VIDEO_Y16,          /* 测试：视频格式设置成Y16+参数行 */
	
    IR_CMD_BUTT
} IR_CMD_TYPE_E;

/* 高德机芯设置命令结构体 */
typedef struct tagIrCamCmd_S
{
    IR_CMD_TYPE_E   enCmdType;          /* 命令类型 */
    sint32          s32Len;             /* 命令长度 */
    unsigned char   aszCommand[IR_GD_CMD_LEN];     /* 命令文本 */
} IR_CMD_S;

/* 高德机芯配置命令,注意顺序要和命令枚举类型一致 */
const IR_CMD_S command[] = 
{
    {IR_CMD_SET_SAVE,             IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x01, 0x00, 0x04, 0x00, 0x00, 0x00, 0x01, 0x03, 0xF0}},
    {IR_CMD_SET_VIDEO_YUV422,     IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x02, 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x06, 0xF0}},
    {IR_CMD_SET_VIDEO_Y16_YUV422, IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x02, 0x01, 0x03, 0x00, 0x00, 0x00, 0x05, 0x02, 0xF0}},
    {IR_CMD_SET_TMP_MODE,         IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x04, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0}},
    {IR_CMD_SET_ANALYSIS_MODE,    IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x03, 0x03, 0x01, 0x00, 0x00, 0x00, 0x01, 0x07, 0xF0}},
    {IR_CMD_GET_STAT_VIDEO,       IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x02, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x84, 0xF0}},
    {IR_CMD_GET_ANALYSIS_MODE,    IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x03, 0x04, 0x80, 0x00, 0x00, 0x00, 0x00, 0x80, 0xF0}},
    {IR_CMD_GET_TEMP_MODE,        IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x04, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x83, 0xF0}},
    {IR_CMD_SET_TEST_VIDEO_Y16,   IR_GD_CMD_LEN,  {0x55, 0xAA, 0x07, 0x02, 0x01, 0x03, 0x00, 0x00, 0x00, 0x03, 0x04, 0xF0}}
};


/* DRM buffer 状态 */
typedef enum tagIRDrmBufStatus_E
{
    IR_DRM_BUF_FORBID = 0x00,        /* BUF不可读不可写 */
    IR_DRM_BUF_WRITE,                /* BUF只写 */
    IR_DRM_BUF_RW,                   /* BUF可读写 */
    IR_DRM_BUF_READ,                 /* BUF只读 */

    IR_BUF_BUTT
} IR_DRM_BUF_STA_E;

/* 堆空间Buffer状态 */
typedef enum tagIRHeapBufStatus_E
{
    IR_HEAP_BUF_FORBID = 0x00,        /* BUF不可读不可写 */
    IR_HEAP_BUF_WRITE,                /* BUF只写 */
    IR_HEAP_BUF_RW,                   /* BUF可读写 */
    IR_HEAP_BUF_READ,                 /* BUF只读 */

    IR_HEAP_BUF_BUTT

} IR_HEAP_BUF_STA_E;

/* 共享内存Buffer状态 */
typedef enum tagIRShmBufStatus_E
{
    IR_SHM_BUF_FORBID = 0x00,         /* BUF不可读不可写 */
    IR_SHM_BUF_WRITE,                 /* BUF只写 */
    IR_SHM_BUF_RW,                    /* BUF可读写 */
    IR_SHM_BUF_READ,                  /* BUF只读 */

    IR_SHM_BUF_BUTT

} IR_SHM_BUF_STA_E;


/* IR DMAbuffer信息 */
typedef struct tagIRDrmBufInfo_S
{
    sint32  s32Fd;                  /* DMA-BUF 对应的文件描述符 */
    sint32  s32Handle;              /* DMA-BUF 对应的句柄 */
    void    *virAddr;               /* DMA-BUF 对应的虚拟内存地址 */
    void    *phyAddr;               /* DMA-BUF 对应的物理地址 */
    uint32  u32Size;                /* BUF对应的大小 */
    uint32  u32Width;               /* BUF对应的宽度 */
    uint32  u32Height;              /* BUF对应的高度 */
    uint32  u32Format;              /* BUF对应的格式 */
    IR_DRM_BUF_STA_E enBufStatus;   /* BUF状态 */
} IR_DRM_BUF_INFO_S;

/* 数据堆信息 */
typedef struct tgaIRHeapBufInfo_S
{
    uint32      u32Width;           /* BUF对应的宽度 */
    uint32      u32Height;          /* BUF对应的高度 */
    uint32      u32Size;            /* 缓存空间大小 */
    void        *virAddr;           /* 缓存空间地址 */
    IR_HEAP_BUF_STA_E    enBufStatus;/* 缓存状态 */
} IR_HEAP_BUF_INFO_S;

/* 共享内存信息 */
typedef struct tgaIRShmBufInfo_S
{
    uint32      u32Width;            /* BUF对应的宽度 */
    uint32      u32Height;           /* BUF对应的高度 */
    uint32      u32Size;             /* 缓存空间大小 */
    sint32      s32SemId;            /* 共享内存sem id */
    sint32      s32ShmId;            /* 共享内存shm id */
    char        szPath[32];          /* 共享内存文件名 */
    void        *virAddr;            /* 缓存空间地址 */
    IR_SHM_BUF_STA_E    enBufStatus; /* 缓存状态 */
} IR_SHM_BUF_INFO_S;

/* 红外测温信息 */
typedef struct 
{
    SV_BOOL     bVaild;              /* 有效标志位 */
    SV_BOOL     bLoadTempCure;       /* 是否已经加载温度曲线 */
    sint32      s32AlarmLv;          /* 报警级别  ,0不报警*/
    float       fTempMax;            /* 最高温度(摄氏度) */
    float       fTempMaxPosX;        /* 最高温度X坐标 */
    float       fTempMaxPosY;        /* 最高温度Y坐标 */
    float       fTempMin;            /* 最低温度(摄氏度) */
    float       fTempMinPosX;        /* 最低温度X坐标 */
    float       fTempMinPosY;        /* 最低温度Y坐标 */
    float       fTempAvg;            /* 区域平均温度(摄氏度) */
               
} IR_TEMP_INFO_S;

/* 机芯命令配置参数 */
typedef struct
{
    IR_CMD_TYPE_E   enCurCmd;         /* 当前命令 */
    SV_BOOL         bGetRespon;       /* 是否获取到回包 */
    SV_BOOL         bIsRight;         /* 回包的参数和预设是否一致 */
} IR_CAM_CFG_S;


/* 红外模块信息 */
typedef struct tagIRInfo_S
{
    SV_BOOL                 bRunning;                           /* 线程是否正在运行 */
    SV_BOOL                 bInterrupt;                         /* 是否中断 */
    SV_BOOL                 bPictureStream;                     /* 是否需要图片流 */
    SV_BOOL                 bInsert;                            /* 红外设备插入情况 */
    SV_BOOL                 bStatusChange;                      /* 红外设备状态变化情况，从插入到未插入或者从未插入到插入都置为1 */
    SV_BOOL                 bEnable;                            /* 红外设备使能情况 */
    SV_BOOL                 bGetFrame;                          /* 是否获取到数据帧 */
    SV_BOOL                 bImageMirror;                       /* 是否使能画面镜像 */
    SV_BOOL                 bImageFlip;                         /* 是否使能画面翻转 */
    SV_BOOL                 bUpdateOsd;                         /* 强制更新osd */
    SV_BOOL                 bRecordStatus;                      /* 是否需要录像 */
    SV_BOOL                 bUvcFirstPowerOn;                  /* 是否拉姆达机芯上电后首次执行 */
    SV_SIZE_S               stSrcVideoSize;                     /* 摄像头视频画面大小 (宽高) */
    sint32                  s32DevFd;                           /* 显卡设备fd */
    sint32                  s32RawIdx;                          /* 源数据索引 */
    sint32                  as32TmpIdx[MPP_IR_BUTT];            /* Tmp buffer索引 */
    sint32                  as32DrmIdx[MPP_IR_BUTT];            /* drm buffer索引 */
    sint32                  as32HeapIdx[MPP_IR_BUTT];           /* heap buffer索引编码用堆，减少拷贝时间 */
    sint32                  as32CvbsDrmIdx;                     /* CVBS缓冲区的索引 */
    sint32                  as32RtspHeapIdx;                    /* Rtsp缓冲区的索引 */
    sint32                  s32VencFd;                          /* 编码通道fd */
    sint32                  s32RtspVencFd;                       /* RTSP功能用的编码通道fd */
    sint32                  s32VencJpegFd;                      /* 图片流venc通道fd */
    sint32                  s32SensorClass;                     /* 红外机芯类型， 0 代表高德的机芯， 1代表拉姆达的机芯*/
    sint32                  s32QueId;                           /* 图像编码通道队列id */
    sint32                  s32RtspQueId;
    SPLIT_MODE              eCurIrMode;                         /* 当前视频显示模式 */
    SPLIT_MODE              eResumeIrMode;                      /* 待恢复的视频显示模式 */
    
    IR_DRM_BUF_INFO_S       astRawDrmBuf[IR_DRM_BUF_NUM];       /* 红外格式转换后的Drmbuf队列 */
    IR_DRM_BUF_INFO_S       astVoDrmBuf[IR_DRM_BUF_NUM];        /* 显示用Drmbuf队列 */
    IR_DRM_BUF_INFO_S       astAlgDrmBuf[IR_ALG_DRM_BUF_NUM];   /* 算法DrmBuf队列 */
    IR_DRM_BUF_INFO_S       astVencH264DrmBuf[IR_DRM_BUF_NUM];  /* H264编码用DrmBuf队列 */
    IR_DRM_BUF_INFO_S       astVencJPEGDrmBuf[IR_DRM_BUF_NUM];  /* H264编码用DrmBuf队列 */
    IR_DRM_BUF_INFO_S       astVmixDrmBuf[IR_DRM_BUF_NUM];      /* Vmix Drmbuf队列 */
    
    IR_HEAP_BUF_INFO_S      astRawHeapBuf[IR_HEAP_BUF_NUM];     /* 源数据堆buffer */
    IR_HEAP_BUF_INFO_S      astRawTempHeapBuf[IR_HEAP_BUF_NUM]; /* 源测温数据 */
    IR_HEAP_BUF_INFO_S      astConvertHeapBuf[IR_HEAP_BUF_NUM]; /* 格式转换后堆buffer */
    IR_HEAP_BUF_INFO_S      astTmpHeapBuf[IR_HEAP_BUF_NUM * MPP_IR_BUTT];/* 中间转换分发各个模块堆buffer */
    IR_HEAP_BUF_INFO_S      astVoHeapBuf[IR_HEAP_BUF_NUM];      /* VO堆buffer */
    IR_HEAP_BUF_INFO_S      astAlgHeapBuf[IR_ALG_HEAP_BUF_NUM]; /* 算法堆buffer */
    IR_HEAP_BUF_INFO_S      astVencH264HeapBuf[IR_HEAP_BUF_NUM];/* 编码堆buffer */
    IR_HEAP_BUF_INFO_S      astVencJpegHeapBuf[IR_HEAP_BUF_NUM];/* 图片编码堆buffer */
    IR_HEAP_BUF_INFO_S      astCvbsVmixHeapBuf[IR_DRM_BUF_NUM];  /* CVBS 缓冲区队列 */
    IR_HEAP_BUF_INFO_S      astRtspVmixHeapBuf[IR_DRM_BUF_NUM];  /* Rtsp 缓冲区队列 */
    
    MEDIA_STAT_CALLBACK     pfStatCallback;                     /* 设备热插拔状态回调函数 */
    MEDIA_RGB_CALLBACK      pfRgbCallback;                      /* CVBS图像传输函数 */

    short                   *pas16ParamLine[IR_HEAP_BUF_NUM];   /* 参数行 */
    IR_TEMP_INFO_S          stTempInfo;                         /* 测温结果 */
    IR_CAM_CFG_S            stCmdCfg;                           /* 红外机芯参数配置 */
    
    pthread_t               pDistributionTid;                   /* 主线程id */
    pthread_t               pUvcTid;                            /* 标准UVC机芯取图线程 */
    pthread_t               pMonitorTid;                        /* 监控线程id */
    pthread_t               apThreadId[MPP_IR_BUTT];            /* 模块线程id */
    pthread_t               pCvbsTid;                           /* CVBS发送线程的线程号 */
    pthread_t               pVencSendTid;                       /* 子码流编码发送线程 */
    pthread_t               pVencRecvTid;                       /* 子码流编码接收线程 */
    pthread_t               pJpegSendTid;                       /* 图片流发送线程 */
    pthread_t               pJpegRecvTid;                       /* 图片流接收线程 */
    pthread_t               pRtspSendTid;                       /* RTSP流用的发送线程 */
    pthread_t               pRtspRecvTid;                       /* RTSP流用的接收线程 */
    pthread_t               pAlgSendTid;                        /* 算法数据流发送线程 */
    pthread_t               pTempAlarmTid;                      /* 测温报警处理线程 */
    pthread_t               pFillBlackTid;                      /* 设备掉线后用黑色填充数据源线程id */
    pthread_mutex_t         pIrDistributionLock;                /* 分发数据互斥锁 */
    pthread_mutex_t         apMutexLock[MPP_IR_BUTT];           /* 模块互斥锁 */
    pthread_mutex_t         pIrCmdLock;                         /* 配置命令参数锁 */ 
    pthread_mutex_t         pIrCvbsLock;                        /* Cvbs模块锁*/
    pthread_mutex_t         pStatusChangeLock;                  /* 机芯插拔状态锁 */

    sint32                  s32ExtscreenWidth;                  /* CVBS缓冲区的宽 */
    sint32                  s32ExtscreenHeight;                 /* CVBS缓冲区的高 */
    SV_BOOL                 bExtscreenExist;                    /* CVBS模块是否存在 */
} MPP_IR_INFO_S;

MPP_IR_INFO_S m_stIRInfo = {0};     /* 红外模块信息 */

/******************************************************************************
 * 函数功能: 获取当前时刻（微秒）
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 微秒
 * 注意    : 无
 *****************************************************************************/
static long long int microtime(void)
{
  struct timeval time;
  gettimeofday(&time, NULL); //This actually returns a struct that has microsecond precision.
  long long int microsec = ((long long)time.tv_sec * 1000000) + time.tv_usec;
  return microsec;
}

/******************************************************************************
 * 函数功能: 数据所占位数转换
 * 输入参数: format -- 视频格式数据
 * 输出参数: 无
 * 返回值  : 位数
 * 注意    : 无
 *****************************************************************************/
static sint32 format_to_bpp(sint32 format)
{
    switch (format) {
    case DRM_FORMAT_NV12:
    case DRM_FORMAT_NV12_10:
    case DRM_FORMAT_YUV420:
        return 8;
    case DRM_FORMAT_BGR565:
    case DRM_FORMAT_RGB565:
    case DRM_FORMAT_UYVY:
    case DRM_FORMAT_YUYV:
        return 16;
    case DRM_FORMAT_BGR888:
    case DRM_FORMAT_RGB888:
        return 24;
    case DRM_FORMAT_ARGB8888:
    case DRM_FORMAT_XRGB8888:
    case DRM_FORMAT_RGBA8888:
    default:
        return 32;
    }
}

static float format_to_bytes(sint32 format)
{
    switch (format) {
    case DRM_FORMAT_NV12:
    case DRM_FORMAT_NV12_10:
    case DRM_FORMAT_YUV420:
        return 1.5;
    case DRM_FORMAT_BGR565:
    case DRM_FORMAT_RGB565:
    case DRM_FORMAT_UYVY:
    case DRM_FORMAT_YUYV:
        return 2;
    case DRM_FORMAT_BGR888:
    case DRM_FORMAT_RGB888:
        return 3;
    case DRM_FORMAT_ARGB8888:
    case DRM_FORMAT_XRGB8888:
    case DRM_FORMAT_RGBA8888:
    default:
        return 4;
    }

}

/******************************************************************************
 * 函数功能: 获取指令的文本返回内容,通过管道方式
 * 输入参数: cpInstruciton -- 指令 pBuf -- 应用提供的缓存区 u32Size -- 缓存大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
static sint32 GetInsContext(const char* cpInstruciton, char* pszBuf, uint32 u32Size)
{
    FILE *fStream;
    
    if((fStream = popen(cpInstruciton, "r")) == NULL)
    {
        print_level(SV_ERROR, "popen %s error \n", cpInstruciton);
        return SV_FAILURE;
    }

    if((fread(pszBuf, sizeof(char), u32Size,  fStream)) < 1)
    {
        if(feof(fStream))
        {
            pclose(fStream);
            return SV_SUCCESS;
        }
        pclose(fStream);
        print_level(SV_ERROR, "fread %s error \n",cpInstruciton);
        return SV_FAILURE;
    }
    pclose( fStream );

    return SV_SUCCESS;    
}

/******************************************************************************
 * 函数功能: 销毁DRMbuffer
 * 输入参数: s32Fd -- 设备文件描述符
              pstDrmBuf -- buffer信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_drm_destroy_fb(sint32 s32Fd, IR_DRM_BUF_INFO_S *pstDrmBuf)
{
    if (pstDrmBuf->virAddr && pstDrmBuf->u32Size > 0)
    {
        munmap(pstDrmBuf->virAddr, pstDrmBuf->u32Size);
        pstDrmBuf->virAddr = NULL;
        pstDrmBuf->u32Size = 0;
    }
    
    if (pstDrmBuf->s32Handle)
    {
        struct drm_mode_destroy_dumb destroy = {0};
        destroy.handle = pstDrmBuf->s32Handle;
        drmIoctl(s32Fd, DRM_IOCTL_MODE_DESTROY_DUMB, &destroy);
        pstDrmBuf->s32Handle = 0;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建DRMbuffer
 * 输入参数: s32Fd -- 设备文件描述符 u32Width -- 宽 u32Height -- 高
             pstDrmBuf -- buffer信息 u32Format -- 数据格式
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_drm_create_fb(sint32 s32Fd, IR_DRM_BUF_INFO_S *pstDrmBuf, uint32 u32Width, uint32 u32Height, uint32 u32Format)
{
    sint32 s32Ret;
    struct drm_mode_create_dumb create = {0};
    struct drm_mode_map_dumb map = {0};
    void *viraddr;
    sint32 s32DrmFd;

    create.width = u32Width;
    create.height = u32Height;
    create.bpp = format_to_bpp(u32Format);
    if (u32Format == DRM_FORMAT_NV12)
    {
        create.height = u32Height * 3 / 2;
    }

    pstDrmBuf->u32Width = u32Width;
    pstDrmBuf->u32Height = u32Height;
    pstDrmBuf->u32Format = u32Format;

    s32Ret = drmIoctl(s32Fd, DRM_IOCTL_MODE_CREATE_DUMB, &create);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "drmIoctl DRM_IOCTL_MODE_CREATE_DUMB failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
    pstDrmBuf->s32Handle = create.handle;
    map.handle = create.handle;
    s32Ret = drmIoctl(s32Fd, DRM_IOCTL_MODE_MAP_DUMB, &map);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "drmIoctl DRM_IOCTL_MODE_MAP_DUMB fail! [err=%d]\n", s32Ret);
        ir_drm_destroy_fb(s32Fd, pstDrmBuf);
        return SV_FAILURE;
    }

    viraddr = mmap(0, create.size, PROT_READ | PROT_WRITE, MAP_SHARED,  s32Fd, map.offset);
    if (NULL == viraddr)
    {
        print_level(SV_ERROR, "mmap failed\n [err=%d]\n", s32Ret);
        ir_drm_destroy_fb(s32Fd, pstDrmBuf);
        return SV_FAILURE;
    }
    pstDrmBuf->virAddr   = viraddr;
    pstDrmBuf->u32Size   = create.size;

    if(u32Width == 720 && u32Height == 480 && u32Format == DRM_FORMAT_UYVY)
        pstDrmBuf->u32Size = 691200;
    if(u32Width == 720 && u32Height == 576 && u32Format == DRM_FORMAT_UYVY)
        pstDrmBuf->u32Size = 829440;

    s32Ret = drmPrimeHandleToFD(s32Fd, create.handle, 0, &s32DrmFd);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "drmIoctl DRM_IOCTL_MODE_MAP_DUMB fail! [err=%d]\n", s32Ret);
        ir_drm_destroy_fb(s32Fd, pstDrmBuf);
        return SV_FAILURE;
    }
    pstDrmBuf->s32Fd = s32DrmFd;
    //print_level(SV_DEBUG, "drm W:%u H:%u size:%d\n",pstDrmBuf->u32Width, pstDrmBuf->u32Height, pstDrmBuf->u32Size);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 释放堆空间
 * 输入参数: pstHeapBuf -- buffer信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_heap_destroy(IR_HEAP_BUF_INFO_S *pstHeapBuf)
{
    if(NULL == pstHeapBuf)
    {
        return ERR_NULL_PTR;
    }
    free(pstHeapBuf->virAddr);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建用户堆空间
 * 输入参数: u32Width -- 宽 u32Height -- 高
             pstHeapBuf -- buffer信息 u32Format -- 数据格式
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_heap_create(IR_HEAP_BUF_INFO_S *pstHeapBuf, uint32 u32Width, uint32 u32Height, uint32 u32Format)
{
    uint32 u32Size = 0, i = 0;
    float f32Bytes = format_to_bytes(u32Format);
    void * pHeapBuf = NULL;
    if (NULL == pstHeapBuf)
    {
        return ERR_NULL_PTR;
    }

    u32Size = u32Width * u32Height * f32Bytes;
    
    pHeapBuf = malloc(u32Size);
    if (NULL == pHeapBuf)
    {
        print_level(SV_ERROR, "malloc fialed \n");
        return SV_FAILURE;
    }
    pstHeapBuf->virAddr = pHeapBuf;
    pstHeapBuf->u32Size = u32Size;
    pstHeapBuf->u32Width = u32Width;
    pstHeapBuf->u32Height = u32Height;
    
    //print_level(SV_DEBUG, "heap W:%u H:%u size:%d\n",pstHeapBuf->u32Width, pstHeapBuf->u32Height, pstHeapBuf->u32Size);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁共享内存
 * 输入参数: pstHeapBuf -- buffer信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_shm_destroy(IR_SHM_BUF_INFO_S *pstShmBuf)
{
    if (NULL == pstShmBuf)
    {
        return ERR_NULL_PTR;
    }
    
    if (pstShmBuf->s32SemId > 0)
    {
        semctl(pstShmBuf->s32SemId, 0, IPC_RMID, 0);
    }

    if (pstShmBuf->s32ShmId > 0)
    {
        shmctl(pstShmBuf->s32ShmId, IPC_RMID, NULL);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建共享内存
 * 输入参数: u32Width -- 宽 u32Height -- 高
             pstShmBuf -- buffer信息 u32Format -- 数据格式
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_shm_create(IR_SHM_BUF_INFO_S *pstShmBuf, uint32 u32Width, uint32 u32Height, uint32 u32Format)
{
    uint32 u32Size = 0, i;
    static uint32 u32NameIdx = 0;
    sint32 s32Ret = -1, s32ShmId = -1, s32SemId = -1;
    FILE* file;
    key_t key;
    uint16 au16Val[5] = {0};
    char szBuf[32] = {0};
    char szCmd[256] = {0};
    void *pVirAddr = NULL;
    uint32 u32SemNum = 5;
    union semun {
       int              val;    /* Value for SETVAL */
       struct semid_ds *buf;    /* Buffer for IPC_STAT, IPC_SET */
       unsigned short  *array;  /* Array for GETALL, SETALL */
       struct seminfo  *__buf;  /* Buffer for IPC_INFO */
    } unSemArgs;
    
    u32Size = u32Width * u32Height * (format_to_bytes(u32Format));

    sprintf(szBuf,"%s%d", IR_SHM_PATH, u32NameIdx);
    if(0 != access(szBuf,F_OK))
    {    
        if(0 != access(IR_SHM_PATH, F_OK))
        {
            sprintf(szCmd, "mkdir -p %s", IR_SHM_PATH);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if(SV_SUCCESS != s32Ret)
            {
                return SV_FAILURE;
            }
        }
        file = fopen(szBuf,"w+");
        if(NULL == file)
        {
            print_level(SV_ERROR, "file %s dose not exist\n", szBuf);
            return SV_FAILURE;
        }
        fclose(file);
    }

    key = ftok(szBuf, 0); 

    s32SemId = semget(key, u32SemNum, IPC_CREAT|0600);
    if(s32SemId < 0 )
    {
        return SV_FAILURE;
    }
    pstShmBuf->s32SemId = s32SemId;
    
    unSemArgs.array = au16Val;
    for (i = 0; i < u32SemNum; i++)
    {
        au16Val[i] = 1;
    }
    s32Ret = semctl(s32SemId, 0, SETALL, unSemArgs);
    if (s32Ret < 0)
    {
        ir_shm_destroy(pstShmBuf);
        return SV_FAILURE;
    }

    s32ShmId = shmget(key, u32Size, IPC_CREAT|0644);
    if(s32ShmId  < 0)
    {
        ir_shm_destroy(pstShmBuf);
        return SV_FAILURE;        
    }
    pstShmBuf->s32ShmId = s32ShmId;
    
    pVirAddr = shmat(s32ShmId, NULL, 0);
    if (NULL == pVirAddr)
    {
        ir_shm_destroy(pstShmBuf);
        return SV_FAILURE;
    }

    pstShmBuf->u32Width = u32Width;
    pstShmBuf->u32Height = u32Height;
    pstShmBuf->u32Size = u32Size;
    pstShmBuf->virAddr = pVirAddr;
    memcpy(pstShmBuf->szPath, szBuf, sizeof(szBuf));
    u32NameIdx++;
    print_level(SV_DEBUG, "shm W:%u H:%u size:%d\n",pstShmBuf->u32Width, pstShmBuf->u32Height, pstShmBuf->u32Size);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 分屏时坐标映射
 * 输入参数: point --- 坐标映射点
             enSplitMode --- 分屏模式
 * 输出参数: point --- 坐标映射点
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_Mapping(MPP_GUI_POINT_S *point, SPLIT_MODE enSplitMode, sint32 s32Chn, sint32 s32Num)
{
    sint32 s32Ret, i;
    float xbias = 0.0, ybias = 0.0, xscale = 1.0, yscale = 1.0;

    if(point == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    switch(enSplitMode)
    {
        case MEDIA_SPLIT_ONE:
            if(s32Chn == 0)
            {
                xbias = 0;
                ybias = 0;
                xscale = 1;
                yscale = 1;
            }
            else
            {
                xbias = 0;
                ybias = 0;
                xscale = 0;
                yscale = 0;
            }
            break;
        case MEDIA_SPLIT_TWO:
            if(s32Chn == 0)
            {
                xbias = 0;
                ybias = 0;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = 1;
            }
            else if(s32Chn == 1)
            {
                xbias = (1920 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                ybias = 0;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = 1;
            }
            break;
        case MEDIA_SPLIT_THREE:
            if(s32Chn == 0)
            {
                xbias = 0;
                ybias = 0;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = 1;
            }
            else if(s32Chn == 1)
            {
                xbias = (1920 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                ybias = 0;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = (1080 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
            }
            else if(s32Chn == 2)
            {
                xbias = (1920 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                ybias = (1080 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = (1080 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
            }
            break;
        case MEDIA_SPLIT_IR_ONE:
            if(s32Chn == 1)
            {
                xbias = 0;
                ybias = 0;
                xscale = 1;
                yscale = 1;
            }
            else
            {
                xbias = 0;
                ybias = 0;
                xscale = 0;
                yscale = 0;
            }
            break;
        case MEDIA_SPLIT_IR_OSD:
            if(s32Chn == 0)
            {
                xbias = 0;
                ybias = 0;
                xscale = 1;
                yscale = 1;
            }
            else if(s32Chn == 1)
            {
                xbias = 1350 / 1.0 / 1920;
                ybias = 50 / 1.0 / 1080;
                xscale = 512 / 1.0 / 1920;
                yscale = 384 / 1.0 / 1080;
            }            
            break;
        case MEDIA_SPLIT_IR_OSD_2:
            if(s32Chn == 1)
            {
                xbias = 0;
                ybias = 0;
                xscale = 1;
                yscale = 1;
            }
            else if(s32Chn == 0)
            {
                xbias = 1350 / 1.0 / 1920;
                ybias = 50 / 1.0 / 1080;
                xscale = 512 / 1.0 / 1920;
                yscale = 384 / 1.0 / 1080;
            }            
            break;
        case MEDIA_SPLIT_IR_FIVE_1:
            if(s32Chn == 0)
            {
                xbias = 0;
                ybias = 0;
                xscale = 1280 / 1.0 / 1920;
                yscale = 720 /1.0 / 1080;
            }
            else if(s32Chn == 1)
            {
                xbias = 1280 / 1.0 / 1920;
                ybias = 0;
                xscale = 640 / 1.0 / 1920;
                yscale = 360 / 1.0 / 1080;
            }            
            break;
        case MEDIA_SPLIT_IR_FIVE_2:
            if(s32Chn == 0)
            {
                xbias = 1280 / 1.0 / 1920;
                ybias = 0;
                xscale = 640 / 1.0 / 1920;
                yscale = 360 / 1.0 / 1080;

            }
            else if(s32Chn == 1)
            {
                xbias = 0;
                ybias = 0;
                xscale = 1280 / 1.0 / 1920;
                yscale = 720 /1.0 / 1080;
            }            
            break;

        case MEDIA_SPLIT_FOUR:
        default:
            if(s32Chn == 0)
            {
                xbias = 0;
                ybias = 0;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = (1080 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
            }
            else if(s32Chn == 1)
            {
                xbias = (1920 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                ybias = 0;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = (1080 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
            }
            else if(s32Chn == 2)
            {
                xbias = 0;
                ybias = (1080 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = (1080 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
            }
            else if(s32Chn == 3)
            {
                xbias = (1920 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                ybias = (1080 + VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
                xscale = (1920 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1920;
                yscale = (1080 - VMIX_BLANK_LINE_WIDTH) / 2.0 / 1080;
            }
            break;
    }

    for(i = 0; i < s32Num; i++)
    {
        point[i].x = xbias + point[i].x * xscale;
        point[i].y = ybias + point[i].y * yscale;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 分屏时坐标映射
 * 输入参数: point --- 坐标映射点
             enSplitMode --- 分屏模式
 * 输出参数: point --- 坐标映射点
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_temp_SetMirrorAndFlip(SV_BOOL bMirror, SV_BOOL bFlip, MPP_GUI_POINT_S *pstPoint)
{
    MPP_GUI_POINT_S stPoint = {0};
    if(NULL == pstPoint)
    {
        return ERR_NULL_PTR;
    }
    if (bMirror)
    {
        stPoint.x = 1 - pstPoint->x;
        pstPoint->x = stPoint.x;        
    }

    if (bFlip)
    {
        stPoint.y = 1 - pstPoint->y;
        pstPoint->y = stPoint.y;        
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置温度全局参数
 * 输入参数: ir_usb_frame_data_t -- 视频参数行
 * 输出参数: 全局温度参数
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_SetTempParam (ir_usb_frame_data_t *pVideoData)
{
    MPP_GUI_POINT_S stPointMax, stPointMin;
    
    if (NULL == pVideoData)
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    m_stIRInfo.stTempInfo.fTempMax = pVideoData->paramLine[46]/10.0f;
    m_stIRInfo.stTempInfo.fTempMin = pVideoData->paramLine[49]/10.0f;
    m_stIRInfo.stTempInfo.fTempAvg = pVideoData->paramLine[53]/10.0f;
    stPointMax.x = pVideoData->paramLine[44] / (float) IR_RAW_WIDTH;
    stPointMax.y = pVideoData->paramLine[45] / (float) IR_RAW_HEIGHT;
    stPointMin.x = pVideoData->paramLine[47] / (float) IR_RAW_WIDTH;
    stPointMin.y = pVideoData->paramLine[48] / (float) IR_RAW_HEIGHT;

    mpp_ir_temp_SetMirrorAndFlip(m_stIRInfo.bImageMirror,m_stIRInfo.bImageFlip,&stPointMax);    
    mpp_ir_temp_SetMirrorAndFlip(m_stIRInfo.bImageMirror,m_stIRInfo.bImageFlip,&stPointMin);

    mpp_ir_Mapping(&stPointMax, m_stIRInfo.eCurIrMode, 1, 1);
    mpp_ir_Mapping(&stPointMin, m_stIRInfo.eCurIrMode, 1, 1);
    m_stIRInfo.stTempInfo.fTempMaxPosX = stPointMax.x;
    m_stIRInfo.stTempInfo.fTempMaxPosY= stPointMax.y;
    m_stIRInfo.stTempInfo.fTempMinPosX = stPointMin.x;
    m_stIRInfo.stTempInfo.fTempMinPosY= stPointMax.y;            
    
    if ((MEDIA_SPLIT_TWO == m_stIRInfo.eCurIrMode) 
        || (MEDIA_SPLIT_IR_ONE == m_stIRInfo.eCurIrMode)
        || (MEDIA_SPLIT_IR_OSD == m_stIRInfo.eCurIrMode)
        || (MEDIA_SPLIT_IR_OSD_2 == m_stIRInfo.eCurIrMode)
        || (MEDIA_SPLIT_IR_FIVE_1 == m_stIRInfo.eCurIrMode)
        || (MEDIA_SPLIT_IR_FIVE_2 == m_stIRInfo.eCurIrMode))
    {
        if (m_stIRInfo.stTempInfo.fTempMax >= IR_TRSHOLD_DARKRED)
        {
            m_stIRInfo.stTempInfo.bVaild = SV_TRUE;
            m_stIRInfo.stTempInfo.s32AlarmLv = 3;
        }
        else if (m_stIRInfo.stTempInfo.fTempMax >= IR_TRSHOLD_RED)
        {
            m_stIRInfo.stTempInfo.bVaild = SV_TRUE;
            m_stIRInfo.stTempInfo.s32AlarmLv = 2;
        }
        else if (m_stIRInfo.stTempInfo.fTempMax >= IR_TRSHOLD_YELLOW)
        {
            m_stIRInfo.stTempInfo.bVaild = SV_TRUE;
            m_stIRInfo.stTempInfo.s32AlarmLv = 1;
        }
        else
        {
            m_stIRInfo.stTempInfo.bVaild = SV_FALSE;
            m_stIRInfo.stTempInfo.s32AlarmLv = 0;
        }        
    }
    else
    {
        m_stIRInfo.stTempInfo.bVaild = SV_FALSE;
    }    
#if 0    
    print_level(SV_DEBUG , "max[%f] min[%f] avg[%f] bVaild[%d] level[%d] max_pos(%f %f)\n",\
                m_stIRInfo.stTempInfo.fTempMax,m_stIRInfo.stTempInfo.fTempMin,m_stIRInfo.stTempInfo.fTempAvg,\
                m_stIRInfo.stTempInfo.bVaild,m_stIRInfo.stTempInfo.s32AlarmLv,\
                m_stIRInfo.stTempInfo.fTempMaxPosX,m_stIRInfo.stTempInfo.fTempMaxPosY);    
#endif
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 串口获取数据回调函数
 * 输入参数: pSerialData -- 串口接收的数据
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_SerailCallBack(ir_usb_serial_data_t *pSerialData)
{
    IR_CAM_CFG_S *pstCmdCfg = &m_stIRInfo.stCmdCfg;
    pthread_mutex_lock(&m_stIRInfo.pIrCmdLock);
    if(pstCmdCfg->enCurCmd == IR_CMD_GET_STAT_VIDEO)
    {
        if (pSerialData->serial_recv_data[2] == 0x13 \
           && pSerialData->serial_recv_data[3] == 0x02 \
           && pSerialData->serial_recv_data[4] == 0x01)
        {
            pstCmdCfg->bGetRespon = SV_TRUE;
            if (BOARD_IsNotCustomer(BOARD_C_ADA32IR_100393))
            {
                pstCmdCfg->bIsRight = (pSerialData->serial_recv_data[7] == 0x01) ? SV_TRUE : SV_FALSE;
            }
            else
            {
                pstCmdCfg->bIsRight = (pSerialData->serial_recv_data[7] == 0x05) ? SV_TRUE : SV_FALSE;
            }
        }
    }
    else if (pstCmdCfg->enCurCmd == IR_CMD_GET_ANALYSIS_MODE)
    {
        if (pSerialData->serial_recv_data[2] == 0x28 \
            && pSerialData->serial_recv_data[3] == 0x03 \
            && pSerialData->serial_recv_data[4] == 0x04)
        {
            pstCmdCfg->bGetRespon = SV_TRUE;
            pstCmdCfg->bIsRight = (pSerialData->serial_recv_data[5] == 0x01) ? SV_TRUE : SV_FALSE;
        }
    }
    else if (pstCmdCfg->enCurCmd == IR_CMD_GET_TEMP_MODE)
    {
        if ((pSerialData->serial_recv_data[2] == 0x19 \
            && pSerialData->serial_recv_data[3] == 0x04 \
            && pSerialData->serial_recv_data[4] == 0x00))
        {
            pstCmdCfg->bGetRespon = SV_TRUE;
            pstCmdCfg->bIsRight = (pSerialData->serial_recv_data[7] == 0x00) ? SV_TRUE : SV_FALSE;
        }
    }
    pthread_mutex_unlock(&m_stIRInfo.pIrCmdLock);
    return 0;
}

/******************************************************************************
 * 函数功能: 连接状态回调函数
 * 输入参数: deviceStatus -- 设备连接状态
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
int ir_usb_ConStatCallBack(ir_usb_device_status_e deviceStatus)
{
    switch (deviceStatus)
    {
        case DEVICE_CONNECT_OK:
            print_level(SV_DEBUG, "IrCam Connect\n");
            break;
        
        case DEVICE_DISCONNECT_OK:
            print_level(SV_DEBUG, "IrCam Disconnect \n");
            break;
        default:
            break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取USB数据帧回调函数
 * 输入参数: pVideoData -- 接收到的数据
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
int ir_usb_FrameCallBack(ir_usb_frame_data_t *pVideoData)
{
    sint32 s32CurIdx = 0, i;
    unsigned char *pData = (unsigned char *)pVideoData->frame_yuv_data;
    unsigned int u32Size = pVideoData->frame_yuv_data_length *2;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    SV_BOOL bGetFrame = SV_FALSE;

    s64LastTime = microtime();
    if(NULL == pData)
    {
        return ERR_NULL_PTR;
    }
    pthread_mutex_lock(&m_stIRInfo.pIrDistributionLock);

    s32CurIdx = (m_stIRInfo.s32RawIdx + 1) % IR_HEAP_BUF_NUM;
    for(i=0; i < IR_HEAP_BUF_NUM; i++)
    {
        if((IR_HEAP_BUF_WRITE == m_stIRInfo.astRawHeapBuf[s32CurIdx].enBufStatus) || (IR_HEAP_BUF_RW ==  m_stIRInfo.astRawHeapBuf[s32CurIdx].enBufStatus))
        {
            m_stIRInfo.s32RawIdx = s32CurIdx;
            bGetFrame = SV_TRUE;
            break;
        }
        s32CurIdx = (s32CurIdx+ 1) % IR_HEAP_BUF_NUM;
    }
    
    if(SV_FALSE == bGetFrame)
    {
        goto skip;
    }
    
    memcpy(m_stIRInfo.astRawHeapBuf[s32CurIdx].virAddr, pData, u32Size);
    if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
    {
#if 0   
        /* 测温的源Y16数据 */
        memcpy(m_stIRInfo.astRawTempHeapBuf[s32CurIdx].virAddr, pVideoData->frame_src_data, pVideoData->frame_src_data_length *2);
        if (NULL != pVideoData->paramLine)
        {
            memcpy(m_stIRInfo.pas16ParamLine[s32CurIdx], pVideoData->paramLine, IR_RAW_WIDTH * sizeof(short));
            //print_level(SV_DEBUG, "temperature max[%f] min[%f]\n",pVideoData->paramLine[46]/10.0f, pVideoData->paramLine[49]/10.0f);
        }
#endif        
        if (NULL != pVideoData->paramLine)
        {
            mpp_ir_SetTempParam(pVideoData);
        }        
    }
    
    m_stIRInfo.astRawHeapBuf[s32CurIdx].enBufStatus = IR_HEAP_BUF_RW;
    s64CurTime = microtime();
    //print_level(SV_DEBUG, "get frame: %d %lld %lld %lld\n",s32CurIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));

skip:
    m_stIRInfo.bGetFrame = SV_TRUE;
    pthread_mutex_unlock(&m_stIRInfo.pIrDistributionLock);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置全局命令状态
 * 输入参数: enCmdType -- 命令类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_SetCmdStat(IR_CMD_TYPE_E enCmdType)
{
    pthread_mutex_lock(&m_stIRInfo.pIrCmdLock);

    m_stIRInfo.stCmdCfg.enCurCmd = enCmdType;
    m_stIRInfo.stCmdCfg.bGetRespon = SV_FALSE;
    m_stIRInfo.stCmdCfg.bIsRight = SV_FALSE;

    pthread_mutex_unlock(&m_stIRInfo.pIrCmdLock);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 清除全局命令状态
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_ClearCmdStat(void)
{
    pthread_mutex_lock(&m_stIRInfo.pIrCmdLock);

    m_stIRInfo.stCmdCfg.enCurCmd = IR_CMD_BUTT;
    m_stIRInfo.stCmdCfg.bGetRespon = SV_FALSE;
    m_stIRInfo.stCmdCfg.bIsRight = SV_FALSE;
    
    pthread_mutex_unlock(&m_stIRInfo.pIrCmdLock);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取全局命令状态
 * 输入参数: 无
 * 输出参数: pstCmdStat -- 命令
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_GetCmdStat(IR_CAM_CFG_S *pstCmdStat)
{
    if (NULL == pstCmdStat)
    {
        return ERR_NULL_PTR;
    }
    pthread_mutex_lock(&m_stIRInfo.pIrCmdLock);
    memcpy(pstCmdStat, &m_stIRInfo.stCmdCfg, sizeof(IR_CAM_CFG_S));    
    pthread_mutex_unlock(&m_stIRInfo.pIrCmdLock);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置单独某项参数
 * 输入参数: pstSendCmd -- 发送命令 pstRecvCmd -- 接收命令 s32Timeout -- 超时
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_SetDevItem(IR_CMD_S *pstSendCmd, IR_CMD_S *pstRecvCmd, sint32 s32Timeout)
{
    sint32 s32Ret = 0;
    SV_BOOL bFail = SV_FALSE;
    IR_CAM_CFG_S stCmdStat = {0};

    if (NULL ==pstSendCmd  || NULL == pstRecvCmd || s32Timeout <= 0)
    {
        return ERR_ILLEGAL_PARAM;
    }
    /* 发送设置命令 */
    ir_usb_SetCmdStat(pstSendCmd->enCmdType);
    s32Ret = ir_usb_sendcommand(pstSendCmd->aszCommand, pstSendCmd->s32Len);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "send cmd[%d] failed\n", pstSendCmd->enCmdType);
        bFail = SV_TRUE;
        goto exit;
    }
    sleep_ms(s32Timeout);
    /* 发送查询设置命令 */
    ir_usb_SetCmdStat(pstRecvCmd->enCmdType);
    s32Ret = ir_usb_sendcommand(pstRecvCmd->aszCommand, pstRecvCmd->s32Len);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "send cmd[%d] failed\n", pstRecvCmd->enCmdType);
        bFail = SV_TRUE;
        goto exit;
    }    
    
    sleep_ms(s32Timeout);
    ir_usb_GetCmdStat(&stCmdStat);
    if (!stCmdStat.bIsRight)
    {
        print_level(SV_ERROR, "set cmd[%d] failed\n", pstSendCmd->enCmdType);
        bFail = SV_TRUE;
        goto exit;
    }
    /* 保存设置 */
    ir_usb_SetCmdStat(IR_CMD_SET_SAVE);
    s32Ret = ir_usb_sendcommand(command[IR_CMD_SET_SAVE].aszCommand, command[IR_CMD_SET_SAVE].s32Len);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "save command failed\n");
        bFail = SV_TRUE;
        goto exit;
    }
    print_level(SV_DEBUG, "set cmd[%d] success!\n",pstSendCmd->enCmdType);
exit:
    ir_usb_ClearCmdStat();

    if(bFail) 
        return SV_FAILURE;
    else
        return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 设置红外摄像头配置参数
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_SetDevParm(void)
{
    sint32 s32Ret = -1;
    SV_BOOL bFail = SV_FALSE;
    
    IR_CAM_CFG_S stCmdStat = {0};

    /* 获取视频参数格式 */
    ir_usb_SetCmdStat(IR_CMD_GET_STAT_VIDEO);
    s32Ret = ir_usb_sendcommand(command[IR_CMD_GET_STAT_VIDEO].aszCommand, command[IR_CMD_GET_ANALYSIS_MODE].s32Len);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "get video param failed\n");
        bFail = SV_TRUE;
        goto exit;
    }
    sleep_ms(200);
    
    ir_usb_GetCmdStat(&stCmdStat);
    if (!stCmdStat.bIsRight)
    {
        print_level(SV_WARN, "ir cam config dismatch\n");
        /* 设置红外摄像头参数 */
        if(stCmdStat.bGetRespon)
        {
            if (BOARD_IsNotCustomer(BOARD_C_ADA32IR_100393))
            {
                s32Ret = ir_usb_SetDevItem(&command[IR_CMD_SET_VIDEO_YUV422], &command[IR_CMD_GET_STAT_VIDEO], 200);
                if (SV_SUCCESS != s32Ret)
                {
                    bFail = SV_TRUE;
                    goto exit;
                }
            }
            else
            {
                s32Ret = ir_usb_SetDevItem(&command[IR_CMD_SET_VIDEO_Y16_YUV422], &command[IR_CMD_GET_STAT_VIDEO], 200);
                if (SV_SUCCESS != s32Ret)
                {
                    bFail = SV_TRUE;
                    goto exit;
                }
                s32Ret = ir_usb_SetDevItem(&command[IR_CMD_SET_ANALYSIS_MODE], &command[IR_CMD_GET_ANALYSIS_MODE], 200);
                if (SV_SUCCESS != s32Ret)
                {
                    bFail = SV_TRUE;
                    goto exit;
                }
                s32Ret = ir_usb_SetDevItem(&command[IR_CMD_SET_TMP_MODE], &command[IR_CMD_GET_TEMP_MODE], 200);
                if (SV_SUCCESS != s32Ret)
                {
                    bFail = SV_TRUE;
                    goto exit;
                }
            }
        }
    }
    
    /* 测温程序需要确认范围和全屏参数 */
    if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
    {
        ir_usb_SetCmdStat(IR_CMD_GET_ANALYSIS_MODE);
        s32Ret = ir_usb_sendcommand(command[IR_CMD_GET_ANALYSIS_MODE].aszCommand, command[IR_CMD_GET_ANALYSIS_MODE].s32Len);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "get video param failed\n");
            bFail = SV_TRUE;
            goto exit;
        }
        sleep_ms(200);
        ir_usb_GetCmdStat(&stCmdStat);
        if (!stCmdStat.bIsRight)
        {
            s32Ret = ir_usb_SetDevItem(&command[IR_CMD_SET_ANALYSIS_MODE], &command[IR_CMD_GET_ANALYSIS_MODE], 200);
            if (SV_SUCCESS != s32Ret)
            {
                bFail = SV_TRUE;
                goto exit;
            }
        }

        ir_usb_SetCmdStat(IR_CMD_GET_TEMP_MODE);
        s32Ret = ir_usb_sendcommand(command[IR_CMD_GET_TEMP_MODE].aszCommand, command[IR_CMD_GET_ANALYSIS_MODE].s32Len);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "get video param failed\n");
            bFail = SV_TRUE;
            goto exit;
        }
        sleep_ms(200);
        ir_usb_GetCmdStat(&stCmdStat);
        if (!stCmdStat.bIsRight)
        {
            s32Ret = ir_usb_SetDevItem(&command[IR_CMD_SET_TMP_MODE], &command[IR_CMD_GET_TEMP_MODE], 200);
            if (SV_SUCCESS != s32Ret)
            {
                bFail = SV_TRUE;
                goto exit;
            }
        }
    }
exit:
    ir_usb_ClearCmdStat();

    if(bFail) 
        return SV_FAILURE;
    else
        return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 打开红外摄像头，开始传输数据
 * 输入参数: pstUSBDevInfo -- usb图像信息  serailCallBack -- 串口回调函数
              conStatCallBack -- 连接状态回调函数             frameCallBack -- 数据帧回调函数
 * 输出参数: pstOutStat -- 输出状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_Open(ir_usb_device_info_t *pstUSBDevInfo, IR_SERIAL_DATA_CALLBACK serailCallBack, IR_USB_DATA_CALLBACK conStatCallBack, \
                        IR_MEDIA_DATA_CALLBACK frameCallBack ,SV_BOOL *pstOutStat)
{
    sint32 s32Ret = -1;
    static SV_BOOL bLoadTmepCure = SV_FALSE;
    
    if(NULL == pstUSBDevInfo || NULL == pstOutStat)
    {
        return SV_FAILURE;
    }
    
    /* 初始化设备 */
    s32Ret = ir_usb_initial();
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "Initial fail:%d \n",s32Ret);
        return SV_FAILURE;
    }
    /* 打开串口命令，不做判断 */
    s32Ret = ir_usb_getserialdata((IR_SERIAL_DATA_CALLBACK)serailCallBack);

    /* 配置参数 */
    s32Ret = ir_usb_SetDevParm();
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "ir_usb_SetDevParm failed [%x]\n",s32Ret);
        goto fail_exit;
    }

    /* 加载测温曲线 */
    if ((!bLoadTmepCure) && (BOARD_IsCustomer(BOARD_C_ADA32IR_100393)))
    {   
#if ((MOUDLETYPE == MOUDLETYPE_TC933) || (MOUDLETYPE == MOUDLETYPE_TC639_T2))
        s32Ret = ir_usb_loadtempcurve();//有概率导致开机时线程崩溃
#endif
        bLoadTmepCure = SV_TRUE;
        *pstOutStat = bLoadTmepCure;
    }

    /* 启动视频流 */
    s32Ret = ir_usb_openstream(pstUSBDevInfo, frameCallBack, conStatCallBack);
    if(s32Ret < 0)
    {
        print_level(SV_ERROR, "open usbcam failed [%x]\n", s32Ret);
        goto fail_exit;
    }

    return SV_SUCCESS;
    
fail_exit:
    if(BOARD_IsCustomer(BOARD_C_ADA32IR_100393) && bLoadTmepCure)
    {
#if ((MOUDLETYPE == MOUDLETYPE_TC933) || (MOUDLETYPE == MOUDLETYPE_TC639_T2))
        s32Ret = ir_usb_deloadtempcurve();
#endif
        bLoadTmepCure = SV_FALSE;
        *pstOutStat = bLoadTmepCure;
    }
    s32Ret = ir_usb_closecommandcontrol();
    s32Ret = ir_usb_exit();
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 关闭红外摄像头
 * 输入参数: pbFlag -- 标志位
 * 输出参数: pbLoardTmepCure -- 加载温度状态
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_usb_Close(SV_BOOL *pbFlag)
{
    sint32 s32Ret = -1;
    
    if(NULL == pbFlag)
    {
        return ERR_NULL_PTR;
    }
    
    s32Ret = ir_usb_closestream();
    
    if(BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
    {
        if (*pbFlag)
        {
#if ((MOUDLETYPE == MOUDLETYPE_TC933) || (MOUDLETYPE == MOUDLETYPE_TC639_T2))
            s32Ret = ir_usb_deloadtempcurve();
            *pbFlag = SV_FALSE;
#endif
        }
    }
    s32Ret = ir_usb_closecommandcontrol();
    s32Ret = ir_usb_exit();
    if(s32Ret < 0)
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将UYVY 422Pack格式的数据化转为422P Plannar格式的数据
 * 输入参数: p8Pcak -- 源数据 p8Planar -- 转换的数据 u32Wdith -- 宽 u32Height -- 高
 *          u8bpp 每像素位数（字节为单位）
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_convert_YUV422PackToPlanar(uint8 *p8Pcak, uint8 *p8Planar, uint32 u32Wdith, uint32 u32Height)
{
    uint32 i;
    uint8 *p8Src = p8Pcak;
    uint8 *pDataY = p8Planar;
    uint8 *pDataUV = p8Planar + u32Wdith * u32Height;
    uint32 u32Size = u32Wdith * u32Height * 2;
    if (NULL == p8Pcak || NULL == p8Planar)
    {
        return SV_FAILURE;
    }

    for (i=0; i<u32Size; i+=2)
    {
        *pDataUV++ = *p8Src++;
        *pDataY++ = *p8Src++;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: Y16灰度图化转为422P Plannar格式的数据
 * 输入参数: pcSrc -- 源数据  u32Wdith -- 宽 u32Height -- 高
 *           pfTemp 对应温度矩阵
 * 输出参数: pcDst -- 转换的数据
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_convert_Y162YUV422Planer(char *pcSrc, char *pcDst, float *pfTemp, uint32 u32Wdith, uint32 u32Height)
{
    sint32 i;
    uint16 *p16Src = (unsigned short *)pcSrc;
	uint16 u16Tmp = 0;
    uint8 u8Tmp = 0;
	uint8 *pDataY = pcDst;
    uint8 *pDataUV = pcDst + u32Wdith * u32Height;
    uint32 u32Size = u32Wdith * u32Height;
	for (i=0; i<u32Size; i++)
	{
		u16Tmp = (*p16Src++);
		//*pDataY++ = (pfTemp[i] > 30) ? (uint8)u16Tmp : 0;
		*pDataY++ = (uint8)u16Tmp;
#if 0		
		if(pfTemp[i] > 30)
        {
            u8Tmp =  (uint8)u16Tmp;
            *pDataY = (u8Tmp >> 6) << 8;
        }
        else
        {
            *pDataY = 0;
        }
#endif        
	}
	memset(pDataUV, 128, u32Size);
	
	return 0;
}

/******************************************************************************
 * 函数功能: 将UYVY 422Pack格式的数据化转为422P Plannar格式的数据
 * 输入参数: p8Pcak -- 源数据 p8Planar -- 转换的数据 u32Wdith -- 宽 u32Height -- 高
 *          u8bpp 每像素位数（字节为单位）
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ir_convert_YUV422PackToPlanarTemp(uint8 *p8Pcak, uint8 *p8Planar, float *pfTemp, uint32 u32Wdith, uint32 u32Height)
{
    uint32 i,j;
    uint8 *p8Src = p8Pcak;
    uint8 *pDataY = p8Planar;
    uint8 *pDataUV = p8Planar + u32Wdith * u32Height;
    uint32 u32Size = u32Wdith * u32Height * 2;
    if (NULL == p8Pcak || NULL == p8Planar)
    {
        return SV_FAILURE;
    }

    for (i=0,j=0; i<u32Size; i+=2)
    {
#if 1    
        j = i>>1;
        if(pfTemp[j] > 25.0)
        {
            *pDataUV++ = *p8Src++;           
            *pDataY++ = ((*p8Src)/128)*255;//相当于低通滤波
            p8Src++;
            //*pDataY++ = *p8Src++;
            //*pDataUV++ = 128;
            //*pDataY++ = 180;
            //p8Src++;
            //p8Src++;            
        }
        else
        {
            *pDataUV++ = 128;
            *pDataY++ = 16;
            p8Src++;
            p8Src++;
        }
#endif

    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 转换源数据格式
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/
sint32 ir_convert_ScaleCopy(IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst, sint32 s32MirrorFlip)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_422_SP;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_422_SP;
    rga_src.bufferSize      = pstDrmBuf_src->u32Size;
    rga_src.rotation        = s32MirrorFlip;

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;
#if 0
    print_level(SV_DEBUG,"src X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                rga_src.rect.xoffset, rga_src.rect.yoffset, rga_src.rect.width, rga_src.rect.height, rga_src.rect.wstride, rga_src.rect.hstride,\
                rga_src.rect.format, rga_src.fd, rga_src.virAddr,rga_src.bufferSize);    
    print_level(SV_DEBUG,"dst X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                rga_dst.rect.xoffset, rga_dst.rect.yoffset, rga_dst.rect.width, rga_dst.rect.height, rga_dst.rect.wstride, rga_dst.rect.hstride,\
                rga_dst.rect.format, rga_dst.fd, rga_dst.virAddr,rga_dst.bufferSize);
#endif
    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 转换源数据格式
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/
sint32 ir_convert_ScaleCopyHeap(IR_HEAP_BUF_INFO_S *pstHeapBuf_src, IR_HEAP_BUF_INFO_S *pstHeapBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst, sint32 s32MirrorFlip)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstHeapBuf_dst == NULL || pstHeapBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstHeapBuf_src->u32Width;
    rga_src.rect.hstride    = pstHeapBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_422_SP;
    rga_src.virAddr         = pstHeapBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_422_SP;
    rga_src.bufferSize      = pstHeapBuf_src->u32Size;
    rga_src.rotation        = s32MirrorFlip;

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstHeapBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstHeapBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.virAddr         = pstHeapBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstHeapBuf_dst->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 转换源数据格式
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/
sint32 ir_convert_ScaleCopyShm(IR_SHM_BUF_INFO_S *pstShmBuf_src, IR_SHM_BUF_INFO_S *pstShmBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst, sint32 s32MirrorFlip)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstShmBuf_dst == NULL || pstShmBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstShmBuf_src->u32Width;
    rga_src.rect.hstride    = pstShmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCrCb_422_P;//RK_FORMAT_YCbCr_422_SP;
    rga_src.virAddr         = pstShmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCrCb_422_P;
    rga_src.bufferSize      = pstShmBuf_src->u32Size;
    rga_src.rotation        = s32MirrorFlip;

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstShmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstShmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.virAddr         = pstShmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstShmBuf_dst->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}
/******************************************************************************
 * 函数功能: 转换源数据格式(拉姆达机芯用)
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/

sint32 mpp_ir_Uvc_ScaleCopy(IR_HEAP_BUF_INFO_S *pstHeapBuf_src, IR_HEAP_BUF_INFO_S *pstHeapBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstHeapBuf_dst == NULL || pstHeapBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstHeapBuf_src->u32Width;
    rga_src.rect.hstride    = pstHeapBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YUYV_422;
    rga_src.virAddr         = pstHeapBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YUYV_422;
    rga_src.bufferSize      = pstHeapBuf_src->u32Size;

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstHeapBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstHeapBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCrCb_422_P;
    rga_dst.virAddr         = pstHeapBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCrCb_422_P;
    rga_dst.bufferSize      = pstHeapBuf_dst->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}



/******************************************************************************
/******************************************************************************
 * 函数功能: 转换源数据格式
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/
sint32 mpp_ir_vo_ScaleCopy(IR_HEAP_BUF_INFO_S *pstHeapBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst, sint32 s32MirrorFlip)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstHeapBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstHeapBuf_src->u32Width;
    rga_src.rect.hstride    = pstHeapBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCrCb_422_P;//RK_FORMAT_YCbCr_422_SP;
    rga_src.virAddr         = pstHeapBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCrCb_422_P;
    rga_src.bufferSize      = pstHeapBuf_src->u32Size;
    rga_src.rotation        = s32MirrorFlip;

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 转换源数据格式
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
              s32MirrorFlip -- 镜像翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/
sint32 mpp_ir_alg_ScaleCopy(IR_HEAP_BUF_INFO_S *pstHeapBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst, sint32 s32MirrorFlip)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstHeapBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstHeapBuf_src->u32Width;
    rga_src.rect.hstride    = pstHeapBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCrCb_422_P;//RK_FORMAT_YCbCr_422_SP;
    rga_src.virAddr         = pstHeapBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCrCb_422_P;
    rga_src.bufferSize      = pstHeapBuf_src->u32Size;
    rga_src.rotation        = s32MirrorFlip;

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_RGB_888;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_RGB_888;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 转换源数据格式
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框 
              s32MirrorFlip -- 镜像翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/
sint32 mpp_ir_venc_ScaleCopy(IR_HEAP_BUF_INFO_S *pstHeapBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst, sint32 s32MirrorFlip)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstHeapBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstHeapBuf_src->u32Width;
    rga_src.rect.hstride    = pstHeapBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCrCb_422_P;//RK_FORMAT_YCbCr_422_SP;
    rga_src.virAddr         = pstHeapBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCrCb_422_P;
    rga_src.bufferSize      = pstHeapBuf_src->u32Size;
    rga_src.rotation        = s32MirrorFlip;


    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 转换源数据格式
 * 输入参数: pstDrmBuf_src -- 源Drm buffer pstDrmBuf_dst -- 目的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框 
              s32MirrorFlip -- 镜像翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 显示部分只需要做一次镜像和翻转就跟原图一样了，原因是硬件本来默认就是反的
             然后后面显示的叠加部分会自动做图像的镜像翻转
 *****************************************************************************/
sint32 mpp_ir_venc_ScaleCopyHeap(IR_HEAP_BUF_INFO_S *pstHeapBuf_src, IR_HEAP_BUF_INFO_S *pstHeapBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst, sint32 s32MirrorFlip)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstHeapBuf_dst == NULL || pstHeapBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_FAILURE;
    }

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstHeapBuf_src->u32Width;
    rga_src.rect.hstride    = pstHeapBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCrCb_422_P;//RK_FORMAT_YCbCr_422_SP;
    rga_src.virAddr         = pstHeapBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCrCb_422_P;
    rga_src.bufferSize      = pstHeapBuf_src->u32Size;
    rga_src.rotation        = s32MirrorFlip;


    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstHeapBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstHeapBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.virAddr         = pstHeapBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstHeapBuf_dst->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 红外合成显示图像格式：可见光
 * 输入参数: pvBuf -- 可见光摄像头media buffer  pstDrmBuf_dst -- 目的显示合成的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 ir_convert_vmix_VisCam(void **pvBuf, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstRgn_src->u32Width;
    rga_src.rect.hstride    = pstRgn_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = RK_MPI_MB_GetFD(*pvBuf);
    rga_src.virAddr         = RK_MPI_MB_GetPtr(*pvBuf);
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = RK_MPI_MB_GetSize(*pvBuf);

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

    return SV_SUCCESS;    
}

/******************************************************************************
 * 函数功能: 红外合成显示图像格式：红外光单独显示
 * 输入参数: pstDrmBuf_src -- 源Drm buffer  pstDrmBuf_dst -- 目的显示合成的Drm buffer  
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 ir_convert_vmix_IrCam(IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = pstDrmBuf_src->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

    return SV_SUCCESS;    
}

/******************************************************************************
 * 函数功能: 红外合成显示图像格式osd叠加模式
 * 输入参数: pvBuf -- vpss 可见光media buffer  pstDrmBuf_src -- 源红外Drm buffer
              pstDrmBuf_dst -- 目的显示合成的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 ir_convert_vmix_Osd(void **pvBuf, IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgnVis_src, SV_RECT_S *pstRgnIr_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgnVis_src == NULL || pstRgnIr_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;    
    
    rga_src.rect.xoffset    = pstRgnVis_src->s32X;
    rga_src.rect.yoffset    = pstRgnVis_src->s32Y;
    rga_src.rect.width      = pstRgnVis_src->u32Width;
    rga_src.rect.height     = pstRgnVis_src->u32Height;
    rga_src.rect.wstride    = pstRgnVis_src->u32Width;
    rga_src.rect.hstride    = pstRgnVis_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = RK_MPI_MB_GetFD(*pvBuf);
    rga_src.virAddr         = RK_MPI_MB_GetPtr(*pvBuf);
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = RK_MPI_MB_GetSize(*pvBuf);
    
    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

    rga_dst.rect.xoffset    = IR_OSD_POS_BAIS_X;
    rga_dst.rect.yoffset    = IR_OSD_POS_BAIS_Y;
    rga_dst.rect.width      = IR_OSD_WIDTH;
    rga_dst.rect.height     = IR_OSD_HEIGHT;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgnIr_src->s32X;
    rga_src.rect.yoffset    = pstRgnIr_src->s32Y;
    rga_src.rect.width      = pstRgnIr_src->u32Width;
    rga_src.rect.height     = pstRgnIr_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = pstDrmBuf_src->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
   

}

/******************************************************************************
 * 函数功能: 红外合成显示图像格式osd叠加模式
 * 输入参数: pvBuf -- vpss 可见光media buffer  pstDrmBuf_src -- 源红外Drm buffer
              pstDrmBuf_dst -- 目的显示合成的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 ir_convert_vmix_Osd_2(void **pvBuf, IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgnVis_src, SV_RECT_S *pstRgnIr_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgnVis_src == NULL || pstRgnIr_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;    
    
    rga_src.rect.xoffset    = pstRgnIr_src->s32X;
    rga_src.rect.yoffset    = pstRgnIr_src->s32Y;
    rga_src.rect.width      = pstRgnIr_src->u32Width;
    rga_src.rect.height     = pstRgnIr_src->u32Height;
    rga_src.rect.wstride    = pstRgnIr_src->u32Width;
    rga_src.rect.hstride    = pstRgnIr_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = pstDrmBuf_src->u32Size;
#if 0
        print_level(SV_DEBUG,"src X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                    rga_src.rect.xoffset, rga_src.rect.yoffset, rga_src.rect.width, rga_src.rect.height, rga_src.rect.wstride, rga_src.rect.hstride,\
                    rga_src.rect.format, rga_src.fd, rga_src.virAddr,rga_src.bufferSize);    
        print_level(SV_DEBUG,"dst X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                    rga_dst.rect.xoffset, rga_dst.rect.yoffset, rga_dst.rect.width, rga_dst.rect.height, rga_dst.rect.wstride, rga_dst.rect.hstride,\
                    rga_dst.rect.format, rga_dst.fd, rga_dst.virAddr,rga_dst.bufferSize);
#endif

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

    rga_dst.rect.xoffset    = IR_OSD_POS_BAIS_X;
    rga_dst.rect.yoffset    = IR_OSD_POS_BAIS_Y;
    rga_dst.rect.width      = IR_OSD_WIDTH;
    rga_dst.rect.height     = IR_OSD_HEIGHT;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgnVis_src->s32X;
    rga_src.rect.yoffset    = pstRgnVis_src->s32Y;
    rga_src.rect.width      = pstRgnVis_src->u32Width;
    rga_src.rect.height     = pstRgnVis_src->u32Height;
    rga_src.rect.wstride    = pstRgnVis_src->u32Width;
    rga_src.rect.hstride    = pstRgnVis_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = RK_MPI_MB_GetFD(*pvBuf);
    rga_src.virAddr         = RK_MPI_MB_GetPtr(*pvBuf);
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = RK_MPI_MB_GetSize(*pvBuf);
#if 0
            print_level(SV_DEBUG,"src X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                        rga_src.rect.xoffset, rga_src.rect.yoffset, rga_src.rect.width, rga_src.rect.height, rga_src.rect.wstride, rga_src.rect.hstride,\
                        rga_src.rect.format, rga_src.fd, rga_src.virAddr,rga_src.bufferSize);    
            print_level(SV_DEBUG,"dst X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                        rga_dst.rect.xoffset, rga_dst.rect.yoffset, rga_dst.rect.width, rga_dst.rect.height, rga_dst.rect.wstride, rga_dst.rect.hstride,\
                        rga_dst.rect.format, rga_dst.fd, rga_dst.virAddr,rga_dst.bufferSize);
#endif

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
   

}


/******************************************************************************
 * 函数功能: 红外合成显示图像格式对称二分屏模式
 * 输入参数: pvBuf -- vpss 可见光media buffer  pstDrmBuf_src -- 源红外Drm buffer
              pstDrmBuf_dst -- 目的显示合成的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 ir_convert_vmix_DivSym(void **pvBuf, IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgnVis_src, SV_RECT_S *pstRgnIr_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgnVis_src == NULL || pstRgnIr_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }


    rga_dst.rect.xoffset    = ((pstRgn_dst->u32Width + IR_VMIX_LINE_WIDTH)>> 1);
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = ((pstRgn_dst->u32Width - IR_VMIX_LINE_WIDTH)>> 1);
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgnIr_src->s32X;
    rga_src.rect.yoffset    = pstRgnIr_src->s32Y;
    rga_src.rect.width      = pstRgnIr_src->u32Width;
    rga_src.rect.height     = pstRgnIr_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = pstDrmBuf_src->u32Size;
    
    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
    
    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = ((pstRgn_dst->u32Width - IR_VMIX_LINE_WIDTH)>> 1);
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;    
    
    rga_src.rect.xoffset    = pstRgnVis_src->s32X;
    rga_src.rect.yoffset    = pstRgnVis_src->s32Y;
    rga_src.rect.width      = pstRgnVis_src->u32Width;
    rga_src.rect.height     = pstRgnVis_src->u32Height;
    rga_src.rect.wstride    = pstRgnVis_src->u32Width;
    rga_src.rect.hstride    = pstRgnVis_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = RK_MPI_MB_GetFD(*pvBuf);
    rga_src.virAddr         = RK_MPI_MB_GetPtr(*pvBuf);
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = RK_MPI_MB_GetSize(*pvBuf);
    
    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }
}

/******************************************************************************
 * 函数功能: 红外合成显示图像格式非对称二分屏模式，可见光主屏
 * 输入参数: pvBuf -- vpss 可见光media buffer  pstDrmBuf_src -- 源红外Drm buffer
              pstDrmBuf_dst -- 目的显示合成的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 ir_convert_vmix_DivAsymVisCam(void **pvBuf, IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgnVis_src, SV_RECT_S *pstRgnIr_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgnVis_src == NULL || pstRgnIr_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = ((pstRgn_dst->u32Width / 3) << 1) - (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.height     = ((pstRgn_dst->u32Height / 3) << 1) - (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;    
    
    rga_src.rect.xoffset    = pstRgnVis_src->s32X;
    rga_src.rect.yoffset    = pstRgnVis_src->s32Y;
    rga_src.rect.width      = pstRgnVis_src->u32Width;
    rga_src.rect.height     = pstRgnVis_src->u32Height;
    rga_src.rect.wstride    = pstRgnVis_src->u32Width;
    rga_src.rect.hstride    = pstRgnVis_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = RK_MPI_MB_GetFD(*pvBuf);
    rga_src.virAddr         = RK_MPI_MB_GetPtr(*pvBuf);
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = RK_MPI_MB_GetSize(*pvBuf);

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

    rga_dst.rect.xoffset    = ((pstRgn_dst->u32Width / 3) << 1) + (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width        = (pstRgn_dst->u32Width / 3) - (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.height     = (pstRgn_dst->u32Height / 3) - (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd                = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format            = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize        = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgnIr_src->s32X;
    rga_src.rect.yoffset    = pstRgnIr_src->s32Y;
    rga_src.rect.width      = pstRgnIr_src->u32Width;
    rga_src.rect.height     = pstRgnIr_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd                = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format            = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize        = pstDrmBuf_src->u32Size;


    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }    
}

/******************************************************************************
 * 函数功能: 红外合成显示图像格式非对称二分屏模式,红外光主屏
 * 输入参数: pvBuf -- vpss 可见光media buffer  pstDrmBuf_src -- 源红外Drm buffer
              pstDrmBuf_dst -- 目的显示合成的Drm buffer 
              pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 ir_convert_vmix_DivAsymIrCam(void **pvBuf, IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_DRM_BUF_INFO_S *pstDrmBuf_dst, SV_RECT_S *pstRgnVis_src, SV_RECT_S *pstRgnIr_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgnVis_src == NULL || pstRgnIr_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = ((pstRgn_dst->u32Width / 3) << 1) - (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.height     = ((pstRgn_dst->u32Height / 3) << 1)- (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;    

    rga_src.rect.xoffset    = pstRgnIr_src->s32X;
    rga_src.rect.yoffset    = pstRgnIr_src->s32Y;
    rga_src.rect.width      = pstRgnIr_src->u32Width;
    rga_src.rect.height     = pstRgnIr_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd                = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format            = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize        = pstDrmBuf_src->u32Size;

    
    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

    rga_dst.rect.xoffset    = ((pstRgn_dst->u32Width / 3) << 1) + (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width        = (pstRgn_dst->u32Width / 3) - (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.height     = (pstRgn_dst->u32Height / 3) - (IR_VMIX_LINE_WIDTH >> 1);
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd                = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format            = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize        = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgnVis_src->s32X;
    rga_src.rect.yoffset    = pstRgnVis_src->s32Y;
    rga_src.rect.width      = pstRgnVis_src->u32Width;
    rga_src.rect.height     = pstRgnVis_src->u32Height;
    rga_src.rect.wstride    = pstRgnVis_src->u32Width;
    rga_src.rect.hstride    = pstRgnVis_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = RK_MPI_MB_GetFD(*pvBuf);
    rga_src.virAddr         = RK_MPI_MB_GetPtr(*pvBuf);
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = RK_MPI_MB_GetSize(*pvBuf);


    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }    
}

/******************************************************************************
 * 函数功能: Vmix线程合成后的图像由1920*1080的YUV420sp图像转换为720*480或者720*576的UYVY422格式
 * 输入参数: pvBuf -- vpss 可见光media buffer  
             pstDrmBuf_src -- 源红外Drm buffer
             pstDrmBuf_dst -- 目的显示合成的Drm buffer 
             pstRgn_src -- 源rag矩形框 pstRgn_dst --     目的rag矩形框
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 

 *****************************************************************************/
sint32 ir_convert_Cvbs(IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_HEAP_BUF_INFO_S *pstHeapBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstHeapBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstHeapBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstHeapBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_UYVY_422;
    rga_dst.virAddr         = pstHeapBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_UYVY_422;
    rga_dst.bufferSize      = pstHeapBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = pstDrmBuf_src->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

#if 0
            print_level(SV_DEBUG,"src X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                        rga_src.rect.xoffset, rga_src.rect.yoffset, rga_src.rect.width, rga_src.rect.height, rga_src.rect.wstride, rga_src.rect.hstride,\
                        rga_src.rect.format, rga_src.fd, rga_src.virAddr,rga_src.bufferSize);    
            print_level(SV_DEBUG,"dst X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                        rga_dst.rect.xoffset, rga_dst.rect.yoffset, rga_dst.rect.width, rga_dst.rect.height, rga_dst.rect.wstride, rga_dst.rect.hstride,\
                        rga_dst.rect.format, rga_dst.fd, rga_dst.virAddr,rga_dst.bufferSize);
#endif

    return SV_SUCCESS;    
}

sint32 ir_convert_Rtsp(IR_DRM_BUF_INFO_S *pstDrmBuf_src, IR_HEAP_BUF_INFO_S *pstHeapBuf_dst, SV_RECT_S *pstRgn_src, SV_RECT_S *pstRgn_dst)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstHeapBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return ERR_NULL_PTR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstHeapBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstHeapBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.virAddr         = pstHeapBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstHeapBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_src.bufferSize      = pstDrmBuf_src->u32Size;

    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_SUCCESS;
    }

#if 0
            print_level(SV_DEBUG,"src X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                        rga_src.rect.xoffset, rga_src.rect.yoffset, rga_src.rect.width, rga_src.rect.height, rga_src.rect.wstride, rga_src.rect.hstride,\
                        rga_src.rect.format, rga_src.fd, rga_src.virAddr,rga_src.bufferSize);    
            print_level(SV_DEBUG,"dst X: %d y %d recw: %d rech: %d recws: %d recwh: %d format: %x fd: %d addr:%08x bufsize %d\n",\
                        rga_dst.rect.xoffset, rga_dst.rect.yoffset, rga_dst.rect.width, rga_dst.rect.height, rga_dst.rect.wstride, rga_dst.rect.hstride,\
                        rga_dst.rect.format, rga_dst.fd, rga_dst.virAddr,rga_dst.bufferSize);
#endif

    return SV_SUCCESS;    
}


/******************************************************************************
 * 函数功能: 处理镜像和翻转
 * 输入参数: s32Mirror -- 镜像设置 s32Flip -- 翻转设置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   原图的镜像和翻转均做了一次的，在这个基础上再做一次,勾选上为0，没有为1
 *****************************************************************************/
sint32 mpp_ir_vo_SetMirrorAndFlip(SV_BOOL bMirror, SV_BOOL bFlip, sint32 *pstResult)
{
    //镜像也做翻转
    if((SV_TRUE == bMirror) && (SV_TRUE == bFlip))
    {
        *pstResult = HAL_TRANSFORM_ROT_180;
    }
    //只做镜像
    else if((SV_TRUE == bMirror) && (SV_FALSE == bFlip))
    {
        *pstResult = HAL_TRANSFORM_FLIP_H;
    }
    //只做翻转
    else if((SV_FALSE == bMirror) && (SV_TRUE == bFlip))
    {
        *pstResult = HAL_TRANSFORM_FLIP_V;
    }
    //什么都不做
    else
    {
        *pstResult = 0;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取红外格式转换后的数据指针
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vo_GetFrame(sint32 *ps32Idx)
{
    sint32 s32CurIndex = 0, i = 0, retry = 10, s32Ret = 0;
    SV_BOOL bGetFrame = SV_FALSE;
    if(ps32Idx == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    while(retry--)
    {    
        pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VO]);
        s32CurIndex = m_stIRInfo.as32DrmIdx[MPP_IR_VO];
        for(i = 0; i < IR_DRM_BUF_NUM; i++)
        {
            if(IR_DRM_BUF_RW == m_stIRInfo.astVoDrmBuf[s32CurIndex].enBufStatus)
            {
                *ps32Idx = s32CurIndex;
                m_stIRInfo.astVoDrmBuf[s32CurIndex].enBufStatus = IR_DRM_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurIndex = (s32CurIndex - 1 + IR_DRM_BUF_NUM) % IR_DRM_BUF_NUM;
        }
        pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VO]);
        
        if(i < IR_DRM_BUF_NUM)
        {
            break;
        }
        sleep_ms(5);
    }
    
    if(SV_FALSE == bGetFrame)
    {
        //print_level(SV_INFO, "get vmix timeout\n");
        return SV_FAILURE;
    }

    s32Ret = mpp_vosd_internal_callback(IR_VOSD_CHN, MPP_VPSS_CHN_VO, \
                                        m_stIRInfo.astVoDrmBuf[s32CurIndex].virAddr,\
                                        m_stIRInfo.astVoDrmBuf[s32CurIndex].u32Width,\
                                        m_stIRInfo.astVoDrmBuf[s32CurIndex].u32Height);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_WARN, "mpp_vosd_callback fail\n");
    }

    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 释放红外数据
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vo_ReleaseFrame(sint32 s32Idx)
{
    if(s32Idx >= IR_DRM_BUF_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VO]);
 
    m_stIRInfo.astVoDrmBuf[s32Idx].enBufStatus = IR_DRM_BUF_WRITE;

    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VO]);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取红外格式转换后的数据指针
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_alg_GetFrame(sint32 *ps32Idx)
{    
    sint32 s32CurIndex = 0, i = 0, retry = 10;
    SV_BOOL bGetFrame = SV_FALSE;
    if (ps32Idx == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    while (retry--)
    {    
        pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_ALG]);
        s32CurIndex = m_stIRInfo.as32DrmIdx[MPP_IR_ALG];
        for (i = 0; i < IR_ALG_DRM_BUF_NUM; i++)
        {
            if (IR_DRM_BUF_RW == m_stIRInfo.astAlgDrmBuf[s32CurIndex].enBufStatus)
            {
                *ps32Idx = s32CurIndex;
                m_stIRInfo.astAlgDrmBuf[s32CurIndex].enBufStatus = IR_DRM_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurIndex = (s32CurIndex - 1 + IR_ALG_DRM_BUF_NUM) % IR_ALG_DRM_BUF_NUM;
        }
        pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_ALG]);
        
        if (i < IR_ALG_DRM_BUF_NUM)
        {
            break;
        }
        sleep_ms(5);
    }
    
    if (SV_FALSE == bGetFrame)
    {
        //print_level(SV_INFO, "get vmix timeout\n");
        return SV_FAILURE;
    }

    return SV_SUCCESS;


}

/******************************************************************************
 * 函数功能: 释放红外数据
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_alg_ReleaseFrame(sint32 s32Idx)
{
    if (s32Idx >= IR_ALG_DRM_BUF_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_ALG]);
 
    m_stIRInfo.astAlgDrmBuf[s32Idx].enBufStatus = IR_DRM_BUF_WRITE;

    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_ALG]);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取红外格式转换后的数据指针
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_venc_GetFrame(sint32 *ps32Idx)
{
    sint32 s32CurIndex = 0, i = 0, retry = 10;
    SV_BOOL bGetFrame = SV_FALSE;
    if (ps32Idx == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    while (retry--)
    {    
        pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VENC]);
        s32CurIndex = m_stIRInfo.as32HeapIdx[MPP_IR_VENC];
        for (i = 0; i < IR_HEAP_BUF_NUM; i++)
        {
            if(IR_HEAP_BUF_RW == m_stIRInfo.astVencH264HeapBuf[s32CurIndex].enBufStatus)
            {
                *ps32Idx = s32CurIndex;
                m_stIRInfo.astVencH264HeapBuf[s32CurIndex].enBufStatus = IR_HEAP_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurIndex = (s32CurIndex - 1 + IR_HEAP_BUF_NUM) % IR_HEAP_BUF_NUM;
        }
        pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VENC]);
        
        if (i < IR_HEAP_BUF_NUM)
        {
            break;
        }
        sleep_ms(5);
    }
    
    if (SV_FALSE == bGetFrame)
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;


}

/******************************************************************************
 * 函数功能: 释放红外数据
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_venc_ReleaseFrame(sint32 s32Idx)
{
    if(s32Idx >= IR_HEAP_BUF_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VENC]);
 
    m_stIRInfo.astVencH264HeapBuf[s32Idx].enBufStatus = IR_HEAP_BUF_WRITE;

    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VENC]);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取红外格式转换后的数据指针
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_jpeg_GetFrame(sint32 *ps32Idx)
{
    sint32 s32CurIndex = 0, i = 0, retry = 10, s32Ret = 0;
    SV_BOOL bGetFrame = SV_FALSE;
    if (ps32Idx == NULL)
    {
        return ERR_NULL_PTR;
    }

    while (retry--)
    {    
        pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_JPEG]);
        s32CurIndex = m_stIRInfo.as32HeapIdx[MPP_IR_JPEG];
        for (i = 0; i < IR_HEAP_BUF_NUM; i++)
        {
            if(IR_HEAP_BUF_RW == m_stIRInfo.astVencJpegHeapBuf[s32CurIndex].enBufStatus)
            {
                *ps32Idx = s32CurIndex;
                m_stIRInfo.astVencJpegHeapBuf[s32CurIndex].enBufStatus = IR_HEAP_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurIndex = (s32CurIndex - 1 + IR_HEAP_BUF_NUM) % IR_HEAP_BUF_NUM;
        }
        pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_JPEG]);
        
        if (i < IR_HEAP_BUF_NUM)
        {
            break;
        }
        sleep_ms(5);
    }


    if (SV_FALSE == bGetFrame)
    {
        return SV_FAILURE;
    }
    s32Ret = mpp_vosd_internal_callback(IR_VOSD_CHN, MPP_VPSS_CHN_PRI, \
                                        m_stIRInfo.astVencJpegHeapBuf[s32CurIndex].virAddr,\
                                        m_stIRInfo.astVencJpegHeapBuf[s32CurIndex].u32Width,\
                                        m_stIRInfo.astVencJpegHeapBuf[s32CurIndex].u32Height);


    return SV_SUCCESS;


}

/******************************************************************************
 * 函数功能: 释放红外数据
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_jpeg_ReleaseFrame(sint32 s32Idx)
{

    if(s32Idx >= IR_HEAP_BUF_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_JPEG]);
 
    m_stIRInfo.astVencJpegHeapBuf[s32Idx].enBufStatus = IR_HEAP_BUF_WRITE;

    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_JPEG]);

    return SV_SUCCESS;
}


sint32 mpp_ir_venc_rtsp_GetFrame(sint32 *ps32Idx)
{
    sint32 s32CurIndex = 0, i = 0, retry = 10;
    SV_BOOL bGetFrame = SV_FALSE;
    if (ps32Idx == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    while (retry--)
    {    
        pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX_RTSP]);
        s32CurIndex = m_stIRInfo.as32HeapIdx[MPP_IR_VMIX_RTSP];
        for (i = 0; i < IR_HEAP_BUF_NUM; i++)
        {
            if(IR_HEAP_BUF_RW == m_stIRInfo.astRtspVmixHeapBuf[s32CurIndex].enBufStatus)
            {
                *ps32Idx = s32CurIndex;
                m_stIRInfo.astRtspVmixHeapBuf[s32CurIndex].enBufStatus = IR_HEAP_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurIndex = (s32CurIndex - 1 + IR_HEAP_BUF_NUM) % IR_HEAP_BUF_NUM;
        }
        pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX_RTSP]);
        
        if (i < IR_HEAP_BUF_NUM)
        {
            break;
        }
        sleep_ms(5);
    }
    
    if (SV_FALSE == bGetFrame)
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;


}

sint32 mpp_ir_venc_rtsp_ReleaseFrame(sint32 s32Idx)
{
    if(s32Idx >= IR_HEAP_BUF_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX_RTSP]);
 
    m_stIRInfo.astRtspVmixHeapBuf[s32Idx].enBufStatus = IR_HEAP_BUF_WRITE;

    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX_RTSP]);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 创建H264编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIChnNum)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_EXIST - 通道已存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_venc_H264CreateChn(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    sint32 s32VpssGrp = 0;
    VENC_CHN VeChnId = s32Chn;
    sint32 s32VencFd = 0;
    VENC_CHN_ATTR_S stVencChnAttr;
    VENC_ATTR_H264_S stVencH264Attr;
    VENC_H264_CBR_S    stVencH264Cbr;
    VENC_SUPERFRAME_CFG_S stVencSuperFrame;
    RGA_CHN stRgaChn = 4;
    RGA_ATTR_S stRgaAttr;
    VENC_RESOLUTION_PARAM_S stResolution;
    uint32 u32Width, u32Height;

    
    stVencChnAttr.stRcAttr.enRcMode     = VENC_RC_MODE_H264CBR;
    stVencH264Cbr.u32Gop                = 25;
    stVencH264Cbr.u32SrcFrameRateDen    = 1;
    stVencH264Cbr.u32SrcFrameRateNum    = 25;
    stVencH264Cbr.fr32DstFrameRateDen   = 1;
    stVencH264Cbr.fr32DstFrameRateNum   = 25;
    stVencH264Cbr.u32BitRate = 4 * 1024 * 1024;
    memcpy(&stVencChnAttr.stRcAttr.stH264Cbr, &stVencH264Cbr, sizeof(VENC_H264_VBR_S));

    stVencChnAttr.stVencAttr.imageType = IMAGE_TYPE_NV12;   //input type
    stVencChnAttr.stVencAttr.enType = RK_CODEC_TYPE_H264;
    stVencChnAttr.stVencAttr.u32VirWidth  = 1280;
    stVencChnAttr.stVencAttr.u32VirHeight = 720;
    stVencChnAttr.stVencAttr.u32PicWidth     = 1280;
    stVencChnAttr.stVencAttr.u32PicHeight    = 720;

    stVencChnAttr.stVencAttr.u32Profile = 100;
    stVencChnAttr.stVencAttr.bByFrame        = RK_TRUE;
    stVencChnAttr.stGopAttr.enGopMode = VENC_GOPMODE_NORMALP;
    stVencChnAttr.stGopAttr.s32IPQpDelta = 0;

    s32Ret = RK_MPI_VENC_CreateChn(VeChnId, &stVencChnAttr);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_CreateChn failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = RK_MPI_VENC_GetSuperFrameStrategy(VeChnId, &stVencSuperFrame);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_GetSuperFrameStrategy failed. [err=%d]\n", s32Ret);
    }

    s32VencFd = RK_MPI_VENC_GetFd(VeChnId);
    if (s32VencFd <= 0)
    {
        return SV_FAILURE;
    }
    m_stIRInfo.s32VencFd = s32VencFd;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建H264编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIChnNum)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_EXIST - 通道已存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_venc_JpegCreateChn(sint32 s32Chn)
{
    sint32 s32Ret = 0, s32Fps;
    sint32 s32VpssGrp = 0;
    VENC_CHN VeChnId = s32Chn;
    sint32 s32VencFd = 0;
    VENC_CHN_ATTR_S stVencChnAttr = {0};
    VENC_ATTR_JPEG_S stVencJpegAttr = {0};
    VENC_JPEG_PARAM_S stVencJpegParam;
    RGA_ATTR_S stRgaAttr;
    VENC_RESOLUTION_PARAM_S stResolution;
    uint32 u32Width, u32Height;

    u32Width = IR_VENC_JPEG_WIDTH;
    u32Height = IR_VENC_JPEG_HEIGHT;

    stVencChnAttr.stVencAttr.imageType          = IMAGE_TYPE_NV12;
    stVencChnAttr.stVencAttr.enType             = RK_CODEC_TYPE_JPEG;
    stVencChnAttr.stVencAttr.u32VirWidth        = u32Width;
    stVencChnAttr.stVencAttr.u32VirHeight       = u32Height;
    stVencChnAttr.stVencAttr.u32PicWidth        = u32Width;
    stVencChnAttr.stVencAttr.u32PicHeight       = u32Height;
    stVencJpegAttr.u32ZoomWidth                 = u32Width;
    stVencJpegAttr.u32ZoomHeight                = u32Height;
    stVencJpegAttr.u32ZoomVirWidth              = u32Width;
    stVencJpegAttr.u32ZoomVirHeight             = u32Height;
    stVencChnAttr.stVencAttr.bByFrame           = RK_TRUE;

    memcpy(&stVencChnAttr.stVencAttr.stAttrJpege, &stVencJpegAttr, sizeof(VENC_ATTR_JPEG_S));
    print_level(SV_WARN, "VENC[%d] (w,h) (%d, %d)\n", VeChnId, stVencChnAttr.stVencAttr.u32VirWidth, stVencChnAttr.stVencAttr.u32VirHeight);
    s32Ret = RK_MPI_VENC_CreateChn(VeChnId, &stVencChnAttr);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_CreateChn failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Fps = 30;
    s32Ret = RK_MPI_VENC_SetFps(VeChnId, 1, 1, s32Fps, 1);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_SetFps failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
    
    stVencJpegParam.u32Qfactor = 3 * 20;
    stVencJpegParam.u32Qfactor = (stVencJpegParam.u32Qfactor > 99) ? 99 : stVencJpegParam.u32Qfactor;
    s32Ret = RK_MPI_VENC_SetJpegParam(VeChnId, &stVencJpegParam);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_SetJpegParam failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    s32VencFd = RK_MPI_VENC_GetFd(VeChnId);
    if (s32VencFd <= 0)
    {
        return SV_FAILURE;
    }

    m_stIRInfo.s32VencJpegFd = s32VencFd;

    return SV_SUCCESS;

}


sint32 mpp_ir_venc_rtsp_H264CreateChn(sint32 s32Chn)
{
    sint32 s32Ret = 0;
    VENC_CHN VeChnId = s32Chn;
    sint32 s32VencFd = 0;
    VENC_CHN_ATTR_S stVencChnAttr;
    VENC_H264_CBR_S    stVencH264Cbr;
    VENC_SUPERFRAME_CFG_S stVencSuperFrame;

    
    stVencChnAttr.stRcAttr.enRcMode     = VENC_RC_MODE_H264CBR;

    stVencH264Cbr.u32Gop                = 25;
    stVencH264Cbr.u32SrcFrameRateDen    = 1;
    stVencH264Cbr.u32SrcFrameRateNum    = 25;
    stVencH264Cbr.fr32DstFrameRateDen   = 1;
    stVencH264Cbr.fr32DstFrameRateNum   = 25;
    stVencH264Cbr.u32BitRate = 4 * 1024 * 1024;
    memcpy(&stVencChnAttr.stRcAttr.stH264Cbr, &stVencH264Cbr, sizeof(VENC_H264_VBR_S));

    stVencChnAttr.stVencAttr.imageType = IMAGE_TYPE_NV12;   
    stVencChnAttr.stVencAttr.enType = RK_CODEC_TYPE_H264; 
    stVencChnAttr.stVencAttr.u32VirWidth  = IR_VENC_WIDTH;
    stVencChnAttr.stVencAttr.u32VirHeight = IR_VENC_HEIGHT;
    stVencChnAttr.stVencAttr.u32PicWidth     = IR_VENC_WIDTH;
    stVencChnAttr.stVencAttr.u32PicHeight    = IR_VENC_HEIGHT;

    stVencChnAttr.stVencAttr.u32Profile = 100;
    stVencChnAttr.stVencAttr.bByFrame        = RK_TRUE;
    stVencChnAttr.stGopAttr.enGopMode = VENC_GOPMODE_NORMALP;
    stVencChnAttr.stGopAttr.s32IPQpDelta = 0;

    s32Ret = RK_MPI_VENC_CreateChn(VeChnId, &stVencChnAttr);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_CreateChn failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = RK_MPI_VENC_GetSuperFrameStrategy(VeChnId, &stVencSuperFrame);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_GetSuperFrameStrategy failed. [err=%d]\n", s32Ret);
    }

    s32VencFd = RK_MPI_VENC_GetFd(VeChnId);
    if (s32VencFd <= 0)
    {
        return SV_FAILURE;
    }
    m_stIRInfo.s32RtspVencFd = s32VencFd;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取红外格式转换后的数据指针
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_GetFrame(void **ppvBuf)
{
    sint32 s32CurIndex = 0, i;
    SV_BOOL bGetFrame = SV_FALSE;
    void *pbmp = NULL;

    if(ppvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    s32CurIndex = m_stIRInfo.as32DrmIdx[MPP_IR_VMIX];
    for(i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        if(IR_DRM_BUF_RW == m_stIRInfo.astVmixDrmBuf[s32CurIndex].enBufStatus)
        {
            pbmp = m_stIRInfo.astVmixDrmBuf[s32CurIndex].virAddr;
            *ppvBuf = pbmp;
            m_stIRInfo.astVmixDrmBuf[s32CurIndex].enBufStatus = IR_DRM_BUF_READ;
            bGetFrame = SV_TRUE;
            break;
        }
        s32CurIndex = (s32CurIndex - 1 + IR_DRM_BUF_NUM) % IR_DRM_BUF_NUM;
    }
    if(SV_FALSE == bGetFrame)
    {
        goto skip;
    }

    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    return SV_SUCCESS;
skip:
    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    return SV_FAILURE;

}

/******************************************************************************
 * 函数功能: 释放红外数据
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_ReleaseFrame(void **ppvBuf)
{
    sint32 i = 0;
    void *pbmp = *ppvBuf;
    
    if(*ppvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    for(i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        if(m_stIRInfo.astVmixDrmBuf[i].virAddr == pbmp)
        {
            m_stIRInfo.astVmixDrmBuf[i].enBufStatus = IR_DRM_BUF_WRITE;
            break;
        }
    }

    if(i > IR_DRM_BUF_NUM)
    {
        print_level(SV_WARN, "mpp_ir_vmix_ReleaseFrame fail!\n");
        pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
        return SV_FAILURE;
    }

    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);

    *ppvBuf = NULL;
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 填充对称模式下边界
 * 输入参数: pstDrmBuf -- 显示drm buffer
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_FillBoundarySym(IR_DRM_BUF_INFO_S *pstDrmBuf)
{
    sint32 s32Ret = -1;
    rga_buffer_t rga_dst;
    im_rect rect;
    rga_dst.format = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd = pstDrmBuf->s32Fd;
    rga_dst.height = pstDrmBuf->u32Height;
    rga_dst.hstride = pstDrmBuf->u32Height;
    rga_dst.width = pstDrmBuf->u32Width;
    rga_dst.wstride = pstDrmBuf->u32Width;
    rga_dst.phy_addr = pstDrmBuf->phyAddr;
    rga_dst.vir_addr = pstDrmBuf->virAddr;
    rga_dst.color = 0xffffffff;

    rect.x = (pstDrmBuf->u32Width / 2) - (IR_VMIX_LINE_WIDTH >> 1);
    rect.y = 0;
    rect.width    = IR_VMIX_LINE_WIDTH;
    rect.height = pstDrmBuf->u32Height;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if(s32Ret != IM_STATUS_SUCCESS)
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 填充非对称模式下边界
 * 输入参数: pstDrmBuf -- 显示drm buffer
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_FillBoundaryAsym(IR_DRM_BUF_INFO_S *pstDrmBuf)
{
    sint32 s32Ret = -1;
    rga_buffer_t rga_dst;
    im_rect rect;
    rga_dst.format = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd = pstDrmBuf->s32Fd;
    rga_dst.height = pstDrmBuf->u32Height;
    rga_dst.hstride = pstDrmBuf->u32Height;
    rga_dst.width = pstDrmBuf->u32Width;
    rga_dst.wstride = pstDrmBuf->u32Width;
    rga_dst.phy_addr = pstDrmBuf->phyAddr;
    rga_dst.vir_addr = pstDrmBuf->virAddr;
    rga_dst.color = 0xffffffff;

    /* 画竖直线 */
    rect.x = (pstDrmBuf->u32Width / 3) * 2 - (IR_VMIX_LINE_WIDTH >> 1);
    rect.y = 0;
    rect.width    = IR_VMIX_LINE_WIDTH;
    rect.height = pstDrmBuf->u32Height;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        print_level(SV_INFO, "imfill fail! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
    
    /* 画横直线 */
    rect.x = 0;
    rect.y = (pstDrmBuf->u32Height / 3) * 2 - (IR_VMIX_LINE_WIDTH >> 1);
    rect.width    = pstDrmBuf->u32Width;
    rect.height = IR_VMIX_LINE_WIDTH;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        return SV_FAILURE;
    }

    /* 画横直线 */
    rect.x = 0;
    rect.y = (pstDrmBuf->u32Height / 3) * 1 - (IR_VMIX_LINE_WIDTH >> 1);
    rect.width    = pstDrmBuf->u32Width;
    rect.height = IR_VMIX_LINE_WIDTH;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 填充osd模式边框
 * 输入参数: pstDrmBuf -- 显示drm buffer
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_FillBoundaryOsd(IR_DRM_BUF_INFO_S *pstDrmBuf)
{
    sint32 s32Ret = -1;
    rga_buffer_t rga_dst;
    im_rect rect;
    rga_dst.format = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd = pstDrmBuf->s32Fd;
    rga_dst.height = pstDrmBuf->u32Height;
    rga_dst.hstride = pstDrmBuf->u32Height;
    rga_dst.width = pstDrmBuf->u32Width;
    rga_dst.wstride = pstDrmBuf->u32Width;
    rga_dst.phy_addr = pstDrmBuf->phyAddr;
    rga_dst.vir_addr = pstDrmBuf->virAddr;
    rga_dst.color = 0xffffffff;

    /* 画左竖直线 */
    rect.x = IR_OSD_POS_BAIS_X - IR_VMIX_LINE_WIDTH;
    rect.y = IR_OSD_POS_BAIS_Y;
    rect.width    = IR_VMIX_LINE_WIDTH;
    rect.height = IR_OSD_HEIGHT;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        print_level(SV_ERROR, "Draw The Border Fail\n");
        return SV_FAILURE;
    }
    
    /* 画右竖直线 */
    rect.x = IR_OSD_POS_BAIS_X + IR_OSD_WIDTH;
    rect.y = IR_OSD_POS_BAIS_Y;
    rect.width    = IR_VMIX_LINE_WIDTH ;
    rect.height = IR_OSD_HEIGHT;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        return SV_FAILURE;
    }
    
    /* 画上横直线 */
    rect.x = IR_OSD_POS_BAIS_X - IR_VMIX_LINE_WIDTH;
    rect.y = IR_OSD_POS_BAIS_Y - IR_VMIX_LINE_WIDTH;
    rect.width    = IR_OSD_WIDTH + (IR_VMIX_LINE_WIDTH << 1);
    rect.height = IR_VMIX_LINE_WIDTH;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        return SV_FAILURE;
    }

    /* 画下横直线 */
    rect.x = IR_OSD_POS_BAIS_X - IR_VMIX_LINE_WIDTH;
    rect.y = IR_OSD_POS_BAIS_Y + IR_OSD_HEIGHT;
    rect.width    = IR_OSD_WIDTH + (IR_VMIX_LINE_WIDTH << 1);
    rect.height = IR_VMIX_LINE_WIDTH;
    s32Ret = imfill(rga_dst, rect, 0xffffffff, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 纯色填充
 * 输入参数: pstDrmBuf -- 显示drm buffer
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_FillColor(IR_DRM_BUF_INFO_S *pstDrmBuf)
{
    sint32 s32Ret = -1;
    rga_buffer_t rga_dst;
    im_rect rect;
    rga_dst.format = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd = pstDrmBuf->s32Fd;
    rga_dst.height = pstDrmBuf->u32Height;
    rga_dst.hstride = pstDrmBuf->u32Height;
    rga_dst.width = pstDrmBuf->u32Width;
    rga_dst.wstride = pstDrmBuf->u32Width;
    rga_dst.phy_addr = pstDrmBuf->phyAddr;
    rga_dst.vir_addr = pstDrmBuf->virAddr;
    rga_dst.color = 0x00000000;
    
    /* 填充颜色 */
    rect.x = 0;
    rect.y = 0;
    rect.width    = pstDrmBuf->u32Width;
    rect.height = pstDrmBuf->u32Height;
    s32Ret = imfill(rga_dst, rect, rga_dst.color, 0);
    if (s32Ret != IM_STATUS_SUCCESS)
    {
        print_level(SV_WARN, "mpp_ir_vmix_FillColor failed\n");
        return SV_FAILURE;
    }
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 清除显示buffer
 * 输入参数: pstIRInfo -- 模块信息 
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_ClearScreen(MPP_IR_INFO_S *pstIRInfo)
{
    sint32 i, s32Ret = -1;
    SV_BOOL bFail = SV_FALSE;
    if (NULL == pstIRInfo)
    {
        return ERR_NULL_PTR;
    }
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = mpp_ir_vmix_FillColor(&pstIRInfo->astVmixDrmBuf[i]);
        if (SV_SUCCESS != s32Ret)
        {
            bFail = SV_TRUE;
        }
    }
    
    if (SV_TRUE == bFail)
    {
        print_level(SV_WARN, "mpp_ir_vmix_ClearScreen failed\n");
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 绘制对称二分屏显示时中间直线
 * 输入参数: pstIRInfo -- 模块信息 
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_FillBoundarySymAll(MPP_IR_INFO_S *pstIRInfo)
{
    sint32 i, s32Ret = -1;
    SV_BOOL bFail = SV_FALSE;
    if (NULL == pstIRInfo)
    {
        return ERR_NULL_PTR;
    }
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = mpp_ir_vmix_FillBoundarySym(&pstIRInfo->astVmixDrmBuf[i]);
        if (SV_SUCCESS != s32Ret)
        {
            bFail = SV_TRUE;
        }
    }

    if (SV_TRUE == bFail)
    {
        print_level(SV_WARN, "mpp_ir_vmix_FillBoundarySymAll failed\n");
        return SV_FAILURE;
    }

    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 绘制非对称显示时框实线
 * 输入参数: pstIRInfo -- 模块信息 
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_FillBoundaryAsymAll(MPP_IR_INFO_S *pstIRInfo)
{
    sint32 i, s32Ret = -1;
    SV_BOOL bFail = SV_FALSE;
    if (NULL == pstIRInfo)
    {
        return ERR_NULL_PTR;
    }
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = mpp_ir_vmix_FillBoundaryAsym(&pstIRInfo->astVmixDrmBuf[i]);
        if (SV_SUCCESS != s32Ret)
        {
            bFail = SV_TRUE;
        }
    }
    
    if (SV_TRUE == bFail)
    {
        print_level(SV_WARN, "mpp_ir_vmix_FillBoundaryAsymAll failed\n");
        return SV_FAILURE;
    }   
    
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 获取转换格式后的CVBS缓冲区的数据指针
 * 输入参数: ps32Idx --- 数据缓存索引
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_Cvbs_GetFrame(sint32 *ps32Idx)
{
    sint32 s32CurIndex = 0, i = 0, retry = 10, s32Ret = 0;
    SV_BOOL bGetFrame = SV_FALSE;
    if(ps32Idx == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    while(retry--)
    {    
        //pthread_mutex_lock(&m_stIRInfo.pIrCvbsLock);
        s32CurIndex = m_stIRInfo.as32CvbsDrmIdx; 
        for(i = 0; i < IR_DRM_BUF_NUM; i++)
        {
            
            if(IR_DRM_BUF_RW == m_stIRInfo.astCvbsVmixHeapBuf[s32CurIndex].enBufStatus)
            {
                *ps32Idx = s32CurIndex;
                m_stIRInfo.astCvbsVmixHeapBuf[s32CurIndex].enBufStatus = IR_DRM_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            
            s32CurIndex = (s32CurIndex - 1 + IR_DRM_BUF_NUM) % IR_DRM_BUF_NUM;
        }
        //pthread_mutex_unlock(&m_stIRInfo.pIrCvbsLock);
        
        if(i < IR_DRM_BUF_NUM)
        {
            break;
        }
        sleep_ms(5);
    }
    
    if(SV_FALSE == bGetFrame)
    {
        //print_level(SV_INFO, "get vmix timeout\n");
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;

}

/******************************************************************************
 * 函数功能: 释放CVBS缓冲区数据
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_Cvbs_ReleaseFrame(sint32 s32Idx)
{
    if(s32Idx >= IR_DRM_BUF_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

 
    m_stIRInfo.astCvbsVmixHeapBuf[s32Idx].enBufStatus = IR_DRM_BUF_WRITE;


    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 开启红外快速抓拍功能
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 可根据掩码开启多路通道
 *****************************************************************************/
sint32 mpp_ir_FastSnapStart(void)
{
    sint32 s32Ret = 0, i , s32Fps;
    VENC_CHN VeChnId = 0;
    VENC_JPEG_PARAM_S stVencJpegParam;
    m_stIRInfo.bPictureStream = SV_TRUE;
    mpp_venc_FastSnapStop();

    VeChnId = IR_VENC_JPEG_CHN;
    s32Fps = 30;
    s32Ret = RK_MPI_VENC_SetFps(VeChnId, s32Fps, 1, s32Fps, 1);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_SetFps failed! [err=%d]\n", s32Ret);
    }

    stVencJpegParam.u32Qfactor = 3 * 20 + 10;
    stVencJpegParam.u32Qfactor = (stVencJpegParam.u32Qfactor > 99) ? 99 : stVencJpegParam.u32Qfactor;
    s32Ret = RK_MPI_VENC_SetJpegParam(VeChnId, &stVencJpegParam);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_SetJpegParam failed! [ch%d, err=%d]\n", i, s32Ret);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 关闭快速抓拍功能
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_FastSnapStop(void)
{
    sint32 s32Ret = 0, i, s32Fps = 30;
    uint32 u32Quality = 3;
    VENC_CHN VeChnId = 0;
    VENC_JPEG_PARAM_S stVencJpegParam;
    m_stIRInfo.bPictureStream = SV_FALSE;
    VeChnId = IR_VENC_JPEG_CHN;
    s32Ret = RK_MPI_VENC_SetFps(VeChnId, 1, 1, s32Fps, 1);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_SetFps failed! [err=%d]\n", s32Ret);
    }

    stVencJpegParam.u32Qfactor = u32Quality * 20 + 10;
    stVencJpegParam.u32Qfactor = (stVencJpegParam.u32Qfactor > 99) ? 99 : stVencJpegParam.u32Qfactor;
    s32Ret = RK_MPI_VENC_SetJpegParam(VeChnId, &stVencJpegParam);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VENC_SetJpegParam failed! [ch%d, err=%d]\n", i, s32Ret);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化sharefifo队列
 * 输入参数: u32Width/u32Height -- 编码宽高 
             u32FrameRate/u32BitRate -- 帧率和位率
 * 输出参数: ps32QueId -- 队列id
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 媒体和sharefifo耦合了
 *****************************************************************************/
sint32 mpp_ir_FIFOInit(sint32 *ps32QueId, uint32 u32Width, uint32 u32Height, uint32 u32FrameRate, uint32 u32BitRate)
{
    sint32 s32QueId = 0;
    SFIFO_MEDIA_ATTR stSfifoMediaAttr = {0};
    sint32 s32Ret = 0;
    /* 初始化队列 */
    s32Ret = SFIFO_CreateQue(SFIFO_IR_SUB_STREAM, SFIFO_VIDEO_QUE_SIZE, &s32QueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_CreateQue failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    s32Ret = SFIFO_ForWriteOpen(SFIFO_IR_SUB_STREAM, &s32QueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForWriteOpen failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    memset(&stSfifoMediaAttr, 0, sizeof(SFIFO_MEDIA_ATTR));
    stSfifoMediaAttr.stMainStreamAttr.bValid = SV_TRUE;
    stSfifoMediaAttr.stMainStreamAttr.u32Width = u32Width;
    stSfifoMediaAttr.stMainStreamAttr.u32Height = u32Height;
    stSfifoMediaAttr.stMainStreamAttr.u32FrameRate = u32FrameRate;
    stSfifoMediaAttr.stMainStreamAttr.u32Bitrate = u32BitRate;
    s32Ret = SFIFO_SetMediaAttr(s32QueId, &stSfifoMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_SetMediaAttr failed.\n");
        return SV_FAILURE;
    }                
    *ps32QueId = s32QueId;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化sharefifo队列
 * 输入参数: s32QueId -- 队列id
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 媒体和sharefifo耦合了
 *****************************************************************************/
sint32 mpp_ir_FIFOFini(sint32 s32QueId)
{
    sint32 s32Ret = 0;
    s32Ret = SFIFO_DestroyQue(s32QueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_DestroyQue failed. [err=%#x]\n", s32Ret);
    }
    return s32Ret;
}

sint32 mpp_ir_Rtsp_FIFOInit(sint32 *ps32QueId, uint32 u32Width, uint32 u32Height, uint32 u32FrameRate, uint32 u32BitRate)
{
    sint32 s32QueId = 0;
    SFIFO_MEDIA_ATTR stSfifoMediaAttr = {0};
    sint32 s32Ret = 0;
     
    s32Ret = SFIFO_CreateQue(SFIFO_IR_VMIX_STREAM, SFIFO_VIDEO_QUE_SIZE, &s32QueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_CreateQue failed. [err=%#x]\n", s32Ret);
        perror("create fifo failed");
        return SV_FAILURE;
    }
    s32Ret = SFIFO_ForWriteOpen(SFIFO_IR_VMIX_STREAM, &s32QueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForWriteOpen failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
 
    memset(&stSfifoMediaAttr, 0, sizeof(SFIFO_MEDIA_ATTR));
    stSfifoMediaAttr.stVmixStreamAttr.bValid = SV_TRUE;
    stSfifoMediaAttr.stVmixStreamAttr.u32Width = u32Width;
    stSfifoMediaAttr.stVmixStreamAttr.u32Height = u32Height;
    stSfifoMediaAttr.stVmixStreamAttr.u32FrameRate = u32FrameRate;
    stSfifoMediaAttr.stVmixStreamAttr.u32Bitrate = u32BitRate;
    s32Ret = SFIFO_SetMediaAttr(s32QueId, &stSfifoMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_SetMediaAttr failed.\n");
        return SV_FAILURE;
    }                
    *ps32QueId = s32QueId;
    print_level(SV_ERROR, "IR QUEUE ID :%d\r\n", s32QueId);
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 红外360yuv协议对接
 * 输入参数: pstGuiImage -- 图像源数据 pstGuiDraw -- 添加操作 
             pstTmepInfo -- 测温数据
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_YUVProtocol(MPP_GUI_IMAGE_S *pstGuiImage, MEDIA_GUI_DRAW_S *pstGuiDraw, IR_TEMP_INFO_S *pstTmepInfo)
{
    sint32 s32Ret = 0;
    MPP_GUI_POINT_S stPoint[2];
    MPP_GUI_COLOR stTmpColor;
    uint32  u32Color = 0x00;
    uint16 u16mask;
    MEDIA_GUI_PERSON_S stGuiPerson = {0};
    
    stGuiPerson.u32PersonNum = 1;
    stGuiPerson.au32Score[0] = 100;
    stGuiPerson.astPersonsRect[0].x1 = pstTmepInfo->fTempMax / 1000.0;//360协议将温度映射到1000度范围然后再映射到16位上
    stGuiPerson.astPersonsRect[0].y1 = pstTmepInfo->fTempMin / 1000.0;
    stGuiPerson.astPersonsRect[0].x2 = pstTmepInfo->fTempMaxPosX;
    stGuiPerson.astPersonsRect[0].y2 = pstTmepInfo->fTempMaxPosX;
        
    u16mask = MEDIA_GUI_GET_MASK(IR_VMIX_CHN, 0, MEDIA_GUI_OP_TEMP_MEAS);
    s32Ret = MEDIA_GUI_INSERT(*pstGuiDraw, u16mask, stGuiPerson);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = MEDIA_VOSD_Gui_Draw(pstGuiDraw);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VOSD_Gui_Draw failed. [err=%#x]\n", s32Ret);
    }

}

/******************************************************************************
 * 函数功能: 使能 alarmout 输出
 * 输入参数: enTriggerType -- 触发类型，高低电平触发
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_AlarmOutEnable(TRIGGER_TYPE_S enTriggerType)
{
    sint32 s32Ret;
    uint8 u8Value = enTriggerType ==  TRIGGER_UP ? 1 : 0;
    
    s32Ret = BOARD_SetAlarmOut(u8Value);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "BOARD_SetAlarmOut fail![err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 关闭 alarmout 输出
 * 输入参数: enTriggerType -- 触发类型，高低电平触发
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_AlarmOutDisable(TRIGGER_TYPE_S enTriggerType)
{
    sint32 s32Ret;
    uint8 u8Value = enTriggerType ==  TRIGGER_UP ? 0 : 1;
    s32Ret = BOARD_SetAlarmOut(u8Value);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "BOARD_SetAlarmOut fail![err=%#x]\n", s32Ret);
        return s32Ret;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 触发电平输出线程
 * 输入参数: pvArg -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_AlarmOutBody(void *pvArg)
{
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32Ret, pwm, i = 0;
    static int bTrigger = 0;
    static sint32 s32AlarmLv;
    static SV_BOOL st_bStop;
    TRIGGER_TYPE_S enTriggerType = TRIGGER_UP;
    static long long s64NowTime = 0;
    static long long s64StartTime = 0;
    s32Ret = pthread_detach(pthread_self());
    if(s32Ret!=0)
    {
        pthread_exit(NULL); // 显式退出
    }

    st_bStop = SV_FALSE;
    s32AlarmLv = pstIRInfo->stTempInfo.s32AlarmLv;   /* 更新报警模式 */
    if(bTrigger)
    {
        s64StartTime = microtime();//重置时间
        st_bStop = SV_TRUE;
        pthread_exit(NULL);
        return;
    }
    st_bStop = SV_TRUE;
    bTrigger = SV_TRUE;
    s64StartTime = microtime();
    s64NowTime = s64StartTime;

    mpp_ir_AlarmOutEnable(enTriggerType);
    while((s64NowTime - s64StartTime < IR_ALARM_INTERVAL) || (st_bStop != SV_TRUE))
    {
        i = (i + 1) % 10;
#if 0        
        /* 设置PWM占用比      30% 60% 100%*/
        if(1 == s32AlarmLv)                 pwm = 3;
        else if(2 == s32AlarmLv)            pwm = 6;
        else if(3 == s32AlarmLv)            pwm = 10;
        else                                pwm = 10;

        if(i < pwm)
        {
            mpp_ir_AlarmOutEnable(enTriggerType);
        }
        else
        {
            mpp_ir_AlarmOutDisable(enTriggerType);
        }
#endif
        sleep_ms(10);
        s64NowTime = microtime();
    }

    mpp_ir_AlarmOutDisable(enTriggerType);
    bTrigger = SV_FALSE;
    st_bStop = SV_TRUE;
    return;
}

/******************************************************************************
 * 函数功能: 报警触发
 * 输入参数: pstIRInfo -- 模块参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_AlarmPost(MPP_IR_INFO_S *pstIRInfo)
{
    sint32 s32Ret;
    pthread_t thread_sound, thread_trigger, thread_alarmer;

    s32Ret = pthread_create(&thread_trigger, NULL, mpp_ir_AlarmOutBody, pstIRInfo);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_AlarmOutBody failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 从标准的UVC设备中获取红外图像数据（拉姆达机芯
）
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 目前使用的机芯有高德和拉姆达，高德的机芯不是标准的UVC设备，图像的获取
           依靠厂家提供的SDK函数进行的；而拉姆达机芯是标准的UVC设备，可以通过V4L2
           框架获取到图像数据
 *****************************************************************************/
void *mpp_ir_Uvc_Body(void *pvArg)
{
    sint32 s32Ret = -1,ret=-1;
    struct timespec tvBegin = {0, 0};
    struct timespec tvEnd = {0, 0};
    sint32 s32CurIdx = 0;
    SV_BOOL bGetFrame = SV_FALSE;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 fd = -1;
    uint32 u32BufNum = 0;
    struct v4l2_format vFormat;
    struct v4l2_requestbuffers vqbuff;
    struct v4l2_buffer vbuff;
    unsigned char * mptr[4];
    struct v4l2_buffer readbuff;
    SV_BOOL  bInitStatus = SV_FALSE;
    sint32 s32ShutterTimes = 0;
    while(pstIRInfo->bRunning)
    {       

        if (pstIRInfo->bInsert == SV_FALSE)
        {
            sleep_ms(100);
            continue;
        }
        
        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }

        if (1 != pstIRInfo->s32SensorClass)
        {
            sleep_ms(100);
            continue;
        }

        if (pstIRInfo->bStatusChange == SV_TRUE && pstIRInfo->bInsert == SV_TRUE && bInitStatus == SV_FALSE)
        {
            s32Ret = mpp_ir_Uvc_Init(&fd, vFormat, vqbuff, &vbuff, mptr, &bInitStatus);
            if (s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "lamb init failed\r\n");
            }
            else
            {
                print_level(SV_ERROR, "lamb init success\r\n");
            }
        }

        if (pstIRInfo->bStatusChange == SV_TRUE && pstIRInfo->bInsert == SV_FALSE && bInitStatus == SV_TRUE)
        {
            s32Ret = mpp_ir_Uvc_Fnit(fd, &vbuff, mptr, &bInitStatus);
            if (s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "lamb finit failed\r\n");
            }
            else
            {
                print_level(SV_ERROR, "lamb finit success\r\n");
            }
        }

        //从队列中取数据
        memset(&readbuff, 0, sizeof(struct v4l2_buffer));
        readbuff.type   = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        readbuff.memory = V4L2_MEMORY_MMAP;
        
        ret=ioctl(fd, VIDIOC_DQBUF, &readbuff);
        if(ret < 0)
        {
            sleep_ms(10);
            continue;    
        }

#if (MOUDLETYPE == MOUDLETYPE_TC639_T6)
        s32ShutterTimes ++;
        if (s32ShutterTimes >= 2000)//每隔60s打一次快门
        {
            struct v4l2_control ctrl;
            ctrl.id = V4L2_CID_ZOOM_ABSOLUTE;
            ctrl.value = 0x8000;//change output mode 0x8004/0x8005
            int result = 0;
            //shutter 0x8000
            if (ioctl(fd, VIDIOC_S_CTRL, &ctrl) == -1)
            {
                printf("v4l2_control error:%d\n",result);
            }
            s32ShutterTimes = 0;
        }
#endif
        
        pthread_mutex_lock(&m_stIRInfo.pIrDistributionLock);

        s32CurIdx = (m_stIRInfo.s32RawIdx + 1) % IR_HEAP_BUF_NUM;
        for(int i=0; i < IR_HEAP_BUF_NUM; i++)
        {
            if((IR_HEAP_BUF_WRITE == m_stIRInfo.astRawHeapBuf[s32CurIdx].enBufStatus) || (IR_HEAP_BUF_RW ==  m_stIRInfo.astRawHeapBuf[s32CurIdx].enBufStatus))
            {
                m_stIRInfo.s32RawIdx = s32CurIdx;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurIdx = (s32CurIdx+ 1) % IR_HEAP_BUF_NUM;
        }

        if(SV_FALSE == bGetFrame)
        {
            goto skip_Uvc;
        }

        memcpy(m_stIRInfo.astRawHeapBuf[s32CurIdx].virAddr, mptr[readbuff.index], readbuff.length);
        m_stIRInfo.astRawHeapBuf[s32CurIdx].enBufStatus = IR_HEAP_BUF_RW;

skip_Uvc:
        m_stIRInfo.bGetFrame = SV_TRUE;
        
        pthread_mutex_unlock(&m_stIRInfo.pIrDistributionLock);

        //通知内核 已经使用完
        ret=ioctl(fd,VIDIOC_QBUF,&readbuff);
        if(ret<0){
        
            printf("put quee fail\n");
        }
    }

    return 0;

}

/******************************************************************************
 * 函数功能: 数据分发线程，从红外摄像头取图分发给其他模块
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_Distribution_Body(void *pvArg)
{
    sint32 s32Ret = -1, i;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    sint32 s32RawIdx = 0, s32CurVoIdx = 0,s32CurAlgIdx = 0, s32CurVencIdx = 0, s32CurJpegIdx = 0;
    SV_BOOL bGetFrame = SV_FALSE;
    float *pfTemp = malloc(IR_RAW_WIDTH * IR_RAW_HEIGHT * sizeof(float));//全图的温度矩阵
#if ((MOUDLETYPE == MOUDLETYPE_TC933) || (MOUDLETYPE == MOUDLETYPE_TC639_T2))
    ir_usb_temp_param  stTempParam = {0};
#endif    
    SV_RECT_S stRgn_src,stRgn_dst;
    stRgn_src.s32X = 0;
    stRgn_src.s32Y = 0;
    stRgn_src.u32Height = IR_RAW_HEIGHT;
    stRgn_src.u32Width = IR_RAW_WIDTH;

    stRgn_dst.s32X = 0;
    stRgn_dst.s32Y = 0;
    stRgn_dst.u32Height = IR_RAW_HEIGHT;
    stRgn_dst.u32Width = IR_RAW_WIDTH;
    s32Ret = prctl(PR_SET_NAME, "mpp_ir_Distribution_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
#if ((MOUDLETYPE == MOUDLETYPE_TC933) || (MOUDLETYPE == MOUDLETYPE_TC639_T2))
    stTempParam.exkf = 100;                        //该参数配置推荐使用默认值
    stTempParam.exb = 0;
    stTempParam.emiss = 98;
    stTempParam.transs = 0;
    stTempParam.reflectTemp = 23.0f;
    stTempParam.fEnvironmentIncrement = 2500;
#endif
    while (pstIRInfo->bRunning)
    {
        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }
        
        bGetFrame = SV_FALSE;
        if (pstIRInfo->bGetFrame)
        {
            /* 红外数据格式转换为RK可识别的格式 */
            s64LastTime = microtime();
            
            pthread_mutex_lock(&pstIRInfo->pIrDistributionLock);
            s32RawIdx = pstIRInfo->s32RawIdx;
            for(i = 0; i < IR_HEAP_BUF_NUM; i++)
            {
                if(IR_HEAP_BUF_RW == pstIRInfo->astRawHeapBuf[s32RawIdx].enBufStatus)
                {
                    pstIRInfo->astRawHeapBuf[s32RawIdx].enBufStatus = IR_HEAP_BUF_READ;
                    bGetFrame = SV_TRUE;
                    break;
                }
                s32RawIdx = (s32RawIdx - 1 + IR_HEAP_BUF_NUM) % IR_HEAP_BUF_NUM;
            }
            if(SV_FALSE == bGetFrame)
            {
                goto skip_convert;
            }
#if 0
            /* 全图测温并根据温度阈值过滤像素 */
            if (pstIRInfo->bEnable)
            {
                guide_measure_convertgray2temper(IR_RAW_WIDTH, IR_RAW_HEIGHT, \
                                    pfTemp, pstIRInfo->astRawTempHeapBuf[s32RawIdx].virAddr,\
                                    pstIRInfo->pas16ParamLine[s32RawIdx] ,\
                                    &stTempParam,0);
            }
            s32Ret = ir_convert_YUV422PackToPlanarTemp(pstIRInfo->astRawHeapBuf[s32RawIdx].virAddr,\
                                          pstIRInfo->astConvertHeapBuf[s32RawIdx].virAddr,\
                                          pfTemp,\
                                          IR_RAW_WIDTH, IR_RAW_HEIGHT);  
#else
            if (pstIRInfo->s32SensorClass == 0)
            {
                s32Ret = ir_convert_YUV422PackToPlanar(pstIRInfo->astRawHeapBuf[s32RawIdx].virAddr,\
                                          pstIRInfo->astConvertHeapBuf[s32RawIdx].virAddr,\
                                          IR_RAW_WIDTH, IR_RAW_HEIGHT);
            }
            else if (pstIRInfo->s32SensorClass == 1)
            {
                mpp_ir_Uvc_ScaleCopy(&pstIRInfo->astRawHeapBuf[s32RawIdx],\
                    &pstIRInfo->astConvertHeapBuf[s32RawIdx], &stRgn_src, &stRgn_dst);
            }
#endif            
            pstIRInfo->astRawHeapBuf[s32RawIdx].enBufStatus = IR_HEAP_BUF_WRITE;
            pstIRInfo->bGetFrame = SV_FALSE;
            bGetFrame = SV_FALSE;
            s64CurTime = microtime();          
            //print_level(SV_DEBUG, "distribution frame: %u  %lld %lld %lld\n",u8LastHeapIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));
skip_convert:
              pthread_mutex_unlock(&pstIRInfo->pIrDistributionLock);

            if (pstIRInfo->bUvcFirstPowerOn == SV_TRUE)
            {
                for (int i = 0; i < IR_HEAP_BUF_NUM; i++)
                {
                    pstIRInfo->astRawHeapBuf[i].enBufStatus = IR_HEAP_BUF_WRITE;
                    pstIRInfo->bUvcFirstPowerOn = SV_FALSE;
                }
                continue;
            }

            /* VO */
            pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_VO]);
            s32CurVoIdx = (pstIRInfo->as32TmpIdx[MPP_IR_VO] + 1) % IR_HEAP_BUF_NUM;
            for(i=0; i < IR_HEAP_BUF_NUM; i ++)
            {
                if((IR_HEAP_BUF_WRITE == m_stIRInfo.astTmpHeapBuf[s32CurVoIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].enBufStatus)\ 
                    || (IR_HEAP_BUF_RW ==  m_stIRInfo.astTmpHeapBuf[s32CurVoIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].enBufStatus))
                {
                    pstIRInfo->as32TmpIdx[MPP_IR_VO] = s32CurVoIdx;
                    bGetFrame = SV_TRUE;
                    break;
                }
                s32CurVoIdx = (s32CurVoIdx + 1) % IR_HEAP_BUF_NUM;
            }
            
            if(SV_FALSE == bGetFrame)
            {
                goto skip_vo;
            }        
            memcpy(pstIRInfo->astTmpHeapBuf[s32CurVoIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].virAddr ,pstIRInfo->astConvertHeapBuf[s32RawIdx].virAddr, pstIRInfo->astConvertHeapBuf[s32RawIdx].u32Size);
            m_stIRInfo.astTmpHeapBuf[s32CurVoIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_RW;
            bGetFrame = SV_FALSE;             
skip_vo:
            pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_VO]);

            /* ALG */
            pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_ALG]);
            s32CurAlgIdx = (pstIRInfo->as32TmpIdx[MPP_IR_ALG] + 1) % IR_ALG_HEAP_BUF_NUM;
            for(i=0; i < IR_ALG_HEAP_BUF_NUM; i ++)
            {
                if((IR_HEAP_BUF_WRITE == m_stIRInfo.astTmpHeapBuf[s32CurAlgIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].enBufStatus)\ 
                    || (IR_HEAP_BUF_RW ==  m_stIRInfo.astTmpHeapBuf[s32CurAlgIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].enBufStatus))
                {
                    pstIRInfo->as32TmpIdx[MPP_IR_ALG] = s32CurAlgIdx;
                    bGetFrame = SV_TRUE;
                    break;
                }
                s32CurAlgIdx = (s32CurAlgIdx + 1) % IR_ALG_HEAP_BUF_NUM;
            }
            
            if(SV_FALSE == bGetFrame)
            {
                goto skip_alg;
            }        
            memcpy(pstIRInfo->astTmpHeapBuf[s32CurAlgIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].virAddr ,pstIRInfo->astConvertHeapBuf[s32RawIdx].virAddr, pstIRInfo->astConvertHeapBuf[s32RawIdx].u32Size);
            m_stIRInfo.astTmpHeapBuf[s32CurAlgIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_RW;
            bGetFrame = SV_FALSE;
skip_alg:
            pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_ALG]);
            if (SV_FALSE == pstIRInfo->bRecordStatus)
            {
                goto skip_venc;
            }
            /* venc */
            pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_VENC]);
            s32CurVencIdx = (pstIRInfo->as32TmpIdx[MPP_IR_VENC] + 1) % IR_HEAP_BUF_NUM;
            for(i=0; i < IR_HEAP_BUF_NUM; i ++)
            {
                if((IR_HEAP_BUF_WRITE == m_stIRInfo.astTmpHeapBuf[s32CurVencIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].enBufStatus)\ 
                    || (IR_HEAP_BUF_RW ==  m_stIRInfo.astTmpHeapBuf[s32CurVencIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].enBufStatus))
                {
                    pstIRInfo->as32TmpIdx[MPP_IR_VENC] = s32CurVencIdx;
                    bGetFrame = SV_TRUE;
                    break;
                }
                s32CurVencIdx = (s32CurVencIdx + 1) % IR_HEAP_BUF_NUM;
            }
            
            if(SV_FALSE == bGetFrame)
            {
                goto skip_venc;
            }        
            memcpy(pstIRInfo->astTmpHeapBuf[s32CurVencIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].virAddr ,pstIRInfo->astConvertHeapBuf[s32RawIdx].virAddr, pstIRInfo->astConvertHeapBuf[s32RawIdx].u32Size);
            m_stIRInfo.astTmpHeapBuf[s32CurVencIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_RW;
            bGetFrame = SV_FALSE;
skip_venc:
            pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_VENC]);

            /* jpeg */
            pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_JPEG]);
            s32CurJpegIdx = (pstIRInfo->as32TmpIdx[MPP_IR_JPEG] + 1) % IR_HEAP_BUF_NUM;
            for(i=0; i < IR_HEAP_BUF_NUM; i ++)
            {
                if((IR_HEAP_BUF_WRITE == m_stIRInfo.astTmpHeapBuf[s32CurJpegIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].enBufStatus)\ 
                    || (IR_HEAP_BUF_RW ==  m_stIRInfo.astTmpHeapBuf[s32CurJpegIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].enBufStatus))
                {
                    pstIRInfo->as32TmpIdx[MPP_IR_JPEG] = s32CurJpegIdx;
                    bGetFrame = SV_TRUE;
                    break;
                }
                s32CurJpegIdx = (s32CurJpegIdx + 1) % IR_HEAP_BUF_NUM;
            }
            
            if(SV_FALSE == bGetFrame)
            {
                goto skip_jpeg;
            }        
            memcpy(pstIRInfo->astTmpHeapBuf[s32CurJpegIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].virAddr ,pstIRInfo->astConvertHeapBuf[s32RawIdx].virAddr, pstIRInfo->astConvertHeapBuf[s32RawIdx].u32Size);
            m_stIRInfo.astTmpHeapBuf[s32CurJpegIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_RW;
            bGetFrame = SV_FALSE;
skip_jpeg:
            pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_JPEG]);

            s64CurTime = microtime();
            //print_level(SV_DEBUG, "distribution frame: %d %d %lld %lld %lld\n",s32RawIdx, s32CurVoIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));
        }
        sleep_ms(5);

    }
    free(pfTemp);
    return NULL;
}


/******************************************************************************
 * 函数功能: vo主线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_Vo_Body(void *pvArg)
{
    sint32 s32Ret = -1, i = 0;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32CurVoTmpHeapIdx = 0, s32CurVodrmIdx = 0;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    sint32 s32MirrorFlip = 0;
    SV_BOOL bGetFrame = SV_FALSE;

    SV_RECT_S stRgn_dst, stRgn_src;
    
    stRgn_src.s32X = 0;
    stRgn_src.s32Y = 0;
    stRgn_src.u32Width  = IR_RAW_WIDTH;
    stRgn_src.u32Height = IR_RAW_HEIGHT;

    stRgn_dst.s32X = 0;
    stRgn_dst.s32Y = 0;
    stRgn_dst.u32Width  = IR_VO_WIDTH;
    stRgn_dst.u32Height = IR_VO_HEIGHT;

    s32Ret = prctl(PR_SET_NAME, "mpp_ir_VO_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    
    while (pstIRInfo->bRunning)
    {
        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }
        
        /* RGA转换成需要的显示格式 */
        s64LastTime = microtime();
        /* vo tmp heap消费者 */
        pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_VO]);
        bGetFrame = SV_FALSE;
        s32CurVoTmpHeapIdx = pstIRInfo->as32TmpIdx[MPP_IR_VO];
        for (i = 0; i < IR_HEAP_BUF_NUM; i++)
        {
            if (IR_HEAP_BUF_RW == pstIRInfo->astTmpHeapBuf[s32CurVoTmpHeapIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].enBufStatus)
            {
                pstIRInfo->astTmpHeapBuf[s32CurVoTmpHeapIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurVoTmpHeapIdx = (s32CurVoTmpHeapIdx - 1 + IR_HEAP_BUF_NUM) % IR_HEAP_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            goto skip_vo;
        }
        
        /* vo drm生产者 */
        bGetFrame = SV_FALSE;
        s32CurVodrmIdx = (pstIRInfo->as32DrmIdx[MPP_IR_VO] + 1) % IR_DRM_BUF_NUM;
        for (i=0; i < IR_DRM_BUF_NUM; i ++)
        {
            if ((IR_DRM_BUF_WRITE == pstIRInfo->astVoDrmBuf[s32CurVodrmIdx].enBufStatus)\ 
                || (IR_DRM_BUF_RW ==  pstIRInfo->astVoDrmBuf[s32CurVodrmIdx].enBufStatus))
            {
                pstIRInfo->as32DrmIdx[MPP_IR_VO] = s32CurVodrmIdx;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurVodrmIdx = (s32CurVodrmIdx + 1) % IR_DRM_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            /* 释放掉heap消费者占用的帧 */
            pstIRInfo->astTmpHeapBuf[s32CurVoTmpHeapIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
            goto skip_vo;
        }

        mpp_ir_vo_SetMirrorAndFlip(pstIRInfo->bImageMirror, pstIRInfo->bImageFlip, &s32MirrorFlip);
        mpp_ir_vo_ScaleCopy(&pstIRInfo->astTmpHeapBuf[s32CurVoTmpHeapIdx + MPP_IR_VO * IR_HEAP_BUF_NUM], &pstIRInfo->astVoDrmBuf[s32CurVodrmIdx], &stRgn_src, &stRgn_dst, s32MirrorFlip);

        pstIRInfo->astTmpHeapBuf[s32CurVoTmpHeapIdx + MPP_IR_VO * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
        pstIRInfo->astVoDrmBuf[s32CurVodrmIdx].enBufStatus = IR_DRM_BUF_RW;

        s64CurTime = microtime();        
        //print_level(SV_DEBUG, "vo frame: %u  %lld %lld %lld\n",s32CurVodrmIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));              
skip_vo:

        pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_VO]);

        sleep_ms(5);
    }    
    return NULL;
}

/******************************************************************************
 * 函数功能: 算法模块数据处理主线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_Alg_Body(void *pvArg)
{
    sint32 s32Ret = -1, i = 0, s32MirrorFlip = 0;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32CurAlgTmpHeapIdx = 0, s32CurAlgdrmIdx = 0;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    SV_RECT_S stRgn_dst, stRgn_src;
    SV_BOOL bGetFrame = SV_FALSE;

    stRgn_src.s32X = 0;
    stRgn_src.s32Y = 0;
    stRgn_src.u32Width  = IR_RAW_WIDTH;
    stRgn_src.u32Height = IR_RAW_HEIGHT;

    stRgn_dst.s32X = 0;
    stRgn_dst.s32Y = 0;
    stRgn_dst.u32Width  = IR_ALG_WIDTH;
    stRgn_dst.u32Height = IR_ALG_HEIGHT;

    
    s32Ret = prctl(PR_SET_NAME, "mpp_ir_Alg_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstIRInfo->bRunning)
    {
        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }
    
        /* RGA转换成算法需要格式 */
        s64LastTime = microtime();
        pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_ALG]);
        bGetFrame = SV_FALSE;
        s32CurAlgTmpHeapIdx = pstIRInfo->as32TmpIdx[MPP_IR_ALG];
        for (i = 0; i < IR_ALG_HEAP_BUF_NUM; i++)
        {
            if (IR_HEAP_BUF_RW == pstIRInfo->astTmpHeapBuf[s32CurAlgTmpHeapIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].enBufStatus)
            {
                pstIRInfo->astTmpHeapBuf[s32CurAlgTmpHeapIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurAlgTmpHeapIdx = (s32CurAlgTmpHeapIdx - 1 + IR_ALG_HEAP_BUF_NUM) % IR_ALG_HEAP_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            goto skip_alg;
        }
        
        /* alg drm生产者 */
        bGetFrame = SV_FALSE;
        s32CurAlgdrmIdx = (pstIRInfo->as32DrmIdx[MPP_IR_ALG] + 1) % IR_ALG_DRM_BUF_NUM;
        for (i=0; i < IR_ALG_DRM_BUF_NUM; i ++)
        {
            if ((IR_DRM_BUF_WRITE == pstIRInfo->astAlgDrmBuf[s32CurAlgdrmIdx].enBufStatus)\ 
                || (IR_DRM_BUF_RW ==  pstIRInfo->astAlgDrmBuf[s32CurAlgdrmIdx].enBufStatus))
            {
                pstIRInfo->as32DrmIdx[MPP_IR_ALG] = s32CurAlgdrmIdx;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurAlgdrmIdx = (s32CurAlgdrmIdx + 1) % IR_ALG_DRM_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            /* 释放掉heap消费者占用的帧 */
            pstIRInfo->astTmpHeapBuf[s32CurAlgTmpHeapIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
            goto skip_alg;
        }
        
        mpp_ir_vo_SetMirrorAndFlip(pstIRInfo->bImageMirror, pstIRInfo->bImageFlip, &s32MirrorFlip);
        mpp_ir_alg_ScaleCopy(&pstIRInfo->astTmpHeapBuf[s32CurAlgTmpHeapIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM], &pstIRInfo->astAlgDrmBuf[s32CurAlgdrmIdx], &stRgn_src, &stRgn_dst, s32MirrorFlip);

        pstIRInfo->astTmpHeapBuf[s32CurAlgTmpHeapIdx + MPP_IR_ALG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
        pstIRInfo->astAlgDrmBuf[s32CurAlgdrmIdx].enBufStatus = IR_DRM_BUF_RW;

        s64CurTime = microtime();        
        //print_level(SV_DEBUG, "alg convert frame: %d  %lld %lld %lld\n",s32CurAlgdrmIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));

skip_alg:
        pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_ALG]);

        sleep_ms(5);

    }
}

/******************************************************************************
 * 函数功能: 编码模块数据处理主线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_Venc_Body(void *pvArg)
{
    sint32 s32Ret = -1, i = 0;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32CurVencTmpHeapIdx = 0, s32CurVencdrmIdx = 0, s32CurVencHeapIdx;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    SV_RECT_S stRgn_dst, stRgn_src;
    SV_BOOL bGetFrame = SV_FALSE;
    sint32 s32MirrorFlip = 0;
    stRgn_src.s32X = 0;
    stRgn_src.s32Y = 0;
    stRgn_src.u32Width  = IR_RAW_WIDTH;
    stRgn_src.u32Height = IR_RAW_HEIGHT;

    stRgn_dst.s32X = 0;
    stRgn_dst.s32Y = 0;
    stRgn_dst.u32Width  = IR_VENC_WIDTH;
    stRgn_dst.u32Height = IR_VENC_HEIGHT;
    
    s32Ret = prctl(PR_SET_NAME, "mpp_ir_venc_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstIRInfo->bRunning)
    {
        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }

        if (SV_FALSE == pstIRInfo->bRecordStatus)
        {
            sleep_ms(100);
            continue;
        }
    
        /* RGA转换成需要的编码格式 */
        s64LastTime = microtime();
        pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_VENC]);
        bGetFrame = SV_FALSE;
        s32CurVencTmpHeapIdx = pstIRInfo->as32TmpIdx[MPP_IR_VENC];
        for (i = 0; i < IR_HEAP_BUF_NUM; i++)
        {
            if (IR_HEAP_BUF_RW == pstIRInfo->astTmpHeapBuf[s32CurVencTmpHeapIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].enBufStatus)
            {
                pstIRInfo->astTmpHeapBuf[s32CurVencTmpHeapIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurVencTmpHeapIdx = (s32CurVencTmpHeapIdx - 1 + IR_HEAP_BUF_NUM) % IR_HEAP_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            goto skip_venc;
        }
        
        /* venc heap生产者 */
        bGetFrame = SV_FALSE;
        s32CurVencHeapIdx = (pstIRInfo->as32HeapIdx[MPP_IR_VENC] + 1) % IR_HEAP_BUF_NUM;
        for (i=0; i < IR_HEAP_BUF_NUM; i ++)
        {
            if ((IR_HEAP_BUF_WRITE == pstIRInfo->astVencH264HeapBuf[s32CurVencHeapIdx].enBufStatus)\ 
                || (IR_HEAP_BUF_RW ==  pstIRInfo->astVencH264HeapBuf[s32CurVencHeapIdx].enBufStatus))
            {
                pstIRInfo->as32HeapIdx[MPP_IR_VENC] = s32CurVencHeapIdx;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurVencHeapIdx = (s32CurVencHeapIdx + 1) % IR_HEAP_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            /* 释放掉heap消费者占用的帧 */
            pstIRInfo->astTmpHeapBuf[s32CurVencTmpHeapIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
            goto skip_venc;
        }
        mpp_ir_vo_SetMirrorAndFlip(pstIRInfo->bImageMirror, pstIRInfo->bImageFlip, &s32MirrorFlip);
        mpp_ir_venc_ScaleCopyHeap(&pstIRInfo->astTmpHeapBuf[s32CurVencTmpHeapIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM], &pstIRInfo->astVencH264HeapBuf[s32CurVencHeapIdx], &stRgn_src, &stRgn_dst,s32MirrorFlip);

        pstIRInfo->astTmpHeapBuf[s32CurVencTmpHeapIdx + MPP_IR_VENC * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
        pstIRInfo->astVencH264HeapBuf[s32CurVencHeapIdx].enBufStatus = IR_HEAP_BUF_RW;
        s64CurTime = microtime();        
        //print_level(SV_DEBUG, "venc frame: %d  %lld %lld %lld\n",s32CurVencHeapIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));
skip_venc:
        pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_VENC]);
        sleep_ms(5);

    }
}

/******************************************************************************
 * 函数功能: 图像编码模块数据处理主线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_Jpeg_Body(void *pvArg)
{
    sint32 s32Ret = -1, i = 0, s32MirrorFlip = 0;;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32CurJpegTmpHeapIdx = 0, s32CurJpegdrmIdx = 0, s32CurJpegHeapIdx = 0;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    SV_RECT_S stRgn_dst, stRgn_src;
    SV_BOOL bGetFrame = SV_FALSE;

    stRgn_src.s32X = 0;
    stRgn_src.s32Y = 0;
    stRgn_src.u32Width  = IR_RAW_WIDTH;
    stRgn_src.u32Height = IR_RAW_HEIGHT;

    stRgn_dst.s32X = 0;
    stRgn_dst.s32Y = 0;
    stRgn_dst.u32Width  = IR_VENC_JPEG_WIDTH;
    stRgn_dst.u32Height = IR_VENC_JPEG_HEIGHT;
    
    s32Ret = prctl(PR_SET_NAME, "mpp_ir_venc_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstIRInfo->bRunning)
    {
        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }
        if (SV_FALSE == pstIRInfo->bPictureStream)
        {
            sleep_ms(100);
            continue;
        }
        /* RGA转换成需要的编码格式 */
        s64LastTime = microtime();
        pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_JPEG]);
        bGetFrame = SV_FALSE;
        s32CurJpegTmpHeapIdx = pstIRInfo->as32TmpIdx[MPP_IR_JPEG];
        for (i = 0; i < IR_HEAP_BUF_NUM; i++)
        {
            if (IR_HEAP_BUF_RW == pstIRInfo->astTmpHeapBuf[s32CurJpegTmpHeapIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].enBufStatus)
            {
                pstIRInfo->astTmpHeapBuf[s32CurJpegTmpHeapIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_READ;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurJpegTmpHeapIdx = (s32CurJpegTmpHeapIdx - 1 + IR_HEAP_BUF_NUM) % IR_HEAP_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            goto skip_jpeg;
        }
        
        /* venc heap生产者 */
        bGetFrame = SV_FALSE;
        s32CurJpegHeapIdx = (pstIRInfo->as32HeapIdx[MPP_IR_JPEG] + 1) % IR_HEAP_BUF_NUM;

        for (i=0; i < IR_HEAP_BUF_NUM; i ++)
        {
            if ((IR_HEAP_BUF_WRITE == pstIRInfo->astVencJpegHeapBuf[s32CurJpegHeapIdx].enBufStatus)\ 
                || (IR_HEAP_BUF_RW ==  pstIRInfo->astVencJpegHeapBuf[s32CurJpegHeapIdx].enBufStatus))
            {
                pstIRInfo->as32HeapIdx[MPP_IR_JPEG] = s32CurJpegHeapIdx;
                bGetFrame = SV_TRUE;
                break;
            }
            s32CurJpegHeapIdx = (s32CurJpegHeapIdx + 1) % IR_HEAP_BUF_NUM;
        }
        
        if (SV_FALSE == bGetFrame)
        {
            /* 释放掉heap消费者占用的帧 */
            pstIRInfo->astTmpHeapBuf[s32CurJpegTmpHeapIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
            goto skip_jpeg;
        }
        
        mpp_ir_vo_SetMirrorAndFlip(pstIRInfo->bImageMirror, pstIRInfo->bImageFlip, &s32MirrorFlip);
        mpp_ir_venc_ScaleCopyHeap(&pstIRInfo->astTmpHeapBuf[s32CurJpegTmpHeapIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM], &pstIRInfo->astVencJpegHeapBuf[s32CurJpegHeapIdx], &stRgn_src, &stRgn_dst, s32MirrorFlip);

        pstIRInfo->astTmpHeapBuf[s32CurJpegTmpHeapIdx + MPP_IR_JPEG * IR_HEAP_BUF_NUM].enBufStatus = IR_HEAP_BUF_WRITE;
        pstIRInfo->astVencJpegHeapBuf[s32CurJpegHeapIdx].enBufStatus = IR_HEAP_BUF_RW;
      
        s64CurTime = microtime();        
        //print_level(SV_DEBUG, "venc jpeg frame: %d  %lld %lld %lld\n",s32CurJpegHeapIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime)); 
skip_jpeg:
        pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_JPEG]);
        sleep_ms(5);

    }
    return;
}

/******************************************************************************
 * 函数功能: 图像合成模块数据处理主线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_Vmix_Body(void *pvArg)
{
    sint32 s32Ret = -1, i;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    void *szVpssMB = NULL;
    sint32 s32IRBufIdx = 0;
    sint32 s32VpssChn = MPP_VPSS_CHN_VO;
    SV_RECT_S stRgn_dst, stRgnIr_src, stRgnVis_src, stRgnVmix_src, stRgnCvbs_dst, stRgnRtsp_dst;
    sint32 s32CurVmixDrmIdx = 0;
    sint32 s32CurCvbsVmixDrmIdx = 0;
    sint32 s32CurRtspVmixHeapIdx = 0;
    SPLIT_MODE eIrMode = pstIRInfo->eCurIrMode;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    SV_BOOL bGetVmixFrame = SV_FALSE, bGetCvbsVmixFrame = SV_FALSE, bGetRtspVmixFrame = SV_FALSE, bClear = SV_FALSE;
    SV_BOOL bEnable = pstIRInfo->bEnable;
    SV_BOOL bForceClean = SV_TRUE;
    MPP_GUI_IMAGE_S stGuiImage = {0};
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    long long s64LLastTime = 0;
    uint32 u32Fps = 0;
    stRgnIr_src.s32X = 0;
    stRgnIr_src.s32Y = 0;
    stRgnIr_src.u32Width  = IR_VO_WIDTH;
    stRgnIr_src.u32Height = IR_VO_HEIGHT;

    stRgnVis_src.s32X = 0;
    stRgnVis_src.s32Y = 0;
    stRgnVis_src.u32Width  = pstIRInfo->stSrcVideoSize.u32Width;
    stRgnVis_src.u32Height = pstIRInfo->stSrcVideoSize.u32Height;
    
    stRgn_dst.s32X = 0;
    stRgn_dst.s32Y = 0;
    stRgn_dst.u32Width  = pstIRInfo->stSrcVideoSize.u32Width;
    stRgn_dst.u32Height = pstIRInfo->stSrcVideoSize.u32Height;

    stRgnVmix_src.s32X = 0;
    stRgnVmix_src.s32Y = 0;
    stRgnVmix_src.u32Width  = 1920;
    stRgnVmix_src.u32Height = 1080;

    stRgnRtsp_dst.s32X = 0;
    stRgnRtsp_dst.s32Y = 0;
    stRgnRtsp_dst.u32Width  = 1280;
    stRgnRtsp_dst.u32Height = 720;


    s32Ret = prctl(PR_SET_NAME, "mpp_ir_Vmix_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstIRInfo->bRunning)
    {
        stRgnCvbs_dst.s32X = 0;
        stRgnCvbs_dst.s32Y = 0;
        stRgnCvbs_dst.u32Width  = pstIRInfo->s32ExtscreenWidth;
        stRgnCvbs_dst.u32Height = pstIRInfo->s32ExtscreenHeight;

        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }

        /* AHD摄像头不考虑热插拔情况 */
        s32Ret = mpp_vi_GetFrame(3, s32VpssChn, &szVpssMB);
        if (s32Ret != SV_SUCCESS)
        {
            //print_level(SV_INFO, "mpp_vpss_GetFrame timeout\n");
            sleep_ms(5);
            continue;
        }

        /* 红外取帧超时处理 */
        s32Ret = mpp_ir_vo_GetFrame(&s32IRBufIdx);        
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(5);
            mpp_vpss_ReleaseFrame(&szVpssMB);
            continue;
        }
                
        s64LastTime = microtime();
        pthread_mutex_lock(&pstIRInfo->apMutexLock[MPP_IR_VMIX]);
        
        /* 处理热插拔视频格式转换，拔出时以可见光全屏显示替换 */
        if (bEnable != pstIRInfo->bEnable)
        {
            /* 插入 */
            if (SV_TRUE == pstIRInfo->bEnable)
            {
                pstIRInfo->eCurIrMode = pstIRInfo->eResumeIrMode;
                print_level(SV_WARN, "device insert \n");
            }
            /* 拔出设备 */
            else
            {
                pstIRInfo->eResumeIrMode = pstIRInfo->eCurIrMode;
                print_level(SV_WARN, "devicce remove \n");
            }
        }
        bEnable = pstIRInfo->bEnable;
        
        /* 切换模式时清空一次显示drm buffer,注意资源互斥 */
        if ((eIrMode != pstIRInfo->eCurIrMode) || (SV_TRUE == bForceClean))
        {
            switch (pstIRInfo->eCurIrMode)
            {
                case MEDIA_SPLIT_TWO:
                    sleep_ms(100);
                    s32Ret = mpp_ir_vmix_ClearScreen(pstIRInfo);
                    if (SV_SUCCESS != s32Ret)
                    {
                        bClear = SV_TRUE;
                    }
                    
                    s32Ret = mpp_ir_vmix_FillBoundarySymAll(pstIRInfo);
                    if (SV_SUCCESS != s32Ret)
                    {
                        bClear = SV_TRUE;
                    }
                    break;
                    
                case MEDIA_SPLIT_IR_FIVE_1:
                case MEDIA_SPLIT_IR_FIVE_2:
                    sleep_ms(100);
                    s32Ret = mpp_ir_vmix_ClearScreen(pstIRInfo);
                    if (SV_SUCCESS != s32Ret)
                    {
                        bClear = SV_TRUE;
                    }

                    s32Ret = mpp_ir_vmix_FillBoundaryAsymAll(pstIRInfo);
                    if (SV_SUCCESS != s32Ret)
                    {
                        bClear = SV_TRUE;
                    }
                    
                    break;
                    
                case MEDIA_SPLIT_ONE:
                case MEDIA_SPLIT_IR_ONE:
                case MEDIA_SPLIT_IR_OSD:
                case MEDIA_SPLIT_IR_OSD_2:
                default:
                    s32Ret = mpp_ir_vmix_ClearScreen(pstIRInfo);
                    break;
            }
            bForceClean = SV_FALSE;
            print_level(SV_INFO, "change display mode [%d]-> [%d]\n",eIrMode, pstIRInfo->eCurIrMode);
        }
        
        bGetVmixFrame = SV_FALSE;
        bGetCvbsVmixFrame = SV_FALSE;
        bGetRtspVmixFrame = SV_FALSE;
        s32CurVmixDrmIdx = (pstIRInfo->as32DrmIdx[MPP_IR_VMIX] + 1) % IR_DRM_BUF_NUM;
        s32CurCvbsVmixDrmIdx = (pstIRInfo->as32CvbsDrmIdx + 1) % IR_DRM_BUF_NUM;
        s32CurCvbsVmixDrmIdx = (pstIRInfo->as32RtspHeapIdx + 1) % IR_DRM_BUF_NUM;

        for (i=0; i < IR_DRM_BUF_NUM; i ++)
        {
            if ((IR_DRM_BUF_WRITE == pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].enBufStatus)\ 
                || (IR_DRM_BUF_RW ==  pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].enBufStatus))
            {
                pstIRInfo->as32DrmIdx[MPP_IR_VMIX] = s32CurVmixDrmIdx;
                bGetVmixFrame = SV_TRUE;
                break;
            }
            s32CurVmixDrmIdx = (s32CurVmixDrmIdx + 1) % IR_DRM_BUF_NUM;
        }
        
        for (i=0; i < IR_DRM_BUF_NUM; i ++)
        {
            if ((IR_DRM_BUF_WRITE == pstIRInfo->astCvbsVmixHeapBuf[s32CurCvbsVmixDrmIdx].enBufStatus)\ 
                || (IR_DRM_BUF_RW ==  pstIRInfo->astCvbsVmixHeapBuf[s32CurCvbsVmixDrmIdx].enBufStatus))
            {
                pstIRInfo->as32CvbsDrmIdx = s32CurCvbsVmixDrmIdx;
                bGetCvbsVmixFrame = SV_TRUE;
                break;
            }
            s32CurCvbsVmixDrmIdx = (s32CurCvbsVmixDrmIdx + 1) % IR_DRM_BUF_NUM;
        }

        for (i=0; i < IR_DRM_BUF_NUM; i ++)
        {
            if ((IR_DRM_BUF_WRITE == pstIRInfo->astRtspVmixHeapBuf[s32CurRtspVmixHeapIdx].enBufStatus)\ 
                || (IR_DRM_BUF_RW ==  pstIRInfo->astRtspVmixHeapBuf[s32CurRtspVmixHeapIdx].enBufStatus))
            {
                pstIRInfo->as32RtspHeapIdx = s32CurRtspVmixHeapIdx;
                bGetCvbsVmixFrame = SV_TRUE;
                break;
            }
            s32CurRtspVmixHeapIdx = (s32CurRtspVmixHeapIdx + 1) % IR_DRM_BUF_NUM;
        }
        
        if (SV_FALSE == bGetVmixFrame || SV_FALSE == bGetCvbsVmixFrame)
        {
            goto skip_vmix;
        }        
        /* 显示模式 */
        switch (pstIRInfo->eCurIrMode)
        {
            case MEDIA_SPLIT_ONE:
                s32Ret = ir_convert_vmix_VisCam(&szVpssMB, &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnVis_src, &stRgn_dst);
                break;
            
            case MEDIA_SPLIT_IR_ONE:
                s32Ret = ir_convert_vmix_IrCam(&pstIRInfo->astVoDrmBuf[s32IRBufIdx], &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnIr_src, &stRgn_dst);
                break;
            
            case MEDIA_SPLIT_IR_OSD:
                s32Ret = ir_convert_vmix_Osd(&szVpssMB, &pstIRInfo->astVoDrmBuf[s32IRBufIdx], &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnVis_src, &stRgnIr_src, &stRgn_dst);
                s32Ret = mpp_ir_vmix_FillBoundaryOsd(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx]);

                break;
            case MEDIA_SPLIT_IR_OSD_2:                
                s32Ret = ir_convert_vmix_Osd_2(&szVpssMB, &pstIRInfo->astVoDrmBuf[s32IRBufIdx], &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnVis_src, &stRgnIr_src, &stRgn_dst);
                s32Ret = mpp_ir_vmix_FillBoundaryOsd(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx]);

                break;
            case MEDIA_SPLIT_TWO:
                s32Ret = ir_convert_vmix_DivSym(&szVpssMB, &pstIRInfo->astVoDrmBuf[s32IRBufIdx], &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnVis_src, &stRgnIr_src, &stRgn_dst);
                s32Ret = mpp_ir_vmix_FillBoundarySym(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx]); 
                break;
            
            case MEDIA_SPLIT_IR_FIVE_1:
                if (SV_TRUE == bClear)
                {
                    s32Ret = mpp_ir_vmix_FillColor(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx]);
                    s32Ret = mpp_ir_vmix_FillBoundaryAsym(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx]);
                }                
                s32Ret = ir_convert_vmix_DivAsymVisCam(&szVpssMB, &pstIRInfo->astVoDrmBuf[s32IRBufIdx], &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnVis_src, &stRgnIr_src, &stRgn_dst);
                break;

            case MEDIA_SPLIT_IR_FIVE_2:
                if (SV_TRUE == bClear)
                {
                    s32Ret = mpp_ir_vmix_FillColor(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx]);
                    s32Ret = mpp_ir_vmix_FillBoundaryAsym(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx]);
                }                                
                s32Ret = ir_convert_vmix_DivAsymIrCam(&szVpssMB, &pstIRInfo->astVoDrmBuf[s32IRBufIdx], &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnVis_src, &stRgnIr_src, &stRgn_dst);

                break;
                
            default:
                s32Ret = ir_convert_vmix_VisCam(&szVpssMB, &pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &stRgnVis_src, &stRgn_dst);
                break;
        }
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(1);
        }
        if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
        {
            stGuiImage = MPP_GUI_GetImage(pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].virAddr,\
                                    pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].u32Width,\
                                    pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].u32Height,\
                                    MPP_GUI_FMT_YUV_420SP);
            memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));        
            s32Ret = mpp_ir_YUVProtocol(&stGuiImage, &stMediaGuiDraw, &pstIRInfo->stTempInfo);     
        }
        s32Ret = mpp_vosd_external_callback(IR_VMIX_CHN, MPP_VPSS_CHN_VO, \
                                            pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].virAddr,\
                                            pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].u32Width,\
                                            pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].u32Height);
        if (pstIRInfo->bExtscreenExist == SV_TRUE)
            s32Ret = ir_convert_Cvbs(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &pstIRInfo->astCvbsVmixHeapBuf[s32CurCvbsVmixDrmIdx], &stRgnVmix_src, &stRgnCvbs_dst);

        //s32Ret = ir_convert_Rtsp(&pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx], &pstIRInfo->astRtspVmixHeapBuf[s32CurRtspVmixHeapIdx], &stRgnVmix_src, &stRgnRtsp_dst);

        
        pstIRInfo->astVmixDrmBuf[s32CurVmixDrmIdx].enBufStatus = IR_DRM_BUF_RW;
        pstIRInfo->astCvbsVmixHeapBuf[s32CurCvbsVmixDrmIdx].enBufStatus = IR_DRM_BUF_RW;
        pstIRInfo->astRtspVmixHeapBuf[s32CurRtspVmixHeapIdx].enBufStatus = IR_DRM_BUF_RW;
        s64CurTime = microtime();
#if 1        
        u32Fps++;
        if (s64CurTime-s64LLastTime > 1000000)
        {
            print_level(SV_DEBUG, "%lld idx[%d] FPS[%u]\n",(s64CurTime - s64LLastTime),s32IRBufIdx,u32Fps);
            u32Fps = 0;
            s64LLastTime = s64CurTime;
        }
#endif
        //print_level(SV_DEBUG, "vmix frame: %d  %lld %lld %lld\n",s32CurVmixDrmIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));

skip_vmix:
        pthread_mutex_unlock(&pstIRInfo->apMutexLock[MPP_IR_VMIX]);
        eIrMode = pstIRInfo->eCurIrMode;
        s32Ret = mpp_ir_vo_ReleaseFrame(s32IRBufIdx);        
        mpp_vpss_ReleaseFrame(&szVpssMB);        
        
        sleep_ms(10);
    }

}

/******************************************************************************
 * 函数功能: CVBS主线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_Cvbs_Body(void *pvArg)
{
    sint32 s32Ret = -1, i = 0;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32CurCvbsVmixDrmIdx = 0;
    void *szMB = NULL;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    uint32 u32Fps = 0;

 
    s32Ret = prctl(PR_SET_NAME, "mpp_ir_Cvbs_Body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    while (pstIRInfo->bRunning)
    {
        if (SV_TRUE == pstIRInfo->bInterrupt)
        {
            sleep_ms(100);
            continue;
        }
        if (SV_FALSE == pstIRInfo->bExtscreenExist)
        {
            sleep_ms(100);
            continue;
        }
        /*  */
        pthread_mutex_lock(&pstIRInfo->pIrCvbsLock);
        mpp_ir_Cvbs_GetFrame(&s32CurCvbsVmixDrmIdx);
        szMB = pstIRInfo->astCvbsVmixHeapBuf[s32CurCvbsVmixDrmIdx].virAddr;
        pstIRInfo->pfRgbCallback(1, szMB, 691200);
        mpp_ir_Cvbs_ReleaseFrame(s32CurCvbsVmixDrmIdx);
       
        s64CurTime = microtime();   
#if 1        
        u32Fps++;
        if (s64CurTime-s64LastTime > 1000000)
        {
            print_level(SV_WARN, "%lld idx[%d] FPS[%u]\n",(s64CurTime - s64LastTime),s32CurCvbsVmixDrmIdx,u32Fps);
            u32Fps = 0;
            s64LastTime = s64CurTime;
        }
#endif
        //print_level(SV_DEBUG, "vo frame: %u  %lld %lld %lld\n",s32CurVodrmIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));              

        pthread_mutex_unlock(&pstIRInfo->pIrCvbsLock);

        sleep_ms(4);
    }    
    return NULL;
}



/******************************************************************************
 * 函数功能: 设备监控线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_DevMonitorBody(void *pvArg)
{
    sint32 s32Ret = -1, i = 0;
    sint32 s32NumOfRestart = 0; //机芯重启间隔次数，用于记录时间，目前的作用是断连情况下每隔5s重新上下电一次
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    SV_BOOL bInsert = SV_FALSE;
    SV_BOOL bLoadTmepCure = SV_FALSE;
    char szBuf[512] = {0};
    ir_usb_device_info_t stUSBDevInfo = {0};
    MEDIA_STAT_S stMediaStat = {0};
    stUSBDevInfo.width = IR_RAW_WIDTH;
    stUSBDevInfo.height = IR_RAW_HEIGHT;

    if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
    {
        stUSBDevInfo.video_mode = Y16_PARAM_YUV;
    }
    else
    {
        stUSBDevInfo.video_mode = YUV_PARAM;
    }
    
    s32Ret = prctl(PR_SET_NAME, "mpp_ir_DevMonitorBody");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstIRInfo->bRunning)
    {  
        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = GetInsContext("lsusb", szBuf, sizeof(szBuf)); 
        if (strstr(szBuf, IR_USB_GD_PVID) == NULL &&  strstr(szBuf, IR_UVC_PVID) == NULL)
        {
            if ((!bInsert) && (s32NumOfRestart == 20))
            {
                s32Ret = BOARD_RK_SetGPIO(1, 8, 0);     /* Alarm Out 初始化配置功能, GPIO1_B0*/
                if(s32Ret != SV_SUCCESS)
                {
                    print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    return s32Ret;
                }
                s32Ret = BOARD_RK_SetGPIO(1, 8, 1);     /* Alarm Out 初始化配置功能, GPIO1_B0*/
                if(s32Ret != SV_SUCCESS)
                {
                    print_level(SV_ERROR, "BOARD_RK_SetGPIO fail![err=%#x]\n", s32Ret);
                    return s32Ret;
                }
                s32NumOfRestart = 0;
            }
        }
        s32NumOfRestart++;
        memset(szBuf, 0, sizeof(szBuf));
        s32Ret = GetInsContext("lsusb", szBuf, sizeof(szBuf)); 
        if (strstr(szBuf, IR_USB_GD_PVID) == NULL &&  strstr(szBuf, IR_UVC_PVID) == NULL)
        {
            /* 拔出设备 */
            if (bInsert)
            {
                pthread_mutex_lock(&m_stIRInfo.pStatusChangeLock);
                pstIRInfo->bStatusChange = 1;
                pthread_mutex_unlock(&m_stIRInfo.pStatusChangeLock);
                print_level(SV_WARN, "ircam device remove\n");
                /* 热插拔一定会返回失败，不做判断 */
                if (pstIRInfo->s32SensorClass == 0)
                {
                    s32Ret = ir_usb_Close(&bLoadTmepCure);
                }
                else if (pstIRInfo->s32SensorClass == 1)
                {
                    pstIRInfo->s32SensorClass = 0;//当拔出时，需要显示黑屏，源数据不需要考虑，因为有黑屏线程，
                                                  //但是黑屏线程搞出来的是Y16格式的，那么在分发数据的时候需要按照高德机芯的做法
                }
                pstIRInfo->bEnable = SV_FALSE;
                pstIRInfo->bInsert = SV_FALSE;
                stMediaStat.bDevLose = SV_TRUE;
                stMediaStat.enSplitMode = pstIRInfo->eCurIrMode;
                if(NULL != pstIRInfo->pfStatCallback)
                {
                    pstIRInfo->pfStatCallback(stMediaStat);
                }            
            }
            pstIRInfo->bEnable = SV_FALSE;
            pstIRInfo->bInsert = SV_FALSE;
            
            /* 无设备时要强制刷新osd */
            if (SV_TRUE == pstIRInfo->bUpdateOsd)
            {
                stMediaStat.bDevLose = SV_TRUE;
                stMediaStat.enSplitMode = pstIRInfo->eCurIrMode;
                if(NULL != pstIRInfo->pfStatCallback)
                {
                    pstIRInfo->pfStatCallback(stMediaStat);
                }
                pstIRInfo->bUpdateOsd = SV_FALSE;
            }
        }
        else
        {
            /* 插入设备 */
            if (!bInsert)
            {
                pthread_mutex_lock(&m_stIRInfo.pStatusChangeLock);
                pstIRInfo->bStatusChange = 1;
                pthread_mutex_unlock(&m_stIRInfo.pStatusChangeLock);
                print_level(SV_WARN, "ircam device insert\n");
                if(strstr(szBuf, IR_USB_GD_PVID) != NULL)
                {
                    pstIRInfo->s32SensorClass = 0;
                    s32Ret = ir_usb_Open(&stUSBDevInfo, ir_usb_SerailCallBack, ir_usb_ConStatCallBack, ir_usb_FrameCallBack, \
                                          &bLoadTmepCure);
                    if(SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_ERROR, "ir_usb_Open failed [error %x]\n",s32Ret);
                        sleep_ms(200);
                        continue;
                    }
                }
                else if (strstr(szBuf, IR_UVC_PVID) != NULL)
                {
                    pstIRInfo->bUvcFirstPowerOn = SV_TRUE;
                    pstIRInfo->s32SensorClass = 1;
                }
                pstIRInfo->bEnable = SV_TRUE;
                stMediaStat.bDevLose = SV_FALSE;
                stMediaStat.enSplitMode = pstIRInfo->eResumeIrMode;
                if (NULL != pstIRInfo->pfStatCallback)
                {
                    pstIRInfo->pfStatCallback(stMediaStat);
                }                
            }
            pstIRInfo->bInsert = SV_TRUE;
        }
        
        bInsert = pstIRInfo->bInsert;
        sleep_ms(250);
    }
    return NULL;
}
/******************************************************************************
 * 函数功能: 设备丢失时采用将黑色图片替换全部过程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_FillBlackBody(void *pvArg)
{
    sint32 s32Ret = -1, s32CurIdx = 0, i;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    long long s64CurTime = 0;
    long long s64LastTime = 0;
    SV_BOOL bGetFrame = SV_FALSE;
    
    char szBlackData[IR_RAW_WIDTH * IR_RAW_HEIGHT * 2] = {0};
    char *pcBlackData = szBlackData;
    
    for (i=0;i<IR_RAW_WIDTH * IR_RAW_HEIGHT * 2;i++)
    {
        if(0 == i % 2)
            *pcBlackData = 128; //uyvy黑色
        else
            *pcBlackData = 0;
        
        pcBlackData++;
    }

    while (pstIRInfo->bRunning)
    {
        if (SV_FALSE == pstIRInfo->bEnable)
        {
            s64LastTime = microtime(); 
            pthread_mutex_lock(&pstIRInfo->pIrDistributionLock);
            
            s32CurIdx = (pstIRInfo->s32RawIdx + 1) % IR_HEAP_BUF_NUM;
            for(i=0; i < IR_HEAP_BUF_NUM; i++)
            {
                if((IR_HEAP_BUF_WRITE == pstIRInfo->astRawHeapBuf[s32CurIdx].enBufStatus) || (IR_HEAP_BUF_RW ==  pstIRInfo->astRawHeapBuf[s32CurIdx].enBufStatus))
                {
                    pstIRInfo->s32RawIdx = s32CurIdx;
                    bGetFrame = SV_TRUE;
                    break;
                }
                s32CurIdx = (s32CurIdx+ 1) % IR_HEAP_BUF_NUM;
            }
            
            if(SV_FALSE == bGetFrame)
            {
                goto skip;
            }
            memcpy(pstIRInfo->astRawHeapBuf[s32CurIdx].virAddr, szBlackData, pstIRInfo->astRawHeapBuf[s32CurIdx].u32Size);
            pstIRInfo->astRawHeapBuf[s32CurIdx].enBufStatus = IR_HEAP_BUF_RW;
            s64CurTime = microtime();
            //print_level(SV_DEBUG, "get frame: %d %lld %lld %lld\n",s32CurIdx, s64CurTime, s64LastTime, (s64CurTime - s64LastTime));
skip:
            pstIRInfo->bGetFrame = SV_TRUE;
            pthread_mutex_unlock(&pstIRInfo->pIrDistributionLock);
        }
        sleep_ms(30);
    }

}

/******************************************************************************
 * 函数功能: 发送数据线程到编码通道线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_venc_SendBody(void *pvArg)
{
    sint32 s32Ret = -1;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32IRBufIdx;
    VENC_RESOLUTION_PARAM_S stResolution;
    MB_IMAGE_INFO_S stImageInfo;
    MB_IMAGE_INFO_S stMb_Info;

    while (pstIRInfo->bRunning)
    {
        s32Ret = mpp_ir_venc_GetFrame(&s32IRBufIdx);
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(5);
            continue;
        }
        if (SV_FALSE == pstIRInfo->bRecordStatus)
        {
            sleep_ms(100);
            continue;
        }
        stImageInfo.u32Width = IR_VENC_WIDTH;
        stImageInfo.u32Height = IR_VENC_HEIGHT;
        stImageInfo.u32HorStride = IR_VENC_WIDTH;
        stImageInfo.u32VerStride = IR_VENC_HEIGHT;
        stImageInfo.enImgType = IMAGE_TYPE_NV12;
        MEDIA_BUFFER mb =  RK_MPI_MB_CreateImageBuffer(&stImageInfo, RK_TRUE, MB_FLAG_NOCACHED);
        memcpy(RK_MPI_MB_GetPtr(mb), pstIRInfo->astVencH264HeapBuf[s32IRBufIdx].virAddr, pstIRInfo->astVencH264HeapBuf[s32IRBufIdx].u32Size);
        RK_MPI_MB_SetSize(mb, pstIRInfo->astVencH264HeapBuf[s32IRBufIdx].u32Size);
        RK_MPI_SYS_SendMediaBuffer(RK_ID_VENC, IR_VENC_CHN, mb);
        RK_MPI_MB_ReleaseBuffer(mb);
        
        mpp_ir_venc_ReleaseFrame(s32IRBufIdx);
        sleep_ms(5);
    }

}

/******************************************************************************
 * 函数功能: 接收编码后的数据
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_venc_ReceiveBody(void *pvArg)
{
    sint32 s32Ret = -1, i;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32IRBufIdx;
    VENC_RESOLUTION_PARAM_S stResolution;
    MB_IMAGE_INFO_S stImageInfo;
    MB_IMAGE_INFO_S stMb_Info;
    MEDIA_BUFFER szMB = NULL;
    sint32 s32MaxFd = 0;
    static fd_set fdSet = {0};
    struct timeval timeout = {2, 0};
    sint32 s32VencFd = pstIRInfo->s32VencFd;
    PACK_TYPE_E enPackType;
    STREAM_FRAME_S stFrame;
    static uint32 framestep = 0;
    SFIFO_MSHEAD stPacketHead = {0};
    SFIFO_MDADDR stMsAddr = {0};
    uint32 u32PktSize = 0;
    struct timeval tv = {0};
    
    while (pstIRInfo->bRunning)
    {
        if (SV_FALSE == pstIRInfo->bRecordStatus)
        {
            sleep_ms(100);
            continue;
        }
        memset(&stPacketHead, 0, sizeof(SFIFO_MSHEAD));
        memset(&stMsAddr, 0, sizeof(SFIFO_MDADDR));
        FD_ZERO(&fdSet);
        FD_SET(s32VencFd, &fdSet);
        s32MaxFd = s32VencFd + 1;
        timeout.tv_sec = 0;
        timeout.tv_usec = 80000;
        s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout); 
        if(s32Ret < 0)
        {
            if(errno != EINTR)
            {
                print_level(SV_ERROR, "select failed\n");
            }
        }
        else if(0 == s32Ret)
        {
            //print_level(SV_INFO, "select timeout\n");
        }
        else if(FD_ISSET(s32VencFd,&fdSet))
        {
            szMB = RK_MPI_SYS_GetMediaBuffer(RK_ID_VENC, IR_VENC_CHN, 50);
            if (!szMB) 
            {
              print_level(SV_ERROR, "RK_MPI_SYS_GetMediaBuffer get null buffer!\n");
              continue;
            }

            gettimeofday(&tv, NULL);
            stFrame.u64PTS = tv.tv_sec*1000000ll + tv.tv_usec;
            stFrame.u32Sep = framestep++;
            stFrame.u32PackCount = 1;            
            switch (RK_MPI_MB_GetFlag(szMB)){
                case VENC_NALU_IDRSLICE:
                case VENC_NALU_ISLICE:
                    enPackType = PACK_TYPE_ISLICE;
                    break;
                case VENC_NALU_PSLICE:
                    enPackType = PACK_TYPE_PSLICE;
                    break;
                default:
                    enPackType = PACK_TYPE_PSLICE;
                    break;
            }
            stFrame.astPacks[0].enPackType = enPackType;
            stFrame.astPacks[0].pu8Addr = RK_MPI_MB_GetPtr(szMB);
            stFrame.astPacks[0].u32Len = RK_MPI_MB_GetSize(szMB);

            u32PktSize = 0;
            stMsAddr.u32DataCnt = stFrame.u32PackCount;
            for (i = 0; i < stFrame.u32PackCount; i++)
            {            
                stMsAddr.pau8Addr[i] = stFrame.astPacks[i].pu8Addr;
                stMsAddr.au32Len[i] = stFrame.astPacks[i].u32Len;
                u32PktSize += stMsAddr.au32Len[i];
            }
            stPacketHead.flag = MSHEAD_FLAG;
            stPacketHead.algorithm = 0;            
            stPacketHead.type = (stFrame.astPacks[0].enPackType == 0) ? 1 : 0;
            stPacketHead.width = IR_VENC_WIDTH;
            stPacketHead.height = IR_VENC_HEIGHT;
            stPacketHead.serial = stFrame.u32Sep;
            stPacketHead.pts = stFrame.u64PTS;
            s32Ret = SFIFO_WritePacket(pstIRInfo->s32QueId, &stPacketHead, &stMsAddr);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_WritePacket failed. [err=%#x]\n", s32Ret);
            }
            
            s32Ret = RK_MPI_MB_ReleaseBuffer(szMB);
            if (RK_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RK_MPI_MB_ReleaseBuffer failed! err[%d]\n", s32Ret);
            }
            
        }

    }

}

/******************************************************************************
 * 函数功能: 发送数据线程到编码通道线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_jpeg_SendBody(void *pvArg)
{
    sint32 s32Ret = -1;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32IRBufIdx;
    VENC_RESOLUTION_PARAM_S stResolution;
    MB_IMAGE_INFO_S stImageInfo;
    MB_IMAGE_INFO_S stMb_Info;
    VENC_CHN_STATUS_S stStat = {0};
    
    while (pstIRInfo->bRunning)
    {
        if (SV_FALSE == pstIRInfo->bPictureStream)
        {
            sleep_ms(100);
            continue;
        }
        s32Ret = mpp_ir_jpeg_GetFrame(&s32IRBufIdx);
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(10);
            continue;
        }        
        
        s32Ret = RK_MPI_VENC_QueryStatus(IR_VENC_JPEG_CHN, &stStat);
        stImageInfo.u32Width = IR_VENC_JPEG_WIDTH;
        stImageInfo.u32Height = IR_VENC_JPEG_HEIGHT;
        stImageInfo.u32HorStride = IR_VENC_JPEG_WIDTH;
        stImageInfo.u32VerStride = IR_VENC_JPEG_HEIGHT;
        stImageInfo.enImgType = IMAGE_TYPE_NV12;
        MEDIA_BUFFER mb =  RK_MPI_MB_CreateImageBuffer(&stImageInfo, RK_TRUE, MB_FLAG_NOCACHED);    
        memcpy(RK_MPI_MB_GetPtr(mb), pstIRInfo->astVencJpegHeapBuf[s32IRBufIdx].virAddr, pstIRInfo->astVencJpegHeapBuf[s32IRBufIdx].u32Size);      
        RK_MPI_MB_SetSize(mb, pstIRInfo->astVencJpegHeapBuf[s32IRBufIdx].u32Size);
        RK_MPI_SYS_SendMediaBuffer(RK_ID_VENC, IR_VENC_JPEG_CHN, mb);
        RK_MPI_MB_ReleaseBuffer(mb);
        
        mpp_ir_jpeg_ReleaseFrame(s32IRBufIdx);      
        sleep_ms(10);

    }

}

/******************************************************************************
 * 函数功能: 接收编码后的数据
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_jpeg_ReceiveBody(void *pvArg)
{
    uint8 *pu8VirAddr = NULL;
    sint32 s32Ret = -1;
    sint32 s32MaxFd = 0;
    sint32 s32Fd = 0;
    sint32 s32IRBufIdx;
    uint32 u32Len = 0;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    VENC_RESOLUTION_PARAM_S stResolution;
    MB_IMAGE_INFO_S stImageInfo;
    MB_IMAGE_INFO_S stMb_Info;
    MEDIA_BUFFER szMB = NULL;
    static fd_set fdSet = {0};
    struct timeval timeout = {2, 0};
    sint32 s32JpegFd = pstIRInfo->s32VencJpegFd;
    char szTempFile[64];
    sprintf(szTempFile, "/var/snap/ir.jpeg");  
    
    while (pstIRInfo->bRunning)
    {
        if (SV_FALSE == pstIRInfo->bPictureStream)
        {
            sleep_ms(100);
            continue;
        }
        FD_ZERO(&fdSet);
        FD_SET(s32JpegFd, &fdSet);
        s32MaxFd = s32JpegFd + 1;
        timeout.tv_sec = 0;
        timeout.tv_usec = 80000;
        s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout); 
        if(s32Ret < 0)
        {
            if(errno != EINTR)
            {
                print_level(SV_ERROR, "select failed\n");
            }
        }
        else if(0 == s32Ret)
        {
            //print_level(SV_INFO, "select timeout\n");
        }
        else if(FD_ISSET(s32JpegFd,&fdSet))
        {
            szMB = RK_MPI_SYS_GetMediaBuffer(RK_ID_VENC, IR_VENC_JPEG_CHN, 50);
            if (!szMB) 
            {
              print_level(SV_ERROR, "RK_MPI_SYS_GetMediaBuffer get null buffer!\n");
              continue;
            }
            
            s32Fd = open(szTempFile, O_CREAT | O_RDWR, S_IRWXU | S_IRGRP | S_IROTH);
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "create file: %s failed! [err: %s]\n", szTempFile, strerror(errno));
                continue;
            }
            pu8VirAddr = RK_MPI_MB_GetPtr(szMB);
            u32Len = RK_MPI_MB_GetSize(szMB);

            s32Ret = write(s32Fd, pu8VirAddr, u32Len);
            if (s32Ret < 0)
            {
                close(s32Fd);
                continue;
            }            
            close(s32Fd);
            rename(szTempFile, IR_SNAP_PATH);
            s32Ret = RK_MPI_MB_ReleaseBuffer(szMB);
            if (RK_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "HI_MPI_VENC_ReleaseStream failed! err[%d]\n", s32Ret);
            }           
        }
    }
}

void *mpp_ir_venc_rtsp_SendBody(void *pvArg)
{
    sint32 s32Ret = -1;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32IRBufIdx;
    VENC_RESOLUTION_PARAM_S stResolution;
    MB_IMAGE_INFO_S stImageInfo;
    MB_IMAGE_INFO_S stMb_Info;
 
    while (pstIRInfo->bRunning)
    {
        s32Ret = mpp_ir_venc_rtsp_GetFrame(&s32IRBufIdx);
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(5);
            continue;
        }
        stImageInfo.u32Width = IR_VENC_WIDTH;
        stImageInfo.u32Height = IR_VENC_HEIGHT;
        stImageInfo.u32HorStride = IR_VENC_WIDTH;
        stImageInfo.u32VerStride = IR_VENC_HEIGHT;
        stImageInfo.enImgType = IMAGE_TYPE_NV12;
        MEDIA_BUFFER mb =  RK_MPI_MB_CreateImageBuffer(&stImageInfo, RK_TRUE, MB_FLAG_NOCACHED);
        memcpy(RK_MPI_MB_GetPtr(mb), pstIRInfo->astRtspVmixHeapBuf[s32IRBufIdx].virAddr, pstIRInfo->astRtspVmixHeapBuf[s32IRBufIdx].u32Size);
        RK_MPI_MB_SetSize(mb, pstIRInfo->astRtspVmixHeapBuf[s32IRBufIdx].u32Size);
        RK_MPI_SYS_SendMediaBuffer(RK_ID_VENC, IR_VENC_VMIX_CHN, mb);
        RK_MPI_MB_ReleaseBuffer(mb);
        
        mpp_ir_venc_rtsp_ReleaseFrame(s32IRBufIdx);
        sleep_ms(5);
    }

}


void *mpp_ir_venc_rtsp_ReceiveBody(void *pvArg)
{
    sint32 s32Ret = -1, i;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32IRBufIdx;
    VENC_RESOLUTION_PARAM_S stResolution;
    MB_IMAGE_INFO_S stImageInfo;
    MB_IMAGE_INFO_S stMb_Info;
    MEDIA_BUFFER szMB = NULL;
    sint32 s32MaxFd = 0;
    static fd_set fdSet = {0};
    struct timeval timeout = {2, 0};
    sint32 s32VencFd = pstIRInfo->s32RtspVencFd;
    PACK_TYPE_E enPackType;
    STREAM_FRAME_S stFrame;
    static uint32 framestep = 0;
    SFIFO_MSHEAD stPacketHead = {0};
    SFIFO_MDADDR stMsAddr = {0};
    uint32 u32PktSize = 0;
    struct timeval tv = {0};
    
    while (pstIRInfo->bRunning)
    {
        memset(&stPacketHead, 0, sizeof(SFIFO_MSHEAD));
        memset(&stMsAddr, 0, sizeof(SFIFO_MDADDR));
        FD_ZERO(&fdSet);
        FD_SET(s32VencFd, &fdSet);
        s32MaxFd = s32VencFd + 1;
        timeout.tv_sec = 0;
        timeout.tv_usec = 80000;
        s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout); 
        if(s32Ret < 0)
        {
            if(errno != EINTR)
            {
                print_level(SV_ERROR, "select failed\n");
            }
        }
        else if(0 == s32Ret)
        {
            //print_level(SV_INFO, "select timeout\n");
        }
        else if(FD_ISSET(s32VencFd,&fdSet))
        {
            szMB = RK_MPI_SYS_GetMediaBuffer(RK_ID_VENC, IR_VENC_VMIX_CHN, 50);
            if (!szMB) 
            {
              print_level(SV_ERROR, "RK_MPI_SYS_GetMediaBuffer get null buffer!\n");
              continue;
            }

            gettimeofday(&tv, NULL);
            stFrame.u64PTS = tv.tv_sec*1000000ll + tv.tv_usec;
            stFrame.u32Sep = framestep++;
            stFrame.u32PackCount = 1;            
            switch (RK_MPI_MB_GetFlag(szMB)){
                case VENC_NALU_IDRSLICE:
                case VENC_NALU_ISLICE:
                    enPackType = PACK_TYPE_ISLICE;
                    break;
                case VENC_NALU_PSLICE:
                    enPackType = PACK_TYPE_PSLICE;
                    break;
                default:
                    enPackType = PACK_TYPE_PSLICE;
                    break;
            }
            stFrame.astPacks[0].enPackType = enPackType;
            stFrame.astPacks[0].pu8Addr = RK_MPI_MB_GetPtr(szMB);
            stFrame.astPacks[0].u32Len = RK_MPI_MB_GetSize(szMB);
 
            u32PktSize = 0;
            stMsAddr.u32DataCnt = stFrame.u32PackCount;
            for (i = 0; i < stFrame.u32PackCount; i++)
            {            
                stMsAddr.pau8Addr[i] = stFrame.astPacks[i].pu8Addr;
                stMsAddr.au32Len[i] = stFrame.astPacks[i].u32Len;
                u32PktSize += stMsAddr.au32Len[i];
            }
            stPacketHead.flag = MSHEAD_FLAG;
            stPacketHead.algorithm = 0;            
            stPacketHead.type = (stFrame.astPacks[0].enPackType == 0) ? 1 : 0;
            stPacketHead.width = IR_VENC_JPEG_WIDTH;
            stPacketHead.height = IR_VENC_JPEG_HEIGHT;
            stPacketHead.serial = stFrame.u32Sep;
            stPacketHead.pts = stFrame.u64PTS;
            s32Ret = SFIFO_WritePacket(pstIRInfo->s32RtspQueId, &stPacketHead, &stMsAddr);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_WritePacket failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = RK_MPI_MB_ReleaseBuffer(szMB);
            if (RK_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "RK_MPI_MB_ReleaseBuffer failed! err[%d]\n", s32Ret);
            }
            
        }

    }

}

/******************************************************************************
 * 函数功能: 红外算法模块发送数据给算法进程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_alg_SendBody(void *pvArg)
{
    sint32 s32Ret = -1;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    sint32 s32IRBufIdx;
    sint32 i;
    sint32 as32AlgIdx[IR_ALG_DRM_BUF_NUM] = {-1};//赋值只有第一个是-1,其余不是-1;
    memset(as32AlgIdx, -1, sizeof(as32AlgIdx));

#if ALG_MUTLIT_BUFFER
    s32Ret = MH_Setup(IR_ALG_CHN);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Setup failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
#endif
    
    while (pstIRInfo->bRunning)
    {
        s32Ret = mpp_ir_alg_GetFrame(&s32IRBufIdx);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_DEBUG, "mpp_ir_alg_GetFrame timeout\n");
            sleep_ms(10);
            continue;
        }
#if ALG_MUTLIT_BUFFER
        s32Ret = MS_V(IR_ALG_SEM_CHN);
      
        for(i = 0; i < IR_ALG_DRM_BUF_NUM; i++)
        {
            if(as32AlgIdx[i] != -1)
            {
                if(as32AlgIdx[i] >= 0 && MH_IsWrite(IR_ALG_CHN, i))
                {
                    MH_SetForbid(IR_ALG_CHN, i, 0);
                    mpp_ir_alg_ReleaseFrame(as32AlgIdx[i]);
                    as32AlgIdx[i] = -1;
                }
            }
        }

        /* 增加可读写块 */
        for(i = 0; i < IR_ALG_DRM_BUF_NUM; i++)
        {
            if(as32AlgIdx[i] == -1)
            {
                as32AlgIdx[i] = s32IRBufIdx;
                if(as32AlgIdx[i] >= 0)
                {
                    MH_SetRW(IR_ALG_CHN, i, 0);
                }
                break;
            }
        }       
        s32Ret = MS_P(IR_ALG_SEM_CHN);
#else
        s32Ret = MS_V(IR_ALG_SEM_CHN);
        sleep_ms(2);
        s32Ret = MS_P(IR_ALG_SEM_CHN);
        mpp_ir_alg_ReleaseFrame(s32IRBufIdx);
#endif
    }

}

/******************************************************************************
 * 函数功能: 报警触发处理线程
 * 输入参数: 模块参数
 * 输出参数: 无
 * 返回值  : NULL
 * 注意    : 无
 *****************************************************************************/
void *mpp_ir_TempAlarmBody(void *pvArg)
{
    sint32 s32Ret = -1, pwm, i = 0, s32AlarmLv = 0;
    MPP_IR_INFO_S *pstIRInfo = (MPP_IR_INFO_S *)pvArg;
    SV_BOOL bLastVaild = pstIRInfo->stTempInfo.bVaild;
    static struct timespec tvStart = {0, 0};
    static struct timespec tvNow = {0, 0};
    TRIGGER_TYPE_S enTriggerType = TRIGGER_UP;
    
    mpp_ir_AlarmOutDisable(enTriggerType);

    while (pstIRInfo->bRunning)
    {
        if (!pstIRInfo->bEnable)
        {
            pstIRInfo->stTempInfo.bVaild = SV_FALSE;
            pstIRInfo->stTempInfo.fTempMax = 0;
            pstIRInfo->stTempInfo.fTempMin = 0;
            pstIRInfo->stTempInfo.fTempMaxPosX = -1;
            pstIRInfo->stTempInfo.fTempMaxPosY = -1;
        }
        
        if (!pstIRInfo->stTempInfo.bVaild)
        {
            sleep_ms(30);
            continue;
        }
        
        mpp_ir_AlarmPost(pstIRInfo);
        
        sleep_ms(30);
        
    }
    return;
}


/******************************************************************************
 * 函数功能: 算法信号初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_alg_Init(void)
{
    sint32 s32Ret;
    s32Ret = MS_Init();
    if(s32Ret != NULL)
    {
        return SV_FAILURE;
    }

#if ALG_MUTLIT_BUFFER
    s32Ret = MH_Init();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Init failed!\n");
        return SV_FAILURE;
    }

    s32Ret = MH_Pretreat();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Pretreat failed!\n");
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: ir初始化
 * 输入参数: pstVdecConf -- 通道配置信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_Init(MPP_IR_CONF_S *pstIRConfig)
{
    sint32 s32Ret = -1, s32DevFd = -1, i;
    uint32 u32DisWidth, u32DisHeight, u32SrcWidth, u32SrcHeight, u32AlgWidth, u32AlgHeight, u32VmixWidth, u32VmixHeight;
    CFG_SYS_PARAM  stSysParam = {0};
    m_stIRInfo.s32ExtscreenWidth = pstIRConfig->s32ExtscreenWidth;
    m_stIRInfo.s32ExtscreenHeight = pstIRConfig->s32ExtscreenHeight;


    IR_DRM_BUF_INFO_S *pstRawDrmBuf = m_stIRInfo.astRawDrmBuf;
    IR_DRM_BUF_INFO_S *pstVoDrmBuf = m_stIRInfo.astVoDrmBuf;
    IR_DRM_BUF_INFO_S *pstAlgDrmBuf = m_stIRInfo.astAlgDrmBuf;
    IR_DRM_BUF_INFO_S *pstVencH264DrmBuf = m_stIRInfo.astVencH264DrmBuf;
    IR_DRM_BUF_INFO_S *pstVencJPEGDrmBuf = m_stIRInfo.astVencJPEGDrmBuf;
    IR_DRM_BUF_INFO_S *pstVmixDrmBuf = m_stIRInfo.astVmixDrmBuf;

    IR_HEAP_BUF_INFO_S *pstRawHeapBuf = m_stIRInfo.astRawHeapBuf;
    IR_HEAP_BUF_INFO_S *pstRawTempHeapBuf = m_stIRInfo.astRawTempHeapBuf;
    IR_HEAP_BUF_INFO_S *pstConvertHeapBuf = m_stIRInfo.astConvertHeapBuf;
    IR_HEAP_BUF_INFO_S *pstTmpHeapBuf = m_stIRInfo.astTmpHeapBuf;
    IR_HEAP_BUF_INFO_S *pstVoHeapBuf = m_stIRInfo.astVoHeapBuf;
    IR_HEAP_BUF_INFO_S *pstAlgHeapBuf = m_stIRInfo.astAlgHeapBuf;
    IR_HEAP_BUF_INFO_S *pstVencHeapBuf = m_stIRInfo.astVencH264HeapBuf;
    IR_HEAP_BUF_INFO_S *pstVencJpegHeapBuf = m_stIRInfo.astVencJpegHeapBuf;
    IR_HEAP_BUF_INFO_S *pstCvbsVmixHeapBuf = m_stIRInfo.astCvbsVmixHeapBuf;
    IR_HEAP_BUF_INFO_S *pstRtspVmixHeapBuf = m_stIRInfo.astRtspVmixHeapBuf;
    struct rgaContext  *stRgaCtx;
    void **prgactx = &stRgaCtx;

    /* 初始化互斥锁 */
    s32Ret = pthread_mutex_init(&m_stIRInfo.pIrDistributionLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%d]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    s32Ret = pthread_mutex_init(&m_stIRInfo.pIrCvbsLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%d]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
    
    for (i=0; i<MPP_IR_BUTT; i++)
    {
        s32Ret = pthread_mutex_init(&m_stIRInfo.apMutexLock[i], NULL);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_mutex_init failed! [err=%d]\n", s32Ret);
            return ERR_SYS_NOTREADY;
        }        
    }

    s32Ret = pthread_mutex_init(&m_stIRInfo.pIrCmdLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%d]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    s32Ret = pthread_mutex_init(&m_stIRInfo.pStatusChangeLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%d]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    /* 打开显卡 */
    s32DevFd = open(MPP_FRAMEBUFFER_FD, O_RDWR | O_CLOEXEC);
    if (s32DevFd <= 0)
    {
        print_level(SV_ERROR, "open %s fail! [err=%#x]\n", MPP_FRAMEBUFFER_FD, s32DevFd);
        return SV_FAILURE;
    }
    
    u32SrcWidth  = IR_RAW_WIDTH;
    u32SrcHeight = IR_RAW_HEIGHT;
    
    u32DisWidth  = IR_VO_WIDTH;
    u32DisHeight = IR_VO_HEIGHT;

    u32AlgWidth  = IR_ALG_WIDTH;
    u32AlgHeight = IR_ALG_HEIGHT;

    u32VmixWidth = IR_VMIX_WIDTH;
    u32VmixHeight = IR_VMIX_HEIGHT;
    
    /* 分配 drm */
    /* RAW 数据 */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = ir_drm_create_fb(s32DevFd, pstRawDrmBuf, IR_RAW_WIDTH, IR_RAW_HEIGHT, DRM_FORMAT_UYVY);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstRawDrmBuf->enBufStatus = IR_DRM_BUF_WRITE;
        pstRawDrmBuf++;
    }    
    /* VO */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = ir_drm_create_fb(s32DevFd, pstVoDrmBuf, IR_VO_WIDTH, IR_VO_HEIGHT, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstVoDrmBuf->enBufStatus = IR_DRM_BUF_WRITE;
        pstVoDrmBuf++;
    }
    /* ALG */
    for (i = 0; i < IR_ALG_DRM_BUF_NUM; i++)
    {
        s32Ret = ir_drm_create_fb(s32DevFd, pstAlgDrmBuf, IR_ALG_WIDTH, IR_ALG_HEIGHT, DRM_FORMAT_RGB888);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstAlgDrmBuf->enBufStatus = IR_DRM_BUF_WRITE;
        pstAlgDrmBuf++;
    }    
    /* VENC H264 */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = ir_drm_create_fb(s32DevFd, pstVencH264DrmBuf, IR_VENC_WIDTH, IR_VENC_HEIGHT, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstVencH264DrmBuf->enBufStatus = IR_DRM_BUF_WRITE;
        pstVencH264DrmBuf++;
    }    
    /* VENC JPEG */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = ir_drm_create_fb(s32DevFd, pstVencJPEGDrmBuf, IR_VENC_JPEG_WIDTH, IR_VENC_JPEG_HEIGHT, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstVencJPEGDrmBuf->enBufStatus = IR_DRM_BUF_WRITE;
        pstVencJPEGDrmBuf++;
    }    
    /* VMIX */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = ir_drm_create_fb(s32DevFd, pstVmixDrmBuf, IR_VMIX_WIDTH, IR_VMIX_HEIGHT, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        mpp_ir_vmix_FillColor(pstVmixDrmBuf);
        pstVmixDrmBuf->enBufStatus = IR_DRM_BUF_WRITE;
        pstVmixDrmBuf++;
    }
    
    /* 分配堆 */
    /* RAW */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstRawHeapBuf, IR_RAW_WIDTH, IR_RAW_HEIGHT, DRM_FORMAT_UYVY);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstRawHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstRawHeapBuf++;
        s32Ret = ir_heap_create(pstRawTempHeapBuf, IR_RAW_WIDTH, IR_RAW_HEIGHT, DRM_FORMAT_UYVY);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstRawTempHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstRawTempHeapBuf++;
                
        m_stIRInfo.pas16ParamLine[i] = (short *)malloc(IR_RAW_WIDTH * sizeof(short));
        memset(m_stIRInfo.pas16ParamLine[i], 0, IR_RAW_WIDTH * sizeof(short));
    }
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstConvertHeapBuf, IR_RAW_WIDTH, IR_RAW_HEIGHT, DRM_FORMAT_UYVY);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstConvertHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstConvertHeapBuf++;
    }
    for (i = 0; i < IR_HEAP_BUF_NUM * MPP_IR_BUTT; i++)
    {
        s32Ret = ir_heap_create(pstTmpHeapBuf, IR_RAW_WIDTH, IR_RAW_HEIGHT, DRM_FORMAT_UYVY);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstTmpHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstTmpHeapBuf++;
    }

    /* VO */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstVoHeapBuf, IR_VO_WIDTH, IR_VO_HEIGHT, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstVoHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstVoHeapBuf++;
    }
    /* ALG */
    for (i = 0; i < IR_ALG_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstAlgHeapBuf, IR_ALG_WIDTH, IR_ALG_HEIGHT, DRM_FORMAT_RGB888);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstAlgHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstAlgHeapBuf++;
    }
    /* VENC */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstVencHeapBuf, IR_VENC_WIDTH, IR_VENC_HEIGHT, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstVencHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstVencHeapBuf++;
    }
    /* VENC JPGC */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstVencJpegHeapBuf, IR_VENC_WIDTH, IR_VENC_HEIGHT, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_create fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstVencJpegHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstVencJpegHeapBuf++;
    }
    /* CVBS */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstCvbsVmixHeapBuf, m_stIRInfo.s32ExtscreenWidth, m_stIRInfo.s32ExtscreenHeight, DRM_FORMAT_UYVY);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        //mpp_ir_vmix_FillColor(pstCvbsVmixDrmBuf);
        pstCvbsVmixHeapBuf->enBufStatus = IR_DRM_BUF_WRITE;
        pstCvbsVmixHeapBuf++;
    }

    /* CVBS */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_create(pstRtspVmixHeapBuf, 1280, 720, DRM_FORMAT_NV12);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        //mpp_ir_vmix_FillColor(pstCvbsVmixDrmBuf);
        pstRtspVmixHeapBuf->enBufStatus = IR_HEAP_BUF_WRITE;
        pstRtspVmixHeapBuf++;
    }

    /* 初始化RGA */
    RgaInit(prgactx);
    if(prgactx == NULL)
    {
        print_level(SV_ERROR, "RgaInit fail! \n");
        return SV_FAILURE;
    }
    /* 创建编码通道 */
    s32Ret = mpp_ir_venc_H264CreateChn(IR_VENC_CHN);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_venc_H264CreateChn failed[%x]\n", s32Ret);
    }

    /* 创建编码通道 */
    s32Ret = mpp_ir_venc_JpegCreateChn(IR_VENC_JPEG_CHN);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_venc_JpegCreateChn failed[%x]\n", s32Ret);
    }

    /* 创建编码通道 */
    s32Ret = mpp_ir_venc_rtsp_H264CreateChn(IR_VENC_VMIX_CHN);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_venc_JpegCreateChn failed[%x]\n", s32Ret);
    }
    
    /* 算法sem需单独初始化 */
    s32Ret = mpp_ir_alg_Init();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ir_alg_Init fail! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
    /* 初始化fifo队列 */
    s32Ret = mpp_ir_FIFOInit(&m_stIRInfo.s32QueId,IR_VENC_WIDTH,IR_VENC_HEIGHT, pstIRConfig->u32ViFrmRate, pstIRConfig->u32Bitrate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_FIFOInit failed [%x]\n", s32Ret);
        return s32Ret;
    }

    /* 初始化RTSP fifo队列 */
    s32Ret = mpp_ir_Rtsp_FIFOInit(&m_stIRInfo.s32RtspQueId, IR_VENC_WIDTH, IR_VENC_HEIGHT, 25, pstIRConfig->u32Bitrate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_Rtsp_FIFOInit failed [%x]\n", s32Ret);
        return s32Ret;
    }
    
    /* 获取系统配置，并根据配置辨别是否需要执行Venc线程 */
    s32Ret = CONFIG_GetSystemParam(&stSysParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetSystemParam failed [%x]\n", s32Ret);
    }
    if (stSysParam.bEnableStorage == SV_TRUE)
    {
        if (stSysParam.enAlarmRecord > 0 || stSysParam.enNormalRecord > 0)
        {
            mpp_ir_RecordStart();
        }
        else
        {
            mpp_ir_RecordStop();
        }
    }
    else
    {
        mpp_ir_RecordStop();
    }
    
    m_stIRInfo.s32DevFd = s32DevFd;
    m_stIRInfo.bImageFlip = pstIRConfig->bImageFlip;
    m_stIRInfo.bImageMirror = pstIRConfig->bImageMirror;
    m_stIRInfo.stSrcVideoSize.u32Width = pstIRConfig->stSrcVideoSize.u32Width;
    m_stIRInfo.stSrcVideoSize.u32Height = pstIRConfig->stSrcVideoSize.u32Height;
    m_stIRInfo.eCurIrMode = pstIRConfig->enVoSplitMode;
    m_stIRInfo.eResumeIrMode = pstIRConfig->enVoSplitMode;
    m_stIRInfo.pfStatCallback = pstIRConfig->pfStatCallback;
    m_stIRInfo.pfRgbCallback = pstIRConfig->pfRgbCallback;
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 拉姆达机芯初始化
 * 输入参数: fd：设备文件描述符
             vFormat：用于存储摄像头格式
             vqbuff：用于申请内核空间的结构体
             vbuff：用于申请内存空间的结构体
             mptr：内存映射所用的指针
             bufType：没什么特别的作用的变量
             u32BufNum:创建的缓冲区数目
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 该函数主要是打开视频接口并且分配内核空间和内存空间
 *****************************************************************************/
sint32 mpp_ir_Uvc_Init(    sint32 *fd, struct v4l2_format vFormat, struct v4l2_requestbuffers vqbuff, struct v4l2_buffer *vbuff, unsigned char * mptr[], SV_BOOL* bInitStatus)
{
    
    sint32 s32Ret = -1,ret=-1;
    sint32 s32Cnt = 20;
    enum v4l2_buf_type bufType = V4L2_BUF_TYPE_VIDEO_CAPTURE;
    while(s32Cnt-- > 0)
    {
        *fd=open ("/dev/video25", O_RDWR | O_NONBLOCK);//打开UVC设备，拉姆达机芯设备在linux上的设备文件就是/dev/video25
        if (*fd < 0)
        {
            print_level(SV_ERROR, "open device fail\n");
            sleep_ms(50);
            continue;
        }
        else if (*fd >= 0)
            break;
     }
    //设置摄像头支持的格式
    vFormat.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;       //该宏代表设备是Camera设备
    vFormat.fmt.pix.width   =   IR_RAW_WIDTH;                      //拉姆达机芯的分辨率是256*192，图像格式是YUYV
    vFormat.fmt.pix.height  =   IR_RAW_HEIGHT;
    vFormat.fmt.pix.pixelformat=V4L2_PIX_FMT_YUYV;
    ret=ioctl(*fd, VIDIOC_S_FMT, &vFormat);            //设置当前驱动的视频捕获格式
    if(ret < 0)
    {
        print_level(SV_ERROR, "set fail\n");
        return SV_FAILURE;
    }
    
    //申请内核空间
    vqbuff.type     =   V4L2_BUF_TYPE_VIDEO_CAPTURE;
    vqbuff.count    =   4;                                //缓冲区数目
    vqbuff.memory   =   V4L2_MEMORY_MMAP;                 //代表该内核空间是以内存映射的方式创建的
    ret =ioctl (*fd, VIDIOC_REQBUFS, &vqbuff);
    if(ret < 0)
    {
        print_level(SV_ERROR, "buff fail\n");
    }
    
    //申请内存空间
    for(int i = 0; i < 4; i++)
    {
        memset (vbuff, 0, sizeof (struct v4l2_buffer));
        vbuff->index     = i;        
        vbuff->type      = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        vbuff->memory    = V4L2_MEMORY_MMAP;
        ret=ioctl(*fd, VIDIOC_QUERYBUF, vbuff);
        if(ret < 0)
        {
            print_level(SV_ERROR, "requeire buff fail\n");
        }
        mptr[i]= (unsigned char *)mmap(NULL, vbuff->length, PROT_READ,MAP_SHARED, *fd, vbuff->m.offset);

        if(mptr[i] == MAP_FAILED)
        {
            print_level(SV_ERROR, "mmap failed. i=%d\n", i);
            return SV_FAILURE;
        }
    }

    for(int i = 0;i < 4; i++)
    {
        vbuff->index     = i;        
        vbuff->type      = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        vbuff->memory    = V4L2_MEMORY_MMAP; //缓冲帧放入缓冲队列
        ret=ioctl(*fd, VIDIOC_QBUF, vbuff);
        if(ret<0)
        {
            print_level(SV_ERROR, "put fail");
            return SV_FAILURE;
        }
    }
    
    // 开始采集    
    ret=ioctl(*fd, VIDIOC_STREAMON, &bufType);
    if(ret < 0)
    {
        print_level(SV_ERROR, "open fail");
        return SV_FAILURE;
    }

    *bInitStatus = SV_TRUE;
    
    pthread_mutex_lock(&m_stIRInfo.pStatusChangeLock);
    m_stIRInfo.bStatusChange = SV_FALSE;
    pthread_mutex_unlock(&m_stIRInfo.pStatusChangeLock);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 拉姆达机芯去初始化
 * 输入参数: fd：设备文件描述符
             vFormat：用于存储摄像头格式
             vqbuff：用于申请内核空间的结构体
             vbuff：用于申请内存空间的结构体
             mptr：内存映射所用的指针
             bufType：没什么特别的作用的变量
             u32BufNum:创建的缓冲区数目
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_Uvc_Fnit(    sint32 fd, struct v4l2_buffer *vbuff, unsigned char * mptr[], SV_BOOL* bInitStatus)
{
    sint32 s32Ret = -1,ret=-1;
    enum v4l2_buf_type bufType = V4L2_BUF_TYPE_VIDEO_CAPTURE;

    if (*bInitStatus == SV_FALSE)
    {
        print_level(SV_WARN, "Not need to lamb fnit\r\n");
        return SV_SUCCESS;
    }
/*去初始化代码开始*/
    //解除内存映射
    for (int i = 0; i < 4; i++) 
    {
        if (mptr[i] != MAP_FAILED) 
        {
            if (munmap(mptr[i], vbuff->length) < 0) 
            {
                print_level(SV_ERROR, "munmap failed. i=%d\n", i);
            }
            mptr[i] = MAP_FAILED;
        }
    }
    
    //关闭设备
    if (close(fd) < 0)
    {
        print_level(SV_ERROR, "fd close failed\r\n");
        fd = -1;
    }

    *bInitStatus = SV_FALSE;

    pthread_mutex_lock(&m_stIRInfo.pStatusChangeLock);
    m_stIRInfo.bStatusChange = SV_FALSE;
    pthread_mutex_unlock(&m_stIRInfo.pStatusChangeLock);
    
    return SV_SUCCESS;
/*去初始化代码结束*/
}

/******************************************************************************
 * 函数功能: ir去初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_Fini(void)
{
    sint32 s32Ret = 0, i;
    sint32 s32DevFd = m_stIRInfo.s32DevFd;
    
    IR_DRM_BUF_INFO_S *pstRawDrmBuf = m_stIRInfo.astRawDrmBuf;
    IR_DRM_BUF_INFO_S *pstVoDrmBuf = m_stIRInfo.astVoDrmBuf;
    IR_DRM_BUF_INFO_S *pstAlgDrmBuf = m_stIRInfo.astAlgDrmBuf;
    IR_DRM_BUF_INFO_S *pstVencH264DrmBuf = m_stIRInfo.astVencH264DrmBuf;
    IR_DRM_BUF_INFO_S *pstVencJPEGDrmBuf = m_stIRInfo.astVencJPEGDrmBuf;
    IR_DRM_BUF_INFO_S *pstVmixDrmBuf = m_stIRInfo.astVmixDrmBuf;

    IR_HEAP_BUF_INFO_S *pstRawHeapBuf = m_stIRInfo.astRawHeapBuf;
    IR_HEAP_BUF_INFO_S *pstRawTempHeapBuf = m_stIRInfo.astRawTempHeapBuf;
    IR_HEAP_BUF_INFO_S *pstConvertHeapBuf = m_stIRInfo.astConvertHeapBuf;
    IR_HEAP_BUF_INFO_S *pstTmpHeapBuf = m_stIRInfo.astTmpHeapBuf;
    IR_HEAP_BUF_INFO_S *pstVoHeapBuf = m_stIRInfo.astVoHeapBuf;
    IR_HEAP_BUF_INFO_S *pstAlgHeapBuf = m_stIRInfo.astAlgHeapBuf;
    IR_HEAP_BUF_INFO_S *pstVencHeapBuf = m_stIRInfo.astVencH264HeapBuf;
    IR_HEAP_BUF_INFO_S *pstVencJpegHeapBuf = m_stIRInfo.astVencJpegHeapBuf;
    IR_HEAP_BUF_INFO_S *pstCvbsVmixHeapBuf = m_stIRInfo.astCvbsVmixHeapBuf;
    IR_HEAP_BUF_INFO_S *pstRtspVmixHeapBuf = m_stIRInfo.astRtspVmixHeapBuf;
    /* 释放 DRM*/
    /* RAW */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret =ir_drm_destroy_fb(s32DevFd, pstRawDrmBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstRawDrmBuf, 0x00, sizeof(IR_DRM_BUF_INFO_S));
        pstRawDrmBuf++;
    }
    /* VO */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret =ir_drm_destroy_fb(s32DevFd, pstVoDrmBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstVoDrmBuf, 0x00, sizeof(IR_DRM_BUF_INFO_S));
        pstVoDrmBuf++;
    }
    /* ALG */
    for (i = 0; i < IR_ALG_DRM_BUF_NUM; i++)
    {
        s32Ret =ir_drm_destroy_fb(s32DevFd, pstAlgDrmBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstAlgDrmBuf, 0x00, sizeof(IR_DRM_BUF_INFO_S));
        pstAlgDrmBuf++;
    }
    /* VENC H264 */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret =ir_drm_destroy_fb(s32DevFd, pstVencH264DrmBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstVencH264DrmBuf, 0x00, sizeof(IR_DRM_BUF_INFO_S));
        pstVencH264DrmBuf++;
    }    
    /* VENC JPEG */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret =ir_drm_destroy_fb(s32DevFd, pstVencJPEGDrmBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstVencJPEGDrmBuf, 0x00, sizeof(IR_DRM_BUF_INFO_S));
        pstVencJPEGDrmBuf++;
    }
    /* VMIX */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret =ir_drm_destroy_fb(s32DevFd, pstVmixDrmBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstVmixDrmBuf, 0x00, sizeof(IR_DRM_BUF_INFO_S));
        pstVmixDrmBuf++;
    }

    /* 释放堆 */
    /* RAW */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_destroy(pstRawHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstRawHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstRawHeapBuf++;
        s32Ret = ir_heap_destroy(pstRawTempHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstRawTempHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstRawTempHeapBuf++;        
        free(m_stIRInfo.pas16ParamLine[i]);
    }
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_destroy(pstConvertHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstConvertHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstConvertHeapBuf++;
    }
    for (i = 0; i < IR_HEAP_BUF_NUM * MPP_IR_BUTT; i++)
    {
        s32Ret = ir_heap_destroy(pstTmpHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstTmpHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstTmpHeapBuf++;
    }
    /* VO */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_destroy(pstVoHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstVoHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstVoHeapBuf++;
    }
    /* ALG */
    for (i = 0; i < IR_ALG_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_destroy(pstAlgHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstAlgHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstAlgHeapBuf++;
    }
    /* VENC */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_destroy(pstVencHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstVencHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstVencHeapBuf++;
    }
    /* VENC JPEG*/
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret = ir_heap_destroy(pstVencJpegHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_heap_destroy fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstVencJpegHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstVencJpegHeapBuf++;
    }
    /* CVBS */
    for (i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        s32Ret =ir_heap_destroy(pstCvbsVmixHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstCvbsVmixHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstCvbsVmixHeapBuf++;
    }
    /* CVBS */
    for (i = 0; i < IR_HEAP_BUF_NUM; i++)
    {
        s32Ret =ir_heap_destroy(pstRtspVmixHeapBuf);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstRtspVmixHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
        pstRtspVmixHeapBuf++;
    }

    s32Ret = mpp_ir_FIFOFini(m_stIRInfo.s32QueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_FIFOFini failed [%x]\n",s32Ret);
    }

    s32Ret = mpp_ir_FIFOFini(m_stIRInfo.s32RtspQueId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_FIFOFini failed [%x]\n",s32Ret);
    }
    
    close(s32DevFd);
    m_stIRInfo.s32DevFd = -1;

    return SV_SUCCESS;
}

sint32 mpp_ir_Start(void)
{
    sint32 s32Ret = -1;
    pthread_t pDistributionTid, pUvcTid, pVoTid, pAlgTid, pVencTid, pMonitorTid, pVmixTid, pFillBlackTid, pTempAlarmTid, pCvbsTid;
    m_stIRInfo.bRunning = SV_TRUE;
    /* 数据分发线程 */
    s32Ret = pthread_create(&pDistributionTid, NULL, mpp_ir_Distribution_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Distribution_Body failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pDistributionTid = pDistributionTid;

    /* 拉姆达机芯取图线程 */
    s32Ret = pthread_create(&pUvcTid, NULL, mpp_ir_Uvc_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Uvc_Body failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pUvcTid = pUvcTid;

    /*VO数据处理线程*/
    s32Ret = pthread_create(&pVoTid, NULL, mpp_ir_Vo_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Vo_Body failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.apThreadId[MPP_IR_VO] = pVoTid;
    /* 算法数据处理线程 */
    s32Ret = pthread_create(&pAlgTid, NULL, mpp_ir_Alg_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Alg_Body failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.apThreadId[MPP_IR_ALG] = pAlgTid;

    /* VENC录像编码数据处理线程 */
    s32Ret = pthread_create(&pVencTid, NULL, mpp_ir_Venc_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Venc_Body failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.apThreadId[MPP_IR_VENC] = pVencTid;
    
        
    /* VENC图片编码数据处理线程 */
    s32Ret = pthread_create(&pVencTid, NULL, mpp_ir_Jpeg_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Jpeg_Body failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.apThreadId[MPP_IR_JPEG] = pVencTid;
    /* VMIX线程 */
    s32Ret = pthread_create(&pVmixTid, NULL, mpp_ir_Vmix_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Vmix_Body failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.apThreadId[MPP_IR_VMIX] = pVmixTid;

    /* CVBS线程 */
    s32Ret = pthread_create(&pCvbsTid, NULL, mpp_ir_Cvbs_Body, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_Cvbs_Body failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pCvbsTid = pCvbsTid;
    
    /* 设备监测线程 */
    s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_DevMonitorBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_DevMonitorBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pMonitorTid = pMonitorTid;
    /* 设备黑图替换线程 */
    s32Ret = pthread_create(&pFillBlackTid, NULL, mpp_ir_FillBlackBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_FillBlackBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pFillBlackTid = pFillBlackTid;    

   
    /* VENC录像数据发送编码线程 */
    s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_venc_SendBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_venc_SendBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pVencSendTid = pMonitorTid;
    /* VENC录像接收编码后的数据线程 */
    s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_venc_ReceiveBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_venc_ReceiveBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pVencRecvTid = pMonitorTid;
    
    
    /* JPEG数据发送编码线程 */
    s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_jpeg_SendBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_jpeg_SendBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pJpegSendTid = pMonitorTid;
    /* JPEG编码数据接收线程 */
    s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_jpeg_ReceiveBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_jpeg_ReceiveBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pJpegRecvTid = pMonitorTid;

    /* RTSP流数据发送编码线程 */
    /*s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_venc_rtsp_SendBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_venc_rtsp_SendBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pRtspSendTid = pMonitorTid;*/
    /* RTSP流数据接收编码后的数据线程 */
    /*s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_venc_rtsp_ReceiveBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
     print_level(SV_ERROR, "pthread_create mpp_ir_venc_rtsp_ReceiveBody failed! [err=%d] \n", s32Ret);
     return s32Ret;
    }
    m_stIRInfo.pRtspRecvTid = pMonitorTid;*/

    /* 算法数据发送线程，响应算法取图操作 */
    s32Ret = pthread_create(&pMonitorTid, NULL, mpp_ir_alg_SendBody, &m_stIRInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create mpp_ir_alg_SendBody failed! [err=%d] \n", s32Ret);
        return s32Ret;
    }
    m_stIRInfo.pAlgSendTid = pMonitorTid;
    
    if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
    {
        /* VENC录像编码数据处理线程 */
        s32Ret = pthread_create(&pTempAlarmTid, NULL, mpp_ir_TempAlarmBody, &m_stIRInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create mpp_ir_Alarm_Body failed! [err=%d]\n", s32Ret);
            return s32Ret;
        }
        m_stIRInfo.pTempAlarmTid = pTempAlarmTid;
    }

    return SV_SUCCESS;
}

sint32 mpp_ir_Stop(void)
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;
    m_stIRInfo.bRunning = SV_FALSE;

    s32Ret = pthread_join(m_stIRInfo.pDistributionTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_MainBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = pthread_join(m_stIRInfo.pUvcTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_Uvc failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    s32Ret = pthread_join(m_stIRInfo.apThreadId[MPP_IR_VO], &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_MainBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    s32Ret = pthread_join(m_stIRInfo.apThreadId[MPP_IR_ALG], &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_MainBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    s32Ret = pthread_join(m_stIRInfo.apThreadId[MPP_IR_VENC], &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_MainBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    s32Ret = pthread_join(m_stIRInfo.apThreadId[MPP_IR_JPEG], &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_MainBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    s32Ret = pthread_join(m_stIRInfo.apThreadId[MPP_IR_VMIX], &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_VedioServerBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = pthread_join(m_stIRInfo.pCvbsTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_Cvbs_body failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }


    s32Ret = pthread_join(m_stIRInfo.pVencSendTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_VedioServerBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    s32Ret = pthread_join(m_stIRInfo.pVencRecvTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_VedioServerBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }    
    
    
    s32Ret = pthread_join(m_stIRInfo.pJpegSendTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_VedioServerBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }        
    s32Ret = pthread_join(m_stIRInfo.pJpegRecvTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_VedioServerBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }   
    s32Ret = pthread_join(m_stIRInfo.pAlgSendTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_VedioServerBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }      
    s32Ret = pthread_join(m_stIRInfo.pMonitorTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_DevMonitorBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    s32Ret = pthread_join(m_stIRInfo.pFillBlackTid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread mpp_ir_FillBlackBody failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }    
    
    if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
    {
        /* VENC录像编码数据处理线程 */
        s32Ret = pthread_join(m_stIRInfo.pTempAlarmTid, &pvRetval);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create mpp_ir_Alarm_Body failed! [err=%d]\n", s32Ret);
            return s32Ret;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取DRM虚拟地址，显示绑定drm需要
 * 输入参数: s32Num -- 对应块 
 * 输出参数: ppvBuf -- 数据指针
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_GetDrmAddr(sint32 s32Num, void **ppvBuf)
{
    void *pbmp = NULL;

    if(ppvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    if(s32Num >= IR_DRM_BUF_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);

    if(NULL == m_stIRInfo.astVmixDrmBuf[s32Num].virAddr)
    {
        pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
        return ERR_NULL_PTR;
    }
    pbmp = m_stIRInfo.astVmixDrmBuf[s32Num].virAddr;
    *ppvBuf = pbmp;
    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 根据虚拟地址获取FD文件描述符
 * 输入参数: pvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : DRMbuffer fd
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vmix_GetFD(void *pvBuf)
{
    int i = 0;
    sint32 s32DrmFd = 0;
    
    if(pvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    for(i = 0; i < IR_DRM_BUF_NUM; i++)
    {
        if(m_stIRInfo.astVmixDrmBuf[i].virAddr == pvBuf)
        {
            s32DrmFd = m_stIRInfo.astVmixDrmBuf[i].s32Fd;
            break;
        }
    }
    return s32DrmFd;
}

/******************************************************************************
 * 函数功能: 获取算法buffer Fd
 * 输入参数: s32Idx -- buffer通道索引
 * 输出参数: ps32Fd -- fd
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_alg_GetFD(sint32 *ps32Fd, sint32 s32Idx)
{
    sint32 s32DrmFd = 0;
    
    if(ps32Fd == NULL)
    {
        return ERR_NULL_PTR;
    }

    s32DrmFd = m_stIRInfo.astAlgDrmBuf[s32Idx].s32Fd;
    *ps32Fd = s32DrmFd;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置镜像和翻转
 * 输入参数: bMirror -- 镜像 bFlip -- 翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_SetChnMirrorFlip(SV_BOOL bMirror, SV_BOOL bFlip)
{
    if(((SV_FALSE != bMirror) && (SV_TRUE != bMirror))\
        ||((SV_FALSE != bMirror) && (SV_TRUE != bMirror)))
    {
        return ERR_ILLEGAL_PARAM;
    }
        
    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    m_stIRInfo.bImageMirror = bMirror;
    m_stIRInfo.bImageFlip = bFlip;
    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置红外显示模式
 * 输入参数: eIrMode -- 红外模式
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_SetMode(SPLIT_MODE eIrMode)
{
    if ((MEDIA_SPLIT_ONE != eIrMode) && (MEDIA_SPLIT_TWO != eIrMode)
        && (MEDIA_SPLIT_IR_ONE != eIrMode)  && (MEDIA_SPLIT_IR_OSD != eIrMode)
        && (MEDIA_SPLIT_IR_FIVE_1 != eIrMode) &&(MEDIA_SPLIT_IR_FIVE_2!= eIrMode)
        && (MEDIA_SPLIT_IR_OSD_2 != eIrMode) && (MEDIA_SPLIT_IR_CIRCULATE != eIrMode))
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    pthread_mutex_lock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    m_stIRInfo.eCurIrMode = eIrMode;
    m_stIRInfo.eResumeIrMode = eIrMode;
    pthread_mutex_unlock(&m_stIRInfo.apMutexLock[MPP_IR_VMIX]);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取红外显示模式
 * 输入参数: 无
 * 输出参数: peIrMode --红外模式
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_GetMode(SPLIT_MODE *peIrMode)
{
    if(NULL == peIrMode)
    {
        return ERR_NULL_PTR;
    }
    *peIrMode = m_stIRInfo.eCurIrMode;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取红外显示模式
 * 输入参数: pstMediaFifoAttr -- fifo info
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_SetFifoAttr(MEDIA_FIFO_ATTR      *pstMediaFifoAttr)
{
    sint32 s32Ret = 0;
    SFIFO_MEDIA_ATTR stSfifoMediaAttr = {0};
    if (NULL == pstMediaFifoAttr)
    {
        return ERR_NULL_PTR;
    }
    
    stSfifoMediaAttr.stMainStreamAttr.bValid = SV_TRUE;
    stSfifoMediaAttr.stMainStreamAttr.u32Width = IR_VENC_WIDTH;
    stSfifoMediaAttr.stMainStreamAttr.u32Height = IR_VENC_HEIGHT;
    stSfifoMediaAttr.stMainStreamAttr.u32FrameRate = pstMediaFifoAttr->stPriVencAttr.u32ViFrmRate;
    stSfifoMediaAttr.stMainStreamAttr.u32Bitrate = pstMediaFifoAttr->stPriVencAttr.u32Bitrate;
    s32Ret = SFIFO_SetMediaAttr(m_stIRInfo.s32QueId, &stSfifoMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_SetMediaAttr failed.\n");
        return SV_FAILURE;
    }  
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: osd获取红外温度信息
 * 输入参数: 无
 * 输出参数: pstTempInfo -- 温度信息
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_vosd_GetTempInfo(MEDIA_TMEP_S *pstTempInfo)
{
    if (NULL == pstTempInfo)
    {
        return ERR_NULL_PTR;
    }
    pstTempInfo->bVaild = m_stIRInfo.stTempInfo.bVaild;
    pstTempInfo->fTempMax = m_stIRInfo.stTempInfo.fTempMax;
    pstTempInfo->fTempMin= m_stIRInfo.stTempInfo.fTempMin;
    pstTempInfo->fTempAvg = m_stIRInfo.stTempInfo.fTempAvg;
    pstTempInfo->s32AlarmLv = m_stIRInfo.stTempInfo.s32AlarmLv;
    pstTempInfo->fTempMaxPosX = m_stIRInfo.stTempInfo.fTempMaxPosX;
    pstTempInfo->fTempMaxPosY = m_stIRInfo.stTempInfo.fTempMaxPosY;
    pstTempInfo->fTempMinPosX = m_stIRInfo.stTempInfo.fTempMinPosX;
    pstTempInfo->fTempMinPosY = m_stIRInfo.stTempInfo.fTempMinPosY;    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新设置CVBS的缓冲区的宽和高
 * 输入参数: s32Width：宽
             s32Height： 高
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_ExtscreenParamReset(sint32 s32Width, sint32 s32Height)
{
    IR_HEAP_BUF_INFO_S *pstCvbsVmixHeapBuf = m_stIRInfo.astCvbsVmixHeapBuf;
    sint32 s32Ret = 0, i = 0;
    if(m_stIRInfo.s32ExtscreenHeight != s32Height)
    {
        m_stIRInfo.s32ExtscreenHeight = s32Height;
        pthread_mutex_lock(&m_stIRInfo.pIrCvbsLock);
        for (i = 0; i < IR_DRM_BUF_NUM; i++)
        {
            s32Ret =ir_heap_destroy(pstCvbsVmixHeapBuf);
            if (s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "ir_drm_destroy_fb fail! [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
            memset(pstCvbsVmixHeapBuf, 0x00, sizeof(IR_HEAP_BUF_INFO_S));
            print_level(SV_ERROR, " mpp_ir_ExtscreenParamReset [%d,%d]\n",   m_stIRInfo.s32ExtscreenWidth, m_stIRInfo.s32ExtscreenHeight);
            s32Ret = ir_heap_create(pstCvbsVmixHeapBuf, m_stIRInfo.s32ExtscreenWidth, s32Height, DRM_FORMAT_UYVY);
            if (s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "ir_drm_create_fb fail! [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
            //mpp_ir_vmix_FillColor(pstCvbsVmixDrmBuf);
            pstCvbsVmixHeapBuf->enBufStatus = IR_DRM_BUF_WRITE;
            pstCvbsVmixHeapBuf++;
        }
        pthread_mutex_unlock(&m_stIRInfo.pIrCvbsLock);
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 通知IR模块CVBS功能已停止，需改变全局变量停止向拓展屏线程（即extscreen.c中的线程）传入数据
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_CvbsStop()
{
    m_stIRInfo.bExtscreenExist = SV_FALSE;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 通知IR模块CVBS功能已停止，需改变全局变量停止向拓展屏线程（即extscreen.c中的线程）传入数据
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_CvbsStart()
{
    m_stIRInfo.bExtscreenExist = SV_TRUE;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 通知IR模块录像功能已开启，需改变全局变量使得Venc线程继续执行
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/

sint32 mpp_ir_RecordStart()
{
    m_stIRInfo.bRecordStatus = SV_TRUE;
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 通知IR模块录像功能已停止，需改变全局变量使得Venc线程暂停
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_ir_RecordStop()
{
    m_stIRInfo.bRecordStatus = SV_FALSE;
    return SV_SUCCESS;
}


