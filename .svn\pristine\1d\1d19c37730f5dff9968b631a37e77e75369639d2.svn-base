/* soapServer.c
   Generated by gSOAP 2.8.55 for onvif.h

gSOAP XML Web services tools
Copyright (C) 2000-2017, <PERSON>, Genivia Inc. All Rights Reserved.
The soapcpp2 tool and its generated software are released under the GPL.
This program is released under the GPL with the additional exemption that
compiling, linking, and/or using OpenSSL is allowed.
--------------------------------------------------------------------------------
A commercial use license is available from Genivia Inc., <EMAIL>
--------------------------------------------------------------------------------
*/

#if defined(__BORLANDC__)
#pragma option push -w-8060
#pragma option push -w-8004
#endif
#include "soapH.h"
#ifdef __cplusplus
extern "C" {
#endif

SOAP_SOURCE_STAMP("@(#) soapServer.c ver 2.8.55 2023-05-24 02:35:22 GMT")
SOAP_FMAC5 int SOAP_FMAC6 soap_serve(struct soap *soap)
{
#ifndef WITH_FASTCGI
	soap->keep_alive = soap->max_keep_alive + 1;
#endif
	do
	{
#ifndef WITH_FASTCGI
		if (soap->keep_alive > 0 && soap->max_keep_alive > 0)
			soap->keep_alive--;
#endif
		if (soap_begin_serve(soap))
		{	if (soap->error >= SOAP_STOP)
				continue;
			return soap->error;
		}
		if ((soap_serve_request(soap) || (soap->fserveloop && soap->fserveloop(soap))) && soap->error && soap->error < SOAP_STOP)
		{
#ifdef WITH_FASTCGI
			soap_send_fault(soap);
#else
			return soap_send_fault(soap);
#endif
		}
#ifdef WITH_FASTCGI
		soap_destroy(soap);
		soap_end(soap);
	} while (1);
#else
	} while (soap->keep_alive);
#endif
	return SOAP_OK;
}

#ifndef WITH_NOSERVEREQUEST
SOAP_FMAC5 int SOAP_FMAC6 soap_serve_request(struct soap *soap)
{
	soap_peek_element(soap);
	if (!soap_match_tag(soap, soap->tag, "SOAP-ENV:Fault"))
		return soap_serve_SOAP_ENV__Fault(soap);
	if (!soap_match_tag(soap, soap->tag, "d:Hello"))
		return soap_serve___dn__Hello(soap);
	if (!soap_match_tag(soap, soap->tag, "d:Bye"))
		return soap_serve___dn__Bye(soap);
	if (!soap_match_tag(soap, soap->tag, "d:Probe"))
		return soap_serve___dn__Probe(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetServices"))
		return soap_serve___tds__GetServices(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetServiceCapabilities"))
		return soap_serve___tds__GetServiceCapabilities(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetDeviceInformation"))
		return soap_serve___tds__GetDeviceInformation(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetSystemDateAndTime"))
		return soap_serve___tds__SetSystemDateAndTime(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetSystemDateAndTime"))
		return soap_serve___tds__GetSystemDateAndTime(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetSystemFactoryDefault"))
		return soap_serve___tds__SetSystemFactoryDefault(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:UpgradeSystemFirmware"))
		return soap_serve___tds__UpgradeSystemFirmware(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SystemReboot"))
		return soap_serve___tds__SystemReboot(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:RestoreSystem"))
		return soap_serve___tds__RestoreSystem(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetSystemBackup"))
		return soap_serve___tds__GetSystemBackup(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetSystemLog"))
		return soap_serve___tds__GetSystemLog(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetSystemSupportInformation"))
		return soap_serve___tds__GetSystemSupportInformation(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetScopes"))
		return soap_serve___tds__GetScopes(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetScopes"))
		return soap_serve___tds__SetScopes(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:AddScopes"))
		return soap_serve___tds__AddScopes(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:RemoveScopes"))
		return soap_serve___tds__RemoveScopes(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetDiscoveryMode"))
		return soap_serve___tds__GetDiscoveryMode(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetDiscoveryMode"))
		return soap_serve___tds__SetDiscoveryMode(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetUsers"))
		return soap_serve___tds__GetUsers(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:CreateUsers"))
		return soap_serve___tds__CreateUsers(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:DeleteUsers"))
		return soap_serve___tds__DeleteUsers(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetUser"))
		return soap_serve___tds__SetUser(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetCapabilities"))
		return soap_serve___tds__GetCapabilities(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetHostname"))
		return soap_serve___tds__GetHostname(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetHostname"))
		return soap_serve___tds__SetHostname(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetHostnameFromDHCP"))
		return soap_serve___tds__SetHostnameFromDHCP(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetDNS"))
		return soap_serve___tds__GetDNS(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetDNS"))
		return soap_serve___tds__SetDNS(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetNTP"))
		return soap_serve___tds__GetNTP(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetNTP"))
		return soap_serve___tds__SetNTP(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetDynamicDNS"))
		return soap_serve___tds__GetDynamicDNS(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetDynamicDNS"))
		return soap_serve___tds__SetDynamicDNS(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetNetworkInterfaces"))
		return soap_serve___tds__GetNetworkInterfaces(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetNetworkInterfaces"))
		return soap_serve___tds__SetNetworkInterfaces(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetNetworkProtocols"))
		return soap_serve___tds__GetNetworkProtocols(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetNetworkProtocols"))
		return soap_serve___tds__SetNetworkProtocols(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetNetworkDefaultGateway"))
		return soap_serve___tds__GetNetworkDefaultGateway(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetNetworkDefaultGateway"))
		return soap_serve___tds__SetNetworkDefaultGateway(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetZeroConfiguration"))
		return soap_serve___tds__GetZeroConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetZeroConfiguration"))
		return soap_serve___tds__SetZeroConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetAccessPolicy"))
		return soap_serve___tds__GetAccessPolicy(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:SetAccessPolicy"))
		return soap_serve___tds__SetAccessPolicy(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:GetSystemUris"))
		return soap_serve___tds__GetSystemUris(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:StartFirmwareUpgrade"))
		return soap_serve___tds__StartFirmwareUpgrade(soap);
	if (!soap_match_tag(soap, soap->tag, "tds:StartSystemRestore"))
		return soap_serve___tds__StartSystemRestore(soap);
	if (!soap_match_tag(soap, soap->tag, "tev:PullMessages"))
		return soap_serve___tev__PullMessages(soap);
	if (!soap_match_tag(soap, soap->tag, "tev:SetSynchronizationPoint"))
		return soap_serve___tev__SetSynchronizationPoint(soap);
	if (!soap_match_tag(soap, soap->tag, "tev:GetServiceCapabilities"))
		return soap_serve___tev__GetServiceCapabilities(soap);
	if (!soap_match_tag(soap, soap->tag, "tev:CreatePullPointSubscription"))
		return soap_serve___tev__CreatePullPointSubscription(soap);
	if (!soap_match_tag(soap, soap->tag, "tev:GetEventProperties"))
		return soap_serve___tev__GetEventProperties(soap);
	if (!soap_match_tag(soap, soap->tag, "wsnt:Renew"))
		return soap_serve___tev__Renew(soap);
	if (!soap_match_tag(soap, soap->tag, "wsnt:Unsubscribe"))
		return soap_serve___tev__Unsubscribe(soap);
	if (!soap_match_tag(soap, soap->tag, "wsnt:Subscribe"))
		return soap_serve___tev__Subscribe(soap);
	if (!soap_match_tag(soap, soap->tag, "wsnt:Notify"))
		return soap_serve___tev__Notify(soap);
	if (!soap_match_tag(soap, soap->tag, "wsnt:Notify"))
		return soap_serve___tev__Notify_(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:GetServiceCapabilities"))
		return soap_serve___timg__GetServiceCapabilities(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:GetImagingSettings"))
		return soap_serve___timg__GetImagingSettings(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:SetImagingSettings"))
		return soap_serve___timg__SetImagingSettings(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:GetOptions"))
		return soap_serve___timg__GetOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:Move"))
		return soap_serve___timg__Move(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:Stop"))
		return soap_serve___timg__Stop(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:GetStatus"))
		return soap_serve___timg__GetStatus(soap);
	if (!soap_match_tag(soap, soap->tag, "timg:GetMoveOptions"))
		return soap_serve___timg__GetMoveOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetServiceCapabilities"))
		return soap_serve___tptz__GetServiceCapabilities(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetConfigurations"))
		return soap_serve___tptz__GetConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetPresets"))
		return soap_serve___tptz__GetPresets(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:SetPreset"))
		return soap_serve___tptz__SetPreset(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:RemovePreset"))
		return soap_serve___tptz__RemovePreset(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GotoPreset"))
		return soap_serve___tptz__GotoPreset(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetStatus"))
		return soap_serve___tptz__GetStatus(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetConfiguration"))
		return soap_serve___tptz__GetConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetNodes"))
		return soap_serve___tptz__GetNodes(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetNode"))
		return soap_serve___tptz__GetNode(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:SetConfiguration"))
		return soap_serve___tptz__SetConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GetConfigurationOptions"))
		return soap_serve___tptz__GetConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:GotoHomePosition"))
		return soap_serve___tptz__GotoHomePosition(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:SetHomePosition"))
		return soap_serve___tptz__SetHomePosition(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:ContinuousMove"))
		return soap_serve___tptz__ContinuousMove(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:RelativeMove"))
		return soap_serve___tptz__RelativeMove(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:SendAuxiliaryCommand"))
		return soap_serve___tptz__SendAuxiliaryCommand(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:AbsoluteMove"))
		return soap_serve___tptz__AbsoluteMove(soap);
	if (!soap_match_tag(soap, soap->tag, "tptz:Stop"))
		return soap_serve___tptz__Stop(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetServiceCapabilities"))
		return soap_serve___tr2__GetServiceCapabilities(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:CreateProfile"))
		return soap_serve___tr2__CreateProfile(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetProfiles"))
		return soap_serve___tr2__GetProfiles(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:AddConfiguration"))
		return soap_serve___tr2__AddConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:RemoveConfiguration"))
		return soap_serve___tr2__RemoveConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:DeleteProfile"))
		return soap_serve___tr2__DeleteProfile(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetVideoSourceConfigurations"))
		return soap_serve___tr2__GetVideoSourceConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetVideoEncoderConfigurations"))
		return soap_serve___tr2__GetVideoEncoderConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetAudioSourceConfigurations"))
		return soap_serve___tr2__GetAudioSourceConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetAudioEncoderConfigurations"))
		return soap_serve___tr2__GetAudioEncoderConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetMetadataConfigurations"))
		return soap_serve___tr2__GetMetadataConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetVideoSourceConfiguration"))
		return soap_serve___tr2__SetVideoSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetVideoEncoderConfiguration"))
		return soap_serve___tr2__SetVideoEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetAudioSourceConfiguration"))
		return soap_serve___tr2__SetAudioSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetAudioEncoderConfiguration"))
		return soap_serve___tr2__SetAudioEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetMetadataConfiguration"))
		return soap_serve___tr2__SetMetadataConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetVideoSourceConfigurationOptions"))
		return soap_serve___tr2__GetVideoSourceConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetVideoEncoderConfigurationOptions"))
		return soap_serve___tr2__GetVideoEncoderConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetAudioSourceConfigurationOptions"))
		return soap_serve___tr2__GetAudioSourceConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetAudioEncoderConfigurationOptions"))
		return soap_serve___tr2__GetAudioEncoderConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetMetadataConfigurationOptions"))
		return soap_serve___tr2__GetMetadataConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetVideoEncoderInstances"))
		return soap_serve___tr2__GetVideoEncoderInstances(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetStreamUri"))
		return soap_serve___tr2__GetStreamUri(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:StartMulticastStreaming"))
		return soap_serve___tr2__StartMulticastStreaming(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:StopMulticastStreaming"))
		return soap_serve___tr2__StopMulticastStreaming(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetSynchronizationPoint"))
		return soap_serve___tr2__SetSynchronizationPoint(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetSnapshotUri"))
		return soap_serve___tr2__GetSnapshotUri(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetVideoSourceModes"))
		return soap_serve___tr2__GetVideoSourceModes(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetVideoSourceMode"))
		return soap_serve___tr2__SetVideoSourceMode(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetOSDs"))
		return soap_serve___tr2__GetOSDs(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetOSDOptions"))
		return soap_serve___tr2__GetOSDOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetOSD"))
		return soap_serve___tr2__SetOSD(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:CreateOSD"))
		return soap_serve___tr2__CreateOSD(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:DeleteOSD"))
		return soap_serve___tr2__DeleteOSD(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetMasks"))
		return soap_serve___tr2__GetMasks(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:GetMaskOptions"))
		return soap_serve___tr2__GetMaskOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:SetMask"))
		return soap_serve___tr2__SetMask(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:CreateMask"))
		return soap_serve___tr2__CreateMask(soap);
	if (!soap_match_tag(soap, soap->tag, "tr2:DeleteMask"))
		return soap_serve___tr2__DeleteMask(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetServiceCapabilities"))
		return soap_serve___trt__GetServiceCapabilities(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetVideoSources"))
		return soap_serve___trt__GetVideoSources(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetAudioSources"))
		return soap_serve___trt__GetAudioSources(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:CreateProfile"))
		return soap_serve___trt__CreateProfile(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetProfile"))
		return soap_serve___trt__GetProfile(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetProfiles"))
		return soap_serve___trt__GetProfiles(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:AddVideoEncoderConfiguration"))
		return soap_serve___trt__AddVideoEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:AddVideoSourceConfiguration"))
		return soap_serve___trt__AddVideoSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:AddAudioEncoderConfiguration"))
		return soap_serve___trt__AddAudioEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:AddAudioSourceConfiguration"))
		return soap_serve___trt__AddAudioSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:AddPTZConfiguration"))
		return soap_serve___trt__AddPTZConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:AddMetadataConfiguration"))
		return soap_serve___trt__AddMetadataConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:RemoveVideoEncoderConfiguration"))
		return soap_serve___trt__RemoveVideoEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:RemoveVideoSourceConfiguration"))
		return soap_serve___trt__RemoveVideoSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:RemoveAudioEncoderConfiguration"))
		return soap_serve___trt__RemoveAudioEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:RemoveAudioSourceConfiguration"))
		return soap_serve___trt__RemoveAudioSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:RemovePTZConfiguration"))
		return soap_serve___trt__RemovePTZConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:RemoveMetadataConfiguration"))
		return soap_serve___trt__RemoveMetadataConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:DeleteProfile"))
		return soap_serve___trt__DeleteProfile(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetVideoSourceConfigurations"))
		return soap_serve___trt__GetVideoSourceConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetVideoEncoderConfigurations"))
		return soap_serve___trt__GetVideoEncoderConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetAudioSourceConfigurations"))
		return soap_serve___trt__GetAudioSourceConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetAudioEncoderConfigurations"))
		return soap_serve___trt__GetAudioEncoderConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetMetadataConfigurations"))
		return soap_serve___trt__GetMetadataConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetVideoSourceConfiguration"))
		return soap_serve___trt__GetVideoSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetVideoEncoderConfiguration"))
		return soap_serve___trt__GetVideoEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetAudioSourceConfiguration"))
		return soap_serve___trt__GetAudioSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetAudioEncoderConfiguration"))
		return soap_serve___trt__GetAudioEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetMetadataConfiguration"))
		return soap_serve___trt__GetMetadataConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetCompatibleVideoEncoderConfigurations"))
		return soap_serve___trt__GetCompatibleVideoEncoderConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetCompatibleVideoSourceConfigurations"))
		return soap_serve___trt__GetCompatibleVideoSourceConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetCompatibleAudioEncoderConfigurations"))
		return soap_serve___trt__GetCompatibleAudioEncoderConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetCompatibleAudioSourceConfigurations"))
		return soap_serve___trt__GetCompatibleAudioSourceConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetCompatibleMetadataConfigurations"))
		return soap_serve___trt__GetCompatibleMetadataConfigurations(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:SetVideoSourceConfiguration"))
		return soap_serve___trt__SetVideoSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:SetVideoEncoderConfiguration"))
		return soap_serve___trt__SetVideoEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:SetAudioSourceConfiguration"))
		return soap_serve___trt__SetAudioSourceConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:SetAudioEncoderConfiguration"))
		return soap_serve___trt__SetAudioEncoderConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:SetMetadataConfiguration"))
		return soap_serve___trt__SetMetadataConfiguration(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetVideoSourceConfigurationOptions"))
		return soap_serve___trt__GetVideoSourceConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetVideoEncoderConfigurationOptions"))
		return soap_serve___trt__GetVideoEncoderConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetAudioSourceConfigurationOptions"))
		return soap_serve___trt__GetAudioSourceConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetAudioEncoderConfigurationOptions"))
		return soap_serve___trt__GetAudioEncoderConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetMetadataConfigurationOptions"))
		return soap_serve___trt__GetMetadataConfigurationOptions(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetGuaranteedNumberOfVideoEncoderInstances"))
		return soap_serve___trt__GetGuaranteedNumberOfVideoEncoderInstances(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetStreamUri"))
		return soap_serve___trt__GetStreamUri(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:StartMulticastStreaming"))
		return soap_serve___trt__StartMulticastStreaming(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:StopMulticastStreaming"))
		return soap_serve___trt__StopMulticastStreaming(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:SetSynchronizationPoint"))
		return soap_serve___trt__SetSynchronizationPoint(soap);
	if (!soap_match_tag(soap, soap->tag, "trt:GetSnapshotUri"))
		return soap_serve___trt__GetSnapshotUri(soap);
	return soap->error = SOAP_NO_METHOD;
}
#endif

SOAP_FMAC5 int SOAP_FMAC6 soap_serve_SOAP_ENV__Fault(struct soap *soap)
{	struct SOAP_ENV__Fault soap_tmp_SOAP_ENV__Fault;
	soap_default_SOAP_ENV__Fault(soap, &soap_tmp_SOAP_ENV__Fault);
	if (!soap_get_SOAP_ENV__Fault(soap, &soap_tmp_SOAP_ENV__Fault, "SOAP-ENV:Fault", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = SOAP_ENV__Fault(soap, soap_tmp_SOAP_ENV__Fault.faultcode, soap_tmp_SOAP_ENV__Fault.faultstring, soap_tmp_SOAP_ENV__Fault.faultactor, soap_tmp_SOAP_ENV__Fault.detail, soap_tmp_SOAP_ENV__Fault.SOAP_ENV__Code, soap_tmp_SOAP_ENV__Fault.SOAP_ENV__Reason, soap_tmp_SOAP_ENV__Fault.SOAP_ENV__Node, soap_tmp_SOAP_ENV__Fault.SOAP_ENV__Role, soap_tmp_SOAP_ENV__Fault.SOAP_ENV__Detail);
	if (soap->error)
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___dn__Hello(struct soap *soap)
{	struct __dn__Hello soap_tmp___dn__Hello;
	struct d__ResolveType dn__HelloResponse;
	soap_default_d__ResolveType(soap, &dn__HelloResponse);
	soap_default___dn__Hello(soap, &soap_tmp___dn__Hello);
	if (!soap_get___dn__Hello(soap, &soap_tmp___dn__Hello, "-dn:Hello", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __dn__Hello(soap, soap_tmp___dn__Hello.d__Hello, &dn__HelloResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_d__ResolveType(soap, &dn__HelloResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_d__ResolveType(soap, &dn__HelloResponse, "dn:HelloResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_d__ResolveType(soap, &dn__HelloResponse, "dn:HelloResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___dn__Bye(struct soap *soap)
{	struct __dn__Bye soap_tmp___dn__Bye;
	struct d__ResolveType dn__ByeResponse;
	soap_default_d__ResolveType(soap, &dn__ByeResponse);
	soap_default___dn__Bye(soap, &soap_tmp___dn__Bye);
	if (!soap_get___dn__Bye(soap, &soap_tmp___dn__Bye, "-dn:Bye", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __dn__Bye(soap, soap_tmp___dn__Bye.d__Bye, &dn__ByeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_d__ResolveType(soap, &dn__ByeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_d__ResolveType(soap, &dn__ByeResponse, "dn:ByeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_d__ResolveType(soap, &dn__ByeResponse, "dn:ByeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___dn__Probe(struct soap *soap)
{	struct __dn__Probe soap_tmp___dn__Probe;
	struct d__ProbeMatchesType d__ProbeMatches;
	soap_default_d__ProbeMatchesType(soap, &d__ProbeMatches);
	soap_default___dn__Probe(soap, &soap_tmp___dn__Probe);
	if (!soap_get___dn__Probe(soap, &soap_tmp___dn__Probe, "-dn:Probe", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __dn__Probe(soap, soap_tmp___dn__Probe.d__Probe, &d__ProbeMatches);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_d__ProbeMatchesType(soap, &d__ProbeMatches);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_d__ProbeMatchesType(soap, &d__ProbeMatches, "d:ProbeMatches", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_d__ProbeMatchesType(soap, &d__ProbeMatches, "d:ProbeMatches", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetServices(struct soap *soap)
{	struct __tds__GetServices soap_tmp___tds__GetServices;
	struct _tds__GetServicesResponse tds__GetServicesResponse;
	soap_default__tds__GetServicesResponse(soap, &tds__GetServicesResponse);
	soap_default___tds__GetServices(soap, &soap_tmp___tds__GetServices);
	if (!soap_get___tds__GetServices(soap, &soap_tmp___tds__GetServices, "-tds:GetServices", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetServices(soap, soap_tmp___tds__GetServices.tds__GetServices, &tds__GetServicesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetServicesResponse(soap, &tds__GetServicesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetServicesResponse(soap, &tds__GetServicesResponse, "tds:GetServicesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetServicesResponse(soap, &tds__GetServicesResponse, "tds:GetServicesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetServiceCapabilities(struct soap *soap)
{	struct __tds__GetServiceCapabilities soap_tmp___tds__GetServiceCapabilities;
	struct _tds__GetServiceCapabilitiesResponse tds__GetServiceCapabilitiesResponse;
	soap_default__tds__GetServiceCapabilitiesResponse(soap, &tds__GetServiceCapabilitiesResponse);
	soap_default___tds__GetServiceCapabilities(soap, &soap_tmp___tds__GetServiceCapabilities);
	if (!soap_get___tds__GetServiceCapabilities(soap, &soap_tmp___tds__GetServiceCapabilities, "-tds:GetServiceCapabilities", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetServiceCapabilities(soap, soap_tmp___tds__GetServiceCapabilities.tds__GetServiceCapabilities, &tds__GetServiceCapabilitiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetServiceCapabilitiesResponse(soap, &tds__GetServiceCapabilitiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetServiceCapabilitiesResponse(soap, &tds__GetServiceCapabilitiesResponse, "tds:GetServiceCapabilitiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetServiceCapabilitiesResponse(soap, &tds__GetServiceCapabilitiesResponse, "tds:GetServiceCapabilitiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetDeviceInformation(struct soap *soap)
{	struct __tds__GetDeviceInformation soap_tmp___tds__GetDeviceInformation;
	struct _tds__GetDeviceInformationResponse tds__GetDeviceInformationResponse;
	soap_default__tds__GetDeviceInformationResponse(soap, &tds__GetDeviceInformationResponse);
	soap_default___tds__GetDeviceInformation(soap, &soap_tmp___tds__GetDeviceInformation);
	if (!soap_get___tds__GetDeviceInformation(soap, &soap_tmp___tds__GetDeviceInformation, "-tds:GetDeviceInformation", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetDeviceInformation(soap, soap_tmp___tds__GetDeviceInformation.tds__GetDeviceInformation, &tds__GetDeviceInformationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetDeviceInformationResponse(soap, &tds__GetDeviceInformationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetDeviceInformationResponse(soap, &tds__GetDeviceInformationResponse, "tds:GetDeviceInformationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetDeviceInformationResponse(soap, &tds__GetDeviceInformationResponse, "tds:GetDeviceInformationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetSystemDateAndTime(struct soap *soap)
{	struct __tds__SetSystemDateAndTime soap_tmp___tds__SetSystemDateAndTime;
	struct _tds__SetSystemDateAndTimeResponse tds__SetSystemDateAndTimeResponse;
	soap_default__tds__SetSystemDateAndTimeResponse(soap, &tds__SetSystemDateAndTimeResponse);
	soap_default___tds__SetSystemDateAndTime(soap, &soap_tmp___tds__SetSystemDateAndTime);
	if (!soap_get___tds__SetSystemDateAndTime(soap, &soap_tmp___tds__SetSystemDateAndTime, "-tds:SetSystemDateAndTime", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetSystemDateAndTime(soap, soap_tmp___tds__SetSystemDateAndTime.tds__SetSystemDateAndTime, &tds__SetSystemDateAndTimeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetSystemDateAndTimeResponse(soap, &tds__SetSystemDateAndTimeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetSystemDateAndTimeResponse(soap, &tds__SetSystemDateAndTimeResponse, "tds:SetSystemDateAndTimeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetSystemDateAndTimeResponse(soap, &tds__SetSystemDateAndTimeResponse, "tds:SetSystemDateAndTimeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetSystemDateAndTime(struct soap *soap)
{	struct __tds__GetSystemDateAndTime soap_tmp___tds__GetSystemDateAndTime;
	struct _tds__GetSystemDateAndTimeResponse tds__GetSystemDateAndTimeResponse;
	soap_default__tds__GetSystemDateAndTimeResponse(soap, &tds__GetSystemDateAndTimeResponse);
	soap_default___tds__GetSystemDateAndTime(soap, &soap_tmp___tds__GetSystemDateAndTime);
	if (!soap_get___tds__GetSystemDateAndTime(soap, &soap_tmp___tds__GetSystemDateAndTime, "-tds:GetSystemDateAndTime", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetSystemDateAndTime(soap, soap_tmp___tds__GetSystemDateAndTime.tds__GetSystemDateAndTime, &tds__GetSystemDateAndTimeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetSystemDateAndTimeResponse(soap, &tds__GetSystemDateAndTimeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetSystemDateAndTimeResponse(soap, &tds__GetSystemDateAndTimeResponse, "tds:GetSystemDateAndTimeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetSystemDateAndTimeResponse(soap, &tds__GetSystemDateAndTimeResponse, "tds:GetSystemDateAndTimeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetSystemFactoryDefault(struct soap *soap)
{	struct __tds__SetSystemFactoryDefault soap_tmp___tds__SetSystemFactoryDefault;
	struct _tds__SetSystemFactoryDefaultResponse tds__SetSystemFactoryDefaultResponse;
	soap_default__tds__SetSystemFactoryDefaultResponse(soap, &tds__SetSystemFactoryDefaultResponse);
	soap_default___tds__SetSystemFactoryDefault(soap, &soap_tmp___tds__SetSystemFactoryDefault);
	if (!soap_get___tds__SetSystemFactoryDefault(soap, &soap_tmp___tds__SetSystemFactoryDefault, "-tds:SetSystemFactoryDefault", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetSystemFactoryDefault(soap, soap_tmp___tds__SetSystemFactoryDefault.tds__SetSystemFactoryDefault, &tds__SetSystemFactoryDefaultResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetSystemFactoryDefaultResponse(soap, &tds__SetSystemFactoryDefaultResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetSystemFactoryDefaultResponse(soap, &tds__SetSystemFactoryDefaultResponse, "tds:SetSystemFactoryDefaultResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetSystemFactoryDefaultResponse(soap, &tds__SetSystemFactoryDefaultResponse, "tds:SetSystemFactoryDefaultResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__UpgradeSystemFirmware(struct soap *soap)
{	struct __tds__UpgradeSystemFirmware soap_tmp___tds__UpgradeSystemFirmware;
	struct _tds__UpgradeSystemFirmwareResponse tds__UpgradeSystemFirmwareResponse;
	soap_default__tds__UpgradeSystemFirmwareResponse(soap, &tds__UpgradeSystemFirmwareResponse);
	soap_default___tds__UpgradeSystemFirmware(soap, &soap_tmp___tds__UpgradeSystemFirmware);
	if (!soap_get___tds__UpgradeSystemFirmware(soap, &soap_tmp___tds__UpgradeSystemFirmware, "-tds:UpgradeSystemFirmware", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__UpgradeSystemFirmware(soap, soap_tmp___tds__UpgradeSystemFirmware.tds__UpgradeSystemFirmware, &tds__UpgradeSystemFirmwareResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__UpgradeSystemFirmwareResponse(soap, &tds__UpgradeSystemFirmwareResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__UpgradeSystemFirmwareResponse(soap, &tds__UpgradeSystemFirmwareResponse, "tds:UpgradeSystemFirmwareResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__UpgradeSystemFirmwareResponse(soap, &tds__UpgradeSystemFirmwareResponse, "tds:UpgradeSystemFirmwareResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SystemReboot(struct soap *soap)
{	struct __tds__SystemReboot soap_tmp___tds__SystemReboot;
	struct _tds__SystemRebootResponse tds__SystemRebootResponse;
	soap_default__tds__SystemRebootResponse(soap, &tds__SystemRebootResponse);
	soap_default___tds__SystemReboot(soap, &soap_tmp___tds__SystemReboot);
	if (!soap_get___tds__SystemReboot(soap, &soap_tmp___tds__SystemReboot, "-tds:SystemReboot", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SystemReboot(soap, soap_tmp___tds__SystemReboot.tds__SystemReboot, &tds__SystemRebootResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SystemRebootResponse(soap, &tds__SystemRebootResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SystemRebootResponse(soap, &tds__SystemRebootResponse, "tds:SystemRebootResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SystemRebootResponse(soap, &tds__SystemRebootResponse, "tds:SystemRebootResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__RestoreSystem(struct soap *soap)
{	struct __tds__RestoreSystem soap_tmp___tds__RestoreSystem;
	struct _tds__RestoreSystemResponse tds__RestoreSystemResponse;
	soap_default__tds__RestoreSystemResponse(soap, &tds__RestoreSystemResponse);
	soap_default___tds__RestoreSystem(soap, &soap_tmp___tds__RestoreSystem);
	if (!soap_get___tds__RestoreSystem(soap, &soap_tmp___tds__RestoreSystem, "-tds:RestoreSystem", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__RestoreSystem(soap, soap_tmp___tds__RestoreSystem.tds__RestoreSystem, &tds__RestoreSystemResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__RestoreSystemResponse(soap, &tds__RestoreSystemResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__RestoreSystemResponse(soap, &tds__RestoreSystemResponse, "tds:RestoreSystemResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__RestoreSystemResponse(soap, &tds__RestoreSystemResponse, "tds:RestoreSystemResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetSystemBackup(struct soap *soap)
{	struct __tds__GetSystemBackup soap_tmp___tds__GetSystemBackup;
	struct _tds__GetSystemBackupResponse tds__GetSystemBackupResponse;
	soap_default__tds__GetSystemBackupResponse(soap, &tds__GetSystemBackupResponse);
	soap_default___tds__GetSystemBackup(soap, &soap_tmp___tds__GetSystemBackup);
	if (!soap_get___tds__GetSystemBackup(soap, &soap_tmp___tds__GetSystemBackup, "-tds:GetSystemBackup", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetSystemBackup(soap, soap_tmp___tds__GetSystemBackup.tds__GetSystemBackup, &tds__GetSystemBackupResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetSystemBackupResponse(soap, &tds__GetSystemBackupResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetSystemBackupResponse(soap, &tds__GetSystemBackupResponse, "tds:GetSystemBackupResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetSystemBackupResponse(soap, &tds__GetSystemBackupResponse, "tds:GetSystemBackupResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetSystemLog(struct soap *soap)
{	struct __tds__GetSystemLog soap_tmp___tds__GetSystemLog;
	struct _tds__GetSystemLogResponse tds__GetSystemLogResponse;
	soap_default__tds__GetSystemLogResponse(soap, &tds__GetSystemLogResponse);
	soap_default___tds__GetSystemLog(soap, &soap_tmp___tds__GetSystemLog);
	if (!soap_get___tds__GetSystemLog(soap, &soap_tmp___tds__GetSystemLog, "-tds:GetSystemLog", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetSystemLog(soap, soap_tmp___tds__GetSystemLog.tds__GetSystemLog, &tds__GetSystemLogResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetSystemLogResponse(soap, &tds__GetSystemLogResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetSystemLogResponse(soap, &tds__GetSystemLogResponse, "tds:GetSystemLogResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetSystemLogResponse(soap, &tds__GetSystemLogResponse, "tds:GetSystemLogResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetSystemSupportInformation(struct soap *soap)
{	struct __tds__GetSystemSupportInformation soap_tmp___tds__GetSystemSupportInformation;
	struct _tds__GetSystemSupportInformationResponse tds__GetSystemSupportInformationResponse;
	soap_default__tds__GetSystemSupportInformationResponse(soap, &tds__GetSystemSupportInformationResponse);
	soap_default___tds__GetSystemSupportInformation(soap, &soap_tmp___tds__GetSystemSupportInformation);
	if (!soap_get___tds__GetSystemSupportInformation(soap, &soap_tmp___tds__GetSystemSupportInformation, "-tds:GetSystemSupportInformation", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetSystemSupportInformation(soap, soap_tmp___tds__GetSystemSupportInformation.tds__GetSystemSupportInformation, &tds__GetSystemSupportInformationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetSystemSupportInformationResponse(soap, &tds__GetSystemSupportInformationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetSystemSupportInformationResponse(soap, &tds__GetSystemSupportInformationResponse, "tds:GetSystemSupportInformationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetSystemSupportInformationResponse(soap, &tds__GetSystemSupportInformationResponse, "tds:GetSystemSupportInformationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetScopes(struct soap *soap)
{	struct __tds__GetScopes soap_tmp___tds__GetScopes;
	struct _tds__GetScopesResponse tds__GetScopesResponse;
	soap_default__tds__GetScopesResponse(soap, &tds__GetScopesResponse);
	soap_default___tds__GetScopes(soap, &soap_tmp___tds__GetScopes);
	if (!soap_get___tds__GetScopes(soap, &soap_tmp___tds__GetScopes, "-tds:GetScopes", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetScopes(soap, soap_tmp___tds__GetScopes.tds__GetScopes, &tds__GetScopesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetScopesResponse(soap, &tds__GetScopesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetScopesResponse(soap, &tds__GetScopesResponse, "tds:GetScopesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetScopesResponse(soap, &tds__GetScopesResponse, "tds:GetScopesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetScopes(struct soap *soap)
{	struct __tds__SetScopes soap_tmp___tds__SetScopes;
	struct _tds__SetScopesResponse tds__SetScopesResponse;
	soap_default__tds__SetScopesResponse(soap, &tds__SetScopesResponse);
	soap_default___tds__SetScopes(soap, &soap_tmp___tds__SetScopes);
	if (!soap_get___tds__SetScopes(soap, &soap_tmp___tds__SetScopes, "-tds:SetScopes", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetScopes(soap, soap_tmp___tds__SetScopes.tds__SetScopes, &tds__SetScopesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetScopesResponse(soap, &tds__SetScopesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetScopesResponse(soap, &tds__SetScopesResponse, "tds:SetScopesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetScopesResponse(soap, &tds__SetScopesResponse, "tds:SetScopesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__AddScopes(struct soap *soap)
{	struct __tds__AddScopes soap_tmp___tds__AddScopes;
	struct _tds__AddScopesResponse tds__AddScopesResponse;
	soap_default__tds__AddScopesResponse(soap, &tds__AddScopesResponse);
	soap_default___tds__AddScopes(soap, &soap_tmp___tds__AddScopes);
	if (!soap_get___tds__AddScopes(soap, &soap_tmp___tds__AddScopes, "-tds:AddScopes", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__AddScopes(soap, soap_tmp___tds__AddScopes.tds__AddScopes, &tds__AddScopesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__AddScopesResponse(soap, &tds__AddScopesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__AddScopesResponse(soap, &tds__AddScopesResponse, "tds:AddScopesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__AddScopesResponse(soap, &tds__AddScopesResponse, "tds:AddScopesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__RemoveScopes(struct soap *soap)
{	struct __tds__RemoveScopes soap_tmp___tds__RemoveScopes;
	struct _tds__RemoveScopesResponse tds__RemoveScopesResponse;
	soap_default__tds__RemoveScopesResponse(soap, &tds__RemoveScopesResponse);
	soap_default___tds__RemoveScopes(soap, &soap_tmp___tds__RemoveScopes);
	if (!soap_get___tds__RemoveScopes(soap, &soap_tmp___tds__RemoveScopes, "-tds:RemoveScopes", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__RemoveScopes(soap, soap_tmp___tds__RemoveScopes.tds__RemoveScopes, &tds__RemoveScopesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__RemoveScopesResponse(soap, &tds__RemoveScopesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__RemoveScopesResponse(soap, &tds__RemoveScopesResponse, "tds:RemoveScopesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__RemoveScopesResponse(soap, &tds__RemoveScopesResponse, "tds:RemoveScopesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetDiscoveryMode(struct soap *soap)
{	struct __tds__GetDiscoveryMode soap_tmp___tds__GetDiscoveryMode;
	struct _tds__GetDiscoveryModeResponse tds__GetDiscoveryModeResponse;
	soap_default__tds__GetDiscoveryModeResponse(soap, &tds__GetDiscoveryModeResponse);
	soap_default___tds__GetDiscoveryMode(soap, &soap_tmp___tds__GetDiscoveryMode);
	if (!soap_get___tds__GetDiscoveryMode(soap, &soap_tmp___tds__GetDiscoveryMode, "-tds:GetDiscoveryMode", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetDiscoveryMode(soap, soap_tmp___tds__GetDiscoveryMode.tds__GetDiscoveryMode, &tds__GetDiscoveryModeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetDiscoveryModeResponse(soap, &tds__GetDiscoveryModeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetDiscoveryModeResponse(soap, &tds__GetDiscoveryModeResponse, "tds:GetDiscoveryModeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetDiscoveryModeResponse(soap, &tds__GetDiscoveryModeResponse, "tds:GetDiscoveryModeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetDiscoveryMode(struct soap *soap)
{	struct __tds__SetDiscoveryMode soap_tmp___tds__SetDiscoveryMode;
	struct _tds__SetDiscoveryModeResponse tds__SetDiscoveryModeResponse;
	soap_default__tds__SetDiscoveryModeResponse(soap, &tds__SetDiscoveryModeResponse);
	soap_default___tds__SetDiscoveryMode(soap, &soap_tmp___tds__SetDiscoveryMode);
	if (!soap_get___tds__SetDiscoveryMode(soap, &soap_tmp___tds__SetDiscoveryMode, "-tds:SetDiscoveryMode", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetDiscoveryMode(soap, soap_tmp___tds__SetDiscoveryMode.tds__SetDiscoveryMode, &tds__SetDiscoveryModeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetDiscoveryModeResponse(soap, &tds__SetDiscoveryModeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetDiscoveryModeResponse(soap, &tds__SetDiscoveryModeResponse, "tds:SetDiscoveryModeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetDiscoveryModeResponse(soap, &tds__SetDiscoveryModeResponse, "tds:SetDiscoveryModeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetUsers(struct soap *soap)
{	struct __tds__GetUsers soap_tmp___tds__GetUsers;
	struct _tds__GetUsersResponse tds__GetUsersResponse;
	soap_default__tds__GetUsersResponse(soap, &tds__GetUsersResponse);
	soap_default___tds__GetUsers(soap, &soap_tmp___tds__GetUsers);
	if (!soap_get___tds__GetUsers(soap, &soap_tmp___tds__GetUsers, "-tds:GetUsers", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetUsers(soap, soap_tmp___tds__GetUsers.tds__GetUsers, &tds__GetUsersResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetUsersResponse(soap, &tds__GetUsersResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetUsersResponse(soap, &tds__GetUsersResponse, "tds:GetUsersResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetUsersResponse(soap, &tds__GetUsersResponse, "tds:GetUsersResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__CreateUsers(struct soap *soap)
{	struct __tds__CreateUsers soap_tmp___tds__CreateUsers;
	struct _tds__CreateUsersResponse tds__CreateUsersResponse;
	soap_default__tds__CreateUsersResponse(soap, &tds__CreateUsersResponse);
	soap_default___tds__CreateUsers(soap, &soap_tmp___tds__CreateUsers);
	if (!soap_get___tds__CreateUsers(soap, &soap_tmp___tds__CreateUsers, "-tds:CreateUsers", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__CreateUsers(soap, soap_tmp___tds__CreateUsers.tds__CreateUsers, &tds__CreateUsersResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__CreateUsersResponse(soap, &tds__CreateUsersResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__CreateUsersResponse(soap, &tds__CreateUsersResponse, "tds:CreateUsersResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__CreateUsersResponse(soap, &tds__CreateUsersResponse, "tds:CreateUsersResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__DeleteUsers(struct soap *soap)
{	struct __tds__DeleteUsers soap_tmp___tds__DeleteUsers;
	struct _tds__DeleteUsersResponse tds__DeleteUsersResponse;
	soap_default__tds__DeleteUsersResponse(soap, &tds__DeleteUsersResponse);
	soap_default___tds__DeleteUsers(soap, &soap_tmp___tds__DeleteUsers);
	if (!soap_get___tds__DeleteUsers(soap, &soap_tmp___tds__DeleteUsers, "-tds:DeleteUsers", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__DeleteUsers(soap, soap_tmp___tds__DeleteUsers.tds__DeleteUsers, &tds__DeleteUsersResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__DeleteUsersResponse(soap, &tds__DeleteUsersResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__DeleteUsersResponse(soap, &tds__DeleteUsersResponse, "tds:DeleteUsersResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__DeleteUsersResponse(soap, &tds__DeleteUsersResponse, "tds:DeleteUsersResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetUser(struct soap *soap)
{	struct __tds__SetUser soap_tmp___tds__SetUser;
	struct _tds__SetUserResponse tds__SetUserResponse;
	soap_default__tds__SetUserResponse(soap, &tds__SetUserResponse);
	soap_default___tds__SetUser(soap, &soap_tmp___tds__SetUser);
	if (!soap_get___tds__SetUser(soap, &soap_tmp___tds__SetUser, "-tds:SetUser", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetUser(soap, soap_tmp___tds__SetUser.tds__SetUser, &tds__SetUserResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetUserResponse(soap, &tds__SetUserResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetUserResponse(soap, &tds__SetUserResponse, "tds:SetUserResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetUserResponse(soap, &tds__SetUserResponse, "tds:SetUserResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetCapabilities(struct soap *soap)
{	struct __tds__GetCapabilities soap_tmp___tds__GetCapabilities;
	struct _tds__GetCapabilitiesResponse tds__GetCapabilitiesResponse;
	soap_default__tds__GetCapabilitiesResponse(soap, &tds__GetCapabilitiesResponse);
	soap_default___tds__GetCapabilities(soap, &soap_tmp___tds__GetCapabilities);
	if (!soap_get___tds__GetCapabilities(soap, &soap_tmp___tds__GetCapabilities, "-tds:GetCapabilities", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetCapabilities(soap, soap_tmp___tds__GetCapabilities.tds__GetCapabilities, &tds__GetCapabilitiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetCapabilitiesResponse(soap, &tds__GetCapabilitiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetCapabilitiesResponse(soap, &tds__GetCapabilitiesResponse, "tds:GetCapabilitiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetCapabilitiesResponse(soap, &tds__GetCapabilitiesResponse, "tds:GetCapabilitiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetHostname(struct soap *soap)
{	struct __tds__GetHostname soap_tmp___tds__GetHostname;
	struct _tds__GetHostnameResponse tds__GetHostnameResponse;
	soap_default__tds__GetHostnameResponse(soap, &tds__GetHostnameResponse);
	soap_default___tds__GetHostname(soap, &soap_tmp___tds__GetHostname);
	if (!soap_get___tds__GetHostname(soap, &soap_tmp___tds__GetHostname, "-tds:GetHostname", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetHostname(soap, soap_tmp___tds__GetHostname.tds__GetHostname, &tds__GetHostnameResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetHostnameResponse(soap, &tds__GetHostnameResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetHostnameResponse(soap, &tds__GetHostnameResponse, "tds:GetHostnameResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetHostnameResponse(soap, &tds__GetHostnameResponse, "tds:GetHostnameResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetHostname(struct soap *soap)
{	struct __tds__SetHostname soap_tmp___tds__SetHostname;
	struct _tds__SetHostnameResponse tds__SetHostnameResponse;
	soap_default__tds__SetHostnameResponse(soap, &tds__SetHostnameResponse);
	soap_default___tds__SetHostname(soap, &soap_tmp___tds__SetHostname);
	if (!soap_get___tds__SetHostname(soap, &soap_tmp___tds__SetHostname, "-tds:SetHostname", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetHostname(soap, soap_tmp___tds__SetHostname.tds__SetHostname, &tds__SetHostnameResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetHostnameResponse(soap, &tds__SetHostnameResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetHostnameResponse(soap, &tds__SetHostnameResponse, "tds:SetHostnameResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetHostnameResponse(soap, &tds__SetHostnameResponse, "tds:SetHostnameResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetHostnameFromDHCP(struct soap *soap)
{	struct __tds__SetHostnameFromDHCP soap_tmp___tds__SetHostnameFromDHCP;
	struct _tds__SetHostnameFromDHCPResponse tds__SetHostnameFromDHCPResponse;
	soap_default__tds__SetHostnameFromDHCPResponse(soap, &tds__SetHostnameFromDHCPResponse);
	soap_default___tds__SetHostnameFromDHCP(soap, &soap_tmp___tds__SetHostnameFromDHCP);
	if (!soap_get___tds__SetHostnameFromDHCP(soap, &soap_tmp___tds__SetHostnameFromDHCP, "-tds:SetHostnameFromDHCP", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetHostnameFromDHCP(soap, soap_tmp___tds__SetHostnameFromDHCP.tds__SetHostnameFromDHCP, &tds__SetHostnameFromDHCPResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetHostnameFromDHCPResponse(soap, &tds__SetHostnameFromDHCPResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetHostnameFromDHCPResponse(soap, &tds__SetHostnameFromDHCPResponse, "tds:SetHostnameFromDHCPResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetHostnameFromDHCPResponse(soap, &tds__SetHostnameFromDHCPResponse, "tds:SetHostnameFromDHCPResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetDNS(struct soap *soap)
{	struct __tds__GetDNS soap_tmp___tds__GetDNS;
	struct _tds__GetDNSResponse tds__GetDNSResponse;
	soap_default__tds__GetDNSResponse(soap, &tds__GetDNSResponse);
	soap_default___tds__GetDNS(soap, &soap_tmp___tds__GetDNS);
	if (!soap_get___tds__GetDNS(soap, &soap_tmp___tds__GetDNS, "-tds:GetDNS", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetDNS(soap, soap_tmp___tds__GetDNS.tds__GetDNS, &tds__GetDNSResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetDNSResponse(soap, &tds__GetDNSResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetDNSResponse(soap, &tds__GetDNSResponse, "tds:GetDNSResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetDNSResponse(soap, &tds__GetDNSResponse, "tds:GetDNSResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetDNS(struct soap *soap)
{	struct __tds__SetDNS soap_tmp___tds__SetDNS;
	struct _tds__SetDNSResponse tds__SetDNSResponse;
	soap_default__tds__SetDNSResponse(soap, &tds__SetDNSResponse);
	soap_default___tds__SetDNS(soap, &soap_tmp___tds__SetDNS);
	if (!soap_get___tds__SetDNS(soap, &soap_tmp___tds__SetDNS, "-tds:SetDNS", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetDNS(soap, soap_tmp___tds__SetDNS.tds__SetDNS, &tds__SetDNSResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetDNSResponse(soap, &tds__SetDNSResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetDNSResponse(soap, &tds__SetDNSResponse, "tds:SetDNSResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetDNSResponse(soap, &tds__SetDNSResponse, "tds:SetDNSResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetNTP(struct soap *soap)
{	struct __tds__GetNTP soap_tmp___tds__GetNTP;
	struct _tds__GetNTPResponse tds__GetNTPResponse;
	soap_default__tds__GetNTPResponse(soap, &tds__GetNTPResponse);
	soap_default___tds__GetNTP(soap, &soap_tmp___tds__GetNTP);
	if (!soap_get___tds__GetNTP(soap, &soap_tmp___tds__GetNTP, "-tds:GetNTP", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetNTP(soap, soap_tmp___tds__GetNTP.tds__GetNTP, &tds__GetNTPResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetNTPResponse(soap, &tds__GetNTPResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetNTPResponse(soap, &tds__GetNTPResponse, "tds:GetNTPResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetNTPResponse(soap, &tds__GetNTPResponse, "tds:GetNTPResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetNTP(struct soap *soap)
{	struct __tds__SetNTP soap_tmp___tds__SetNTP;
	struct _tds__SetNTPResponse tds__SetNTPResponse;
	soap_default__tds__SetNTPResponse(soap, &tds__SetNTPResponse);
	soap_default___tds__SetNTP(soap, &soap_tmp___tds__SetNTP);
	if (!soap_get___tds__SetNTP(soap, &soap_tmp___tds__SetNTP, "-tds:SetNTP", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetNTP(soap, soap_tmp___tds__SetNTP.tds__SetNTP, &tds__SetNTPResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetNTPResponse(soap, &tds__SetNTPResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetNTPResponse(soap, &tds__SetNTPResponse, "tds:SetNTPResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetNTPResponse(soap, &tds__SetNTPResponse, "tds:SetNTPResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetDynamicDNS(struct soap *soap)
{	struct __tds__GetDynamicDNS soap_tmp___tds__GetDynamicDNS;
	struct _tds__GetDynamicDNSResponse tds__GetDynamicDNSResponse;
	soap_default__tds__GetDynamicDNSResponse(soap, &tds__GetDynamicDNSResponse);
	soap_default___tds__GetDynamicDNS(soap, &soap_tmp___tds__GetDynamicDNS);
	if (!soap_get___tds__GetDynamicDNS(soap, &soap_tmp___tds__GetDynamicDNS, "-tds:GetDynamicDNS", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetDynamicDNS(soap, soap_tmp___tds__GetDynamicDNS.tds__GetDynamicDNS, &tds__GetDynamicDNSResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetDynamicDNSResponse(soap, &tds__GetDynamicDNSResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetDynamicDNSResponse(soap, &tds__GetDynamicDNSResponse, "tds:GetDynamicDNSResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetDynamicDNSResponse(soap, &tds__GetDynamicDNSResponse, "tds:GetDynamicDNSResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetDynamicDNS(struct soap *soap)
{	struct __tds__SetDynamicDNS soap_tmp___tds__SetDynamicDNS;
	struct _tds__SetDynamicDNSResponse tds__SetDynamicDNSResponse;
	soap_default__tds__SetDynamicDNSResponse(soap, &tds__SetDynamicDNSResponse);
	soap_default___tds__SetDynamicDNS(soap, &soap_tmp___tds__SetDynamicDNS);
	if (!soap_get___tds__SetDynamicDNS(soap, &soap_tmp___tds__SetDynamicDNS, "-tds:SetDynamicDNS", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetDynamicDNS(soap, soap_tmp___tds__SetDynamicDNS.tds__SetDynamicDNS, &tds__SetDynamicDNSResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetDynamicDNSResponse(soap, &tds__SetDynamicDNSResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetDynamicDNSResponse(soap, &tds__SetDynamicDNSResponse, "tds:SetDynamicDNSResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetDynamicDNSResponse(soap, &tds__SetDynamicDNSResponse, "tds:SetDynamicDNSResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetNetworkInterfaces(struct soap *soap)
{	struct __tds__GetNetworkInterfaces soap_tmp___tds__GetNetworkInterfaces;
	struct _tds__GetNetworkInterfacesResponse tds__GetNetworkInterfacesResponse;
	soap_default__tds__GetNetworkInterfacesResponse(soap, &tds__GetNetworkInterfacesResponse);
	soap_default___tds__GetNetworkInterfaces(soap, &soap_tmp___tds__GetNetworkInterfaces);
	if (!soap_get___tds__GetNetworkInterfaces(soap, &soap_tmp___tds__GetNetworkInterfaces, "-tds:GetNetworkInterfaces", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetNetworkInterfaces(soap, soap_tmp___tds__GetNetworkInterfaces.tds__GetNetworkInterfaces, &tds__GetNetworkInterfacesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetNetworkInterfacesResponse(soap, &tds__GetNetworkInterfacesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetNetworkInterfacesResponse(soap, &tds__GetNetworkInterfacesResponse, "tds:GetNetworkInterfacesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetNetworkInterfacesResponse(soap, &tds__GetNetworkInterfacesResponse, "tds:GetNetworkInterfacesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetNetworkInterfaces(struct soap *soap)
{	struct __tds__SetNetworkInterfaces soap_tmp___tds__SetNetworkInterfaces;
	struct _tds__SetNetworkInterfacesResponse tds__SetNetworkInterfacesResponse;
	soap_default__tds__SetNetworkInterfacesResponse(soap, &tds__SetNetworkInterfacesResponse);
	soap_default___tds__SetNetworkInterfaces(soap, &soap_tmp___tds__SetNetworkInterfaces);
	if (!soap_get___tds__SetNetworkInterfaces(soap, &soap_tmp___tds__SetNetworkInterfaces, "-tds:SetNetworkInterfaces", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetNetworkInterfaces(soap, soap_tmp___tds__SetNetworkInterfaces.tds__SetNetworkInterfaces, &tds__SetNetworkInterfacesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetNetworkInterfacesResponse(soap, &tds__SetNetworkInterfacesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetNetworkInterfacesResponse(soap, &tds__SetNetworkInterfacesResponse, "tds:SetNetworkInterfacesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetNetworkInterfacesResponse(soap, &tds__SetNetworkInterfacesResponse, "tds:SetNetworkInterfacesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetNetworkProtocols(struct soap *soap)
{	struct __tds__GetNetworkProtocols soap_tmp___tds__GetNetworkProtocols;
	struct _tds__GetNetworkProtocolsResponse tds__GetNetworkProtocolsResponse;
	soap_default__tds__GetNetworkProtocolsResponse(soap, &tds__GetNetworkProtocolsResponse);
	soap_default___tds__GetNetworkProtocols(soap, &soap_tmp___tds__GetNetworkProtocols);
	if (!soap_get___tds__GetNetworkProtocols(soap, &soap_tmp___tds__GetNetworkProtocols, "-tds:GetNetworkProtocols", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetNetworkProtocols(soap, soap_tmp___tds__GetNetworkProtocols.tds__GetNetworkProtocols, &tds__GetNetworkProtocolsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetNetworkProtocolsResponse(soap, &tds__GetNetworkProtocolsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetNetworkProtocolsResponse(soap, &tds__GetNetworkProtocolsResponse, "tds:GetNetworkProtocolsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetNetworkProtocolsResponse(soap, &tds__GetNetworkProtocolsResponse, "tds:GetNetworkProtocolsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetNetworkProtocols(struct soap *soap)
{	struct __tds__SetNetworkProtocols soap_tmp___tds__SetNetworkProtocols;
	struct _tds__SetNetworkProtocolsResponse tds__SetNetworkProtocolsResponse;
	soap_default__tds__SetNetworkProtocolsResponse(soap, &tds__SetNetworkProtocolsResponse);
	soap_default___tds__SetNetworkProtocols(soap, &soap_tmp___tds__SetNetworkProtocols);
	if (!soap_get___tds__SetNetworkProtocols(soap, &soap_tmp___tds__SetNetworkProtocols, "-tds:SetNetworkProtocols", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetNetworkProtocols(soap, soap_tmp___tds__SetNetworkProtocols.tds__SetNetworkProtocols, &tds__SetNetworkProtocolsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetNetworkProtocolsResponse(soap, &tds__SetNetworkProtocolsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetNetworkProtocolsResponse(soap, &tds__SetNetworkProtocolsResponse, "tds:SetNetworkProtocolsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetNetworkProtocolsResponse(soap, &tds__SetNetworkProtocolsResponse, "tds:SetNetworkProtocolsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetNetworkDefaultGateway(struct soap *soap)
{	struct __tds__GetNetworkDefaultGateway soap_tmp___tds__GetNetworkDefaultGateway;
	struct _tds__GetNetworkDefaultGatewayResponse tds__GetNetworkDefaultGatewayResponse;
	soap_default__tds__GetNetworkDefaultGatewayResponse(soap, &tds__GetNetworkDefaultGatewayResponse);
	soap_default___tds__GetNetworkDefaultGateway(soap, &soap_tmp___tds__GetNetworkDefaultGateway);
	if (!soap_get___tds__GetNetworkDefaultGateway(soap, &soap_tmp___tds__GetNetworkDefaultGateway, "-tds:GetNetworkDefaultGateway", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetNetworkDefaultGateway(soap, soap_tmp___tds__GetNetworkDefaultGateway.tds__GetNetworkDefaultGateway, &tds__GetNetworkDefaultGatewayResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetNetworkDefaultGatewayResponse(soap, &tds__GetNetworkDefaultGatewayResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetNetworkDefaultGatewayResponse(soap, &tds__GetNetworkDefaultGatewayResponse, "tds:GetNetworkDefaultGatewayResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetNetworkDefaultGatewayResponse(soap, &tds__GetNetworkDefaultGatewayResponse, "tds:GetNetworkDefaultGatewayResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetNetworkDefaultGateway(struct soap *soap)
{	struct __tds__SetNetworkDefaultGateway soap_tmp___tds__SetNetworkDefaultGateway;
	struct _tds__SetNetworkDefaultGatewayResponse tds__SetNetworkDefaultGatewayResponse;
	soap_default__tds__SetNetworkDefaultGatewayResponse(soap, &tds__SetNetworkDefaultGatewayResponse);
	soap_default___tds__SetNetworkDefaultGateway(soap, &soap_tmp___tds__SetNetworkDefaultGateway);
	if (!soap_get___tds__SetNetworkDefaultGateway(soap, &soap_tmp___tds__SetNetworkDefaultGateway, "-tds:SetNetworkDefaultGateway", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetNetworkDefaultGateway(soap, soap_tmp___tds__SetNetworkDefaultGateway.tds__SetNetworkDefaultGateway, &tds__SetNetworkDefaultGatewayResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetNetworkDefaultGatewayResponse(soap, &tds__SetNetworkDefaultGatewayResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetNetworkDefaultGatewayResponse(soap, &tds__SetNetworkDefaultGatewayResponse, "tds:SetNetworkDefaultGatewayResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetNetworkDefaultGatewayResponse(soap, &tds__SetNetworkDefaultGatewayResponse, "tds:SetNetworkDefaultGatewayResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetZeroConfiguration(struct soap *soap)
{	struct __tds__GetZeroConfiguration soap_tmp___tds__GetZeroConfiguration;
	struct _tds__GetZeroConfigurationResponse tds__GetZeroConfigurationResponse;
	soap_default__tds__GetZeroConfigurationResponse(soap, &tds__GetZeroConfigurationResponse);
	soap_default___tds__GetZeroConfiguration(soap, &soap_tmp___tds__GetZeroConfiguration);
	if (!soap_get___tds__GetZeroConfiguration(soap, &soap_tmp___tds__GetZeroConfiguration, "-tds:GetZeroConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetZeroConfiguration(soap, soap_tmp___tds__GetZeroConfiguration.tds__GetZeroConfiguration, &tds__GetZeroConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetZeroConfigurationResponse(soap, &tds__GetZeroConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetZeroConfigurationResponse(soap, &tds__GetZeroConfigurationResponse, "tds:GetZeroConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetZeroConfigurationResponse(soap, &tds__GetZeroConfigurationResponse, "tds:GetZeroConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetZeroConfiguration(struct soap *soap)
{	struct __tds__SetZeroConfiguration soap_tmp___tds__SetZeroConfiguration;
	struct _tds__SetZeroConfigurationResponse tds__SetZeroConfigurationResponse;
	soap_default__tds__SetZeroConfigurationResponse(soap, &tds__SetZeroConfigurationResponse);
	soap_default___tds__SetZeroConfiguration(soap, &soap_tmp___tds__SetZeroConfiguration);
	if (!soap_get___tds__SetZeroConfiguration(soap, &soap_tmp___tds__SetZeroConfiguration, "-tds:SetZeroConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetZeroConfiguration(soap, soap_tmp___tds__SetZeroConfiguration.tds__SetZeroConfiguration, &tds__SetZeroConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetZeroConfigurationResponse(soap, &tds__SetZeroConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetZeroConfigurationResponse(soap, &tds__SetZeroConfigurationResponse, "tds:SetZeroConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetZeroConfigurationResponse(soap, &tds__SetZeroConfigurationResponse, "tds:SetZeroConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetAccessPolicy(struct soap *soap)
{	struct __tds__GetAccessPolicy soap_tmp___tds__GetAccessPolicy;
	struct _tds__GetAccessPolicyResponse tds__GetAccessPolicyResponse;
	soap_default__tds__GetAccessPolicyResponse(soap, &tds__GetAccessPolicyResponse);
	soap_default___tds__GetAccessPolicy(soap, &soap_tmp___tds__GetAccessPolicy);
	if (!soap_get___tds__GetAccessPolicy(soap, &soap_tmp___tds__GetAccessPolicy, "-tds:GetAccessPolicy", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetAccessPolicy(soap, soap_tmp___tds__GetAccessPolicy.tds__GetAccessPolicy, &tds__GetAccessPolicyResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetAccessPolicyResponse(soap, &tds__GetAccessPolicyResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetAccessPolicyResponse(soap, &tds__GetAccessPolicyResponse, "tds:GetAccessPolicyResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetAccessPolicyResponse(soap, &tds__GetAccessPolicyResponse, "tds:GetAccessPolicyResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__SetAccessPolicy(struct soap *soap)
{	struct __tds__SetAccessPolicy soap_tmp___tds__SetAccessPolicy;
	struct _tds__SetAccessPolicyResponse tds__SetAccessPolicyResponse;
	soap_default__tds__SetAccessPolicyResponse(soap, &tds__SetAccessPolicyResponse);
	soap_default___tds__SetAccessPolicy(soap, &soap_tmp___tds__SetAccessPolicy);
	if (!soap_get___tds__SetAccessPolicy(soap, &soap_tmp___tds__SetAccessPolicy, "-tds:SetAccessPolicy", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__SetAccessPolicy(soap, soap_tmp___tds__SetAccessPolicy.tds__SetAccessPolicy, &tds__SetAccessPolicyResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__SetAccessPolicyResponse(soap, &tds__SetAccessPolicyResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__SetAccessPolicyResponse(soap, &tds__SetAccessPolicyResponse, "tds:SetAccessPolicyResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__SetAccessPolicyResponse(soap, &tds__SetAccessPolicyResponse, "tds:SetAccessPolicyResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__GetSystemUris(struct soap *soap)
{	struct __tds__GetSystemUris soap_tmp___tds__GetSystemUris;
	struct _tds__GetSystemUrisResponse tds__GetSystemUrisResponse;
	soap_default__tds__GetSystemUrisResponse(soap, &tds__GetSystemUrisResponse);
	soap_default___tds__GetSystemUris(soap, &soap_tmp___tds__GetSystemUris);
	if (!soap_get___tds__GetSystemUris(soap, &soap_tmp___tds__GetSystemUris, "-tds:GetSystemUris", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__GetSystemUris(soap, soap_tmp___tds__GetSystemUris.tds__GetSystemUris, &tds__GetSystemUrisResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__GetSystemUrisResponse(soap, &tds__GetSystemUrisResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__GetSystemUrisResponse(soap, &tds__GetSystemUrisResponse, "tds:GetSystemUrisResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__GetSystemUrisResponse(soap, &tds__GetSystemUrisResponse, "tds:GetSystemUrisResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__StartFirmwareUpgrade(struct soap *soap)
{	struct __tds__StartFirmwareUpgrade soap_tmp___tds__StartFirmwareUpgrade;
	struct _tds__StartFirmwareUpgradeResponse tds__StartFirmwareUpgradeResponse;
	soap_default__tds__StartFirmwareUpgradeResponse(soap, &tds__StartFirmwareUpgradeResponse);
	soap_default___tds__StartFirmwareUpgrade(soap, &soap_tmp___tds__StartFirmwareUpgrade);
	if (!soap_get___tds__StartFirmwareUpgrade(soap, &soap_tmp___tds__StartFirmwareUpgrade, "-tds:StartFirmwareUpgrade", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__StartFirmwareUpgrade(soap, soap_tmp___tds__StartFirmwareUpgrade.tds__StartFirmwareUpgrade, &tds__StartFirmwareUpgradeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__StartFirmwareUpgradeResponse(soap, &tds__StartFirmwareUpgradeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__StartFirmwareUpgradeResponse(soap, &tds__StartFirmwareUpgradeResponse, "tds:StartFirmwareUpgradeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__StartFirmwareUpgradeResponse(soap, &tds__StartFirmwareUpgradeResponse, "tds:StartFirmwareUpgradeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tds__StartSystemRestore(struct soap *soap)
{	struct __tds__StartSystemRestore soap_tmp___tds__StartSystemRestore;
	struct _tds__StartSystemRestoreResponse tds__StartSystemRestoreResponse;
	soap_default__tds__StartSystemRestoreResponse(soap, &tds__StartSystemRestoreResponse);
	soap_default___tds__StartSystemRestore(soap, &soap_tmp___tds__StartSystemRestore);
	if (!soap_get___tds__StartSystemRestore(soap, &soap_tmp___tds__StartSystemRestore, "-tds:StartSystemRestore", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tds__StartSystemRestore(soap, soap_tmp___tds__StartSystemRestore.tds__StartSystemRestore, &tds__StartSystemRestoreResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tds__StartSystemRestoreResponse(soap, &tds__StartSystemRestoreResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tds__StartSystemRestoreResponse(soap, &tds__StartSystemRestoreResponse, "tds:StartSystemRestoreResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tds__StartSystemRestoreResponse(soap, &tds__StartSystemRestoreResponse, "tds:StartSystemRestoreResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__PullMessages(struct soap *soap)
{	struct __tev__PullMessages soap_tmp___tev__PullMessages;
	struct _tev__PullMessagesResponse tev__PullMessagesResponse;
	soap_default__tev__PullMessagesResponse(soap, &tev__PullMessagesResponse);
	soap_default___tev__PullMessages(soap, &soap_tmp___tev__PullMessages);
	if (!soap_get___tev__PullMessages(soap, &soap_tmp___tev__PullMessages, "-tev:PullMessages", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__PullMessages(soap, soap_tmp___tev__PullMessages.tev__PullMessages, &tev__PullMessagesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tev__PullMessagesResponse(soap, &tev__PullMessagesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tev__PullMessagesResponse(soap, &tev__PullMessagesResponse, "tev:PullMessagesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tev__PullMessagesResponse(soap, &tev__PullMessagesResponse, "tev:PullMessagesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__SetSynchronizationPoint(struct soap *soap)
{	struct __tev__SetSynchronizationPoint soap_tmp___tev__SetSynchronizationPoint;
	struct _tev__SetSynchronizationPointResponse tev__SetSynchronizationPointResponse;
	soap_default__tev__SetSynchronizationPointResponse(soap, &tev__SetSynchronizationPointResponse);
	soap_default___tev__SetSynchronizationPoint(soap, &soap_tmp___tev__SetSynchronizationPoint);
	if (!soap_get___tev__SetSynchronizationPoint(soap, &soap_tmp___tev__SetSynchronizationPoint, "-tev:SetSynchronizationPoint", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__SetSynchronizationPoint(soap, soap_tmp___tev__SetSynchronizationPoint.tev__SetSynchronizationPoint, &tev__SetSynchronizationPointResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tev__SetSynchronizationPointResponse(soap, &tev__SetSynchronizationPointResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tev__SetSynchronizationPointResponse(soap, &tev__SetSynchronizationPointResponse, "tev:SetSynchronizationPointResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tev__SetSynchronizationPointResponse(soap, &tev__SetSynchronizationPointResponse, "tev:SetSynchronizationPointResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__GetServiceCapabilities(struct soap *soap)
{	struct __tev__GetServiceCapabilities soap_tmp___tev__GetServiceCapabilities;
	struct _tev__GetServiceCapabilitiesResponse tev__GetServiceCapabilitiesResponse;
	soap_default__tev__GetServiceCapabilitiesResponse(soap, &tev__GetServiceCapabilitiesResponse);
	soap_default___tev__GetServiceCapabilities(soap, &soap_tmp___tev__GetServiceCapabilities);
	if (!soap_get___tev__GetServiceCapabilities(soap, &soap_tmp___tev__GetServiceCapabilities, "-tev:GetServiceCapabilities", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__GetServiceCapabilities(soap, soap_tmp___tev__GetServiceCapabilities.tev__GetServiceCapabilities, &tev__GetServiceCapabilitiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tev__GetServiceCapabilitiesResponse(soap, &tev__GetServiceCapabilitiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tev__GetServiceCapabilitiesResponse(soap, &tev__GetServiceCapabilitiesResponse, "tev:GetServiceCapabilitiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tev__GetServiceCapabilitiesResponse(soap, &tev__GetServiceCapabilitiesResponse, "tev:GetServiceCapabilitiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__CreatePullPointSubscription(struct soap *soap)
{	struct __tev__CreatePullPointSubscription soap_tmp___tev__CreatePullPointSubscription;
	struct _tev__CreatePullPointSubscriptionResponse tev__CreatePullPointSubscriptionResponse;
	soap_default__tev__CreatePullPointSubscriptionResponse(soap, &tev__CreatePullPointSubscriptionResponse);
	soap_default___tev__CreatePullPointSubscription(soap, &soap_tmp___tev__CreatePullPointSubscription);
	if (!soap_get___tev__CreatePullPointSubscription(soap, &soap_tmp___tev__CreatePullPointSubscription, "-tev:CreatePullPointSubscription", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__CreatePullPointSubscription(soap, soap_tmp___tev__CreatePullPointSubscription.tev__CreatePullPointSubscription, &tev__CreatePullPointSubscriptionResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tev__CreatePullPointSubscriptionResponse(soap, &tev__CreatePullPointSubscriptionResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tev__CreatePullPointSubscriptionResponse(soap, &tev__CreatePullPointSubscriptionResponse, "tev:CreatePullPointSubscriptionResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tev__CreatePullPointSubscriptionResponse(soap, &tev__CreatePullPointSubscriptionResponse, "tev:CreatePullPointSubscriptionResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__GetEventProperties(struct soap *soap)
{	struct __tev__GetEventProperties soap_tmp___tev__GetEventProperties;
	struct _tev__GetEventPropertiesResponse tev__GetEventPropertiesResponse;
	soap_default__tev__GetEventPropertiesResponse(soap, &tev__GetEventPropertiesResponse);
	soap_default___tev__GetEventProperties(soap, &soap_tmp___tev__GetEventProperties);
	if (!soap_get___tev__GetEventProperties(soap, &soap_tmp___tev__GetEventProperties, "-tev:GetEventProperties", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__GetEventProperties(soap, soap_tmp___tev__GetEventProperties.tev__GetEventProperties, &tev__GetEventPropertiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tev__GetEventPropertiesResponse(soap, &tev__GetEventPropertiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tev__GetEventPropertiesResponse(soap, &tev__GetEventPropertiesResponse, "tev:GetEventPropertiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tev__GetEventPropertiesResponse(soap, &tev__GetEventPropertiesResponse, "tev:GetEventPropertiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__Renew(struct soap *soap)
{	struct __tev__Renew soap_tmp___tev__Renew;
	struct _wsnt__RenewResponse wsnt__RenewResponse;
	soap_default__wsnt__RenewResponse(soap, &wsnt__RenewResponse);
	soap_default___tev__Renew(soap, &soap_tmp___tev__Renew);
	if (!soap_get___tev__Renew(soap, &soap_tmp___tev__Renew, "-tev:Renew", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__Renew(soap, soap_tmp___tev__Renew.wsnt__Renew, &wsnt__RenewResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__wsnt__RenewResponse(soap, &wsnt__RenewResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__wsnt__RenewResponse(soap, &wsnt__RenewResponse, "wsnt:RenewResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__wsnt__RenewResponse(soap, &wsnt__RenewResponse, "wsnt:RenewResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__Unsubscribe(struct soap *soap)
{	struct __tev__Unsubscribe soap_tmp___tev__Unsubscribe;
	struct _wsnt__UnsubscribeResponse wsnt__UnsubscribeResponse;
	soap_default__wsnt__UnsubscribeResponse(soap, &wsnt__UnsubscribeResponse);
	soap_default___tev__Unsubscribe(soap, &soap_tmp___tev__Unsubscribe);
	if (!soap_get___tev__Unsubscribe(soap, &soap_tmp___tev__Unsubscribe, "-tev:Unsubscribe", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__Unsubscribe(soap, soap_tmp___tev__Unsubscribe.wsnt__Unsubscribe, &wsnt__UnsubscribeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__wsnt__UnsubscribeResponse(soap, &wsnt__UnsubscribeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__wsnt__UnsubscribeResponse(soap, &wsnt__UnsubscribeResponse, "wsnt:UnsubscribeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__wsnt__UnsubscribeResponse(soap, &wsnt__UnsubscribeResponse, "wsnt:UnsubscribeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__Subscribe(struct soap *soap)
{	struct __tev__Subscribe soap_tmp___tev__Subscribe;
	struct _wsnt__SubscribeResponse wsnt__SubscribeResponse;
	soap_default__wsnt__SubscribeResponse(soap, &wsnt__SubscribeResponse);
	soap_default___tev__Subscribe(soap, &soap_tmp___tev__Subscribe);
	if (!soap_get___tev__Subscribe(soap, &soap_tmp___tev__Subscribe, "-tev:Subscribe", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__Subscribe(soap, soap_tmp___tev__Subscribe.wsnt__Subscribe, &wsnt__SubscribeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__wsnt__SubscribeResponse(soap, &wsnt__SubscribeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__wsnt__SubscribeResponse(soap, &wsnt__SubscribeResponse, "wsnt:SubscribeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__wsnt__SubscribeResponse(soap, &wsnt__SubscribeResponse, "wsnt:SubscribeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__Notify(struct soap *soap)
{	struct __tev__Notify soap_tmp___tev__Notify;
	soap_default___tev__Notify(soap, &soap_tmp___tev__Notify);
	if (!soap_get___tev__Notify(soap, &soap_tmp___tev__Notify, "-tev:Notify", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__Notify(soap, soap_tmp___tev__Notify.wsnt__Notify);
	if (soap->error)
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tev__Notify_(struct soap *soap)
{	struct __tev__Notify_ soap_tmp___tev__Notify_;
	soap_default___tev__Notify_(soap, &soap_tmp___tev__Notify_);
	if (!soap_get___tev__Notify_(soap, &soap_tmp___tev__Notify_, "-tev:Notify", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tev__Notify_(soap, soap_tmp___tev__Notify_.wsnt__Notify);
	if (soap->error)
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__GetServiceCapabilities(struct soap *soap)
{	struct __timg__GetServiceCapabilities soap_tmp___timg__GetServiceCapabilities;
	struct _timg__GetServiceCapabilitiesResponse timg__GetServiceCapabilitiesResponse;
	soap_default__timg__GetServiceCapabilitiesResponse(soap, &timg__GetServiceCapabilitiesResponse);
	soap_default___timg__GetServiceCapabilities(soap, &soap_tmp___timg__GetServiceCapabilities);
	if (!soap_get___timg__GetServiceCapabilities(soap, &soap_tmp___timg__GetServiceCapabilities, "-timg:GetServiceCapabilities", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__GetServiceCapabilities(soap, soap_tmp___timg__GetServiceCapabilities.timg__GetServiceCapabilities, &timg__GetServiceCapabilitiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__GetServiceCapabilitiesResponse(soap, &timg__GetServiceCapabilitiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__GetServiceCapabilitiesResponse(soap, &timg__GetServiceCapabilitiesResponse, "timg:GetServiceCapabilitiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__GetServiceCapabilitiesResponse(soap, &timg__GetServiceCapabilitiesResponse, "timg:GetServiceCapabilitiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__GetImagingSettings(struct soap *soap)
{	struct __timg__GetImagingSettings soap_tmp___timg__GetImagingSettings;
	struct _timg__GetImagingSettingsResponse timg__GetImagingSettingsResponse;
	soap_default__timg__GetImagingSettingsResponse(soap, &timg__GetImagingSettingsResponse);
	soap_default___timg__GetImagingSettings(soap, &soap_tmp___timg__GetImagingSettings);
	if (!soap_get___timg__GetImagingSettings(soap, &soap_tmp___timg__GetImagingSettings, "-timg:GetImagingSettings", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__GetImagingSettings(soap, soap_tmp___timg__GetImagingSettings.timg__GetImagingSettings, &timg__GetImagingSettingsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__GetImagingSettingsResponse(soap, &timg__GetImagingSettingsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__GetImagingSettingsResponse(soap, &timg__GetImagingSettingsResponse, "timg:GetImagingSettingsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__GetImagingSettingsResponse(soap, &timg__GetImagingSettingsResponse, "timg:GetImagingSettingsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__SetImagingSettings(struct soap *soap)
{	struct __timg__SetImagingSettings soap_tmp___timg__SetImagingSettings;
	struct _timg__SetImagingSettingsResponse timg__SetImagingSettingsResponse;
	soap_default__timg__SetImagingSettingsResponse(soap, &timg__SetImagingSettingsResponse);
	soap_default___timg__SetImagingSettings(soap, &soap_tmp___timg__SetImagingSettings);
	if (!soap_get___timg__SetImagingSettings(soap, &soap_tmp___timg__SetImagingSettings, "-timg:SetImagingSettings", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__SetImagingSettings(soap, soap_tmp___timg__SetImagingSettings.timg__SetImagingSettings, &timg__SetImagingSettingsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__SetImagingSettingsResponse(soap, &timg__SetImagingSettingsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__SetImagingSettingsResponse(soap, &timg__SetImagingSettingsResponse, "timg:SetImagingSettingsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__SetImagingSettingsResponse(soap, &timg__SetImagingSettingsResponse, "timg:SetImagingSettingsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__GetOptions(struct soap *soap)
{	struct __timg__GetOptions soap_tmp___timg__GetOptions;
	struct _timg__GetOptionsResponse timg__GetOptionsResponse;
	soap_default__timg__GetOptionsResponse(soap, &timg__GetOptionsResponse);
	soap_default___timg__GetOptions(soap, &soap_tmp___timg__GetOptions);
	if (!soap_get___timg__GetOptions(soap, &soap_tmp___timg__GetOptions, "-timg:GetOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__GetOptions(soap, soap_tmp___timg__GetOptions.timg__GetOptions, &timg__GetOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__GetOptionsResponse(soap, &timg__GetOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__GetOptionsResponse(soap, &timg__GetOptionsResponse, "timg:GetOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__GetOptionsResponse(soap, &timg__GetOptionsResponse, "timg:GetOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__Move(struct soap *soap)
{	struct __timg__Move soap_tmp___timg__Move;
	struct _timg__MoveResponse timg__MoveResponse;
	soap_default__timg__MoveResponse(soap, &timg__MoveResponse);
	soap_default___timg__Move(soap, &soap_tmp___timg__Move);
	if (!soap_get___timg__Move(soap, &soap_tmp___timg__Move, "-timg:Move", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__Move(soap, soap_tmp___timg__Move.timg__Move, &timg__MoveResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__MoveResponse(soap, &timg__MoveResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__MoveResponse(soap, &timg__MoveResponse, "timg:MoveResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__MoveResponse(soap, &timg__MoveResponse, "timg:MoveResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__Stop(struct soap *soap)
{	struct __timg__Stop soap_tmp___timg__Stop;
	struct _timg__StopResponse timg__StopResponse;
	soap_default__timg__StopResponse(soap, &timg__StopResponse);
	soap_default___timg__Stop(soap, &soap_tmp___timg__Stop);
	if (!soap_get___timg__Stop(soap, &soap_tmp___timg__Stop, "-timg:Stop", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__Stop(soap, soap_tmp___timg__Stop.timg__Stop, &timg__StopResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__StopResponse(soap, &timg__StopResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__StopResponse(soap, &timg__StopResponse, "timg:StopResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__StopResponse(soap, &timg__StopResponse, "timg:StopResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__GetStatus(struct soap *soap)
{	struct __timg__GetStatus soap_tmp___timg__GetStatus;
	struct _timg__GetStatusResponse timg__GetStatusResponse;
	soap_default__timg__GetStatusResponse(soap, &timg__GetStatusResponse);
	soap_default___timg__GetStatus(soap, &soap_tmp___timg__GetStatus);
	if (!soap_get___timg__GetStatus(soap, &soap_tmp___timg__GetStatus, "-timg:GetStatus", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__GetStatus(soap, soap_tmp___timg__GetStatus.timg__GetStatus, &timg__GetStatusResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__GetStatusResponse(soap, &timg__GetStatusResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__GetStatusResponse(soap, &timg__GetStatusResponse, "timg:GetStatusResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__GetStatusResponse(soap, &timg__GetStatusResponse, "timg:GetStatusResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___timg__GetMoveOptions(struct soap *soap)
{	struct __timg__GetMoveOptions soap_tmp___timg__GetMoveOptions;
	struct _timg__GetMoveOptionsResponse timg__GetMoveOptionsResponse;
	soap_default__timg__GetMoveOptionsResponse(soap, &timg__GetMoveOptionsResponse);
	soap_default___timg__GetMoveOptions(soap, &soap_tmp___timg__GetMoveOptions);
	if (!soap_get___timg__GetMoveOptions(soap, &soap_tmp___timg__GetMoveOptions, "-timg:GetMoveOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __timg__GetMoveOptions(soap, soap_tmp___timg__GetMoveOptions.timg__GetMoveOptions, &timg__GetMoveOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__timg__GetMoveOptionsResponse(soap, &timg__GetMoveOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__timg__GetMoveOptionsResponse(soap, &timg__GetMoveOptionsResponse, "timg:GetMoveOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__timg__GetMoveOptionsResponse(soap, &timg__GetMoveOptionsResponse, "timg:GetMoveOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetServiceCapabilities(struct soap *soap)
{	struct __tptz__GetServiceCapabilities soap_tmp___tptz__GetServiceCapabilities;
	struct _tptz__GetServiceCapabilitiesResponse tptz__GetServiceCapabilitiesResponse;
	soap_default__tptz__GetServiceCapabilitiesResponse(soap, &tptz__GetServiceCapabilitiesResponse);
	soap_default___tptz__GetServiceCapabilities(soap, &soap_tmp___tptz__GetServiceCapabilities);
	if (!soap_get___tptz__GetServiceCapabilities(soap, &soap_tmp___tptz__GetServiceCapabilities, "-tptz:GetServiceCapabilities", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetServiceCapabilities(soap, soap_tmp___tptz__GetServiceCapabilities.tptz__GetServiceCapabilities, &tptz__GetServiceCapabilitiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetServiceCapabilitiesResponse(soap, &tptz__GetServiceCapabilitiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetServiceCapabilitiesResponse(soap, &tptz__GetServiceCapabilitiesResponse, "tptz:GetServiceCapabilitiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetServiceCapabilitiesResponse(soap, &tptz__GetServiceCapabilitiesResponse, "tptz:GetServiceCapabilitiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetConfigurations(struct soap *soap)
{	struct __tptz__GetConfigurations soap_tmp___tptz__GetConfigurations;
	struct _tptz__GetConfigurationsResponse tptz__GetConfigurationsResponse;
	soap_default__tptz__GetConfigurationsResponse(soap, &tptz__GetConfigurationsResponse);
	soap_default___tptz__GetConfigurations(soap, &soap_tmp___tptz__GetConfigurations);
	if (!soap_get___tptz__GetConfigurations(soap, &soap_tmp___tptz__GetConfigurations, "-tptz:GetConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetConfigurations(soap, soap_tmp___tptz__GetConfigurations.tptz__GetConfigurations, &tptz__GetConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetConfigurationsResponse(soap, &tptz__GetConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetConfigurationsResponse(soap, &tptz__GetConfigurationsResponse, "tptz:GetConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetConfigurationsResponse(soap, &tptz__GetConfigurationsResponse, "tptz:GetConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetPresets(struct soap *soap)
{	struct __tptz__GetPresets soap_tmp___tptz__GetPresets;
	struct _tptz__GetPresetsResponse tptz__GetPresetsResponse;
	soap_default__tptz__GetPresetsResponse(soap, &tptz__GetPresetsResponse);
	soap_default___tptz__GetPresets(soap, &soap_tmp___tptz__GetPresets);
	if (!soap_get___tptz__GetPresets(soap, &soap_tmp___tptz__GetPresets, "-tptz:GetPresets", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetPresets(soap, soap_tmp___tptz__GetPresets.tptz__GetPresets, &tptz__GetPresetsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetPresetsResponse(soap, &tptz__GetPresetsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetPresetsResponse(soap, &tptz__GetPresetsResponse, "tptz:GetPresetsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetPresetsResponse(soap, &tptz__GetPresetsResponse, "tptz:GetPresetsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__SetPreset(struct soap *soap)
{	struct __tptz__SetPreset soap_tmp___tptz__SetPreset;
	struct _tptz__SetPresetResponse tptz__SetPresetResponse;
	soap_default__tptz__SetPresetResponse(soap, &tptz__SetPresetResponse);
	soap_default___tptz__SetPreset(soap, &soap_tmp___tptz__SetPreset);
	if (!soap_get___tptz__SetPreset(soap, &soap_tmp___tptz__SetPreset, "-tptz:SetPreset", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__SetPreset(soap, soap_tmp___tptz__SetPreset.tptz__SetPreset, &tptz__SetPresetResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__SetPresetResponse(soap, &tptz__SetPresetResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__SetPresetResponse(soap, &tptz__SetPresetResponse, "tptz:SetPresetResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__SetPresetResponse(soap, &tptz__SetPresetResponse, "tptz:SetPresetResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__RemovePreset(struct soap *soap)
{	struct __tptz__RemovePreset soap_tmp___tptz__RemovePreset;
	struct _tptz__RemovePresetResponse tptz__RemovePresetResponse;
	soap_default__tptz__RemovePresetResponse(soap, &tptz__RemovePresetResponse);
	soap_default___tptz__RemovePreset(soap, &soap_tmp___tptz__RemovePreset);
	if (!soap_get___tptz__RemovePreset(soap, &soap_tmp___tptz__RemovePreset, "-tptz:RemovePreset", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__RemovePreset(soap, soap_tmp___tptz__RemovePreset.tptz__RemovePreset, &tptz__RemovePresetResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__RemovePresetResponse(soap, &tptz__RemovePresetResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__RemovePresetResponse(soap, &tptz__RemovePresetResponse, "tptz:RemovePresetResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__RemovePresetResponse(soap, &tptz__RemovePresetResponse, "tptz:RemovePresetResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GotoPreset(struct soap *soap)
{	struct __tptz__GotoPreset soap_tmp___tptz__GotoPreset;
	struct _tptz__GotoPresetResponse tptz__GotoPresetResponse;
	soap_default__tptz__GotoPresetResponse(soap, &tptz__GotoPresetResponse);
	soap_default___tptz__GotoPreset(soap, &soap_tmp___tptz__GotoPreset);
	if (!soap_get___tptz__GotoPreset(soap, &soap_tmp___tptz__GotoPreset, "-tptz:GotoPreset", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GotoPreset(soap, soap_tmp___tptz__GotoPreset.tptz__GotoPreset, &tptz__GotoPresetResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GotoPresetResponse(soap, &tptz__GotoPresetResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GotoPresetResponse(soap, &tptz__GotoPresetResponse, "tptz:GotoPresetResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GotoPresetResponse(soap, &tptz__GotoPresetResponse, "tptz:GotoPresetResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetStatus(struct soap *soap)
{	struct __tptz__GetStatus soap_tmp___tptz__GetStatus;
	struct _tptz__GetStatusResponse tptz__GetStatusResponse;
	soap_default__tptz__GetStatusResponse(soap, &tptz__GetStatusResponse);
	soap_default___tptz__GetStatus(soap, &soap_tmp___tptz__GetStatus);
	if (!soap_get___tptz__GetStatus(soap, &soap_tmp___tptz__GetStatus, "-tptz:GetStatus", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetStatus(soap, soap_tmp___tptz__GetStatus.tptz__GetStatus, &tptz__GetStatusResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetStatusResponse(soap, &tptz__GetStatusResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetStatusResponse(soap, &tptz__GetStatusResponse, "tptz:GetStatusResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetStatusResponse(soap, &tptz__GetStatusResponse, "tptz:GetStatusResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetConfiguration(struct soap *soap)
{	struct __tptz__GetConfiguration soap_tmp___tptz__GetConfiguration;
	struct _tptz__GetConfigurationResponse tptz__GetConfigurationResponse;
	soap_default__tptz__GetConfigurationResponse(soap, &tptz__GetConfigurationResponse);
	soap_default___tptz__GetConfiguration(soap, &soap_tmp___tptz__GetConfiguration);
	if (!soap_get___tptz__GetConfiguration(soap, &soap_tmp___tptz__GetConfiguration, "-tptz:GetConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetConfiguration(soap, soap_tmp___tptz__GetConfiguration.tptz__GetConfiguration, &tptz__GetConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetConfigurationResponse(soap, &tptz__GetConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetConfigurationResponse(soap, &tptz__GetConfigurationResponse, "tptz:GetConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetConfigurationResponse(soap, &tptz__GetConfigurationResponse, "tptz:GetConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetNodes(struct soap *soap)
{	struct __tptz__GetNodes soap_tmp___tptz__GetNodes;
	struct _tptz__GetNodesResponse tptz__GetNodesResponse;
	soap_default__tptz__GetNodesResponse(soap, &tptz__GetNodesResponse);
	soap_default___tptz__GetNodes(soap, &soap_tmp___tptz__GetNodes);
	if (!soap_get___tptz__GetNodes(soap, &soap_tmp___tptz__GetNodes, "-tptz:GetNodes", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetNodes(soap, soap_tmp___tptz__GetNodes.tptz__GetNodes, &tptz__GetNodesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetNodesResponse(soap, &tptz__GetNodesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetNodesResponse(soap, &tptz__GetNodesResponse, "tptz:GetNodesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetNodesResponse(soap, &tptz__GetNodesResponse, "tptz:GetNodesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetNode(struct soap *soap)
{	struct __tptz__GetNode soap_tmp___tptz__GetNode;
	struct _tptz__GetNodeResponse tptz__GetNodeResponse;
	soap_default__tptz__GetNodeResponse(soap, &tptz__GetNodeResponse);
	soap_default___tptz__GetNode(soap, &soap_tmp___tptz__GetNode);
	if (!soap_get___tptz__GetNode(soap, &soap_tmp___tptz__GetNode, "-tptz:GetNode", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetNode(soap, soap_tmp___tptz__GetNode.tptz__GetNode, &tptz__GetNodeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetNodeResponse(soap, &tptz__GetNodeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetNodeResponse(soap, &tptz__GetNodeResponse, "tptz:GetNodeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetNodeResponse(soap, &tptz__GetNodeResponse, "tptz:GetNodeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__SetConfiguration(struct soap *soap)
{	struct __tptz__SetConfiguration soap_tmp___tptz__SetConfiguration;
	struct _tptz__SetConfigurationResponse tptz__SetConfigurationResponse;
	soap_default__tptz__SetConfigurationResponse(soap, &tptz__SetConfigurationResponse);
	soap_default___tptz__SetConfiguration(soap, &soap_tmp___tptz__SetConfiguration);
	if (!soap_get___tptz__SetConfiguration(soap, &soap_tmp___tptz__SetConfiguration, "-tptz:SetConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__SetConfiguration(soap, soap_tmp___tptz__SetConfiguration.tptz__SetConfiguration, &tptz__SetConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__SetConfigurationResponse(soap, &tptz__SetConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__SetConfigurationResponse(soap, &tptz__SetConfigurationResponse, "tptz:SetConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__SetConfigurationResponse(soap, &tptz__SetConfigurationResponse, "tptz:SetConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GetConfigurationOptions(struct soap *soap)
{	struct __tptz__GetConfigurationOptions soap_tmp___tptz__GetConfigurationOptions;
	struct _tptz__GetConfigurationOptionsResponse tptz__GetConfigurationOptionsResponse;
	soap_default__tptz__GetConfigurationOptionsResponse(soap, &tptz__GetConfigurationOptionsResponse);
	soap_default___tptz__GetConfigurationOptions(soap, &soap_tmp___tptz__GetConfigurationOptions);
	if (!soap_get___tptz__GetConfigurationOptions(soap, &soap_tmp___tptz__GetConfigurationOptions, "-tptz:GetConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GetConfigurationOptions(soap, soap_tmp___tptz__GetConfigurationOptions.tptz__GetConfigurationOptions, &tptz__GetConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GetConfigurationOptionsResponse(soap, &tptz__GetConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GetConfigurationOptionsResponse(soap, &tptz__GetConfigurationOptionsResponse, "tptz:GetConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GetConfigurationOptionsResponse(soap, &tptz__GetConfigurationOptionsResponse, "tptz:GetConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__GotoHomePosition(struct soap *soap)
{	struct __tptz__GotoHomePosition soap_tmp___tptz__GotoHomePosition;
	struct _tptz__GotoHomePositionResponse tptz__GotoHomePositionResponse;
	soap_default__tptz__GotoHomePositionResponse(soap, &tptz__GotoHomePositionResponse);
	soap_default___tptz__GotoHomePosition(soap, &soap_tmp___tptz__GotoHomePosition);
	if (!soap_get___tptz__GotoHomePosition(soap, &soap_tmp___tptz__GotoHomePosition, "-tptz:GotoHomePosition", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__GotoHomePosition(soap, soap_tmp___tptz__GotoHomePosition.tptz__GotoHomePosition, &tptz__GotoHomePositionResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__GotoHomePositionResponse(soap, &tptz__GotoHomePositionResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__GotoHomePositionResponse(soap, &tptz__GotoHomePositionResponse, "tptz:GotoHomePositionResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__GotoHomePositionResponse(soap, &tptz__GotoHomePositionResponse, "tptz:GotoHomePositionResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__SetHomePosition(struct soap *soap)
{	struct __tptz__SetHomePosition soap_tmp___tptz__SetHomePosition;
	struct _tptz__SetHomePositionResponse tptz__SetHomePositionResponse;
	soap_default__tptz__SetHomePositionResponse(soap, &tptz__SetHomePositionResponse);
	soap_default___tptz__SetHomePosition(soap, &soap_tmp___tptz__SetHomePosition);
	if (!soap_get___tptz__SetHomePosition(soap, &soap_tmp___tptz__SetHomePosition, "-tptz:SetHomePosition", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__SetHomePosition(soap, soap_tmp___tptz__SetHomePosition.tptz__SetHomePosition, &tptz__SetHomePositionResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__SetHomePositionResponse(soap, &tptz__SetHomePositionResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__SetHomePositionResponse(soap, &tptz__SetHomePositionResponse, "tptz:SetHomePositionResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__SetHomePositionResponse(soap, &tptz__SetHomePositionResponse, "tptz:SetHomePositionResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__ContinuousMove(struct soap *soap)
{	struct __tptz__ContinuousMove soap_tmp___tptz__ContinuousMove;
	struct _tptz__ContinuousMoveResponse tptz__ContinuousMoveResponse;
	soap_default__tptz__ContinuousMoveResponse(soap, &tptz__ContinuousMoveResponse);
	soap_default___tptz__ContinuousMove(soap, &soap_tmp___tptz__ContinuousMove);
	if (!soap_get___tptz__ContinuousMove(soap, &soap_tmp___tptz__ContinuousMove, "-tptz:ContinuousMove", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__ContinuousMove(soap, soap_tmp___tptz__ContinuousMove.tptz__ContinuousMove, &tptz__ContinuousMoveResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__ContinuousMoveResponse(soap, &tptz__ContinuousMoveResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__ContinuousMoveResponse(soap, &tptz__ContinuousMoveResponse, "tptz:ContinuousMoveResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__ContinuousMoveResponse(soap, &tptz__ContinuousMoveResponse, "tptz:ContinuousMoveResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__RelativeMove(struct soap *soap)
{	struct __tptz__RelativeMove soap_tmp___tptz__RelativeMove;
	struct _tptz__RelativeMoveResponse tptz__RelativeMoveResponse;
	soap_default__tptz__RelativeMoveResponse(soap, &tptz__RelativeMoveResponse);
	soap_default___tptz__RelativeMove(soap, &soap_tmp___tptz__RelativeMove);
	if (!soap_get___tptz__RelativeMove(soap, &soap_tmp___tptz__RelativeMove, "-tptz:RelativeMove", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__RelativeMove(soap, soap_tmp___tptz__RelativeMove.tptz__RelativeMove, &tptz__RelativeMoveResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__RelativeMoveResponse(soap, &tptz__RelativeMoveResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__RelativeMoveResponse(soap, &tptz__RelativeMoveResponse, "tptz:RelativeMoveResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__RelativeMoveResponse(soap, &tptz__RelativeMoveResponse, "tptz:RelativeMoveResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__SendAuxiliaryCommand(struct soap *soap)
{	struct __tptz__SendAuxiliaryCommand soap_tmp___tptz__SendAuxiliaryCommand;
	struct _tptz__SendAuxiliaryCommandResponse tptz__SendAuxiliaryCommandResponse;
	soap_default__tptz__SendAuxiliaryCommandResponse(soap, &tptz__SendAuxiliaryCommandResponse);
	soap_default___tptz__SendAuxiliaryCommand(soap, &soap_tmp___tptz__SendAuxiliaryCommand);
	if (!soap_get___tptz__SendAuxiliaryCommand(soap, &soap_tmp___tptz__SendAuxiliaryCommand, "-tptz:SendAuxiliaryCommand", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__SendAuxiliaryCommand(soap, soap_tmp___tptz__SendAuxiliaryCommand.tptz__SendAuxiliaryCommand, &tptz__SendAuxiliaryCommandResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__SendAuxiliaryCommandResponse(soap, &tptz__SendAuxiliaryCommandResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__SendAuxiliaryCommandResponse(soap, &tptz__SendAuxiliaryCommandResponse, "tptz:SendAuxiliaryCommandResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__SendAuxiliaryCommandResponse(soap, &tptz__SendAuxiliaryCommandResponse, "tptz:SendAuxiliaryCommandResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__AbsoluteMove(struct soap *soap)
{	struct __tptz__AbsoluteMove soap_tmp___tptz__AbsoluteMove;
	struct _tptz__AbsoluteMoveResponse tptz__AbsoluteMoveResponse;
	soap_default__tptz__AbsoluteMoveResponse(soap, &tptz__AbsoluteMoveResponse);
	soap_default___tptz__AbsoluteMove(soap, &soap_tmp___tptz__AbsoluteMove);
	if (!soap_get___tptz__AbsoluteMove(soap, &soap_tmp___tptz__AbsoluteMove, "-tptz:AbsoluteMove", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__AbsoluteMove(soap, soap_tmp___tptz__AbsoluteMove.tptz__AbsoluteMove, &tptz__AbsoluteMoveResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__AbsoluteMoveResponse(soap, &tptz__AbsoluteMoveResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__AbsoluteMoveResponse(soap, &tptz__AbsoluteMoveResponse, "tptz:AbsoluteMoveResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__AbsoluteMoveResponse(soap, &tptz__AbsoluteMoveResponse, "tptz:AbsoluteMoveResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tptz__Stop(struct soap *soap)
{	struct __tptz__Stop soap_tmp___tptz__Stop;
	struct _tptz__StopResponse tptz__StopResponse;
	soap_default__tptz__StopResponse(soap, &tptz__StopResponse);
	soap_default___tptz__Stop(soap, &soap_tmp___tptz__Stop);
	if (!soap_get___tptz__Stop(soap, &soap_tmp___tptz__Stop, "-tptz:Stop", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tptz__Stop(soap, soap_tmp___tptz__Stop.tptz__Stop, &tptz__StopResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tptz__StopResponse(soap, &tptz__StopResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tptz__StopResponse(soap, &tptz__StopResponse, "tptz:StopResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tptz__StopResponse(soap, &tptz__StopResponse, "tptz:StopResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetServiceCapabilities(struct soap *soap)
{	struct __tr2__GetServiceCapabilities soap_tmp___tr2__GetServiceCapabilities;
	struct _tr2__GetServiceCapabilitiesResponse tr2__GetServiceCapabilitiesResponse;
	soap_default__tr2__GetServiceCapabilitiesResponse(soap, &tr2__GetServiceCapabilitiesResponse);
	soap_default___tr2__GetServiceCapabilities(soap, &soap_tmp___tr2__GetServiceCapabilities);
	if (!soap_get___tr2__GetServiceCapabilities(soap, &soap_tmp___tr2__GetServiceCapabilities, "-tr2:GetServiceCapabilities", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetServiceCapabilities(soap, soap_tmp___tr2__GetServiceCapabilities.tr2__GetServiceCapabilities, &tr2__GetServiceCapabilitiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetServiceCapabilitiesResponse(soap, &tr2__GetServiceCapabilitiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetServiceCapabilitiesResponse(soap, &tr2__GetServiceCapabilitiesResponse, "tr2:GetServiceCapabilitiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetServiceCapabilitiesResponse(soap, &tr2__GetServiceCapabilitiesResponse, "tr2:GetServiceCapabilitiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__CreateProfile(struct soap *soap)
{	struct __tr2__CreateProfile soap_tmp___tr2__CreateProfile;
	struct _tr2__CreateProfileResponse tr2__CreateProfileResponse;
	soap_default__tr2__CreateProfileResponse(soap, &tr2__CreateProfileResponse);
	soap_default___tr2__CreateProfile(soap, &soap_tmp___tr2__CreateProfile);
	if (!soap_get___tr2__CreateProfile(soap, &soap_tmp___tr2__CreateProfile, "-tr2:CreateProfile", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__CreateProfile(soap, soap_tmp___tr2__CreateProfile.tr2__CreateProfile, &tr2__CreateProfileResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__CreateProfileResponse(soap, &tr2__CreateProfileResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__CreateProfileResponse(soap, &tr2__CreateProfileResponse, "tr2:CreateProfileResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__CreateProfileResponse(soap, &tr2__CreateProfileResponse, "tr2:CreateProfileResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetProfiles(struct soap *soap)
{	struct __tr2__GetProfiles soap_tmp___tr2__GetProfiles;
	struct _tr2__GetProfilesResponse tr2__GetProfilesResponse;
	soap_default__tr2__GetProfilesResponse(soap, &tr2__GetProfilesResponse);
	soap_default___tr2__GetProfiles(soap, &soap_tmp___tr2__GetProfiles);
	if (!soap_get___tr2__GetProfiles(soap, &soap_tmp___tr2__GetProfiles, "-tr2:GetProfiles", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetProfiles(soap, soap_tmp___tr2__GetProfiles.tr2__GetProfiles, &tr2__GetProfilesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetProfilesResponse(soap, &tr2__GetProfilesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetProfilesResponse(soap, &tr2__GetProfilesResponse, "tr2:GetProfilesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetProfilesResponse(soap, &tr2__GetProfilesResponse, "tr2:GetProfilesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__AddConfiguration(struct soap *soap)
{	struct __tr2__AddConfiguration soap_tmp___tr2__AddConfiguration;
	struct _tr2__AddConfigurationResponse tr2__AddConfigurationResponse;
	soap_default__tr2__AddConfigurationResponse(soap, &tr2__AddConfigurationResponse);
	soap_default___tr2__AddConfiguration(soap, &soap_tmp___tr2__AddConfiguration);
	if (!soap_get___tr2__AddConfiguration(soap, &soap_tmp___tr2__AddConfiguration, "-tr2:AddConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__AddConfiguration(soap, soap_tmp___tr2__AddConfiguration.tr2__AddConfiguration, &tr2__AddConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__AddConfigurationResponse(soap, &tr2__AddConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__AddConfigurationResponse(soap, &tr2__AddConfigurationResponse, "tr2:AddConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__AddConfigurationResponse(soap, &tr2__AddConfigurationResponse, "tr2:AddConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__RemoveConfiguration(struct soap *soap)
{	struct __tr2__RemoveConfiguration soap_tmp___tr2__RemoveConfiguration;
	struct _tr2__RemoveConfigurationResponse tr2__RemoveConfigurationResponse;
	soap_default__tr2__RemoveConfigurationResponse(soap, &tr2__RemoveConfigurationResponse);
	soap_default___tr2__RemoveConfiguration(soap, &soap_tmp___tr2__RemoveConfiguration);
	if (!soap_get___tr2__RemoveConfiguration(soap, &soap_tmp___tr2__RemoveConfiguration, "-tr2:RemoveConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__RemoveConfiguration(soap, soap_tmp___tr2__RemoveConfiguration.tr2__RemoveConfiguration, &tr2__RemoveConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__RemoveConfigurationResponse(soap, &tr2__RemoveConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__RemoveConfigurationResponse(soap, &tr2__RemoveConfigurationResponse, "tr2:RemoveConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__RemoveConfigurationResponse(soap, &tr2__RemoveConfigurationResponse, "tr2:RemoveConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__DeleteProfile(struct soap *soap)
{	struct __tr2__DeleteProfile soap_tmp___tr2__DeleteProfile;
	struct _tr2__DeleteProfileResponse tr2__DeleteProfileResponse;
	soap_default__tr2__DeleteProfileResponse(soap, &tr2__DeleteProfileResponse);
	soap_default___tr2__DeleteProfile(soap, &soap_tmp___tr2__DeleteProfile);
	if (!soap_get___tr2__DeleteProfile(soap, &soap_tmp___tr2__DeleteProfile, "-tr2:DeleteProfile", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__DeleteProfile(soap, soap_tmp___tr2__DeleteProfile.tr2__DeleteProfile, &tr2__DeleteProfileResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__DeleteProfileResponse(soap, &tr2__DeleteProfileResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__DeleteProfileResponse(soap, &tr2__DeleteProfileResponse, "tr2:DeleteProfileResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__DeleteProfileResponse(soap, &tr2__DeleteProfileResponse, "tr2:DeleteProfileResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetVideoSourceConfigurations(struct soap *soap)
{	struct __tr2__GetVideoSourceConfigurations soap_tmp___tr2__GetVideoSourceConfigurations;
	struct _tr2__GetVideoSourceConfigurationsResponse tr2__GetVideoSourceConfigurationsResponse;
	soap_default__tr2__GetVideoSourceConfigurationsResponse(soap, &tr2__GetVideoSourceConfigurationsResponse);
	soap_default___tr2__GetVideoSourceConfigurations(soap, &soap_tmp___tr2__GetVideoSourceConfigurations);
	if (!soap_get___tr2__GetVideoSourceConfigurations(soap, &soap_tmp___tr2__GetVideoSourceConfigurations, "-tr2:GetVideoSourceConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetVideoSourceConfigurations(soap, soap_tmp___tr2__GetVideoSourceConfigurations.tr2__GetVideoSourceConfigurations, &tr2__GetVideoSourceConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetVideoSourceConfigurationsResponse(soap, &tr2__GetVideoSourceConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetVideoSourceConfigurationsResponse(soap, &tr2__GetVideoSourceConfigurationsResponse, "tr2:GetVideoSourceConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetVideoSourceConfigurationsResponse(soap, &tr2__GetVideoSourceConfigurationsResponse, "tr2:GetVideoSourceConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetVideoEncoderConfigurations(struct soap *soap)
{	struct __tr2__GetVideoEncoderConfigurations soap_tmp___tr2__GetVideoEncoderConfigurations;
	struct _tr2__GetVideoEncoderConfigurationsResponse tr2__GetVideoEncoderConfigurationsResponse;
	soap_default__tr2__GetVideoEncoderConfigurationsResponse(soap, &tr2__GetVideoEncoderConfigurationsResponse);
	soap_default___tr2__GetVideoEncoderConfigurations(soap, &soap_tmp___tr2__GetVideoEncoderConfigurations);
	if (!soap_get___tr2__GetVideoEncoderConfigurations(soap, &soap_tmp___tr2__GetVideoEncoderConfigurations, "-tr2:GetVideoEncoderConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetVideoEncoderConfigurations(soap, soap_tmp___tr2__GetVideoEncoderConfigurations.tr2__GetVideoEncoderConfigurations, &tr2__GetVideoEncoderConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetVideoEncoderConfigurationsResponse(soap, &tr2__GetVideoEncoderConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetVideoEncoderConfigurationsResponse(soap, &tr2__GetVideoEncoderConfigurationsResponse, "tr2:GetVideoEncoderConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetVideoEncoderConfigurationsResponse(soap, &tr2__GetVideoEncoderConfigurationsResponse, "tr2:GetVideoEncoderConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetAudioSourceConfigurations(struct soap *soap)
{	struct __tr2__GetAudioSourceConfigurations soap_tmp___tr2__GetAudioSourceConfigurations;
	struct _tr2__GetAudioSourceConfigurationsResponse tr2__GetAudioSourceConfigurationsResponse;
	soap_default__tr2__GetAudioSourceConfigurationsResponse(soap, &tr2__GetAudioSourceConfigurationsResponse);
	soap_default___tr2__GetAudioSourceConfigurations(soap, &soap_tmp___tr2__GetAudioSourceConfigurations);
	if (!soap_get___tr2__GetAudioSourceConfigurations(soap, &soap_tmp___tr2__GetAudioSourceConfigurations, "-tr2:GetAudioSourceConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetAudioSourceConfigurations(soap, soap_tmp___tr2__GetAudioSourceConfigurations.tr2__GetAudioSourceConfigurations, &tr2__GetAudioSourceConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetAudioSourceConfigurationsResponse(soap, &tr2__GetAudioSourceConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetAudioSourceConfigurationsResponse(soap, &tr2__GetAudioSourceConfigurationsResponse, "tr2:GetAudioSourceConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetAudioSourceConfigurationsResponse(soap, &tr2__GetAudioSourceConfigurationsResponse, "tr2:GetAudioSourceConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetAudioEncoderConfigurations(struct soap *soap)
{	struct __tr2__GetAudioEncoderConfigurations soap_tmp___tr2__GetAudioEncoderConfigurations;
	struct _tr2__GetAudioEncoderConfigurationsResponse tr2__GetAudioEncoderConfigurationsResponse;
	soap_default__tr2__GetAudioEncoderConfigurationsResponse(soap, &tr2__GetAudioEncoderConfigurationsResponse);
	soap_default___tr2__GetAudioEncoderConfigurations(soap, &soap_tmp___tr2__GetAudioEncoderConfigurations);
	if (!soap_get___tr2__GetAudioEncoderConfigurations(soap, &soap_tmp___tr2__GetAudioEncoderConfigurations, "-tr2:GetAudioEncoderConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetAudioEncoderConfigurations(soap, soap_tmp___tr2__GetAudioEncoderConfigurations.tr2__GetAudioEncoderConfigurations, &tr2__GetAudioEncoderConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetAudioEncoderConfigurationsResponse(soap, &tr2__GetAudioEncoderConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetAudioEncoderConfigurationsResponse(soap, &tr2__GetAudioEncoderConfigurationsResponse, "tr2:GetAudioEncoderConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetAudioEncoderConfigurationsResponse(soap, &tr2__GetAudioEncoderConfigurationsResponse, "tr2:GetAudioEncoderConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetMetadataConfigurations(struct soap *soap)
{	struct __tr2__GetMetadataConfigurations soap_tmp___tr2__GetMetadataConfigurations;
	struct _tr2__GetMetadataConfigurationsResponse tr2__GetMetadataConfigurationsResponse;
	soap_default__tr2__GetMetadataConfigurationsResponse(soap, &tr2__GetMetadataConfigurationsResponse);
	soap_default___tr2__GetMetadataConfigurations(soap, &soap_tmp___tr2__GetMetadataConfigurations);
	if (!soap_get___tr2__GetMetadataConfigurations(soap, &soap_tmp___tr2__GetMetadataConfigurations, "-tr2:GetMetadataConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetMetadataConfigurations(soap, soap_tmp___tr2__GetMetadataConfigurations.tr2__GetMetadataConfigurations, &tr2__GetMetadataConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetMetadataConfigurationsResponse(soap, &tr2__GetMetadataConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetMetadataConfigurationsResponse(soap, &tr2__GetMetadataConfigurationsResponse, "tr2:GetMetadataConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetMetadataConfigurationsResponse(soap, &tr2__GetMetadataConfigurationsResponse, "tr2:GetMetadataConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetVideoSourceConfiguration(struct soap *soap)
{	struct __tr2__SetVideoSourceConfiguration soap_tmp___tr2__SetVideoSourceConfiguration;
	struct tr2__SetConfigurationResponse tr2__SetVideoSourceConfigurationResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__SetVideoSourceConfigurationResponse);
	soap_default___tr2__SetVideoSourceConfiguration(soap, &soap_tmp___tr2__SetVideoSourceConfiguration);
	if (!soap_get___tr2__SetVideoSourceConfiguration(soap, &soap_tmp___tr2__SetVideoSourceConfiguration, "-tr2:SetVideoSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetVideoSourceConfiguration(soap, soap_tmp___tr2__SetVideoSourceConfiguration.tr2__SetVideoSourceConfiguration, &tr2__SetVideoSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__SetVideoSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetVideoSourceConfigurationResponse, "tr2:SetVideoSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetVideoSourceConfigurationResponse, "tr2:SetVideoSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetVideoEncoderConfiguration(struct soap *soap)
{	struct __tr2__SetVideoEncoderConfiguration soap_tmp___tr2__SetVideoEncoderConfiguration;
	struct tr2__SetConfigurationResponse tr2__SetVideoEncoderConfigurationResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__SetVideoEncoderConfigurationResponse);
	soap_default___tr2__SetVideoEncoderConfiguration(soap, &soap_tmp___tr2__SetVideoEncoderConfiguration);
	if (!soap_get___tr2__SetVideoEncoderConfiguration(soap, &soap_tmp___tr2__SetVideoEncoderConfiguration, "-tr2:SetVideoEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetVideoEncoderConfiguration(soap, soap_tmp___tr2__SetVideoEncoderConfiguration.tr2__SetVideoEncoderConfiguration, &tr2__SetVideoEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__SetVideoEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetVideoEncoderConfigurationResponse, "tr2:SetVideoEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetVideoEncoderConfigurationResponse, "tr2:SetVideoEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetAudioSourceConfiguration(struct soap *soap)
{	struct __tr2__SetAudioSourceConfiguration soap_tmp___tr2__SetAudioSourceConfiguration;
	struct tr2__SetConfigurationResponse tr2__SetAudioSourceConfigurationResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__SetAudioSourceConfigurationResponse);
	soap_default___tr2__SetAudioSourceConfiguration(soap, &soap_tmp___tr2__SetAudioSourceConfiguration);
	if (!soap_get___tr2__SetAudioSourceConfiguration(soap, &soap_tmp___tr2__SetAudioSourceConfiguration, "-tr2:SetAudioSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetAudioSourceConfiguration(soap, soap_tmp___tr2__SetAudioSourceConfiguration.tr2__SetAudioSourceConfiguration, &tr2__SetAudioSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__SetAudioSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetAudioSourceConfigurationResponse, "tr2:SetAudioSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetAudioSourceConfigurationResponse, "tr2:SetAudioSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetAudioEncoderConfiguration(struct soap *soap)
{	struct __tr2__SetAudioEncoderConfiguration soap_tmp___tr2__SetAudioEncoderConfiguration;
	struct tr2__SetConfigurationResponse tr2__SetAudioEncoderConfigurationResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__SetAudioEncoderConfigurationResponse);
	soap_default___tr2__SetAudioEncoderConfiguration(soap, &soap_tmp___tr2__SetAudioEncoderConfiguration);
	if (!soap_get___tr2__SetAudioEncoderConfiguration(soap, &soap_tmp___tr2__SetAudioEncoderConfiguration, "-tr2:SetAudioEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetAudioEncoderConfiguration(soap, soap_tmp___tr2__SetAudioEncoderConfiguration.tr2__SetAudioEncoderConfiguration, &tr2__SetAudioEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__SetAudioEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetAudioEncoderConfigurationResponse, "tr2:SetAudioEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetAudioEncoderConfigurationResponse, "tr2:SetAudioEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetMetadataConfiguration(struct soap *soap)
{	struct __tr2__SetMetadataConfiguration soap_tmp___tr2__SetMetadataConfiguration;
	struct tr2__SetConfigurationResponse tr2__SetMetadataConfigurationResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__SetMetadataConfigurationResponse);
	soap_default___tr2__SetMetadataConfiguration(soap, &soap_tmp___tr2__SetMetadataConfiguration);
	if (!soap_get___tr2__SetMetadataConfiguration(soap, &soap_tmp___tr2__SetMetadataConfiguration, "-tr2:SetMetadataConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetMetadataConfiguration(soap, soap_tmp___tr2__SetMetadataConfiguration.tr2__SetMetadataConfiguration, &tr2__SetMetadataConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__SetMetadataConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetMetadataConfigurationResponse, "tr2:SetMetadataConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetMetadataConfigurationResponse, "tr2:SetMetadataConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetVideoSourceConfigurationOptions(struct soap *soap)
{	struct __tr2__GetVideoSourceConfigurationOptions soap_tmp___tr2__GetVideoSourceConfigurationOptions;
	struct _tr2__GetVideoSourceConfigurationOptionsResponse tr2__GetVideoSourceConfigurationOptionsResponse;
	soap_default__tr2__GetVideoSourceConfigurationOptionsResponse(soap, &tr2__GetVideoSourceConfigurationOptionsResponse);
	soap_default___tr2__GetVideoSourceConfigurationOptions(soap, &soap_tmp___tr2__GetVideoSourceConfigurationOptions);
	if (!soap_get___tr2__GetVideoSourceConfigurationOptions(soap, &soap_tmp___tr2__GetVideoSourceConfigurationOptions, "-tr2:GetVideoSourceConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetVideoSourceConfigurationOptions(soap, soap_tmp___tr2__GetVideoSourceConfigurationOptions.tr2__GetVideoSourceConfigurationOptions, &tr2__GetVideoSourceConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetVideoSourceConfigurationOptionsResponse(soap, &tr2__GetVideoSourceConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetVideoSourceConfigurationOptionsResponse(soap, &tr2__GetVideoSourceConfigurationOptionsResponse, "tr2:GetVideoSourceConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetVideoSourceConfigurationOptionsResponse(soap, &tr2__GetVideoSourceConfigurationOptionsResponse, "tr2:GetVideoSourceConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetVideoEncoderConfigurationOptions(struct soap *soap)
{	struct __tr2__GetVideoEncoderConfigurationOptions soap_tmp___tr2__GetVideoEncoderConfigurationOptions;
	struct _tr2__GetVideoEncoderConfigurationOptionsResponse tr2__GetVideoEncoderConfigurationOptionsResponse;
	soap_default__tr2__GetVideoEncoderConfigurationOptionsResponse(soap, &tr2__GetVideoEncoderConfigurationOptionsResponse);
	soap_default___tr2__GetVideoEncoderConfigurationOptions(soap, &soap_tmp___tr2__GetVideoEncoderConfigurationOptions);
	if (!soap_get___tr2__GetVideoEncoderConfigurationOptions(soap, &soap_tmp___tr2__GetVideoEncoderConfigurationOptions, "-tr2:GetVideoEncoderConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetVideoEncoderConfigurationOptions(soap, soap_tmp___tr2__GetVideoEncoderConfigurationOptions.tr2__GetVideoEncoderConfigurationOptions, &tr2__GetVideoEncoderConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetVideoEncoderConfigurationOptionsResponse(soap, &tr2__GetVideoEncoderConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetVideoEncoderConfigurationOptionsResponse(soap, &tr2__GetVideoEncoderConfigurationOptionsResponse, "tr2:GetVideoEncoderConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetVideoEncoderConfigurationOptionsResponse(soap, &tr2__GetVideoEncoderConfigurationOptionsResponse, "tr2:GetVideoEncoderConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetAudioSourceConfigurationOptions(struct soap *soap)
{	struct __tr2__GetAudioSourceConfigurationOptions soap_tmp___tr2__GetAudioSourceConfigurationOptions;
	struct _tr2__GetAudioSourceConfigurationOptionsResponse tr2__GetAudioSourceConfigurationOptionsResponse;
	soap_default__tr2__GetAudioSourceConfigurationOptionsResponse(soap, &tr2__GetAudioSourceConfigurationOptionsResponse);
	soap_default___tr2__GetAudioSourceConfigurationOptions(soap, &soap_tmp___tr2__GetAudioSourceConfigurationOptions);
	if (!soap_get___tr2__GetAudioSourceConfigurationOptions(soap, &soap_tmp___tr2__GetAudioSourceConfigurationOptions, "-tr2:GetAudioSourceConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetAudioSourceConfigurationOptions(soap, soap_tmp___tr2__GetAudioSourceConfigurationOptions.tr2__GetAudioSourceConfigurationOptions, &tr2__GetAudioSourceConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetAudioSourceConfigurationOptionsResponse(soap, &tr2__GetAudioSourceConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetAudioSourceConfigurationOptionsResponse(soap, &tr2__GetAudioSourceConfigurationOptionsResponse, "tr2:GetAudioSourceConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetAudioSourceConfigurationOptionsResponse(soap, &tr2__GetAudioSourceConfigurationOptionsResponse, "tr2:GetAudioSourceConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetAudioEncoderConfigurationOptions(struct soap *soap)
{	struct __tr2__GetAudioEncoderConfigurationOptions soap_tmp___tr2__GetAudioEncoderConfigurationOptions;
	struct _tr2__GetAudioEncoderConfigurationOptionsResponse tr2__GetAudioEncoderConfigurationOptionsResponse;
	soap_default__tr2__GetAudioEncoderConfigurationOptionsResponse(soap, &tr2__GetAudioEncoderConfigurationOptionsResponse);
	soap_default___tr2__GetAudioEncoderConfigurationOptions(soap, &soap_tmp___tr2__GetAudioEncoderConfigurationOptions);
	if (!soap_get___tr2__GetAudioEncoderConfigurationOptions(soap, &soap_tmp___tr2__GetAudioEncoderConfigurationOptions, "-tr2:GetAudioEncoderConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetAudioEncoderConfigurationOptions(soap, soap_tmp___tr2__GetAudioEncoderConfigurationOptions.tr2__GetAudioEncoderConfigurationOptions, &tr2__GetAudioEncoderConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetAudioEncoderConfigurationOptionsResponse(soap, &tr2__GetAudioEncoderConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetAudioEncoderConfigurationOptionsResponse(soap, &tr2__GetAudioEncoderConfigurationOptionsResponse, "tr2:GetAudioEncoderConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetAudioEncoderConfigurationOptionsResponse(soap, &tr2__GetAudioEncoderConfigurationOptionsResponse, "tr2:GetAudioEncoderConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetMetadataConfigurationOptions(struct soap *soap)
{	struct __tr2__GetMetadataConfigurationOptions soap_tmp___tr2__GetMetadataConfigurationOptions;
	struct _tr2__GetMetadataConfigurationOptionsResponse tr2__GetMetadataConfigurationOptionsResponse;
	soap_default__tr2__GetMetadataConfigurationOptionsResponse(soap, &tr2__GetMetadataConfigurationOptionsResponse);
	soap_default___tr2__GetMetadataConfigurationOptions(soap, &soap_tmp___tr2__GetMetadataConfigurationOptions);
	if (!soap_get___tr2__GetMetadataConfigurationOptions(soap, &soap_tmp___tr2__GetMetadataConfigurationOptions, "-tr2:GetMetadataConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetMetadataConfigurationOptions(soap, soap_tmp___tr2__GetMetadataConfigurationOptions.tr2__GetMetadataConfigurationOptions, &tr2__GetMetadataConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetMetadataConfigurationOptionsResponse(soap, &tr2__GetMetadataConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetMetadataConfigurationOptionsResponse(soap, &tr2__GetMetadataConfigurationOptionsResponse, "tr2:GetMetadataConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetMetadataConfigurationOptionsResponse(soap, &tr2__GetMetadataConfigurationOptionsResponse, "tr2:GetMetadataConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetVideoEncoderInstances(struct soap *soap)
{	struct __tr2__GetVideoEncoderInstances soap_tmp___tr2__GetVideoEncoderInstances;
	struct _tr2__GetVideoEncoderInstancesResponse tr2__GetVideoEncoderInstancesResponse;
	soap_default__tr2__GetVideoEncoderInstancesResponse(soap, &tr2__GetVideoEncoderInstancesResponse);
	soap_default___tr2__GetVideoEncoderInstances(soap, &soap_tmp___tr2__GetVideoEncoderInstances);
	if (!soap_get___tr2__GetVideoEncoderInstances(soap, &soap_tmp___tr2__GetVideoEncoderInstances, "-tr2:GetVideoEncoderInstances", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetVideoEncoderInstances(soap, soap_tmp___tr2__GetVideoEncoderInstances.tr2__GetVideoEncoderInstances, &tr2__GetVideoEncoderInstancesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetVideoEncoderInstancesResponse(soap, &tr2__GetVideoEncoderInstancesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetVideoEncoderInstancesResponse(soap, &tr2__GetVideoEncoderInstancesResponse, "tr2:GetVideoEncoderInstancesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetVideoEncoderInstancesResponse(soap, &tr2__GetVideoEncoderInstancesResponse, "tr2:GetVideoEncoderInstancesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetStreamUri(struct soap *soap)
{	struct __tr2__GetStreamUri soap_tmp___tr2__GetStreamUri;
	struct _tr2__GetStreamUriResponse tr2__GetStreamUriResponse;
	soap_default__tr2__GetStreamUriResponse(soap, &tr2__GetStreamUriResponse);
	soap_default___tr2__GetStreamUri(soap, &soap_tmp___tr2__GetStreamUri);
	if (!soap_get___tr2__GetStreamUri(soap, &soap_tmp___tr2__GetStreamUri, "-tr2:GetStreamUri", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetStreamUri(soap, soap_tmp___tr2__GetStreamUri.tr2__GetStreamUri, &tr2__GetStreamUriResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetStreamUriResponse(soap, &tr2__GetStreamUriResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetStreamUriResponse(soap, &tr2__GetStreamUriResponse, "tr2:GetStreamUriResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetStreamUriResponse(soap, &tr2__GetStreamUriResponse, "tr2:GetStreamUriResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__StartMulticastStreaming(struct soap *soap)
{	struct __tr2__StartMulticastStreaming soap_tmp___tr2__StartMulticastStreaming;
	struct tr2__SetConfigurationResponse tr2__StartMulticastStreamingResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__StartMulticastStreamingResponse);
	soap_default___tr2__StartMulticastStreaming(soap, &soap_tmp___tr2__StartMulticastStreaming);
	if (!soap_get___tr2__StartMulticastStreaming(soap, &soap_tmp___tr2__StartMulticastStreaming, "-tr2:StartMulticastStreaming", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__StartMulticastStreaming(soap, soap_tmp___tr2__StartMulticastStreaming.tr2__StartMulticastStreaming, &tr2__StartMulticastStreamingResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__StartMulticastStreamingResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__StartMulticastStreamingResponse, "tr2:StartMulticastStreamingResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__StartMulticastStreamingResponse, "tr2:StartMulticastStreamingResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__StopMulticastStreaming(struct soap *soap)
{	struct __tr2__StopMulticastStreaming soap_tmp___tr2__StopMulticastStreaming;
	struct tr2__SetConfigurationResponse tr2__StopMulticastStreamingResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__StopMulticastStreamingResponse);
	soap_default___tr2__StopMulticastStreaming(soap, &soap_tmp___tr2__StopMulticastStreaming);
	if (!soap_get___tr2__StopMulticastStreaming(soap, &soap_tmp___tr2__StopMulticastStreaming, "-tr2:StopMulticastStreaming", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__StopMulticastStreaming(soap, soap_tmp___tr2__StopMulticastStreaming.tr2__StopMulticastStreaming, &tr2__StopMulticastStreamingResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__StopMulticastStreamingResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__StopMulticastStreamingResponse, "tr2:StopMulticastStreamingResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__StopMulticastStreamingResponse, "tr2:StopMulticastStreamingResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetSynchronizationPoint(struct soap *soap)
{	struct __tr2__SetSynchronizationPoint soap_tmp___tr2__SetSynchronizationPoint;
	struct _tr2__SetSynchronizationPointResponse tr2__SetSynchronizationPointResponse;
	soap_default__tr2__SetSynchronizationPointResponse(soap, &tr2__SetSynchronizationPointResponse);
	soap_default___tr2__SetSynchronizationPoint(soap, &soap_tmp___tr2__SetSynchronizationPoint);
	if (!soap_get___tr2__SetSynchronizationPoint(soap, &soap_tmp___tr2__SetSynchronizationPoint, "-tr2:SetSynchronizationPoint", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetSynchronizationPoint(soap, soap_tmp___tr2__SetSynchronizationPoint.tr2__SetSynchronizationPoint, &tr2__SetSynchronizationPointResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__SetSynchronizationPointResponse(soap, &tr2__SetSynchronizationPointResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__SetSynchronizationPointResponse(soap, &tr2__SetSynchronizationPointResponse, "tr2:SetSynchronizationPointResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__SetSynchronizationPointResponse(soap, &tr2__SetSynchronizationPointResponse, "tr2:SetSynchronizationPointResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetSnapshotUri(struct soap *soap)
{	struct __tr2__GetSnapshotUri soap_tmp___tr2__GetSnapshotUri;
	struct _tr2__GetSnapshotUriResponse tr2__GetSnapshotUriResponse;
	soap_default__tr2__GetSnapshotUriResponse(soap, &tr2__GetSnapshotUriResponse);
	soap_default___tr2__GetSnapshotUri(soap, &soap_tmp___tr2__GetSnapshotUri);
	if (!soap_get___tr2__GetSnapshotUri(soap, &soap_tmp___tr2__GetSnapshotUri, "-tr2:GetSnapshotUri", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetSnapshotUri(soap, soap_tmp___tr2__GetSnapshotUri.tr2__GetSnapshotUri, &tr2__GetSnapshotUriResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetSnapshotUriResponse(soap, &tr2__GetSnapshotUriResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetSnapshotUriResponse(soap, &tr2__GetSnapshotUriResponse, "tr2:GetSnapshotUriResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetSnapshotUriResponse(soap, &tr2__GetSnapshotUriResponse, "tr2:GetSnapshotUriResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetVideoSourceModes(struct soap *soap)
{	struct __tr2__GetVideoSourceModes soap_tmp___tr2__GetVideoSourceModes;
	struct _tr2__GetVideoSourceModesResponse tr2__GetVideoSourceModesResponse;
	soap_default__tr2__GetVideoSourceModesResponse(soap, &tr2__GetVideoSourceModesResponse);
	soap_default___tr2__GetVideoSourceModes(soap, &soap_tmp___tr2__GetVideoSourceModes);
	if (!soap_get___tr2__GetVideoSourceModes(soap, &soap_tmp___tr2__GetVideoSourceModes, "-tr2:GetVideoSourceModes", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetVideoSourceModes(soap, soap_tmp___tr2__GetVideoSourceModes.tr2__GetVideoSourceModes, &tr2__GetVideoSourceModesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetVideoSourceModesResponse(soap, &tr2__GetVideoSourceModesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetVideoSourceModesResponse(soap, &tr2__GetVideoSourceModesResponse, "tr2:GetVideoSourceModesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetVideoSourceModesResponse(soap, &tr2__GetVideoSourceModesResponse, "tr2:GetVideoSourceModesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetVideoSourceMode(struct soap *soap)
{	struct __tr2__SetVideoSourceMode soap_tmp___tr2__SetVideoSourceMode;
	struct _tr2__SetVideoSourceModeResponse tr2__SetVideoSourceModeResponse;
	soap_default__tr2__SetVideoSourceModeResponse(soap, &tr2__SetVideoSourceModeResponse);
	soap_default___tr2__SetVideoSourceMode(soap, &soap_tmp___tr2__SetVideoSourceMode);
	if (!soap_get___tr2__SetVideoSourceMode(soap, &soap_tmp___tr2__SetVideoSourceMode, "-tr2:SetVideoSourceMode", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetVideoSourceMode(soap, soap_tmp___tr2__SetVideoSourceMode.tr2__SetVideoSourceMode, &tr2__SetVideoSourceModeResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__SetVideoSourceModeResponse(soap, &tr2__SetVideoSourceModeResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__SetVideoSourceModeResponse(soap, &tr2__SetVideoSourceModeResponse, "tr2:SetVideoSourceModeResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__SetVideoSourceModeResponse(soap, &tr2__SetVideoSourceModeResponse, "tr2:SetVideoSourceModeResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetOSDs(struct soap *soap)
{	struct __tr2__GetOSDs soap_tmp___tr2__GetOSDs;
	struct _tr2__GetOSDsResponse tr2__GetOSDsResponse;
	soap_default__tr2__GetOSDsResponse(soap, &tr2__GetOSDsResponse);
	soap_default___tr2__GetOSDs(soap, &soap_tmp___tr2__GetOSDs);
	if (!soap_get___tr2__GetOSDs(soap, &soap_tmp___tr2__GetOSDs, "-tr2:GetOSDs", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetOSDs(soap, soap_tmp___tr2__GetOSDs.tr2__GetOSDs, &tr2__GetOSDsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetOSDsResponse(soap, &tr2__GetOSDsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetOSDsResponse(soap, &tr2__GetOSDsResponse, "tr2:GetOSDsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetOSDsResponse(soap, &tr2__GetOSDsResponse, "tr2:GetOSDsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetOSDOptions(struct soap *soap)
{	struct __tr2__GetOSDOptions soap_tmp___tr2__GetOSDOptions;
	struct _tr2__GetOSDOptionsResponse tr2__GetOSDOptionsResponse;
	soap_default__tr2__GetOSDOptionsResponse(soap, &tr2__GetOSDOptionsResponse);
	soap_default___tr2__GetOSDOptions(soap, &soap_tmp___tr2__GetOSDOptions);
	if (!soap_get___tr2__GetOSDOptions(soap, &soap_tmp___tr2__GetOSDOptions, "-tr2:GetOSDOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetOSDOptions(soap, soap_tmp___tr2__GetOSDOptions.tr2__GetOSDOptions, &tr2__GetOSDOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetOSDOptionsResponse(soap, &tr2__GetOSDOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetOSDOptionsResponse(soap, &tr2__GetOSDOptionsResponse, "tr2:GetOSDOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetOSDOptionsResponse(soap, &tr2__GetOSDOptionsResponse, "tr2:GetOSDOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetOSD(struct soap *soap)
{	struct __tr2__SetOSD soap_tmp___tr2__SetOSD;
	struct tr2__SetConfigurationResponse tr2__SetOSDResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__SetOSDResponse);
	soap_default___tr2__SetOSD(soap, &soap_tmp___tr2__SetOSD);
	if (!soap_get___tr2__SetOSD(soap, &soap_tmp___tr2__SetOSD, "-tr2:SetOSD", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetOSD(soap, soap_tmp___tr2__SetOSD.tr2__SetOSD, &tr2__SetOSDResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__SetOSDResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetOSDResponse, "tr2:SetOSDResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetOSDResponse, "tr2:SetOSDResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__CreateOSD(struct soap *soap)
{	struct __tr2__CreateOSD soap_tmp___tr2__CreateOSD;
	struct _tr2__CreateOSDResponse tr2__CreateOSDResponse;
	soap_default__tr2__CreateOSDResponse(soap, &tr2__CreateOSDResponse);
	soap_default___tr2__CreateOSD(soap, &soap_tmp___tr2__CreateOSD);
	if (!soap_get___tr2__CreateOSD(soap, &soap_tmp___tr2__CreateOSD, "-tr2:CreateOSD", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__CreateOSD(soap, soap_tmp___tr2__CreateOSD.tr2__CreateOSD, &tr2__CreateOSDResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__CreateOSDResponse(soap, &tr2__CreateOSDResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__CreateOSDResponse(soap, &tr2__CreateOSDResponse, "tr2:CreateOSDResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__CreateOSDResponse(soap, &tr2__CreateOSDResponse, "tr2:CreateOSDResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__DeleteOSD(struct soap *soap)
{	struct __tr2__DeleteOSD soap_tmp___tr2__DeleteOSD;
	struct tr2__SetConfigurationResponse tr2__DeleteOSDResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__DeleteOSDResponse);
	soap_default___tr2__DeleteOSD(soap, &soap_tmp___tr2__DeleteOSD);
	if (!soap_get___tr2__DeleteOSD(soap, &soap_tmp___tr2__DeleteOSD, "-tr2:DeleteOSD", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__DeleteOSD(soap, soap_tmp___tr2__DeleteOSD.tr2__DeleteOSD, &tr2__DeleteOSDResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__DeleteOSDResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__DeleteOSDResponse, "tr2:DeleteOSDResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__DeleteOSDResponse, "tr2:DeleteOSDResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetMasks(struct soap *soap)
{	struct __tr2__GetMasks soap_tmp___tr2__GetMasks;
	struct _tr2__GetMasksResponse tr2__GetMasksResponse;
	soap_default__tr2__GetMasksResponse(soap, &tr2__GetMasksResponse);
	soap_default___tr2__GetMasks(soap, &soap_tmp___tr2__GetMasks);
	if (!soap_get___tr2__GetMasks(soap, &soap_tmp___tr2__GetMasks, "-tr2:GetMasks", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetMasks(soap, soap_tmp___tr2__GetMasks.tr2__GetMasks, &tr2__GetMasksResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetMasksResponse(soap, &tr2__GetMasksResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetMasksResponse(soap, &tr2__GetMasksResponse, "tr2:GetMasksResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetMasksResponse(soap, &tr2__GetMasksResponse, "tr2:GetMasksResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__GetMaskOptions(struct soap *soap)
{	struct __tr2__GetMaskOptions soap_tmp___tr2__GetMaskOptions;
	struct _tr2__GetMaskOptionsResponse tr2__GetMaskOptionsResponse;
	soap_default__tr2__GetMaskOptionsResponse(soap, &tr2__GetMaskOptionsResponse);
	soap_default___tr2__GetMaskOptions(soap, &soap_tmp___tr2__GetMaskOptions);
	if (!soap_get___tr2__GetMaskOptions(soap, &soap_tmp___tr2__GetMaskOptions, "-tr2:GetMaskOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__GetMaskOptions(soap, soap_tmp___tr2__GetMaskOptions.tr2__GetMaskOptions, &tr2__GetMaskOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__GetMaskOptionsResponse(soap, &tr2__GetMaskOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__GetMaskOptionsResponse(soap, &tr2__GetMaskOptionsResponse, "tr2:GetMaskOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__GetMaskOptionsResponse(soap, &tr2__GetMaskOptionsResponse, "tr2:GetMaskOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__SetMask(struct soap *soap)
{	struct __tr2__SetMask soap_tmp___tr2__SetMask;
	struct tr2__SetConfigurationResponse tr2__SetMaskResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__SetMaskResponse);
	soap_default___tr2__SetMask(soap, &soap_tmp___tr2__SetMask);
	if (!soap_get___tr2__SetMask(soap, &soap_tmp___tr2__SetMask, "-tr2:SetMask", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__SetMask(soap, soap_tmp___tr2__SetMask.tr2__SetMask, &tr2__SetMaskResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__SetMaskResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetMaskResponse, "tr2:SetMaskResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__SetMaskResponse, "tr2:SetMaskResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__CreateMask(struct soap *soap)
{	struct __tr2__CreateMask soap_tmp___tr2__CreateMask;
	struct _tr2__CreateMaskResponse tr2__CreateMaskResponse;
	soap_default__tr2__CreateMaskResponse(soap, &tr2__CreateMaskResponse);
	soap_default___tr2__CreateMask(soap, &soap_tmp___tr2__CreateMask);
	if (!soap_get___tr2__CreateMask(soap, &soap_tmp___tr2__CreateMask, "-tr2:CreateMask", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__CreateMask(soap, soap_tmp___tr2__CreateMask.tr2__CreateMask, &tr2__CreateMaskResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__tr2__CreateMaskResponse(soap, &tr2__CreateMaskResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__tr2__CreateMaskResponse(soap, &tr2__CreateMaskResponse, "tr2:CreateMaskResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__tr2__CreateMaskResponse(soap, &tr2__CreateMaskResponse, "tr2:CreateMaskResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___tr2__DeleteMask(struct soap *soap)
{	struct __tr2__DeleteMask soap_tmp___tr2__DeleteMask;
	struct tr2__SetConfigurationResponse tr2__DeleteMaskResponse;
	soap_default_tr2__SetConfigurationResponse(soap, &tr2__DeleteMaskResponse);
	soap_default___tr2__DeleteMask(soap, &soap_tmp___tr2__DeleteMask);
	if (!soap_get___tr2__DeleteMask(soap, &soap_tmp___tr2__DeleteMask, "-tr2:DeleteMask", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __tr2__DeleteMask(soap, soap_tmp___tr2__DeleteMask.tr2__DeleteMask, &tr2__DeleteMaskResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize_tr2__SetConfigurationResponse(soap, &tr2__DeleteMaskResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__DeleteMaskResponse, "tr2:DeleteMaskResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put_tr2__SetConfigurationResponse(soap, &tr2__DeleteMaskResponse, "tr2:DeleteMaskResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetServiceCapabilities(struct soap *soap)
{	struct __trt__GetServiceCapabilities soap_tmp___trt__GetServiceCapabilities;
	struct _trt__GetServiceCapabilitiesResponse trt__GetServiceCapabilitiesResponse;
	soap_default__trt__GetServiceCapabilitiesResponse(soap, &trt__GetServiceCapabilitiesResponse);
	soap_default___trt__GetServiceCapabilities(soap, &soap_tmp___trt__GetServiceCapabilities);
	if (!soap_get___trt__GetServiceCapabilities(soap, &soap_tmp___trt__GetServiceCapabilities, "-trt:GetServiceCapabilities", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetServiceCapabilities(soap, soap_tmp___trt__GetServiceCapabilities.trt__GetServiceCapabilities, &trt__GetServiceCapabilitiesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetServiceCapabilitiesResponse(soap, &trt__GetServiceCapabilitiesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetServiceCapabilitiesResponse(soap, &trt__GetServiceCapabilitiesResponse, "trt:GetServiceCapabilitiesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetServiceCapabilitiesResponse(soap, &trt__GetServiceCapabilitiesResponse, "trt:GetServiceCapabilitiesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetVideoSources(struct soap *soap)
{	struct __trt__GetVideoSources soap_tmp___trt__GetVideoSources;
	struct _trt__GetVideoSourcesResponse trt__GetVideoSourcesResponse;
	soap_default__trt__GetVideoSourcesResponse(soap, &trt__GetVideoSourcesResponse);
	soap_default___trt__GetVideoSources(soap, &soap_tmp___trt__GetVideoSources);
	if (!soap_get___trt__GetVideoSources(soap, &soap_tmp___trt__GetVideoSources, "-trt:GetVideoSources", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetVideoSources(soap, soap_tmp___trt__GetVideoSources.trt__GetVideoSources, &trt__GetVideoSourcesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetVideoSourcesResponse(soap, &trt__GetVideoSourcesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetVideoSourcesResponse(soap, &trt__GetVideoSourcesResponse, "trt:GetVideoSourcesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetVideoSourcesResponse(soap, &trt__GetVideoSourcesResponse, "trt:GetVideoSourcesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetAudioSources(struct soap *soap)
{	struct __trt__GetAudioSources soap_tmp___trt__GetAudioSources;
	struct _trt__GetAudioSourcesResponse trt__GetAudioSourcesResponse;
	soap_default__trt__GetAudioSourcesResponse(soap, &trt__GetAudioSourcesResponse);
	soap_default___trt__GetAudioSources(soap, &soap_tmp___trt__GetAudioSources);
	if (!soap_get___trt__GetAudioSources(soap, &soap_tmp___trt__GetAudioSources, "-trt:GetAudioSources", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetAudioSources(soap, soap_tmp___trt__GetAudioSources.trt__GetAudioSources, &trt__GetAudioSourcesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetAudioSourcesResponse(soap, &trt__GetAudioSourcesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetAudioSourcesResponse(soap, &trt__GetAudioSourcesResponse, "trt:GetAudioSourcesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetAudioSourcesResponse(soap, &trt__GetAudioSourcesResponse, "trt:GetAudioSourcesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__CreateProfile(struct soap *soap)
{	struct __trt__CreateProfile soap_tmp___trt__CreateProfile;
	struct _trt__CreateProfileResponse trt__CreateProfileResponse;
	soap_default__trt__CreateProfileResponse(soap, &trt__CreateProfileResponse);
	soap_default___trt__CreateProfile(soap, &soap_tmp___trt__CreateProfile);
	if (!soap_get___trt__CreateProfile(soap, &soap_tmp___trt__CreateProfile, "-trt:CreateProfile", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__CreateProfile(soap, soap_tmp___trt__CreateProfile.trt__CreateProfile, &trt__CreateProfileResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__CreateProfileResponse(soap, &trt__CreateProfileResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__CreateProfileResponse(soap, &trt__CreateProfileResponse, "trt:CreateProfileResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__CreateProfileResponse(soap, &trt__CreateProfileResponse, "trt:CreateProfileResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetProfile(struct soap *soap)
{	struct __trt__GetProfile soap_tmp___trt__GetProfile;
	struct _trt__GetProfileResponse trt__GetProfileResponse;
	soap_default__trt__GetProfileResponse(soap, &trt__GetProfileResponse);
	soap_default___trt__GetProfile(soap, &soap_tmp___trt__GetProfile);
	if (!soap_get___trt__GetProfile(soap, &soap_tmp___trt__GetProfile, "-trt:GetProfile", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetProfile(soap, soap_tmp___trt__GetProfile.trt__GetProfile, &trt__GetProfileResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetProfileResponse(soap, &trt__GetProfileResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetProfileResponse(soap, &trt__GetProfileResponse, "trt:GetProfileResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetProfileResponse(soap, &trt__GetProfileResponse, "trt:GetProfileResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetProfiles(struct soap *soap)
{	struct __trt__GetProfiles soap_tmp___trt__GetProfiles;
	struct _trt__GetProfilesResponse trt__GetProfilesResponse;
	soap_default__trt__GetProfilesResponse(soap, &trt__GetProfilesResponse);
	soap_default___trt__GetProfiles(soap, &soap_tmp___trt__GetProfiles);
	if (!soap_get___trt__GetProfiles(soap, &soap_tmp___trt__GetProfiles, "-trt:GetProfiles", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetProfiles(soap, soap_tmp___trt__GetProfiles.trt__GetProfiles, &trt__GetProfilesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetProfilesResponse(soap, &trt__GetProfilesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetProfilesResponse(soap, &trt__GetProfilesResponse, "trt:GetProfilesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetProfilesResponse(soap, &trt__GetProfilesResponse, "trt:GetProfilesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__AddVideoEncoderConfiguration(struct soap *soap)
{	struct __trt__AddVideoEncoderConfiguration soap_tmp___trt__AddVideoEncoderConfiguration;
	struct _trt__AddVideoEncoderConfigurationResponse trt__AddVideoEncoderConfigurationResponse;
	soap_default__trt__AddVideoEncoderConfigurationResponse(soap, &trt__AddVideoEncoderConfigurationResponse);
	soap_default___trt__AddVideoEncoderConfiguration(soap, &soap_tmp___trt__AddVideoEncoderConfiguration);
	if (!soap_get___trt__AddVideoEncoderConfiguration(soap, &soap_tmp___trt__AddVideoEncoderConfiguration, "-trt:AddVideoEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__AddVideoEncoderConfiguration(soap, soap_tmp___trt__AddVideoEncoderConfiguration.trt__AddVideoEncoderConfiguration, &trt__AddVideoEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__AddVideoEncoderConfigurationResponse(soap, &trt__AddVideoEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__AddVideoEncoderConfigurationResponse(soap, &trt__AddVideoEncoderConfigurationResponse, "trt:AddVideoEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__AddVideoEncoderConfigurationResponse(soap, &trt__AddVideoEncoderConfigurationResponse, "trt:AddVideoEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__AddVideoSourceConfiguration(struct soap *soap)
{	struct __trt__AddVideoSourceConfiguration soap_tmp___trt__AddVideoSourceConfiguration;
	struct _trt__AddVideoSourceConfigurationResponse trt__AddVideoSourceConfigurationResponse;
	soap_default__trt__AddVideoSourceConfigurationResponse(soap, &trt__AddVideoSourceConfigurationResponse);
	soap_default___trt__AddVideoSourceConfiguration(soap, &soap_tmp___trt__AddVideoSourceConfiguration);
	if (!soap_get___trt__AddVideoSourceConfiguration(soap, &soap_tmp___trt__AddVideoSourceConfiguration, "-trt:AddVideoSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__AddVideoSourceConfiguration(soap, soap_tmp___trt__AddVideoSourceConfiguration.trt__AddVideoSourceConfiguration, &trt__AddVideoSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__AddVideoSourceConfigurationResponse(soap, &trt__AddVideoSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__AddVideoSourceConfigurationResponse(soap, &trt__AddVideoSourceConfigurationResponse, "trt:AddVideoSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__AddVideoSourceConfigurationResponse(soap, &trt__AddVideoSourceConfigurationResponse, "trt:AddVideoSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__AddAudioEncoderConfiguration(struct soap *soap)
{	struct __trt__AddAudioEncoderConfiguration soap_tmp___trt__AddAudioEncoderConfiguration;
	struct _trt__AddAudioEncoderConfigurationResponse trt__AddAudioEncoderConfigurationResponse;
	soap_default__trt__AddAudioEncoderConfigurationResponse(soap, &trt__AddAudioEncoderConfigurationResponse);
	soap_default___trt__AddAudioEncoderConfiguration(soap, &soap_tmp___trt__AddAudioEncoderConfiguration);
	if (!soap_get___trt__AddAudioEncoderConfiguration(soap, &soap_tmp___trt__AddAudioEncoderConfiguration, "-trt:AddAudioEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__AddAudioEncoderConfiguration(soap, soap_tmp___trt__AddAudioEncoderConfiguration.trt__AddAudioEncoderConfiguration, &trt__AddAudioEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__AddAudioEncoderConfigurationResponse(soap, &trt__AddAudioEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__AddAudioEncoderConfigurationResponse(soap, &trt__AddAudioEncoderConfigurationResponse, "trt:AddAudioEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__AddAudioEncoderConfigurationResponse(soap, &trt__AddAudioEncoderConfigurationResponse, "trt:AddAudioEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__AddAudioSourceConfiguration(struct soap *soap)
{	struct __trt__AddAudioSourceConfiguration soap_tmp___trt__AddAudioSourceConfiguration;
	struct _trt__AddAudioSourceConfigurationResponse trt__AddAudioSourceConfigurationResponse;
	soap_default__trt__AddAudioSourceConfigurationResponse(soap, &trt__AddAudioSourceConfigurationResponse);
	soap_default___trt__AddAudioSourceConfiguration(soap, &soap_tmp___trt__AddAudioSourceConfiguration);
	if (!soap_get___trt__AddAudioSourceConfiguration(soap, &soap_tmp___trt__AddAudioSourceConfiguration, "-trt:AddAudioSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__AddAudioSourceConfiguration(soap, soap_tmp___trt__AddAudioSourceConfiguration.trt__AddAudioSourceConfiguration, &trt__AddAudioSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__AddAudioSourceConfigurationResponse(soap, &trt__AddAudioSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__AddAudioSourceConfigurationResponse(soap, &trt__AddAudioSourceConfigurationResponse, "trt:AddAudioSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__AddAudioSourceConfigurationResponse(soap, &trt__AddAudioSourceConfigurationResponse, "trt:AddAudioSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__AddPTZConfiguration(struct soap *soap)
{	struct __trt__AddPTZConfiguration soap_tmp___trt__AddPTZConfiguration;
	struct _trt__AddPTZConfigurationResponse trt__AddPTZConfigurationResponse;
	soap_default__trt__AddPTZConfigurationResponse(soap, &trt__AddPTZConfigurationResponse);
	soap_default___trt__AddPTZConfiguration(soap, &soap_tmp___trt__AddPTZConfiguration);
	if (!soap_get___trt__AddPTZConfiguration(soap, &soap_tmp___trt__AddPTZConfiguration, "-trt:AddPTZConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__AddPTZConfiguration(soap, soap_tmp___trt__AddPTZConfiguration.trt__AddPTZConfiguration, &trt__AddPTZConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__AddPTZConfigurationResponse(soap, &trt__AddPTZConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__AddPTZConfigurationResponse(soap, &trt__AddPTZConfigurationResponse, "trt:AddPTZConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__AddPTZConfigurationResponse(soap, &trt__AddPTZConfigurationResponse, "trt:AddPTZConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__AddMetadataConfiguration(struct soap *soap)
{	struct __trt__AddMetadataConfiguration soap_tmp___trt__AddMetadataConfiguration;
	struct _trt__AddMetadataConfigurationResponse trt__AddMetadataConfigurationResponse;
	soap_default__trt__AddMetadataConfigurationResponse(soap, &trt__AddMetadataConfigurationResponse);
	soap_default___trt__AddMetadataConfiguration(soap, &soap_tmp___trt__AddMetadataConfiguration);
	if (!soap_get___trt__AddMetadataConfiguration(soap, &soap_tmp___trt__AddMetadataConfiguration, "-trt:AddMetadataConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__AddMetadataConfiguration(soap, soap_tmp___trt__AddMetadataConfiguration.trt__AddMetadataConfiguration, &trt__AddMetadataConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__AddMetadataConfigurationResponse(soap, &trt__AddMetadataConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__AddMetadataConfigurationResponse(soap, &trt__AddMetadataConfigurationResponse, "trt:AddMetadataConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__AddMetadataConfigurationResponse(soap, &trt__AddMetadataConfigurationResponse, "trt:AddMetadataConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__RemoveVideoEncoderConfiguration(struct soap *soap)
{	struct __trt__RemoveVideoEncoderConfiguration soap_tmp___trt__RemoveVideoEncoderConfiguration;
	struct _trt__RemoveVideoEncoderConfigurationResponse trt__RemoveVideoEncoderConfigurationResponse;
	soap_default__trt__RemoveVideoEncoderConfigurationResponse(soap, &trt__RemoveVideoEncoderConfigurationResponse);
	soap_default___trt__RemoveVideoEncoderConfiguration(soap, &soap_tmp___trt__RemoveVideoEncoderConfiguration);
	if (!soap_get___trt__RemoveVideoEncoderConfiguration(soap, &soap_tmp___trt__RemoveVideoEncoderConfiguration, "-trt:RemoveVideoEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__RemoveVideoEncoderConfiguration(soap, soap_tmp___trt__RemoveVideoEncoderConfiguration.trt__RemoveVideoEncoderConfiguration, &trt__RemoveVideoEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__RemoveVideoEncoderConfigurationResponse(soap, &trt__RemoveVideoEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__RemoveVideoEncoderConfigurationResponse(soap, &trt__RemoveVideoEncoderConfigurationResponse, "trt:RemoveVideoEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__RemoveVideoEncoderConfigurationResponse(soap, &trt__RemoveVideoEncoderConfigurationResponse, "trt:RemoveVideoEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__RemoveVideoSourceConfiguration(struct soap *soap)
{	struct __trt__RemoveVideoSourceConfiguration soap_tmp___trt__RemoveVideoSourceConfiguration;
	struct _trt__RemoveVideoSourceConfigurationResponse trt__RemoveVideoSourceConfigurationResponse;
	soap_default__trt__RemoveVideoSourceConfigurationResponse(soap, &trt__RemoveVideoSourceConfigurationResponse);
	soap_default___trt__RemoveVideoSourceConfiguration(soap, &soap_tmp___trt__RemoveVideoSourceConfiguration);
	if (!soap_get___trt__RemoveVideoSourceConfiguration(soap, &soap_tmp___trt__RemoveVideoSourceConfiguration, "-trt:RemoveVideoSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__RemoveVideoSourceConfiguration(soap, soap_tmp___trt__RemoveVideoSourceConfiguration.trt__RemoveVideoSourceConfiguration, &trt__RemoveVideoSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__RemoveVideoSourceConfigurationResponse(soap, &trt__RemoveVideoSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__RemoveVideoSourceConfigurationResponse(soap, &trt__RemoveVideoSourceConfigurationResponse, "trt:RemoveVideoSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__RemoveVideoSourceConfigurationResponse(soap, &trt__RemoveVideoSourceConfigurationResponse, "trt:RemoveVideoSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__RemoveAudioEncoderConfiguration(struct soap *soap)
{	struct __trt__RemoveAudioEncoderConfiguration soap_tmp___trt__RemoveAudioEncoderConfiguration;
	struct _trt__RemoveAudioEncoderConfigurationResponse trt__RemoveAudioEncoderConfigurationResponse;
	soap_default__trt__RemoveAudioEncoderConfigurationResponse(soap, &trt__RemoveAudioEncoderConfigurationResponse);
	soap_default___trt__RemoveAudioEncoderConfiguration(soap, &soap_tmp___trt__RemoveAudioEncoderConfiguration);
	if (!soap_get___trt__RemoveAudioEncoderConfiguration(soap, &soap_tmp___trt__RemoveAudioEncoderConfiguration, "-trt:RemoveAudioEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__RemoveAudioEncoderConfiguration(soap, soap_tmp___trt__RemoveAudioEncoderConfiguration.trt__RemoveAudioEncoderConfiguration, &trt__RemoveAudioEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__RemoveAudioEncoderConfigurationResponse(soap, &trt__RemoveAudioEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__RemoveAudioEncoderConfigurationResponse(soap, &trt__RemoveAudioEncoderConfigurationResponse, "trt:RemoveAudioEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__RemoveAudioEncoderConfigurationResponse(soap, &trt__RemoveAudioEncoderConfigurationResponse, "trt:RemoveAudioEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__RemoveAudioSourceConfiguration(struct soap *soap)
{	struct __trt__RemoveAudioSourceConfiguration soap_tmp___trt__RemoveAudioSourceConfiguration;
	struct _trt__RemoveAudioSourceConfigurationResponse trt__RemoveAudioSourceConfigurationResponse;
	soap_default__trt__RemoveAudioSourceConfigurationResponse(soap, &trt__RemoveAudioSourceConfigurationResponse);
	soap_default___trt__RemoveAudioSourceConfiguration(soap, &soap_tmp___trt__RemoveAudioSourceConfiguration);
	if (!soap_get___trt__RemoveAudioSourceConfiguration(soap, &soap_tmp___trt__RemoveAudioSourceConfiguration, "-trt:RemoveAudioSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__RemoveAudioSourceConfiguration(soap, soap_tmp___trt__RemoveAudioSourceConfiguration.trt__RemoveAudioSourceConfiguration, &trt__RemoveAudioSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__RemoveAudioSourceConfigurationResponse(soap, &trt__RemoveAudioSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__RemoveAudioSourceConfigurationResponse(soap, &trt__RemoveAudioSourceConfigurationResponse, "trt:RemoveAudioSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__RemoveAudioSourceConfigurationResponse(soap, &trt__RemoveAudioSourceConfigurationResponse, "trt:RemoveAudioSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__RemovePTZConfiguration(struct soap *soap)
{	struct __trt__RemovePTZConfiguration soap_tmp___trt__RemovePTZConfiguration;
	struct _trt__RemovePTZConfigurationResponse trt__RemovePTZConfigurationResponse;
	soap_default__trt__RemovePTZConfigurationResponse(soap, &trt__RemovePTZConfigurationResponse);
	soap_default___trt__RemovePTZConfiguration(soap, &soap_tmp___trt__RemovePTZConfiguration);
	if (!soap_get___trt__RemovePTZConfiguration(soap, &soap_tmp___trt__RemovePTZConfiguration, "-trt:RemovePTZConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__RemovePTZConfiguration(soap, soap_tmp___trt__RemovePTZConfiguration.trt__RemovePTZConfiguration, &trt__RemovePTZConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__RemovePTZConfigurationResponse(soap, &trt__RemovePTZConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__RemovePTZConfigurationResponse(soap, &trt__RemovePTZConfigurationResponse, "trt:RemovePTZConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__RemovePTZConfigurationResponse(soap, &trt__RemovePTZConfigurationResponse, "trt:RemovePTZConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__RemoveMetadataConfiguration(struct soap *soap)
{	struct __trt__RemoveMetadataConfiguration soap_tmp___trt__RemoveMetadataConfiguration;
	struct _trt__RemoveMetadataConfigurationResponse trt__RemoveMetadataConfigurationResponse;
	soap_default__trt__RemoveMetadataConfigurationResponse(soap, &trt__RemoveMetadataConfigurationResponse);
	soap_default___trt__RemoveMetadataConfiguration(soap, &soap_tmp___trt__RemoveMetadataConfiguration);
	if (!soap_get___trt__RemoveMetadataConfiguration(soap, &soap_tmp___trt__RemoveMetadataConfiguration, "-trt:RemoveMetadataConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__RemoveMetadataConfiguration(soap, soap_tmp___trt__RemoveMetadataConfiguration.trt__RemoveMetadataConfiguration, &trt__RemoveMetadataConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__RemoveMetadataConfigurationResponse(soap, &trt__RemoveMetadataConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__RemoveMetadataConfigurationResponse(soap, &trt__RemoveMetadataConfigurationResponse, "trt:RemoveMetadataConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__RemoveMetadataConfigurationResponse(soap, &trt__RemoveMetadataConfigurationResponse, "trt:RemoveMetadataConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__DeleteProfile(struct soap *soap)
{	struct __trt__DeleteProfile soap_tmp___trt__DeleteProfile;
	struct _trt__DeleteProfileResponse trt__DeleteProfileResponse;
	soap_default__trt__DeleteProfileResponse(soap, &trt__DeleteProfileResponse);
	soap_default___trt__DeleteProfile(soap, &soap_tmp___trt__DeleteProfile);
	if (!soap_get___trt__DeleteProfile(soap, &soap_tmp___trt__DeleteProfile, "-trt:DeleteProfile", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__DeleteProfile(soap, soap_tmp___trt__DeleteProfile.trt__DeleteProfile, &trt__DeleteProfileResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__DeleteProfileResponse(soap, &trt__DeleteProfileResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__DeleteProfileResponse(soap, &trt__DeleteProfileResponse, "trt:DeleteProfileResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__DeleteProfileResponse(soap, &trt__DeleteProfileResponse, "trt:DeleteProfileResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetVideoSourceConfigurations(struct soap *soap)
{	struct __trt__GetVideoSourceConfigurations soap_tmp___trt__GetVideoSourceConfigurations;
	struct _trt__GetVideoSourceConfigurationsResponse trt__GetVideoSourceConfigurationsResponse;
	soap_default__trt__GetVideoSourceConfigurationsResponse(soap, &trt__GetVideoSourceConfigurationsResponse);
	soap_default___trt__GetVideoSourceConfigurations(soap, &soap_tmp___trt__GetVideoSourceConfigurations);
	if (!soap_get___trt__GetVideoSourceConfigurations(soap, &soap_tmp___trt__GetVideoSourceConfigurations, "-trt:GetVideoSourceConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetVideoSourceConfigurations(soap, soap_tmp___trt__GetVideoSourceConfigurations.trt__GetVideoSourceConfigurations, &trt__GetVideoSourceConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetVideoSourceConfigurationsResponse(soap, &trt__GetVideoSourceConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetVideoSourceConfigurationsResponse(soap, &trt__GetVideoSourceConfigurationsResponse, "trt:GetVideoSourceConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetVideoSourceConfigurationsResponse(soap, &trt__GetVideoSourceConfigurationsResponse, "trt:GetVideoSourceConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetVideoEncoderConfigurations(struct soap *soap)
{	struct __trt__GetVideoEncoderConfigurations soap_tmp___trt__GetVideoEncoderConfigurations;
	struct _trt__GetVideoEncoderConfigurationsResponse trt__GetVideoEncoderConfigurationsResponse;
	soap_default__trt__GetVideoEncoderConfigurationsResponse(soap, &trt__GetVideoEncoderConfigurationsResponse);
	soap_default___trt__GetVideoEncoderConfigurations(soap, &soap_tmp___trt__GetVideoEncoderConfigurations);
	if (!soap_get___trt__GetVideoEncoderConfigurations(soap, &soap_tmp___trt__GetVideoEncoderConfigurations, "-trt:GetVideoEncoderConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetVideoEncoderConfigurations(soap, soap_tmp___trt__GetVideoEncoderConfigurations.trt__GetVideoEncoderConfigurations, &trt__GetVideoEncoderConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetVideoEncoderConfigurationsResponse(soap, &trt__GetVideoEncoderConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetVideoEncoderConfigurationsResponse(soap, &trt__GetVideoEncoderConfigurationsResponse, "trt:GetVideoEncoderConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetVideoEncoderConfigurationsResponse(soap, &trt__GetVideoEncoderConfigurationsResponse, "trt:GetVideoEncoderConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetAudioSourceConfigurations(struct soap *soap)
{	struct __trt__GetAudioSourceConfigurations soap_tmp___trt__GetAudioSourceConfigurations;
	struct _trt__GetAudioSourceConfigurationsResponse trt__GetAudioSourceConfigurationsResponse;
	soap_default__trt__GetAudioSourceConfigurationsResponse(soap, &trt__GetAudioSourceConfigurationsResponse);
	soap_default___trt__GetAudioSourceConfigurations(soap, &soap_tmp___trt__GetAudioSourceConfigurations);
	if (!soap_get___trt__GetAudioSourceConfigurations(soap, &soap_tmp___trt__GetAudioSourceConfigurations, "-trt:GetAudioSourceConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetAudioSourceConfigurations(soap, soap_tmp___trt__GetAudioSourceConfigurations.trt__GetAudioSourceConfigurations, &trt__GetAudioSourceConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetAudioSourceConfigurationsResponse(soap, &trt__GetAudioSourceConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetAudioSourceConfigurationsResponse(soap, &trt__GetAudioSourceConfigurationsResponse, "trt:GetAudioSourceConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetAudioSourceConfigurationsResponse(soap, &trt__GetAudioSourceConfigurationsResponse, "trt:GetAudioSourceConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetAudioEncoderConfigurations(struct soap *soap)
{	struct __trt__GetAudioEncoderConfigurations soap_tmp___trt__GetAudioEncoderConfigurations;
	struct _trt__GetAudioEncoderConfigurationsResponse trt__GetAudioEncoderConfigurationsResponse;
	soap_default__trt__GetAudioEncoderConfigurationsResponse(soap, &trt__GetAudioEncoderConfigurationsResponse);
	soap_default___trt__GetAudioEncoderConfigurations(soap, &soap_tmp___trt__GetAudioEncoderConfigurations);
	if (!soap_get___trt__GetAudioEncoderConfigurations(soap, &soap_tmp___trt__GetAudioEncoderConfigurations, "-trt:GetAudioEncoderConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetAudioEncoderConfigurations(soap, soap_tmp___trt__GetAudioEncoderConfigurations.trt__GetAudioEncoderConfigurations, &trt__GetAudioEncoderConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetAudioEncoderConfigurationsResponse(soap, &trt__GetAudioEncoderConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetAudioEncoderConfigurationsResponse(soap, &trt__GetAudioEncoderConfigurationsResponse, "trt:GetAudioEncoderConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetAudioEncoderConfigurationsResponse(soap, &trt__GetAudioEncoderConfigurationsResponse, "trt:GetAudioEncoderConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetMetadataConfigurations(struct soap *soap)
{	struct __trt__GetMetadataConfigurations soap_tmp___trt__GetMetadataConfigurations;
	struct _trt__GetMetadataConfigurationsResponse trt__GetMetadataConfigurationsResponse;
	soap_default__trt__GetMetadataConfigurationsResponse(soap, &trt__GetMetadataConfigurationsResponse);
	soap_default___trt__GetMetadataConfigurations(soap, &soap_tmp___trt__GetMetadataConfigurations);
	if (!soap_get___trt__GetMetadataConfigurations(soap, &soap_tmp___trt__GetMetadataConfigurations, "-trt:GetMetadataConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetMetadataConfigurations(soap, soap_tmp___trt__GetMetadataConfigurations.trt__GetMetadataConfigurations, &trt__GetMetadataConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetMetadataConfigurationsResponse(soap, &trt__GetMetadataConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetMetadataConfigurationsResponse(soap, &trt__GetMetadataConfigurationsResponse, "trt:GetMetadataConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetMetadataConfigurationsResponse(soap, &trt__GetMetadataConfigurationsResponse, "trt:GetMetadataConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetVideoSourceConfiguration(struct soap *soap)
{	struct __trt__GetVideoSourceConfiguration soap_tmp___trt__GetVideoSourceConfiguration;
	struct _trt__GetVideoSourceConfigurationResponse trt__GetVideoSourceConfigurationResponse;
	soap_default__trt__GetVideoSourceConfigurationResponse(soap, &trt__GetVideoSourceConfigurationResponse);
	soap_default___trt__GetVideoSourceConfiguration(soap, &soap_tmp___trt__GetVideoSourceConfiguration);
	if (!soap_get___trt__GetVideoSourceConfiguration(soap, &soap_tmp___trt__GetVideoSourceConfiguration, "-trt:GetVideoSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetVideoSourceConfiguration(soap, soap_tmp___trt__GetVideoSourceConfiguration.trt__GetVideoSourceConfiguration, &trt__GetVideoSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetVideoSourceConfigurationResponse(soap, &trt__GetVideoSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetVideoSourceConfigurationResponse(soap, &trt__GetVideoSourceConfigurationResponse, "trt:GetVideoSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetVideoSourceConfigurationResponse(soap, &trt__GetVideoSourceConfigurationResponse, "trt:GetVideoSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetVideoEncoderConfiguration(struct soap *soap)
{	struct __trt__GetVideoEncoderConfiguration soap_tmp___trt__GetVideoEncoderConfiguration;
	struct _trt__GetVideoEncoderConfigurationResponse trt__GetVideoEncoderConfigurationResponse;
	soap_default__trt__GetVideoEncoderConfigurationResponse(soap, &trt__GetVideoEncoderConfigurationResponse);
	soap_default___trt__GetVideoEncoderConfiguration(soap, &soap_tmp___trt__GetVideoEncoderConfiguration);
	if (!soap_get___trt__GetVideoEncoderConfiguration(soap, &soap_tmp___trt__GetVideoEncoderConfiguration, "-trt:GetVideoEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetVideoEncoderConfiguration(soap, soap_tmp___trt__GetVideoEncoderConfiguration.trt__GetVideoEncoderConfiguration, &trt__GetVideoEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetVideoEncoderConfigurationResponse(soap, &trt__GetVideoEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetVideoEncoderConfigurationResponse(soap, &trt__GetVideoEncoderConfigurationResponse, "trt:GetVideoEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetVideoEncoderConfigurationResponse(soap, &trt__GetVideoEncoderConfigurationResponse, "trt:GetVideoEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetAudioSourceConfiguration(struct soap *soap)
{	struct __trt__GetAudioSourceConfiguration soap_tmp___trt__GetAudioSourceConfiguration;
	struct _trt__GetAudioSourceConfigurationResponse trt__GetAudioSourceConfigurationResponse;
	soap_default__trt__GetAudioSourceConfigurationResponse(soap, &trt__GetAudioSourceConfigurationResponse);
	soap_default___trt__GetAudioSourceConfiguration(soap, &soap_tmp___trt__GetAudioSourceConfiguration);
	if (!soap_get___trt__GetAudioSourceConfiguration(soap, &soap_tmp___trt__GetAudioSourceConfiguration, "-trt:GetAudioSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetAudioSourceConfiguration(soap, soap_tmp___trt__GetAudioSourceConfiguration.trt__GetAudioSourceConfiguration, &trt__GetAudioSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetAudioSourceConfigurationResponse(soap, &trt__GetAudioSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetAudioSourceConfigurationResponse(soap, &trt__GetAudioSourceConfigurationResponse, "trt:GetAudioSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetAudioSourceConfigurationResponse(soap, &trt__GetAudioSourceConfigurationResponse, "trt:GetAudioSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetAudioEncoderConfiguration(struct soap *soap)
{	struct __trt__GetAudioEncoderConfiguration soap_tmp___trt__GetAudioEncoderConfiguration;
	struct _trt__GetAudioEncoderConfigurationResponse trt__GetAudioEncoderConfigurationResponse;
	soap_default__trt__GetAudioEncoderConfigurationResponse(soap, &trt__GetAudioEncoderConfigurationResponse);
	soap_default___trt__GetAudioEncoderConfiguration(soap, &soap_tmp___trt__GetAudioEncoderConfiguration);
	if (!soap_get___trt__GetAudioEncoderConfiguration(soap, &soap_tmp___trt__GetAudioEncoderConfiguration, "-trt:GetAudioEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetAudioEncoderConfiguration(soap, soap_tmp___trt__GetAudioEncoderConfiguration.trt__GetAudioEncoderConfiguration, &trt__GetAudioEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetAudioEncoderConfigurationResponse(soap, &trt__GetAudioEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetAudioEncoderConfigurationResponse(soap, &trt__GetAudioEncoderConfigurationResponse, "trt:GetAudioEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetAudioEncoderConfigurationResponse(soap, &trt__GetAudioEncoderConfigurationResponse, "trt:GetAudioEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetMetadataConfiguration(struct soap *soap)
{	struct __trt__GetMetadataConfiguration soap_tmp___trt__GetMetadataConfiguration;
	struct _trt__GetMetadataConfigurationResponse trt__GetMetadataConfigurationResponse;
	soap_default__trt__GetMetadataConfigurationResponse(soap, &trt__GetMetadataConfigurationResponse);
	soap_default___trt__GetMetadataConfiguration(soap, &soap_tmp___trt__GetMetadataConfiguration);
	if (!soap_get___trt__GetMetadataConfiguration(soap, &soap_tmp___trt__GetMetadataConfiguration, "-trt:GetMetadataConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetMetadataConfiguration(soap, soap_tmp___trt__GetMetadataConfiguration.trt__GetMetadataConfiguration, &trt__GetMetadataConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetMetadataConfigurationResponse(soap, &trt__GetMetadataConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetMetadataConfigurationResponse(soap, &trt__GetMetadataConfigurationResponse, "trt:GetMetadataConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetMetadataConfigurationResponse(soap, &trt__GetMetadataConfigurationResponse, "trt:GetMetadataConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetCompatibleVideoEncoderConfigurations(struct soap *soap)
{	struct __trt__GetCompatibleVideoEncoderConfigurations soap_tmp___trt__GetCompatibleVideoEncoderConfigurations;
	struct _trt__GetCompatibleVideoEncoderConfigurationsResponse trt__GetCompatibleVideoEncoderConfigurationsResponse;
	soap_default__trt__GetCompatibleVideoEncoderConfigurationsResponse(soap, &trt__GetCompatibleVideoEncoderConfigurationsResponse);
	soap_default___trt__GetCompatibleVideoEncoderConfigurations(soap, &soap_tmp___trt__GetCompatibleVideoEncoderConfigurations);
	if (!soap_get___trt__GetCompatibleVideoEncoderConfigurations(soap, &soap_tmp___trt__GetCompatibleVideoEncoderConfigurations, "-trt:GetCompatibleVideoEncoderConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetCompatibleVideoEncoderConfigurations(soap, soap_tmp___trt__GetCompatibleVideoEncoderConfigurations.trt__GetCompatibleVideoEncoderConfigurations, &trt__GetCompatibleVideoEncoderConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetCompatibleVideoEncoderConfigurationsResponse(soap, &trt__GetCompatibleVideoEncoderConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetCompatibleVideoEncoderConfigurationsResponse(soap, &trt__GetCompatibleVideoEncoderConfigurationsResponse, "trt:GetCompatibleVideoEncoderConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetCompatibleVideoEncoderConfigurationsResponse(soap, &trt__GetCompatibleVideoEncoderConfigurationsResponse, "trt:GetCompatibleVideoEncoderConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetCompatibleVideoSourceConfigurations(struct soap *soap)
{	struct __trt__GetCompatibleVideoSourceConfigurations soap_tmp___trt__GetCompatibleVideoSourceConfigurations;
	struct _trt__GetCompatibleVideoSourceConfigurationsResponse trt__GetCompatibleVideoSourceConfigurationsResponse;
	soap_default__trt__GetCompatibleVideoSourceConfigurationsResponse(soap, &trt__GetCompatibleVideoSourceConfigurationsResponse);
	soap_default___trt__GetCompatibleVideoSourceConfigurations(soap, &soap_tmp___trt__GetCompatibleVideoSourceConfigurations);
	if (!soap_get___trt__GetCompatibleVideoSourceConfigurations(soap, &soap_tmp___trt__GetCompatibleVideoSourceConfigurations, "-trt:GetCompatibleVideoSourceConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetCompatibleVideoSourceConfigurations(soap, soap_tmp___trt__GetCompatibleVideoSourceConfigurations.trt__GetCompatibleVideoSourceConfigurations, &trt__GetCompatibleVideoSourceConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetCompatibleVideoSourceConfigurationsResponse(soap, &trt__GetCompatibleVideoSourceConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetCompatibleVideoSourceConfigurationsResponse(soap, &trt__GetCompatibleVideoSourceConfigurationsResponse, "trt:GetCompatibleVideoSourceConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetCompatibleVideoSourceConfigurationsResponse(soap, &trt__GetCompatibleVideoSourceConfigurationsResponse, "trt:GetCompatibleVideoSourceConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetCompatibleAudioEncoderConfigurations(struct soap *soap)
{	struct __trt__GetCompatibleAudioEncoderConfigurations soap_tmp___trt__GetCompatibleAudioEncoderConfigurations;
	struct _trt__GetCompatibleAudioEncoderConfigurationsResponse trt__GetCompatibleAudioEncoderConfigurationsResponse;
	soap_default__trt__GetCompatibleAudioEncoderConfigurationsResponse(soap, &trt__GetCompatibleAudioEncoderConfigurationsResponse);
	soap_default___trt__GetCompatibleAudioEncoderConfigurations(soap, &soap_tmp___trt__GetCompatibleAudioEncoderConfigurations);
	if (!soap_get___trt__GetCompatibleAudioEncoderConfigurations(soap, &soap_tmp___trt__GetCompatibleAudioEncoderConfigurations, "-trt:GetCompatibleAudioEncoderConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetCompatibleAudioEncoderConfigurations(soap, soap_tmp___trt__GetCompatibleAudioEncoderConfigurations.trt__GetCompatibleAudioEncoderConfigurations, &trt__GetCompatibleAudioEncoderConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetCompatibleAudioEncoderConfigurationsResponse(soap, &trt__GetCompatibleAudioEncoderConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetCompatibleAudioEncoderConfigurationsResponse(soap, &trt__GetCompatibleAudioEncoderConfigurationsResponse, "trt:GetCompatibleAudioEncoderConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetCompatibleAudioEncoderConfigurationsResponse(soap, &trt__GetCompatibleAudioEncoderConfigurationsResponse, "trt:GetCompatibleAudioEncoderConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetCompatibleAudioSourceConfigurations(struct soap *soap)
{	struct __trt__GetCompatibleAudioSourceConfigurations soap_tmp___trt__GetCompatibleAudioSourceConfigurations;
	struct _trt__GetCompatibleAudioSourceConfigurationsResponse trt__GetCompatibleAudioSourceConfigurationsResponse;
	soap_default__trt__GetCompatibleAudioSourceConfigurationsResponse(soap, &trt__GetCompatibleAudioSourceConfigurationsResponse);
	soap_default___trt__GetCompatibleAudioSourceConfigurations(soap, &soap_tmp___trt__GetCompatibleAudioSourceConfigurations);
	if (!soap_get___trt__GetCompatibleAudioSourceConfigurations(soap, &soap_tmp___trt__GetCompatibleAudioSourceConfigurations, "-trt:GetCompatibleAudioSourceConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetCompatibleAudioSourceConfigurations(soap, soap_tmp___trt__GetCompatibleAudioSourceConfigurations.trt__GetCompatibleAudioSourceConfigurations, &trt__GetCompatibleAudioSourceConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetCompatibleAudioSourceConfigurationsResponse(soap, &trt__GetCompatibleAudioSourceConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetCompatibleAudioSourceConfigurationsResponse(soap, &trt__GetCompatibleAudioSourceConfigurationsResponse, "trt:GetCompatibleAudioSourceConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetCompatibleAudioSourceConfigurationsResponse(soap, &trt__GetCompatibleAudioSourceConfigurationsResponse, "trt:GetCompatibleAudioSourceConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetCompatibleMetadataConfigurations(struct soap *soap)
{	struct __trt__GetCompatibleMetadataConfigurations soap_tmp___trt__GetCompatibleMetadataConfigurations;
	struct _trt__GetCompatibleMetadataConfigurationsResponse trt__GetCompatibleMetadataConfigurationsResponse;
	soap_default__trt__GetCompatibleMetadataConfigurationsResponse(soap, &trt__GetCompatibleMetadataConfigurationsResponse);
	soap_default___trt__GetCompatibleMetadataConfigurations(soap, &soap_tmp___trt__GetCompatibleMetadataConfigurations);
	if (!soap_get___trt__GetCompatibleMetadataConfigurations(soap, &soap_tmp___trt__GetCompatibleMetadataConfigurations, "-trt:GetCompatibleMetadataConfigurations", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetCompatibleMetadataConfigurations(soap, soap_tmp___trt__GetCompatibleMetadataConfigurations.trt__GetCompatibleMetadataConfigurations, &trt__GetCompatibleMetadataConfigurationsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetCompatibleMetadataConfigurationsResponse(soap, &trt__GetCompatibleMetadataConfigurationsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetCompatibleMetadataConfigurationsResponse(soap, &trt__GetCompatibleMetadataConfigurationsResponse, "trt:GetCompatibleMetadataConfigurationsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetCompatibleMetadataConfigurationsResponse(soap, &trt__GetCompatibleMetadataConfigurationsResponse, "trt:GetCompatibleMetadataConfigurationsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__SetVideoSourceConfiguration(struct soap *soap)
{	struct __trt__SetVideoSourceConfiguration soap_tmp___trt__SetVideoSourceConfiguration;
	struct _trt__SetVideoSourceConfigurationResponse trt__SetVideoSourceConfigurationResponse;
	soap_default__trt__SetVideoSourceConfigurationResponse(soap, &trt__SetVideoSourceConfigurationResponse);
	soap_default___trt__SetVideoSourceConfiguration(soap, &soap_tmp___trt__SetVideoSourceConfiguration);
	if (!soap_get___trt__SetVideoSourceConfiguration(soap, &soap_tmp___trt__SetVideoSourceConfiguration, "-trt:SetVideoSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__SetVideoSourceConfiguration(soap, soap_tmp___trt__SetVideoSourceConfiguration.trt__SetVideoSourceConfiguration, &trt__SetVideoSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__SetVideoSourceConfigurationResponse(soap, &trt__SetVideoSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__SetVideoSourceConfigurationResponse(soap, &trt__SetVideoSourceConfigurationResponse, "trt:SetVideoSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__SetVideoSourceConfigurationResponse(soap, &trt__SetVideoSourceConfigurationResponse, "trt:SetVideoSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__SetVideoEncoderConfiguration(struct soap *soap)
{	struct __trt__SetVideoEncoderConfiguration soap_tmp___trt__SetVideoEncoderConfiguration;
	struct _trt__SetVideoEncoderConfigurationResponse trt__SetVideoEncoderConfigurationResponse;
	soap_default__trt__SetVideoEncoderConfigurationResponse(soap, &trt__SetVideoEncoderConfigurationResponse);
	soap_default___trt__SetVideoEncoderConfiguration(soap, &soap_tmp___trt__SetVideoEncoderConfiguration);
	if (!soap_get___trt__SetVideoEncoderConfiguration(soap, &soap_tmp___trt__SetVideoEncoderConfiguration, "-trt:SetVideoEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__SetVideoEncoderConfiguration(soap, soap_tmp___trt__SetVideoEncoderConfiguration.trt__SetVideoEncoderConfiguration, &trt__SetVideoEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__SetVideoEncoderConfigurationResponse(soap, &trt__SetVideoEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__SetVideoEncoderConfigurationResponse(soap, &trt__SetVideoEncoderConfigurationResponse, "trt:SetVideoEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__SetVideoEncoderConfigurationResponse(soap, &trt__SetVideoEncoderConfigurationResponse, "trt:SetVideoEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__SetAudioSourceConfiguration(struct soap *soap)
{	struct __trt__SetAudioSourceConfiguration soap_tmp___trt__SetAudioSourceConfiguration;
	struct _trt__SetAudioSourceConfigurationResponse trt__SetAudioSourceConfigurationResponse;
	soap_default__trt__SetAudioSourceConfigurationResponse(soap, &trt__SetAudioSourceConfigurationResponse);
	soap_default___trt__SetAudioSourceConfiguration(soap, &soap_tmp___trt__SetAudioSourceConfiguration);
	if (!soap_get___trt__SetAudioSourceConfiguration(soap, &soap_tmp___trt__SetAudioSourceConfiguration, "-trt:SetAudioSourceConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__SetAudioSourceConfiguration(soap, soap_tmp___trt__SetAudioSourceConfiguration.trt__SetAudioSourceConfiguration, &trt__SetAudioSourceConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__SetAudioSourceConfigurationResponse(soap, &trt__SetAudioSourceConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__SetAudioSourceConfigurationResponse(soap, &trt__SetAudioSourceConfigurationResponse, "trt:SetAudioSourceConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__SetAudioSourceConfigurationResponse(soap, &trt__SetAudioSourceConfigurationResponse, "trt:SetAudioSourceConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__SetAudioEncoderConfiguration(struct soap *soap)
{	struct __trt__SetAudioEncoderConfiguration soap_tmp___trt__SetAudioEncoderConfiguration;
	struct _trt__SetAudioEncoderConfigurationResponse trt__SetAudioEncoderConfigurationResponse;
	soap_default__trt__SetAudioEncoderConfigurationResponse(soap, &trt__SetAudioEncoderConfigurationResponse);
	soap_default___trt__SetAudioEncoderConfiguration(soap, &soap_tmp___trt__SetAudioEncoderConfiguration);
	if (!soap_get___trt__SetAudioEncoderConfiguration(soap, &soap_tmp___trt__SetAudioEncoderConfiguration, "-trt:SetAudioEncoderConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__SetAudioEncoderConfiguration(soap, soap_tmp___trt__SetAudioEncoderConfiguration.trt__SetAudioEncoderConfiguration, &trt__SetAudioEncoderConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__SetAudioEncoderConfigurationResponse(soap, &trt__SetAudioEncoderConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__SetAudioEncoderConfigurationResponse(soap, &trt__SetAudioEncoderConfigurationResponse, "trt:SetAudioEncoderConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__SetAudioEncoderConfigurationResponse(soap, &trt__SetAudioEncoderConfigurationResponse, "trt:SetAudioEncoderConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__SetMetadataConfiguration(struct soap *soap)
{	struct __trt__SetMetadataConfiguration soap_tmp___trt__SetMetadataConfiguration;
	struct _trt__SetMetadataConfigurationResponse trt__SetMetadataConfigurationResponse;
	soap_default__trt__SetMetadataConfigurationResponse(soap, &trt__SetMetadataConfigurationResponse);
	soap_default___trt__SetMetadataConfiguration(soap, &soap_tmp___trt__SetMetadataConfiguration);
	if (!soap_get___trt__SetMetadataConfiguration(soap, &soap_tmp___trt__SetMetadataConfiguration, "-trt:SetMetadataConfiguration", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__SetMetadataConfiguration(soap, soap_tmp___trt__SetMetadataConfiguration.trt__SetMetadataConfiguration, &trt__SetMetadataConfigurationResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__SetMetadataConfigurationResponse(soap, &trt__SetMetadataConfigurationResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__SetMetadataConfigurationResponse(soap, &trt__SetMetadataConfigurationResponse, "trt:SetMetadataConfigurationResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__SetMetadataConfigurationResponse(soap, &trt__SetMetadataConfigurationResponse, "trt:SetMetadataConfigurationResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetVideoSourceConfigurationOptions(struct soap *soap)
{	struct __trt__GetVideoSourceConfigurationOptions soap_tmp___trt__GetVideoSourceConfigurationOptions;
	struct _trt__GetVideoSourceConfigurationOptionsResponse trt__GetVideoSourceConfigurationOptionsResponse;
	soap_default__trt__GetVideoSourceConfigurationOptionsResponse(soap, &trt__GetVideoSourceConfigurationOptionsResponse);
	soap_default___trt__GetVideoSourceConfigurationOptions(soap, &soap_tmp___trt__GetVideoSourceConfigurationOptions);
	if (!soap_get___trt__GetVideoSourceConfigurationOptions(soap, &soap_tmp___trt__GetVideoSourceConfigurationOptions, "-trt:GetVideoSourceConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetVideoSourceConfigurationOptions(soap, soap_tmp___trt__GetVideoSourceConfigurationOptions.trt__GetVideoSourceConfigurationOptions, &trt__GetVideoSourceConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetVideoSourceConfigurationOptionsResponse(soap, &trt__GetVideoSourceConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetVideoSourceConfigurationOptionsResponse(soap, &trt__GetVideoSourceConfigurationOptionsResponse, "trt:GetVideoSourceConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetVideoSourceConfigurationOptionsResponse(soap, &trt__GetVideoSourceConfigurationOptionsResponse, "trt:GetVideoSourceConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetVideoEncoderConfigurationOptions(struct soap *soap)
{	struct __trt__GetVideoEncoderConfigurationOptions soap_tmp___trt__GetVideoEncoderConfigurationOptions;
	struct _trt__GetVideoEncoderConfigurationOptionsResponse trt__GetVideoEncoderConfigurationOptionsResponse;
	soap_default__trt__GetVideoEncoderConfigurationOptionsResponse(soap, &trt__GetVideoEncoderConfigurationOptionsResponse);
	soap_default___trt__GetVideoEncoderConfigurationOptions(soap, &soap_tmp___trt__GetVideoEncoderConfigurationOptions);
	if (!soap_get___trt__GetVideoEncoderConfigurationOptions(soap, &soap_tmp___trt__GetVideoEncoderConfigurationOptions, "-trt:GetVideoEncoderConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetVideoEncoderConfigurationOptions(soap, soap_tmp___trt__GetVideoEncoderConfigurationOptions.trt__GetVideoEncoderConfigurationOptions, &trt__GetVideoEncoderConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetVideoEncoderConfigurationOptionsResponse(soap, &trt__GetVideoEncoderConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetVideoEncoderConfigurationOptionsResponse(soap, &trt__GetVideoEncoderConfigurationOptionsResponse, "trt:GetVideoEncoderConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetVideoEncoderConfigurationOptionsResponse(soap, &trt__GetVideoEncoderConfigurationOptionsResponse, "trt:GetVideoEncoderConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetAudioSourceConfigurationOptions(struct soap *soap)
{	struct __trt__GetAudioSourceConfigurationOptions soap_tmp___trt__GetAudioSourceConfigurationOptions;
	struct _trt__GetAudioSourceConfigurationOptionsResponse trt__GetAudioSourceConfigurationOptionsResponse;
	soap_default__trt__GetAudioSourceConfigurationOptionsResponse(soap, &trt__GetAudioSourceConfigurationOptionsResponse);
	soap_default___trt__GetAudioSourceConfigurationOptions(soap, &soap_tmp___trt__GetAudioSourceConfigurationOptions);
	if (!soap_get___trt__GetAudioSourceConfigurationOptions(soap, &soap_tmp___trt__GetAudioSourceConfigurationOptions, "-trt:GetAudioSourceConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetAudioSourceConfigurationOptions(soap, soap_tmp___trt__GetAudioSourceConfigurationOptions.trt__GetAudioSourceConfigurationOptions, &trt__GetAudioSourceConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetAudioSourceConfigurationOptionsResponse(soap, &trt__GetAudioSourceConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetAudioSourceConfigurationOptionsResponse(soap, &trt__GetAudioSourceConfigurationOptionsResponse, "trt:GetAudioSourceConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetAudioSourceConfigurationOptionsResponse(soap, &trt__GetAudioSourceConfigurationOptionsResponse, "trt:GetAudioSourceConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetAudioEncoderConfigurationOptions(struct soap *soap)
{	struct __trt__GetAudioEncoderConfigurationOptions soap_tmp___trt__GetAudioEncoderConfigurationOptions;
	struct _trt__GetAudioEncoderConfigurationOptionsResponse trt__GetAudioEncoderConfigurationOptionsResponse;
	soap_default__trt__GetAudioEncoderConfigurationOptionsResponse(soap, &trt__GetAudioEncoderConfigurationOptionsResponse);
	soap_default___trt__GetAudioEncoderConfigurationOptions(soap, &soap_tmp___trt__GetAudioEncoderConfigurationOptions);
	if (!soap_get___trt__GetAudioEncoderConfigurationOptions(soap, &soap_tmp___trt__GetAudioEncoderConfigurationOptions, "-trt:GetAudioEncoderConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetAudioEncoderConfigurationOptions(soap, soap_tmp___trt__GetAudioEncoderConfigurationOptions.trt__GetAudioEncoderConfigurationOptions, &trt__GetAudioEncoderConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetAudioEncoderConfigurationOptionsResponse(soap, &trt__GetAudioEncoderConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetAudioEncoderConfigurationOptionsResponse(soap, &trt__GetAudioEncoderConfigurationOptionsResponse, "trt:GetAudioEncoderConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetAudioEncoderConfigurationOptionsResponse(soap, &trt__GetAudioEncoderConfigurationOptionsResponse, "trt:GetAudioEncoderConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetMetadataConfigurationOptions(struct soap *soap)
{	struct __trt__GetMetadataConfigurationOptions soap_tmp___trt__GetMetadataConfigurationOptions;
	struct _trt__GetMetadataConfigurationOptionsResponse trt__GetMetadataConfigurationOptionsResponse;
	soap_default__trt__GetMetadataConfigurationOptionsResponse(soap, &trt__GetMetadataConfigurationOptionsResponse);
	soap_default___trt__GetMetadataConfigurationOptions(soap, &soap_tmp___trt__GetMetadataConfigurationOptions);
	if (!soap_get___trt__GetMetadataConfigurationOptions(soap, &soap_tmp___trt__GetMetadataConfigurationOptions, "-trt:GetMetadataConfigurationOptions", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetMetadataConfigurationOptions(soap, soap_tmp___trt__GetMetadataConfigurationOptions.trt__GetMetadataConfigurationOptions, &trt__GetMetadataConfigurationOptionsResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetMetadataConfigurationOptionsResponse(soap, &trt__GetMetadataConfigurationOptionsResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetMetadataConfigurationOptionsResponse(soap, &trt__GetMetadataConfigurationOptionsResponse, "trt:GetMetadataConfigurationOptionsResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetMetadataConfigurationOptionsResponse(soap, &trt__GetMetadataConfigurationOptionsResponse, "trt:GetMetadataConfigurationOptionsResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetGuaranteedNumberOfVideoEncoderInstances(struct soap *soap)
{	struct __trt__GetGuaranteedNumberOfVideoEncoderInstances soap_tmp___trt__GetGuaranteedNumberOfVideoEncoderInstances;
	struct _trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse;
	soap_default__trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse(soap, &trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse);
	soap_default___trt__GetGuaranteedNumberOfVideoEncoderInstances(soap, &soap_tmp___trt__GetGuaranteedNumberOfVideoEncoderInstances);
	if (!soap_get___trt__GetGuaranteedNumberOfVideoEncoderInstances(soap, &soap_tmp___trt__GetGuaranteedNumberOfVideoEncoderInstances, "-trt:GetGuaranteedNumberOfVideoEncoderInstances", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetGuaranteedNumberOfVideoEncoderInstances(soap, soap_tmp___trt__GetGuaranteedNumberOfVideoEncoderInstances.trt__GetGuaranteedNumberOfVideoEncoderInstances, &trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse(soap, &trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse(soap, &trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse, "trt:GetGuaranteedNumberOfVideoEncoderInstancesResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse(soap, &trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse, "trt:GetGuaranteedNumberOfVideoEncoderInstancesResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetStreamUri(struct soap *soap)
{	struct __trt__GetStreamUri soap_tmp___trt__GetStreamUri;
	struct _trt__GetStreamUriResponse trt__GetStreamUriResponse;
	soap_default__trt__GetStreamUriResponse(soap, &trt__GetStreamUriResponse);
	soap_default___trt__GetStreamUri(soap, &soap_tmp___trt__GetStreamUri);
	if (!soap_get___trt__GetStreamUri(soap, &soap_tmp___trt__GetStreamUri, "-trt:GetStreamUri", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetStreamUri(soap, soap_tmp___trt__GetStreamUri.trt__GetStreamUri, &trt__GetStreamUriResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetStreamUriResponse(soap, &trt__GetStreamUriResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetStreamUriResponse(soap, &trt__GetStreamUriResponse, "trt:GetStreamUriResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetStreamUriResponse(soap, &trt__GetStreamUriResponse, "trt:GetStreamUriResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__StartMulticastStreaming(struct soap *soap)
{	struct __trt__StartMulticastStreaming soap_tmp___trt__StartMulticastStreaming;
	struct _trt__StartMulticastStreamingResponse trt__StartMulticastStreamingResponse;
	soap_default__trt__StartMulticastStreamingResponse(soap, &trt__StartMulticastStreamingResponse);
	soap_default___trt__StartMulticastStreaming(soap, &soap_tmp___trt__StartMulticastStreaming);
	if (!soap_get___trt__StartMulticastStreaming(soap, &soap_tmp___trt__StartMulticastStreaming, "-trt:StartMulticastStreaming", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__StartMulticastStreaming(soap, soap_tmp___trt__StartMulticastStreaming.trt__StartMulticastStreaming, &trt__StartMulticastStreamingResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__StartMulticastStreamingResponse(soap, &trt__StartMulticastStreamingResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__StartMulticastStreamingResponse(soap, &trt__StartMulticastStreamingResponse, "trt:StartMulticastStreamingResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__StartMulticastStreamingResponse(soap, &trt__StartMulticastStreamingResponse, "trt:StartMulticastStreamingResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__StopMulticastStreaming(struct soap *soap)
{	struct __trt__StopMulticastStreaming soap_tmp___trt__StopMulticastStreaming;
	struct _trt__StopMulticastStreamingResponse trt__StopMulticastStreamingResponse;
	soap_default__trt__StopMulticastStreamingResponse(soap, &trt__StopMulticastStreamingResponse);
	soap_default___trt__StopMulticastStreaming(soap, &soap_tmp___trt__StopMulticastStreaming);
	if (!soap_get___trt__StopMulticastStreaming(soap, &soap_tmp___trt__StopMulticastStreaming, "-trt:StopMulticastStreaming", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__StopMulticastStreaming(soap, soap_tmp___trt__StopMulticastStreaming.trt__StopMulticastStreaming, &trt__StopMulticastStreamingResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__StopMulticastStreamingResponse(soap, &trt__StopMulticastStreamingResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__StopMulticastStreamingResponse(soap, &trt__StopMulticastStreamingResponse, "trt:StopMulticastStreamingResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__StopMulticastStreamingResponse(soap, &trt__StopMulticastStreamingResponse, "trt:StopMulticastStreamingResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__SetSynchronizationPoint(struct soap *soap)
{	struct __trt__SetSynchronizationPoint soap_tmp___trt__SetSynchronizationPoint;
	struct _trt__SetSynchronizationPointResponse trt__SetSynchronizationPointResponse;
	soap_default__trt__SetSynchronizationPointResponse(soap, &trt__SetSynchronizationPointResponse);
	soap_default___trt__SetSynchronizationPoint(soap, &soap_tmp___trt__SetSynchronizationPoint);
	if (!soap_get___trt__SetSynchronizationPoint(soap, &soap_tmp___trt__SetSynchronizationPoint, "-trt:SetSynchronizationPoint", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__SetSynchronizationPoint(soap, soap_tmp___trt__SetSynchronizationPoint.trt__SetSynchronizationPoint, &trt__SetSynchronizationPointResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__SetSynchronizationPointResponse(soap, &trt__SetSynchronizationPointResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__SetSynchronizationPointResponse(soap, &trt__SetSynchronizationPointResponse, "trt:SetSynchronizationPointResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__SetSynchronizationPointResponse(soap, &trt__SetSynchronizationPointResponse, "trt:SetSynchronizationPointResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

SOAP_FMAC5 int SOAP_FMAC6 soap_serve___trt__GetSnapshotUri(struct soap *soap)
{	struct __trt__GetSnapshotUri soap_tmp___trt__GetSnapshotUri;
	struct _trt__GetSnapshotUriResponse trt__GetSnapshotUriResponse;
	soap_default__trt__GetSnapshotUriResponse(soap, &trt__GetSnapshotUriResponse);
	soap_default___trt__GetSnapshotUri(soap, &soap_tmp___trt__GetSnapshotUri);
	if (!soap_get___trt__GetSnapshotUri(soap, &soap_tmp___trt__GetSnapshotUri, "-trt:GetSnapshotUri", NULL))
		return soap->error;
	if (soap_body_end_in(soap)
	 || soap_envelope_end_in(soap)
	 || soap_end_recv(soap))
		return soap->error;
	soap->error = __trt__GetSnapshotUri(soap, soap_tmp___trt__GetSnapshotUri.trt__GetSnapshotUri, &trt__GetSnapshotUriResponse);
	if (soap->error)
		return soap->error;
	soap->encodingStyle = NULL;
	soap_serializeheader(soap);
	soap_serialize__trt__GetSnapshotUriResponse(soap, &trt__GetSnapshotUriResponse);
	if (soap_begin_count(soap))
		return soap->error;
	if (soap->mode & SOAP_IO_LENGTH)
	{	if (soap_envelope_begin_out(soap)
		 || soap_putheader(soap)
		 || soap_body_begin_out(soap)
		 || soap_put__trt__GetSnapshotUriResponse(soap, &trt__GetSnapshotUriResponse, "trt:GetSnapshotUriResponse", "")
		 || soap_body_end_out(soap)
		 || soap_envelope_end_out(soap))
			 return soap->error;
	};
	if (soap_end_count(soap)
	 || soap_response(soap, SOAP_OK)
	 || soap_envelope_begin_out(soap)
	 || soap_putheader(soap)
	 || soap_body_begin_out(soap)
	 || soap_put__trt__GetSnapshotUriResponse(soap, &trt__GetSnapshotUriResponse, "trt:GetSnapshotUriResponse", "")
	 || soap_body_end_out(soap)
	 || soap_envelope_end_out(soap)
	 || soap_end_send(soap))
		return soap->error;
	return soap_closesock(soap);
}

#ifdef __cplusplus
}
#endif

#if defined(__BORLANDC__)
#pragma option pop
#pragma option pop
#endif

/* End of soapServer.c */
