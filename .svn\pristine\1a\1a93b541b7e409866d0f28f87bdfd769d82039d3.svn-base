#! /bin/sh

hwclock -s
insmod /root/ko/extdrv/mtdex.ko
insmod /root/ko/extdrv/rndis_host.ko
fs_write_enable 0
#cd -
#ulimit -c 10240
#echo "/var/core.%e" > /proc/sys/kernel/core_pattern

umount -lf /mnt/sdcard/ 1>/dev/null 2>/dev/null
rm /mnt/* -r
mount -t tmpfs -o size=512k tmpfs /mnt/
mkdir /mnt/sdcard
mkdir /mnt/udisk
cp -rf /var /tmp/
mount -t tmpfs -o size=10M tmpfs /var/ 
sleep 0.1
mv /tmp/var/* /var/
touch /var/mainStream
touch /var/subStream
touch /var/audStream
mkdir /var/nfs
mkdir /var/snap
mkdir /var/warn
mkdir /var/info
mkdir /var/lock
mkdir /var/log
mkdir /var/log/uploaded 
mkdir /var/run
mkdir /var/mounting

export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:.:/usr/lib/
cp /etc/config.xml /var/config.xml
tar -zxf /root/webui.tar.gz -C /var/
fw_printenv | grep hardwareVersion
if [ $? -ne 0 ];then
	fw_setenv hardwareVersion $(cat /etc/hardwareVersion)
fi

telnetd &

/etc/init.d/S49alsa start
#/root/gpio_enable.sh
/root/ipsys >> /dev/null &
#sleep 3
/root/alg >> /dev/null &
/root/wtd.sh >> /dev/null &
/root/sys_wtd.sh >> /dev/null &
