/******************************************************************************
Copyright (C) 2024-2025 广州敏视数码科技有限公司版权所有.

文件名：plug.cpp

日期: 2024-02-22

文件功能描述: 定义算法插件模块功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <sys/statvfs.h>
#include <pthread.h>
#include <ctype.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <dirent.h>
#include <dlfcn.h>

#include "print.h"
#include "../../../include/common.h"
#include "safefunc.h"
#include "op.h"
#include "msg.h"
#include "alarm.h"
#include "avalarmer.h"
#include "led.h"
#include "plug.h"
#include "plug_com.h"
#include "alg.h"

#include "config.h"
#include "media.h"
#include "media_sem.h"
#include "media_shm.h"
#include "cJSON.h"
#include "utils.h"
#include "board.h"
#include "thpool.h"

#define PLUG_IMAGE_WIDTH      608           /* 算法图像帧宽度 */
#define PLUG_IMAGE_HEIGHT     352           /* 算法图像帧高度 */

/* 插件控制接口结构体 */
typedef struct tagPlugCtrlApi_S
{
    SV_BOOL         bValid;                 /* 插件是否有效 */
    SV_BOOL         bEnable;                /* 插件是否使能运行 */
    SV_BOOL         bInited;                /* 插件是否已初始化 */
    SV_BOOL         bRunning;               /* 插件是否正在运行 */
    sint32          s32Interval;            /* 插件运行间隔(秒) */
    char            szPlugPath[256];        /* 插件存放的绝对路径 */
    AlgPlugInfoT    stPlugInfo;             /* 插件信息 */
    void           *pvHandle;               /* 插件库句柄 */
    PLUG_RES_TYPE   enResType;              /* 推理结果类型 */
    PlugGetInfo     pfnPlugGetInfo;         /* 获取算法插件信息函数 */
    PlugCheckModel  pfnCheckModel;          /* 算法插件校验模型文件 */
    Plug_Init       pfnPlug_Init;           /* 算法插件初始化 */
    Plug_Fini       pfnPlug_Fini;           /* 算法插件去初始化 */
    Plug_ResType    pfnPlug_ResType;        /* 获取算法结果类型值 */
    Plug_Forward    pfnPlug_Forward;        /* 算法插件前推一帧图像 */
    Plug_GetResult  pfnPlug_GetResult;      /* 获取算法推理结果 */
    Plug_ConfigUpdate pfnPlug_ConfigUpdate; /* 算法配置参数发生变化 */
} PLUG_CTRL_API;

/* 模块控制信息 */
typedef struct tagPlugInfo_S
{
    SV_BOOL         bKeyAuth;               /* 密钥是否有效 */
    uint32          u32ChnNum;              /* PD算法通道数目 */
#if ALG_MUTLIT_BUFFER
    sint32          as32MediaBufFd[4][3];   /* 媒体通道Media Buffer的文件描述符 */
#else
    sint32          as32MediaBufFd[4];      /* 媒体通道Media Buffer的文件描述符 */
#endif
    uint32          u32Width;               /* 算法图片宽 */
    uint32          u32Height;              /* 算法图片高 */
    CFG_ALG_PARAM   stCfgParam;             /* 算法配置参数 */
    uint32          u32TidAlg;              /* 算法线程ID */
    uint32          u32TidStat;             /* 状态线程ID */
    SV_BOOL         bRunning;               /* 线程是否正在运行 */
    pthread_mutex_t mutexLock;              /* 插件操作线程互斥锁 */
    SV_BOOL         bPlugListChange;        /* 算法插件列表配置发生变化 */
    PLUG_CTRL_API   astPlugCtrlApiList[ALG_PLUG_MAX_NUM];   /* 算法插件列表API */
	threadpool      thpool;                 /* 线程池 */
} PLUG_INFO_S;

sint32 pd_get_readIdx(sint32 s32Chn, uint64 *pu64Pts);

sint32 pd_release_readIdx(sint32 s32Chn, sint32 s32Idx);


PLUG_INFO_S m_stPlugInfo = {0};             /* 模块控制信息 */


sint32 plug_GetAlgWidth()
{
    return PLUG_IMAGE_WIDTH;
}

sint32 plug_GetAlgHeight()
{
    return PLUG_IMAGE_HEIGHT;
}

sint32 plug_CheckCallback(uint8 *au8SourceAddr, uint8 *au8ResultAddr)
{
    sint32 s32Ret, i;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    KEY_AUTH_S stAuthSrc = {0}, stAuthDes = {0};

    stAuthSrc.s32Len = 16;
    memcpy(stAuthSrc.au8Buf, au8SourceAddr, 16);
    stMsgPkt.pu8Data = (uint8 *)&stAuthSrc;
    stMsgPkt.u32Size = sizeof(KEY_AUTH_S);
    stRetPkt.pu8Data = (uint8 *)&stAuthDes;
    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_KEY_AUTH, &stMsgPkt, &stRetPkt, sizeof(KEY_AUTH_S));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_KEY_AUTH failed. [err=%#x]\n", s32Ret);
    }

    m_stPlugInfo.bKeyAuth = stRetPkt.stMsg.s32Param;
    memcpy(au8ResultAddr, stAuthDes.au8Buf, 16);
    print_level(SV_DEBUG, "bKeyAuth:%d\n", m_stPlugInfo.bKeyAuth);
    
    return 0;
}

/******************************************************************************
 * 函数功能: 将插件信息写入到映射表/root/plug/map.json
 * 输入参数: pastPlugCtrlApiList --- 插件列表
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 plug_WritePlugsMap(PLUG_CTRL_API *pastPlugCtrlApiList)
{
    sint32 s32Ret = 0, i;
    sint32 s32Fd = -1;
    cJSON *pstJson = NULL, *pstPlugList = NULL, *pstPlugInfo = NULL;
    char szBuf[64*1024];

    if (NULL == pastPlugCtrlApiList)
    {
        return ERR_NULL_PTR;
    }

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return SV_FAILURE;
    }

    pstPlugList = cJSON_CreateArray();
    if (NULL == pstPlugList)
    {
        print_level(SV_ERROR, "cJSON_CreateArray failed.\n");
        return SV_FAILURE;
    }

    cJSON_AddItemToObject(pstJson, "plugs", pstPlugList);
    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (!pastPlugCtrlApiList[i].bValid)
        {
            continue;
        }

        pstPlugInfo = cJSON_CreateObject();
        if (NULL == pstPlugInfo)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            continue;
        }

        cJSON_AddItemToObject(pstPlugInfo, "plugPath", cJSON_CreateString(pastPlugCtrlApiList[i].szPlugPath));
        cJSON_AddItemToObject(pstPlugInfo, "name", cJSON_CreateString(pastPlugCtrlApiList[i].stPlugInfo.szPlugName));
        cJSON_AddItemToObject(pstPlugInfo, "version", cJSON_CreateString(pastPlugCtrlApiList[i].stPlugInfo.szPlugVer));
        cJSON_AddItemToObject(pstPlugInfo, "description", cJSON_CreateString(pastPlugCtrlApiList[i].stPlugInfo.szPlugDesp));
        cJSON_AddItemToObject(pstPlugInfo, "paramTemplate", cJSON_CreateString(pastPlugCtrlApiList[i].stPlugInfo.szPlugParamTemplate));
        cJSON_AddItemToObject(pstPlugInfo, "resultType", cJSON_CreateNumber(pastPlugCtrlApiList[i].enResType));
        cJSON_AddItemToObject(pstPlugInfo, "enable", cJSON_CreateBool(pastPlugCtrlApiList[i].bEnable));
        cJSON_AddItemToObject(pstPlugInfo, "interval", cJSON_CreateNumber(pastPlugCtrlApiList[i].s32Interval));
        cJSON_AddItemToArray(pstPlugList, pstPlugInfo);
    }

    memset(szBuf, 0, 64*1024);
    cJSON_PrintPreallocated(pstJson, szBuf, 64*1024, 1);
    remove("/root/plug/map-tmp");
    s32Fd = open("/root/plug/map-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: /root/plug/map-tmp failed. [err:%s]\n", strerror(errno));
        cJSON_Delete(pstJson);
        return SV_FAILURE;
    }

    s32Ret = write(s32Fd, szBuf, strlen(szBuf));
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "write file: /root/plug/map-tmp failed. [err:%s]\n", strerror(errno));
        close(s32Fd);
        cJSON_Delete(pstJson);
        return SV_FAILURE;
    }
    
    close(s32Fd);
    rename("/root/plug/map-tmp", ALG_PLUG_MAP_PATH);
    cJSON_Delete(pstJson);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将插件信息写入到映射表/root/plug/map.json
 * 输入参数: 无
 * 输出参数: pastPlugCtrlApiList --- 插件列表
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 plug_ReadPlugsMap(PLUG_CTRL_API *pastPlugCtrlApiList)
{
    sint32 s32Ret = 0, i;
    sint32 s32Fd = -1;
    uint32 u32Num = 0;
    cJSON *pstJson = NULL, *pstPlugList = NULL, *pstPlugInfo = NULL, *pstTmp = NULL;
    char szBuf[64*1024];

    if (NULL == pastPlugCtrlApiList)
    {
        return ERR_NULL_PTR;
    }

    s32Fd = open(ALG_PLUG_MAP_PATH, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file:%s failed.\n", ALG_PLUG_MAP_PATH);
        return SV_FAILURE;
    }

    s32Ret = read(s32Fd, szBuf, sizeof(szBuf));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "read file:%s failed.\n", ALG_PLUG_MAP_PATH);
        return SV_FAILURE;
    }

    close(s32Fd);
    pstJson = cJSON_Parse(szBuf);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return SV_FAILURE;
    }

    pstPlugList = cJSON_GetObjectItemCaseSensitive(pstJson, "plugs");
    if (NULL == pstPlugList || !cJSON_IsArray(pstPlugList))
    {
        print_level(SV_ERROR, "Get plugs failed.\n");
        return SV_FAILURE;
    }

    u32Num = cJSON_GetArraySize(pstPlugList);
    if (0 == u32Num)
    {
        memset(pastPlugCtrlApiList, 0x0, sizeof(PLUG_CTRL_API)*ALG_PLUG_MAX_NUM);
        return SV_SUCCESS;
    }

    memset(pastPlugCtrlApiList, 0x0, sizeof(PLUG_CTRL_API)*ALG_PLUG_MAX_NUM);
    for (i = 0; i < u32Num; i++)
    {
        pstPlugInfo = cJSON_GetArrayItem(pstPlugList, i);
        if (NULL == pstPlugInfo)
        {
            continue;
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "plugPath");
        if (NULL != pstTmp && cJSON_IsString(pstTmp))
        {
            strncpy(pastPlugCtrlApiList[i].szPlugPath, pstTmp->valuestring, 255);
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "name");
        if (NULL != pstTmp && cJSON_IsString(pstTmp))
        {
            strncpy(pastPlugCtrlApiList[i].stPlugInfo.szPlugName, pstTmp->valuestring, 127);
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "version");
        if (NULL != pstTmp && cJSON_IsString(pstTmp))
        {
            strncpy(pastPlugCtrlApiList[i].stPlugInfo.szPlugVer, pstTmp->valuestring, 127);
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "description");
        if (NULL != pstTmp && cJSON_IsString(pstTmp))
        {
            strncpy(pastPlugCtrlApiList[i].stPlugInfo.szPlugDesp, pstTmp->valuestring, 127);
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "paramTemplate");
        if (NULL != pstTmp && cJSON_IsString(pstTmp))
        {
            strncpy(pastPlugCtrlApiList[i].stPlugInfo.szPlugParamTemplate, pstTmp->valuestring, 1023);
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "resultType");
        if (NULL != pstTmp && cJSON_IsNumber(pstTmp))
        {
            pastPlugCtrlApiList[i].enResType = pstTmp->valueint;
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "enable");
        if (NULL != pstTmp && cJSON_IsBool(pstTmp))
        {
            pastPlugCtrlApiList[i].bEnable = pstTmp->valueint;
        }

        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "interval");
        if (NULL != pstTmp && cJSON_IsNumber(pstTmp))
        {
            pastPlugCtrlApiList[i].s32Interval = pstTmp->valueint;
        }

        print_level(SV_DEBUG, "szPlugPath:%s, szPlugName:%s, szPlugVer:%s, szPlugDesp:%s, szPlugParamTemplate:%s, enResType:%d, bEnable:%d, s32Interval:%d\n", \
                pastPlugCtrlApiList[i].szPlugPath, pastPlugCtrlApiList[i].stPlugInfo.szPlugName, pastPlugCtrlApiList[i].stPlugInfo.szPlugVer, \
                pastPlugCtrlApiList[i].stPlugInfo.szPlugDesp, pastPlugCtrlApiList[i].stPlugInfo.szPlugParamTemplate, pastPlugCtrlApiList[i].enResType, \
                pastPlugCtrlApiList[i].bEnable, pastPlugCtrlApiList[i].s32Interval);

        pastPlugCtrlApiList[i].bValid = SV_TRUE;
    }

    cJSON_Delete(pstJson);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 扫描插件目录(/root/plug/)收集加载插件信息更新到/root/plug/map.json映射表
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 plug_ScanPlugs(PLUG_CTRL_API *pastPlugCtrlApiList)
{
    sint32 s32Ret = 0, i;
    uint32 u32Cnt = 0;
    DIR *pDir = NULL;
    struct dirent *pstDirent = NULL;
    char szSoFilePath[256];
    PLUG_CTRL_API astPlugCtrlApiList[ALG_PLUG_MAX_NUM] = {0};

    if (NULL == pastPlugCtrlApiList)
    {
        return ERR_NULL_PTR;
    }

    pDir = opendir("/root/plug");
	if(NULL == pDir)
    {
        print_level(SV_ERROR, "opendir /root/plug failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    while (1)
    {
        pstDirent = readdir(pDir);
        if (NULL == pstDirent)
        {
            break;
        }

        if (pstDirent->d_type != DT_DIR || 0 == strcmp(pstDirent->d_name, ".") || 0 == strcmp(pstDirent->d_name, ".."))
        {
            continue;
        }

        print_level(SV_DEBUG, "%s\n", pstDirent->d_name);
        sprintf(szSoFilePath, "/root/plug/%s/so/%s", pstDirent->d_name, ALG_PLUG_SO_NAME);
        s32Ret = access(szSoFilePath, F_OK);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "access file: %s failed.\n", szSoFilePath);
            continue;
        }

        sprintf(astPlugCtrlApiList[u32Cnt].szPlugPath, "/root/plug/%s", pstDirent->d_name);
        astPlugCtrlApiList[u32Cnt].pvHandle = dlopen(szSoFilePath, RTLD_LAZY);
        if (NULL == astPlugCtrlApiList[u32Cnt].pvHandle)
        {
            print_level(SV_ERROR, "dlopen %s failed. [err:%s]\n", szSoFilePath, strerror(errno));
            continue;
        }

        astPlugCtrlApiList[u32Cnt].pfnPlugGetInfo = (PlugGetInfo)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlugGetInfo");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnPlugGetInfo)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlugGetInfo failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        s32Ret = astPlugCtrlApiList[u32Cnt].pfnPlugGetInfo(&astPlugCtrlApiList[u32Cnt].stPlugInfo);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "[%s] pfnPlugGetInfo failed. [err=%#x]\n", szSoFilePath, s32Ret);
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        /* 生产烧录默认开启行人检测算法 */
        if (0 == strcmp(astPlugCtrlApiList[u32Cnt].stPlugInfo.szPlugName, "行人检测"))
        {
            astPlugCtrlApiList[u32Cnt].bEnable = SV_TRUE;
        }

        astPlugCtrlApiList[u32Cnt].pfnPlug_ResType = (PlugGetInfo)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlug_ResType");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnPlug_ResType)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlug_ResType failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        astPlugCtrlApiList[u32Cnt].enResType = astPlugCtrlApiList[u32Cnt].pfnPlug_ResType();
        print_level(SV_DEBUG, "enResType:%d, szPlugName:%s, szPlugVer:%s, szPlugDesp:%s, szPlugParamTemplate:%s\n", astPlugCtrlApiList[u32Cnt].enResType, \
                        astPlugCtrlApiList[u32Cnt].stPlugInfo.szPlugName, astPlugCtrlApiList[u32Cnt].stPlugInfo.szPlugVer, astPlugCtrlApiList[u32Cnt].stPlugInfo.szPlugDesp, astPlugCtrlApiList[u32Cnt].stPlugInfo.szPlugParamTemplate);

        astPlugCtrlApiList[u32Cnt].pfnCheckModel = (PlugCheckModel)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlugCheckModel");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnCheckModel)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlugCheckModel failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        s32Ret = astPlugCtrlApiList[u32Cnt].pfnCheckModel(astPlugCtrlApiList[u32Cnt].szPlugPath);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "[%s] pfnCheckModel failed. [err=%#x]\n", szSoFilePath, s32Ret);
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        astPlugCtrlApiList[u32Cnt].pfnPlug_Init = (PlugGetInfo)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlug_Init");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnPlug_Init)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlug_Init failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        astPlugCtrlApiList[u32Cnt].pfnPlug_Fini = (PlugGetInfo)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlug_Fini");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnPlug_Fini)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlug_Fini failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        astPlugCtrlApiList[u32Cnt].pfnPlug_Forward = (PlugGetInfo)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlug_Forward");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnPlug_Forward)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlug_Forward failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        astPlugCtrlApiList[u32Cnt].pfnPlug_GetResult = (PlugGetInfo)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlug_GetResult");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnPlug_GetResult)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlug_GetResult failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        astPlugCtrlApiList[u32Cnt].pfnPlug_ConfigUpdate = (PlugGetInfo)dlsym(astPlugCtrlApiList[u32Cnt].pvHandle, "AlgPlug_ConfigUpdate");
        if (NULL == astPlugCtrlApiList[u32Cnt].pfnPlug_ConfigUpdate)
        {
            print_level(SV_ERROR, "[%s] dlsym AlgPlug_ConfigUpdate failed. [err:%s]\n", szSoFilePath, strerror(errno));
            dlclose(astPlugCtrlApiList[u32Cnt].pvHandle);
            continue;
        }

        astPlugCtrlApiList[u32Cnt].bValid = SV_TRUE;
        u32Cnt++;
    }

    closedir(pDir);
    s32Ret = plug_WritePlugsMap(astPlugCtrlApiList);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "plug_WritePlugsMap failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    memcpy(pastPlugCtrlApiList, astPlugCtrlApiList, sizeof(astPlugCtrlApiList));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读取so库文件加载映射接口
 * 输入参数: pszSoFilePath --- so库文件路径
 * 输出参数: pastPlugCtrlApi --- 插件控制接口
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 plug_LoadPlugSo(char *pszSoFilePath, PLUG_CTRL_API *pastPlugCtrlApi)
{
    sint32 s32Ret = 0;

    if (NULL == pszSoFilePath || NULL == pastPlugCtrlApi)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = access(pszSoFilePath, F_OK);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "access file: %s failed.\n", pszSoFilePath);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->pvHandle = dlopen(pszSoFilePath, RTLD_LAZY);
    if (NULL == pastPlugCtrlApi->pvHandle)
    {
        print_level(SV_ERROR, "dlopen %s failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        return SV_FAILURE;
    }

    pastPlugCtrlApi->pfnPlugGetInfo = (PlugGetInfo)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlugGetInfo");
    if (NULL == pastPlugCtrlApi->pfnPlugGetInfo)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlugGetInfo failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    s32Ret = pastPlugCtrlApi->pfnPlugGetInfo(&pastPlugCtrlApi->stPlugInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "[%s] pfnPlugGetInfo failed. [err=%#x]\n", pszSoFilePath, s32Ret);
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }    

    pastPlugCtrlApi->pfnPlug_ResType = (PlugGetInfo)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlug_ResType");
    if (NULL == pastPlugCtrlApi->pfnPlug_ResType)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_ResType failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->enResType = pastPlugCtrlApi->pfnPlug_ResType();
    pastPlugCtrlApi->pfnCheckModel = (PlugCheckModel)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlugCheckModel");
    if (NULL == pastPlugCtrlApi->pfnCheckModel)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlugCheckModel failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->pfnPlug_Init = (PlugGetInfo)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlug_Init");
    if (NULL == pastPlugCtrlApi->pfnPlug_Init)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_Init failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->pfnPlug_Fini = (PlugGetInfo)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlug_Fini");
    if (NULL == pastPlugCtrlApi->pfnPlug_Fini)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_Fini failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->pfnPlug_Forward = (PlugGetInfo)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlug_Forward");
    if (NULL == pastPlugCtrlApi->pfnPlug_Forward)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_Forward failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->pfnPlug_GetResult = (PlugGetInfo)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlug_GetResult");
    if (NULL == pastPlugCtrlApi->pfnPlug_GetResult)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_GetResult failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->pfnPlug_ConfigUpdate = (PlugGetInfo)dlsym(pastPlugCtrlApi->pvHandle, "AlgPlug_ConfigUpdate");
    if (NULL == pastPlugCtrlApi->pfnPlug_ConfigUpdate)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_ConfigUpdate failed. [err:%s]\n", pszSoFilePath, strerror(errno));
        dlclose(pastPlugCtrlApi->pvHandle);
        return SV_FAILURE;
    }

    pastPlugCtrlApi->bValid = SV_TRUE;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读取/root/plug/map.json映射表加载相应的插件信息
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 plug_LoadPlugs(PLUG_CTRL_API *pastPlugCtrlApiList)
{
    sint32 s32Ret = 0, i;
    char szSoFilePath[256];
    PLUG_CTRL_API astPlugCtrlApiList[ALG_PLUG_MAX_NUM] = {0};

    if (NULL == pastPlugCtrlApiList)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = plug_ReadPlugsMap(astPlugCtrlApiList);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "plug_ReadPlugsMap failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (!astPlugCtrlApiList[i].bValid)
        {
            continue;
        }

        astPlugCtrlApiList[i].bValid = SV_FALSE;
        print_level(SV_DEBUG, "%s\n", astPlugCtrlApiList[i].szPlugPath);
        sprintf(szSoFilePath, "%s/so/%s", astPlugCtrlApiList[i].szPlugPath, ALG_PLUG_SO_NAME);
        s32Ret = plug_LoadPlugSo(szSoFilePath, &astPlugCtrlApiList[i]);
        if (SV_SUCCESS != s32Ret)
        {
             print_level(SV_ERROR, "plug_LoadPlugSo failed. [err=%#x]\n", s32Ret);
            continue;
        }
    }

    memcpy(pastPlugCtrlApiList, astPlugCtrlApiList, sizeof(astPlugCtrlApiList));

    return SV_SUCCESS;
}

/* 传递报警信息进行录像 */
void plug_Audio_Play(void *pvArg)
{
    sint32 s32Ret;
	sint32 s32Chn = 0;
    static AlarmPost_TYPE_E enLastType = AlarmPost_TYPE_BUTT;
    static int bPlay = 0;
    ALARM_EVENT_S stAlarmEvent = {0};
    MSG_PACKET_S stMsgPkt = {0};
    struct timeval tvAlarm;
    struct timezone tz;
    char szCmd[128] = {0};
	int enType = *((int*)pvArg);//*((int*)pvArg);
	#define PLUG_PD_ALARM_TYPE_RED 3

	if(bPlay)
	{
		return;
	}
	bPlay = 1;
	enLastType = enType;

    memset(&stAlarmEvent, 0, sizeof(stAlarmEvent));
    gettimeofday(&tvAlarm, &tz);
    if(enType == AlarmPost_TYPE_PERSON || enType==AlarmPost_TYPE_CAR)
    {
		stAlarmEvent.enAlarmEvent = ALARM_EVENT_PD;
		stAlarmEvent.enAlarmType = ALARM_PD_ROI3;
    }
	else
	{
		stAlarmEvent.enAlarmEvent = ALARM_EVENT_DMM;
		stAlarmEvent.enAlarmType = ALARM_NO_HELMET;
	}

	
    stAlarmEvent.s64TimeStamp = (sint64)tvAlarm.tv_sec;
    stAlarmEvent.s32Chn = s32Chn;

    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    stMsgPkt.stMsg.u16OpCode = OP_EVENT_ALG_ALARM;
    stMsgPkt.pu8Data = (uint8 *)&stAlarmEvent;
    stMsgPkt.u32Size = sizeof(stAlarmEvent);
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_ALG_ALARM, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
    }

    /*s32Ret = Msg_submitEvent(EP_HTTPSERVER, OP_EVENT_ALG_ALARM, &stMsgPkt); //回传到web端显示报警信息
    if (SV_SUCCESS != s32Ret)
    {
       print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
    }*/

#if 0
		sprintf(szCmd, "/root/gpio.sh  1 25 1");
		SAFE_SV_System(szCmd);
#endif
		switch(enType)

		{
			case AlarmPost_TYPE_PERSON:
				ALARM_PlayAlarm(s32Chn, PLUG_PD_ALARM_TYPE_RED , SV_TRUE);	// 播放行人的报警音，默认红色roi报警
				break;
			case AlarmPost_TYPE_CAR:
				ALARM_PlayAlarm(s32Chn, PLUG_PD_ALARM_TYPE_RED , SV_FALSE);	// 播放汽车的报警音，默认红色roi报警
				break;
			case AlarmPost_TYPE_NOHAT:
				ALARM_PlayAudio(ALARM_NO_HELMET);//播放无头盔音频
				break;
			default:
				print_level(SV_ERROR,"alarm type is not support play audio!\n");
				break;
		}
		bPlay = 0;
		
#if 0       
		sprintf(szCmd, "/root/gpio.sh  1 25 0");
		SAFE_SV_System(szCmd);
#endif		

    return;
}

sint32 Plug_Alarm_Post(AlarmPost_TYPE_E enType)
{
    sint32 s32Ret;
	if(enType == AlarmPost_TYPE_BUTT)
	{
		return SV_FAILURE;
	}
	static AlarmPost_TYPE_E enAlarmType = enType;
#if (defined(BOARD_ADA47V1))
    if (BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        return SV_SUCCESS;
    }    
#endif
	enAlarmType = enType;
    thpool_add_work(m_stPlugInfo.thpool, plug_Audio_Play, &enAlarmType);
    
    return SV_SUCCESS;
}

/* 算法插件驱动线程 */
void * plug_alg_Body(void *pvArg)
{
    sint32 s32Ret = 0, i, j;
    sint32 s32CurChn = 0, s32Idx;
    uint64 u64Pts = 0;
    uint32 u32StepTimeMs = 0;
    struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};
    PLUG_INFO_S *pstPlugInfo = (PLUG_INFO_S *)pvArg;
    AlgPlugInfoT stPlugInfo = {0};
    SV_BOOL bNoRunning = SV_FALSE;
    void *pvParam = NULL;
#if ALG_MUTLIT_BUFFER
    void *apvBuf[4][3] = {NULL};
#else
    void *apvBuf[4] = {NULL};
#endif
    uint32 u32BufLen = pstPlugInfo->u32Width*pstPlugInfo->u32Height*3;
    uint32 u32Width = 0, u32Height = 0;
    IMG_FMT_E enImgFmt = IMG_FMT_RGB888;
    PLUG_PD_RES stPdRes = {0};
    PLUG_PC_RES stPcRes = {0};
    PLUG_SH_RES stShRes = {0};
    uint16 u16mask = 0;
    uint32 u32RectCnt = 0;
    MSG_PACKET_S stMsgPkt = {0};    // 绘制消息包
    MEDIA_GUI_DRAW_S stMediaGuiDraw = {0};
    MEDIA_GUI_PERSON_S stGuiRect = {0};
    MEDIA_GUI_NULL_S stGuiNull;
	AlarmPost_TYPE_E enAlarmPostType = AlarmPost_TYPE_BUTT;
		
    s32Ret = prctl(PR_SET_NAME, "plug_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    for (i = 0; i < pstPlugInfo->u32ChnNum; i++)
    {

#if ALG_MUTLIT_BUFFER
        for(j = 0; j < 3; j++)
        {
            apvBuf[i][j] = mmap(NULL, u32BufLen, PROT_READ, MAP_SHARED, pstPlugInfo->as32MediaBufFd[i][j], 0);
            if (MAP_FAILED == apvBuf[i][j])
            {
                print_level(SV_ERROR, "mmap[%d] failed.\n", i);
                return NULL;
            }
        }
#else
        apvBuf[i] = mmap(NULL, u32BufLen, PROT_READ, MAP_SHARED, pstPlugInfo->as32MediaBufFd[i], 0);
        if (MAP_FAILED == apvBuf[i])
        {
            print_level(SV_ERROR, "mmap[%d] failed.\n", i);
            return NULL;
        }
#endif
    }

    print_level(SV_INFO, "enter PLUG detection.\n");
    while (pstPlugInfo->bRunning)
    {
        //print_level(SV_DEBUG, "plug_alg_Body running %d ...\n", s32CurChn);
        //sleep_ms(1000);

        /* 初始化各插件 */
        pthread_mutex_lock(&pstPlugInfo->mutexLock);
        for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
        {
            if (!pstPlugInfo->astPlugCtrlApiList[i].bValid || !pstPlugInfo->astPlugCtrlApiList[i].bEnable)
            {
                pstPlugInfo->astPlugCtrlApiList[i].bRunning = SV_FALSE;
                continue;
            }

            if (pstPlugInfo->astPlugCtrlApiList[i].bInited)
            {
                continue;
            }

            switch (pstPlugInfo->astPlugCtrlApiList[i].enResType)
            {
                case PLUG_RES_PD:
                case PLUG_RES_PC:
                case PLUG_RES_SH:
                    pvParam = plug_CheckCallback;
                    break;
                    
                default:
                    pvParam = NULL;
            }
            
            s32Ret = pstPlugInfo->astPlugCtrlApiList[i].pfnPlug_Init(pstPlugInfo->astPlugCtrlApiList[i].szPlugPath, pvParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "pfnPlug_Init failed. [szPlugPath:%s]\n", pstPlugInfo->astPlugCtrlApiList[i].szPlugPath);
                pstPlugInfo->astPlugCtrlApiList[i].bValid = SV_FALSE;
                continue;
            }
        }
        pthread_mutex_unlock(&pstPlugInfo->mutexLock);

        /* 运行各插件推理获取结果 */
        while (pstPlugInfo->bRunning)
        {
            if (pstPlugInfo->bPlugListChange)
            {
                pstPlugInfo->bPlugListChange = SV_FALSE;
                break;
            }
            
            clock_gettime(CLOCK_MONOTONIC, &tvNow);
            u32StepTimeMs = ((tvNow.tv_sec*1000 + tvNow.tv_nsec/1000000) - (tvLast.tv_sec*1000 + tvLast.tv_nsec/1000000));
            tvLast = tvNow;
            s32CurChn++;
            if (s32CurChn >= pstPlugInfo->u32ChnNum)
            {
                s32CurChn = 0;
            }

            bNoRunning = SV_TRUE;
            pthread_mutex_lock(&pstPlugInfo->mutexLock);
            for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
            {
                if (!pstPlugInfo->astPlugCtrlApiList[i].bValid || !pstPlugInfo->astPlugCtrlApiList[i].bEnable)
                {
                    pstPlugInfo->astPlugCtrlApiList[i].bRunning = SV_FALSE;
                    continue;
                }

                bNoRunning = SV_TRUE;
                pstPlugInfo->astPlugCtrlApiList[i].bRunning = SV_TRUE;

                /* 锁住计算资源,保证同一时刻只跑一个算法*/
                s32Ret = ALG_Calculate_Lock();
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
                    sleep_ms(1);
                    continue;
                }
#if ALG_MUTLIT_BUFFER
                s32Idx = pd_get_readIdx(s32CurChn, &u64Pts);
                if(s32Idx < 0)
                {
                    //print_level(SV_ERROR, "pd_get_readIdx failed!\n");
                    ALG_Calculate_unLock();
                    sleep_ms(1);
                    continue;
                }
#else
                /* P操作进入MediaBuffer临界区 */
                s32Ret = MS_P(s32CurChn);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "MS_P failed. [err=%d]\n", s32Ret);
                    ALG_Calculate_unLock();
                    sleep_ms(1);
                    continue;
                }
#endif

                u32Width = pstPlugInfo->u32Width;
                u32Height = pstPlugInfo->u32Height;
                //print_level(SV_DEBUG, "szPlugPath: %s\n", pstPlugInfo->astPlugCtrlApiList[i].szPlugPath);
#if ALG_MUTLIT_BUFFER
                s32Ret = pstPlugInfo->astPlugCtrlApiList[i].pfnPlug_Forward((char *)apvBuf[s32CurChn][s32Idx], u32Width, u32Height, enImgFmt, pstPlugInfo->as32MediaBufFd[s32CurChn]);
#else
                s32Ret = pstPlugInfo->astPlugCtrlApiList[i].pfnPlug_Forward((char *)apvBuf[s32CurChn], u32Width, u32Height, enImgFmt, pstPlugInfo->as32MediaBufFd[s32CurChn]);
#endif

#if ALG_MUTLIT_BUFFER
                pd_release_readIdx(s32CurChn, s32Idx);
#else
                MS_V(s32CurChn);
#endif
                ALG_Calculate_unLock();
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "pfnPlug_Forward failed. [err=%#x]\n", s32Ret);
                    sleep_ms(10);
                    continue;
                }

                
                memset(&stMediaGuiDraw, 0x00, sizeof(stMediaGuiDraw));
                memset(&stGuiRect, 0x00, sizeof(stGuiRect));
                u32RectCnt = 0;

                /* 清空原来的画板 */
                u16mask = MEDIA_GUI_GET_MASK(s32CurChn, 0, MEDIA_GUI_OP_CLEAR);
                s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
                }

                switch (pstPlugInfo->astPlugCtrlApiList[i].enResType)
                {
                    case PLUG_RES_PD:
                        s32Ret = pstPlugInfo->astPlugCtrlApiList[i].pfnPlug_GetResult((void *)&stPdRes);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "pfnPlug_GetResult failed. [err=%#x]\n", s32Ret);
                            continue;
                        }

                       // print_level(SV_DEBUG, "detect pd:%d\n", stPdRes.u32Num);
                        for (j = 0; j < stPdRes.u32Num; j++)
                        {
                            //print_level(SV_DEBUG, "[%f] (%fx%f) (%fx%f)\n", stPdRes.stTarget[i].fConfidence, stPdRes.stTarget[i].fX1, stPdRes.stTarget[i].fY1, stPdRes.stTarget[i].fX2, stPdRes.stTarget[i].fY2);
                            stGuiRect.astPersonsRect[u32RectCnt].color = GUI_COLOR_GREEN;
                            stGuiRect.astPersonsRect[u32RectCnt].stick = 3;
                            stGuiRect.classes[u32RectCnt] = PD_CLS_PERSON;
                            stGuiRect.astPersonsRect[u32RectCnt].x1 = stPdRes.stTarget[j].fX1;
                            stGuiRect.astPersonsRect[u32RectCnt].y1 = stPdRes.stTarget[j].fY1;
                            stGuiRect.astPersonsRect[u32RectCnt].x2 = stPdRes.stTarget[j].fX2;
                            stGuiRect.astPersonsRect[u32RectCnt].y2 = stPdRes.stTarget[j].fY2;
                            stGuiRect.astPersonsRect[u32RectCnt].fontscale = 2;
                            stGuiRect.au32Score[u32RectCnt] = (uint16)(stPdRes.stTarget[j].fConfidence*1000);
                            stGuiRect.as32DistanceY[u32RectCnt] = -1;
							enAlarmPostType = AlarmPost_TYPE_PERSON;
                            u32RectCnt++;
                        }
                        stGuiRect.u32PersonNum = u32RectCnt;

                        /* 添加行人矩形绘制操作 */
                        u16mask = MEDIA_GUI_GET_MASK(s32CurChn, 0, MEDIA_GUI_OP_PERSON_RECT);
                        s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiRect);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
                        }		
																						
                        break;

                    case PLUG_RES_PC:
                        s32Ret = pstPlugInfo->astPlugCtrlApiList[i].pfnPlug_GetResult((void *)&stPcRes);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "pfnPlug_GetResult failed. [err=%#x]\n", s32Ret);
                            continue;
                        }

                        //print_level(SV_DEBUG, "detect pd:%d\n", stPdRes.u32Num);
                        for (j = 0; j < stPcRes.u32Num; j++)
                        {
                            //print_level(SV_DEBUG, "[%f] (%fx%f) (%fx%f)\n", stPcRes.stTarget[i].fConfidence, stPcRes.stTarget[i].fX1, stPcRes.stTarget[i].fY1, stPcRes.stTarget[i].fX2, stPcRes.stTarget[i].fY2);
                            stGuiRect.astPersonsRect[u32RectCnt].color = (stPcRes.stTarget[j].enType == PC_TYPE_PERSON) ? GUI_COLOR_ORANGE : GUI_COLOR_PURPLE;
                            stGuiRect.astPersonsRect[u32RectCnt].stick = 3;
                            stGuiRect.classes[u32RectCnt] = (stPcRes.stTarget[j].enType == PC_TYPE_PERSON) ? PD_CLS_PERSON : PD_CLS_CAR;
                            stGuiRect.astPersonsRect[u32RectCnt].x1 = stPcRes.stTarget[j].fX1;
                            stGuiRect.astPersonsRect[u32RectCnt].y1 = stPcRes.stTarget[j].fY1;
                            stGuiRect.astPersonsRect[u32RectCnt].x2 = stPcRes.stTarget[j].fX2;
                            stGuiRect.astPersonsRect[u32RectCnt].y2 = stPcRes.stTarget[j].fY2;
                            stGuiRect.astPersonsRect[u32RectCnt].fontscale = 2;
                            stGuiRect.au32Score[u32RectCnt] = (uint16)(stPcRes.stTarget[j].fConfidence*1000);
                            stGuiRect.as32DistanceY[u32RectCnt] = -1;
							enAlarmPostType = (stPcRes.stTarget[j].enType == PC_TYPE_PERSON) ? AlarmPost_TYPE_PERSON:AlarmPost_TYPE_CAR;//有行人 有汽车，类型为行人，录像只要行人的类型
                            u32RectCnt++;
                        }
                        stGuiRect.u32PersonNum = u32RectCnt;

                        /* 添加行人矩形绘制操作 */
                        u16mask = MEDIA_GUI_GET_MASK(s32CurChn, 0, MEDIA_GUI_OP_PERSON_RECT);
                        s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiRect);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
                        }    					
						
                        break;

                    case PLUG_RES_SH:
                        s32Ret = pstPlugInfo->astPlugCtrlApiList[i].pfnPlug_GetResult((void *)&stShRes);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "pfnPlug_GetResult failed. [err=%#x]\n", s32Ret);
                            continue;
                        }

                        //print_level(SV_DEBUG, "detect sh:%d\n", stShRes.u32Num);
                        for (j = 0; j < stShRes.u32Num; j++)
                        {
                            //print_level(SV_DEBUG, "[%f] (%fx%f) (%fx%f)\n", stPcRes.stTarget[i].fConfidence, stPcRes.stTarget[i].fX1, stPcRes.stTarget[i].fY1, stPcRes.stTarget[i].fX2, stPcRes.stTarget[i].fY2);
                            switch (stShRes.stTarget[j].enType)
                            {
                                case SH_TYPE_NO_HAT:
									enAlarmPostType = AlarmPost_TYPE_NOHAT;
                                    stGuiRect.astPersonsRect[u32RectCnt].color = GUI_COLOR_L_GREEN;
                                    break;
                                case SH_TYPE_HAT_RED:
                                    stGuiRect.astPersonsRect[u32RectCnt].color = GUI_COLOR_RED;
                                    break;
                                case SH_TYPE_HAT_YELLOW:
                                    stGuiRect.astPersonsRect[u32RectCnt].color = GUI_COLOR_YELLOW;
                                    break;
                                case SH_TYPE_HAT_WHITE:
                                    stGuiRect.astPersonsRect[u32RectCnt].color = GUI_COLOR_WHITE;
                                    break;
                                case SH_TYPE_HAT_BLUE:
                                    stGuiRect.astPersonsRect[u32RectCnt].color = GUI_COLOR_BLUE;
                                    break;
                                default:
                                    print_level(SV_ERROR, "unknown type: %d\n", stShRes.stTarget[j].enType);
                                    continue;
                            }
                            stGuiRect.astPersonsRect[u32RectCnt].stick = 3;
                            stGuiRect.classes[u32RectCnt] = PD_CLS_PERSON;
                            stGuiRect.astPersonsRect[u32RectCnt].x1 = stShRes.stTarget[j].fX1;
                            stGuiRect.astPersonsRect[u32RectCnt].y1 = stShRes.stTarget[j].fY1;
                            stGuiRect.astPersonsRect[u32RectCnt].x2 = stShRes.stTarget[j].fX2;
                            stGuiRect.astPersonsRect[u32RectCnt].y2 = stShRes.stTarget[j].fY2;
                            stGuiRect.astPersonsRect[u32RectCnt].fontscale = 2;
                            stGuiRect.au32Score[u32RectCnt] = (uint16)(stShRes.stTarget[j].fConfidence*1000);
                            stGuiRect.as32DistanceY[u32RectCnt] = -1;
                            u32RectCnt++;
                        }
                        stGuiRect.u32PersonNum = u32RectCnt;

                        /* 添加行人矩形绘制操作 */
                        u16mask = MEDIA_GUI_GET_MASK(s32CurChn, 0, MEDIA_GUI_OP_PERSON_RECT);
                        s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiRect);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
                        }
                        						
                        break;

                    default:
                        print_level(SV_WARN, "no deal with type:%d\n", pstPlugInfo->astPlugCtrlApiList[i].enResType);
                }

                /* 发送绘制消息 */
                memset(&stMsgPkt, 0, sizeof(stMsgPkt));
                stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
                stMsgPkt.u32Size = MEDIA_GUI_SIZE(stMediaGuiDraw);
                s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
                }

				Plug_Alarm_Post(enAlarmPostType);//检测到行人 汽车 没有戴头盔才触发报警
				enAlarmPostType = AlarmPost_TYPE_BUTT;
				
            }
            
            pthread_mutex_unlock(&pstPlugInfo->mutexLock);
            if (bNoRunning)
            {
                sleep_ms(100);
            }
        }
    }

    /* 去初始化各插件 */
    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (!pstPlugInfo->astPlugCtrlApiList[i].bValid || !pstPlugInfo->astPlugCtrlApiList[i].bEnable)
        {
            pstPlugInfo->astPlugCtrlApiList[i].bRunning = SV_FALSE;
            continue;
        }

        s32Ret = pstPlugInfo->astPlugCtrlApiList[i].pfnPlug_Fini();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "pfnPlug_Fini failed. [szPlugPath:%s]\n", pstPlugInfo->astPlugCtrlApiList[i].szPlugPath);
            continue;
        }

        pstPlugInfo->astPlugCtrlApiList[i].bInited = SV_FALSE;
        pstPlugInfo->astPlugCtrlApiList[i].bRunning = SV_FALSE;
    }

    for (i = 0; i < pstPlugInfo->u32ChnNum; i++)
    {
#if ALG_MUTLIT_BUFFER
        for(j = 0; j < 3; j++)
        {
            munmap(apvBuf[i][j], u32BufLen);
        }
#else
        munmap(apvBuf[i], u32BufLen);
#endif
    }

    print_level(SV_INFO, "exit PLUG detection.\n");

    return NULL;
}

/* 算法插件状态线程 */
void * plug_stat_Body(void *pvArg)
{
    sint32 s32Ret = 0, i, j;
    PLUG_INFO_S *pstPlugInfo = (PLUG_INFO_S *)pvArg;
    struct statvfs stStatvfs = {0};
    uint64 u64TotalBytes = 0ll, u64AvailBytes = 0ll;
    uint32 u32TotalMB = 0, u32AvailMB = 0;
    sint32 s32Fd = -1;
    cJSON *pstJson = NULL, *pstPlugList = NULL, *pstPlug = NULL;
    char szBuf[64*1024];

    s32Ret = prctl(PR_SET_NAME, "plug_stat");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
    
    while (pstPlugInfo->bRunning)
    {
        //print_level(SV_DEBUG, "plug_stat_Body running...\n");
        sleep_ms(1000);
        
        /* 获取flash容量 */
        s32Ret = statvfs("/", &stStatvfs);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR,"statvfs failed for path /\n");       
            continue;
        }
        
        u64TotalBytes = stStatvfs.f_blocks * stStatvfs.f_bsize;
        u64AvailBytes = stStatvfs.f_bavail * stStatvfs.f_bsize;
        u32TotalMB = u64TotalBytes/1024/1024;
        u32AvailMB = u64AvailBytes/1024/1024;
        //print_level(SV_DEBUG, "%d(MB)/%d(MB)\n", u32AvailMB, u32TotalMB);
        pstJson = cJSON_CreateObject();
        if (NULL == pstJson)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            continue;
        }

        cJSON_AddItemToObject(pstJson, "fsTotalSizeMB", cJSON_CreateNumber(u32TotalMB));
        cJSON_AddItemToObject(pstJson, "fsRemainSizeMB", cJSON_CreateNumber(u32AvailMB));
        pstPlugList = cJSON_CreateArray();
        if (NULL == pstPlugList)
        {
            print_level(SV_ERROR, "cJSON_CreateArray failed.\n");
            continue;
        }
        
        cJSON_AddItemToObject(pstJson, "plugs", pstPlugList);
        for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
        {
            if (!pstPlugInfo->astPlugCtrlApiList[i].bValid)
            {
                continue;
            }

            pstPlug = cJSON_CreateObject();
            if (NULL == pstPlug)
            {
                print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
                continue;
            }

            cJSON_AddItemToObject(pstPlug, "plugPath", cJSON_CreateString(pstPlugInfo->astPlugCtrlApiList[i].szPlugPath));
            cJSON_AddItemToObject(pstPlug, "name", cJSON_CreateString(pstPlugInfo->astPlugCtrlApiList[i].stPlugInfo.szPlugName));
            cJSON_AddItemToObject(pstPlug, "version", cJSON_CreateString(pstPlugInfo->astPlugCtrlApiList[i].stPlugInfo.szPlugVer));
            cJSON_AddItemToObject(pstPlug, "running", cJSON_CreateBool(pstPlugInfo->astPlugCtrlApiList[i].bRunning));
            cJSON_AddItemToArray(pstPlugList, pstPlug);
        }

        memset(szBuf, 0, 64*1024);
        cJSON_PrintPreallocated(pstJson, szBuf, 64*1024, 0);
        remove("/var/info/plug-tmp");
        s32Fd = open("/var/info/plug-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: /root/plug/map-tmp failed. [err:%s]\n", strerror(errno));
            cJSON_Delete(pstJson);
            continue;
        }

        s32Ret = write(s32Fd, szBuf, strlen(szBuf));
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "write file: /root/plug/map-tmp failed. [err:%s]\n", strerror(errno));
            close(s32Fd);
            cJSON_Delete(pstJson);
            continue;
        }
        
        close(s32Fd);
        rename("/var/info/plug-tmp", DUMP_INFO_PLUG);
        cJSON_Delete(pstJson);
    }

    return NULL;
}

sint32 PLUG_Init(PLUG_CFG_PARAM_S *pstInitParam)
{
    sint32 s32Ret = 0, i, j;
    SV_BOOL bValidFd = SV_FALSE;
    CHN_ALG_E *apenChnAlg[ALG_MAX_CHN] = {0};
    CFG_PDS_PARAM *apstPdsParam[ALG_MAX_CHN] = {0};
    char *pszModelFile = NULL;
    char *pszModelFileList[3];

    if (NULL == pstInitParam)
    {
        return ERR_NULL_PTR;
    }

    for (i = 0; i < 4; i++)
    {
#if ALG_MUTLIT_BUFFER
        for(j = 0; j < 3; j++)
        {
            if (pstInitParam->as32MediaBufFd[i][j] > 0)
            {
                bValidFd = SV_TRUE;
            }
        }
#else
        if (pstInitParam->as32MediaBufFd[i] > 0)
        {
            bValidFd = SV_TRUE;
        }
#endif
    }

    if (!bValidFd)
    {
        return ERR_ILLEGAL_PARAM;
    }

    memset(&m_stPlugInfo, 0, sizeof(PLUG_INFO_S));
    s32Ret = pthread_mutex_init(&m_stPlugInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
    
    s32Ret = MS_Init();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MS_Init failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

#if ALG_MUTLIT_BUFFER
    s32Ret = MH_Init();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MH_Init failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    MH_PrintState(0);
#endif

    
    m_stPlugInfo.u32ChnNum = pstInitParam->u32ChnNum;
    for (i = 0; i < 4; i++)
    {
#if ALG_MUTLIT_BUFFER
        for (j = 0; j < 3; j++)
        {
            m_stPlugInfo.as32MediaBufFd[i][j] = pstInitParam->as32MediaBufFd[i][j];
        }
#else
		m_stPlugInfo.as32MediaBufFd[i] = pstInitParam->as32MediaBufFd[i];
#endif
    }

    if (0 != pstInitParam->u32Width && 0 != pstInitParam->u32Height)
    {
        m_stPlugInfo.u32Width = pstInitParam->u32Width;
        m_stPlugInfo.u32Height = pstInitParam->u32Height;
    }
    else
    {
        m_stPlugInfo.u32Width = plug_GetAlgWidth();
        m_stPlugInfo.u32Height = plug_GetAlgHeight();
    }

    m_stPlugInfo.stCfgParam = pstInitParam->stAlgParam;
    if (0 == access(ALG_PLUG_MAP_PATH, F_OK))
    {
        s32Ret = plug_LoadPlugs(m_stPlugInfo.astPlugCtrlApiList);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "plug_LoadPlugs failed. [err=%#x]\n", s32Ret);
        }
    }
    else
    {
        s32Ret = plug_ScanPlugs(m_stPlugInfo.astPlugCtrlApiList);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "plug_ScanPlugs failed. [err=%#x]\n", s32Ret);
        }
    }

    print_level(SV_INFO, "init successful. (%dx%d)\n", m_stPlugInfo.u32Width, m_stPlugInfo.u32Height);

    return SV_SUCCESS;
}

sint32 PLUG_Fini()
{
    sint32 i;

    pthread_mutex_destroy(&m_stPlugInfo.mutexLock);
    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (!m_stPlugInfo.astPlugCtrlApiList[i].bValid)
        {
            continue;
        }

        if (NULL != m_stPlugInfo.astPlugCtrlApiList[i].pvHandle)
        {
            dlclose(m_stPlugInfo.astPlugCtrlApiList[i].pvHandle);
            m_stPlugInfo.astPlugCtrlApiList[i].pvHandle = NULL;
        }
    }

    return SV_SUCCESS;
}

sint32 PLUG_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread;

    m_stPlugInfo.bRunning = SV_TRUE;
    s32Ret = pthread_create(&thread, NULL, plug_alg_Body, &m_stPlugInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    m_stPlugInfo.u32TidAlg = thread;
    s32Ret = pthread_create(&thread, NULL, plug_stat_Body, &m_stPlugInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    m_stPlugInfo.u32TidStat = thread;

    m_stPlugInfo.thpool  = thpool_init(10);    /* 创建线程池, 数量为 10 */

    return SV_SUCCESS;
}

sint32 PLUG_Stop()
{
    sint32 s32Ret = 0;
    pthread_t thread = m_stPlugInfo.u32TidAlg;
    pthread_t thread2 = m_stPlugInfo.u32TidStat;
    void *pvRetval = NULL;

    m_stPlugInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = pthread_join(thread2, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    thpool_wait(m_stPlugInfo.thpool);
    thpool_destroy(m_stPlugInfo.thpool);

    return SV_SUCCESS;
}

sint32 PLUG_ConfigSet(sint32 s32Index, char *pszJson)
{
    sint32 s32Ret, i, j;
    uint32 u32Num = 0;
    cJSON *pstJson = NULL, *pstPlugList = NULL, *pstPlugInfo = NULL, *pstTmp = NULL;
    char szPlugPath[256];

    if (NULL == pszJson)
    {
        return ERR_NULL_PTR;
    }

    print_level(SV_DEBUG, "s32Index:%d, json: %s\n", s32Index, pszJson);
    pstJson = cJSON_Parse(pszJson);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return SV_FAILURE;
    }
    
    if (0 == s32Index)
    {
        pstPlugList = cJSON_GetObjectItemCaseSensitive(pstJson, "plugs");
        if (NULL == pstPlugList || !cJSON_IsArray(pstPlugList))
        {
            print_level(SV_ERROR, "Get plugs failed.\n");
            return SV_FAILURE;
        }

        u32Num = cJSON_GetArraySize(pstPlugList);
        for (i = 0; i < u32Num; i++)
        {
            pstPlugInfo = cJSON_GetArrayItem(pstPlugList, i);
            if (NULL == pstPlugInfo)
            {
                continue;
            }

            pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "plugPath");
            if (NULL != pstTmp && cJSON_IsString(pstTmp))
            {
                strncpy(szPlugPath, pstTmp->valuestring, 255);
            }

            for (j = 0; j < ALG_PLUG_MAX_NUM; j++)
            {
                if (!m_stPlugInfo.astPlugCtrlApiList[j].bValid || 0 != strcmp(m_stPlugInfo.astPlugCtrlApiList[j].szPlugPath, szPlugPath))
                {
                    continue;
                }

                pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "enable");
                if (NULL != pstTmp && cJSON_IsBool(pstTmp))
                {
                    m_stPlugInfo.astPlugCtrlApiList[j].bEnable = pstTmp->valueint;
                }

                pstTmp = cJSON_GetObjectItemCaseSensitive(pstPlugInfo, "interval");
                if (NULL != pstTmp && cJSON_IsNumber(pstTmp))
                {
                    m_stPlugInfo.astPlugCtrlApiList[j].s32Interval = pstTmp->valueint;
                }

                print_level(SV_DEBUG, "szPlugPath:%s, bEnable:%d, s32Interval:%d\n", \
                    m_stPlugInfo.astPlugCtrlApiList[j].szPlugPath, m_stPlugInfo.astPlugCtrlApiList[j].bEnable, m_stPlugInfo.astPlugCtrlApiList[j].s32Interval);
            }
        }

        s32Ret = plug_WritePlugsMap(m_stPlugInfo.astPlugCtrlApiList);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "plug_WritePlugsMap failed. [err=%#x]\n", s32Ret);
            cJSON_Delete(pstJson);
            return s32Ret;
        }

        m_stPlugInfo.bPlugListChange = SV_TRUE;
    }
    else
    {
        sprintf(szPlugPath, "/root/plug/%03d", s32Index);
        print_level(SV_DEBUG, "szPlugPath:%s\n", szPlugPath);
        for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
        {
            if (!m_stPlugInfo.astPlugCtrlApiList[i].bValid || 0 != strcmp(m_stPlugInfo.astPlugCtrlApiList[i].szPlugPath, szPlugPath))
            {
                continue;
            }

            s32Ret = m_stPlugInfo.astPlugCtrlApiList[i].pfnPlug_ConfigUpdate(pszJson);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "pfnPlug_ConfigUpdate failed. [szPlugPath:%s]\n", m_stPlugInfo.astPlugCtrlApiList[i].szPlugPath);
            }
        }
    }

    cJSON_Delete(pstJson);
    
    return SV_SUCCESS;
}

sint32 PLUG_AddPlug(char *pszPlugFile)
{
    sint32 s32Ret = 0, i, j;
    SV_BOOL bExist = SV_FALSE;
    char szPlugPath[256];
    char szSoFilePath[256];
    char szCmd[256];
    void *pvHandle = NULL;
    PlugCheckModel pfnCheckModel = NULL;
    Plug_ResType pfnPlug_ResType = NULL;
    PLUG_RES_TYPE enResType;
    PLUG_CTRL_API stPlugCtrlApi = {0};

    if (NULL == pszPlugFile)
    {
        return ERR_NULL_PTR;
    }

    if (0 != access(pszPlugFile, F_OK))
    {
        print_level(SV_ERROR, "access file: %s failed.\n", pszPlugFile);
        return SV_FAILURE;
    }

    for (i = 1; i <= ALG_PLUG_MAX_NUM; i++)
    {
        sprintf(szPlugPath, "/root/plug/%03d", i);
        bExist = SV_FALSE;
        for (j = 0; j < ALG_PLUG_MAX_NUM; j++)
        {
            if (m_stPlugInfo.astPlugCtrlApiList[j].bValid && 0 == strcmp(m_stPlugInfo.astPlugCtrlApiList[j].szPlugPath, szPlugPath))
            {
                bExist = SV_TRUE;
                break;
            }
        }

        if (!bExist)
        {
            break;
        }
    }

    if (bExist)
    {
        print_level(SV_ERROR, "plug full!\n");
        return ERR_NOBUF;
    }
	
    sprintf(szCmd, "openssl enc -aes-256-cbc -d -in %s -out /tmp/plug.tar.gz -pass pass:a47plug", pszPlugFile);
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "szCmd: %s failed.\n", szCmd);
        goto err_exit;
    }

    sprintf(szCmd, "mkdir -p %s", szPlugPath);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "szCmd: %s failed.\n", szCmd);
        goto err_exit;
    }
    
    sprintf(szCmd, "tar -zxf /tmp/plug.tar.gz -C %s", szPlugPath);
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "szCmd: %s failed.\n", szCmd);
        goto err_exit;
    }

    sprintf(szSoFilePath, "%s/so/%s", szPlugPath, ALG_PLUG_SO_NAME);
    s32Ret = access(szSoFilePath, F_OK);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "access file: %s failed.\n", szSoFilePath);\
        goto err_exit;
    }

    pvHandle = dlopen(szSoFilePath, RTLD_LAZY);
    if (NULL == pvHandle)
    {
        print_level(SV_ERROR, "dlopen %s failed. [err:%s]\n", szSoFilePath, strerror(errno));
        goto err_exit;
    }

    pfnPlug_ResType = (PlugGetInfo)dlsym(pvHandle, "AlgPlug_ResType");
    if (NULL == pfnPlug_ResType)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_ResType failed. [err:%s]\n", szSoFilePath, strerror(errno));
        dlclose(pvHandle);
        goto err_exit;
    }

    enResType = pfnPlug_ResType();
    if (enResType >= PLUG_RES_BUTT)
    {
        print_level(SV_ERROR, "[%s] unsupport result type: %d\n", szSoFilePath, enResType);
        dlclose(pvHandle);
        goto err_exit;
    }

    pfnCheckModel = (PlugCheckModel)dlsym(pvHandle, "AlgPlugCheckModel");
    if (NULL == pfnCheckModel)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlugCheckModel failed. [err:%s]\n", szSoFilePath, strerror(errno));
        dlclose(pvHandle);
        goto err_exit;
    }

    s32Ret = pfnCheckModel(szPlugPath);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "[%s] pfnCheckModel failed. [err=%#x]\n", szSoFilePath, s32Ret);
        dlclose(pvHandle);
        goto err_exit;
    }

    dlclose(pvHandle);
    strcpy(stPlugCtrlApi.szPlugPath, szPlugPath);
    s32Ret = plug_LoadPlugSo(szSoFilePath, &stPlugCtrlApi);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "plug_LoadPlugSo failed. [err=%#x]\n", s32Ret);
        goto err_exit;
    }

    pthread_mutex_lock(&m_stPlugInfo.mutexLock);
    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (!m_stPlugInfo.astPlugCtrlApiList[i].bValid)
        {
            m_stPlugInfo.astPlugCtrlApiList[i] = stPlugCtrlApi;
            break;
        }
    }

    pthread_mutex_unlock(&m_stPlugInfo.mutexLock);
    if (i >= ALG_PLUG_MAX_NUM)
    {
        print_level(SV_ERROR, "plug full!\n");
        return ERR_NOBUF;
    }

    s32Ret = plug_WritePlugsMap(m_stPlugInfo.astPlugCtrlApiList);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "plug_WritePlugsMap failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    
    sprintf(szCmd, "rm -f %s", pszPlugFile);
    SAFE_System(szCmd, NORMAL_WAIT_TIME);
    SAFE_System("rm -f /tmp/plug.tar.gz", NORMAL_WAIT_TIME);
    m_stPlugInfo.bPlugListChange = SV_TRUE;
    
    return SV_SUCCESS;

err_exit:
    sprintf(szCmd, "rm -f %s", pszPlugFile);
    SAFE_System(szCmd, NORMAL_WAIT_TIME);
    sprintf(szCmd, "rm -rf %s", szPlugPath);
    SAFE_System(szCmd, NORMAL_WAIT_TIME);
    SAFE_System("rm -f /tmp/plug.tar.gz", NORMAL_WAIT_TIME);
    return SV_FAILURE;
}

sint32 PLUG_DeletePlug(char *pszPlugPath)
{
    sint32 s32Ret = 0, i;
    char szCmd[128] = {0};

    if (NULL == pszPlugPath)
    {
        return ERR_NULL_PTR;
    }

    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (m_stPlugInfo.astPlugCtrlApiList[i].bValid && 0 == strcmp(m_stPlugInfo.astPlugCtrlApiList[i].szPlugPath, pszPlugPath))
        {
            if (0 != access(pszPlugPath, F_OK))
            {
                print_level(SV_ERROR, "path: %s unexist.\n", pszPlugPath);
                m_stPlugInfo.astPlugCtrlApiList[i].bValid = SV_FALSE;
                continue;
            }
            
            print_level(SV_INFO, "try to delete plug: %s\n", pszPlugPath);
            pthread_mutex_lock(&m_stPlugInfo.mutexLock);
            s32Ret = m_stPlugInfo.astPlugCtrlApiList[i].pfnPlug_Fini();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "pfnPlug_Fini failed. [plug:%s, err=%#x]\n", pszPlugPath, s32Ret);
            }
            
            dlclose(m_stPlugInfo.astPlugCtrlApiList[i].pvHandle);
            memset(&m_stPlugInfo.astPlugCtrlApiList[i], 0, sizeof(PLUG_CTRL_API));
            m_stPlugInfo.bPlugListChange = SV_TRUE;
            pthread_mutex_unlock(&m_stPlugInfo.mutexLock);
            sprintf(szCmd, "rm -rf %s", pszPlugPath);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "szCmd: %s failed.\n", szCmd);
            }

            s32Ret = plug_WritePlugsMap(m_stPlugInfo.astPlugCtrlApiList);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "plug_WritePlugsMap failed. [err=%#x]\n", s32Ret);
            }
            
            print_level(SV_INFO, "delete plug: %s successful!\n", pszPlugPath);
            break;
        }
    }

    return SV_SUCCESS;
}

sint32 PLUG_UpdatePlug(char *pszOldPlugFile,char* pszNewPlugFile)
{
	sint32 s32Ret = 0, i, j;
    char szPlugPath[256];
    char szSoFilePath[256];
    char szCmd[256];
    void *pvHandle = NULL;
    PlugCheckModel pfnCheckModel = NULL;
    Plug_ResType pfnPlug_ResType = NULL;
    PLUG_RES_TYPE enResType;
    PLUG_CTRL_API stPlugCtrlApi = {0};

    if (NULL == pszOldPlugFile || NULL == pszNewPlugFile)
    {
        return ERR_NULL_PTR;
    }

    if (0 != access(pszNewPlugFile, F_OK))//新的算法插件
    {
        print_level(SV_ERROR, "access file: %s failed.\n", pszNewPlugFile);
        return SV_FAILURE;
    }

	if (0 != access(pszOldPlugFile, F_OK))//旧的算法插件 
    {
        print_level(SV_ERROR, "access file: %s failed.\n", pszOldPlugFile);
        return SV_FAILURE;
    }
		
    sprintf(szCmd, "openssl enc -aes-256-cbc -d -in %s -out /tmp/plug.tar.gz -pass pass:a47plug", pszNewPlugFile);
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "szCmd: %s failed.\n", szCmd);
        goto err_exit;
    }
    
    sprintf(szCmd, "tar -zxf /tmp/plug.tar.gz -C %s", pszOldPlugFile);
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "szCmd: %s failed.\n", szCmd);
        goto err_exit;
    }
	
    sprintf(szSoFilePath, "%s/so/%s", pszOldPlugFile, ALG_PLUG_SO_NAME);
    s32Ret = access(szSoFilePath, F_OK);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "access file: %s failed.\n", szSoFilePath);\
        goto err_exit;
    }

	pthread_mutex_lock(&m_stPlugInfo.mutexLock);
    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (strcmp(m_stPlugInfo.astPlugCtrlApiList[i].szPlugPath,pszOldPlugFile)==0)
        {
			dlclose(m_stPlugInfo.astPlugCtrlApiList[i].pvHandle);			
            break;
        }
    }
    pthread_mutex_unlock(&m_stPlugInfo.mutexLock);
	
	
    pvHandle = dlopen(szSoFilePath, RTLD_LAZY);
    if (NULL == pvHandle)
    {
        print_level(SV_ERROR, "dlopen %s failed. [err:%s]\n", szSoFilePath, strerror(errno));
        goto err_exit;
    }

    pfnPlug_ResType = (PlugGetInfo)dlsym(pvHandle, "AlgPlug_ResType");
    if (NULL == pfnPlug_ResType)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlug_ResType failed. [err:%s]\n", szSoFilePath, strerror(errno));
        dlclose(pvHandle);
        goto err_exit;
    }

    enResType = pfnPlug_ResType();
    if (enResType >= PLUG_RES_BUTT)
    {
        print_level(SV_ERROR, "[%s] unsupport result type: %d\n", szSoFilePath, enResType);
        dlclose(pvHandle);
        goto err_exit;
    }

    strcpy(stPlugCtrlApi.szPlugPath, pszOldPlugFile);
    s32Ret = plug_LoadPlugSo(szSoFilePath, &stPlugCtrlApi);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "plug_LoadPlugSo failed. [err=%#x]\n", s32Ret);
        goto err_exit;
    }

    pthread_mutex_lock(&m_stPlugInfo.mutexLock);
    for (i = 0; i < ALG_PLUG_MAX_NUM; i++)
    {
        if (strcmp(m_stPlugInfo.astPlugCtrlApiList[i].szPlugPath,pszOldPlugFile)==0)
        {
			print_level(SV_DEBUG,"%s update plug info!\n",pszOldPlugFile);
            m_stPlugInfo.astPlugCtrlApiList[i] = stPlugCtrlApi;
            break;
        }
    }

    pthread_mutex_unlock(&m_stPlugInfo.mutexLock);
    
    s32Ret = plug_WritePlugsMap(m_stPlugInfo.astPlugCtrlApiList);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "plug_WritePlugsMap failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
	
    sprintf(szCmd, "rm -f %s", pszNewPlugFile);
    SAFE_System(szCmd, NORMAL_WAIT_TIME);
    SAFE_System("rm -f /tmp/plug.tar.gz", NORMAL_WAIT_TIME);
	sprintf(szCmd, "rm -f %s/%s", pszOldPlugFile, ALG_PLUG_CONFIG);
	SAFE_System(szCmd, NORMAL_WAIT_TIME);
    m_stPlugInfo.bPlugListChange = SV_TRUE;

	pfnCheckModel = (PlugCheckModel)dlsym(pvHandle, "AlgPlugCheckModel");
    if (NULL == pfnCheckModel)
    {
        print_level(SV_ERROR, "[%s] dlsym AlgPlugCheckModel failed. [err:%s]\n", szSoFilePath, strerror(errno));
        dlclose(pvHandle);
        goto err_exit;
    }
	s32Ret = pfnCheckModel(pszOldPlugFile);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "[%s] pfnCheckModel failed. [err=%#x]\n", szSoFilePath, s32Ret);
        dlclose(pvHandle);
        goto err_exit;
    }
	
    dlclose(pvHandle);
    return SV_SUCCESS;

err_exit:
    sprintf(szCmd, "rm -f %s", pszNewPlugFile);
    SAFE_System(szCmd, NORMAL_WAIT_TIME);
    sprintf(szCmd, "rm -rf %s", pszOldPlugFile);
    SAFE_System(szCmd, NORMAL_WAIT_TIME);
    SAFE_System("rm -f /tmp/plug.tar.gz", NORMAL_WAIT_TIME);
    return SV_FAILURE;
	return SV_SUCCESS;
}

