<h4>introduce</h4>
<p>There are two compressed files as follows: repo.tar.gz --- RockChip original SDK (This file is cut into repo.part01~09.rar fragment files, please download them all before decompression)</p>
<h4>RockChip original SDK download links:</h4>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part01.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part01.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part02.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part02.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part03.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part03.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part04.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part04.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part05.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part05.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part06.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part06.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part07.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part07.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part08.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part08.rar</a></p>
<p><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part09.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/rv1126_rv1109_sdk_v1.8.0_20210224/repo.part09.rar</a></p>
<p>&nbsp;</p>
<h4>HD900_xxxxxxxx.xxxx.tar.gz --- STONKAM SDK</h4>
<p>To install the SDKs, please unzip the STONKAM SDK first and refer to the document: HD900/doc/en/2.Development Environment Construction and Project Compilation.docx </p>
<p>there are a different version of the STONKAM SDK download link as follows. It is recommended to use the latest version for development.</p>
<hr />

<h4>2025-06-09</h4>
<p><strong>HD900_20250609.5622</strong> is stable for ADA32E.</p>
<ol>
<li><p>ADA32E supports trigger line input and output;</p>
</li>
<li><p>ADA32E supports wide temperature sensor;</p>
</li>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20250609.5622.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20250609.5622.tar.gz</a></p>
</li>
</ol>

<h4>2025-05-08</h4>
<p><strong>HD900_20250508.5931</strong> is stable for ADA32.</p>
<ol>
<li><p>Update the 2.3mm iq file to optimize the problem of redness in the sun;</p>
</li>
<li><p>Optimize the calibration process;</p>
</li>
<li><p>Algorithm model of synchronous V7.3;</p>
</li>
<li><p>Support online upgrades on web ui</p>
</li>
<li><p>The ADA38 wide-temperature version turns on the WDR function at room temperature and turns off the WDR function at high temperature</p>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20250508.5931.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20250508.5931.tar.gz</a></p>
</li>
</ol>

<h4>2024-11-08</h4>
<p><strong>HD900_20241108.5405</strong> is stable for DMS31.</p>
<ol>
<li>Optimize algorithm effect.</li>
<li>Add pitch distraction detection.</li>
<li>Add automatic calibration function (It is only available when there is a GPS module and the speed is ≥40km/h).</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20241108.5405.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20241108.5405.tar.gz</a></p>
<h4>2024-10-25</h4>
<p><strong>HD900_20240801.4822</strong> is stable for ADA32N.</p>
<ol>
<li><p>A32N is compatible with the new hardware light board.</p>
</li>
<li><p>Remove the 720P option from the sub stream resolution and add the 540P option.</p>
</li>
<li><p>Support alarm input and output ports.</p>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240801.4822.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240801.4822.tar.gz</a></p>
</li>

</ol>
<h4>2024-10-17</h4>
<p><strong>HD900_20240827.5148</strong> is stable for ADA32.</p>
<ol>
<li><p>Camera blocking alarm, Spanish alarm voice changed from CAMARA ANORMAL to &quot;CAMARA OBSTRUIDA&quot;;</p>
</li>
<li><p>Modify part of the Spanish translation;</p>
</li>
<li><p>The IQ of the 2.3mm lens in 2053 has been updated: the white balance has been optimized for the 2.3mm lens to improve the redness of the road surface under the sun;</p>
</li>
<li><p>Updated the algorithm model and optimized some common false detection problems in forklift applications in the warehouse scene, such as forklift self, yellow and black low column, halo, black pole, etc.;
5, support 2.3mm lens calibration and ranging function;
6, occlusion alarm increase audio control switch, time threshold, trigger output switch of a single trigger line;
7, support RS232 output alarm information;</p>
</li>
<li><p>Update the algorithm detection model to V4.1, and the corresponding MD5 code is db1c8054;
9, the face Mosaic is changed to the full picture of people are added;</p>
</li>
<li><p>Change the volume setting range to 0 to 100.</p>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240827.5148.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240827.5148.tar.gz</a></p>
</li>

</ol>
<h4>2024-10-14</h4>
<p><strong>HD900_20241014.5255</strong> is stable for ADA32E.</p>
<ol>
<li>ADA32E supports USB access to WIFI.</li>
<li>ADA32E supports USBA to CAN function.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20241014.5255.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20241014.5255.tar.gz</a></p>
<h4>2024-08-30</h4>
<p><strong>HD900_20240830.5172</strong> is stable for DMS31.</p>
<ol>
<li>Optimize algorithm effect.</li>
<li>Fix the problem that simcom7600CE module can&#39;t access the Internet and causes CMS to drop offline.</li>
<li>Sub-stream supports CIF resolution.</li>
<li>Optimize the recording function when repairing TF card.</li>
<li>Added fatigue Level2 detection function.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240830.5172.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240830.5172.tar.gz</a></p>
<h4>2024-8-1</h4>
<p><strong>HD900_20240801.4822</strong> is stable for ADA32N.</p>
<p>1.A32N is compatible with the new hardware light board.
2.Remove the 720P option from the sub stream resolution and add the 540P option.
3.Support alarm input and output ports.</p>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240801.4822.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240801.4822.tar.gz</a></p>
<h4>2024-5-7</h4>
<p><strong>HD900_20240507.4706</strong> is stable for DMS31.</p>
<ol>
<li>Add CVBS output support (need to use CVBS wire).</li>
<li>Increase the support of Ministry Standard 808.</li>
<li>Add reflective scene configuration options (optimize the image effect for workers wearing reflective clothes).</li>
<li>Update the models of smoking, phone call, drinking and face detection, and optimize the frame rate of DMS algorithm.</li>
<li>Optimize the CMS connection function.</li>
<li>Add the alarm icon switch, after turning on, the text OSD and the icon OSD are displayed at the same time.</li>
<li>Modify the volume progress value to 1.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240507.4706.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240507.4706.tar.gz</a></p>
<h4>2024-3-7</h4>
<p><strong>HD900_20240307.4485</strong> is stable for ADA32N.</p>
<ol>
<li>A32N is compatible with the new hardware light board.</li>
<li>Solve the problem of sensor unknown display on the web.</li>
<li>Boot hidden the WiFi hotspot name.</li>
<li>Fix audio noise problem.</li>
<li>Fix the issue of not displaying the main code stream when using vertical ROI line styles.</li>
<li>Default sub code stream open.</li>
<li>Change the default IP to 88.126.</li>
<li>Fix the problem of probability collapse of main code stream occlusion during vertical ROI.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240307.4485.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240307.4485.tar.gz</a></p>
<h4>2024-2-21</h4>
<p><strong>HD900_20240226.4451</strong> is stable for ADA32.</p>
<ol>
<li>Modify each line of the three trigger lines to choose which kind of target to output to. At present, it supports three kinds of targets: human, vehicle and occlusion alarm, and retains the original single trigger line switch.</li>
<li>Optimize the slow start-up and drawing of some hardware.</li>
<li>Add the function of configuring ADA32 through CAN.</li>
<li>Increase 201244 customer specific requirements: (1) dedicated CAN protocol, which retains only a single data frame, including red, yellow and green area alarm information, occlusion alarm information, and software version; (2) the default CAN baud rate is 250kbps, P system.</li>
<li>202264 customers use a dedicated model.</li>
<li>200598 customers add dedicated configurations with customer item numbers 93833 and 93726.</li>
<li>100214 customers turn on Alarm In by default and turn off the alarm output in the green area.</li>
<li>200930 customers obtain configuration parameters through CAN, and only return the corresponding parameters, which need to be matched with DVR456 test.</li>
<li>Fix the problem that the test type is fixed to check the person and car after ordinary customers are equipped with IRCUT.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240226.4451.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240226.4451.tar.gz</a></p>
<h4>2024-1-8</h4>
<p><strong>HD900_20240108.4262</strong> is stable for ADA32.</p>
<ol>
<li>Add 2093 iq files needed to configure IRCUT.</li>
<li>Update the MCU program to be compatible with new CAN materials.</li>
<li>The program is modified based on the 20231226.4262 version passed in the last test. You only need to test the 2093 configuration IRCUT and the wire with MCU (CAN, acousto-optic alarm, gps, three-wire trigger) to test.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240108.4262.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20240108.4262.tar.gz</a></p>
<h4>2023-12-26</h4>
<p><strong>HD900_20231226.4262</strong> is stable for ADA32.</p>
<ol>
<li>Optimize the calibration mode, eliminating the need to manually frame the position of the QR code.</li>
<li>Add pedestrian frame overlay mosaic function, off by default. Algorithm parameters add mosaic switch, mosaic size options.</li>
<li>Optimize the occlusion alarm function, occlusion for 3 to 4 seconds will trigger the occlusion alarm.</li>
<li>Added ellipse ROI type to fix possible omissions when using elliptical regions.</li>
<li>Added ADA32IRCUT configuration to support IRCUT infrared switching.</li>
<li>Fixed the problem that the program crashed when the bit rate of H265 was high.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20231226.4262.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20231226.4262.tar.gz</a></p>
<h4>2023-11-15</h4>
<p><strong>HD900_20231115.4079</strong> is stable for ADA32.</p>
<ol>
<li>Modify the 200019 customer function of ADA32V2 to only detect people when the input is not triggered.
two。. Set mirroring and flip to use v4l2 directly.</li>
<li>Add dump message support for occlusion alarm, and switch to the new version of CAN protocol before sending CAN data for occlusion alarm.</li>
<li>Add ADA48 function required by 201623 customers, dedicated boot LOGO, dedicated webui login interface, administrator and non-administrator accounts, dedicated wifi name, light board flashing, fisheye removal function (only available with 1.99lenses), and output AlarmOut alarm signal when the camera screen / algorithm is abnormal. ADA48 needs to add a light board to the hardware of A32 and focus on testing.</li>
<li>Fixed the abnormal quality setting of snap0 image capture.</li>
<li>Fix the webui problem in ADA48.</li>
<li>Roll back the model version of the testing person and the testing vehicle, which is version ********.3144</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20231115.4079.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20231115.4079.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2023-9-20</h4>
<p><strong>HD900_20230920.3908</strong> is stable for DMS31.</p>
<ol>
<li>Support CMS to import configuration and face ID files.</li>
<li>Add offline GSP information upload function.</li>
<li>Increase the alarm without safety helmet.</li>
<li>Open the function of face timing detection.</li>
<li>Modify the websocket alarm push function. If the face ID of the current device is not empty (that is, face recognition is successful and out of date), the face information will be added to the alarm event synchronously.</li>
<li>Modify the failure of mp4 playback video, fix the crash of DVR used as IPC connection, and fix the problem that face can not be detected when assigning virtual speed.</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230920.3908.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230920.3908.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2023-9-11</h4>
<p><strong>HD900_20230911.3911</strong> is stable for ADA32.</p>
<ol>
<li>Fixed the problem of USB_HUB + third line triggering alarm aging and dropping.</li>
<li>Fix the problem of CVBS wire + three-wire trigger alarm aging drop.</li>
<li>Added advanced settings to support the overlap judgment strategy of pedestrian frame and ROI area, including the entire pedestrian frame or the bottom of pedestrian box.</li>
<li>200055 VT customer requirements change: add dedicated wifi name and password.
Add 200055A requirements: there is one person in the screen, two people in the green trigger line high level screen, more than two people in the yellow trigger line high level screen, and the red trigger line high level.</li>
<li>Add 200019 customers:
-the human and vehicle detection function is used by default.
-AlarmIn function is enabled by default.
-AlarmIn does not trigger the input high level, only detects people. AlarmIn has trigger input high level to detect people and cars.</li>
<li>Change 200032: add a switch to dynamically switch the alarm icon; turn it off by default.</li>
<li>Add 201217 demand: add bear detection model</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230911.3911.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230911.3911.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2023-8-9</h4>
<p><strong>HD900_20230809.3773</strong> is stable for ADA32.</p>
<ol>
<li>Support connection to DVR (need to be configured as 88 network segment)</li>
<li>Add websocket alarm push</li>
<li>The main stream supports H265 format encoding
4.200055 The customer replaced the webui and boot logo</li>
<li>The 202122 and 201819 customers added the tamper alarm switch</li>
<li>Added German language option</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230809.3773.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230809.3773.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2023-6-5</h4>
<p><strong>HD900_20230605.3496</strong> is stable for ADA32.</p>
<ol>
<li>Repair the output of the alarm of the third road, the output of the third road, the aging time is too long, the alarm has a delay problem</li>
<li>WebUI newly added plate -type ROI area</li>
<li>200055 Customer new special logo</li>
<li>A38 with light version integrates to the general version</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230605.3496.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20230605.3496.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2023-3-7</h4>
<p><strong>HD900_********.3144</strong> is stable for ADA32.</p>
<ol>
<li>CVBS system output increases transparency</li>
<li>Fix the problem of the Alarmout trigger method that cannot be adapted to the three -color alarm line of red, yellow and green</li>
<li>Fix Alarmout&#39;s duration that cannot adapt to the problem of red, yellow and green three -color alarm lines (increase the default value of Auto, trigger the default 1s of three -line trigger 1s, single -line trigger default 2S)</li>
<li>Increase the rotating 90 degrees function, support HD250 electronic rearview mirror</li>
<li>Increase French support</li>
<li>Low delay optimization of algorithms, low -delay optimization of AHD drawing (no test group is required)</li>
<li>The algorithm model is updated, adding a variety of scenes such as reflector and construction sites (no test group is required)</li>
<li>CAN protocol modification, support instruction setting protocol version and Alarm in function (the test group does not need to be tested)</li>
<li>Fix the configuration of ROI collapse, modify the alarm output Auto translation</li>

</ol>
<p><strong>download link: </strong><a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_********.3144.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_********.3144.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2022-12-15</h4>
<p><strong>HD900_20221215.2850</strong> is stable for ADA32.</p>
<ol>
<li><em>200032 Customer added German, Italian, French functions</em></li>
<li><em>Add 202122 customer dedicated configuration and add 200001 customers</em></li>
<li><em>Add advanced Settings: add the AlarmOut high level and low level output switch, and increase the sensitivity setting from Low to High threshold setting</em></li>
<li><em>Support the CVBS function</em></li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20221215.2850.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20221215.2850.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2022-11-14</h4>
<p>HD900_20221103.2719 is stable for ADA32.</p>
<ol>
<li>add alg/demo</li>

</ol>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20221103.2719.tar.gz' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/HD900_20221103.2719.tar.gz</a></p>
<p>&nbsp;</p>
<h4>2022-09-14</h4>
<p>HD900_20220914.2501 is stable for ADA32.</p>
<ol>
<li>Added 201352 customer and 200789 customer-only version</li>
<li>ADA32/ADA37/ADA38/ADA43/ADA45 device program sharing</li>
<li>Optimize the SDK package, support API to send data to CAN port</li>

</ol>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h4>Related documentation on algorithm development</h4>
<p><strong>download link:</strong> <a href='http://dvr.stonkam.com:6061/tmp/rk_sdk/Deploy_YOLOv5_on_RV1126.rar' target='_blank' >http://dvr.stonkam.com:6061/tmp/rk_sdk/Deploy_YOLOv5_on_RV1126.rar</a></p>
<p>&nbsp;</p>
<p>&nbsp;</p>
