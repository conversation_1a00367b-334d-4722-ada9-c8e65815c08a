/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file：       file.cpp
author:     lyn
version:    1.0.0
date:       2023-12-06
function:   recorder file source file
notice:     none
*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <list>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <regex>
#include <sys/time.h>

#include "r_file.h"
#include "r_mp4.h"
#include "r_avi.h"
#include "r_csv.h"
#include "r_jpg.h"

#include "r_common.h"
#include "board.h"
#include "storage.h"
#include "print.h"

namespace recorder{

const char* TransNumber2EventType(REC_TYPE_E enRecType)
{
    const char *p = NULL;
    switch (enRecType)
    {
        case REC_TYPE_NORMAL:                   p = "NM"; break;
        case REC_TYPE_FATIGUE:
        case REC_TYPE_KSS7_MILD_FATIGUE:
            p = "FT";
            break;
        case REC_TYPE_DISTRACTION:              p = "DS"; break;
        case REC_TYPE_NO_DRIVER:                p = "ND"; break;
        case REC_TYPE_SMOKE:                    p = "SM"; break;
        case REC_TYPE_PHONE:                    p = "CA"; break;
        case REC_TYPE_RADAR:                    p = "RA"; break;
        case REC_TYPE_YAWN:                     p = "YW"; break;
        case REC_TYPE_FACE_AUTH_FAILED:         p = "AF"; break;
        case REC_TYPE_FACE_AUTH_TIMEOUT:        p = "AT"; break;
        case REC_TYPE_NO_MASK:                  p = "NK"; break;
        case REC_TYPE_SUNGLASS:                 p = "SG"; break;
        case REC_TYPE_FATIGUE_L2:
        case REC_TYPE_KSS8_MODERATE_FATIGUE:
            p = "L2";
            break;
        case REC_TYPE_PD_ROI:                   p = "PB"; break;
        case REC_TYPE_SEATBELT:                 p = "NB"; break;
        case REC_TYPE_SHELTER:                  p = "CO"; break;
        case REC_TYPE_DRINKEAT:                 p = "DE"; break;
        case REC_TYPE_CHANGE_GUARD:             p = "CG"; break;
        case REC_TYPE_OVERSPEED:                p = "OS"; break;
        case REC_TYPE_NO_HELMET:                p = "NH"; break;
        //case REC_TYPE_KSS7_MILD_FATIGUE:        p = "LF"; break;
        //case REC_TYPE_KSS8_MODERATE_FATIGUE:    p = "MF"; break;
        case REC_TYPE_KSS9_SEVERE_FATIGUE:      p = "SF"; break;
        case REC_TYPE_USER_ALARM:               p = "UA"; break;
        default:                                p = "NM"; break;
    }
    return p;
}

const char* TransNumber2FileType(REC_FILE_E enFile)
{
    const char *p = NULL;
    switch (enFile)
    {
        case R_FILE_MP4:            p = "mp4"; break;
        case R_FILE_AVI:            p = "avi"; break;
        case R_FILE_JPG:            p = "jpg"; break;
        case R_FILE_CSV:            p = "csv"; break;
        default:                    p = "mp4"; break;
    }
    return p;
}

STORAGE_POS_E Trans2StoragePos(REC_POS_E enRecPos, sint32 subIdx)
{
    STORAGE_POS_E enStoPos = STORAGE_MAIN_SD1;
    switch (enRecPos)
    {
        case R_POS_SD:
            enStoPos = STORAGE_MAIN_SD1 + subIdx;
            break;
        case R_POS_UDISK:
            enStoPos = STORAGE_EXTRA_SD + subIdx;
            break;
        case R_POS_EMMC:
            enStoPos = STORAGE_INNER_EMMC + subIdx;
            break;
        case R_POS_MEM:
            enStoPos = STORAGE_MEMORY + subIdx;
            break;
        default :
            enStoPos = STORAGE_UNKNOWN;
    }
    return enStoPos;
}

REC_POS_E Trans2RecorderPos(STORAGE_POS_E enStoPos, sint32 *pSubIdx)
{
    REC_POS_E enPos = R_POS_UNKNOW;
    *pSubIdx = 0;
    switch (enStoPos)
    {
        case STORAGE_MAIN_SD1:
            *pSubIdx = 0;
            enPos = R_POS_SD;
            break;
        case STORAGE_MAIN_SD2:
            *pSubIdx = 1;
            enPos = R_POS_SD;
            break;
        case STORAGE_MAIN_SD3:
            *pSubIdx = 2;
            enPos = R_POS_SD;
            break;
        case STORAGE_INNER_EMMC:
            *pSubIdx = 0;
            enPos = R_POS_EMMC;
            break;
        case STORAGE_EXTRA_SD:
            *pSubIdx = 0;
            enPos = R_POS_UDISK;
            break;
        case STORAGE_MEMORY:
            *pSubIdx = 0;
            enPos = R_POS_MEM;
        default:
            break;
    }
    return enPos;
}

/******************************************************************************
 * 函数功能: 获取录像路径
 * 输入参数: enPos -- 卡位置 enRecType -- 录像类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32  TransRootPath(STORAGE_POS_E enPos, REC_OBJ_E enObj, char *szBuf)
{
    char *pszMediaPath = NULL;

    if (enPos > STORAGE_MAIN_ALL)
    {
        return ERR_INVALID_CHNID;
    }

    pszMediaPath = STORAGE_GetMediaPath(enPos);
    if (NULL == pszMediaPath || NULL == szBuf)
    {
        return ERR_NULL_PTR;
    }

    if (REC_NORAML == enObj)
    {
        sprintf(szBuf, "%s/normal", pszMediaPath);
    }
    else if (REC_PIC == enObj)
    {
        sprintf(szBuf, "%s/picture", pszMediaPath);
    }
    else if (REC_INFO == enObj)
    {
        sprintf(szBuf, "%s/file", pszMediaPath);
    }
    else if (REC_MAN == enObj)
    {
        sprintf(szBuf, "%s/manual", pszMediaPath);
    }
    else
    {
        sprintf(szBuf, "%s/alarm", pszMediaPath);
    }

    return SV_SUCCESS;
}
REC_POS_E TransPath2RecPos(char *path, sint32 *pSubIdx)
{
    STORAGE_POS_E enStoPos;
    REC_POS_E     enRecPos = R_POS_UNKNOW;
    sint32        subIdx = -1;
    char *pc = NULL;
    char *mp = NULL;

    for (enStoPos = STORAGE_MAIN_SD1; enStoPos<=STORAGE_EXTRA_SD; enStoPos = enStoPos+1)
    {
        mp = STORAGE_GetMediaPath(enStoPos);
        if (NULL != mp)
        {
            pc = strstr(path, mp);
            if (NULL != pc)
            {
                enRecPos = Trans2RecorderPos(enStoPos, &subIdx);
                break;
            }
        }
    }
    *pSubIdx = subIdx;
    return enRecPos;
}

REC_OBJ_E TransPath2RecObj(char *path)
{
    char        *pc     = NULL;
    REC_OBJ_E   enObj   = REC_UNKNOW;

    pc = strstr(path, "normal");
    if (NULL != pc)
    {
        enObj = REC_NORAML;
        goto skip;
    }

    pc = strstr(path, "alarm");
    if (NULL != pc)
    {
        enObj = REC_ALARM;
        goto skip;
    }

    pc = strstr(path, "picture");
    if (NULL != pc)
    {
        enObj = REC_PIC;
        goto skip;
    }

    pc = strstr(path, "file");
    if (NULL != pc)
    {
        enObj = REC_INFO;
        goto skip;
    }

skip:
    return enObj;
}


sint32 TransPath2FileName(const char *pszFilePath, uint32 u32Size, REC_FILE_NAME_S *pstFileNameParams)
{
    sint32                  s32OffSet           = 0;
    REC_FILE_NAME_S         stFileNameParams    = {0};
    char                    *pszFileName        = NULL;
    char                    szPath[256]         = {0};
    char                    szPlateNum[128]     = {0};
    char                    *pc                 = NULL;

    if ((NULL == pszFilePath) || (0 ==u32Size) || (u32Size > REC_FILE_NAME_LEN))
    {
        return ERR_ILLEGAL_PARAM;
    }

    memcpy(szPath, pszFilePath, u32Size);
    pszFileName = strrchr(szPath, '/');
    if (NULL == pszFileName)
    {
        print_level(SV_ERROR, "strrchr failed.\n");
        return SV_FAILURE;
    }
    s32OffSet = pszFileName - szPath + 1;

    if (s32OffSet < 0)
    {
        return SV_FAILURE;
    }
    sscanf(szPath+s32OffSet, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
                    ,&stFileNameParams.s32Year\
                    ,&stFileNameParams.s32Month\
                    ,&stFileNameParams.s32Day\
                    ,&stFileNameParams.s32Hour\
                    ,&stFileNameParams.s32Minute\
                    ,&stFileNameParams.s32Second\
                    ,stFileNameParams.cEventType\
                    ,&stFileNameParams.s32Duration\
                    ,&stFileNameParams.s32Size\
                    ,&stFileNameParams.s32Width\
                    ,&stFileNameParams.s32Height\
                    ,&stFileNameParams.s32FrameRate\
                    ,&stFileNameParams.s32BitRate\
                    ,stFileNameParams.cPlateNum\
                    ,&stFileNameParams.s32ChNum\
                    ,&stFileNameParams.u64DeviceID\
                    ,&stFileNameParams.u8Flag\
                    ,&stFileNameParams.s32Msec\
                    ,&stFileNameParams.s32PreRecordMsec\
                    ,stFileNameParams.cVersionNum\
                    ,stFileNameParams.cCustomerNum\
                    ,stFileNameParams.cTimeZone\
                    ,&stFileNameParams.cDST\
                    ,stFileNameParams.cFileType);
#if 0
    print_level(SV_DEBUG, "%04d%02d%02d%02d%02d%02d_%02s_%07d_%010d_%04d_%04d_%02d_%08d_%01s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s\n"\
                    ,stFileNameParams.s32Year\
                    ,stFileNameParams.s32Month\
                    ,stFileNameParams.s32Day\
                    ,stFileNameParams.s32Hour\
                    ,stFileNameParams.s32Minute\
                    ,stFileNameParams.s32Second\
                    ,stFileNameParams.cEventType\
                    ,stFileNameParams.s32Duration\
                    ,stFileNameParams.s32Size\
                    ,stFileNameParams.s32Width\
                    ,stFileNameParams.s32Height\
                    ,stFileNameParams.s32FrameRate\
                    ,stFileNameParams.s32BitRate\
                    ,stFileNameParams.cPlateNum\
                    ,stFileNameParams.s32ChNum\
                    ,stFileNameParams.u64DeviceID\
                    ,stFileNameParams.u8Flag\
                    ,stFileNameParams.s32Msec\
                    ,stFileNameParams.s32PreRecordMsec\
                    ,stFileNameParams.cVersionNum\
                    ,stFileNameParams.cCustomerNum\
                    ,stFileNameParams.cTimeZone\
                    ,stFileNameParams.cDST\
                    ,stFileNameParams.cFileType);
#endif
    *pstFileNameParams = stFileNameParams;

    return SV_SUCCESS;
}
uint64 TransDeviceId(void)
{
    char buf[64] = {0};
    sint32 s32Fd = -1;
    uint64 u64Uid = 0;

    if(u64Uid != 0)
    {
        return u64Uid;
    }
#ifndef UID_PATH
#define     UID_PATH            CONFIG_SERAILNUM        /* 序列号文件路径 */
#endif
    s32Fd = open(UID_PATH, O_RDONLY);
    if(s32Fd <0)
    {
        return u64Uid;
    }

    read(s32Fd, buf, 64);
    close(s32Fd);
    sscanf(buf, "%llu", &u64Uid);
    return u64Uid;
}

const char* TransNumber2EventInof(REC_TYPE_E enRecType)
{
    const char *p = NULL;
    switch (enRecType)
    {
        case REC_TYPE_NORMAL:       		    p = "Normal";           break;
        case REC_TYPE_FATIGUE:      		    p = "Fatigue";          break;
        case REC_TYPE_DISTRACTION:  		    p = "Distraction";      break;
        case REC_TYPE_NO_DRIVER:    		    p = "No Driver";        break;
        case REC_TYPE_SMOKE:        		    p = "Smoke";            break;
        case REC_TYPE_PHONE:        		    p = "Phone";            break;
        case REC_TYPE_RADAR:        		    p = "Radar";            break;
        case REC_TYPE_YAWN:         		    p = "Yawn";             break;
        case REC_TYPE_FACE_AUTH_FAILED: 	    p = "Login Fail";       break;
        case REC_TYPE_FACE_AUTH_TIMEOUT: 	    p = "Login Timeout";    break;
        case REC_TYPE_NO_MASK:      		    p = "No Mask";          break;
        case REC_TYPE_SUNGLASS:     		    p = "Sunglass";         break;
        case REC_TYPE_FATIGUE_L2:   		    p = "Fatigue L2";       break;
        case REC_TYPE_PD_ROI:       		    p = "Roi Person";       break;
        case REC_TYPE_SEATBELT:     		    p = "No Seatbelt";      break;
        case REC_TYPE_SHELTER:      		    p = "Camera Occlusion"; break;
        case REC_TYPE_DRINKEAT:     		    p = "Drink";            break;
        case REC_TYPE_CHANGE_GUARD: 		    p = "Change Guard";     break;
        case REC_TYPE_OVERSPEED:    		    p = "Overspeed";        break;
        case REC_TYPE_NO_HELMET:    		    p = "No Helmet";        break;
        case REC_TYPE_KSS7_MILD_FATIGUE:    	p = "Mild Fatigue";     break;
        case REC_TYPE_KSS8_MODERATE_FATIGUE:    p = "Moderate Fatigue"; break;
        case REC_TYPE_KSS9_SEVERE_FATIGUE:    	p = "Severe Fatigue";   break;
        case REC_TYPE_USER_ALARM:   		    p = "User_Alarm";       break;
        default:                    		    p = "Normal";           break;
    }
    return p;
}
REC_TYPE_E TransString2RecType(char *cEventType)
{
    if (0 == strncmp(cEventType, "NM", 2))          return REC_TYPE_NORMAL;
    else if (0 == strncmp(cEventType, "FT", 2))
    {
        if (BOARD_IsDMS31P())
            return REC_TYPE_KSS7_MILD_FATIGUE;
        else
            return REC_TYPE_FATIGUE;
    }
    else if (0 == strncmp(cEventType, "DS", 2))     return REC_TYPE_DISTRACTION;
    else if (0 == strncmp(cEventType, "ND", 2))     return REC_TYPE_NO_DRIVER;
    else if (0 == strncmp(cEventType, "SM", 2))     return REC_TYPE_SMOKE;
    else if (0 == strncmp(cEventType, "CA", 2))     return REC_TYPE_PHONE;
    else if (0 == strncmp(cEventType, "RA", 2))     return REC_TYPE_RADAR;
    else if (0 == strncmp(cEventType, "YW", 2))     return REC_TYPE_YAWN;
    else if (0 == strncmp(cEventType, "AF", 2))     return REC_TYPE_FACE_AUTH_FAILED;
    else if (0 == strncmp(cEventType, "AT", 2))     return REC_TYPE_FACE_AUTH_TIMEOUT;
    else if (0 == strncmp(cEventType, "NK", 2))     return REC_TYPE_NO_MASK;
    else if (0 == strncmp(cEventType, "SG", 2))     return REC_TYPE_SUNGLASS;
    else if (0 == strncmp(cEventType, "L2", 2))
    {
        if (BOARD_IsDMS31P())
            return REC_TYPE_KSS8_MODERATE_FATIGUE;
        else
            return REC_TYPE_FATIGUE_L2;
    }
    else if (0 == strncmp(cEventType, "PB", 2))     return REC_TYPE_PD_ROI;
    else if (0 == strncmp(cEventType, "NB", 2))     return REC_TYPE_SEATBELT;
    else if (0 == strncmp(cEventType, "CO", 2))     return REC_TYPE_SHELTER;
    else if (0 == strncmp(cEventType, "DE", 2))     return REC_TYPE_DRINKEAT;
    else if (0 == strncmp(cEventType, "CG", 2))     return REC_TYPE_CHANGE_GUARD;
    else if (0 == strncmp(cEventType, "OS", 2))     return REC_TYPE_OVERSPEED;
    else if (0 == strncmp(cEventType, "NH", 2))     return REC_TYPE_NO_HELMET;
    //else if (0 == strncmp(cEventType, "LF", 2))     return REC_TYPE_KSS7_MILD_FATIGUE;
    //else if (0 == strncmp(cEventType, "MF", 2))     return REC_TYPE_KSS8_MODERATE_FATIGUE;
    else if (0 == strncmp(cEventType, "SF", 2))     return REC_TYPE_KSS9_SEVERE_FATIGUE;
    else if (0 == strncmp(cEventType, "UA", 2))     return REC_TYPE_USER_ALARM;
    else                                            return REC_TYPE_NORMAL;

}

REC_OBJ_E TransNumber2RecObj(REC_TYPE_E enType)
{
    switch (enType)
    {
        case REC_TYPE_NORMAL:   return REC_NORAML;
        case REC_TYPE_ALARM:    return REC_ALARM;
        case REC_TYPE_PIC:      return REC_PIC;
        default:                return REC_UNKNOW;
    }
}

sint32 TransGetPathName(REC_POS_E enRecPos, sint32 subIdx,REC_OBJ_E enObj, REC_FILE_NAME_S *pstFileNameParams, char *pNewFileName)
{
    sint32  s32Ret                          = 0;
    char    szFileName[REC_FILE_NAME_LEN]   = {0};
    char    *pRecPos                        = NULL;
    char    *pRecType                       = NULL;
    char    szRecPos[32]                    = {0};
    char    szRootBuf[64]                   = {0};

    if ((enRecPos == R_POS_UNKNOW) || (R_POS_BUTT == enRecPos))
    {
        return ERR_ILLEGAL_PARAM;
    }
    s32Ret = TransRootPath(Trans2StoragePos(enRecPos, subIdx), enObj, szRootBuf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "media root invalid. [pos:%d, enObj:%d]\n", enRecPos, enObj);
        return SV_FAILURE;
    }

    snprintf(szFileName, REC_FILE_NAME_LEN,"%s/%04d%02d%02d/%02d/%02d/%04d%02d%02d%02d%02d%02d_%02s_%07d_%010d_%04d_%04d_%02d_%08d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
                    ,szRootBuf\
                    ,pstFileNameParams->s32Year\
                    ,pstFileNameParams->s32Month\
                    ,pstFileNameParams->s32Day\
                    ,pstFileNameParams->s32Hour\
                    ,pstFileNameParams->s32Minute\
                    ,pstFileNameParams->s32Year\
                    ,pstFileNameParams->s32Month\
                    ,pstFileNameParams->s32Day\
                    ,pstFileNameParams->s32Hour\
                    ,pstFileNameParams->s32Minute\
                    ,pstFileNameParams->s32Second\
                    ,pstFileNameParams->cEventType\
                    ,pstFileNameParams->s32Duration\
                    ,pstFileNameParams->s32Size\
                    ,pstFileNameParams->s32Width\
                    ,pstFileNameParams->s32Height\
                    ,pstFileNameParams->s32FrameRate\
                    ,pstFileNameParams->s32BitRate\
                    ,pstFileNameParams->cPlateNum\
                    ,pstFileNameParams->s32ChNum\
                    ,pstFileNameParams->u64DeviceID\
                    ,pstFileNameParams->u8Flag\
                    ,pstFileNameParams->s32Msec\
                    ,pstFileNameParams->s32PreRecordMsec\
                    ,pstFileNameParams->cVersionNum\
                    ,pstFileNameParams->cCustomerNum\
                    ,pstFileNameParams->cTimeZone\
                    ,pstFileNameParams->cDST\
                    ,pstFileNameParams->cFileType);
    //print_level(SV_DEBUG, "recorder_GetPathName %s\n",szFileName);
    memcpy(pNewFileName, szFileName, REC_FILE_NAME_LEN);
    return SV_SUCCESS;
}

}
