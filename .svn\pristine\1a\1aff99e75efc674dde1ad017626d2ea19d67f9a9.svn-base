﻿/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：mpp_sys.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2016-05-17

文件功能描述: 封装海思MPP系统控制模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明
  1. :
  2. :
  3. :
  4. :
  5. :
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#ifndef __HuaweiLite__ 
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <linux/fb.h>
#include <error.h>
#endif
#include <sys/ioctl.h>
#include <sys/prctl.h>
#include <fcntl.h>
#include <time.h>
#include <pthread.h>
#include <errno.h>

#include "common.h"
#include "print.h"
#include "../../include/board.h"
#include "mpi_vb.h"
#include "mpi_sys.h"
#include "mpi_ae.h"
#include "mpp_sys.h"
#include "media.h"

/* 系统控制模块控制信息 */
typedef struct tagMppSysInfo_S
{
    pthread_mutex_t callbackLock;   /* 数据回调线程互斥锁 (视频编码回调和音频编码回调的互斥) */
    MPP_BIND_INFO_S stBindInfo;     /* 通道绑定信息 */
    SV_BOOL     bNightMode;         /* 是否启用夜晚模式 */
    uint32      u32IRcutMode;       /* IRCUT模式 */
	WDR_MODE_EE enSysWDRMode;		/* 是否使能WDR */
    uint32      u32TID;             /* 系统控制线程ID */
    SV_BOOL     bRunning;           /* 线程是否正在运行 */
    SV_BOOL     bException;         /* 线程是否出现异常 */
} MPP_SYS_INFO_S;

MPP_SYS_INFO_S m_stSysInfo = {0};   /* 系统控制模块信息 */

extern MPP_PHY_CHN g_astViPhyChn[VIM_MAX_CHN_NUM];
extern MPP_PHY_CHN g_astAiPhyChn[MPP_AIO_MAX_CHN_NUM];

extern void * mpp_sys_Body(MPP_SYS_INFO_S *pstSysInfo);
static uint64 mpp_sys_GetSysTimeByUsec(void)
{
    struct timeval stTime;

    gettimeofday(&stTime, NULL);

    return  (stTime.tv_sec * 1000000LLU) + stTime.tv_usec;
}

/******************************************************************************
 * 函数功能: 初始化HiFB，图像层绑定VO设备
 * 输入参数: stSrcSize --- 视频源画面大小 (宽高)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_initHiFB(SV_SIZE_S stSrcSize)
{
    

    return SV_SUCCESS;
}

#if 1	//lueng, wdr_2to1, from sdk_sample code
#define ALIGN_UP_test(x, a)           ( ( ((x) + ((a) - 1) ) / a ) * a )
#define HI_MAXINUM_LIMIT_test 10000

__inline static HI_VOID COMMON_GetPicBufferSize_test(HI_U32 u32Width, HI_U32 u32Height,
    PIXEL_FORMAT_E enPixelFormat, DATA_BITWIDTH_E enBitWidth,
    COMPRESS_MODE_E enCmpMode, HI_U32 u32Align, VB_CAL_CONFIG_S *pstCalConfig)
{
    HI_U32 u32BitWidth = 0;
    HI_U32 u32VBSize = 0;
    HI_U32 u32HeadSize = 0;
    HI_U32 u32AlignHeight;
    HI_U32 u32MainStride = 0;
    HI_U32 u32MainSize = 0;
    HI_U32 u32HeadStride = 0;
    HI_U32 u32HeadYSize = 0;
    HI_U32 u32YSize = 0;

    if((u32Width > HI_MAXINUM_LIMIT_test) || (u32Height > HI_MAXINUM_LIMIT_test)){
        pstCalConfig->u32VBSize = 0;
    }

    /* u32Align: 0 is automatic mode, alignment size following system. Non-0 for specified alignment size */
    if (u32Align == 0) {
        u32Align = DEFAULT_ALIGN;
    } else if (u32Align > MAX_ALIGN) {
        u32Align = MAX_ALIGN;
    } else {
        u32Align = (ALIGN_UP_test(u32Align, DEFAULT_ALIGN));
    }

    switch (enBitWidth) {
        case DATA_BITWIDTH_8: {
            u32BitWidth = 8;
            break;
        }
        case DATA_BITWIDTH_16: {
            u32BitWidth = 16;
            break;
        }
        default:
        {
            u32BitWidth = 0;
            break;
        }
    }

    u32AlignHeight = ALIGN_UP_test(u32Height, 2);

    if (enCmpMode == COMPRESS_MODE_NONE) {
        u32MainStride = ALIGN_UP_test((u32Width * u32BitWidth + 7) >> 3, u32Align);
        u32YSize = u32MainStride * u32AlignHeight;

        if ((PIXEL_FORMAT_YVU_SEMIPLANAR_420 == enPixelFormat) ||
            (PIXEL_FORMAT_YUV_SEMIPLANAR_420 == enPixelFormat)) {
                u32MainSize = (u32MainStride * u32AlignHeight) * 3 >> 1;
            } else if ((PIXEL_FORMAT_YVU_SEMIPLANAR_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_YUV_SEMIPLANAR_422 == enPixelFormat)) {
                u32MainSize = u32MainStride * u32AlignHeight * 2;
            } else if ((enPixelFormat == PIXEL_FORMAT_YUV_400) ||
                   (enPixelFormat == PIXEL_FORMAT_S16C1) ||
                   (enPixelFormat == PIXEL_FORMAT_U16C1) ||
                   (enPixelFormat == PIXEL_FORMAT_S8C1) ||
                   (enPixelFormat == PIXEL_FORMAT_U8C1)) {
                u32MainSize = u32MainStride * u32AlignHeight;
            } else if ((PIXEL_FORMAT_YUYV_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_YVYU_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_UYVY_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_VYUY_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_YYUV_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_YYVU_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_UVYY_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_VUYY_PACKAGE_422 == enPixelFormat) ||
                   (PIXEL_FORMAT_VY1UY0_PACKAGE_422 == enPixelFormat)) {
                u32MainStride = ALIGN_UP_test((u32Width * u32BitWidth + 7) >> 3, u32Align) * 2;
                u32MainSize = u32MainStride * u32AlignHeight;
            } else {
                u32MainSize = u32MainStride * u32AlignHeight * 3;
            }

            u32VBSize = u32MainSize;
        } else {
            HI_U32 u32CmpRatioLuma = 1450;
            HI_U32 u32CmpRatioChroma = 1800;

            u32HeadStride = 16;
            u32HeadYSize = u32HeadStride * u32AlignHeight;

#ifndef __HuaweiLite__
#ifdef __KERNEL__

            u32YSize = osal_div64_u64(u32Width * u32AlignHeight * 1000ULL, u32CmpRatioLuma);
#else
            u32YSize = u32Width * u32AlignHeight * 1000ULL / u32CmpRatioLuma;

#endif
#else
            u32YSize = u32Width * u32AlignHeight * 1000ULL / u32CmpRatioLuma;
#endif

            u32YSize = ALIGN_UP_test(u32YSize, DEFAULT_ALIGN);

            if ((PIXEL_FORMAT_YVU_SEMIPLANAR_420 == enPixelFormat) ||
                (PIXEL_FORMAT_YUV_SEMIPLANAR_420 == enPixelFormat)) {
                HI_U32 u32CSize;

                u32HeadSize = u32HeadYSize + u32HeadYSize / 2;
#ifndef __HuaweiLite__
#ifdef __KERNEL__
                u32CSize = osal_div64_u64(u32Width * u32AlignHeight * 1000ULL, u32CmpRatioChroma * 2);
#else
                u32CSize = u32Width * u32AlignHeight * 1000ULL / u32CmpRatioChroma;
#endif
#else
                u32CSize = u32Width * u32AlignHeight * 1000ULL / u32CmpRatioChroma;
#endif

                u32CSize = ALIGN_UP_test(u32CSize, DEFAULT_ALIGN);
                u32MainSize = u32YSize + u32CSize;
            } else if (PIXEL_FORMAT_YVU_SEMIPLANAR_422 == enPixelFormat ||
                PIXEL_FORMAT_YUV_SEMIPLANAR_422 == enPixelFormat) {
                u32HeadSize = u32HeadYSize * 2;
                u32MainSize = u32YSize * 2;
            } else if (enPixelFormat == PIXEL_FORMAT_YUV_400) {
                u32HeadSize = u32HeadYSize;
                u32MainSize = u32YSize;
            } else {
                u32HeadSize = u32HeadYSize * 3;
                u32MainSize = u32YSize * 3;
            }

            if (u32Width <= VPSS_LINE_BUFFER) {
                u32HeadSize = 64 + ALIGN_UP_test(u32HeadSize, u32Align);
                u32VBSize = u32HeadSize + u32MainSize;
            } else {
                u32HeadSize = (64 + ALIGN_UP_test(u32HeadSize, u32Align)) * 2;
                u32VBSize = u32HeadSize + u32MainSize + 2 * DEFAULT_ALIGN;
            }
        }

        pstCalConfig->u32VBSize = u32VBSize;
        pstCalConfig->u32HeadStride = u32HeadStride;
        pstCalConfig->u32HeadYSize = u32HeadYSize;
        pstCalConfig->u32HeadSize = u32HeadSize;
        pstCalConfig->u32MainStride = u32MainStride;
        pstCalConfig->u32MainYSize = u32YSize;
        pstCalConfig->u32MainSize = u32MainSize;
        pstCalConfig->u32ExtStride = 0;
        pstCalConfig->u32ExtYSize = 0;

        return;
}
#endif

/******************************************************************************
 * 函数功能: 配置并初始化MPP系统
 * 输入参数: pstVbConf --- 视频公共缓存池配置信息 u32MaxPoolCnt: [1, MPP_SYS_MAX_COMM_POOLS]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_ILLEGAL_PARAM - 参数错误
             ERR_SYS_NOTREADY - 系统未初始化
             ERR_NOMEM - 内存不足
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Init(MPP_VB_CONF_S *pstVbConf)
{
    HI_S32 s32Ret = HI_FAILURE, i;
    VB_CONFIG_S stVbConf = {0};
    //MPP_SYS_CONF_S stSysConf = {0};
    VI_VPSS_MODE_S stVIVPSSMode;
    HI_U64 u64PtsBase = 0;

    if (NULL == pstVbConf)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstVbConf->u32MaxPoolCnt || pstVbConf->u32MaxPoolCnt > MPP_SYS_MAX_COMM_POOLS)
    {
        return ERR_ILLEGAL_PARAM;
    }
    
    mpp_sys_Fini();
    
    stVbConf.u32MaxPoolCnt = pstVbConf->u32MaxPoolCnt;
    for (i = 0; i < pstVbConf->u32MaxPoolCnt; i++)
    {
        stVbConf.astCommPool[i].u64BlkSize	= pstVbConf->astCommPool[i].u32BlkSize;
        stVbConf.astCommPool[i].u32BlkCnt = pstVbConf->astCommPool[i].u32BlkCnt;
    }
	
    s32Ret = HI_MPI_VB_SetConfig(&stVbConf);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_VB_SetConfig failed! [err=%#x]\n", s32Ret);
        return ERR_BUSY;
    }

    s32Ret = HI_MPI_VB_Init();
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_VB_Init failed! [err=%#x]\n", s32Ret);
        if (HI_ERR_VB_NOMEM == s32Ret)
        {
            return ERR_NOMEM;
        }
        else
        {
            return SV_FAILURE;
        }
    }
#if 0
    stSysConf.u32AlignWidth = 64;
    s32Ret = HI_MPI_SYS_SetConf(&stSysConf);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_SetConf failed! [err=%#x]\n", s32Ret);
        if (HI_ERR_SYS_NOT_PERM == s32Ret)
        {
            return ERR_NOT_PERM;
        }
        else
        {
            return SV_FAILURE;
        }
    }
#endif
    s32Ret = HI_MPI_SYS_Init();
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_Init failed! [err=%#x]\n", s32Ret);
        if (HI_ERR_SYS_BUSY == s32Ret)
        {
            return ERR_BUSY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    u64PtsBase = mpp_sys_GetSysTimeByUsec();
    s32Ret = HI_MPI_SYS_InitPTSBase(u64PtsBase);
    print_level(SV_DEBUG, "now: %d, Init Pts=%lld\n", time(NULL), u64PtsBase);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_InitPTSBase failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = HI_MPI_SYS_GetVIVPSSMode(&stVIVPSSMode);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_GetVIVPSSMode failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    if(pstVbConf->enSysWDRMode == WDR_MODE_ENABLE)
    {
        stVIVPSSMode.aenMode[0] = VI_ONLINE_VPSS_ONLINE;
        stVIVPSSMode.aenMode[1] = VI_OFFLINE_VPSS_ONLINE;
    }
    else
    {
        stVIVPSSMode.aenMode[0] = VI_ONLINE_VPSS_ONLINE;
    }
    
    s32Ret = HI_MPI_SYS_SetVIVPSSMode(&stVIVPSSMode);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_SetVIVPSSMode failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    memset (&m_stSysInfo, 0x0, sizeof(MPP_SYS_INFO_S));
	m_stSysInfo.enSysWDRMode = pstVbConf->enSysWDRMode;
    s32Ret = pthread_mutex_init(&m_stSysInfo.callbackLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化MPP系统
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_BUSY - 系统忙碌
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Fini()
{
    sint32 s32Ret = 0, i;
    
    HI_MPI_SYS_Exit();
    
    for (i = 0; i<VB_MAX_USER; i++)
    {
         HI_MPI_VB_ExitModCommPool(i);
    }
    
    for (i = 0; i<VB_MAX_POOLS; i++)
    {
         HI_MPI_VB_DestroyPool(i);
    }
    
    HI_MPI_VB_Exit();
    s32Ret = pthread_mutex_destroy(&m_stSysInfo.callbackLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_destroy failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动SYS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Start()
{
    sint32 s32Ret = 0;
    uint32 u32TID = 0;
#if (BOARD != BOARD_WFTR20S2)
    m_stSysInfo.bRunning = SV_TRUE;
    m_stSysInfo.bException = SV_FALSE;
    s32Ret = pthread_create(&u32TID, NULL, mpp_sys_Body, &m_stSysInfo);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stSysInfo.u32TID = u32TID;
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止SYS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_Stop()
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;
#if (BOARD != BOARD_WFTR20S2)
    m_stSysInfo.bRunning = SV_FALSE;
    s32Ret = pthread_join(m_stSysInfo.u32TID, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for VENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: SYS模块线程体
 * 输入参数: pstSysInfo --- MPP系统控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_sys_Body(MPP_SYS_INFO_S *pstSysInfo)
{
    sint32 s32Ret = 0, i;
    uint32 u32Value = 0, u32CdsStat = 0x01;
    uint32 u32ChnNum = 1;
    ISP_DEV IspDev = 0;
    ISP_EXP_INFO_S stExpInfo = {0};
    SV_BOOL bInitFlag = SV_FALSE;
    SV_BOOL bDisableIR = SV_FALSE;

    s32Ret = prctl(PR_SET_NAME, "mpp_sys_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    s32Ret = BOARD_InitIrCut();
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_InitIrCut failed. [err=%#x]\n", s32Ret);
    }
    
    while (pstSysInfo->bRunning)
    {
        sleep_ms(50);
        u32Value = 0;

        if(!pstSysInfo->u32IRcutMode)
        {
            if(bDisableIR)
                continue;
            
            print_level(SV_INFO, "disable ircut.\n");

            bInitFlag = SV_FALSE;
            bDisableIR = SV_TRUE;
            pstSysInfo->bNightMode = SV_FALSE;
            s32Ret = BOARD_SetIrCutStatue(pstSysInfo->bNightMode);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = mpp_venc_Color2GreyDisable();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_venc_Color2GreyDisable failed. [err=%#x]\n", s32Ret);
            }
            
            continue;
        }
        else
        {
            bDisableIR = SV_FALSE;
        }
        
        for (i = 0; i < 10; i++)
        {
            s32Ret = BOARD_CDS_DETECT(&u32Value);
            //print_level(SV_WARN, "BOARD_CDS_DETECT u32Value:%d\n",u32Value);
            if (SV_SUCCESS == s32Ret)
            {
                break;
            }
            print_level(SV_WARN, "BOARD_CDS_DETECT failed.\n");
            sleep_ms(10);
        }

     
        if (bInitFlag && (u32CdsStat == u32Value || BOARD_GetVersion() == BOARD_IPCR20S2_V1))
        {
            continue;
        }
        
        bInitFlag = SV_TRUE;
        u32CdsStat = u32Value;
        if (0x01 == u32CdsStat)
        {
            print_level(SV_INFO, "switch to day mode.\n");
            pstSysInfo->bNightMode = SV_FALSE;
            
            s32Ret = BOARD_SetIrCutStatue(pstSysInfo->bNightMode);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
            }

            s32Ret = mpp_venc_Color2GreyDisable();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_venc_Color2GreyDisable failed. [err=%#x]\n", s32Ret);
            }
        }
        else if (0x00 == u32CdsStat)
        {
            print_level(SV_INFO, "switch to night mode.\n");
            pstSysInfo->bNightMode = SV_TRUE;
            sleep_ms(50);
            
            s32Ret = mpp_venc_Color2GreyEnable();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_venc_Color2GreyEnable failed. [err=%#x]\n", s32Ret);
            }
            
            s32Ret = BOARD_SetIrCutStatue(pstSysInfo->bNightMode);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "BOARD_SetIrCutStatue failed. [err=%#x]\n", s32Ret);
            }
        }
    }

    return NULL;
}

/******************************************************************************
 * 函数功能: 获取MPP时间戳
 * 输入参数: 无
 * 输出参数: pu64Pts --- 时间戳
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_GetPts(uint64 *pu64Pts)
{
    sint32 s32Ret = 0;
    HI_U64 u64PtsGet = 0ll;

    if (NULL == pu64Pts)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = HI_MPI_SYS_GetCurPTS(&u64PtsGet);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_GetCurPTS failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    *pu64Pts = u64PtsGet;
    //print_level(SV_DEBUG, "u64PtsGet=%lld\n", u64PtsGet);
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 同步系统时间戳
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_UpdatePts()
{
    sint32 s32Ret = 0;
    HI_U64 u64PtsBase = 0ll;
    HI_U64 u64PtsGet = 0ll;

    s32Ret = HI_MPI_SYS_GetCurPTS(&u64PtsGet);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_GetCurPTS failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    u64PtsBase = mpp_sys_GetSysTimeByUsec();
    //print_level(SV_DEBUG, "now: %d, Sync Pts=%lld\n", time(NULL), u64PtsBase);
    if (llabs((sint64)u64PtsGet - (sint64)u64PtsBase) < 10000000)
    {
        s32Ret = HI_MPI_SYS_SyncPTS(u64PtsBase);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_SYS_SyncPTS failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }
    else
    {
        s32Ret = HI_MPI_SYS_InitPTSBase(u64PtsBase);
        if (HI_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HI_MPI_SYS_InitPTSBase failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道绑定到视频编码通道组
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VoChn --- 视频输出通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_INVALID_CHNID - 通道号无效
             ERR_MEDIA_INVALID_DEVID - 无效设备号
             ERR_INVALID_CHNID - 无效通道号
             ERR_UNEXIST - 通道组不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViGroupBind(sint32 s32ViChn, sint32 s32VeGroup)
{
    
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道到视频编码通道组解绑定
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VoChn --- 视频输出通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道组不存在
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViGroupUnBind(sint32 s32ViChn, sint32 s32VeGroup)
{
    
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道绑定到视频处理通道组
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViVpssBind(sint32 s32ViChn, sint32 s32VpssGrp)
{
    HI_S32 s32Ret = HI_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32ViChn >= VIM_MAX_CHN_NUM || s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = HI_ID_VI;
    stSrcChn.s32DevId = g_astViPhyChn[s32ViChn].DevId;
    stSrcChn.s32ChnId = g_astViPhyChn[s32ViChn].ChnId;

    stDestChn.enModId = HI_ID_VPSS;
    stDestChn.s32DevId = s32VpssGrp;
    stDestChn.s32ChnId = 0;
    s32Ret = HI_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频输入通道到视频处理通道组解绑定
 * 输入参数: s32ViChn --- 视频输入通道号
             s32VpssGrp --- 视频处理通道组
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_ViVpssUnBind(sint32 s32ViChn, sint32 s32VpssGrp)
{
    HI_S32 s32Ret = HI_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32ViChn >= VIM_MAX_CHN_NUM || s32VpssGrp >= VPS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = HI_ID_VI;
    stSrcChn.s32DevId = g_astViPhyChn[s32ViChn].DevId;
    stSrcChn.s32ChnId = g_astViPhyChn[s32ViChn].ChnId;

    stDestChn.enModId = HI_ID_VPSS;
    stDestChn.s32DevId = s32VpssGrp;
    stDestChn.s32ChnId = 0;
    s32Ret = HI_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_UnBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频处理通道绑定到视频编码通道
 * 输入参数: s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道号 [0, 1]
             s32VencChn --- 视频编码通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssVencBind(sint32 s32VpssGrp, sint32 s32VpssChn, sint32 s32VencChn)
{
    HI_S32 s32Ret = HI_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32VpssGrp >= VPS_MAX_GRP_NUM || s32VencChn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = HI_ID_VPSS;
    stSrcChn.s32DevId = s32VpssGrp;
    stSrcChn.s32ChnId = s32VpssChn;

    stDestChn.enModId = HI_ID_VENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32VencChn;
    s32Ret = HI_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 视频处理通道到视频编码通道解绑定
 * 输入参数: s32VpssGrp --- 视频处理通道组
             s32VpssChn --- 视频处理通道号 [0, 1]
             s32VencChn --- 视频编码通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_VpssVencUnBind(sint32 s32VpssGrp, sint32 s32VpssChn, sint32 s32VencChn)
{
    HI_S32 s32Ret = HI_SUCCESS;
    MPP_CHN_S stSrcChn;
    MPP_CHN_S stDestChn;

    if (s32VpssGrp >= VPS_MAX_GRP_NUM || s32VencChn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    stSrcChn.enModId = HI_ID_VPSS;
    stSrcChn.s32DevId = s32VpssGrp;
    stSrcChn.s32ChnId = s32VpssChn;

    stDestChn.enModId = HI_ID_VENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32VencChn;
    s32Ret = HI_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_UnBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 音频输入通道绑定到音频编码通道
 * 输入参数: s32AiChn --- 音频输入通道号
             s32AencChn --- 音频编码通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_AiAencBind(sint32 s32AiChn, sint32 s32AencChn)
{
    HI_S32 s32Ret = 0;
    MPP_CHN_S stSrcChn,stDestChn;

    if (s32AiChn > MPP_AENC_MAX_CHN_NUM || s32AencChn > MPP_AENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stSrcChn.enModId = HI_ID_AI;
    stSrcChn.s32DevId = g_astAiPhyChn[s32AiChn].DevId;
    stSrcChn.s32ChnId = g_astAiPhyChn[s32AiChn].ChnId;
    stDestChn.enModId = HI_ID_AENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32AencChn;
    s32Ret = HI_MPI_SYS_Bind(&stSrcChn, &stDestChn);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_Bind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].s32Chn = s32AencChn;
    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].enChnType = MPP_CHN_AENC;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].s32Chn = s32AiChn;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].enChnType = MPP_CHN_AI;
  
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 音频输入通道到音频编码通道解绑定
 * 输入参数: s32AiChn --- 音频输入通道号
             s32AencChn --- 音频编码通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_AiAencUnBind(sint32 s32AiChn, sint32 s32AencChn)
{
    HI_S32 s32Ret = 0;
    MPP_CHN_S stSrcChn,stDestChn;

    if (s32AiChn > MPP_AIO_MAX_CHN_NUM || s32AencChn > MPP_AENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    stSrcChn.enModId = HI_ID_AI;
    stSrcChn.s32DevId = g_astAiPhyChn[s32AiChn].DevId;
    stSrcChn.s32ChnId = g_astAiPhyChn[s32AiChn].ChnId;
    stDestChn.enModId = HI_ID_AENC;
    stDestChn.s32DevId = 0;
    stDestChn.s32ChnId = s32AencChn;
    s32Ret = HI_MPI_SYS_UnBind(&stSrcChn, &stDestChn);
    if (HI_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HI_MPI_SYS_UnBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].s32Chn = 0;
    m_stSysInfo.stBindInfo.astAiChn[s32AiChn].enChnType = MPP_CHN_INVALID;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].s32Chn = 0;
    m_stSysInfo.stBindInfo.astAencChn[s32AencChn].enChnType = MPP_CHN_INVALID;
   
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 查询通道绑定信息
 * 输入参数: 无
 * 输出参数: pstBindInfo --- 通道绑定信息指针
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_QueryBindInfo(MPP_BIND_INFO_S *pstBindInfo)
{
    if (NULL == pstBindInfo)
    {
        return ERR_NULL_PTR;
    }

    memcpy(pstBindInfo, &m_stSysInfo.stBindInfo, sizeof(MPP_BIND_INFO_S));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 加锁音视频编码回调互斥锁
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_CallbackLock()
{
    sint32 s32Ret = 0;
    
    s32Ret = pthread_mutex_lock(&m_stSysInfo.callbackLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_lock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解锁音视频编码回调互斥锁
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_sys_CallbackUnlock()
{
    sint32 s32Ret = 0;

    s32Ret = pthread_mutex_unlock(&m_stSysInfo.callbackLock);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_unlock failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 当前摄像头是否为夜晚模式
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 是
             SV_FALSE - 否
 * 注意    : 无
 *****************************************************************************/
SV_BOOL mpp_sys_IsNightMode()
{
    return m_stSysInfo.bNightMode;
}

/******************************************************************************
 * 函数功能: 更新IRCUT状态
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void mpp_sys_UpdateIRCUT_Status(uint32 u32IRcutMode)
{
    m_stSysInfo.u32IRcutMode = u32IRcutMode;
}

