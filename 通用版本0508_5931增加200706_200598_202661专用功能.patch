Index: BSD_DTS_2505_288_480_V6.2_8p_silu_rv1126_json_662af493.rknn
===================================================================
无法显示: 文件标记为二进制类型。
svn:mime-type = application/octet-stream

Property changes on: BSD_DTS_2505_288_480_V6.2_8p_silu_rv1126_json_662af493.rknn
___________________________________________________________________
Added: svn:mime-type
## -0,0 +1 ##
+application/octet-stream
\ No newline at end of property
Index: include/board.h
===================================================================
--- include/board.h	(版本 5931)
+++ include/board.h	(工作副本)
@@ -443,6 +443,7 @@
 #define BOARD_C_ADA32V2_202883           "202883"   /* 202883客户 */
 #define BOARD_C_ADA32V2_WE               "201894"   /* 201894客户 */
 #define BOARD_C_ADA32V2_100520           "100520"   /* 100520客户 */
+#define BOARD_C_ADA32V2_200706           "200706"   /* 200706客户 */
 
 /* ADA32 版本文件路径 */
 #define PATH_C_ADA32V2_DEFAULT         "ADA32"
Index: include/config.h
===================================================================
--- include/config.h	(版本 5931)
+++ include/config.h	(工作副本)
@@ -448,6 +448,9 @@
     SV_BOOL     bPdAlarmOutGreen;               /* 行人算法 AlarmOut 绿色使能 */
     SV_BOOL     bPdAlarmOutYellow;              /* 行人算法 AlarmOut 黄色使能 */
     SV_BOOL     bPdAlarmOutRed;                 /* 行人算法 AlarmOut 红色使能 */
+    SV_BOOL     bPdSTLRed;                      /* 标识检测算法红区使能 */
+    SV_BOOL     bPdSTLYellow;                   /* 标识检测算法黄区使能 */
+    SV_BOOL     bPdSTLGreen;                    /* 标识检测算法绿区使能 */
     SV_BOOL     bPdRoiGreen;                    /* 行人算法检测区域绿色区域使能 */
     SV_BOOL     bPdRoiYellow;                   /* 行人算法检测区域黄色区域使能 */
     SV_BOOL     bPdRoiRed;                      /* 行人算法检测区域红色区域使能 */
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(版本 5931)
+++ src/alg/pd/pd.cpp	(工作副本)
@@ -105,6 +105,7 @@
 #define PD_MODEL_RGB_IR_PC          "/root/model/RGB_NIR_PC.rknn"           /* 带IRCut红外灯模型 */
 #define PD_MODEL_RGB_PC_90          "/root/model/RGB_PC_90.rknn"            /* 可见光检人旋转90度版本 */
 #define PD_MODEL_RGB_PC_90_S        "/root/model/RGB_PC_90_S.rknn"          /* 可见光检人旋转90度版本, RV1106的SILU模型，检测效果好，升压升频版本用 */
+#define PD_MODEL_RGB_PC_UNISIGN_90  "/root/model/RGB_PC_UNISIGN_90.rknn"    /* 可见光通用标识检测旋转90度版本 */
 #define PD_MODEL_RGB_PC_R151_90     "/root/model/RGB_PC_R151_90.rknn"       /* R151可见光检人旋转90度版本 */
 #define PD_MODEL_RGB_SH             "/root/model/RGB_SH.rknn"               /* 可见光检人配带安全帽 */
 #define PD_MODEL_RGB_CQQ_SIGN       "/root/model/RGB_CQQ_SIGN.rknn"         /* 201165 客户检测SpeedZone标志*/
@@ -4838,7 +4839,7 @@
     }
     else
     {
-        if (BOARD_IsCustomer(BOARD_C_ADA32V2_201186))
+        if (BOARD_IsCustomer(BOARD_C_ADA32V2_201186) || BOARD_IsCustomer(BOARD_C_ADA32V2_200706))
         {
             bNowStatus = SV_FALSE;
         }
@@ -5742,7 +5743,7 @@
         }
     }
 
-    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200598) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93852))
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93852))
     {
         if (pdsa32::E_CLS_UNI_SIGN_PIC3 == eClasses)
         {
@@ -5800,6 +5801,18 @@
         eClassesOut = eClasses;
     }
 
+    if(BOARD_IsCustomer(BOARD_C_ADA32V2_200706))
+    {
+        if (eClasses == pdsa32::E_CLS_UNI_SIGN_PIC4)
+        {
+            bSignOn = SV_TRUE;
+        }
+        else if (eClasses == pdsa32::E_CLS_UNI_SIGN_PIC5)
+        {
+            bSignOn = SV_FALSE;
+        }
+    }
+
     // 指定客户 do 指定图标控制输出 图标指定是否检测
     if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
     {
@@ -6256,7 +6269,6 @@
     {
         //print_level(SV_DEBUG, "pd_alg_Body running %d ...\n",s32CurChn);
         //sleep_ms(1000);
-
         clock_gettime(CLOCK_MONOTONIC, &tvNow);
         u32StepTimeMs = ((tvNow.tv_sec*1000 + tvNow.tv_nsec/1000000) - (tvLast.tv_sec*1000 + tvLast.tv_nsec/1000000));
         tvLast = tvNow;
@@ -6664,6 +6676,7 @@
     SV_BOOL bSignOff = SV_FALSE;
     SV_BOOL bLastAlarmOut = SV_FALSE;
     SV_BOOL bContinueAlarmTmp = SV_FALSE;   /* 是否在持续报警的状态，用于检测到通用标志后保持单根触发线输出 */
+	SV_BOOL bSignZoneOn = SV_FALSE;
 
     pdsa32::EAlgObjectClass eClassTmp = pdsa32::E_CLS_RESERVE1;
     pdsa32::EAlgObjectClass eClassAlarmOut = pdsa32::E_CLS_RESERVE1;
@@ -7121,6 +7134,27 @@
                 }
             }
 
+            if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598))
+            {
+                if((apstPdsParam[s32CurChn]->bPdSTLRed && enRoi == PD_ROI_RED) || (apstPdsParam[s32CurChn]->bPdSTLYellow && enRoi == PD_ROI_YELLOW) || (apstPdsParam[s32CurChn]->bPdSTLGreen && enRoi == PD_ROI_GREEN))
+                {
+                    bSignZoneOn = SV_TRUE;
+                }
+                else
+                {
+                    bSignZoneOn = SV_FALSE;
+                }
+
+                if (bSignZoneOn && pdsa32::E_CLS_UNI_SIGN_PIC3 == stPdResult.stResults[i].classes)
+                {
+                    bSignOn = SV_TRUE;
+                }
+                else if (bSignZoneOn && pdsa32::E_CLS_UNI_SIGN_PIC1 == stPdResult.stResults[i].classes)
+                {
+                    bSignOff = SV_TRUE;
+                }
+            }
+
             enRoi = pd_CheckRoiUniSign(enRoi, &stPdResult.stResults[i]);
 #if defined(BOARD_ADA46V1)
             if (apstPdsParam[s32CurChn]->bPdTestMode == SV_FALSE && enRoi <= PD_ROI_GREEN && !pd_R151_Check_TestCase(s32GpsSpeed, enRoi, &stPdResult.stResults[i]))
@@ -7647,6 +7681,14 @@
             }
         }
 
+        if (BOARD_IsCustomer(BOARD_C_ADA32V2_200706))
+        {
+            if (pstPdInfo->stCfgParam.stAlgCh2.stPdsParam.enPdsModel >= E_PDS_UNISIGN && pstPdInfo->stCfgParam.stAlgCh2.stPdsParam.enPdsModel <= E_PDS_PC_UNISIGN)
+            {
+                bUnisignAlarmOut = pd_UNISIGN_Acitve(bSignOn, bSignOff);
+            }
+        }
+
         if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
         {
             baUnisignAlarmOut[0] = (SV_BOOL)pd_UNISIGN_AcitveColor(baSignOn[0], baSignOff[0], 0);
@@ -8026,6 +8068,18 @@
             }
         }
 
+        if(BOARD_IsCustomer(BOARD_C_ADA32V2_200706))
+        {
+            if(bUnisignAlarmOut)
+            {
+                stPdDumpInfo.bAlarmOut[0] = SV_TRUE; // 200706客户检测到通用标志后，红色触发线触发输出
+            }
+            else
+            {
+                stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
+            }
+        }
+
         if (BOARD_IsCustomer(BOARD_C_ADA32V2_VT) || BOARD_IsCustomer(BOARD_C_ADA32V3_VT))
         {
             if (0x10 == (0x10 & apstPdsParam[s32CurChn]->astTriggerSrc[0].s32AlarmTypeMask))
@@ -8876,6 +8930,10 @@
         }
         #endif
 
+        if(BOARD_IsCustomer(BOARD_C_ADA32V2_200706))
+        {
+            apstPdsParam[i]->enPdsModel = E_PDS_PC_UNISIGN;
+        }
         /* 获取模型文件位置 */
         memset(pszModelFileList, 0x00, sizeof(pszModelFileList));
         s32Ret = pd_model_file(apstPdsParam[i]->enPdsModel, pszModelFileList);
@@ -9223,7 +9281,20 @@
     }
 
 #if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))
-    char *pszModelPath_90 = BOARD_IsNotCustomer(BOARD_C_ADA32V2_R151) ? PD_MODEL_RGB_PC_90 : PD_MODEL_RGB_PC_R151_90;
+    char *pszModelPath_90;
+    //区分旋转采用标识检测还是行人检测
+    switch (apstPdsParam[0]->enPdsModel)
+    {
+        case E_PDS_UNISIGN:
+        case E_PDS_P_UNISIGN:
+        case E_PDS_C_UNISIGN:
+        case E_PDS_PC_UNISIGN:
+            pszModelPath_90 = PD_MODEL_RGB_PC_UNISIGN_90;
+            break;
+        default:
+            pszModelPath_90 = BOARD_IsNotCustomer(BOARD_C_ADA32V2_R151) ? PD_MODEL_RGB_PC_90 : PD_MODEL_RGB_PC_R151_90;
+    }
+
     if (pstInitParam->bRotate)
     {
         /* 为旋转配置,去除原来所有模型信息 */
@@ -9234,7 +9305,7 @@
 
         memset(pszModelFileList, 0x00, sizeof(pszModelFileList));
         pszModelFileList[0] = pszModelPath_90;
-
+        
         s32Ret = getModelListMessage(pszModelFileList, m_stPdInfo.modelMessageList);
         if(s32Ret != SV_SUCCESS)
         {
@@ -9258,21 +9329,29 @@
 
         print_level(SV_INFO, "ready to load model: %s\n", pszModelPath_90);
 
-
+        if(0 != strcmp(pszModelPath_90, PD_MODEL_RGB_PC_UNISIGN_90))
+        {
 #if defined(BOARD_ADA46V1)
-        apcsPdsAlgRgbPC_90 = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_AIBOX_RGB_PC, pdsa32::E_CAM_VIEW_BACK, 50);
+            apcsPdsAlgRgbPC_90 = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_AIBOX_RGB_PC, pdsa32::E_CAM_VIEW_BACK, 50);
 #else
     #if defined(BOARD_ADA32V2)
-        if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159())
-        {
-            apcsPdsAlgRgbPC_90 = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_AIBOX_RGB_PC, pdsa32::E_CAM_VIEW_BACK, 50);
+            if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159())
+            {
+                apcsPdsAlgRgbPC_90 = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_AIBOX_RGB_PC, pdsa32::E_CAM_VIEW_BACK, 50);
+            }
+            else
+    #endif
+            {
+                apcsPdsAlgRgbPC_90 = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_AIBOX_RGB_PC);
+            }
+#endif
         }
         else
-    #endif
-        {
-            apcsPdsAlgRgbPC_90 = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_AIBOX_RGB_PC);
+        { 
+            apcsPdsAlgRgbPC_90 = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC_UNISIGN);
+            print_level(SV_INFO, "finish new pdsa32::CPdsAlg UNISIGN_90.\n");
         }
-#endif
+
         if (NULL == apcsPdsAlgRgbPC_90)
         {
             print_level(SV_WARN, "new pdsa32::CPdsAlg failed. [err=%d]\n", s32Ret);
Index: src/common/config/config.c
===================================================================
--- src/common/config/config.c	(版本 5931)
+++ src/common/config/config.c	(工作副本)
@@ -386,6 +386,9 @@
         || 0 == strcmp(pszName, "pdRoiGreenEnable")
         || 0 == strcmp(pszName, "pdRoiYellowEnable")
         || 0 == strcmp(pszName, "pdRoiRedEnable")
+        || 0 == strcmp(pszName, "PdSTLRedEnable")
+        || 0 == strcmp(pszName, "PdSTLYellowEnable")
+        || 0 == strcmp(pszName, "PdSTLGreenEnable")
         || 0 == strcmp(pszName, "pdRoiGui")
         || 0 == strcmp(pszName, "roiStyle")
         || 0 == strcmp(pszName, "detectPart")
@@ -5568,6 +5571,9 @@
     mxml_node_t *pstPdRoiGreenEnable = NULL;
     mxml_node_t *pstPdRoiYellowEnable = NULL;
     mxml_node_t *pstPdRoiRedEnable = NULL;
+    mxml_node_t *pstPdSTLGreenEnable = NULL;
+    mxml_node_t *pstPdSTLYellowEnable = NULL;
+    mxml_node_t *pstPdSTLRedEnable = NULL;
     mxml_node_t *pstPdRoiGui = NULL;
     mxml_node_t *pstPdRoiStyle = NULL;
     mxml_node_t *pstPdDetectPart = NULL;
@@ -9038,6 +9044,48 @@
     }
 #endif
 
+    pstPdSTLGreenEnable = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "PdSTLGreenEnable", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstPdSTLGreenEnable)
+    {
+        pstPdSTLGreenEnable = mxmlNewElement(pstAlg, "PdSTLGreenEnable");
+        if (NULL != pstPdSTLGreenEnable)
+        {
+            mxmlNewInteger(pstPdSTLGreenEnable, pstAlgParam->stAlgCh2.stPdsParam.bPdSTLGreen);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstPdSTLGreenEnable, pstAlgParam->stAlgCh2.stPdsParam.bPdSTLGreen);
+    }
+
+    pstPdSTLYellowEnable = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "PdSTLYellowEnable", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstPdSTLYellowEnable)
+    {
+        pstPdSTLYellowEnable = mxmlNewElement(pstAlg, "PdSTLYellowEnable");
+        if (NULL != pstPdSTLYellowEnable)
+        {
+            mxmlNewInteger(pstPdSTLYellowEnable, pstAlgParam->stAlgCh2.stPdsParam.bPdSTLYellow);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstPdSTLYellowEnable, pstAlgParam->stAlgCh2.stPdsParam.bPdSTLYellow);
+    }
+
+    pstPdSTLRedEnable = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "PdSTLRedEnable", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstPdSTLRedEnable)
+    {
+        pstPdSTLRedEnable = mxmlNewElement(pstAlg, "PdSTLRedEnable");
+        if (NULL != pstPdSTLRedEnable)
+        {
+            mxmlNewInteger(pstPdSTLRedEnable, pstAlgParam->stAlgCh2.stPdsParam.bPdSTLRed);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstPdSTLRedEnable, pstAlgParam->stAlgCh2.stPdsParam.bPdSTLRed);
+    }
+
     pstRedWireAlarmOutSwitch = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "RedWireAlarmOutSwitch", NULL, NULL, MXML_DESCEND);
     if (NULL == pstRedWireAlarmOutSwitch)
     {
@@ -10804,6 +10852,9 @@
     mxml_node_t *pstPdRoiGreenEnable = NULL;
     mxml_node_t *pstPdRoiYellowEnable = NULL;
     mxml_node_t *pstPdRoiRedEnable = NULL;
+    mxml_node_t *pstPdSTLGreenEnable = NULL;
+    mxml_node_t *pstPdSTLYellowEnable = NULL;
+    mxml_node_t *pstPdSTLRedEnable = NULL;
     mxml_node_t *pstPdRoiGui = NULL;
     mxml_node_t *pstPdRoiStyle = NULL;
     mxml_node_t *pstPdDetectPart = NULL;
@@ -11095,6 +11146,9 @@
     stAlgParam.stAlgCh2.stPdsParam.bPdRoiGreen              = config_bPdRoiGreen();
     stAlgParam.stAlgCh2.stPdsParam.bPdRoiYellow             = config_bPdRoiYellow();
     stAlgParam.stAlgCh2.stPdsParam.bPdRoiRed                = config_bPdRoiRed();
+    stAlgParam.stAlgCh2.stPdsParam.bPdSTLGreen              = SV_TRUE;
+    stAlgParam.stAlgCh2.stPdsParam.bPdSTLYellow             = SV_TRUE;
+    stAlgParam.stAlgCh2.stPdsParam.bPdSTLRed                = SV_TRUE;
     stAlgParam.stAlgCh2.stPdsParam.bOvertakingEnable        = SV_TRUE;
     stAlgParam.stAlgCh2.stPdsParam.bOpticalFlowEnable       = SV_TRUE;
     stAlgParam.stAlgCh2.stPdsParam.s32OpticalFlowFrmInt     = config_s32OpticalFlowFrmInt();
@@ -12643,6 +12697,22 @@
 #endif
     }
 
+    pstPdSTLRedEnable = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "PdSTLRedEnable", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstPdSTLRedEnable)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.bPdSTLRed = mxmlGetInteger(pstPdSTLRedEnable);
+    }
+    pstPdSTLYellowEnable = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "PdSTLYellowEnable", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstPdSTLYellowEnable)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.bPdSTLYellow = mxmlGetInteger(pstPdSTLYellowEnable);
+    }
+    pstPdSTLGreenEnable = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "PdSTLGreenEnable", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstPdSTLGreenEnable)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.bPdSTLGreen = mxmlGetInteger(pstPdSTLGreenEnable);
+    }
+
     pstPdAlarmOutInterval = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdAlarmOutInterval", NULL, NULL, MXML_DESCEND);
     if (NULL != pstPdAlarmOutInterval)
     {
Index: src/ipserver/http/jsonHandle.cpp
===================================================================
--- src/ipserver/http/jsonHandle.cpp	(版本 5931)
+++ src/ipserver/http/jsonHandle.cpp	(工作副本)
@@ -3207,7 +3207,7 @@
 
     cJSON *pstJsonConfig, *pstIdentification, *pstMedia, *pstAlg, *pstAdas, *pstApc, *pstDms, *pstPdsList, *pstPds, *pstNetwork, *pstServer, *pstSystem, *pstExtraInfo;
     cJSON *pstImage, *pstMainStream, *pstSubStream, *pstJpegStream, *pstVoStream, *pstExtscreenStream, *pstOsdConf;
-    cJSON *pstAudio, *pstDmsInterval, *pstAlgWorkspeed, *pstPdInterval, *pstPdRoiEnable, *pstPdAlarmOutEnable, *pstPdRoiBoard, *pstAudioSettings, *pstDmsAlarmOutSettings, *pstAdvancedSettings, *pstDmsDDAWSettings;
+    cJSON *pstAudio, *pstDmsInterval, *pstAlgWorkspeed, *pstPdInterval, *pstPdRoiEnable, *pstPdSTLEnable, *pstPdAlarmOutEnable, *pstPdRoiBoard, *pstAudioSettings, *pstDmsAlarmOutSettings, *pstAdvancedSettings, *pstDmsDDAWSettings;
     cJSON *pstRedWireAlarmOut, *pstYellowWireAlarmOut, *pstGreenWireAlarmOut;
     cJSON *pstPdSafetyHelmet;
     cJSON *pstEthernet, *pstWifi, *pstNtp, *pstProtocol, *pst4g, *pstBle, *pstBleEnable, *pstBleName;
@@ -4148,10 +4148,15 @@
 #if defined(BOARD_ADA32C4)
 		cJSON_AddItemToObject(pstPds, "pdAlarmLight", cJSON_CreateBool(apstPdsParam[i]->bAlarmLight));
 #endif
+        pstPdSTLEnable = cJSON_CreateObject();
         cJSON_AddItemToObject(pstPds, "pdRoiEnable", pstPdRoiEnable);
         cJSON_AddItemToObject(pstPdRoiEnable, "red", cJSON_CreateBool(apstPdsParam[i]->bPdRoiRed));
         cJSON_AddItemToObject(pstPdRoiEnable, "yellow", cJSON_CreateBool(apstPdsParam[i]->bPdRoiYellow));
         cJSON_AddItemToObject(pstPdRoiEnable, "green", cJSON_CreateBool(apstPdsParam[i]->bPdRoiGreen));
+        cJSON_AddItemToObject(pstPds, "pdSTLEnable", pstPdSTLEnable);
+        cJSON_AddItemToObject(pstPdSTLEnable, "red", cJSON_CreateBool(apstPdsParam[i]->bPdSTLRed));
+        cJSON_AddItemToObject(pstPdSTLEnable, "yellow", cJSON_CreateBool(apstPdsParam[i]->bPdSTLYellow));
+        cJSON_AddItemToObject(pstPdSTLEnable, "green", cJSON_CreateBool(apstPdsParam[i]->bPdSTLGreen));
         cJSON_AddItemToObject(pstPds, "pdAlarmOutEnable", pstPdAlarmOutEnable);
         cJSON_AddItemToObject(pstPdAlarmOutEnable, "red", cJSON_CreateBool(apstPdsParam[i]->bPdAlarmOutRed));
         cJSON_AddItemToObject(pstPdAlarmOutEnable, "yellow", cJSON_CreateBool(apstPdsParam[i]->bPdAlarmOutYellow));
@@ -4748,7 +4753,7 @@
     cJSON *pstIdentification, *pstMedia, *pstAlg, *pstNetwork, *pstServer, *pstSystem;
     cJSON *pstImage, *pstMainStream, *pstSubStream, *pstJpegStream, *pstVoStream, *pstExtscreenStream, *pstOsdConf, *pstAudio, *pstDmsInterval, *pstPdInterval,*pstPdAlarmOutInterval;
     cJSON *pstWorkSpeed;
-    cJSON *pstPdRectPerson,*pstPdAlarmLight, *pstPdRoiEnable, *pstAlarmOutEnable, *pstPdRoiGui, *pstAudioSettings, *pstDmsAlarmOutSettings, *pstAdvancedSettings, *pstDmsDDAWSettings, *pstEthernet, *pstWifi, *pstApMode, *pstStaMode, *pstNtp, *pstProtocol;
+    cJSON *pstPdRectPerson,*pstPdAlarmLight, *pstPdRoiEnable, *pstPdSTLEnable, *pstAlarmOutEnable, *pstPdRoiGui, *pstAudioSettings, *pstDmsAlarmOutSettings, *pstAdvancedSettings, *pstDmsDDAWSettings, *pstEthernet, *pstWifi, *pstApMode, *pstStaMode, *pstNtp, *pstProtocol;
     cJSON *pstRedWireAlarmOut, *pstYellowWireAlarmOut, *pstGreenWireAlarmOut;
     cJSON *pstPdHollow;
 
@@ -6499,6 +6504,28 @@
                             }
                         }
 
+                        pstPdSTLEnable = cJSON_GetObjectItemCaseSensitive(pstPds, "pdSTLEnable");
+                        if(NULL != pstPdSTLEnable)
+                        {
+                            pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdSTLEnable, "red");
+                            if (NULL != pstTmp)
+                            {
+                                apstPdsParam[i]->bPdSTLRed = pstTmp->valueint;
+                            }
+
+                            pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdSTLEnable, "yellow");
+                            if (NULL != pstTmp)
+                            {
+                                apstPdsParam[i]->bPdSTLYellow = pstTmp->valueint;
+                            }
+
+                            pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdSTLEnable, "green");
+                            if (NULL != pstTmp)
+                            {
+                                apstPdsParam[i]->bPdSTLGreen = pstTmp->valueint;
+                            }
+                        }
+
                         pstRedWireAlarmOut = cJSON_GetObjectItemCaseSensitive(pstPds, "RedWireAlarmOut");
                         if(NULL != pstRedWireAlarmOut)
                         {
Index: src/webui/config.html
===================================================================
--- src/webui/config.html	(版本 5931)
+++ src/webui/config.html	(工作副本)
@@ -2426,6 +2426,23 @@
 								</td>
 							</table>
 						</div>
+						<div data-role="none" class="multi_selectbox" {{{showHWandCustomer "ADA32V2" "200598"}}}>
+							<p class="multi_selectbox_title">{{getKeyLang "Single-Trigger-Line-Switch"}}</p>
+							<table rules="none" class="multi_selectbox_table">
+								<td>
+									<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSTLEnable-red" {{#if pdSTLEnable.red}}checked="checked"{{/if}}>
+									<label for="algConfig-pdsConf-{{@index}}-pdSTLEnable-red"></label>
+								</td>
+								<td>
+									<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSTLEnable-yellow" {{#if pdSTLEnable.yellow}}checked="checked"{{/if}}>
+									<label for="algConfig-pdsConf-{{@index}}-pdSTLEnable-yellow"></label>								
+								</td>
+								<td>
+									<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-pdsConf-{{@index}}-pdSTLEnable-green" {{#if pdSTLEnable.green}}checked="checked"{{/if}}>
+									<label for="algConfig-pdsConf-{{@index}}-pdSTLEnable-green"></label>								
+								</td>
+							</table>
+						</div>
 						<div data-role="none" class="multi_selectbox" {{{hideHardware "HDW845V1"}}}>
 							<p class="multi_selectbox_title">{{getKeyLang "pdRoi-Switch"}}</p>
 							<table rules="none" class="multi_selectbox_table">
Index: src/webui/js/webapp-language.js
===================================================================
--- src/webui/js/webapp-language.js	(版本 5931)
+++ src/webui/js/webapp-language.js	(工作副本)
@@ -172,7 +172,8 @@
 	 "calibration-mode-measure": {"EN": "measure mode", "CN": "测距模式", "JP": "歩行者距離検出モード", "ES": "modo de medición", "PT": "modo de alcance", "RU": "Режим измерения", "TUR": "Ölçüm Modu", "DG": "Messmodus", "ITA": "modalità misura", "FRA": "mode mesure"},
 	 "display-pdRectPerson": {"EN": "Person Rect", "CN": "行人矩形框", "JP": "検出ボックスOn/Off", "ES": "Persona Rect", "PT": "Pessoa Rect", "RU": "Обводка человека", "TUR": "Yaya Dikdörtgeni", "DG": "Personen-Rechteck", "ITA": "Retto persona", "FRA": "Encadrer la personne"},
 	 "pdRoi-Switch": {"EN": "Detection Zone Switch", "CN": "检测区域开关", "JP": "検出ゾーンスイッチ", "ES": "Interruptor de la zona de detección", "PT": "Estilo da Zona de Detecção", "RU": "Отображать зоны обнаружения", "TUR": "Algılama Alan Anahtarı", "DG": "Erkennungsbereich Schalter", "ITA": "Interruttore Zona di rilevamento", "FRA": "Commutateur de Zone Détection"},
-	 "enable-pdRoiRed": {"EN": "Red Zone", "CN": "红色识别区域", "JP": "赤い識別エリア", "ES": "Zona Roja", "PT": "Zona Vermelha", "RU": "Красная зона", "TUR": "Kırmızı Alan", "DG": "Roter Bereich", "ITA": "Zona rossa", "FRA": "Zone rouge"},
+	 "Single-Trigger-Line-Switch":{"EN": "Single Trigger Line Switch", "CN": "单触发线路开关", "JP": "単発トリガー線スイッチ", "ES": "Switch de línea mono-trigger", "PT": "Switch de trigger único", "RU": "Одноком. линейный выключатель", "TUR": "Tek trigger anahtarı", "DG": "Single-Trigger-Schalter", "ITA": "Switch a trigger singolo", "FRA": "Switch à trigger unique"},
+     "enable-pdRoiRed": {"EN": "Red Zone", "CN": "红色识别区域", "JP": "赤い識別エリア", "ES": "Zona Roja", "PT": "Zona Vermelha", "RU": "Красная зона", "TUR": "Kırmızı Alan", "DG": "Roter Bereich", "ITA": "Zona rossa", "FRA": "Zone rouge"},
 	 "enable-pdRoiYellow": {"EN": "Yellow Zone", "CN": "黄色识别区域", "JP": "黄色い識別エリア", "ES": "Zona Amarilla", "PT": "Zona Amarela", "RU": "Желтая зона", "TUR": "Sarı Alan", "DG": "Gelber Bereich", "ITA": "Zona gialla", "FRA": "Zone jaune"},
 	 "enable-pdRoiGreen": {"EN": "Green Zone", "CN": "绿色识别区域", "JP": "緑の識別エリア", "ES": "Zona Verde", "PT": "Zona Verde", "RU": "Зеленая зона", "TUR": "Yeşil Alan", "DG": "Grüner Bereich", "ITA": "Zona verde", "FRA": "Zone verte"},
 	 "pdroigui": {"EN": "Detection Zone Style", "CN": "检测区域显示模式", "JP": "検知エリア表示方法", "ES": "Estilo de zona detección", "PT": "Estilo de Exibição Zona", "RU": "Тип отображения зоны", "TUR": "Algılama Bölgesi Stili", "DG": "Erkennungszonenstil", "ITA": "Stile Zona(Rilevamento)", "FRA": "Style de Zone Détection"},
Index: src/webui/js/webapp-model.js
===================================================================
--- src/webui/js/webapp-model.js	(版本 5931)
+++ src/webui/js/webapp-model.js	(工作副本)
@@ -325,6 +325,11 @@
                         "yellow": "boolean",
                         "green": "boolean"
                     },
+                    "pdSTLEnable": {
+                        "red": "boolean",
+                        "yellow": "boolean",
+                        "green": "boolean"
+                    },
                     "pdRoiGui": "number",
                     "minWorkSpeed":"number",
                     "maxWorkSpeed":"number",
