#include <errno.h>
#include <dirent.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <list>

#include "cms_common.h"
#include "cms_devInfo.h"
#include "cms_offlineInfo.h"
#include "utils.h"
#include "alarm.h"
#include "cJSON.h"
#include "alarm.h"
#include "mongoose.h"
#include "safefunc.h"
#include <sys/statvfs.h>

#define ALARMINFO_MAX_MEMSIZE       (30*1024*1024)      /* 内存目录离线报警信息文件最大占用空间 */
#define ALARMINFO_MAX_SDSIZE        (256*1024*1024)     /* SD卡目录离线报警信息文件最大占用空间 */

#define ALARMINFO_PATH_MEM          "/tmp"              /* 离线报警信息文件内存目录 */
#define ALARMINFO_PATH_SD           "/mnt/sdcard"       /* 离线报警信息文件SD卡目录 */
#define ALARMINFO_PATH_UDISK        "/mnt/udisk"        /* 离线报警信息文件U盘目录 */

static char szOfflineFileFullPath[OFFLINE_TYPE_BUTT][STORAGE_FULLPATH_LEN];
static char szofflineUploadFilePath[STORAGE_FULLPATH_LEN];
static uint8 u8IndexNum;
SV_COMMON_Mutex sMutex_Offline;
SV_COMMON_Mutex sMutex_OfflineCarState;

static bool bOfflineRunning = false;

typedef struct tagOfflineAlarmInfo_S
{
    ALARM_EVENT_S   stAlarmEvent;       /* 报警原始信息 */
    sint32          s32Event;           /* 转换后的报警事件 */
} OFFLINE_ALARM_INFO;

std::list<OFFLINE_ALARM_INFO> m_listOfflineAlarm;   /* 报警信息列表 */
pthread_mutex_t m_mutexOfflineAlarm;                /* 报警信息互斥锁 */

sint32 SV_DVR_CheckAndDeletEmptyDir(const char *pDirPath);

char *SV_DVR_StoragePath()
{
    if (BOARD_DMS31V2_V2 == BOARD_GetVersion())
    {
        return ALARMINFO_PATH_UDISK;
    }
    else
    {
        return ALARMINFO_PATH_SD;
    }
}

sint32 SV_DVR_writeOfflineInfo(const char *pFilePath, const char *pSrc, sint32 s32Size)
{
	sint32 s32Fd;

    /* 只要不是/tmp内存目录的话，都需要检查是否可读写 */
    if (NULL == strstr(pFilePath, ALARMINFO_PATH_MEM) && !SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
	{
		return SV_FAILURE;
	}

	s32Fd = open(pFilePath, O_RDWR | O_CREAT);
	if(s32Fd < 0 )
	{
		print_level(SV_ERROR,"open File:%s error[%s]\n", pFilePath, strerror(errno));
		return SV_FAILURE;
	}

	SV_DVR_COMMON_CloseEvec(s32Fd);
	if( write(s32Fd, pSrc, s32Size) < 0 )
	{
		print_level(SV_ERROR,"write error[%s]\n", strerror(errno));
        close(s32Fd);
        return SV_FAILURE;
	}

	close(s32Fd);

	return SV_SUCCESS;
}

sint32 SV_DVR_createOfflineAlarmInfoFile(const char *pFilePath, OfflineAlarmInfo_st* pstAlarmInfo)
{
    sint32 s32Ret;
	char szUuid[38] = {0};
	char szLat[24] = {0}, szLon[24] = {0};
	cJSON *root, *pSensor, *pOther;
	char *out;
	sint32 s32Size = 0;
	double dLat = 0.0, dLon = 0.0, dSpeed = 0.0;
    DUMP_DMM_S stDumpDmmInfo = {0};
	CFG_SYS_PARAM stSystemParam = {0};
	char szTmpFileName[256] = {0};

	s32Ret = CONFIG_GetSystemParam(&stSystemParam);
	if(s32Ret != SV_SUCCESS)
	{
		print_level(SV_ERROR,"get system config fail.\n");
		return;
	}

    /* GPS信息 */
    if (pstAlarmInfo->stGpsInfo.bHadData)
    {
        dLat = pstAlarmInfo->stGpsInfo.dLatitude;
        dLon = pstAlarmInfo->stGpsInfo.dLongitude;
        dSpeed = pstAlarmInfo->stGpsInfo.dSpk;
    }

    /* 司机名字 */
    dump_GetDmmInfo(&stDumpDmmInfo);

	SV_ADAS_getuuid(szUuid);
	snprintf(szLat, 24, "%lf", dLat);
	snprintf(szLon, 24, "%lf", dLon);

	root=cJSON_CreateObject();
	cJSON_AddStringToObject(root, "EventId",   szUuid);
	cJSON_AddNumberToObject(root, "DeviceId",  SV_ADAS_getDeviceId());
	cJSON_AddStringToObject(root, "DriverId",   stDumpDmmInfo.u8UsrName);
	cJSON_AddNumberToObject(root, "Type",   pstAlarmInfo->u8Type);

	cJSON_AddItemToObject(root, "SensorValue",   pSensor=cJSON_CreateObject());
	cJSON_AddStringToObject(pSensor, "Lat",   szLat);
	cJSON_AddStringToObject(pSensor, "Lng",   szLon);
	cJSON_AddNumberToObject(pSensor, "Speed", (int)dSpeed);

	cJSON_AddItemToObject(root, "Other",   pOther=cJSON_CreateObject());
	cJSON_AddStringToObject(pOther, "AlarmId",   pstAlarmInfo->szAlarmId);
	cJSON_AddStringToObject(pOther, "Time",   pstAlarmInfo->szAlarmTime);
	cJSON_AddNumberToObject(pOther, "alarmRecord",stSystemParam.enAlarmRecord);

	out=cJSON_Print(root);
	s32Size = strlen(out) + 1;

	sprintf(szTmpFileName, "%s_tmp", pFilePath);
	SV_DVR_writeOfflineInfo(szTmpFileName, out, s32Size);
	cJSON_Delete(root);
	free(out);

	rename(szTmpFileName, pFilePath);
    SAFE_SV_System("sync &");

	return SV_SUCCESS;

}


static sint32 OfflineInfo_createMultiDir(const char *path)
{
        sint32 i, s32len;
        s32len = strlen(path);
        char dir_path[s32len+1];
        dir_path[s32len] = '\0';

        strncpy(dir_path, path, s32len);
        print_level(SV_INFO,"OfflineInfo_createMultiDir Dir:%s\n", path);
        for (i=0; i < s32len; i++)
        {
            if (dir_path[i] == '/' && i > 0)
            {
                dir_path[i]='\0';
                if (access(dir_path, F_OK) < 0)
                {
                    if (mkdir(dir_path, S_IRWXU | S_IRWXG | S_IRWXO) < 0)
                    {
                        print_level(SV_ERROR,"mkdir=%s:msg=%s\n", dir_path, strerror(errno));
                        return SV_FAILURE;
                    }
                }
                dir_path[i]='/';
            }
        }

        return SV_SUCCESS;
}

static sint32 OfflineInfo_GetSaveDisk(char *path, uint8 u8Type)
{
    char szStoragePath[32] = {0};
	char szSaveDir[128] = {0};
    STORAGE_POS_E enPos;

	if(u8Type == OFFLINE_ALARM_TYPE)
	{
		strncpy(szSaveDir, OFFLINE_ALARM_DIR, 128);
	}
	else if(u8Type == OFFLINE_BBALARM_TYPE)
	{
		strncpy(szSaveDir, OFFLINE_BBALARM_DIR, 128);
	}
	else
	{
		strncpy(szSaveDir, OFFLINE_INFO_DIR, 128);
	}

    SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);
    sprintf(path, "%s/%s", szStoragePath, szSaveDir);

	return SV_SUCCESS;
}

static sint32 OfflineInfo_GetSaveDir(char *path)
{

	time_t timep;
	struct tm tmNowTime;

	time (&timep);
	localtime_r(&timep, &tmNowTime);
	snprintf(path, STORAGE_FULLPATH_LEN, "%04d%02d%02d/%02d/%02d/", (tmNowTime.tm_year+1900), (tmNowTime.tm_mon+1),
		tmNowTime.tm_mday, tmNowTime.tm_hour, tmNowTime.tm_min);

	return SV_SUCCESS;
}

sint32 SV_DVR_GetOfflineInfoDirPath(uint8 u8Type, char *szSavrDir, char *szPath)
{
	char szDiskDir[STORAGE_FULLPATH_LEN] = {0};

	OfflineInfo_GetSaveDisk(szDiskDir, u8Type);

	if(u8Type == OFFLINE_DOWNLOAD_TYPE)
	{
		snprintf(szPath, STORAGE_FULLPATH_LEN, "%s", szDiskDir);
	}
	else
	{
		snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", szDiskDir, szSavrDir);
	}

	if( access(szPath, F_OK) < 0 )
	{
		if( OfflineInfo_createMultiDir(szPath) < 0 )
		{
			print_level(SV_ERROR,"mkdir error\n");
			return SV_FAILURE;
		}
		else
		{
		//	print_level(SV_DEBUG,"mkdir success\n");
		}
	}

	return SV_SUCCESS;
}

sint32 SV_DVR_createOfflineAlarmInfo(char *szSavrDir, OfflineAlarmInfo_st * pstAlarmInfo)
{
	SV_COMMON_ScopedLock  lock(sMutex_Offline);
	char szFullPath[STORAGE_FULLPATH_LEN] = {0}, szSaveFullDir[STORAGE_FULLPATH_LEN] = {0};
	SV_DVR_GetOfflineInfoDirPath(OFFLINE_ALARM_TYPE, szSavrDir, szSaveFullDir);

	snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s%s_%d", szSaveFullDir, pstAlarmInfo->szAlarmId, pstAlarmInfo->u32AlarmSeq);
	SV_DVR_createOfflineAlarmInfoFile(szFullPath, pstAlarmInfo);
	print_level(SV_DEBUG,"create szFullPath:%s finished\n", szFullPath);

	return SV_SUCCESS;
}

sint32 SV_DVR_createOfflineBBAlarmInfo(char *szSavrDir, OfflineAlarmInfo_st * pstAlarmInfo)
{
	SV_COMMON_ScopedLock  lock(sMutex_Offline);
	char szFullPath[STORAGE_FULLPATH_LEN] = {0}, szSaveFullDir[STORAGE_FULLPATH_LEN] = {0};
	SV_DVR_GetOfflineInfoDirPath(OFFLINE_BBALARM_TYPE, szSavrDir, szSaveFullDir);

	snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s%s_%d", szSaveFullDir, pstAlarmInfo->szAlarmId, pstAlarmInfo->u32AlarmSeq);
	SV_DVR_createOfflineAlarmInfoFile(szFullPath, pstAlarmInfo);
	print_level(SV_DEBUG,"create szFullPath:%s finished\n", szFullPath);

	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取所有报警信息文件总大小(字节)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : >0 - 总大小
             <0 - 失败
 * 注意    : 无
 *****************************************************************************/
uint64 getFileSize(char *dirname)
{
    DIR *dir;
    struct dirent *pstDir;
    uint64 u64TotalSize = 0;
    char szPath[PATH_MAX] = {0};

    dir = opendir(dirname);
    if(dir == NULL)
    {
        print_level(SV_ERROR,"open dir: %s failed.\n",szPath);
        exit(1);
    }

    while((pstDir=readdir(dir)) != NULL)
    {
        snprintf(szPath, (size_t)PATH_MAX, "%s/%s", dirname,pstDir->d_name);
        struct stat stBuf;
        if(lstat(szPath, &stBuf) < 0)
        {
                print_level(SV_ERROR,"lstat %s error.\n", szPath);
        }
        if(strcmp(pstDir->d_name,".") == 0)
        {
                u64TotalSize += stBuf.st_size;
                continue;
        }
        if(strcmp(pstDir->d_name,"..") == 0)
        {
                continue;
        }
        if(pstDir->d_type == DT_DIR)
        {
            u64TotalSize += getFileSize(szPath);
            memset(szPath, 0, sizeof(szPath));
        }
        else
        {
            u64TotalSize += stBuf.st_size;
        }
    }
    closedir(dir);
    return u64TotalSize;
}

sint32 getAlarmInfoPathRemainSize(char *szPath, uint64 *u64FreeSize)
{
    sint32 s32Ret = 0;
    uint64 u64FreeBytes = 0;
    struct statvfs stStatvfs = {0};

    if (SV_SUCCESS != access(szPath, F_OK))
    {
        print_level(SV_ERROR, "%s is not exist!\n", szPath);
        return SV_FAILURE;
    }

    s32Ret = statvfs((szPath), &stStatvfs);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR,"statvfs failed for path->[%s]\n", szPath);
        return SV_FAILURE;
    }
    u64FreeBytes = stStatvfs.f_bfree * stStatvfs.f_bsize;
    *u64FreeSize = u64FreeBytes;

    return SV_SUCCESS;
}

sint32 getAlarmInfoFileSize(char *szPath, sint32 *s32UsedSize)
{
	sint32 s32Ret = 0;
    char szCmd[64] = {0};
    char szBuf[128] = {0};
    sint32 s32UsedBytes = 0;

    if (SV_SUCCESS != access(szPath, F_OK))
    {
        print_level(SV_ERROR, "%s is not exist!\n", szPath);
        return SV_FAILURE;
    }

    sprintf(szCmd, "du -s %s | awk -F ' ' '{print $1}'", szPath);
    s32Ret = SAFE_System_Recv(szCmd, szBuf, 128);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }
    COMMON_CutLineBreak(szBuf);

    if (0 != strlen(szBuf))
    {
        s32UsedBytes = atoi(szBuf)*1024;
    }
    *s32UsedSize = s32UsedBytes;


    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 删除过旧的报警信息文件释放空间
 * 输入参数: szPath --- 要遍历查找的目录
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 removeOldAlarmInfoFile(char *szPath)
{

    sint32 s32Ret = 0;
    DIR *pRootDir = NULL;
    DIR *pDir = NULL;
    struct dirent *pstDirent = NULL;
    char szOldestDayFile[64] = {0};
	char szOldestHourFile[64] = {0};
    char szOldestDayPath[64];
	char szOldestHourPath[64];
	char szCmd[64];

    pRootDir = opendir(szPath);
    if(NULL == pRootDir)
    {
        print_level(SV_ERROR, "opendir %s failed. [err: %s]\n", szPath, strerror(errno));
        return SV_FAILURE;
    }
    while (1)
    {
        if (NULL == strstr(szPath, ALARMINFO_PATH_MEM) && !SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
        {
            print_level(SV_ERROR, "sdcard is not writable now, skip to remove old sd file.\n");
            goto exit;
        }

        pstDirent = readdir(pRootDir);
        if(NULL == pstDirent)
        {
            closedir(pRootDir);
            pRootDir = NULL;
            break;
        }
		if (strcmp(pstDirent->d_name, ".")==0)
			continue;
		if (strcmp(pstDirent->d_name, "..")==0)
			continue;

        if (0 == strlen(szOldestDayFile))
        {
            strcpy(szOldestDayFile, pstDirent->d_name);
        }
        else if (strcmp(pstDirent->d_name, szOldestDayFile) < 0)
        {
            strcpy(szOldestDayFile, pstDirent->d_name);
        }
    }//找出最旧的“日”目录

    if (0 != strlen(szOldestDayFile))
    {
        sprintf(szOldestDayPath, "%s/%s", szPath, szOldestDayFile);
		pDir = opendir(szOldestDayPath);
	    if(NULL == pDir)
	    {
	        print_level(SV_ERROR, "opendir %s failed. [err: %s]\n", szOldestDayPath, strerror(errno));
	        return SV_FAILURE;
	    }
	    while (1)
	    {
            if (NULL == strstr(szPath, ALARMINFO_PATH_MEM) && !SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
            {
                print_level(SV_ERROR, "sdcard is not writable now, skip to remove old sd file.\n");
                goto exit;
            }

	        pstDirent = readdir(pDir);
	        if(NULL == pstDirent)
	        {
	            closedir(pDir);
                pDir = NULL;
	            break;
	        }
			if (strcmp(pstDirent->d_name, ".")==0)
				continue;
			if (strcmp(pstDirent->d_name, "..")==0)
				continue;

	        if (0 == strlen(szOldestHourFile))
	        {
	            strcpy(szOldestHourFile, pstDirent->d_name);
	        }
	        else if (strcmp(pstDirent->d_name, szOldestHourFile) < 0)
	        {
	            strcpy(szOldestHourFile, pstDirent->d_name);
	        }
	    }//找出最旧的“时”目录

		if (0 != strlen(szOldestHourFile))
	    {
	        sprintf(szOldestHourPath, "%s/%s", szOldestDayPath, szOldestHourFile);
			sprintf(szCmd, "rm -r %s", szOldestHourPath);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (SV_SUCCESS != s32Ret)
            {
	            print_level(SV_ERROR, "remove file: %s failed. [err: %s]\n", szOldestHourPath, strerror(errno));
                return SV_FAILURE;
            }
            SV_DVR_CheckAndDeletEmptyDir(szOldestDayPath);
	    }
    }


exit:
    if (NULL != pRootDir)
    {
        closedir(pRootDir);
    }

    if (NULL != pDir)
    {
        closedir(pDir);
    }

    return SV_SUCCESS;
}

sint32 SV_DVR_AddOfflineAlarmInfo(ALARM_EVENT_S *pstAlarmEvent, int event)
{
    OFFLINE_ALARM_INFO stOfflineAlarmInfo = {0};

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        return SV_SUCCESS;
    }

    if (NULL == pstAlarmEvent)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        return SV_FAILURE;
    }

    memcpy(&stOfflineAlarmInfo.stAlarmEvent, pstAlarmEvent, sizeof(ALARM_EVENT_S));
    stOfflineAlarmInfo.s32Event = event;

    pthread_mutex_lock(&m_mutexOfflineAlarm);
    m_listOfflineAlarm.push_back(stOfflineAlarmInfo);
    pthread_mutex_unlock(&m_mutexOfflineAlarm);
    //print_level(SV_INFO, "+++push_back time: %lld, event: %d\n", pstAlarmEvent->s64TimeStamp, event);

	return SV_SUCCESS;
}

static sint32 SV_DVR_ReadFileBuf(const char *pFile, char *pBuf, uint32 u32Size)
{
	//print_level(SV_DEBUG,"pFile:%s\n", pFile);
	sint32 s32Fd = -1, s32ReadNum = -1;

	if( (s32Fd = open(pFile, O_RDWR)) < 0 )
	{
	//	print_level(SV_DEBUG,"Open error! file:%s\n", pFile);
		return SV_FAILURE;
	}

	SV_DVR_COMMON_CloseEvec(s32Fd);
	s32ReadNum = read(s32Fd, pBuf, u32Size);
	if( s32ReadNum <= 0 )
	{
		print_level(SV_ERROR,"Read file:%s error! %s\n", pFile, strerror(errno));
		close(s32Fd);
		unlink(pFile);
		return SV_FAILURE;
	}

	close(s32Fd);
	//print_level(SV_DEBUG,"SV_SUCCESS\n");
	return SV_SUCCESS;
}

sint32 SV_DVR_GetofflineDirInfo(const char *pDirPath, uint8 u8Type, char *pBuf, uint32 u32Size, SV_BOOL bUpload)
{
    sint32 s32Ret = SV_FAILURE, i;
    DIR *pDir = NULL;
    struct dirent* file = NULL;

    char szPath[STORAGE_FULLPATH_LEN] = {0};
	char szOldFile[STORAGE_FULLPATH_LEN] = {0};
	char szOldDir[STORAGE_FULLPATH_LEN] = {0};
    strncpy(szOldFile, "CanData_999999", STORAGE_FULLPATH_LEN);
	strncpy(szOldDir, "999999", STORAGE_FULLPATH_LEN);
	SV_BOOL bHaveDir = SV_FALSE;

    /* 目录不存在或空目录时返回 */
	if (!COMMON_IsPathExist(pDirPath))
	{
		return SV_FAILURE;
	}

	if (NULL == strstr(pDirPath, ALARMINFO_PATH_MEM) && !SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
	{
		print_level(SV_WARN, "pDirPath: %s but is not writable!\n", pDirPath);
		return SV_FAILURE;
	}

    pDir = opendir(pDirPath);
    if (NULL == pDir)
	{
		print_level(SV_DEBUG,"Open dir:%s error!\n", pDirPath);
		return SV_FAILURE;
	}
	//print_level(SV_DEBUG,"Open dir:%s successfully!\n", pDirPath);

	while ((file = readdir(pDir)) != NULL)
	{
		if (strcmp(file->d_name,".") == 0 || strcmp(file->d_name,"..") == 0)
		{
			continue;
		}

		if (file->d_type & DT_DIR)
		{
			if (u8Type == OFFLINE_CANDATA_TYPE)
			{
				bHaveDir = SV_TRUE;
				if (strncmp(szOldDir, file->d_name, STORAGE_FULLPATH_LEN) > 0)
				{
					strncpy(szOldDir, file->d_name, STORAGE_FULLPATH_LEN);
				}
			}
			else
			{
				snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s/", pDirPath, file->d_name);
				if (SV_DVR_GetofflineDirInfo(szPath, u8Type, pBuf, u32Size, bUpload) == SV_SUCCESS)
				{
					s32Ret = SV_SUCCESS;
					break;
				}
			}
		}
		else
		{
		    if (NULL != strstr(file->d_name, "_tmp"))
		    {
                //print_level(SV_WARN, "get file name: %s is writing, wating!\n", file->d_name);
                continue;
		    }

			if (u8Type == OFFLINE_CANDATA_TYPE)
			{
				if (strncmp(szOldFile, file->d_name, STORAGE_FULLPATH_LEN) > 0)
				{
					strncpy(szOldFile, file->d_name, STORAGE_FULLPATH_LEN);
				}
			}
			else
			{
				if (bUpload && strstr(file->d_name, "_upload") && u8Type == OFFLINE_ALARM_TYPE)
				{
					snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", pDirPath, file->d_name);
					if( SV_DVR_ReadFileBuf(szPath, pBuf, u32Size) == SV_SUCCESS)
					{
						strncpy(szofflineUploadFilePath, szPath, STORAGE_FULLPATH_LEN);
						s32Ret = SV_SUCCESS;

					}
				}
				else if (!bUpload && !strstr(file->d_name, "_upload"))
				{
					snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", pDirPath, file->d_name);
					if( SV_DVR_ReadFileBuf(szPath, pBuf, u32Size) == SV_SUCCESS)
					{
						strncpy(szOfflineFileFullPath[u8Type], szPath, STORAGE_FULLPATH_LEN);
						s32Ret = SV_SUCCESS;
						break;
					}
				}
			}
		}
	}
	closedir(pDir);

	if (u8Type == OFFLINE_CANDATA_TYPE)
	{

		if(bHaveDir)
		{
			snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s/", pDirPath, szOldDir);
			if (SV_DVR_GetofflineDirInfo(szPath, u8Type, pBuf, u32Size, bUpload) == SV_SUCCESS)
			{
				s32Ret = SV_SUCCESS;
			}
		}

		snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", pDirPath, szOldFile);
		if (SV_DVR_ReadFileBuf(szPath, pBuf, u32Size) == SV_SUCCESS)
		{
			strncpy(szOfflineFileFullPath[u8Type], szPath, STORAGE_FULLPATH_LEN);
			print_level(SV_DEBUG,"szPath:%s\n", szPath);
			s32Ret = SV_SUCCESS;
		}
	}

	return s32Ret;
}

sint32 SV_DVR_GetofflineInfo(uint8 u8Type, char *pBuf, uint32 u32Size, SV_BOOL bUpload)
{
	//SV_COMMON_ScopedLock  lock(sMutex_Offline);
    char szStoragePath[32] = {0};
	char szSaveDir[128] = {0}, szPath[STORAGE_FULLPATH_LEN];

	if(u8Type == OFFLINE_ALARM_TYPE)
	{
		strncpy(szSaveDir, OFFLINE_ALARM_DIR, 128);
	}
	else
	{
		strncpy(szSaveDir, OFFLINE_INFO_DIR, 128);
	}

	snprintf(szPath, STORAGE_FULLPATH_LEN, "%s/%s", ALARMINFO_PATH_MEM, szSaveDir);
	if( SV_DVR_GetofflineDirInfo(szPath, u8Type, pBuf, u32Size, bUpload) == SV_SUCCESS)
	{
		return SV_SUCCESS;
	}

	if (SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath))
	{
		snprintf(szPath, STORAGE_FULLPATH_LEN, "%s/%s", szStoragePath, szSaveDir );
		if( SV_DVR_GetofflineDirInfo(szPath, u8Type, pBuf, u32Size, bUpload) == SV_SUCCESS)
		{
			return SV_SUCCESS;
		}

	}

	return SV_FAILURE;
}

sint32 SV_DVR_getFileDir(const char *szSrc, char *szDir)
{
	sint32 s32Count = 0, s32DirLen = 0;
	char szSrcPath[256] = {0};
    strncpy(szSrcPath, szSrc, 256);
	sint32 s32len = strlen(szSrcPath);
	char *pTemp = szSrcPath;

	if (szSrcPath[s32len-1] == '/')
	{
        szSrcPath[s32len-1] = '\0';
	}

    pTemp = strrchr(szSrcPath, '/');
    if (NULL == pTemp)
    {
        print_level(SV_WARN, "not find\n");
    }

	s32DirLen = pTemp - szSrcPath;
	//print_level(SV_DEBUG,"s32DirLen:%d pTemp:%s\n", s32DirLen, pTemp);

	strncpy(szDir, szSrcPath, s32DirLen);
	szDir[s32DirLen] = 0;

	return SV_SUCCESS;
}

sint32 SV_DVR_CheckAndDeletEmptyDir(const char *pDirPath)
{
    sint32 s32Ret = 0;

    if (!COMMON_IsPathExist(pDirPath))
    {
        return SV_FAILURE;
    }

    s32Ret = rmdir(pDirPath);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }

    print_level(SV_INFO, "rmdir empty dir: %s success\n", pDirPath);
	return s32Ret;
}

sint32 SV_DVR_DeletEmptyDirRound(const char *pDirPath)
{
    sint32 s32Ret = 0;
	uint8 u8len = 0;
    char szStoragePath[32] = {0};
	char szDir[STORAGE_FULLPATH_LEN], szTempDir[STORAGE_FULLPATH_LEN];

    if (!COMMON_IsPathExist(pDirPath))
    {
        return SV_FAILURE;
    }

    s32Ret = rmdir(pDirPath);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }

    SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);
    u8len = strlen(szStoragePath);// /mnt/sdcard

	SV_DVR_getFileDir(pDirPath, szDir);
	while(strlen(szDir) > u8len) // /data/ssd
	{
		strncpy(szTempDir, szDir, STORAGE_FULLPATH_LEN);
		SV_DVR_DeletEmptyDirRound(szDir);
		SV_DVR_getFileDir(szTempDir, szDir);
	}

    print_level(SV_INFO, "rmdir empty dir: %s success\n", pDirPath);
	return s32Ret;
}

sint32 SV_DVR_DelFileAndEmptyDir(char *szFilePath)
{
    char szStoragePath[32] = {0};
	char szDir[STORAGE_FULLPATH_LEN], szTempDir[STORAGE_FULLPATH_LEN];
	uint8 u8len = 0;

	//print_level(SV_INFO,"szFilePath:%s\n", szFilePath);
	if( unlink(szFilePath) != 0 )
	{
		print_level(SV_ERROR,"file:%s unlink error:%s\n", szFilePath, strerror(errno));
	}

    SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);
    u8len = strlen(szStoragePath);// /mnt/sdcard

	SV_DVR_getFileDir(szFilePath, szDir);
	while(strlen(szDir) > u8len) // /data/ssd
	{
		strncpy(szTempDir, szDir, STORAGE_FULLPATH_LEN);
		//print_level(SV_DEBUG,"szTempDir:%s\n", szTempDir);
		SV_DVR_CheckAndDeletEmptyDir(szDir);
		SV_DVR_getFileDir(szTempDir, szDir);
	}

	return SV_SUCCESS;
}

sint32 SV_DVR_DelofflineInfo(const char *pAlarmId, uint8 u8Type)
{
	SV_COMMON_ScopedLock  lock(sMutex_Offline);

	SV_DVR_DelFileAndEmptyDir(szOfflineFileFullPath[u8Type]);
	print_level(SV_DEBUG,"delete file: %s success\n", szOfflineFileFullPath[u8Type]);

	return SV_SUCCESS;
}

sint32 SV_DVR_RenameofflineInfo(const char *pAlarmId, uint8 u8Type)
{
	SV_COMMON_ScopedLock  lock(sMutex_Offline);
	char szNewName[STORAGE_FULLPATH_LEN] = {0};
	snprintf(szNewName, STORAGE_FULLPATH_LEN, "%s_upload", szOfflineFileFullPath[u8Type]);
	rename(szOfflineFileFullPath[u8Type], szNewName);
	return SV_SUCCESS;
}

sint32 SV_DVR_DeleteRelatedAlarmTilePath(char *pPath)
{
	char szDir[STORAGE_FULLPATH_LEN] = {0}, szFileName[32] = {0}, szAlarmId[32] = {0};
	char szFullPath[STORAGE_FULLPATH_LEN] = {0};
	struct dirent* file = SV_NULL;
	DIR *pDir = SV_NULL;

	SV_COMMON_getFileDir(pPath, szDir);
	SV_COMMON_cutOutName(pPath, szFileName);
	memcpy(szAlarmId, szFileName, 14);          //"19000101010101"

	if( !(pDir=opendir(szDir)) )
	{
		print_level(SV_DEBUG,"Open dir:%s error!\n", szDir);
		return SV_FAILURE;
	}
	else
	{
		print_level(SV_DEBUG,"Open dir:%s success!\n", szDir);
	}

	while( (file = readdir(pDir)) != NULL )
	{
		if(strcmp(file->d_name,".")==0 || strcmp(file->d_name,"..")==0)
		{
			continue;
		}

		if(strstr(file->d_name, szAlarmId))
		{
			snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s/%s", szDir, file->d_name);
			print_level(SV_DEBUG,"unlink szFullPath:%s\n", szFullPath);
			unlink(szFullPath);
		}

	}
	closedir(pDir);

	return SV_SUCCESS;
}

sint32 SV_DVR_DeleteRelatedAlarmTile()
{
	char szDir[STORAGE_FULLPATH_LEN] = {0}, szFileName[32] = {0}, szAlarmId[32] = {0};
	char szFullPath[STORAGE_FULLPATH_LEN] = {0};
	struct dirent* file = SV_NULL;
	DIR *pDir = SV_NULL;

	SV_COMMON_getFileDir(szofflineUploadFilePath, szDir);
	SV_COMMON_cutOutName(szofflineUploadFilePath, szFileName);
	memcpy(szAlarmId, szFileName, 14);          //"19000101010101"

	if( !(pDir=opendir(szDir)) )
	{
		print_level(SV_DEBUG,"Open dir:%s error!\n", szDir);
		return SV_FAILURE;
	}
	else
	{
		print_level(SV_DEBUG,"Open dir:%s success!\n", szDir);
	}

	while( (file = readdir(pDir)) != NULL )
	{
		if(strcmp(file->d_name,".")==0 || strcmp(file->d_name,"..")==0)
		{
			continue;
		}

		if(strstr(file->d_name, szAlarmId))
		{
			snprintf(szFullPath, STORAGE_FULLPATH_LEN, "%s/%s", szDir, file->d_name);
			print_level(SV_DEBUG,"unlink szFullPath:%s\n", szFullPath);
			unlink(szFullPath);
		}

	}
	closedir(pDir);

	memset(szofflineUploadFilePath, 0, STORAGE_FULLPATH_LEN);
	return SV_SUCCESS;
}

sint32 cms_offline_GetCarStateFileSize(char *szPath, sint32 *s32UsedSize)
{
	sint32 s32Ret = 0;
    char szCmd[64] = {0};
    char szBuf[128] = {0};
    sint32 s32UsedBytes = 0;

    if (SV_SUCCESS != access(szPath, F_OK))
    {
        print_level(SV_ERROR, "%s is not exist!\n", szPath);
        return SV_FAILURE;
    }

    sprintf(szCmd, "du -s %s | awk -F ' ' '{print $1}'", szPath);
    s32Ret = SAFE_System_Recv(szCmd, szBuf, 128);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }
    COMMON_CutLineBreak(szBuf);

    if (0 != strlen(szBuf))
    {
        s32UsedBytes = atoi(szBuf)*1024;
    }
    *s32UsedSize = s32UsedBytes;


    return SV_SUCCESS;
}

sint32 cms_offline_removeOldCarStateFile(char *szPath)
{

    sint32 s32Ret = 0;
    DIR *pRootDir = NULL;
    struct dirent *pstDirent = NULL;
    char szOldestFile[128] = {0};
    char szDelPath[128] = {0};
	char szCmd[128] = {0};

    pRootDir = opendir(szPath);
    if(NULL == pRootDir)
    {
        print_level(SV_ERROR, "opendir %s failed. [err: %s]\n", szPath, strerror(errno));
        goto error_exit;
    }

    while (1)
    {
        if (NULL == strstr(szPath, CAR_STATE_PATH_MEM) && !SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
        {
            print_level(SV_ERROR, "sdcard is not writable now, skip to remove old sd file.\n");
            goto exit;
        }

        pstDirent = readdir(pRootDir);
        if(NULL == pstDirent)
        {
            closedir(pRootDir);
            pRootDir = NULL;
            break;
        }

		if (strcmp(pstDirent->d_name, ".") == 0 || strcmp(pstDirent->d_name, "..") == 0 || strcmp(pstDirent->d_name, "offlineCarStateFile") == 0)
		{
			continue;
		}

        if (0 == strlen(szOldestFile))
        {
            strcpy(szOldestFile, pstDirent->d_name);
        }
        else if (strcmp(pstDirent->d_name, szOldestFile) < 0)
        {
            strcpy(szOldestFile, pstDirent->d_name);
        }
    }//找出最旧的“日”目录

    if (0 != strlen(szOldestFile))
    {
        sprintf(szDelPath, "%s/%s", szPath, szOldestFile);
		sprintf(szCmd, "rm -rf %s", szDelPath);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "remove file: %s failed. [err: %s]\n", szDelPath, strerror(errno));
            goto error_exit;
        }
    }

exit:
    if (NULL != pRootDir)
    {
        closedir(pRootDir);
    }
    return SV_SUCCESS;

error_exit:
    if (NULL != pRootDir)
    {
        closedir(pRootDir);
    }
    return SV_FAILURE;

}

const std::string cms_offlineInfo_GetTimeString()
{
	char buf[20];
    struct tm stTime = {0};
    struct timeval tvTime;
    struct timezone tz;

    gettimeofday(&tvTime, &tz);
    tvTime.tv_sec += (tz.tz_minuteswest * 60);
    tvTime.tv_usec = 0;
    gmtime_r((time_t *)&tvTime.tv_sec, &stTime);
	strftime(buf, sizeof(buf), "%Y%m%d%H%M%S", &stTime);
	std::string strRet(buf);
	return strRet;
}

sint32 cms_offlineInfo_GetCarStateDirInfo(const char *pDirPath, uint8 u8Type, char *pBuf, uint32 u32Size, char *pSendCarStateId)
{
	sint32 s32Ret = SV_FAILURE;

	DIR *pDir = NULL;
	struct dirent* file = NULL;
	char szPath[STORAGE_FULLPATH_LEN] = {0};

    if (NULL == pBuf || NULL == pSendCarStateId)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        return SV_FAILURE;
    }

    if (!COMMON_IsPathExist(pDirPath))
    {
        return SV_FAILURE;
    }

    if (NULL == strstr(pDirPath, CAR_STATE_PATH_MEM) && !SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
	{
        print_level(SV_ERROR, "szPath: %s but storage is not writable!\n", pDirPath);
		return SV_FAILURE;
	}

    s32Ret = SV_DVR_CheckAndDeletEmptyDir(pDirPath);
	if (SV_SUCCESS == s32Ret)
	{
		return SV_FAILURE;
	}

    pDir = opendir(pDirPath);
    if (NULL == pDir)
    {
        print_level(SV_DEBUG,"Open dir:%s error!\n", pDirPath);
		return SV_FAILURE;
    }

	while ((file = readdir(pDir)) != NULL)
	{
        if (strcmp(file->d_name, ".") == 0 || strcmp(file->d_name, "..") == 0 || file->d_type & DT_DIR || strcmp(file->d_name, "offlineCarStateFile") == 0)
        {
            continue;
        }

		if (strstr(file->d_name, "offlineCarStateFile_"))
		{
			snprintf(szPath, STORAGE_FULLPATH_LEN, "%s/%s", pDirPath, file->d_name);
            s32Ret = SV_DVR_ReadFileBuf(szPath, pBuf, u32Size);
			if (SV_SUCCESS != s32Ret)
			{
				s32Ret = SV_FAILURE;
                continue;
			}

            char *res = strstr(file->d_name, "offlineCarStateFile_");
            strcpy(pSendCarStateId, res+strlen("offlineCarStateFile_"));
            strncpy(szOfflineFileFullPath[u8Type], szPath, STORAGE_FULLPATH_LEN);
            s32Ret = SV_SUCCESS;
            break;
		}
	}
	closedir(pDir);

	return s32Ret;
}

sint32 SV_DVR_DelOfflineCarStateInfo(uint8 u8Type)
{
	SV_COMMON_ScopedLock  lock(sMutex_OfflineCarState);

	print_level(SV_DEBUG,"delete file:%s\n", szOfflineFileFullPath[u8Type]);
	SV_DVR_DelFileAndEmptyDir(szOfflineFileFullPath[u8Type]);

	return SV_SUCCESS;
}

sint32 SV_DVR_GetOfflineCarStateInfo(uint8 u8Type, char *pBuf, uint32 u32Size, char *pSendCarStateId)
{
	SV_COMMON_ScopedLock  lock(sMutex_OfflineCarState);
    sint32 s32Ret = 0;
    char szSaveDir[STORAGE_FULLPATH_LEN] = {0};
    char szStoragePath[32] = {0};

	snprintf(szSaveDir, STORAGE_FULLPATH_LEN, "%s", CAR_STATE_PATH_MEM);
    s32Ret = cms_offlineInfo_GetCarStateDirInfo(szSaveDir, u8Type, pBuf, u32Size, pSendCarStateId);
	if (SV_SUCCESS == s32Ret)
	{
		return SV_SUCCESS;
	}

	if (SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath))
	{
        snprintf(szSaveDir, STORAGE_FULLPATH_LEN, "%s/%s", szStoragePath, CAR_STATE_PATH);
        s32Ret = cms_offlineInfo_GetCarStateDirInfo(szSaveDir, u8Type, pBuf, u32Size, pSendCarStateId);
    	if (SV_SUCCESS == s32Ret)
    	{
    		return SV_SUCCESS;
    	}
	}

	return SV_FAILURE;
}


sint32 SV_DVR_CreateOfflineCarStateFile(CAR_STATE_S *pstCarState)
{
    sint32 s32Ret = 0, s32Fd = -1;
    uint32 u32ValidDataLen = 0;
    char szBuf[30*1024] = {0};
    cJSON *pstStatJson = NULL, *pstRegularData = NULL, *pstDataList = NULL, *pstData = NULL;
    cJSON *pstRegularDataFile = NULL, *pstDataListFile = NULL;
    cJSON *pstAllData = NULL;
    char *pszStatJson = NULL;
    FILE *fp = NULL;
    char szCmd[128] = {0};
    char szStoragePath[32] = {0};
	char szSaveDir[STORAGE_FULLPATH_LEN] = {0};
	char szSavePath[STORAGE_FULLPATH_LEN] = {0};
	char szSaveDirFull[STORAGE_FULLPATH_LEN] = {0};
	char szCarStateFile[STORAGE_FULLPATH_LEN] = {0};
    sint32 s32ArraySize = 0;
    static char szLastDir[STORAGE_FULLPATH_LEN] = {0};
    char szNewName[STORAGE_FULLPATH_LEN] = {0};
    char szCurTime[SK_DATE_TIME_STRING_MAX_LENGTH] = {0};
    char szCurDate[8] = {0};
    SV_BOOL bNeedCheckSize = SV_FALSE;
    static SV_BOOL bWorking = SV_FALSE;
    sint32 s32UsedSize = 0, s32MaxSize = 0;
    DUMP_GPS_S stGpsInfo = {0};
    double dSensorX = 0.0, dSensorY = 0.0, dSensorZ = 0.0;
    SV_BOOL bStorageWritable = SV_FALSE;

    if (NULL == pstCarState)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        return SV_FAILURE;
    }

    pstStatJson = cJSON_CreateObject();
    if (NULL == pstStatJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return SV_FAILURE;
    }

    pstRegularData = cJSON_CreateObject();
    if (NULL == pstRegularData)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        cJSON_Delete(pstStatJson);
        return SV_FAILURE;
    }
    cJSON_AddItemToObject(pstStatJson, "RegularData", pstRegularData);

	cJSON_AddItemToObject(pstRegularData, "DeviceId", cJSON_CreateNumber(SV_ADAS_getDeviceId()));
	cJSON_AddItemToObject(pstRegularData, "DataType", cJSON_CreateNumber(1));
	cJSON_AddItemToObject(pstRegularData, "DataAttr", cJSON_CreateNumber(0));

    pstDataList = cJSON_CreateArray();
    if (NULL == pstDataList)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        cJSON_Delete(pstStatJson);
        return SV_FAILURE;
    }
    cJSON_AddItemToObject(pstRegularData, "Data", pstDataList);

    pstData = cJSON_CreateObject();
    if (NULL == pstData)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        cJSON_Delete(pstStatJson);
        return SV_FAILURE;
    }
    cJSON_AddItemToArray(pstDataList, pstData);

    #if 1
	cJSON_AddItemToObject(pstData, "ExDevSate", cJSON_CreateNumber(pstCarState->usExDevSate));
	cJSON_AddItemToObject(pstData, "DiskState", cJSON_CreateNumber(pstCarState->uiDiskState));
	cJSON_AddItemToObject(pstData, "Time", cJSON_CreateString(pstCarState->szCurTime));
	cJSON_AddItemToObject(pstData, "Direction", cJSON_CreateNumber(pstCarState->sCourse));
	cJSON_AddItemToObject(pstData, "Speed", cJSON_CreateNumber(pstCarState->uiCarSpeed));
	cJSON_AddItemToObject(pstData, "Lat", cJSON_CreateString(pstCarState->szLatitude));
	cJSON_AddItemToObject(pstData, "Lng", cJSON_CreateString(pstCarState->szLongitude));
	cJSON_AddItemToObject(pstData, "Elv", cJSON_CreateString(pstCarState->szElv));
	cJSON_AddItemToObject(pstData, "Temperature", cJSON_CreateNumber(pstCarState->uiCarTemperature));
    #else
    cJSON_AddItemToObject(pstData, "ExDevSate", cJSON_CreateNumber(1));
	cJSON_AddItemToObject(pstData, "DiskState", cJSON_CreateNumber(pstCarState->uiDiskState));
	cJSON_AddItemToObject(pstData, "Time", cJSON_CreateString(pstCarState->szCurTime));
	cJSON_AddItemToObject(pstData, "Direction", cJSON_CreateNumber(pstCarState->sCourse));
	cJSON_AddItemToObject(pstData, "Speed", cJSON_CreateNumber(pstCarState->uiCarSpeed));
	cJSON_AddItemToObject(pstData, "Lat", cJSON_CreateString("115.382794"));
	cJSON_AddItemToObject(pstData, "Lng", cJSON_CreateString("21.122363"));
	cJSON_AddItemToObject(pstData, "Temperature", cJSON_CreateNumber(pstCarState->uiCarTemperature));
    #endif

    pszStatJson = cJSON_Print(pstStatJson);
    u32ValidDataLen = strlen(pszStatJson) + 1;

	SV_COMMON_ScopedLock  lock(sMutex_OfflineCarState);

    bStorageWritable = SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath);
    if (bStorageWritable)
	{
        sprintf(szSaveDir, "%s/%s", szStoragePath, CAR_STATE_PATH);
        s32MaxSize = CAR_STATE_MAX_SDSIZE;
	}
	else
	{
        sprintf(szSaveDir, "%s/%s", szStoragePath, CAR_STATE_PATH);
        s32MaxSize = CAR_STATE_MAX_MEMSIZE;
	}


    if (!COMMON_IsPathExist(szSaveDir))
    {
        sprintf(szCmd, "mkdir -p %s", szSaveDir);
        SAFE_SV_System(szCmd);
    }
    sprintf(szCarStateFile, "%s/offlineCarStateFile", szSaveDir);

    if (0 != strlen(szLastDir) && 0 != strcmp(szLastDir, szSaveDir) && COMMON_IsPathExist(CAR_STATE_FILE_MEM) && 0 != strcmp(szSaveDir, CAR_STATE_PATH_MEM))
    {
        if (COMMON_IsPathExist(szCarStateFile))
        {
            strncpy(szCurTime, cms_offlineInfo_GetTimeString().c_str(), SK_DATE_TIME_STRING_MAX_LENGTH);
            sprintf(szNewName, "%s_%s", CAR_STATE_FILE_MEM, szCurTime);
            rename(CAR_STATE_FILE_MEM, szNewName);
        }
        else
        {
            sprintf(szCmd, "mv %s %s", CAR_STATE_FILE_MEM, szCarStateFile);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
        }

        #if 0
        sprintf(szCmd, "mv %s/* %s", CAR_STATE_PATH_MEM, szSaveDir);
        print_level(SV_INFO, "exec cmd: %s\n", szCmd);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
        #endif
        SAFE_SV_System("sync &");
    }
    strcpy(szLastDir, szSaveDir);

    if (COMMON_IsPathExist(szCarStateFile))
    {
        s32Fd = open(szCarStateFile, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "fopen file failed.\n");
            return SV_FAILURE;
        }

        read(s32Fd, szBuf, 30*1024);
        close(s32Fd);
        s32Fd = -1;

        //print_level(SV_INFO, "get szBuf: \n%s\n", szBuf);
        pstAllData = cJSON_Parse(szBuf);
        if (NULL == pstAllData)
        {
            print_level(SV_ERROR, "get null data in json file!\n");
            remove(szCarStateFile);
            goto first_write;
        }

        pstRegularDataFile = cJSON_GetObjectItemCaseSensitive(pstAllData, "RegularData");
        if (NULL == pstRegularDataFile)
        {
            print_level(SV_ERROR, "no RegularData data in json file!\n");
            return SV_FAILURE;
        }

        pstDataListFile = cJSON_GetObjectItemCaseSensitive(pstRegularDataFile, "Data");
        if (!cJSON_IsArray(pstDataListFile))
        {
            print_level(SV_ERROR, "Failed to get data array.\n");
            cJSON_Delete(pstAllData);
            return SV_FAILURE;
        }

        cJSON_AddItemToArray(pstDataListFile, pstData);
        s32ArraySize = cJSON_GetArraySize(pstDataListFile);

        pszStatJson = cJSON_Print(pstAllData);
        u32ValidDataLen = strlen(pszStatJson) + 1;

        //print_level(SV_INFO, "get json:\n%s\narray size: %d\n", pszStatJson, s32ArraySize);

        s32Fd = open(szCarStateFile, O_RDWR | O_TRUNC, 0666);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "fopen file failed.\n");
            return SV_FAILURE;
        }

        write(s32Fd, pszStatJson, u32ValidDataLen);
        close(s32Fd);
        s32Fd = -1;

        if (s32ArraySize >= CAR_STATE_MAX_COUNT)
        {
            strncpy(szCurTime, cms_offlineInfo_GetTimeString().c_str(), SK_DATE_TIME_STRING_MAX_LENGTH);
            sprintf(szNewName, "%s_%s", szCarStateFile, szCurTime);
            print_level(SV_INFO, "old name: %s, new name: %s\n", szCarStateFile, szNewName);
            rename(szCarStateFile, szNewName);
            bNeedCheckSize = SV_TRUE;
        }
    }
    else
    {
first_write:
        s32Fd = open(szCarStateFile, O_RDWR | O_CREAT | O_TRUNC, 0666);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file %s failed.\n", szCarStateFile);
            return SV_FAILURE;
        }

        write(s32Fd, pszStatJson, u32ValidDataLen);
        close(s32Fd);
        s32Fd = -1;
    }

    free(pszStatJson);
    if (NULL != pstAllData)
        cJSON_Delete(pstAllData);
    else
        cJSON_Delete(pstStatJson);

    if (bWorking)
    {
        print_level(SV_INFO, "another thread is working to find file, skip to this.\n");
        goto exit;
    }

    bNeedCheckSize = SV_TRUE;
    bWorking = SV_TRUE;
	if (bNeedCheckSize)
	{
        while (1)
        {
            s32Ret = cms_offline_GetCarStateFileSize(szSaveDir, &s32UsedSize);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "getAlarmInfoFileSize failed.\n");
                break;
            }

            //print_level(SV_INFO, "used size: %d, max size: %d\n", s32UsedSize, s32MaxSize);
            if (s32UsedSize > s32MaxSize)
            {
                print_level(SV_INFO, "OfflineCarStateFile dir: %s is full, remove old file now! usedSize: %d\n", szSaveDir, s32UsedSize);
			    cms_offline_removeOldCarStateFile(szSaveDir);
            }
            else
            {
                break;
            }
        }
	}
    bWorking = SV_FALSE;

exit:
    return SV_SUCCESS;

}

sint32 SV_DVR_RenameOfflineCarStateFile()
{

    SV_COMMON_ScopedLock  lock(sMutex_OfflineCarState);
    char szCurTime[SK_DATE_TIME_STRING_MAX_LENGTH] = {0};
	char szSaveDir[STORAGE_FULLPATH_LEN] = {0};
    char szNewName[STORAGE_FULLPATH_LEN] = {0};
    char szStoragePath[32] = {0};

	snprintf(szSaveDir, STORAGE_FULLPATH_LEN, "%s", CAR_STATE_FILE_MEM);
	if (COMMON_IsPathExist(szSaveDir))
	{
        strncpy(szCurTime, cms_offlineInfo_GetTimeString().c_str(), SK_DATE_TIME_STRING_MAX_LENGTH);
        sprintf(szNewName, "%s_%s", szSaveDir, szCurTime);
        rename(szSaveDir, szNewName);
	}

	if (SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath))
	{
        snprintf(szSaveDir, STORAGE_FULLPATH_LEN, "%s/%s/offlineCarStateFile", szStoragePath, CAR_STATE_PATH);
        if (COMMON_IsPathExist(szSaveDir))
        {
            strncpy(szCurTime, cms_offlineInfo_GetTimeString().c_str(), SK_DATE_TIME_STRING_MAX_LENGTH);
            sprintf(szNewName, "%s_%s", szSaveDir, szCurTime);
            rename(szSaveDir, szNewName);
        }
	}


    return SV_SUCCESS;
}

sint32 SV_DVR_RenameAlarmInfo(const char *pDirPath)
{
    sint32 s32Ret = SV_SUCCESS, i;
    DIR *pDir = NULL;
    struct dirent* file = NULL;

	char szCmd[256] = {0};
    char szPath[STORAGE_FULLPATH_LEN] = {0};
	char szDestName[256] = {0};

    /* 目录不存在或空目录时返回 */
	if (!COMMON_IsPathExist(pDirPath))
	{
		return SV_SUCCESS;
	}

	if (SV_DVR_DeletEmptyDirRound(pDirPath) == SV_SUCCESS)
	{
		return SV_FAILURE;
	}

	if (NULL == strstr(pDirPath, ALARMINFO_PATH_MEM) && !SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(NULL))
	{
		print_level(SV_WARN, "pDirPath: %s but is not writable!\n", pDirPath);
		return SV_FAILURE;
	}

    pDir = opendir(pDirPath);
    if (NULL == pDir)
	{
		print_level(SV_DEBUG,"Open dir:%s error!\n", pDirPath);
		return SV_FAILURE;
	}
	//print_level(SV_DEBUG,"Open dir:%s successfully!\n", pDirPath);

	while ((file = readdir(pDir)) != NULL)
	{
		if (strcmp(file->d_name,".") == 0 || strcmp(file->d_name,"..") == 0)
		{
			continue;
		}

		if (file->d_type & DT_DIR)
		{
			snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s/", pDirPath, file->d_name);
			s32Ret = SV_DVR_RenameAlarmInfo(szPath);
			if (SV_SUCCESS != s32Ret)
			{
                print_level(SV_DEBUG,"SV_DVR_RenameAlarmInfo failed!\n");
			}
		}
		else
		{
            if (NULL == strstr(file->d_name, "_tmp"))
            {
                continue;
            }

            snprintf(szPath, STORAGE_FULLPATH_LEN, "%s%s", pDirPath, file->d_name);
            strcpy(szDestName, szPath);
            char *pszTmp = strstr(szDestName, "_tmp");
            if (NULL != pszTmp)
            {
                *pszTmp = '\0';
            }

            sprintf(szCmd, "mv %s %s", szPath, szDestName);
            if (SV_SUCCESS != SAFE_System(szCmd, MEDIUM_WAIT_TIME))
            {
                print_level(SV_DEBUG,"SAFE_System: %s failed!\n", szCmd);
            }
		}
	}
	closedir(pDir);

	return s32Ret;
}

sint32 SV_DVR_SortLastAlarmFile()
{
    sint32 s32Ret = 0;
    char szStoragePath[32] = {0};
	char szSaveDir[128] = {0}, szPath[STORAGE_FULLPATH_LEN];
	static bool bSortTmp = false;
	static bool bSortSd = false;

    if (bSortTmp)
    {
        strncpy(szSaveDir, OFFLINE_ALARM_DIR, 128);
    	snprintf(szPath, STORAGE_FULLPATH_LEN, "%s/%s", ALARMINFO_PATH_MEM, szSaveDir);
    	s32Ret = SV_DVR_RenameAlarmInfo(szPath);
    	if (SV_SUCCESS != s32Ret)
    	{
            print_level(SV_WARN, "SV_DVR_SortLastAlarmFile szPath: %s failed\n", szPath);
    	}
    	bSortTmp = true;
    }

	if (!bSortSd && SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath))
	{
        strncpy(szSaveDir, OFFLINE_ALARM_DIR, 128);
		snprintf(szPath, STORAGE_FULLPATH_LEN, "%s/%s", szStoragePath, szSaveDir);
        s32Ret = SV_DVR_RenameAlarmInfo(szPath);
    	if (SV_SUCCESS != s32Ret)
    	{
            print_level(SV_WARN, "SV_DVR_SortLastAlarmFile szPath: %s failed\n", szPath);
    	}
    	bSortSd = true;
	}

	return SV_SUCCESS;
}


void *CMS_Server_OfflineAlarm_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    OFFLINE_ALARM_INFO *pstOfflineAlarm = NULL;
    std::list<OFFLINE_ALARM_INFO>::iterator listOfflineAlarm_itor;

    int alarmType;
    struct tm tmNowTime;
    struct tm tmUtcTime;
    static uint32 u32AlarmSeq = 0;
    OfflineAlarmInfo_st stAlarmInfo;
	char szInfoPath[64];
    char szAlarmId[32] = {0};
	char szBBInfoPath[64];
    char szAlarmTime[32] = {0};
    char szSavrDir[STORAGE_FULLPATH_LEN] = {0};
    struct timeval tvNow;
    struct timezone tz;
    static SV_BOOL bWorking = SV_FALSE;
    sint32 s32UsedSize = 0;
    char szStoragePath[32] = {0};
    bool bCmsAlarmClearOver = false;
    bool bBBAlarmClearOver = false;
    uint32 u32MaxSize = ALARMINFO_MAX_SDSIZE;

    pthread_mutex_init(&m_mutexOfflineAlarm, NULL);

    while (bOfflineRunning)
    {
        SV_DVR_SortLastAlarmFile();

        while (m_listOfflineAlarm.empty())
        {
            sleep_ms(100);
        }

        while (!m_listOfflineAlarm.empty())
        {
            //print_level(SV_INFO, "++m_listOfflineAlarm size: %d\n", m_listOfflineAlarm.size());
            pthread_mutex_lock(&m_mutexOfflineAlarm);
            listOfflineAlarm_itor = m_listOfflineAlarm.begin();
            pthread_mutex_unlock(&m_mutexOfflineAlarm);
            pstOfflineAlarm = &(*listOfflineAlarm_itor);

            gettimeofday(&tvNow, &tz);
            if (0 != pstOfflineAlarm->s32Event)
            {
                tvNow.tv_sec = (time_t)pstOfflineAlarm->stAlarmEvent.s64TimeStamp;     // 报警录像取上层传下来的预录时间点为起始时间
            }
            gmtime_r((time_t *)&tvNow.tv_sec, &tmUtcTime);  // 平台那边要求统一上传时间为UTC时间

            tvNow.tv_sec += (tz.tz_minuteswest * 60);
            gmtime_r((time_t *)&tvNow.tv_sec, &tmNowTime);  // 但AlarmId要和录像名的时间一致，所以也要使用本地时间

            snprintf(szAlarmId, 32, "%04d%02d%02d%02d%02d%02d", (tmNowTime.tm_year+1900), (tmNowTime.tm_mon+1),
        		tmNowTime.tm_mday, tmNowTime.tm_hour, tmNowTime.tm_min, tmNowTime.tm_sec);

        	snprintf(szAlarmTime, 32, "%04d-%02d-%02d %02d:%02d:%02d", (tmUtcTime.tm_year+1900), (tmUtcTime.tm_mon+1),
        		tmUtcTime.tm_mday, tmUtcTime.tm_hour, tmUtcTime.tm_min, tmUtcTime.tm_sec);

        	snprintf(szSavrDir, STORAGE_FULLPATH_LEN, "%04d%02d%02d/%02d/%02d/", (tmNowTime.tm_year+1900), (tmNowTime.tm_mon+1),
        		tmNowTime.tm_mday, tmNowTime.tm_hour, tmNowTime.tm_min);
        	print_level(SV_INFO,"createofflinealarm ID: %s, szAlarmTime: %s, szSavrDir: %s\n", szAlarmId, szAlarmTime, szSavrDir);

            GET_ALARMTYPE(pstOfflineAlarm->s32Event, alarmType);
            stAlarmInfo.u8Type = alarmType;
            stAlarmInfo.u32AlarmSeq = ++u32AlarmSeq;
            strcpy(stAlarmInfo.szAlarmId, szAlarmId);
            strcpy(stAlarmInfo.szAlarmTime, szAlarmTime);
            stAlarmInfo.stGpsInfo = pstOfflineAlarm->stAlarmEvent.stGpsInfo;

            s32Ret = SV_DVR_createOfflineAlarmInfo(szSavrDir, &stAlarmInfo);//用于cms服务器的报警信息文件
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR,"SV_DVR_createOfflineAlarmInfo failed.\n");
                return SV_FAILURE;
            }

			s32Ret = SV_DVR_createOfflineBBAlarmInfo(szSavrDir, &stAlarmInfo);//用于部标服务器的报警信息文件
			if(s32Ret != SV_SUCCESS)
			{
				print_level(SV_ERROR,"SV_DVR_createOfflineAlarmInfo failed.\n");
				return SV_FAILURE;
			}

            pthread_mutex_lock(&m_mutexOfflineAlarm);
            m_listOfflineAlarm.erase(listOfflineAlarm_itor);
            pthread_mutex_unlock(&m_mutexOfflineAlarm);
            //print_level(SV_INFO, "--m_listOfflineAlarm size: %d\n", m_listOfflineAlarm.size());

			if (0 != u32AlarmSeq%10)
			{
                continue;
			}

        	if (SV_NETWORK_DVRINFO::getInstance()->getStorageStatus(szStoragePath))
        	{
                u32MaxSize = ALARMINFO_MAX_SDSIZE;
        	}
        	else
        	{
                u32MaxSize = ALARMINFO_MAX_MEMSIZE;
        	}
        	sprintf(szInfoPath, "%s/offlineAlarm", szStoragePath);
			sprintf(szBBInfoPath, "%s/offlineBBAlarm", szStoragePath);
            bCmsAlarmClearOver = false;
            bBBAlarmClearOver = false;
            while (bOfflineRunning)
            {
                s32Ret = getAlarmInfoFileSize(szInfoPath, &s32UsedSize);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "getAlarmInfoFileSize failed.\n");
                    bCmsAlarmClearOver = true;
                }

                //print_level(SV_INFO, "used size: %d, max size: %d\n", s32UsedSize, ALARMINFO_MAX_SDSIZE);
                if (s32UsedSize > u32MaxSize)
                {
                    print_level(SV_INFO, "sdcard offlineInfo dir is full, remove old file now! usedSize: %d\n", s32UsedSize);
    			    removeOldAlarmInfoFile(szInfoPath);
                }
                else
                {
                    bCmsAlarmClearOver = true;
                }


                s32Ret = getAlarmInfoFileSize(szBBInfoPath, &s32UsedSize);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "getAlarmInfoFileSize failed.\n");
                    bBBAlarmClearOver = true;
                }

                //print_level(SV_INFO, "used size: %d, max size: %d\n", s32UsedSize, ALARMINFO_MAX_SDSIZE);
                if (s32UsedSize > u32MaxSize)
                {
                    print_level(SV_INFO, "sdcard offlineInfo dir is full, remove old file now! usedSize: %d\n", s32UsedSize);
    			    removeOldAlarmInfoFile(szBBInfoPath);
                }
                else
                {
                    bBBAlarmClearOver = true;
                }

                if (bCmsAlarmClearOver && bBBAlarmClearOver)
                {
                    break;
                }
            }
        }
    }

    pthread_mutex_destroy(&m_mutexOfflineAlarm);

    return NULL;
}

void CMSOffline_StartBody()
{
    pthread_t pid;
    pthread_attr_t attr;
	pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);

    if (!bOfflineRunning)
    {
        bOfflineRunning = true;
        if (pthread_create(&pid, &attr, CMS_Server_OfflineAlarm_Body, NULL) != 0)
        {
        	printf("create thread error\n");
            return;
        }
    }

    pthread_attr_destroy(&attr);
    return;
}




