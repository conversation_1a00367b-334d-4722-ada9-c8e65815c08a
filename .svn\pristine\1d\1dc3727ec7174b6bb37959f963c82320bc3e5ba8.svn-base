Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(版本 3942)
+++ src/alg/pd/pd.cpp	(工作副本)
@@ -94,8 +94,8 @@
 #define PD_MODEL_RGB_SSR    "/root/model/RGB_SSR.rknn"              /* 201338 客户检测限速标志*/
 #define PD_MODEL_TRAFFIC    "/root/model/TRAFFIC.rknn"              /* 交通灯模型*/
 #define PD_MODEL_RGB_PC_202406 "/root/model/RGB_PC_202406.rknn"     /* 202406 客户模型 */
+#define PD_MODEL_RGB_PC_SPECIFIC "/root/model/RGB_PC_specific_version.rknn"  /* 可见光检人和车模型 */
 
-
 #if !defined(BOARD_ADA32V3)
 #define PD_IMAGE_WIDTH      608                     /* 算法图像帧宽度 */
 #define PD_IMAGE_HEIGHT     352                     /* 算法图像帧高度 */
@@ -4781,7 +4781,7 @@
         switch (apstPdsParam[i]->enPdsModel)
         {
             case E_PDS_P:
-                if (BOARD_IsCustomer(BOARD_C_ADA32V2_200889) || BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
+                //if (BOARD_IsCustomer(BOARD_C_ADA32V2_200889) || BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
                 {
                     goto toPcModel;
                 }
@@ -5329,7 +5329,7 @@
             return SV_FAILURE;
             break;
     }
-
+	modefilelist[0] = PD_MODEL_RGB_P;
     return SV_SUCCESS;
 }
 
