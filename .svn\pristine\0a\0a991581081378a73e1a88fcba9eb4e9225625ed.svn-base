/*
	qbytearray_hex.cpp

	See qbytearray_hex.h for documentation.

	Compile this file and link it with your code.

gSOAP XML Web services tools
Copyright (C) 2000-2016, <PERSON>, Genivia Inc., All Rights Reserved.
This part of the software is released under ONE of the following licenses:
GPL, the gSOAP public license, OR Genivia's license for commercial use.
--------------------------------------------------------------------------------
gSOAP public license.

The contents of this file are subject to the gSOAP Public License Version 1.3
(the "License"); you may not use this file except in compliance with the
License. You may obtain a copy of the License at
http://www.cs.fsu.edu/~engelen/soaplicense.html
Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License
for the specific language governing rights and limitations under the License.

The Initial Developer of the Original Code is <PERSON>.
Copyright (C) 2000-2016, <PERSON>, Genivia, Inc., All Rights Reserved.
--------------------------------------------------------------------------------
GPL license.

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free Software
Foundation; either version 2 of the License, or (at your option) any later
version.

This program is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A
PARTICULAR PURPOSE. See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc., 59 Temple
Place, Suite 330, Boston, MA 02111-1307 USA

Author contact information:
<EMAIL> / <EMAIL>

This program is released under the GPL with the additional exemption that
compiling, linking, and/or using OpenSSL is allowed.
--------------------------------------------------------------------------------
A commercial use license is available from Genivia, Inc., <EMAIL>
--------------------------------------------------------------------------------
*/

/* When using soapcpp2 option -q<name> or -p<name>, change "soapH.h" in the line below. */
/* soapH.h generated by soapcpp2 from .h file containing #import "qbytearray_hex.h" */

#include "soapH.h"

static void * instantiate_xsd__hexBinary(struct soap*, int, const char*, const char*, size_t*);

static int delete_xsd__hexBinary(struct soap_clist*);

static void copy_xsd__hexBinary(struct soap*, int, int, void*, size_t, const void*, void**);

void soap_serialize_xsd__hexBinary(struct soap *soap, QByteArray const *a)
{
  (void)soap; (void)a; /* appease -Wall -Werror */
}

void soap_default_xsd__hexBinary(struct soap *soap, QByteArray *a)
{
  (void)soap; /* appease -Wall -Werror */
  *a = QByteArray();
}

int soap_out_xsd__hexBinary(struct soap *soap, char const *tag, int id, QByteArray const *a, char const *type)
{
  if (soap_element_begin_out(soap, tag, soap_embedded_id(soap, id, a, SOAP_TYPE_xsd__hexBinary), type)
   || soap_string_out(soap, soap_xsd__hexBinary2s(soap, *a), 0))
    return soap->error;
  return soap_element_end_out(soap, tag);
}

QByteArray *soap_in_xsd__hexBinary(struct soap *soap, char const *tag, QByteArray *a, char const *type)
{
  if (soap_element_begin_in(soap, tag, 0, NULL))
    return NULL;
  if (*soap->type
   && soap_match_att(soap, soap->type, type)
   && soap_match_att(soap, soap->type, ":hexBinary"))
  {
    soap->error = SOAP_TYPE;
    soap_revert(soap);
    return NULL;
  }
  a = (QByteArray*)soap_id_enter(soap, soap->id, a, SOAP_TYPE_xsd__hexBinary, sizeof(QByteArray), NULL, NULL, instantiate_xsd__hexBinary, NULL);
  if (*soap->href)
  {
    a = (QByteArray*)soap_id_forward(soap, soap->href, a, 0, SOAP_TYPE_xsd__hexBinary, 0, sizeof(QByteArray), 0, copy_xsd__hexBinary, NULL);
  }
  else if (a)
  {
    if (soap_s2xsd__hexBinary(soap, soap_string_in(soap, 0, -1, -1, NULL), a))
      return NULL;
  }
  if (soap->body && soap_element_end_in(soap, tag))
    return NULL;
  return a;
}

const char * soap_xsd__hexBinary2s(struct soap *soap, QByteArray a)
{
  QByteArray ba = a.toHex();
  size_t n = qstrlen(ba);
  char *s = (char*)soap_malloc(soap, n + 1);
  soap_strcpy(s, n + 1, ba.constData());
  return s;
}

int soap_s2xsd__hexBinary(struct soap *soap, const char *s, QByteArray *a)
{
  *a = QByteArray::fromHex(s);
  return soap->error;
}

static void * instantiate_xsd__hexBinary(struct soap *soap, int n, const char *type, const char *arrayType, size_t *size)
{
  DBGLOG(TEST, SOAP_MESSAGE(fdebug, "soap_instantiate_xsd__hexBinary(%d, %s, %s)\n", n, type?type:"", arrayType?arrayType:""));
  struct soap_clist *cp = soap_link(soap, NULL, SOAP_TYPE_xsd__hexBinary, n, delete_xsd__hexBinary);
  (void)type; (void)arrayType; /* appease -Wall -Werror */
  if (!cp)
    return NULL;
  if (n < 0)
  {	cp->ptr = SOAP_NEW(QByteArray);
    if (size)
      *size = sizeof(QByteArray);
  }
  else
  {	cp->ptr = SOAP_NEW_ARRAY(QByteArray, n);
    if (size)
      *size = n * sizeof(QByteArray);
  }
  DBGLOG(TEST, SOAP_MESSAGE(fdebug, "Instantiated location=%p\n", cp->ptr));
  if (!cp->ptr)
    soap->error = SOAP_EOM;
  return (QByteArray*)cp->ptr;
}

static int delete_xsd__hexBinary(struct soap_clist *p)
{
  if (p->type == SOAP_TYPE_xsd__hexBinary)
  {
    if (p->size < 0)
      SOAP_DELETE(static_cast<QByteArray*>(p->ptr));
    else
      SOAP_DELETE_ARRAY(static_cast<QByteArray*>(p->ptr));
    return SOAP_OK;
  }
  return SOAP_ERR;
}

static void copy_xsd__hexBinary(struct soap *soap, int st, int tt, void *p, size_t index, const void *q, void **x)
{
  (void)soap; (void)st; (void)tt; (void)index; (void)x; /* appease -Wall -Werror */
  DBGLOG(TEST, SOAP_MESSAGE(fdebug, "Copying QByteArray %p -> %p\n", q, p));
  *(QByteArray*)p = *(QByteArray*)q;
}
