/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件功能描述: 查看共享媒体队列信息

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <unistd.h>
#include <signal.h>
#include <ctype.h>

#include "common.h"
#include "print.h"
#include "sharefifo.h"

static void exit_handle(int signalnum)
{
    printf("catch signalnum %d!\n", signalnum);
    exit(-1);
}

void print_usage()
{
    printf("\r\nfifodump <filepath> <streamType> <duration> <outputfile>\n"
           "filepath:   /var/mainStream\n"
           "streamType: 0-video, 1-audio, 2-video+audio\n"
           "duration:   second\n");
}

int ipsys_log_level = SV_DEBUG;

int main(int argc, char** argv)
{
    sint32 s32Ret = 0, i;
    sint32 s32Fd = -1;
    sint32 s32QueId = 0, s32ConsumerId = 0;
    char *pszPathName = NULL, *pszOutput = NULL;
    sint32 s32Type = 0, s32Duration = 0;
    SFIFO_MSHEAD *pstPacket = NULL;
    uint64 u64BeginPts = 0ll;
    SV_BOOL bGotFirstIfrm = SV_FALSE;
    //key_t key;   
    //sint32 s32ShmId = 0;
    //void *pvStartVirAddr = NULL;

	if (SIG_ERR == signal(SIGINT, exit_handle))
    {
        printf("catch signal SIGKILL Error: %d, %s\n", errno, strerror(errno));
    }

    if (argc != 5)
	{
		print_usage();
		return -1;
	}

	pszPathName = argv[1];
	if (0 != access(pszPathName, F_OK))
	{
	    printf("file: %s unexist.\n", pszPathName);
	    return -1;
	}

	s32Type = atoi(argv[2]);
	s32Duration = atoi(argv[3]);
    pszOutput = argv[4];

    s32Ret = SFIFO_ForReadOpen(pszPathName, &s32QueId, &s32ConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed[%#x].\n", pszPathName, s32Ret);
        return -1;
    }

    remove(pszOutput);
    s32Fd = open(pszOutput, O_CREAT | O_RDWR, S_IRWXU | S_IRGRP | S_IROTH);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file:%s failed.\n", pszOutput);
        return -1;
    }

    while (1)
    {
        s32Ret = SFIFO_GetPacket(s32QueId, s32ConsumerId, &pstPacket);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "SFIFO_GetPacket failed[%#x].\n", s32Ret);
            sleep_ms(10);
            continue;
        }

        if (!bGotFirstIfrm)
        {
            if (pstPacket->type != 1)
            {
                SFIFO_ReleasePacket(s32QueId, s32ConsumerId, pstPacket);
                continue;
            }
            else
            {
                u64BeginPts = pstPacket->pts;
                bGotFirstIfrm = SV_TRUE;
            }
        }

        if ((s32Type == 0 && pstPacket->type == 2) || (s32Type == 1 && pstPacket->type != 2))
        {
            SFIFO_ReleasePacket(s32QueId, s32ConsumerId, pstPacket);
            continue;
        }

        print_level(SV_DEBUG, "type:%d, serial:%d, pts:%lld, size:%d\n", pstPacket->type, pstPacket->serial, pstPacket->pts, pstPacket->msdsize);
        write(s32Fd, pstPacket->data, pstPacket->msdsize);
        if ((pstPacket->pts - u64BeginPts) > (s32Duration * 1000000ll))
        {
            SFIFO_ReleasePacket(s32QueId, s32ConsumerId, pstPacket);
            break;
        }
        
        SFIFO_ReleasePacket(s32QueId, s32ConsumerId, pstPacket);
    }
    
    close(s32Fd);
    SFIFO_ForReadClose(s32QueId, s32ConsumerId);
    
    return 0;
}


