#ifndef __CONFIG_FACTORY_H__
#define __CONFIG_FACTORY_H__

#include "board.h"
#include "config.h"
#include "common.h"

extern const char *m_pszDmsWorkSpeed[ALARM_NO_HELMET+1];

#if 1

static SV_BOOL config_bDhcpEnable()
{
    SV_BOOL bDhcpEnable = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200946) || BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT) ||
        BOARD_IsCustomer(BOARD_C_ADA32E1_200946))
    {
        bDhcpEnable = SV_TRUE;
    }

    return bDhcpEnable;
}

static int config_DhcpTimeout()
{
    int dhcpTimeout = 16;
    if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
        dhcpTimeout = 300;
    }
    return dhcpTimeout;
}

static uint32 config_RtpDstPort()
{
    uint32 rtpDstPort = 60000;
    if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
        rtpDstPort = 5181;
    }
    return rtpDstPort;
}

static uint32 config_UdpComPort()
{
    uint32 udpComPort = 17215;
    if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
        udpComPort = 50010;
    }
    return udpComPort;
}

static SV_BOOL config_bNoStreamAtBoot()
{
    SV_BOOL bNoStreamAtBoot = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_201368))
    {
        bNoStreamAtBoot = SV_FALSE;
    }

    return bNoStreamAtBoot;
}


static char (*config_u8AdminPassword())[3][3][128]
{
                                                            // 账户名字-默认密码-当前密码
    static char szUserPassword[3][3][CONFIG_MAX_BUF_SIZE] = {{"admin","",""},{"Intg","Intg","Intg"},{"Install","Install","Install"}};

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        strcpy(szUserPassword[0][1], "Ray7ana.15");//默认密码
		strcpy(szUserPassword[0][2], "Ray7ana.15");//当前使用密码
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_SAFE))
    {
		strcpy(szUserPassword[0][1], "M@X5@F317");
		strcpy(szUserPassword[0][2], "M@X5@F317");
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        // pyl
        strcpy(szUserPassword[0][1], "Ai_Br1GaDe_3000");
		strcpy(szUserPassword[0][2], "Ai_Br1GaDe_3000");

        strcpy(szUserPassword[2][1], "Br1GaDeAI");
		strcpy(szUserPassword[2][2], "Br1GaDeAI");
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        strcpy(szUserPassword[0][1], "PX321%");
		strcpy(szUserPassword[0][2], "PX321%");
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_WE))
    {
        strcpy(szUserPassword[0][1], "Admin123@");
		strcpy(szUserPassword[0][2], "Admin123@");
    }

#if (defined(BOARD_ADA32V2))
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202461))
    {
        strcpy(szUserPassword[0][0], "installer");
        strcpy(szUserPassword[0][1], "installer");
		strcpy(szUserPassword[0][2], "installer");
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        strcpy(szUserPassword[0][1], "BVAdmin123");
		strcpy(szUserPassword[0][2], "BVAdmin123");
    }
#endif

#if (defined(BOARD_ADA32IR))
    else if (BOARD_IsCustomer(BOARD_C_ADA32IR_202461))
    {
        strcpy(szUserPassword[0][0], "installer");
        strcpy(szUserPassword[0][1], "installer");
		strcpy(szUserPassword[0][2], "installer");
    }
#endif

#if (defined(BOARD_HDW845V1))
    else if (BOARD_IsCustomer(BOARD_C_HDW845V1_202461))
    {
        strcpy(szUserPassword[0][0], "installer");
        strcpy(szUserPassword[0][1], "installer");
		strcpy(szUserPassword[0][2], "installer");
    }
#endif

    return &szUserPassword;
}

static int config_bRemoveFishEye()
{
    SV_BOOL bRemoveFishEye = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        bRemoveFishEye = SV_TRUE;
    }

    return bRemoveFishEye;
}

static int config_bImageFlip()
{
    SV_BOOL bFlip = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200889) || BOARD_IsHardware(BOARD_S_ADA32V3_ADA635M) || BOARD_IsHardware(BOARD_S_ADA32V3_ADA656M))
    {
        bFlip = SV_TRUE;
    }
    return bFlip;
}

static int config_bImageMirror()
{
    SV_BOOL bMirror = SV_FALSE;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V3_200001) || BOARD_IsCustomer(BOARD_C_ADA32V2_200001)) && (BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8)))
    {
        bMirror = SV_TRUE;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_201165) || BOARD_IsCustomer(BOARD_C_ADA32V2_WE) || BOARD_IsCustomer(BOARD_C_ADA32E1_200946))
    {
        bMirror = SV_TRUE;
    }
    return bMirror;
}

static int config_bRefScene()
{
    SV_BOOL bRefScene = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE) || BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        bRefScene = SV_TRUE;
    }

    return bRefScene;
}

static SV_ROT_ANGLE_E config_enRotateAngle()
{
    SV_ROT_ANGLE_E enRotateAngle = SV_ROTATION_0;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_AAU) || (BOARD_IsCustomer(BOARD_C_ADA32V2_R151) && BOARD_IsSVersion(BOARD_S_H_3M6)) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        enRotateAngle = SV_ROTATION_90;
    }
    return enRotateAngle;
}

static int config_u32VoFramerate()
{
    uint32 u32VoFramerate = 30;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_ACR) || BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_FTC)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_200889)
        || BOARD_IsCustomer(BOARD_C_DMS31V2_VUE)
		|| BOARD_IsCustomer(BOARD_C_ADA32V2_SPILLARD)
		|| BOARD_IsCustomer(BOARD_C_ADA32V2_VT)
		|| BOARD_IsCustomer(BOARD_C_ADA32V2_VT_A)
		|| BOARD_IsCustomer(BOARD_C_ADA32V3_VT)
		|| BOARD_IsCustomer(BOARD_C_ADA32V2_201851)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_201244)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_202661)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_201623)
        || BOARD_IsCustomer(BOARD_C_DMS31V2_200706)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_202668)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_200001)
        || BOARD_IsCustomer(BOARD_C_ADA32V3_200001)
		)
    {
        u32VoFramerate = 25;
    }

    return u32VoFramerate;
}

/* cvbs制式要和上面的vo帧率保持对应关系，默认NTSC时VO帧率默认要30fps，默认PAL时VO帧率默认要25fps */
static MEDIA_CVBS config_enMediaCvbs(uint32 u32VoFramerate)
{
    MEDIA_CVBS enCvbs = CVBS_NTSC;
    if (u32VoFramerate == 25)
    {
        enCvbs = CVBS_PAL;
    }

    return enCvbs;
}

static int config_bShowCsrIcon()
{
    SV_BOOL bShowIcon = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        bShowIcon = SV_TRUE;
    }

    return bShowIcon;
}

static int config_bShowAlarmIcon()
{
    SV_BOOL bShowIcon = SV_TRUE;
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    bShowIcon = SV_FALSE;
#else
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_LUIS))
    {
        bShowIcon = SV_FALSE;
    }
#endif
    return bShowIcon;
}

static int config_bAudioEnable()
{
    SV_BOOL bAudioEnable = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        bAudioEnable = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        bAudioEnable = SV_TRUE;
    }

    return bAudioEnable;
}

static ENCODE_E config_enMainEncode()
{
    ENCODE_E enMainEncode = ENCODE_H264;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200946) || BOARD_IsCustomer(BOARD_C_ADA32V2_WE))
    {
        enMainEncode = ENCODE_MJPEG;
    }
    return enMainEncode;
}

static MEDIA_RES config_enMainResolution()
{
    MEDIA_RES enMainResolution = RES_1080P;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        enMainResolution = RES_720P;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200946))
    {
        enMainResolution = RES_VGA;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_WE))
    {
        enMainResolution = RES_HVGA;
    }
    else if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA32E1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        enMainResolution = RES_5MP;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        enMainResolution = RES_D1;
    }

    return enMainResolution;
}

static uint32 config_u32MainFramerate()
{
    uint32 u32MainFramerate = 25;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL) || BOARD_IsCustomer(BOARD_C_ADA32E1_200946))
    {
        u32MainFramerate = 30;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        u32MainFramerate = 5;
    }

    return u32MainFramerate;
}

static int config_enMainBitrate()
{
    int enMainBitrate = 4096;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200946))
    {
        enMainBitrate = 8192;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        enMainBitrate = 3072;
    }
    else if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA32E1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        enMainBitrate = 8192;
    }

    return enMainBitrate;
}

static RC_MODE_E config_enMainRcMode()
{
    RC_MODE_E enMainRcMode = RC_MODE_CBR;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT) || BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159() || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        enMainRcMode = RC_MODE_ABR;
    }

    return enMainRcMode;
}

static uint32 config_u32MainIfrmInterval()
{
    uint32 u32MainIfrmInterval = 25;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL) || BOARD_IsCustomer(BOARD_C_ADA32E1_200946))
    {
        u32MainIfrmInterval = 30;
    }
    else if (BOARD_ADA32E1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        u32MainIfrmInterval = 30;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        u32MainIfrmInterval = 5;
    }

    return u32MainIfrmInterval;
}

static MEDIA_RES config_enSubResolution()
{
    MEDIA_RES enResolution = RES_D1;
    return enResolution;
}

static uint32 config_u32SubBitrate()
{
    uint32 u32Bitrate = 1024;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32C4))
    u32Bitrate = 256;
#endif

    return u32Bitrate;
}

static MEDIA_RES config_enJpegResolution()
{
    MEDIA_RES enJpegResolution = RES_1080P;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        enJpegResolution = RES_720P;
    }

    return enJpegResolution;
}

static MEDIA_RES config_enVoResolution()
{
    MEDIA_RES enVoResolution = RES_1080P;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE)   ||
        BOARD_IsCustomer(BOARD_C_ADA32V2_200889)
    )
    {
        enVoResolution = RES_720P;
    }
    return enVoResolution;
}

/* ROI GUI 叠加设置 */
static int config_bShowGuiMask()
{
    sint32 bShowGuiMask = 0b1100;
#if (defined(BOARD_ADA32IR))
    bShowGuiMask = 0b1101;
#endif

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined (BOARD_ADA32E1))
    bShowGuiMask = 0b1101;
#elif (defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1))
    bShowGuiMask = 0b1111;
#elif  (defined(BOARD_ADA32N1))
    bShowGuiMask = 0b0111;
#elif  (defined(BOARD_ADA32C4))
	bShowGuiMask = 0b0011;
#endif

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_WXKY) || BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        bShowGuiMask = 0b1111;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        bShowGuiMask = 0b0000;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        bShowGuiMask = 0b1000;
    }


    return bShowGuiMask;
}

static SV_BOOL config_bShowTime()
{
    SV_BOOL bShowTime = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018) || BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        bShowTime = SV_TRUE;
    }

    return bShowTime;
}

static TIME_FMT config_enTimeFormat()
{
    TIME_FMT enTimeFormat = TIME_FMT_YYYYMMDD;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        enTimeFormat = TIME_FMT_DDMMYYYY;
    }

    return enTimeFormat;
}


/* AlarmOut 持续时间*/
static int config_s32PdAlarmOutInterval()
{
    sint32 s32PdAlarmOutInterval = -1;  /* auto模式:三线报警触发延时为1秒,单线报警触发延时为2秒 */
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_100346) || BOARD_IsCustomer(BOARD_C_ADA32V2_202883))
    {
        s32PdAlarmOutInterval = 70;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_200001))
    {
        s32PdAlarmOutInterval = 3100;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        s32PdAlarmOutInterval = 200;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        s32PdAlarmOutInterval = 2000;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_202668))
    {
        s32PdAlarmOutInterval = 50;
    }

    return s32PdAlarmOutInterval;
}


/* 行人算法检测目标OSD叠加字体大小[0~3](0:不叠加, 1~3叠加字体大小1x~3x) */
static int config_s32PdOsdFontSize()
{
    sint32 s32PdOsdFontSize = 1;

    if(BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        s32PdOsdFontSize = 2;
    }

    return s32PdOsdFontSize;
}


static int config_s32OpticalFlowFrmInt()
{
    sint32 s32OpticalFlowFrmInt = 1;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_R151) && BOARD_IsSVersion(BOARD_S_H_3M6)) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        s32OpticalFlowFrmInt = 2;
    }
    return s32OpticalFlowFrmInt;
}

static int config_s32OvertakingDetFrmCnt()
{
    sint32 s32OvertakingDetFrmCnt = 3;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_R151) && BOARD_IsSVersion(BOARD_S_H_3M6)) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        s32OvertakingDetFrmCnt = 5;
    }
    return s32OvertakingDetFrmCnt;
}

static float config_fOvertakeRefRate()
{
    float fOvertakeRefRate = 0.6;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_R151) && BOARD_IsSVersion(BOARD_S_H_3M6)) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        fOvertakeRefRate = 0.8;
    }
    return fOvertakeRefRate;
}


/* ROI GUI 绘制样式 */
static int config_enRoiGui()
{
    sint32 enRoiGui = CFG_PDROI_GUI_FILL;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_VT) || BOARD_IsCustomer(BOARD_C_ADA32V2_VT_A) || BOARD_IsCustomer(BOARD_C_ADA32V3_VT) || BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159() \
        || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        enRoiGui = CFG_PDROI_GUI_LINE;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_EXHIBITION_A) || BOARD_IsCustomer(BOARD_C_ADA32V2_EXHIBITION_B) || BOARD_IsCustomer(BOARD_C_ADA47V1_TDCZ))
    {
        enRoiGui = CFG_PDROI_GUI_HIDE;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_EXHIBITION_C))
    {
        enRoiGui = CFG_PDROI_GUI_LINE;
    }
    else if((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
        && (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8)))
    {
        enRoiGui = CFG_PDROI_GUI_HIDE;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_201165) || BOARD_IsCustomer(BOARD_C_ADA32V2_202668))
    {
        enRoiGui = CFG_PDROI_GUI_LINE;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        enRoiGui = CFG_PDROI_GUI_HIDE;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        enRoiGui = CFG_PDROI_GUI_HIDE;
    }
#if (defined(BOARD_ADA32IR))
    else if(BOARD_IsCustomer(BOARD_C_ADA32IR_100393))
    {
        enRoiGui = CFG_PDROI_GUI_HIDE;
    }
    else if (BOARD_IsNotCustomer(BOARD_C_ADA32IR_100393))
    {
        enRoiGui = CFG_PDROI_GUI_LINE;
    }
#endif

#if (defined(BOARD_ADA47V1))
    if (BOARD_GetVersion() != BOARD_ADA47V1_V3)
    {
        enRoiGui = CFG_PDROI_GUI_HIDE;
    }
#elif (defined(BOARD_ADA46V1))
    enRoiGui = CFG_PDROI_GUI_LINE;
#endif

#if (defined(BOARD_HDW845V1))
    enRoiGui = CFG_PDROI_GUI_LINE;
#endif

    return enRoiGui;
}

/* ROI GUI 绘制类型 */
static int config_enRoiStyle()
{
    sint32 enRoiStyle = CFG_PDROI_BOTTOM;

    if(BOARD_IsSVersion(BOARD_S_V_6M) || BOARD_IsSVersion(BOARD_S_V_2M3) ||
       BOARD_IsSVersion(BOARD_S_V_1M45) || BOARD_IsSVersion(BOARD_S_V_1M99)) /* 竖直版本 */
    {
        enRoiStyle = CFG_PDROI_LEFT;
    }

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_EXHIBITION_C))
    {
        enRoiStyle = CFG_PDROI_SEMICIRCLE;
    }

    if (BOARD_IsCustomer(BOARD_C_ADA32V3_EXHIBITION))
    {
        enRoiStyle = CFG_PDROI_SEMICIRCLE;
    }

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159() || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        enRoiStyle = CFG_PDROI_DRAWBOARD;
    }

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        enRoiStyle = CFG_PDROI_DRAWBOARD;
    }

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201724))
    {
        enRoiStyle = CFG_PDROI_SEMICIRCLE;
    }

#if (defined(BOARD_ADA32IR))
    enRoiStyle = CFG_PDROI_SEMICIRCLE;
#elif (defined(BOARD_ADA46V1))
    enRoiStyle = CFG_PDROI_DRAWBOARD;
#endif

    return enRoiStyle;
}

/* 高级设置 */
static int config_s32PdSensitivity()
{
    sint32 s32PdSensitivity = 1;    /*0:低灵敏度,1:中灵敏度,2:高灵敏度*/
    if((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001)))
    {
        s32PdSensitivity = 2;
    }

    if(BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        s32PdSensitivity = 0;
    }

    if(BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        s32PdSensitivity = 0;
    }

    return s32PdSensitivity;
}

/* 检测区域镂空 使能开关 */
static int config_enPdHollow()
{
    sint32 enPdHollow = SV_FALSE;

    return enPdHollow;
}

static SV_POINT2_S *config_PdHollowPoints()
{
    static SV_POINT2_S stPdHollowPoints[10];

    stPdHollowPoints[0].dX = 0.2;
    stPdHollowPoints[0].dY = 0.6;

    stPdHollowPoints[1].dX = 0.2;
    stPdHollowPoints[1].dY = 0.7;

    stPdHollowPoints[2].dX = 0.2;
    stPdHollowPoints[2].dY = 0.8;

    stPdHollowPoints[3].dX = 0.4;
    stPdHollowPoints[3].dY = 0.8;

    stPdHollowPoints[4].dX = 0.6;
    stPdHollowPoints[4].dY = 0.8;

    stPdHollowPoints[5].dX = 0.8;
    stPdHollowPoints[5].dY = 0.8;

    stPdHollowPoints[6].dX = 0.8;
    stPdHollowPoints[6].dY = 0.7;

    stPdHollowPoints[7].dX = 0.8;
    stPdHollowPoints[7].dY = 0.6;

    stPdHollowPoints[8].dX = 0.6;
    stPdHollowPoints[8].dY = 0.6;

    stPdHollowPoints[9].dX = 0.4;
    stPdHollowPoints[9].dY = 0.6;

    return stPdHollowPoints;
}


/* ROI 红色区域报警间隔 */
static int config_s32PdRedInterval()
{
    sint32 s32PdRedInterval = 0;

    return s32PdRedInterval;
}

/* ROI 黄色区域报警间隔 */
static int config_s32PdYellowInterval()
{
    sint32 s32PdYellowInterval = 0;

    if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        s32PdYellowInterval = 3;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202668))
    {
        s32PdYellowInterval = 2;
    }

    return s32PdYellowInterval;
}

/* ROI 绿色区域报警间隔 */
static int config_s32PdGreenInterval()
{
    sint32 s32PdGreenInterval = 0;

    if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        s32PdGreenInterval = -1;
    }

    return s32PdGreenInterval;
}

/* ROI 红色区域使能 */
static SV_BOOL config_bPdRoiRed()
{
    SV_BOOL bPdRoiRed = SV_TRUE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        bPdRoiRed = SV_FALSE;
    }

    return bPdRoiRed;
}

/* ROI 黄色区域使能 */
static SV_BOOL config_bPdRoiYellow()
{
    SV_BOOL bPdRoiYellow = SV_TRUE;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
        && (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8)))
    {
        bPdRoiYellow = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        bPdRoiYellow = SV_FALSE;
    }
	else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        bPdRoiYellow = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201724))
    {
        bPdRoiYellow = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        bPdRoiYellow = SV_FALSE;
    }

    return bPdRoiYellow;
}

/* ROI 绿色区域使能 */
static SV_BOOL config_bPdRoiGreen()
{
    SV_BOOL bPdRoiGreen = SV_TRUE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_LUIS))
    {
        bPdRoiGreen = SV_FALSE;
    }
    else if ((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
        && (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8)))
    {
        bPdRoiGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        bPdRoiGreen = SV_FALSE;
    }
    else if (BOARD_IsADA38_R159())
    {
        bPdRoiGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202668))
    {
        bPdRoiGreen = SV_FALSE;
    }
    else if (BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        bPdRoiGreen = SV_FALSE;
    }
	else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        bPdRoiGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201724))
    {
        bPdRoiGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        bPdRoiGreen = SV_FALSE;
    }

    return bPdRoiGreen;
}

/* AlarmOut 红色使能 */
static SV_BOOL config_bPdAlarmOutRed()
{
    SV_BOOL bPdAlarmOutRed = SV_TRUE;
    return bPdAlarmOutRed;
}

/* AlarmOut 黄色使能 */
static SV_BOOL config_bPdAlarmOutYellow()
{
    SV_BOOL bPdAlarmOutYellow = SV_TRUE;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
        && (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8)))
    {
        bPdAlarmOutYellow = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        bPdAlarmOutYellow = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        bPdAlarmOutYellow = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        bPdAlarmOutYellow = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        bPdAlarmOutYellow = SV_FALSE;
    }

    return bPdAlarmOutYellow;
}

/* AlarmOut 绿色使能 */
static SV_BOOL config_bPdAlarmOutGreen()
{
    SV_BOOL bPdAlarmOutGreen = SV_TRUE;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
            && (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8)))
    {
        bPdAlarmOutGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        bPdAlarmOutGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        bPdAlarmOutGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_100214))
    {
        bPdAlarmOutGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202668))
    {
        bPdAlarmOutGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        bPdAlarmOutGreen = SV_FALSE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        bPdAlarmOutGreen = SV_FALSE;
    }

    return bPdAlarmOutGreen;
}

static sint32 config_RedWireZoneMask()
{
    sint32 s32RedWireZoneMask = 1;

    return s32RedWireZoneMask;
}

static sint32 config_RedWireModelMask()
{
    sint32 s32RedWireModelMask = 3;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        s32RedWireModelMask = 7;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX) || BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        s32RedWireModelMask = 1;
    }

    return s32RedWireModelMask;
}

static sint32 config_YellowWireZoneMask()
{
    sint32 s32YellowWireZoneMask = 2;

    return s32YellowWireZoneMask;
}

static sint32 config_YellowWireModelMask()
{
    sint32 s32YellowWireModelMask = 3;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        s32YellowWireModelMask = 7;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX) || BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        s32YellowWireModelMask = 1;
    }

    return s32YellowWireModelMask;
}

static sint32 config_GreenWireZoneMask()
{
    sint32 s32GreenWireZoneMask = 4;

    return s32GreenWireZoneMask;
}

static sint32 config_GreenWireModelMask()
{
    sint32 s32GreenWireModelMask = 3;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200889))
    {
        s32GreenWireModelMask = 4;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        s32GreenWireModelMask = 7;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX) || BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        s32GreenWireModelMask = 1;
    }

    return s32GreenWireModelMask;
}

/* 报警输入使能 */
static SV_BOOL config_bPdAlarmIn()
{
    SV_BOOL bPdAlarmIn = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201165) || BOARD_IsCustomer(BOARD_C_ADA32V2_200019)
        || BOARD_IsCustomer(BOARD_C_ADA32V2_100214) || BOARD_IsCustomer(BOARD_C_ADA32E1_201989)
        || BOARD_IsHardware(BOARD_S_ADA32V3_ADA635M) || BOARD_IsHardware(BOARD_S_ADA32V3_ADA656M))
    {
        bPdAlarmIn = SV_TRUE;
    }

    return bPdAlarmIn;
}

static SV_BOOL config_bPdCrosshairIcon()
{
    SV_BOOL bPdCrosshairIcon = SV_FALSE;
    if(BOARD_IsCustomer(BOARD_C_ADA32V2_202613) || BOARD_IsCustomer(BOARD_C_ADA32IR_202613))
    {
        bPdCrosshairIcon = SV_TRUE;
    }

    return bPdCrosshairIcon;
}

/* 报警输入模式 */
static TRIGGER_TYPE_S config_enPdAlarmInTrigger()
{
    TRIGGER_TYPE_S enPdAlarmInTrigger = TRIGGER_UP;
    return enPdAlarmInTrigger;
}

/* 显示行人框使能 */
static SV_BOOL config_bPdRectPerson()
{
    SV_BOOL bRectPerson = SV_TRUE;
#if 0
    if(BOARD_IsSVersion(BOARD_S_H_1M45) ||
       BOARD_IsSVersion(BOARD_S_H_1M99))
    {
        bRectPerson = SV_FALSE;
    }
#endif
    return bRectPerson;
}

static SV_BOOL config_bAlarmLight()
{
	SV_BOOL bAlarmLight = SV_TRUE;

	return bAlarmLight;
}
/* ROI 测试模式使能 */
static SV_BOOL config_bPdTestMode()
{
    SV_BOOL bPdTestMode = SV_FALSE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        bPdTestMode = SV_TRUE;
    }

    return bPdTestMode;
}

/* 闪烁红色图标 */
static SV_BOOL config_bFlashIcon()
{
    SV_BOOL bFlashIcon = SV_FALSE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201933))
    {
        bFlashIcon = SV_TRUE;
    }

    return bFlashIcon;
}


/* 摄象头相机角度 */
static CFG_PD_VIEW_DIRECTION_E config_ePdViewDirection()
{
    CFG_PD_VIEW_DIRECTION_E ePdViewDirection = CFG_PD_VIEW_OFF;

    return ePdViewDirection;
}



/* PD遮挡报警使能开关 */
static SV_BOOL config_bshelterEnable()
{

    SV_BOOL bshelterEnable = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_FTC) || BOARD_IsCustomer(BOARD_C_ADA32V2_202063) || BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159() \
        || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR) || BOARD_IsCustomer(BOARD_C_ADA32V2_202668) \
        || BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V2_201933) || BOARD_IsCustomer(BOARD_C_ADA32V2_202319) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        bshelterEnable = SV_TRUE;
    }

    return bshelterEnable;
}

/* PD遮挡报警触发输出使能开关，只用于单根触发线 */
static SV_BOOL config_bshelterAlarmout()
{
    SV_BOOL bshelterAlarmout = SV_FALSE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_202668) || BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        bshelterAlarmout = SV_TRUE;
    }

    return bshelterAlarmout;
}

/* PD遮挡报警音频开关 */
static SV_BOOL config_bshelterAudioEnable()
{
    SV_BOOL bshelterAudioEnable = SV_TRUE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200049))
    {
        bshelterAudioEnable = SV_FALSE;
    }

    return bshelterAudioEnable;
}

/* 单触发线，PWM输出模式开关 */
static SV_BOOL config_bAlarmoutPWMmode()
{
    SV_BOOL bAlarmoutPWMmode = SV_FALSE;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_202883))
    {
        bAlarmoutPWMmode = SV_TRUE;
    }

    return bAlarmoutPWMmode;
}


/* ROI 红色区域敏感度 */
static float *config_astPdRedSensitivityIr()
{
    static float stPdRedSensitivity[3] = {0.46, 0.51, 0.62};

    stPdRedSensitivity[0] = 0.46;
    stPdRedSensitivity[1] = 0.60;
    stPdRedSensitivity[2] = 0.65;


    return stPdRedSensitivity;
}

/* ROI 黄色区域敏感度 */
static float *config_astPdYellowSensitivityIr()
{
    static float stPdYellowSensitivity[3] = {0.46, 0.51, 0.62};

    stPdYellowSensitivity[0] = 0.46;
    stPdYellowSensitivity[1] = 0.60;
    stPdYellowSensitivity[2] = 0.65;

    return stPdYellowSensitivity;
}

/* ROI 绿色区域敏感度 */
static float *config_astPdGreenSensitivityIr()
{
    static float stPdGreenSensitivity[3] = {0.46, 0.51, 0.62};

    stPdGreenSensitivity[0] = 0.46;
    stPdGreenSensitivity[1] = 0.60;
    stPdGreenSensitivity[2] = 0.65;

    return stPdGreenSensitivity;
}


/* ROI 红色区域敏感度 */
static float *config_astPdRedSensitivity()
{
    static float stPdRedSensitivity[3] = {0.46, 0.51, 0.62};
#if 0
    if(BOARD_IsSVersion(BOARD_S_H_1M99) ||
       BOARD_IsSVersion(BOARD_S_H_1M45))
    {
        stPdRedSensitivity[0] = 0.5;
        stPdRedSensitivity[1] = 0.6;
        stPdRedSensitivity[2] = 0.8;
    }
#endif
    return stPdRedSensitivity;
}

/* ROI 黄色区域敏感度 */
static float *config_astPdYellowSensitivity()
{
    static float stPdYellowSensitivity[3] = {0.46, 0.51, 0.62};
#if 0
    if(BOARD_IsSVersion(BOARD_S_H_1M99) ||
       BOARD_IsSVersion(BOARD_S_H_1M45))
    {
        stPdYellowSensitivity[0] = 0.5;
        stPdYellowSensitivity[1] = 0.6;
        stPdYellowSensitivity[2] = 0.7;
    }
#endif
    return stPdYellowSensitivity;
}

/* ROI 绿色区域敏感度 */
static float *config_astPdGreenSensitivity()
{
    static float stPdGreenSensitivity[3] = {0.46, 0.51, 0.62};
#if 0
    if(BOARD_IsSVersion(BOARD_S_H_1M99) ||
       BOARD_IsSVersion(BOARD_S_H_1M45))
    {
        stPdGreenSensitivity[0] = 0.5;
        stPdGreenSensitivity[1] = 0.6;
        stPdGreenSensitivity[2] = 0.7;
    }
#endif
    return stPdGreenSensitivity;
}

static SV_POINT2_S *config_PdRoiGreenPoints(SV_BOOL bR151Regulation)
{
    static SV_POINT2_S stPdRoiGreenPoints[10] = {0};

#if defined(BOARD_ADA46V1)
    if (1)
#else
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151))
#endif
    {
        if (BOARD_IsSVersion(BOARD_S_V_1M78) || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT))
        {
            if (bR151Regulation)
            {
                stPdRoiGreenPoints[0].dX = 0.08;
                stPdRoiGreenPoints[0].dY = 0.39;

                stPdRoiGreenPoints[1].dX = 0.20;
                stPdRoiGreenPoints[1].dY = 0.63;

                stPdRoiGreenPoints[2].dX = 0.40;
                stPdRoiGreenPoints[2].dY = 0.82;

                stPdRoiGreenPoints[3].dX = 0.44;
                stPdRoiGreenPoints[3].dY = 0.66;

                stPdRoiGreenPoints[4].dX = 0.51;
                stPdRoiGreenPoints[4].dY = 0.37;

                stPdRoiGreenPoints[5].dX = 0.57;
                stPdRoiGreenPoints[5].dY = 0.08;

                stPdRoiGreenPoints[6].dX = 0.43;
                stPdRoiGreenPoints[6].dY = 0.08;

                stPdRoiGreenPoints[7].dX = 0.31;
                stPdRoiGreenPoints[7].dY = 0.11;

                stPdRoiGreenPoints[8].dX = 0.21;
                stPdRoiGreenPoints[8].dY = 0.16;

                stPdRoiGreenPoints[9].dX = 0.15;
                stPdRoiGreenPoints[9].dY = 0.26;
            }
            else
            {
                stPdRoiGreenPoints[0].dX = 0.05;
                stPdRoiGreenPoints[0].dY = 0.33;

                stPdRoiGreenPoints[1].dX = 0.08;
                stPdRoiGreenPoints[1].dY = 0.39;

                stPdRoiGreenPoints[2].dX = 0.10;
                stPdRoiGreenPoints[2].dY = 0.35;

                stPdRoiGreenPoints[3].dX = 0.13;
                stPdRoiGreenPoints[3].dY = 0.28;

                stPdRoiGreenPoints[4].dX = 0.16;
                stPdRoiGreenPoints[4].dY = 0.21;

                stPdRoiGreenPoints[5].dX = 0.18;
                stPdRoiGreenPoints[5].dY = 0.17;

                stPdRoiGreenPoints[6].dX = 0.14;
                stPdRoiGreenPoints[6].dY = 0.19;

                stPdRoiGreenPoints[7].dX = 0.11;
                stPdRoiGreenPoints[7].dY = 0.21;

                stPdRoiGreenPoints[8].dX = 0.09;
                stPdRoiGreenPoints[8].dY = 0.25;

                stPdRoiGreenPoints[9].dX = 0.07;
                stPdRoiGreenPoints[9].dY = 0.29;
            }
        }
        else if (BOARD_IsSVersion(BOARD_S_H_3M6) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
        {
            if (bR151Regulation)
            {
                stPdRoiGreenPoints[0].dX = 0.70;
                stPdRoiGreenPoints[0].dY = 0.31;

                stPdRoiGreenPoints[1].dX = 0.64;
                stPdRoiGreenPoints[1].dY = 0.33;

                stPdRoiGreenPoints[2].dX = 0.75;
                stPdRoiGreenPoints[2].dY = 0.33;

                stPdRoiGreenPoints[3].dX = 0.84;
                stPdRoiGreenPoints[3].dY = 0.33;

                stPdRoiGreenPoints[4].dX = 0.92;
                stPdRoiGreenPoints[4].dY = 0.33;

                stPdRoiGreenPoints[5].dX = 0.98;
                stPdRoiGreenPoints[5].dY = 0.33;

                stPdRoiGreenPoints[6].dX = 0.98;
                stPdRoiGreenPoints[6].dY = 0.31;

                stPdRoiGreenPoints[7].dX = 0.92;
                stPdRoiGreenPoints[7].dY = 0.31;

                stPdRoiGreenPoints[8].dX = 0.85;
                stPdRoiGreenPoints[8].dY = 0.31;

                stPdRoiGreenPoints[9].dX = 0.78;
                stPdRoiGreenPoints[9].dY = 0.31;
            }
            else
            {
                stPdRoiGreenPoints[0].dX = 0.79;
                stPdRoiGreenPoints[0].dY = 0.28;

                stPdRoiGreenPoints[1].dX = 0.70;
                stPdRoiGreenPoints[1].dY = 0.31;

                stPdRoiGreenPoints[2].dX = 0.77;
                stPdRoiGreenPoints[2].dY = 0.31;

                stPdRoiGreenPoints[3].dX = 0.84;
                stPdRoiGreenPoints[3].dY = 0.31;

                stPdRoiGreenPoints[4].dX = 0.91;
                stPdRoiGreenPoints[4].dY = 0.31;

                stPdRoiGreenPoints[5].dX = 0.98;
                stPdRoiGreenPoints[5].dY = 0.31;

                stPdRoiGreenPoints[6].dX = 0.98;
                stPdRoiGreenPoints[6].dY = 0.28;

                stPdRoiGreenPoints[7].dX = 0.93;
                stPdRoiGreenPoints[7].dY = 0.28;

                stPdRoiGreenPoints[8].dX = 0.88;
                stPdRoiGreenPoints[8].dY = 0.28;

                stPdRoiGreenPoints[9].dX = 0.84;
                stPdRoiGreenPoints[9].dY = 0.28;
            }
        }
        else
        {
            stPdRoiGreenPoints[0].dX = 0.2;
            stPdRoiGreenPoints[0].dY = 0.2;

            stPdRoiGreenPoints[1].dX = 0.2;
            stPdRoiGreenPoints[1].dY = 0.3;

            stPdRoiGreenPoints[2].dX = 0.2;
            stPdRoiGreenPoints[2].dY = 0.4;

            stPdRoiGreenPoints[3].dX = 0.4;
            stPdRoiGreenPoints[3].dY = 0.4;

            stPdRoiGreenPoints[4].dX = 0.6;
            stPdRoiGreenPoints[4].dY = 0.4;

            stPdRoiGreenPoints[5].dX = 0.8;
            stPdRoiGreenPoints[5].dY = 0.4;

            stPdRoiGreenPoints[6].dX = 0.8;
            stPdRoiGreenPoints[6].dY = 0.3;

            stPdRoiGreenPoints[7].dX = 0.8;
            stPdRoiGreenPoints[7].dY = 0.2;

            stPdRoiGreenPoints[8].dX = 0.6;
            stPdRoiGreenPoints[8].dY = 0.2;

            stPdRoiGreenPoints[9].dX = 0.4;
            stPdRoiGreenPoints[9].dY = 0.2;
        }
    }
    else if (BOARD_IsADA38_R159())
    {
        stPdRoiGreenPoints[0].dX = 0.41;
        stPdRoiGreenPoints[0].dY = 0.07;

        stPdRoiGreenPoints[1].dX = 0.33;
        stPdRoiGreenPoints[1].dY = 0.07;

        stPdRoiGreenPoints[2].dX = 0.31;
        stPdRoiGreenPoints[2].dY = 0.12;

        stPdRoiGreenPoints[3].dX = 0.28;
        stPdRoiGreenPoints[3].dY = 0.19;

        stPdRoiGreenPoints[4].dX = 0.75;
        stPdRoiGreenPoints[4].dY = 0.19;

        stPdRoiGreenPoints[5].dX = 0.72;
        stPdRoiGreenPoints[5].dY = 0.12;

        stPdRoiGreenPoints[6].dX = 0.70;
        stPdRoiGreenPoints[6].dY = 0.07;

        stPdRoiGreenPoints[7].dX = 0.63;
        stPdRoiGreenPoints[7].dY = 0.07;

        stPdRoiGreenPoints[8].dX = 0.55;
        stPdRoiGreenPoints[8].dY = 0.07;

        stPdRoiGreenPoints[9].dX = 0.48;
        stPdRoiGreenPoints[9].dY = 0.07;
    }
    else
    {
        stPdRoiGreenPoints[0].dX = 0.2;
        stPdRoiGreenPoints[0].dY = 0.2;

        stPdRoiGreenPoints[1].dX = 0.2;
        stPdRoiGreenPoints[1].dY = 0.3;

        stPdRoiGreenPoints[2].dX = 0.2;
        stPdRoiGreenPoints[2].dY = 0.4;

        stPdRoiGreenPoints[3].dX = 0.4;
        stPdRoiGreenPoints[3].dY = 0.4;

        stPdRoiGreenPoints[4].dX = 0.6;
        stPdRoiGreenPoints[4].dY = 0.4;

        stPdRoiGreenPoints[5].dX = 0.8;
        stPdRoiGreenPoints[5].dY = 0.4;

        stPdRoiGreenPoints[6].dX = 0.8;
        stPdRoiGreenPoints[6].dY = 0.3;

        stPdRoiGreenPoints[7].dX = 0.8;
        stPdRoiGreenPoints[7].dY = 0.2;

        stPdRoiGreenPoints[8].dX = 0.6;
        stPdRoiGreenPoints[8].dY = 0.2;

        stPdRoiGreenPoints[9].dX = 0.4;
        stPdRoiGreenPoints[9].dY = 0.2;
    }

    return stPdRoiGreenPoints;
}

static SV_POINT2_S *config_PdRoiYellowPoints(SV_BOOL bR151Regulation)
{
    static SV_POINT2_S stPdRoiYellowPoints[10] = {0};

#if defined(BOARD_ADA46V1)
    if (1)
#else
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151))
#endif
    {
        if (BOARD_IsSVersion(BOARD_S_V_1M78) || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT))
        {
            if (bR151Regulation)
            {
                stPdRoiYellowPoints[0].dX = 0.40;
                stPdRoiYellowPoints[0].dY = 0.82;

                stPdRoiYellowPoints[1].dX = 0.62;
                stPdRoiYellowPoints[1].dY = 0.91;

                stPdRoiYellowPoints[2].dX = 0.85;
                stPdRoiYellowPoints[2].dY = 0.90;

                stPdRoiYellowPoints[3].dX = 0.86;
                stPdRoiYellowPoints[3].dY = 0.68;

                stPdRoiYellowPoints[4].dX = 0.87;
                stPdRoiYellowPoints[4].dY = 0.47;

                stPdRoiYellowPoints[5].dX = 0.88;
                stPdRoiYellowPoints[5].dY = 0.20;

                stPdRoiYellowPoints[6].dX = 0.73;
                stPdRoiYellowPoints[6].dY = 0.13;

                stPdRoiYellowPoints[7].dX = 0.57;
                stPdRoiYellowPoints[7].dY = 0.08;

                stPdRoiYellowPoints[8].dX = 0.51;
                stPdRoiYellowPoints[8].dY = 0.37;

                stPdRoiYellowPoints[9].dX = 0.46;
                stPdRoiYellowPoints[9].dY = 0.57;
            }
            else
            {
                stPdRoiYellowPoints[0].dX = 0.10;
                stPdRoiYellowPoints[0].dY = 0.44;

                stPdRoiYellowPoints[1].dX = 0.13;
                stPdRoiYellowPoints[1].dY = 0.50;

                stPdRoiYellowPoints[2].dX = 0.16;
                stPdRoiYellowPoints[2].dY = 0.56;

                stPdRoiYellowPoints[3].dX = 0.20;
                stPdRoiYellowPoints[3].dY = 0.63;

                stPdRoiYellowPoints[4].dX = 0.36;
                stPdRoiYellowPoints[4].dY = 0.10;

                stPdRoiYellowPoints[5].dX = 0.31;
                stPdRoiYellowPoints[5].dY = 0.11;

                stPdRoiYellowPoints[6].dX = 0.26;
                stPdRoiYellowPoints[6].dY = 0.13;

                stPdRoiYellowPoints[7].dX = 0.22;
                stPdRoiYellowPoints[7].dY = 0.15;

                stPdRoiYellowPoints[8].dX = 0.18;
                stPdRoiYellowPoints[8].dY = 0.17;

                stPdRoiYellowPoints[9].dX = 0.08;
                stPdRoiYellowPoints[9].dY = 0.39;
            }
        }
        else if (BOARD_IsSVersion(BOARD_S_H_3M6) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
        {
            if (bR151Regulation)
            {
                stPdRoiYellowPoints[0].dX = 0.52;
                stPdRoiYellowPoints[0].dY = 0.37;

                stPdRoiYellowPoints[1].dX = 0.34;
                stPdRoiYellowPoints[1].dY = 0.43;

                stPdRoiYellowPoints[2].dX = 0.16;
                stPdRoiYellowPoints[2].dY = 0.50;

                stPdRoiYellowPoints[3].dX = 0.59;
                stPdRoiYellowPoints[3].dY = 0.50;

                stPdRoiYellowPoints[4].dX = 0.79;
                stPdRoiYellowPoints[4].dY = 0.50;

                stPdRoiYellowPoints[5].dX = 0.98;
                stPdRoiYellowPoints[5].dY = 0.50;

                stPdRoiYellowPoints[6].dX = 0.98;
                stPdRoiYellowPoints[6].dY = 0.43;

                stPdRoiYellowPoints[7].dX = 0.98;
                stPdRoiYellowPoints[7].dY = 0.33;

                stPdRoiYellowPoints[8].dX = 0.84;
                stPdRoiYellowPoints[8].dY = 0.33;

                stPdRoiYellowPoints[9].dX = 0.64;
                stPdRoiYellowPoints[9].dY = 0.33;
            }
            else
            {
                stPdRoiYellowPoints[0].dX = 0.64;
                stPdRoiYellowPoints[0].dY = 0.33;

                stPdRoiYellowPoints[1].dX = 0.58;
                stPdRoiYellowPoints[1].dY = 0.35;

                stPdRoiYellowPoints[2].dX = 0.47;
                stPdRoiYellowPoints[2].dY = 0.39;

                stPdRoiYellowPoints[3].dX = 0.66;
                stPdRoiYellowPoints[3].dY = 0.39;

                stPdRoiYellowPoints[4].dX = 0.81;
                stPdRoiYellowPoints[4].dY = 0.39;

                stPdRoiYellowPoints[5].dX = 0.98;
                stPdRoiYellowPoints[5].dY = 0.39;

                stPdRoiYellowPoints[6].dX = 0.98;
                stPdRoiYellowPoints[6].dY = 0.35;

                stPdRoiYellowPoints[7].dX = 0.98;
                stPdRoiYellowPoints[7].dY = 0.31;

                stPdRoiYellowPoints[8].dX = 0.84;
                stPdRoiYellowPoints[8].dY = 0.31;

                stPdRoiYellowPoints[9].dX = 0.70;
                stPdRoiYellowPoints[9].dY = 0.31;
            }
        }
        else
        {
            stPdRoiYellowPoints[0].dX = 0.2;
            stPdRoiYellowPoints[0].dY = 0.4;

            stPdRoiYellowPoints[1].dX = 0.2;
            stPdRoiYellowPoints[1].dY = 0.5;

            stPdRoiYellowPoints[2].dX = 0.2;
            stPdRoiYellowPoints[2].dY = 0.6;

            stPdRoiYellowPoints[3].dX = 0.4;
            stPdRoiYellowPoints[3].dY = 0.6;

            stPdRoiYellowPoints[4].dX = 0.6;
            stPdRoiYellowPoints[4].dY = 0.6;

            stPdRoiYellowPoints[5].dX = 0.8;
            stPdRoiYellowPoints[5].dY = 0.6;

            stPdRoiYellowPoints[6].dX = 0.8;
            stPdRoiYellowPoints[6].dY = 0.5;

            stPdRoiYellowPoints[7].dX = 0.8;
            stPdRoiYellowPoints[7].dY = 0.4;

            stPdRoiYellowPoints[8].dX = 0.6;
            stPdRoiYellowPoints[8].dY = 0.4;


            stPdRoiYellowPoints[9].dX = 0.4;
            stPdRoiYellowPoints[9].dY = 0.4;
        }
    }
    else if (BOARD_IsADA38_R159())
    {
        stPdRoiYellowPoints[0].dX = 0.16;
        stPdRoiYellowPoints[0].dY = 0.53;

        stPdRoiYellowPoints[1].dX = 0.02;
        stPdRoiYellowPoints[1].dY = 1.00;

        stPdRoiYellowPoints[2].dX = 0.23;
        stPdRoiYellowPoints[2].dY = 1.00;

        stPdRoiYellowPoints[3].dX = 0.35;
        stPdRoiYellowPoints[3].dY = 0.39;

        stPdRoiYellowPoints[4].dX = 0.68;
        stPdRoiYellowPoints[4].dY = 0.39;

        stPdRoiYellowPoints[5].dX = 0.78;
        stPdRoiYellowPoints[5].dY = 1.00;

        stPdRoiYellowPoints[6].dX = 0.98;
        stPdRoiYellowPoints[6].dY = 1.00;

        stPdRoiYellowPoints[7].dX = 0.86;
        stPdRoiYellowPoints[7].dY = 0.55;

        stPdRoiYellowPoints[8].dX = 0.75;
        stPdRoiYellowPoints[8].dY = 0.19;

        stPdRoiYellowPoints[9].dX = 0.28;
        stPdRoiYellowPoints[9].dY = 0.19;
    }
    else
    {
        stPdRoiYellowPoints[0].dX = 0.2;
        stPdRoiYellowPoints[0].dY = 0.4;

        stPdRoiYellowPoints[1].dX = 0.2;
        stPdRoiYellowPoints[1].dY = 0.5;

        stPdRoiYellowPoints[2].dX = 0.2;
        stPdRoiYellowPoints[2].dY = 0.6;

        stPdRoiYellowPoints[3].dX = 0.4;
        stPdRoiYellowPoints[3].dY = 0.6;

        stPdRoiYellowPoints[4].dX = 0.6;
        stPdRoiYellowPoints[4].dY = 0.6;

        stPdRoiYellowPoints[5].dX = 0.8;
        stPdRoiYellowPoints[5].dY = 0.6;

        stPdRoiYellowPoints[6].dX = 0.8;
        stPdRoiYellowPoints[6].dY = 0.5;

        stPdRoiYellowPoints[7].dX = 0.8;
        stPdRoiYellowPoints[7].dY = 0.4;

        stPdRoiYellowPoints[8].dX = 0.6;
        stPdRoiYellowPoints[8].dY = 0.4;


        stPdRoiYellowPoints[9].dX = 0.4;
        stPdRoiYellowPoints[9].dY = 0.4;
    }

    return stPdRoiYellowPoints;
}

static SV_POINT2_S *config_PdRoiRedPoints(SV_BOOL bR151Regulation)
{
    static SV_POINT2_S stPdRoiRedPoints[10] = {0};

#if defined(BOARD_ADA46V1)
    if (1)
#else
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_R151))
#endif
    {
        if (BOARD_IsSVersion(BOARD_S_V_1M78) || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT))
        {
            if (bR151Regulation)
            {
                stPdRoiRedPoints[0].dX = 0.85;
                stPdRoiRedPoints[0].dY = 0.90;

                stPdRoiRedPoints[1].dX = 0.94;
                stPdRoiRedPoints[1].dY = 0.86;

                stPdRoiRedPoints[2].dX = 1.00;
                stPdRoiRedPoints[2].dY = 0.80;

                stPdRoiRedPoints[3].dX = 1.00;
                stPdRoiRedPoints[3].dY = 0.65;

                stPdRoiRedPoints[4].dX = 1.00;
                stPdRoiRedPoints[4].dY = 0.52;

                stPdRoiRedPoints[5].dX = 1.00;
                stPdRoiRedPoints[5].dY = 0.33;

                stPdRoiRedPoints[6].dX = 0.95;
                stPdRoiRedPoints[6].dY = 0.26;

                stPdRoiRedPoints[7].dX = 0.88;
                stPdRoiRedPoints[7].dY = 0.20;

                stPdRoiRedPoints[8].dX = 0.87;
                stPdRoiRedPoints[8].dY = 0.48;

                stPdRoiRedPoints[9].dX = 0.86;
                stPdRoiRedPoints[9].dY = 0.68;
            }
            else
            {
                stPdRoiRedPoints[0].dX = 0.40;
                stPdRoiRedPoints[0].dY = 0.82;

                stPdRoiRedPoints[1].dX = 0.62;
                stPdRoiRedPoints[1].dY = 0.91;

                stPdRoiRedPoints[2].dX = 0.85;
                stPdRoiRedPoints[2].dY = 0.90;

                stPdRoiRedPoints[3].dX = 1.00;
                stPdRoiRedPoints[3].dY = 0.80;

                stPdRoiRedPoints[4].dX = 1.00;
                stPdRoiRedPoints[4].dY = 0.30;

                stPdRoiRedPoints[5].dX = 0.88;
                stPdRoiRedPoints[5].dY = 0.20;

                stPdRoiRedPoints[6].dX = 0.73;
                stPdRoiRedPoints[6].dY = 0.13;

                stPdRoiRedPoints[7].dX = 0.57;
                stPdRoiRedPoints[7].dY = 0.08;

                stPdRoiRedPoints[8].dX = 0.36;
                stPdRoiRedPoints[8].dY = 0.10;

                stPdRoiRedPoints[9].dX = 0.20;
                stPdRoiRedPoints[9].dY = 0.63;
            }
        }
        else if (BOARD_IsSVersion(BOARD_S_H_3M6) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
        {
            if (bR151Regulation)
            {
                stPdRoiRedPoints[0].dX = 0.16;
                stPdRoiRedPoints[0].dY = 0.50;

                stPdRoiRedPoints[1].dX = 0.03;
                stPdRoiRedPoints[1].dY = 0.55;

                stPdRoiRedPoints[2].dX = 0.02;
                stPdRoiRedPoints[2].dY = 0.99;

                stPdRoiRedPoints[3].dX = 0.35;
                stPdRoiRedPoints[3].dY = 0.99;

                stPdRoiRedPoints[4].dX = 0.65;
                stPdRoiRedPoints[4].dY = 0.99;

                stPdRoiRedPoints[5].dX = 0.97;
                stPdRoiRedPoints[5].dY = 0.99;

                stPdRoiRedPoints[6].dX = 0.97;
                stPdRoiRedPoints[6].dY = 0.83;

                stPdRoiRedPoints[7].dX = 0.97;
                stPdRoiRedPoints[7].dY = 0.70;

                stPdRoiRedPoints[8].dX = 0.98;
                stPdRoiRedPoints[8].dY = 0.50;

                stPdRoiRedPoints[9].dX = 0.61;
                stPdRoiRedPoints[9].dY = 0.50;
                }
            else
            {
                stPdRoiRedPoints[0].dX = 0.47;
                stPdRoiRedPoints[0].dY = 0.39;

                stPdRoiRedPoints[1].dX = 0.03;
                stPdRoiRedPoints[1].dY = 0.55;

                stPdRoiRedPoints[2].dX = 0.02;
                stPdRoiRedPoints[2].dY = 0.99;

                stPdRoiRedPoints[3].dX = 0.35;
                stPdRoiRedPoints[3].dY = 0.99;

                stPdRoiRedPoints[4].dX = 0.65;
                stPdRoiRedPoints[4].dY = 0.99;

                stPdRoiRedPoints[5].dX = 0.97;
                stPdRoiRedPoints[5].dY = 0.99;

                stPdRoiRedPoints[6].dX = 0.97;
                stPdRoiRedPoints[6].dY = 0.83;

                stPdRoiRedPoints[7].dX = 0.97;
                stPdRoiRedPoints[7].dY = 0.70;

                stPdRoiRedPoints[8].dX = 0.98;
                stPdRoiRedPoints[8].dY = 0.39;

                stPdRoiRedPoints[9].dX = 0.73;
                stPdRoiRedPoints[9].dY = 0.39;
            }
        }
        else
        {
            stPdRoiRedPoints[0].dX = 0.2;
            stPdRoiRedPoints[0].dY = 0.6;

            stPdRoiRedPoints[1].dX = 0.2;
            stPdRoiRedPoints[1].dY = 0.7;

            stPdRoiRedPoints[2].dX = 0.2;
            stPdRoiRedPoints[2].dY = 0.8;

            stPdRoiRedPoints[3].dX = 0.4;
            stPdRoiRedPoints[3].dY = 0.8;

            stPdRoiRedPoints[4].dX = 0.6;
            stPdRoiRedPoints[4].dY = 0.8;

            stPdRoiRedPoints[5].dX = 0.8;
            stPdRoiRedPoints[5].dY = 0.8;

            stPdRoiRedPoints[6].dX = 0.8;
            stPdRoiRedPoints[6].dY = 0.7;

            stPdRoiRedPoints[7].dX = 0.8;
            stPdRoiRedPoints[7].dY = 0.6;

            stPdRoiRedPoints[8].dX = 0.6;
            stPdRoiRedPoints[8].dY = 0.6;

            stPdRoiRedPoints[9].dX = 0.4;
            stPdRoiRedPoints[9].dY = 0.6;
        }
    }
    else if (BOARD_IsADA38_R159())
    {
        stPdRoiRedPoints[0].dX = 0.35;
        stPdRoiRedPoints[0].dY = 0.39;

        stPdRoiRedPoints[1].dX = 0.29;
        stPdRoiRedPoints[1].dY = 0.68;

        stPdRoiRedPoints[2].dX = 0.23;
        stPdRoiRedPoints[2].dY = 1.00;

        stPdRoiRedPoints[3].dX = 0.38;
        stPdRoiRedPoints[3].dY = 1.00;

        stPdRoiRedPoints[4].dX = 0.50;
        stPdRoiRedPoints[4].dY = 1.00;

        stPdRoiRedPoints[5].dX = 0.64;
        stPdRoiRedPoints[5].dY = 1.00;

        stPdRoiRedPoints[6].dX = 0.78;
        stPdRoiRedPoints[6].dY = 1.00;

        stPdRoiRedPoints[7].dX = 0.73;
        stPdRoiRedPoints[7].dY = 0.70;

        stPdRoiRedPoints[8].dX = 0.68;
        stPdRoiRedPoints[8].dY = 0.39;

        stPdRoiRedPoints[9].dX = 0.51;
        stPdRoiRedPoints[9].dY = 0.39;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        stPdRoiRedPoints[0].dX = 0.2;
        stPdRoiRedPoints[0].dY = 0.6;

        stPdRoiRedPoints[1].dX = 0.12;
        stPdRoiRedPoints[1].dY = 0.77;

        stPdRoiRedPoints[2].dX = 0.02;
        stPdRoiRedPoints[2].dY = 1;

        stPdRoiRedPoints[3].dX = 0.41;
        stPdRoiRedPoints[3].dY = 1;

        stPdRoiRedPoints[4].dX = 0.68;
        stPdRoiRedPoints[4].dY = 1;

        stPdRoiRedPoints[5].dX = 1;
        stPdRoiRedPoints[5].dY = 0.99;

        stPdRoiRedPoints[6].dX = 0.87;
        stPdRoiRedPoints[6].dY = 0.75;

        stPdRoiRedPoints[7].dX = 0.8;
        stPdRoiRedPoints[7].dY = 0.6;

        stPdRoiRedPoints[8].dX = 0.6;
        stPdRoiRedPoints[8].dY = 0.6;

        stPdRoiRedPoints[9].dX = 0.4;
        stPdRoiRedPoints[9].dY = 0.6;
    }
    else
    {
        stPdRoiRedPoints[0].dX = 0.2;
        stPdRoiRedPoints[0].dY = 0.6;

        stPdRoiRedPoints[1].dX = 0.2;
        stPdRoiRedPoints[1].dY = 0.7;

        stPdRoiRedPoints[2].dX = 0.2;
        stPdRoiRedPoints[2].dY = 0.8;

        stPdRoiRedPoints[3].dX = 0.4;
        stPdRoiRedPoints[3].dY = 0.8;

        stPdRoiRedPoints[4].dX = 0.6;
        stPdRoiRedPoints[4].dY = 0.8;

        stPdRoiRedPoints[5].dX = 0.8;
        stPdRoiRedPoints[5].dY = 0.8;

        stPdRoiRedPoints[6].dX = 0.8;
        stPdRoiRedPoints[6].dY = 0.7;

        stPdRoiRedPoints[7].dX = 0.8;
        stPdRoiRedPoints[7].dY = 0.6;

        stPdRoiRedPoints[8].dX = 0.6;
        stPdRoiRedPoints[8].dY = 0.6;

        stPdRoiRedPoints[9].dX = 0.4;
        stPdRoiRedPoints[9].dY = 0.6;
    }

    return stPdRoiRedPoints;
}

static float *config_afEllipseB()
{
    static float fEllipseB[3] = {0};

    fEllipseB[0] = 0.05;
    fEllipseB[1] = 0.1;
    fEllipseB[2] = 0.15;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        fEllipseB[0] = 0.1;
        fEllipseB[1] = 0.1;
        fEllipseB[2] = 0.1;
    }

    return fEllipseB;
}


/* ROI 区域点坐标 */
static SV_POINT2_S *config_astPdCalibrationPoints()
{
    static SV_POINT2_S stPdCalibrationPoints[12] = {0};

    /* 默认水平大角度 */
    stPdCalibrationPoints[0].dX = 0.4;
    stPdCalibrationPoints[0].dY = 0.4;
    stPdCalibrationPoints[1].dX = 0.3;
    stPdCalibrationPoints[1].dY = 0.6;
    stPdCalibrationPoints[2].dX = 0.2;
    stPdCalibrationPoints[2].dY = 0.8;
    stPdCalibrationPoints[3].dX = 0.1;
    stPdCalibrationPoints[3].dY = 1.0;

    stPdCalibrationPoints[4].dX = 0.6;
    stPdCalibrationPoints[4].dY = 0.4;
    stPdCalibrationPoints[5].dX = 0.7;
    stPdCalibrationPoints[5].dY = 0.6;
    stPdCalibrationPoints[6].dX = 0.8;
    stPdCalibrationPoints[6].dY = 0.8;
    stPdCalibrationPoints[7].dX = 0.9;
    stPdCalibrationPoints[7].dY = 1.0;

    stPdCalibrationPoints[8].dX = 0.35;
    stPdCalibrationPoints[8].dY = 0.7;
    stPdCalibrationPoints[9].dX = 0.43;
    stPdCalibrationPoints[9].dY = 0.7;
    stPdCalibrationPoints[10].dX = 0.35;
    stPdCalibrationPoints[10].dY = 0.96;
    stPdCalibrationPoints[11].dX = 0.43;
    stPdCalibrationPoints[11].dY = 0.96;

    if ((BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_H_6M))    /* 优先客户专用配置 */
        && (BOARD_IsCustomer(BOARD_C_ADA32V2_VT) || BOARD_IsCustomer(BOARD_C_ADA32V2_VT_A) || BOARD_IsCustomer(BOARD_C_ADA32V3_VT))
        )
    {
        stPdCalibrationPoints[0].dX = 0.00;
        stPdCalibrationPoints[0].dY = 0.00;
        stPdCalibrationPoints[1].dX = 0.00;
        stPdCalibrationPoints[1].dY = 0.33;
        stPdCalibrationPoints[2].dX = 0.00;
        stPdCalibrationPoints[2].dY = 0.66;
        stPdCalibrationPoints[3].dX = 0.00;
        stPdCalibrationPoints[3].dY = 1.00;

        stPdCalibrationPoints[4].dX = 1.00;
        stPdCalibrationPoints[4].dY = 0.00;
        stPdCalibrationPoints[5].dX = 1.00;
        stPdCalibrationPoints[5].dY = 0.33;
        stPdCalibrationPoints[6].dX = 1.00;
        stPdCalibrationPoints[6].dY = 0.66;
        stPdCalibrationPoints[7].dX = 1.00;
        stPdCalibrationPoints[7].dY = 1.00;
    }
    else if((BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_H_6M))
             && BOARD_IsCustomer(BOARD_C_ADA32V2_HJWHG02))
    {
        stPdCalibrationPoints[0].dX = 0.43;
        stPdCalibrationPoints[0].dY = 0.61;
        stPdCalibrationPoints[1].dX = 0.36;
        stPdCalibrationPoints[1].dY = 0.69;
        stPdCalibrationPoints[2].dX = 0.27;
        stPdCalibrationPoints[2].dY = 0.80;
        stPdCalibrationPoints[3].dX = 0.10;
        stPdCalibrationPoints[3].dY = 1;

        stPdCalibrationPoints[4].dX = 0.69;
        stPdCalibrationPoints[4].dY = 0.61;
        stPdCalibrationPoints[5].dX = 0.73;
        stPdCalibrationPoints[5].dY = 0.69;
        stPdCalibrationPoints[6].dX = 0.79;
        stPdCalibrationPoints[6].dY = 0.80;
        stPdCalibrationPoints[7].dX = 0.90;
        stPdCalibrationPoints[7].dY = 1.00;

    }
    else if((BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_H_6M))
             && BOARD_IsCustomer(BOARD_C_ADA32V2_201352))
    {
        stPdCalibrationPoints[0].dX = 0.0;
        stPdCalibrationPoints[0].dY = 0.4;
        stPdCalibrationPoints[1].dX = 0.0;
        stPdCalibrationPoints[1].dY = 0.6;
        stPdCalibrationPoints[2].dX = 0.0;
        stPdCalibrationPoints[2].dY = 0.8;
        stPdCalibrationPoints[3].dX = 0.0;
        stPdCalibrationPoints[3].dY = 1.0;

        stPdCalibrationPoints[4].dX = 1.0;
        stPdCalibrationPoints[4].dY = 0.4;
        stPdCalibrationPoints[5].dX = 1.0;
        stPdCalibrationPoints[5].dY = 0.6;
        stPdCalibrationPoints[6].dX = 1.0;
        stPdCalibrationPoints[6].dY = 0.8;
        stPdCalibrationPoints[7].dX = 1.0;
        stPdCalibrationPoints[7].dY = 1.0;
    }
    else if((BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_H_6M))
             && BOARD_IsCustomer(BOARD_C_ADA32V2_FTC))
    {
        stPdCalibrationPoints[0].dX = 0.310;
        stPdCalibrationPoints[0].dY = 0.202;
        stPdCalibrationPoints[1].dX = 0.260;
        stPdCalibrationPoints[1].dY = 0.393;
        stPdCalibrationPoints[2].dX = 0.192;
        stPdCalibrationPoints[2].dY = 0.650;
        stPdCalibrationPoints[3].dX = 0.100;
        stPdCalibrationPoints[3].dY = 1.000;

        stPdCalibrationPoints[4].dX = 0.706;
        stPdCalibrationPoints[4].dY = 0.202;
        stPdCalibrationPoints[5].dX = 0.752;
        stPdCalibrationPoints[5].dY = 0.393;
        stPdCalibrationPoints[6].dX = 0.815;
        stPdCalibrationPoints[6].dY = 0.650;
        stPdCalibrationPoints[7].dX = 0.900;
        stPdCalibrationPoints[7].dY = 1.000;
    }
    else if((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001)) && BOARD_IsSVersion(BOARD_S_H_6M))
    {
        stPdCalibrationPoints[0].dX = 0.01067;
        stPdCalibrationPoints[0].dY = 0.01;
        stPdCalibrationPoints[1].dX = 0.01067;
        stPdCalibrationPoints[1].dY = 0.01;
        stPdCalibrationPoints[2].dX = 0.01067;
        stPdCalibrationPoints[2].dY = 0.1754;
        stPdCalibrationPoints[3].dX = 0.01067;
        stPdCalibrationPoints[3].dY = 0.9668;
        stPdCalibrationPoints[4].dX = 0.99;
        stPdCalibrationPoints[4].dY = 0.01;
        stPdCalibrationPoints[5].dX = 0.99;
        stPdCalibrationPoints[5].dY = 0.01;
        stPdCalibrationPoints[6].dX = 0.9899;
        stPdCalibrationPoints[6].dY = 0.1754;
        stPdCalibrationPoints[7].dX = 0.9853;
        stPdCalibrationPoints[7].dY = 0.9688;
    }
    else if ((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001)) && (BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8)))
    {
        stPdCalibrationPoints[0].dX = 0.224;
        stPdCalibrationPoints[0].dY = 0.204;
        stPdCalibrationPoints[1].dX = 0.224;
        stPdCalibrationPoints[1].dY = 0.251;
        stPdCalibrationPoints[2].dX = 0.224;
        stPdCalibrationPoints[2].dY = 0.251;
        stPdCalibrationPoints[3].dX = 0.224;
        stPdCalibrationPoints[3].dY = 1;

        stPdCalibrationPoints[4].dX = 0.808;
        stPdCalibrationPoints[4].dY = 0.204;
        stPdCalibrationPoints[5].dX = 0.808;
        stPdCalibrationPoints[5].dY = 0.251;
        stPdCalibrationPoints[6].dX = 0.808;
        stPdCalibrationPoints[6].dY = 0.251;
        stPdCalibrationPoints[7].dX = 0.808;
        stPdCalibrationPoints[7].dY = 1;

    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
    {
        stPdCalibrationPoints[0].dX = 0.000;
        stPdCalibrationPoints[0].dY = 0.582947;
        stPdCalibrationPoints[1].dX = 0.000;
        stPdCalibrationPoints[1].dY = 0.69146;
        stPdCalibrationPoints[2].dX = 0.000521;
        stPdCalibrationPoints[2].dY = 0.883225;
        stPdCalibrationPoints[3].dX = 0.001564;
        stPdCalibrationPoints[3].dY = 0.999073;

        stPdCalibrationPoints[4].dX = 0.998957;
        stPdCalibrationPoints[4].dY = 0.582947;
        stPdCalibrationPoints[5].dX = 0.999479;
        stPdCalibrationPoints[5].dY = 0.69146;
        stPdCalibrationPoints[6].dX = 0.999479;
        stPdCalibrationPoints[6].dY = 0.883225;
        stPdCalibrationPoints[7].dX = 0.998957;
        stPdCalibrationPoints[7].dY = 0.999073;

        stPdCalibrationPoints[8].dX = 0.35;
        stPdCalibrationPoints[8].dY = 0.7;
        stPdCalibrationPoints[9].dX = 0.43;
        stPdCalibrationPoints[9].dY = 0.7;
        stPdCalibrationPoints[10].dX = 0.35;
        stPdCalibrationPoints[10].dY = 0.96;
        stPdCalibrationPoints[11].dX = 0.43;
        stPdCalibrationPoints[11].dY = 0.96;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_201724))
    {
        stPdCalibrationPoints[0].dX = 0.5;
        stPdCalibrationPoints[0].dY = 0.99;
        stPdCalibrationPoints[1].dX = 0.077058;
        stPdCalibrationPoints[1].dY = 0.99;
        stPdCalibrationPoints[2].dX = 0.084027;
        stPdCalibrationPoints[2].dY = 0.99;
        stPdCalibrationPoints[3].dX = 0.092346;
        stPdCalibrationPoints[3].dY = 0.99;

        stPdCalibrationPoints[4].dX = 0.758955;
        stPdCalibrationPoints[4].dY = 0.522238;
        stPdCalibrationPoints[5].dX = 0.763951;
        stPdCalibrationPoints[5].dY = 0.609408;
        stPdCalibrationPoints[6].dX = 0.780605;
        stPdCalibrationPoints[6].dY = 0.9;
        stPdCalibrationPoints[7].dX = 0.785763;
        stPdCalibrationPoints[7].dY = 0.99;

        stPdCalibrationPoints[8].dX = 0.35;
        stPdCalibrationPoints[8].dY = 0.7;
        stPdCalibrationPoints[9].dX = 0.43;
        stPdCalibrationPoints[9].dY = 0.7;
        stPdCalibrationPoints[10].dX = 0.35;
        stPdCalibrationPoints[10].dY = 0.96;
        stPdCalibrationPoints[11].dX = 0.43;
        stPdCalibrationPoints[11].dY = 0.96;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V3_EXHIBITION) || BOARD_IsCustomer(BOARD_C_ADA32V2_EXHIBITION_C))
    {
        stPdCalibrationPoints[0].dX = 0.500;
        stPdCalibrationPoints[0].dY = 0.990;
        stPdCalibrationPoints[1].dX = 0.104;
        stPdCalibrationPoints[1].dY = 0.990;
        stPdCalibrationPoints[2].dX = 0.245;
        stPdCalibrationPoints[2].dY = 0.990;
        stPdCalibrationPoints[3].dX = 0.356;
        stPdCalibrationPoints[3].dY = 0.990;

        stPdCalibrationPoints[4].dX = 0.759;
        stPdCalibrationPoints[4].dY = 0.522;
        stPdCalibrationPoints[5].dX = 0.764;
        stPdCalibrationPoints[5].dY = 0.609;
        stPdCalibrationPoints[6].dX = 0.781;
        stPdCalibrationPoints[6].dY = 0.900;
        stPdCalibrationPoints[7].dX = 0.786;
        stPdCalibrationPoints[7].dY = 0.990;

        stPdCalibrationPoints[8].dX = 0.400;
        stPdCalibrationPoints[8].dY = 0.500;
        stPdCalibrationPoints[9].dX = 0.550;
        stPdCalibrationPoints[9].dY = 0.500;
        stPdCalibrationPoints[10].dX = 0.400;
        stPdCalibrationPoints[10].dY = 0.850;
        stPdCalibrationPoints[11].dX = 0.550;
        stPdCalibrationPoints[11].dY = 0.850;
    }
    else if (BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8))
    {
        stPdCalibrationPoints[0].dX = 0.4;
        stPdCalibrationPoints[0].dY = 0.4;
        stPdCalibrationPoints[1].dX = 0.3;
        stPdCalibrationPoints[1].dY = 0.6;
        stPdCalibrationPoints[2].dX = 0.2;
        stPdCalibrationPoints[2].dY = 0.8;
        stPdCalibrationPoints[3].dX = 0.1;
        stPdCalibrationPoints[3].dY = 1.0;

        stPdCalibrationPoints[4].dX = 0.6;
        stPdCalibrationPoints[4].dY = 0.4;
        stPdCalibrationPoints[5].dX = 0.7;
        stPdCalibrationPoints[5].dY = 0.6;
        stPdCalibrationPoints[6].dX = 0.8;
        stPdCalibrationPoints[6].dY = 0.8;
        stPdCalibrationPoints[7].dX = 0.9;
        stPdCalibrationPoints[7].dY = 1.0;
    }
    else if(BOARD_IsSVersion(BOARD_S_V_6M) || BOARD_IsSVersion(BOARD_S_V_2M3) ||
        BOARD_IsSVersion(BOARD_S_V_1M45) || BOARD_IsSVersion(BOARD_S_V_1M99))
    {
        stPdCalibrationPoints[0].dX = 0.2;
        stPdCalibrationPoints[0].dY = 0.5;
        stPdCalibrationPoints[1].dX = 0.4;
        stPdCalibrationPoints[1].dY = 0.5;
        stPdCalibrationPoints[2].dX = 0.6;
        stPdCalibrationPoints[2].dY = 0.5;
        stPdCalibrationPoints[3].dX = 0.8;
        stPdCalibrationPoints[3].dY = 0.5;

        stPdCalibrationPoints[4].dX = 0.2;
        stPdCalibrationPoints[4].dY = 1.0;
        stPdCalibrationPoints[5].dX = 0.4;
        stPdCalibrationPoints[5].dY = 1.0;
        stPdCalibrationPoints[6].dX = 0.6;
        stPdCalibrationPoints[6].dY = 1.0;
        stPdCalibrationPoints[7].dX = 0.8;
        stPdCalibrationPoints[7].dY = 1.0;
    }
    else if(BOARD_IsSVersion(BOARD_S_H_1M99))
    {
        stPdCalibrationPoints[0].dX = 0.31;
        stPdCalibrationPoints[0].dY = 0.09;
        stPdCalibrationPoints[1].dX = 0.25;
        stPdCalibrationPoints[1].dY = 0.28;
        stPdCalibrationPoints[2].dX = 0.14;
        stPdCalibrationPoints[2].dY = 0.61;
        stPdCalibrationPoints[3].dX = 0.00;
        stPdCalibrationPoints[3].dY = 1.00;

        stPdCalibrationPoints[4].dX = 0.69;
        stPdCalibrationPoints[4].dY = 0.09;
        stPdCalibrationPoints[5].dX = 0.75;
        stPdCalibrationPoints[5].dY = 0.28;
        stPdCalibrationPoints[6].dX = 0.86;
        stPdCalibrationPoints[6].dY = 0.61;
        stPdCalibrationPoints[7].dX = 1.00;
        stPdCalibrationPoints[7].dY = 1.00;
    }
    else if(BOARD_IsSVersion(BOARD_S_H_1M45))
    {
        stPdCalibrationPoints[0].dX = 0.26;
        stPdCalibrationPoints[0].dY = 0.30;
        stPdCalibrationPoints[1].dX = 0.21;
        stPdCalibrationPoints[1].dY = 0.45;
        stPdCalibrationPoints[2].dX = 0.13;
        stPdCalibrationPoints[2].dY = 0.67;
        stPdCalibrationPoints[3].dX = 0.03;
        stPdCalibrationPoints[3].dY = 0.95;

        stPdCalibrationPoints[4].dX = 0.81;
        stPdCalibrationPoints[4].dY = 0.30;
        stPdCalibrationPoints[5].dX = 0.84;
        stPdCalibrationPoints[5].dY = 0.45;
        stPdCalibrationPoints[6].dX = 0.89;
        stPdCalibrationPoints[6].dY = 0.67;
        stPdCalibrationPoints[7].dX = 0.96;
        stPdCalibrationPoints[7].dY = 0.95;
    }

    if(BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
        stPdCalibrationPoints[0].dX = 0.4;
        stPdCalibrationPoints[0].dY = 0.683897;
        stPdCalibrationPoints[1].dX = 0.313208;
        stPdCalibrationPoints[1].dY = 0.775348;
        stPdCalibrationPoints[2].dX = 0.245283;
        stPdCalibrationPoints[2].dY = 0.846918;
        stPdCalibrationPoints[3].dX = 0.1;
        stPdCalibrationPoints[3].dY = 1;

        stPdCalibrationPoints[4].dX = 0.606264;
        stPdCalibrationPoints[4].dY = 0.683897;
        stPdCalibrationPoints[5].dX = 0.691244;
        stPdCalibrationPoints[5].dY = 0.775348;
        stPdCalibrationPoints[6].dX = 0.75775;
        stPdCalibrationPoints[6].dY = 0.846918;
        stPdCalibrationPoints[7].dX = 0.9;
        stPdCalibrationPoints[7].dY = 1;

        stPdCalibrationPoints[8].dX = 0.35;
        stPdCalibrationPoints[8].dY = 0.7;
        stPdCalibrationPoints[9].dX = 0.43;
        stPdCalibrationPoints[9].dY = 0.7;
        stPdCalibrationPoints[10].dX = 0.35;
        stPdCalibrationPoints[10].dY = 0.96;
        stPdCalibrationPoints[11].dX = 0.43;
        stPdCalibrationPoints[11].dY = 0.96;
    }

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        stPdCalibrationPoints[0].dX = 0.31;
        stPdCalibrationPoints[0].dY = 0.09;
        stPdCalibrationPoints[1].dX = 0.27547;
        stPdCalibrationPoints[1].dY = 0.28;
        stPdCalibrationPoints[2].dX = 0.226462;
        stPdCalibrationPoints[2].dY = 0.549658;
        stPdCalibrationPoints[3].dX = 0.178227;
        stPdCalibrationPoints[3].dY = 0.815068;
        stPdCalibrationPoints[4].dX = 0.69;
        stPdCalibrationPoints[4].dY = 0.09;
        stPdCalibrationPoints[5].dX = 0.733273;
        stPdCalibrationPoints[5].dY = 0.28;
        stPdCalibrationPoints[6].dX = 0.794688;
        stPdCalibrationPoints[6].dY = 0.549658;
        stPdCalibrationPoints[7].dX = 0.855136;
        stPdCalibrationPoints[7].dY = 0.815068;
    }

#if (defined(BOARD_ADA32IR))

    stPdCalibrationPoints[0].dX = 0.500;
    stPdCalibrationPoints[0].dY = 0.990;
    stPdCalibrationPoints[1].dX = 0.077;
    stPdCalibrationPoints[1].dY = 0.990;
    stPdCalibrationPoints[2].dX = 0.245;
    stPdCalibrationPoints[2].dY = 0.990;
    stPdCalibrationPoints[3].dX = 0.356;
    stPdCalibrationPoints[3].dY = 0.990;

    stPdCalibrationPoints[4].dX = 0.759;
    stPdCalibrationPoints[4].dY = 0.522;
    stPdCalibrationPoints[5].dX = 0.764;
    stPdCalibrationPoints[5].dY = 0.609;
    stPdCalibrationPoints[6].dX = 0.781;
    stPdCalibrationPoints[6].dY = 0.900;
    stPdCalibrationPoints[7].dX = 0.786;
    stPdCalibrationPoints[7].dY = 0.990;

    stPdCalibrationPoints[8].dX = 0.400;
    stPdCalibrationPoints[8].dY = 0.500;
    stPdCalibrationPoints[9].dX = 0.550;
    stPdCalibrationPoints[9].dY = 0.500;
    stPdCalibrationPoints[10].dX = 0.400;
    stPdCalibrationPoints[10].dY = 0.850;
    stPdCalibrationPoints[11].dX = 0.550;
    stPdCalibrationPoints[11].dY = 0.850;

#endif
#if (defined(BOARD_HDW845V1))
    /*只需要前面四个点，后面8个点都是无效数据*/
    stPdCalibrationPoints[0].dX = 0.100;
    stPdCalibrationPoints[0].dY = 0.100;
    stPdCalibrationPoints[1].dX = 0.900;
    stPdCalibrationPoints[1].dY = 0.900;
    stPdCalibrationPoints[2].dX = 0.400;
    stPdCalibrationPoints[2].dY = 0.400;
    stPdCalibrationPoints[3].dX = 0.600;
    stPdCalibrationPoints[3].dY = 0.600;

    stPdCalibrationPoints[4].dX = 0.000;
    stPdCalibrationPoints[4].dY = 0.000;
    stPdCalibrationPoints[5].dX = 0.000;
    stPdCalibrationPoints[5].dY = 0.000;
    stPdCalibrationPoints[6].dX = 0.000;
    stPdCalibrationPoints[6].dY = 0.000;
    stPdCalibrationPoints[7].dX = 0.000;
    stPdCalibrationPoints[7].dY = 0.000;

    stPdCalibrationPoints[8].dX = 0.000;
    stPdCalibrationPoints[8].dY = 0.000;
    stPdCalibrationPoints[9].dX = 0.000;
    stPdCalibrationPoints[9].dY = 0.000;
    stPdCalibrationPoints[10].dX = 0.000;
    stPdCalibrationPoints[10].dY = 0.000;
    stPdCalibrationPoints[11].dX = 0.000;
    stPdCalibrationPoints[11].dY = 0.000;

#endif
    return stPdCalibrationPoints;
}

static sint32 config_enPdsModel()
{
    EPdsModel enPdsModel = E_PDS_P;

    if((BOARD_IsSVersion(BOARD_S_H_1M99) || BOARD_IsSVersion(BOARD_S_H_1M45)) && BOARD_IsNotCustomer(BOARD_C_ADA32V2_201623))
    {
        enPdsModel = E_PDS_OWP;
    }

    if(BOARD_IsCustomer(BOARD_C_ADA32V2_201266A))
    {
        enPdsModel = E_PDS_MANHOLE;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202406))
    {
        enPdsModel = E_PDS_PC;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200019))
    {
        enPdsModel = E_PDS_PC;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_VT) || BOARD_IsCustomer(BOARD_C_ADA32V2_VT_A)|| BOARD_IsCustomer(BOARD_C_ADA32V3_VT))
    {
        enPdsModel = E_PDS_PC;
    }

    return enPdsModel;
}

static CHN_ALG_E config_enAlgType()
{
    CHN_ALG_E enAlgType = ALG_DMS;
    if (BOARD_IsCustomer(BOARD_C_ADA47V1_TDCZA))
    {
        enAlgType = ALG_APC;
    }
    else if ((BOARD_GetVersion() == BOARD_ADA47V1_V1) || (BOARD_GetVersion() == BOARD_ADA47V1_V3) || (!BOARD_IsSVersion(BOARD_S_ADA47V1_H_6M) && !BOARD_IsSVersion(BOARD_S_ADA47V1_G_6M)))
    {
        enAlgType = ALG_PDS;
    }

    return enAlgType;
}


static sint32 config_enAlgTrigger()
{
    TRIGGER_TYPE_S enAlgTrigger = TRIGGER_UP;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_WXKY))
    {
        enAlgTrigger = TRIGGER_DOWN;
    }
    return enAlgTrigger;
}

/* PD报警音频类型 */
static ALARM_AUD_E config_ePdAudioType()
{
    ALARM_AUD_E enAudioType = ALARM_AUD_DING;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        enAudioType = ALARM_AUD_A6;
    }

    return enAudioType;
}

static ALARM_AUD_E config_enDmsAlarmAudioType()
{
    ALARM_AUD_E enAlarmAudioType = ALARM_AUD_VOICE;
    if (BOARD_IsDMS31P())
    {
        enAlarmAudioType = ALARM_AUD_DING;
    }

    return enAlarmAudioType;
}

static sint32 config_s32AlgAudioVolume()
{
    sint32 s32AudioVolume = 5*20;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32AudioVolume = 4*20;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32AudioVolume = 3*20;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202668))
    {
        s32AudioVolume = 0;
    }

    return s32AudioVolume;
}

static sint32 config_s32PdsAlarmerVolume()
{
    sint32 s32AudioVolume = 8;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_202668))
    {
        s32AudioVolume = 0;
    }

    return s32AudioVolume;
}


static sint32 config_s32FatigueInterval()
{
    sint32 s32Interval = 0;

    if (BOARD_IsDMS31P())
    {
        s32Interval = 5;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Interval = 30;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32Interval = 5;
    }

    return s32Interval;
}

static sint32 config_s32FatigueL2Interval()
{
    sint32 s32Interval = -1;
    if(BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Interval = 0;
    }
    return s32Interval;
}

static sint32 config_s32DistractionInterval()
{
    sint32 s32Interval = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Interval = 120;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32Interval = 10;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32Interval = 2;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_111428))
    {
        s32Interval = -1;
    }

    return s32Interval;
}

static sint32 config_s32NoDriverInterval()
{
    sint32 s32Interval = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Interval = 300;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI))
    {
        s32Interval = 180;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE) || BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION) || BOARD_IsCustomer(BOARD_C_DMS31V2_202742))
    {
        s32Interval = -1;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32Interval = 2;
    }

    return s32Interval;
}

static sint32 config_s32SmokeInterval()
{
    sint32 s32Interval = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Interval = 120;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI))
    {
        s32Interval = 180;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32Interval = 10;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018) || BOARD_IsCustomer(BOARD_C_DMS31V2_111428))
    {
        s32Interval = -1;
    }

    return s32Interval;
}

static sint32 config_s32PhoneInterval()
{
    sint32 s32Interval = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Interval = 30;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI))
    {
        s32Interval = 180;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32Interval = 10;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_111428))
    {
        s32Interval = -1;
    }

    return s32Interval;
}

static sint32 config_s32YawnInterval()
{
    sint32 s32Interval = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32Interval = 30;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32Interval = 10;
    }

    return s32Interval;
}

static sint32 config_s32NoMaskInterval()
{

    sint32 s32Interval = -1;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_ACR))
    {
        s32Interval = 0;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE) || BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION)
        || BOARD_IsCustomer(BOARD_C_DMS31V2_111396) || BOARD_IsCustomer(BOARD_C_DMS31V2_111428) || BOARD_IsCustomer(BOARD_C_DMS31V2_202742))
    {
        s32Interval = -1;
    }

    return s32Interval;
}

static sint32 config_s32SunGlassInterval()
{
    sint32 s32Interval = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE) || BOARD_IsCustomer(BOARD_C_DMS31V2_202018)
        || BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION) || BOARD_IsCustomer(BOARD_C_DMS31V2_111396)
        || BOARD_IsCustomer(BOARD_C_DMS31V2_111428) || BOARD_IsCustomer(BOARD_C_DMS31V2_202742))
    {
        s32Interval = -1;
    }

    return s32Interval;
}

static sint32 config_s32SeatBeltInterval()
{

    sint32 s32Interval = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE) || BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION)
        || BOARD_IsCustomer(BOARD_C_DMS31V2_202018) || BOARD_IsCustomer(BOARD_C_DMS31V2_111396) || BOARD_IsCustomer(BOARD_C_DMS31V2_111428))
    {
        s32Interval = -1;
    }

    return s32Interval;
}

static sint32 config_s32ShelterInterval()
{
    sint32 s32Interval = 5;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE) || BOARD_IsCustomer(BOARD_C_DMS31V2_202018) || BOARD_IsCustomer(BOARD_C_DMS31V2_111428))
    {
        s32Interval = -1;
    }

    return s32Interval;
}

static sint32 config_s32DrinkEatInterval()
{
    sint32 s32Interval = -1;
    return s32Interval;
}

static sint32 config_s32OverspeedInterval()
{
    sint32 s32Interval = -1;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        s32Interval = 60;
    }
    return s32Interval;
}

static sint32 config_s32NoHelmetInterval()
{
    sint32 s32Interval = -1;
    return s32Interval;
}

static sint32 config_s32ChangeGuardInterval()
{
    sint32 s32Interval = -1;
    return s32Interval;
}

static sint32 config_s32DmsSensitivity()
{
    sint32 s32DmsSensitivity = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI))
    {
        s32DmsSensitivity = -1;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsSensitivity = 1;
    }

    return s32DmsSensitivity;
}

static sint32 config_s32DmsMiddleSpeedThr()
{
    sint32 s32DmsMiddleSpeedThr = 40;
    return s32DmsMiddleSpeedThr;
}

static sint32 config_s32DmsHighSpeedThr()
{
    sint32 s32DmsHighSpeedThr = 70;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI))
    {
        s32DmsHighSpeedThr = 50;
    }
    return s32DmsHighSpeedThr;
}

static SV_BOOL config_bDmsOsdEnable()
{
    SV_BOOL bDmsOsdEnable = SV_TRUE;
    return bDmsOsdEnable;
}

static SV_BOOL config_bDmsWorkspeed_almNoGPS()
{

    SV_BOOL bDmsWorkspeed_almNoGPS = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_ACR) || BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        bDmsWorkspeed_almNoGPS = SV_TRUE;
    }
    return bDmsWorkspeed_almNoGPS;
}

static sint32 config_s32DmsWorkspeed_speed()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_ACR) || BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) || BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32WorkSpeed = 30;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32WorkSpeed = 10;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_fatigue()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_distraction()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_noDriver()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_smoke()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_phone()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_yawn()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_noMask()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_sunglass()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_seatbelt()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_shelter()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_fatigueL2()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_drinkEat()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_overspeed()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

static sint32 config_s32DmsWorkspeed_noHelmet()
{
    sint32 s32WorkSpeed = 0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32WorkSpeed = 15;
    }

    return s32WorkSpeed;
}

typedef sint32 (*configWorkSpeedFunction)();
configWorkSpeedFunction config_s32DmsWorkspeed[] = {
    config_s32DmsWorkspeed_speed,
    config_s32DmsWorkspeed_fatigue,
    config_s32DmsWorkspeed_distraction,
    config_s32DmsWorkspeed_noDriver,
    config_s32DmsWorkspeed_smoke,
    config_s32DmsWorkspeed_phone,
    config_s32DmsWorkspeed_yawn,
    config_s32DmsWorkspeed_noMask,
    config_s32DmsWorkspeed_sunglass,
    config_s32DmsWorkspeed_seatbelt,
    config_s32DmsWorkspeed_shelter,
    config_s32DmsWorkspeed_fatigueL2,
    config_s32DmsWorkspeed_drinkEat,
    config_s32DmsWorkspeed_overspeed,
    config_s32DmsWorkspeed_noHelmet
};

#define config_s32DmsWorkspeed_dmm(dmmType) \
( \
{ \
    sint32 s32WorkSpeed = 0; \
    s32WorkSpeed = config_s32DmsWorkspeed[dmmType](); \
    s32WorkSpeed; \
} \
)

static SV_BOOL config_bDmsCalibrated()
{
    SV_BOOL bDmsCalibrated = SV_FALSE;
    return bDmsCalibrated;
}

static EDmsLogin config_enDmsLoginMode()
{
    EDmsLogin enDmsLoginMode = E_LOGIN_OFF;
    return enDmsLoginMode;
}

static sint32 config_s32DmsFrInterval()
{
    sint32 s32DmsFrInterval = -1;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL))
    {
        s32DmsFrInterval = 300;
    }
    return s32DmsFrInterval;
}

static SV_BOOL config_bDmsFaceCapture()
{
    SV_BOOL bTrue = SV_FALSE;
    return bTrue;
}

static SV_BOOL config_bDmsGazeTracking()
{
    SV_BOOL bDmsGazeTracking = SV_TRUE;
    if (BOARD_IsDMS31P())
    {
        bDmsGazeTracking = SV_FALSE;
    }

    return bDmsGazeTracking;
}

static sint32 config_s32DmsEyelidClosure()
{
    sint32 s32DmsEyelidClosure = 60;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE) || BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsEyelidClosure = 35;
    }

    return s32DmsEyelidClosure;
}

static sint32 config_s32DmsFatigueTimelimit()
{
    sint32 s32DmsFatigueTimelimit = 3;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION) || BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsFatigueTimelimit = 1;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_111314))
    {
        s32DmsFatigueTimelimit = 5;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001))
    {
        s32DmsFatigueTimelimit = 3;
    }

    return s32DmsFatigueTimelimit;
}

static sint32 config_s32DmsFatigueSumTime()
{
    sint32 s32Value = 60;
    return s32Value;
}

static sint32 config_s32DmsFatigueClosePercent()
{
    sint32 s32Value = 20;
    return s32Value;
}

static sint32 config_s32DmsDistractionAngle()
{
    sint32 s32DmsDistractionAngle = 35;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) || BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsDistractionAngle = 45;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32DmsDistractionAngle = 40;
    }

    return s32DmsDistractionAngle;
}

static sint32 config_s32DmsDistractionTimelimit()
{
    sint32 s32DmsDistractionTimelimit = 5;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsDistractionTimelimit = 2;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsDistractionTimelimit = 4;
    }

    return s32DmsDistractionTimelimit;
}

static sint32 config_s32DmsNodriverTimelimit()
{
    sint32 s32DmsNodriverTimelimit = 15;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) || BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsNodriverTimelimit = 30;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsNodriverTimelimit = 3;
    }

    return s32DmsNodriverTimelimit;
}

static sint32 config_s32DmsSmokeThreshold()
{
    sint32 s32DmsSmokeThreshold = 50;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32DmsSmokeThreshold = 45;
    }
    else if(BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32DmsSmokeThreshold = 90;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsSmokeThreshold = 30;
    }

    return s32DmsSmokeThreshold;
}

static sint32 config_s32DmsSmokeTimelimit()
{
    sint32 s32DmsSmokeTimelimit = 3;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32DmsSmokeTimelimit = 5;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsSmokeTimelimit = 1;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsSmokeTimelimit = 0;
    }

    return s32DmsSmokeTimelimit;
}

static sint32 config_s32DmsPhoneThreshold()
{
    sint32 s32DmsPhoneThreshold = 55;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32DmsPhoneThreshold = 65;
    }
    else if(BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32DmsPhoneThreshold = 10;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsPhoneThreshold = 65;
    }

    return s32DmsPhoneThreshold;
}

static sint32 config_s32DmsPhoneTimelimit()
{
    sint32 s32DmsPhoneTimelimit = 3;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE))
    {
        s32DmsPhoneTimelimit = 2;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32DmsPhoneTimelimit = 5;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsPhoneTimelimit = 1;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsPhoneTimelimit = 0;
    }

    return s32DmsPhoneTimelimit;
}

static sint32 config_s32DmsYawnTimelimit()
{
    sint32 s32DmsYawnTimelimit = 2;
    return s32DmsYawnTimelimit;
}

static sint32 config_s32DmsNoMaskTimelimit()
{
    sint32 s32DmsNoMaskTimelimit = 5;
    return s32DmsNoMaskTimelimit;
}

static sint32 config_s32DmsSunGlassTimelimit()
{
    sint32 s32DmsSunGlassTimelimit = 5;
    return s32DmsSunGlassTimelimit;
}

static sint32 config_s32DmsSeatBeltThreshold()
{
    sint32 s32DmsSeatBeltThreshold = 50;
    return s32DmsSeatBeltThreshold;
}

static sint32 config_s32DmsSeatBeltTimelimit()
{
    sint32 s32DmsSeatBeltTimelimit = 10;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsSeatBeltTimelimit = 5;
    }

    return s32DmsSeatBeltTimelimit;
}

static sint32 config_s32DmsShelterTimelimit()
{
    sint32 s32DmsShelterTimelimit = 20;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_EXHIBITION))
    {
        s32DmsShelterTimelimit = 17;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32DmsShelterTimelimit = 1;
    }

    return s32DmsShelterTimelimit;
}

static sint32 config_s32DmsDrinkEatThreshold()
{
    sint32 s32DmsDirnkEatThreshold = 50;
    return s32DmsDirnkEatThreshold;
}

static sint32 config_s32DmsDrinkEatTimelimit()
{
    sint32 s32DmsDirnkEatTimelimit = 3;
    return s32DmsDirnkEatTimelimit;
}

static sint32 config_s32DmsOverspeedLimit()
{
    sint32 s32DmsOverspeedLimit = 60;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        s32DmsOverspeedLimit = 50;
    }
    return s32DmsOverspeedLimit;
}

static sint32 config_s32DmsNoHelmetThreshold()
{
    sint32 s32Threshold = 50;
    return s32Threshold;
}

static sint32 config_s32DmsNoHelmetTimelimit()
{
    sint32 s32Timelimit = 10;
    return s32Timelimit;
}

static sint32 config_s32DmsFatigueMildThres()
{
    sint32 s32Thres = 30;
    return s32Thres;
}

static sint32 config_s32DmsFatigueModerateThres()
{
    sint32 s32Thres = 50;
    return s32Thres;
}

static sint32 config_s32DmsFatigueSevereThres()
{
    sint32 s32Thres = 70;
    return s32Thres;
}

static CFG_DDAW_PARAM config_s32DmsDDAWParam()
{
    CFG_DDAW_PARAM stDDAWPram = {0};
    stDDAWPram.fBlinkScorer = 10.0;
    stDDAWPram.fYawnScorer = 30.0;
    stDDAWPram.fGazeScorer = 30.0;
    stDDAWPram.fGazeThreshold = 25.0;
    stDDAWPram.fFatigueYawRadius = 25.0;
    stDDAWPram.fFatigueEcrGradThreshold = 0.25;
    stDDAWPram.fFatigueYawGradThreshold = 8.0;
    stDDAWPram.fFatiguePitchGradThreshold = 8.0;
    stDDAWPram.fShelterThreshold = 0.5;
    stDDAWPram.s32KssLevel7Thres = 50;
    stDDAWPram.s32KssLevel8Thres = 65;
    stDDAWPram.s32KssLevel9Thres = 85;
    return stDDAWPram;
}

static SV_BOOL config_bDmsAlarmOutFatigue()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutDistraction()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutNoDriver()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutSmoke()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutPhone()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutYawn()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutNoMask()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutSunGlass()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutSeatBelt()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutShelter()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutFatigueL2()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutDrinkEat()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutLoginFail()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB) || BOARD_IsCustomer(BOARD_C_DMS31V2_111371))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutLoginSuccess()
{
    SV_BOOL bAlarmOut = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111371))
    {
        bAlarmOut = SV_TRUE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutOverspeed()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_SEB))
    {
        bAlarmOut = SV_FALSE;
    }
    return bAlarmOut;
}

static SV_BOOL config_bDmsAlarmOutNoHelmet()
{
    SV_BOOL bAlarmOut = SV_TRUE;
    return bAlarmOut;
}

static SV_BOOL config_bServerEnable()
{
    SV_BOOL bServerEnable = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        bServerEnable = SV_FALSE;
    }

    return bServerEnable;
}

static UPLOAD_FILE_OPTS_E config_enUploadFileOpts()
{
    UPLOAD_FILE_OPTS_E enUploadFileOpts = UPLOAD_FILE_OFF;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL))
    {
        enUploadFileOpts = UPLOAD_FILE_OFF;
    }

    return enUploadFileOpts;
}

static SV_BOOL config_bLoopOverwrite()
{
    SV_BOOL bLoopOverwrite = SV_TRUE;
    if(BOARD_IsCustomer(BOARD_C_ADA32IR_202217))
    {
        bLoopOverwrite = SV_FALSE;
    }
    return bLoopOverwrite;
}

static REC_ALARM_E config_enAlarmRecord()
{
    REC_ALARM_E enAlarmRecord = E_REC_VIDEO_PIC;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018) || BOARD_IsCustomer(BOARD_C_ADA47V1_CZAEX))
    {
        enAlarmRecord = E_REC_OFF;
    }
#else
    if(BOARD_IsCustomer(BOARD_C_ADA32IR_202217))
    {
        enAlarmRecord = E_REC_OFF;
    }
    else
    {
        enAlarmRecord = E_REC_VIDEO;
    }
#endif
    return enAlarmRecord;
}

static SV_BOOL config_enableSDLedAlarm()
{
    SV_BOOL bEnableSDLedAlarm = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202116))
    {
        bEnableSDLedAlarm = SV_TRUE;
    }

    return bEnableSDLedAlarm;
}

static SV_BOOL config_enableStorage()
{
    SV_BOOL bEnableStorage = SV_FALSE;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    if (BOARD_DMS31V2_V2 != BOARD_GetVersion())
    {
        bEnableStorage = SV_TRUE;
    }
#elif (defined(BOARD_ADA32IR))
    if (BOARD_IsCustomer(BOARD_C_ADA32IR_202217))
    {
        bEnableStorage = SV_FALSE;
    }
#endif

    return bEnableStorage;
}

static REC_NORMAL_E config_enNormalRecord()
{
    REC_NORMAL_E enNormalRecord = E_REC_NOR_OFF;

    return enNormalRecord;
}

static uint32 config_u32PreRecDuration()
{
    uint32 u32Duration = 10;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_DBDX))
    {
        u32Duration = 10;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_111219))
    {
        u32Duration = 5;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        u32Duration = 3;
    }

    return u32Duration;
}

static uint32 config_u32PostRecDuration()
{
    uint32 u32Duration = 10;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_DBDX))
    {
        u32Duration = 20;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_111219))
    {
        u32Duration = 5;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
    {
        u32Duration = 3;
    }

    return u32Duration;
}

static uint32 config_u32RecFileLen()
{
    uint32 u32RecFileLen = 5;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        u32RecFileLen = 10;
    }

    return u32RecFileLen;
}

static SV_BOOL config_bEnableGPStime()
{
    SV_BOOL bEnableGPStime = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        bEnableGPStime = SV_FALSE;
    }

    return bEnableGPStime;
}

static sint32 config_s32UTChour()
{
    sint32 s32UTChour = 8;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_VUE))
    {
        s32UTChour = 1;
    }
    else if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018))
    {
        s32UTChour = 3;
    }

    return s32UTChour;
}

static sint32 config_s32UTCminute()
{
    sint32 s32UTCminute = 0;
    return s32UTCminute;
}

static SV_BOOL config_bNtpEnable()
{
    SV_BOOL bNtpEnable = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_202018) || BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL))
    {
        bNtpEnable = SV_TRUE;
    }

    return bNtpEnable;
}

static uint32 config_u32NtpInterval()
{
    uint32 u32NtpInterval = 30;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL))
    {
        u32NtpInterval = 1440;
    }

    return u32NtpInterval;
}

static char *config_u8IpAddr()
{
    char *IpAddr = NULL;
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_200001) || BOARD_IsCustomer(BOARD_C_ADA32V3_200001)))
    {
        IpAddr = "*************";
    }
    return IpAddr;
}

static char *config_u8NtpServer()
{
    char *pszNtpServer = "**************";
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL))
    {
        pszNtpServer = "************";
    }

    return pszNtpServer;
}

static char *config_u8RecFileType()
{
    char *pszRecFileType = "MP4";
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL) || BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159() \
        || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        pszRecFileType = "AVI";
    }

    return pszRecFileType;
}

static char *config_u8PdsCanid()
{
    char *pszPdsCanid = "0x18fad0fc";
    #if 0
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_R151) && BOARD_IsSVersion(BOARD_S_H_3M6)) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        pszPdsCanid = "0x19fad0fc";
    }
    else if (BOARD_IsADA38_R159())
    {
        pszPdsCanid = "0x1afad0fc";
    }
    #endif
    if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
        pszPdsCanid = "0x18FFD0B6";
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32IR_100249))
    {
        pszPdsCanid = "0x18fa9c59";
    }

    return pszPdsCanid;
}

static sint32 config_s32CanBaudrate()
{
    sint32 s32CanBaudrate = 500;
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201244) || BOARD_IsCustomer(BOARD_C_ADA32E1_200946) || BOARD_IsCustomer(BOARD_C_ADA32IR_100249))
    {
        s32CanBaudrate = 250;
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32V2_111573))
    {
        s32CanBaudrate = 125;
    }

    return s32CanBaudrate;
}

static CAN_FORMAT_E config_s32CanFormat()
{
    CAN_FORMAT_E enFrameFormat = E_CAN_EXTENDED;

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_111573))
    {
        enFrameFormat = E_CAN_STANDARD;
    }

    return enFrameFormat;
}


static char *config_u8PdsExtCanid()
{
    char *pszPdsExtCanid = "0x18fae0fc";
    if ((BOARD_IsCustomer(BOARD_C_ADA32V2_R151) && BOARD_IsSVersion(BOARD_S_H_3M6)) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
    {
        pszPdsExtCanid = "0x19fae0fc";
    }
    else if (BOARD_IsADA38_R159())
    {
        pszPdsExtCanid = "0x1afae0fc";
    }
    else if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
        pszPdsExtCanid = "0x18FFD0B6";
    }

    return pszPdsExtCanid;
}

static CAN_PROTOCOL_VER_E config_enCanProtocolVer()
{
    CAN_PROTOCOL_VER_E enCanProtocolVer = CAN_PROTOCOL_VERSION_0;
    if (BOARD_IsCustomer(BOARD_C_DMS31V2_GBS) || BOARD_IsCustomer(BOARD_C_ADA32V2_R151) || BOARD_IsADA38_R159()
        || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR)
        || BOARD_DMS31V2_V2 == BOARD_GetVersion())
    {
        enCanProtocolVer = CAN_PROTOCOL_VERSION_1;
    }

    return enCanProtocolVer;
}


/* 系统语言配置 */
static int config_enLang()
{
    int enLang = LANG_EN;
    if(BOARD_IsCustomer(BOARD_C_ADA32V2_WXKY) || BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI) || BOARD_IsCustomer(BOARD_C_DMS31V2_BJSL) || BOARD_IsCustomer(BOARD_C_DMS31V2_111371) || BOARD_IsCustomer(BOARD_C_DMS31V2_111396))
    {
        enLang = LANG_CN;
    }
    else if(BOARD_IsCustomer(BOARD_C_DMS31V2_ACR) || BOARD_IsCustomer(BOARD_C_DMS31V2_CREARE) || BOARD_IsCustomer(BOARD_C_DMS31V2_201614) || BOARD_IsCustomer(BOARD_C_DMS31V2_202315))
    {
        enLang = LANG_PT;
    }
    else if(BOARD_IsCustomer(BOARD_C_DMS31V2_FCS) || BOARD_IsCustomer(BOARD_C_ADA32V2_FCS))
    {
        enLang = LANG_RU;
    }
    else if(BOARD_IsCustomer(BOARD_C_DMS31V2_WFT) || BOARD_IsCustomer(BOARD_C_DMS31V2_201019) || BOARD_IsCustomer(BOARD_C_DMS31V2_GAMONT) || BOARD_IsCustomer(BOARD_C_DMS31V2_ABASTIBLE))
    {
        enLang = LANG_ES;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_202461))
    {
        enLang = LANG_FRA;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_202319))
    {
        enLang = LANG_ES;
    }
#if (defined(BOARD_ADA32IR))
    else if(BOARD_IsCustomer(BOARD_C_ADA32IR_202461))
    {
        enLang = LANG_FRA;
    }
#endif
    else if(BOARD_IsCustomer(BOARD_C_HDW845V1_202461))
    {
        enLang = LANG_FRA;
    }
    else if(BOARD_IsCustomer(BOARD_C_DMS31V2_202742))
    {
        enLang = LANG_FI;
    }
    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
    {
        enLang = LANG_CN;
    }
    return enLang;
}

/* yuv协议配置 */
static SV_BOOL config_bYuvProtocol()
{
    SV_BOOL bYuvProtocol = SV_FALSE;
    if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393) || BOARD_IsCustomer(BOARD_C_DMS31V2_KS))
    {
        bYuvProtocol = SV_TRUE;
    }
    return bYuvProtocol;
}


static SV_BOOL config_bAlgEnable()
{
    SV_BOOL bAlgEnable = SV_TRUE;
    if (BOARD_IsCustomer(BOARD_C_ADA32IR_100393) || BOARD_IsHardware(BOARD_S_ADA32E1_NOALG))
    {
        bAlgEnable = SV_FALSE;
    }
    return bAlgEnable;
}

#endif

#endif
