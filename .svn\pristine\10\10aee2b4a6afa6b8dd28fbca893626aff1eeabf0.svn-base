#
# Auto dependency checker
#
ifndef SRCS
SRCS    := $(wildcard *.c)
endif

ifndef SRCPPS
SRCPPS    := $(wildcard *.cpp)
endif

OBJS    := $(SRCS:%.c=%.o)
OBJS	+= $(SRCPPS:%.cpp=%.o)

# Add for libsigtracer.so
ifndef _TARGET_DIR_
_TARGET_DIR_ = $(USRLIB_PATH)
endif

ifneq ($(findstring .so,$(TARGET_LIB)), )
CFLAGS += -fPIC -mlong-calls -nostdlib
CPPFLAGS += -fPIC -mlong-calls -nostdlib
endif

CPPFLAGS += -fpermissive
CPPFLAGS := $(CPPFLAGS:-std%c99=)
CPPFLAGS := $(CPPFLAGS:-Wstrict-prototypes=)

.PHONY:	clean

ifndef BOARD
perr:
	@$(ECHO) --------------------------------------
	@$(ECHO) please use command: "make menu"
	@$(ECHO) --------------------------------------
endif

ifdef IGNORE_THIS_BUILD
ignore:
	@$(ECHO) [ignore build] module:$(IGNORE_THIS_BUILD) board:$(BOARD) path:$(PWD) 
endif

$(_TARGET_DIR_)/$(TARGET_LIB):$(TARGET_LIB)
	@$(MKDIR) -p $(_TARGET_DIR_)
	@$(CP) -uv $(TARGET_LIB) $@
	
ifeq ($(findstring .so,$(TARGET_LIB)), )
$(TARGET_LIB):$(OBJS) $(EXT_OBJS)
	@$(ECHO) --------- make static lib ----------
	$(AR) $(AR_FLAGS) $@ $^
else
$(TARGET_LIB):
	@$(ECHO) --------- make dynamic lib ----------
ifeq ($(SRCPPS), )
	$(CC) $(SRCS) $(CFLAGS) -shared -o $@
else
	$(CXX) $(SRCS) $(SRCPPS) $(CPPFLAGS) -shared -o $@ 
endif
endif

clean:
	@$(RM) $(MAKE_LOG_FILE)
	@$(RM) $(DEPEND_FILE_NAME)
	@$(RM) $(TARGET_LIB)
	@$(RM) $(_TARGET_DIR_)/$(TARGET_LIB)
	@$(RM) $(OBJS)

$(DEPEND_FILE_NAME):$(SRCS) $(SRCPPS)
ifndef IGNORE_THIS_BUILD
	@$(RM)	$@
ifneq ($(CROSS_COMPILE), no_default_cross_compile_)
ifneq ($(SRCS), )
	$(SV_ECHO)for x in $(SRCS);	\
	do			\
		$(CC) -MM -MT "$${x%.c}.o" $(CFLAGS) $$x >> $@;\
	done
endif

ifneq ($(SRCPPS), )
	$(SV_ECHO)for x in $(SRCPPS);	\
	do			\
		$(CXX) -MM -MT "$${x%.cpp}.o" $(CPPFLAGS) $$x >> $@;\
	done	
endif
endif
endif

include $(BUILD_MK)

ifneq "$(MAKECMDGOALS)" "clean"
sinclude $(DEPEND_FILE_NAME)
endif
