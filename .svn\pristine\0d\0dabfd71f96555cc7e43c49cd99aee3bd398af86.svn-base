/******************************************************************************
Copyright (C) 2018-2020 广州敏视数码科技有限公司版权所有.

文件名：mpp_bmp.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2018-06-07

文件功能描述: 媒体层位图模块(沿用海思代码)

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>

#include "print.h"
#include "mpp_bmp.h"

OSD_COMP_INFO s_OSDCompInfo[OSD_COLOR_FMT_BUTT] = {{0, 4, 4, 4},   /*RGB444*/
    {4, 4, 4, 4},   /*ARGB4444*/
    {0, 5, 5, 5},   /*RGB555*/
    {0, 5, 6, 5},   /*RGB565*/
    {1, 5, 5, 5},   /*ARGB1555*/
    {0, 0, 0, 0},   /*RESERVED*/
    {0, 8, 8, 8},   /*RGB888*/
    {8, 8, 8, 8}    /*ARGB8888*/
};
static inline uint16 OSD_MAKECOLOR_U16(uint8 r, uint8 g, uint8 b, OSD_COMP_INFO compinfo)
{
    uint8 r1, g1, b1;
    uint16 pixel = 0;
    uint32 tmp = 15;

    r1 = g1 = b1 = 0;
    r1 = r >> (8 - compinfo.rlen);
    g1 = g >> (8 - compinfo.glen);
    b1 = b >> (8 - compinfo.blen);
    while (compinfo.alen)
    {
        pixel |= (1 << tmp);
        tmp --;
        compinfo.alen--;
    }

    pixel |= (r1 | (g1 << compinfo.blen) | (b1 << (compinfo.blen + compinfo.glen)));
    return pixel;
}

sint32 GetBmpInfo(const char* filename,     OSD_BITMAPFILEHEADER*  pBmpFileHeader
                  , OSD_BITMAPINFO* pBmpInfo)
{
    FILE* pFile;

    uint16    bfType;

    if (NULL == filename)
    {
        print_level(SV_ERROR, "OSD_LoadBMP: filename=NULL\n");
        return -1;
    }

    if ((pFile = fopen((char*)filename, "rb")) == NULL)
    {
        print_level(SV_ERROR, "Open file faild:%s!\n", filename);
        return -1;
    }

    if (fread(&bfType, 1, sizeof(bfType), pFile) != sizeof(bfType))
    {
		print_level(SV_ERROR, "fread file failed:%s!\n", filename);
        fclose(pFile);
        return -1;
	}
    if (bfType != 0x4d42)
    {
        print_level(SV_ERROR, "not bitmap file\n");
        fclose(pFile);
        return -1;
    }

    if (fread(pBmpFileHeader, 1, sizeof(OSD_BITMAPFILEHEADER), pFile) != sizeof(OSD_BITMAPFILEHEADER))
    {
		print_level(SV_ERROR, "fread OSD_BITMAPFILEHEADER failed:%s!\n", filename);
        fclose(pFile);
        return -1;
	}
    if (fread(pBmpInfo, 1, sizeof(OSD_BITMAPINFO), pFile) != sizeof(OSD_BITMAPINFO))
    {
		print_level(SV_ERROR, "fread OSD_BITMAPINFO failed:%s!\n", filename);
        fclose(pFile);
        return -1;
	}
    fclose(pFile);

    return 0;
}

int LoadBMP(const char* filename, OSD_LOGO_T* pVideoLogo)
{
    FILE* pFile;
    uint16  i, j;

    uint32  w, h;
    uint16 Bpp;
    uint16 dstBpp;

    OSD_BITMAPFILEHEADER  bmpFileHeader;
    OSD_BITMAPINFO            bmpInfo;

    uint8* pOrigBMPBuf;
    uint8* pRGBBuf;
    uint32 stride;

    if (NULL == filename)
    {
        print_level(SV_ERROR, "OSD_LoadBMP: filename=NULL\n");
        return -1;
    }

    if (GetBmpInfo(filename, &bmpFileHeader, &bmpInfo) < 0)
    {
        return -1;
    }

    Bpp = bmpInfo.bmiHeader.biBitCount / 8;
    if (Bpp < 2)
    {
        /* only support 1555.8888  888 bitmap */
        print_level(SV_ERROR, "bitmap format not supported!\n");
        return -1;
    }

    if (bmpInfo.bmiHeader.biCompression != 0)
    {
        print_level(SV_ERROR, "not support compressed bitmap file!\n");
        return -1;
    }

    if (bmpInfo.bmiHeader.biHeight < 0)
    {
        print_level(SV_ERROR, "bmpInfo.bmiHeader.biHeight < 0\n");
        return -1;
    }

    if ( (pFile = fopen((char*)filename, "rb")) == NULL)
    {
        print_level(SV_ERROR, "Open file faild:%s!\n", filename);
        return -1;
    }

    pVideoLogo->width = (uint16)bmpInfo.bmiHeader.biWidth;
    pVideoLogo->height = (uint16)((bmpInfo.bmiHeader.biHeight > 0) ? bmpInfo.bmiHeader.biHeight : (-bmpInfo.bmiHeader.biHeight));
    w = pVideoLogo->width;
    h = pVideoLogo->height;

    stride = w * Bpp;
#if 1
    if (stride % 4)
    {
        stride = (stride & 0xfffc) + 4;
    }
#endif
    /* RGB8888 or RGB1555 */
    pOrigBMPBuf = (uint8*)malloc(h * stride);
    if (NULL == pOrigBMPBuf)
    {
        print_level(SV_ERROR, "not enough memory to malloc!\n");
        fclose(pFile);
        return -1;
    }

    pRGBBuf = pVideoLogo->pRGBBuffer;

    if (fseek(pFile, bmpFileHeader.bfOffBits, 0))
    {
        print_level(SV_ERROR, "fseek failed!\n");
        fclose(pFile);
        free(pOrigBMPBuf);
        pOrigBMPBuf = NULL;
        return -1;
    }
    if (fread(pOrigBMPBuf, 1, (uint32)(h * stride), pFile) != (uint32)(h * stride) )
    {
        print_level(SV_ERROR, "fread error!line:%d\n", __LINE__);
        perror("fread:");
    }

    if (Bpp > 2)
    {
        dstBpp = 4;
    }
    else
    {
        dstBpp = 2;
    }

    if (0 == pVideoLogo->stride)
    {
        pVideoLogo->stride = pVideoLogo->width * dstBpp;
    }

    for (i = 0; i < h; i++)
    {
        for (j = 0; j < w; j++)
        {
            memcpy(pRGBBuf + i * pVideoLogo->stride + j * dstBpp, pOrigBMPBuf + ((h - 1) - i)*stride + j * Bpp, Bpp);

            if (dstBpp == 4)
            {
                //*(pRGBBuf + i*pVideoLogo->stride + j*dstbpp + 3) = random()&0xff; /*alpha*/
                *(pRGBBuf + i * pVideoLogo->stride + j * dstBpp + 3) = 0x80; /*alpha*/
            }
        }

    }

    free(pOrigBMPBuf);
    pOrigBMPBuf = NULL;

    fclose(pFile);
    return 0;
}

int LoadBMPEx(const char* filename, OSD_LOGO_T* pVideoLogo, OSD_COLOR_FMT_E enFmt)
{
    FILE* pFile;
    uint16  i, j;

    uint32  w, h;
    uint16 Bpp;

    OSD_BITMAPFILEHEADER  bmpFileHeader;
    OSD_BITMAPINFO            bmpInfo;

    uint8* pOrigBMPBuf;
    uint8* pRGBBuf;
    uint32 stride;
    uint8 r, g, b;
    uint8* pStart;
    uint16* pDst;

    if (NULL == filename)
    {
        print_level(SV_ERROR, "OSD_LoadBMP: filename=NULL\n");
        return -1;
    }

    if (GetBmpInfo(filename, &bmpFileHeader, &bmpInfo) < 0)
    {
        return -1;
    }

    Bpp = bmpInfo.bmiHeader.biBitCount / 8;
    if (Bpp < 2)
    {
        /* only support 1555.8888  888 bitmap */
        print_level(SV_ERROR, "bitmap format not supported!\n");
        return -1;
    }

    if (bmpInfo.bmiHeader.biCompression != 0)
    {
        print_level(SV_ERROR, "not support compressed bitmap file!\n");
        return -1;
    }

    if (bmpInfo.bmiHeader.biHeight < 0)
    {
        print_level(SV_ERROR, "bmpInfo.bmiHeader.biHeight < 0\n");
        return -1;
    }

    if ( (pFile = fopen((char*)filename, "rb")) == NULL)
    {
        print_level(SV_ERROR, "Open file faild:%s!\n", filename);
        return -1;
    }

    pVideoLogo->width = (uint16)bmpInfo.bmiHeader.biWidth;
    pVideoLogo->height = (uint16)((bmpInfo.bmiHeader.biHeight > 0) ? bmpInfo.bmiHeader.biHeight : (-bmpInfo.bmiHeader.biHeight));
    w = pVideoLogo->width;
    h = pVideoLogo->height;

    stride = w * Bpp;
#if 1
    if (stride % 4)
    {
        stride = (stride & 0xfffc) + 4;
    }
#endif

    /* RGB8888 or RGB1555 */
    pOrigBMPBuf = (uint8*)malloc(h * stride);
    if (NULL == pOrigBMPBuf)
    {
        print_level(SV_ERROR, "not enough memory to malloc!\n");
        fclose(pFile);
        return -1;
    }

    pRGBBuf = pVideoLogo->pRGBBuffer;

    if (fseek(pFile, bmpFileHeader.bfOffBits, 0))
    {
		print_level(SV_ERROR, "fseek failed!\n");
        fclose(pFile);
		free(pOrigBMPBuf);
    	pOrigBMPBuf = NULL;
        return -1;
	}
    if (fread(pOrigBMPBuf, 1, (uint32)(h * stride), pFile) != (uint32)(h * stride) )
    {
        print_level(SV_ERROR, "fread (%d*%d)error!line:%d\n", h, stride, __LINE__);
        perror("fread:");
    }

    if (enFmt >= OSD_COLOR_FMT_RGB888)
    {
        pVideoLogo->stride = pVideoLogo->width * 4;
    }
    else
    {
        pVideoLogo->stride = pVideoLogo->width * 2;
    }

    for (i = 0; i < h; i++)
    {
        for (j = 0; j < w; j++)
        {
            if (Bpp == 3) /*.....*/
            {
                switch (enFmt)
                {
                    case OSD_COLOR_FMT_RGB444:
                    case OSD_COLOR_FMT_RGB555:
                    case OSD_COLOR_FMT_RGB565:
                    case OSD_COLOR_FMT_RGB1555:
                    case OSD_COLOR_FMT_RGB4444:
                        /* start color convert */
                        pStart = pOrigBMPBuf + ((h - 1) - i) * stride + j * Bpp;
                        pDst = (uint16*)(pRGBBuf + i * pVideoLogo->stride + j * 2);
                        r = *(pStart);
                        g = *(pStart + 1);
                        b = *(pStart + 2);
                        *pDst = OSD_MAKECOLOR_U16(r, g, b, s_OSDCompInfo[enFmt]);
                        break;

                    case OSD_COLOR_FMT_RGB888:
                    case OSD_COLOR_FMT_RGB8888:
                        memcpy(pRGBBuf + i * pVideoLogo->stride + j * 4, pOrigBMPBuf + ((h - 1) - i)*stride + j * Bpp, Bpp);
                        *(pRGBBuf + i * pVideoLogo->stride + j * 4 + 3) = 0xff; /*alpha*/
                        break;

                    default:
                        print_level(SV_ERROR, "file(%s), line(%d), no such format!\n", __FILE__, __LINE__);
                        break;
                }
            }
            else if ((Bpp == 2) || (Bpp == 4)) /*..............*/
            {
                memcpy(pRGBBuf + i * pVideoLogo->stride + j * Bpp, pOrigBMPBuf + ((h - 1) - i)*stride + j * Bpp, Bpp);
            }

        }

    }

    free(pOrigBMPBuf);
    pOrigBMPBuf = NULL;

    fclose(pFile);
    return 0;
}


int LoadBMPCanvas(const char* filename, OSD_LOGO_T* pVideoLogo, OSD_COLOR_FMT_E enFmt)
{
    FILE* pFile;
    uint16  i, j;

    uint32  w, h;
    uint16 Bpp;

    OSD_BITMAPFILEHEADER  bmpFileHeader;
    OSD_BITMAPINFO            bmpInfo;

    uint8* pOrigBMPBuf;
    uint8* pRGBBuf;
    uint32 stride;
    uint8 r, g, b;
    uint8* pStart;
    uint16* pDst;

    if (NULL == filename)
    {
        print_level(SV_ERROR, "OSD_LoadBMP: filename=NULL\n");
        return -1;
    }

    if (GetBmpInfo(filename, &bmpFileHeader, &bmpInfo) < 0)
    {
        return -1;
    }

    Bpp = bmpInfo.bmiHeader.biBitCount / 8;
    if (Bpp < 2)
    {
        /* only support 1555.8888  888 bitmap */
        print_level(SV_ERROR, "bitmap format not supported!\n");
        return -1;
    }

    if (bmpInfo.bmiHeader.biCompression != 0)
    {
        print_level(SV_ERROR, "not support compressed bitmap file!\n");
        return -1;
    }

    if (bmpInfo.bmiHeader.biHeight < 0)
    {
        print_level(SV_ERROR, "bmpInfo.bmiHeader.biHeight < 0\n");
        return -1;
    }

    if ( (pFile = fopen((char*)filename, "rb")) == NULL)
    {
        print_level(SV_ERROR, "Open file faild:%s!\n", filename);
        return -1;
    }

    w = (uint16)bmpInfo.bmiHeader.biWidth;
    h = (uint16)((bmpInfo.bmiHeader.biHeight > 0) ? bmpInfo.bmiHeader.biHeight : (-bmpInfo.bmiHeader.biHeight));

    stride = w * Bpp;

#if 1
    if (stride % 4)
    {
        stride = (stride & 0xfffc) + 4;
    }
#endif

    /* RGB8888 or RGB1555 */
    pOrigBMPBuf = (uint8*)malloc(h * stride);
    if (NULL == pOrigBMPBuf)
    {
        print_level(SV_ERROR, "not enough memory to malloc!\n");
        fclose(pFile);
        return -1;
    }

    pRGBBuf = pVideoLogo->pRGBBuffer;

    if (stride > pVideoLogo->stride)
    {
        print_level(SV_ERROR, "Bitmap's stride(%d) is bigger than canvas's stide(%d). Load bitmap error!\n", stride, pVideoLogo->stride);
        fclose(pFile);
		free(pOrigBMPBuf);
        return -1;
    }

    if (h > pVideoLogo->height)
    {
        print_level(SV_ERROR, "Bitmap's height(%d) is bigger than canvas's height(%d). Load bitmap error!\n", h, pVideoLogo->height);
		fclose(pFile);
		free(pOrigBMPBuf);
        return -1;
    }

    if (w > pVideoLogo->width)
    {
        print_level(SV_ERROR, "Bitmap's width(%d) is bigger than canvas's width(%d). Load bitmap error!\n", w, pVideoLogo->width);
		fclose(pFile);
		free(pOrigBMPBuf);
        return -1;
    }

    if (fseek(pFile, bmpFileHeader.bfOffBits, 0))
	{
        print_level(SV_ERROR, "fseek error!\n");
		fclose(pFile);
		free(pOrigBMPBuf);
        return -1;
    }	
    if (fread(pOrigBMPBuf, 1, (uint32)(h * stride), pFile) != (uint32)(h * stride) )
    {
        print_level(SV_ERROR, "fread (%d*%d)error!line:%d\n", h, stride, __LINE__);
        perror("fread:");
    }

    for (i = 0; i < h; i++)
    {
        for (j = 0; j < w; j++)
        {
            if (Bpp == 3) /*.....*/
            {
                switch (enFmt)
                {
                    case OSD_COLOR_FMT_RGB444:
                    case OSD_COLOR_FMT_RGB555:
                    case OSD_COLOR_FMT_RGB565:
                    case OSD_COLOR_FMT_RGB1555:
                    case OSD_COLOR_FMT_RGB4444:
                        /* start color convert */
                        pStart = pOrigBMPBuf + ((h - 1) - i) * stride + j * Bpp;
                        pDst = (uint16*)(pRGBBuf + i * pVideoLogo->stride + j * 2);
                        r = *(pStart);
                        g = *(pStart + 1);
                        b = *(pStart + 2);
                        //print_level(SV_ERROR, "Func: %s, line:%d, Bpp: %d, bmp stride: %d, Canvas stride: %d, h:%d, w:%d.\n",
                        //    __FUNCTION__, __LINE__, Bpp, stride, pVideoLogo->stride, i, j);
                        *pDst = OSD_MAKECOLOR_U16(r, g, b, s_OSDCompInfo[enFmt]);

                        break;

                    case OSD_COLOR_FMT_RGB888:
                    case OSD_COLOR_FMT_RGB8888:
                        memcpy(pRGBBuf + i * pVideoLogo->stride + j * 4, pOrigBMPBuf + ((h - 1) - i)*stride + j * Bpp, Bpp);
                        *(pRGBBuf + i * pVideoLogo->stride + j * 4 + 3) = 0xff; /*alpha*/
                        break;

                    default:
                        print_level(SV_ERROR, "file(%s), line(%d), no such format!\n", __FILE__, __LINE__);
                        break;
                }
            }
            else if ((Bpp == 2) || (Bpp == 4)) /*..............*/
            {
                memcpy(pRGBBuf + i * pVideoLogo->stride + j * Bpp, pOrigBMPBuf + ((h - 1) - i)*stride + j * Bpp, Bpp);
            }

        }

    }

    free(pOrigBMPBuf);
    pOrigBMPBuf = NULL;

    fclose(pFile);
    return 0;
}

char* GetExtName(char* filename)
{
    char* pret = NULL;
    uint32 fnLen;

    if (NULL == filename)
    {
        print_level(SV_ERROR, "filename can't be null!");
        return NULL;
    }

    fnLen = strlen(filename);
    while (fnLen)
    {
        pret = filename + fnLen;
        if (*pret == '.')
        { return (pret + 1); }

        fnLen--;
    }

    return pret;
}


int LoadImage(const char* filename, OSD_LOGO_T* pVideoLogo)
{
    char* ext = GetExtName((char*)filename);

    if (ext != NULL && strcmp(ext, "bmp") == 0)
    {
        if (0 != LoadBMP(filename, pVideoLogo))
        {
            print_level(SV_ERROR, "OSD_LoadBMP error!\n");
            return -1;
        }
    }
    else
    {
        print_level(SV_ERROR, "not supported image file!\n");
        return -1;
    }

    return 0;
}

int LoadImageEx(const char* filename, OSD_LOGO_T* pVideoLogo, OSD_COLOR_FMT_E enFmt)
{
    char* ext = GetExtName((char*)filename);

    if (ext != NULL && strcmp(ext, "bmp") == 0)
    {
        if (0 != LoadBMPEx(filename, pVideoLogo, enFmt))
        {
            print_level(SV_ERROR, "OSD_LoadBMP error!\n");
            return -1;
        }
    }
    else
    {
        print_level(SV_ERROR, "not supported image file!\n");
        return -1;
    }

    return 0;
}


int LoadCanvasEx(const char* filename, OSD_LOGO_T* pVideoLogo, OSD_COLOR_FMT_E enFmt)
{
    char* ext = GetExtName((char*)filename);

    if (ext != NULL && strcmp(ext, "bmp") == 0)
    {
        if (0 != LoadBMPCanvas(filename, pVideoLogo, enFmt))
        {
            print_level(SV_ERROR, "OSD_LoadBMP error!\n");
            return -1;
        }
    }
    else
    {
        print_level(SV_ERROR, "not supported image file!\n");
        return -1;
    }

    return 0;
}


sint32 LoadBitMap2Surface(const char* pszFileName, const OSD_SURFACE_S* pstSurface, uint8* pu8Virt)
{
    OSD_LOGO_T stLogo;
    stLogo.stride = pstSurface->u16Stride;
    stLogo.pRGBBuffer = pu8Virt;

    return LoadImage(pszFileName, &stLogo);
}

sint32 CreateSurfaceByBitMap(const char* pszFileName, OSD_SURFACE_S* pstSurface, uint8* pu8Virt)
{
    OSD_LOGO_T stLogo;
    stLogo.pRGBBuffer = pu8Virt;
    if (LoadImageEx(pszFileName, &stLogo, pstSurface->enColorFmt) < 0)
    {
        print_level(SV_ERROR, "load bmp error!\n");
        return -1;
    }

    pstSurface->u16Height = stLogo.height;
    pstSurface->u16Width = stLogo.width;
    pstSurface->u16Stride = stLogo.stride;

    return 0;
}


sint32 CreateSurfaceByCanvas(const char* pszFileName, OSD_SURFACE_S* pstSurface, uint8* pu8Virt, uint32 u32Width, uint32 u32Height, uint32 u32Stride)
{
    sint32 i, j;
    uint16 *pu16Pixcel = NULL;
    OSD_LOGO_T stLogo;
    stLogo.pRGBBuffer = pu8Virt;
    stLogo.width = u32Width;
    stLogo.height = u32Height;
    stLogo.stride = u32Stride;
    if (LoadCanvasEx(pszFileName, &stLogo, pstSurface->enColorFmt) < 0)
    {
        print_level(SV_ERROR, "load bmp error!\n");
        return -1;
    }

    pu16Pixcel = (uint16 *)pu8Virt;
    for (i = 0; i < u32Height; i++)
    {
        for (j = 0; j < u32Stride/2; j++)
        {
            if ((*pu16Pixcel == 0x7fff)/* || (*pu16Pixcel == 0x0)*/)
            {
                *pu16Pixcel = 0x8000;
                //printf(".");
            }
            /*
            else if (*pu16Pixcel & 0x8000)
            {
                //printf("#");
            }
            else
            {
                //printf("*");
            }
            */
            pu16Pixcel++;
        }
        //printf("\n");
    }

    pstSurface->u16Height = u32Height;
    pstSurface->u16Width = u32Width;
    pstSurface->u16Stride = u32Stride;

    return 0;
}

