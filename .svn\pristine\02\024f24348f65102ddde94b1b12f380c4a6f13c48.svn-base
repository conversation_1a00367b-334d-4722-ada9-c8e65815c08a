/******************************************************************************
Copyright (C) 2021-2023 广州敏视数码科技有限公司版权所有.

文件名：alg.cpp

日期: 2021-08-03

文件功能描述: 定义算法进程入口程序

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <stddef.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/fcntl.h>
#include <sys/un.h>

#include <pthread.h>
#include <ctype.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>


#include "print.h"
#include "common.h"
#include "config.h"
#include "../../include/board.h"
#include "op.h"
#include "msg.h"
#include "log.h"
#include "plug.h"
#include "dmm.h"
#include "pd.h"
#include "apc.h"
#include "adas.h"
#include "alarm.h"
#include "unsocket.h"
#include "gps.h"
#include "zoom.h"

int ipsys_log_level = SV_DEBUG;
#if ALG_MUTLIT_BUFFER
// 各物理通道的Media Buffer的文件描述符 // The file descriptor of the Media Buffer of each physical channel
int g_as32MediaBufFd[4][ALG_MULTI_BUF_NUM] =
    {
        {-1, -1, -1},
        {-1, -1, -1},
        {-1, -1, -1},
        {-1, -1, -1}
    };
#else
int g_as32MediaBufFd[4] = {-1, -1, -1, -1}; // 各物理通道的Media Buffer的文件描述符 // The file descriptor of the Media Buffer of each physical channel
#endif
int g_s32SocketFd = -1;
pthread_mutex_t g_Mutex;            // 锁住计算资源,保证同一时刻只跑一个算法  // Lock computing resources to ensure that only one algorithm is run at the same time
int g_alg_use_plug=0;               // 是否使用算法插件化

sint32 ALG_Init()
{
    sint32 s32Ret;
    s32Ret = pthread_mutex_init(&g_Mutex, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "pthread_mutex_Init fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    return SV_SUCCESS;
}

sint32 ALG_Fini()
{
    sint32 s32Ret;
    s32Ret = pthread_mutex_destroy(&g_Mutex);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "pthread_mutex_Init fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    return SV_SUCCESS;
}

sint32 ALG_Calculate_Lock()
{
    sint32 s32Ret;
    s32Ret = pthread_mutex_lock(&g_Mutex);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "pthread_mutex_lock fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    return SV_SUCCESS;
}

sint32 ALG_Calculate_unLock()
{
    sint32 s32Ret;
    s32Ret = pthread_mutex_unlock(&g_Mutex);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "pthread_mutex_lock fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    return SV_SUCCESS;
}


static sint32 callbackConfigUpdate(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{

    sint32 s32Ret = 0;
    CFG_MEDIA_PARAM stMediaParam = {0};
    CFG_ALG_PARAM stAlgParam = {0};
    CFG_SYS_PARAM stSysParam = {0};
    ALARM_CFG_PARAM_S stAlarmParam = {0};
    print_level(SV_INFO, "recive: OP_EVENT_CFG_UPDATE\n");

    s32Ret = CONFIG_ReloadFile();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_ReloadFile failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    s32Ret = CONFIG_GetAlgParam(&stAlgParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetAlgParam failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    s32Ret = CONFIG_GetMediaParam(&stMediaParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetMediaParam failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }
    stAlgParam.bImageMirror = stMediaParam.astChnParam[0].bImageMirror;


    s32Ret = CONFIG_GetSystemParam(&stSysParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetAlgParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    stAlarmParam.enLanguage = stSysParam.enLang;
    stAlarmParam.aenPdsAudioType[0] = stAlgParam.stAlgCh1.stPdsParam.enAudioType;
    stAlarmParam.aenPdsAudioType[1] = stAlgParam.stAlgCh2.stPdsParam.enAudioType;
    stAlarmParam.aenPdsAudioType[2] = stAlgParam.stAlgCh3.stPdsParam.enAudioType;
    stAlarmParam.enAdasAudioType = stAlgParam.stAlgCh1.stAdasParam.enAudioType;
    stAlarmParam.enDmsAudioType = stAlgParam.stAlgCh2.stDmsParam.enAudioType;
    stAlarmParam.s32AudioVolume = stAlgParam.s32AudioVolume;
    s32Ret = ALARM_ConfigSet(&stAlarmParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "DMM_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32N1))
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    if(BOARD_GetVersion() != BOARD_ADA47V1_V1 && BOARD_GetVersion() != BOARD_ADA47V1_V3 && !g_alg_use_plug)
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion()&&(BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M)))
#endif
    {
        s32Ret = DMM_ConfigSet(&stAlgParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_ConfigSet failed. [err=%#x]\n", s32Ret);
            return MSG_DEFAULT_FAIL;
        }
    }
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4))
    if(BOARD_GetVersion() != BOARD_ADA47V1_V2 && !g_alg_use_plug && BOARD_GetVersion() != BOARD_ADA32N1_V3)
    {
        s32Ret = PD_ConfigSet(&stAlgParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "PD_ConfigSet failed. [err=%#x]\n", s32Ret);
            return MSG_DEFAULT_FAIL;
        }
    }
#endif

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA32N1))
#if (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V3 == BOARD_GetVersion())
#endif
    {
        s32Ret = APC_ConfigSet(&stAlgParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_ConfigSet failed. [err=%#x]\n", s32Ret);
            return MSG_DEFAULT_FAIL;
        }
    }
#endif

#if (defined(BOARD_HDW845V1))
        s32Ret = Zoom_ConfigSet(&stAlgParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Zoom_ConfigSet failed. [err=%#x]\n", s32Ret);
            return MSG_DEFAULT_FAIL;
        }
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4))
    s32Ret = PD_RotateSet((stMediaParam.astChnParam[0].enRotateAngle == SV_ROTATION_90 || stMediaParam.astChnParam[0].enRotateAngle == SV_ROTATION_270) ? SV_TRUE : SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "PD_RotateSet failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }
#endif

    return SV_SUCCESS;
}

static sint32 callbackCalibration(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = DMM_RES_SUCCESS;
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1)|| defined(BOARD_ADA32N1))
    DMM_RUN_E enRunStat;

    print_level(SV_INFO, "recive: OP_REQ_ALG_CALIBRATION %d\n", pstMsgPkt->stMsg.s32Param);
    enRunStat = DMM_GetRunStatus();
    switch (pstMsgPkt->stMsg.s32Param)
    {
        case 0:
            if (enRunStat == DMM_RUN_CALIBRATION_PRE || enRunStat == DMM_RUN_CALIBRATION)
            {
                DMM_SetRunStatus(DMM_RUN_DETECTION);
            }
            break;
        case 1:
            if (enRunStat != DMM_RUN_CALIBRATION_PRE)
            {
                DMM_SetRunStatus(DMM_RUN_CALIBRATION_PRE);
            }
            break;
        case 2:
            if (enRunStat != DMM_RUN_CALIBRATION)
            {
                sint32 s32Result;
                sint32 s32QueryTimes = 0;
                DMM_SetRunStatus(DMM_RUN_CALIBRATION);

                while(DMM_RUN_CALIBRATION == DMM_GetRunStatus() && DMM_GetRunResult(NULL) == DMM_RES_RUNNING)
                {
                    if(s32QueryTimes > 140)
                    {
                        print_level(SV_WARN, "calibration timeout!\n");
                        break;
                    }
                    s32QueryTimes++;
                    sleep_ms(100);
                }

                s32Ret = DMM_GetRunResult(NULL);
            }
            break;
    }
#endif

    return s32Ret;
}


static sint32 callbackQRCodeCalibration(MSG_PACKET_S * pstMsgPkt, MSG_PACKET_S * pstRetPkt)
{
#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4))
    sint32 s32Ret = 0, i;
    sint32 s32Chn = 0;
    MSG_QRCode_CFG *pstQRCodeCfg = (MSG_QRCode_CFG *)pstMsgPkt->pu8Data;
    //s32Ret = pd_QRCodeCaliberation(s32Chn, pstQRCodeCfg->QRCode1, pstQRCodeCfg->QRCode2);
    s32Ret = PD_SetRunStatus(PD_RUN_CALIBRATION);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "PD_SetRunStatus failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    sint32 s32Time = 20;
    while (s32Time > 0)
    {
        if (PD_RES_SUCCESS == PD_GetRunResult())
        {
            return SV_SUCCESS;
        }
        else if (PD_RES_FAILURE == PD_GetRunResult())
        {
            return SV_FAILURE;
        }

        sleep_ms(500);
        s32Time--;
    }

#endif
    return SV_FAILURE;
}

static sint32 callbackPointImageToReal(MSG_PACKET_S * pstMsgPkt, MSG_PACKET_S * pstRetPkt)
{
#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4))
    sint32 s32Ret = 0, i;
    MSG_POINT_IMAGE *pstPointImage = (MSG_POINT_IMAGE *)pstMsgPkt->pu8Data;
    MSG_POINT_REAL  *pstPointReal = (MSG_POINT_REAL *)pstRetPkt->pu8Data;

    for(i = 0; i < pstPointImage->u32Num; i++)
    {
        s32Ret = pd_PointImageToReal(&pstPointImage->stPoint[i], &pstPointReal->stPoint[i], pstMsgPkt->stMsg.s32Param);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "pd_PointImageToReal failed. [err=%#x]\n", s32Ret);
        }
    }

    pstRetPkt->u32Size = sizeof(MSG_POINT_REAL);
#endif
    return SV_SUCCESS;
}

static sint32 callbackPointRealToImage(MSG_PACKET_S * pstMsgPkt, MSG_PACKET_S * pstRetPkt)
{
#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4))
    sint32 s32Ret = 0, i;
    MSG_POINT_REAL  *pstPointReal = (MSG_POINT_REAL *)pstMsgPkt->pu8Data;
    MSG_POINT_IMAGE *pstPointImage = (MSG_POINT_IMAGE *)pstRetPkt->pu8Data;

    for(i = 0; i < pstPointImage->u32Num; i++)
    {
        s32Ret = pd_PointRealToImage(&pstPointReal->stPoint[i], &pstPointImage->stPoint[i], pstMsgPkt->stMsg.s32Param);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "pd_PointRealToImage failed. [err=%#x]\n", s32Ret);
        }
    }

    pstRetPkt->u32Size = sizeof(MSG_POINT_IMAGE);
#endif
    return SV_SUCCESS;
}

static sint32 callbackIrADASCalibrate(MSG_PACKET_S * pstMsgPkt, MSG_PACKET_S * pstRetPkt)
{
    sint32 s32Ret = 0, i;
#if (defined(BOARD_ADA32IR))
    MSG_ADASCode_CFG  *pstADASCaliCfg = (MSG_ADASCode_CFG *)pstMsgPkt->pu8Data;

    sint32 s32Chn = pstADASCaliCfg->s32Chn;
    sint32 s32CalibrationHeight = pstADASCaliCfg->s32CalibrationHeight;
    float  fCalibrationGuides = pstADASCaliCfg->fCalibrationGuides;

    s32Ret = pd_Ir_ADAS_Calibrate(s32Chn, s32CalibrationHeight, fCalibrationGuides);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "pd_Ir_ADAS_Calibrate failed\r\n");
    }
#endif
    return SV_SUCCESS;
}


static sint32 callbackAddUser(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1)|| defined(BOARD_ADA32N1))
    sint32 s32Ret = 0, i;
    DMM_RES_E enRes = 0;
    USER_INFO_S stUserInfo = {0};

    ALARM_CFG_PARAM_S stAlarmParam = {0};

    if (DMM_IsFatigueOnly())
    {
        return SV_FAILURE;
    }

    print_level(SV_INFO, "recive: OP_REQ_ALG_FR_ADDUSER: %s\n", (char*)pstMsgPkt->pu8Data);
    s32Ret = DMM_RegisterUser((char*)pstMsgPkt->pu8Data);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "DMM_RegisterUser failed. [err=%#x]\n", s32Ret);
        if (ERR_ILLEGAL_PARAM == s32Ret)
        {
            return MSG_RANGE_OUT;
        }
        else if (ERR_EXIST == s32Ret)
        {
            return MSG_RES_EXIST;
        }
        else if (ERR_BUF_FULL == s32Ret)
        {
            return MSG_NO_ENOUGH_MEMORY;
        }
        else
        {
            return MSG_DEFAULT_FAIL;
        }
    }

    DMM_SetRunStatus(DMM_RUN_REGISTER);
    for (i = 0; i < 20; i++)
    {
        enRes = DMM_GetRunResult(&stUserInfo);
        if (DMM_RES_RUNNING != enRes)
        {
            break;
        }
        sleep_ms(1000);
    }
    if (i >= 20 || enRes != DMM_RES_SUCCESS)
    {
        DMM_DeleteUser((char*)pstMsgPkt->pu8Data);
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

static sint32 callbackDelUser(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1)|| defined(BOARD_ADA32N1))
    sint32 s32Ret = 0;
    USER_LIST_S stDelUserList = {0};

    print_level(SV_INFO, "recive: OP_REQ_ALG_FR_DELUSER, s32Param: %d\n", pstMsgPkt->stMsg.s32Param);

    if (0 == pstMsgPkt->stMsg.s32Param)
    {
        s32Ret = DMM_DeleteUser((char*)pstMsgPkt->pu8Data);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_DeleteUser failed. [err=%#x]\n", s32Ret);
            if (ERR_UNEXIST == s32Ret)
            {
                return MSG_RES_EXIST;
            }
            else
            {
                return MSG_DEFAULT_FAIL;
            }
        }
    }
    else if (1 == pstMsgPkt->stMsg.s32Param)
    {
        s32Ret = DMM_GetUserListDelFormJson(&stDelUserList);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_GetUserListDelFormJson failed.\n");
            return MSG_DEFAULT_FAIL;
        }

        s32Ret = DMM_DeleteUserBatch(&stDelUserList);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_DeleteUserBatch failed. [err=%#x]\n", s32Ret);
            if (ERR_UNEXIST == s32Ret)
            {
                return MSG_RES_EXIST;
            }
            else
            {
                return MSG_DEFAULT_FAIL;
            }
        }
    }
    else
    {
        print_level(SV_ERROR, "not support opcode!\n");
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

static sint32 callbackModifyUser(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1)|| defined(BOARD_ADA32N1))
    sint32 s32Ret = 0;

    print_level(SV_INFO, "recive: OP_REQ_ALG_FR_MODUSER: %s\n", (char*)pstMsgPkt->pu8Data);
    s32Ret = DMM_ModifyUser((char*)pstMsgPkt->pu8Data);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "DMM_DeleteUser failed. [err=%#x]\n", s32Ret);
        if (ERR_UNEXIST == s32Ret)
        {
            return MSG_RES_EXIST;
        }
        else
        {
            return MSG_DEFAULT_FAIL;
        }
    }
#endif

    return SV_SUCCESS;
}


static sint32 callbackGetUsers(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1)|| defined(BOARD_ADA32N1))
    sint32 s32Ret = 0;
    USER_LIST_S *pstUserList = (USER_LIST_S *)pstRetPkt->pu8Data;

    print_level(SV_INFO, "recive: OP_REQ_ALG_FR_GETUSERS\n");
    s32Ret = DMM_GetUserList(pstUserList);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "DMM_GetUserList failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    pstRetPkt->u32Size = sizeof(USER_LIST_S);
#endif

    return SV_SUCCESS;
}

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA32N1))
static sint32 callbackSetApcNum(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    MSG_APC_NUM_S *pstApcNum = (MSG_APC_NUM_S *)pstMsgPkt->pu8Data;
    print_level(SV_INFO, "OP_EVENT_SET_APC_NUM: total:%d, in:%d, out:%d\n", pstApcNum->s32TotalNum, pstApcNum->s32InNum, pstApcNum->s32OutNum);

    APC_SetApcNum(pstApcNum->s32TotalNum, pstApcNum->s32InNum, pstApcNum->s32OutNum);

    return SV_SUCCESS;
}
#endif

#if (defined(BOARD_ADA47V1))
static sint32 callbackAddPlug(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0;

    print_level(SV_INFO, "OP_REQ_ADD_PLUG %s\n", pstMsgPkt->pu8Data);

    s32Ret = PLUG_AddPlug((char *)pstMsgPkt->pu8Data);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "PLUG_AddPlug failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    return SV_SUCCESS;
}

static sint32 callbackDeletePlug(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0;

    print_level(SV_INFO, "OP_REQ_DEL_PLUG %s \n", pstMsgPkt->pu8Data);

    s32Ret = PLUG_DeletePlug((char *)pstMsgPkt->pu8Data);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "PLUG_DeletePlug failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    return SV_SUCCESS;
}

static sint32 callbackUpdatePlug(MSG_PACKET_S *pstMsgPkt,MSG_PACKET_S *pstRetPkt)
{
	sint32 s32Ret = 0;
	MSG_PLUG_UPDATE *pstPulgUpdate = (MSG_PLUG_UPDATE *)pstMsgPkt->pu8Data;

	print_level(SV_INFO,"OP_REQ_UPDATE_PLUG PlugPath:%s fileName:%s \n",pstPulgUpdate->szPlugPath,pstPulgUpdate->szFileName);

	s32Ret = PLUG_UpdatePlug(pstPulgUpdate->szPlugPath,pstPulgUpdate->szFileName);
	if(SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR,"PLUG_UpdatePlug failed.[err=%#x]\n",s32Ret);
		return MSG_DEFAULT_FAIL;
	}
	return SV_SUCCESS;

}

static sint32 callbackSetPlugConfig(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0;

    print_level(SV_INFO, "OP_REQ_SET_PLUG_CONFIG s32Param:%d\n", pstMsgPkt->stMsg.s32Param);

    s32Ret = PLUG_ConfigSet(pstMsgPkt->stMsg.s32Param, (char *)pstMsgPkt->pu8Data);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "PLUG_ConfigSetPLUG_ConfigSet failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    return SV_SUCCESS;
}
#endif

static sint32 callbackLogin(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1)|| defined(BOARD_ADA32N1))
    sint32 s32Ret = 0, i, warnState;
    DMM_RES_E enRes = 0;
    USER_INFO_S stUserInfo = {0};
    USER_INFO_S *pstUserInfo = (USER_INFO_S *)pstRetPkt->pu8Data;

    print_level(SV_INFO, "recive: OP_REQ_ALG_FR_LOGIN\n");
    DMM_SetRunStatus(DMM_RUN_RECOGNITION);
    for (i = 0; i < 15; i++)
    {
        enRes = DMM_GetRunResult(&stUserInfo);
        if (DMM_RES_RUNNING != enRes)
        {
            break;
        }
        sleep_ms(1000);
    }

	if (i >= 20)
	{
		print_level(SV_WARN, "login recognition timeout!\n");
		enRes = DMM_RES_TIMEOUT;
	}

	if (enRes == DMM_RES_SUCCESS)
	{
		warnState = NOTIFY_LNGIN_SUCCESS;
		pstRetPkt->u32Size = sizeof(USER_INFO_S);
		*pstUserInfo = stUserInfo;
	}
	else
	{
		warnState = NOTIFY_LNGIN_FAILED;
	}

	if (DMM_RES_TIMEOUT == enRes)
		return MSG_TIMEOUT_FAIL;
	else if (DMM_RES_FAILURE == enRes)
		return MSG_DATA_UNMATCH;
#endif

    return MSG_SUCCESS_RES;
}
static sint32 callbackSplitNotify(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0;
    MSG_VIDEO_CFG *pstVideoCfg = (MSG_VIDEO_CFG *)pstMsgPkt->pu8Data;
    SPLIT_MODE enSplitMode = pstVideoCfg->enVoSplitMode;
#if (defined(BOARD_ADA32IR))
    s32Ret = PD_SplitSet(enSplitMode);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "callbackSplitNotify failed \n");
    }
#endif
    return MSG_SUCCESS_RES;
}

static sint32 callbackPtzGetTrackPos(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 s32Ret = 0;
    PTZ_POS_S *pstTrackPos = (PTZ_POS_S *)pstMsgPkt->pu8Data;

#if (defined(BOARD_HDW845V1))
    s32Ret = Zoom_PosUpdate(*pstTrackPos);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "callbackSplitNotify failed \n");
    }
#endif

    return MSG_SUCCESS_RES;

}

static sint32 callbackUpdateGpsData(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    sint32 s32Ret = 0;
	GPS_DATA_S stGpsData = {0};
	memcpy(&stGpsData, pstMsgPkt->pu8Data, sizeof(GPS_DATA_S));
#endif

    return SV_SUCCESS;
}

static sint32 callbackSetVirDDAWAlarm(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2))
    sint32 s32Ret = 0;
	MSG_DDAW_ALARM_CFG stVirDDAWAlarm = {0};
	memcpy(&stVirDDAWAlarm, pstMsgPkt->pu8Data, sizeof(MSG_DDAW_ALARM_CFG));
	DMM_SetVirDDAWAlarm(&stVirDDAWAlarm);
#endif

    return SV_SUCCESS;
}

static sint32 callbackFaceCapture(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    sint32 s32Ret = 0;
	DMM_StartFaceCapture();
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读取到单帧CAN数据的回调函数
 * 返回值  : SV_SUCCESS - 成功
 			 SV_FAILURE - 其它错误
 * 说明    :   无
 *****************************************************************************/
static sint32 callbackReadCanData(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{

    sint32 i, s32Ret = 0;
	MSG_CAN_DATA_S stMsgCanData = {0};
    memcpy(&stMsgCanData, pstMsgPkt->pu8Data, pstMsgPkt->u32Size);

    /* 打印can 数据 */
    print_level(SV_DEBUG, "alg recv CANid[%#x] data: \n", stMsgCanData.u32CanId);
    for (i=0; i < CAN_UTILS_START_FRAME_LEN; i++){
        printf("%02X ", stMsgCanData.szCanData[i]);
    }
    printf("\n ");


    return SV_SUCCESS;
}

sint32 alg_RegisterMsgCallBack()
{
    sint32 s32Ret;

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_CFG_UPDATE, callbackConfigUpdate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback_ThreadExec failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_ALG_CALIBRATION, callbackCalibration);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_ALG_QRCODE_CALIBRATION, callbackQRCodeCalibration);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_ALG_POINT_IMAGE_TO_REAL, callbackPointImageToReal);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_ALG_POINT_REAL_TO_IMAGE, callbackPointRealToImage);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_IR_ADAS_CALIBRATE, callbackIrADASCalibrate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_ALG_FR_ADDUSER, callbackAddUser);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_ALG_FR_DELUSER, callbackDelUser);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_ALG_FR_MODUSER, callbackModifyUser);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_ALG_FR_GETUSERS, callbackGetUsers);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_ALG_FR_LOGIN, callbackLogin);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_EVENT_ALG_SPLIT_UPDATE, callbackSplitNotify);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_EVENT_RESET_POS, callbackPtzGetTrackPos);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_CAN_SINGLE, callbackReadCanData);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }


#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_GPS_DATA, callbackUpdateGpsData);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    //s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_CAN_DVR_FACE_CAPTURE, callbackFaceCapture);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

#if defined(BOARD_DMS31V2)
    s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_VIR_DDAW_ALARM, callbackSetVirDDAWAlarm);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA32N1))
    s32Ret = Msg_registerOpCallback(EP_ALG, OP_EVENT_SET_APC_NUM, callbackSetApcNum);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

#if (defined(BOARD_ADA47V1))
    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_ADD_PLUG, callbackAddPlug);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_DEL_PLUG, callbackDeletePlug);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_ALG, OP_REQ_UPDATE_PLUG, callbackUpdatePlug);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_ALG, OP_REQ_SET_PLUG_CONFIG, callbackSetPlugConfig);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 隐藏图片流
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             其他 - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 ALG_Hide_Picstream()
{
    sint32 s32Ret;
    MSG_VIDEO_CFG stVideoCfg = {0};
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    if(!(stVideoCfg.astChnParam[0].bShowGuiMask & 0b0100))
    {
        return SV_SUCCESS;
    }

    stVideoCfg.astChnParam[0].bShowGuiMask &= 0b1011;

    stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
    stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
    //print_level(SV_WARN, "stMsgPkt.u32Size = %d\n", stMsgPkt.u32Size);
    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
        return MSG_DEFAULT_FAIL;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 切换AlarmOut开关
 * 输入参数: bEnable 是否打开
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             其他 - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 ALG_AlarmOut_Switch(SV_BOOL bEnable)
{
    printf("ALG_AlarmOut_Switch, bEnable=%d\n", bEnable);
    sint32 s32Ret;
    MSG_ALG_CFG stAlgCfg = {0};
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    stRetPkt.pu8Data = (uint8 *)&stAlgCfg;
    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_GET_ALG_CFG, NULL, &stRetPkt, sizeof(MSG_ALG_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed. [err=%#x]\n", s32Ret);
        return MSG_DEFAULT_FAIL;
    }

    if ( stAlgCfg.stAlgCh2.stPdsParam.bPdAlarmOutRed == bEnable
      && stAlgCfg.stAlgCh2.stPdsParam.bPdAlarmOutYellow == bEnable
      && stAlgCfg.stAlgCh2.stPdsParam.bPdAlarmOutGreen  == bEnable)
    {
        return SV_SUCCESS;
    }

    stAlgCfg.stAlgCh2.stPdsParam.bPdAlarmOutRed = bEnable;
    stAlgCfg.stAlgCh2.stPdsParam.bPdAlarmOutYellow = bEnable;
    stAlgCfg.stAlgCh2.stPdsParam.bPdAlarmOutGreen = bEnable;

    stMsgPkt.pu8Data = (uint8 *)&stAlgCfg;
    stMsgPkt.u32Size = sizeof(MSG_ALG_CFG);
    //print_level(SV_WARN, "stMsgPkt.u32Size = %d\n", stMsgPkt.u32Size);
    s32Ret = Msg_execRequestBlock(EP_ALG, EP_CONTROL, OP_REQ_SET_ALG_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
        return MSG_DEFAULT_FAIL;
    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 通过socket获取MediaBuf Fd
 * 输入参数: s32SocketFd --- socket fd
               s32Chn --- 通道号
 * 输出参数: ps32MediaBufFd --- MediaBuf Fd
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 说明    : 无
 *****************************************************************************/
sint32 alg_GetMediaBufFd(sint32 s32SocketFd, sint32 s32Chn, sint32 *ps32MediaBufFd)
{
    sint32 s32Ret = 0, i, idx;
    SocketPacket stSocketPkt = {0};
    fd_set read_fds, write_fds;
    struct timeval timeout;

    if (s32SocketFd < 0 || s32Chn < 0 || s32Chn >= 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == ps32MediaBufFd)
    {
        return ERR_NULL_PTR;
    }

    FD_ZERO(&write_fds);
    FD_SET(s32SocketFd, &write_fds);
    timeout.tv_sec=3;
    timeout.tv_usec=0;
    s32Ret = select(s32SocketFd + 1, NULL, &write_fds, NULL, &timeout);
    if (s32Ret <= 0)
    {
        print_level(SV_WARN, "select write failed. [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

#if ALG_MUTLIT_BUFFER
    for(idx = 0; idx < ALG_MULTI_BUF_NUM; idx++)
    {
        stSocketPkt.header.startode = MSG_STARTCODE;
        stSocketPkt.header.opcode = SOCKET_OP_GET_FD;
        stSocketPkt.header.params = s32Chn;
        stSocketPkt.args[0] = idx;    /* 取得第一个BUFFER */
        s32Ret = unsock_write(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt));
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "unsock_write failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        FD_ZERO(&read_fds);
        for (i = 0; i < 10; i++)
        {
            timeout.tv_sec=1;
            timeout.tv_usec=0;
            FD_SET(s32SocketFd, &read_fds);
            s32Ret = select(s32SocketFd + 1, &read_fds, NULL, NULL, &timeout);
            if (s32Ret <= 0)
            {
                print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
                continue;
            }

            s32Ret = unsock_recvPacket(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt), &ps32MediaBufFd[idx]);
            if (s32Ret < 0 || ps32MediaBufFd[idx] < 0)
            {
                print_level(SV_ERROR, "unsock_write failed. [err=%#x, fd=%d]\n", s32Ret, ps32MediaBufFd[idx]);
                return SV_FAILURE;
            }
            else
            {
                print_level(SV_INFO, "socket recvfd[%d]:%d\n", idx, ps32MediaBufFd[idx]);
                break;
            }
        }

        if (i >= 10)
        {
            print_level(SV_ERROR, "wait for unsock_recvPacket timeout.\n");
            close(s32SocketFd);
            return SV_FAILURE;
        }
    }

#else
    stSocketPkt.header.startode = MSG_STARTCODE;
    stSocketPkt.header.opcode = SOCKET_OP_GET_FD;
    stSocketPkt.header.params = s32Chn;
    s32Ret = unsock_write(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "unsock_write failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
	}
	print_level(SV_INFO, "unsock_write successful. fd:%d\n", s32SocketFd);
    FD_ZERO(&read_fds);
    for (i = 0; i < 5; i++)
    {
        timeout.tv_sec=1;
        timeout.tv_usec=0;
        FD_SET(s32SocketFd, &read_fds);
        s32Ret = select(s32SocketFd + 1, &read_fds, NULL, NULL, &timeout);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            continue;
        }

        s32Ret = unsock_recvPacket(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt), ps32MediaBufFd);
        if (s32Ret < 0 || ps32MediaBufFd < 0)
        {
            print_level(SV_ERROR, "unsock_write failed. [err=%#x, fd=%d]\n", s32Ret, ps32MediaBufFd);
            return SV_FAILURE;
        }
        else
        {
            break;
        }
    }

    if (i >= 5)
    {
        print_level(SV_ERROR, "wait for unsock_recvPacket timeout.\n");
        close(s32SocketFd);
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 通过socket获取媒体分辨率（宽高）
 * 输入参数: s32SocketFd --- socket fd
               s32Chn --- 通道号
 * 输出参数: u32Width --- 宽
             u32Height --- 高
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 说明    : 无
 *****************************************************************************/
sint32 alg_GetMediaRes(sint32 s32SocketFd, sint32 s32Chn, uint32 *u32Width, uint32 *u32Height)
{
    sint32 s32Ret = 0, i;
    SocketPacket stSocketPkt = {0};
    fd_set read_fds, write_fds;
    struct timeval timeout;

    if (s32SocketFd < 0 || s32Chn < 0 || s32Chn >= 4)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == u32Width || NULL == u32Height)
    {
        return ERR_NULL_PTR;
    }

    FD_ZERO(&write_fds);
    FD_SET(s32SocketFd, &write_fds);
    timeout.tv_sec=3;
    timeout.tv_usec=0;
    s32Ret = select(s32SocketFd + 1, NULL, &write_fds, NULL, &timeout);
    if (s32Ret <= 0)
    {
        print_level(SV_WARN, "select write failed. [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    stSocketPkt.header.startode = MSG_STARTCODE;
    stSocketPkt.header.opcode = SOCKET_OP_GET_RES;
    stSocketPkt.header.params = s32Chn;
    s32Ret = unsock_write(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "unsock_write failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
	}
	print_level(SV_INFO, "unsock_write successful. fd:%d\n", s32SocketFd);
    FD_ZERO(&read_fds);
    for (i = 0; i < 5; i++)
    {
        timeout.tv_sec=1;
        timeout.tv_usec=0;
        FD_SET(s32SocketFd, &read_fds);
        s32Ret = select(s32SocketFd + 1, &read_fds, NULL, NULL, &timeout);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            continue;
        }

        s32Ret = unsock_recvPacket(s32SocketFd, &stSocketPkt, sizeof(stSocketPkt), NULL);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "unsock_write failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        else
        {
            break;
        }
    }

    if (i >= 5)
    {
        print_level(SV_ERROR, "wait for unsock_recvPacket timeout.\n");
        close(s32SocketFd);
        return SV_FAILURE;
    }

    if (stSocketPkt.header.len == 2*sizeof(sint32))
    {
        memcpy(u32Width, &stSocketPkt.args[0*sizeof(sint32)], 4);
        memcpy(u32Height, &stSocketPkt.args[1*sizeof(sint32)], 4);
    }

    return SV_SUCCESS;
}

/* 中断退出 */
static void exit_handle(int signalnum)
{
    sint32 s32Ret = 0;

    printf("catch signalnum %d!\n", signalnum);
    exit(EXIT_FAILURE);
}

int main(int argc, char **argv)
{
    sint32 s32Ret = 0, n, i, j;
    sint32 s32ChnNum;
    uint32 u32Width = 0;
    uint32 u32Height = 0;
    sint32 s32Tmp = 0;
    char *pszConfigFile = CONFIG_XML;
    char *pszConfigBak1 = CONFIG_BAK1;
    char *pszConfigBak2 = CONFIG_BAK2;
    char *pszConfigDefault = CONFIG_DEFAULT;
    CFG_ALG_PARAM stAlgParam = {0};
    CFG_SYS_PARAM stSysParam = {0};
    CFG_SER_PARAM stSerParam = {0};
    CFG_MEDIA_PARAM stMediaParam = {0};
    PLUG_CFG_PARAM_S stPlugParam = {0};
    ADAS_CFG_PARAM_S stAdasParam = {0};
    DMM_CFG_PARAM_S stDmmParam = {0};
    PD_CFG_PARAM_S stPdParam = {0};
    APC_CFG_PARAM_S stApcParam = {0};
    ZOOM_CFG_PARAM_S stZoomParam = {0};
    ALARM_CFG_PARAM_S stAlarmParam = {0};
    SocketPacket stSocketPkt = {0};
    fd_set read_fds, write_fds;
    struct timeval timeout;
    SV_BOOL bAdas = SV_FALSE;
    SV_BOOL bDms = SV_FALSE;
    SV_BOOL bPds = SV_FALSE;
    SV_BOOL bApc = SV_FALSE;
    SV_BOOL bTrack = SV_FALSE;
    SV_BOOL bGetMediaBuf = SV_FALSE;
    CHN_ALG_E aenChnAlg[ALG_MAX_CHN] = {0};
#if ALG_MUTLIT_BUFFER
    int as32MediaBufFd[4][ALG_MULTI_BUF_NUM] = {-1};
#else
	int as32MediaBufFd[4] = {-1, -1, -1, -1};
#endif

#if (defined(BOARD_ADA32IR))
    s32ChnNum = 2;
#else
    s32ChnNum = 1;
#endif

    if (argc == 2 && atoi(argv[1]) >= SV_ERROR && atoi(argv[1]) <= SV_ALWAYS)
    {
        ipsys_log_level = atoi(argv[1]);
    }

    /*捕获进程退出的系统消息*/
    if (SIG_ERR == signal(SIGINT, exit_handle))
    {
        printf("catch signal SIGKILL Error: %d, %s\n", errno, strerror(errno));
    }

    /*忽略PIPE消息*/
    if (SIG_ERR == signal(SIGPIPE, SIG_IGN))
    {
        printf("catch signal SIGPIPE Error: %d, %s\n", errno, strerror(errno));
    }

    s32Ret = ALG_Init();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "ALG_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = BOARD_Init(INIT_MOD_ALG);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if 0
#if (defined(BOARD_ADA32V2))
    /* ipsys和alg不能同时跑原先的CONFIG_Init。如果在boottstart.sh中ipsys和alg错开几秒运行则基本不会出现这个问题。目前只是简单加延时处理，
        后续需要做互斥的处理 */
    s32Ret = CONFIG_Init_ALG(pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
#else
    s32Ret = CONFIG_Init(pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
#endif
#else
    s32Ret = CONFIG_Init(INIT_MOD_ALG, pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
#endif
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = CONFIG_GetAlgParam(&stAlgParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetAlgParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    s32Ret = BOARD_SetAlarmOut(stAlgParam.enAlgTrigger ==  TRIGGER_UP ? 0 : 1);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BOARD_SetAlarmOut failed. [err=%#x]\n", s32Ret);
    }
    if (BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        s32Ret = BOARD_SetSwitchOut(1, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "BOARD_SetSwitchOut failed. [err=%#x]\n", s32Ret);
        }
    }
#endif

#if defined(BOARD_ADA47V1)
    if (ALG_PDS == stAlgParam.stAlgCh2.enAlgType && (BOARD_GetVersion() != BOARD_ADA47V1_V3))
    {
        g_alg_use_plug = 1;
        print_level(SV_INFO, "alg run as plug mode.\n");
    }
#endif

    aenChnAlg[0] = stAlgParam.stAlgCh1.enAlgType;
    aenChnAlg[1] = stAlgParam.stAlgCh2.enAlgType;
    aenChnAlg[2] = stAlgParam.stAlgCh3.enAlgType;
    for(n = 0; n < s32ChnNum; n++)
    {
        switch (aenChnAlg[n])
        {
            case ALG_ADAS:
                print_level(SV_INFO, "ch%d run ADAS\n", n);
                bAdas = SV_TRUE;
                break;
            case ALG_DMS:
                print_level(SV_INFO, "ch%d run DMS\n", n);
                bDms = SV_TRUE;
                break;
            case ALG_PDS:
                print_level(SV_INFO, "ch%d run PDS\n", n);
                bPds = SV_TRUE;
                break;
            case ALG_APC:
                print_level(SV_INFO, "ch%d run APC\n", n);
                bApc = SV_TRUE;
                break;
            case ALG_TRACK:
                print_level(SV_INFO, "ch%d run TRACK\n", n);
                bTrack = SV_TRUE;
                break;
            default:
                print_level(SV_WARN, "ch%d unknown type: %d\n", n, aenChnAlg[n]);
        }
    }

    const int reconnect_times = 20;
#if ((!defined(BOARD_DMS31V2) && !defined(BOARD_ADA32V2) && !defined(BOARD_ADA32V3)) || defined(DMS31SDK) || defined(ADA32SDK))
    for (i = 0; i < reconnect_times; i++)
    {
        g_s32SocketFd = cli_connect(CS_PATH_CAM_STREAM, 'c');
        if (g_s32SocketFd > 0)
        {
            break;
        }
        print_level(SV_WARN, "cli_connect %s failed, try time: %d\n", CS_PATH_CAM_STREAM, i+1);
        sleep_ms(500);
    }
    if (i >= reconnect_times)
    {
        print_level(SV_ERROR, "wait for cli_connect %s timeout.\n", CS_PATH_CAM_STREAM);
        return -1;
    }

    print_level(SV_INFO, "cli_connect successful. fd:%d\n", g_s32SocketFd);
    for(n = 0; n < s32ChnNum; n++)
    {
#if ALG_MUTLIT_BUFFER
        s32Ret = alg_GetMediaBufFd(g_s32SocketFd, n, g_as32MediaBufFd[n]);
#else
		s32Ret = alg_GetMediaBufFd(g_s32SocketFd, n, &g_as32MediaBufFd[n]);
#endif
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "alg_GetMediaBufFd failed.[err=%#x]\n", s32Ret);
            close(g_s32SocketFd);
            return -1;
        }

        //print_level(SV_INFO, "got media buffer fd:%d\n", g_as32MediaBufFd[n]);

#if (defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1))
        s32Ret = alg_GetMediaRes(g_s32SocketFd, n, &u32Width, &u32Height);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "alg_GetMediaRes failed.[err=%#x]\n", s32Ret);
            close(g_s32SocketFd);
            return -1;
        }
        print_level(SV_INFO, "get alg w: %d, h: %d\n", u32Width, u32Height);
#endif
    }
    bGetMediaBuf = SV_TRUE;
#endif

    s32Ret = CONFIG_GetServerParam(&stSerParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetServerParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = CONFIG_GetSystemParam(&stSysParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetAlgParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }


    s32Ret = CONFIG_GetMediaParam(&stMediaParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetMediaParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    stPdParam.s32SplitMode = (sint32)stMediaParam.enVoSplitMode;
    stPdParam.bRotate = (stMediaParam.astChnParam[0].enRotateAngle == SV_ROTATION_90 || stMediaParam.astChnParam[0].enRotateAngle == SV_ROTATION_270) ? SV_TRUE : SV_FALSE;

    if (bGetMediaBuf)
    {
        s32Ret = LOG_Init(&stSerParam, SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "LOG_Init failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = MSG_SysInit(SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_SysInit failed. [err=%#x]\n", s32Ret);
            return -1;
        }

        s32Ret = MSG_ReciverStart(EP_ALG);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    stAlarmParam.enLanguage = stSysParam.enLang;
    stAlarmParam.aenPdsAudioType[0] = stAlgParam.stAlgCh1.stPdsParam.enAudioType;
    stAlarmParam.aenPdsAudioType[1] = stAlgParam.stAlgCh2.stPdsParam.enAudioType;
    stAlarmParam.aenPdsAudioType[2] = stAlgParam.stAlgCh3.stPdsParam.enAudioType;
    stAlarmParam.enAdasAudioType = stAlgParam.stAlgCh1.stAdasParam.enAudioType;
    stAlarmParam.enDmsAudioType = stAlgParam.stAlgCh2.stDmsParam.enAudioType;
    stAlarmParam.s32AudioVolume = stAlgParam.s32AudioVolume;
    stAlarmParam.bPlayWelcome = stAlgParam.stAlgCh2.stDmsParam.bDmsAudioWelcome;
    stAlarmParam.bPlayWelcome_FaceCap = stAlgParam.stAlgCh2.stDmsParam.bDmsFaceCapture;
    s32Ret = ALARM_Init(SV_FALSE, &stAlarmParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "ALARM_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (defined(BOARD_ADA47V1))
    if (g_alg_use_plug)
    {
        stPlugParam.u32ChnNum = s32ChnNum;
        for (i = 0; i < s32ChnNum; i++)
        {
#if ALG_MUTLIT_BUFFER
            for (j = 0; j < ALG_MULTI_BUF_NUM; j++)
            {
                stPlugParam.as32MediaBufFd[i][j] = g_as32MediaBufFd[i][j];
            }
#else
    		stPlugParam.as32MediaBufFd[i] = g_as32MediaBufFd[i];
#endif
        }
        stPlugParam.u32Width = u32Width;
        stPlugParam.u32Height = u32Height;
        stPlugParam.stAlgParam = stAlgParam;
        s32Ret = PLUG_Init(&stPlugParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "PLUG_Init failed. [err=%#x]\n", PLUG_Init);
#if (defined(DMS31SDK) || defined(ADA32SDK) || defined(ADA32NSDK) || defined(ADA32ESDK) || defined(BOARD_ADA47V1))
            goto err_loop;
#endif
            if (BOARD_IsNeedKeyAuth())
            {
                goto err_loop;
            }

            return s32Ret;
        }

        goto alg_start;
    }
#endif

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1)|| defined(BOARD_ADA32N1) || defined(BOARD_DMS51V1))
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_DMS51V1))
#if ALG_MUTLIT_BUFFER
    for(i = 0; i < ALG_MULTI_BUF_NUM; i++)
    {
        stDmmParam.s32MediaBufFd[i] = g_as32MediaBufFd[0][i];
    }
#else
	stDmmParam.s32MediaBufFd = g_as32MediaBufFd[0];
#endif
    stDmmParam.s32Chn = 0;
    bDms = SV_TRUE;
    if(BOARD_GetVersion() == BOARD_ADA47V1_V1)
    {
        bDms = SV_FALSE;
    }

#if defined(BOARD_ADA47V1)
    if (stAlgParam.stAlgCh2.enAlgType != ALG_DMS)
    {
        bDms = SV_FALSE;
    }
#endif

#elif (defined(BOARD_ADA32N1))

    if (BOARD_ADA32N1_V2 == BOARD_GetVersion()&&(BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M)))
    {
#if ALG_MUTLIT_BUFFER
        for(i = 0; i < ALG_MULTI_BUF_NUM; i++)
        {
            stDmmParam.s32MediaBufFd[i] = g_as32MediaBufFd[0][i];
        }
#else
        stDmmParam.s32MediaBufFd = g_as32MediaBufFd[0];
#endif
        stDmmParam.s32Chn = 0;
        bDms = SV_TRUE;

    }
#else
#if ALG_MUTLIT_BUFFER
    for(i = 0; i < ALG_MULTI_BUF_NUM; i++)
    {
        stDmmParam.s32MediaBufFd[i] = g_as32MediaBufFd[1][i];
    }
#else
	stDmmParam.s32MediaBufFd = g_as32MediaBufFd[1];
#endif
    stDmmParam.s32Chn = 1;

#endif
    stDmmParam.stAlgParam = stAlgParam;
    stDmmParam.stAlgParam.bImageMirror = stMediaParam.astChnParam[0].bImageMirror;
    if (bDms)
    {
        s32Ret = DMM_Init(&stDmmParam);
        if (SV_SUCCESS != s32Ret)
        {
#if (defined(DMS31SDK) || defined(ADA32SDK) || defined(ADA32NSDK) || defined(ADA32ESDK) || defined(BOARD_ADA47V1))
            goto err_loop;
#endif
            print_level(SV_ERROR, "DMM_Init failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4))
    bPds = SV_TRUE;
    if((BOARD_GetVersion() == BOARD_ADA47V1_V2) || (BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M) && BOARD_GetVersion() == BOARD_ADA32N1_V2) || (BOARD_GetVersion() == BOARD_ADA32N1_V3))
    {
        bPds = SV_FALSE;
    }
    stPdParam.u32ChnNum = s32ChnNum;
    for (i = 0; i < s32ChnNum; i++)
    {
#if ALG_MUTLIT_BUFFER
        for (j = 0; j < ALG_MULTI_BUF_NUM; j++)
        {
            stPdParam.as32MediaBufFd[i][j] = g_as32MediaBufFd[i][j];
        }
#else
		stPdParam.as32MediaBufFd[i] = g_as32MediaBufFd[i];
#endif
    }
    stPdParam.u32Width = u32Width;
    stPdParam.u32Height = u32Height;
    if(BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() == BOARD_ADA47V1_V3)
    {
        stAlgParam.stAlgCh2.enAlgType = ALG_PDS;
        stAlgParam.stAlgCh2.stPdsParam.bPdTestMode = SV_TRUE;
    }
#if defined(BOARD_ADA32IR)
    stPdParam.u32Width = 608;
    stPdParam.u32Height = 352;
    stPdParam.u32IrWidth  = 512;
    stPdParam.u32IrHeight = 384;
#endif
    stPdParam.stAlgParam = stAlgParam;
    if (bPds)
    {
        s32Ret = PD_Init(&stPdParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "PD_Init failed. [err=%#x]\n", s32Ret);
#if (defined(DMS31SDK) || defined(ADA32SDK) || defined(ADA32NSDK) || defined(ADA32ESDK) || defined(BOARD_ADA47V1))
            goto err_loop;
#endif
            if (BOARD_IsNeedKeyAuth())
            {
                goto err_loop;
            }

            return s32Ret;
        }
    }
#endif

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32N1))
    if (stAlgParam.stAlgCh2.enAlgType == ALG_APC)
    {
        bApc = SV_TRUE;
    }

    if(BOARD_GetVersion() == BOARD_ADA32N1_V1 || BOARD_GetVersion() == BOARD_ADA32N1_V2)
    {
        bApc = SV_FALSE;
    }

    stApcParam.u32ChnNum = s32ChnNum;
    for (i = 0; i < s32ChnNum; i++)
    {
#if ALG_MUTLIT_BUFFER
        for (j = 0; j < ALG_MULTI_BUF_NUM; j++)
        {
            stApcParam.as32MediaBufFd[i][j] = g_as32MediaBufFd[i][j];
        }
#else
        stApcParam.as32MediaBufFd[i] = g_as32MediaBufFd[i];
#endif
    }
    stApcParam.stAlgParam = stAlgParam;

    if (bApc)
    {
        s32Ret = APC_Init(&stApcParam);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "APC_Init failed. [err=%#x]\n", s32Ret);
#if (defined(DMS31SDK) || defined(ADA32SDK) || defined(ADA32NSDK) || defined(ADA32ESDK) || defined(BOARD_ADA47V1))
            goto err_loop;
#endif
            if (BOARD_IsNeedKeyAuth())
            {
                goto err_loop;
            }
            return s32Ret;
        }
    }

#endif

#if (defined(BOARD_HDW845V1))
    stZoomParam.u32ChnNum = s32ChnNum;
    for (i = 0; i < s32ChnNum; i++)
    {
        stZoomParam.as32MediaBufFd[i] = g_as32MediaBufFd[i];
    }
    stZoomParam.stAlgParam = stAlgParam;

    s32Ret = Zoom_Init(&stZoomParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Zoom_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

alg_start:

    if (bGetMediaBuf)
    {
        s32Ret = alg_RegisterMsgCallBack();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "alg_RegisterMsgCallBack failed!\n");
            return s32Ret;
        }
    }

#if (defined(BOARD_ADA47V1))
    if (g_alg_use_plug)
    {
        s32Ret = PLUG_Start();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "PLUG_Start failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        goto alg_loop;
    }
#endif

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    //先不跑DMS_start
    if (bDms)
    {
        s32Ret = DMM_Start();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_Start failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#elif (defined(BOARD_ADA32N1))
    if (BOARD_ADA32N1_V2 == BOARD_GetVersion()&&(BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M))&&bDms)
    {
        s32Ret = DMM_Start();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_Start failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4))
    if (bPds)
    {
        if(BOARD_IsNotCustomer(BOARD_C_ADA32IR_100393))
        {
            s32Ret = PD_Start();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "PD_Start failed. [err=%#x]\n", s32Ret);
                return s32Ret;
            }
        }
    }
#endif

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32N1))
    if (bApc)
    {
        s32Ret = APC_Start();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "APC_Start failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif

#if (defined(BOARD_HDW845V1))
        s32Ret = Zoom_Start();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Zoom_Start failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
#endif

alg_loop:

    while (1)
    {
        sleep(1);

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3))
        if (bGetMediaBuf)
        {
            continue;
        }

        for (i = 0; i < reconnect_times; i++)
        {
            g_s32SocketFd = cli_connect(CS_PATH_CAM_STREAM, 'c');
            if (g_s32SocketFd > 0)
            {
                break;
            }
            print_level(SV_WARN, "cli_connect %s failed, try time: %d\n", CS_PATH_CAM_STREAM, i+1);
            sleep_ms(500);
        }
        if (i >= reconnect_times)
        {
            print_level(SV_ERROR, "wait for cli_connect %s timeout.\n", CS_PATH_CAM_STREAM);
            return -1;
        }

        print_level(SV_INFO, "cli_connect successful. fd:%d\n", g_s32SocketFd);
        for(n = 0; n < s32ChnNum; n++)
        {
#if ALG_MUTLIT_BUFFER
            s32Ret = alg_GetMediaBufFd(g_s32SocketFd, n, g_as32MediaBufFd[n]);
#else
            s32Ret = alg_GetMediaBufFd(g_s32SocketFd, n, &g_as32MediaBufFd[n]);
#endif
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "alg_GetMediaBufFd failed.[err=%#x]\n", s32Ret);
                close(g_s32SocketFd);
                return -1;
            }
        }

#if !defined(ADA32SDK)
        s32Ret = LOG_Init(&stSerParam, SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "LOG_Init failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = MSG_SysInit(SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_SysInit failed. [err=%#x]\n", s32Ret);
            return -1;
        }

        s32Ret = MSG_ReciverStart(EP_ALG);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
#endif

        s32Ret = alg_RegisterMsgCallBack();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "alg_RegisterMsgCallBack failed!\n");
            return s32Ret;
        }

        print_level(SV_INFO, "g_as32MediaBufFd[0][0]=%d, [0][1]=%d, [0][2]=%d\n", g_as32MediaBufFd[0][0], g_as32MediaBufFd[0][1], g_as32MediaBufFd[0][2]);

        s32Ret = PD_SetMediaBufFd(&g_as32MediaBufFd);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "PD_SetMediaBufFd failed\n");
            continue;
        }
        bGetMediaBuf = SV_TRUE;
#elif defined(BOARD_DMS31V2)
        if (bGetMediaBuf)
        {
            continue;
        }

        for (i = 0; i < reconnect_times; i++)
        {
            g_s32SocketFd = cli_connect(CS_PATH_CAM_STREAM, 'c');
            if (g_s32SocketFd > 0)
            {
                break;
            }
            print_level(SV_WARN, "cli_connect %s failed, try time: %d\n", CS_PATH_CAM_STREAM, i+1);
            sleep_ms(500);
        }
        if (i >= reconnect_times)
        {
            print_level(SV_ERROR, "wait for cli_connect %s timeout.\n", CS_PATH_CAM_STREAM);
            return -1;
        }

        print_level(SV_INFO, "cli_connect successful. fd:%d\n", g_s32SocketFd);
        for(n = 0; n < s32ChnNum; n++)
        {
#if ALG_MUTLIT_BUFFER
            s32Ret = alg_GetMediaBufFd(g_s32SocketFd, n, g_as32MediaBufFd[n]);
#else
    		s32Ret = alg_GetMediaBufFd(g_s32SocketFd, n, &g_as32MediaBufFd[n]);
#endif
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "alg_GetMediaBufFd failed.[err=%#x]\n", s32Ret);
                close(g_s32SocketFd);
                return -1;
            }
        }

#if !defined(DMS31SDK)
        s32Ret = LOG_Init(&stSerParam, SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "LOG_Init failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = MSG_SysInit(SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_SysInit failed. [err=%#x]\n", s32Ret);
            return -1;
        }

        s32Ret = MSG_ReciverStart(EP_ALG);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
            return s32Ret;
        }
#endif

        s32Ret = alg_RegisterMsgCallBack();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "alg_RegisterMsgCallBack failed!\n");
            return s32Ret;
        }

        s32Ret = DMM_SetMediaBufFd(&g_as32MediaBufFd);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "DMM_SetMediaBufFd failed\n");
            continue;
        }
        bGetMediaBuf = SV_TRUE;
#endif
    }

err_loop:
    while (1)
    {
        print_level(SV_ERROR, "auth failed...\n");
        sleep(5);
    }

    return 0;
}

