/******************************************************************************
Copyright (C) 2018-2020 广州敏视数码科技有限公司版权所有.

文件名: libhttp.c

作者: 许家铭    版本: v1.0.0(初始版本号)    日期: 2018-01-08

文件功能描述: HTTP服务器端功能定义

版本: v1.0.0(最新版本号)

历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/vfs.h>
#include <sys/syscall.h>
#include <sys/statvfs.h>
#include <sys/prctl.h>
#include <sys/file.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>
#include <string.h>
#include <semaphore.h>

#include <list>

#define COM_MOD_ID  COM_ID_IPSERVER

#include "board.h"
#include "common.h"
#include "print.h"
#include "msg.h"
#include "config.h"
#include "cJSON.h"
#include "safefunc.h"
#include "storage.h"
#include "jsonHandle.h"
#include "httpHandle.h"
#include "libhttp.h"
#include "sharefifo.h"
#include "faac/faac.h"
#include "flv_format_define.h"
#include "websocket.h"
#include "mongoose.h"
#include "alarm.h"
#include "storage.h"
#include "utils.h"
#include "network.h"
#include "media.h"
#include "../../media/include/mpp_protocol.h"

//#define HTTP_FLV_CHUNKED 1

char *http_common_resource_path = "/var/webui/";
char *http_default_resource_path = "/var/webui/";
char *http_A32_201623_resource_path = "/var/webui/extern/a32/201623/";


sint32 g_s32QuickJpegFd[HTTP_MAX_QUICK_THR] = {0};
#define MG_FD(c_) ((sint32) (size_t) (c_)->fd)

SV_BOOL bResetMJpeg = SV_FALSE;

/* 模块控制信息 */
typedef struct tag_httpSvrComInfo_S
{
    SV_BOOL     bKeyAuth;                               /* 密钥是否有效 */
    sint32      s32SvrSocket;                           /* http服务端的侦听socket */
    sint32      s32Port80Socket;                        /* http 80端口侦听socket */
    sint32      s32IPSearchSocket;                      /* 2887端口侦听ip搜索socket */
    sint32      s32QuickSvrSocket;	                    /* 快速抓图http服务端的侦听socket */
	sint32      s32FlvSocket;                           /* http服务端8082端口侦听socket */
	sint32      s32WsJpegSocket;                        /* 快速抓图ws服务端侦听socket*/
    sint32      s32MJpegSocket;                         /* http服务端8084端口侦听socket */
    sint32      s32UdpMjpegSocket;                      /* udp服务端50001端口侦听socket */
	sint32      s32HttpReqFd[HTTP_MAX_HTTP_THR];        /* http服务端8080端口请求fd，上限存储100个 */

    sint32      s32QuickHandleWtd;                      /* 处理快速抓图喂狗 */
    uint32      u32QuickHandleNum;                      /* 当前处理快速请求的线程数 */
    HTTP_SESSION_S astSessionList[HTTP_MAX_SESSION];    /* 会话列表 */

    uint32      u32TidService;                          /* 服务端线程ID */
    uint32      u32TidQuickJpeg;                        /* 快速抓图http服务器线程ID */
	uint32      u32TidFlv;                              /* http服务器FLV传输线程ID */
	uint32      u32TidPort80;                           /* 80端口线程ID */
	uint32      u32TidWsJpeg;                           /* 快速抓图websocket服务器线程ID*/
    uint32      u32TidMJpeg;                            /* http服务器MJpeg传输线程ID */
    uint32      u32TidIpSearch;                         /* ip搜索线程ID */
    uint32      u32TidUdpMJpeg;                         /* Udp-MJpeg传输线程ID */

    SV_BOOL     bNetworkInited;                         /* 网络是否初始化完成 */
    SV_BOOL     bWriting;                               /* 正在写入升级包 */
    SV_BOOL     bRunning;                               /* 线程是否正在运行 */
    SV_BOOL     bResetMJpeg;                            /* 是否重置http-mjpeg */
    uint32      u32MJpegClients;                        /* mjpeg拉流客户端数 */
    sem_t       semDownFile;                            /* 发送文件信号量 */

    char        szSerialNum[32];                        /* 设备序列号 */
    pthread_mutex_t mutexLock;                          /* 线程统计互斥锁 */
    pthread_mutex_t mutexLockWarning;                   /* websocket报警锁 */
} HTTP_COM_INFO_S;

HTTP_COM_INFO_S m_stHttpInfo = {0};                     /* 模块控制信息 */
char            m_szUserPassword[3][3][CONFIG_MAX_BUF_SIZE]; /* 用户管理     [0][0]--用户名 [0][1]--默认密码 [0][2]--当前使用密码*/
char            m_szPacketMd5sum[40] = {0};             /* 升级包md5码 */
pthread_mutex_t m_mutexLockStrtok = {0};                /* Strtok互斥锁 */

typedef struct tag_wsAlarmSnap_S
{
	sint64  s64TimeStamp;
	char    szWarnPath[128];
}ALARM_SNAP_INFO_S;


typedef struct tag_wsAlarmInfo_S
{
	sint32          s32DMMWarnState;						/* 报警类型  */
	sint32          s32PDWarnState;						    /* 行人检测报警类型  */
    sint32          s32APCWarnState;                        /* APC客流统计报警类型 */
    sint64          s64TimeStamp;                           /* 报警发生时的时间戳 */
    uint16          u16UsrId;                               /* 人脸认证司机ID */
    float           fDmsEventScore;                         /* DMS报警事件置信度 */
    char            szUsrName[64];                          /* 人脸认证司机名字 */
    char            szAccountName[64];                      /* 人脸识别账户名，西井科技需求 */
	char            szWarnText[64];                         /* 报警描述 */
    char            szAlarmUuid[64];                        /* 报警uuid */
	char            szWarnPath[128];                         /* 报警图片路径 */
	char            szWarnVidPath[256];                     /* 报警录像路径 */
    DUMP_GPS_S      stGpsInfo;                              /* 报警时的GPS信息 */
    SV_BOOL         bWarnVid;							    /* 是否为报警录像路径推送 */
}WEBSOCKET_ALARM_INFO_S;

typedef struct tag_wsSvrComInfo_S
{
	uint32                              u32TidWsAlarm;				    /* websocket连接线程 */
	sint32                              s32ConnLen;						/* 删除的连接数量 */
	sint32                              s32ConnTimeOut;					/* 连接的空闲时间标志 */
	sint32                              s32HttpFlag;					/* 使用http协议标志 */
	pthread_mutex_t                     mutexWarnLock;					/* 报警类型获取互斥锁 */
    struct mg_mgr                       *pmg_mgr;                       /* 引出的MOOGSE结构体 */
	struct mg_connection*               DeleteConn[20];		            /* 待删除的连接列表 */
	sint8                               s8CurrentAddr[32];

    WEBSOCKET_ALARM_INFO_S              stWsAlarmInfo;                  /* 报警推送信息 */
    std::list<WEBSOCKET_ALARM_INFO_S>   listSendingWsAlarmInfo;			/* websocket待推送列表 */
    std::list<WEBSOCKET_ALARM_INFO_S>   listSendedWsAlarmInfo;			/* websocket已推送列表 */
    std::list<ALARM_SNAP_INFO_S>        listWarnSnapInfo;			    /* 报警截图信息列表 */
    SV_BOOL                             bFirstConnect;                  /* 是否初次连接 */
}WEBSOCKET_COM_INFO_S;

typedef struct tag_wsJpegComInfo_S
{
	SV_BOOL			bRunning;
	mg_connection   *video_nc;
}WEBSOCKET_JPEG_INFO_S;

typedef struct tag_mgComInfo
{
    mg_connection *nc;
    sint32 s32HeadLen;
    sint32 s32BodyLen;
    sint32 s32ChunkLen;
	char pszSid[MG_IO_SIZE];
    char szHead[MG_IO_SIZE];
    char szBody[MG_IO_SIZE];
    char szChunk[MG_IO_SIZE];
}MG_COM_INFO_S;

WEBSOCKET_COM_INFO_S m_stWebSocket = {0}; 				/* websocket控制模块 */
static sig_atomic_t s_signal_received = 0;
#define WEB_COMMAND_DMM_WARNING_JSON "{\"Warning\":\"%d\"}"
extern SNAP_INFO_S m_stSnapInfo;

WEBSOCKET_JPEG_INFO_S m_stWebSocketJpeg[HTTP_MAX_QUICK_THR] = {0};

/******************************************************************************
 * 函数功能: 创建FLV头

 * 输入参数: 无
 * 输出参数:
             无

 * 返回值  : flv_header --- 封装好的FLV头
 * 注意      : 无
 *****************************************************************************/
flv_header_t createFlvHeader()
{
	flv_header_t flv_header;
	memset(&flv_header,0,sizeof(flv_header));

	memcpy(flv_header.signature, "FLV", 3);
	flv_header.version = 1;
	//flv_header.info = 0x01;						// 仅含有视频
	flv_header.info = 0x05;						// 含有音视频
	flv_header.data_offset[3] = 0x9;
	flv_header.pre_tagsize[4] = 0;

	return flv_header;
}


/******************************************************************************
 * 函数功能: 创建Tag头

 * 输入参数: ucTagType --- Tag类型
             uiDataSize --- Tag包含数据大小
             uiTimeStamp --- Tag的时间戳
             ucTimeStampEx --- 时间戳高八位扩展
             uiStreamID --- 0
 * 输出参数:
             无

 * 返回值  : tag_header --- 封装好的Tag头
 * 注意      : 无
 *****************************************************************************/
tag_header_t createTagHeader(const uint8 ucTagType, const uint32 uiDataSize, const uint32 uiTimeStamp,
                                 const uint8 ucTimeStampEx, const uint32 uiStreamID)
{
	tag_header_t tag_header;

	tag_header.tag_type = ucTagType;

	uint32 data_size = htonl(uiDataSize);
	memcpy((char *)tag_header.data_size, (char *)&data_size + 1, sizeof(tag_header.data_size));

	uint32 timestamp = htonl(uiTimeStamp);
	memcpy((char *)tag_header.timestamp, (char *)&timestamp + 1, sizeof(tag_header.timestamp));

	tag_header.timestamp_ex = 0;

	memset(tag_header.streamID, 0, sizeof(tag_header.streamID));

	return tag_header;
}


/******************************************************************************
 * 函数功能: 将Meta数据封入Flv的第一个Tag

 * 输入参数: tag_av_info --- Tag参数信息
 * 输出参数:
             u8OutData --- 输出数据
             iOutDataSize --- 输出数据长度

 * 返回值  : u8OutData --- MetaTag数据
 * 注意      : 无
 *****************************************************************************/
uint8 * createMetaTag(tag_avInfo_t tag_av_info, uint8* u8OutData, int *iOutDataSize)
{
	//uint8 *buffer = (uint8 *)new char[1024];
	uint8 *pbuf = u8OutData;
	int metadata_pos = 0, metadata_size_pos = 0;
	int duration_pos = 0;
	int filesize_pos = 0;
	int data_size = 0;

	pbuf = ui08_to_bytes(pbuf, 18);							// MetaTagFlag,0x12

	metadata_size_pos = (int)(pbuf - (uint8 *)u8OutData);
	pbuf = ui24_to_bytes(pbuf, 0);          				// 数据部分总长度,初始化为0 (sum of all parts below)

    pbuf = ui24_to_bytes(pbuf, 0);          				// 时间戳
    pbuf = ui32_to_bytes(pbuf, 0);							// 时间戳高八位扩展及StreamID

    /* now data of data_size size */
	//metadata_pos = FLV_HEAD_SIZE + FLV_TAG_PRE_SIZE + FLV_TAG_HEAD_SIZE;

    /* first event name as a string */
    pbuf = ui08_to_bytes(pbuf, AMF_DATA_TYPE_STRING);		// 定义首个AMF包类型为AMF_DATA_TYPE_STRING
    pbuf = amf_string_to_bytes(pbuf, "onMetaData"); 		// 写入10字节字符串表明这是存放MetaData的Tag

    /* mixed array (hash) with size and string/type/data tuples */
    pbuf = ui08_to_bytes(pbuf, AMF_DATA_TYPE_MIXEDARRAY);	// 定义第二个AMF包类型为AMF_DATA_TYPE_MIXEDARRAY
    pbuf = ui32_to_bytes(pbuf, 7); 							/* 定义数据中含有7个数组(Key-Value),视频信
    												     	 息占5个数组,filesize和duration各占1个 */

	int offset = 0;

	pbuf = amf_string_to_bytes(pbuf, "duration");			// 第一个数组中Key名为"duration"
	offset = (int)(pbuf - (uint8 *)u8OutData);
	duration_pos = metadata_pos + offset;
	pbuf = amf_double_to_bytes(pbuf, 0); 					// 占位

    pbuf = amf_string_to_bytes(pbuf, "width");				// 第二个数组中Key名为"width"
    pbuf = amf_double_to_bytes(pbuf, (double)tag_av_info.avcWidth);

    pbuf = amf_string_to_bytes(pbuf, "height");				// 第三个数组中Key名为"height"
	pbuf = amf_double_to_bytes(pbuf, (double)tag_av_info.avcHeight);

    pbuf = amf_string_to_bytes(pbuf, "videodatarate");		// 第四个数组中Key名为"videodatarate"
    pbuf = amf_double_to_bytes(pbuf, (double)tag_av_info.avcBitRate);

    pbuf = amf_string_to_bytes(pbuf, "framerate");			// 第五个数组中Key名为"framerate"
    pbuf = amf_double_to_bytes(pbuf, (double)tag_av_info.avcFrameRate);

    pbuf = amf_string_to_bytes(pbuf, "videocodecid");		// 第六个数组中Key名为"videocodecid"
    pbuf = amf_double_to_bytes(pbuf, 7.00); 				// H264

	pbuf = amf_string_to_bytes(pbuf, "filesize");			// 第七个数组中Key名为"filesize"
	offset = (int)(pbuf - (uint8 *)u8OutData);
	filesize_pos = metadata_pos + offset;
	pbuf = amf_double_to_bytes(pbuf, 0); 					// 占位

	pbuf = amf_string_to_bytes(pbuf, "");
    pbuf = ui08_to_bytes(pbuf, AMF_DATA_TYPE_OBJECT_END);	// data写入结束

	data_size = (int)(pbuf - (uint8 *)u8OutData) - 11;
	ui24_to_bytes(u8OutData + metadata_size_pos, data_size);
	//print_level(SV_INFO, "MetaTag Data size: %d\r\n", data_size);

    pbuf = ui32_to_bytes(pbuf, data_size + 11); // Pre Tag Size
    //print_level(SV_INFO, "MetaTag size: %d\r\n", data_size + 11);

    *iOutDataSize = (int)(pbuf - u8OutData);
	return u8OutData;

}


/******************************************************************************
 * 函数功能: 将数据封装成Tag

 * 输入参数: tag_header --- Tag的头部
 		     pStreamData --- 输入数据
 		     iStreamDataSize --- 输入数据长度
 		     iFrameType --- 输入数据帧的类型
 		     iAVCPacketType --- 输入视频数据的编码类型
 * 输出参数:
             u8OutData --- 输出数据
             iOutDataSize --- 输出数据长度


 * 返回值  : 0 - 成功
             -1 - 其它错误
 * 注意      : 无
 *****************************************************************************/
uint8 * createVideoTag(const tag_header_t tag_header, const sint8 *pStreamData, const int iStreamDataSize,
                                        const int iFrameType, const int iAVCPacketType, uint8 *u8OutData, int *iOutDataSize)
{
	int iTotalLength = 0;
	int iOffset = 0;

	if(tag_header.tag_type == 0x09)     // VideoTag
	{
		/* 将Tag Header写入data数组 */
		memcpy(u8OutData + iOffset, (char *)&tag_header, sizeof(tag_header));
		iOffset += sizeof(tag_header);

		/* 将Tag数据部分写入 */
		if(iFrameType == 1)             // Key Frame
			u8OutData[iOffset] = 0x17;
		else
			u8OutData[iOffset] = 0x27;
		iOffset += 1;

		if(iAVCPacketType == 1)         // AVC NALU
			u8OutData[iOffset] = 0x01;
		else
			u8OutData[iOffset] = 0;
		iOffset += 1;

		/* Compotion Time */
		memset(u8OutData + iOffset, 0, 3);
		iOffset += 3;

		/* NALU Size */
		if(iAVCPacketType == 1)
		{
			uint32 uiNaluSize = htonl(iStreamDataSize);
			memcpy(u8OutData + iOffset, (char*)&uiNaluSize, 4);
			iOffset += 4;
		}

		/* 写入Data */
		memcpy(u8OutData + iOffset, pStreamData, iStreamDataSize);
		iOffset += iStreamDataSize;
		//print_level(SV_INFO, "iOffset - %d\r\n", iOffset);

		/* 写入前一个Tag的大小(若使用Chunk方式传输，实际上也写入了前面的Chunk Size和1个"\r\n") */
		uint32 uiPreviousTagSize = htonl(iOffset);
		//print_level(SV_INFO, "PreviousTagSize: %d\r\n", iOffset);
		memcpy(u8OutData + iOffset, (char *)&uiPreviousTagSize, 4);
		iOffset += 4;

		*iOutDataSize = iOffset ;
		return u8OutData;
	}
	return NULL;
}


/******************************************************************************
 * 函数功能: 封装第一个AudioTag

 * 输入参数: uiTimeStamp --- 时间戳

 * 输出参数:
             u8OutData --- 输出数据
             iOutDataSize --- 输出数据长度


 * 返回值  : 0 - 成功
             -1 - 其它错误
 * 注意    : 无
 *****************************************************************************/
uint8 * createSpeCfgTag(const uint32 uiTimeStamp, uint8 *u8OutData, int *iOutDataSize)
{
    int iOffset = 0;
	tag_header_t tag_header;
	int iSpeCfgDataSize = 1 + 1 + 2;            // AudioInfo(1) + AACPacketType(1) + AudioSpecificConfig(2)
    tag_header = createTagHeader(0x08, iSpeCfgDataSize, uiTimeStamp, 0, 0);

	/* 将Tag Header写入data数组 */
	memcpy(u8OutData + iOffset, (char *)&tag_header, sizeof(tag_header));
	iOffset += sizeof(tag_header);

	/* 将Tag数据部分写入 */
	u8OutData[iOffset] = 0xA6;				    // AAC/11kHz/16bit/mono,AF则对应AAC/44kHz/16bit/stereo
	iOffset += 1;

	u8OutData[iOffset] = 0;
	iOffset += 1;

	u8OutData[iOffset] = 0x14;
	iOffset += 1;
	u8OutData[iOffset] = 0x08;
	iOffset += 1;

    /* 写入前一个Tag的大小(若使用Chunk方式传输，实际上也写入了前面的Chunk Size和1个"\r\n") */
	uint32 uiPreviousTagSize = htonl(iOffset);
	//print_level(SV_INFO, "PreviousTagSize: %d\r\n", iOffset);
	memcpy(u8OutData + iOffset, (char *)&uiPreviousTagSize, 4);
	iOffset += 4;

	*iOutDataSize = iOffset ;
	return u8OutData;
}


/******************************************************************************
 * 函数功能: 将音频数据封装成Tag

 * 输入参数: tag_header --- Tag的头部
 		     pStreamData --- 输入数据
 		     iStreamDataSize --- 输入数据长度

 * 输出参数:
             u8OutData --- 输出数据
             iOutDataSize --- 输出数据长度


 * 返回值  : 0 - 成功
             -1 - 其它错误
 * 注意      : 无
 *****************************************************************************/
uint8 * createAudioTag(const tag_header_t tag_header, const sint8 *pStreamData, const int iStreamDataSize,
                             uint8 *u8OutData, int *iOutDataSize)
{
	int iOffset = 0;

	if(tag_header.tag_type == 0x08)             // AudioTag
	{
		/* 将Tag Header写入data数组 */
		memcpy(u8OutData + iOffset, (char *)&tag_header, sizeof(tag_header));
		iOffset += sizeof(tag_header);

		/* 将Tag数据部分写入 */
		u8OutData[iOffset] = 0xA6;				// AAC/11kHz/16bit/mono,AF则对应AAC/44kHz/16bit/stereo
		iOffset += 1;

		u8OutData[iOffset] = 0x01;  			// AAC RAW
		iOffset += 1;
		/* 写入Data */
		memcpy(u8OutData + iOffset, pStreamData, iStreamDataSize);
		iOffset += iStreamDataSize;
		//print_level(SV_INFO, "iOffset - %d\r\n", iOffset);

		/* 写入前一个Tag的大小(若使用Chunk方式传输，实际上也写入了前面的Chunk Size和1个"\r\n") */
		uint32 uiPreviousTagSize = htonl(iOffset);
		//print_level(SV_INFO, "PreviousTagSize: %d\r\n", iOffset);
		memcpy(u8OutData + iOffset, (char *)&uiPreviousTagSize, 4);
		iOffset += 4;

		*iOutDataSize = iOffset ;
		return u8OutData;
	}
	return NULL;
}


/******************************************************************************
 * 函数功能: 将Sps和Pps数据封入第一个VideoTag

 * 输入参数: uiTimeStamp --- Tag的时间戳
 		     pSps --- 输入Sps数据
 		     iSpsLen --- 输入Sps数据长度
 		     pPps --- 输入Pps数据
 		     iPpsLen --- 输入Pps数据长度
 * 输出参数:
             u8OutData --- 输出数据
             iOutDataSize --- 输出数据长度

 * 返回值  : u8OutData --- 第一个VideoTag的数据（AVCC Data）
 * 注意      : 无
 *****************************************************************************/
uint8 * createSpsPpsTag(const uint32 uiTimeStamp, const char *pSps, const int iSpsLen, const char *pPps,
                             const int iPpsLen, uint8 *u8OutData, int *iOutDataSize)
{
	int iSpsPpsTotalSize = 8 + iSpsLen + 3 + iPpsLen;
	//char *pSpsPpsData = new char[iSpsPpsTotalSize];
	char *pSpsPpsData = (char*)malloc(iSpsPpsTotalSize);
	pSpsPpsData[0] = 0x01;
	pSpsPpsData[1] = pSps[1];
	pSpsPpsData[2] = pSps[2];
	pSpsPpsData[3] = pSps[3];
	pSpsPpsData[4] = 0xff;
	pSpsPpsData[5] = 0xe1;

	// Sps长度
	int rdata = iSpsLen >>8;
	pSpsPpsData[6] = *(char *)&rdata;
	pSpsPpsData[7] = *(char *)&iSpsLen;  //11

	// Sps
	for(int i = 8 ; i < 8 + iSpsLen;i++)
 	{
 		pSpsPpsData[i] = pSps[i - 8];
 	}

 	// Pps数为1
	pSpsPpsData[8 + iSpsLen] = 0x01;

	// Pps长度
	rdata = iPpsLen >>8;
	pSpsPpsData[9 + iSpsLen] = *(char *)&rdata;
	pSpsPpsData[10 + iSpsLen] = *(char *)&iPpsLen;

	// Pps
	for(int i = 11 + iSpsLen; i < 11 + iSpsLen + iPpsLen;i++)
 	{
 		pSpsPpsData[i] = pSps[i - 11 - iSpsLen];
 	}

    //#ifdef HTTP_FLV_CHUNKED
    //int iFlvDataLen = 5 + 4 + iSpsPpsTotalSize; // video_tag(5) + 0x0001(4) + sps_pps_data_len
    //#else
	int iFlvDataLen = 5 + iSpsPpsTotalSize;     // video_tag(5) + sps_pps_data_len
	//#endif
	tag_header_t tag_header = createTagHeader(0x09, iFlvDataLen, uiTimeStamp, 0, 0);
	createVideoTag(tag_header, pSpsPpsData, iSpsPpsTotalSize, 1, 0, u8OutData, iOutDataSize);
	//delete []pSpsPpsData;
	free(pSpsPpsData);
	pSpsPpsData = NULL;
	return u8OutData;
}


static void sendHttpLog(sint32 s32ClientSocket, char const *pszEvent)
{
    uint32 s32Addr = 0;
    struct sockaddr addr;
    struct sockaddr_in *paddr = NULL;
    socklen_t addrlen;
    char szClientIp[32];
    char logLine[1024];

    addrlen = sizeof(addr);
    if (0 == getpeername(s32ClientSocket, &addr, &addrlen))
    {
        paddr = (struct sockaddr_in *)&addr;
        s32Addr = htonl(paddr->sin_addr.s_addr);
        sprintf(szClientIp, "%u.%u.%u.%u", (s32Addr>>24)&0xFF, (s32Addr>>16)&0xFF, (s32Addr>>8)&0xFF, s32Addr&0xFF);
    }
    else
    {
        strcpy(szClientIp, "unknown");
    }

    snprintf(logLine, 1024, "type=\"HTTP\"\tclient=\"%s\"\tevent=\"%s\"", szClientIp, pszEvent);
    LOG_Submit(-1, logLine);
}

uint32_t http_findMjpgData(unsigned char *data, uint32_t length)
{
    uint32_t i = 0;
    uint32_t tmpLen = 0;
    static uint32_t pos = 0;

    if(pos != 0)
        return pos;

    while(i < length - 1)
    {
        if(data[i] == 0xff && data[i+1] == 0xda)
            break;

        i++;
    }

    tmpLen = data[i+2] << 4 | data[i+3];

    pos = i+2+tmpLen;

    return pos;
}


static void http_start_thread(void (*f)(void *), void *p) {
	pthread_t thread_id = (pthread_t) 0;
	pthread_attr_t attr;
	sint32 s32Ret = 0;

    pthread_attr_init(&attr);
	pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    s32Ret = pthread_create(&thread_id, &attr,(void *(*) (void *) ) f, p);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
        pthread_attr_destroy(&attr);
    }
    pthread_attr_destroy(&attr);
}

static inline void http_CutLineBreak(char *pszStr)
{
    char *pcTmp = NULL;

    pcTmp = strchr(pszStr, '\r');
	if(NULL == pcTmp)
		pcTmp = strchr(pszStr, '\n');
	if(NULL != pcTmp)
		*pcTmp = '\0';
}

/******************************************************************************
 * 函数功能: 创建http服务器socket
 * 输入参数: s32Port --- 所监听的端口
 * 输出参数: ps32SocketFd --- socket fd
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
sint32 http_CreateHttpd(sint32 s32Port, sint32 *ps32SocketFd)
{
    sint32 s32Ret = 0;
    sint32 s32SocketFd = 0;
    sint32 s32On = 1;
    struct sockaddr_in stName = {0};

    if (NULL == ps32SocketFd)
    {
        return ERR_ILLEGAL_PARAM;
    }

    /* 创建socket    协议族(ipv4)，类型（TCP），协议（0为根据协议族和类型自动选择）*/
    s32SocketFd = socket(PF_INET, SOCK_STREAM, 0);
    if (s32SocketFd < 0)
    {
        print_level(SV_ERROR, "create socket for httpd failed. [err=%#x]\n", errno);
        return ERR_SYS_NOTREADY;
    }

    /* 设置该socket在close后可重用 */
    s32Ret = setsockopt(s32SocketFd, SOL_SOCKET, SO_REUSEADDR, &s32On, sizeof(s32On));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "set socket option[SO_REUSEADDR] failed. [err=%#x]\n", errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    stName.sin_family = AF_INET;
    stName.sin_port = htons(s32Port);				                                    // 端口号转换为网络字节序
    stName.sin_addr.s_addr = htonl(INADDR_ANY);		                                    // 地址转换为网络字节序

    /* 将socket绑定到指定端口 */
    s32Ret = bind(s32SocketFd, (struct sockaddr *)&stName, sizeof(stName));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "httpd bind socket to port: %d failed. [err=%#x]\n", s32Port, errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    /* 监听socket，等待客户端连接请求，最大连接数5 */
    s32Ret = listen(s32SocketFd, 5);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "listen httpd failed. [err=%#x]\n", errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    *ps32SocketFd = s32SocketFd;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建udp mjpeg socket
 * 输入参数: s32Port --- 所监听的端口
 * 输出参数: ps32SocketFd --- socket fd
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
sint32 http_CreateUdpd(sint32 s32Port, sint32 *ps32SocketFd)
{
    sint32 s32Ret = 0;
    sint32 s32SocketFd = 0;
    sint32 s32On = 1;
    struct sockaddr_in stLocalAddr = {0};

    if (NULL == ps32SocketFd)
    {
        return ERR_ILLEGAL_PARAM;
    }

    /* 创建socket    协议族(ipv4)，类型（UDP），协议（0为根据协议族和类型自动选择）*/
    s32SocketFd = socket(AF_INET, SOCK_DGRAM, 0);
    if (s32SocketFd < 0)
    {
        print_level(SV_ERROR, "create socket for httpd failed. [err=%#x]\n", errno);
        return ERR_SYS_NOTREADY;
    }

    /* 设置该socket在close后可重用 */
    s32Ret = setsockopt(s32SocketFd, SOL_SOCKET, SO_REUSEADDR, &s32On, sizeof(s32On));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "set socket option[SO_REUSEADDR] failed. [err=%#x]\n", errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    // 本地ip地址初始化
    stLocalAddr.sin_family = AF_INET;
    stLocalAddr.sin_port = htons(s32Port);				                                // 端口号转换为网络字节序
    stLocalAddr.sin_addr.s_addr = htonl(INADDR_ANY);		                            // 地址转换为网络字节序

    // 将socket绑定到指定端口
    s32Ret = bind(s32SocketFd, (struct sockaddr *)&stLocalAddr, sizeof(stLocalAddr));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "httpd bind socket to port: %d failed. [err=%#x]\n", s32Port, errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    s32Ret = setsockopt(s32SocketFd, SOL_SOCKET, SO_REUSEPORT, &s32On, sizeof(s32On));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "set socket option[SO_REUSEPORT] failed. [err=%#x]\n", errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    *ps32SocketFd = s32SocketFd;

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 创建udp socket
 * 输入参数: s32Port --- 所监听的端口
 * 输出参数: ps32SocketFd --- socket fd
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
sint32 http_CreateUdpMulticastd(sint32 s32Port, sint32 *ps32SocketFd)
{
    sint32 s32Ret = 0;
    sint32 s32SocketFd = 0;
    sint32 s32On = 1;
    sint32 s32Loop = 1;
    struct sockaddr_in stLocalAddr = {0};
    struct sockaddr_in stgroupAddr = {0};
    struct ip_mreq stMreq = {0};

    if (NULL == ps32SocketFd)
    {
        return ERR_ILLEGAL_PARAM;
    }

    /* 创建socket    协议族(ipv4)，类型（UDP），协议（0为根据协议族和类型自动选择）*/
    s32SocketFd = socket(AF_INET, SOCK_DGRAM, 0);
    if (s32SocketFd < 0)
    {
        print_level(SV_ERROR, "create socket for httpd failed. [err=%#x]\n", errno);
        return ERR_SYS_NOTREADY;
    }

    /* 设置该socket在close后可重用 */
    s32Ret = setsockopt(s32SocketFd, SOL_SOCKET, SO_REUSEADDR, &s32On, sizeof(s32On));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "set socket option[SO_REUSEADDR] failed. [err=%#x]\n", errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

#if 1
    // 本地ip地址初始化
    stLocalAddr.sin_family = AF_INET;
    stLocalAddr.sin_port = htons(s32Port);				                                // 端口号转换为网络字节序
    stLocalAddr.sin_addr.s_addr = htonl(INADDR_ANY);		                            // 地址转换为网络字节序

    // 将socket绑定到指定端口
    s32Ret = bind(s32SocketFd, (struct sockaddr *)&stLocalAddr, sizeof(stLocalAddr));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "httpd bind socket to port: %d failed. [err=%#x]\n", s32Port, errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    s32Ret = setsockopt(s32SocketFd, SOL_SOCKET, SO_REUSEPORT, &s32On, sizeof(s32On));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "set socket option[SO_REUSEPORT] failed. [err=%#x]\n", errno);
        close(s32SocketFd);
        return ERR_SYS_NOTREADY;
    }

    // 设置回环许可
    s32Ret = setsockopt(s32SocketFd, IPPROTO_IP, IP_MULTICAST_LOOP, &s32Loop, sizeof(s32Loop));
    if(s32Ret != 0)
    {
        print_level(SV_ERROR, "setsockopt: IP_MULTICAST_LOOP failed! %s\n ", strerror(errno));
        return SV_FAILURE;
    }

    do
    {
        print_level(SV_WARN, "Network not inited!\n ");
        sleep(5);
    }
    while(!m_stHttpInfo.bNetworkInited);

    // 加入多播组
    stMreq.imr_multiaddr.s_addr = inet_addr("***************");
    stMreq.imr_interface.s_addr = htonl(INADDR_ANY);
    if((sint32)stMreq.imr_multiaddr.s_addr == -1)
    {
        print_level(SV_ERROR, "*************** not a legal multicast address! %s\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = setsockopt(s32SocketFd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &stMreq, sizeof(stMreq));
    if( s32Ret != 0 )
    {
        print_level(SV_ERROR, "setsockopt: IP_ADD_MEMBERSHIP failed! %s\n ", strerror(errno));
        return SV_FAILURE;
    }

#endif
    *ps32SocketFd = s32SocketFd;

    return SV_SUCCESS;
}



/******************************************************************************
 * 函数功能: 销毁http服务器socket
 * 输入参数: s32SocketFd --- socket fd
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
sint32 http_DestroyHttpd(sint32 s32SocketFd)
{
    sint32 s32Ret = 0;

    if (s32SocketFd < 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

    close(s32SocketFd);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 切换webui资源路径
 * 输入参数: pszSid --- 会话ID
 * 输出参数: 无
 * 返回值  : SV_TRUE - 有效
             SV_FALSE - 无效
 * 注意      : 无
 *****************************************************************************/
static sint32 http_switch_source_path()
{
#if (defined(BOARD_ADA32V4) || defined(BOARD_ADA32V2))
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        print_level(SV_INFO, "switch webui path:%s -> %s\n", http_common_resource_path, http_A32_201623_resource_path);
        http_common_resource_path = http_A32_201623_resource_path;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 判断SID是否在会话表中有效
 * 输入参数: pszSid --- 会话ID
 * 输出参数: 无
 * 返回值  : SV_TRUE - 有效
             SV_FALSE - 无效
 * 注意      : 无
 *****************************************************************************/
SV_BOOL http_IsSidValid(char *pszSid)
{
    sint32 i;
    time_t tNow = time(NULL);

    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_SHWJ))
    {
        return SV_TRUE;
    }

    if (NULL == pszSid)
    {
        return SV_FALSE;
    }

    for (i = 0; i < HTTP_MAX_SESSION; i++)
    {
        if (m_stHttpInfo.astSessionList[i].bValid)
        {
            /* 超时30分钟无会话则认为无效 */
            if (abs(tNow - m_stHttpInfo.astSessionList[i].tLastTime) > 1800)
            {
                m_stHttpInfo.astSessionList[i].bValid = SV_FALSE;
            }
            else if (0 == strcmp(m_stHttpInfo.astSessionList[i].szSid, pszSid))
            {
                m_stHttpInfo.astSessionList[i].tLastTime = tNow;
                return SV_TRUE;
            }
        }
    }

    return SV_FALSE;
}


/******************************************************************************
 * 函数功能: 判断会话表是否满
 * 输入参数: pszSid --- 会话ID
 * 输出参数: 无
 * 返回值  : SV_TRUE - 已满
             SV_FALSE - 未满
 * 注意      : 无
 *****************************************************************************/
SV_BOOL http_IsSessionFull()
{
    sint32 i;

    for (i = 0; i < HTTP_MAX_SESSION; i++)
    {
        if (!m_stHttpInfo.astSessionList[i].bValid)
        {
            return SV_FALSE;
        }
    }

    return SV_TRUE;
}


/******************************************************************************
 * 函数功能: 向会话列表增加有效会话
 * 输入参数: pstSession --- 会话信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
sint32 http_AddSession(HTTP_SESSION_S *pstSession)
{
    sint32 i;

    for (i = 0; i < HTTP_MAX_SESSION; i++)
    {
        if (!m_stHttpInfo.astSessionList[i].bValid)
        {
            m_stHttpInfo.astSessionList[i] = *pstSession;
            return SV_SUCCESS;
        }
    }

    print_level(SV_ERROR, "no more free session for adding.\n");

    return SV_FAILURE;
}


/******************************************************************************
 * 函数功能: 根据会话ID删除会话列表里的会话
 * 输入参数: pszSid --- 会话ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
sint32 http_DelSession(char *pszSid)
{
    sint32 i;

    for (i = 0; i < HTTP_MAX_SESSION; i++)
    {
        if (m_stHttpInfo.astSessionList[i].bValid && 0 == strcmp(m_stHttpInfo.astSessionList[i].szSid, pszSid))
        {
            m_stHttpInfo.astSessionList[i].bValid = SV_FALSE;
            return SV_SUCCESS;
        }
    }

    print_level(SV_WARN, "not found session id=%s.\n", pszSid);

    return SV_FAILURE;
}


/******************************************************************************
 * 函数功能: 在数据块中查找出分割字符串
 * 输入参数: pu8Data --- 数据块地址
             u32Len --- 数据块长度
             pszBoundary --- 分割字符串
 * 输出参数: 无
 * 返回值  : NULL - 未找到
             非NULL - 分割字符串地址
 * 注意      : 无
 *****************************************************************************/
char * http_FindBoundary(uint8 *pu8Data, uint32 u32Len, char *pszBoundary)
{
    sint32 i, j;
    uint32 u32StrLen = 0;

    if (NULL == pu8Data || 0 == u32Len || NULL == pszBoundary)
    {
        return NULL;
    }

    u32StrLen = strlen(pszBoundary);
    for (i = 0; i < u32Len; i++)
    {
        for (j = 0; j < u32StrLen; j++)
        {
            if (pu8Data[i+j] != pszBoundary[j])
            {
                break;
            }
        }
        if (j >= u32StrLen)
        {
            return &pu8Data[i];
        }
    }

    return NULL;
}


/******************************************************************************
 * 函数功能: 获取某个文件的md5码值
 * 输入参数: pszFilePath --- 文件路径
 * 输出参数: pszMd5 --- md5码字符串
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
sint32 http_GetFileMd5(const char *pszFilePath, char *pszMd5)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    char szCmd[128];
    char szMd5[40] = {0};
    char *pszTmp = NULL;
    char *pszCmdRes = "/var/cmd";

    if (NULL == pszFilePath || NULL == pszMd5)
    {
        return SV_FAILURE;
    }

    sprintf(szCmd, "md5sum %s > %s", pszFilePath, pszCmdRes);
    s32Ret = SAFE_System(szCmd, 60000);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    s32Fd = open(pszCmdRes, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszCmdRes, strerror(errno));
        return SV_FAILURE;
    }
    read(s32Fd, szMd5, 32);
    szMd5[32] = '\0';
    close(s32Fd);
    strcpy(pszMd5, szMd5);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 查找字符串的尾部位置
 * 输入参数:
 * 输出参数:
 * 返回值  :

 * 注意      : 无
 *****************************************************************************/
int findStrTailPos(uint8* byteArray,uint32 len,char* strIndex)
{
	int findpos = 0;
	for (int i = 0; i < len; i++)
	{
		int find = 1;
		for(int j=0;j<strlen(strIndex);j++)
		{
				if(strIndex[j] != byteArray[i+j])
				{
					find = 0;
					break;
				}
		}
		if(find)
		{
			findpos = i+strlen(strIndex);
			break;
		}
	}

	return findpos;
}


/******************************************************************************
 * 函数功能: 查找字符串的前部位置
 * 输入参数:
 * 输出参数:
 * 返回值  :

 * 注意      : 无
 *****************************************************************************/
int findStrHeaderPos(uint8* byteArray,uint32 len,char* strIndex)
{
	int findpos = 0;
	for (int i = 0; i < len; i++)
	{
		int find = 1;
		for(int j=0;j<strlen(strIndex);j++)
		{
			if(strIndex[j] != byteArray[i+j])
			{
				find = 0;
				break;
			}
		}
		if(find)
		{
			findpos = i;
			break;
		}
	}
	return findpos;
}


/******************************************************************************
 * 函数功能: 处理客户端http请求线程体
 * 输入参数: pvArg --- client fd
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
void * http_HandleRequest(void *pvArg)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32Size = 0;
    sint32 s32Fd = -1;
    sint32 s32ClientFd = (sint32)pvArg;
    uint32 u32RecvSize = 0;
    uint32 u32ResSize = 0;
    uint8 *pu8Buf = NULL;
    char *pszSid = NULL;
    char *pszETag = NULL;
    char *pszTmp = NULL;
    char szHeader[2048];
    char szFilePath[512];
    char szJsonOut[512];
    char szJsonBody[20*1024];
    char szCmd[128] = {0};
    char szBuf[256] = {0};
    char szPath[128] = {0};
    HTTP_HEADER_S stHttpHeader = {0};
    fd_set read_fds;
    struct timeval stTimeval = {0};
    int bHeader = SV_FALSE;
    int header_length = 0, content_length = 0;
    SV_BOOL bSnap = SV_FALSE;
    SV_BOOL bWarnSnap = SV_FALSE;
    SV_BOOL bRecSnap = SV_FALSE;

    s32Ret = prctl(PR_SET_NAME, "http_handle");		                                // 设置进程名称
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    pu8Buf = (uint8 *)malloc(HTTP_BUF_SIZE);
    if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc memory for http request failed.\n");
        goto exit1;
    }

    memset(pu8Buf, 0x0, HTTP_BUF_SIZE);

    stTimeval.tv_sec = 1;
    stTimeval.tv_usec = 0;
    setsockopt(s32ClientFd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));		// 发送时限
    setsockopt(s32ClientFd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));		// 接收时限

	while (u32RecvSize < HTTP_BUF_SIZE)
    {
        FD_ZERO(&read_fds);					                                        // 清空文件描述符集合read_fds
	    FD_SET(s32ClientFd, &read_fds);		                                        // 将s32ClientFd加入read_fds
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);		// 监视read_fds状态
        if (s32Ret <= 0)
        {
            break;
        }

        /* 将接收的数据存入buffer             接收数据存放区域               接收数据buffer的长度 */
        s32Size = recv(s32ClientFd, pu8Buf + u32RecvSize, HTTP_BUF_SIZE - u32RecvSize, 0);
        if (s32Size <=0)
        {
        	print_level((s32Size < 0) ? SV_ERROR : SV_WARN, "libhttp receive error. [err: %s]\n", strerror(errno));
            break;
        }

        u32RecvSize += s32Size;
#if (defined(BOARD_ADA32V4) || (defined(BOARD_DMS31V2)) \
    || (defined(BOARD_ADA32V2)) || defined(BOARD_ADA32V3) || (defined(BOARD_ADA32IR)) || defined(BOARD_ADA32N1) \
    || (defined(BOARD_IPCR20S3)) || (defined(BOARD_IPCR20S4)) || (defined(BOARD_IPCR20S5)) \
    || (defined(BOARD_ADA47V1)) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_IPTR20S1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))

        if(!bHeader && ((uint8 *)strstr((char *)pu8Buf, "\r\n\r\n") !=NULL ))    /* 检测尾部 */
        {
            char *lengthbuf;
            bHeader = SV_TRUE;  /* 完整读取到头部 */
            header_length = ((uint8 *)strstr((char *)pu8Buf, "\r\n\r\n") - pu8Buf);
            header_length += strlen("\r\n\r\n");  /* 获取尾部长度 */

            lengthbuf = strstr((char *)pu8Buf, "Content-Length: ");
            if(lengthbuf == NULL)   /* 只有头部没有内容 */
            {
                break;
            }

            lengthbuf = lengthbuf + strlen("Content-Length: ");
            content_length = atoi(lengthbuf);
        }

        if(bHeader && u32RecvSize >= (header_length + content_length))
        {
            break;
        }

#endif
    }

    if (0 == u32RecvSize)
    {
        goto exit;
    }

    //print_level(SV_DEBUG, "[u32RecvSize=%d]\n%s\n", u32RecvSize, pu8Buf);
    memcpy(szHeader, pu8Buf, 2048);

    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader((uint8*)szHeader, 2048, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }

	if (NULL != stHttpHeader.pszCookie)
	{
		pszSid = strstr(stHttpHeader.pszCookie, "sid=");		// 找到"sid="在pszCookie中首次出现的地址
		if (NULL != pszSid)
        {
            pszSid += strlen("sid=");
            *(pszSid+36) = '\0';
        }
	}

    if ((NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "GET")) &&
        (0 == strcmp(stHttpHeader.pszUrl, "/") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".html") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".htm") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".css") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".js") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".jpg") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".jpeg") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".png") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".gif") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".mp4") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".flv") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".swf") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".map") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".svg") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".ttf") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".woff") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".woff2") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".eot") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".vtt") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".txt") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".ico")))
    {
        //print_level(SV_DEBUG, "%s\n", pu8Buf);
        if (0 != access(HTTP_RESOURCE_PATH, F_OK))
	    {
        #if (defined(BOARD_IPCR20S3))
            print_level(SV_WARN, "/root/webui not found! Trying to uncompress one.\n");
            if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
            {
                print_level(SV_ERROR, "Disable FlashProtection failed.\n");
            }

            s32Ret = SAFE_System("tar -zxf /root/webui.tar.gz -C /root/", MEDIUM_WAIT_TIME);
	        if (0 != s32Ret)
	        {
	            print_level(SV_ERROR, "szCmd: tar -zxf /root/webui.tar.gz -C /root/ failed. [err=%d]\n", s32Ret);
	        }

	        if (SV_SUCCESS != CONFIG_FlashProtection(SV_TRUE))
            {
                print_level(SV_ERROR, "Enable FlashProtection failed.\n");
            }
        #else
            print_level(SV_WARN, "/var/webui not found! Trying to uncompress one.\n");
	        s32Ret = SAFE_System("tar -zxf /root/webui.tar.gz -C /var/", NORMAL_WAIT_TIME);
	        if (0 != s32Ret)
	        {
	            print_level(SV_ERROR, "szCmd: tar -zxf /root/webui.tar.gz -C /var/ failed. [err=%d]\n", s32Ret);
	        }
	    #endif
	    }

	    if (NULL != strcasestr(stHttpHeader.pszUrl, "snap0.jpeg"))
	    {
            bSnap = SV_TRUE;
	        strcpy(szFilePath, "/var/snap/");
	    }
        else if (NULL != strcasestr(stHttpHeader.pszUrl, WARN_SANP_PATH))
        {
            bWarnSnap = SV_TRUE;
        }
        else if (NULL != strcasestr(stHttpHeader.pszUrl, "picture"))
        {
            bRecSnap = SV_TRUE;
        }
/*
		else if (NULL != strcasestr(stHttpHeader.pszUrl, "test.flv"))
	    {
	        strcpy(szFilePath, "/mnt/");
	    }
*/
        else if (NULL != strcasestr(stHttpHeader.pszUrl, "system_time.txt"))
	    {
	        strcpy(szFilePath, "/root/");
	    }
	    else
	    {
            strcpy(szFilePath, HTTP_RESOURCE_PATH);
        }
#if 0
        if (NULL != strcasestr(stHttpHeader.pszUrl, "imudata.txt"))
        {
            strcpy(szFilePath, "/var/imudata/");
        }
#endif
		// 没有指定URL默认打开index.html
        if (0 == strcmp(stHttpHeader.pszUrl, "/"))
	    {
	        strcat(szFilePath, "/index.html");
	    }
        else
	    {
            if (bWarnSnap || bRecSnap)
            {
                strcpy(szFilePath, stHttpHeader.pszUrl);
            }
            else
            {
    	    	strcat(szFilePath, stHttpHeader.pszUrl);
    	        pszTmp = strstr(szFilePath, "?");
    	        if (NULL != pszTmp)
    	        {
    	            *pszTmp = '\0';
    	        }
            }
	    }

	    if (NULL != stHttpHeader.pszIfNotMatch)
		{
			pszETag = stHttpHeader.pszIfNotMatch + 1;
			*(pszETag+strlen(pszETag)-1) = '\0';
		}

        if (!http_IsSidValid(pszSid)
            && NULL == strcasestr(stHttpHeader.pszUrl, "login.html")
		    && NULL == strcasestr(stHttpHeader.pszUrl, ".css")
		    && NULL == strcasestr(stHttpHeader.pszUrl, ".js")
            && NULL == strcasestr(stHttpHeader.pszUrl, ".png")
            && NULL == strcasestr(stHttpHeader.pszUrl, "jquery")
            && NULL == strcasestr(stHttpHeader.pszUrl, "ajax")
            && NULL == strcasestr(stHttpHeader.pszUrl, "favicon.ico")
            && NULL == strcasestr(stHttpHeader.pszUrl, "imudata.txt")
            && NULL == strcasestr(stHttpHeader.pszUrl, "font")
            && NULL == strcasestr(stHttpHeader.pszUrl, "logo")
            && NULL == strcasestr(stHttpHeader.pszUrl, "snap0.jpeg")
            && NULL == strcasestr(stHttpHeader.pszUrl, WARN_SANP_PATH)
            && NULL == strcasestr(stHttpHeader.pszUrl, ".mp4")
            && NULL == strcasestr(stHttpHeader.pszUrl, ".jpg")
            && NULL == strcasestr(stHttpHeader.pszUrl,"no_password.html"))
        {
            print_level(SV_WARN, "unauthorized request: %s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);
            sprintf((char *)pu8Buf, "HTTP/1.1 302 Move temporarily\r\n"
                                    "Content-Type: text/html\r\n"
                                    "Connection: close\r\n"
                                    "Pramga: no-cache\r\n"
                                    "Cache-Control: no-cache, no-store\r\n"
        						    "Location: /login.html \r\n\r\n");
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
        }
        else if (http_IsSidValid(pszSid) && NULL != strcasestr(stHttpHeader.pszUrl, "login.html"))
        {
            sprintf((char *)pu8Buf, "HTTP/1.1 302 Move temporarily\r\n"
                                    "Content-Type: text/html\r\n"
                                    "Connection: close\r\n"
                                    "Pramga: no-cache\r\n"
                                    "Cache-Control: no-cache, no-store\r\n"
        						    "Location: /index.html \r\n\r\n");
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
        }
        else if (NULL != pszETag && HTTP_HDL_IsMatchETag(szFilePath, pszETag))
        {
        	sprintf((char *)pu8Buf, "HTTP/1.1 304 Not Modified\r\n"
                                    "ETag: \"%s\"\r\n\r\n", pszETag);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
        }
        else
        {
        	sint32 s32BeginOffset = -1;
        	sint32 s32EndOffset = -1;
        	if (NULL != stHttpHeader.pszRange)
        	{
        		char *pszBegin = NULL;
        		char *pszEnd = NULL;

        		pszBegin = stHttpHeader.pszRange;
        		if (NULL != pszBegin)
        		{
        			s32BeginOffset = atoi(pszBegin);
        			pszEnd = strstr(pszBegin, "-");
        			if (NULL != pszEnd)
        			{
        				pszEnd++;
        				if (*pszEnd != '\0')
        				{
        					s32EndOffset = atoi(pszEnd);
        				}
        			}
        			print_level(SV_INFO, "request %s range: %d-%d\n", szFilePath, s32BeginOffset, s32EndOffset);
        		}
        	}

            if (bSnap)
            {
                HTTP_HDL_ReplySnapJpeg(s32ClientFd);
            }
            else
            {
                HTTP_HDL_ReplyByFile(SV_FALSE, NULL, s32ClientFd, szFilePath, s32BeginOffset, s32EndOffset);
            }
            if (bWarnSnap)
            {
                snprintf(szCmd, 128, "rm -f %s", szFilePath);
                SAFE_System(szCmd, NORMAL_WAIT_TIME);
            }
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "login"))
    {
        HTTP_SESSION_S stSession;
        static time_t tLastLoginTime = 0;
        time_t tNow = time(NULL);

        print_level(SV_DEBUG, "%s\n", pu8Buf);
        if (http_IsSessionFull() || tLastLoginTime == tNow)
        {
            snprintf((char *)pu8Buf, HTTP_BUF_SIZE - 1, "HTTP/1.1 503 Service Unavailable \r\n"
                            						    "Content-Type: text/plain \r\n"
                            						    "Content-Length: 0 \r\n\r\n");
        }
        else
        {
            tLastLoginTime = tNow;
            s32Ret = HTTP_HDL_Authentication(pu8Buf, u32RecvSize, pu8Buf, &stSession);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "HTTP_HDL_Authentication failed. [err=%#x]\n", s32Ret);
            }
            else
            {
                http_AddSession(&stSession);
                sendHttpLog(s32ClientFd, "login");
            }
        }
        print_level(SV_DEBUG, "%s\n", pu8Buf);
        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "logout"))
    {
        if (!http_IsSidValid(pszSid) || SV_SUCCESS != http_DelSession(pszSid))
        {
            print_level(SV_ERROR, "logout error!\n");
            snprintf((char *)pu8Buf, HTTP_BUF_SIZE - 1, "HTTP/1.1 500 Internal Server Error \r\n"
                            							"Content-Type: application/json; charset=UTF-8 \r\n"
                            							"Content-Length: 2 \r\n\r\n{}");
        }
        else
        {
            print_level(SV_INFO, "logout successful!\n");
            sendHttpLog(s32ClientFd, "logout");
            snprintf((char *)pu8Buf, HTTP_BUF_SIZE - 1, "HTTP/1.1 200 OK \r\n"
                            							"Content-Type: application/json; charset=UTF-8 \r\n"
                            							"Content-Length: 2 \r\n\r\n{}");
        }
        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "firmware"))
    {
        uint32 u32SizeCnt = 0, i;
        char szBuf[128*1024+64];
        char szSplit[64];
        char *pszFileName = NULL;
#if defined(BOARD_DMS31V2)
        char *pszTmpFile = NULL;
        if (BOARD_DMS31V2_V1 == BOARD_GetVersion())
            pszTmpFile = "/mnt/sdcard/tmp-upgrade-packet";
        else if (BOARD_DMS31V2_V2 == BOARD_GetVersion())
            pszTmpFile = "/mnt/udisk/tmp-upgrade-packet";
#elif defined(BOARD_ADA47V1)
        char *pszTmpFile = "/userdata/tmp-upgrade-packet";
#elif defined(BOARD_IPCR20S5)
        char *pszTmpFile = "/customer/boot/tmp-upgrade-packet";
#else
        char *pszTmpFile = "/boot/tmp-upgrade-packet";
#endif
        sint32 s32Fd = 0;
        uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
        char *pszPktPrefix = HTTP_PKT_PREFIX;
		char szRealMd5sum[64];
        char szMd5sum[64];
        uint32 u32MaxFileLen = 10*1024*1024;

#if (defined(BOARD_DMS31V2) || defined(BOARD_HDW845V1))
        if (BOARD_DMS31V2_V2 == BOARD_GetVersion())
        {
            if (!STORAGE_IsWritable(STORAGE_EXTRA_SD))
            {
                print_level(SV_ERROR, "no storage device for packet.\n");
                HTTP_HDL_GenerateErrorReply(MSG_DEVICE_LACK, pu8Buf);
    			send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
    			goto exit;
            }
        }
        else
        {
            if (!STORAGE_IsWritable(STORAGE_MAIN_SD1))
            {
                print_level(SV_ERROR, "no storage device for packet.\n");
                HTTP_HDL_GenerateErrorReply(MSG_DEVICE_LACK, pu8Buf);
    			send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
    			goto exit;
            }
        }
#elif defined(BOARD_ADA47V1)
        if (!STORAGE_IsWritable(STORAGE_INNER_EMMC))
        {
            print_level(SV_ERROR, "no storage device for packet.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_LACK, pu8Buf);
			send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
			goto exit;
        }
#endif

#if defined(BOARD_WFTR20S3)
        sleep(5);
#endif

#if (defined(BOARD_ADA32V4) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
        u32MaxFileLen = 74*1024*1024;
#elif ( defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
        u32MaxFileLen = 256*1024*1024;
#endif

        if (m_stHttpInfo.bWriting)
        {
            print_level(SV_ERROR, "it is writing frimware now.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
        if (atoi(stHttpHeader.pszContentLenght) > u32MaxFileLen)
        {
            print_level(SV_ERROR, "file size[%s] is too larged! [limit:%d]\n", stHttpHeader.pszContentLenght, u32MaxFileLen);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        sprintf(szSplit, "\r\n--%s", stHttpHeader.pszBoundary);
        pszTmp = strstr((char *)pu8Buf, szSplit);
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found split: %s\n", szSplit);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

		pszTmp = strstr((char *)pu8Buf, "\MD5\"\r\n\r\n");
		if (NULL == pszTmp)
		{
			print_level(SV_ERROR, "not found file md5.\n");
			HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
			send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
			goto exit;
		}
		strncpy(szRealMd5sum,pszTmp+strlen("\MD5\"\r\n\r\n"),32);
		print_level(SV_DEBUG,"RealMD5sum:%s\n",szRealMd5sum);

		pszTmp++;
        pszTmp = strstr((char *)pu8Buf, "filename=\"");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found filename.\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pszFileName = pszTmp+strlen("filename=\"");
        pszTmp = strstr(pszFileName, "\"");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found filename.\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        *pszTmp = '\0';
        pszTmp++;

#if defined(BOARD_ADA47V1)
        if (NULL == strstr(pszFileName, pszPktPrefix) && NULL == strstr(pszFileName, "KBA12C_upgrade") && NULL == strstr(pszFileName, "KBA18E_upgrade"))
#else
        if (NULL == strstr(pszFileName, pszPktPrefix))
#endif
        {
            print_level(SV_ERROR, "invalid packet file: %s\n", pszFileName);
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pszTmp = strstr(pszTmp, "\r\n\r\n");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found data body.\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
#if (!defined(BOARD_ADA47V1))
        if (!STORAGE_IsWritable(STORAGE_MAIN_SD1))
#else
        if (!STORAGE_IsWritable(STORAGE_INNER_EMMC))
#endif
        {
            print_level(SV_ERROR, "no storage device for packet.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_LACK, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }
#endif

        if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
        {
            print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
            goto exit;
        }

#if defined(BOARD_DMS31V2)
        if (BOARD_DMS31V2_V1 == BOARD_GetVersion())
            sprintf(szFilePath, "/mnt/sdcard/%s", pszFileName);
        else if (BOARD_DMS31V2_V2 == BOARD_GetVersion())
            sprintf(szFilePath, "/mnt/udisk/%s", pszFileName);
#elif defined(BOARD_ADA47V1)
        sprintf(szFilePath, "/userdata/%s", pszFileName);
#elif defined(BOARD_IPCR20S5)
        sprintf(szFilePath, "/customer/boot/%s", pszFileName);
#else
        sprintf(szFilePath, "/boot/%s", pszFileName);
#endif
        remove(pszTmpFile);
        s32Fd = open(pszTmpFile, O_CREAT|O_RDWR);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            CONFIG_FlashProtection(SV_TRUE);
            goto exit;
        }

        sendHttpLog(s32ClientFd, "begin download firmware");
        m_stHttpInfo.bWriting = SV_TRUE;
        s32Ret = Msg_submitEvent(EP_RTSPSERVER, OP_EVENT_DOWNLOAD_FIRMWARE, NULL);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
        }
        sleep_ms(500);
#if (defined(BOARD_WFCR20S2))
        s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MCU_STOP_SLEEP, NULL);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
        }
        sleep_ms(100);
#endif
        pu8DataBegin = pszTmp + strlen("\r\n\r\n");
        pu8DataEnd = http_FindBoundary(pu8DataBegin, pu8Buf+u32RecvSize-pu8DataBegin, szSplit);
        if (NULL == pu8DataEnd)
        {
            pu8DataEnd = pu8Buf+u32RecvSize;
        }

        write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
        while (1)
        {
            stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            FD_ZERO(&read_fds);
    	    FD_SET(s32ClientFd, &read_fds);
            s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
            if (s32Ret <= 0)
            {
                print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
                break;
            }

            stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            s32Size = recv(s32ClientFd, szBuf, 128*1024, 0);
            if (s32Size <=0)
           {
                print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
                break;
            }

            pu8DataEnd = http_FindBoundary(szBuf, s32Size, szSplit);
            if (NULL == pu8DataEnd)
            {
                write(s32Fd, szBuf, s32Size);
                sync();     // 没有每次sync的话，仅靠最后sync一次会导致venc喂狗超时，进程退出
            }
            else
            {
                pu8DataBegin = szBuf;
                write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
                sync();
                break;
            }

            u32SizeCnt += s32Size;
        }
        close(s32Fd);
        s32Ret = rename(pszTmpFile, szFilePath);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "rename %s %s failed.\n", pszTmpFile, szFilePath);
        }
        sync();
        m_stHttpInfo.bWriting = SV_FALSE;

        sendHttpLog(s32ClientFd, "download firmware successful");
        if (strlen(szRealMd5sum) == 32)
        {
            s32Ret = http_GetFileMd5(szFilePath, szMd5sum);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "http_GetFileMd5 failed. [err=%#x]\n", s32Ret);
                remove(szFilePath);
                CONFIG_FlashProtection(SV_TRUE);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
                goto exit;
            }

			print_level(SV_DEBUG,"szRealMD5sum:%s    FileMD5sum:%s\n",szRealMd5sum,szMd5sum);
            if (0 != strcmp(szRealMd5sum, szMd5sum))
            {
                print_level(SV_ERROR, "md5sum unmatch exp: %s, real: %s\n", m_szPacketMd5sum, szMd5sum);
                remove(szFilePath);
                CONFIG_FlashProtection(SV_TRUE);
                HTTP_HDL_GenerateErrorReply(MSG_DATA_UNMATCH, pu8Buf);
                send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
                goto exit;
            }
        }

        CONFIG_FlashProtection(SV_TRUE);
        HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
        print_level(SV_INFO, "reboot for update firmware...\n");
        sleep_ms(1000);
        close(s32ClientFd);
        sleep_ms(1000);

#if 0
        if (0 != SAFE_System("fw_setenv update y", NORMAL_WAIT_TIME))
        {
            SAFE_SV_System("fw_setenv update y");
        }
        SAFE_System("sync", NORMAL_WAIT_TIME);
        sleep_ms(1000);
#endif
        //SAFE_System("umount -l /var", NORMAL_WAIT_TIME);
        if (0 != SAFE_System("safereboot", NORMAL_WAIT_TIME))
        {
            SAFE_SV_System("safereboot");
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "upgrade"))
    {
        uint32 u32SizeCnt = 0, i;
        char szBuf[128*1024+64];
#if defined(BOARD_DMS31V2)
        char *pszTmpFile = "/mnt/sdcard/tmp-upgrade-packet";
#elif defined(BOARD_ADA47V1)
        char *pszTmpFile = "/userdata/tmp-upgrade-packet";
#else
        char *pszTmpFile = "/boot/tmp-upgrade-packet";
#endif

        sint32 s32Fd = 0;
        uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
        char *pszPktName = HTTP_PKT_NAME;
        char szMd5sum[64];

#if defined(BOARD_WFTR20S3)
        sleep(5);
#endif

        if (m_stHttpInfo.bWriting)
        {
            print_level(SV_ERROR, "it is writing frimware now.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        print_level(SV_INFO, "firmware file size: %s\n", stHttpHeader.pszContentLenght);
        pszTmp = strstr((char *)pu8Buf, "\r\n\r\n");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found data body\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
#if (!defined(BOARD_ADA47V1))
        if (!STORAGE_IsWritable(STORAGE_MAIN_SD1))
#else
        if (!STORAGE_IsWritable(STORAGE_INNER_EMMC))
#endif
        {
            print_level(SV_ERROR, "no storage device for packet.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_LACK, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }
#endif


        if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
        {
            print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
            goto exit;
        }
        remove(pszTmpFile);
        s32Fd = open(pszTmpFile, O_CREAT|O_RDWR);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            CONFIG_FlashProtection(SV_TRUE);
            goto exit;
        }

        sendHttpLog(s32ClientFd, "begin download firmware");
        m_stHttpInfo.bWriting = SV_TRUE;
        s32Ret = Msg_submitEvent(EP_RTSPSERVER, OP_EVENT_DOWNLOAD_FIRMWARE, NULL);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
        }
        sleep_ms(500);

        pu8DataBegin = pszTmp + strlen("\r\n\r\n");
        pu8DataEnd = pu8Buf+u32RecvSize;
        write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
        while (1)
        {
            stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            FD_ZERO(&read_fds);
    	    FD_SET(s32ClientFd, &read_fds);
            s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
            if (s32Ret <= 0)
            {
                print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
                break;
            }

            stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            s32Size = recv(s32ClientFd, szBuf, 128*1024, 0);
            if (s32Size <=0)
            {
                print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
                break;
            }

            write(s32Fd, szBuf, s32Size);
            u32SizeCnt += s32Size;
        }
        close(s32Fd);
        rename(pszTmpFile, pszPktName);
        sync();
        m_stHttpInfo.bWriting = SV_FALSE;

        sendHttpLog(s32ClientFd, "download firmware successful");
        if (strlen(m_szPacketMd5sum) == 32)
        {
            s32Ret = http_GetFileMd5(pszPktName, szMd5sum);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "http_GetFileMd5 failed. [err=%#x]\n", s32Ret);
                remove(pszPktName);
                CONFIG_FlashProtection(SV_TRUE);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
                goto exit;
            }

            if (0 != strcmp(m_szPacketMd5sum, szMd5sum))
            {
                print_level(SV_ERROR, "md5sum unmatch exp: %s, real: %s\n", m_szPacketMd5sum, szMd5sum);
                remove(pszPktName);
                CONFIG_FlashProtection(SV_TRUE);
                HTTP_HDL_GenerateErrorReply(MSG_DATA_UNMATCH, pu8Buf);
                send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
                goto exit;
            }
        }

        CONFIG_FlashProtection(SV_TRUE);
        HTTP_HDL_GenerateSuccessReply("", pu8Buf);
        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
        print_level(SV_INFO, "reboot for update firmware...\n");
        sleep_ms(1000);
        close(s32ClientFd);
        sleep_ms(1000);
        //SAFE_System("umount -l /var", NORMAL_WAIT_TIME);
        if (0 != SAFE_System("safereboot", NORMAL_WAIT_TIME))
        {
            SAFE_SV_System("safereboot");
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "query_log"))
    {
        s32Ret = HTTP_HDL_QueryLog(http_IsSidValid(pszSid), pu8Buf, u32RecvSize, pu8Buf, &u32ResSize);
        if (SV_SUCCESS == s32Ret)
        {
            HTTP_HDL_ReplyByFile(SV_FALSE, NULL, s32ClientFd, "/var/log.json", -1, -1);
            remove("/var/log.json");
        }
        else if (s32Ret > 0)
        {
            s32Ret = send(s32ClientFd, pu8Buf, u32ResSize, 0);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "send to client failed. [err=%#x]\n", errno);
            }
        }
        else
        {
            print_level(SV_ERROR, "HTTP_HDL_QueryLog failed. [err=%#x]\n", s32Ret);
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "query_record"))
    {
        s32Ret = HTTP_HDL_QueryRecord(http_IsSidValid(pszSid), pu8Buf, u32RecvSize, pu8Buf, &u32ResSize);
        if (SV_SUCCESS == s32Ret)
        {
            HTTP_HDL_ReplyByFile(SV_FALSE, NULL, s32ClientFd, "/var/record.json", -1, -1);
            remove("/var/record.json");
        }
        else if (s32Ret > 0)
        {
            s32Ret = send(s32ClientFd, pu8Buf, u32ResSize, 0);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "send to client failed. [err=%#x]\n", errno);
            }
        }
        else
        {
            print_level(SV_ERROR, "HTTP_HDL_QueryLog failed. [err=%#x]\n", s32Ret);
            goto exit;
        }
    }
#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "import_userpic"))
    {
        char szHttpContent[1024];
        char szSplit[128];
        char *pszTemp = NULL;
        FILE *fp = NULL;
        int nWriteBytes = 0;
        int nOffset = 0;
        int nPosF = 0;
        int nPosB = 0;

        if (m_stHttpInfo.bWriting)
        {
            print_level(SV_ERROR, "it is writing frimware now.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        if(strstr(stHttpHeader.pszContentType, "image/jpeg") == NULL)
        {
            print_level(SV_ERROR, "%s ContentType error\n", stHttpHeader.pszContentType);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        //截取数据
        fp = fopen("/var/user.jpeg","wb");
        if(NULL == fp)
        {
            print_level(SV_ERROR, "create file failed.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        fseek(fp,nOffset,SEEK_SET);
        //截取第一段数据
        sprintf(szSplit, "\r\n\r\n");
        nPosB = findStrTailPos(pu8Buf,u32RecvSize,szSplit);
        nWriteBytes = fwrite(pu8Buf+nPosB,sizeof(char),u32RecvSize-nPosB,fp);
        nOffset += nWriteBytes;

        while (1)
        {
            stTimeval.tv_sec = 0;
            stTimeval.tv_usec = 100*1000;
            FD_ZERO(&read_fds);
            FD_SET(s32ClientFd, &read_fds);
            s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
            if (s32Ret <= 0)
            {
                print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
                break;
            }

            stTimeval.tv_sec = 0;
            stTimeval.tv_usec = 100*1000;
            memset(szHttpContent,0,128*1024);
            s32Size = recv(s32ClientFd, szHttpContent, 128*1024, 0);
            if (s32Size <=0)
            {
                print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
                break;
            }
            fseek(fp,nOffset,SEEK_SET);
            nWriteBytes = fwrite(szHttpContent,sizeof(char),s32Size,fp);
            nOffset += nWriteBytes;
        }

        fclose(fp);
        sleep_ms(100);

        HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);

    }
#endif
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1))
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "import_FaceID"))
    {
	   char szHttpContent[1024];
	   char szSplit[128];
	   char *pszTemp = NULL;
	   FILE *fp = NULL;
	   char filename[128] = {0};
	   int nWriteBytes = 0;
	   int nOffset = 0;
	   int nPosF = 0;
	   int nPosB = 0;

	   if (m_stHttpInfo.bWriting)
	   {
		   print_level(SV_ERROR, "it is writing frimware now.\n");
		   HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
		   send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
		   goto exit;
	   }

	   //获取文件名
	   sprintf(szSplit, "filename=\"");
	   nPosB = findStrTailPos(pu8Buf,u32RecvSize,szSplit);
	   sprintf(szSplit, "\"");
	   nPosF = nPosB+findStrHeaderPos(pu8Buf+nPosB,u32RecvSize-nPosB,szSplit);
	   if(nPosF-nPosB > 0)
	   {
		   strncpy(filename,(char*)(pu8Buf+nPosB),nPosF-nPosB);
	   }
	   else
	   {
		   print_level(SV_ERROR, "not found split: %s\n", szSplit);
		   HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
		   send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
		   goto exit;
	   }

	   // 解除文件系统写保护
	   if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
	   {
		   print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
           goto exit;
	   }
	   //截取数据
	   fp = fopen("/var/FaceId.tar.gz","wb");
	   if(NULL == fp)
	   {
		   print_level(SV_ERROR, "create file failed.\n");
		   CONFIG_FlashProtection(SV_TRUE);
		   HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
		   send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
		   goto exit;
	   }

	   fseek(fp,nOffset,SEEK_SET);
	   //截取第一段数据
	   sprintf(szSplit, "Content-Type: application/octet-stream\r\n\r\n");
	   nPosB = findStrTailPos(pu8Buf,u32RecvSize,szSplit);
	   nWriteBytes = fwrite(pu8Buf+nPosB,sizeof(char),u32RecvSize-nPosB,fp);
	   nOffset += nWriteBytes;

	   while (1)
	   {
           stTimeval.tv_sec = 5;
           stTimeval.tv_usec = 0;
		   FD_ZERO(&read_fds);
		   FD_SET(s32ClientFd, &read_fds);
		   s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
		   if (s32Ret <= 0)
		   {
			   print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
			   break;
		   }

		   stTimeval.tv_sec = 5;
		   stTimeval.tv_usec = 0;
		   memset(szHttpContent,0,1024);
		   s32Size = recv(s32ClientFd, szHttpContent, 1024, 0);
		   if (s32Size <=0)
		   {
			   print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
			   break;
		   }
		   fseek(fp,nOffset,SEEK_SET);
		   nWriteBytes = fwrite(szHttpContent,sizeof(char),s32Size,fp);
		   nOffset += nWriteBytes;
	   }

	   fclose(fp);
	   sleep_ms(500);

	   SAFE_System("tar -xzvf /var/FaceId.tar.gz -C /root", NORMAL_WAIT_TIME);
	   remove("/var/FaceId.tar.gz");
	   CONFIG_FlashProtection(SV_TRUE);

	   HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
	   send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
	   print_level(SV_INFO, "import FaceId success.\n");

    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "export_FaceID"))
    {
        s32Ret = SAFE_System("tar -zcvf /var/FaceId.tar.gz -C /root ID", NORMAL_WAIT_TIME);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "tar -zcvf /var/FaceId.tar.gz -C /root ID failed.\n");
        }

        sleep_ms(500);

        s32Ret = HTTP_HDL_ReplyByMultipartFormFile(SV_FALSE, NULL, s32ClientFd, pu8Buf, "/var/FaceId.tar.gz");
        remove("/var/FaceId.tar.gz");
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HTTP_HDL_ReplyByMultipartFormFile failed.\n");
            goto exit;
        }
        print_level(SV_INFO, "export FaceID success.\n");
    }
#endif
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "export_logfile"))
    {
        strcpy(szPath, "/var/log.tar.gz");
        strcpy(szCmd, "tar -zcvf /var/log.tar.gz -C /var/log .");
#if defined(BOARD_DMS31V2)
        if (STORAGE_IsWritable(STORAGE_MAIN_SD1))
        {
            strcpy(szPath, "/tmp/log.tar.gz");
            strcpy(szCmd, "tar -zcvf /tmp/log.tar.gz -C /mnt/sdcard/log .");
        }
        else if (STORAGE_IsWritable(STORAGE_EXTRA_SD))
        {
            strcpy(szPath, "/tmp/log.tar.gz");
            strcpy(szCmd, "tar -zcvf /tmp/log.tar.gz -C /mnt/udisk/log .");
        }
#elif defined(BOARD_ADA47V1)
        if (STORAGE_IsWritable(STORAGE_INNER_EMMC))
        {
            strcpy(szPath, "/tmp/log.tar.gz");
            strcpy(szCmd, "tar -zcvf /tmp/log.tar.gz -C /userdata/log .");
        }
#endif
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
        sleep_ms(1000);

        s32Ret = HTTP_HDL_ReplyByMultipartFormFile(SV_FALSE, NULL, s32ClientFd, pu8Buf, szPath);
        remove(szPath);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HTTP_HDL_ReplyByMultipartFormFile failed.\n");
            goto exit;
        }
        print_level(SV_INFO, "export logfile success.\n");
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "import_key"))
    {
        uint32 u32SizeCnt = 0, i;
        char szBuf[128*1024+64];
        char szSplit[64];
        char *pszFileName = NULL;
        char *pszTmpFile = "/var/test_license.txt";
        sint32 s32Fd = 0;
        uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
        char *pszPktPrefix = "license";
        char szCmd[64];

        print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
        if (atoi(stHttpHeader.pszContentLenght) > 10*1024*1024)
        {
            print_level(SV_ERROR, "file size[%s] is too larged!\n", stHttpHeader.pszContentLenght);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        sprintf(szSplit, "\r\n--%s", stHttpHeader.pszBoundary);
        pszTmp = strstr((char *)pu8Buf, szSplit);
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found split: %s\n", szSplit);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pszTmp = strstr((char *)pu8Buf, "filename=\"");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found filename.\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pszFileName = pszTmp+strlen("filename=\"");
        pszTmp = strstr(pszFileName, "\"");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found filename.\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        *pszTmp = '\0';
        pszTmp++;
        pszTmp = strstr(pszTmp, "\r\n\r\n");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found data body\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        sprintf(szFilePath, "/var/%s", pszFileName);
        remove(pszTmpFile);
        s32Fd = open(pszTmpFile, O_CREAT|O_RDWR, 0755);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pu8DataBegin = pszTmp + strlen("\r\n\r\n");
        pu8DataEnd = http_FindBoundary(pu8DataBegin, pu8Buf+u32RecvSize-pu8DataBegin, szSplit);
        if (NULL == pu8DataEnd)
        {
            pu8DataEnd = pu8Buf+u32RecvSize;
        }

        write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
        close(s32Fd);
        s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_CHECK_KEY_FILE, NULL, NULL, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_CHECK_KEY_FILE failed.\n");
            HTTP_HDL_GenerateErrorReply(MSG_AUTHORITY_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        // 解除文件系统写保护
        if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
        {
            print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        SAFE_System("mv /var/test_license.txt /etc/license2.txt", NORMAL_WAIT_TIME);
        CONFIG_FlashProtection(SV_TRUE);
        print_level(SV_INFO, "import key success.\n");
        HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "import_config"))
    {
		char szHttpContent[1024];
		char szSplit[128];
		char *pszTemp = NULL;
		char filename[128] = {0};
		FILE *fp = NULL;
		int nWriteBytes = 0;
		int nOffset = 0;
		int nPosF = 0;
		int nPosB = 0;

		if (m_stHttpInfo.bWriting)
        {
            print_level(SV_ERROR, "it is writing frimware now.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

    	//获取文件名
		sprintf(szSplit, "filename=\"");
		nPosB = findStrTailPos(pu8Buf,u32RecvSize,szSplit);
		sprintf(szSplit, "\"");
		nPosF = nPosB+findStrHeaderPos(pu8Buf+nPosB,u32RecvSize-nPosB,szSplit);
		if(nPosF-nPosB > 0)
		{
			strncpy(filename,(char*)(pu8Buf+nPosB),nPosF-nPosB);
		}
		else
		{
			print_level(SV_ERROR, "not found split: %s\n", szSplit);
			HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
			send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
			goto exit;
		}

        // 解除文件系统写保护
        if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
        {
            print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
            goto exit;
        }

		//截取数据
		fp = fopen("/etc/config.tar.gz","wb");
		if(NULL == fp)
		{
			print_level(SV_ERROR, "create file failed.\n");
            CONFIG_FlashProtection(SV_TRUE);
			HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
			send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
			goto exit;

		}
		fseek(fp,nOffset,SEEK_SET);
		//截取第一段数据
		sprintf(szSplit, "Content-Type: application/octet-stream\r\n\r\n");
		nPosB = findStrTailPos(pu8Buf,u32RecvSize,szSplit);
        print_level(SV_INFO, "pos: %d, recv size: %d\n", nPosB, u32RecvSize);
		nWriteBytes = fwrite(pu8Buf+nPosB,sizeof(char),u32RecvSize-nPosB,fp);
		nOffset += nWriteBytes;
		while (1)
		{
		    stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            FD_ZERO(&read_fds);
    	    FD_SET(s32ClientFd, &read_fds);
            s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
            if (s32Ret <= 0)
            {
                print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
                break;
            }

            stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            memset(szHttpContent,0,1024);
            s32Size = recv(s32ClientFd, szHttpContent, 1024, 0);
            if (s32Size <=0)
            {
                print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
                break;
            }
            fseek(fp,nOffset,SEEK_SET);
			nWriteBytes = fwrite(szHttpContent,sizeof(char),s32Size,fp);
			nOffset += nWriteBytes;
        }
        fclose(fp);
        sleep_ms(500);
        if (0 != access("/var/config", F_OK))
        {
            SAFE_System("mkdir -p /var/config", NORMAL_WAIT_TIME);
        }
        SAFE_System("rm /var/config/*", NORMAL_WAIT_TIME);
		SAFE_System("tar -zxvf /etc/config.tar.gz -C /var/config", NORMAL_WAIT_TIME);

        SV_BOOL bOldMethod = SV_FALSE;
        if (0 != access(HTTP_CONFIG_JSON_PATH, F_OK))
        {
            print_level(SV_WARN, "config file: %s is not exist!\n", HTTP_CONFIG_JSON_PATH);
            if (0 != access("/var/config/config.xml", F_OK) || 0 != access("/var/config/config_bak1.xml", F_OK) || 0 != access("/var/config/config_bak2.xml", F_OK))
            {
                print_level(SV_WARN, "config file: /var/config/config.xml is not exist!\n");
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto importConfig_exit;
            }
            else
            {
                bOldMethod = SV_TRUE;
            }
        }

        if (bOldMethod)
        {
            print_level(SV_INFO, "old import config method.\n");
            strcpy(szCmd, "mv /var/config/*.xml /etc");
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto importConfig_exit;
            }

            sprintf(szCmd, "cp %s %s", CONFIG_XML, CONFIG_TMP_PATH);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto importConfig_exit;
            }

            /* 如果是在扫码时导入配置，不马上生效 */
            if (stHttpHeader.bFactory)
            {
                HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
                print_level(SV_INFO, "import config when scan, skip JSON_HDL_ReloadConfig.\n");
                goto importConfig_exit;
            }

            s32Ret = JSON_HDL_ReloadConfig();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "JSON_HDL_ReloadConfig failed.\n");
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            }
            else
            {
                HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
                print_level(SV_INFO, "import config success.\n");
            }
        }
        else
        {
            print_level(SV_INFO, "new import config method.\n");
            s32Fd = open(HTTP_CONFIG_JSON_PATH, O_RDONLY);
            if (s32Fd <= 0)
            {
                print_level(SV_ERROR, "open %s failed.\n", HTTP_CONFIG_JSON_PATH);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto importConfig_exit;
            }

            s32Ret = read(s32Fd, szJsonBody, sizeof(szJsonBody));
            if (s32Ret <= 0)
            {
                print_level(SV_ERROR, "read %s failed.\n", HTTP_CONFIG_JSON_PATH);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto importConfig_exit;
            }
            print_level(SV_INFO, "import config:\n%s\n", szJsonBody);

            s32Ret = JSON_HDL_SetConfig(szJsonBody, szJsonOut);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "JSON_HDL_SetConfig failed.\n");
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            }
            else
            {
                HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
                print_level(SV_INFO, "import config success.\n");
            }
            close(s32Fd);
            remove(HTTP_CONFIG_JSON_PATH);

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_IPTR20S1))
            memset(szJsonBody, 0, sizeof(szJsonBody));
            memset(szJsonOut, 0, sizeof(szJsonOut));

            s32Fd = open(IMG_PARAM_JSON_PATH, O_RDONLY);
            if (s32Fd <= 0)
            {
                print_level(SV_ERROR, "open %s failed.\n", IMG_PARAM_JSON_PATH);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto importConfig_exit;
            }

            s32Ret = read(s32Fd, szJsonBody, 1024);
            if (s32Ret <= 0)
            {
                print_level(SV_ERROR, "read %s failed.\n", IMG_PARAM_JSON_PATH);
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto importConfig_exit;
            }
            print_level(SV_INFO, "set image param:\n%s\n", szJsonBody);

            s32Ret = JSON_HDL_SetImageParam(szJsonBody, szJsonOut);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "JSON_HDL_SetConfig failed.\n");
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            }
            else
            {
                HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
                print_level(SV_INFO, "import config success.\n");
            }
            close(s32Fd);
            remove(IMG_PARAM_JSON_PATH);
#endif
        }

importConfig_exit:
		CONFIG_FlashProtection(SV_TRUE);
        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "export_config"))
    {
        if (0 != access("/var/config", F_OK))
        {
            SAFE_System("mkdir -p /var/config", NORMAL_WAIT_TIME);
        }

        /* 将当前配置参数写入json */
        strcpy(szCmd, "export_config");
        s32Ret = JSON_HDL_GetConfig(szCmd, szJsonBody);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "JSON_HDL_GetConfig failed. [err: %d]\n", s32Ret);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
        }
        else
        {
            print_level(SV_INFO, "export config:\n%s\n", szJsonBody);
            sint32 s32Fd = open(HTTP_CONFIG_TMP_PATH, O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "open file: %s failed. [err:%s]\n", HTTP_CONFIG_TMP_PATH, strerror(errno));
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto exit;
            }

            s32Ret = write(s32Fd, szJsonBody, strlen(szJsonBody));
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "write file: %s failed. [err:%s]\n", HTTP_CONFIG_TMP_PATH, strerror(errno));
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                close(s32Fd);
                goto exit;
            }
            close(s32Fd);
            rename(HTTP_CONFIG_TMP_PATH, HTTP_CONFIG_JSON_PATH);
        }

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_IPTR20S1))
        memset(szJsonBody, 0, sizeof(szJsonBody));
        memset(szCmd, 0, sizeof(szCmd));

        /* 将当前图像参数写入json文件 */
        strcpy(szCmd, "image_param");
        s32Ret = JSON_HDL_GetImageParam(szCmd, szJsonBody);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "JSON_HDL_GetImageParam failed. [err: %d]\n", s32Ret);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
        }
        else
        {
            print_level(SV_INFO, "export image param:\n%s\n", szJsonBody);
            s32Fd = open(IMG_PARAM_TMP_PATH, O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "open file: %s failed. [err:%s]\n", IMG_PARAM_TMP_PATH, strerror(errno));
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                goto exit;
            }

            s32Ret = write(s32Fd, szJsonBody, strlen(szJsonBody));
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "write file: %s failed. [err:%s]\n", IMG_PARAM_TMP_PATH, strerror(errno));
                HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
                close(s32Fd);
                goto exit;
            }
            close(s32Fd);
            rename(IMG_PARAM_TMP_PATH, IMG_PARAM_JSON_PATH);
        }
#endif
        if (SV_SUCCESS != access(HTTP_CONFIG_JSON_PATH, F_OK))
        {
            print_level(SV_ERROR, "config file: %s is not exist!\n", HTTP_CONFIG_JSON_PATH);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            goto exit;
        }

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_IPTR20S1))
        if (SV_SUCCESS != access(IMG_PARAM_JSON_PATH, F_OK))
        {
            print_level(SV_ERROR, "img_param file: %s is not exist!\n", IMG_PARAM_JSON_PATH);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            goto exit;
        }

        SAFE_System("tar -zcvf /var/config.tar.gz -C /var/config ./config.json ./img_param.json", NORMAL_WAIT_TIME);
#else
        SAFE_System("tar -zcvf /var/config.tar.gz -C /var/config ./config.json", NORMAL_WAIT_TIME);
#endif
        sleep_ms(500);

        if (SV_SUCCESS != access("/var/config.tar.gz", F_OK))
        {
            print_level(SV_ERROR, "config file: /var/config.tar.gz is not exist!\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            goto exit;
        }

        s32Ret = HTTP_HDL_ReplyByMultipartFormFile(SV_FALSE, NULL, s32ClientFd, pu8Buf, "/var/config.tar.gz");
        remove("/var/config.tar.gz");
        remove(HTTP_CONFIG_JSON_PATH);
#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_IPTR20S1))
        remove(IMG_PARAM_JSON_PATH);
#endif
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HTTP_HDL_ReplyByMultipartFormFile failed.\n");
            goto exit;
        }
        print_level(SV_INFO, "export config success.\n");
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && (NULL != strcasestr(stHttpHeader.pszUrl, ".mp4")|| \
        NULL != strcasestr(stHttpHeader.pszUrl, ".avi") || NULL != strcasestr(stHttpHeader.pszUrl, ".jpg")))
    {
        print_level(SV_INFO, "try to export video file: %s\n", stHttpHeader.pszUrl);
        s32Ret = HTTP_HDL_ReplyByMultipartFormFile(SV_FALSE, NULL, s32ClientFd, pu8Buf, stHttpHeader.pszUrl);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HTTP_HDL_ReplyByMultipartFormFile failed.\n");
            goto exit;
        }
        print_level(SV_INFO, "export video file success.\n");
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "run_factory"))
    {
        uint32 u32SizeCnt = 0, i;
        char szBuf[128*1024+64];
        char szSplit[64];
        char *pszFileName = NULL;
        char *pszTmpFile = "/var/tmp-factory";
        sint32 s32Fd = 0;
        uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
        char *pszPktPrefix = "factory";
        char szCmd[64];

        if (m_stHttpInfo.bWriting)
        {
            print_level(SV_ERROR, "it is writing frimware now.\n");
            HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
        if (atoi(stHttpHeader.pszContentLenght) > 10*1024*1024)
        {
            print_level(SV_ERROR, "file size[%s] is too larged!\n", stHttpHeader.pszContentLenght);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        sprintf(szSplit, "\r\n--%s", stHttpHeader.pszBoundary);
        pszTmp = strstr((char *)pu8Buf, szSplit);
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found split: %s\n", szSplit);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pszTmp = strstr((char *)pu8Buf, "filename=\"");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found filename.\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pszFileName = pszTmp+strlen("filename=\"");
        pszTmp = strstr(pszFileName, "\"");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found filename.\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        *pszTmp = '\0';
        pszTmp++;
        if (NULL == strstr(pszFileName, pszPktPrefix))
        {
            print_level(SV_ERROR, "invalid packet file: %s\n", pszFileName);
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        pszTmp = strstr(pszTmp, "\r\n\r\n");
        if (NULL == pszTmp)
        {
            print_level(SV_ERROR, "not found data body\n");
            HTTP_HDL_GenerateErrorReply(MSG_RANGE_OUT, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        sprintf(szFilePath, "/var/%s", pszFileName);
        remove(pszTmpFile);
        s32Fd = open(pszTmpFile, O_CREAT|O_RDWR, 0755);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
            send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
            goto exit;
        }

        sendHttpLog(s32ClientFd, "begin download factory");
        m_stHttpInfo.bWriting = SV_TRUE;
        pu8DataBegin = pszTmp + strlen("\r\n\r\n");
        pu8DataEnd = http_FindBoundary(pu8DataBegin, pu8Buf+u32RecvSize-pu8DataBegin, szSplit);
        if (NULL == pu8DataEnd)
        {
            pu8DataEnd = pu8Buf+u32RecvSize;
        }

        write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
        while (1)
        {
            stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            FD_ZERO(&read_fds);
            FD_SET(s32ClientFd, &read_fds);
            s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
            if (s32Ret <= 0)
            {
                print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
                break;
            }

            stTimeval.tv_sec = 5;
            stTimeval.tv_usec = 0;
            s32Size = recv(s32ClientFd, szBuf, 128*1024, 0);
            if (s32Size <=0)
            {
                print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
                break;
            }

            pu8DataEnd = http_FindBoundary(szBuf, s32Size, szSplit);
            if (NULL == pu8DataEnd)
            {
                write(s32Fd, szBuf, s32Size);
            }
            else
            {
                pu8DataBegin = szBuf;
                write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
                break;
            }

            u32SizeCnt += s32Size;
        }
        close(s32Fd);
		print_level(SV_INFO, "old szFilePath: %s\n", szFilePath);
		strcpy(szFilePath, "/var/factory");
		print_level(SV_INFO, "new szFilePath: %s\n", szFilePath);
        rename(pszTmpFile, szFilePath);
        m_stHttpInfo.bWriting = SV_FALSE;
        print_level(SV_INFO, "run factory...\n");
        sprintf(szCmd, "%s >> /dev/null &", szFilePath);
        if (0 != SAFE_System(szCmd, NORMAL_WAIT_TIME))
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            HTTP_HDL_GenerateErrorReply(MSG_DEFAULT_FAIL, pu8Buf);
        }
        else
        {
            print_level(SV_INFO, "cmd: %s success.\n", szCmd);
            HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
        }

        send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
    }
    else
    {
        s32Ret = HTTP_HDL_MsgHandle(http_IsSidValid(pszSid), pu8Buf, u32RecvSize, pu8Buf);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "HTTP_HDL_MsgHandle failed. [err=%#x]\n", s32Ret);
            goto exit;
        }
        else {
        	u32ResSize = s32Ret;
        	if (0 == strcmp(stHttpHeader.pszMethod, "POST") && 0 == strcmp(stHttpHeader.pszUrl, "/config"))
        	{
        	    char szLog[1024];
        	    sprintf(szLog, "POST %s %s", stHttpHeader.pszUrl, stHttpHeader.pu8Body);
        	    sendHttpLog(s32ClientFd, szLog);

        	    if (m_stHttpInfo.bWriting)
                {
                    print_level(SV_ERROR, "it is writing frimware now.\n");
                    HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
                    send(s32ClientFd, pu8Buf, strlen((char *)pu8Buf), 0);
                    goto exit;
                }
        	}
       }

        print_level(SV_DEBUG, "reply: \n%s\n", (char *)pu8Buf);
        s32Ret = send(s32ClientFd, pu8Buf, u32ResSize, 0);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "send to client failed. [err=%#x]\n", errno);
            if (errno != EINTR || errno != EWOULDBLOCK || errno != EAGAIN)
            {
            	print_level(SV_ERROR, "client disconnect!! close current client now. [err: %s]\n", strerror(errno));
            }
        }
    }

exit:
    free(pu8Buf);
exit1:
    close(s32ClientFd);

    for (i = 0; i < HTTP_MAX_HTTP_THR; i++)
	{
		if (s32ClientFd == m_stHttpInfo.s32HttpReqFd[i])
		{
			m_stHttpInfo.s32HttpReqFd[i] = 0;
			break;
		}
	}

    return NULL;
}


/******************************************************************************
 * 函数功能: 处理客户端ip查找请求线程体
 * 输入参数: pvArg --- client fd
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
void * http_HandleIPSearch(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32Size = 0;
    uint32 u32Cnt = 0;
    uint32 u32RecvSize = 0;
    sint32 s32SearchFd = (sint32)pvArg;
    fd_set read_fds;
    struct timeval stTimeval = {0};
    uint8 au8RecvBuf[1024] = {0};
    uint8 au8SendBuf[1024] = {0};
    sint32 s32Loop = 1;
    sint32 s32On = 1;
    struct sockaddr_in stLocalAddr = {0};
    struct sockaddr_in stgroupAddr = {0};
    struct sockaddr_in stCntAddr = {0};
    struct ip_mreq stMreq;
    socklen_t sock_len = sizeof(struct sockaddr_in);
    CFG_NETWORK_PARAM stNetworkParam = {0};
    sint32 s32Port = 2887;

    s32Ret = prctl(PR_SET_NAME, "ip_search");                                     // 设置进程名称
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    // 多播ip地址初始化
    stgroupAddr.sin_family = AF_INET;
    stgroupAddr.sin_port = htons(s32Port);				                          // 端口号转换为网络字节序
    stgroupAddr.sin_addr.s_addr = inet_addr("***************");		              // 这里不能用htonl，否则sendto参数无效

    s32Ret = CONFIG_GetNetworkParam(&stNetworkParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetNetworkParam failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    //print_level(SV_WARN, "ip: %s\n", stNetworkParam.pszEthIpAddr);
    sprintf((char *)au8SendBuf, "<?xml version=\"1.0\" encoding=\"utf-8\"?>" \
                                "<IPAddress>%s</IPAddress>"
                                "<Gateway>%s</Gateway>" \
                                "<Submask>%s</Submask>", \
             stNetworkParam.pszEthIpAddr, stNetworkParam.pszEthGateway, stNetworkParam.pszEthSubmask);

    while (m_stHttpInfo.bRunning)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32SearchFd, &read_fds);
        s32Ret = select(s32SearchFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            if (0 == s32Ret)
                print_level(SV_WARN, "socket wait for data timeout.\n");
            break;
        }

        memset(au8RecvBuf, 0x0, 1024);

        s32Size = recvfrom(s32SearchFd, au8RecvBuf, sizeof(au8RecvBuf), 0, NULL, NULL);
        if (s32Size <= 0)
        {
			print_level(SV_ERROR, "recvfrom error: %s\n", strerror(errno));
			goto exit;
        }
        //print_level(SV_DEBUG, "sendaddr:%s, prot:%d\n", inet_ntop(AF_INET, &stCntAddr.sin_addr.s_addr,  au8RecvBuf, sizeof(au8RecvBuf)),  stCntAddr.sin_port);
        print_level(SV_DEBUG, "message: %s\n", au8RecvBuf);

        if(strstr((char *)au8RecvBuf, "stonkam ipsearch") != NULL)
        {
            print_level(SV_INFO, "receive ipsearch request.\n");
            s32Size = sendto(s32SearchFd, au8SendBuf, strlen(au8SendBuf) + 1, 0, (struct sockaddr *)&stgroupAddr, sizeof(stgroupAddr));
            if (s32Size <= 0)
            {
				print_level(SV_ERROR, "sendto error: %s\n", strerror(errno));
				goto exit;
            }
        }
    }

exit:
    close(s32SearchFd);
    return NULL;
}


/******************************************************************************
 * 函数功能: 处理快速抓图请求线程体
 * 输入参数: pvArg --- client fd
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
void * http_HandleQuickJpeg(void *pvArg)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32Size = 0;
    sint32 s32Quality = 0;
    uint32 u32Cnt = 0;
    uint32 u32RecvSize = 0;
    uint32 u32ResSize = 0;
    sint32 s32ClientFd = (sint32)pvArg;
    fd_set read_fds;
    struct timeval stTimeval = {1, 0};
    uint8 au8Buf[8*1024];
    HTTP_HEADER_S stHttpHeader = {0};
    char *pstTmp = NULL;
    char szFilePath[128];
    char szCmd[128] = {0};
    char szBuf[256] = {0};

    s32Ret = prctl(PR_SET_NAME, "quick_jpeg");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    sleep_ms(1);
    while (m_stHttpInfo.bRunning)
    {
        stTimeval.tv_sec = 5;					                                    // 阻塞时间，秒
        stTimeval.tv_usec = 0;					                                    //           微秒
        FD_ZERO(&read_fds);						                                    // 清空文件描述符集合read_fds
	    FD_SET(s32ClientFd, &read_fds);			                                    // 将s32ClientFd加入文件描述符集合
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);		// 监视读文件集read_fds状态
        if (s32Ret <= 0)
        {
            if (0 == s32Ret)
                print_level(SV_WARN, "socket wait for data timeout.\n");
            break;
        }

        memset(au8Buf, 0x0, 2048);
        u32RecvSize = 0;
        while (u32RecvSize < 2048)
        {
            s32Size = recv(s32ClientFd, au8Buf + u32RecvSize, 16*1024-u32RecvSize, MSG_DONTWAIT);
            if (s32Size <= 0)
            {
            	if(errno == EAGAIN)
            	{
            		if(u32Cnt > 500)
            		{
            			print_level(SV_ERROR, "recv timeout\n");
        				goto quick_exit;
            		}
                    usleep(1000);
                    u32Cnt++;
                    continue;
            	}
            	else
            	{
    				//print_level(SV_WARN, "recv error: %s\n", strerror(errno));
    				goto quick_exit;
            	}
            }
            if(strstr((char*)au8Buf,"\r\n\r\n")!=NULL)
            {
            	u32RecvSize = u32RecvSize+s32Size;
            	break;
            }
            u32RecvSize += s32Size;
        }

        s32Ret = HTTP_HDL_ParseHeader((uint8*)au8Buf, u32RecvSize, &stHttpHeader);
        if (SV_SUCCESS != s32Ret)
        {
        	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
        	goto quick_exit;
        }

        if (NULL == strcasestr(stHttpHeader.pszUrl, "snap") ||
            0 != strcmp(stHttpHeader.pszMethod, "GET"))
        {
            print_level(SV_ERROR, "invalid request: %s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);
        	goto quick_exit;
        }

        pstTmp = strcasestr(stHttpHeader.pszUrl, "quality=");
        if (NULL != pstTmp)
            s32Quality = atoi(pstTmp+strlen("quality="));
        else
            s32Quality = 3;
        if (NULL != strcasestr(szFilePath, "snap0.jpeg"))//避免ADA32IR的snap1.jpeg请求也会触发新连接
            HTTP_HDL_QuickJpeg(s32Quality);
        pstTmp = strcasestr(stHttpHeader.pszUrl, "?");
        if (NULL != pstTmp)
            *pstTmp = '\0';
        sprintf(szFilePath, "/var/snap/%s", stHttpHeader.pszUrl);
        if (NULL != strcasestr(szFilePath, "snap0.jpeg"))
        {
            HTTP_HDL_ReplySnapJpeg(s32ClientFd);
        }
        else
        {
            HTTP_HDL_ReplyByFile(SV_FALSE, NULL, s32ClientFd, szFilePath, -1, -1);
        }
    }

quick_exit:
    close(s32ClientFd);
    pthread_mutex_lock(&m_stHttpInfo.mutexLock);
    if (m_stHttpInfo.u32QuickHandleNum > 0)
    {
        m_stHttpInfo.u32QuickHandleNum--;
		for (i = 0; i < HTTP_MAX_QUICK_THR; i++)
		{
			if (s32ClientFd == g_s32QuickJpegFd[i])
			{
				g_s32QuickJpegFd[i] = 0;
				break;
			}
		}
    }
    pthread_mutex_unlock(&m_stHttpInfo.mutexLock);
    return NULL;
}

/******************************************************************************
 * 函数功能: 处理快速抓图请求线程体,websocket协议传输图像
 * 输入参数: pvArg --- client fd
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
void * ws_HandleQuickJpeg(void *pvArg)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32Size = 0;
    sint32 s32Quality = 0;
    uint32 u32Cnt = 0;
    sint32 s32ClientFd = (sint32)pvArg;
    fd_set read_fds;
    struct timeval stTimeval = {0};
    uint8 au8Buf[8*1024];
    HTTP_HEADER_S stHttpHeader = {0};
    char *pstTmp = NULL;
	int nSendBuf=300*1024;
	pthread_t thread;
    pthread_attr_t 	attr;
	sint32 s32Connecting = 0;
	char one_char;
	char opcode;
	int flags;
    s32Ret = prctl(PR_SET_NAME, "ws_quick_jpeg");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }
	stTimeval.tv_sec = 5;
	stTimeval.tv_usec = 0;
	setsockopt(s32ClientFd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
	setsockopt(s32ClientFd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
	setsockopt(s32ClientFd, SOL_SOCKET, SO_SNDBUF, (const char*)&nSendBuf,sizeof(int));

    sleep_ms(1);
	m_stSnapInfo.s32Flag = 0;
	while (m_stHttpInfo.bRunning)
	{
		stTimeval.tv_sec = 5;
		stTimeval.tv_usec = 0;
		FD_ZERO(&read_fds);						                                    // 清空文件描述符集合read_fds
	    FD_SET(s32ClientFd, &read_fds);			                                    // 将s32ClientFd加入文件描述符集合
	    s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);		// 监视读文件集read_fds状态
		if (s32Ret <= 0)
        {
            if (0 == s32Ret && m_stSnapInfo.s32Flag == 1)
           {
		   		m_stSnapInfo.s32Flag = 0;
            	break;
           }
			continue;
        }
		memset(au8Buf, 0x0, 2048);
        s32Size = recv(s32ClientFd, au8Buf, 2048, MSG_DONTWAIT);
        if (s32Size <= 0)
        {
        	if(errno == EAGAIN)
        	{
        		if(u32Cnt > 500)
        		{
        			print_level(SV_ERROR, "recv timeout\n");
    				goto quick_exit;
        		}
                usleep(1000);
                u32Cnt++;
                continue;
        	}
        	else
        	{
				goto quick_exit;
        	}
        }

		if(!s32Connecting)
		{
			s32Ret = WS_ShakeHand((uint8*)au8Buf, s32ClientFd, &stHttpHeader);
		    if (SV_SUCCESS != s32Ret)
		    {

		    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
		    	goto quick_exit;
		    }
			/*if (NULL == strcasestr(stHttpHeader.pszUrl, "snap") ||
					0 != strcmp(stHttpHeader.pszMethod, "GET"))
			{
				print_level(SV_ERROR, "invalid request: %s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);
				goto quick_exit;
			}

		    pstTmp = strcasestr(stHttpHeader.pszUrl, "quality=");
		    if (NULL != pstTmp)
		        s32Quality = atoi(pstTmp+strlen("quality="));
		    else*/
		    s32Quality = 3;
		    HTTP_HDL_QuickJpeg(s32Quality);
		/*	s32Ret = WS_ReplySnap((void *)s32ClientFd);
			if(SV_FAILURE == s32Ret)
			{
				goto quick_exit;
			}	*/

			pthread_attr_init(&attr);
			pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
			s32Ret = pthread_create(&thread, &attr,WS_ReplySnap, (void *)s32ClientFd);
			if (s32Ret < 0)
			{
				print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
				close(s32ClientFd);
				continue;
			}

		   s32Connecting = 1;

		}
		else
		{
			one_char = (char)au8Buf[0];
		    opcode = one_char & 0x0F;
			if(WEBSOCKET_OP_CLOSE == opcode)
			{
				//print_level(SV_WARN,"client close connection.\n",s32ClientFd);
				goto quick_exit;
			}
		}
	}
quick_exit:
    close(s32ClientFd);
	print_level(SV_WARN,"client close connection.\n",s32ClientFd);
    pthread_mutex_lock(&m_stHttpInfo.mutexLock);
    if (m_stHttpInfo.u32QuickHandleNum > 0)
    {
        m_stHttpInfo.u32QuickHandleNum--;
		for (i = 0; i < HTTP_MAX_QUICK_THR; i++)
		{
			if (s32ClientFd == g_s32QuickJpegFd[i])
			{
				g_s32QuickJpegFd[i] = 0;
				break;
			}
		}
    }
    pthread_mutex_unlock(&m_stHttpInfo.mutexLock);
    return NULL;
}



/******************************************************************************
 * 函数功能: 处理TF卡或者U盘导入的参数，分离线程
 * 输入参数: pvArg --- HTTP_COM_INFO_S
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
void * http_ImportConfigBody(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    char szPath[128] = {0};
    char szJsonBody[20*1024] = {0};
    char szJsonOut[512];
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "importConfigBody");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    if (COMMON_IsPathExist(HTTP_TMP_CONFIG_JSON_PATH))
    {
        print_level(SV_INFO, "found storage import config file, set config now!\n");
        s32Fd = open(HTTP_TMP_CONFIG_JSON_PATH, O_RDONLY);
        if (s32Fd <= 0)
        {
            print_level(SV_ERROR, "open %s failed.\n", HTTP_TMP_CONFIG_JSON_PATH);
            goto exit;
        }

        s32Ret = read(s32Fd, szJsonBody, 20*1024);
        if (s32Ret <= 0)
        {
            print_level(SV_ERROR, "read %s failed.\n", HTTP_TMP_CONFIG_JSON_PATH);
            goto exit;
        }
        print_level(SV_INFO, "get import config:\n%s\n", szJsonBody);

        s32Ret = JSON_HDL_SetConfig(szJsonBody, szJsonOut);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "JSON_HDL_SetConfig failed.\n");
            goto exit;
        }
        else
        {
            print_level(SV_INFO, "import config success.\n");
        }
        close(s32Fd);
        s32Fd = -1;
        remove(HTTP_TMP_CONFIG_JSON_PATH);
    }
    else
    {
        print_level(SV_INFO, "not found storage import config file!\n");
    }

exit:
    if (s32Fd > 0)
    {
        close(s32Fd);
    }
    return NULL;
}

/******************************************************************************
 * 函数功能: 处理Flv请求线程体
 * 输入参数: pvArg --- client fd
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
void * http_HandleFlv(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32Size = 0;
    uint32 u32Cnt = 0;
    uint32 u32RecvSize = 0;
    //uint32 u32ResSize = 0;
    sint32 s32ClientFd = (sint32)pvArg;
    fd_set read_fds;
    struct timeval stTimeval = {0};
    uint8 au8Buf[16*1024];
    HTTP_HEADER_S stHttpHeader = {0};
    char *pstTmp = NULL;
    char szFilePath[128];

    s32Ret = prctl(PR_SET_NAME, "flv");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    sleep_ms(1);
    while (m_stHttpInfo.bRunning)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            if (0 == s32Ret)
                print_level(SV_WARN, "socket wait for data timeout.\n");
            break;
        }

        memset(au8Buf, 0x0, 2048);
        u32RecvSize = 0;
        while (u32RecvSize < 2048)
        {
            s32Size = recv(s32ClientFd, au8Buf + u32RecvSize, 16*1024-u32RecvSize, MSG_DONTWAIT);
            if (s32Size <= 0)
            {
            	if(errno == EAGAIN)
            	{
            		if(u32Cnt > 500)
            		{
            			print_level(SV_ERROR, "recv timeout\n");
        				goto quick_exit;
            		}
                    usleep(1000);
                    u32Cnt++;
                    continue;
            	}
            	else
            	{
    				//print_level(SV_WARN, "recv error: %s\n", strerror(errno));
    				goto quick_exit;
            	}
            }
            if(strstr((char*)au8Buf,"\r\n\r\n")!=NULL)
            {
            	u32RecvSize = u32RecvSize+s32Size;
            	break;
            }
            u32RecvSize += s32Size;
        }

        s32Ret = HTTP_HDL_ParseHeader((uint8*)au8Buf, u32RecvSize, &stHttpHeader);
        if (SV_SUCCESS != s32Ret)
        {
        	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
        	goto quick_exit;
        }

        if (NULL == strcasestr(stHttpHeader.pszUrl, "flv") ||
            0 != strcmp(stHttpHeader.pszMethod, "GET"))
        {
            print_level(SV_ERROR, "invalid request: %s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);
        	goto quick_exit;
        }

        HTTP_HDL_FLV(s32ClientFd);
    }

quick_exit:
    close(s32ClientFd);
    return NULL;
}


/******************************************************************************
 * 函数功能: 处理MJpeg请求线程体
 * 输入参数: pvArg --- client fd
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意      : 无
 *****************************************************************************/
void * http_HandleMJpeg(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32Size = 0;
    uint32 u32Cnt = 0;
    uint32 u32RecvSize = 0;
    //uint32 u32ResSize = 0;
    sint32 s32ClientFd = (sint32)pvArg;
    fd_set read_fds;
    struct timeval stTimeval = {0};
    uint8 au8Buf[16*1024];
    HTTP_HEADER_S stHttpHeader = {0};
    char *pstTmp = NULL;
    char szFilePath[128];
    static uint32 u32Clients = 0;

    s32Ret = prctl(PR_SET_NAME, "mjpeg");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    sleep_ms(1);
    while (m_stHttpInfo.bRunning)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            if (0 == s32Ret)
                print_level(SV_WARN, "socket wait for data timeout.\n");
            break;
        }

        memset(au8Buf, 0x0, 2048);
        u32RecvSize = 0;
        while (u32RecvSize < 2048)
        {
            s32Size = recv(s32ClientFd, au8Buf + u32RecvSize, 16*1024-u32RecvSize, MSG_DONTWAIT);
            if (s32Size <= 0)
            {
            	if(errno == EAGAIN)
            	{
            		if(u32Cnt > 500)
            		{
            			print_level(SV_ERROR, "recv timeout\n");
        				goto quick_exit;
            		}
                    usleep(1000);
                    u32Cnt++;
                    continue;
            	}
            	else
            	{
    				print_level(SV_ERROR, "recv error: %s\n", strerror(errno));
    				goto quick_exit;
            	}
            }
            if(strstr((char*)au8Buf,"\r\n\r\n")!=NULL)
            {
            	u32RecvSize = u32RecvSize+s32Size;
            	break;
            }
            u32RecvSize += s32Size;
        }

        s32Ret = HTTP_HDL_ParseHeader((uint8*)au8Buf, u32RecvSize, &stHttpHeader);
        if (SV_SUCCESS != s32Ret)
        {
        	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
        	goto quick_exit;
        }

        if (NULL == strcasestr(stHttpHeader.pszUrl, "mjpeg") ||
            0 != strcmp(stHttpHeader.pszMethod, "GET"))
        {
            print_level(SV_ERROR, "invalid request: %s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);
        	goto quick_exit;
        }
        else
        {
            m_stHttpInfo.u32MJpegClients ++;
            //u32Clients = m_stHttpInfo.u32MJpegClients;
        }

        HTTP_HDL_MJPEG(s32ClientFd, &m_stHttpInfo.u32MJpegClients);
        print_level(SV_DEBUG, "m_stHttpInfo.u32MJpegClients = %d\n", m_stHttpInfo.u32MJpegClients);
    }

quick_exit:
    close(s32ClientFd);
    return NULL;
}

void * http_Port80_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32Size = 0;
    uint32 u32RecvSize = 0;
    sint32 s32ClientFd = 0;
    sint32 s32NameLen = 0;
    pthread_t thread;
    pthread_attr_t 	attr;
    struct sockaddr_in stClientName = {0};
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;
    fd_set read_fds;
    struct timeval stTimeval = {0};
    char szHeader[2048];
    char *pcTmp = NULL;
    HTTP_HEADER_S stHttpHeader = {0};

    s32Ret = prctl(PR_SET_NAME, "http_Port80_Body");
    while (pstHttpInfo->bRunning)
    {
        //print_level(SV_DEBUG, "http_Port80_Body running...\n");
        sleep_ms(100);
        s32NameLen = sizeof(stClientName);
        s32ClientFd = accept(pstHttpInfo->s32Port80Socket, (struct sockaddr *)&stClientName, (socklen_t*)&s32NameLen);
        if (s32ClientFd < 0)
        {
            print_level(SV_ERROR, "accept client failed. [err=%#x]\n", errno);
            continue;
        }

        stTimeval.tv_sec = 1;
        stTimeval.tv_usec = 0;
        u32RecvSize = 0;
        memset(szHeader, 0x0, 2048);
        while (u32RecvSize < 2048)
        {
            FD_ZERO(&read_fds);
            FD_SET(s32ClientFd, &read_fds);
            s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
            if (s32Ret <= 0)
            {
                break;
            }

            stTimeval.tv_sec = 0;
            stTimeval.tv_usec = 20000;
            s32Size = recv(s32ClientFd, szHeader + u32RecvSize, 2048 - u32RecvSize, 0);
            if (s32Size <=0)
            {
                break;
            }

            u32RecvSize += s32Size;
        }

        szHeader[2047] = '\0';
        s32Ret = HTTP_HDL_ParseHeader((uint8*)szHeader, 2048, &stHttpHeader);
	    if (SV_SUCCESS != s32Ret)
	    {
	    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
	    	close(s32ClientFd);
            continue;
	    }

	    pcTmp = strstr(stHttpHeader.pszUrl, "?");
        if (NULL != pcTmp)
        {
            *pcTmp = '\0';
        }

        print_level(SV_DEBUG, "%s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);
        if (0 == strcmp(stHttpHeader.pszMethod, "GET"))
        {
            char szRedirect[128];

			if (NULL != stHttpHeader.pszHost)
			{
				sint32 a,b,c,d;
				s32Ret = sscanf(stHttpHeader.pszHost, "%d.%d.%d.%d", &a, &b, &c, &d);
				if (s32Ret == 4 && a >=0 && a <=255 && b >=0 && b <=255 && c >=0 && c <=255 && d >=0 && d <=255)
				{
					sprintf(szRedirect, "http://%s:8080/login.html", stHttpHeader.pszHost);
				}
				else
				{
					strcpy(szRedirect, "http://************:8080/login.html");
				}
			}
            else
            {
                strcpy(szRedirect, "http://************:8080/login.html");
            }

            sprintf((char *)szHeader, "HTTP/1.1 302 Move temporarily\r\n"
                                    "Content-Type: text/html\r\n"
                                    "Connection: close\r\n"
                                    "Pramga: no-cache\r\n"
                                    "Cache-Control: no-cache, no-store\r\n"
        						    "Location: %s \r\n\r\n", szRedirect);
            print_level(SV_DEBUG, "> %s\n", (char *)szHeader);
            s32Ret = send(s32ClientFd, szHeader, strlen((char *)szHeader), 0);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "send to client failed. [err=%#x]\n", errno);
            }
        }
        else
        {
		    sprintf((char *)szHeader, "HTTP/1.1 403 Forbidden\r\n"
								    "Content-Type: text/plain \r\n"
								    "Content-Length: 0\r\n\r\n");
		    s32Ret = send(s32ClientFd, szHeader, strlen((char *)szHeader), 0);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "send to client failed. [err=%#x]\n", errno);
            }
        }
        close(s32ClientFd);
    }
}


void * http_SVR_Body(void *pvArg)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32ClientFd = 0;
    sint32 s32NameLen = 0;
    pthread_t thread;
    pthread_attr_t 	attr;
    struct sockaddr_in stClientName = {0};
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "http_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstHttpInfo->bRunning)
    {
        //print_level(SV_DEBUG, "http_SVR_Body running...\n");

        s32NameLen = sizeof(stClientName);
        s32ClientFd = accept(pstHttpInfo->s32SvrSocket, (struct sockaddr *)&stClientName, (socklen_t*)&s32NameLen);
        if (s32ClientFd < 0)
        {
            print_level(SV_ERROR, "accept client failed. [err=%#x]\n", errno);
            continue;
        }

        for (i = 0; i < HTTP_MAX_HTTP_THR; i++)
		{
			if (m_stHttpInfo.s32HttpReqFd[i] == 0)
			{
				m_stHttpInfo.s32HttpReqFd[i] = s32ClientFd;
				break;
			}
		}

		pthread_attr_init(&attr);
		pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
        s32Ret = pthread_create(&thread , &attr, http_HandleRequest, (void *)s32ClientFd);
        pthread_attr_destroy(&attr);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "pthread_create for client failed. [err=%#x]\n", errno);
            close(s32ClientFd);
            continue;
        }

        usleep(5000);
    }

    return NULL;
}


void * http_IpSearch_Body(void *pvArg)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32SearchFd = 0;
    fd_set read_fds;
    struct timeval timeout;
    struct timeval stTimeval = {1, 0};
    pthread_t thread;
    pthread_attr_t 	attr;
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;
    sint32 s32Size = 0;
    uint32 u32Cnt = 0;
    uint32 u32RecvSize = 0;
    uint8 au8RecvBuf[1024];
    uint8 au8SendBuf[1024];
    sint32 s32Loop = 1;
    struct sockaddr_in stLocalAddr = {0};
    struct sockaddr_in stgroupAddr = {0};
    struct sockaddr_in stCntAddr = {0};
    struct ip_mreq stMreq;
    socklen_t grpaddr_len = sizeof(stgroupAddr);
    CFG_NETWORK_PARAM stNetworkParam = {0};
    sint32 s32Port = 2887;
    char szRealMacAddr[32];
    char szRealIpAddr[32];
    char szRealGateway[32] = {0};
    char szRealSubmask[32];
    //char *pszInterface = "eth0";

    s32Ret = prctl(PR_SET_NAME, "ipsearch_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    s32Ret = http_CreateUdpMulticastd(2887, &s32SearchFd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stHttpInfo.s32IPSearchSocket = s32SearchFd;

    //s32SearchFd = pstHttpInfo->s32IPSearchSocket;
    //setsockopt(s32SearchFd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));      // 发送时限
    //setsockopt(s32SearchFd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));      // 接收时限

    // 多播ip地址初始化
    stgroupAddr.sin_family = AF_INET;
    stgroupAddr.sin_port = htons(s32Port);				                          // 端口号转换为网络字节序
    stgroupAddr.sin_addr.s_addr = inet_addr("***************");		              // 这里不能用htonl，否则sendto参数无效

    while (pstHttpInfo->bRunning)
    {
        FD_ZERO(&read_fds);
        FD_SET(pstHttpInfo->s32IPSearchSocket, &read_fds);
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;
        s32Ret = select(pstHttpInfo->s32IPSearchSocket + 1, &read_fds, NULL, NULL, &timeout);
        if(s32Ret <= 0)
        {
        	sleep_ms(500);
        	continue;
        }
#if 1
        memset(au8RecvBuf, 0x0, 1024);

        s32Size = recvfrom(s32SearchFd, au8RecvBuf, sizeof(au8RecvBuf), 0, NULL, NULL);
        if (s32Size <= 0)
        {
			print_level(SV_ERROR, "recvfrom error: %s\n", strerror(errno));
			break;
        }
        //print_level(SV_DEBUG, "sendaddr:%s, prot:%d\n", inet_ntop(AF_INET, &stCntAddr.sin_addr.s_addr,  au8RecvBuf, sizeof(au8RecvBuf)),  stCntAddr.sin_port);
        print_level(SV_DEBUG, "message: %s\n", au8RecvBuf);

        if(strstr((char *)au8RecvBuf, "stonkam ipsearch") != NULL)
        {
            print_level(SV_INFO, "receive ipsearch request.\n");
            /*
            s32Ret = CONFIG_GetNetworkParam(&stNetworkParam);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "CONFIG_GetNetworkParam failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
            sprintf((char *)au8SendBuf, "<?xml version=\"1.0\" encoding=\"utf-8\"?>" \
                                "<IPAddress>%s</IPAddress>"
                                "<Gateway>%s</Gateway>" \
                                "<Submask>%s</Submask>", \
                    stNetworkParam.pszEthIpAddr, stNetworkParam.pszEthGateway, stNetworkParam.pszEthSubmask);
            */
            NETWORK_GetEthAddr(szRealMacAddr, szRealIpAddr, szRealSubmask, szRealGateway);

            sprintf((char *)au8SendBuf,
                    "<?xml version=\"1.0\" encoding=\"utf-8\"?>" \
                    "<IPAddress>%s</IPAddress>"
                    "<Gateway>%s</Gateway>" \
                    "<Submask>%s</Submask>", \
                    szRealIpAddr, szRealGateway, szRealSubmask);

            s32Size = sendto(s32SearchFd, au8SendBuf, strlen(au8SendBuf) + 1, 0, (struct sockaddr *)&stgroupAddr, sizeof(stgroupAddr));
            if (s32Size <= 0)
            {
				print_level(SV_ERROR, "sendto error: %s\n", strerror(errno));
				break;
            }
        }
#else
        s32SearchFd = pstHttpInfo->s32IPSearchSocket;
        if (s32SearchFd < 0)
        {
            print_level(SV_ERROR, "accept client failed. [err: %s]\n", strerror(errno));
            continue;
        }

        print_level(SV_INFO, "s32SearchFd = %d\n", s32SearchFd);
        print_level(SV_INFO, "accept new ipsearch request\n");

        pthread_attr_init(&attr);
		pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
        s32Ret = pthread_create(&thread, &attr, http_HandleIPSearch, (void *)s32SearchFd);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
            close(s32SearchFd);
            continue;
        }

        //usleep(100000);
#endif
    }

    close(s32SearchFd);
    return NULL;
}

void * udp_Mjpeg_Body(void *pvArg)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32UdpFd = 0;
    struct timeval timeout;
    struct timeval stTimeval = {1, 0};
    int optval = 1;
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;
    sint32 s32Size = 0;
    uint32 u32Cnt = 0;
    struct sockaddr_in stLocalAddr = {0};
    struct sockaddr_in stDstAddr = {0};
    sint32 s32Port = 50001;
    MSG_PACKET_S stMsgPkt = {0};
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    SFIFO_MSHEAD *pstPacket = NULL;
    uint32 u32VideoBufSize = 501*1024;
    uint8 *pu8VideoBuf = NULL;
    uint32 u32PacketSize = 0;
    uint32 u32Serial = 0;
    uint32 u32PacketNum = 0;
    uint32 u32LastPacketSize = 0;
    //uint32 u32PacketIndex = 0;
    int iPos = 0;
    sint32 s32ImgSize = 0;

    s32Ret = prctl(PR_SET_NAME, "udp_mjpeg_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    pu8VideoBuf = (uint8 *)malloc(u32VideoBufSize);
	if (NULL == pu8VideoBuf)
    {
        print_level(SV_ERROR, "malloc: pu8Buf failed.\n");
        return;
    }

    s32UdpFd = pstHttpInfo->s32UdpMjpegSocket;
    setsockopt(s32UdpFd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));      // 发送时限
    setsockopt(s32UdpFd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));      // 接收时限
    //setsockopt(s32UdpFd, SOL_SOCKET, SO_BROADCAST, &optval, sizeof(int));

    // 目的ip地址初始化
    stDstAddr.sin_family = AF_INET;
    stDstAddr.sin_port = htons(s32Port);				                          // 端口号转换为网络字节序
    stDstAddr.sin_addr.s_addr = inet_addr("*************");		              // 这里不能用htonl，否则sendto参数无效

    while (pstHttpInfo->bRunning)
    {
        sleep(3);
        stMsgPkt.stMsg.s32Param = 3;
        s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        s32Ret = SFIFO_ForReadOpen(SFIFO_PIC_STREAM, &s32MainQueId, &s32MainConsumerId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream: %s failed.\n", SFIFO_PIC_STREAM);
            return;
        }

        while(1)
        {
        #if 1
            s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
                //print_level(SV_ERROR, "SFIFO_GetPacket failed. [err=%#x]\n", s32Ret);
                //print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                sleep_ms(10);
                continue;
            }

            if (pstPacket->type == 1)
            {
                //print_level(SV_DEBUG, "pts: %lld\n", pstPacket->pts);
                //print_level(SV_DEBUG, "size: %d\n", pstPacket->msdsize);
                //print_level(SV_DEBUG, "type = %d, serial = %d\n", pstPacket->type, pstPacket->serial);
                memset(pu8VideoBuf, 0, u32VideoBufSize);
                if (pstPacket->msdsize > u32VideoBufSize)
                {
                    print_level(SV_ERROR, "packet size %d lager than buf size!\n", pstPacket->msdsize);
                    continue;
                }

                //iPos = http_findMjpgData((unsigned char *)pstPacket->data, pstPacket->msdsize);

                u32PacketSize = pstPacket->msdsize - iPos;
                memcpy(pu8VideoBuf, pstPacket->data + iPos, u32PacketSize);

                s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
                }

                if (pstPacket->serial != u32Serial + 1) // 监测帧顺序是否正常
                {
                    print_level(SV_WARN, "Abnormal Frame Serial Number. Present Frame: %d, Last Frame: %d.\n", pstPacket->serial, u32Serial);
                    //print_level(SV_DEBUG, "u32PacketSize = %d\n", u32PacketSize);
                }
                u32Serial = pstPacket->serial;

                //print_level(SV_DEBUG, "FrameType: %d(%dx%d) pts: %lld, serial: %d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->serial);

                //memcpy(pu8Buf, pstPacket->data, pstPacket->msdsize);
                //s32Size = send(socketFd, pu8Buf, pstPacket->msdsize, 0);

                u32PacketNum = u32PacketSize / UDP_PDU_SIZE;     // 分包数
                u32LastPacketSize = u32PacketSize % UDP_PDU_SIZE;// 最后一个包大小

                if (u32LastPacketSize != 0)
                {
                    u32PacketNum +=1;
                }

                while (i < u32PacketNum - 1)
                {
                    s32Size = sendto(s32UdpFd, pu8VideoBuf + i*UDP_PDU_SIZE, UDP_PDU_SIZE, 0, (struct sockaddr *)&stDstAddr, sizeof(stDstAddr));
                    i++;
                }

                s32Size = sendto(s32UdpFd, pu8VideoBuf + i*UDP_PDU_SIZE, u32LastPacketSize, 0, (struct sockaddr *)&stDstAddr, sizeof(stDstAddr));
                i = 0;

            }
            else
            {
                s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
                }
            }
        #else
            memset(pu8VideoBuf, 0, u32VideoBufSize);
            pthread_mutex_lock(&m_stSnapInfo.mutexSnap);
            s32ImgSize = m_stSnapInfo.s32ImgSize;
            memcpy(pu8VideoBuf, m_stSnapInfo.apvImgBuf, s32ImgSize);
            pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
/*
            sprintf((char *)pu8VideoBuf, "Do not go gentle into the good night.");
            s32Size = sendto(s32UdpFd, pu8VideoBuf, strlen(pu8VideoBuf) + 1, 0, (struct sockaddr *)&stDstAddr, sizeof(stDstAddr));
            if (s32Size <= 0)
            {
                print_level(SV_WARN, "sendto failed. [err=%s]\n", strerror(errno));
            }*/

            u32PacketNum = s32ImgSize / 1200;     // 分包数
            u32LastPacketSize = s32ImgSize % 1200;// 最后一个包大小

            if (u32LastPacketSize != 0)
            {
                u32PacketNum += 1;
            }

            print_level(SV_DEBUG, "i=%d, pkn=%d, lpks=%d.\n", i, u32PacketNum, u32LastPacketSize);

            while (i < (u32PacketNum - 1))
            {
                s32Size = sendto(s32UdpFd, pu8VideoBuf + i*1200, 1200, 0, (struct sockaddr *)&stDstAddr, sizeof(stDstAddr));
                if (s32Size <= 0)
                {
                    print_level(SV_WARN, "sendto failed. [err=%s]\n", strerror(errno));
                }

                i += 1;
            }

            s32Size = sendto(s32UdpFd, pu8VideoBuf + i*1200, u32LastPacketSize, 0, (struct sockaddr *)&stDstAddr, sizeof(stDstAddr));
            if (s32Size <= 0)
            {
                print_level(SV_WARN, "sendto failed. [err=%s]\n", strerror(errno));
            }

            i = 0;

            //sleep_ms(1000);
        #endif
        }

        s32Ret = SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SFIFO_ForReadClose stream: %s failed.\n", SFIFO_MAIN_STREAM);
            return;
        }

        stMsgPkt.stMsg.s32Param = -1;
        s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }


        free(pu8VideoBuf);
        pu8VideoBuf = NULL;
    }

quick_exit:
    close(s32UdpFd);
    return NULL;
}


void * http_QuickJpeg_Body(void *pvArg)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32ClientFd = 0;
    sint32 s32NameLen = 0;
    fd_set read_fds;
    struct timeval timeout;
    pthread_t thread;
    pthread_attr_t 	attr;
    struct sockaddr_in stClientName = {0};
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;
    struct timeval stTimeval = {1, 0};

    s32Ret = prctl(PR_SET_NAME, "http_quick_jpeg_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstHttpInfo->bRunning)
    {
        FD_ZERO(&read_fds);
        FD_SET(pstHttpInfo->s32QuickSvrSocket, &read_fds);
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;
        s32Ret = select(pstHttpInfo->s32QuickSvrSocket + 1, &read_fds, NULL, NULL, &timeout);
        if(s32Ret <= 0)
        {
        	if (0 == pstHttpInfo->u32QuickHandleNum)
        	{
        		HTTP_HDL_QuickJpeg(-1);
        	}
        	sleep_ms(10);
        	continue;
        }

        s32NameLen = sizeof(stClientName);
        s32ClientFd = accept(pstHttpInfo->s32QuickSvrSocket, (struct sockaddr *)&stClientName, (socklen_t*)&s32NameLen);
        if (s32ClientFd < 0)
        {
            print_level(SV_ERROR, "accept client failed. [err: %s]\n", strerror(errno));
            continue;
        }
        print_level(SV_INFO, "accept new connection\n");

        for (i = 0; i < HTTP_MAX_QUICK_THR; i++)
		{
			if (g_s32QuickJpegFd[i] == 0)
			{
				g_s32QuickJpegFd[i] = s32ClientFd;
				break;
			}
		}

        pthread_mutex_lock(&pstHttpInfo->mutexLock);
        if (pstHttpInfo->u32QuickHandleNum > HTTP_MAX_QUICK_THR)
        {
            print_level(SV_WARN, "current handle quick thread is full num:%d\n", pstHttpInfo->u32QuickHandleNum);
            pthread_mutex_unlock(&pstHttpInfo->mutexLock);
            close(s32ClientFd);
            continue;
        }

        pstHttpInfo->u32QuickHandleNum++;
        pthread_mutex_unlock(&pstHttpInfo->mutexLock);
        pthread_attr_init(&attr);
		pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
        s32Ret = pthread_create(&thread, &attr,http_HandleQuickJpeg, (void *)s32ClientFd);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
            close(s32ClientFd);
            continue;
        }
        pthread_attr_destroy(&attr);
    }
    return NULL;
}


void * http_Flv_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32ClientFd = 0;
    sint32 s32NameLen = 0;
    fd_set read_fds;
    struct timeval timeout;
    pthread_t thread;
    pthread_attr_t 	attr;
    struct sockaddr_in stClientName = {0};
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;
    struct timeval stTimeval = {1, 0};


    s32Ret = prctl(PR_SET_NAME, "flv_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    setsockopt(s32ClientFd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
    setsockopt(s32ClientFd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));

    while (pstHttpInfo->bRunning)
    {

        FD_ZERO(&read_fds);
        FD_SET(pstHttpInfo->s32FlvSocket, &read_fds);
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;
        s32Ret = select(pstHttpInfo->s32FlvSocket + 1, &read_fds, NULL, NULL, &timeout);
        if(s32Ret <= 0)
        {
        	sleep_ms(10);
        	continue;
        }

        s32NameLen = sizeof(stClientName);
        s32ClientFd = accept(pstHttpInfo->s32FlvSocket, (struct sockaddr *)&stClientName, (socklen_t*)&s32NameLen);
        if (s32ClientFd < 0)
        {
            print_level(SV_ERROR, "accept client failed. [err: %s]\n", strerror(errno));
            continue;
        }

        print_level(SV_INFO, "accept new connection\n");

        pthread_attr_init(&attr);
		pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
        s32Ret = pthread_create(&thread, &attr, http_HandleFlv, (void *)s32ClientFd);
        pthread_attr_destroy(&attr);

        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
            close(s32ClientFd);
            continue;
        }

        //usleep(100000);
    }
    return NULL;
}

void * websocket_QuickJpeg_Body(void *pvArg){
	sint32 s32Ret = 0, i = 0;
	sint32 s32ClientFd = 0;
	sint32 s32NameLen = 0;
	fd_set read_fds;
	struct timeval timeout;
	pthread_t thread;
	pthread_attr_t	attr;
	struct sockaddr_in stClientName = {0};
	HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;
	struct timeval stTimeval = {1, 0};

	s32Ret = prctl(PR_SET_NAME, "websocket_quick_jpeg_body");
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
	}


	while (pstHttpInfo->bRunning)
	{
		FD_ZERO(&read_fds);
		FD_SET(pstHttpInfo->s32WsJpegSocket, &read_fds);
		timeout.tv_sec = 5;
		timeout.tv_usec = 0;
		s32Ret = select(pstHttpInfo->s32WsJpegSocket + 1, &read_fds, NULL, NULL, &timeout);
		if(s32Ret <= 0)
		{
			if (0 == pstHttpInfo->u32QuickHandleNum)
			{
				HTTP_HDL_QuickJpeg(-1);
			}
			sleep_ms(10);
			continue;
		}

		s32NameLen = sizeof(stClientName);
		s32ClientFd = accept(pstHttpInfo->s32WsJpegSocket, (struct sockaddr *)&stClientName, (socklen_t*)&s32NameLen);
		if (s32ClientFd < 0)
		{
			print_level(SV_ERROR, "accept client failed. [err: %s]\n", strerror(errno));
			continue;
		}

		print_level(SV_INFO, "websocket new connection\n");
		pthread_mutex_lock(&pstHttpInfo->mutexLock);
		if (pstHttpInfo->u32QuickHandleNum > HTTP_MAX_QUICK_THR)
		{
			print_level(SV_WARN, "current handle quick thread is full num:%d\n", pstHttpInfo->u32QuickHandleNum);
			pthread_mutex_unlock(&pstHttpInfo->mutexLock);
			close(s32ClientFd);
			continue;
		}

		for (i = 0; i < HTTP_MAX_QUICK_THR; i++)
		{
			if (g_s32QuickJpegFd[i] == 0)
			{
				g_s32QuickJpegFd[i] = s32ClientFd;
				break;
			}
		}
		pstHttpInfo->u32QuickHandleNum++;
		pthread_mutex_unlock(&pstHttpInfo->mutexLock);
		pthread_attr_init(&attr);
		pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
		s32Ret = pthread_create(&thread, &attr,ws_HandleQuickJpeg, (void *)s32ClientFd);
		if (s32Ret < 0)
		{
			print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
			close(s32ClientFd);
			continue;
		}
        pthread_attr_destroy(&attr);

	}
	return NULL;

}

void * http_MJpeg_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32ClientFd = 0;
    sint32 s32NameLen = 0;
    fd_set read_fds;
    struct timeval timeout;
    pthread_t thread;
    pthread_attr_t 	attr;
    struct sockaddr_in stClientName = {0};
    HTTP_COM_INFO_S *pstHttpInfo = (HTTP_COM_INFO_S *)pvArg;
    struct timeval stTimeval = {1, 0};
    int iSendBufSize = 800*1024;

    s32Ret = prctl(PR_SET_NAME, "mjpeg_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    setsockopt(s32ClientFd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
    setsockopt(s32ClientFd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
	setsockopt(s32ClientFd, SOL_SOCKET, SO_SNDBUF, &iSendBufSize, sizeof(iSendBufSize));

    while (pstHttpInfo->bRunning)
    {

        FD_ZERO(&read_fds);
        FD_SET(pstHttpInfo->s32MJpegSocket, &read_fds);
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;
        s32Ret = select(pstHttpInfo->s32MJpegSocket + 1, &read_fds, NULL, NULL, &timeout);
        if(s32Ret <= 0)
        {
        	sleep_ms(10);
        	continue;
        }

        s32NameLen = sizeof(stClientName);
        s32ClientFd = accept(pstHttpInfo->s32MJpegSocket, (struct sockaddr *)&stClientName, (socklen_t*)&s32NameLen);
        if (s32ClientFd < 0)
        {
            print_level(SV_ERROR, "accept client failed. [err: %s]\n", strerror(errno));
            continue;
        }

        print_level(SV_INFO, "accept new connection\n");

        pthread_attr_init(&attr);
		pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
        /*
        // 设置线程优先级（较高的值表示较高的优先级）
        pthread_attr_setschedpolicy(&attr, SCHED_FIFO);  // 使用FIFO调度策略
        uint32 u32Level = sched_get_priority_max(SCHED_FIFO);
        print_level(SV_INFO, "u32Level = %d.\n", u32Level);
        struct sched_param param;
        param.sched_priority = 99;  // 设置优先级
        pthread_attr_setschedparam(&attr, &param);*/
        s32Ret = pthread_create(&thread, &attr, http_HandleMJpeg, (void *)s32ClientFd);
        pthread_attr_destroy(&attr);


        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
            close(s32ClientFd);
            continue;
        }

        //usleep(100000);
    }
    return NULL;
}

static sint32 callbackUsrParamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    MSG_USR_CFG *pstUsrCfg = (MSG_USR_CFG *)pstMsgPkt->pu8Data;
	int i=0,j=0;

    print_level(SV_INFO, "recive: OP_EVENT_USR_CHANGE\n");
	for(i = 0;i<3;i++)
	{
		for(j = 0;j<3;j++)
		{
			strcpy(m_szUserPassword[i][j], pstUsrCfg->szUserPassword[i][j]);
		}
	}

    return SV_SUCCESS;
}

static sint32 callbackJpegSizeChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_INFO, "recive: OP_EVENT_JPEG_SIZE_CHANGE\n");
    HTTP_HDL_QuickJpeg(-1);

    return SV_SUCCESS;
}

static sint32 callbackCloseHttp(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 i = 0;
    NETWORK_STAT_S *pstNetworkStat = (NETWORK_STAT_S *)pstMsgPkt->pu8Data;
    //print_level(SV_INFO, "recive: OP_EVENT_NETWORK_STAT, type: %d, exist: %d\n", pstNetworkStat->enNetworkType, pstNetworkStat->bExist);

    if (NETWORK_TYPE_LAN != pstNetworkStat->enNetworkType && SV_FALSE != pstNetworkStat->bExist)
    {
        return SV_SUCCESS;
    }
    else if (NETWORK_TYPE_LAN == pstNetworkStat->enNetworkType && SV_FALSE != pstNetworkStat->bExist)
    {
        m_stHttpInfo.bNetworkInited = SV_TRUE;
    }

    for (i = 0; i < HTTP_MAX_HTTP_THR; i++)
	{
		if (0 != m_stHttpInfo.s32HttpReqFd[i])
		{
    		print_level(SV_WARN, "network is down, force to close http index: %d, fd: %d\n", i, m_stHttpInfo.s32HttpReqFd[i]);
			m_stHttpInfo.s32HttpReqFd[i] = 0;
		}
	}

    for (i = 0; i < HTTP_MAX_QUICK_THR; i++)
    {
    	if (0 != g_s32QuickJpegFd[i])
    	{
    		print_level(SV_WARN, "network is down, force to close quick jpeg index: %d, fd: %d\n", i, g_s32QuickJpegFd[i]);
    		close(g_s32QuickJpegFd[i]);
    		g_s32QuickJpegFd[i] = 0;
    	}
    }

    return SV_SUCCESS;
}

static sint32 callbackNetworkParamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_INFO, "recive: OP_EVENT_NETWORK_CHANGE\n");
    if (m_stHttpInfo.u32MJpegClients != 0)    // 若修改ip时未停止拉流则重置（关闭先前打开的队列）
    {
        bResetMJpeg = SV_TRUE;
    }
    //m_stHttpInfo.u32MJpegClients = 0;

    return SV_SUCCESS;
}

#if 1 //mongoose实现
static void signal_handler(int sig_num)
{
    print_level(SV_WARN, "Caught signal: %d\n", sig_num);
    signal(sig_num, signal_handler);  // Reinstantiate signal handler
    s_signal_received = sig_num;
}

static int is_websocket(const struct mg_connection *nc)
{
	if (nc!=NULL)
	{
		return nc->is_websocket;
	}
	else
	{
		return 0;
	}
}

static void http_mg_reply(struct mg_connection *nc, sint32 s32MsgRet, char *pszInJson)
{
    sint32 s32Code = 200;
    time_t now = time(NULL);
    char szGMTTime[64];
    char szHeader[1024] = {0};

    if (NULL == nc && NULL == pszInJson)
        return;

    if (MSG_SUCCESS_RES == s32MsgRet)
    {
        s32Code = 200;
    }
    else
    {
        s32Code = 500;
        HTTP_HDL_ParseError(s32MsgRet, pszInJson);
    }

    s32Code = (s32MsgRet == MSG_SUCCESS_RES) ? 200 : 500;
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&now));
    snprintf(szHeader, 1024,
                    "Date: %s\r\n"
                    "Access-Control-Allow-Origin:*\r\n"
                    "Content-Type: application/json; charset=UTF-8\r\n", szGMTTime);

    mg_http_reply(nc, s32Code, szHeader, pszInJson);
}

sint32 http_mg_GenerateKey()
{
    sint32 s32Ret;
    sint32 s32Fd = -1;
    char *pszTmp = NULL;
    char szCmd[128] = {0};
    char szBuf[128] = {0};
    char szPasswd[128] = {0};

    if (COMMON_IsPathExist(CONFIG_HTTPS_CRT) && COMMON_IsPathExist(CONFIG_HTTPS_KEY))
    {
        print_level(SV_INFO, "https crt and key is exist, skip to generate.\n");
        return SV_SUCCESS;
    }

    s32Fd = open(CONFIG_UUID, O_RDONLY);
    if (s32Fd <= 0)
    {
        print_level(SV_ERROR, "open %s failed.\n", CONFIG_UUID);
        return SV_FAILURE;
    }

    s32Ret = read(s32Fd, szBuf, 128);
    if(s32Ret <=  0)
    {
        print_level(SV_ERROR, "read %s failed [err: %s]\n", CONFIG_UUID, strerror(errno));
        close(s32Fd);
        return SV_FAILURE;
    }
    close(s32Fd);

    /* 加密密码使用uuid第一个横杠后的内容 */
    pszTmp = strstr(szBuf, "-");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "get uuid failed.\n");
        return SV_FAILURE;
    }
    strcpy(szPasswd, pszTmp+1);

    s32Ret = CONFIG_FlashProtection(SV_FALSE);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "disable flash protection failed.\n");
        return SV_FAILURE;
    }

    /* 生成服务器加密的RSA密钥 */
    sprintf(szCmd, "openssl genrsa -aes256 -passout pass:%s -out /var/server_rsa.pem 2048", szPasswd);
	s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
		goto exit;
    }

    /* 生成服务器证书请求文件 */
    sprintf(szCmd, "openssl req -new -key /var/server_rsa.pem -passin pass:%s -out /var/server.csr -subj \"/C=CN/ST=GD/L=GZ/O=GENERAL/OU=AI/CN=CAMERA\"", szPasswd);
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
		goto exit;
    }

    /* 自签名生成服务器证书 */
    sprintf(szCmd, "openssl req -x509 -days 3650 -passin pass:%s -key /var/server_rsa.pem -in /var/server.csr > %s", szPasswd, CONFIG_HTTPS_CRT);
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
		goto exit;
    }

    /* 将加密的RSA密钥转成未加密的RSA密钥，否则每次请求都需要输入密钥 */
	sprintf(szCmd, "openssl rsa -passin pass:%s -in /var/server_rsa.pem -out %s", szPasswd, CONFIG_HTTPS_KEY);
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
		goto exit;
    }

exit:
    s32Ret = CONFIG_FlashProtection(SV_TRUE);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "disable flash protection failed.\n");
        return SV_FAILURE;
    }

    return s32Ret;
}

void http_mg_Unlink(struct mg_connection *nc)
{
	if(nc==NULL) return;
    memset(nc->recv.buf, 0, nc->recv.len);
    nc->recv.len = 0;
    nc->fn_data = NULL;
}

sint32 http_Hdl_Factory(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
	sint32 s32Ret = 0;
    sint32 s32Size = 0;
    uint32 u32RecvSize = 0;
    uint32 u32SizeCnt = 0, i;
    sint32 s32ClientFd = -1, s32Fd = -1;
	sint32 s32HeadSize;
    sint32 s32MsgRet = MSG_SUCCESS_RES;

    char szHeader[2048];
    char szBuf[128*1024+64];
    char szJsonBody[10*1024] = {0};
    char szSplit[64];
    char szHeadSplit[64];
    char szTailSplit[64];
    char szFilePath[512];
    char *pszTmp = NULL;
    char *pszFileName = NULL;
    uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
    char *pszTmpFile = "/var/tmp-factory";
    char *pszPktPrefix = "factory";
    char szCmd[64];
    SV_BOOL bChunk = SV_TRUE;
    HTTP_HEADER_S stHttpHeader = {0};
    struct timeval stTimeval = {0};
    fd_set read_fds;

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
    if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing frimware now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto exit;
    }

    s32HeadSize = (hm->head.len < 2048) ? hm->head.len : 2048;
    memcpy(szHeader, hm->head.ptr, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }

    print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
    if (atoi(stHttpHeader.pszContentLenght) > 10*1024*1024)
    {
        print_level(SV_ERROR, "file size[%s] is too larged!\n", stHttpHeader.pszContentLenght);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }

    u32RecvSize = hm->chunk.len;
    bChunk = SV_TRUE;
    if (0 == u32RecvSize)
    {
        bChunk = SV_FALSE;
        u32RecvSize = hm->body.len;
        if (0 == u32RecvSize)
        {
            print_level(SV_INFO, "get chunk zero bytes, return.\n");
            return SV_SUCCESS;
        }
        print_level(SV_INFO, "hm->body.len=%d\n", hm->body.len);
    }

    sprintf(szHeadSplit, "--%s", stHttpHeader.pszBoundary);
    sprintf(szTailSplit, "\r\n--%s", stHttpHeader.pszBoundary);

    if (bChunk)
        pszTmp = strstr((char *)hm->chunk.ptr, szHeadSplit);
    else
        pszTmp = strstr((char *)hm->body.ptr, szHeadSplit);

    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found split: %s\n", szHeadSplit);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }

    if (bChunk)
    {
        pszTmp = strstr((char *)hm->chunk.ptr, "filename=\"");
    }
    else
    {
        pszTmp = strstr((char *)hm->body.ptr, "filename=\"");
    }


    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto exit;
    }

    pszFileName = pszTmp+strlen("filename=\"");
    pszTmp = strstr(pszFileName, "\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto exit;
    }

    *pszTmp = '\0';
    pszTmp++;
    if (NULL == strstr(pszFileName, pszPktPrefix))
    {
        print_level(SV_ERROR, "invalid packet file: %s\n", pszFileName);
        s32MsgRet = MSG_RANGE_OUT;
        goto exit;
    }

    pszTmp = strstr(pszTmp, "\r\n\r\n");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found data body\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto exit;
    }

    sprintf(szFilePath, "/var/%s", pszFileName);
    remove(pszTmpFile);
    s32Fd = open(pszTmpFile, O_CREAT|O_RDWR, 0755);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }

    sendHttpLog(s32ClientFd, "begin download factory");
    m_stHttpInfo.bWriting = SV_TRUE;

    pu8DataBegin = pszTmp + strlen("\r\n\r\n");

    if (bChunk)
    {
        pu8DataEnd = http_FindBoundary(pu8DataBegin, (uint8 *)hm->chunk.ptr+u32RecvSize-pu8DataBegin, szTailSplit);
        if (NULL == pu8DataEnd)
        {
            pu8DataEnd = hm->chunk.ptr+u32RecvSize;
        }
    }
    else
    {
        pu8DataEnd = http_FindBoundary(pu8DataBegin, (uint8 *)hm->body.ptr+u32RecvSize-pu8DataBegin, szTailSplit);
        if (NULL == pu8DataEnd)
        {
            pu8DataEnd = hm->body.ptr+u32RecvSize;
        }
    }

    write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
    while (1)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        memset(szBuf, 0, sizeof(szBuf));
        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szBuf, 128*1024);
        if (s32Size <= 0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }

        pu8DataEnd = http_FindBoundary(szBuf, s32Size, szTailSplit);
        if (NULL == pu8DataEnd)
        {
            write(s32Fd, szBuf, s32Size);
            sync();     // 没有每次sync的话，仅靠最后sync一次会导致venc喂狗超时，进程退出
        }
        else
        {
            pu8DataBegin = szBuf;
            write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
            sync();
            break;
        }

        u32SizeCnt += s32Size;
        printf("factory recv size: %d, total: %d\n", s32Size, u32SizeCnt);
    }
    close(s32Fd);

	print_level(SV_INFO, "old szFilePath: %s\n", szFilePath);
	strcpy(szFilePath, "/var/factory");
	print_level(SV_INFO, "new szFilePath: %s\n", szFilePath);
    s32Ret = rename(pszTmpFile, szFilePath);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "rename %s %s failed. error: %s\n", pszTmpFile, szFilePath, strerror(errno));
    }
    sync();
    m_stHttpInfo.bWriting = SV_FALSE;

    print_level(SV_INFO, "run factory...\n");
    sprintf(szCmd, "%s >> /dev/null &", szFilePath);
    if (0 != SAFE_System(szCmd, NORMAL_WAIT_TIME))
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }
    else
    {
        print_level(SV_INFO, "cmd: %s success.\n", szCmd);
    }

exit:
    http_mg_reply(nc, s32MsgRet, (s32MsgRet == MSG_SUCCESS_RES) ? "{}" : szJsonBody);
    return (s32MsgRet == MSG_SUCCESS_RES) ? SV_SUCCESS : SV_FAILURE;
}

sint32 http_Hdl_AddPlug(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret = 0;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    sint32 s32ClientFd = -1, s32Fd = -1;
    sint32 s32Size = 0;
    uint32 u32SizeCnt = 0, i;
    uint32 u32RecvSize = 0;
    char szHeader[2048];
    char szBuf[128*1024+64];
    char szJsonBody[10*1024] = {0};
    char szHeadSplit[64];
    char szTailSplit[64];
    char szFilePath[512];
    char szCmd[256];
    char *pszTmp = NULL;
    char *pszFileName = NULL;
	sint32 s32HeadSize;
    char *pszTmpFile = "/tmp/tmp-plug";
    uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
    char *pszPktPrefix = HTTP_PKT_PREFIX;
    uint32 u32MaxFileLen = 128*1024*1024;
    SV_BOOL bUnLockFlash = SV_FALSE;
    HTTP_HEADER_S stHttpHeader = {0};
    struct timeval stTimeval = {0};
    fd_set read_fds;
    MSG_PACKET_S stMsgPkt = {0};
    MSG_PACKET_S stMsgRet = {0};

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
    if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing file now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto error_exit;
    }

    //print_level(SV_INFO, "get header:\n%s\n", hm->head.ptr);
    s32HeadSize = (hm->head.len < 2048) ? hm->head.len : 2048;
    memcpy(szHeader, hm->head.ptr, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }

    print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
    if (atoi(stHttpHeader.pszContentLenght) > u32MaxFileLen)
    {
        print_level(SV_ERROR, "file size[%s] is too larged! [limit:%d]\n", stHttpHeader.pszContentLenght, u32MaxFileLen);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    u32RecvSize = hm->chunk.len;
    if (0 == u32RecvSize)
    {
        print_level(SV_INFO, "get chunk zero bytes, return.\n");    // 第一次接收到数据时有时候只是接收了头部信息，分片内容会在后面再接收
        return SV_SUCCESS;
    }

    sprintf(szHeadSplit, "--%s", stHttpHeader.pszBoundary);
    sprintf(szTailSplit, "\r\n--%s", stHttpHeader.pszBoundary);

    // 找头部分隔符
    pszTmp = strstr((char *)hm->chunk.ptr, szHeadSplit);
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found split: %s\n", szHeadSplit);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    // 找文件名
    pszTmp = strstr((char *)hm->chunk.ptr, "filename=\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    pszFileName = pszTmp+strlen("filename=\"");
    pszTmp = strstr(pszFileName, "\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    *pszTmp = '\0';
    pszTmp++;

    // 从头部分隔符之后开始找两个\r\n，这两个\r\n之后就是数据内容
    pszTmp = strstr(pszTmp, "\r\n\r\n");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found data body.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
    {
        print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
        goto error_exit;
    }
    bUnLockFlash = SV_TRUE;

    sprintf(szFilePath, "/tmp/%s", pszFileName);
    remove(pszTmpFile);
    s32Fd = open(pszTmpFile, O_CREAT|O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    sendHttpLog(s32ClientFd, "begin download plug file");
    m_stHttpInfo.bWriting = SV_TRUE;

    // 开始位置偏移到这两个\r\n之后，寻找是否包含尾部分隔符，没有尾部分隔符的话直接写入第一包数据
    pu8DataBegin = pszTmp + strlen("\r\n\r\n");
    pu8DataEnd = http_FindBoundary(pu8DataBegin, (uint8 *)hm->chunk.ptr+u32RecvSize-pu8DataBegin, szTailSplit);
    if (NULL == pu8DataEnd)
    {
        pu8DataEnd = hm->chunk.ptr+u32RecvSize;
    }
    write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);

    // 开始循环接收后续数据包，每一包数据都寻找是否包含尾部分隔符，包含的话即是最后一包数据，写入之后退出
    while (1)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        memset(szBuf, 0, sizeof(szBuf));
        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szBuf, 128*1024);
        if (s32Size <= 0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }

        pu8DataEnd = http_FindBoundary(szBuf, s32Size, szTailSplit);
        if (NULL == pu8DataEnd)
        {
            write(s32Fd, szBuf, s32Size);
            sync();     // 没有每次sync的话，仅靠最后sync一次会导致venc喂狗超时，进程退出
        }
        else
        {
            pu8DataBegin = szBuf;
            write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
            sync();
            break;
        }

        u32SizeCnt += s32Size;
        printf("plug file recv size: %d, total: %d\n", s32Size, u32SizeCnt);
    }
    close(s32Fd);

    print_level(SV_INFO, "rename %s -> %s\n", pszTmpFile, szFilePath);
    s32Ret = rename(pszTmpFile, szFilePath);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "rename %s %s failed. error: %s\n", pszTmpFile, szFilePath, strerror(errno));
    }
    sync();
    m_stHttpInfo.bWriting = SV_FALSE;

    sendHttpLog(s32ClientFd, "download plug file successful");

    CONFIG_FlashProtection(SV_TRUE);
    stMsgPkt.pu8Data = (uint8 *)szFilePath;
    stMsgPkt.u32Size = strlen(szFilePath)+1;
    s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_ALG, OP_REQ_ADD_PLUG, &stMsgPkt, &stMsgRet, 0);
    if (SV_SUCCESS != s32Ret || 0 != stMsgRet.stMsg.s32Param)
    {
        print_level(SV_ERROR, "OP_REQ_ADD_PLUG failed.\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        sprintf(szCmd, "rm -f %s", szFilePath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
        goto error_exit;
    }

    HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
    mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    sleep_ms(1000);
    http_mg_Unlink(nc);
    //print_level(SV_INFO, "reboot for update firmware...\n");

    return SV_SUCCESS;

error_exit:
    if (bUnLockFlash)
    {
        CONFIG_FlashProtection(SV_TRUE);
    }

    HTTP_HDL_GenerateErrorReply(s32MsgRet, szJsonBody);
    mg_http_send(SV_TRUE, nc, 0, szJsonBody, strlen(szJsonBody));
    return SV_FAILURE;
}



sint32 http_Hdl_UpdatePlug(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret = 0;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    sint32 s32ClientFd = -1, s32Fd = -1;
    sint32 s32Size = 0;
    uint32 u32SizeCnt = 0, i;
    uint32 u32RecvSize = 0;
    char szHeader[2048];
    char szBuf[128*1024+64];
    char szJsonBody[10*1024] = {0};
    char szHeadSplit[64];
    char szTailSplit[64];
    char szFilePath[512];
    char szCmd[256];
    char *pszTmp = NULL;
    char *pszFileName = NULL;
	char szUpdatePlugPath[128] = {0};
	sint32 s32HeadSize;
    char *pszTmpFile = "/tmp/tmp-plug";
    uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
    char *pszPktPrefix = HTTP_PKT_PREFIX;
    uint32 u32MaxFileLen = 128*1024*1024;
    SV_BOOL bUnLockFlash = SV_FALSE;
    HTTP_HEADER_S stHttpHeader = {0};
    struct timeval stTimeval = {0};
    fd_set read_fds;
    MSG_PACKET_S stMsgPkt = {0};
    MSG_PACKET_S stMsgRet = {0};
	MSG_PLUG_UPDATE stPlugUpdate = {0};

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
    if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing file now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto error_exit;
    }

	//找到要更新的插件的路径PlugPath
	pszTmp = strstr((char*)hm->uri.ptr,"path=");
	if(NULL == pszTmp)
	{
		print_level(SV_ERROR, "not found update Plug path\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;

	}
	strncpy(szUpdatePlugPath,pszTmp+strlen("path="),14);
	print_level(SV_DEBUG,"szupdate plugpath:%s \n",szUpdatePlugPath);

    //print_level(SV_INFO, "get header:\n%s\n", hm->head.ptr);
    s32HeadSize = (hm->head.len < 2048) ? hm->head.len : 2048;
    memcpy(szHeader, hm->head.ptr, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }

    print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
    if (atoi(stHttpHeader.pszContentLenght) > u32MaxFileLen)
    {
        print_level(SV_ERROR, "file size[%s] is too larged! [limit:%d]\n", stHttpHeader.pszContentLenght, u32MaxFileLen);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    u32RecvSize = hm->chunk.len;
    if (0 == u32RecvSize)
    {
        print_level(SV_INFO, "get chunk zero bytes, return.\n");    // 第一次接收到数据时有时候只是接收了头部信息，分片内容会在后面再接收
        return SV_SUCCESS;
    }

    sprintf(szHeadSplit, "--%s", stHttpHeader.pszBoundary);
    sprintf(szTailSplit, "\r\n--%s", stHttpHeader.pszBoundary);

    // 找头部分隔符
    pszTmp = strstr((char *)hm->chunk.ptr, szHeadSplit);
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found split: %s\n", szHeadSplit);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }


    // 找文件名
    pszTmp = strstr((char *)hm->chunk.ptr, "filename=\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    pszFileName = pszTmp+strlen("filename=\"");
    pszTmp = strstr(pszFileName, "\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    *pszTmp = '\0';
    pszTmp++;

    // 从头部分隔符之后开始找两个\r\n，这两个\r\n之后就是数据内容
    pszTmp = strstr(pszTmp, "\r\n\r\n");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found data body.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
    {
        print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
        goto error_exit;
    }
    bUnLockFlash = SV_TRUE;

    sprintf(szFilePath, "/tmp/%s", pszFileName);
    remove(pszTmpFile);
    s32Fd = open(pszTmpFile, O_CREAT|O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    sendHttpLog(s32ClientFd, "begin download plug file");
    m_stHttpInfo.bWriting = SV_TRUE;

    // 开始位置偏移到这两个\r\n之后，寻找是否包含尾部分隔符，没有尾部分隔符的话直接写入第一包数据
    pu8DataBegin = pszTmp + strlen("\r\n\r\n");
    pu8DataEnd = http_FindBoundary(pu8DataBegin, (uint8 *)hm->chunk.ptr+u32RecvSize-pu8DataBegin, szTailSplit);
    if (NULL == pu8DataEnd)
    {
        pu8DataEnd = hm->chunk.ptr+u32RecvSize;
    }
    write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);

    // 开始循环接收后续数据包，每一包数据都寻找是否包含尾部分隔符，包含的话即是最后一包数据，写入之后退出
    while (1)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        memset(szBuf, 0, sizeof(szBuf));
        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szBuf, 128*1024);
        if (s32Size <= 0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }

        pu8DataEnd = http_FindBoundary(szBuf, s32Size, szTailSplit);
        if (NULL == pu8DataEnd)
        {
            write(s32Fd, szBuf, s32Size);
            sync();     // 没有每次sync的话，仅靠最后sync一次会导致venc喂狗超时，进程退出
        }
        else
        {
            pu8DataBegin = szBuf;
            write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
            sync();
            break;
        }

        u32SizeCnt += s32Size;
        printf("plug file recv size: %d, total: %d\n", s32Size, u32SizeCnt);
    }
    close(s32Fd);

    print_level(SV_INFO, "rename %s -> %s\n", pszTmpFile, szFilePath);
    s32Ret = rename(pszTmpFile, szFilePath);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "rename %s %s failed. error: %s\n", pszTmpFile, szFilePath, strerror(errno));
    }
    sync();
    m_stHttpInfo.bWriting = SV_FALSE;

    sendHttpLog(s32ClientFd, "download plug file successful");

    CONFIG_FlashProtection(SV_TRUE);
	strcpy(stPlugUpdate.szFileName,szFilePath);
	strcpy(stPlugUpdate.szPlugPath,szUpdatePlugPath);
    stMsgPkt.pu8Data = (uint8*)&stPlugUpdate;
    stMsgPkt.u32Size = sizeof(MSG_PLUG_UPDATE);
    s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_ALG, OP_REQ_UPDATE_PLUG, &stMsgPkt, &stMsgRet, 0);
    if (SV_SUCCESS != s32Ret || 0 != stMsgRet.stMsg.s32Param)
    {
        print_level(SV_ERROR, "OP_REQ_ADD_PLUG failed.\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        sprintf(szCmd, "rm -f %s", szFilePath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
        goto error_exit;
    }

    HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
    mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    sleep_ms(1000);
    http_mg_Unlink(nc);
    //print_level(SV_INFO, "reboot for update firmware...\n");

    return SV_SUCCESS;

error_exit:
    if (bUnLockFlash)
    {
        CONFIG_FlashProtection(SV_TRUE);
    }

    HTTP_HDL_GenerateErrorReply(s32MsgRet, szJsonBody);
    mg_http_send(SV_TRUE, nc, 0, szJsonBody, strlen(szJsonBody));
    return SV_FAILURE;
}


sint32 http_Hdl_Firmware(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret = 0;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    sint32 s32ClientFd = -1, s32Fd = -1;
    sint32 s32Size = 0;
    uint32 u32SizeCnt = 0, i;
    uint32 u32RecvSize = 0;
    char szHeader[2048];
    char szBuf[128*1024+64];
    char szJsonBody[10*1024] = {0};
    char szHeadSplit[64];
    char szTailSplit[64];
    char szMd5sum[64];
	char szRealMd5sum[64];
    char szFilePath[512];
    char *pszTmp = NULL;
    char *pszFileName = NULL;
    char *pszParentheses = NULL;
	sint32 s32HeadSize;
	uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
    char *pszPktPrefix = HTTP_PKT_PREFIX;
    uint32 u32MaxFileLen = 10*1024*1024;
    SV_BOOL bUnLockFlash = SV_FALSE;
    HTTP_HEADER_S stHttpHeader = {0};
    struct timeval stTimeval = {0};
    fd_set read_fds;
    static SV_BOOL bPrint = SV_FALSE;
	SV_BOOL bReboot = SV_TRUE;
    uint32 u32TmpAvailableSpace;
    uint32 u32PacketSize = 0;
    double available = 0;
    double dPacketMaybeSize;
    uint32 u32PacketMaybeSize;
    char unit;
    char buffer[16];

    char szStoragePath[128] = {0};
    char szTmpFilePath[128] = {0};
    char *pszTmpFile = NULL;
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3))
    if (STORAGE_IsWritable(STORAGE_MAIN_SD1))
    {
        strcpy(szStoragePath, "/mnt/sdcard");
        bReboot = SV_TRUE;
    }
    else if (STORAGE_IsWritable(STORAGE_EXTRA_SD))
    {
        strcpy(szStoragePath, "/mnt/udisk");
        bReboot = SV_TRUE;
    }
    else
    {
        strcpy(szStoragePath, "/tmp");
        bReboot = SV_FALSE;
    }
#elif defined(BOARD_ADA47V1)
    strcpy(szStoragePath, "/userdata");
    bReboot = SV_TRUE;
#else
    strcpy(szStoragePath, "/boot");
    bReboot = SV_TRUE;
#endif
    sprintf(szTmpFilePath, "%s/tmp-upgrade-packet", szStoragePath);

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

#if 0
#if (defined(BOARD_DMS31V2))
    if (BOARD_DMS31V2_V2 == BOARD_GetVersion())
    {
        if (!STORAGE_IsWritable(STORAGE_EXTRA_SD))
        {
            print_level(SV_ERROR, "no storage device for packet.\n");
            s32MsgRet = MSG_DEVICE_LACK;
            goto error_exit;
        }
    }
    else
    {
        if (!STORAGE_IsWritable(STORAGE_MAIN_SD1))
        {
            print_level(SV_ERROR, "no storage device for packet.\n");
            s32MsgRet = MSG_DEVICE_LACK;
            goto error_exit;
        }
    }
#elif defined(BOARD_ADA47V1)
    if (!STORAGE_IsWritable(STORAGE_INNER_EMMC))
    {
        print_level(SV_ERROR, "no storage device for packet.\n");
        s32MsgRet = MSG_DEVICE_LACK;
        goto error_exit;
    }
#endif
#else

#if defined(BOARD_ADA47V1)
    if (!STORAGE_IsWritable(STORAGE_INNER_EMMC))
    {
        print_level(SV_ERROR, "no storage device for packet.\n");
        s32MsgRet = MSG_DEVICE_LACK;
        goto error_exit;
    }
#endif

#endif

    s32ClientFd = MG_FD(nc);
    if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing frimware now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto error_exit;
    }

#if defined(BOARD_WFTR20S3)
    sleep_ms(5000);
#endif

#if (defined(BOARD_ADA32V4) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    u32MaxFileLen = 256*1024*1024;
#elif (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
    u32MaxFileLen = 256*1024*1024;
#endif

    if (!bPrint)
    {
        print_level(SV_INFO, "get header:\n%.*s\n", hm->head.len, hm->head.ptr);
        print_level(SV_INFO, "get chunk:\n%.*s\n", hm->chunk.len, hm->chunk.ptr);
        bPrint = SV_TRUE;
    }

    s32HeadSize = (hm->head.len < 2048) ? hm->head.len : 2048;
    memcpy(szHeader, hm->head.ptr, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }

    /* 1106判断/tmp空间剩余空间大小和包可能的大小 */
#if defined(BOARD_ADA32V3)
    do
    {
        if (0 != strcmp(szStoragePath, "/tmp"))
        {
            break;
        }

        FILE *fp = popen("df -h /tmp | awk 'NR==2 {print $4}'", "r");
        if (NULL == fp)
        {
            perror("popen");
            s32MsgRet = MSG_NO_ENOUGH_MEMORY;
            goto error_exit;
        }

        if (fgets(buffer, sizeof(buffer), fp) == NULL)
        {
            pclose(fp);
            s32MsgRet = MSG_NO_ENOUGH_MEMORY;
            goto error_exit;
        }
        pclose(fp);

        if (sscanf(buffer, "%lf%c", &available, &unit) == 2)
        {
            available *= 1024 * 1024;
        }
        u32TmpAvailableSpace = (uint32)available;
        u32PacketSize = atoi(stHttpHeader.pszContentLenght);
        dPacketMaybeSize = u32PacketSize*1.0 + u32PacketSize*1.7;
        u32PacketMaybeSize = (uint32)dPacketMaybeSize;
        print_level(SV_DEBUG, "u32TmpAvailableSpace: %dM, u32PacketMaybeSize: %dM\n", u32TmpAvailableSpace/1024/1024, u32PacketMaybeSize/1024/1024);

        if (u32PacketMaybeSize > u32TmpAvailableSpace)
        {
            print_level(SV_ERROR, "u32PacketMaybeSize: %d > u32TmpAvailableSpace: %d!!!\n", u32PacketMaybeSize/1024/1024, u32TmpAvailableSpace/1024/1024);
            s32MsgRet = MSG_NO_ENOUGH_MEMORY;
            goto error_exit;
        }
    } while(0);
#endif

    print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
    if (atoi(stHttpHeader.pszContentLenght) > u32MaxFileLen)
    {
        print_level(SV_ERROR, "file size[%s] is too larged! [limit:%d]\n", stHttpHeader.pszContentLenght, u32MaxFileLen);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    u32RecvSize = hm->chunk.len;
    if (0 == u32RecvSize)
    {
        print_level(SV_INFO, "get chunk zero bytes, return.\n");    // firmware第一次接收到数据时有时候只是接收了头部信息，分片内容会在后面再接收
        return SV_SUCCESS;
    }

    sprintf(szHeadSplit, "--%s", stHttpHeader.pszBoundary);
    sprintf(szTailSplit, "\r\n--%s", stHttpHeader.pszBoundary);

    // 找头部分隔符
    pszTmp = strstr((char *)hm->chunk.ptr, szHeadSplit);
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found split: %s\n", szHeadSplit);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

#if (!defined(BOARD_ADA47V1))
	//找文件MD5
	pszTmp = strstr((char *)hm->chunk.ptr, "\MD5\"\r\n\r\n");
	if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found md5!\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }
	strncpy(szRealMd5sum, pszTmp+strlen("\MD5\"\r\n\r\n"), 32);
	if (strlen(szRealMd5sum) != 32)
    {
		print_level(SV_ERROR, "md5: %s length: %d is not correct!\n", szRealMd5sum, strlen(szRealMd5sum));
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
	}
	print_level(SV_DEBUG, "RealMD5sum:%s\n", szRealMd5sum);
    pszTmp++;
#endif

    // 找文件名
    pszTmp = strstr((char *)hm->chunk.ptr, "filename=\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    pszFileName = pszTmp+strlen("filename=\"");
    pszTmp = strstr(pszFileName, "\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    *pszTmp = '\0';
    pszTmp++;

#if defined(BOARD_ADA47V1)
    if (NULL == strstr(pszFileName, pszPktPrefix) && NULL == strstr(pszFileName, "KBA12C_upgrade") && NULL == strstr(pszFileName, "KBA18E_upgrade") && NULL == strstr(pszFileName, "MN234_upgrade"))
#elif defined(BOARD_ADA32N1)
    if (NULL == strstr(pszFileName, pszPktPrefix) && NULL == strstr(pszFileName, "DMS885N_upgrade") && NULL == strstr(pszFileName, "ADA38N1_upgrade"))
#else
    if (NULL == strstr(pszFileName, pszPktPrefix))
#endif
    {
        print_level(SV_ERROR, "invalid packet file: %s\n", pszFileName);
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    // 从头部分隔符之后开始找两个\r\n，这两个\r\n之后就是数据内容
    pszTmp = strstr(pszTmp, "\r\n\r\n");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found data body.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
    {
        print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
        goto error_exit;
    }
    bUnLockFlash = SV_TRUE;

    pszParentheses = strstr(pszFileName, "(");
    if (NULL != pszParentheses)
    {
        print_level(SV_INFO, "pszFileName: %s with (), need to change name!\n", pszFileName);
        *pszParentheses = '\0';
        strcat(pszFileName, ".bin");
        print_level(SV_INFO, "pszFileName change to %s\n", pszFileName);
    }

    sprintf(szFilePath, "%s/%s", szStoragePath, pszFileName);

    remove(szTmpFilePath);
    s32Fd = open(szTmpFilePath, O_CREAT|O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", szTmpFilePath, strerror(errno));
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    sendHttpLog(s32ClientFd, "begin download firmware");
    m_stHttpInfo.bWriting = SV_TRUE;
    s32Ret = Msg_submitEvent(EP_RTSPSERVER, OP_EVENT_DOWNLOAD_FIRMWARE, NULL);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
    }
    sleep_ms(500);
#if (defined(BOARD_WFCR20S2))
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MCU_STOP_SLEEP, NULL);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
    }
    sleep_ms(100);
#endif

    // 开始位置偏移到这两个\r\n之后，寻找是否包含尾部分隔符，没有尾部分隔符的话直接写入第一包数据
    pu8DataBegin = pszTmp + strlen("\r\n\r\n");
    pu8DataEnd = http_FindBoundary(pu8DataBegin, (uint8 *)hm->chunk.ptr+u32RecvSize-pu8DataBegin, szTailSplit);
    if (NULL == pu8DataEnd)
    {
        pu8DataEnd = hm->chunk.ptr+u32RecvSize;
    }
    write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);

    // 开始循环接收后续数据包，每一包数据都寻找是否包含尾部分隔符，包含的话即是最后一包数据，写入之后退出
    while (1)
    {
        stTimeval.tv_sec = 25;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        memset(szBuf, 0, sizeof(szBuf));
        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szBuf, 128*1024);
        if (s32Size <= 0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }

        pu8DataEnd = http_FindBoundary(szBuf, s32Size, szTailSplit);
        if (NULL == pu8DataEnd)
        {
            write(s32Fd, szBuf, s32Size);
            sync();     // 没有每次sync的话，仅靠最后sync一次会导致venc喂狗超时，进程退出
        }
        else
        {
            pu8DataBegin = szBuf;
            write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
            sync();
            break;
        }

        u32SizeCnt += s32Size;
        printf("firmware recv size: %d, total: %d\n", s32Size, u32SizeCnt);
    }
    close(s32Fd);

    print_level(SV_INFO, "rename %s -> %s\n", szTmpFilePath, szFilePath);
    s32Ret = rename(szTmpFilePath, szFilePath);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "rename %s %s failed. error: %s\n", szTmpFilePath, szFilePath, strerror(errno));
    }
    sync();
    m_stHttpInfo.bWriting = SV_FALSE;

    sendHttpLog(s32ClientFd, "download firmware successful");

#if (!defined(BOARD_ADA47V1))
    s32Ret = http_GetFileMd5(szFilePath, szMd5sum);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_GetFileMd5 failed. [err=%#x]\n", s32Ret);
        remove(szFilePath);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }
	print_level(SV_DEBUG,"szRealMD5sum:%s, FileMD5sum:%s\n",szRealMd5sum, szMd5sum);

    if (0 != strcmp(szRealMd5sum, szMd5sum))
    {
        print_level(SV_ERROR, "md5sum unmatch exp: %s, real: %s\n", szRealMd5sum, szMd5sum);
        remove(szFilePath);
        s32MsgRet = MSG_DATA_UNMATCH;
        goto error_exit;
    }
#endif

    bPrint = SV_FALSE;
    CONFIG_FlashProtection(SV_TRUE);
    HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
    mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    sleep_ms(1000);
    http_mg_Unlink(nc);
    print_level(SV_INFO, "reboot for update firmware...\n");

#if 0
    if (0 != SAFE_System("fw_setenv update y", NORMAL_WAIT_TIME))
    {
        SAFE_SV_System("fw_setenv update y");
    }
#endif
    SAFE_System("sync", NORMAL_WAIT_TIME);

    if (bReboot)
    {
        sleep_ms(1000);
        if (0 != SAFE_System("safereboot", NORMAL_WAIT_TIME))
        {
            SAFE_SV_System("safereboot");
        }
    }
    else
    {
        //启动脚本，用于内存在线升级
    	sleep_ms(2000);

    	// 下面加一个exit退出ipsys，kill有可能出现僵尸进程
    	// ipsys这里直接起update.sh有可能会因为ipsys退出导致脚本运行出现问题，这里改为通过upgrade_detect.sh检测/var/upgradeOnline标志位文件进行在线升级
    	//SAFE_SV_System("/root/update.sh &");
    	SAFE_SV_System("touch /tmp/upgradeOnline");
        exit(EXIT_SUCCESS);
    }

    return SV_SUCCESS;

error_exit:
    if (bUnLockFlash)
    {
        CONFIG_FlashProtection(SV_TRUE);
    }

    bPrint = SV_FALSE;
    HTTP_HDL_GenerateErrorReply(s32MsgRet, szJsonBody);
    mg_http_send(SV_TRUE, nc, 0, szJsonBody, strlen(szJsonBody));
    return SV_FAILURE;
}

sint32 http_Hdl_Upgrade(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret = 0;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    sint32 s32ClientFd = -1, s32Fd = -1;
    sint32 s32WriteBytes = 0;
    sint32 s32Size = 0;
    sint32 s32Offset = 0;
    uint32 u32SizeCnt = 0, i;
    uint32 u32RecvSize = 0;
    char szHeader[2048];
    char szBuf[128*1024+64];
    char szJsonBody[10*1024] = {0};
    char szHeadSplit[64];
    char szTailSplit[64];
    char szMd5sum[64];
    char szFilePath[512];
    char *pszTmp = NULL;
    char *pszFileName = NULL;
#if defined(BOARD_DMS31V2)
    char *pszTmpFile = "/mnt/sdcard/tmp-upgrade-packet";
#elif defined(BOARD_ADA47V1)
    char *pszTmpFile = "/userdata/tmp-upgrade-packet";
#else
    char *pszTmpFile = "/boot/tmp-upgrade-packet";
#endif
    uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
    char *pszPktPrefix = HTTP_PKT_PREFIX;
    char *pszPktName = HTTP_PKT_NAME;
    uint32 u32MaxFileLen = 10*1024*1024;
    SV_BOOL bUnLockFlash = SV_FALSE;
    HTTP_HEADER_S stHttpHeader = {0};
    struct timeval stTimeval = {0};
    fd_set read_fds;
	sint32 s32HeadSize;

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
#if (!defined(BOARD_ADA47V1))
    if (!STORAGE_IsWritable(STORAGE_MAIN_SD1))
#else
    if (!STORAGE_IsWritable(STORAGE_INNER_EMMC))
#endif
    {
        print_level(SV_ERROR, "no storage device for packet.\n");
        s32MsgRet = MSG_DEVICE_LACK;
        goto error_exit;
    }
#endif

    s32ClientFd = MG_FD(nc);
    if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing frimware now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto error_exit;
    }

#if defined(BOARD_WFTR20S3)
    sleep_ms(5000);
#endif

#if (defined(BOARD_ADA32V4) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    u32MaxFileLen = 64*1024*1024;
#elif (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
    u32MaxFileLen = 256*1024*1024;
#endif

    s32HeadSize = (hm->head.len < 2048) ? hm->head.len : 2048;
    memcpy(szHeader, hm->head.ptr, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }
    print_level(SV_INFO, "firmware file size: %s\n", stHttpHeader.pszContentLenght);

    u32RecvSize = hm->chunk.len;
    if (0 == u32RecvSize)
    {
        print_level(SV_INFO, "get chunk zero bytes, return.\n");
        return SV_SUCCESS;
    }

    pszTmp = strstr((char *)hm->message.ptr, "\r\n\r\n");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found data body\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
    {
        print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
        s32MsgRet = MSG_DEVICE_LACK;
        goto error_exit;
    }
    bUnLockFlash = SV_TRUE;

    remove(pszTmpFile);
    s32Fd = open(pszTmpFile, O_CREAT|O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    sendHttpLog(s32ClientFd, "begin download firmware");
    m_stHttpInfo.bWriting = SV_TRUE;
    s32Ret = Msg_submitEvent(EP_RTSPSERVER, OP_EVENT_DOWNLOAD_FIRMWARE, NULL);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
    }
    sleep_ms(500);

    pu8DataBegin = hm->chunk.ptr;
    pu8DataEnd = hm->chunk.ptr+u32RecvSize;
    write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);

    while (1)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szBuf, 128*1024);
        if (s32Size <=0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }

        write(s32Fd, szBuf, s32Size);
        sync();
        u32SizeCnt += s32Size;
        printf("upgrade recv size: %d, total: %d\n", s32Size, u32SizeCnt);
    }
    close(s32Fd);
    rename(pszTmpFile, pszPktName);
    sync();
    m_stHttpInfo.bWriting = SV_FALSE;

    sendHttpLog(s32ClientFd, "download firmware successful");
    /*if (strlen(m_szPacketMd5sum) == 32)
    {
        s32Ret = http_GetFileMd5(szFilePath, szMd5sum);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_GetFileMd5 failed. [err=%#x]\n", s32Ret);
            remove(szFilePath);
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto error_exit;
        }

        print_level(SV_INFO, "md5sum src: %s, real: %s\n", m_szPacketMd5sum, szMd5sum);
        if (0 != strcmp(m_szPacketMd5sum, szMd5sum))
        {
            print_level(SV_ERROR, "md5sum unmatch exp: %s, real: %s\n", m_szPacketMd5sum, szMd5sum);
            remove(szFilePath);
            s32MsgRet = MSG_DATA_UNMATCH;
            goto error_exit;
        }
    }*/

    CONFIG_FlashProtection(SV_TRUE);
    HTTP_HDL_GenerateSuccessReply("{}", pu8Buf);
    mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    sleep_ms(1000);
    http_mg_Unlink(nc);
    print_level(SV_INFO, "reboot for update firmware...\n");

#ifdef PLATFORM_RV1126
    if (0 != SAFE_System("fw_setenv update y", NORMAL_WAIT_TIME))
    {
        SAFE_SV_System("fw_setenv update y");
    }
    SAFE_System("sync", NORMAL_WAIT_TIME);
    sleep_ms(1000);
#endif
#ifdef PLATFORM_NT98539
    if (0 != SAFE_System("fw_setenv update y", NORMAL_WAIT_TIME))
    {
        SAFE_SV_System("fw_setenv update y");
    }
    SAFE_System("sync", NORMAL_WAIT_TIME);
    sleep_ms(1000);
#endif
    if (0 != SAFE_System("safereboot", NORMAL_WAIT_TIME))
    {
        SAFE_SV_System("safereboot");
    }
    return SV_SUCCESS;

error_exit:
    if (bUnLockFlash)
    {
        CONFIG_FlashProtection(SV_TRUE);
    }

    HTTP_HDL_GenerateErrorReply(s32MsgRet, szJsonBody);
    mg_http_send(SV_TRUE, nc, 0, szJsonBody, strlen(szJsonBody));
    return SV_FAILURE;
}


sint32 http_ImportUserPic(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret = 0;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    sint32 s32ClientFd = -1;
    FILE *fp = NULL;
    sint32 s32WriteBytes = 0;
    sint32 s32Size = 0;
    sint32 s32Offset = 0;
    char szHttpContent[128*1024];
    char szJsonBody[10*1024] = {0};
    struct timeval stTimeval = {0};
    fd_set read_fds;

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
    if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing frimware now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto exit;
    }

    //截取数据
    fp = fopen("/var/user.jpeg", "wb");
    if(NULL == fp)
    {
        print_level(SV_ERROR, "create file failed.\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }

    fseek(fp, s32Offset, SEEK_SET);
    //截取第一段数据
    s32WriteBytes = fwrite(hm->body.ptr, sizeof(char), hm->body.len, fp);
    s32Offset += s32WriteBytes;

    while (1)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
        FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        memset(szHttpContent, 0, 128*1024);
        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szHttpContent, 128*1024);
        if (s32Size <= 0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }
        fseek(fp, s32Offset, SEEK_SET);
        s32WriteBytes = fwrite(szHttpContent, sizeof(char), s32Size, fp);
        s32Offset += s32WriteBytes;
    }
    fclose(fp);
    sleep_ms(100);
    print_level(SV_INFO, "import userPic success.\n");

exit:
    CONFIG_FlashProtection(SV_TRUE);
    http_mg_reply(nc, s32MsgRet, (s32MsgRet == MSG_SUCCESS_RES) ? "{}" : szJsonBody);
    sleep_ms(500);
    http_mg_Unlink(nc);
    return (s32MsgRet == MSG_SUCCESS_RES) ? SV_SUCCESS : SV_FAILURE;
}

sint32 http_ImportFaceId(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret;
    sint32 s32ClientFd = -1;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    FILE *fp = NULL;
    char szHttpContent[1024];
    char szSplit[128];
    char filename[128] = {0};
    char szJsonBody[10*1024] = {0};
    sint32 s32WriteBytes = 0;
    sint32 s32Size = 0;
    sint32 s32Offset = 0;
    sint32 s32Pos;
    fd_set read_fds;
    struct timeval stTimeval = {0};
    char *pszFileName = HTTP_FACEID_PATH;

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
    if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing frimware now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto exit;
    }

    // 解除文件系统写保护
    if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
    {
        print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }

    remove(pszFileName);

    //截取数据
    fp = fopen(pszFileName, "wb");
    if (NULL == fp)
    {
        print_level(SV_ERROR, "create file failed.\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }

    fseek(fp, s32Offset, SEEK_SET);
    //截取第一段数据
	sprintf(szSplit, "Content-Type: application/octet-stream\r\n\r\n");
    if (0 != hm->chunk.len)
    {
        s32Pos = findStrTailPos(hm->chunk.ptr, hm->chunk.len, szSplit);
        s32WriteBytes = fwrite(hm->chunk.ptr+s32Pos, sizeof(char), hm->chunk.len-s32Pos, fp);
    }
    else
    {
    	s32Pos = findStrTailPos(hm->body.ptr, hm->body.len, szSplit);
        s32WriteBytes = fwrite(hm->body.ptr+s32Pos, sizeof(char), hm->body.len-s32Pos, fp);
    }
    s32Offset += s32WriteBytes;

    while (1)
    {
        stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
        FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        memset(szHttpContent,0,1024);
        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szHttpContent, 128*1024);
        if (s32Size <=0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }
        fseek(fp, s32Offset, SEEK_SET);
        s32WriteBytes = fwrite(szHttpContent, sizeof(char), s32Size, fp);
        s32Offset += s32WriteBytes;
    }
    fclose(fp);
    sleep_ms(500);

    s32Ret = SAFE_System("tar -xzvf /var/FaceId.tar.gz -C /root", LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: tar -xzvf %s -C /root failed.\n", pszFileName);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto exit;
    }
    remove(pszFileName);

    s32MsgRet = MSG_SUCCESS_RES;
    print_level(SV_INFO, "import FaceId success.\n");

exit:
    CONFIG_FlashProtection(SV_TRUE);
    http_mg_reply(nc, s32MsgRet, (s32MsgRet == MSG_SUCCESS_RES) ? "{}" : szJsonBody);
    sleep_ms(500);
    http_mg_Unlink(nc);
    return (s32MsgRet == MSG_SUCCESS_RES) ? SV_SUCCESS : SV_FAILURE;
}

sint32 http_ImportConfig(SV_BOOL bFactory, struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    sint32 s32Fd = -1, s32ClientFd = -1;
    char szCmd[128] = {0};
    char szHttpContent[128*1024] = {0};
    char szJsonBody[20*1024] = {0};
    char szJsonOut[512];
    char szSplit[128];
	FILE *fp = NULL;
    sint32 s32WriteBytes = 0;
    sint32 s32Size = 0;
    sint32 s32Offset = 0;
    sint32 s32Pos;
    fd_set read_fds;
    struct timeval stTimeval = {0};
	SV_BOOL bOldMethod;
    char *pszFileName = "/etc/config.tar.gz";

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
	if (m_stHttpInfo.bWriting)
    {
        print_level(SV_ERROR, "it is writing frimware now.\n");
        s32MsgRet = MSG_DEVICE_BUSY;
        goto exit;
    }

    // 解除文件系统写保护
    if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
    {
        print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
        goto exit;
    }

    remove(pszFileName);

	//截取数据
	fp = fopen(pszFileName, "wb");
	if (NULL == fp)
	{
		print_level(SV_ERROR, "create file failed.\n");
        CONFIG_FlashProtection(SV_TRUE);
        s32MsgRet = MSG_DEFAULT_FAIL;
		goto exit;

	}

	fseek(fp, s32Offset, SEEK_SET);
    //截取第一段数据
	sprintf(szSplit, "Content-Type: application/octet-stream\r\n\r\n");
    if (0 != hm->chunk.len)
    {
        s32Pos = findStrTailPos(hm->chunk.ptr, hm->chunk.len, szSplit);
        s32WriteBytes = fwrite(hm->chunk.ptr+s32Pos, sizeof(char), hm->chunk.len-s32Pos, fp);
    }
    else
    {
    	s32Pos = findStrTailPos(hm->body.ptr, hm->body.len, szSplit);
        s32WriteBytes = fwrite(hm->body.ptr+s32Pos, sizeof(char), hm->body.len-s32Pos, fp);
    }
    s32Offset += s32WriteBytes;

	while (1)
	{
	    stTimeval.tv_sec = 5;
        stTimeval.tv_usec = 0;
        FD_ZERO(&read_fds);
	    FD_SET(s32ClientFd, &read_fds);
        s32Ret = select(s32ClientFd + 1, &read_fds, NULL, NULL, &stTimeval);
        if (s32Ret <= 0)
        {
            print_level(SV_WARN, "select failed. [err=%d]\n", s32Ret);
            break;
        }

        memset(szHttpContent, 0, 128*1024);
        s32Size = mg_http_recv(SV_TRUE, nc, s32ClientFd, szHttpContent, 128*1024);
        if (s32Size <= 0)
        {
            print_level(SV_WARN, "recv failed. [s32Size=%d]\n", s32Size);
            break;
        }
        fseek(fp, s32Offset, SEEK_SET);
		s32WriteBytes = fwrite(szHttpContent,sizeof(char),s32Size,fp);
		s32Offset += s32WriteBytes;
    }
    fclose(fp);
    sleep_ms(500);

    if (0 != access("/var/config", F_OK))
    {
        SAFE_System("mkdir -p /var/config", NORMAL_WAIT_TIME);
    }
    SAFE_System("rm /var/config/*", NORMAL_WAIT_TIME);
	s32Ret = SAFE_System("tar -zxvf /etc/config.tar.gz -C /var/config", NORMAL_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: tar -zxvf /etc/config.tar.gz -C /var/config failed.\n");
        s32MsgRet = MSG_FILE_LACK;
        goto exit;
    }

    bOldMethod = SV_FALSE;
    if (0 != access(HTTP_CONFIG_JSON_PATH, F_OK))
    {
        print_level(SV_WARN, "config file: %s is not exist!\n", HTTP_CONFIG_JSON_PATH);
        if (0 != access("/var/config/config.xml", F_OK) || 0 != access("/var/config/config_bak1.xml", F_OK) || 0 != access("/var/config/config_bak2.xml", F_OK))
        {
            print_level(SV_WARN, "config file: /var/config/config.xml is not exist!\n");
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }
        else
        {
            bOldMethod = SV_TRUE;
        }
    }

    if (bOldMethod)
    {
        print_level(SV_INFO, "old import config method.\n");
        strcpy(szCmd, "mv /var/config/*.xml /etc");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }

        sprintf(szCmd, "cp %s %s", CONFIG_XML, CONFIG_TMP_PATH);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }

        /* 如果是在扫码时导入配置，不马上生效 */
        if (bFactory)
        {
            s32MsgRet = MSG_SUCCESS_RES;
            print_level(SV_INFO, "import config when scan, skip JSON_HDL_ReloadConfig.\n");
            goto exit;
        }

        s32Ret = JSON_HDL_ReloadConfig();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "JSON_HDL_ReloadConfig failed.\n");
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }
        else
        {
            s32MsgRet = MSG_SUCCESS_RES;
            print_level(SV_INFO, "import config success.\n");
        }
    }
    else
    {
        print_level(SV_INFO, "new import config method.\n");
        s32Fd = open(HTTP_CONFIG_JSON_PATH, O_RDONLY);
        if (s32Fd <= 0)
        {
            print_level(SV_ERROR, "open %s failed.\n", HTTP_CONFIG_JSON_PATH);
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }

        s32Ret = read(s32Fd, szJsonBody, 20*1024);
        if (s32Ret <= 0)
        {
            print_level(SV_ERROR, "read %s failed.\n", HTTP_CONFIG_JSON_PATH);
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }
        print_level(SV_INFO, "import config:\n%s\n", szJsonBody);

        s32Ret = JSON_HDL_SetConfig(szJsonBody, szJsonOut);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "JSON_HDL_SetConfig failed.\n");
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }
        else
        {
            s32MsgRet = MSG_SUCCESS_RES;
            print_level(SV_INFO, "import config success.\n");
        }
        close(s32Fd);
        s32Fd = -1;
        remove(HTTP_CONFIG_JSON_PATH);

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5))
        memset(szJsonBody, 0, sizeof(szJsonBody));
        memset(szJsonOut, 0, sizeof(szJsonOut));

        s32Fd = open(IMG_PARAM_JSON_PATH, O_RDONLY);
        if (s32Fd <= 0)
        {
            print_level(SV_ERROR, "open %s failed.\n", IMG_PARAM_JSON_PATH);
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }

        s32Ret = read(s32Fd, szJsonBody, 1024);
        if (s32Ret <= 0)
        {
            print_level(SV_ERROR, "read %s failed.\n", IMG_PARAM_JSON_PATH);
            s32MsgRet = MSG_DEFAULT_FAIL;
            goto exit;
        }
        print_level(SV_INFO, "set image param:\n%s\n", szJsonBody);

        s32Ret = JSON_HDL_SetImageParam(szJsonBody, szJsonOut);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "JSON_HDL_SetConfig failed.\n");
            s32MsgRet = MSG_DEFAULT_FAIL;
        }
        else
        {
            s32MsgRet = MSG_SUCCESS_RES;
            print_level(SV_INFO, "import image config success.\n");
        }
        close(s32Fd);
        s32Fd = -1;
        remove(IMG_PARAM_JSON_PATH);
#endif
    }

exit:
    if (s32Fd > 0)
    {
        close(s32Fd);
    }
    if (COMMON_IsPathExist(pszFileName))
    {
        remove(pszFileName);
    }
	CONFIG_FlashProtection(SV_TRUE);
    http_mg_reply(nc, s32MsgRet, (s32MsgRet == MSG_SUCCESS_RES) ? "{}" : szJsonBody);
    sleep_ms(500);
    http_mg_Unlink(nc);
    return (s32MsgRet == MSG_SUCCESS_RES) ? SV_SUCCESS : SV_FAILURE;
}

sint32 http_ImportKey(struct mg_connection *nc, struct mg_http_message *hm, uint8 *pu8Buf)
{
    sint32 s32Ret = 0;
    sint32 s32MsgRet = MSG_SUCCESS_RES;
    sint32 s32ClientFd = -1, s32Fd = -1;
    uint32 u32RecvSize = 0;
    char szHeader[2048];
    char szJsonBody[10*1024] = {0};
    char szHeadSplit[64];
    char szTailSplit[64];
    char *pszTmp = NULL;
    char *pszFileName = NULL;
	sint32 s32HeadSize;
    char *pszTmpFile = "/var/test_license.txt";
    uint8 *pu8DataBegin = NULL, *pu8DataEnd = NULL;
    uint32 u32MaxFileLen = 128*1024;
    HTTP_HEADER_S stHttpHeader = {0};

    if (NULL == nc || NULL == hm || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
    u32RecvSize = hm->chunk.len;
    s32HeadSize = (hm->head.len < 2048) ? hm->head.len : 2048;
    memcpy(szHeader, hm->head.ptr, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader(szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }

    print_level(SV_DEBUG, "Content-Lenght: %s, boundary: %s\n", stHttpHeader.pszContentLenght, stHttpHeader.pszBoundary);
    if (atoi(stHttpHeader.pszContentLenght) > u32MaxFileLen)
    {
        print_level(SV_ERROR, "file size[%s] is too larged! [limit:%d]\n", stHttpHeader.pszContentLenght, u32MaxFileLen);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    sprintf(szHeadSplit, "--%s", stHttpHeader.pszBoundary);
    sprintf(szTailSplit, "\r\n--%s", stHttpHeader.pszBoundary);
    pszTmp = strstr((char *)hm->chunk.ptr, szHeadSplit);
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found split: %s\n", szHeadSplit);
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    pszTmp = strstr((char *)hm->chunk.ptr, "filename=\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    pszFileName = pszTmp+strlen("filename=\"");
    pszTmp = strstr(pszFileName, "\"");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found filename.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    *pszTmp = '\0';
    pszTmp++;
    pszTmp = strstr(pszTmp, "\r\n\r\n");
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "not found data body.\n");
        s32MsgRet = MSG_RANGE_OUT;
        goto error_exit;
    }

    remove(pszTmpFile);
    s32Fd = open(pszTmpFile, O_CREAT|O_RDWR);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open file: %s failed. [err: %s]\n", pszTmpFile, strerror(errno));
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    pu8DataBegin = pszTmp + strlen("\r\n\r\n");
    pu8DataEnd = http_FindBoundary(pu8DataBegin, (uint8 *)hm->chunk.ptr+u32RecvSize-pu8DataBegin, szTailSplit);
    if (NULL == pu8DataEnd)
    {
        pu8DataEnd = hm->chunk.ptr+u32RecvSize;
    }

    write(s32Fd, pu8DataBegin, pu8DataEnd-pu8DataBegin);
    close(s32Fd);
    s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_CHECK_KEY_FILE, NULL, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_CHECK_KEY_FILE failed.\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    // 解除文件系统写保护
    if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
    {
        print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
        s32MsgRet = MSG_DEFAULT_FAIL;
        goto error_exit;
    }

    SAFE_System("mv /var/test_license.txt /etc/license2.txt", NORMAL_WAIT_TIME);
    CONFIG_FlashProtection(SV_TRUE);
    http_mg_reply(nc, MSG_SUCCESS_RES, "{}");
    print_level(SV_INFO, "import key success.\n");

    return SV_SUCCESS;

error_exit:
    http_mg_reply(nc, s32MsgRet, (s32MsgRet == MSG_SUCCESS_RES) ? "{}" : szJsonBody);
    return SV_FAILURE;
}


sint32 http_ExportFaceId(struct mg_connection *nc, uint8 *pu8Buf)
{
    sint32 s32Ret;
    sint32 s32ClientFd = -1;
    char szJsonBody[10*1024];
    char szCmd[128] = {0};
    char *pszFileName = "/var/FaceId.tar.gz";

    if (NULL == nc || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    sprintf(szCmd, "tar -zcvf %s -C /root ID", pszFileName);
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "%s failed.\n", szCmd);
        goto error_exit;
    }
    sleep_ms(500);

    if (!COMMON_IsPathExist(pszFileName))
    {
        print_level(SV_ERROR, "config file: %s is not exist!\n", pszFileName);
        goto error_exit;
    }

    s32ClientFd = MG_FD(nc);
    s32Ret = HTTP_HDL_ReplyByMultipartFormFile(SV_TRUE, nc, s32ClientFd, pu8Buf, pszFileName);
    remove(pszFileName);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HTTP_HDL_ReplyByMultipartFormFile failed.\n");
        goto error_exit;
    }

    print_level(SV_INFO, "export FaceID success.\n");
    return SV_SUCCESS;

error_exit:
    http_mg_reply(nc, MSG_DEFAULT_FAIL, szJsonBody);
    return SV_FAILURE;
}

sint32 http_ExportConfig(struct mg_connection *nc, uint8 *pu8Buf)
{
    sint32 s32Ret;
    sint32 s32Fd = -1;
    sint32 s32ClientFd = -1;
    char szJsonBody[20*1024];
    char szCmd[128] = {0};
    char *pszFileName = "/var/config.tar.gz";

    if (NULL == nc || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    if (!COMMON_IsPathExist("/var/config"))
    {
        SAFE_System("mkdir -p /var/config", NORMAL_WAIT_TIME);
    }

    strcpy(szCmd, "export_config");
    s32Ret = JSON_HDL_GetConfig(szCmd, szJsonBody);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "JSON_HDL_GetConfig failed. [err: %#x]\n", s32Ret);
        goto error_exit;
    }
    else
    {
        print_level(SV_INFO, "export config len:%d\n%s\n", strlen(szJsonBody), szJsonBody);
        sint32 s32Fd = open(HTTP_CONFIG_TMP_PATH, O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "open file: %s failed. [err:%s]\n", HTTP_CONFIG_TMP_PATH, strerror(errno));
            goto error_exit;
        }

        s32Ret = write(s32Fd, szJsonBody, strlen(szJsonBody));
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "write file: %s failed. [err:%s]\n", HTTP_CONFIG_TMP_PATH, strerror(errno));
            goto error_exit;
        }
        close(s32Fd);
        s32Fd = -1;
        rename(HTTP_CONFIG_TMP_PATH, HTTP_CONFIG_JSON_PATH);
    }


    if (!COMMON_IsPathExist(HTTP_CONFIG_JSON_PATH))
    {
        print_level(SV_ERROR, "config file: %s is not exist!\n", HTTP_CONFIG_JSON_PATH);
        goto error_exit;
    }

    sprintf(szCmd, "tar -zcvf %s -C /var/config ./config.json", pszFileName);
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: %s failed.\n", szCmd);
        goto error_exit;
    }
    sleep_ms(500);

    if (!COMMON_IsPathExist(pszFileName))
    {
        print_level(SV_ERROR, "config file: %s is not exist!\n", pszFileName);
        goto error_exit;
    }

    s32ClientFd = MG_FD(nc);
    HTTP_HDL_ReplyByMultipartFormFile(SV_TRUE, nc, s32ClientFd, pu8Buf, pszFileName);
    remove(pszFileName);
    remove(HTTP_CONFIG_JSON_PATH);
    print_level(SV_INFO, "export config success.\n");
    return SV_SUCCESS;

error_exit:
    if (s32Fd >= 0)
    {
        close(s32Fd);
        s32Fd = -1;
    }

    http_mg_reply(nc, MSG_DEFAULT_FAIL, szJsonBody);
    return SV_FAILURE;
}

sint32 http_ExportLog(struct mg_connection *nc, uint8 *pu8Buf)
{
    sint32 s32Ret;
    sint32 s32ClientFd = -1;
    char szJsonBody[10*1024];
    char szCmd[128] = {0};
    char szPath[128] = {0};

    if (NULL == nc || NULL == pu8Buf)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return SV_FAILURE;
    }

    strcpy(szPath, "/var/log.tar.gz");
    strcpy(szCmd, "tar -zcvf /var/log.tar.gz -C /var/log .");
#if (defined(BOARD_DMS31V2)||defined(BOARD_ADA32C4))
    if (STORAGE_IsWritable(STORAGE_MAIN_SD1))
    {
        strcpy(szPath, "/tmp/log.tar.gz");
        strcpy(szCmd, "tar -zcvf /tmp/log.tar.gz -C /mnt/sdcard/log .");
    }
    else if (STORAGE_IsWritable(STORAGE_EXTRA_SD))
    {
        strcpy(szPath, "/tmp/log.tar.gz");
        strcpy(szCmd, "tar -zcvf /tmp/log.tar.gz -C /mnt/udisk/log .");
    }
#elif defined(BOARD_ADA47V1)
    if (STORAGE_IsWritable(STORAGE_INNER_EMMC))
    {
        strcpy(szPath, "/tmp/log.tar.gz");
        strcpy(szCmd, "tar -zcvf /tmp/log.tar.gz -C /userdata/log .");
    }
#endif
    s32Ret = SAFE_System(szCmd, LARGE_WAIT_TIME);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: %s failed.\n", szCmd);
        goto error_exit;
    }

    sleep_ms(1000);

    if (!COMMON_IsPathExist(szPath))
    {
        print_level(SV_ERROR, "config file: %s is not exist!\n", HTTP_CONFIG_JSON_PATH);
        goto error_exit;
    }

    s32ClientFd = MG_FD(nc);
    s32Ret = HTTP_HDL_ReplyByMultipartFormFile(SV_TRUE, nc, s32ClientFd, pu8Buf, szPath);
    remove(szPath);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "HTTP_HDL_ReplyByMultipartFormFile failed.\n");
        goto error_exit;
    }
    print_level(SV_INFO, "export logfile success.\n");
    return SV_SUCCESS;

error_exit:
    http_mg_reply(nc, MSG_DEFAULT_FAIL, szJsonBody);
    return SV_FAILURE;
}

sint32 http_mg_replySnap(struct mg_connection *nc)
{
	sint32 s32Ret = 0;
	sint32 s32ImgSize = 0;
	char *pszContentType = NULL;
	char szETag[32];
	char szGMTTime[64];
	char szModifyTime[64];
	char szBuf[16*1024];
	time_t tNow;
	static time_t tLast = 0;
    sint32 s32Size = 0;
    sint32 s32SendSize = 0;
    sint32 s32Offset = 0;


	pthread_mutex_lock(&m_stSnapInfo.mutexSnap);
	s32ImgSize = m_stSnapInfo.s32ImgSize;
	pszContentType = "image/jpeg";
	tNow = time(NULL);
	if (tLast == 0)
		tLast = tNow;
	strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tNow));
	strftime(szModifyTime, sizeof(szModifyTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tLast));
	HTTP_HDL_GenerateETag("snap0", s32ImgSize, (sint32)tLast, szETag);
	sprintf(szBuf,	"HTTP/1.1 200 OK \r\n"
					"Content-Type: %s \r\n"
					"Last-Modified: %s\r\n"
					"ETag: \"%s\"\r\n"
					"Date: %s\r\n"
					"Access-Control-Allow-Origin: *\r\n"
					"Content-Length: %d \r\n\r\n", pszContentType, szModifyTime, szETag, szGMTTime, s32ImgSize);


    s32Ret = mg_http_send(SV_TRUE, nc, 0, szBuf, strlen(szBuf));
	if (s32Ret < 0)
	{
		print_level(SV_ERROR, "send header to client failed. [err=%#x]\n", errno);
	}

    #if 0
    s32SendSize = 0;
    s32Size = 16*1024;
    while (s32SendSize < s32ImgSize)
    {
        s32Ret = mg_http_send(SV_TRUE, nc, 0, m_stSnapInfo.apvImgBuf+s32SendSize, s32Size);
        if (s32Ret < 0)
        {
            print_level(SV_WARN, "send body to client failed. [err=%s]\n", strerror(errno));
            if (EPIPE == errno)
            	break;
        }

        s32SendSize += s32Ret;
        print_level(SV_INFO, "send size: %d, fd: %d, id: %d\n", s32SendSize, nc->fd, nc->id);
    }
    #else
    s32Ret = mg_http_send(SV_TRUE, nc, 0, m_stSnapInfo.apvImgBuf, s32ImgSize);
    if (s32Ret < 0)
    {
        print_level(SV_WARN, "send body to client failed. [err=%s]\n", strerror(errno));
    }
    #endif
	tLast = tNow;

	m_stSnapInfo.bWaitRead = SV_FALSE;
	pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
	return SV_SUCCESS;

}

static void http_mg_handleMJpeg(void *param)
{
    struct mg_connection *nc = (struct mg_connection *) param;
	sint32 s32Ret = 0;
	sint32 s32ImgSize = 0;
	char *pszContentType = NULL;
	char szETag[32];
	char szGMTTime[64];
	char szModifyTime[64];
	char szBuf[501*1024];
    uint32 u32VideoBufSize = 1024*501;
	time_t tNow;
	static time_t tLast = 0;
    sint32 s32Size = 0;
    sint32 s32SendSize = 0;
    sint32 s32Offset = 0;
    struct timeval stTimeval = {0};
    uint8 *pu8Buf = NULL;         // 写入mjpeg缓存
    uint8 *pu8VideoBuf = NULL;
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    SFIFO_MSHEAD *pstPacket = NULL;
    uint32 u32Serial =0;
    uint32 u32Cnt = 0;
    uint32 u32PacketSize = 0;


    s32Ret = prctl(PR_SET_NAME, "mjpeg");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    stTimeval.tv_sec = 3;					                                    // 阻塞时间，秒
    stTimeval.tv_usec = 0;
	setsockopt(MG_FD(nc), SOL_SOCKET, SO_SNDBUF, (const char*)&u32VideoBufSize,sizeof(int));
	setsockopt(MG_FD(nc), SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
	setsockopt(MG_FD(nc), SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
    sleep_ms(1);

    pu8Buf = (uint8 *)malloc(2*1024);
	if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc: pu8Buf failed.\n");
        return;
    }

    pu8VideoBuf = (uint8 *)malloc(u32VideoBufSize);
	if (NULL == pu8VideoBuf)
    {
        print_level(SV_ERROR, "malloc: pu8Buf failed.\n");
        return;
    }

    /* 获取当前时间 */
    time_t now = time(NULL);
    strftime(szGMTTime, sizeof(szGMTTime) , "%a, %b %d %Y %H:%M:%S GMT", gmtime(&now));

    sprintf((char *)pu8Buf, "HTTP/1.1 200 OK\r\n"
                            "Access-Control-Allow-Origin: *\r\n"
                            "Connection: keep-alive\r\n"
                            "Server: MJPG-Streamer/0.2\r\n"
                            "Cache-Control: no-store, no-cache, must-revalidate, pre-check=0, post-check=0, max-age=0\r\n"
                            "Pragma: no-cache\r\n"
                            "Expires: %s\r\n"
                            "Content-Type: multipart/x-mixed-replace;boundary=BOUNDARY\r\n"
                            "\r\n"
                            "--BOUNDARY\r\n", szGMTTime);

    s32Size = mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    if (s32Size < 0)
    {
        print_level(SV_ERROR, "Sent Http Header to client failed. [err=%#x]\n", errno);
        print_level(SV_ERROR, "Error: %s\n", strerror(errno));
    }

    s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }

    print_level(SV_DEBUG, "s32MainQueId = %d, s32MainConsumerId =%d\n", s32MainQueId, s32MainConsumerId);

/*
    s32Ret = SFIFO_GetMediaAttr(s32MainQueId, &stMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_GetMediaAttr stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }
*/

    while (1)
    {
        if (SV_TRUE == bResetMJpeg)    // 修改ip时先关闭之前打开的队列
        {
            print_level(SV_INFO, "http-mjpeg reset\n");
            //bResetMJpeg = SV_FALSE;
            break;
        }
        s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
        if (SV_SUCCESS != s32Ret)
        {
            //print_level(SV_ERROR, "SFIFO_GetPacket failed. [err=%#x]\n", s32Ret);
            //print_level(SV_ERROR, "Error: %s\n", strerror(errno));
            //sleep_ms(10);
            continue;
        }

        if (pstPacket->type == 1)
        {
            //print_level(SV_DEBUG, "pts: %lld\n", pstPacket->pts);
            //print_level(SV_DEBUG, "size: %d\n", pstPacket->msdsize);
            //print_level(SV_DEBUG, "type = %d, serial = %d\n", pstPacket->type, pstPacket->serial);
            memset(pu8VideoBuf, 0, u32VideoBufSize);
            if (pstPacket->msdsize > u32VideoBufSize)
            {
                print_level(SV_ERROR, "packet size %d lager than buf size!\n", pstPacket->msdsize);
                continue;
            }
            u32PacketSize = pstPacket->msdsize;
            memcpy(pu8VideoBuf, pstPacket->data, pstPacket->msdsize);

            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
            }

            if (pstPacket->serial != u32Serial + 1) // 监测帧顺序是否正常
            {
                print_level(SV_WARN, "Abnormal Frame Serial Number. Present Frame: %d, Last Frame: %d.\n", pstPacket->serial, u32Serial);
                //print_level(SV_DEBUG, "u32PacketSize = %d\n", u32PacketSize);
            }
            u32Serial = pstPacket->serial;

            memset(pu8Buf, 0, 2*1024);
            //print_level(SV_DEBUG, "FrameType: %d(%dx%d) pts: %lld, serial: %d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->serial);

            sprintf((char*)pu8Buf, "Content-Type: image/jpeg\r\n" \
                                   "Content-Length: %d\r\n" \
                                   "\r\n", u32PacketSize);

            s32Size = mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
            if (s32Size < 0)
            {
                print_level(SV_ERROR, "Sent MJpeg Header to client failed. [err=%#x]\n", errno);
                print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                //break;
            }

            s32Size = mg_http_send(SV_TRUE, nc, 0, pu8VideoBuf, u32PacketSize);
            if (s32Size < 0)
            {
                print_level(SV_ERROR, "Sent MJpeg Data to client failed. [err=%#x]\n", errno);
                print_level(SV_ERROR, "Error: %s\n", strerror(errno));

                ++u32Cnt;
                if(u32Cnt > 3)
                {
                    break;
                }
            }
            else
            {
                u32Cnt = 0;
            }

            memset(pu8Buf, 0, 2*1024);
            sprintf((char*)pu8Buf, "\r\n"
                                   "--BOUNDARY\r\n");
            s32Size = mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
            if (s32Size < 0)
            {
                print_level(SV_ERROR, "Sent Http boundary to client failed. [err=%#x]\n", errno);
                print_level(SV_ERROR, "Error: %s\n", strerror(errno));
                //break;
            }
                //print_level(SV_DEBUG, "sent size: %d\n", s32Size);

        }
        else
        {
            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
            }
        }
        //sleep_ms(5);
    }

    print_level(SV_DEBUG, "s32MainQueId = %d, s32MainConsumerId =%d\n", s32MainQueId, s32MainConsumerId);
    s32Ret = SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadClose stream: %s failed.\n", SFIFO_MAIN_STREAM);
        return;
    }

    m_stHttpInfo.u32MJpegClients --;
    print_level(SV_INFO, "http-mpeg clients: %d\n", m_stHttpInfo.u32MJpegClients);
    free(pu8Buf);
    free(pu8VideoBuf);
   	pu8Buf = NULL;
    pu8VideoBuf = NULL;

    if (0 == m_stHttpInfo.u32MJpegClients)    // 每个客户端打开的队列都被关闭后则将标志位置回false
    {
        bResetMJpeg = SV_FALSE;
    }



    s32Ret = mg_http_send(SV_TRUE, nc, 0, szBuf, strlen(szBuf));
	if (s32Ret < 0)
	{
		print_level(SV_ERROR, "send header to client failed. [err=%#x]\n", errno);
	}

    #if 0
    s32SendSize = 0;
    s32Size = 16*1024;
    while (s32SendSize < s32ImgSize)
    {
        s32Ret = mg_http_send(SV_TRUE, nc, 0, m_stSnapInfo.apvImgBuf+s32SendSize, s32Size);
        if (s32Ret < 0)
        {
            print_level(SV_WARN, "send body to client failed. [err=%s]\n", strerror(errno));
            if (EPIPE == errno)
            	break;
        }

        s32SendSize += s32Ret;
        print_level(SV_INFO, "send size: %d, fd: %d, id: %d\n", s32SendSize, nc->fd, nc->id);
    }
    #else
    s32Ret = mg_http_send(SV_TRUE, nc, 0, m_stSnapInfo.apvImgBuf, s32ImgSize);
    if (s32Ret < 0)
    {
        print_level(SV_WARN, "send body to client failed. [err=%s]\n", strerror(errno));
    }
    #endif
	tLast = tNow;

	return SV_SUCCESS;

}

static void mg_ws_replySnap(void *param)
{
  	struct mg_connection *nc = (struct mg_connection *) param;
	uint8 u8Header[14] = {0};
	uint32 u32HeaderLen = 0;
	uint32 u32SendBuf = 1024*300;
	sint32 s32Ret = 0;
	char szFilePath[256];
	sint32 s32SendLen = 1024*20;
    char szBuf[s32SendLen]={0};
	uint64 u64FileSize=0;
	sint32 s32WriteSize = 0;
	sint32 s32SendRet = 0;
    struct stat stFileInfo = {0};
	sint32 s32ReadSize = 0;
	sint32 s32Fd = 0;
	struct timeval stTimeval = {0};
	s32Ret = prctl(PR_SET_NAME, "ws_quick_jpeg");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    stTimeval.tv_sec = 3;					                                    // 阻塞时间，秒
    stTimeval.tv_usec = 0;
	setsockopt(MG_FD(nc), SOL_SOCKET, SO_SNDBUF, (const char*)&u32SendBuf,sizeof(int));
	setsockopt(MG_FD(nc), SOL_SOCKET, SO_SNDTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
	setsockopt(MG_FD(nc), SOL_SOCKET, SO_RCVTIMEO, (const char*)&stTimeval, sizeof(struct timeval));
    sleep_ms(1);

	while(1)
	{
		pthread_mutex_lock(&m_stSnapInfo.mutexSnap);
		if(nc->is_closing)
		{
			print_level(SV_INFO,"connection close.\n");
			close_conn(nc);
			pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
			break;
		}
		u32HeaderLen = mkhdr(m_stSnapInfo.s32ImgSize, WEBSOCKET_OP_BINARY, nc->is_client, u8Header);
		s32Ret = nc->is_tls ? mg_tls_send(nc, u8Header, u32HeaderLen) : mg_sock_send(nc, u8Header, u32HeaderLen);
		if(-1 == s32Ret)
		{
			print_level(SV_INFO,"connection close.\n");
			pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
			break;
		}
		s32Ret =nc->is_tls ? mg_tls_send(nc, m_stSnapInfo.apvImgBuf,m_stSnapInfo.s32ImgSize) : mg_sock_send(nc, m_stSnapInfo.apvImgBuf,m_stSnapInfo.s32ImgSize);
		pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
		memset(u8Header,0,14);
		sleep_ms(40);
	}
}

void http_quickJpegTimer()
{
    uint32 u32StepTimeMs = 0;
    static uint32 u32QuickJpegTime = 0;
    static struct timespec tvLast = {0, 0};
    struct timespec tvNow = {0, 0};

    /* webui拉流计时，5s没有继续请求jpeg，会关闭快速抓图功能 */
    if (m_stHttpInfo.s32QuickHandleWtd >= 0)
    {
        clock_gettime(CLOCK_MONOTONIC, &tvNow);
        u32StepTimeMs = ((tvNow.tv_sec*1000 + tvNow.tv_nsec/1000000) - (tvLast.tv_sec*1000 + tvLast.tv_nsec/1000000));
        tvLast = tvNow;
        u32QuickJpegTime += u32StepTimeMs;

        if (u32QuickJpegTime >= 500)
        {
            u32QuickJpegTime = 0;
            m_stHttpInfo.s32QuickHandleWtd--;
        }

        if (m_stHttpInfo.s32QuickHandleWtd == -1)
        {
            HTTP_HDL_QuickJpeg(-1);
        }
    }
}

static void *http_websocket_cmd_handler(void *pvArg)
{
    char *pData = (char *)pvArg;
    if (NULL == pData)
    {
        return NULL;
    }

    sint32 s32Ret;
    char pOut[1024];
    char *pszCommand = NULL;
    char *pszArgsBody = NULL;
	cJSON *pstJson = NULL, *pstCommand = NULL, *pstArgs = NULL;
    cJSON *pstSdChn = NULL, *pstSdItem = NULL;
    cJSON *pstRoot = NULL, *pstCmd = NULL, *pstUserName = NULL, *pstDriverName = NULL;
	MSG_PACKET_S stMsgPkt = {0};

	pstJson = cJSON_Parse(pData);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed. get body: \n%s\n", pData);
        return NULL;
    }

    pstCommand = cJSON_GetObjectItemCaseSensitive(pstJson, "cmd");
    if (NULL == pstCommand || (NULL != pstCommand && !cJSON_IsString(pstCommand)))
    {
        print_level(SV_ERROR, "get invalid body: \n%s\n", pData);
		cJSON_Delete(pstJson);
        return NULL;
    }
	else
	{
		pszCommand = pstCommand->valuestring;
	}

    pstArgs = cJSON_GetObjectItemCaseSensitive(pstJson, "args");
    if (NULL != pstArgs)
    {
        pszArgsBody = cJSON_Print(pstArgs);
    }

	if (NULL != strcasestr(pszCommand, "gpsData"))
	{
        s32Ret = JSON_HDL_UserGPSData(pszArgsBody, pOut);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "JSON_HDL_UserGPSData failed!\n");
        }
	}
	else
	{
        print_level(SV_WARN, "not support cmd: %s\n", pszCommand);
	}

    if (NULL != pszArgsBody)
        free(pszArgsBody);
    if (NULL != pstJson)
        cJSON_Delete(pstJson);
    free(pData);
    return NULL;
}

static void *http_thread_HandleRequest(void *pvArg)
{
    sint32 s32Ret = 0;
	sint32 s32Size = 0;
    sint32 s32ClientFd = -1;
    uint32 u32RecvSize = 0;
    uint32 u32ResSize = 0;
    char szHeader[2048] = {0};
	fd_set read_fds;
	char *pszTmp = NULL;
	char szJsonBody[10*1024];
	char szFilePath[512];
	struct timeval stTimeval = {0};
    HTTP_HEADER_S stHttpHeader = {0};
	sint32 s32HeadSize;
    MG_COM_INFO_S *pstMgInfo = (MG_COM_INFO_S *)pvArg;
    struct mg_connection *nc = pstMgInfo->nc;
    if (NULL == nc)
    {
        print_level(SV_ERROR, "get null pointer.\n");
        return;
    }
    s32ClientFd = MG_FD(nc);
    uint8 *pu8Buf = NULL;
    pu8Buf = (uint8 *)malloc(HTTP_BUF_SIZE);
    if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc memory for http request failed.\n");
        goto exit;
    }
    memset(pu8Buf, 0x0, HTTP_BUF_SIZE);
    memcpy(pu8Buf, pstMgInfo->szHead, pstMgInfo->s32HeadLen);
    u32RecvSize = pstMgInfo->s32HeadLen;
    if (u32RecvSize+pstMgInfo->s32BodyLen < HTTP_BUF_SIZE)
    {
        memcpy(pu8Buf+u32RecvSize, pstMgInfo->szBody, pstMgInfo->s32BodyLen);
        u32RecvSize += pstMgInfo->s32BodyLen;
    }
    s32HeadSize = (u32RecvSize < 2048) ? u32RecvSize : 2048;
    memcpy(szHeader, pu8Buf, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader((uint8*)szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
	    goto exit;
    }
    print_level(SV_INFO, "%s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);

    if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "export_FaceID"))
    {
        s32Ret = http_ExportFaceId(nc, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_ExportFaceId failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "export_config"))
    {
        s32Ret = http_ExportConfig(nc, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_ExportConfig failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "export_logfile"))
    {
        s32Ret = http_ExportLog(nc, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_ExportLog failed.\n");
            goto exit;
        }
    }
    else if (0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != stHttpHeader.pszUrl &&
        (NULL != strcasestr(stHttpHeader.pszUrl, ".mp4") || NULL != strcasestr(stHttpHeader.pszUrl, ".avi") || NULL != strcasestr(stHttpHeader.pszUrl, ".jpg")))
    {
        s32ClientFd = MG_FD(nc);
        print_level(SV_INFO, "try to export video file: %s, get lock...\n", stHttpHeader.pszUrl);
        sem_wait(&m_stHttpInfo.semDownFile);
        print_level(SV_DEBUG, "video file: %s lock success, send file now!\n", stHttpHeader.pszUrl);
        s32Ret = HTTP_HDL_ReplyByMultipartFormFile(SV_TRUE, nc, s32ClientFd, pu8Buf, stHttpHeader.pszUrl);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "HTTP_HDL_ReplyByMultipartFormFile failed.\n");
            sem_post(&m_stHttpInfo.semDownFile);
            goto exit;
        }
        sem_post(&m_stHttpInfo.semDownFile);
        print_level(SV_INFO, "export video file: %s success.\n", stHttpHeader.pszUrl);
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "query_log"))
    {
        print_level(SV_INFO, "get query log id: %d\n", nc->id);
        s32Ret = HTTP_HDL_QueryLog(SV_TRUE, pu8Buf, u32RecvSize, pu8Buf, &u32ResSize);
        if (SV_SUCCESS == s32Ret)
        {
            HTTP_HDL_ReplyByFile(SV_TRUE, nc, s32ClientFd, "/var/log.json", -1, -1);
            remove("/var/log.json");
        }
        else if (s32Ret > 0)
        {
            s32Ret = mg_http_send(SV_TRUE, nc, s32ClientFd, pu8Buf, u32ResSize);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "send to client failed. [err=%#x]\n", errno);
            }
        }
        else
        {
            print_level(SV_ERROR, "HTTP_HDL_QueryLog failed. [err=%#x]\n", s32Ret);
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "query_record"))
    {
        s32Ret = HTTP_HDL_QueryRecord(SV_TRUE, pu8Buf, u32RecvSize, pu8Buf, &u32ResSize);
        if (SV_SUCCESS == s32Ret)
        {
            HTTP_HDL_ReplyByFile(SV_TRUE, nc, s32ClientFd, "/var/record.json", -1, -1);
            remove("/var/record.json");
        }
        else if (s32Ret > 0)
        {
            s32Ret = send(s32ClientFd, pu8Buf, u32ResSize, 0);
            if (s32Ret < 0)
            {
                print_level(SV_ERROR, "send to client failed. [err=%#x]\n", errno);
            }
        }
        else
        {
            print_level(SV_ERROR, "HTTP_HDL_QueryRecord failed. [err=%#x]\n", s32Ret);
            goto exit;
        }
    }
    else
    {
        s32Ret = HTTP_HDL_MsgHandle(http_IsSidValid(pstMgInfo->pszSid), pu8Buf, u32RecvSize, pu8Buf);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "HTTP_HDL_MsgHandle failed. [err=%#x]\n", s32Ret);
            goto exit;
        }
        else
        {
        	if (0 == strcmp(stHttpHeader.pszMethod, "POST") && 0 == strcmp(stHttpHeader.pszUrl, "/config"))
        	{
        	    char szLog[1024];
        	    sprintf(szLog, "POST %s %s", stHttpHeader.pszUrl, stHttpHeader.pu8Body);
        	    sendHttpLog(s32ClientFd, szLog);

        	    if (m_stHttpInfo.bWriting)
                {
                    print_level(SV_ERROR, "it is writing frimware now.\n");
                    HTTP_HDL_GenerateErrorReply(MSG_DEVICE_BUSY, pu8Buf);
                    mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
                    goto exit;
                }
        	}
        }
        print_level(SV_DEBUG, "reply: \n%s\n", (char *)pu8Buf);
        mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    }

exit:
    if (NULL != pu8Buf)
    {
        free(pu8Buf);
        pu8Buf = NULL;
    }
    if (NULL != pstMgInfo)
    {
        free(pstMgInfo);
        pstMgInfo = NULL;
    }
    return NULL;
}

sint32 http_mg_HandleRequest(struct mg_connection *nc, struct mg_http_message *hm)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32ClientFd = -1;
	sint32 s32Fd = 0;
    uint32 u32RecvSize = 0;
    uint32 u32ResSize = 0;
    SV_BOOL bSnap = SV_FALSE;
    SV_BOOL bWarnSnap = SV_FALSE;
    SV_BOOL bRecSnap = SV_FALSE;
    SV_BOOL bFileCap = SV_FALSE;
    SV_BOOL bFactory = SV_FALSE;
    char szCmd[128] = {0};
    char szHeader[2048] = {0};
    char szFilePath[512];
	sint32 s32HeadSize;
	char *pszSid = NULL;
    char *pszTmp = NULL;
    char *pszETag = NULL;
    HTTP_HEADER_S stHttpHeader = {0};
    static struct mg_http_serve_opts s_stSerOpts;
    s_stSerOpts.root_dir = "";
    PLAYBACK_INFO_S stPlaybackInfo = {0};
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};

    if (NULL ==  nc || NULL == hm)
    {
        print_level(SV_ERROR, "null pointer.\n");
        return SV_FAILURE;
    }

    s32ClientFd = MG_FD(nc);
    uint8 *pu8Buf = NULL;
    pu8Buf = (uint8 *)malloc(HTTP_BUF_SIZE);
    if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc memory for http request failed.\n");
        goto exit;
    }
    memset(pu8Buf, 0x0, HTTP_BUF_SIZE);


    memcpy(pu8Buf, hm->head.ptr, hm->head.len);
    u32RecvSize = hm->head.len;
    if (u32RecvSize+hm->body.len < HTTP_BUF_SIZE)
    {
        memcpy(pu8Buf+u32RecvSize, hm->body.ptr, hm->body.len);
        u32RecvSize += hm->body.len;
    }

     s32HeadSize = (u32RecvSize < 2048) ? u32RecvSize : 2048;
    memcpy(szHeader, pu8Buf, s32HeadSize);
    szHeader[2047] = '\0';
    s32Ret = HTTP_HDL_ParseHeader((uint8*)szHeader, s32HeadSize, &stHttpHeader);
    if (SV_SUCCESS != s32Ret)
    {
    	print_level(SV_ERROR, "HTTP_HDL_ParseHeader failed. [err=%#x]\n", s32Ret);
    }
    //print_level(SV_INFO, "%s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);

	if (NULL != stHttpHeader.pszCookie)
	{
		pszSid = strstr(stHttpHeader.pszCookie, "sid=");		// 找到"sid="在pszCookie中首次出现的地址
		if (NULL != pszSid)
		{
			pszSid += strlen("sid=");
			*(pszSid+36) = '\0';
		}
	}

    /*******************************************************************************************
    * 网页显示目录内容, root_dir+uri为寻找的路径, 此处直接使用uri中填写的路径，root_dir不赋值
    * 仅sdcard目录对客户开放, 使用密码可以看到除/root外的其他路径，如/MONGOOSE_FILE_PASSWD/etc
    *******************************************************************************************/
    if (NULL != stHttpHeader.pszUrl && NULL == strcasestr(stHttpHeader.pszUrl, "/root") &&
        (NULL != strcasestr(stHttpHeader.pszUrl, MONGOOSE_FILE_PASSWD)
        || NULL != strcasestr(stHttpHeader.pszUrl, "/sdcard")
        || NULL != strcasestr(stHttpHeader.pszUrl, "/mnt/udisk")
#if defined(BOARD_ADA47V1)
        || NULL != strcasestr(stHttpHeader.pszUrl, "/userdata")
#endif
        ) &&
        (NULL != stHttpHeader.pszMethod  &&  0 == strcmp(stHttpHeader.pszMethod, "GET"))) //下载视频/图片的url 是post,目录查询的url 是get
    {
		char szPath[256] = {0};
		sint32 s32PathLen = (int) hm->uri.len;
        strncpy(szPath, hm->uri.ptr, s32PathLen);

#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(DMS31SDK) || defined(BOARD_ADA32V2) || defined(ADA32SDK))
        memset(&stRetPkt, 0, sizeof(stRetPkt));
        stRetPkt.pu8Data = (uint8 *)&stPlaybackInfo;
        s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_PLAYABCK, NULL, &stRetPkt, sizeof(PLAYBACK_INFO_S));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_GET_DEVINFO failed. [err=%#x]\n", s32Ret);
            return MSG_DEFAULT_FAIL;
        }

#if defined(BOARD_DMS31V2)
        char *pszDir[3] = {"/tmp/sdcard", "/mnt/sdcard", "/mnt/udisk"};
        sint32 s32Index = -1;
        char *tmp = NULL;

        if ((NULL == strstr(szPath, ".mp4") && NULL == strstr(szPath, ".avi") && NULL == strstr(szPath, ".jpg"))
            || NULL == strstr(szPath, pszDir[0]))
        {
            if (stPlaybackInfo.bStart)
                goto check_playback;
            else
                goto server_dir;
        }

        if (COMMON_IsPathExist(szPath))
        {
            goto check_playback;
        }

        for (i = 0; i < 3; i++)
        {
            tmp = strstr(hm->uri.ptr, pszDir[i]);
            if (NULL != tmp)
            {
                s32Index = i;
                break;
            }
        }

        for (i = s32Index+1; i < 3; i++)
        {
            strcpy(szPath, pszDir[i]);
            strncat(szPath, tmp+strlen(pszDir[s32Index]), s32PathLen-strlen(pszDir[s32Index]));

            if (COMMON_IsPathExist(szPath))
            {
                print_level(SV_INFO, "change file to read: %s\n", szPath);
                memset(hm->uri.ptr, 0, s32PathLen);
                memcpy(hm->uri.ptr, szPath, strlen(szPath));
                hm->uri.len = strlen(szPath);
                break;
            }
        }
#endif

check_playback:
        char *pszTrueName = NULL;

        strncpy(szPath, hm->uri.ptr, s32PathLen);
        if (!stPlaybackInfo.bStart || (NULL == strstr(szPath, ".mp4") && NULL == strstr(szPath, ".avi")))
        {
            goto server_dir;
		}
        nc->bPlayback = stPlaybackInfo.bStart;

        pszTrueName = strstr(szPath, MONGOOSE_FILE_PASSWD);
        if (NULL != pszTrueName)
        {
            strcpy(szPath, pszTrueName+strlen(MONGOOSE_FILE_PASSWD));
        }

        stPlaybackInfo.bLoopPlay = SV_FALSE;
        stPlaybackInfo.bBootstart = SV_FALSE;
        strcpy(stPlaybackInfo.szPlaybackFile, szPath);
        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
        stMsgPkt.stMsg.u16OpCode = OP_EVENT_CTRL_PLAYBACK;
        stMsgPkt.pu8Data = (uint8 *)&stPlaybackInfo;
        stMsgPkt.u32Size = sizeof(stPlaybackInfo);
        s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_CTRL_PLAYBACK, &stMsgPkt);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
#endif

server_dir:
        mg_http_serve_dir(nc, hm, &s_stSerOpts);
    }
    /*else if (NULL != stHttpHeader.pszUrl && 0 == strcmp(stHttpHeader.pszUrl, "/"))
    {
        s_stSerOpts.root_dir = "/var/webui";
        s_stSerOpts.ssi_pattern = MG_HTTP_INDEX;
        mg_http_serve_dir(nc, hm, &s_stSerOpts);
    }*/
    else if (NULL != stHttpHeader.pszUrl && NULL != strcasestr(stHttpHeader.pszUrl, "jpeg?quality")) // http/https 传图
    {
        uint32 s32Quality = 3;
        char *pstTmp = strcasestr(stHttpHeader.pszUrl, "quality=");
        if (NULL != pstTmp)
            s32Quality = atoi(pstTmp+strlen("quality="));
		if(NULL != strcasestr(stHttpHeader.pszHost,"8080"))
			HTTP_HDL_NewJpeg(s32Quality);
		else
			HTTP_HDL_QuickJpeg(s32Quality);
        http_mg_replySnap(nc);
        m_stHttpInfo.s32QuickHandleWtd = 10;
    }
    else if ((NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "GET")) &&
        (0 == strcmp(stHttpHeader.pszUrl, "/") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".html") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".htm") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".css") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".js") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".jpg") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".jpeg") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".png") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".gif") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".mp4") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".flv") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".swf") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".map") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".svg") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".ttf") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".woff") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".woff2") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".eot") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".vtt") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".txt") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".cap") ||
        NULL != strcasestr(stHttpHeader.pszUrl, ".ico")))
    {
        if (0 != access(HTTP_RESOURCE_PATH, F_OK))
	    {
	    	print_level(SV_WARN, "webui not found! Trying to uncompress one.\n");
#if (defined(BOARD_IPCR20S3))
            if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
            {
                print_level(SV_ERROR, "Disable FlashProtection failed.\n");
            }

            s32Ret = SAFE_System("tar -zxf /root/webui.tar.gz -C /root/", 3*NORMAL_WAIT_TIME);
	        if (0 != s32Ret)
	        {
	            print_level(SV_ERROR, "szCmd: tar -zxf /root/webui.tar.gz -C /root/ failed. [err=%d]\n", s32Ret);
	        }

	        if (SV_SUCCESS != CONFIG_FlashProtection(SV_TRUE))
            {
                print_level(SV_ERROR, "Enable FlashProtection failed.\n");
            }
#else
	        s32Ret = SAFE_System("tar -zxf /root/webui.tar.gz -C /var/", NORMAL_WAIT_TIME);
	        if (0 != s32Ret)
	        {
	            print_level(SV_ERROR, "szCmd: tar -zxf /root/webui.tar.gz -C /var/ failed. [err=%d]\n", s32Ret);
	        }
#endif
	    }

		if (NULL != strcasestr(stHttpHeader.pszUrl, "snap0.jpeg"))
		{
			bSnap = SV_TRUE;
		}
        else if (NULL != strcasestr(stHttpHeader.pszUrl, WARN_SANP_PATH))
        {
            bWarnSnap = SV_TRUE;
        }
        else if (NULL != strcasestr(stHttpHeader.pszUrl, "picture"))
        {
            bRecSnap = SV_TRUE;
        }
        else if (NULL != strcasestr(stHttpHeader.pszUrl, "file.cap"))
        {
            strcpy(szFilePath, "/tmp");
            bFileCap = SV_TRUE;
        }
        else
        {
            s_stSerOpts.root_dir = HTTP_RESOURCE_PATH;
            strcpy(szFilePath, HTTP_RESOURCE_PATH);
        }


        // 没有指定URL默认打开index.html
        if (0 == strcmp(stHttpHeader.pszUrl, "/"))
	    {
	        strcat(szFilePath, "/index.html");
			//s_stSerOpts.ssi_pattern = MG_HTTP_INDEX;
	    }
        else
	    {
            if (bWarnSnap || bRecSnap)
            {
                strcpy(szFilePath, stHttpHeader.pszUrl);
            }
            else
            {
    	    	strcat(szFilePath, stHttpHeader.pszUrl);
    	        pszTmp = strstr(szFilePath, "?");
    	        if (NULL != pszTmp)
    	        {
    	            *pszTmp = '\0';
    	        }
            }
	    }

	    if (NULL != stHttpHeader.pszIfNotMatch)
		{
			pszETag = stHttpHeader.pszIfNotMatch + 1;
			*(pszETag+strlen(pszETag)-1) = '\0';
		}

        if (!http_IsSidValid(pszSid)
            && NULL == strcasestr(stHttpHeader.pszUrl, "login.html")
		    && NULL == strcasestr(stHttpHeader.pszUrl, ".css")
            && NULL == strcasestr(stHttpHeader.pszUrl, ".js")
            && NULL == strcasestr(stHttpHeader.pszUrl, ".png")
            && NULL == strcasestr(stHttpHeader.pszUrl, "jquery")
            && NULL == strcasestr(stHttpHeader.pszUrl, "ajax")
            && NULL == strcasestr(stHttpHeader.pszUrl, "favicon.ico")
            && NULL == strcasestr(stHttpHeader.pszUrl, "imudata.txt")
            && NULL == strcasestr(stHttpHeader.pszUrl, "font")
            && NULL == strcasestr(stHttpHeader.pszUrl, "logo")
            && NULL == strcasestr(stHttpHeader.pszUrl, "snap0.jpeg")
            && NULL == strcasestr(stHttpHeader.pszUrl, WARN_SANP_PATH)
            && NULL == strcasestr(stHttpHeader.pszUrl, ".mp4")
            && NULL == strcasestr(stHttpHeader.pszUrl, ".jpg")
            && NULL == strcasestr(stHttpHeader.pszUrl,"no_password.html"))
        {
            char loginhtml[512];
            print_level(SV_WARN, "unauthorized request: %s %s\n", stHttpHeader.pszMethod, stHttpHeader.pszUrl);
            s_stSerOpts.ssi_pattern = "login.html";

            strcpy(loginhtml, s_stSerOpts.root_dir);
            strcat(loginhtml, s_stSerOpts.ssi_pattern);
            mg_http_serve_file(nc, hm, loginhtml, &s_stSerOpts);
            //mg_http_serve_dir(nc, hm, &s_stSerOpts);
        }
        else if (http_IsSidValid(pszSid) && NULL != strcasestr(stHttpHeader.pszUrl, "login.html"))
        {
             s_stSerOpts.ssi_pattern = "index.html";
			 mg_http_serve_dir(nc, hm, &s_stSerOpts);
        }
        else if (NULL != pszETag && HTTP_HDL_IsMatchETag(szFilePath, pszETag))
        {
        	sprintf((char *)pu8Buf, "HTTP/1.1 304 Not Modified\r\n"
                                    "ETag: \"%s\"\r\n\r\n", pszETag);
            mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
        }
        else
        {
			if (bSnap)
            {
                http_mg_replySnap(nc);
            }
			else if(bWarnSnap || bRecSnap || bFileCap)
			{
				mg_http_serve_file(nc, hm, szFilePath, &s_stSerOpts);
			}
            else
            {
                mg_http_serve_dir(nc, hm, &s_stSerOpts);
            }

            #if 0
            if (bWarnSnap)
            {
                if (!COMMON_IsPathExist(szFilePath))
                {
                    print_level(SV_WARN, "file: %s is not exist!\n", szFilePath);
                }
                else
                {
                    print_level(SV_INFO, "remove file: %s\n", szFilePath);
                    remove(szFilePath);
                }
            }
            #endif
        }

    }
	else if (mg_http_match_uri(hm, "/ws"))  			// ws/wss 传图和报警信息
    {
        print_level(SV_INFO, "websocket upgrade\n");
        mg_ws_upgrade(nc, hm, NULL);
    }
    else if (mg_http_match_uri(hm, "/mjpeg"))  			// mjpeg流
    {
        print_level(SV_INFO, "mjpeg stream\n");
        m_stHttpInfo.u32MJpegClients ++;
        http_start_thread(http_mg_handleMJpeg, (void *) nc);  // Start thread
    }
	else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "login"))
    {
        HTTP_SESSION_S stSession;
        static time_t tLastLoginTime = 0;
        time_t tNow = time(NULL);

        print_level(SV_DEBUG, "%s\n", pu8Buf);
        if (http_IsSessionFull() || tLastLoginTime == tNow)
        {
            snprintf((char *)pu8Buf, HTTP_BUF_SIZE - 1, "HTTP/1.1 503 Service Unavailable \r\n"
                            						    "Content-Type: text/plain \r\n"
                            						    "Content-Length: 0 \r\n\r\n");
        }
        else
        {
            tLastLoginTime = tNow;
            s32Ret = HTTP_HDL_Authentication(pu8Buf, u32RecvSize, pu8Buf, &stSession);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "HTTP_HDL_Authentication failed. [err=%#x]\n", s32Ret);
            }
            else
            {
                http_AddSession(&stSession);
                sendHttpLog(s32ClientFd, "login");
            }
        }
        print_level(SV_DEBUG, "%s\n", pu8Buf);
        mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST")  && NULL != strcasestr(stHttpHeader.pszUrl, "logout"))
    {
        if (!http_IsSidValid(pszSid) || SV_SUCCESS != http_DelSession(pszSid))
        {
            print_level(SV_ERROR, "logout error!\n");
            snprintf((char *)pu8Buf, HTTP_BUF_SIZE - 1, "HTTP/1.1 500 Internal Server Error \r\n"
                            							"Content-Type: application/json; charset=UTF-8 \r\n"
                            							"Content-Length: 2 \r\n\r\n{}");
        }
        else
        {
            print_level(SV_INFO, "logout successful!\n");
            sendHttpLog(s32ClientFd, "logout");
            snprintf((char *)pu8Buf, HTTP_BUF_SIZE - 1, "HTTP/1.1 200 OK \r\n"
                            							"Content-Type: application/json; charset=UTF-8 \r\n"
                            							"Content-Length: 2 \r\n\r\n{}");
        }
        mg_http_send(SV_TRUE, nc, 0, pu8Buf, strlen((char *)pu8Buf));
    }
    /* multipart数据接收暂时使用单线程阻塞回调接收方式 */
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "firmware"))
    {
        s32Ret = http_Hdl_Firmware(nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_Hdl_Firmware failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "upgrade"))
    {
        s32Ret = http_Hdl_Upgrade(nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_Hdl_Upgrade failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "import_userpic"))
    {
        s32Ret = http_ImportUserPic(nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_ImportUserPic failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "import_FaceID"))
    {
        s32Ret = http_ImportFaceId(nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_ImportFaceId failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "import_config"))
    {
        bFactory = stHttpHeader.bFactory;

        s32Ret = http_ImportConfig(bFactory, nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_ImportConfig failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "import_key"))
    {
        s32Ret = http_ImportKey(nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_ImportKey failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "run_factory"))
    {
        s32Ret = http_Hdl_Factory(nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_Hdl_Factory failed.\n");
            goto exit;
        }
    }
    else if (NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
        0 == strcmp(stHttpHeader.pszMethod, "POST") && NULL != strcasestr(stHttpHeader.pszUrl, "addPlug"))
    {
        s32Ret = http_Hdl_AddPlug(nc, hm, pu8Buf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_Hdl_AddPlug failed.\n");
            goto exit;
        }
    }
	else if(NULL != stHttpHeader.pszMethod && NULL != stHttpHeader.pszUrl &&
		 0 == strcmp(stHttpHeader.pszMethod,"POST") && NULL != strcasestr(stHttpHeader.pszUrl,"updatePlug"))
	{
		s32Ret = http_Hdl_UpdatePlug(nc,hm,pu8Buf);
		if(SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR,"http_Hdl_UpdatePlug failed.\n");
			goto exit;
		}
	}
    else
    {
        /* 其他非multipart数据接收的url使用多线程处理 */
        MG_COM_INFO_S *pstMgComInfo = (MG_COM_INFO_S *)malloc(sizeof(MG_COM_INFO_S));
        pstMgComInfo->nc = nc;
        pstMgComInfo->s32HeadLen = hm->head.len;
        pstMgComInfo->s32BodyLen = hm->body.len;
        pstMgComInfo->s32ChunkLen = hm->chunk.len;
        memset(pstMgComInfo->pszSid, 0, MG_IO_SIZE);
        if (NULL != pszSid)
        {
		    memcpy(pstMgComInfo->pszSid, pszSid, strlen(pszSid));
        }
        memset(pstMgComInfo->szHead, 0, MG_IO_SIZE);
        memcpy(pstMgComInfo->szHead, hm->head.ptr, (hm->head.len < MG_IO_SIZE) ? hm->head.len : MG_IO_SIZE);
        memset(pstMgComInfo->szBody, 0, MG_IO_SIZE);
        memcpy(pstMgComInfo->szBody, hm->body.ptr, (hm->body.len < MG_IO_SIZE) ? hm->body.len : MG_IO_SIZE);
        memset(pstMgComInfo->szChunk, 0, MG_IO_SIZE);
        memcpy(pstMgComInfo->szChunk, hm->chunk.ptr, (hm->chunk.len < MG_IO_SIZE) ? hm->chunk.len : MG_IO_SIZE);
        http_start_thread(http_thread_HandleRequest, (void *) pstMgComInfo);
    }

exit:
    if (pu8Buf)
    {
        free(pu8Buf);
        pu8Buf = NULL;
    }

    return SV_SUCCESS;
}

static void http_mg_handler(struct mg_connection *nc, int ev, void *ev_data, void *fn_data)
{
    sint32 s32Ret = 0, i = 0;
    pthread_attr_t 	attr;
    pthread_t thread;
    SV_BOOL bSnap = SV_FALSE;
    SV_BOOL bWarnSnap = SV_FALSE;
    SV_BOOL bRecSnap = SV_FALSE;
    char szHeader[2048];
    HTTP_HEADER_S stHttpHeader = {0};

    char szMethod[128] = {0};
    char szUrl[128] = {0};
    struct mg_http_message *hm = (struct mg_http_message *) ev_data;
    struct mg_ws_message *wm = (struct mg_ws_message *) ev_data;
    static struct mg_tls_opts s_stTlsOpts = {0};

    switch (ev)
    {
        case MG_EV_POLL:
            break;

        case MG_EV_CONNECT:
            print_level(SV_INFO, "MG_EV_CONNECT\n");
            break;

        case MG_EV_ACCEPT:
            if (NULL != fn_data)
            {
                s_stTlsOpts.cert = CONFIG_HTTPS_CRT;
                s_stTlsOpts.certkey = CONFIG_HTTPS_KEY;
                mg_tls_init(nc, &s_stTlsOpts);
            }
            break;

        case MG_EV_CLOSE:
            if(is_websocket(nc) && nc->label[0]=='A')
            {

                print_level(SV_INFO, "close get_warning connection \n");
                pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
               // m_stWebSocket.get_warning_nc = NULL;
                pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
            }
			if(is_websocket(nc) && nc->label[0]=='J')
			{
				print_level(SV_INFO,"close get_view connection.\n");
		        pthread_mutex_lock(&m_stHttpInfo.mutexLock);
		        if (m_stHttpInfo.u32QuickHandleNum > 0)
		        {
		            m_stHttpInfo.u32QuickHandleNum--;
		        }
		        pthread_mutex_unlock(&m_stHttpInfo.mutexLock);
			}
			if(is_websocket(nc) && nc->label[0]=='P')
            {

                print_level(SV_INFO, "close get_position connection \n");
                pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
               // m_stWebSocket.get_warning_nc = NULL;
                pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
            }
            break;

        case MG_EV_HTTP_MSG:
            //print_level(SV_DEBUG, "http_mg_HandleRequest.\n");
			s32Ret = http_mg_HandleRequest(nc, hm);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "http_mg_HandleRequest failed.\n");
            }
            break;

        case MG_EV_HTTP_CHUNK:
            sprintf(szMethod, "%.*s", (int) hm->method.len, hm->method.ptr);
            sprintf(szUrl, "%.*s", (int) hm->uri.len, hm->uri.ptr);
            if (0 == strcmp(szMethod, "POST") && (NULL != strcasestr(szUrl, "firmware") || NULL != strcasestr(szUrl, "upgrade") || NULL != strcasestr(szUrl, "addPlug")|| NULL != strcasestr(szUrl, "updatePlug")))
            {
                s32Ret = http_mg_HandleRequest(nc, hm);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "http_mg_HandleRequest failed.\n");
                }
            }
            break;

        case MG_EV_WS_OPEN:
            print_level(SV_INFO, "websocket connect\n");
			break;

        case MG_EV_WS_MSG:
            //print_level(SV_DEBUG,"websocket frame %s flag %d\n", wm->data.ptr, wm->flags);
			if (0 == memcmp("get_warning", wm->data.ptr, wm->data.len))
            {
                pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
                //m_stWebSocket.get_warning_nc = nc;
                pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
				nc->label[0]='A';
                m_stWebSocket.bFirstConnect = SV_TRUE;
                print_level(SV_DEBUG,"start get warning\n");
            }
			else if (0 == memcmp("get_view", wm->data.ptr, wm->data.len))
            {
                print_level(SV_DEBUG,"start get view.\n");
		        pthread_mutex_lock(&m_stHttpInfo.mutexLock);
		        if (m_stHttpInfo.u32QuickHandleNum > HTTP_MAX_QUICK_THR)
		        {
		            print_level(SV_WARN, "current handle quick thread is full num:%d\n",m_stHttpInfo.u32QuickHandleNum);
		            pthread_mutex_unlock(&m_stHttpInfo.mutexLock);
		            close(MG_FD(nc));
		            break;
		        }
		        m_stHttpInfo.u32QuickHandleNum++;
		        pthread_mutex_unlock(&m_stHttpInfo.mutexLock);
				HTTP_HDL_QuickJpeg(3);
				nc->label[0]='J';
				http_start_thread(mg_ws_replySnap, (void *) nc);  // Start thread
                break;
            }
            else if (0 == memcmp("close", wm->data.ptr, 5))
            {
                nc->is_closing = 1;
            }
#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA32N1))
            else if (NULL != strstr(wm->data.ptr, "set_totalNum=")
                    || NULL != strstr(wm->data.ptr, "set_InNum=")
                    || NULL != strstr(wm->data.ptr, "set_OutNum="))
            {
                char *pcTmp = NULL;
                MSG_PACKET_S stMsgPkt = {0};
                MSG_APC_NUM_S stApcNum = {-1, -1, -1};

                pcTmp = strstr(wm->data.ptr, "set_totalNum=");
                if (NULL != pcTmp)
                {
                    stApcNum.s32TotalNum = atoi(pcTmp+13);
                    print_level(SV_DEBUG, "set_totalNum=%d\n", stApcNum.s32TotalNum);
                }

                pcTmp = strstr(wm->data.ptr, "set_InNum=");
                if (NULL != pcTmp)
                {
                    stApcNum.s32InNum = atoi(pcTmp+10);
                    print_level(SV_DEBUG, "set_InNum=%d\n", stApcNum.s32InNum);
                }

                pcTmp = strstr(wm->data.ptr, "set_OutNum=");
                if (NULL != pcTmp)
                {
                    stApcNum.s32OutNum = atoi(pcTmp+11);
                    print_level(SV_DEBUG, "set_OutNum=%d\n", stApcNum.s32OutNum);
                }

                stApcNum.s32TotalNum = (stApcNum.s32TotalNum < 999) ? stApcNum.s32TotalNum : 999;
                stMsgPkt.pu8Data = (uint8*)&stApcNum;
                stMsgPkt.u32Size = sizeof(stApcNum);
                s32Ret = Msg_submitEvent(EP_ALG, OP_EVENT_SET_APC_NUM, &stMsgPkt);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
                }
            }
#endif
#if (defined(BOARD_ADA32V4) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1))
            else if (0 == memcmp("get_position", wm->data.ptr, 12))
            {
                pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
                //m_stWebSocket.get_warning_nc = nc;
                pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
				nc->label[0]='P';
                print_level(SV_DEBUG,"start get position\n");
            }
#endif
            else if (0 == memcmp("ping", wm->data.ptr, wm->data.len))
            {
                pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
                mg_ws_send(nc, "pong", strlen("pong"), WEBSOCKET_OP_TEXT);
                pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
            }
            else if (NULL != strstr(wm->data.ptr, "\"cmd\""))
            {
                pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
                char *pData = (char *)malloc(wm->data.len+1);
                if (NULL == pData)
                {
                    print_level(SV_ERROR, "Failed to allocate memory for command data.\n");
                }
                else
                {
                    memcpy(pData, wm->data.ptr, wm->data.len);
                    pData[wm->data.len] = '\0';
                    http_start_thread(http_websocket_cmd_handler, (void *)pData);
                }
                pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
            }
            break;
        default:
            //print_level(SV_ERROR, "not support option.[%d]\n", ev);
            break;
    }

exit:
    (void) fn_data;
}

bool http_SortWarnSnapByTime(const ALARM_SNAP_INFO_S& a, const ALARM_SNAP_INFO_S& b)
{
    return a.s64TimeStamp > b.s64TimeStamp;
}

sint32 http_SortWarnSnap(char *pszNewSnapPath)
{
    sint32 s32Ret, i, j;
    char szCmd[128] = {0};
    char szTmp[64] = {0};
    char szFilePath[128] = {0};
    char szWarnPathList[10][64] = {0};
    uint32 u32FileCnt = 0, u32FileSum = 0;
    DIR *pstDir = NULL;
    struct dirent *pstDirent = NULL;
    struct stat stFileInfo = {0};
    ALARM_SNAP_INFO_S stAlarmSnapInfo = {0};
    WEBSOCKET_COM_INFO_S *pstWebSocketInfo = (WEBSOCKET_COM_INFO_S *)&m_stWebSocket;

    if (NULL == pszNewSnapPath)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        return SV_FAILURE;
    }

    if (!pstWebSocketInfo->listWarnSnapInfo.empty())
    {
        s32Ret = stat(pszNewSnapPath, &stFileInfo);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "stat file: %s failed!\n", pszNewSnapPath);
            return SV_FAILURE;
        }
        stAlarmSnapInfo.s64TimeStamp = (sint64)(stFileInfo.st_ctim.tv_sec*1000ll + stFileInfo.st_ctim.tv_nsec/1000000ll);
        strcpy(stAlarmSnapInfo.szWarnPath, pszNewSnapPath);
        pstWebSocketInfo->listWarnSnapInfo.push_front(stAlarmSnapInfo);
        goto check_cnt;
    }

    pstDir = opendir(WARN_SANP_PATH);
    if (NULL == pstDir)
    {
        return SV_FAILURE;
    }

    while (NULL != (pstDirent = readdir(pstDir)))
    {
        if (0 == strcmp(pstDirent->d_name, ".")
            || 0 == strcmp(pstDirent->d_name, ".."))
        {
            continue;
        }

        sprintf(szFilePath, "%s/%s", WARN_SANP_PATH, pstDirent->d_name);
        s32Ret = stat(szFilePath, &stFileInfo);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "stat file: %s failed!\n", szFilePath);
            continue;
        }
        stAlarmSnapInfo.s64TimeStamp = (sint64)(stFileInfo.st_ctim.tv_sec*1000ll + stFileInfo.st_ctim.tv_nsec/1000000ll);
        strcpy(stAlarmSnapInfo.szWarnPath, szFilePath);
        pstWebSocketInfo->listWarnSnapInfo.push_back(stAlarmSnapInfo);
    }
    closedir(pstDir);

    /* 通过时间戳进行列表重排序 */
    pstWebSocketInfo->listWarnSnapInfo.sort(http_SortWarnSnapByTime);

check_cnt:
    #if 0
    auto it = pstWebSocketInfo->listWarnSnapInfo.begin();
    for (i = 0; i < pstWebSocketInfo->listWarnSnapInfo.size(); i++)
    {
        print_level(SV_INFO, "s64TimeStamp: %lld, path: %s\n", (*it).s64TimeStamp, (*it).szWarnPath);
        it++;
    }
    #endif

    while (pstWebSocketInfo->listWarnSnapInfo.size() > WARN_SANP_MAX_CNT)
    {
        auto it = pstWebSocketInfo->listWarnSnapInfo.begin();
        std::advance(it, WARN_SANP_MAX_CNT);

        std::list<ALARM_SNAP_INFO_S>::iterator listSanpInfo_itor;
        for (listSanpInfo_itor = it; listSanpInfo_itor != pstWebSocketInfo->listWarnSnapInfo.end(); )
        {
            ALARM_SNAP_INFO_S stSnapInfo = *listSanpInfo_itor;
            if (0 != strlen(stSnapInfo.szWarnPath) && COMMON_IsPathExist(stSnapInfo.szWarnPath))
            {
                print_level(SV_INFO, "file cnt is larger than %d, remove file: %s\n", WARN_SANP_MAX_CNT, stSnapInfo.szWarnPath);
                remove(stSnapInfo.szWarnPath);
            }
            listSanpInfo_itor = pstWebSocketInfo->listWarnSnapInfo.erase(listSanpInfo_itor);
        }
    }

    return SV_SUCCESS;
}

void http_WsInfo_clear(WEBSOCKET_ALARM_INFO_S *pstWsAlarmInfo)
{
    if (NULL == pstWsAlarmInfo)
    {
        return;
    }

    memset(pstWsAlarmInfo, 0, sizeof(WEBSOCKET_ALARM_INFO_S));
    pstWsAlarmInfo->s32DMMWarnState = -1;
    pstWsAlarmInfo->s32PDWarnState = -1;
    pstWsAlarmInfo->s32APCWarnState = -1;
}

void *mongoose_server_Body(void *pvArg)
{
	sint32 s32Ret, i;
	WEBSOCKET_COM_INFO_S *pstWebSocketInfo = (WEBSOCKET_COM_INFO_S *)pvArg;
    WEBSOCKET_ALARM_INFO_S *pstWsAlarmInfo = &pstWebSocketInfo->stWsAlarmInfo;
	WEBSOCKET_ALARM_INFO_S *pstWsAlarmSending = NULL;
    struct mg_mgr mgr;
    struct mg_connection *nc;
	struct mg_connection *tmp_c;
    SV_BOOL bSendOff = SV_TRUE;
    struct timespec lastTime = {0, 0};
    struct timespec currentTime = {0, 0};
    char str[128]={0};
    signal(SIGTERM, signal_handler);
	setvbuf(stdout, NULL, _IOLBF, 0);
	setvbuf(stderr, NULL, _IOLBF, 0);
    m_stHttpInfo.s32QuickHandleWtd = -1;
    DUMP_APC_S stDumpApc = {0};
    uint8 u8ApcWarn[64] = {0};
    char *pszWarnText = NULL;
    sint32 s32ApcAlarm = -1;
    cJSON *pstJson = NULL;
    char *alarmJson = NULL;
    char szConfidence[12] = {0};

    static const char *s_http_addr = "http://0.0.0.0:8080";                 /* HTTP server port */
	static const char *s_http_quickjpeg_addr = "http://0.0.0.0:8081";       /* HTTP jepg port */
    static const char *s_http_mjpeg_addr = "http://0.0.0.0:8084";           /* HTTP mjepg port */
    static const char *s_https_addr = "https://0.0.0.0:8443";               /* HTTPS server port */
	static const char *s_https_quickjpeg_addr = "https://0.0.0.0:8445";     /* HTTPS jpeg port */

    static const char *s_ws_addr = "ws://0.0.0.0:8085";                     /* ws alarm port */
	static const char *s_ws_quickjpeg_addr = "ws://0.0.0.0:8083";           /* ws jpeg port */
	static const char *s_wss_addr = "wss://0.0.0.0:8447";                   /* wss alarm port */
	static const char *s_wss_quickjpeg_addr = "wss://0.0.0.0:8448";         /* wss jpeg port */


    /* 生成密钥移到线程这里面来执行，在HTTP_SVR_Init里面执行会阻塞一段时间 */
    s32Ret = http_mg_GenerateKey();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_mg_GenerateKey failed.\n");
        //return s32Ret;
    }

    mg_log_set("2");
    mg_mgr_init(&mgr);
    mg_http_listen(&mgr, s_http_addr, http_mg_handler, NULL);                   /* http监听 */
    mg_http_listen(&mgr, s_http_quickjpeg_addr, http_mg_handler, NULL);         /* http 图片流监听 */

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_WE))
    {
        mg_http_listen(&mgr, s_http_mjpeg_addr, http_mg_handler, NULL);             /* http 图片流监听 */
    }

    mg_http_listen(&mgr, s_https_addr, http_mg_handler, (void *) 1);            /* https监听 */
    mg_http_listen(&mgr, s_https_quickjpeg_addr, http_mg_handler, (void *) 1);  /* https 图片流监听 */

    mg_http_listen(&mgr, s_ws_addr, http_mg_handler, NULL);                     /* websocket监听 */
    mg_http_listen(&mgr, s_ws_quickjpeg_addr, http_mg_handler, NULL);
    mg_http_listen(&mgr, s_wss_addr, http_mg_handler, (void *) 1);              /* websocket监听 */
    mg_http_listen(&mgr, s_wss_quickjpeg_addr, http_mg_handler, (void *) 1);

    pstWebSocketInfo->pmg_mgr = &mgr;
    http_WsInfo_clear(pstWsAlarmInfo);

    while(1)
    {
        mg_mgr_poll(&mgr, 500);             // Infinite event loop

        http_quickJpegTimer();

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA32N1))
#if (defined(BOARD_ADA32N1))
        if (BOARD_ADA32N1_V3 == BOARD_GetVersion())
#endif
        {
            if (pstWebSocketInfo->bFirstConnect)
            {
                memset(&stDumpApc, 0, sizeof(stDumpApc));
                s32Ret = dump_GetApcInfo(&stDumpApc);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "dump_GetApcInfo failed. [err=%#x]\n", s32Ret);
                }

                ALARM_GET_APC_EVENT(stDumpApc.s32CurAlarm, s32ApcAlarm, pszWarnText);

                snprintf(str, 128, "{\"Warning\":\"%s\",\"code\":%d,\"In\":%d,\"Out\":%d,\"Rect\":%d}", pszWarnText, s32ApcAlarm , stDumpApc.s32InNumber, \
                    stDumpApc.s32OutNumber, stDumpApc.s32RectNumber);
                pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
                for (tmp_c =  mgr.conns; tmp_c != NULL; tmp_c = tmp_c->next)
                {
                    if (tmp_c->label[0] != 'A')
                        continue;         // Skip non-stream connections
                    mg_ws_send(tmp_c, str, strlen(str), WEBSOCKET_OP_TEXT);
                }
                pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
                pstWebSocketInfo->bFirstConnect = SV_FALSE;
            }
        }
#endif

        if (pstWebSocketInfo->listSendingWsAlarmInfo.empty() && pstWsAlarmInfo->s32PDWarnState < 0 && bSendOff)
        {
            continue;
        }

push:
        clock_gettime(CLOCK_MONOTONIC, &currentTime);

        if (!pstWebSocketInfo->listSendingWsAlarmInfo.empty())
        {
            std::list<WEBSOCKET_ALARM_INFO_S>::iterator listSending_itor;

			pthread_mutex_lock(&pstWebSocketInfo->mutexWarnLock);
			listSending_itor = pstWebSocketInfo->listSendingWsAlarmInfo.begin();
			pstWsAlarmSending = &(*listSending_itor);
			pthread_mutex_unlock(&pstWebSocketInfo->mutexWarnLock);

			if (0 == strlen(pstWsAlarmSending->szWarnText) || 0 == strlen(pstWsAlarmSending->szWarnPath))
			{
				print_level(SV_ERROR, "get null pointer.\n");
				http_WsInfo_clear(pstWsAlarmSending);
				goto exit;
			}

			pstJson = cJSON_CreateObject();
			if (NULL == pstJson)
			{
				print_level(SV_ERROR, "cJSON_CreateObject failed!\n");
				http_WsInfo_clear(pstWsAlarmSending);
				goto exit;
			}

            if (BOARD_IsCustomer(BOARD_C_DMS31V2_111334))
            {
    			cJSON_AddItemToObject(pstJson, "serialNo", cJSON_CreateString(m_stHttpInfo.szSerialNum));
            }
			cJSON_AddItemToObject(pstJson, "Warning", cJSON_CreateString(pstWsAlarmSending->szWarnText));
			cJSON_AddItemToObject(pstJson, "code", cJSON_CreateNumber(pstWsAlarmSending->s32DMMWarnState));
			cJSON_AddItemToObject(pstJson, "timestamp", cJSON_CreateNumber(pstWsAlarmSending->s64TimeStamp));

            if (BOARD_IsCustomer(BOARD_C_DMS31V2_OPTALERT))
            {
                sprintf(szConfidence, "%f", pstWsAlarmSending->fDmsEventScore);
                cJSON_AddItemToObject(pstJson, "confidence", cJSON_CreateRaw(szConfidence));
            }

			if (0 != strlen(pstWsAlarmSending->szAlarmUuid))
			{
				cJSON_AddItemToObject(pstJson, "uuid", cJSON_CreateString(pstWsAlarmSending->szAlarmUuid));
			}
			if (0 != strlen(pstWsAlarmSending->szUsrName))
			{
				char szformattedID[4] = {0};
				sprintf(szformattedID, "%03u", pstWsAlarmSending->u16UsrId);
				cJSON_AddItemToObject(pstJson, "driverID", cJSON_CreateRaw(szformattedID));
				cJSON_AddItemToObject(pstJson, "driverName", cJSON_CreateString(pstWsAlarmSending->szUsrName));
			}
			if (0 != strlen(pstWsAlarmSending->szAccountName))
			{
                cJSON_AddItemToObject(pstJson, "driverAccountName", cJSON_CreateString(pstWsAlarmSending->szAccountName));
			}
			if (pstWsAlarmSending->stGpsInfo.bHadData)
			{
				char szSpeed[12] = {0};
				char szLat[12] = {0};
				char szLon[12] = {0};
				sprintf(szSpeed, "%lf", pstWsAlarmSending->stGpsInfo.dSpk);
				sprintf(szLat, "%lf", pstWsAlarmSending->stGpsInfo.dLatitude);
				sprintf(szLon, "%lf", pstWsAlarmSending->stGpsInfo.dLongitude);
				cJSON *pstJsonGps = cJSON_CreateObject();
				if (NULL == pstJsonGps)
				{
					print_level(SV_ERROR, "cJSON_CreateObject failed!\n");
					http_WsInfo_clear(pstWsAlarmSending);
					goto exit;
				}

				cJSON_AddItemToObject(pstJsonGps, "gpsStatus", cJSON_CreateBool((pstWsAlarmSending->stGpsInfo.s32Status == 0) ? SV_FALSE : SV_TRUE));
				cJSON_AddItemToObject(pstJsonGps, "speed", cJSON_CreateRaw(szSpeed));
				cJSON_AddItemToObject(pstJsonGps, "latitude", cJSON_CreateRaw(szLat));
				cJSON_AddItemToObject(pstJsonGps, "longitude", cJSON_CreateRaw(szLon));

				cJSON_AddItemToObject(pstJson, "gpsInfo", pstJsonGps);
			}

			if (pstWsAlarmSending->bWarnVid)
			{
				cJSON_AddItemToObject(pstJson, "warnVid", cJSON_CreateString(pstWsAlarmSending->szWarnVidPath));
			}
			else
			{
				cJSON_AddItemToObject(pstJson, "warnSnap", cJSON_CreateString(pstWsAlarmSending->szWarnPath));
			}
			alarmJson = cJSON_Print(pstJson);
			print_level(SV_INFO, "alarmJson: %s\n", alarmJson);

			pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
			for (tmp_c =  mgr.conns; tmp_c != NULL; tmp_c = tmp_c->next)
			{
				if (tmp_c->label[0] != 'A')
					continue;         // Skip non-stream connections
				mg_ws_send(tmp_c, alarmJson, strlen(alarmJson), WEBSOCKET_OP_TEXT);
			}
			free(alarmJson);
			cJSON_Delete(pstJson);
			lastTime = currentTime;
			bSendOff = SV_FALSE;
			pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);

			if (pstWsAlarmSending->bWarnVid)
			{
				std::list<WEBSOCKET_ALARM_INFO_S>::iterator listSended_itor;
				WEBSOCKET_ALARM_INFO_S stSendedWsAlarmInfo = {0};

				for (listSended_itor = pstWebSocketInfo->listSendedWsAlarmInfo.begin(); listSended_itor != pstWebSocketInfo->listSendedWsAlarmInfo.end(); listSended_itor++)
				{
					stSendedWsAlarmInfo = *listSended_itor;
					if (stSendedWsAlarmInfo.s64TimeStamp == pstWsAlarmSending->s64TimeStamp && 0 == strcmp(stSendedWsAlarmInfo.szAlarmUuid, pstWsAlarmSending->szAlarmUuid))
					{
						//print_level(SV_INFO, "remove uuid: %s in listSendedWsAlarmInfo\n", pstWsAlarmSending->szAlarmUuid);
						pstWebSocketInfo->listSendedWsAlarmInfo.erase(listSended_itor);
						break;
					}
				}
			}
			else if (0 != strlen(pstWsAlarmSending->szAlarmUuid))
			{
				pstWebSocketInfo->listSendedWsAlarmInfo.push_back(*pstWsAlarmSending);
			}

exit:
			pthread_mutex_lock(&pstWebSocketInfo->mutexWarnLock);
			http_WsInfo_clear(pstWsAlarmSending);
			pstWebSocketInfo->listSendingWsAlarmInfo.erase(listSending_itor);
			pthread_mutex_unlock(&pstWebSocketInfo->mutexWarnLock);

			if (!pstWebSocketInfo->listSendingWsAlarmInfo.empty())
			{
				print_level(SV_INFO, "websocket list is not empty, continue to push again!\n");
				goto push;
			}
        }
        else if (pstWsAlarmInfo->s32PDWarnState > 0)
        {
            if (0 == strlen(pstWsAlarmInfo->szWarnText))
            {
                print_level(SV_ERROR, "get null pointer.\n");
                http_WsInfo_clear(pstWsAlarmInfo);
                continue;
            }

            snprintf(str, 128, "{\"Warning\":\"%s\", \"code\":%d}", pstWsAlarmInfo->szWarnText, pstWsAlarmInfo->s32PDWarnState);
            pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
            for (tmp_c =  mgr.conns; tmp_c != NULL; tmp_c = tmp_c->next)
            {
                if (tmp_c->label[0] != 'A')
                    continue;         // Skip non-stream connections
                mg_ws_send(tmp_c, str, strlen(str), WEBSOCKET_OP_TEXT);
            }
            lastTime = currentTime;
            bSendOff = SV_FALSE;
            pthread_mutex_lock(&pstWebSocketInfo->mutexWarnLock);
            http_WsInfo_clear(pstWsAlarmInfo);
            pthread_mutex_unlock(&pstWebSocketInfo->mutexWarnLock);
            pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
        }

        if (!bSendOff && currentTime.tv_sec - lastTime.tv_sec > 2)
        {
            pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
            snprintf(str,128, "{\"Warning\": \"No_Warning\", \"code\": 0}");
            for (tmp_c = mgr.conns; tmp_c != NULL; tmp_c = tmp_c->next)
            {
                if (tmp_c->label[0] != 'A')
                    continue;
                mg_ws_send(tmp_c, str, strlen(str), WEBSOCKET_OP_TEXT);
            }
            lastTime = currentTime;
            bSendOff = SV_TRUE;
            pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
        }
    }

    mg_mgr_free(&mgr);
    return 0;
}
#endif

static sint32 callbackWebsocketAlarm(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    sint32 s32Ret = 0, warnState = -1;
    char *pszWarnText = NULL;
    char szCmd[128] = {0};
    static char szWarnPath[128] = {0};
    sint32 s32Fd = -1;
    sint32 s32WriteSize = 0;
    WEBSOCKET_COM_INFO_S *pstWebSocketInfo = &m_stWebSocket;
    WEBSOCKET_ALARM_INFO_S *pstWsAlarmInfo = &m_stWebSocket.stWsAlarmInfo;
    WEBSOCKET_ALARM_INFO_S stWsAlarmInfo = {0};
    std::list<WEBSOCKET_ALARM_INFO_S>::iterator list_itor;
    SV_BOOL bFound = SV_FALSE;
    sint32 s32ErrCnt = 0;

#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    ALARM_EVENT_S *pstAlarmEvent = (ALARM_EVENT_S *)pstMsgPkt->pu8Data;
    print_level(SV_INFO, "ws enAlarmEvent: %d, enAlarmType: %d, timeStamp: %lld\n", pstAlarmEvent->enAlarmEvent, pstAlarmEvent->enAlarmType, pstAlarmEvent->s64TimeStamp);

    switch (pstAlarmEvent->enAlarmEvent)
    {
        case ALARM_EVENT_DMM:
        case ALARM_EVENT_FR:
            if (0 != strlen(pstAlarmEvent->szPath))
            {
            	if (0 != strlen(pstAlarmEvent->u8AlarmUuid))
            	{
find_last:
	                for (list_itor = pstWebSocketInfo->listSendedWsAlarmInfo.begin(); list_itor != pstWebSocketInfo->listSendedWsAlarmInfo.end(); list_itor++)
	                {
	                    stWsAlarmInfo = *list_itor;
	                    if (stWsAlarmInfo.s64TimeStamp == pstAlarmEvent->s64TimeStamp && 0 == strcmp(stWsAlarmInfo.szAlarmUuid, pstAlarmEvent->u8AlarmUuid))
	                    {
	                        strcpy(stWsAlarmInfo.szWarnVidPath, pstAlarmEvent->szPath);
	                        stWsAlarmInfo.bWarnVid = SV_TRUE;
	                        bFound = SV_TRUE;
	                        break;
	                    }
	                }

	                if (!bFound)
	                {
	                    s32ErrCnt++;
	                    if (s32ErrCnt <= 10)
	                    {
	                        sleep_ms(500);
	                        goto find_last;
	                    }
	                    else
	                    {
	                        print_level(SV_ERROR, "not found uuid: %s in list!!!\n", pstAlarmEvent->u8AlarmUuid);
	                        goto exit;
	                    }
	                }
					else
					{
						pthread_mutex_lock(&pstWebSocketInfo->mutexWarnLock);
						pstWebSocketInfo->listSendingWsAlarmInfo.push_back(stWsAlarmInfo);
						pthread_mutex_unlock(&pstWebSocketInfo->mutexWarnLock);
						goto exit;
					}
            	}
				else
				{
					goto exit;
				}
            }

            if (pstAlarmEvent->enAlarmEvent == ALARM_EVENT_DMM)
                ALARM_GET_DMS_EVENT(pstAlarmEvent->enAlarmType, warnState, pszWarnText);
            else
                ALARM_GET_FR_EVENT(pstAlarmEvent->enAlarmType, warnState, pszWarnText);

            if (0 != strlen(pstAlarmEvent->u8Warning))
            {
                pszWarnText = pstAlarmEvent->u8Warning;
            }

			stWsAlarmInfo.s32DMMWarnState = warnState;
            stWsAlarmInfo.s64TimeStamp = pstAlarmEvent->s64TimeStamp;
            stWsAlarmInfo.fDmsEventScore = pstAlarmEvent->fDmsEventScore;
            stWsAlarmInfo.u16UsrId = pstAlarmEvent->u16UsrId;
            strcpy(stWsAlarmInfo.szUsrName, pstAlarmEvent->u8UsrName);
            strcpy(stWsAlarmInfo.szAccountName, pstAlarmEvent->u8AccountName);
            strcpy(stWsAlarmInfo.szWarnText, pszWarnText);
            strcpy(stWsAlarmInfo.szAlarmUuid, pstAlarmEvent->u8AlarmUuid);

            stWsAlarmInfo.stGpsInfo = pstAlarmEvent->stGpsInfo;
            if (0 != strlen(pstAlarmEvent->u8AlarmUuid))
                snprintf(stWsAlarmInfo.szWarnPath, 128, "/tmp/warn/%lld_%s.jpeg", pstAlarmEvent->s64TimeStamp, pstAlarmEvent->u8AlarmUuid);
            else
                snprintf(stWsAlarmInfo.szWarnPath, 128, "/tmp/warn/%02d_%lld.jpeg", warnState, pstAlarmEvent->s64TimeStamp);


            /* 获取snap0全局锁，写文件 */
            pthread_mutex_lock(&m_stSnapInfo.mutexSnap);
            s32Fd = open(stWsAlarmInfo.szWarnPath, O_CREAT | O_RDWR, S_IRWXU | S_IRGRP | S_IROTH);
            if (s32Fd <= 0)
            {
                print_level(SV_ERROR, "open file: %s failed.\n", stWsAlarmInfo.szWarnPath);
				memset(stWsAlarmInfo.szWarnPath, 0, sizeof(stWsAlarmInfo.szWarnPath));
                goto pic_exit;
            }
            else
            {
                s32WriteSize = write(s32Fd, m_stSnapInfo.apvImgBuf, m_stSnapInfo.s32ImgSize);
                if (s32WriteSize < m_stSnapInfo.s32ImgSize)
                {
                    print_level(SV_ERROR, "write ws snap failed.\n");
					memset(stWsAlarmInfo.szWarnPath, 0, sizeof(stWsAlarmInfo.szWarnPath));
                    goto pic_exit;
                }
                close(s32Fd);
                s32Fd = -1;
            }

            http_SortWarnSnap(stWsAlarmInfo.szWarnPath);

pic_exit:
            if (s32Fd > 0)
            {
                close(s32Fd);
            }
            pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);

			pthread_mutex_lock(&pstWebSocketInfo->mutexWarnLock);
			pstWebSocketInfo->listSendingWsAlarmInfo.push_back(stWsAlarmInfo);
			pthread_mutex_unlock(&pstWebSocketInfo->mutexWarnLock);
exit:
            print_level(SV_INFO,"dmm_warning_state: %d, szWarnText: %s, szWarnPath: %s, szWarnVidPath: %s\n", \
				stWsAlarmInfo.s32DMMWarnState, stWsAlarmInfo.szWarnText, stWsAlarmInfo.szWarnPath, stWsAlarmInfo.szWarnVidPath);
            break;

        case ALARM_EVENT_PD:
            pstWsAlarmInfo->u16UsrId = pstAlarmEvent->u16UsrId;
            ALARM_GET_PD_EVENT(pstAlarmEvent->enAlarmType, warnState, pszWarnText);
            pstWsAlarmInfo->s32PDWarnState = warnState;
            strcpy(pstWsAlarmInfo->szWarnText, pszWarnText);
            print_level(SV_INFO,"pd_warning_state: %d, %s, %s\n",pstWsAlarmInfo->s32PDWarnState, pstWsAlarmInfo->szWarnText, szWarnPath);
            break;

        default:
            print_level(SV_ERROR, "not support opcode: %d\n", pstMsgPkt->stMsg.u16OpCode);
            break;
    }
#endif
#endif

    return SV_SUCCESS;
}

static sint32 callbackWebsocketInfo(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(PLATFORM_RV1126))

    WEBSOCKET_COM_INFO_S *pstWebSocket = &m_stWebSocket;
    MSG_WEBSOCKET_INFO_S *pstWebsocketInfo = (MSG_WEBSOCKET_INFO_S *)pstMsgPkt->pu8Data;
    struct mg_mgr *pmg_mgr = pstWebSocket->pmg_mgr;
    char *pstr = pstWebsocketInfo->stWsInfo;
    struct mg_connection *nc;
    struct mg_connection *tmp_c;

    if (pmg_mgr == NULL)
    {
        return SV_SUCCESS;
    }

    pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
    for (tmp_c = pmg_mgr->conns; tmp_c != NULL; tmp_c = tmp_c->next)
    {
        if (tmp_c->label[0] != 'A')
            continue;               // Skip non-stream connections

        mg_ws_send(tmp_c, pstr, strlen(pstr), WEBSOCKET_OP_TEXT);
    }
    pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
#endif

#if (defined(PLATFORM_NT98539))
    WEBSOCKET_COM_INFO_S *pstWebSocket = &m_stWebSocket;
    MSG_WEBSOCKET_INFO_S *pstWebsocketInfo = (MSG_WEBSOCKET_INFO_S *)pstMsgPkt->pu8Data;
    struct mg_mgr *pmg_mgr = pstWebSocket->pmg_mgr;
    char *pstr = pstWebsocketInfo->stWsInfo;
    struct mg_connection *nc;
    struct mg_connection *tmp_c;

    if (pmg_mgr == NULL)
    {
        return SV_SUCCESS;
    }

    pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
    for (tmp_c = pmg_mgr->conns; tmp_c != NULL; tmp_c = tmp_c->next)
    {
        if (tmp_c->label[0] != 'A')
            continue;               // Skip non-stream connections

        mg_ws_send(tmp_c, pstr, strlen(pstr), WEBSOCKET_OP_TEXT);
    }
    pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
#endif
    return SV_SUCCESS;
}

static sint32 callbackWebsocketPosition(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
#if (defined(PLATFORM_NT98539) || defined(PLATFORM_RV1106) || defined(PLATFORM_RV1126))
    sint32 s32Ret = 0, i;
    WEBSOCKET_COM_INFO_S *pstWebSocket = &m_stWebSocket;
    MEDIA_GUI_PERSON_S *pstGuiPersion = (MEDIA_GUI_PERSON_S *)pstMsgPkt->pu8Data;
    //print_level(SV_DEBUG, "u32PersonNum:%d\n", pstGuiPersion->u32PersonNum);
    struct mg_mgr *pmg_mgr = pstWebSocket->pmg_mgr;
    char szBuf[5*1024];
    struct mg_connection *nc;
    struct mg_connection *tmp_c;
    cJSON *pstJson = NULL, *pstPersonList = NULL, *pstPersonInfo = NULL;
    uint8 u8Hardware = 0;

    if (pmg_mgr == NULL)
    {
        return SV_SUCCESS;
    }

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return SV_ERROR;
    }

    cJSON_AddItemToObject(pstJson, "factory", cJSON_CreateNumber(0x01));

    // if(BOARD_IsSVersion(BOARD_S_H_1M45))
    // {
    //     u8Hardware = HardWare_ADA37_1M45;
    // }
    // else if(BOARD_IsSVersion(BOARD_S_H_1M99))
    // {
    //     u8Hardware = HardWare_ADA37_1M99;
    // }
    // else if(BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_V_2M3))
    // {
    //     u8Hardware = HardWare_ADA32_2M3;
    // }
    // else if(BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_V_6M))
    // {
    //     u8Hardware = HardWare_ADA32_6M;
    // }

    #if defined(PLATFORM_RV1126)
        if(BOARD_IsSVersion(BOARD_S_H_1M45))
        {
            u8Hardware = HardWare_1M45_1080;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_1M99))
        {
            u8Hardware = HardWare_1M99_1080;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_V_2M3))
        {
            u8Hardware = HardWare_2M3_1080;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_V_6M))
        {
            u8Hardware = HardWare_6M_1080;
        }
    #elif defined(PLATFORM_RV1106)
        if(BOARD_IsSVersion(BOARD_S_H_1M45))
        {
            u8Hardware = HardWare_1M45_720;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_1M99))
        {
            u8Hardware = HardWare_1M99_720;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_V_2M3))
        {
            u8Hardware = HardWare_2M3_720;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_V_6M))
        {
            u8Hardware = HardWare_6M_720;
        }
    #else
         print_level(SV_ERROR, " platform is others !!! ... \n");
    #endif

    cJSON_AddItemToObject(pstJson, "hardware", cJSON_CreateNumber(u8Hardware));
    cJSON_AddItemToObject(pstJson, "software", cJSON_CreateNumber(Software_ALG_2));
    cJSON_AddItemToObject(pstJson, "targetTotal", cJSON_CreateNumber(pstGuiPersion->u32PersonNum));
    pstPersonList = cJSON_CreateArray();
    if (NULL == pstPersonList)
    {
        print_level(SV_ERROR, "cJSON_CreateArray failed.\n");
        cJSON_Delete(pstJson);
        return SV_ERROR;
    }

    cJSON_AddItemToObject(pstJson, "targetList", pstPersonList);
    for (i = 0; i < pstGuiPersion->u32PersonNum; i++)
    {
        pstPersonInfo = cJSON_CreateObject();
        if (NULL == pstPersonInfo)
        {
            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
            cJSON_Delete(pstJson);
        }

        cJSON_AddItemToObject(pstPersonInfo, "type", cJSON_CreateNumber(pstGuiPersion->classes[i]));
        cJSON_AddItemToObject(pstPersonInfo, "confidence", cJSON_CreateNumber(pstGuiPersion->au32Score[i]));
        cJSON_AddItemToObject(pstPersonInfo, "x1", cJSON_CreateNumber(pstGuiPersion->astPersonsRect[i].x1));
        cJSON_AddItemToObject(pstPersonInfo, "y1", cJSON_CreateNumber(pstGuiPersion->astPersonsRect[i].y1));
        cJSON_AddItemToObject(pstPersonInfo, "x2", cJSON_CreateNumber(pstGuiPersion->astPersonsRect[i].x2));
        cJSON_AddItemToObject(pstPersonInfo, "y2", cJSON_CreateNumber(pstGuiPersion->astPersonsRect[i].y2));
        cJSON_AddItemToArray(pstPersonList, pstPersonInfo);
    }

    memset(szBuf, 0x0, sizeof(szBuf));
    cJSON_PrintPreallocated(pstJson, szBuf, 5*1024, 0);
    cJSON_Delete(pstJson);
    //print_level(SV_DEBUG, "%s\n", szBuf);
    pthread_mutex_lock(&m_stHttpInfo.mutexLockWarning);
    for (tmp_c = pmg_mgr->conns; tmp_c != NULL; tmp_c = tmp_c->next)
    {
        if (tmp_c->label[0] != 'P')
            continue;               // Skip non-stream connections

        mg_ws_send(tmp_c, szBuf, strlen(szBuf), WEBSOCKET_OP_TEXT);
    }
    pthread_mutex_unlock(&m_stHttpInfo.mutexLockWarning);
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 搜索Sps和Pps
 * 输入参数:     pNuil --- 输入指针
 		     iNuilLen --- 输入数据大小
 * 输出参数:
             sps_data --- sps数据起始位置
             iOutSpsDataLen --- sps数据
             pps_data --- pps数据起始位置
             iOutPpsDataLen --- pps数据

 * 返回值  : 0 - 成功
             -1 - 其它错误
 * 注意      : 无
 *****************************************************************************/
int HTTP_SearchSpsPpsData(sint8 *pNuil, const int iNuilLen, char *sps_data, int *iOutSpsDataLen, char *pps_data, int *iOutPpsDataLen)
{
    if (pNuil == NULL || sps_data == NULL || pps_data == NULL)
    {
        print_level(SV_ERROR, "NULL Ptr\r\n");
		return -1;
    }

	if(iNuilLen < 100)
	{
		print_level(SV_ERROR, "Search SpsPpsData failed, nuil len is too short\r\n");
		return -1;
	}
	int iSpsPos = -1;
	int iPpsPos = -1;
	int iPpsEndPos = -1;
	bool bSearch = false;
	for(int i = 0; i < 100; i++)
	{
		if(*(pNuil +i) == 0x67)
		{
			iSpsPos = i;
			for(int j = i; j < 100;j++)
			{
				if(*(pNuil + j) == 0x68)
				{
					iPpsPos = j;
					for(int k = j;k < 100;k++)
					{
						if(*(pNuil + k) == 0x00 && *(pNuil + k + 1) == 0x00 && *(pNuil + k + 2) == 0x00 &&
							*(pNuil + k + 3) == 0x01)
						{
							iPpsEndPos = k;
							bSearch = true;
							break;
						}
					}
				}
				if(bSearch)
					break;
			}
		}
		if(bSearch)
			break;
	}

	if(iPpsEndPos > iPpsPos && iPpsPos > iSpsPos)
	{
		*iOutSpsDataLen = iPpsPos - iSpsPos - 4;
		//*sps_data = new char[iOutSpsDataLen];
		memcpy(sps_data, pNuil + iSpsPos, *iOutSpsDataLen);

		*iOutPpsDataLen = iPpsEndPos - iPpsPos;
		//*pps_data = new char[iOutPpsDataLen];
		memcpy(pps_data, pNuil + iPpsPos, *iOutPpsDataLen);

		return 0;
	}
	else
	{
		print_level(SV_ERROR, "Can not find sps pps data, iPpsEndPos[%d], iPpsPos[%d], iSpsPos[%d]\r\n", iPpsEndPos, iPpsPos, iSpsPos);
		return -1;
	}
}

sint32 HTTP_SVR_Init(HTTP_CFG_PARAM_S *pstInitParam)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32SocketFd = 0;
    sint32 s32Flag = 0;
    char szCmd[128] = {0};
	int j = 0;
    memset(&m_stHttpInfo, 0x0, sizeof(HTTP_COM_INFO_S));
    m_stHttpInfo.bKeyAuth = pstInitParam->bKeyAuth;
    strncpy(m_stHttpInfo.szSerialNum, pstInitParam->szSerialNum, 31);

    s32Ret = http_switch_source_path();
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "http_switch_source_path failed! [err=%#x]\n", s32Ret);
    }

#if 0
    if(BOARD_IsCustomer(BOARD_C_ADA32V2_WE))
    {
        /* 8084端口监听MJPEG请求 */
        s32Ret = http_CreateHttpd(8084, &s32SocketFd);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        /* 设置为非阻塞式 */
        s32Flag = fcntl(s32SocketFd,F_GETFL,0);
        if (s32Flag < 0)
        {
            print_level(SV_ERROR, "fcntl F_GETFL failed. [err: %s]\n", strerror(errno));
        }
        else
        {
            s32Flag &= O_NONBLOCK;
            fcntl(s32SocketFd, F_SETFL, s32Flag);
        }

        m_stHttpInfo.s32MJpegSocket = s32SocketFd;
    }
#endif

#if (!defined(BOARD_ADA32V4) && !defined(BOARD_DMS31V2) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA900V1) && !defined(BOARD_ADA32V2) && !defined(BOARD_ADA32V3) && !defined(BOARD_ADA32N1) && !defined(BOARD_ADA32C4) && !defined(BOARD_ADA32E1) && !defined(BOARD_HDW845V1))

    s32Ret = http_CreateHttpd(pstInitParam->u32ServicePort, &s32SocketFd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stHttpInfo.s32SvrSocket = s32SocketFd;

/*
#if (defined(BOARD_IPCR20S4))
    s32Ret = http_CreateUdpd(50001, &s32SocketFd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    m_stHttpInfo.s32UdpMjpegSocket = s32SocketFd;
#endif
*/
#if 0//(defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    s32Ret = http_CreateUdpMulticastd(2887, &s32SocketFd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    m_stHttpInfo.s32IPSearchSocket = s32SocketFd;
#endif

	/* 8081端口监听抓图 */
    s32Ret = http_CreateHttpd(8081, &s32SocketFd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 快速抓图socket设置为非阻塞式 */
    s32Flag = fcntl(s32SocketFd,F_GETFL,0);
    if (s32Flag < 0)
    {
        print_level(SV_ERROR, "fcntl F_GETFL failed. [err: %s]\n", strerror(errno));
    }
    else
    {
        s32Flag &= O_NONBLOCK;
        fcntl(s32SocketFd, F_SETFL, s32Flag);
    }
    m_stHttpInfo.s32QuickSvrSocket = s32SocketFd;

	/* 8083端口监听websocket */
#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3))

		s32Ret = http_CreateHttpd(8083, &s32SocketFd);
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
			return s32Ret;
		}

		s32Flag = fcntl(s32SocketFd,F_GETFL,0);
		if (s32Flag < 0)
		{
			print_level(SV_ERROR, "fcntl F_GETFL failed. [err: %s]\n", strerror(errno));
		}
		else
		{
			s32Flag &= O_NONBLOCK;
			fcntl(s32SocketFd, F_SETFL, s32Flag);
		}
		m_stHttpInfo.s32WsJpegSocket = s32SocketFd;
#endif

#if (defined(BOARD_IPCR20S4))
	/* 8082端口监听Flv请求 */
	s32Ret = http_CreateHttpd(8082, &s32SocketFd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 设置为非阻塞式 */
    s32Flag = fcntl(s32SocketFd,F_GETFL,0);
    if (s32Flag < 0)
    {
        print_level(SV_ERROR, "fcntl F_GETFL failed. [err: %s]\n", strerror(errno));
    }
    else
    {
        s32Flag &= O_NONBLOCK;
        fcntl(s32SocketFd, F_SETFL, s32Flag);
    }

	m_stHttpInfo.s32FlvSocket = s32SocketFd;
#endif

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    if(BOARD_IsCustomer(BOARD_C_IPCR20S4_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_200028))
    {
        /* 8084端口监听MJPEG请求 */
        s32Ret = http_CreateHttpd(8084, &s32SocketFd);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        /* 设置为非阻塞式 */
        s32Flag = fcntl(s32SocketFd,F_GETFL,0);
        if (s32Flag < 0)
        {
            print_level(SV_ERROR, "fcntl F_GETFL failed. [err: %s]\n", strerror(errno));
        }
        else
        {
            s32Flag &= O_NONBLOCK;
            fcntl(s32SocketFd, F_SETFL, s32Flag);
        }

        m_stHttpInfo.s32MJpegSocket = s32SocketFd;
    }
#endif

#endif

    s32Ret = sem_init(&m_stHttpInfo.semDownFile, 0, 2);     // 最大同时只能两个录像一起下载，否则对卡IO操作太频繁会导致卡图
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "sem_init failed! [err=%#x]\n", s32Ret);
        close(m_stHttpInfo.s32SvrSocket);
        close(m_stHttpInfo.s32QuickSvrSocket);
		close(m_stHttpInfo.s32FlvSocket);
		close(m_stHttpInfo.s32WsJpegSocket);
        close(m_stHttpInfo.s32MJpegSocket);
        return s32Ret;
	}

    s32Ret = pthread_mutex_init(&m_stHttpInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        close(m_stHttpInfo.s32SvrSocket);
        close(m_stHttpInfo.s32QuickSvrSocket);
		close(m_stHttpInfo.s32FlvSocket);
		close(m_stHttpInfo.s32WsJpegSocket);
        close(m_stHttpInfo.s32MJpegSocket);
        return s32Ret;
    }

    s32Ret = pthread_mutex_init(&m_stHttpInfo.mutexLockWarning, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        close(m_stHttpInfo.s32SvrSocket);
        close(m_stHttpInfo.s32QuickSvrSocket);
		close(m_stHttpInfo.s32FlvSocket);
		close(m_stHttpInfo.s32WsJpegSocket);
        close(m_stHttpInfo.s32MJpegSocket);
        return s32Ret;
    }

	s32Ret = pthread_mutex_init(&m_mutexLockStrtok, NULL);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_mutex_init failed! [err=%#x]\n", s32Ret);
        close(m_stHttpInfo.s32SvrSocket);
        close(m_stHttpInfo.s32QuickSvrSocket);
		close(m_stHttpInfo.s32FlvSocket);
		close(m_stHttpInfo.s32WsJpegSocket);
        close(m_stHttpInfo.s32MJpegSocket);
        return s32Ret;
    }

	for(i = 0;i<3;i++)
	{
		for(j = 0;j<3;j++)
		{
			strcpy(m_szUserPassword[i][j], pstInitParam->szUserPassword[i][j]);
		}
	}

#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    m_stWebSocket.stWsAlarmInfo.s32DMMWarnState = -1;
    m_stWebSocket.stWsAlarmInfo.s32PDWarnState = -1;
    m_stWebSocket.stWsAlarmInfo.s32APCWarnState = -1;
	s32Ret = pthread_mutex_init(&m_stWebSocket.mutexWarnLock,NULL);
	if(0!=s32Ret)
	{
		print_level(SV_ERROR,"pthread_mutex_init failed![err=%#x]\n",s32Ret);
		return s32Ret;
	}

    if (!COMMON_IsPathExist(WARN_SANP_PATH))
    {
        sprintf(szCmd, "mkdir -p %s", WARN_SANP_PATH);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
    }

#if defined(BOARD_DMS31V2)
    snprintf(szCmd, 256, "ln -s %s %s", FRS_USERINFOS_PATH, HTTP_RESOURCE_PATH);
    SAFE_SV_System(szCmd);
#endif
#endif

    return SV_SUCCESS;
}


sint32 HTTP_SVR_Fini()
{
    sint32 s32Ret = 0;

    sem_destroy(&m_stHttpInfo.semDownFile);
    pthread_mutex_destroy(&m_stHttpInfo.mutexLock);
    pthread_mutex_destroy(&m_stHttpInfo.mutexLockWarning);
	pthread_mutex_destroy(&m_mutexLockStrtok);

#if (!defined(BOARD_DMS31V2) && !defined(BOARD_ADA47V1))
    s32Ret = http_DestroyHttpd(m_stHttpInfo.s32SvrSocket);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = http_DestroyHttpd(m_stHttpInfo.s32QuickSvrSocket);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3))
		s32Ret = http_DestroyHttpd(m_stHttpInfo.s32WsJpegSocket);
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
			return s32Ret;
		}
#endif

#if (defined(BOARD_IPCR20S4))
	s32Ret = http_DestroyHttpd(m_stHttpInfo.s32FlvSocket);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif


#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    if(BOARD_IsCustomer(BOARD_C_IPCR20S4_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_200028))
    {
        s32Ret = http_DestroyHttpd(m_stHttpInfo.s32MJpegSocket);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "http_CreateHttpd failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif

#endif

    return SV_SUCCESS;
}


sint32 HTTP_SVR_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread1 = 0;
    pthread_t thread2 = 0;
    pthread_t thread3 = 0;
	pthread_t thread4 = 0;
    pthread_t thread5 = 0;
	pthread_t thread6 = 0;
    pthread_t thread7 = 0;
    pthread_t thread8 = 0;
    pthread_t thread9 = 0;

    s32Ret = MSG_ReciverStart(EP_HTTPSERVER);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_HTTPSERVER, OP_EVENT_USR_CHANGE, callbackUsrParamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_HTTPSERVER, OP_EVENT_NETWORK_STAT, callbackCloseHttp);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_HTTPSERVER, OP_EVENT_JPEG_SIZE_CHANGE, callbackJpegSizeChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_HTTPSERVER, OP_EVENT_NETWORK_CHANGE, callbackNetworkParamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_DMS51V1))
    //websocket 回传报警信息
    s32Ret = Msg_registerOpCallback_ThreadExec(EP_HTTPSERVER, OP_EVENT_ALG_ALARM, callbackWebsocketAlarm);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR,"Msg_registerOpCallback failed.[err=%#x]\n",s32Ret);
        return s32Ret;
    }
#endif

#if (defined(PLATFORM_RV1126))
    s32Ret = Msg_registerOpCallback_ThreadExec(EP_HTTPSERVER, OP_EVENT_WEBSOCKET_INFO, callbackWebsocketInfo);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR,"Msg_registerOpCallback_ThreadExec failed.[err=%#x]\n",s32Ret);
        return s32Ret;
    }
#endif

#if (defined(PLATFORM_NT98539))
    s32Ret = Msg_registerOpCallback_ThreadExec(EP_HTTPSERVER, OP_EVENT_WEBSOCKET_INFO, callbackWebsocketInfo);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR,"Msg_registerOpCallback_ThreadExec failed.[err=%#x]\n",s32Ret);
        return s32Ret;
    }
#endif

#if (defined(BOARD_ADA32V4) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1))
    s32Ret = Msg_registerOpCallback_ThreadExec(EP_HTTPSERVER, OP_EVENT_WEBSOCKET_POSITION, callbackWebsocketPosition);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR,"Msg_registerOpCallback_ThreadExec failed.[err=%#x]\n",s32Ret);
        return s32Ret;
    }
#endif

    m_stHttpInfo.bRunning = SV_TRUE;

#if 0
    if(BOARD_IsCustomer(BOARD_C_ADA32V2_WE))
    {
        s32Ret = pthread_create(&thread7, NULL, http_MJpeg_Body, &m_stHttpInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
            return ERR_SYS_NOTREADY;
        }
    	m_stHttpInfo.u32TidMJpeg = thread7;
    }
#endif

#if (!defined(BOARD_ADA32V4) && !defined(BOARD_DMS31V2) && !defined(BOARD_ADA47V1) && !defined(BOARD_ADA900V1) && !defined(BOARD_ADA32V2) && !defined(BOARD_ADA32V3) &&!defined(BOARD_ADA32N1) &&!defined(BOARD_ADA32C4) &&!defined(BOARD_ADA32E1) && !defined(BOARD_HDW845V1))

    s32Ret = pthread_create(&thread1, NULL, http_SVR_Body, &m_stHttpInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
	m_stHttpInfo.u32TidService = thread1;

    s32Ret = pthread_create(&thread2, NULL, http_QuickJpeg_Body, &m_stHttpInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
    m_stHttpInfo.u32TidQuickJpeg = thread2;

#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3))
		s32Ret = pthread_create(&thread6,NULL,websocket_QuickJpeg_Body,&m_stHttpInfo);
		if(0!=s32Ret)
		{
			print_level(SV_ERROR,"pthread_create failed.[err=%#x]\n",s32Ret);
			return ERR_SYS_NOTREADY;
		}
		m_stHttpInfo.u32TidWsJpeg = thread6;
#endif


#if (defined(BOARD_IPCR20S4))
	s32Ret = pthread_create(&thread4, NULL, http_Flv_Body, &m_stHttpInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
	m_stHttpInfo.u32TidFlv = thread4;
#endif
/*
#if (defined(BOARD_IPCR20S4))
    s32Ret = pthread_create(&thread9, NULL, udp_Mjpeg_Body, &m_stHttpInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
	m_stHttpInfo.u32TidUdpMJpeg = thread9;
#endif
*/
#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    if(BOARD_IsCustomer(BOARD_C_IPCR20S4_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_200028))
    {
        s32Ret = pthread_create(&thread7, NULL, http_MJpeg_Body, &m_stHttpInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
            return ERR_SYS_NOTREADY;
        }
    	m_stHttpInfo.u32TidMJpeg = thread7;
    }
#endif

#endif

#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_HDW845V1))
	s32Ret = pthread_create(&thread5, NULL, mongoose_server_Body, &m_stWebSocket);
	if(0!=s32Ret)
	{
		print_level(SV_ERROR,"pthread_create failed.[err=%#x]\n",s32Ret);
		return ERR_SYS_NOTREADY;
	}
	m_stWebSocket.u32TidWsAlarm = thread5;
#endif

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4)/* || defined(BOARD_IPCR20S5)*/)
    s32Ret = pthread_create(&thread8, NULL, http_IpSearch_Body, &m_stHttpInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }
	m_stHttpInfo.u32TidIpSearch = thread8;
#endif

#if (defined(PLATFORM_NT98539) || defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
    pthread_t thread_trigger;
    pthread_attr_t 	attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    s32Ret = pthread_create(&thread_trigger, NULL, http_ImportConfigBody, &m_stHttpInfo);
	if (0 != s32Ret)
	{
		print_level(SV_ERROR,"pthread_create failed.[err=%#x]\n",s32Ret);
		return ERR_SYS_NOTREADY;
	}
    pthread_attr_destroy(&attr);
#endif

    return SV_SUCCESS;
}


sint32 HTTP_SVR_Stop()
{
	sint32 s32Ret = 0;
#if (!defined(BOARD_DMS31V2) && !defined(BOARD_ADA47V1))
    pthread_t thread1 = m_stHttpInfo.u32TidService;
    pthread_t thread2 = m_stHttpInfo.u32TidQuickJpeg;
    pthread_t thread3 = m_stHttpInfo.u32TidPort80;
	pthread_t thread6 = m_stHttpInfo.u32TidWsJpeg;
#endif
	pthread_t thread4 = m_stHttpInfo.u32TidFlv;
    pthread_t thread5 = m_stWebSocket.u32TidWsAlarm;
    pthread_t thread7 = m_stHttpInfo.u32TidMJpeg;
    void *pvRetval = NULL;

    m_stHttpInfo.bRunning = SV_FALSE;

#if (!defined(BOARD_DMS31V2) && !defined(BOARD_ADA47V1))
    s32Ret = pthread_join(thread1, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = pthread_join(thread2, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3))
	s32Ret = pthread_join(thread6, &pvRetval);
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
		return SV_FAILURE;
	}
#endif


#if (defined(BOARD_IPCR20S4))
	s32Ret = pthread_join(thread4, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    if(BOARD_IsCustomer(BOARD_C_IPCR20S4_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_WE) || BOARD_IsCustomer(BOARD_C_IPCR20S3_200028))
    {
        s32Ret = pthread_join(thread7, &pvRetval);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }
#endif

#endif

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1))
		s32Ret = pthread_join(thread5, &pvRetval);
		if (0 != s32Ret)
		{
			print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
			return SV_FAILURE;
		}
#endif

    s32Ret = MSG_ReciverStop(EP_HTTPSERVER);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStop failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

