Index: include/config_factory.h
===================================================================
--- include/config_factory.h	(revision 4451)
+++ include/config_factory.h	(working copy)
@@ -43,6 +43,10 @@
     {
         pszAdminPassword = "installer";
     }
+    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
+    {
+        pszAdminPassword = "BVAdmin123";
+    }
 #endif
 #if (defined(BOARD_ADA32IR))
     else if(BOARD_IsCustomer(BOARD_C_ADA32IR_202461))
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 4451)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -6381,7 +6381,7 @@
     {
         if(model == E_PDS_P || model == E_PDS_OWP)
             model = E_PDS_NIR_P;
-        else if(model || model == E_PDS_OWPC)
+        else if(model == E_PDS_PC || model == E_PDS_OWPC)
             model = E_PDS_NIR_PC;
         else if(model == E_PDS_C || model == E_PDS_OWC)
             model = E_PDS_NIR_C;
@@ -6401,9 +6401,6 @@
         case E_PDS_P:
             modefilelist[0] = PD_MODEL_RGB_P;
 
-            if (BOARD_IsCustomer(BOARD_C_ADA32V2_FTC))
-                modefilelist[0] = PD_MODEL_RGB_P_FTC;
-
             if (BOARD_IsCustomer(BOARD_C_ADA32V2_201266B) || BOARD_IsCustomer(BOARD_C_ADA32V2_201266C))
                 modefilelist[0] = PD_MODEL_RGB_P_201266B;
 
Index: src/autoUpdate/autoUpdate.c
===================================================================
--- src/autoUpdate/autoUpdate.c	(revision 4451)
+++ src/autoUpdate/autoUpdate.c	(working copy)
@@ -2818,6 +2818,7 @@
             m_stUpdateInfo.bUpdateSource = SV_TRUE;
         }
 
+#if 0
         if (j < 3 && strstr(szSrcPath, "specialVersion") != NULL)
         {
             print_level(SV_INFO, "update specialVersion latter.\n");
@@ -2824,6 +2825,7 @@
             m_stUpdateInfo.bUpdateSpecialVersion = SV_TRUE;
         }
 #endif
+#endif
 
         if (j >= 3)
         {
Index: src/common/board/board.c
===================================================================
--- src/common/board/board.c	(revision 4451)
+++ src/common/board/board.c	(working copy)
@@ -1050,7 +1050,9 @@
     }
     close(s32Fd);
 
-#if (!defined(PLATFORM_SSC335))
+    print_level(SV_ERROR, "skip cp default\n");
+//#if (!defined(PLATFORM_SSC335))
+#if 0
     sprintf(szCmd, "cp %s %s", CONFIG_DEFAULT_BAK, CONFIG_XML);
     s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
     if (0 != s32Ret)
