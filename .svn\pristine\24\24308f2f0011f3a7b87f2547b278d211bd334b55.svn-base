/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file:       r_signal.h
author:     lyn
version:    1.0.0
date:       2023-12-08
function:   recorder signal header file
notice:     移植开源信号槽机制(https://github.com/pbhogan/Signals)
            不要在槽函数内部调用同样的信号其他操作，如Disconnect这些操作，否则会出现死锁情况;
*******************************************************************************/
#ifndef _R_SIGNAL_H_
#define _R_SIGNAL_H_

#include "common.h"
#include "signal/Signal.h"
#include "signal/Delegate.h"

#include "r_common.h"

using namespace Gallant;

namespace recorder{

#define emit

class RSignal
{
private:
public:
    /* 信号命名规则，后面数字代表该信号参数个数 */
    Signal1< REC_CONF_S >       configSys;              /* 配置信号 */
    Signal1< REC_MEDIA_S >      configMedia;            /* 媒体配置信号 */
    Signal1< REC_ALARM_CONF_S > alarm;                  /* 报警信号 */
    Signal1< REC_STATUS_E >     getAlarmRecStat;        /* 获取报警录像状态 */
    Signal2< REC_POS_E, int >   posChange;              /* 存储位置变更 */
    Signal0< void >             requestAlarmRecStat;    /* 请求获取报警录像状态 */
    Signal1< RECORDER_CMS_NOTICE_CALLBACK > cmsRegister;/* 注册CMS回调函数 */
    Signal2< REC_POS_E, int >   posRemove;              /* 存储位置移除 */
    Signal1< REC_MAN_PARAM_S *> startRecord;            /* 开启手动录像 */
    Signal1< sint32 >           stopRecord;             /* 结束手动录像 */
    Signal1< REC_DMM_EYELID_S > writeData;              /* 写眼部数据 */
};
}

#endif /* _R_SIGNAL_H_ */

