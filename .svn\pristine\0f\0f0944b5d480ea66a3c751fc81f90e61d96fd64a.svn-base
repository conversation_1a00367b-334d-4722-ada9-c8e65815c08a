#! /bin/sh

echo "start update.sh to upgrade ! ! ! !"

killall -9 wtd.sh
killall -9 alg
killall -9 ipsys  
    
autoUpdateRes=0
cd /boot
if [ -e autoUpdate ]; then
    ./autoUpdate "update" "/tmp"
    autoUpdateRes=$?
    echo autoUpdateRes: $autoUpdateRes
    if [ $autoUpdateRes -eq 11 ]; then
        /boot/autoUpdate "update" "/tmp"
    fi
fi

safereboot

#cd /root
#if [ -e bootstart.sh ]; then
#    ./bootstart.sh
#fi

