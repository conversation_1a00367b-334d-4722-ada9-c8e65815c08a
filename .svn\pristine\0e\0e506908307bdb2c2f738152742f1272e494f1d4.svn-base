#
# Link all library, and build the final excutable file
#

include ../../Makefile.param

# 1
ifneq ($(PLATFORM),$(findstring $(PLATFORM),NT98539,RV1126,RV1126_REBUILD,RV1106))
IGNORE_THIS_BUILD = alg
endif

ifeq ($(BOARD),$(findstring $(BOARD),DMS31V2,DMS31SDK,DMS51V1))
SRCPPS = $(wildcard ./dmm/*.cpp)
SV_COM_LIBS = -ldmm
endif

ifeq ($(BOARD),$(findstring $(BOARD),ADA32V2,ADA32SDK,ADA32N1,ADA32NSDK,ADA32C4,ADA32E1,ADA32ESDK,ADA46V1))
SRCPPS = $(wildcard ./pd/*.cpp)
SV_COM_LIBS = -lcalibrate_radar -lpds_general_rv1126 -ljpeg -locclusion

ifeq ($(BOARD),$(findstring $(BOARD),ADA32V2,ADA32N1))
SV_COM_LIBS += -ladas_general_rk
endif

ifeq ($(BOARD), $(findstring $(BOARD),ADA32V2,ADA32SDK,ADA32N1,ADA32NSDK,ADA32E1,ADA32ESDK,ADA46V1))
CFLAGS += -DUseOpflow
SV_COM_LIBS += -lopticalflow_rk
endif

ifeq ($(BOARD), $(findstring $(BOARD),ADA32N1,ADA32NSDK))
SRCPPS += $(wildcard ./dmm/*.cpp)
SRCPPS += $(wildcard ./apc/*.cpp)
CFLAGS += -ldmm
endif 
endif

ifeq ($(BOARD), ADA32V3)
SRCPPS = $(wildcard ./pd/*.cpp)
SV_COM_LIBS = -lpds_general_rv1106 -ljpeg -locclusion
endif

# pyl TODO
ifeq ($(BOARD), ADA32V4)
SRCPPS = $(wildcard ./pd/*.cpp)
SV_COM_LIBS = -ljpeg -lyuv -lstdc++ -lvos -lvendor_ai3 -lvendor_ai3_pub -lvendor_media -lprebuilt_ai -lstdc++ -lpthread -lhdal -lpds_general_ly98539g -lopencv_imgcodecs -lopencv_imgproc -lopencv_core
CFLAGS   += -I./build/nt98539/normal/include -I/opt/NT98539G/ns02302_linux_sdk/code/hdal/include -DPLATFORM=PLATFORM_NT98539

LD_FLAGS += -L./build/nt98539/normal/lib \
			-L/opt/NT98539G/ns02302_linux_sdk/code/hdal/vendor/output  \
			-L/opt/NT98539G/ns02302_linux_sdk/code/hdal/output \
			-L/opt/NT98539G/ns02302_linux_sdk/code/vos/output
endif

ifeq ($(BOARD), ADA32IR)
SRCPPS = $(wildcard ./pd/*.cpp)
SV_COM_LIBS = -lpds_general_rv1126 -lcalibrate_radar -ladas_general_rk -ljpeg 
endif

ifeq ($(BOARD), ADA47V1)
SRCPPS = $(wildcard ./dmm/*.cpp)
SRCPPS += $(wildcard ./pd/*.cpp)
SRCPPS += $(wildcard ./apc/*.cpp)
SRCPPS += $(wildcard ./plug/*.cpp)
CFLAGS += -DUseOpflow
SV_COM_LIBS = -ldmm -lpds_general_rv1126 -lcalibrate_radar -lopticalflow_rk -ljpeg -lsqlite3
endif

ifeq ($(BOARD), ADA900V1)
SRCPPS = $(wildcard ./apc/*.cpp)
SV_COM_LIBS = -lpds_general_rv1126 -ljpeg
endif

ifeq ($(BOARD), HDW845V1)
SRCPPS = $(wildcard ./zoom/*.cpp)
SV_COM_LIBS =-lpds_general_rv1126 -lcalibrate_radar -ljpeg -ltrack
endif

SRCPPS += alg.cpp
# Link SV Common library
SV_COM_LIBS +=  -lalarm -lcjson -lsharefifo -llog -lstorage -lmsg -lcjson -lutils -lboard -lconfig -lmxml -luuid -lsafefunc -lthpool 

################## optional library ######################
ifneq ($(findstring -DMAKE_LED,$(CFLAGS) $(CPPFLAGS)), )
SV_COM_LIBS += -lled
endif

##########################################################

# Link other SV libs
ifeq ($(BOARD), ADA32V4)

else
OTHER_SV_LIBS	= -lrknn_api -lrga -lrockchip_mpp
ifneq ($(BOARD), $(findstring $(BOARD),ADA32V3,DMS51V1))
OTHER_SV_LIBS	+=  -lv4l2 -lv4lconvert -ldrm
endif
endif

SYSTEM_LIB	= -lssl -lcrypto -ldl -lrt -lpthread -lm -lstdc++ -lz

CFLAGS += -I$(INC_PATH)/libdrm -I$(INC_PATH)/libdrm/drm -I./include -I./include/rknn -I./include/ncnn -I$(INC_PATH)/cjson
CPPFLAGS = $(CFLAGS)

TARGET_BIN	= alg
COPY_TO_DIR = $(ROOT_PATH)

LIB_DEPEND	= $(COMP_DEPEND)
LD_FLAGS	+= -L$(LIB_PATH) -L$(TOP_LIB) 
LD_FLAGS	+= $(SV_COM_LIBS) $(OTHER_SV_LIBS) $(SYSTEM_LIB)

include $(BIN_AUTO_DEP_MK)

# vim:noet:sw=4:ts=4
