/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：msg_queue.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2014-09-03

文件功能描述: 定义共享消息队列功能函数

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <pthread.h>
#include <error.h>
#include <errno.h>

#include "print.h"
#include "msg_queue.h"

#define SLICE_POOL_KEY      0xaba99527  /* 碎片共享内存池键值 */
#define SLICE_MAGIC         0xabdacdcd  /* 碎片池魔术字值 */
#define SLICE_SHM_VIRADDR   0x80000000   /* 碎片共享内存映射的虚拟地址 */
#define SLICE_MAX_TYPE_NUM  11  /* 最大允许的碎片尺寸类型种数 */
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
#define SLICE_TYPE_NUM      11  /* 碎片尺寸类型种数 */
#elif defined(PLATFORM_NT98539)
#define SLICE_TYPE_NUM      11  /* 碎片尺寸类型种数 */
#else
#define SLICE_TYPE_NUM      8   /* 碎片尺寸类型种数 */
#endif
#define SLICE_STAT_USED     1   /* 碎片被占用 */
#define SLICE_STAT_FREE     0   /* 碎片空闲 */

/* 系统层消息结构定义 */
typedef struct tag_msgp_s
{
    ulng32      ulMsgType;      /* 消息类型，消息类型必须放在第一个。因为msgsnd函数会根据消息体的前四个字节（long）判断消息体的类型，消息类型是必须要有的且必须大于0，否则msgsnd函数会报错  */
    MSG_BUF_S   stUsrMsg;       /* 用户消息体 */
    uint8      *puAttachAddr;   /* 消息附加数据地址 */
    uint32      u32AttachSize;  /* 消息附加数据大小 */
} MSGP_S;

/* 内存碎片结点信息定义 */
typedef struct tag_SliceNode_S
{
    uint32  u32Stat;            /* 结点状态 */
    uint32  u32VirAddrOffset;   /* 不同进程共享内存指针地址不同,因此使用地址偏移较合适 */
    //void   *pVirAddr;           /* 结点数据虚拟地址 */
} SLICE_NODE_S;

/* 内存碎片统计结构定义 */
typedef struct tag_SliceInfo_S
{
    uint32  u32SliceSize;       /* 碎片心尺寸 */
    uint32  u32TotalNum;        /* 碎片总数 */
    uint32  u32UsedNum;         /* 占用的碎片数 */
    uint32  u32FreeNum;         /* 空闲的碎片数 */
    uint32  u32SliceTabOffset;  /* 不同进程共享内存指针地址不同,因此使用地址偏移较合适 */
    //SLICE_NODE_S *pstSliceTab;  /* 碎片表格 */
} SLICE_INFO_S;

/* 内存碎片池信息结构定义 */
typedef struct tag_SlicePool_S
{
    uint32  Magic;              /* 魔术字(某固定常数): 作为内存访问验证标识 */
    sint32  s32PoolId;          /* 内存碎片池ID */
    uint32  u32StartVirAddr;    /* 共享内存块起始虚拟地址 */
    pthread_mutex_t mutexLock;  /* 内存碎片池互斥锁 */
    uint32  u32TypeNum;         /* 碎片尺寸类型种数 */
    SLICE_INFO_S astSliceInfo[SLICE_MAX_TYPE_NUM];   /* 碎片统计表 */
} SLICE_POOL_S;

/* 内存碎片池配置结构定义 */
typedef struct tag_PoolCfg_S
{
    uint32  u32SliceSize;       /* 碎片尺寸 */
    uint32  u32SliceNum;        /* 碎片数目 */
} POOL_CFG_S;

/* 内存碎片池配置 (注意: 块大小必需由从小到大排序) */
static POOL_CFG_S m_stPoolCfg[SLICE_MAX_TYPE_NUM] =
{
    {32, MSG_SLICE_NUM_32},
    {64, MSG_SLICE_NUM_64},
    {128, MSG_SLICE_NUM_128},
    {256, MSG_SLICE_NUM_256},
    {512, MSG_SLICE_NUM_512},
    {1024, MSG_SLICE_NUM_1K},
    {2048, MSG_SLICE_NUM_2K},
    {4096, MSG_SLICE_NUM_4K},
#if (defined(PLATFORM_NT98539) || defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
    {8192, MSG_SLICE_NUM_8K},
    {16384, MSG_SLICE_NUM_16K},
    {256*1024, MSG_SLICE_NUM_256K},
#endif
};

typedef struct tag_SlicePoolInfo_S
{
    uint32 u32QueKey;
    sint32 s32QueId;
    uint32 u32PoolKey;
    SLICE_POOL_S *pstSlicePool;
}SLICE_POOL_INFO_S;

SLICE_POOL_INFO_S m_stSlicePoolInfo[POOL_TYPE_BUTT] = {0};

SLICE_POOL_S *m_pstSlicePool[POOL_TYPE_BUTT] = {0};     /* 内存碎片总控制头指针 */

SV_BOOL m_bMaster = SV_FALSE;           /* 是否为主进程 */

extern sint32 msgCreateSlicePool(uint32 Key, POOL_CFG_S *pstPoolCfg, uint32 u32TypeNum);
extern sint32 msgDestroySlicePool(uint32 Key);
extern sint32 msgOpenSlicePool(uint32 Key, POOL_CFG_S *pstPoolCfg);
extern void * msgMallocSlice(uint32 u32Size, uint32 *poffset, POOL_TYPE_E enPoolType);
//extern sint32 msgFreeSlice(void *pAddr);
extern sint32 msgFreeSlice(uint32 offset, POOL_TYPE_E enPoolType);
extern void* msgGetSlice(uint32 offset, POOL_TYPE_E enPoolType);
extern void msgPrintPoolInfo(POOL_TYPE_E enPoolType);

void MSG_SetMaster(SV_BOOL bMaster)
{
    m_bMaster = bMaster;
}

sint32 MSG_CreateQue(uint32 Key, uint32 u32QueLen, sint32 *ps32MsgQueId)
{
    sint32 s32Ret = 0;
    sint32 s32QueId = 0;
    SV_BOOL bCreate = SV_FALSE;
    struct msqid_ds stMsgQueAttr;
    POOL_TYPE_E enPoolType = (Key == LOG_QUE_KEY) ? POOL_TYPE_LOG : POOL_TYPE_MSG;

    if (0 == u32QueLen || u32QueLen > MSG_MAX_QUE_LEN)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == ps32MsgQueId)
    {
        return ERR_NULL_PTR;
    }

    s32QueId = msgget(Key, IPC_CREAT | IPC_EXCL | 0666);
    if (s32QueId < 0)
    {
        if (EEXIST == errno)
        {
#if (PLATFORM == PLATFORM_NT98539 || PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
            if(m_stSlicePoolInfo[enPoolType].pstSlicePool == NULL)
            {
                bCreate = SV_TRUE;
                s32QueId = msgget(Key, IPC_CREAT | 0666);
            }
            else
            {
#endif
                return ERR_EXIST;
#if (PLATFORM == PLATFORM_NT98539 || PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
            }
#endif
        }
        else if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    s32Ret = msgctl(s32QueId, IPC_STAT, &stMsgQueAttr);
    if (s32Ret < 0)
    {
        return ERR_NOMEM;
    }

    stMsgQueAttr.msg_qbytes = u32QueLen * (sizeof(MSGP_S) - sizeof(ulng32));
    s32Ret = msgctl(s32QueId, IPC_SET, &stMsgQueAttr);
    if (s32Ret < 0)
    {
        return ERR_NOMEM;
    }

    m_stSlicePoolInfo[enPoolType].u32QueKey = Key;
    m_stSlicePoolInfo[enPoolType].s32QueId = s32QueId;
    m_stSlicePoolInfo[enPoolType].u32PoolKey = s32QueId+100;

    s32Ret = msgCreateSlicePool(s32QueId+100, m_stPoolCfg, SLICE_TYPE_NUM);
    if (s32Ret < 0)
    {
        msgctl(s32QueId, IPC_RMID, NULL);
        return s32Ret;
    }

    *ps32MsgQueId = s32QueId;

    return SV_SUCCESS;
}

sint32 MSG_DestroyQue(sint32 s32MsgQueId)
{
    sint32 s32Ret = 0;

    s32Ret = msgDestroySlicePool(s32MsgQueId+100);
    if (s32Ret < 0)
    {
        return s32Ret;
    }

    s32Ret = msgctl(s32MsgQueId, IPC_RMID, NULL);
    if (s32Ret < 0)
    {
        if (EINVAL == errno || EIDRM == errno)
        {
            return ERR_UNEXIST;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

sint32 MSG_OpenQue(uint32 Key, sint32 *ps32MsgQueId)
{
    sint32 s32Ret = 0;
    sint32 s32QueId = 0;
    struct msqid_ds stMsgQueAttr;

    if (NULL == ps32MsgQueId)
    {
        return ERR_NULL_PTR;
    }

    s32QueId = msgget(Key, 0666);
    if (s32QueId < 0)
    {
        if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (ENOENT == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    // 暂时规避BSD进程报错
    s32Ret = msgOpenSlicePool(s32QueId+100, m_stPoolCfg);
    if (SV_SUCCESS != s32Ret)
    {
        printf("%s>%s:#%d, err=%#x\n", __FILE__, __FUNCTION__, __LINE__, s32Ret);
        if (ERR_UNEXIST == s32Ret)
        {
            s32Ret = msgCreateSlicePool(s32QueId+100, m_stPoolCfg, SLICE_TYPE_NUM);
            if (s32Ret < 0)
            {
                return s32Ret;
            }
        }
        else
        {
            return s32Ret;
        }
    }

    *ps32MsgQueId = s32QueId;

    return SV_SUCCESS;
}

sint32 MSG_Write(sint32 s32MsgQueId, MSG_BUF_S *pstMsgBuf, uint8 *puData, uint32 u32Size)
{
    sint32 s32Ret = 0;
    MSGP_S stMsgp;
    uint32 offset;
    uint8 *puSliceAddr = NULL;
    POOL_TYPE_E enPoolType = (s32MsgQueId == m_stSlicePoolInfo[POOL_TYPE_LOG].s32QueId) ? POOL_TYPE_LOG : POOL_TYPE_MSG;

    if (NULL == pstMsgBuf)
    {
        return ERR_NULL_PTR;
    }

    //printf("***u32Size = %d***\n", u32Size);
    if (NULL != puData && (0 == u32Size || u32Size > MSG_MAX_DATA_LEN))
    {
        printf("***Illegal Param! u32Size: %d***\n", u32Size);
        return ERR_ILLEGAL_PARAM;
    }
    //printf("write id: %d, op: %d\n", pstMsgBuf->u16DestId, pstMsgBuf->u16OpCode);

    if (NULL != puData)
    {
        puSliceAddr = (uint8 *)msgMallocSlice(u32Size, &offset, enPoolType);
        if (NULL == puSliceAddr)
        {
            printf("NULL == puSliceAddr\n");
            return ERR_NOMEM;
        }
        memcpy(puSliceAddr, puData, u32Size);
    }

    stMsgp.ulMsgType = (ulng32)pstMsgBuf->u16DestId;
    memcpy(&stMsgp.stUsrMsg, pstMsgBuf, sizeof(MSG_BUF_S));
    stMsgp.puAttachAddr = offset;
    stMsgp.u32AttachSize = u32Size;

    s32Ret = msgsnd(s32MsgQueId, (void *)&stMsgp, (sizeof(MSGP_S) - sizeof(ulng32)), IPC_NOWAIT);
    if (s32Ret < 0)
    {
        if (NULL != puSliceAddr)
        {
            (void)msgFreeSlice(offset, enPoolType);
        }

        if (EINVAL == errno || EIDRM == errno)
        {
            printf("%s>%s:#%d, err: %s, id=%d, type=%d\n", __FILE__, __FUNCTION__, __LINE__, strerror(errno), s32MsgQueId, stMsgp.ulMsgType);
            return ERR_UNEXIST;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else if (EAGAIN == errno)
        {
            return ERR_BUF_FULL;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

sint32 MSG_Read(sint32 s32MsgQueId, MSG_BUF_S *pstMsgBuf, uint8 *puData, uint32 *pu32Size)
{
    sint32 u32ReadSize;
    ulng32 ulMsgType;
    MSGP_S stMsgp;
    void *paddr;
    POOL_TYPE_E enPoolType = (s32MsgQueId == m_stSlicePoolInfo[POOL_TYPE_LOG].s32QueId) ? POOL_TYPE_LOG : POOL_TYPE_MSG;

    if (NULL == pstMsgBuf || NULL == puData || NULL == pu32Size)
    {
        printf("%s: line %d\n", __func__, __LINE__);
        return ERR_NULL_PTR;
    }

    ulMsgType = (ulng32)pstMsgBuf->u16DestId;
    u32ReadSize = msgrcv(s32MsgQueId, (void *)&stMsgp, (sizeof(MSGP_S) - sizeof(ulng32)), ulMsgType, 0);
    if (u32ReadSize < 0)
    {
        if (EINVAL == errno || EIDRM == errno)
        {
            printf("%s: line %d\n", __func__, __LINE__);
            return ERR_UNEXIST;
        }
        else if (EACCES == errno)
        {
            printf("%s: line %d\n", __func__, __LINE__);
            return ERR_NOT_PERM;
        }
        else if (ENOMSG == errno)
        {
            printf("%s: line %d\n", __func__, __LINE__);
            return ERR_BUF_EMPTY;
        }
        else
        {
            printf("%s: line %d\n", __func__, __LINE__);
            return SV_FAILURE;
        }
    }

    if (u32ReadSize != (sizeof(MSGP_S)-sizeof(ulng32)))
    {
        printf("%s: line %d\n", __func__, __LINE__);
        printf("readsize:%d, sizeof(MSGP_S):%d, sizeof(ulng32):%d\n", u32ReadSize, sizeof(MSGP_S), sizeof(ulng32));
        return ERR_MSG_DATAFALSE;
    }

    memcpy(pstMsgBuf, &stMsgp.stUsrMsg, sizeof(MSG_BUF_S));
    //printf("read id: %d, op: %d\n", pstMsgBuf->u16DestId, pstMsgBuf->u16OpCode);
    if (NULL != stMsgp.puAttachAddr && 0 != stMsgp.u32AttachSize)
    {
        void *paddr = msgGetSlice(stMsgp.puAttachAddr, enPoolType);
        memcpy(puData, paddr, stMsgp.u32AttachSize);
        *pu32Size = stMsgp.u32AttachSize;
        (void)msgFreeSlice(stMsgp.puAttachAddr, enPoolType);
    }
    else
    {
        *pu32Size = 0;
    }

    return SV_SUCCESS;
}

sint32 MSG_QueInfoQuery(sint32 s32MsgQueId, MSG_QUE_INFO_S *pstQueInfo)
{
    sint32 s32Ret = 0;
    struct msqid_ds stMsgQueAttr;

    if (NULL == pstQueInfo)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = msgctl(s32MsgQueId, IPC_STAT, &stMsgQueAttr);
    if (s32Ret < 0)
    {
        if (EINVAL == errno || EIDRM == errno)
        {
            return ERR_UNEXIST;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pstQueInfo->u32QueLen = stMsgQueAttr.msg_qbytes / (sizeof(MSGP_S)-sizeof(ulng32));
    pstQueInfo->u32UsedMsgs = stMsgQueAttr.msg_qnum;
    pstQueInfo->u32FreeMsgs = pstQueInfo->u32QueLen - pstQueInfo->u32UsedMsgs;

    return SV_SUCCESS;
}

sint32 MSG_QueInfoPrint(sint32 s32MsgQueId)
{
    sint32 s32Ret = 0;
    struct msqid_ds stMsgQueAttr;
    MSG_QUE_INFO_S stQueInfo;
    POOL_TYPE_E enPoolType = (s32MsgQueId == m_stSlicePoolInfo[POOL_TYPE_LOG].s32QueId) ? POOL_TYPE_LOG : POOL_TYPE_MSG;

    s32Ret = msgctl(s32MsgQueId, IPC_STAT, &stMsgQueAttr);
    if (s32Ret < 0)
    {
        if (EINVAL == errno || EIDRM == errno)
        {
            return ERR_UNEXIST;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    stQueInfo.u32QueLen = stMsgQueAttr.msg_qbytes / (sizeof(MSGP_S)-sizeof(ulng32));
    stQueInfo.u32UsedMsgs = stMsgQueAttr.msg_qnum;
    stQueInfo.u32FreeMsgs = stQueInfo.u32QueLen - stQueInfo.u32UsedMsgs;

    printf("\n======= Que ID=%u Info =====================================\n", s32MsgQueId);
    printf("QueLen:%u \nUsedMsgs:%u \nFreeMsgs:%u\n", stQueInfo.u32QueLen, stQueInfo.u32UsedMsgs, stQueInfo.u32FreeMsgs);
    msgPrintPoolInfo(enPoolType);

    return SV_SUCCESS;
}

/*================================ 内部函数定义 =====================================*/

/******************************************************************************
 * 函数功能: 创建消息附加数据内存碎片池
 * 输入参数: Key --- 内存碎片池的键值: 作为打开内存碎片池的标识
             pstPoolCfg --- 碎片池配置表
             u32TypeNum --- 碎片类型数 [1, SLICE_MAX_TYPE_NUM]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_MSG_NULL_PTR - 传入指针为NULL
             ERR_MSG_ILLEGAL_PARAM - 碎片类型数超出合法范围
             ERR_MSG_NOMEM - 内存不足
             ERR_MSG_EXIST - 内存池已存在
             ERR_MSG_BADADDR - 地址映射错误
             SV_FAILURE - 其它错误
 * 注意    : 对同一个Key值, 该函数只能被调用一次。
 *****************************************************************************/
sint32 msgCreateSlicePool(uint32 Key, POOL_CFG_S *pstPoolCfg, uint32 u32TypeNum)
{
    sint32 s32Ret = 0, i, j;
    uint32 u32TypeCnt = 0;
    uint32 u32ShmTotalSize = 0;
    uint32 u32SliceTotalNum = 0;
    uint32 u32DataTotalSize = 0;
    uint32 u32DataOffset = 0;
    uint32 u32InfoOffset = 0;
    sint32 s32ShmId = 0;
    char  *pStartVirAddr = NULL;
    SLICE_POOL_S stSlicePool;
    SLICE_POOL_S *pstSlicePool = NULL;
    SLICE_INFO_S *pstSliceInfo = NULL;
    pthread_mutexattr_t mattr;
    POOL_TYPE_E enPoolType = (Key == m_stSlicePoolInfo[POOL_TYPE_LOG].u32PoolKey) ? POOL_TYPE_LOG : POOL_TYPE_MSG;

    if (NULL == pstPoolCfg)
    {
        return ERR_NULL_PTR;
    }

    if (0 == u32TypeNum || u32TypeNum > SLICE_MAX_TYPE_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    memset(&stSlicePool, 0x0, sizeof(SLICE_POOL_S));
    stSlicePool.Magic = SLICE_MAGIC;
    for (i = 0; i < u32TypeNum; i++)
    {
        if (0 == pstPoolCfg[i].u32SliceSize || 0 ==pstPoolCfg[i].u32SliceNum)
        {
            continue;
        }

        stSlicePool.astSliceInfo[u32TypeCnt].u32SliceSize = pstPoolCfg[i].u32SliceSize;
        stSlicePool.astSliceInfo[u32TypeCnt].u32TotalNum = pstPoolCfg[i].u32SliceNum;
        stSlicePool.astSliceInfo[u32TypeCnt].u32UsedNum = 0;
        stSlicePool.astSliceInfo[u32TypeCnt].u32FreeNum = pstPoolCfg[i].u32SliceNum;

        u32SliceTotalNum += pstPoolCfg[i].u32SliceNum;
        u32DataTotalSize += pstPoolCfg[i].u32SliceSize * pstPoolCfg[i].u32SliceNum;
        u32TypeCnt++;
    }

    stSlicePool.u32TypeNum = u32TypeCnt;

    /* 共享内存分布: 内存池总信息头 + 碎片结点信息 + 数据空间 */
    u32ShmTotalSize = sizeof(SLICE_POOL_S) + u32SliceTotalNum * sizeof(SLICE_NODE_S) + u32DataTotalSize;
    u32InfoOffset = sizeof(SLICE_POOL_S);
    u32DataOffset = sizeof(SLICE_POOL_S) + u32SliceTotalNum * sizeof(SLICE_NODE_S);

#if (PLATFORM != PLATFORM_NT98539 && PLATFORM != PLATFORM_RV1126 && PLATFORM != PLATFORM_RV1106)
    s32ShmId = shmget(Key, u32ShmTotalSize, IPC_CREAT | IPC_EXCL | 0600);
#else
    s32ShmId = shmget(Key, u32ShmTotalSize, IPC_CREAT | 0600);      // IPC_EXCL表示在创建时如果已经存在则直接报错，RK的alg进程只需要获取共享内存就行，所以这里不需要加PC_EXCL标志
#endif
    if ( s32ShmId < 0 )
    {
        printf("%s>%s:#%d, err: %s, key=%d\n", __FILE__, __FUNCTION__, __LINE__, strerror(errno), Key);
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pStartVirAddr = (char *)shmat(s32ShmId, NULL, 0);
    if (NULL == pStartVirAddr)
    {
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }

    if (m_bMaster)
    {
        memset(pStartVirAddr, 0x0, u32ShmTotalSize);
    }
    pstSlicePool = (SLICE_POOL_S *)pStartVirAddr;
    memcpy(pstSlicePool, &stSlicePool, sizeof(SLICE_POOL_S));
    pstSlicePool->s32PoolId = s32ShmId;
    pstSlicePool->u32StartVirAddr = (uint32)pStartVirAddr;

    printf("%s>%s:#%d, key=%d, shmId=%d, Magic=%#x, s32PoolId=%d, u32StartVirAddr=%#x\n", __FILE__, __FUNCTION__, __LINE__, Key, s32ShmId, pstSlicePool->Magic, pstSlicePool->s32PoolId, pstSlicePool->u32StartVirAddr);
    s32Ret = pthread_mutexattr_init(&mattr);
    s32Ret = pthread_mutexattr_setpshared(&mattr, PTHREAD_PROCESS_SHARED);

    s32Ret = pthread_mutex_init(&pstSlicePool->mutexLock, &mattr);
    if (0 != s32Ret)
    {
        return SV_FAILURE;
    }

    SLICE_NODE_S *pstSliceTab;
    for (i = 0; i < pstSlicePool->u32TypeNum; i++)
    {
        pstSliceInfo = &pstSlicePool->astSliceInfo[i];
        pstSliceInfo->u32SliceTabOffset = u32InfoOffset;    // 循环填充完SLICE_INFO_S的u32SliceTabOffset，该结构体内其他成员已经在前面填充好了
        pstSliceTab = (SLICE_NODE_S *)(pStartVirAddr + pstSliceInfo->u32SliceTabOffset);
        u32InfoOffset += pstSliceInfo->u32TotalNum * sizeof(SLICE_NODE_S);      // u32InfoOffset偏移的是内存碎片块信息的占用空间

        /* 初始化每一个内存碎片的状态和地址 */
        for(j = 0; j < pstSliceInfo->u32TotalNum; j++)
        {
            pstSliceTab[j].u32Stat = SLICE_STAT_FREE;
            pstSliceTab[j].u32VirAddrOffset = u32DataOffset;
            u32DataOffset += pstSliceInfo->u32SliceSize;                        // u32DataOffset偏移的是内存碎片块实际数据的占用空间
        }
    }

    m_stSlicePoolInfo[enPoolType].pstSlicePool = pstSlicePool;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁消息附加数据内存碎片池
 * 输入参数: Key --- 内存碎片池的键值: 作为打开内存碎片池的标识

 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_MSG_UNEXIST - 内存池不存在
             ERR_MSG_NOT_PERM - 不允许操作
             ERR_MSG_DATAFALSE - 内存池数据错误
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 msgDestroySlicePool(uint32 Key)
{
    sint32 s32Ret = 0;
    POOL_TYPE_E enPoolType = (Key == m_stSlicePoolInfo[POOL_TYPE_LOG].u32PoolKey) ? POOL_TYPE_LOG : POOL_TYPE_MSG;
    SLICE_POOL_S *pstSlicePool = m_stSlicePoolInfo[enPoolType].pstSlicePool;

    (void)Key;
    if (NULL == pstSlicePool)
    {
        return ERR_UNEXIST;
    }

    if (SLICE_MAGIC != pstSlicePool->Magic)
    {
        return ERR_MSG_DATAFALSE;
    }

    s32Ret = pthread_mutex_destroy(&pstSlicePool->mutexLock);
    if (0 != s32Ret)
    {
        return SV_FAILURE;
    }

    s32Ret = shmctl(pstSlicePool->s32PoolId, IPC_RMID, NULL);
    if (s32Ret < 0)
    {
        if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else if(EPERM == errno)
        {
            return ERR_NOT_PERM;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    s32Ret = shmdt((void *)pstSlicePool->u32StartVirAddr);
    if (s32Ret < 0)
    {
        if (EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    memset(&m_stSlicePoolInfo[enPoolType], 0, sizeof(m_stSlicePoolInfo));
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 打开消息附加数据内存碎片池
 * 输入参数: Key --- 内存碎片池的键值: 作为打开内存碎片池的标识

 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_MSG_UNEXIST - 内存池不存在
             ERR_MSG_NOMEM - 内存不足
             ERR_MSG_UNEXIST - 内存池不存在
             ERR_MSG_BADADDR - 地址映射错误
             ERR_MSG_DATAFALSE - 内存池数据错误
             ERR_MSG_NOT_PERM - 无访问权限
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 msgOpenSlicePool(uint32 Key, POOL_CFG_S *pstPoolCfg)
{
    sint32 s32Ret = 0, i;
    sint32 s32ShmId = 0;
    uint32 u32TypeCnt = 0;
    uint32 u32ShmTotalSize = 0;
    uint32 u32SliceTotalNum = 0;
    uint32 u32DataTotalSize = 0;
    char  *pStartVirAddr = NULL;
    SLICE_POOL_S *pstSlicePool = NULL;
    SLICE_INFO_S *pstSliceInfo = NULL;
    POOL_TYPE_E enPoolType = (Key == m_stSlicePoolInfo[POOL_TYPE_LOG].u32PoolKey) ? POOL_TYPE_LOG : POOL_TYPE_MSG;

    if (NULL != m_stSlicePoolInfo[enPoolType].pstSlicePool && SLICE_MAGIC == m_stSlicePoolInfo[enPoolType].pstSlicePool->Magic)
    {
        return SV_SUCCESS;
    }

    if (NULL == pstPoolCfg)
    {
        return ERR_NULL_PTR;
    }

    for (i = 0; i < SLICE_TYPE_NUM; i++)
    {
        if (0 == pstPoolCfg[i].u32SliceSize || 0 ==pstPoolCfg[i].u32SliceNum)
        {
            continue;
        }

        u32SliceTotalNum += pstPoolCfg[i].u32SliceNum;
        u32DataTotalSize += pstPoolCfg[i].u32SliceSize * pstPoolCfg[i].u32SliceNum;
    }

    /* 共享内存分布: 内存池总信息头 + 碎片结点信息 + 数据空间 */
    u32ShmTotalSize = sizeof(SLICE_POOL_S) + u32SliceTotalNum * sizeof(SLICE_NODE_S) + u32DataTotalSize;

#if (PLATFORM != PLATFORM_NT98539 && PLATFORM != PLATFORM_RV1126 && PLATFORM != PLATFORM_RV1106)
    s32ShmId = shmget(Key, u32ShmTotalSize, IPC_CREAT | IPC_EXCL | 0600);
#else
    s32ShmId = shmget(Key, u32ShmTotalSize, IPC_CREAT | 0600);      // IPC_EXCL表示在创建时如果已经存在则直接报错，RK的alg进程只需要获取共享内存就行，所以这里不需要加PC_EXCL标志
#endif
    if (s32ShmId < 0)
    {
        printf("%s>%s:#%d, err: %s, key=%d\n", __FILE__, __FUNCTION__, __LINE__, strerror(errno), Key);
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if(ENOENT == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pStartVirAddr = (char *)shmat(s32ShmId, NULL, 0);
    if (NULL == pStartVirAddr)
    {
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }

    pstSlicePool = (SLICE_POOL_S *)pStartVirAddr;
    if (SLICE_MAGIC != pstSlicePool->Magic)
    {
        printf("%s>%s:#%d, shmId=%d, pStartVirAddr=%#x, Magic=%#x, s32PoolId=%d, u32StartVirAddr=%#x\n", __FILE__, __FUNCTION__, __LINE__, s32ShmId, pStartVirAddr, pstSlicePool->Magic, pstSlicePool->s32PoolId, pstSlicePool->u32StartVirAddr);
        return ERR_UNEXIST;
    }

    if (pstSlicePool->u32TypeNum > SLICE_MAX_TYPE_NUM)
    {
        return ERR_MSG_DATAFALSE;
    }

#if 0
    for (i = 0; i < pstSlicePool->u32TypeNum; i++)
    {
        pstSliceInfo = &pstSlicePool->astSliceInfo[i];
        u32ShmTotalSize += pstSliceInfo->u32SliceSize * pstSliceInfo->u32TotalNum;
    }

    /* 删除原映射内存, 再重新映射u32ShmTotalSize 大小的内存 */
    s32Ret = shmctl(s32ShmId, IPC_RMID, NULL);
    if (s32Ret < 0)
    {
        printf("%s>%s:#%d, err: %s, key=%d, shmId=%d\n", __FILE__, __FUNCTION__, __LINE__, strerror(errno), Key, s32ShmId);
        if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else if(EPERM == errno)
        {
            return ERR_NOT_PERM;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    s32ShmId = shmget(Key, u32ShmTotalSize, IPC_CREAT | 0600 ) ;
    if ( s32ShmId < 0 )
    {
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pStartVirAddr = (char *)shmat(s32ShmId, NULL, 0);
    if (NULL == pStartVirAddr)
    {
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }
#endif

    m_stSlicePoolInfo[enPoolType].pstSlicePool = pstSlicePool;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 申请消息附加数据内存碎片
 * 输入参数: u32Size --- 内存碎片大小 (0, 配置的最大碎片尺寸]

 * 输出参数: poffset --- 内存碎片在共享内存中的偏移量
 * 返回值  : 内存碎片地址

 * 注意    : 无
 *****************************************************************************/
void * msgMallocSlice(uint32 u32Size, uint32 *poffset, POOL_TYPE_E enPoolType)
{
    sint32 s32Ret = 0, i, j;
    void *pvAddr = NULL;
    SLICE_POOL_S *pstSlicePool = m_stSlicePoolInfo[enPoolType].pstSlicePool;
    SLICE_INFO_S *pstSliceInfo = NULL;

    if (0 == u32Size || NULL == pstSlicePool)
    {
        printf("u32Size = %d, NULL == pstSlicePool\n", u32Size);
        return NULL;
    }

    s32Ret = pthread_mutex_lock(&pstSlicePool->mutexLock);
    if (s32Ret < 0)
    {
        printf("pthread_mutex_lock failed\n");
        return NULL;
    }
    SLICE_NODE_S *pstSliceTab;
    for (i = 0; i < pstSlicePool->u32TypeNum; i++)
    {
        pstSliceInfo = &pstSlicePool->astSliceInfo[i];
        if (pstSliceInfo->u32SliceSize < u32Size)
        {
            continue;
        }

        if (0 == pstSliceInfo->u32FreeNum)
        {
            //print_level(SV_ERROR, "typeNum = %d, u32FreeNum: %d, no free space\n", i, pstSliceInfo->u32FreeNum);
            continue;
        }

        pstSliceTab = (SLICE_NODE_S *)((void*)pstSlicePool + pstSliceInfo->u32SliceTabOffset);
        for (j = 0; j < pstSliceInfo->u32TotalNum; j++)
        {
            if (SLICE_STAT_FREE == pstSliceTab[j].u32Stat)
            {
                pvAddr = ((void*)pstSlicePool + pstSliceTab[j].u32VirAddrOffset);
                *poffset = pstSliceTab[j].u32VirAddrOffset;
                pstSliceTab[j].u32Stat = SLICE_STAT_USED;
                break;
            }
        }

        if (j >= pstSliceInfo->u32TotalNum)
        {
            printf("u32TypeNum = %d, u32TotalNum = %d, no free space\n", i, pstSliceInfo->u32TotalNum);
        }

        pstSliceInfo->u32UsedNum++;
        pstSliceInfo->u32FreeNum--;
        break;
    }

    if (i >= pstSlicePool->u32TypeNum)
    {
        printf("u32TypeNum = %d, u32TotalNum = %d, need size: %d, no free space\n", i, pstSlicePool->u32TypeNum, u32Size);
    }

    s32Ret = pthread_mutex_unlock(&pstSlicePool->mutexLock);
    if (s32Ret < 0)
    {
        printf("pthread_mutex_unlock failed\n");
        return NULL;
    }

    return pvAddr;
}

/******************************************************************************
 * 函数功能: 获取消息附加数据内存碎片
 * 输入参数: u32Size --- 内存碎片大小 (0, 配置的最大碎片尺寸]

 * 输出参数: 无
 * 返回值  : 内存碎片地址

 * 注意    : 无
 *****************************************************************************/
void * msgGetSlice(uint32 offset, POOL_TYPE_E enPoolType)
{
    sint32 s32Ret = 0, i, j;
    void *pvAddr = NULL;
    SLICE_POOL_S *pstSlicePool = m_stSlicePoolInfo[enPoolType].pstSlicePool;
    SLICE_INFO_S *pstSliceInfo = NULL;
    if (NULL == pstSlicePool)
    {
        return NULL;
    }

    return (void*)pstSlicePool + offset;
}

/******************************************************************************
 * 函数功能: 释放消息附加数据内存碎片
 * 输入参数: pAddr --- 内存碎片地址
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_MSG_BADADDR - 地址错误
             ERR_MSG_UNEXIST - 内存池不存在
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 msgFreeSlice(uint32 offset, POOL_TYPE_E enPoolType)
{
    sint32 s32Ret = 0, i, j;
    sint32 s32Index = 0;
    uint32 u32TotalSliceNum = 0;
    SLICE_POOL_S *pstSlicePool = m_stSlicePoolInfo[enPoolType].pstSlicePool;
    SLICE_INFO_S *pstSliceInfo = NULL;
    SLICE_NODE_S *pstSliceTab = NULL;

    if (NULL == offset)
    {
        return ERR_BADADDR;
    }

    if (NULL == pstSlicePool)
    {
        return ERR_UNEXIST;
    }

    for (i = 0; i < pstSlicePool->u32TypeNum; i++)
    {
        u32TotalSliceNum += pstSlicePool->astSliceInfo[i].u32TotalNum;
    }

    s32Ret = pthread_mutex_lock(&pstSlicePool->mutexLock);
    if (s32Ret < 0)
    {
        return SV_FAILURE;
    }

    pstSliceTab = (SLICE_NODE_S *)((void*)pstSlicePool + pstSlicePool->astSliceInfo[0].u32SliceTabOffset);
    for (i = 0; i < u32TotalSliceNum; i++)
    {
        if (pstSliceTab[i].u32VirAddrOffset == offset)
        {
            pstSliceTab[i].u32Stat = SLICE_STAT_FREE;
            break;
        }
    }

    if (i >= u32TotalSliceNum)
    {
        s32Ret = pthread_mutex_unlock(&pstSlicePool->mutexLock);
        if (s32Ret < 0)
        {
            return SV_FAILURE;
        }
        return ERR_BADADDR;
    }

    s32Index = i;
    for (i = 0; i < pstSlicePool->u32TypeNum; i++)
    {
        if (s32Index < pstSlicePool->astSliceInfo[i].u32TotalNum)
        {
            pstSlicePool->astSliceInfo[i].u32FreeNum++;
            pstSlicePool->astSliceInfo[i].u32UsedNum--;
            break;
        }
        s32Index -= pstSlicePool->astSliceInfo[i].u32TotalNum;
    }

    s32Ret = pthread_mutex_unlock(&pstSlicePool->mutexLock);
    if (s32Ret < 0)
    {
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 查询消息附加数据内存碎片池信息
 * 输入参数: 无

 * 输出参数: pstSlicePoolInfo --- 内存碎片池信息
 * 返回值  : 无

 * 注意    : 无
 *****************************************************************************/
sint32 msgQueryPoolInfo(SLICE_POOL_S *pstSlicePoolInfo, POOL_TYPE_E enPoolType)
{
    SLICE_POOL_S *pstSlicePool = m_stSlicePoolInfo[enPoolType].pstSlicePool;

    if (NULL == pstSlicePool)
    {
        return ERR_UNEXIST;
    }

    memcpy(pstSlicePoolInfo, pstSlicePool, sizeof(SLICE_POOL_S));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 打印消息附加数据内存碎片池信息
 * 输入参数: 无

 * 输出参数: pstSlicePoolInfo --- 内存碎片池信息
 * 返回值  : 无

 * 注意    : 无
 *****************************************************************************/
void msgPrintPoolInfo(POOL_TYPE_E enPoolType)
{
    sint32 i;
    SLICE_POOL_S *pstSlicePool = m_stSlicePoolInfo[enPoolType].pstSlicePool;
    SLICE_NODE_S *pstSliceTab = NULL;
    if (NULL == pstSlicePool)
    {
        return;
    }

    printf("\n======= Pool ID=%u | VirAddr=%#x ==============================\n", pstSlicePool->s32PoolId, pstSlicePool->u32StartVirAddr);
    printf("Size    TotalNum    UsedNum    FreeNum   TabAddr\n");
    for (i = 0; i < pstSlicePool->u32TypeNum; i++)
    {
        pstSliceTab = (SLICE_NODE_S *)((void*)pstSlicePool + pstSlicePool->astSliceInfo[i].u32SliceTabOffset);
        printf("%-8u    %-8u    %-7u    %-7u   %#x\n", pstSlicePool->astSliceInfo[i].u32SliceSize, pstSlicePool->astSliceInfo[i].u32TotalNum, \
                    pstSlicePool->astSliceInfo[i].u32UsedNum, pstSlicePool->astSliceInfo[i].u32FreeNum, (uint32)pstSliceTab);
    }
}

/*================================ 内部函数定义 =====================================*/

