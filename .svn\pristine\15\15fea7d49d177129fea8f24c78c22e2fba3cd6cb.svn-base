#ifndef _GDFONTT_H_
#define _GDFONTT_H_ 1

#ifdef __cplusplus
extern "C"
{
#endif

/*
	This is a header file for gd font, generated using
	bdftogd version 0.5 by <PERSON>, ad<PERSON><PERSON>@fi.muni.cz
	from bdf font
	-Misc-Fixed-Medium-R-Normal--8-80-75-75-C-50-ISO8859-2
	at Thu Jan  8 13:49:54 1998.
	The original bdf was holding following copyright:
	"Libor Skarvada, <EMAIL>"
 */

#include "gd.h"

extern BGD_EXPORT_DATA_PROT gdFontPtr gdFontTiny;
BGD_DECLARE(gdFontPtr) gdFontGetTiny(void);

#ifdef __cplusplus
}
#endif

#endif
