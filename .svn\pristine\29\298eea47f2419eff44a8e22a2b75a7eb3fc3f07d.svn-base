##### hostapd configuration file ##############################################

interface=wlan0
ctrl_interface=/var/hostapd
ssid=WFC-12345678
channel=6
wpa=2
wpa_passphrase=88888888
bridge=br0
ignore_broadcast_ssid=0


##### Wi-Fi Protected Setup (WPS) #############################################

eap_server=1

# WPS state
# 0 = WPS disabled (default)
# 1 = WPS enabled, not configured
# 2 = WPS enabled, configured
wps_state=2

uuid=12345678-9abc-def0-1234-56789abcdef0

# Device Name
# User-friendly description of device; up to 32 octets encoded in UTF-8
device_name=RTL8192CU

# Manufacturer
# The manufacturer of the device (up to 64 ASCII characters)
manufacturer=Realtek

# Model Name
# Model of the device (up to 32 ASCII characters)
model_name=RTW_SOFTAP

# Model Number
# Additional device description (up to 32 ASCII characters)
model_number=WLAN_CU

# Serial Number
# Serial number of the device (up to 32 characters)
serial_number=12345

# Primary Device Type
# Used format: <categ>-<OUI>-<subcateg>
# categ = Category as an integer value
# OUI = OUI and type octet as a 4-octet hex-encoded value; 0050F204 for
#       default WPS OUI
# subcateg = OUI-specific Sub Category as an integer value
# Examples:
#   1-0050F204-1 (Computer / PC)
#   1-0050F204-2 (Computer / Server)
#   5-0050F204-1 (Storage / NAS)
#   6-0050F204-1 (Network Infrastructure / AP)
device_type=6-0050F204-1

# OS Version
# 4-octet operating system version number (hex string)
os_version=01020300

# Config Methods
# List of the supported configuration methods
config_methods=label display push_button keypad


##### default configuration #######################################

driver=rtl871xdrv
beacon_int=100
hw_mode=g
ieee80211n=0
wme_enabled=1
ht_capab=[SHORT-GI-20]
wpa_key_mgmt=WPA-PSK
wpa_pairwise=CCMP
max_num_sta=8
wpa_group_rekey=86400

