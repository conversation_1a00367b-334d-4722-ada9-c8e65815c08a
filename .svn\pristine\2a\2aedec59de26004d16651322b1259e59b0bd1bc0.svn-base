#
# Link all library, and build the final excutable file
#

include ../../Makefile.param

# link pthread and math library
SYSTEM_LIB	= -lpthread -lm 

# Link SV Common library
SV_COM_LIBS = -lmsg

# Link other SV libs
OTHER_SV_LIBS 	=  

CPPFLAGS += $(CFLAGS)

TARGET_BIN	= msgread
LIB_DEPEND	= $(COMP_DEPEND)
LD_FLAGS	+=$(SYSTEM_LIB) $(OTHER_SV_LIBS) $(SV_COM_LIBS)

COPY_TO_DIR = $(BIN_PATH)
include $(BIN_AUTO_DEP_MK)

# vim:noet:sw=4:ts=4

