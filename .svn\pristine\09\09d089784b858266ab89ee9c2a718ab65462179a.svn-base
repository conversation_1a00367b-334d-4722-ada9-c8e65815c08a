#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <unistd.h>
#include <signal.h>

#include "common.h"
#include "print.h"
#include "config.h"
#include "autoUpdate.h"
#include "CUnit.h"
#include "Concurrent.h"

#if (defined(BOARD_WFCR10S1) || defined(BOARD_WFCR10S1LOS) || defined(BOARD_WFCR20S1) || defined(BOARD_WFCR20S1LOS) || defined(BOARD_WFCR20S2))
extern SV_BOOL mbIsMcuExist;
#endif
extern void pktPrintFileInfo(PACKET_INFO_S *pstPacketInfo);
extern sint32 updateMkdirp(char *pszPath);
extern sint32 updateSyncOldParams();
/***************************************************************
*-# 用例编号: itest_LOG_Init_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_updateInit_001()
{
    sint32 s32Ret = 0;

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = updateStart();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    
    print_level(SV_DEBUG, "updateInit done.\n");
    sleep_ms(3000);

    s32Ret = updateStop();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_updateMkdirp_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_updateMkdirp_001()
{
    sint32 s32Ret = 0;
    char *pstPath = "/var/a/b//c/d";

    s32Ret = updateMkdirp(pstPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_pktFindUpdatePacket_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_pktFindUpdatePacket_001()
{
    sint32 s32Ret = 0;
    char szFilePath[64];
    char *pszBootPath = "/boot";

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket(pszBootPath, szFilePath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szFilePath);

    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_pktDecryption_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_pktDecryption_001()
{
    sint32 s32Ret = 0;
    char szPacketPath[64];
    char szTargzPath[64];
    char *pszBootPath = "/boot";

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket(pszBootPath, szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {
        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    }

    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_pktUntarFile_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_pktUntarFile_001()
{
    sint32 s32Ret = 0;
    char szPacketPath[64];
    char szTargzPath[64];
    char szUntarPath[64];
    char *pszBootPath = "/boot";

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket(pszBootPath, szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {
        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        strcpy(szUntarPath, "/var");
        s32Ret = pktUntarFile(szTargzPath, szUntarPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    }

    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_pktParseMd5check_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_pktParseMd5check_001()
{
    sint32 s32Ret = 0;
    char szPacketPath[64];
    char szTargzPath[64];
    char szUntarPath[64];
    PACKET_INFO_S stPacketInfo = {0};

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket("/boot", szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {
        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        strcpy(szUntarPath, "/var");
        s32Ret = pktUntarFile(szTargzPath, szUntarPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        pktPrintFileInfo(&stPacketInfo);
    }

    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_pktCheckPacketMd5_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_pktCheckPacketMd5_001()
{
    sint32 s32Ret = 0;
    char szPacketPath[64];
    char szTargzPath[64];
    char szUntarPath[64];
    PACKET_INFO_S stPacketInfo = {0};

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket("/boot", szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {
        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        strcpy(szUntarPath, "/var/");
        s32Ret = pktUntarFile(szTargzPath, szUntarPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        pktPrintFileInfo(&stPacketInfo);
        s32Ret = pktCheckPacketMd5(UNTAR_DIR_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    }

    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_updateComparePacketWithSystem_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_updateComparePacketWithSystem_001()
{
    sint32 s32Ret = 0;
    char szPacketPath[64];
    char szTargzPath[64];
    char szUntarPath[64];
    PACKET_INFO_S stPacketInfo = {0};

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket("/boot", szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {
        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        strcpy(szUntarPath, "/var");
        s32Ret = pktUntarFile(szTargzPath, szUntarPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        pktPrintFileInfo(&stPacketInfo);
        s32Ret = pktCheckPacketMd5(UNTAR_DIR_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = updateComparePacketWithSystem(&stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, 1);
    }

    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_updateComparePacketWithSystem_002
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_updateComparePacketWithSystem_002()
{
    sint32 s32Ret = 0;
    char szPacketPath[64];
    char szTargzPath[64];
    char szUntarPath[64];
    PACKET_INFO_S stPacketInfo = {0};

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket("/boot", szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {

#if (defined(BOARD_WFCR10S1) || defined(BOARD_WFCR10S1LOS) || defined(BOARD_WFCR20S1) || defined(BOARD_WFCR20S1LOS) || defined(BOARD_WFCR20S2))
        s32Ret = mcuUpdateInit();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mcuUpdateInit failed.\n");
            return;
        }
        
        s32Ret = mcuGotoBootloaderMode();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "mcuGotoBootloaderMode failed.\n");
            return;
        }
        mbIsMcuExist = SV_TRUE;
#endif

        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        strcpy(szUntarPath, "/var");
        s32Ret = pktUntarFile(szTargzPath, szUntarPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        pktPrintFileInfo(&stPacketInfo);
        s32Ret = pktCheckPacketMd5(UNTAR_DIR_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = updateComparePacketWithSystem(&stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, 0x3);
    }

    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_updateOverwritePacketToSystem_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_updateOverwritePacketToSystem_001()
{
    sint32 s32Ret = 0;
    char szPacketPath[64];
    char szTargzPath[64];
    char szUntarPath[64];
    PACKET_INFO_S stPacketInfo = {0};

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    //s32Ret = updateStart();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket("/boot", szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {
        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        strcpy(szUntarPath, "/var");
        s32Ret = pktUntarFile(szTargzPath, szUntarPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        pktPrintFileInfo(&stPacketInfo);
        s32Ret = pktCheckPacketMd5(UNTAR_DIR_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = updateComparePacketWithSystem(&stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, 1);
        s32Ret = updateOverwritePacketToSystem(UNTAR_DIR_PATH, &stPacketInfo, s32Ret);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = updateComparePacketWithSystem(&stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, 0);
    }

    //s32Ret = updateStop();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

/***************************************************************
*-# 用例编号: itest_updateOverwritePacketToSystem_002
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_updateOverwritePacketToSystem_002()
{
    sint32 s32Ret = 0;
    SV_BOOL bIsMcuUpdate = SV_FALSE;
    char szPacketPath[64];
    char szTargzPath[64];
    char szUntarPath[64];
    PACKET_INFO_S stPacketInfo = {0};

    s32Ret = updateInit();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    //s32Ret = updateStart();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = pktFindUpdatePacket("/boot", szPacketPath);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    print_level(SV_DEBUG, "szFilePath: %s\n", szPacketPath);
    if (SV_SUCCESS == s32Ret)
    {
#if (defined(BOARD_WFCR10S1) || defined(BOARD_WFCR10S1LOS) || defined(BOARD_WFCR20S1) || defined(BOARD_WFCR20S1LOS) || defined(BOARD_WFCR20S2))
        s32Ret = mcuUpdateInit();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mcuUpdateInit failed.\n");
            return;
        }
        
        s32Ret = mcuGotoBootloaderMode();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_WARN, "mcuGotoBootloaderMode failed.\n");
            return;
        }
        mbIsMcuExist = SV_TRUE;
#endif
        strcpy(szTargzPath, DECRYPTION_TARGZ_PATH);
        s32Ret = pktDecryption(szPacketPath, szTargzPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        strcpy(szUntarPath, "/var");
        s32Ret = pktUntarFile(szTargzPath, szUntarPath);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = pktParseMd5check(MD5CHECK_FILE_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        pktPrintFileInfo(&stPacketInfo);
        s32Ret = pktCheckPacketMd5(UNTAR_DIR_PATH, &stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = updateComparePacketWithSystem(&stPacketInfo);
        if (0 != (s32Ret&0x2))
        {
            bIsMcuUpdate = SV_TRUE;
        }
        CU_ASSERT_EQUAL(s32Ret, 0x3);
        s32Ret = updateOverwritePacketToSystem(UNTAR_DIR_PATH, &stPacketInfo, s32Ret);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        s32Ret = updateComparePacketWithSystem(&stPacketInfo);
        CU_ASSERT_EQUAL(s32Ret, 0);
#if (defined(BOARD_WFCR10S1) || defined(BOARD_WFCR10S1LOS) || defined(BOARD_WFCR20S1) || defined(BOARD_WFCR20S1LOS) || defined(BOARD_WFCR20S2))        
        if (!bIsMcuUpdate)
        {
            s32Ret = mcuGotoAppMode();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "mcuGotoAppMode failed.\n");
            }
        }
#endif        
    }

    //s32Ret = updateStop();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = updateFini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}


/***************************************************************
*-# 用例编号: itest_updateSyncOldParams_001
*-# 测试功能: 
*-# 测试类型: 自动化
*-# 预置条件: 无
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_updateSyncOldParams_001()
{
    sint32 s32Ret = 0;
    char *pszConfigFile = CONFIG_XML;
    char *pszConfigBak1 = CONFIG_BAK1;
    char *pszConfigBak2 = CONFIG_BAK2;
    char *pszConfigDefault = CONFIG_DEFAULT;

    s32Ret = CONFIG_Init(pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    
    s32Ret = updateSyncOldParams();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    s32Ret = CONFIG_Fini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}

void Suite_autoUpdate_API()
{
    CU_pSuite pSuite;

    if (NULL == CU_get_registry())
    {
        printf("CUnit not registry!\n");
        return;
    }

    pSuite = CU_ADD_SUITE(NULL, NULL);
    CU_ADD_TEST(pSuite, itest_updateInit_001);
    CU_ADD_TEST(pSuite, itest_updateMkdirp_001);
    CU_ADD_TEST(pSuite, itest_pktFindUpdatePacket_001);
    CU_ADD_TEST(pSuite, itest_pktDecryption_001);
    CU_ADD_TEST(pSuite, itest_pktUntarFile_001);
    CU_ADD_TEST(pSuite, itest_pktParseMd5check_001);
    CU_ADD_TEST(pSuite, itest_pktCheckPacketMd5_001);
    CU_ADD_TEST(pSuite, itest_updateComparePacketWithSystem_001);
    CU_ADD_TEST(pSuite, itest_updateComparePacketWithSystem_002);
    CU_ADD_TEST(pSuite, itest_updateOverwritePacketToSystem_001);
    CU_ADD_TEST(pSuite, itest_updateOverwritePacketToSystem_002);
    CU_ADD_TEST(pSuite, itest_updateSyncOldParams_001);
}

void AddTests_itest_autoUpdate()
{
    Suite_autoUpdate_API();
}


