#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>
#include <ctype.h>

#include <net/if.h>
#include <arpa/inet.h>


#include "openssl/sha.h"
#include "openssl/pem.h"
#include "openssl/bio.h"
#include "common.h"
#include "print.h"
#include "httpHandle.h"
#include "websocket.h"

//websocket guid
#define GUID "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"
extern pthread_mutex_t m_mutexLockStrtok; 
extern SNAP_INFO_S m_stSnapInfo;

/******************************************************************************
 * 函数功能: 字符串反转函数,用于解决大端小端问题 
 * 输入参数: str ---需要反转的字符串
 			 len ---字符串长度
 * 输出参数: str
 * 返回值  : 无
 * 注意      : 无
 *****************************************************************************/
void WS_InvertedString(char *str,sint32 len)
{
    sint32 i; 
	char temp;
    for (i=0;i<len/2;++i)
    {
        temp = *(str+i);
        *(str+i) = *(str+len-i-1);
        *(str+len-i-1) = temp;
    }
}

 /******************************************************************************
 * 函数功能: 字符串反转函数,用于解决大端小端问题 
 * 输入参数: data --- 传过来时为密文，解码后的明文同样存储在这里
 			 len  --- 数据的长度
 			 mask --- 掩码
 * 输出参数: str
 * 返回值  : 无
 * 注意      : 无
 *****************************************************************************/
void WS_Umask(char *data,sint32 len,char *mask)
{
    sint32 i;
    for (i=0;i<len;++i)
        *(data+i) ^= *(mask+(i%4));
}

/******************************************************************************
* 函数功能: websocket建立握手
* 输入参数: pu8Message---请求包
			s32ClientFd---客户端socket fd
			
* 输出参数: pstHeader---请求包解析后数据
* 返回值  : SV_SUCCESS---成功
* 注意	  : 无
*****************************************************************************/
sint32 WS_ShakeHand(uint8 *pu8Message,sint32 s32ClientFd,HTTP_HEADER_S *pstHeader)
{

	char *pszTmp = NULL;  
    char key_str[256];
    char sec_accept[32];
    unsigned char sha1_data[SHA_DIGEST_LENGTH+1]={0};
    char head[1024] = {0};

	if (NULL == pu8Message)
	{
		return ERR_NULL_PTR;
	}  

	pthread_mutex_lock(&m_mutexLockStrtok);
	pstHeader->pszMethod = strtok((char *)pu8Message, " ");
    if (NULL == pstHeader->pszMethod)
    {
        print_level(SV_ERROR, "http header parse Method error.\n");
		pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }
    
    pstHeader->pszUrl = strtok(NULL, " ");
    if (NULL == pstHeader->pszUrl)
    {
        print_level(SV_ERROR, "http header parse URL error.\n");
		pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }
	
    pstHeader->pszVersion = strtok(NULL,"\r\n");
    if (NULL == pstHeader->pszVersion)
    {
        print_level(SV_ERROR, "http header parse VERSION error.\n");
		pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

	pszTmp=pstHeader->pszVersion + strlen(pstHeader->pszVersion)+1;
	memset(key_str,0,sizeof(key_str));
	pszTmp=strstr(pszTmp,"Sec-WebSocket-Key:");// 该字符串有19个字符

	if (pszTmp!=NULL)
	{
		strtok(pszTmp,"\r\n");   	//截取出key这一行
		strcpy(key_str,pszTmp+19);	// 取出key的值
		strcat(key_str,GUID);  
		print_level(SV_WARN, "Sec-WebSocket-Key+GUID = %s \n ", key_str);
		
		SHA1((unsigned char*)&key_str,strlen(key_str),(unsigned char*)&sha1_data);
		ws_base64_encode(sha1_data,strlen(sha1_data),sec_accept);
		sprintf(head, "HTTP/1.1 101 Switching Protocols\r\n" \
					  "Upgrade: websocket\r\n" \
					  "Connection: Upgrade\r\n" \
					  "Sec-WebSocket-Accept: %s\r\n" \
					  "\r\n",sec_accept);
		print_level(SV_WARN,"response:\n %s",head);
		
		if (write(s32ClientFd,head,strlen(head))<0)
		{
			 print_level(SV_ERROR, "websocket header send error，cannot shakehand.\n");
			 pthread_mutex_unlock(&m_mutexLockStrtok);
			 return ERR_INVALID_CHNID;
		}
		print_level(SV_WARN,"WebSocket shakehand success \n");
		pthread_mutex_unlock(&m_mutexLockStrtok);
		return SV_SUCCESS;	
	}
    return ERR_INVALID_CHNID;
}


/******************************************************************************
* 函数功能: 发送一帧完整数据
* 输入参数: clientFd---客户端socket fd
			op---数据类型
			data---发送的数据
			len---数据的长度
* 输出参数: 无
* 返回值  : SV_SUCCESS---成功
* 注意	  : 无
*****************************************************************************/
sint32 WS_Send_Frame(sint32 clientFd,char op,char *data,unsigned long long len)
{
	sint32 s32Ret=0;
	int data_len;
	char *response;

	if (len < 126) {
		response = malloc(2+len);
		response[0]=(op & WEBSOCKET_DONT_FIN ? 0x0 : FLAGS_MASK_FIN) | (op & FLAGS_MASK_OP);
		response[1]=(unsigned char) len;
		memcpy(&response[2],data,len);
		data_len = 2+len;
	} else if (len < 65535) {
		uint16_t tmp = htons((uint16_t) len);
		response = malloc(4+len);
		response[0]=(op & WEBSOCKET_DONT_FIN ? 0x0 : FLAGS_MASK_FIN) | (op & FLAGS_MASK_OP);
		response[1] = 126;
		memcpy(&response[2], &tmp, sizeof(tmp));
		memcpy(&response[4], data, len);
		data_len = 4+len;
	} else {
		uint32_t tmp;
		response = malloc(10+len);
		response[0]=(op & WEBSOCKET_DONT_FIN ? 0x0 : FLAGS_MASK_FIN) | (op & FLAGS_MASK_OP);
		response[1] = 127;
		tmp = htonl((uint32_t)((uint64_t) len >> 32));
		memcpy(&response[2], &tmp, sizeof(tmp));
		tmp = htonl((uint32_t)(len & 0xffffffff));
		memcpy(&response[6], &tmp, sizeof(tmp));
		memcpy(&response[10], data, len);
		data_len = 10+len;
	}
	s32Ret = send(clientFd,response, data_len, 0);
	free(response);
	if(s32Ret<0){
        return SV_FAILURE;
	}
    return SV_SUCCESS;
}

/******************************************************************************
* 函数功能: 发送帧头
* 输入参数: clientFd---客户端socket fd
			op---数据类型
			len---数据的长度
* 输出参数: 无
* 返回值  : SV_SUCCESS---成功
* 注意	  : 无
*****************************************************************************/
sint32 WS_Send_Head(sint32 clientFd, char op,unsigned long long len)
{
	int header_len;
	unsigned char header[10];
	sint32 s32Ret;

	header[0] =(op & WEBSOCKET_DONT_FIN ? 0x0 : FLAGS_MASK_FIN) | (op & FLAGS_MASK_OP);
	if (len < 126) {
		header[1] = (unsigned char) len;
		header_len = 2;
	} else if (len < 65535) {
		uint16_t tmp = htons((uint16_t) len);
		header[1] = 126;
		memcpy(&header[2], &tmp, sizeof(tmp));
		header_len = 4;
	} else {
		uint32_t tmp;
		header[1] = 127;
		tmp = htonl((uint32_t)((uint64_t) len >> 32));
		memcpy(&header[2], &tmp, sizeof(tmp));
		tmp = htonl((uint32_t)(len & 0xffffffff));
		memcpy(&header[6], &tmp, sizeof(tmp));
		header_len = 10;
	}
	s32Ret = send(clientFd,header, header_len, 0);
    return s32Ret;

}


/******************************************************************************
* 函数功能: 接收及存储数据帧头
* 输入参数: clientFd---客户端socket fd
* 输出参数: head---数据帧头
* 返回值  : SV_SUCCESS---成功
* 注意	  : 无
*****************************************************************************/
sint32 WS_RECV_FrameHead(sint32 ClientFd,WS_HEAD_S* head)
{
    char one_char;
    /*read fin and op code*/
    if (read(ClientFd,&one_char,1)<=0)
    {
        perror("read fin");
        return -1;
    }
    head->fin = (one_char & 0x80) == 0x80;
    head->opcode = one_char & 0x0F;
    if (read(ClientFd,&one_char,1)<=0)
    {
        perror("read mask");
        return -1;
    }
    head->mask = (one_char & 0x80) == 0X80;

    /*get payload length*/
    head->payload_length = one_char & 0x7F;

    if (head->payload_length == 126)
    {
        char extern_len[2];
        if (read(ClientFd,extern_len,2)<=0)
        {
            perror("read extern_len");
            return -1;
        }
        head->payload_length = (extern_len[0]&0xFF) << 8 | (extern_len[1]&0xFF);
    }
    else if (head->payload_length == 127)
    {
        char extern_len[8];
        if (read(ClientFd,extern_len,8)<=0)
        {
            perror("read extern_len");
            return -1;
        }
        WS_InvertedString(extern_len,8);
        memcpy(&(head->payload_length),extern_len,8);
    }

    /*read masking-key*/
    if (read(ClientFd,head->masking_key,4)<=0)
    {
        perror("read masking-key");
        return -1;
    }

    return 0;
}


/******************************************************************************
 * 函数功能: 回复文件内容
 * 输入参数: s32ClientFd --- 客户端socketfd
             pszFilePath --- 文件路径
             
 * 输出参数: 无
 * 返回值  : 成功返回 0
             失败返回 <0 
 * 注意    : 无
 *****************************************************************************/
sint32 WS_ReplySnap(void *pvArg)
{
    sint32 s32Ret = 0;
    sint32 s32ImgSize = 0;
    sint32 socketFd = (sint32)pvArg;
	
	s32Ret = prctl(PR_SET_NAME, "ws_reply_file");
	if (0 != s32Ret)
	{
		 print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
	}
	while(1)
	{	
		pthread_mutex_lock(&m_stSnapInfo.mutexSnap);
		s32ImgSize = m_stSnapInfo.s32ImgSize;
		s32Ret = WS_Send_Head(socketFd,WEBSOCKET_OP_BINARY,s32ImgSize);
		if( s32Ret <= 0)
		{
			print_level(SV_WARN,"reply websocket frame head failed!\n");
			pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
			m_stSnapInfo.s32Flag = 1;
			return SV_FAILURE;
		}
		s32Ret = send(socketFd,m_stSnapInfo.apvImgBuf,s32ImgSize,0);	
		if(s32Ret <= 0)
		{
			print_level(SV_ERROR,"reply websocket snap data failed.\n");
			pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
			m_stSnapInfo.s32Flag = 1;
			return SV_FAILURE;
		}
		pthread_mutex_unlock(&m_stSnapInfo.mutexSnap);
		sleep_ms(30);
	}
	
}

	
/*Base64编码函数*/
unsigned long long ws_base64_encode(char *in_str, int in_len, char *out_str)
{
    BIO *b64, *bio;
    BUF_MEM *bptr = NULL;
    unsigned long long size = 0;

    if (in_str == NULL || out_str == NULL)
        return -1;

    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_write(bio, in_str, in_len);
    BIO_flush(bio);

    BIO_get_mem_ptr(bio, &bptr);
    memcpy(out_str, bptr->data, bptr->length);
    out_str[bptr->length-1] = '\0';
    size = bptr->length;

    BIO_free_all(bio);
    return size;
}

