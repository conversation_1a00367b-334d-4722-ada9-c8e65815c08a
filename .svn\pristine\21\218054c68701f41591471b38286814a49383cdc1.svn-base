<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope
    xmlns:SOAP-ENV="http://www.w3.org/2003/05/soap-envelope"
    xmlns:SOAP-ENC="http://www.w3.org/2003/05/soap-encoding"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:chan="http://schemas.microsoft.com/ws/2005/02/duplex"
    xmlns:wsa5="http://www.w3.org/2005/08/addressing"
    xmlns:c14n="http://www.w3.org/2001/10/xml-exc-c14n#"
    xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
    xmlns:xenc="http://www.w3.org/2001/04/xmlenc#"
    xmlns:wsc="http://schemas.xmlsoap.org/ws/2005/02/sc"
    xmlns:ds="http://www.w3.org/2000/09/xmldsig#"
    xmlns:wsse="http://schemas.xmlsoap.org/ws/2002/12/secext"
    xmlns:xmime5="http://www.w3.org/2005/05/xmlmime"
    xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing"
    xmlns:xmime="http://www.w3.org/2005/05/xmlmime"
    xmlns:xop="http://www.w3.org/2004/08/xop/include"
    xmlns:tt="http://www.onvif.org/ver10/schema"
    xmlns:wsbf2="http://docs.oasis-open.org/wsrf/bf-2"
    xmlns:wstop="http://docs.oasis-open.org/wsn/t-1"
    xmlns:wsr2="http://docs.oasis-open.org/wsrf/r-2"
    xmlns:dn="http://www.onvif.org/ver10/network/wsdl"
    xmlns:d="http://schemas.xmlsoap.org/ws/2005/04/discovery"
    xmlns:tds="http://www.onvif.org/ver10/device/wsdl"
    xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2"
    xmlns:tev="http://www.onvif.org/ver10/events/wsdl"
    xmlns:timg="http://www.onvif.org/ver20/imaging/wsdl"
    xmlns:tptz="http://www.onvif.org/ver20/ptz/wsdl"
    xmlns:trt="http://www.onvif.org/ver10/media/wsdl">
 <SOAP-ENV:Header>
 </SOAP-ENV:Header>
 <SOAP-ENV:Body>
  <trt:GetVideoSourceConfigurationsResponse>
   <trt:Configurations token="" ViewMode="">
    <tt:Name></tt:Name>
    <tt:UseCount>0</tt:UseCount>
    <tt:SourceToken></tt:SourceToken>
    <tt:Bounds x="0" y="0" width="0" height="0">
    </tt:Bounds>
    <!-- extensibility element(s) -->
    <!-- extensibility element(s) -->
    <tt:Extension>
     <tt:Rotate>
      <tt:Mode>OFF</tt:Mode>
      <tt:Degree>0</tt:Degree>
      <tt:Extension>
       <!-- extensibility element(s) -->
       <!-- extensibility element(s) -->
      </tt:Extension>
     </tt:Rotate>
     <tt:Extension>
      <tt:LensDescription FocalLength="0.0">
       <tt:Offset x="0.0" y="0.0">
       </tt:Offset>
       <tt:Projection>
        <tt:Angle>0.0</tt:Angle>
        <tt:Radius>0.0</tt:Radius>
        <!-- extensibility element(s) -->
        <!-- extensibility element(s) -->
       </tt:Projection>
       <tt:Projection>
        <tt:Angle>0.0</tt:Angle>
        <tt:Radius>0.0</tt:Radius>
        <!-- extensibility element(s) -->
        <!-- extensibility element(s) -->
       </tt:Projection>
       <tt:XFactor>0.0</tt:XFactor>
       <!-- extensibility element(s) -->
       <!-- extensibility element(s) -->
      </tt:LensDescription>
      <tt:LensDescription FocalLength="0.0">
       <tt:Offset x="0.0" y="0.0">
       </tt:Offset>
       <tt:Projection>
        <tt:Angle>0.0</tt:Angle>
        <tt:Radius>0.0</tt:Radius>
        <!-- extensibility element(s) -->
        <!-- extensibility element(s) -->
       </tt:Projection>
       <tt:Projection>
        <tt:Angle>0.0</tt:Angle>
        <tt:Radius>0.0</tt:Radius>
        <!-- extensibility element(s) -->
        <!-- extensibility element(s) -->
       </tt:Projection>
       <tt:XFactor>0.0</tt:XFactor>
       <!-- extensibility element(s) -->
       <!-- extensibility element(s) -->
      </tt:LensDescription>
      <tt:SceneOrientation>
       <tt:Mode>MANUAL</tt:Mode>
       <tt:Orientation></tt:Orientation>
      </tt:SceneOrientation>
      <!-- extensibility element(s) -->
      <!-- extensibility element(s) -->
     </tt:Extension>
    </tt:Extension>
   </trt:Configurations>
  </trt:GetVideoSourceConfigurationsResponse>
 </SOAP-ENV:Body>
</SOAP-ENV:Envelope>
