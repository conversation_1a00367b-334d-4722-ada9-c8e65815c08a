﻿/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：mpp_vi.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2021-04-26

文件功能描述: 封装RK MPP视频输入模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <linux/ioctl.h>
#include <sys/ioctl.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>
#include <sys/stat.h>
#include <fcntl.h>

#include "print.h"
#include "common.h"
#include "utils.h"
#include "../../../include/board.h"
#include "rkmedia_api.h"
#include "mpp_vi.h"
#include "media.h"
#include "mpp_gui.h"
#include "mpp_vosd.h"
#include "rk_isp.h"
#include <linux/videodev2.h> 

#include <math.h>

#define RKMODULE_MAX_VC_CH 4


struct rkmodule_vc_fmt_info { 
__u32 width[RKMODULE_MAX_VC_CH];
__u32 height[RKMODULE_MAX_VC_CH];
__u32 fps[RKMODULE_MAX_VC_CH]; 
} __attribute__ ((packed));

struct rkmodule_vc_hotplug_info { 
__u8 detect_status; 
} __attribute__ ((packed));


#define RKMODULE_GET_VC_FMT_INFO 		_IOR('V', BASE_VIDIOC_PRIVATE + 12, struct rkmodule_vc_fmt_info)
#define RKMODULE_GET_VC_HOTPLUG_INFO 	_IOR('V', BASE_VIDIOC_PRIVATE + 13, struct rkmodule_vc_hotplug_info)

#define max(x, y) ((x) > (y) ? (x) : (y))
#define min(x, y) ((x) < (y) ? (x) : (y))


/* 数值范围限制 */
#define MPP_RANGE_LIMIT(v, a, b) do{ \
    if (v < a) v=a; \
    if (v > b) v=b; \
}while(0)

/* VI设备属性配置 */
typedef struct tagMppViDevAttr_S
{
} MPP_VI_DEV_ATTR_S;

/* VI通道属性配置 */
typedef struct tagMppViChnAttr_S
{
} MPP_VI_CHN_ATTR_S;

/* VI属性配置信息 */
typedef struct tagMppViAttrInfo_S
{
    uint32              u32DevNum;      /* VI设备数目 */
    uint32              u32ChnNum;      /* VI通道数目 */
    MPP_VI_DEV_ATTR_S   astDevAttr[VIM_MAX_DEV_NUM];    /* VI设备属性列表 */
    MPP_VI_CHN_ATTR_S   astChnAttr[VIM_MAX_CHN_NUM];    /* VI通道属性列表 */
} MPP_VI_ATTR_INFO_S;


/* 视频输入模块控制信息 */
typedef struct tagViInfo_S
{
    uint32              u32Fps;         /* 视频帧率 */
    uint32              u32ChnNum;      /* 视频源通道数目 */
    SEN_TYPE_E          enSenChipType;  /* 前端sensor芯片类型 */
    VIDEO_MODE_EE       enVideoMode[MEDIA_MAX_CHN];    /* 通道视频制式 */
    WDR_MODE_EE         enSysWDRMode;   /* WDR模式 */
    VI_USERPIC_ATTR_S   enUserPic[4];       /* 插入用户图片信息 */
    uint32              u32TID;         /* ISP线程ID */
    
    sint32              s32Framerate[MPP_VPSS_CHN_BUTT]; /* 各个通道的帧率 */
    SV_BOOL             bSkipChn[MPP_VPSS_CHN_BUTT];     /* 当不拉流的时候,是否跳帧 */
    SV_BOOL             bInterrupt;     /* 线程是否暂停 */
    SV_BOOL             bRunning;       /* 线程启动 */
} MPP_VI_INFO_S;

#define print_ViChnAttr(stViChnAttr) \
{ \
	print_level(SV_INFO, "node:%s width:%d height:%d\n" \
				"enPixFmt:%d, u32BufCnt:%d, enBufType:%d, enWorkMode:%d\n", \
				stViChnAttr.pcVideoNode, stViChnAttr.u32Width, stViChnAttr.u32Height, \
				stViChnAttr.enPixFmt,stViChnAttr.u32BufCnt,stViChnAttr.enBufType,stViChnAttr.enWorkMode); \
}

#define MPP_VI_CHN_FPS_MAX    30
#define MPP_VI_CHN0_FPS_SLEEP 1
#define MPP_VI_CHN1_FPS_SLEEP 20


static int c_vi_fps[] = {MPP_VI_CHN_FPS_MAX, MPP_VI_CHN_FPS_MAX};
static int c_vi_fps_sleep[] = {MPP_VI_CHN0_FPS_SLEEP, MPP_VI_CHN1_FPS_SLEEP};

MPP_VI_INFO_S m_stViInfo = {0};         /* 视频输入控制信息 */

MPP_PHY_CHN g_astViPhyChn[VIM_MAX_CHN_NUM] = {0};   /* 逻辑通道对应的物理通道索引表 */


sint32 GetSensorSize(SEN_TYPE_E enSenType, uint32* pu32Width, uint32* pu32Height)
{
    switch(enSenType)
    {
        case SEN_TYPE_GC2053:
        case SEN_TYPE_GC2093:
            *pu32Width = 1920;
            *pu32Height = 1080;
            break;
        case SEN_TYPE_OS05A20:
            *pu32Width = 2688;
            *pu32Height = 1944;
            break;
        case SEN_TYPE_IMX415:
            *pu32Width = 3840;
            *pu32Height = 2160;
            break;
        default:
            return SV_FAILURE;
    }
    return SV_SUCCESS;
}

sint32 GetSensorWidth(SEN_TYPE_E enSenType)
{
    switch(enSenType)
    {
        case SEN_TYPE_GC2053:
        case SEN_TYPE_GC2093:
            return 1920;
            break;
        case SEN_TYPE_OS05A20:
            return 2688;
            break;
        case SEN_TYPE_IMX415:
            return 3840;
        default:
            return 1920;
    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 获取VI属性配置信息
 * 输入参数: enSenChipType --- sensor类型
             enVideoMode --- 视频制式
 * 输出参数: pstViAttrInfo --- VI配置属性参数
 * 返回值  : 
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_GetAttrCfg(TypeMode_S *stTypeMode, MPP_VI_ATTR_INFO_S *pstViAttrInfo)
{
    RK_S32 s32Ret = 0, i;
    uint32 u32DevNum = 1;
    uint32 u32ChnNum = 1; 

    if (NULL == pstViAttrInfo)
    {
        return ERR_NULL_PTR;
    }

    if (stTypeMode->enSenChipType >= SEN_TYPE_BUTT || stTypeMode->enVideoMode >= VIDEO_MODE_BUTTE || stTypeMode->enSysWDRMode >= WDR_MODE_BUTTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pstViAttrInfo->u32DevNum = u32DevNum;
    pstViAttrInfo->u32ChnNum = u32DevNum;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置MIPI接口工作参数
 * 输入参数: enSenType --- sensor类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_MIPI_SetParam(SEN_TYPE_E enSenType, WDR_MODE_EE enWDR_MODE)
{
    return SV_SUCCESS;
}

void * mpp_vi_ISP_Body(void *pvArg)
{
    return NULL;
}

/******************************************************************************
 * 函数功能: 将ISP由彩色切换到黑白
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_Color2GreyEnable()
{
    sint32 s32Ret;
    VI_PIPE ViPipe = 0;
    
    s32Ret = RK_ISP_SET_GrayMode(ViPipe, 2);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_ISP_SET_GrayMode[%d] failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将ISP由黑白切换到彩色
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_Color2GreyDisable()
{
    sint32 s32Ret;
    VI_PIPE ViPipe = 0;
    
    s32Ret = RK_ISP_SET_GrayMode(ViPipe, 1);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vi_Color2GreyDisable failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    return SV_SUCCESS;
}

sint32 mpp_vi_SetFps(uint32 u32Fps)
{
    sint32 s32Ret;
    VI_PIPE ViPipe = 0;

    s32Ret = RK_ISP_SetFrameRate(ViPipe, u32Fps);
    if(RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_ISP_SetFrameRate failed! [err=%#x]\n", s32Ret);
        RK_ISP_Stop(ViPipe);
        return s32Ret;
    }
    m_stViInfo.u32Fps = u32Fps;

    return SV_SUCCESS;
}

uint32 mpp_vi_GetFps(uint32 ViPipe)
{
    uint32 u32Fps = 30;
    u32Fps = m_stViInfo.u32Fps;
    return u32Fps;
}




/* VI ISP裁剪 */
sint32 mpp_vi_ISP_Crop(SEN_TYPE_E enSenType)
{
    uint32 u32Width, u32Height;
    sint32 s32Ret;
    rk_aiq_rect_t rect;
    VI_PIPE ViPipe = 0;

    s32Ret = GetSensorSize(enSenType, &u32Width, &u32Height);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "GetSensorSize failed!\n");
        return SV_SUCCESS;
    }

    if (u32Width >= 3840 && u32Height >= 2160)          /* 标准格式 */
    {
        if (u32Width == 3840 && u32Height == 2160)
        {
            return SV_SUCCESS;
        }

        rect.left = (u32Width - 3840) / 2;
        rect.top = (u32Height - 2160) / 2;
        rect.width = u32Width;
        rect.height = u32Height;
    }
    else if (u32Width >= 2560 && u32Height >= 1440)          /* 标准格式 */
    {
        if (u32Width == 2560 && u32Height == 1440)
        {
            return SV_SUCCESS;
        }

        rect.left = (u32Width - 2560) / 2;
        rect.top = (u32Height - 1440) / 2;
        rect.width = u32Width;
        rect.height = u32Height;
    }
    else if  (u32Width >= 1920 && u32Height >= 1080)          /* 标准格式 */
    {
        if (u32Width == 1920 && u32Height == 1080)
        {
            return SV_SUCCESS;
        }

        rect.left = (u32Width - 1920) / 2;
        rect.top = (u32Height - 1080) / 2;
        rect.width = u32Width;
        rect.height = u32Height;
    }

    s32Ret = RK_ISP_SET_Crop(ViPipe, rect);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_ISP_SET_Crop failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

char *mpp_vi_Get_ISP_PATH(SEN_TYPE_E enSenType, char *pszIqfilesPath, SV_BOOL bRemoveFishEye)
{
    char *pIspPath = NULL;
    RK_CHAR *pszSensor = NULL;
    char tmpIqfilesPath[128];

    switch(enSenType)
    {
        case SEN_TYPE_GC2053:
            pszSensor = "sensor_gc2053";
            break;
        case SEN_TYPE_GC2093:
            pszSensor = "sensor_gc2093";
            break;
        case SEN_TYPE_IMX415:
            pszSensor = "sensor_imx415";
            break;
        case SEN_TYPE_OS05A20:
            pszSensor = "sensor_os05a20";
            break;
        default:
            return SV_FAILURE;
    }

#if (BOARD == BOARD_DMS31V2)
    sprintf(pszIqfilesPath, "/etc/scene_auto/%s", pszSensor);
#elif (BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32N1) || BOARD == BOARD_ADA32E1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4))
    if ((BOARD_IsADA32IRCUT() || BOARD_ADA32V2_V2 == BOARD_GetVersion())||(BOARD_IsSVersion(BOARD_S_ADA32N1_G_6M)&& BOARD_GetVersion() == BOARD_ADA32N1_V2))
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/IR", pszSensor);
    }
    else if(BOARD_IsSVersion(BOARD_S_6M))
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/6M", pszSensor);
    }
    else if(BOARD_IsSVersion(BOARD_S_2M3))
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/2M3", pszSensor);
    }
    else if(BOARD_IsSVersion(BOARD_S_1M99))
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/1M99", pszSensor);
    }
    else if(BOARD_IsSVersion(BOARD_S_1M45))
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/1M45", pszSensor);
    }
    else
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/2M3", pszSensor);
    }

    if (bRemoveFishEye && (BOARD_IsSVersion(BOARD_S_1M45) || BOARD_IsSVersion(BOARD_S_1M99)))
    {        
        strcpy(tmpIqfilesPath, pszIqfilesPath);
        sprintf(pszIqfilesPath, "%s/fisheye", tmpIqfilesPath);
    }

    if (BOARD_IsCustomer(BOARD_C_ADA32V2_111620))
    {
        strcpy(tmpIqfilesPath, pszIqfilesPath);
        sprintf(pszIqfilesPath, "%s/fisheye", tmpIqfilesPath);
    }

    if (access(pszIqfilesPath, F_OK) == -1)
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/2M3", pszSensor);
    }
    
#elif (BOARD == BOARD_ADA47V1)
    if (SEN_TYPE_IMX415 == enSenType)
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/AF8MP5X", pszSensor);
    }
    else if (SEN_TYPE_OS05A20 == enSenType)
    {
        sprintf(pszIqfilesPath, "/etc/scene_auto/%s/2M3", pszSensor);
    }
    else
    {
        if (BOARD_IsSVersion(BOARD_S_ADA47V1_H_6M) || 
            BOARD_IsSVersion(BOARD_S_ADA47V1_G_6M))
        {
            sprintf(pszIqfilesPath, "/etc/scene_auto/%s/6M_940IRFIL", pszSensor);
        }
        else if(BOARD_IsSVersion(BOARD_S_ADA47V1_C_2M8))
        {
            sprintf(pszIqfilesPath, "/etc/scene_auto/%s/2M8_IRCUT", pszSensor);
        }
        else if(BOARD_IsSVersion(BOARD_S_ADA47V1_C_3M6))
        {
            sprintf(pszIqfilesPath, "/etc/scene_auto/%s/3M6_IRCUT", pszSensor);
        }
        else if(BOARD_IsSVersion(BOARD_S_ADA47V1_C_6M))
        {
            sprintf(pszIqfilesPath, "/etc/scene_auto/%s/6M_IRCUT", pszSensor);
        }
        else
        {
            sprintf(pszIqfilesPath, "/etc/scene_auto/%s", pszSensor);
        }
    }
#elif (BOARD == BOARD_ADA32IR)

#if (MOUDLETYPE == MOUDLETYPE_TC933)
    sprintf(pszIqfilesPath, "/etc/scene_auto/%s/TC933/6M_640IRFIL", pszSensor);
    print_level(SV_ERROR, "This device is TC933\r\n");
#elif (MOUDLETYPE == MOUDLETYPE_TC639_T2)
    sprintf(pszIqfilesPath, "/etc/scene_auto/%s/TC639-T2/6M_640IRFIL", pszSensor);
    print_level(SV_ERROR, "This device is TC639-T2\r\n");
#elif (MOUDLETYPE == MOUDLETYPE_TC639_T3)
    sprintf(pszIqfilesPath, "/etc/scene_auto/%s/TC639-T3/8M_640IRFIL", pszSensor);
    print_level(SV_ERROR, "This device is TC639-T3\r\n");
#elif (MOUDLETYPE == MOUDLETYPE_TC639_T6)
    sprintf(pszIqfilesPath, "/etc/scene_auto/%s/TC639-T6/6M_640IRFIL", pszSensor);
    print_level(SV_ERROR, "This device is TC639-T6\r\n");
#endif

#elif (BOARD == BOARD_ADA900V1)
    sprintf(pszIqfilesPath, "/etc/scene_auto/%s", pszSensor);
#endif
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动ISP图像参数自动调节模块
 * 输入参数: enSenType --- sensor类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_ISP_Start(SEN_TYPE_E enSenType, WDR_MODE_EE enWDRMode, uint32 u32Framerate, SV_BOOL bRemovedFishEye)
{
    VI_PIPE ViPipe = 0;
    sint32 s32Ret;
    RK_CHAR *pszSensor = NULL;
    RK_CHAR szIqfilesPath[256] = {0};
    RK_BOOL bMultictx = RK_FALSE;
    rk_aiq_working_mode_t hdr_mode = RK_AIQ_WORKING_MODE_NORMAL;

    if (enSenType >= SEN_TYPE_BUTT || (SEN_TYPE_GC2053 != enSenType && SEN_TYPE_GC2093 != enSenType 
                                        && SEN_TYPE_IMX415 != enSenType && SEN_TYPE_OS05A20 != enSenType))
    {
        return ERR_ILLEGAL_PARAM;
    }

    switch (enSenType)
    {
        case SEN_TYPE_GC2053:
        case SEN_TYPE_IMX415:
            hdr_mode = RK_AIQ_WORKING_MODE_NORMAL;
            break;
        case SEN_TYPE_GC2093:
        case SEN_TYPE_OS05A20:
            hdr_mode = RK_AIQ_WORKING_MODE_ISP_HDR2;
            break;
    }

    if (BOARD == BOARD_ADA32IR)
    {
        hdr_mode = RK_AIQ_WORKING_MODE_NORMAL;
    }


    print_level(SV_INFO, "bRemovedFishEye:%d enSenType:%d\n", bRemovedFishEye, enSenType);
    s32Ret = mpp_vi_Get_ISP_PATH(enSenType, szIqfilesPath, bRemovedFishEye);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vi_Get_ISP_PATH failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

#if ((BOARD == BOARD_ADA47V1))
    RK_ISP_Stop(ViPipe);
    sleep_ms(500);
#endif
    s32Ret = RK_ISP_Init(ViPipe, hdr_mode, bMultictx, szIqfilesPath);
    if(RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_ISP_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1  || BOARD == BOARD_ADA32C4)
    print_level(SV_INFO, "==> RK_ISP_SetFrameRate %d\n", u32Framerate);
    sleep_ms(500);
#endif

    s32Ret = RK_ISP_SetFrameRate(ViPipe, u32Framerate);
    if(RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_ISP_SetFrameRate failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA47V1)
    print_level(SV_INFO, "==> RK_ISP_Run\n");
    sleep_ms(500);
#endif    

    /* 解决A38夜视上电出现瞬间偏紫问题 */
    if (BOARD_IsADA32IRCUT() || BOARD_ADA32V2_V2 == BOARD_GetVersion())
    {
        s32Ret = RK_ISP_SET_GrayMode(ViPipe, 2);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "RK_ISP_SET_GrayMode[%d] failed! [err=%d]\n", s32Ret);
            return s32Ret;
        }
    }
    
    s32Ret = RK_ISP_Run(ViPipe);
    if(RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_ISP_Run failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }


    if (enSenType == SEN_TYPE_IMX415)
    {
        char *pCmd = "media-ctl --set-v4l2 '\"m01_f_imx415 1-001a\":0[@10000/100000]'"; /* imx415 设置为10帧 */
        s32Ret = SAFE_System(pCmd, 100);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SAFE_System Cmd ==> %s\n", pCmd);
        }
        /* VI 为10帧 */
        c_vi_fps[0] = 10;
        c_vi_fps[1] = 10;
    }

    /* 打印ISP信息 */
    DUMP_ISP_S stDumpIsp;
    strcpy(stDumpIsp.iqPath, szIqfilesPath);
    dump_SetIspInfo(&stDumpIsp);
    

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止ISP图像参数自动调节模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_ISP_Stop()
{
    VI_PIPE ViPipe = 0;
    sint32 s32Ret;
    s32Ret = RK_ISP_Stop(ViPipe);
    if(RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_ISP_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

int getCamFmt(struct rkmodule_vc_fmt_info *fmt)
{
    int cnt = 0;
    struct rkmodule_vc_hotplug_info status;

    int fd = open("/dev/v4l-subdev6", O_RDWR, 0); 
    if(fd > 0)
    {
        ioctl(fd, RKMODULE_GET_VC_FMT_INFO, fmt);

        for(int i=0; i<4; i++)
        {
            ioctl(fd, RKMODULE_GET_VC_HOTPLUG_INFO, &status);
            if(((status.detect_status >> (i*2)) & 0x03) == 1)
            {
                cnt++;
                if(cnt > 10)
                {
                    print_level(SV_ERROR, "AD unstable status, after try cnt=%d !!!!!!!!!!!!!!!!!!!!!!!!!\n", cnt);
                    break;
                }
                sleep_ms(100);
                continue;
            }
            break;
        }

        close(fd);
        return 0;
    }

    print_level(SV_ERROR, "getCamFmt failed\n");
    return -1;
}

sint32 mpp_vi_SetParam(MEDIA_INIT_S * pstInitParam, MPP_VI_CONF_S *pstViConf)
{
    if (pstViConf == NULL || pstInitParam == NULL)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstInitParam->u32ChnNum || pstInitParam->u32ChnNum > VIM_MAX_CHN_NUM || pstInitParam->stTypeMode.enSenChipType >= SEN_TYPE_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pstViConf->stTypeMode = pstInitParam->stTypeMode;
    pstViConf->u32ChnNum  = pstInitParam->u32ChnNum;
    pstViConf->u32PriWidth = pstInitParam->stPriVencAttr.u32PicWidth;
    pstViConf->u32PriHeight = pstInitParam->stPriVencAttr.u32PicHeight;
    pstViConf->u32SecWidth = pstInitParam->stSecVencAttr.u32PicWidth;
    pstViConf->u32SecHeight = pstInitParam->stSecVencAttr.u32PicHeight;
    pstViConf->u32Fps = pstInitParam->stVoAttr.u32FrmRate;
        
#if (BOARD == BOARD_ADA32N1)
    pstViConf->u32Fps = pstInitParam->u32ViFramerate;
#else
    if (BOARD_ADA32E1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        pstViConf->u32Fps = 30;
    }
#endif

    m_stViInfo.u32ChnNum = pstViConf->u32ChnNum;
    m_stViInfo.u32Fps = pstViConf->u32Fps;
    m_stViInfo.enSenChipType = pstViConf->stTypeMode.enSenChipType;

    for(int i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {
        m_stViInfo.s32Framerate[i] = m_stViInfo.u32Fps;
    }

    print_level(SV_INFO, "set VI param, channel total: %d FPS: %d\n", pstViConf->u32ChnNum, pstViConf->u32Fps);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化VI模块
 * 输入参数: pstViConf --- 视频输入配置信息 u32ChnNum: [1, VIM_MAX_CHN_NUM]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 参数指针为NULL
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_MEDIA_NOT_DISABLE - 设备已被使能
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_Init(MPP_VI_CONF_S *pstViConf)
{
    RK_S32 s32Ret = 0;
    VI_PIPE viPipe = 0;
    VI_CHN viChnId = 0;
    VI_CHN_ATTR_S stViChnAttr;
    static RK_CHAR node[][32] = {"rkispp_scale0", "rkispp_scale1", "rkispp_scale2", "rkispp_m_bypass"};
    
    s32Ret = mpp_vi_ISP_Start(pstViConf->stTypeMode.enSenChipType, 0, pstViConf->u32Fps, SV_FALSE);
    if(RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_ISP_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    for (viChnId = MPP_VI_CHN_0; viChnId < MPP_VI_CHN_BUTT; viChnId++) 
    {
#if (BOARD != BOARD_ADA32N1)    // 子码流用VI通道2直连VENC
        if (viChnId == MPP_VI_CHN_2)
        {
            continue;
        }
#endif

#if (BOARD != BOARD_ADA900V1 && BOARD != BOARD_ADA32IR)
        if (viChnId == MPP_VI_CHN_3)
        {
            continue;
        }
#endif

        stViChnAttr.pcVideoNode = node[viChnId];
        stViChnAttr.u32BufCnt = viChnId == MPP_VI_CHN_0 ? 5 : 3;

        switch (viChnId) {
            case MPP_VI_CHN_0:
                stViChnAttr.u32Width = pstViConf->u32PriWidth;
                stViChnAttr.u32Height = pstViConf->u32PriHeight;
                break;
            case MPP_VI_CHN_1:
                stViChnAttr.u32Width = 960;
				stViChnAttr.u32Height = 540;
                break;
            case MPP_VI_CHN_2:
                stViChnAttr.u32Width = pstViConf->u32SecWidth;
				stViChnAttr.u32Height = pstViConf->u32SecHeight;
                break;
            case MPP_VI_CHN_3:
                stViChnAttr.u32Width = 1920;
                stViChnAttr.u32Height = 1080;
                break;
        }

        stViChnAttr.enPixFmt = IMAGE_TYPE_NV12;
        stViChnAttr.enBufType = VI_CHN_BUF_TYPE_DMA;
        stViChnAttr.enWorkMode = VI_WORK_MODE_NORMAL;
#if (BOARD == BOARD_ADA47V1)
        if (viChnId == MPP_VI_CHN_0) {
            /* 当VI的和尺寸和原尺寸不匹配，并且宽度大于2080时，需要将格式改为UYVY格式 */
            stViChnAttr.enPixFmt = IMAGE_TYPE_UYVY422;
        }
#endif

        print_ViChnAttr(stViChnAttr);
        s32Ret = RK_MPI_VI_SetChnAttr(viPipe, viChnId, &stViChnAttr);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VI_SetChnAttr[%d] failed! [err=%d]\n", viChnId, s32Ret);
            return s32Ret;
        }
        sleep_ms(30);

        s32Ret = RK_MPI_VI_EnableChn(viPipe, viChnId);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VI_EnableChn[%d] failed! [err=%d]\n", viChnId, s32Ret);
            return s32Ret;
        }

        s32Ret = RK_MPI_VI_StartStream(viPipe, viChnId);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VI_StartStream[%d] failed! [err=%d]\n", viChnId, s32Ret);
            return s32Ret;
        }

        g_astViPhyChn[viChnId].DevId = 0;
        g_astViPhyChn[viChnId].ChnId = viChnId;

        print_level(SV_INFO, "VI channel [%d] created successfully, resolution is (%d %d), framerate is %d.\n",
            viChnId, stViChnAttr.u32Width, stViChnAttr.u32Height, pstViConf->u32Fps);
    }

    return SV_SUCCESS;
}

sint32 mpp_vi_get_width()
{
    sint32 s32Width;
    switch(m_stViInfo.enSenChipType)
    {
        case SEN_TYPE_IMX415:
            s32Width = 3840;
            break;
        case SEN_TYPE_OS05A20:
            s32Width = 2688;
            break;
        case SEN_TYPE_GC2053:
        case SEN_TYPE_GC2093:
        default:
            s32Width = 1920;
            break;
    }
    return s32Width;
}

sint32 mpp_vi_get_Height()
{
    sint32 s32Height;
    switch(m_stViInfo.enSenChipType)
    {
        case SEN_TYPE_IMX415:
            s32Height = 2160;
            break;
        case SEN_TYPE_OS05A20:
            s32Height = 1944;
            break;
        case SEN_TYPE_GC2053:
        case SEN_TYPE_GC2093:
        default:
            s32Height = 1080;
            break;
    }
    return s32Height;
}



/******************************************************************************
 * 函数功能: 去初始化VI模块
 * 输入参数: 
 * 输出参数: 
 * 返回值  : 
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_Fini()
{
    RK_S32 s32Ret = 0;
    VI_PIPE ViPipe = 0;
    VI_CHN ViChnId = 0;
    for (int i = 0; i < m_stViInfo.u32ChnNum; i++)
    {
        ViChnId = i;
        print_level(SV_INFO, "RK_MPI_VI_DisableChn[%d] start\n", ViChnId);
        s32Ret = RK_MPI_VI_DisableChn(ViPipe, ViChnId);
        if(RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VI_DisableChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        print_level(SV_INFO, "RK_MPI_VI_DisableChn[%d] succeed\n", ViChnId);
    }

    print_level(SV_INFO, "mpp_vi_ISP stop\n", ViChnId);
    s32Ret = mpp_vi_ISP_Stop();
    if(RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_ISP failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    print_level(SV_INFO, "mpp_vi_ISP succeed\n", ViChnId);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 设置通道画面是否镜像或反转
 * 输入参数: s32Chn --- 通道号
             bMirror --- 是否水平翻转
             bFlip --- 是否垂直翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_SetChnMirrorFlip(sint32 s32Chn, SV_BOOL bMirror, SV_BOOL bFlip)
{
    sint32 s32Ret = 0;
    VI_CHN ViChn = 0;
    VI_PIPE ViPipe = 0;
    static SV_BOOL bSet = SV_FALSE;
    static SV_BOOL bMirrorNow = SV_FALSE, bFlipNow = SV_FALSE;
    uint8 u8Cmd[256] = {0};
    
    if (s32Chn < 0 || s32Chn > VIM_MAX_CHN_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    if (m_stViInfo.enSenChipType == SEN_TYPE_GC2053 || m_stViInfo.enSenChipType == SEN_TYPE_GC2093)
    {
        bMirror = !bMirror;
        bFlip = !bFlip;
    }
    else if (m_stViInfo.enSenChipType == SEN_TYPE_OS05A20)
    {
        bMirror = !bMirror;
    }

    if (!bSet || bMirror != bMirrorNow || bFlip != bFlipNow)
    {
#if (BOARD != BOARD_ADA32IR)
        print_level(SV_INFO, "set mirror=%d, flip=%d\n", bMirror, bFlip);
        RK_ISP_SET_mirror(ViChn, ((bFlip) << 1) + !bMirror);     /* 连续配置两次才生效 */
        sleep_ms(200);
        s32Ret = RK_ISP_SET_mirror(ViChn, (bFlip << 1) + bMirror);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "RK_ISP_SET_mirror failed! [err=0x%x]!\n", s32Ret);
            return s32Ret;
        }

        sleep_ms(50);
#else
        //2093使用RK_ISP_SET_mirror设置有概率失败，改为直接使用v4l2设置。镜原像、翻转要分开设置
        sprintf(u8Cmd, "v4l2-ctl --set-ctrl=horizontal_flip=%d -d /dev/v4l-subdev3", bMirror);
        print_level(SV_INFO, "000 u8Cmd=%s\n", u8Cmd);
        SAFE_System(u8Cmd,2000);

        sleep_ms(50);

        sprintf(u8Cmd, "v4l2-ctl --set-ctrl=vertical_flip=%d -d /dev/v4l-subdev3", bFlip);
        SAFE_System(u8Cmd,2000);
        print_level(SV_INFO, "111 u8Cmd=%s\n", u8Cmd);
#endif
        bSet = SV_TRUE;
        bMirrorNow = bMirror;
        bFlipNow = bFlip;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置图像参数
 * 输入参数: u8Brightness --- 亮度 [0-100]
             u8Contrast --- 对比度 [0-100]
             u8Saturation --- 饱和度 [0-100]
             u8Sharpness --- 锐度 [0-100]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_SetImageParam(uint8 u8Brightness, uint8 u8Contrast, uint8 u8Saturation, uint8 u8Sharpness)
{
	sint32 s32Ret = 0;
    VI_PIPE ViPipe = 0;

	if(u8Brightness > 255 || u8Brightness < 0 || u8Contrast > 255|| u8Contrast <0 || u8Saturation > 255|| u8Saturation <0 || u8Sharpness > 255 || u8Sharpness <0 )
	{
		print_level(SV_ERROR,"Image Param illegal!\n");
		return ERR_ILLEGAL_PARAM;

	}
	s32Ret =  RK_ISP_SET_Brightness(ViPipe, u8Brightness);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_ISP_SET_Brightness failed! [err=0x%x]!\n", s32Ret);
        return s32Ret;
    }
	s32Ret =  RK_ISP_SET_Contrast(ViPipe, u8Contrast);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_ISP_SET_Contrast failed! [err=0x%x]!\n", s32Ret);
        return s32Ret;
    }
	s32Ret =  RK_ISP_SET_Saturation(ViPipe, u8Saturation);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_ISP_SET_Saturation failed! [err=0x%x]!\n", s32Ret);
        return s32Ret;
    }
	s32Ret =  RK_ISP_SET_Sharpness(ViPipe, u8Sharpness);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_ISP_SET_Sharpness failed! [err=0x%x]!\n", s32Ret);
        return s32Ret;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取图像参数
 * 输入参数: 无
 * 输出参数: u8Brightness --- 亮度 [0-100]
             u8Contrast --- 对比度 [0-100]
             u8Saturation --- 饱和度 [0-100]
             u8Sharpness --- 锐度 [0-100]
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_GetImageParam(uint8 *u8Brightness, uint8 *u8Contrast, uint8 *u8Saturation, uint8 *u8Sharpness)
{
    return SV_SUCCESS;
}

sint32 mpp_vi_SetLscAttr(uint32 IspDev, uint32 u32ISO)
{
    return SV_SUCCESS;
}

sint32 mpp_vi_RenewAttr(VIDEO_MODE_EE *pnewVideoMode)
{
    return SV_SUCCESS;
}

sint32 mpp_vi_SetUserPic(sint32 s32Chn, char *pUserPicPath)
{
    return SV_SUCCESS;
}

sint32 mpp_vi_EnableUserPic(sint32 s32Chn)
{
    return SV_SUCCESS;
}

sint32 mpp_vi_DisableUserPic(sint32 s32Chn)
{
    return SV_SUCCESS;
}

sint32 mpp_vi_GetFrame(sint32 s32Chn, sint32 s32VpssChn, void **ppvBuf)
{
    sint32 s32Ret = 0;
    void *szMB;
    void *pbmp;
    MB_IMAGE_INFO_S image_info;
    sint32 s32ViChn = s32Chn;

    if(ppvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }

    szMB = RK_MPI_SYS_GetMediaBuffer(RK_ID_VI, s32ViChn, 200);
    if (szMB == RK_NULL)
    {
        return SV_FAILURE;
    }

    s32Ret = RK_MPI_MB_GetImageInfo(szMB, &image_info);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_MB_GetImageInfo fail! [err=%d]\n", s32Ret);
        RK_MPI_MB_ReleaseBuffer(szMB);
        return SV_FAILURE;
    }

    pbmp = RK_MPI_MB_GetPtr(szMB);
    if(pbmp == NULL)
    {
        print_level(SV_ERROR, "RK_MPI_MB_GetPtr fail\n");
        return SV_FAILURE;
    }

    if (s32ViChn == MPP_VI_CHN_0 || s32ViChn == MPP_VI_CHN_2 || s32ViChn == MPP_VI_CHN_3)
    {
        s32Chn = 0;	// 摄像头通道固定为 0
        s32Ret = mpp_vosd_internal_callback(s32Chn, s32VpssChn, pbmp, image_info.u32Width, image_info.u32Height);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_WARN, "mpp_vosd_callback fail\n");
        }


#if (BOARD == BOARD_ADA32IR)
        if (s32ViChn != MPP_VI_CHN_3) //TC639的AHD通道报警图标等OSD叠加在图像合成后做，不在这里做
#endif
        {
            s32Ret = mpp_vosd_external_callback(s32Chn, s32VpssChn, pbmp, image_info.u32Width, image_info.u32Height);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_WARN, "mpp_vosd_callback fail\n");
            }
        }

        RK_MPI_MB_BeginCPUAccess(szMB, RK_FALSE);
        RK_MPI_MB_EndCPUAccess(szMB, RK_FALSE);
    }

    *ppvBuf = szMB;

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 释放VPSS通道数据
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_VPSS_CHN_BUTT)
             ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
 sint32 mpp_vi_ReleaseFrame(void **ppvBuf)
{
    sint32 s32Ret = 0;
    void *szMB;
    if(ppvBuf == NULL)
    {
        return SV_SUCCESS;
    }

    if(*ppvBuf == NULL)
    {
        return SV_SUCCESS;
    }

    s32Ret = RK_MPI_MB_ReleaseBuffer(*ppvBuf);
    if(s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_MB_ReleaseBuffer failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    *ppvBuf = NULL;
    return SV_SUCCESS;

}

SV_BOOL generate_binary_signals(float prob) 
{
    float r;
    
    if (prob >= 1) {
        return SV_TRUE;
    }
    srand(clock());

    r = (float)rand() / RAND_MAX;
    if (r < prob) {
        return SV_TRUE;
    } else {
        return SV_FALSE;
    }
}

SV_BOOL mpp_vi_IsStreamOn(MPP_VPSS_CHN_TYPE_E type)
{
    sint32 s32Ret;
    DUMP_SHAREFIFO_S stDumpShareFifo;

    s32Ret = dump_GetShareFifoInfo(&stDumpShareFifo);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_WARN, "dump_GetShareFifoInfo failed! [err=%d]\n", s32Ret);
        return SV_TRUE;
    }

    if (type == MPP_VPSS_CHN_PRI) {
        return stDumpShareFifo.bMainStream;
    } else {
        return stDumpShareFifo.bSubStream;
    }
}

void * mpp_vi_MainBody(void *pvArg)
{
    sint32 s32Ret = 0;
    void *mb = NULL;
    sint32 s32ViChn = MPP_VI_CHN_0;
    sint32 s32VpssChn = MPP_VPSS_CHN_PRI;
    sint32 fps;
    float tfps = 30.0;
    const char* threadName = "mpp_vi_MainBody";

    s32Ret = prctl(PR_SET_NAME, threadName);
    if (0 != s32Ret) {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    print_level(SV_INFO, "Start thread [mpp_vi_MainBody], bRunning: %d bInterrupt: %d ISP FPS: %d target FPS: %d.\n",
        m_stViInfo.bRunning, m_stViInfo.bInterrupt, m_stViInfo.u32Fps, m_stViInfo.s32Framerate[s32VpssChn]);

    while (m_stViInfo.bRunning) {
        if (m_stViInfo.bInterrupt)
        {
            sleep_ms(30);
            continue;
        }

        s32Ret = mpp_vi_GetFrame(s32ViChn, s32VpssChn, &mb);
        if(s32Ret != SV_SUCCESS) {
            sleep_ms(30);
            continue;
        }

#if USING_SHAREFIFO_OPT
        //fps = mpp_vi_IsStreamOn(MPP_VPSS_CHN_PRI) ? m_stViInfo.u32Fps : 1;
        fps = mpp_vi_IsStreamOn(MPP_VPSS_CHN_PRI) ? m_stViInfo.s32Framerate[s32VpssChn] : 1;
        tfps = m_stViInfo.u32Fps;

        if (generate_binary_signals(fps / tfps))
#endif
        {
            s32Ret = mpp_venc_SetFrame(s32ViChn, s32VpssChn, mb);
            if(s32Ret != SV_SUCCESS) {
                print_level(SV_INFO, "mpp_venc_SetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
                mpp_vi_ReleaseFrame(&mb);
                continue;
            }
        }
        mpp_vi_ReleaseFrame(&mb);
    }
}

void * mpp_vi_MainBody2(void *pvArg)
{
    sint32 s32Ret = 0;
    void *mb = NULL, *mb_s = NULL;
    sint32 s32ViChn, s32VpssChn;
    sint32 s32ViPipe = 0;
    sint32 fps;
    float tfps = 30.0;
    const char* threadName = "mpp_vi_MainBody2";

    s32Ret = prctl(PR_SET_NAME, threadName);
    if (0 != s32Ret) {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    print_level(SV_INFO, "VI main body, VI[%d] to VPSS(RGA).\n", s32ViPipe);

    while (m_stViInfo.bRunning) {
        if (m_stViInfo.bInterrupt) {
            sleep_ms(30);
            continue;
        }

        s32ViChn = MPP_VI_CHN_1;
        s32VpssChn = MPP_VPSS_CHN_PRI;
        s32Ret = mpp_vi_GetFrame(s32ViChn, s32VpssChn, &mb);
        if(s32Ret != SV_SUCCESS) {
            sleep_ms(30);
            continue;
        }

#if (BOARD != BOARD_ADA32N1)    // 子码流用VI通道2直连VENC
        s32VpssChn = MPP_VPSS_CHN_SEC;
#if USING_SHAREFIFO_OPT
        //fps = mpp_vi_IsStreamOn(MPP_VPSS_CHN_SEC) ? m_stViInfo.u32Fps : 1;
        fps = mpp_vi_IsStreamOn(MPP_VPSS_CHN_SEC) ? m_stViInfo.s32Framerate[s32VpssChn] : 1;
        tfps = m_stViInfo.u32Fps;

        if (generate_binary_signals(fps / tfps))
#endif
        {
            s32Ret = mpp_vpss_SetFrame(s32ViPipe, s32VpssChn, mb);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_INFO, "mpp_vpss_SetFrame[%d %d] fail\n", s32ViPipe, s32VpssChn);
                mpp_vi_ReleaseFrame(&mb);
                continue;
            }
        }
#endif

        s32VpssChn = MPP_VPSS_CHN_JPEG;
#if USING_SHAREFIFO_OPT
        fps = m_stViInfo.s32Framerate[s32VpssChn];
        tfps = m_stViInfo.u32Fps;

        if (generate_binary_signals(fps / tfps))
#endif
        {
            s32Ret = mpp_vpss_SetFrame(s32ViPipe, s32VpssChn, mb);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_INFO, "mpp_vpss_SetFrame[%d %d] fail\n", s32ViPipe, s32VpssChn);
                mpp_vi_ReleaseFrame(&mb);
                continue;
            }
        }

        s32VpssChn = MPP_VPSS_CHN_ALG;
        s32Ret = mpp_vpss_SetFrame(s32ViPipe, s32VpssChn, mb);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "mpp_vpss_SetFrame[%d %d] fail\n", s32ViPipe, s32VpssChn);
            mpp_vi_ReleaseFrame(&mb);
            continue;
        }

        mpp_vi_ReleaseFrame(&mb);
    }
}

void * mpp_vi_MainBody3(void *pvArg)
{
    sint32 s32Ret = 0;
    void *mb = NULL;
    sint32 s32ViChn = MPP_VI_CHN_2;
    sint32 s32VpssChn = MPP_VPSS_CHN_SEC;//MPP_VPSS_CHN_JPEG;
    sint32 s32ViPipe = 0;
    sint32 fps;
    float tfps = 30.0;
    const char* threadName = "mpp_vi_MainBody3";

    s32Ret = prctl(PR_SET_NAME, threadName);
    if (0 != s32Ret) {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    print_level(SV_INFO, "Start thread [mpp_vi_MainBody3], bRunning: %d bInterrupt: %d.\n",
        m_stViInfo.bRunning, m_stViInfo.bInterrupt);

    while (m_stViInfo.bRunning) {
        if (m_stViInfo.bInterrupt)
        {
            sleep_ms(30);
            continue;
        }

        s32Ret = mpp_vi_GetFrame(s32ViChn, s32VpssChn, &mb);
        if(s32Ret != SV_SUCCESS) {
            sleep_ms(30);
            continue;
        }

#if USING_SHAREFIFO_OPT
        fps = mpp_vi_IsStreamOn(MPP_VPSS_CHN_SEC) ? m_stViInfo.s32Framerate[s32VpssChn] : 1;
        tfps = m_stViInfo.u32Fps;

        if (generate_binary_signals(fps / tfps))
#endif
        {
            s32Ret = mpp_venc_SetFrame(s32ViPipe, s32VpssChn, mb);
            if(s32Ret != SV_SUCCESS) {
                print_level(SV_INFO, "mpp_venc_SetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
                mpp_vi_ReleaseFrame(&mb);
                continue;
            }
        }
        mpp_vi_ReleaseFrame(&mb);
    }
}


void mpp_vi_set_chnFramerate(sint32 streamType, sint32 fps)
{
    if (streamType == STREAM_TYPE_PRI) {
        m_stViInfo.s32Framerate[MPP_VPSS_CHN_PRI] = fps;
    } else if (streamType == STREAM_TYPE_SNAP0) {
        m_stViInfo.s32Framerate[MPP_VPSS_CHN_JPEG] = fps;
    } else if (streamType == STREAM_TYPE_SEC) {
        m_stViInfo.s32Framerate[MPP_VPSS_CHN_SEC] = fps;
    } else {
        m_stViInfo.s32Framerate[streamType] = fps;
    }
    print_level(SV_INFO, "VI set fps to %d, stream type: %d.\n", fps, streamType);
}

/******************************************************************************
 * 函数功能: 启动VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_Start()
{
    sint32 s32Ret = 0;
    uint32 u32Tid = 0;

    pthread_attr_t 	attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程

    m_stViInfo.bRunning = SV_TRUE;
    s32Ret = pthread_create(&u32Tid, &attr, mpp_vi_MainBody, NULL);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread [mpp_vi_MainBody] failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    s32Ret = pthread_create(&u32Tid, &attr, mpp_vi_MainBody2, NULL);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread [mpp_vi_MainBody2] failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

#if (BOARD == BOARD_ADA32N1)    // 子码流用VI通道2直连VENC
    s32Ret = pthread_create(&u32Tid, &attr, mpp_vi_MainBody3, NULL);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread [mpp_vi_MainBody] failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止VI模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_Stop()
{
    m_stViInfo.bRunning = SV_FALSE;
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: VI模块重新配置属性
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_RecreateChn(sint32 s32Chn, uint32 u32Width, uint32 u32Height)
{
    sint32 s32Ret;
    VI_PIPE ViPipe = 0;
    VI_CHN ViChn = s32Chn;
    VI_CHN_ATTR_S stViChnAttr;
    
    s32Ret = RK_MPI_VI_GetChnAttr(ViPipe, ViChn, &stViChnAttr);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_VI_SetChnAttr failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    if (stViChnAttr.u32Width == u32Width && stViChnAttr.u32Height == u32Height)
    {
        print_level(SV_INFO, "there is no need for mpp_vi_RecreateChn!\n");
        return SV_SUCCESS;
    }

    print_level(SV_INFO, "s32Chn[%d]\n", s32Chn);
    //print_ViChnAttr(stViChnAttr);

    s32Ret = RK_MPI_VI_DisableChn(ViPipe, ViChn);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_VI_DisableChn failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    sleep_ms(30);

    stViChnAttr.u32Width = u32Width;
    stViChnAttr.u32Height = u32Height;
    s32Ret = RK_MPI_VI_SetChnAttr(ViPipe, ViChn, &stViChnAttr);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_VI_SetChnAttr failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    sleep_ms(30);

    s32Ret = RK_MPI_VI_EnableChn(ViPipe, ViChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VI_EnableChn failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }
    
    s32Ret = RK_MPI_VI_StartStream(ViPipe, ViChn);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VI_EnableChn failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 暂停VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vi_Interrupt(SV_BOOL bInterrupt)
{
    m_stViInfo.bInterrupt = bInterrupt;
    return SV_SUCCESS;
}

sint32 mpp_vi_SetPlayback(SV_BOOL bPlayback)
{
    return SV_SUCCESS;
}

//u32Gain[0-100]
sint32 mpp_vi_SetExposure(EX_TYPE_E enShutter, uint32 u32Gain)
{
	static uint32 u32GainNow=0;
	static EX_TYPE_E enShutterNow = SHUTTERSPEED_BUTT;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;
	if(enShutterNow != enShutter || u32GainNow != u32Gain)
	{
		s32Ret = RK_ISP_SET_ExposureMode(ViChn,enShutter,u32Gain);		
		enShutterNow = enShutter;
		u32GainNow = u32Gain;
	}
	return s32Ret;
}

//u32Level[0-2]
sint32 mpp_vi_SetBackLight(uint32 u32Level	, BLC_AREA_EE enArea)
{
	
	static uint32 u32LevelNow = 1;
	static BLC_AREA_EE enAreaNow = BLC_AREA_AUTO;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;
	if(u32LevelNow != u32Level || enAreaNow != enArea)
	{
		if(enArea == BLC_AREA_DISABLE)
		{
			s32Ret = RK_ISP_SET_BackLight(ViChn,SV_FALSE,u32Level,BLC_AREA_AUTO);
		}
		else
		{			
			s32Ret = RK_ISP_SET_BackLight(ViChn,SV_TRUE,u32Level,enArea);
		}
		u32LevelNow = u32Level;
		enAreaNow = enArea;
	}
	return s32Ret;
}

//u32Level [0-10]
sint32 mpp_vi_SetHDRMode(HDR_MODE_EE enHDRMode,uint32 u32Level)
{
	static uint32 u32LevelNow = -1,enHDRModeNow = HDR_MODE_AUTO;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;

	if(u32LevelNow != u32Level || enHDRModeNow != enHDRMode)
	{		
		s32Ret = RK_ISP_SET_HDRMode(ViChn,enHDRMode,u32Level);
		u32LevelNow = u32Level;
		enHDRModeNow  = enHDRMode;
	}
	return  s32Ret;
	
}

//强光抑制u8Strength[0-255]  		暗区提升	u8Level[0-255]
sint32 mpp_vi_SetLightInhibition(SV_BOOL bEnable,uint8 u8Strength, uint8 u8Level)
{
	static SV_BOOL bEnableNow = SV_FALSE;
	static uint8 u8LevelNow = 0, u8StrengthNow = 100;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;
	if(bEnableNow != bEnable || u8LevelNow != u8Level || u8StrengthNow != u8Strength )
	{
		s32Ret = RK_ISP_SET_LightInhibition(ViChn,bEnable,u8Strength,u8Level);		
		bEnableNow = bEnable;
		u8StrengthNow = u8Strength;
		u8LevelNow = u8Level;
	}
	return s32Ret;
}

//u32RGain u32GGain u32BGain [0-255]
sint32 mpp_vi_SetWhiteBalance(WB_MODE_EE enWBMode,uint32 u32RGain, uint32 u32GGain,uint32 u32BGain)
{
	static SV_BOOL enWBModeNow = SV_TRUE;
	static uint32 u32RGainNow = 0, u32GGainNow = 0,u32BGainNow = 0;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;

	if(enWBModeNow != enWBMode && enWBMode == WB_MODE_AUTO) 
	{
		s32Ret = RK_ISP_SET_AutoWhiteBalance(ViChn);
		enWBModeNow = enWBMode;
	}
	else if((enWBModeNow != enWBMode && enWBMode == WB_MODE_MANUAL) || 
		    (enWBMode == WB_MODE_MANUAL && (u32RGainNow != u32RGain || u32GGainNow != u32GGain || u32BGainNow != u32BGain)))
	{
		s32Ret = RK_ISP_SET_ManualWhiteBalance(ViChn,u32RGain,u32GGain,u32BGain);
		u32RGainNow = u32RGain;
		u32GGainNow = u32GGain;
		u32BGainNow = u32BGain;
		enWBModeNow = enWBMode;
	}
	else if(enWBModeNow != enWBMode && enWBMode != WB_MODE_AUTO && enWBMode != WB_MODE_MANUAL)
	{
		switch(enWBMode)
		{
			case WB_MODE_FluorescentLight:
				u32RGain = 200;
				u32GGain = 220;
				u32BGain = 225;
				break;
			case WB_MODE_IncandescentLight:
				u32RGain = 255;
				u32GGain = 180;
				u32BGain = 100;
				break;
			case WB_MODE_WarmLight:
				u32RGain = 255;
				u32GGain = 220;
				u32BGain = 150;
				break;
			case WB_MODE_NaturalLight:
				u32RGain = 220;
				u32GGain = 220;
				u32BGain = 220;
				break;
			default:
				break;
		}
		s32Ret = RK_ISP_SET_ManualWhiteBalance(ViChn,u32RGain,u32GGain,u32BGain);
		enWBModeNow = enWBMode;
	}
	

	return s32Ret;
}

// enMode 0:off, 1:2d, 2:3d, 3: 2d+3d value:[0-255]
sint32 mpp_vi_SetDNR(DNR_MODE_EE enMode,uint32 u322DValue,uint32 u323DValue)
{
	static DNR_MODE_EE enModeNow = DNR_MODE_BUTT;
	static uint32 u322DValueNow = 100,u323DValueNow = 100;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;
	if(enModeNow != enMode || u322DValueNow != u322DValue || u323DValueNow != u323DValue)
	{
		s32Ret =  RK_ISP_SET_DNRStrength(ViChn,enMode, u322DValue,u323DValue);
		enModeNow = enMode;
		u322DValueNow = u322DValue;
		u323DValueNow = u323DValue;
	}

	return s32Ret;
}

//u32Value[0-255]
sint32 mpp_vi_SetDefog(DF_MODE_EE enMode,uint32 u32Level)
{
	static DF_MODE_EE enModeNow = DF_MODE_BUTT;
	static uint32 u32LevelNow = 100;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;
	if(enModeNow != enMode || u32LevelNow != u32Level)
	{
		s32Ret = RK_ISP_SET_DefogEnable(ViChn,enMode);
		if(enMode != DF_MODE_DISABLE)
		{
			s32Ret |= RK_ISP_SET_DefogStrength(ViChn, enMode,u32Level);
			enModeNow = enMode;
			u32LevelNow = u32Level;
		}
		
	}

	return s32Ret;
}

sint32 mpp_vi_SetImgEnhance(SV_BOOL bEnable)
{
	static SV_BOOL bEnableNow = SV_FALSE;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;
	if(bEnableNow != bEnable)
	{
		s32Ret = RK_ISP_SET_ImgEnhance(ViChn, bEnable);
		bEnableNow = bEnable;
	}
	else
	{
		s32Ret = RK_ISP_SET_ImgEnhance(ViChn, bEnableNow);
	}
	return s32Ret;
}

sint32 mpp_vi_SetFocusMode(FC_MODE_EE enMode)
{	
	static FC_MODE_EE enModeNow = FC_MODE_BUTT;
	VI_CHN ViChn = 0;
	sint32 s32Ret = 0;
	if(enModeNow != enMode)
	{
		s32Ret = RK_ISP_SET_FocusMode(ViChn, enMode);
		enModeNow = enMode;
	}
	return s32Ret;

}
