/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file:       media.cpp
author:     lyn
version:    1.0.0
date:       2023-12-06
function:   recorder media source file
notice:     none
*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <list>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <regex>
#include <sys/prctl.h>

#include "r_media.h"
#include "print.h"
namespace recorder{

/* 内存调试打印开关 */
#undef REC_PRINT_BUFF
#define REC_PRINT_BUFF  0

/******************************************************************************
 * 函数功能: 媒体队列写线程
 * 输入参数: pavArg -- 对象指针
 * 输出参数：无
 * 返回值  : NULL
 * 注意    :   无
 *****************************************************************************/
void * recorder_MediaBody(void *pvArg)
{
    sint32 s32Ret = 0;
    RMedia *pCRMedia = (RMedia*)pvArg;
    
    s32Ret = prctl(PR_SET_NAME, "recorder_MediaBody");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    //print_level(SV_DEBUG, "recorder_MediaBody Start\n");
    while (pCRMedia->bRunning)
    {
        s32Ret = pCRMedia->Write();
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(5);
        }
        sleep_ms(5);
    }
    return NULL;
}

void * recorder_testBody(void *pvArg)
{
    SV_BOOL             bGotIfrm = SV_FALSE;
    sint32 s32Ret = 0;
    RMedia *pCRMedia = (RMedia*)pvArg;
    REC_D_PACK_S        pack;
    list<REC_D_PACK_S>::iterator it;
    time_t  tRecStart = 0, tCurTime = 0, tInterval;
    sint32              s64CurPts = 0;
    sint64              s64StartPts = 0;

retry:
    sleep(3);
    s32Ret = pCRMedia->ReadPackbyTime(it, pack);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR,"read pack failed\n");
        goto retry;
    }
    print_level(SV_WARN, "read [%d] pts[%lld] type[%d]size [%d]\n",pack.num,pack.pts,pack.type,pack.size);
    s64StartPts = (*it).pts; 
    tRecStart = time(NULL);
    tCurTime = time(NULL);

    while (pCRMedia->bRunning)
    {
        s32Ret = pCRMedia->ReadNextPack(it, pack);
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(10);
            continue;
        }
        print_level(SV_DEBUG, "read [%d] pts[%lld] type[%d]size [%d]\n",pack.num,pack.pts,pack.type,pack.size);
        /* 获取I帧 */
        if (SV_FALSE == bGotIfrm)
        {
            if (1 != pack.type)
            {
                continue;
            }
            bGotIfrm = SV_TRUE;
        }
        if (pack.type == 2)
        {
            continue;
        }
        tCurTime = time(NULL);
        tInterval = abs(tCurTime - tRecStart);
        if (tInterval > 10)
        {
           break;
        }
    }
    pCRMedia->ReadClose(it);
    goto retry;
    
    return NULL;
}

/******************************************************************************
 * 函数功能: 构造函数
 * 输入参数: chn -- 通道 path -- fifo 键值
 * 输出参数：无
 * 返回值  : 无
 * 注意    :   无
 *****************************************************************************/
RMedia::RMedia(sint32 chn, char *path)
{
    channel = chn;
    memcpy(szQuePath, path, sizeof(szQuePath));
    totalPackSize = 0;
}

/******************************************************************************
 * 函数功能: 析构函数
 * 输入参数: 无
 * 输出参数：无
 * 返回值  : 无
 * 注意    :   无
 *****************************************************************************/
RMedia::~RMedia()
{

}
/******************************************************************************
 * 函数功能: 创建fofo通道
 * 输入参数: chn -- 通道 path -- fifo 键值
 * 输出参数：无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::Create()
{
    sint32          s32Ret = 0;
    s32Ret = SFIFO_ForReadOpen(szQuePath, &queId, &customerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForRecReadOpen chn[%d] [error = %x]\n", channel, s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}
/******************************************************************************
 * 函数功能: 销毁fifo通道
 * 输入参数: 无
 * 输出参数：无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::Destory()
{
    sint32 s32Ret = SFIFO_ForReadClose(queId, customerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadClose chn[%d] [error = %x]\n", s32Ret);
        return SV_FAILURE;
    }

    packList.clear();
    totalPackSize = 0;
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动队列管理的相关线程
 * 输入参数: 无
 * 输出参数：无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::Start()
{
    sint32          s32Ret = 0;
    bRunning = SV_TRUE;
    s32Ret = pthread_create(&thread, NULL, recorder_MediaBody, this);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
#if 0
    s32Ret = pthread_create(&thread, NULL, recorder_testBody, this);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
#endif
    return SV_SUCCESS;
}
/******************************************************************************
 * 函数功能: 结束相关线程
 * 输入参数: 无
 * 输出参数：无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::Stop()
{
    sint32          s32Ret      = 0;
    void            *pvRetval   = NULL;
    bRunning = SV_FALSE;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 格式转换，转换sharefifo的包为队列包格式
 * 输入参数: pstPack -- fifo包
 * 输出参数：pstPack -- 队列包
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::CreatePack(REC_D_PACK_S *pstPack, SFIFO_MSHEAD *pfifoData)
{
    static int num = 0;

    if (0 == pfifoData->pts)
    {
        return SV_FAILURE;
    }
    pstPack->num = num++;
    pstPack->use  = 0;
    pstPack->type = pfifoData->type;
    pstPack->size = pfifoData->msdsize;
    pstPack->pts = pfifoData->pts;
    //pstPack->data = malloc(pfifoData->msdsize);
    pstPack->data = std::shared_ptr<char[]>(new char[pfifoData->msdsize], std::default_delete<char[]>());
    if (NULL == pstPack->data)
    {
        return SV_FAILURE;
    }
    memcpy((pstPack->data).get(), pfifoData->data, pfifoData->msdsize);
    if (0 == num%1500)
    {
        num = 0;
    }
    /* 将数据包压入队列 */
    totalPackSize += pstPack->size;
    dataMutex.lock();
    packList.push_back(*pstPack);
    dataMutex.unlock();
#if 0
    print_level(SV_DEBUG,"write [%d] type[%d] size[%d] pts[%lld] data[%x] totalSize[%d] count[%d]\n",\
                pstPack->num,pstPack->type, pstPack->size, pstPack->pts, (pstPack->data).get(), totalPackSize, packList.size());
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 释放队列的包
 * 输入参数: 无
 * 输出参数：无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::DestoryPack()
{
    REC_D_PACK_S        stPack = {0};
    
    dataMutex.lock();
    stPack = packList.front();
    
    if (0 == stPack.use)
    {
        totalPackSize -= stPack.size;
        //free(stPack.data);
        packList.pop_front();
    }
    dataMutex.unlock();
#if 0
    print_level(SV_DEBUG,"release [%d] pack[%d] [%lld] count[%d]\n",stPack.num, stPack.use,stPack.pts,packList.size());
#endif
    return SV_SUCCESS;
}
/******************************************************************************
 * 函数功能: 往队列写包
 * 输入参数: 无
 * 输出参数：无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::Write()
{
    sint32              s32Ret = 0;
    SFIFO_MSHEAD        *pstPacket;
    REC_D_PACK_S        stPack = {0};
    /* 从fifo队列获取包 */
    s32Ret = SFIFO_RecGetPacket(queId, customerId, &pstPacket);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }
    /* 拷贝包到新的队列 */
    s32Ret = CreatePack(&stPack, pstPacket);
    /* 根据大小覆盖旧包 */
    if (totalPackSize > REC_PACK_LIST_SIZE)
    {
        s32Ret = DestoryPack();
    }
    s32Ret = SFIFO_RecReleasePacket(queId, customerId, pstPacket);
    if (SV_SUCCESS != s32Ret)
    {
        return SV_FAILURE;
    }

    return s32Ret;
}

/******************************************************************************
 * 函数功能: 往队列读取包
 * 输入参数: timestamp -- 时间戳
 * 输出参数：pack -- 数据包
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::ReadPackbyTime(uint64 timestamp, list<REC_D_PACK_S>::iterator &resIter, REC_D_PACK_S &pack)
{
    sint32      s32Time = 5;
    sint32      s32Tmp  = 0;
    dataMutex.lock();
    /* 查找第一个时间戳比设置时间戳大的包*/
    auto it = find_if(packList.begin(), packList.end(), \
        [timestamp](REC_D_PACK_S &stPack){
         //print_level(SV_WARN,"comp [%d][%llu] [%llu]\n",stPack.num,timestamp, stPack.pts);
         if (timestamp <= stPack.pts)
         {
            
            return true;
         }
         return false;
        });
    dataMutex.unlock();
    /* 查找到包 */
    if (it != packList.end())
    {   dataMutex.lock();
        s32Tmp = (*it).use;
        (*it).use = (s32Tmp <= 0) ? 1 : (s32Tmp+1);
        pack = (*it);
        dataMutex.unlock();
    }
    else
    {
retry:
        if (packList.empty()&& (s32Time > 0))
        {
            s32Time--;
            sleep_ms(30);
            goto retry;
        }
        if (packList.empty())
        {
            return SV_FAILURE;;
        }
        dataMutex.lock();
        it = packList.begin();
        s32Tmp = (*it).use;
        (*it).use = (s32Tmp <= 0) ? 1 : (s32Tmp+1);
        pack = (*it);
        dataMutex.unlock();
    }
    resIter = it;
    return SV_SUCCESS;
}
/******************************************************************************
 * 函数功能: 重载读取包函数，普通录像不需要根据时间戳获取包
 * 输入参数: 无
 * 输出参数：pack -- 数据包
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::ReadPackbyTime(list<REC_D_PACK_S>::iterator &resIter, REC_D_PACK_S &pack)
{
    sint32  s32Time = 5;
    sint32  s32Tmp;
retry:
    if (packList.empty()&& (s32Time > 0))
    {
        s32Time--;
        sleep_ms(30);
        goto retry;
    }
    if (packList.empty())
    {
        //print_level(SV_ERROR, "packList is empty\n");
        return SV_FAILURE;
    }
    dataMutex.lock();
    auto it = prev(packList.end());
    s32Tmp = (*it).use;
    (*it).use = (s32Tmp <= 0) ? 1 : (s32Tmp+1);
    pack = (*it);
    resIter = it;
    dataMutex.unlock();
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读取下一个数据包，需要先调ReadPackbyTime获取第一个开始的包
 * 输入参数: next -- 队列迭代器指针
 * 输出参数：pack -- 数据包
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
sint32 RMedia::ReadNextPack(list<REC_D_PACK_S>::iterator &it, REC_D_PACK_S &pack)
{
    sint32  s32Tmp;
    list<REC_D_PACK_S>::iterator it_next;
    dataMutex.lock();
    it_next = it;
    ++it_next;
    if (it_next == packList.end())
    {
        dataMutex.unlock();
        return SV_FAILURE;
    }
    else
    {
        /* 释放上一个包 */
        s32Tmp = (*it).use;
        (*it).use = (s32Tmp <= 0) ? 0 : (s32Tmp-1);
        /* 标记本包 */
        it++;
        s32Tmp = (*it).use;
        (*it).use = (s32Tmp <= 0) ? 1 : (s32Tmp+1);
        pack = (*it);
    }
    dataMutex.unlock();
    return SV_SUCCESS;
}

sint32 RMedia::ReadClose(list<REC_D_PACK_S>::iterator &it)
{
    sint32 s32Tmp = 0;
    dataMutex.lock();
    s32Tmp = (*it).use;
    (*it).use = (s32Tmp <= 0) ? 0 : (s32Tmp-1);
    dataMutex.unlock();
    return SV_SUCCESS;
}

sint32 RMedia::GetMediaAttr(REC_MEDIA_CONF_S *pstMediaCfg)
{
    sint32                  s32Ret              = 0;
    SFIFO_MEDIA_ATTR        stSfifoMediaAttr    = {0};
    
    s32Ret = SFIFO_GetMediaAttr(queId, &stSfifoMediaAttr);
    if (SV_SUCCESS != s32Ret || !stSfifoMediaAttr.stMainStreamAttr.bValid)
    {
        print_level(SV_WARN, "chn[%d] SFIFO_GetMediaAttr failed. [err=%#x]\n",channel, s32Ret);
        return SV_FAILURE;
    }
    pstMediaCfg->u32Width = stSfifoMediaAttr.stMainStreamAttr.u32Width;
    pstMediaCfg->u32Height = stSfifoMediaAttr.stMainStreamAttr.u32Height;
    pstMediaCfg->u32Framerate = stSfifoMediaAttr.stMainStreamAttr.u32FrameRate;
    pstMediaCfg->u32Bitrate = stSfifoMediaAttr.stMainStreamAttr.u32Bitrate;

    return SV_SUCCESS;
}

}
