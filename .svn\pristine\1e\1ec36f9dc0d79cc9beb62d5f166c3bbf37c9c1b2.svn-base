/******************************************************************************
Copyright (C) 2018-2020 广州敏视数码科技有限公司版权所有.

文件名: DMS31Handle.c

作者: czr   版本: v1.0.0(初始版本号)    日期: 2021-09-13

文件功能描述: 定义DMS31v2测试指令消息处理功能函数

版本: v1.0.0(最新版本号)
  
历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <termios.h>    

#ifndef __HuaweiLite__
#include <linux/ioctl.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#endif

#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>
#include <ctype.h>

#include "common.h"
#include "print.h"
#include "board.h"
#include "msg.h"
#include "safefunc.h"
#include "config.h"
#include "wifi.h"
#include "cJSON.h"
#include "uuid.h"
#include "libhttp.h"
#include "factory.h"
#include "recorder.h"
#include "DMS31Handle.h"

#define UPLOAD_VIDEO_FILE_FLAG     "/var/info/recorder"
#define CELL_INFO_FILE             "/var/info/cellular"
#define GPS_INFO_FILE              "/var/info/gps"

/* 4G模块厂商ID */
#define VID_GEMATO          "0061"
#define VID_SERRIA          "1199"
#define VID_QUECTEL         "05c6"
#define VID_HUAWEI          "12d1"   
#define VID_TELIT           "0036"
#define VID_TELIT910_C1     "1201"
#define VID_YIYUAN          "2c7c"
#define VID_FIBOCOM         "1508:1001"
/* SIMCOM模块厂商和设备ID */
#define VPID_SIM7600        "1e0e:9001"

#define UUID_PATH               "/etc/uuid"
#define CELL_INFO_PATH          "/var/info/cellular"
#define CELL_TEST_PATH          "/tmp/AutoTest/CellTest.txt"
#define CELL_READY_WAV_PATH     "/tmp/AutoTest/CellReady.wav"
#define CELL_ERROR_WAV_PATH     "/tmp/AutoTest/CellTestError.wav"
#define CELL_FINISH_WAV_PATH    "/tmp/AutoTest/CellTestFinish.wav"

#define EC25_AT_PORT            "/dev/ttyUSB2"          /* EC25的AT指令端口 */
#define SIM7600_AT_PORT         "/dev/ttyUSB2"          /* SIMCOM的AT指令端口 */

sint32 g_s32CanFd = 0;
sint32 g_s32CanFlag = 0;
sint32 g_s32SerialFd_update;
sint32 g_s32CellFd = -1; 

#define EC25_AT_PORT_PATH         "/sys/devices/platform/ffe00000.usb/usb1/1-1/1-1.2/1-1.2:1.2"
#define SIM7600_AT_PORT_PATH      "/sys/devices/platform/ffe00000.usb/usb1/1-1/1-1.2/1-1.2:1.2"

/* 串口控制信息 */
typedef struct tag_SERIALInfo_S
{
    sint32                      s32SerialFd;               
    SV_BOOL                     bRunning;            /* 线程运行状态 */
    uint32                      u32Tid;              /* 线程ID */
}STSerialInfo;

STSerialInfo m_stSerialInfo;

/* 向测试板发送指令信息 */
typedef struct set_STPowerdownInfo
{
    sint32                  s32SerialFd;                
    uint8                   au8Cmd[512];            /* 设置测试板掉电的AT指令 */
}STPowerdownInfo;

STPowerdownInfo g_stPowerdownInfo;

/* 解析参数, 获取string */
sint32 param2string(sint8 *pCmdPara, sint8 *pOutBuf, const sint8 *pBuf)
{
    cJSON *pstJson, *pstParam;
    
    pstJson = cJSON_Parse(pCmdPara); // 解析参数
    if (!pstJson) 
    {
        print_level(SV_ERROR, "%s\n", "Error before param2string cJSON_Parse");
        goto err;
    }   
    else    
    {   
        pstParam = cJSON_GetObjectItem(pstJson, pBuf);
        if(pstParam == NULL)
        {
            print_level(SV_ERROR, "%s\n", "pstParam is NULL!");
            goto err;
        }
        
        if(strlen(pstParam->valuestring) < 128)
        {
            memset(pOutBuf, 0, 128);
            memcpy(pOutBuf, pstParam->valuestring, strlen(pstParam->valuestring));
        }
        else
        {
            print_level(SV_ERROR, "%s\n", "string length is too long!");
            goto err;
        }
    }

    cJSON_Delete(pstJson);     
    return 0;
    
err:
    cJSON_Delete(pstJson);
    return -1;
}

/* 获取数字 num */
sint32 param2num(sint8 *pCmdPara, sint32 *pNum, const sint8 *pBuf)
{
    cJSON *pstJson, *pstParam;
    
    pstJson = cJSON_Parse(pCmdPara);
    if (!pstJson) 
    {
        print_level(SV_ERROR, "%s\n", "Error before param2num:cJSON_Parse");
        goto err;
    }   
    else    
    {   
        pstParam = cJSON_GetObjectItem(pstJson, pBuf);
        if(pstParam == NULL)
        {
            print_level(SV_ERROR, "%s\n", "pstParam is NULL!");
            goto err;
        }

        *pNum = pstParam->valuedouble;
    }

    cJSON_Delete(pstJson);
    return 0;
    
err:
    cJSON_Delete(pstJson);
    return -1;
}

/*******************************************************************************
  * @函数名称:  getRtcTime
  * @函数说明: 获取硬件时间
  * @输入参数: 无
  * @输出参数:     无
  * @返回参数: 成功或者失败
*******************************************************************************/
sint32 getRtcTime(const sint8 *pGetRtcTime)
{
    long int timeSum = 0;
    sint8 szTmpBuf[64] = {0};
    struct tm *pstTm = NULL;
    struct tm stTime;
    
    time_t captureTime;
    time(&captureTime);

    pstTm = gmtime_r(&captureTime, &stTime);    
    printf("getTime: %04d-%02d-%02d %02d:%02d:%02d \n", (1900 + stTime.tm_year), (1 + stTime.tm_mon), 
          (stTime.tm_mday), (stTime.tm_hour), (stTime.tm_min), (stTime.tm_sec));
    captureTime = mktime(&stTime);
    timeSum = (long int)captureTime;
    snprintf(szTmpBuf, 64, "%ld", timeSum);
    memcpy(pGetRtcTime, szTmpBuf, 64);

    return SV_SUCCESS;
}

/*******************************************************************************
  * @函数名称:  setRtcTime
  * @函数说明: 设置硬件时间
  * @输入参数: 无
  * @输出参数:     无
  * @返回参数: 成功或者失败
*******************************************************************************/
sint32 setRtcTime(const sint8 *pSetRtcTime)
{
    sint32 s32Ret = SV_FAILURE;
    struct tm *pstTm = NULL;    
    struct tm stTime;
    
    long lTmpNum = atol(pSetRtcTime);
    
    if (lTmpNum <= 0)
    {
        printf("params parse error!\n");
        return SV_FAILURE;
    }

    pstTm = gmtime_r(&lTmpNum, &stTime);
    /*
    printf("setTime: %04d-%02d-%02d %02d:%02d:%02d \n", (1900 + pstTm->tm_year), (1 + pstTm->tm_mon), 
          (pstTm->tm_mday), (pstTm->tm_hour), (pstTm->tm_min), (pstTm->tm_sec));
    */
    printf("setTime: %04d-%02d-%02d %02d:%02d:%02d \n", (1900 + stTime.tm_year), (1 + stTime.tm_mon), 
          (stTime.tm_mday), (stTime.tm_hour), (stTime.tm_min), (stTime.tm_sec));
    
    time_t mtime = mktime(&stTime);
    stime(&mtime);//设置系统时间

    char szCmd[64];
    memset(szCmd, 0, sizeof(szCmd));
    sprintf(szCmd, "hwclock -w");
    s32Ret = SAFE_System(szCmd, 2000);
    if(0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        s32Ret = SAFE_System(szCmd, 2000);
    }
    
    //system("hwclock --systohc");
    if (SV_SUCCESS != s32Ret)
        return SV_FAILURE;
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查恢复出厂配置参数合法性
 * 输入参数: pszInJson --- json字符串
 * 输出参数: 无
 * 返回值  : SV_TRUE - 合法
             SV_FALSE - 非法
 * 注意    : 无
 *****************************************************************************/
SV_BOOL checkJsonRestoreParam(cJSON *pstJson)
{
    cJSON *pstType;

    pstType = cJSON_GetObjectItemCaseSensitive(pstJson, "type");
    if (NULL == pstType)
    {
        return SV_FALSE;
    }

    if (NULL != pstType && (!cJSON_IsString(pstType) || 
        (0 != strcmp(pstType->valuestring, "soft")
         && 0 != strcmp(pstType->valuestring, "hard"))))
    {
        return SV_FALSE;
    }

    return SV_TRUE;
}

char * transWifiAuth2String(uint32 enAuth)
{
    switch (enAuth)
    {
        case 0:
            return "NONE";
        case 1:
            return "WPAPSK";
        default:
            return NULL;
    }
}

/*******************************************************************************
  * @函数名称:  setCleanLineBreak
  * @函数说明: 去掉换行符
  * @输入参数: sint8* buf
  * @输出参数:     无
  * @返回参数:     无
*******************************************************************************/
void setCleanLineBreak(const sint8 *ps8Buf)
{
    sint8 *ps8Tmp = NULL;
    if ((ps8Tmp = strstr(ps8Buf, "\n")))
        *ps8Tmp = '\0';
}

/*******************************************************************************
  * @函数名称:  getCameraStatus
  * @函数说明: 获取摄像头输入是否正常
  * @输入参数: 无
  * @输出参数:     无
  * @返回参数:     无
*******************************************************************************/
sint32 getCameraStatus(void)
{
    sint32 s32Num1 = 0, s32Num2 = 0;
    sint32 s32Res = 0;
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8Buf[CMD_LEN] = {0};
    sint8 as8TmpBuf[CMD_LEN] = {0};

    /* 第一次获取输入帧数 */
    snprintf(as8Cmd, CMD_LEN, "cat /proc/rkcif_mipi_lvds | grep \"frame amount\"");  

    SAFE_System_Recv(as8Cmd, as8Buf, CMD_LEN);
    setCleanLineBreak(as8Buf);
    sscanf(as8Buf, "%*[^:]%s", as8TmpBuf); 
    strcpy(as8TmpBuf, as8TmpBuf + 1);

    s32Num1 = atoi(as8TmpBuf);
    print_level(SV_INFO, "Num1 : %d\n", s32Num1);
    sleep_ms(1000);

    /* 第二次获取输入帧数 */
    memset(as8Cmd, 0, sizeof(as8Cmd));
    snprintf(as8Cmd, CMD_LEN, "cat /proc/rkcif_mipi_lvds | grep \"frame amount\"");

    SAFE_System_Recv(as8Cmd, as8Buf, CMD_LEN);
    setCleanLineBreak(as8Buf);
    sscanf(as8Buf, "%*[^:]%s", as8TmpBuf);
    strcpy(as8TmpBuf, as8TmpBuf + 1);

    s32Num2 = atoi(as8TmpBuf);
    print_level(SV_INFO, "Num2 : %d\n", s32Num2);

    /* 获取结果判断输入是否正常 */
    s32Res = s32Num2 - s32Num1;
    if (s32Res > 0)
    {
        return SV_SUCCESS;
    }
    return SV_FAILURE;
}

/*******************************************************************************
  * @函数名称:  getCameraStatus
  * @函数说明: 获取vo出图是否正常
  * @输入参数: 无
  * @输出参数:     无
  * @返回参数:     无
*******************************************************************************/
sint32 getVoStatus(void)
{
    sint32 s32Num = 0;
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8Buf[CMD_LEN] = {0};
    sint8 as8TmpBuf[CMD_LEN] = {0};
    
    snprintf(as8Cmd, CMD_LEN, "modeprint /dev/dri/card0 -plane | grep \"Plane Run State\""); 
    SAFE_System_Recv(as8Cmd, as8Buf, CMD_LEN);
    setCleanLineBreak(as8Buf);
    strcpy(as8TmpBuf, as8Buf + (strlen(as8Buf) - 1));   
    s32Num = atoi(as8TmpBuf);

    if (s32Num == 1)
    {
        return SV_SUCCESS;
    }
    
    return SV_FAILURE;
}

/*******************************************************************************
  * @函数名称:  getCanStatus
  * @函数说明: 获取CAN硬件是否正常运行
  * @输入参数: 无
  * @输出参数:     无
  * @返回参数: 成功或者失败
*******************************************************************************/
sint32 getCanStatus(void)
{
    sint8 as8Buf[256] = {0};

    /* 判断CAN0 设备是否存在 */
    SAFE_System_Recv((char*)"ifconfig -a | grep can0 | awk -F ' ' '{print$1}'", as8Buf, CMD_LEN);
    setCleanLineBreak(as8Buf);

    if (0 != strncmp(as8Buf, "can", 3))
    {
        printf("no can device\n");  
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 serial_init(const sint8* port, sint32 baud)
{
    sint32 s32FdUpdate;
    struct termios stAttr;
    sint32 s32Ret = -1;
    s32FdUpdate = open(port, O_RDWR|O_NOCTTY|O_NDELAY);
    if (s32FdUpdate < 0)
    {        
        print_level(SV_ERROR, "serial_init: %s Open failed\n", port);
        close(s32FdUpdate);
        return SV_FALSE;
    }
    print_level(SV_INFO, "serial_init: %s Open successful\n", port);
    tcgetattr(s32FdUpdate, &stAttr);
    bzero(&stAttr, sizeof(stAttr));

    switch (baud) {
        case 9600:
            cfsetispeed(&stAttr, B9600);
            cfsetospeed(&stAttr, B9600);
            break;
        case 19200:
            cfsetispeed(&stAttr, B19200);
            cfsetospeed(&stAttr, B19200);
            break;
        case 38400:
            cfsetispeed(&stAttr, B38400);
            cfsetospeed(&stAttr, B38400);
            break;
        case 115200:
            cfsetispeed(&stAttr, B115200);
            cfsetospeed(&stAttr, B115200);
            break;
        default:
            fprintf(stderr, "Warning: Baudrate not supported!\n");
            close(s32FdUpdate);
            return -1;
    }

    stAttr.c_cflag |= CLOCAL | CREAD;
    stAttr.c_cflag &= ~CSIZE;
    stAttr.c_cflag |= CS8;
    stAttr.c_cflag &= ~PARENB;
    stAttr.c_cflag &= ~CSTOPB;
    stAttr.c_cflag &= ~CRTSCTS;
    stAttr.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    stAttr.c_oflag &= ~OPOST;
    s32Ret = tcflush(s32FdUpdate, TCIFLUSH);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "tcflush failed. [err=%#x]\n", errno);
    }
    stAttr.c_cc[VTIME] = 255;
    stAttr.c_cc[VMIN] = 0;
    s32Ret = tcsetattr(s32FdUpdate, TCSANOW, &stAttr);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "tcsetattr failed. [err=%#x]\n", errno);
    }
    g_s32SerialFd_update = s32FdUpdate;

    /* TCIFLUSH: flushes data received but not read.
     * TCOFLUSH: flushes data written but not transmitted
     * TCIOFLUSH: flushes both data received but not read, and data written but not transmitted
     */
    tcflush(g_s32SerialFd_update, TCIOFLUSH);  /* 清空串口1的读写缓冲 */

    return SV_TRUE;
}

/* 检查是否插入SD卡 */
sint32 isSDCardInsert(void)
{
    sint32 s32Ret = 0;
    sint8 as8Cmd[1024] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson;

    snprintf(as8Cmd, 1024, "cat /var/info/storage | grep devStat");  
    s32Ret = SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);
    
    ps8Pos = strstr(as8TmpBuf, "devStat");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("devStat") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "final result = %s \n", as8Buf);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }

    if (0 == strncmp(as8Buf, "0", strlen("0")))
    {
        return SV_FALSE;
    }
    else
    {
        return SV_TRUE;
    }
}

/* 检查存储分区是否挂载上 */
sint32 isStorageMounted(void)
{
    sint32 s32Ret = 0;
    sint8 as8Cmd[1024] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson;

    snprintf(as8Cmd, 1024, "cat /var/info/storage | grep devStat");  
    s32Ret = SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);
    
    ps8Pos = strstr(as8TmpBuf, "devStat");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("devStat") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "final result = %s \n", as8Buf);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }

    if (0 == strncmp(as8Buf, "2", strlen("2")))
    {
        return SV_TRUE;
    }
    else
    {
        return SV_FALSE;
    }
}

sint32 isStorageWritable(void)
{
    sint32 s32Ret = 0;
    sint8 as8Cmd[1024] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson;

    snprintf(as8Cmd, 1024, "cat /var/info/storage | grep devStat");  
    s32Ret = SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);

    ps8Pos = strstr(as8TmpBuf, "devStat");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("devStat") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "final result = %s \n", as8Buf);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }

    if (0 == strncmp(as8Buf, "1", strlen("1")) || 0 == strncmp(as8Buf, "2", strlen("2")))
    {
        return SV_TRUE;
    }
    else
    {
        return SV_FALSE;
    }
}

sint32 IsStorageEnable(void)
{
    sint32 s32Ret = 0;
    sint8 as8Cmd[1024] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson;

    snprintf(as8Cmd, 1024, "cat /var/info/storage | grep enable");   
    s32Ret = SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);

    ps8Pos = strstr(as8TmpBuf, "enable");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("enable") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "final result = %s \n", as8Buf);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }

    if (0 == strncmp(as8Buf, "true", strlen("true")))
    {
        return SV_TRUE;
    }
    else
    {
        return SV_FALSE;
    }
}



/* 获取存储分区的总容量 */
sint32 getStorageTotalSize(void)
{
    sint32 s32Ret = 0;
    sint32 s32Num;
    sint8 as8Cmd[1024] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson;

    snprintf(as8Cmd, 1024, "cat /var/info/storage | grep fsTotalSizeMB");    
    s32Ret = SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);
    
    ps8Pos = strstr(as8TmpBuf, "fsTotalSizeMB");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("fsTotalSizeMB") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "final result = %s \n", as8Buf);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }
    
    s32Num = atoi(as8Buf);
    print_level(SV_INFO, "Num result = %d \n", s32Num);

    return s32Num;
}



/* 获取存储分区的剩余容量 */
sint32 getStorageRemainSize(void)
{
    sint32 s32Ret = 0;
    sint32 s32Num;
    sint8 as8Cmd[1024] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson;

    snprintf(as8Cmd, 1024, "cat /var/info/storage | grep fsRemainSizeMB");   
    s32Ret = SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);
    
    ps8Pos = strstr(as8TmpBuf, "fsRemainSizeMB");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        sscanf(as8Buf, "%[^,]", as8Buf);
        strcpy(as8Buf, as8Buf + strlen("fsRemainSizeMB") + 2);
        print_level(SV_INFO, "final result = %s \n", as8Buf);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }
    
    s32Num = atoi(as8Buf);
    print_level(SV_INFO, "Num result = %d \n", s32Num);

    return s32Num;
}


/* 判断是否在录像中 */
sint32 isRecording(void)
{
    sint32 s32Ret = 0;
    sint8 as8Cmd[1024] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson;

    snprintf(as8Cmd, 1024, "cat /var/info/recorder | grep recording");  
    s32Ret = SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);

    ps8Pos = strstr(as8TmpBuf, "recording");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf+strlen("recording")+2);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }

    sscanf(as8Buf, "%[^}]", as8Buf);
    print_level(SV_INFO, "final result = %s \n", as8Buf);

    if (0 == strncmp(as8Buf, "true", strlen("true")))
    {
        return SV_TRUE;
    }
    else
    {
        return SV_FALSE;
    }
}


/*******************************************************************************
  * @函数名称:  getUploadVideoName
  * @函数说明: 获取上传录像的名字
  * @输入参数: 无
  * @输出参数:     无
  * @返回参数: 成功或者失败
*******************************************************************************/
sint32 getUploadVideoName(const sint8 *pNewName, const sint8 *pUpVideoName)
{
    sint32 s32Ret = -1;
    sint8 as8Cmd[512] = {0};
    sint8 as8TmpBuf[1024] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 *ps8Pos;
    REC_FILE_INFO_S stRecInfo;
    MSG_PACKET_S stRetPkt = {0};
    MSG_PACKET_S stMsgPkt = {0};
    REC_TYPE_E eRecType = REC_TYPE_NORMAL;

#if 0
    //1 判断flag 存在与否
    if (access(UPLOAD_VIDEO_FILE_FLAG, F_OK) != SV_SUCCESS)
    {
        print_level(SV_ERROR, "%s not exist\n", UPLOAD_VIDEO_FILE_FLAG);
        return SV_FAILURE;
    }

    // 将里面内容读出来
    snprintf(as8Cmd, 512, "cat %s", UPLOAD_VIDEO_FILE_FLAG);
    SAFE_System_Recv(as8Cmd, as8TmpBuf, 1024);
    setCleanLineBreak(as8TmpBuf);
    
    ps8Pos = strstr(as8TmpBuf, "lastRecFilePath");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("lastRecFilePath") + 3);
        sscanf(as8Buf, "%[^\"]", as8Buf);
        print_level(SV_INFO, "final result = %s \n", as8Buf);
    }
    else
    {
        sprintf(as8Buf, "%s", as8TmpBuf);
    }
    strcpy(pUpVideoName, as8Buf);
    print_level(SV_INFO, "pUpVideoName:%s\n", pUpVideoName);

    // 判断对应录像文件是否存在
    if (SV_SUCCESS != access(pUpVideoName, F_OK))
    {
        print_level(SV_INFO, "%s not exist\n", pUpVideoName);
        return SV_FAILURE;
    }
#endif

    /* 获取普通录像的最新的文件名 */
    stMsgPkt.pu8Data = (uint8 *)&eRecType;
    stMsgPkt.u32Size = sizeof(eRecType);
    stMsgPkt.stMsg.s32Param = STORAGE_MAIN_SD1;

    stRetPkt.pu8Data = (uint8 *)&stRecInfo;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_GET_RECORDER_VEDIO_PATH, &stMsgPkt, &stRetPkt, sizeof(REC_FILE_INFO_S));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "get normal vedio info failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    strncpy(pUpVideoName, stRecInfo.szFilepath, REC_FILE_NAME_LEN);
    print_level(SV_INFO, "pUpVideoName = %s\n", pUpVideoName);

    // 重新命名上传视频的名字
    memset(as8Cmd, 0, sizeof(as8Cmd));
    
    if (NULL != strstr(pUpVideoName, "avi"))
    {
        snprintf(as8Cmd, 256, "echo \"%s\" | awk -F '/'  '{printf$8}'", pUpVideoName);
    }
    else
    {
        snprintf(as8Cmd, 256, "echo \"%s\" | awk -F '/'  '{printf$8}' | sed 's/_.*\\././g'", pUpVideoName);
    }   

    SAFE_System_Recv(as8Cmd, pNewName, 256);
    print_level(SV_INFO, "pNewName:%s\n", pNewName);
    
    return SV_SUCCESS;
}

/* 将sint32类型的4G模块类型转换成sint8类型 */
sint32 transNum2ModuleType(sint32 s32Num, sint8 *ps8ModuleType)
{
    switch(s32Num)
    {
        case CELLULAR_GEMATO:
            strcpy(ps8ModuleType, "GEMATO");
            break;
        case CELLULAR_SERRIA:
            strcpy(ps8ModuleType, "SERRIA");
            break;
        case CELLULAR_HUAWEI:
            strcpy(ps8ModuleType, "HUAWEI");
            break;
        case CELLULAR_HUAWEI_MU709S_6:
            strcpy(ps8ModuleType, "HUAWEI_MU709S_6");
            break;
        case CELLULAR_HUAWEI_ME909S_120:
            strcpy(ps8ModuleType, "HUAWEI_ME909S_120");
            break;
        case CELLULAR_TELIT:
            strcpy(ps8ModuleType, "TELIT");
            break;
        case CELLULAR_TELIT910C1:
            strcpy(ps8ModuleType, "TELIT910C1");
            break;
        case CELLULAR_FIBOCOM:
            strcpy(ps8ModuleType, "FIBOCOM");
            break;
        case CELLULAR_EC25:
            strcpy(ps8ModuleType, "EC25");
            break;
        case CELLULAR_EC200T:
            strcpy(ps8ModuleType, "EC200T");
            break;
        case CELLULAR_SIM7600:
            strcpy(ps8ModuleType, "SIM7600");
            break;
		case CELLULAR_EC200A:
            strcpy(ps8ModuleType, "EC200A");
            break;
		case CELLULAR_EC200U:
            strcpy(ps8ModuleType, "EC200U");
            break;
        default:
            strcpy(ps8ModuleType, "Unknown type");
            break;
    }
    
    return SV_SUCCESS;
}

void* setTBPowerdown(void *pvArg)
{
    sint32 s32SerialFd = -1;
    sint32 s32Ret = -1;
    sint32 s32WriteLen = -1;
    
    usleep(1000);
    STPowerdownInfo *pPowerdownInfo = (STPowerdownInfo *)pvArg;
    s32SerialFd = pPowerdownInfo->s32SerialFd;
    
    /* 发送指令给串口 */
    s32WriteLen = write(s32SerialFd, pPowerdownInfo->au8Cmd, strlen(pPowerdownInfo->au8Cmd));
    print_level(SV_INFO, "SendTBcmd s32WriteLen = %d\n", s32WriteLen);
    close(s32SerialFd);
}

/******************************************************************************
 * 函数功能: 去换行符\r \n
 * 输入参数: pBuf -- 数据指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellCutChar(char* pszBuf,uint32 s32Len)
{
    sint32 s32Cnt = 0;
    sint32 i = 0;
    char szTemp[AT_BUFFER_SIZE] = {0};

    if (NULL == pszBuf)
    {
        return SV_FAILURE;
    }
    for (i = 0; i < s32Len; i++)
    {
        if(pszBuf[i] != '\n' && pszBuf[i] != '\r')
        {
            szTemp[s32Cnt] = pszBuf[i];
            s32Cnt++;
        }
    }
    strncpy(pszBuf, szTemp, AT_BUFFER_SIZE);
    
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 根据设备文件查找对应端口号
 * 输入参数: u32Size -- 输出数据长度
 * 输出参数: port -- 返回端口
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 searchCellDev(char *port, uint32 u32Size, char *portPath)
{
    sint32 s32Ret = -1;
    char szBuf[128] = {0};
    char szCmd[128] = {0};
    char szPath[128] = {0};
    char *pcTmp = NULL;

    sprintf(szCmd, "find /sys/devices/platform/ -name \"*-1.2\:1.2\"");
    s32Ret = GetInsContext(szCmd, szPath, sizeof(szPath));

    /* 去换行符 */
    pcTmp = strchr(szPath, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szPath, '\n');
    
    if(NULL != pcTmp)
        *pcTmp = '\0';

    memset(szCmd, 0, sizeof(szCmd));
    
    sprintf(szCmd, "ls %s | grep tty", szPath);
    s32Ret = GetInsContext(szCmd, szBuf, sizeof(szBuf));

    /* 去换行符 */
    pcTmp = strchr(szBuf, '\r');
    if(NULL == pcTmp)
        pcTmp = strchr(szBuf, '\n');
    
    if(NULL != pcTmp)
        *pcTmp = '\0';
    
    if((SV_SUCCESS == s32Ret) && (NULL != strstr(szBuf, "ttyUSB")))
    {
        snprintf(port, u32Size, "/dev/%s", szBuf);
        return SV_SUCCESS;
    }
    
    return SV_FAILURE;
}


/******************************************************************************
 * 函数功能: 打开AT指令设备文件
 * 输入参数: ps32Fd -- 返回文件描述符存储区
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 openCellATInterface(sint32 *ps32Fd)
{
    sint32 s32Fd = 0;
    sint32 s32Ret = -1;
    sint32 s32Num;
    sint8 as8Cmd[CMD_LEN]={0};
    sint8 as8Buf[512]={0};
    sint8 as8CellInfoFile[512]={0};
    sint8 as8ModuleType[32] = {0};
    sint8 as8Port[128] = {0};
    sint8 *ps8Pos;

    /* 通过打开/var/info/cellular 查询CELL信息文件是否存在 */
    if (access(CELL_INFO_FILE, F_OK) != SV_SUCCESS)
    {
        printf("%s not exist\n", CELL_INFO_FILE);
        return FACTORY_CELL_LACK;
    }

    /* 将/var/info/cellular里面的内容读出来 */
    snprintf(as8Cmd, 256, "cat %s", CELL_INFO_FILE);
    SAFE_System_Recv(as8Cmd, as8CellInfoFile, 256);
    setCleanLineBreak(as8CellInfoFile);
    strcpy(as8Buf, as8CellInfoFile);
    
    /* 解析读取的内容，获取模块类型ModuleType */
    ps8Pos = strstr(as8Buf,"ModuleType");
    if (ps8Pos != NULL)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf+strlen("ModuleType") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        s32Num = atoi(as8Buf);
        transNum2ModuleType(s32Num, as8ModuleType);
    }
    else
    {
        print_level(SV_INFO, "read stModuleType failed!\n");
        return FACTORY_DEFAULT_FAIL;
    }

    switch (s32Num)
    {
        case CELLULAR_GEMATO:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_SERRIA:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_HUAWEI:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_HUAWEI_MU709S_6:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_HUAWEI_ME909S_120:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_TELIT:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_TELIT910C1:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_FIBOCOM:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
        case CELLULAR_EC25:
            //strcpy(port, EC25_AT_PORT);
            s32Ret = searchCellDev(as8Port, sizeof(as8Port), EC25_AT_PORT_PATH);
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "search AT Port error\n");
                return SV_FAILURE;
            }
            break;
        case CELLULAR_EC200T:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
		case CELLULAR_EC200A:
            if (SV_SUCCESS != access("/dev/ttyUSB1", F_OK))
            {
                strcpy(as8Port, "/dev/ttyUSB2");
            }
            else
            {
                strcpy(as8Port, "/dev/ttyUSB1");
            }
            break;
		case CELLULAR_EC200U:
            strcpy(as8Port, "/dev/ttyUSB0");
            break;
        case CELLULAR_SIM7600:
            //strcpy(port, SIM7600_AT_PORT);
            s32Ret = searchCellDev(as8Port, sizeof(as8Port), SIM7600_AT_PORT_PATH);
            if(SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "search AT Port error\n");
                return SV_FAILURE;
            }
            break;
        default:
            strcpy(as8Port, "/dev/ttyUSB2");
            break;
    }
    
    s32Fd = open(as8Port, O_CLOEXEC|O_RDWR|O_NONBLOCK);
    
    if(s32Fd < 0)
    {
        print_level(SV_ERROR, "open %s error\n", as8Port);
        return SV_FAILURE;
    }
    *ps32Fd = s32Fd;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 往设备内写文件
 * 输入参数: s32Fd -- 端口设备文件 pcCmd -- 命令字符串指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellWrite(sint32 s32Fd, char* pcCmd)
{
    sint32 s32Ret = 0;
    if(s32Fd <= 0 || pcCmd == NULL)
    {
        return SV_FAILURE;
    }

    tcflush(s32Fd, TCIOFLUSH);

    s32Ret = write(s32Fd, pcCmd, strlen(pcCmd));
    if(s32Ret != strlen(pcCmd))
    {
        print_level(SV_ERROR, "write AT CMD %s error\n", pcCmd);
        return SV_FAILURE;
    }
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读设备返回的数据
 * 输入参数: s32Fd -- 端口设备文件 pBuf -- 缓存地址 u32Timeout -- 超时等待时间(s)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellRead(sint32 s32Fd, char* pszBuf, uint32 u32Timeout)
{
    sint32 s32Ret = -1;
    sint32 s32MaxFd = s32Fd + 1;
    sint32 s32Count = 0;
    char szTemp[256] = {0};
    struct timeval stTimeval;
    fd_set stReadFds;
    char* pc = NULL;

    if(s32Fd <= 0 || NULL == pszBuf)
    {
        return SV_FAILURE;
    }
    
    stTimeval.tv_usec = 0;
    stTimeval.tv_sec = u32Timeout;
    FD_ZERO(&stReadFds);
    FD_SET(s32Fd, &stReadFds);
Wait:
    s32Ret = select(s32MaxFd, &stReadFds, NULL, NULL, &stTimeval);
    if (0 == s32Ret)
    {
        print_level(SV_DEBUG, "read timeout\n");
        return SV_FAILURE;
    }
    else if (-1 == s32Ret)
    {
        if(errno == EAGAIN || errno == EWOULDBLOCK || errno == EINTR)
        {
            goto Wait;
        }
        else
        {
            print_level(SV_ERROR,"read fail error[%s]\n", strerror(errno));
            return SV_FAILURE;
        }
    }
    sleep_ms(100);

    //读取多行数据
    while(1)
    {
        s32Ret = read(s32Fd, szTemp, 256 - s32Count);
        if(s32Ret > 0)
        {
            memcpy(pszBuf+s32Count, szTemp, s32Ret);
            s32Count += s32Ret;
        }
        else
        {
            break;
        }
    }
    //去换行符
    cellCutChar(pszBuf,s32Count);
    //print_level(SV_INFO, "AT read: %s \n", pszBuf);
    
    //去掉OK字符
    pc = strstr(pszBuf, "OK");
    if(pc)
    {
        *pc = '\0';
        return SV_SUCCESS;
    }

    return SV_FAILURE;
}


/******************************************************************************
 * 函数功能: 发送命令并获取返回数据
 * 输入参数: s32Fd -- 文件描述符 pcCmd -- 命令字符串 
              pBuf -- 返回数据存储区 u32Timeout -- 超时时间
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 cellSendAndRecv(sint32 s32Fd, char* pcCmd, char* pBuf, uint32 u32Timeout)
{
    sint32 s32Ret = -1;

    if(s32Fd <= 0 || NULL == pcCmd || NULL == pBuf)
    {
        return SV_FAILURE;
    }
    
    s32Ret = cellWrite(s32Fd, pcCmd);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }
	sleep_ms(500);
    s32Ret = cellRead(s32Fd, pBuf, u32Timeout);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }
    sleep_ms(50);

    return SV_SUCCESS;
}


/* 检查4G模块是否存在 */
SV_BOOL checkCellExist()
{
    sint8 as8Buf[512]={0};
    uint32 au32RetryTimes = 3;
    uint32 i = 0;
    SV_BOOL bIsCellExist = SV_FALSE;

    for (i = 0; i < au32RetryTimes; i++)
    {
        SAFE_System_Recv("lsusb", as8Buf, 1000);
        
        if(NULL != strstr(as8Buf, VID_GEMATO) || NULL != strstr(as8Buf, VID_SERRIA) || NULL != strstr(as8Buf, VID_QUECTEL)  
            || NULL != strstr(as8Buf, VID_HUAWEI) || NULL != strstr(as8Buf, VID_TELIT) || NULL != strstr(as8Buf, VID_TELIT910_C1) 
            || NULL != strstr(as8Buf, VID_YIYUAN) || NULL != strstr(as8Buf, VID_FIBOCOM) || NULL != strstr(as8Buf, VPID_SIM7600))
        {
            bIsCellExist = SV_TRUE;
            break;
        }
    }

    return bIsCellExist;
}

/* 4g模块接收功率测试 */
SV_BOOL cellRxTest(CellRxTestParam_S CellRxTestParam)
{
    sint32 s32Ret = -1;
    sint32 i = 0;
    sint32 s32ModuleStat = -1;
    sint32 s32MainPower;       /* 主集接收功率 */
    sint32 s32DiversityPower;  /* 分集接收功率 */
    uint32 u32WaitTime = 30;
    sint8 as8ResBuf[512] = {0};
    sint8 as8ATCmd[AT_BUFFER_SIZE] = {0};
    sint8 as8RecvBuf[AT_BUFFER_SIZE] = {0};
    sint8 *ps8TmpBuf;
    sint8 *ps8Pos;
    uint8 au8Cmd[512] = {0};
    uint8 au8CurCellName[64];
    SV_BOOL bCellExist = SV_FALSE;
    SV_BOOL bMainSignalFlag = SV_FALSE;
    SV_BOOL bDivSignalFlag  = SV_FALSE;
    SV_BOOL bCellRxResult   = SV_FALSE;
    SV_BOOL bRetry = SV_TRUE;
    MSG_PACKET_S stMsgPkt = {0};
    
    /* 查询CELL模块是否存在,不存在则等待，超时就退出 */
    do{
        bCellExist = checkCellExist();
        if (SV_TRUE == bCellExist)
        {
            break;
        }
        else
        {
            u32WaitTime--;
            print_level(SV_INFO, "wait time(30): %ds......\n", u32WaitTime);
            sleep_ms(1000);
        }
    } while(u32WaitTime > 0);
    if (u32WaitTime == 0)
        return SV_FALSE;

    /* 设置CELL进入工厂测试模式，避免干扰 */
    stMsgPkt.stMsg.s32Param = 1;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_SET_CELL_FACTORY, &stMsgPkt, NULL, 0);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "Cellular_FactoryEnter failed.\n");
        return SV_FALSE;
    }

    /* 查询是否在正常运行状态 */
    u32WaitTime = 15;
    snprintf(au8Cmd, 512, "cat %s", CELL_INFO_PATH);
    do{
        if (SV_SUCCESS != access(CELL_INFO_PATH, F_OK))
        {
            u32WaitTime--;
            print_level(SV_INFO, "wait time(15s): %ds......\n", u32WaitTime);
            sleep_ms(1000);
            continue;
        }
        SAFE_System_Recv(au8Cmd, as8ResBuf, 1024);
        setCleanLineBreak(as8ResBuf);
        ps8Pos = strstr(as8ResBuf, "ModuleStat");
        sprintf(as8ResBuf, "%s", ps8Pos);
        strcpy(as8ResBuf, as8ResBuf + strlen("ModuleStat") + 2);
        sscanf(as8ResBuf, "%[^,]", as8ResBuf);
        s32ModuleStat = atoi(as8ResBuf);
        
        if (s32ModuleStat == CELL_STAT_FACTORY)
        {
            break;
        }
        else
        {
            u32WaitTime--;
            print_level(SV_INFO, "wait time(15s): %ds......\n", u32WaitTime);
            sleep_ms(1000);
        }
    } while(u32WaitTime > 0);
    if (0 == u32WaitTime)
    {
        return SV_FALSE;
    }
    
CheckCellType:  
    /* 查询CELL版本类型 */
    strcpy(as8ATCmd, "ATI\r");
    for (i = 0; i < 3; i++)
    {
        s32Ret = cellSendAndRecv(g_s32CellFd, as8ATCmd, as8RecvBuf, 1);
        if (SV_SUCCESS == s32Ret)
        {
            break;
        }
    }
    if (3 == i)
    {
        print_level(SV_ERROR, "Check cellular type fail.\n");
        return SV_FALSE;
    }

    if (NULL != strstr(as8RecvBuf, "EC25"))
        strcpy(au8CurCellName, "EC25");
    else if (NULL != strstr(as8RecvBuf, "SIM7600"))
        strcpy(au8CurCellName, "SIM7600");
    else if (NULL != strstr(as8RecvBuf, "EG25"))
        strcpy(au8CurCellName, "EG25");
    else
    {
        if (bRetry)
        {
            bRetry = SV_FALSE;
            sleep_ms(5000);
            goto CheckCellType;
        }
        else
        {
            print_level(SV_ERROR, "not support module type.");
            return SV_FALSE;
        }
    }

    /* 设置CELL进入工程模式 */
    if (0 == strcmp(au8CurCellName, "EC25") || 0 == strcmp(au8CurCellName, "EG25"))
        strcpy(as8ATCmd, "AT+QRFTESTMODE=1\r");
    else if (0 == strcmp(au8CurCellName, "SIM7600"))
        strcpy(as8ATCmd, "AT+CFUN=5\r");
    else
        return SV_FALSE;

    for (i = 0; i < 3; i++)
    {
        memset(as8RecvBuf, 0, AT_BUFFER_SIZE);
        s32Ret = cellSendAndRecv(g_s32CellFd, as8ATCmd, as8RecvBuf, 1);
        if (SV_SUCCESS == s32Ret)
        {
            break;
        }
    }
    if (3 == i)
    {
        print_level(SV_ERROR, "Enter factory mode fail.\n");
        return SV_FALSE;
    }
    
    /* 如果模块是SIM7600,则先设置频点 */
    if (strstr(au8CurCellName, "SIM7600") != NULL)
    {
        for (i = 0; i < 3; i++)
        {
            memset(as8RecvBuf, 0, AT_BUFFER_SIZE);
            s32Ret = cellSendAndRecv(g_s32CellFd, CellRxTestParam.au8SetFrequencyAT, as8RecvBuf, 1);
            if (SV_SUCCESS == s32Ret)
            {
                break;
            }
        }
        if (i == 3)
        {
            print_level(SV_ERROR, "set cellular frequency fail.\n");
            return SV_FALSE;
        }
    }

    /* 获取主集接收功率 */ 
    for (i = 0; i < 3; i++)
    {
        memset(as8RecvBuf, 0, AT_BUFFER_SIZE);
        s32Ret = cellSendAndRecv(g_s32CellFd, CellRxTestParam.au8MainATcmd, as8RecvBuf, 1);
        if (SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "get cellular main Rx power success.\nResult=%s\n", as8RecvBuf);              
            ps8TmpBuf = strtok(as8RecvBuf, ",");
            if (ps8TmpBuf != NULL)
            {
                strcpy(as8RecvBuf, ps8TmpBuf);
                strcpy(as8RecvBuf, strtok(NULL, ","));
            }
            else
            {
                print_level(SV_INFO, "main Rx power format error.\n", as8RecvBuf);
                return SV_FALSE;
            }
            print_level(SV_INFO, "RecvBuf = %s\n", as8RecvBuf);
            s32MainPower = atoi(as8RecvBuf);
            print_level(SV_INFO, "s32MainPower = %d\n", s32MainPower);
            break;
        }
    }
    if (3 == i)
    {
        print_level(SV_ERROR, "get cellular main Rx power fail.\n");
        return SV_FALSE;
    }
    print_level(SV_INFO,"minMain = %d, maxMain= %d \n", CellRxTestParam.s32MinMain, CellRxTestParam.s32MaxMain);
    if (s32MainPower >= CellRxTestParam.s32MinMain && s32MainPower <= CellRxTestParam.s32MaxMain)
    {
        bMainSignalFlag = SV_TRUE;
    }

    /* 获取分集接收功率 */
    for (i = 0; i < 3; i++)
    {
        memset(as8RecvBuf, 0, AT_BUFFER_SIZE);
        s32Ret = cellSendAndRecv(g_s32CellFd, CellRxTestParam.au8DiversityATcmd, as8RecvBuf, 1);
        if (SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "get cellular diversity Rx power success.\nResult=%s\n", as8RecvBuf);
            ps8TmpBuf = strtok(as8RecvBuf, ",");
            if (ps8TmpBuf != NULL)
            {
                strcpy(as8RecvBuf, ps8TmpBuf);
                strcpy(as8RecvBuf, strtok(NULL, ","));
            }
            else
            {
                print_level(SV_INFO, "diversity Rx power format error.\n", as8RecvBuf);
                return SV_FALSE;
            }
            print_level(SV_INFO, "RecvBuf = %s\n", as8RecvBuf);
            s32DiversityPower = atoi(as8RecvBuf);
            print_level(SV_INFO, "s32DiversityPower = %d\n", s32DiversityPower);
            
            break;
        }
    }
    if (3 == i)
    {
        print_level(SV_ERROR, "get cellular diversity Rx power fail.\n");
        return SV_FALSE;
    }
    print_level(SV_INFO,"minDiversity = %d, maxDiversity= %d \n",CellRxTestParam.s32MinDiversity, CellRxTestParam.s32MaxDiversity);
    if (s32DiversityPower >= CellRxTestParam.s32MinDiversity && s32DiversityPower <= CellRxTestParam.s32MaxDiversity)
    {
        bDivSignalFlag = SV_TRUE;
    }
    
    /* 判断主集和分集的接收功率是否在给定范围内 */
    if (bMainSignalFlag && bDivSignalFlag)
    {
        bCellRxResult =  SV_TRUE;
        return SV_TRUE;
    }
    else
    {   
        print_level(SV_INFO, "bMainSignalFlag = %d, bDivSignalFlag = %d\n", bMainSignalFlag, bDivSignalFlag);
        return SV_FALSE;
    }   
}

//4g模块强发功率测试
SV_BOOL cellTransmitPowerTest(CellTxTestParam_S CellTxTestParam)
{
    sint32 s32Ret = -1;
    sint32 ModuleStat = -1;
    uint32 u32WaitTime = 30;
    uint32 i = 0;
    uint8 as8Cmd[512] = {0};
    uint8 au8CurCellName[64];
    sint8 as8ResBuf[512] = {0};
    sint8 *ps8Pos;
    SV_BOOL bCellExist = SV_FALSE;
    SV_BOOL bRetry = SV_TRUE;
    char szATCmd[AT_BUFFER_SIZE] = {0};   
    char szRecvBuf[AT_BUFFER_SIZE] = {0};
    MSG_PACKET_S stMsgPkt = {0};
    
    /* 查询CELL模块是否存在,不存在则退出 */
    do{
        bCellExist = checkCellExist();
        if (SV_TRUE == bCellExist)
        {
            print_level(SV_INFO, "cellular exist.\n");
            break;
        }
        else
        {
            u32WaitTime--;
            print_level(SV_INFO, "wait time: %ds......\n", u32WaitTime);
            sleep_ms(1000);
        }
    } while(u32WaitTime > 0);
    if (0 == u32WaitTime)
    {
        return SV_FALSE;
    }
    
    /* 设置CELL进入工厂测试模式，避免干扰 */
    //s32Ret = Cellular_FactoryEnter();
    stMsgPkt.stMsg.s32Param = 1;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_SET_CELL_FACTORY, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Cellular_FactoryEnter failed.\n");
        return SV_FALSE;
    }
    else
    {
        print_level(SV_INFO, "Cellular_FactoryEnter SUCCESS.\n");
    }

    /* 查询是否在工厂测试状态 */
    u32WaitTime = 15;
    snprintf(as8Cmd, 512, "cat %s", CELL_INFO_PATH);
    do{
        if (SV_SUCCESS != access(CELL_INFO_PATH, F_OK))
        {
            u32WaitTime--;
            print_level(SV_INFO, "file not exist.Wait time(15s): %ds......\n", u32WaitTime);
            sleep_ms(1000);
            continue;
        }
        SAFE_System_Recv(as8Cmd, as8ResBuf, 1024);
        setCleanLineBreak(as8ResBuf);
        ps8Pos = strstr(as8ResBuf, "ModuleStat");
        sprintf(as8ResBuf, "%s", ps8Pos);
        strcpy(as8ResBuf, as8ResBuf + strlen("ModuleStat") + 2);
        sscanf(as8ResBuf, "%[^,]", as8ResBuf);
        print_level(SV_INFO, "as8TmpBuf = %s\n", as8ResBuf);
        ModuleStat = atoi(as8ResBuf);
        
        if (CELL_STAT_FACTORY == ModuleStat)
        {
            break;
        }
        else
        {
            u32WaitTime--;
            print_level(SV_INFO, "wait time(15s): %ds......\n", u32WaitTime);
            sleep_ms(1000);
        }
    } while(u32WaitTime > 0);
    if (0 == u32WaitTime)
    {
        print_level(SV_ERROR, "wait time(15s): %ds......\n", u32WaitTime);
        return SV_FALSE;
    }

CheckCellType:  
    /* 查询CELL版本类型 */
    strcpy(szATCmd, "ATI\r");
    for (i = 0; i < 3; i++)
    {
        s32Ret = cellSendAndRecv(g_s32CellFd, szATCmd, szRecvBuf, 1);
        if(SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "Check cellular type success.\n");
            break;
        }
    }
    if (3 == i)
    {   
        print_level(SV_INFO, "Check cellular type fail.\n");
        return SV_FALSE;
    }

    if (NULL != strstr(szRecvBuf, "EC25"))
        strcpy(au8CurCellName, "EC25");
    else if (NULL != strstr(szRecvBuf, "SIM7600"))
        strcpy(au8CurCellName, "SIM7600");
    else if (NULL != strstr(szRecvBuf, "EG25"))
        strcpy(au8CurCellName, "EG25");
    else
    {
        if (bRetry)
        {
            bRetry = SV_FALSE;
            sleep_ms(5000);
            goto CheckCellType;
        }
        else
        {
            print_level(SV_ERROR, "not support module type.");
            return SV_FALSE;
        }
    }

    /* 设置CELL进入工程模式 */
    if (0 == strcmp(au8CurCellName, "EC25") || 0 == strcmp(au8CurCellName, "EG25"))
        strcpy(szATCmd, "AT+QRFTESTMODE=1\r");
    else if (0 == strcmp(au8CurCellName, "SIM7600"))
        strcpy(szATCmd, "AT+CFUN=5\r");
    else
        return SV_FALSE;

    for (i = 0; i < 3; i++)
    {
        memset(szRecvBuf, 0, AT_BUFFER_SIZE);
        s32Ret = cellSendAndRecv(g_s32CellFd, szATCmd, szRecvBuf, 1);
        if(SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "Enter factory mode success.\n");
            break;
        }
    }
    if (3 == i)
    {   
        print_level(SV_INFO, "Enter factory mode fail.\n");
        return SV_FALSE;
    }
    
    
    /* 发送AT指令,检查指令是否执行成功 */
    print_level(SV_INFO, "CellTxTestParam.szATCmd = %s\n",CellTxTestParam.au8ATcmd);
    for (i = 0; i < 3; i++)
    {
        memset(szRecvBuf, 0, AT_BUFFER_SIZE);
        s32Ret = cellSendAndRecv(g_s32CellFd, CellTxTestParam.au8ATcmd, szRecvBuf, 1);
        if(SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "Excute AT command success.\n");
            break;
        }
    }
    if (3 == i)
    {   
        print_level(SV_ERROR, "Excute AT command fail.\n");
        return SV_FALSE;
    }

    /* sleep数秒，检查cellular模块是否丢失 */
    sleep_ms(3000);
    strcpy(szATCmd, "AT\r");
    for (i = 0; i < 3; i++)
    {
        memset(szRecvBuf, 0, AT_BUFFER_SIZE);
        s32Ret = cellSendAndRecv(g_s32CellFd, szATCmd, szRecvBuf, 1);
        if(SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "cell exist.\n");
            break;
        }
    }
    if (3 == i)
    {   
        print_level(SV_INFO, "cell is lack.\n");
        return SV_FALSE;
    }

    /* 设置CELL退出工程模式 */
    if (0 == strcmp(au8CurCellName, "EC25") || 0 == strcmp(au8CurCellName, "EG25"))
        strcpy(szATCmd, "AT+QRFTESTMODE=0\r");
    else if (0 == strcmp(au8CurCellName, "SIM7600"))
        strcpy(szATCmd, "AT+CFUN=1\r");
    else
        return SV_FALSE;

    for (i = 0; i < 3; i++)
    {
        memset(szRecvBuf, 0, AT_BUFFER_SIZE);
        s32Ret = cellSendAndRecv(g_s32CellFd, szATCmd, szRecvBuf, 1);
        if (SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "Exit factory mode success.\n");
            break;
        }
    }
    if (3 == i)
    {   
        print_level(SV_INFO, "Exit factory mode fail.\n");
        return SV_FALSE;
    }

    /* 返回结果 */
    return SV_TRUE;
}

sint32 dms31Handle_ParseHeader(uint8 *pu8Message, uint32 u32MsgSize, HTTP_HEADER_S *pstHeader)
{
    sint32 s32Ret = 0;
    char *pszTmp = NULL;
    char *pszTTmp = NULL;
    if (NULL == pu8Message || NULL == pstHeader)
    {
        return ERR_NULL_PTR;
    }

    if (u32MsgSize == 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

    //pthread_mutex_lock(&m_mutexLockStrtok);
    pstHeader->pszMethod = strtok_r((char *)pu8Message, " ", &pszTTmp);
    if (NULL == pstHeader->pszMethod)
    {
        print_level(SV_ERROR, "http header parse Method error.\n");
        return ERR_INVALID_CHNID;
    }

    pstHeader->pszUrl = strtok_r(NULL, " ", &pszTTmp);
    if (NULL == pstHeader->pszUrl)
    {
        print_level(SV_ERROR, "http header parse URL error.\n");
        //pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

    pstHeader->pszVersion = pstHeader->pszUrl + strlen(pstHeader->pszUrl) + 1;
    if (NULL == pstHeader->pszVersion)
    {
        print_level(SV_ERROR, "http header parse VERSION error.\n");
        //pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }
    
    pstHeader->pu8Body = (uint8 *)strstr(pstHeader->pszVersion, "\r\n\r\n");
    if (NULL == pstHeader->pu8Body)
    {
        print_level(SV_ERROR, "http header parse Body error.\n");
        //pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

    pszTmp = strtok_r(pstHeader->pszVersion, "\r\n", &pszTTmp);
    if (NULL == pszTmp)
    {
        print_level(SV_ERROR, "http header parse VERSION error.\n");
        //pthread_mutex_unlock(&m_mutexLockStrtok);
        return ERR_INVALID_CHNID;
    }

    while (NULL != pszTmp)
    {
        //print_level(SV_DEBUG, "%s\n", pszTmp);
        if (NULL != strcasestr(pszTmp, "Host: "))
        {
            pstHeader->pszHost = pszTmp + strlen("Host: ");
        }
        else if (NULL != strcasestr(pszTmp, "Cookie: "))
        {
            pstHeader->pszCookie = pszTmp + strlen("Cookie: ");
        }
        else if (NULL != strcasestr(pszTmp, "If-None-Match: "))
        {
            pstHeader->pszIfNotMatch = pszTmp + strlen("If-None-Match: ");
        }
        else if (NULL != strcasestr(pszTmp, "Range: bytes="))
        {
            pstHeader->pszRange = pszTmp + strlen("Range: bytes=");
        }
        else if (NULL != strcasestr(pszTmp, "Content-Type: multipart/form-data; boundary="))
        {
            pstHeader->pszBoundary = pszTmp + strlen("Content-Type: multipart/form-data; boundary=");
        }
        else if (NULL != strcasestr(pszTmp, "Content-Length: "))
        {
            pstHeader->pszContentLenght = pszTmp + strlen("Content-Length: ");
        }
        else if (NULL != strcasestr(pszTmp, "Content-Type: "))
        {
            pstHeader->pszContentType = pszTmp + strlen("Content-Type: ");
        }
        if ((pszTmp + strlen(pszTmp)) >= (char *)pstHeader->pu8Body)
        {
            break;
        }
        pszTmp = strtok_r(NULL, "\r\n", &pszTTmp);
        
    }
    pstHeader->pu8Body += 4;
    pstHeader->u32Size = u32MsgSize - (pstHeader->pu8Body - pu8Message);
    //pthread_mutex_unlock(&m_mutexLockStrtok);
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 重启设备
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_Reboot(char *pszInJson, char *pszOutJson)
{
    sint32 s32Ret = 0;
    sint8 as8Cmd[CMD_LEN] = {0};
    
    strcpy(as8Cmd, "sleep 1 | safereboot &");
    SAFE_System(as8Cmd, 1000);
    strcpy(pszOutJson, "{\"res\": \"success\"}");

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 版本程序校验
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_GetConfig(char *pszInJson, char *pszOutJson)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_DEV_INFO stDevInfo = {0};
    cJSON *pstJsonConfig, *pstIdentification;
 
    stRetPkt.pu8Data = (uint8 *)&stDevInfo;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_GET_DEVINFO, NULL, &stRetPkt, sizeof(MSG_DEV_INFO));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_DEVINFO failed. [err=%#x]\n", s32Ret);
        cJSON_Delete(pstJsonConfig);
        return FACTORY_GET_DEVINFO_FAIL;
    }

    pstJsonConfig = cJSON_CreateObject();
    if (NULL == pstJsonConfig)
    {
          print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
          cJSON_Delete(pstJsonConfig);
          return FACTORY_NULL_PTR;
    }
    
    pstIdentification = cJSON_CreateObject();
    if (NULL == pstIdentification)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        cJSON_Delete(pstJsonConfig);
        return FACTORY_NULL_PTR;
    }
    cJSON_AddItemToObject(pstIdentification, "serialNo", cJSON_CreateString(stDevInfo.szSerial));
    cJSON_AddItemToObject(pstIdentification, "firmware", cJSON_CreateString(stDevInfo.szVersion));
    cJSON_AddItemToObject(pstIdentification, "hardware", cJSON_CreateString(stDevInfo.szHardware));
    cJSON_AddItemToObject(pstIdentification, "uuid", cJSON_CreateString(stDevInfo.szUuid));
    cJSON_AddItemToObject(pstIdentification, "bootuuid", cJSON_CreateString(stDevInfo.szBootUuid));
    cJSON_AddItemToObject(pstIdentification, "board", cJSON_CreateNumber(BOARD_GetVersion()));  
    cJSON_AddItemToObject(pstJsonConfig, "DMS31Identification", pstIdentification);
    
    cJSON_PrintPreallocated(pstJsonConfig, pszOutJson, 10*1024, 0);
    cJSON_Delete(pstJsonConfig);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 获取Md5配置文件
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_GetMd5(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[1024] = {0};
    cJSON *pstJson;
    sint8 *ps8Out = NULL;
    sint8 as8FileMd5[64] = {0};
    sint8 as8CmdSec[CMD_LEN]={0};
    sint32 s32Ret = 0;
    
    pstJson = cJSON_CreateObject();
    strcpy(as8CmdSec, "md5sum /etc/config.xml | awk -F ' ' '{print$1}'");
    s32Ret = SAFE_System_Recv(as8CmdSec, as8FileMd5, sizeof(as8FileMd5));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "md5sum config.xml failed!\n");
        cJSON_Delete(pstJson);
        free(ps8Out);
        return FACTORY_GET_MD5_FAIL;
    }
    setCleanLineBreak(as8FileMd5);
    print_level(SV_INFO, "config.xml as8FileMd5 = %s  \n", as8FileMd5);
    cJSON_AddItemToObject(pstJson, "config.xml", cJSON_CreateString(as8FileMd5));

    strcpy(as8CmdSec, "md5sum /etc/config_bak1.xml | awk -F ' ' '{print$1}'");
    memset(as8FileMd5, 0, sizeof(as8FileMd5));
    s32Ret = SAFE_System_Recv(as8CmdSec, as8FileMd5, sizeof(as8FileMd5));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "md5sum config_bak1.xml failed!\n");
        cJSON_Delete(pstJson);
        free(ps8Out);
        return FACTORY_GET_MD5_FAIL;
    }
    setCleanLineBreak(as8FileMd5);
    print_level(SV_INFO, "config_bak1.xml as8FileMd5 = %s  \n", as8FileMd5);
    cJSON_AddItemToObject(pstJson, "config_bak1.xml", cJSON_CreateString(as8FileMd5));

    strcpy(as8CmdSec, "md5sum /etc/config_bak2.xml | awk -F ' ' '{print$1}'");
    memset(as8FileMd5, 0, sizeof(as8FileMd5));
    s32Ret = SAFE_System_Recv(as8CmdSec, as8FileMd5, sizeof(as8FileMd5));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "md5sum config_bak2.xml failed!\n");
        cJSON_Delete(pstJson);
        free(ps8Out);
        return FACTORY_GET_MD5_FAIL;
    }
    setCleanLineBreak(as8FileMd5);
    print_level(SV_INFO, "config_bak2.xml as8FileMd5 = %s  \n", as8FileMd5);
    cJSON_AddItemToObject(pstJson, "config_bak2.xml", cJSON_CreateString(as8FileMd5));

    strcpy(as8CmdSec, "md5sum /etc/config_default.xml | awk -F ' ' '{print$1}'");
    memset(as8FileMd5, 0, sizeof(as8FileMd5));
    s32Ret = SAFE_System_Recv(as8CmdSec, as8FileMd5, sizeof(as8FileMd5));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "md5sum config_default.xml failed!\n");
        cJSON_Delete(pstJson);
        free(ps8Out);
        return FACTORY_GET_MD5_FAIL;
    }
    setCleanLineBreak(as8FileMd5);
    print_level(SV_INFO, "config_default.xml as8FileMd5 = %s  \n", as8FileMd5);
    cJSON_AddItemToObject(pstJson, "config_default.xml", cJSON_CreateString(as8FileMd5));

    strcpy(as8CmdSec, "md5sum /var/config.xml | awk -F ' ' '{print$1}'");
    memset(as8FileMd5, 0, sizeof(as8FileMd5));
    s32Ret = SAFE_System_Recv(as8CmdSec, as8FileMd5, sizeof(as8FileMd5));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "md5sum config_bak2.xml failed!\n");
        cJSON_Delete(pstJson);
        free(ps8Out);
        return FACTORY_GET_MD5_FAIL;
    }
    setCleanLineBreak(as8FileMd5);
    print_level(SV_INFO, "config_bak2.xml as8FileMd5 = %s  \n", as8FileMd5);
    cJSON_AddItemToObject(pstJson, "/var/config.xml", cJSON_CreateString(as8FileMd5));
    
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 测试SD卡状态
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_GetStorageStatus(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[256] = {0};
    cJSON *pstJson;
    sint8 *ps8Out = NULL;
    sint32 s32Inserted = 0;
    sint32 s32StorageExist = 0;
    sint32 s32StorageWritable = 0;
    sint32 s32Recording = 0;
    uint32 u32RemainSize = 0;
    uint32 u32TotalSize = 0;
    
    pstJson = cJSON_CreateObject();
    
    /* 先判断是否插入设备（SD卡）*/
    s32Inserted = isSDCardInsert();

    /* 判断SD卡是否挂载,通过读取/var/info/storage */
    s32StorageExist = isStorageMounted();

    /* 判断是否可写 */
    s32StorageWritable = isStorageWritable();

    /* 获取总容量，刷新容量 */
    u32TotalSize = getStorageTotalSize();

    /* 获取剩余容量，刷新容量 */
    u32RemainSize = getStorageRemainSize();
    
    /* 获取当前录像状态 */
    s32Recording = isRecording();
        
    cJSON_AddItemToObject(pstJson, "inserted", cJSON_CreateNumber(s32Inserted));
    cJSON_AddItemToObject(pstJson, "storageExist", cJSON_CreateNumber(s32StorageExist));
    cJSON_AddItemToObject(pstJson, "storageWritable", cJSON_CreateNumber(s32StorageWritable));
    cJSON_AddItemToObject(pstJson, "totalSize", cJSON_CreateNumber(u32TotalSize));
    cJSON_AddItemToObject(pstJson, "remainSize", cJSON_CreateNumber(u32RemainSize));
    cJSON_AddItemToObject(pstJson, "isRecording", cJSON_CreateNumber(s32Recording));
    
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 重新拉起WLAN0
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_ResetWlan0(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[1024] = {0};
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 *ps8Out = NULL;
    cJSON *pstJson;
    
    pstJson = cJSON_CreateObject();

    /* 重新启动wlan0，避免测试板引入的电源干扰 */
    snprintf(as8Cmd, CMD_LEN, "ifconfig wlan0 down");
    if (SAFE_System(as8Cmd, 2000) != SV_SUCCESS)
    {
        printf("ifconfig wlan0 down failed \n");
        cJSON_Delete(pstJson);
        return FACTORY_GET_WIFI_SSID_FAIL;
    }

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);
    
    return FACTORY_SUCCESS_RES;
}


/******************************************************************************
 * 函数功能: 测试WIFI状态
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestWifi(char *pszInJson, char *pszOutJson)
{
    sint8 as8WifiTypeStr[64] = {0};
    sint8 as8OutBuf[256] = {0};
        
    cJSON *pstJson;
    sint8 *ps8Out = NULL;
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8TmpBuf[CMD_LEN] = {0};
    sint8 as8Buf[CMD_LEN] = {0};
    sint32 s32Num = 0;
    sint32 s32Ret = 0;

    MSG_PACKET_S stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    char *pszTmp = NULL;

    pstJson = cJSON_CreateObject();

    /* 重新启动wlan0，避免测试板引入的电源干扰 */
    snprintf(as8Cmd, CMD_LEN, "ifconfig wlan0 down");
    SAFE_System(as8Cmd, 2000);
    sleep_ms(15000);

    /* 先用iwconfig获取ssid，若失败，直接返回 */
    snprintf(as8Cmd, CMD_LEN, "iwconfig wlan0 | grep ESSID | awk -F '\"' '{print$2}' 2>1");  
    if (SAFE_System_Recv(as8Cmd, as8TmpBuf, CMD_LEN) == SV_SUCCESS)
    {
        setCleanLineBreak(as8TmpBuf);
    }   
    else
    {
        printf("iwconfig wlan0 failed \n");
        cJSON_Delete(pstJson);
        return FACTORY_GET_WIFI_SSID_FAIL;
    }

    memset(as8Cmd, 0, CMD_LEN);

    /* 获取当前通道   */
    if (0 == access(RTL8821Proc, F_OK))
    {
        snprintf(as8Cmd, CMD_LEN, "cat %s/wlan0/ap_info | grep cur_channel", RTL8821Proc);
    }
    else if(0 == access(RTL8811Proc, F_OK))
    {
        snprintf(as8Cmd, CMD_LEN, "cat %s/wlan0/ap_info | grep cur_channel", RTL8811Proc);
    }
    else if(0 == access(RTL8188FuProc, F_OK))
    {
        snprintf(as8Cmd, CMD_LEN, "cat %s/wlan0/ap_info | grep cur_channel", RTL8188FuProc);
    }
    else if(0 == access(RTL8188GtvuProc, F_OK))
    {
        snprintf(as8Cmd, CMD_LEN, "cat %s/wlan0/ap_info | grep cur_channel", RTL8188GtvuProc);
    }
    else
    {   
        printf("wifi's Ko is not Inserted \n");
        cJSON_Delete(pstJson);
        return FACTORY_GET_WIFI_CHANNEL_FAIL;
    }
    print_level(SV_INFO, "wifi: as8Cmd = %s\n", as8Cmd);
    SAFE_System_Recv(as8Cmd, as8TmpBuf, CMD_LEN);
    setCleanLineBreak(as8TmpBuf);
    sscanf(as8TmpBuf, "%[^,]", as8Buf);
    sscanf(as8Buf, "%*[^=]%s", as8Buf);   
    strcpy(as8Buf, as8Buf + 1);
    print_level(SV_INFO, "CHANNEL = %s \n", as8Buf);
    s32Num = atoi(as8Buf);
    if (s32Num <= 0)
    {
        printf("wifi get channel failed \n");
        cJSON_Delete(pstJson);
        return FACTORY_GET_WIFI_CHANNEL_FAIL;
    }

    memset(as8Cmd, 0, CMD_LEN);

    /* 获取wifi模块类型 */
    memset(as8TmpBuf, 0, CMD_LEN);
    snprintf(as8Cmd, CMD_LEN, "lsusb");
    SAFE_System_Recv(as8Cmd, as8TmpBuf, CMD_LEN);
    if (NULL != strstr(as8TmpBuf, WG_217_PID))
        strcpy(as8WifiTypeStr, "WG217");
    else if (NULL != strstr(as8TmpBuf, FU_8188_PID))
        strcpy(as8WifiTypeStr, "8188FU");
    else if (NULL != strstr(as8TmpBuf, CVTE_8188_PID))
        strcpy(as8WifiTypeStr, "8188CVTE");
    else if (NULL != strstr(as8TmpBuf, REALTEK_8811_PID))
        strcpy(as8WifiTypeStr, "8811");
    else if (NULL != strstr(as8TmpBuf, WG_209_PID))
        strcpy(as8WifiTypeStr, "WG209");
    else
        strcpy(as8WifiTypeStr, "unknown");

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed. [err=%#x]\n", s32Ret);
        cJSON_Delete(pstJson);
        return FACTORY_GET_NETWORK_CFG_FAIL;
    }
    
    cJSON_AddItemToObject(pstJson, "type", cJSON_CreateString(as8WifiTypeStr));
    cJSON_AddItemToObject(pstJson, "authMode", cJSON_CreateString(transWifiAuth2String(stNetworkCfg.enWifiAuth)));
    cJSON_AddItemToObject(pstJson, "ssid", cJSON_CreateString(stNetworkCfg.szWifiApSsid));
    cJSON_AddItemToObject(pstJson, "apIpAddress", cJSON_CreateString(stNetworkCfg.szWifiApIpAddr));
    cJSON_AddItemToObject(pstJson, "current channel", cJSON_CreateString(as8Buf));
    cJSON_AddItemToObject(pstJson, "password", cJSON_CreateString(stNetworkCfg.szWifiApPwd));
    switch (stNetworkCfg.enWifiFreq)
    {
        case WIFI_FREQ_2_4G:
            pszTmp = "2.4G";
            break;
        case WIFI_FREQ_5G:
            pszTmp = "5G";
            break;
        default:
            pszTmp = "AUTO";
    }
    cJSON_AddItemToObject(pstJson, "frequency", cJSON_CreateString(pszTmp));

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  as8OutBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */

    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 处理复位测试命令
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_ResetFactory(char *pszInJson, char *pszOutJson)
{
    sint32 s32Ret = 0;
    cJSON *pstJson = NULL;
    cJSON *pstType = NULL;
    MSG_PACKET_S stMsgPkt = {0};

    pstJson = cJSON_Parse(pszInJson);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return FACTORY_NULL_PTR;
    }

    if (!checkJsonRestoreParam(pstJson))
    {
        print_level(SV_ERROR, "pstJson pstParam is out of range.\n");
        cJSON_Delete(pstJson);
        return FACTORY_RANGE_OUT;
    }

    pstType = cJSON_GetObjectItemCaseSensitive(pstJson, "type");
    if (0 == strcmp(pstType->valuestring, "soft"))
    {
        stMsgPkt.stMsg.s32Param = 0;
    }
    else
    {
        stMsgPkt.stMsg.s32Param = 1;
    }
    
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_RESET_FACTORY, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_execRequestBlock failed. [s32Ret=%#x]\n", s32Ret);
        cJSON_Delete(pstJson);
        return FACTORY_RESET_FACTORY_FALI;
    }

    cJSON_Delete(pstJson);
    strcpy(pszOutJson, "{\"res\": \"success\"}");

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 设置设备录像状态
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_SetRec(char *pszInJson, char *pszOutJson)
{
    sint32 s32Ret = 0;
    sint32 s32Status = 0;
    sint32 s32OnOff = 1;
    sint32 s32RecTime = 1;
    sint8  as8OutBuf[256] = {0};
    sint8 *p8Out = NULL;
    cJSON *pstJson;
    MSG_SYS_CFG stSysParam;
    MSG_PACKET_S stMsgPkt = {0};
    MSG_PACKET_S stRetPkt = {0};

    /* 先获取系统参数，存放在stSysParam中 */
    stRetPkt.pu8Data = (uint8 *)&stSysParam;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_GET_SYS_CFG, NULL, &stRetPkt, sizeof(MSG_SYS_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "get system config failed. [err=%#x]\n", s32Ret);
        cJSON_Delete(pstJson);
        return FACTORY_GET_SYS_CFG_FAIL;
    }
    else
    {
        print_level(SV_INFO, "system config IsStorage: %d \n", stSysParam.bEnableStorage);
    }

    pstJson = cJSON_CreateObject();
    
    if (param2num(pszInJson, &s32OnOff, "onOff") < 0 || param2num(pszInJson, &s32RecTime, "recTime") < 0)
    {
        strcpy(pszOutJson, "{ params parse error! }");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    if (0 != s32OnOff)
    {
        stSysParam.enNormalRecord = E_REC_NOR_CON;
        stSysParam.enAlarmRecord = E_REC_OFF;
        //stSysParam.u32RecFileLen = 5;               //直接设置为5min录像，当前只支持5、10、15min  
        //strcpy(stSysParam.szRecFileType, "MP4");
        //stSysParam.enAlarmRecord = E_REC_OFF;
    }
    else
    {
        stSysParam.enNormalRecord = E_REC_NOR_OFF;
        stSysParam.enAlarmRecord = E_REC_OFF;
    }

    /* 用修改完的stSysParam去配置系统参数 */
    stMsgPkt.pu8Data = (uint8 *)&stSysParam;
    stMsgPkt.u32Size = sizeof(MSG_SYS_CFG);
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_SET_SYS_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "set system config failed. [err=%#x]\n", s32Ret);
        cJSON_Delete(pstJson);
        return FACTORY_SET_SYS_CFG_FAIL;
    }
    else
    {
        print_level(SV_INFO, "set system config success. \n");
    }

    /* 设置完成之后获取存储状态 */
    if (0 != s32OnOff)
    {   
        sleep(1);
        /* 存储分区是否可写 */
        if (!isStorageWritable())
        {
            s32Status = 0;
            cJSON_Delete(pstJson);
            return FACTORY_SD_NOT_WRITABLE;
        }
        else 
        {
            /* 录像状态 */
            if (isRecording())
            {
                s32Status = 1;
                cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
                cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("start recording success"));
            }
            else
            {
                s32Status = -1;
                cJSON_Delete(pstJson);
                return FACTORY_START_RECORDER_FAIL;
            }
        }
    }
    else
    {
        sleep(1);
        if (SV_FALSE == isRecording())
        {
            s32Status = 1;
            cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
            cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("stop recording success"));
        }
        else
        {
            cJSON_Delete(pstJson);
            return FACTORY_STOP_RECORDER_FAIL;
        }
    }

    p8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, p8Out);    
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(p8Out);
    
    return FACTORY_SUCCESS_RES; 
}


/******************************************************************************
 * 函数功能: 设置设备存储状态
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_SetStorage(char *pszInJson, char *pszOutJson)
{
    sint32 s32OnOff = 1;
    sint32 s32Status = 0;
    sint32 s32Ret = 0;
    sint8 as8OutBuf[256] = {0};
    sint8 *p8Out = NULL;
    cJSON *pstJson;
    MSG_SYS_CFG stSysParam;
    MSG_PACKET_S stMsgPkt = {0};
    MSG_PACKET_S stRetPkt = {0};

    /* 先获取系统参数，存放在stSysParam中 */
    stRetPkt.pu8Data = (uint8 *)&stSysParam;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_GET_SYS_CFG, NULL, &stRetPkt, sizeof(MSG_SYS_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "get system config failed. [err=%#x]\n", s32Ret);
        return FACTORY_GET_SYS_CFG_FAIL;
    }
    else
    {
        print_level(SV_INFO, "system config IsStorage: %d \n", stSysParam.bEnableStorage);
    }

    pstJson = cJSON_CreateObject();
    
    if (param2num(pszInJson, &s32OnOff, "onOff") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        strcpy(pszOutJson, "{ params parse error! }");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    if (0 != s32OnOff)
    {
        stSysParam.bEnableStorage = SV_TRUE;
        
    }
    else
    {
        stSysParam.bEnableStorage = SV_FALSE;
    }

    /* 用修改完的stSysParam去配置系统参数 */
    stMsgPkt.pu8Data = (uint8 *)&stSysParam;
    stMsgPkt.u32Size = sizeof(MSG_SYS_CFG);
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_SET_SYS_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "set Storage config failed. [err=%#x]\n", s32Ret);
        cJSON_Delete(pstJson);
        return FACTORY_SET_SYS_CFG_FAIL;
    }
    else
    {
        print_level(SV_INFO, "set Storage config success. \n");
    }

    /* 设置完成之后获取存储状态 */
    if (0 != s32OnOff)
    {   
        sleep(2);
        /* 存储是否使能 */
        if (!IsStorageEnable())
        {
            s32Status = 0;
            cJSON_Delete(pstJson);
            return FACTORY_ENABLE_STORAGE_FAIL;
        }
        else 
        {
            s32Status = 1;
            cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
            cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("storage enable success"));
        }
    }
    else
    {
        sleep(2);
        if (SV_FALSE == IsStorageEnable())
        {
            s32Status = 1;
            cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
            cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("storage disable success"));
        }
        else
        {
            cJSON_Delete(pstJson);
            return FACTORY_DISABLE_STORAGE_FAIL;
        }
    }

    p8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, p8Out);    
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(p8Out);
    
    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 删除文件
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_RemoveFile(char *pszInJson, char *pszOutJson)
{
    sint8 as8ParaFIleNum[128] = {0};
    sint8 as8OutBuf[256] = {0};
    sint8 *p8Out = NULL;
    cJSON *pstJson;
    
    pstJson = cJSON_CreateObject();
    
    if (param2string(pszInJson, as8ParaFIleNum, "file") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    if (SV_SUCCESS == access(as8ParaFIleNum, F_OK))
    {
        print_level(SV_INFO, "file exist, remove it now !\n");
        if(remove(as8ParaFIleNum) != SV_SUCCESS)
        {
            cJSON_Delete(pstJson);
            return FACTORY_REMOVE_FILE_FAIL;
        }
        else
            cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("success"));
    }
    else
    {
        print_level(SV_ERROR, "file not exist\n");
        cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("file not exist"));
    }
    
    p8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, p8Out);
    strcpy(pszOutJson, as8OutBuf);
    cJSON_Delete(pstJson);
    free(p8Out);
    
    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 删除目录
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_RemoveDir(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[256] = {0};
    sint8 as8ParaFIleNum[128] = {0};
    sint8 as8Cmd[256] = {0};
    sint8 *p8Out = NULL;
    sint32 s32Status = 0;
    sint32 s32Ret = 0;
    char szParaSetRecIn[64] = {0};    /* 设置录像的输入 */
    char szParaSetRecOut[64] = {0};   /* 设置录像的输出 */
    cJSON *pstJson;

    pstJson = cJSON_CreateObject();
    
    if (param2string(pszInJson, as8ParaFIleNum, "dir") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        strcpy(pszOutJson, "{ params parse error! }");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    if (SV_SUCCESS == access(as8ParaFIleNum, F_OK))
    {
        snprintf(as8Cmd, sizeof(as8Cmd), "rm %s -rf", as8ParaFIleNum);
        print_level(SV_INFO, "cmd:%s\n", as8Cmd);

        /* 如果删除的是录像目录的话，且此时在录像的情况下，需要先停录像 */
        if (NULL != strstr(as8Cmd, "/normal") &&
           (SREC_STAT_RECORDING == isRecording()))
        {
            print_level(SV_WARN, "stop rec\n");
            strcpy(szParaSetRecIn, "{\"onOff\": 0,\"recTime\": 1}");
            s32Ret = dms31Handle_SetRec(szParaSetRecIn, szParaSetRecOut);
            if (SV_SUCCESS != s32Ret)
            {
                cJSON_Delete(pstJson);
                return FACTORY_SET_SYS_CFG_FAIL;
            }
            sleep(5);
        }
        s32Ret = SAFE_System(as8Cmd, 20);
        
        if (SV_SUCCESS == s32Ret)
        {
            print_level(SV_INFO, "%s success\n", as8Cmd);
            s32Status = 1;
            cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
            cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("rm dir success"));
        }
        else if (-1 == s32Ret)
        {
            print_level(SV_WARN, "%s error or timeout\n", as8Cmd);
            cJSON_Delete(pstJson);
            return FACTORY_TIMEOUT_FAIL;
        }
        else
        {
            print_level(SV_ERROR, "%s error\n", as8Cmd);
            cJSON_Delete(pstJson);
            return FACTORY_REMOVE_DIR_FAIL;
        }
    }
    else
    {
        print_level(SV_ERROR, "DIR not exist\n");
        cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
        cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("dir not exist"));
    }

    p8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, p8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(p8Out);
    
    return FACTORY_SUCCESS_RES;
}


/******************************************************************************
 * 函数功能: 给设备设置时间
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_SetRtcTime(char *pszInJson, char *pszOutJson)
{
    sint8 as8ParaBuf[128] = {0};
    
    if (param2string(pszInJson, as8ParaBuf, "time") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        return FACTORY_JSON_LOSTPARAM;
    }

    if (SV_SUCCESS != setRtcTime(as8ParaBuf))
    {
        print_level(SV_ERROR, "setRtcTime error!\n");
        return FACTORY_SET_RTC_TIME_FAIL;
    }

    strcpy(pszOutJson, "{\"res\": \"success\" }");
    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 获取设备时间
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_GetRtcTime(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[256] = {0};
    sint8 as8TmpBuf[64] = {0};
    sint8 *ps8Out = NULL;
    sint32 s32Status = 1;
    cJSON *pstJson;

    getRtcTime(as8TmpBuf);
    pstJson = cJSON_CreateObject();
    
    cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
    cJSON_AddItemToObject(pstJson, "time", cJSON_CreateString(as8TmpBuf));
    cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("Get rtc success"));

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}


/******************************************************************************
 * 函数功能: 改变文件配置
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_SetFile(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[256] = {0};
    sint8 as8Filebuf[128] = {0};
    sint8 as8TmpBuf[128] = {0};
    sint8 *ps8Out = NULL;
    sint32 s32Status = 0;
    sint32 s32Ret = SV_FAILURE;
    cJSON *pstJson;
    MSG_VIDEO_CFG stVideoCfg;
    MSG_PACKET_S stMsgPkt = {0};
    MSG_PACKET_S stRetPkt = {0};

    pstJson = cJSON_CreateObject();

    if (param2string(pszInJson, as8Filebuf, "file") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    if (SV_SUCCESS != access(as8Filebuf, F_OK))
    {
        print_level(SV_ERROR, "file not exist\n");
        cJSON_Delete(pstJson);
        return FACTORY_FILE_LACK;
    }

    /* 先获取媒体参数，存放在stChnParam中 */
    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "get video config failed. [err=%#x]\n", s32Ret);
        cJSON_Delete(pstJson);
        return FACTORY_GET_VEDIO_CFG_FAIL;
    }
    else
    {
        print_level(SV_INFO, "main IfrmInterval: %d \n", stVideoCfg.astChnParam[0].u32MainIfrmInterval);
    }

    stVideoCfg.astChnParam[0].u32MainFramerate = 20;
    stVideoCfg.astChnParam[0].u32MainIfrmInterval = 44;
    
    /* 用修改完的stChnParam去配置视频参数 */
    stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
    stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "set video config failed.\n");
        cJSON_Delete(pstJson);
        return FACTORY_SET_VEDIO_CFG_FAIL;
    }
    else
    {
        print_level(SV_INFO, "set video config success. \n");
    }
    
    s32Status = 1;

    cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
    cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("set file success"));

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 获取摄像头状态
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestCamera(char *pszInJson, char *pszOutJson)
{
    sint32 s32Status = 0;
    sint32 s32CameraStatus = 0;
    sint32 s32OutputStatus = 0;
    sint8 as8SensorStr[32] = {0};
    sint8 as8OutBuf[256] = {0};
    sint8 *ps8Out = NULL;
    cJSON *pstJson;

    /* 如果存在，则sensor类型为GC2053 */
    if (SV_SUCCESS == access("/etc/scene_auto/sensor_gc2053", F_OK))
    {
        strcpy(as8SensorStr,"GC2053");
    }
    
    if (SV_SUCCESS == getCameraStatus())
    {
        s32CameraStatus = 1;
    }
    if (SV_SUCCESS == getVoStatus())
    {
        s32OutputStatus = 1;
    }

    if (1 == s32CameraStatus && 1 == s32OutputStatus)
    {
        s32Status = 1;
    }

    pstJson = cJSON_CreateObject();
    cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));

    cJSON_AddNumberToObject(pstJson, "viStatus", s32CameraStatus);
    if (1 == s32CameraStatus)
        cJSON_AddItemToObject(pstJson, "sensor", cJSON_CreateString(as8SensorStr));
    else
    {
        cJSON_Delete(pstJson);
        return FACTORY_CAMERA_VI_ERROR;
    }
    
    cJSON_AddItemToObject(pstJson, "voStatus", cJSON_CreateNumber(s32OutputStatus));
    if (1 == s32OutputStatus)
        cJSON_AddItemToObject(pstJson, "vo", cJSON_CreateString("ok"));
    else
    {
        cJSON_Delete(pstJson);
        return FACTORY_CAMERA_VO_ERROR;
    }
    
    if (1 == s32Status)
        cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("vi vo test ok"));
    else
        cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("vi vo test failed"));

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);
    
    return FACTORY_SUCCESS_RES; 
}

/******************************************************************************
 * 函数功能: 测试Sensor
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestSensor(char *pszInJson, char *pszOutJson)
{
    SEN_TYPE_E SensorType;
    sint8 as8SenChipType[64] = {0};
    sint8 as8OutBuf[256] = {0};
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8Buf[CMD_LEN] = {0};
    sint8 *ps8Out = NULL;
    cJSON *pstJson;
    
    pstJson = cJSON_CreateObject();

    snprintf(as8Cmd, CMD_LEN, "i2cget -y -f 1 0x37 0xF1");   
    SAFE_System_Recv(as8Cmd, as8Buf, CMD_LEN);
    setCleanLineBreak(as8Buf);
    
    print_level(SV_INFO, "TEST_SENSOR ret: %s\n", as8Buf);

    if (0 == strcmp(as8Buf, "0x53"))
    {
        strcpy(as8SenChipType, "GC2053");
    }
    else
    {
        strcpy(as8SenChipType, "Unknown");
    }

    cJSON_AddItemToObject(pstJson, "sensor", cJSON_CreateString(as8SenChipType));
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    strcpy(pszOutJson, as8OutBuf);
    cJSON_Delete(pstJson);
    free(ps8Out);
    print_level(SV_INFO, "sensor type result = %s \n", as8OutBuf);
    if (!strcmp(as8SenChipType, "GC2053"))
    {
        print_level(SV_INFO, "check sensor type success !\n");
        return FACTORY_SUCCESS_RES;
    }
    else
        return FACTORY_TEST_SENSOR_FAIL;
}


/******************************************************************************
 * 函数功能: 测试DA芯片
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestDAchip(char *pszInJson, char *pszOutJson)
{
    sint32 s32Num;
    sint8 as8OutBuf[256]={0};
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8Buf[CMD_LEN] = {0};
    sint8 *ps8Out = NULL;
    cJSON *pstJson;

    pstJson = cJSON_CreateObject();
    snprintf(as8Cmd, CMD_LEN, "i2cget -y -f 3 0x30 0x40");   
    SAFE_System_Recv(as8Cmd, as8Buf, CMD_LEN);
    setCleanLineBreak(as8Buf);
    
    print_level(SV_INFO, "TEST_DAchip ret  : %s\n", as8Buf);
    s32Num = atoi(as8Buf);
    print_level(SV_INFO, "TEST_DAchip ret s32Num : %d\n", s32Num);
    
    cJSON_AddItemToObject(pstJson, "isDAchip", cJSON_CreateString(as8Buf));
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);

    strcpy(pszOutJson, as8OutBuf);     /* 输出 */
    cJSON_Delete(pstJson);
    free(ps8Out);
    print_level(SV_INFO, "DAchip result = %s.\n", as8OutBuf);
    if (strcmp(as8Buf, "0x01") == 0)
    {
        print_level(SV_INFO, "check DAchip type success !   \n");
        return FACTORY_SUCCESS_RES;
    }
    else
        return FACTORY_TEST_DA_FAIL;
}

/******************************************************************************
 * 函数功能: 测试can
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestCan(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[256] = {0};
    sint8 *ps8Out = NULL;
    sint32 s32Status = 0;
    sint32 s32Writelen = 0;
    sint32 s32Ret = 0;
    cJSON *pstJson;
    struct can_frame frame;

    pstJson = cJSON_CreateObject();

    g_s32CanFlag = 0;
    frame.can_id = 0x88;
    frame.can_dlc = 1;
    frame.data[0] = 0x66;
    s32Writelen = write(g_s32CanFd, &frame, sizeof(struct can_frame));   
    print_level(SV_INFO, "write nbytes %d\n", s32Writelen);
    sleep(1);

    if (SV_SUCCESS != getCanStatus())
    {
        cJSON_Delete(pstJson);
        return FACTORY_CAN_DEVICE_UNNORMAL;
    }
    else if (1 != g_s32CanFlag)
    {
        cJSON_Delete(pstJson);
        return FACTORY_CAN_TRANSFER_FAIL;
    }
    else
    {
        s32Status = 1;
        cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
        cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("test CAN success"));
    }

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 测试GPS
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestGPS(char *pszInJson, char *pszOutJson)
{
    sint32 s32Status = 0;
    sint32 s32SatMinSnr = -1;
    sint32 s32Snr = 0;
    sint32 s32CurSnr = 0;
    sint32 s32Ret = 0;
    sint32 s32Num = 0;
    sint8 as8OutBuf[512] = {0};
    sint8 as8BufValue[128] = {0};
    sint8 as8Cmd[512] = {0};
    sint8 as8Buf[512] = {0};
    sint8 as8GpsInfoFile[512] = {0};
    sint8 as8TmpBuf[512]={0};
    sint8 *ps8Pos;
    sint8 *ps8Out = NULL;
    double dBuf;
    cJSON *pstJson;

    if (param2string(pszInJson, as8BufValue, "stdSatMinSnr") < 0)
    {
        print_level(SV_ERROR, "HDL_CMD_TEST_GPS params parse error!\n");
        return FACTORY_JSON_LOSTPARAM;
    }
    
    s32SatMinSnr = atoi(as8BufValue);
    if (0 == s32SatMinSnr)
    {
        print_level(SV_ERROR, "HDL_CMD_TEST_GPS atoi error!\n");
        return FACTORY_JSON_LOSTPARAM;
    }

    pstJson = cJSON_CreateObject();
            
    /* 通过打开/var/info/gps 查询gps信息文件是否存在 */
    if (SV_SUCCESS != access(GPS_INFO_FILE, F_OK))
    {
        printf("%s not exist\n", GPS_INFO_FILE);
        cJSON_Delete(pstJson);
        return FACTORY_FILE_LACK;
    }
    else
    {
        print_level(SV_INFO, "gps exit\n");
    }

    /* 将/var/info/gps里面的内容读出来 */
    snprintf(as8Cmd, 256, "cat %s", GPS_INFO_FILE); 
    SAFE_System_Recv(as8Cmd, as8GpsInfoFile, 512);
    setCleanLineBreak(as8GpsInfoFile);
    print_level(SV_INFO, "gps InfoFile:%s\n", as8GpsInfoFile);
    strcpy(as8Buf, as8GpsInfoFile);
    
    /* 解析读取的内容，获取gps状态Status */
    ps8Pos = strstr(as8Buf,"Status");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("Status") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "Status = %s\n", as8Buf);
        s32Num = atoi(as8Buf);
        cJSON_AddItemToObject(pstJson, "gps Status", cJSON_CreateNumber(s32Num));
    }
    else
        cJSON_AddItemToObject(pstJson, "gps Status", cJSON_CreateString("read gps Status failed!"));

    /* 解析读取的内容，获取Satinuse */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"Satinuse");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("Satinuse") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "Satinuse = %s\n", as8Buf);
        s32Num = atoi(as8Buf);
        cJSON_AddItemToObject(pstJson, "Satinuse", cJSON_CreateNumber(s32Num));
    }
    else
        cJSON_AddItemToObject(pstJson, "Satinuse", cJSON_CreateString("read Satinuse failed!"));

    /* 解析读取的内容，获取sat_count */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"sat_count");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("sat_count") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "sat_count = %s\n", as8Buf);
        s32Num = atoi(as8Buf);
        cJSON_AddItemToObject(pstJson, "sat_count", cJSON_CreateNumber(s32Num));
    }
    else
        cJSON_AddItemToObject(pstJson, "sat_count", cJSON_CreateString("read sat_count failed!"));
    
    /* 解析读取的内容，获取latitude */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"latitude");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("latitude") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "latitude = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "latitude", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "latitude", cJSON_CreateString("read latitude failed!"));

    /* 解析读取的内容，获取longitude */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"longitude");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("longitude") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "longitude = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "longitude", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "longitude", cJSON_CreateString("read longitude failed!"));

    /* 解析读取的内容，获取direction */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"direction");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("direction") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "direction = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "direction", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "direction", cJSON_CreateString("read direction failed!"));

    /* 解析读取的内容，获取PDOP */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"PDOP");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("PDOP") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "PDOP = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "PDOP", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "PDOP", cJSON_CreateString("read PDOP failed!"));

    /* 解析读取的内容，获取HDOP */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"HDOP");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("HDOP") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "HDOP = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "HDOP", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "HDOP", cJSON_CreateString("read HDOP failed!"));

    /* 解析读取的内容，获取VDOP */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"VDOP");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("VDOP") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "VDOP = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "VDOP", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "VDOP", cJSON_CreateString("read VDOP failed!"));

    /* 解析读取的内容，获取天线高度elv */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"elv");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("elv") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "elv = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "elv", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "elv", cJSON_CreateString("read elv failed!"));

    
    /* 解析读取的内容，获取速度spk */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"spk");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("spk") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "spk = %s\n", as8Buf);
        dBuf = atof(as8Buf);
        cJSON_AddItemToObject(pstJson, "spk", cJSON_CreateNumber(dBuf));
    }
    else
        cJSON_AddItemToObject(pstJson, "spk", cJSON_CreateString("read spk failed!"));

    /* 解析读取的内容，获取snrList的内容 */
    strcpy(as8Buf, as8GpsInfoFile);
    ps8Pos = strstr(as8Buf,"snrList");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("snrList") + 2);
        print_level(SV_INFO, "snrList = %s\n", as8Buf);
        cJSON_AddItemToObject(pstJson, "snrList", cJSON_CreateString(as8Buf));
        for(int i = 0; i < 20; i++)
        {   
            ps8Pos = strstr(as8Buf,"snr");
            if (NULL != ps8Pos)
            {
                sprintf(as8Buf, "%s", ps8Pos);
                strcpy(as8Buf, as8Buf + strlen("snr") + 2);
                sscanf(as8Buf, "%[^}]", as8TmpBuf);               
                s32CurSnr = atoi(as8TmpBuf);              
                if(s32CurSnr >= s32SatMinSnr)
                    s32Snr++;
            }
            else
                break;
        }
    }
    else
    {
        cJSON_AddItemToObject(pstJson, "snrList", cJSON_CreateString("read snrList failed!"));
    }
    
    cJSON_AddItemToObject(pstJson, "SatMinSnrNum", cJSON_CreateNumber(s32Snr));
    
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 测试上传录像
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestUploadVideo(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[256] = {0};
    sint8 as8Cmd[1024] = {0};
    sint8 as8NewName[256] = {0};
    sint8 as8UploadName[256] = {0};
    sint8 as8VideoMd5[128] = {0};
    sint8 *ps8Out = NULL;
    sint32 s32Status = 0;
    sint32 s32Ret;
    sint32 s32Timeout = 9000;
    UPLOAD_VIDEO_INFO_S stUpVideoInfo = {0};
    cJSON *pstJson;
    
    pstJson = cJSON_CreateObject();  

    if ( param2string(pszInJson, stUpVideoInfo.szUserName, "user") < 0 || param2string(pszInJson, stUpVideoInfo.szPasswd, "passwd") < 0  || 
        param2string(pszInJson, stUpVideoInfo.szFtpDir, "ftpDstPath") < 0 || param2string(pszInJson, stUpVideoInfo.szServerIp, "serverIp") < 0 ||
        param2num(pszInJson, &stUpVideoInfo.s32Port, "port") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    print_level(SV_INFO, "user:%s  dir:%s  passwd:%s  ip:%s port:%d\n", stUpVideoInfo.szUserName, stUpVideoInfo.szFtpDir,
                                      stUpVideoInfo.szPasswd,  stUpVideoInfo.szServerIp, stUpVideoInfo.s32Port);

    if (SV_SUCCESS == getUploadVideoName(as8NewName, as8UploadName))
    {
        print_level(SV_INFO, "%s success\n", as8UploadName);
        memset(as8Cmd, 0, sizeof(as8Cmd));
        snprintf(as8Cmd, sizeof(as8Cmd), "curl ftp://%s:%d/%s/%s  -u %s:%s -T %s", 
                                                    stUpVideoInfo.szServerIp, stUpVideoInfo.s32Port, 
                                                    stUpVideoInfo.szFtpDir, as8NewName, stUpVideoInfo.szUserName,
                                                    stUpVideoInfo.szPasswd, as8UploadName);
        print_level(SV_INFO, "cmd:%s\n", as8Cmd);
        s32Ret = SAFE_System(as8Cmd, s32Timeout);
        if (SV_SUCCESS == s32Ret)
        {
            s32Status = 1;
            print_level(SV_INFO, "cmd running normal\n");
        }
        else if(-2 == s32Ret)
        {
            s32Status = -2;
            SAFE_System("killall -9 curl", 3);
            print_level(SV_ERROR, "cmd running timeout\n");
        }
        else
        {
            s32Status = -1;
            print_level(SV_ERROR, "cmd running error\n");
        }
    }
    else
    {
        s32Status = -3;
        print_level(SV_ERROR, "getUploadVideoName  error\n");
    }
    
    cJSON_AddItemToObject(pstJson, "uploadStatus", cJSON_CreateNumber(s32Status));

    if (-3 == s32Status)
    {
        print_level(SV_INFO, "no video exist\n");
    }
    else
    {
        cJSON_AddItemToObject(pstJson, "name", cJSON_CreateString(as8NewName));
        memset(as8Cmd, 0, sizeof(as8Cmd));
        snprintf(as8Cmd, 256, "md5sum %s", as8UploadName);
        print_level(SV_INFO, "cmd:%s\n", as8Cmd);
        SAFE_System_Recv(as8Cmd, as8VideoMd5, 33);
        setCleanLineBreak(as8VideoMd5);
        print_level(SV_INFO, "videoMd5:%s\n", as8VideoMd5);
        cJSON_AddItemToObject(pstJson, "md5", cJSON_CreateString(as8VideoMd5));
    }
    
    if (s32Status == 1)
    {
        cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("uploadvideo success"));
    } 
    else if (-3 == s32Status)
    {
        return FACTORY_FILE_LACK;
    }
    else if (-1 == s32Status)
    {
        return FACTORY_UPLOADVEDIO_FAIL;
    }
    else if (-2 == s32Status)
    {
        return FACTORY_TIMEOUT_FAIL;
    }

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 使4G模块重新上电
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_CellReset(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[256] = {0};
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8Buf[CMD_LEN] = {0};
    sint8 *ps8Out = NULL;
    sint32 s32Ret = -1;
    cJSON *pstJson;

    pstJson = cJSON_CreateObject();
    snprintf(as8Cmd, CMD_LEN, "/root/gpio.sh  2 22 0");
    
    s32Ret = SAFE_System(as8Cmd, 100);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cell down failed!\n");
        
        return FACTORY_DEFAULT_FAIL;
    }

    sleep_ms(2000);

    snprintf(as8Cmd, CMD_LEN, "/root/gpio.sh  2 22 1");
    
    s32Ret = SAFE_System(as8Cmd, 100);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "cell up failed!\n");
        return FACTORY_DEFAULT_FAIL;
    }
    
    cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("success"));
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    strcpy(pszOutJson, as8OutBuf);     /* 输出 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 通过串口发送指令给测试板
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_TestBoard(char *pszInJson, char *pszOutJson)
{
    sint32 s32SerialFd = -1;
    sint32 s32Ret = 0;
    sint32 s32ReadLen = -1;
    sint32 s32WriteLen = -1;
    sint32 s32Num = 0;
    sint32 s32Status = 0;
    sint32 s32Baudrate = 115200;   //9600 19200 38400 115200
    sint8 *ps8Port = "/dev/ttyS0";
    sint8  as8OutBuf[1024] = {0};
    sint8 *ps8Out = NULL;
    sint8 as8SendTBcmd[512] = {0};
    sint8 as8RecvData[512] = {0};
    sint8 as8Temp[512] = {0};
    uint8 au8Buf[512] = {0};
    sint8 *ps8Pos;
    uint32 u32Timeout = 3;
    uint32 u32ReadPos = 0;
    uint32 u32DataLen = 0;
    pthread_t thread;
    struct timeval l_timeout;
    TYPE_TestBoard_E eTestBoardType = UNKNOWN;
    fd_set UartFd_Set;
    cJSON *pstJson;
    
    pstJson = cJSON_CreateObject();

    /* 解析参数 */
    if (param2string(pszInJson, as8SendTBcmd, "cmd") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    /* 将参数进行转存，末尾加入回车换行 */
    int tmp = strlen(as8SendTBcmd);
    print_level(SV_INFO, "tmp len = %d\n", tmp);
    print_level(SV_INFO, "send cmd  = %s\n", as8SendTBcmd);
    as8SendTBcmd[strlen(as8SendTBcmd)] = 0x0d;
    as8SendTBcmd[strlen(as8SendTBcmd) + 1] = 0x0a;

    /* 初始化串口1 */
    if (!serial_init(ps8Port, s32Baudrate))
    {
        print_level(SV_ERROR, "%s initialize failed\n", ps8Port);
        cJSON_Delete(pstJson);
        return FACTORY_INIT_SERIAL_FAIL;
    }
    
    s32SerialFd = g_s32SerialFd_update;

    /* 如果是控制测试板掉电，先回包，再发指令到串口 */
    if (NULL != strstr(as8SendTBcmd,"POWERDOWN"))
    {
        strcpy(g_stPowerdownInfo.au8Cmd, as8SendTBcmd);
        g_stPowerdownInfo.s32SerialFd = g_s32SerialFd_update;
        s32Ret = pthread_create(&thread, NULL, setTBPowerdown, &g_stPowerdownInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "set TB power down failed! \n");
            cJSON_Delete(pstJson);
            return FACTORY_SET_TB_POWERDOWN_FAIL;
        }
        else
        {
            cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
            cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("OK"));
            cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("creat thread to set TB powerdown success !"));
            goto exit;
        }
    }

    /* 发送指令给串口 */
    s32WriteLen = write(s32SerialFd, as8SendTBcmd, strlen(as8SendTBcmd));
    print_level(SV_INFO, "as8SendTBcmd s32WriteLen = %d\n", s32WriteLen);

    if (s32SerialFd < 0)
    {
        return FACTORY_DEFAULT_FAIL;
    }
    
    l_timeout.tv_usec = 0;
    l_timeout.tv_sec = u32Timeout;
    FD_ZERO(&UartFd_Set);
    FD_SET(s32SerialFd,&UartFd_Set);
Wait:
    s32Ret = select(s32SerialFd + 1, &UartFd_Set, NULL, NULL, &l_timeout);
    if (0 == s32Ret)
    {
        print_level(SV_DEBUG, "read u32Timeout\n");
        return FACTORY_DEFAULT_FAIL;
    }
    else if (-1 == s32Ret)
    {
        if (errno == EAGAIN || errno == EWOULDBLOCK || errno == EINTR)
        {
            goto Wait;
        }
        else
        {
            print_level(SV_ERROR,"read fail error[%s]\n", strerror(errno));
            return FACTORY_DEFAULT_FAIL;
        }
    }
    sleep_ms(100);

    /* 读取多行数据 */
    while(1)
    {
        s32ReadLen = read(s32SerialFd, as8Temp, 512 - u32ReadPos);
        if(s32ReadLen > 0)
        {
            memcpy(as8RecvData + u32ReadPos, as8Temp, s32ReadLen);
            u32ReadPos += s32ReadLen;
        }
        else
        {
            break;
        }
    }
    
    u32DataLen = strlen(as8RecvData);

    print_level(SV_INFO, "u32DataLen :%d\n", u32DataLen);
    print_level(SV_INFO, "recv info:%s\n", as8RecvData);

    /* 根据不同的发送命令选择不同类型 */
    if (NULL != strstr(as8SendTBcmd,"AT!SETINPUT"))
    {
        eTestBoardType = SETINPUT;
    }
    else if (NULL != strstr(as8SendTBcmd,"AT!SETVOLTAGE"))
    {
        eTestBoardType = SETVOLTAGE;
    }
    else if (NULL != strstr(as8SendTBcmd,"AT!GETCURRENT"))
    {
        eTestBoardType = GETCURRENT;
    }
    else if (NULL != strstr(as8SendTBcmd,"getadc"))
    {
        eTestBoardType = GETADC;
    }
    else if (NULL != strstr(as8SendTBcmd,"POWERDOWN"))
    {
        eTestBoardType = POWERDOWN;
    }
    else if (NULL != strstr(as8SendTBcmd,"ip"))
    {
        eTestBoardType = IP;
    }
    else
        eTestBoardType = UNKNOWN;

    /* 根据解析的结果返回相应的回包 */
    switch(eTestBoardType)
    {
        case SETINPUT:
            if (NULL != strstr(as8RecvData, "OK"))
            {
                s32Status = 1;
                cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
                cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("OK"));
                cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("set TB status success !"));
            }
            else
            {
                cJSON_Delete(pstJson);
                close(s32SerialFd);
                return FACTORY_SET_TB_STATUS_FAIL;
            }
            break;
        case SETVOLTAGE:
            if (NULL != strstr(as8RecvData, as8SendTBcmd))
            {
                s32Status = 1;
                cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
                cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("OK"));
                cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("set TB voltage success !"));
            }
            else
            {
                cJSON_Delete(pstJson);
                close(s32SerialFd);
                return FACTORY_SET_TB_VOLT_FAIL;
            }
            break;
        case GETCURRENT:
            ps8Pos = strstr(as8RecvData, "CURRENT:");
            if (NULL != ps8Pos)
            {
                s32Status = 1;     
                sprintf(au8Buf, "%s", ps8Pos);
                strcpy(au8Buf, au8Buf + 9);
                sscanf(au8Buf, "%[^\]", au8Buf);
                print_level(SV_INFO, "CURRENT as8Buf = %s\n", au8Buf);
                s32Num = atoi(au8Buf);
                cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
                cJSON_AddItemToObject(pstJson, "current", cJSON_CreateNumber(s32Num));
                cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("get output current success !"));
            }
            else
            {
                cJSON_Delete(pstJson);
                close(s32SerialFd);
                ps8Pos = NULL;
                return FACTORY_GET_TB_CURRENT_FAIL;
            }
            ps8Pos = NULL;
            break;
        case GETADC:
            ps8Pos = strstr(as8RecvData, "Vout");
            if (NULL != ps8Pos)
            {
                s32Status = 1;
                sprintf(au8Buf, "%s", ps8Pos);
                strcpy(au8Buf, au8Buf + 7);
                sscanf(au8Buf, "%[^,]", au8Buf);
                print_level(SV_INFO, "Vout au8Buf = %s\n", au8Buf);
                s32Num = atoi(au8Buf);
                cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
                cJSON_AddItemToObject(pstJson, "voltage", cJSON_CreateNumber(s32Num));
                cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("get output voltage success !"));
            }
            else
            {
                cJSON_Delete(pstJson);
                close(s32SerialFd);
                ps8Pos = NULL;
                return FACTORY_GET_TB_VOLT_FAIL;
            }
            ps8Pos = NULL;
            break;
        case POWERDOWN:
            if (NULL != strstr(as8RecvData, "OK"))
            {
                s32Status = 1;
                cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
                cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("OK"));
                cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("set TB powerdown success !"));
            }
            else
            {
                cJSON_Delete(pstJson);
                close(s32SerialFd);
                return FACTORY_SET_TB_POWERDOWN_FAIL;
            }
            ps8Pos = NULL;
            break;
        case IP:
            ps8Pos = strstr(as8RecvData, "ip:");
            if (NULL != ps8Pos)
            {
                s32Status = 1;
                sprintf(au8Buf, "%s", ps8Pos);
                strcpy(au8Buf, au8Buf + 4);
                cellCutChar(au8Buf,strlen(au8Buf));
                sscanf(au8Buf, "%[^n]", au8Buf);
                print_level(SV_INFO, "ip au8Buf = %s\n", au8Buf);
                cJSON_AddItemToObject(pstJson, "status", cJSON_CreateNumber(s32Status));
                cJSON_AddItemToObject(pstJson, "ip", cJSON_CreateString(au8Buf));
                cJSON_AddItemToObject(pstJson, "Description", cJSON_CreateString("get ip success !"));
            }
            else
            {
                cJSON_Delete(pstJson);
                close(s32SerialFd);
                ps8Pos = NULL;
                return FACTORY_GET_TB_IP_FAIL;
            }
            ps8Pos = NULL;
            break;
        default:
            cJSON_Delete(pstJson);
            close(s32SerialFd);
            return FACTORY_RANGE_OUT;
            break;
    }

exit:
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    memset(ps8Out,'\0',strlen(ps8Out));
    free(ps8Out);
    if (strstr(as8SendTBcmd,"POWERDOWN") == NULL)
    {
        print_level(SV_INFO, "test board close socket\n");
        close(s32SerialFd);
    }
    
    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 通过信息文件检查SIM卡模块是否插入
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_checkSIM(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[1024] = {0};
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8Buf[512] = {0};
    sint8 as8CellInfoFile[512] = {0};
    sint8 *ps8Out = NULL;
    sint8 *ps8Pos;
    cJSON *pstJson;
    
    pstJson = cJSON_CreateObject();

    /* 通过打开/var/info/cellular 查询CELL信息文件是否存在 */
    if (access(CELL_INFO_FILE, F_OK) != SV_SUCCESS)
    {
        printf("%s not exist\n", CELL_INFO_FILE);
        return FACTORY_CELL_LACK;
    }
    else
    {
        print_level(SV_INFO, "cellular exit\n");
    }

    /* 将/var/info/cellular里面的内容读出来 */
    snprintf(as8Cmd, 256, "cat %s", CELL_INFO_FILE);
    SAFE_System_Recv(as8Cmd, as8CellInfoFile, 256);
    setCleanLineBreak(as8CellInfoFile);
    print_level(SV_INFO, "cellInfoFile:%s\n", as8CellInfoFile);
    strcpy(as8Buf, as8CellInfoFile);

    /* 解析读取的内容，获取SIM卡是否插入 */
    ps8Pos = strstr(as8Buf,"hadSIM");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf+strlen("hadSIM")+2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "hadSIM = %s\n", as8Buf);
        cJSON_AddItemToObject(pstJson, "hadSIM", cJSON_CreateString(as8Buf));
    }
    else
        cJSON_AddItemToObject(pstJson, "hadSIM", cJSON_CreateString("read hadSIM failed!"));

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);
    
    return FACTORY_SUCCESS_RES;
}


/******************************************************************************
 * 函数功能: 获取4G模块状态信息
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_GetCellStatus(char *pszInJson, char *pszOutJson)
{
    sint32 s32Ret = -1;
    sint32 s32Fd = -1;
    sint8 as8OutBuf[1024] = {0};
    sint8 as8Cmd[CMD_LEN] = {0};
    sint8 as8Buf[512] = {0};
    sint8 as8CellInfoFile[512] = {0};
    sint8 as8ModuleType[32] = {0};
    sint8 *ps8Pos = NULL;
    sint8 *ps8Out = NULL;
    cJSON *pstJson;

    pstJson = cJSON_CreateObject();

    /* 通过打开/var/info/cellular 查询CELL信息文件是否存在 */
    if (SV_SUCCESS != access(CELL_INFO_FILE, F_OK))
    {
        printf("%s not exist\n", CELL_INFO_FILE);
        return FACTORY_CELL_LACK;
    }
    else
    {
        print_level(SV_INFO, "cellular exit\n");
    }
    
    /* 将/var/info/cellular里面的内容读出来 */
    snprintf(as8Cmd, 256, "cat %s", CELL_INFO_FILE);
    SAFE_System_Recv(as8Cmd, as8CellInfoFile, 256);
    setCleanLineBreak(as8CellInfoFile);
    print_level(SV_INFO, "as8CellInfoFile:%s\n", as8CellInfoFile);
    strcpy(as8Buf, as8CellInfoFile);

    /* 解析读取的内容，获取SIM卡是否插入 */
    ps8Pos = strstr(as8Buf,"hadSIM");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("hadSIM") + 2);
        sscanf(as8Buf, "%[^,]", as8Buf);
        print_level(SV_INFO, "hadSIM = %s\n", as8Buf);
        cJSON_AddItemToObject(pstJson, "hadSIM", cJSON_CreateString(as8Buf));
    }
    else
    {
        cJSON_AddItemToObject(pstJson, "hadSIM", cJSON_CreateString("read hadSIM failed!"));
    }

    /* 获取模块类型 */
    s32Ret = openCellATInterface(&s32Fd);
    if(SV_FAILURE == s32Ret)
    {
        return FACTORY_DEFAULT_FAIL;
    }
    memset(as8Buf, 0, sizeof(as8Buf));
    s32Ret = cellSendAndRecv(s32Fd, "ATI\r", as8Buf, 3);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "cellular_FactoryOps:CELL_FACTORY_ATCMD fail.\n");
        close(s32Fd);
        return FACTORY_ATCMD_FAIL;
    }
    
    if (NULL != strstr(as8Buf, "EC25E") && NULL == strstr(as8Buf, "EC25EU"))
        strcpy(as8ModuleType, "EC25E");
    else if (NULL != strstr(as8Buf, "EC25EU") && NULL == strstr(as8Buf, "EC25EUX"))
        strcpy(as8ModuleType, "EC25EU");
    else if (NULL != strstr(as8Buf, "EC25EUX"))
        strcpy(as8ModuleType, "EC25EUX");
    else if (NULL != strstr(as8Buf, "EC25J"))
        strcpy(as8ModuleType, "EC25J");
    else if (NULL != strstr(as8Buf, "EC25A") && NULL == strstr(as8Buf, "EC25AF") && NULL == strstr(as8Buf, "EC25AU"))
        strcpy(as8ModuleType, "EC25A");
    else if (NULL != strstr(as8Buf, "EC25V"))
        strcpy(as8ModuleType, "EC25V");
    else if (NULL != strstr(as8Buf, "EC25AF") && NULL == strstr(as8Buf, "EC25AFX"))
        strcpy(as8ModuleType, "EC25AF");
    else if (NULL != strstr(as8Buf, "EC25AFX"))
        strcpy(as8ModuleType, "EC25AFX");
    else if (NULL != strstr(as8Buf, "EC25AUT") && NULL == strstr(as8Buf, "EC25AUTL"))
        strcpy(as8ModuleType, "EC25AUT");
    else if (NULL != strstr(as8Buf, "EC25AUTL"))
        strcpy(as8ModuleType, "EC25AUTL");
    else if (NULL != strstr(as8Buf, "EC25AU") && NULL == strstr(as8Buf, "EC25AUT") && NULL == strstr(as8Buf, "EC25AUX"))
        strcpy(as8ModuleType, "EC25AU");
    else if (NULL != strstr(as8Buf, "EC25AUX"))
        strcpy(as8ModuleType, "EC25AUX");
    else if (NULL != strstr(as8Buf, "EC20") && NULL == strstr(as8Buf, "EC200"))
        strcpy(as8ModuleType, "EC20");
    else if (NULL != strstr(as8Buf, "EC200AEU"))
        strcpy(as8ModuleType, "EC200AEU");
    else if (NULL != strstr(as8Buf, "EC200AAU"))
        strcpy(as8ModuleType, "EC200AAU");
    else if (NULL != strstr(as8Buf, "EC200ACN"))
        strcpy(as8ModuleType, "EC200ACN");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600E-H1CD"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600E-H1CD");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600E-H") && NULL == strstr(as8Buf, "SIMCOM_SIM7600E-H1CD"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600E-H");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600A-H"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600A-H");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600SA-H"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600SA-H");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600G-H"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600G-H");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600V-H"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600V-H");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600JC-H"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600JC-H");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600NA-H"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600NA-H");
    else if (NULL != strstr(as8Buf, "SIMCOM_SIM7600CE-TVSE"))
        strcpy(as8ModuleType, "SIMCOM_SIM7600CE-TVSE");
    else
        strcpy(as8ModuleType, "Unknow");
    
    cJSON_AddItemToObject(pstJson, "ModuleType", cJSON_CreateString(as8ModuleType));

    /* 获取模块版本 */
    ps8Pos = strstr(as8Buf,"Revision");
    if (NULL != ps8Pos)
    {
        sprintf(as8Buf, "%s", ps8Pos);
        strcpy(as8Buf, as8Buf + strlen("Revision") + 2);
        if (NULL != strstr(as8ModuleType, "EC25") || NULL != strstr(as8ModuleType, "EC20"))
        {
            setCleanLineBreak(as8Buf);
            cJSON_AddItemToObject(pstJson, "Version", cJSON_CreateString(as8Buf));
        }
        else if (NULL != strstr(as8ModuleType, "SIM7600"))
        {
            ps8Pos = strstr(as8Buf,"IMEI:");
            if (NULL != ps8Pos)
            {
                *ps8Pos = '\0';
                cJSON_AddItemToObject(pstJson, "Version", cJSON_CreateString(as8Buf));   
            }
            else
            {
                cJSON_AddItemToObject(pstJson, "Version", cJSON_CreateString("Unknow"));
            }
        }
        else
        {
            cJSON_AddItemToObject(pstJson, "Version", cJSON_CreateString("Unknow"));
        }
        print_level(SV_INFO, "Revision = %s\n", as8Buf);
    }
    else
    {
        cJSON_AddItemToObject(pstJson, "Version", cJSON_CreateString("Unknow"));
    }

    if (NULL != strstr(as8ModuleType, "EC2"))
    {
        memset(as8Buf, 0, sizeof(as8Buf));
        s32Ret = cellSendAndRecv(s32Fd, "AT+GSN\r", as8Buf, 3);
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "cellular_FactoryOps:CELL_FACTORY_ATCMD fail.\n");
            close(s32Fd);
            return FACTORY_ATCMD_FAIL;
        }

        if (NULL != as8Buf)
        {
            cJSON_AddItemToObject(pstJson, "IMEI", cJSON_CreateString(as8Buf));
        }
        else
        {
            cJSON_AddItemToObject(pstJson, "IMEI", cJSON_CreateString("Unknow"));
        }
    }
    else if (NULL != strstr(as8ModuleType, "SIMCOM"))
    {
        memset(as8Buf, 0, sizeof(as8Buf));
        s32Ret = cellSendAndRecv(s32Fd, "AT+CGSN\r", as8Buf, 3);
        if(SV_FAILURE == s32Ret)
        {
            print_level(SV_ERROR, "cellular_FactoryOps:CELL_FACTORY_ATCMD fail.\n");
            close(s32Fd);
            return FACTORY_ATCMD_FAIL;
        }

        if (NULL != as8Buf)
        {
            cJSON_AddItemToObject(pstJson, "IMEI", cJSON_CreateString(as8Buf));
        }
        else
        {
            cJSON_AddItemToObject(pstJson, "IMEI", cJSON_CreateString("Unknow"));
        }
    }
    else
    {        
        cJSON_AddItemToObject(pstJson, "IMEI", cJSON_CreateString("Unknow"));
    }
    
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);
    close(s32Fd);
    
    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 复位4G模块
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_ResetCell(char *pszInJson, char *pszOutJson)
{
    sint32 s32Ret = -1;
    sint8 as8OutBuf[1024] = {0};
    sint8 *ps8Out = NULL;
    cJSON *pstJson;
    MSG_PACKET_S stMsgPkt = {0};
    
    pstJson = cJSON_CreateObject();

    s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_RESET_CELL, &stMsgPkt, NULL, 0);
    if(SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "cellular_FactoryOps:CELL_FACTORY_RESET fail.\n");
        return FACTORY_RESET_CELL_FAIL;
    }
    else
    {
        cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("success"));
    }

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);
    
    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 设置4G模块是否进入测试模式
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_SetCellFactory(char *pszInJson, char *pszOutJson)
{
    sint32 s32Status = 0;
    sint32 s32Ret = -1;
    sint32 s32OnOff = 0;
    sint32 s32ModuleStat = -1;
    uint32 u32WaitTime = 0;
    sint8 as8Cmd[CMD_LEN]={0}; 
    sint8 as8ResBuf[CMD_LEN] = {0};
    sint8 as8OutBuf[1024] = {0};
    sint8 *ps8Out = NULL;
    sint8 *ps8Pos;
    cJSON *pstJson;
    MSG_PACKET_S stMsgPkt = {0};

    pstJson = cJSON_CreateObject();
    if (param2num(pszInJson, &s32OnOff, "onOff") < 0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }
    if (1 == s32OnOff)
    {
        stMsgPkt.stMsg.s32Param = 1;
        s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_SET_CELL_FACTORY, &stMsgPkt, NULL, 0);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "Cellular_FactoryEnter failed.\n");
            cJSON_Delete(pstJson);
            return FACTORY_SET_CELLFACTORY_FAIL;
        }
    }
    else
    {
        stMsgPkt.stMsg.s32Param = 0;
        s32Ret = Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_SET_CELL_FACTORY, &stMsgPkt, NULL, 0);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "cellular_FactoryExit failed.\n");
            cJSON_Delete(pstJson);
            return FACTORY_SET_CELLFACTORY_FAIL;
        }
    }

    /* 查询是否在正常运行状态 */
    u32WaitTime = 20;
    snprintf(as8Cmd, 512, "cat %s", CELL_INFO_PATH);
    do{
        if (SV_SUCCESS != access(CELL_INFO_PATH, F_OK))
        {
            u32WaitTime--;
            print_level(SV_INFO, "wait time(20s): %ds......\n", u32WaitTime);
            sleep_ms(1000);
            continue;
        }
        SAFE_System_Recv(as8Cmd, as8ResBuf, 1024);
        setCleanLineBreak(as8ResBuf);
        ps8Pos = strstr(as8ResBuf, "ModuleStat");
        sprintf(as8ResBuf, "%s", ps8Pos);
        strcpy(as8ResBuf, as8ResBuf + strlen("ModuleStat") + 2);
        sscanf(as8ResBuf, "%[^,]", as8ResBuf);
        print_level(SV_INFO, "as8TmpBuf = %s\n", as8ResBuf);
        s32ModuleStat = atoi(as8ResBuf);

        if (1 == s32OnOff)
        {
            if (CELL_STAT_FACTORY == s32ModuleStat)
            {
                break;
            }
            else
            {
                u32WaitTime--;
                print_level(SV_INFO, "wait time(20s): %ds......\n", u32WaitTime);
                sleep_ms(1000);
            }
        }
        else
        {
            if (CELL_STAT_FACTORY != s32ModuleStat)
            {
                break;
            }
            else
            {
                u32WaitTime--;
                print_level(SV_INFO, "wait time(20s): %ds......\n", u32WaitTime);
                sleep_ms(1000);
            }
        }
    } while(u32WaitTime > 0);
    if (u32WaitTime == 0)
    {
        return FACTORY_TIMEOUT_FAIL;
    }
    
    cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString("success"));

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

/******************************************************************************
 * 函数功能: 给设备发送AT指令，用于CELLULAR模块测试
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_ExecAT(char *pszInJson, char *pszOutJson)
{
    sint32 s32Ret = -1;
    sint32 s32Fd = -1; 
    sint8 as8OutBuf[256] = {0};
    sint8 *ps8Out = NULL;
    char szSendCmd[AT_BUFFER_SIZE] = {0}; 
    char szRecvData[AT_BUFFER_SIZE] = {0};
    cJSON *pstJson;
        
    pstJson = cJSON_CreateObject();
    
    /* 分析参数 */
    if (param2string(pszInJson, szSendCmd, "cmd")<0)
    {
        print_level(SV_ERROR, "params parse error!\n");
        cJSON_Delete(pstJson);
        return FACTORY_JSON_LOSTPARAM;
    }

    /* 打开AT指令端口 */
    s32Ret = openCellATInterface(&s32Fd);
    if (SV_FAILURE == s32Ret)
    {
        return FACTORY_DEFAULT_FAIL;
    }
    
    strcat(szSendCmd, "\r");
    print_level(SV_INFO, "SendCmd:%s\n", szSendCmd);
    s32Ret = cellSendAndRecv(s32Fd, szSendCmd, szRecvData, 3);
    if (SV_FAILURE == s32Ret)
    {
        print_level(SV_ERROR, "cellular_FactoryOps:CELL_FACTORY_ATCMD fail.\n");
        close(s32Fd);
        return FACTORY_ATCMD_FAIL;
    }

    print_level(SV_INFO, "recv info:%s\n", szRecvData);
    cJSON_AddItemToObject(pstJson, "res", cJSON_CreateString(szRecvData));
    
    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);
    close(s32Fd);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 检查cellular是否存在
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
sint32 dms31Handle_CheckCellExist(char *pszInJson, char *pszOutJson)
{
    sint8 as8OutBuf[1024] = {0};
    sint8 *ps8Out = NULL;
    cJSON *pstJson;
    
    pstJson = cJSON_CreateObject();
    
    if(SV_TRUE == checkCellExist())
    {
        cJSON_AddItemToObject(pstJson, "IsCellExist", cJSON_CreateNumber(1));
    }
    else
        cJSON_AddItemToObject(pstJson, "IsCellExist", cJSON_CreateNumber(0));

    ps8Out = cJSON_Print(pstJson);
    strcat(as8OutBuf, ps8Out);
    print_level(SV_INFO, "ready to write back, len %d  outBuf = %s  \n", strlen(as8OutBuf), as8OutBuf);
    strcpy(pszOutJson, as8OutBuf);     /* 直接从outBuf复制到pszOutJson返回 */
    cJSON_Delete(pstJson);
    free(ps8Out);

    return FACTORY_SUCCESS_RES;
}

sint32 DMS31Handle_CellSingleTest(void)
{
    sint32 s32Ret = -1;
    sint32 s32CellRxResult = 0;
    sint32 s32CellTxResult = 0;
    sint32 s32ModuleStat = -1;
    uint32 u32Timeout = 30;
    uint8 au8Cmd[512] = {0};
    uint8 au8FactoryServerIP[32] = {0};
    uint8 au8Uuid[64] = {0};
    sint8 as8Buf[1024] = {0};
    sint8 as8ResBuf[512] = {0};
    sint8 *ps8Pos;
    cJSON *pstJson = NULL;
    cJSON *pstCellTxTestParam = NULL;
    cJSON *pstCellRxTestParam = NULL;
    cJSON *pstTmp = NULL;
    CellRxTestParam_S stCellRxTestParam;
    CellTxTestParam_S stCellTxTestParam;
    MSG_PACKET_S stMsgPkt = {0};

    /* 查找/tmp/AutoTest目录下的配置文件是否存在，暂时命名为CellTest.txt */
    if (SV_SUCCESS != access(CELL_TEST_PATH, F_OK))
    {
        print_level(SV_ERROR, "%s not exist\n", CELL_TEST_PATH);
        return SV_FAILURE;
    }

    /* 将里面内容读出来 */
    snprintf(au8Cmd, 512, "cat %s", CELL_TEST_PATH);
    SAFE_System_Recv(au8Cmd, as8Buf, 1024);
    print_level(SV_INFO, "CellTest.txt info:\n%s\n", as8Buf);

    /* 解析内容 */
    pstJson = cJSON_Parse(as8Buf);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return SV_FAILURE;
    }
    
    pstTmp = cJSON_GetObjectItem(pstJson, "factoryServerIP");
    if (NULL != pstTmp)
    {
        strcpy(au8FactoryServerIP, pstTmp->valuestring);
    }
    else
    {
        print_level(SV_ERROR, "get factoryServerIP failed.\n");
        return SV_FAILURE;
    }
    pstCellRxTestParam = cJSON_GetObjectItemCaseSensitive(pstJson, "cellRxTest");
    if (NULL != pstCellRxTestParam)
    {
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "enable");
        if (NULL != pstTmp)
        {
            stCellRxTestParam.s32Enable = pstTmp->valueint;
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "minMain");
        if (NULL != pstTmp)
        {
            stCellRxTestParam.s32MinMain = pstTmp->valueint;
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "maxMain");
        if (NULL != pstTmp)
        {
            stCellRxTestParam.s32MaxMain = pstTmp->valueint;
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "minDiversity");
        if (NULL != pstTmp)
        {
            stCellRxTestParam.s32MinDiversity = pstTmp->valueint;
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "maxDiversity");
        if (NULL != pstTmp)
        {
            stCellRxTestParam.s32MaxDiversity = pstTmp->valueint;
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "setFrequencyAT");
        if (NULL != pstTmp)
        {
            strcpy(stCellRxTestParam.au8SetFrequencyAT, pstTmp->valuestring);
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "mainATcmd");
        if (NULL != pstTmp)
        {
            strcpy(stCellRxTestParam.au8MainATcmd, pstTmp->valuestring);
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellRxTestParam, "diversityATcmd");
        if (NULL != pstTmp)
        {
            strcpy(stCellRxTestParam.au8DiversityATcmd, pstTmp->valuestring);
        }
    }
    
    pstCellTxTestParam = cJSON_GetObjectItemCaseSensitive(pstJson, "cellTxTest");
    if (NULL != pstCellTxTestParam)
    {
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellTxTestParam, "enable");
        if (NULL != pstTmp)
        {
            stCellTxTestParam.s32Enable = pstTmp->valueint;
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellTxTestParam, "timeout");
        if (NULL != pstTmp)
        {
            stCellTxTestParam.s32Timeout = pstTmp->valueint;
        }
        pstTmp = cJSON_GetObjectItemCaseSensitive(pstCellTxTestParam, "ATcmd");
        if (NULL != pstTmp)
        {
            strcpy(stCellTxTestParam.au8ATcmd, pstTmp->valuestring);
        }
    }

    /* 等待4G模块启动 */
    memset(au8Cmd, 0, 512);
    snprintf(au8Cmd, 512, "cat %s", CELL_INFO_PATH);
    print_level(SV_INFO, "au8Cmd=%s\n", au8Cmd);
    while(1)
    {
        if (SV_SUCCESS != access(CELL_INFO_PATH, F_OK))
        {
            print_level(SV_INFO, "cellular info file not exist\n");
            sleep_ms(1000);
            continue;
        }
        SAFE_System_Recv(au8Cmd, as8ResBuf, 512);
        setCleanLineBreak(as8ResBuf);
        print_level(SV_INFO, "as8TmpBuf = %s\n", as8ResBuf);
        ps8Pos = strstr(as8ResBuf, "ModuleStat");
        if (NULL == ps8Pos)
        {
            memset(as8ResBuf, 0, 512);
            sleep_ms(1000);
            continue;
        }
        sprintf(as8ResBuf, "%s", ps8Pos);
        strcpy(as8ResBuf, as8ResBuf + strlen("ModuleStat") + 2);
        sscanf(as8ResBuf, "%[^,]", as8ResBuf);
        print_level(SV_INFO, "as8TmpBuf = %s\n", as8ResBuf);
        s32ModuleStat = atoi(as8ResBuf);
        
        if ((CELL_STAT_NORMAL == s32ModuleStat) || (CELL_STAT_CONNECT == s32ModuleStat))
        {
            break;
        }
        else
        {
            print_level(SV_INFO, "cellular not ready\n");
            sleep_ms(1000);
        }
    }
    sleep_ms(10000);

    /* 打开AT指令端口 */
    s32Ret = openCellATInterface(&g_s32CellFd);
    if(SV_FAILURE == s32Ret)
    {
        return SV_FAILURE;
    }

    /* 播放4G已启动的提示声 */
    memset(au8Cmd, 0, 512);
    snprintf(au8Cmd, 512, "aplay %s > /dev/null &", CELL_READY_WAV_PATH);
    s32Ret = SAFE_System(au8Cmd, 1000);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "au8Cmd: %s failed.\n", au8Cmd);
        return SV_FAILURE;
    }

    /* 等待连接服务器 */
    snprintf(au8Cmd, 512, "ping %s -c 1 -W 1", au8FactoryServerIP);
    print_level(SV_INFO, "pingCmd=%s\n", au8Cmd);
    memset(as8Buf, 0, strlen(as8Buf));
    while(1)
    {
        SAFE_System_Recv(au8Cmd, as8Buf, 1024);
        print_level(SV_INFO, "as8Buf=%s\n", as8Buf);
        if (NULL != strstr(as8Buf, " 0% packet loss"))
            break;
        else
            print_level(SV_INFO, "wait for connecting %s\n", au8FactoryServerIP);
    }

    /* 如果要测接收功率 */
    do{
        if (stCellRxTestParam.s32Enable)
        {
            /* 执行接收功率测试 */
            s32CellRxResult = cellRxTest(stCellRxTestParam);
            print_level(SV_INFO, "CELL RX test result: %d\n", s32CellRxResult);    
            
            /* 把接收功率的测试结果上传到服务器 */
            /* 1 先获取设备的uuid */
            if (SV_SUCCESS != access(UUID_PATH, F_OK))
            {
                print_level(SV_ERROR, "%s not exist\n", UUID_PATH);
                break;      
            }
            snprintf(au8Cmd, 512, "cat %s", UUID_PATH);
            SAFE_System_Recv(au8Cmd, au8Uuid, 1024);
            setCleanLineBreak(au8Uuid);
            if (0 != strcmp(au8Uuid, ""))
            {
                print_level(SV_INFO, "au8Uuid = %s\n", au8Uuid);
            }
            else
            {
                print_level(SV_ERROR, "cat au8Uuid fail.\n");
                break;
            }
            
            /* 2 上传Rx结果到服务器 */
            snprintf(au8Cmd, 512, "curl -i -X POST -H \"'Content-type':'application/json'\" --connect-timeout %d \
            -d '{\"uuid\":\"%s\",\"cellRxResult\": %d}' http://%s:9101/adas/set", u32Timeout, au8Uuid, s32CellRxResult, au8FactoryServerIP);            
            print_level(SV_INFO, "curl au8Cmd:%s\n", au8Cmd);
            memset(as8Buf, 0, strlen(as8Buf));
            SAFE_System_Recv(au8Cmd, as8Buf, 1024);
            setCleanLineBreak(as8Buf);
            print_level(SV_INFO, "http res = %s\n", as8Buf);

            if (SV_SUCCESS == strstr(as8Buf, "200 OK"))
            {
                print_level(SV_ERROR, "Execute curl fail.");
            }
        }
    }while (0);

    /* 如果要测发射功率 */
    if (stCellTxTestParam.s32Enable)
    {
        s32CellTxResult = cellTransmitPowerTest(stCellTxTestParam);
        print_level(SV_INFO, "CELL TX test result: %d\n", s32CellTxResult);
        snprintf(au8Cmd, 512, "aplay %s > /dev/null &", CELL_FINISH_WAV_PATH);
        SAFE_System(au8Cmd, 1000);
        
        Msg_execRequestBlock(EP_FACTORY, EP_CONTROL, OP_REQ_RESET_CELL, &stMsgPkt, NULL, 0);     
    }

    return SV_SUCCESS;
}

sint32 DMS31Handle_TestCmd(uint8 *pu8Message, uint32 u32MsgSize, uint8 *pu8Result)
{
    sint32 s32Ret = 0;
    sint32 s32Res = 0;  
    char *pszReqPath = NULL;
    char *pszReqParam = NULL;
    HTTP_HEADER_S stHeader = {0};
    char szJsonBody[10*1024];

    if (NULL == pu8Message || NULL == pu8Result)
    {
        return ERR_NULL_PTR;
    }    

    if (u32MsgSize == 0)
    {
        return ERR_ILLEGAL_PARAM;
    }

    pu8Message[u32MsgSize] = '\0';
    s32Ret = dms31Handle_ParseHeader(pu8Message, u32MsgSize, &stHeader);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "dms31Handle_ParseHeader failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    pszReqPath = stHeader.pszUrl;
    pszReqParam = strchr(stHeader.pszUrl, '?');
    if (NULL != pszReqParam)
    {
        *pszReqParam = '\0';
        pszReqParam++;
    }

    print_level(SV_DEBUG, "%s %s\n%s\n", stHeader.pszMethod, pszReqPath, stHeader.pu8Body);
    
    if (NULL != strcasestr(pszReqPath, "reboot"))       
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            print_level(SV_INFO, "reboot!\n");
            s32Res = dms31Handle_Reboot(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support reboot with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "getConfig"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_GetConfig(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support getConfig with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "getMd5Config"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_GetMd5(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support getMd5Config with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testStorageStatus"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_GetStorageStatus(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testStorageStatus with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testWifi"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_TestWifi(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testWifi with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }  
    else if (NULL != strcasestr(pszReqPath, "testResetFactory"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_ResetFactory(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testResetFactory with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    } 
    else if (NULL != strcasestr(pszReqPath, "setRec"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_SetRec(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support setRec with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "setStorage"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_SetStorage(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support setStorage with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    } 
    else if (NULL != strcasestr(pszReqPath, "removeFile"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_RemoveFile(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support removeFile with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    } 
    else if (NULL != strcasestr(pszReqPath, "removeDir"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_RemoveDir(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support removeDir with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "setRtcTime"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_SetRtcTime(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support setRtcTime with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "getRtcTime"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_GetRtcTime(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support getRtcTime with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testSetFile"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_SetFile(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testSetFile with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "cameraStatus"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_TestCamera(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support cameraStatus with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testSensor"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_TestSensor(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testSensor with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testDA"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_TestDAchip(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testDA with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testGPS"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_TestGPS(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testGPS with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testCan"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_TestCan(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testCan with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testBoard"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_TestBoard(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testBoard with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "GetCellStatus"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_GetCellStatus(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support GetCellStatus with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "testUploadVideo"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_TestUploadVideo(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support testUploadVideo with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "ExecAT"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_ExecAT(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support ExecAT with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "ResetCell"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_ResetCell(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support ResetCell with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "SetCellFactory"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_SetCellFactory(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support SetCellFactory with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "CheckCellExist"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_CheckCellExist(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support CheckCellExist with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "CellReset"))
    {
        if (0 == strcmp(stHeader.pszMethod, "POST"))
        {
            s32Res = dms31Handle_CellReset(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support RebootCell with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "checkSIM"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_checkSIM(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support checkSIM with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else if (NULL != strcasestr(pszReqPath, "resetWlan0"))
    {
        if (0 == strcmp(stHeader.pszMethod, "GET"))
        {
            s32Res = dms31Handle_ResetWlan0(stHeader.pu8Body, szJsonBody);
        }
        else
        {
            print_level(SV_ERROR, "not support resetWlan0 with method: %s\n", stHeader.pszMethod);
            s32Res =  FACTORY_NOTSUPPORT;
        }
    }
    else
    {
        print_level(SV_WARN, "not support requset: %s %s\n", stHeader.pszMethod, pszReqPath);
        s32Res =  FACTORY_NOTSUPPORT;
    }

    if (FACTORY_SUCCESS_RES != s32Res)
    {
        FTY_HDL_GenerateErrorReply(s32Res, (char *)pu8Result);
        return strlen((char *)pu8Result);
    }
    
    FTY_HDL_GenerateSuccessReply(szJsonBody, (char *)pu8Result);

    return strlen((char *)pu8Result);
}


