﻿/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_vpss.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2021-04-26

文件功能描述: 封装RK MPP视频处理子系统模块功能

其他:

版本: v1.0.0(最新版本号)

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述


*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <math.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "common.h"
#include "mpp_com.h"
#include "mpp_vpss.h"
#include "media.h"
#include "mpp_vosd.h"
#include "media_sem.h"
#if ALG_MUTLIT_BUFFER
#include "media_shm.h"
#endif

#include "rockit_api.h"


#if((BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || (BOARD == BOARD_ADA47V1) || (BOARD == BOARD_ADA900V1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_DMS51V1))
#define MPP_VPSS_TMP_FORMAT IMAGE_TYPE_RGB565

#else
#define MPP_VPSS_TMP_FORMAT IMAGE_TYPE_NV12
#endif

#define MPP_VPSS_TOTAL_CHN (MPP_VPSS_CHN_BUTT*MEDIA_MAX_CHN)

#if 0
#define printf_RgaAttr(stRgaAttr) \
{ \
    print_level(SV_INFO, "u16Rotaion:%d bEnBufPool:%d u16BufPoolCnt:%d enFlip:%d \n"  \
                         "In==> imgType:%d u32X:%d u32Y:%d u32Width:%d u32Height:%d u32HorStride:%d u32VirStride:%d \n"  \
                         "OUt=> imgType:%d u32X:%d u32Y:%d u32Width:%d u32Height:%d u32HorStride:%d u32VirStride:%d \n",  \
                         stRgaAttr.u16Rotaion, stRgaAttr.bEnBufPool, stRgaAttr.u16BufPoolCnt,stRgaAttr.enFlip,  \
                         stRgaAttr.stImgIn.imgType,stRgaAttr.stImgIn.u32X,stRgaAttr.stImgIn.u32Y,stRgaAttr.stImgIn.u32Width,stRgaAttr.stImgIn.u32Height,stRgaAttr.stImgIn.u32HorStride,stRgaAttr.stImgIn.u32VirStride,  \
                         stRgaAttr.stImgOut.imgType,stRgaAttr.stImgOut.u32X,stRgaAttr.stImgOut.u32Y,stRgaAttr.stImgOut.u32Width,stRgaAttr.stImgOut.u32Height,stRgaAttr.stImgOut.u32HorStride,stRgaAttr.stImgOut.u32VirStride \
                         ); \
}
#endif

#define MPP_VPSS_ALG_BUF_NUM    3

/*************************************************************
 * 通道 0 为 主码流绑定VENC H264 通道
 * 通道 1 为 子码流绑定VENC H264 通道
 * 通道 2 为 jpeg流绑定VENC mjpeg 通道
 * 通道 3 为 VO输出通道
 * 通道 4 为 算法接口
 *************************************************************/

/* 算法通道信息 */
typedef struct tagVpssAlgInfo_S
{
    SV_BOOL bInit[VIM_MAX_DEV_NUM];
    uint32  u32FD[VIM_MAX_DEV_NUM][MPP_VPSS_ALG_BUF_NUM];
    uint64  u32Pts[VIM_MAX_DEV_NUM][MPP_VPSS_ALG_BUF_NUM];
    uint32  u32Width;
    uint32  u32Height;
} MPP_VPSS_ALG_INFO_S;

/* 视频处理模块控制信息 */
typedef struct tagVpssInfo_S
{
    uint32              u32ChnNum;                          /* 视频源通道数目 */
    VIDEO_MODE_EE       enVideoMode[MEDIA_MAX_CHN];         /* 视频制式 */
    CHN_ALG_E           enAlgType[MEDIA_MAX_CHN];           /* 算法类型 */
    SV_SIZE_S           stSizes[MPP_VPSS_CHN_BUTT];         /* 不同通道的尺寸 */ //对于RK版本的venc,不支持设置尺寸,所以应该在VPSS先设置
    uint32              u32Tid[MPP_VPSS_TOTAL_CHN];         /* 线程id */
    uint32              u32MainTid[VIM_MAX_DEV_NUM];        /* 主码流线程id */
    uint32              u32SecTid[VIM_MAX_DEV_NUM];         /* 子码流线程id */
    uint32              u32JpegTid[VIM_MAX_DEV_NUM];        /* jpeg流线程id */
    uint32              u32AlgTid[VIM_MAX_DEV_NUM];         /* 算法流线程id */
    SV_BOOL             bImageMirror;                       /* 是否使能画面镜像 */
    SV_BOOL             bImageFlip;                         /* 是否使能画面翻转 */
    SV_BOOL             bRunning;                           /* 线程运行标志位 */
    SV_BOOL             bInterrupt[MPP_VPSS_CHN_BUTT];      /* 是否暂停获取通道数据 */
    SV_BOOL             bChnEnable[MPP_VPSS_CHN_BUTT];      /* 通道是否使能 */
    NETWORK_STAT_S      stNetworkStat[NETWORK_TYPE_BUTT];   /* 网络状态，决定是否要进行网络流的osd叠加 */
    SV_ROT_ANGLE_E      enRotateAngle;                      /* 画面旋转角度 */
    MB_BLK              mbAlgRotateBlk;                     /* 算法通道旋转图像缓存块 */
    MEDIA_YUV_CALLBACK  pfYuvCallback;                      /* YUV数据回调函数指针 */
    MEDIA_RGB_CALLBACK  pfRGBCallback;                      /* RGB数据回调函数指针 */
    MEDIA_BUF_CALLBACK  pfBufCallback;                      /* BUF数据回调函数指针 */
    MPP_VPSS_ALG_INFO_S stVpssAlgInfo;                      /* 算法相关配置信息 */
} MPP_VPSS_INFO_S;

MPP_VPSS_INFO_S m_stVpssInfo = {0};     /* 视频处理模块控制信息 */

typedef struct tagVpss_DUMP_MEMBUF_S
{
    SV_BOOL bCreated;
    MB_BLK  hBlock;
    MB_POOL hPool;
    uint64  u64PhyAddr;
    uint64  u64VirAddr;
    uint32  u32MemSize;
} MPP_VPSS_DUMP_MEMBUF_S;

typedef struct tagVpss_DEI_S
{
    SV_BOOL   bRunning;                 /* 线程是否正在运行 */
    SV_SIZE_S stPriVencSize;            /* 主码流编码画面大小 (宽高) */
    uint32    u32DEITid;                /* DEI线程ID */
}MPP_VPSS_DEI_S;

static MPP_VPSS_DUMP_MEMBUF_S stVpssMem[3] = {0};
static MPP_VPSS_DEI_S stVpssDEI = {0};
//static MEDIA_BUFFER stRgaMb[MEDIA_MAX_CHN] = {RK_NULL};

void * mpp_vpss_MainBody(void *pvArg);
void * mpp_vpss_AlgBody(void *pvArg);
sint32 mpp_vpss_Alg_Init();
sint32 mpp_vpss_Alg_Fini();

#define VPSS_ALG_IMAGE_WIDTH          608
#define VPSS_ALG_IMAGE_HEIGHT         352

static SV_BOOL bGetFrame = SV_FALSE;

static VIDEO_FRAME_INFO_S m_stVideoFrame = {0};
static void *m_pvAddr = NULL;

sint32 mpp_vpss_SetNetwork(NETWORK_STAT_S *pstNetworkStat)
{
    switch(pstNetworkStat->enNetworkType)
    {
        case NETWORK_TYPE_LAN:
            m_stVpssInfo.stNetworkStat[NETWORK_TYPE_LAN].bExist = pstNetworkStat->bExist;
            print_level(SV_INFO, "lan: %d, wifi: %d\n", m_stVpssInfo.stNetworkStat[NETWORK_TYPE_LAN].bExist,
                        m_stVpssInfo.stNetworkStat[NETWORK_TYPE_WIFI].bExist);
            break;

        case NETWORK_TYPE_WIFI:
            m_stVpssInfo.stNetworkStat[NETWORK_TYPE_WIFI].bExist = pstNetworkStat->bExist;
            print_level(SV_INFO, "lan: %d, wifi: %d\n", m_stVpssInfo.stNetworkStat[NETWORK_TYPE_LAN].bExist,
                        m_stVpssInfo.stNetworkStat[NETWORK_TYPE_WIFI].bExist);
            break;

        default:
            break;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 初始化VPSS模块
 * 输入参数: pstVpssConf --- 视频处理配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS --- 成功
             ERR_NULL_PTR --- 传入参数指针为NULL
             ERR_ILLEGAL_PARAM --- 参数错误
             ERR_NOT_SURPPORT --- 不支持配置
             ERR_SYS_NOTREADY --- 系统未初始化
             SV_FAILURE --- 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Init(MPP_VPSS_CONF_S *pstVpssConf)
{

    sint32 s32Ret = 0, i;
    uint32 u32MaxW, u32MaxH;
    SV_SIZE_S *pu32Sizes = m_stVpssInfo.stSizes;

    if (NULL == pstVpssConf)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstVpssConf->u32ChnNum || pstVpssConf->u32ChnNum > VIM_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    for (i = 0; i < NETWORK_TYPE_BUTT; i++)
    {
        m_stVpssInfo.stNetworkStat[i].enNetworkType = i;
    }

    s32Ret = mpp_vpss_Alg_Init();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vpss_Alg_Init fail! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    pu32Sizes[MPP_VPSS_CHN_PRI].u32Width    = pstVpssConf->stPriVencSize.u32Width;
    pu32Sizes[MPP_VPSS_CHN_PRI].u32Height   = pstVpssConf->stPriVencSize.u32Height;
    pu32Sizes[MPP_VPSS_CHN_SEC].u32Width    = pstVpssConf->stSubVencSize.u32Width;
    pu32Sizes[MPP_VPSS_CHN_SEC].u32Height   = pstVpssConf->stSubVencSize.u32Height;
    pu32Sizes[MPP_VPSS_CHN_ALG].u32Width    = VPSS_ALG_IMAGE_WIDTH;
    pu32Sizes[MPP_VPSS_CHN_ALG].u32Height   = VPSS_ALG_IMAGE_HEIGHT;
    pu32Sizes[MPP_VPSS_CHN_VO].u32Width     = 1280;
    pu32Sizes[MPP_VPSS_CHN_VO].u32Height    = 720;

    for (i=0; i<MPP_VPSS_CHN_BUTT; i++)
    {
        print_level(SV_INFO, "pu32Sizes[%d]: w:%d, h:%d\n", i, pu32Sizes[i].u32Width, pu32Sizes[i].u32Height);
    }

    u32MaxW = pstVpssConf->u32MaxW;
    u32MaxH = pstVpssConf->u32MaxH;

    for(i = 0; i < pstVpssConf->u32ChnNum; i++)
    {
        print_level(SV_INFO, "enAlgType:%d\n", pstVpssConf->enAlgType[i]);
        m_stVpssInfo.enAlgType[i] = pstVpssConf->enAlgType[i];
    }

    m_stVpssInfo.bImageMirror = pstVpssConf->bImageMirror;
    m_stVpssInfo.bImageFlip = pstVpssConf->bImageFlip;
    m_stVpssInfo.enRotateAngle = pstVpssConf->enRotateAngle;
    for (i = 0; i < pstVpssConf->u32ChnNum; i++)
    {
        s32Ret = mpp_vpss_CreateGrp(i, pu32Sizes);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_CreateGrp[%d] failed! [err=%#x]\n", i, s32Ret);
            return s32Ret;
        }
    }

    for (i = 0; i < pstVpssConf->u32ChnNum; i++)
    {
        m_stVpssInfo.enVideoMode[i] = pstVpssConf->enVideoMode;
    }

    m_stVpssInfo.u32ChnNum = pstVpssConf->u32ChnNum;
    m_stVpssInfo.pfYuvCallback = pstVpssConf->pfYuvCallback;
    m_stVpssInfo.pfRGBCallback = pstVpssConf->pfRgbCallback;
    m_stVpssInfo.pfBufCallback = pstVpssConf->pfBufCallback;

    s32Ret = RK_TDE_Open();
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_TDE_Open failed! [err=%#x]\n", s32Ret);
        return RK_FAILURE;
    }

    s32Ret = RK_MPI_SYS_MmzAlloc(&m_stVpssInfo.mbAlgRotateBlk, RK_NULL, RK_NULL, m_stVpssInfo.stVpssAlgInfo.u32Width*m_stVpssInfo.stVpssAlgInfo.u32Height*3);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_MmzAlloc failed!\n");
        return RK_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Fini()
{

    sint32 s32Ret = 0, i;

    s32Ret = mpp_vpss_Alg_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Alg_Fini failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vpss_DestroyGrp(0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_DestroyGrp failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    if (m_stVpssInfo.mbAlgRotateBlk)
    {
        RK_MPI_SYS_Free(m_stVpssInfo.mbAlgRotateBlk);
    }

    RK_TDE_Close();

    memset(&m_stVpssInfo, 0, sizeof(m_stVpssInfo));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Start()
{
    sint32 s32Ret = 0, i, j, index;
    uint32 u32Tid = 0;
    static int rchannel[MPP_VPSS_TOTAL_CHN] = {0};
    pthread_attr_t 	attr;
    m_stVpssInfo.bRunning = SV_TRUE;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    for (i = 0; i < m_stVpssInfo.u32ChnNum; i++)    // i 表示摄像头通道数
    {
        for (j = 0; j < MPP_VPSS_CHN_BUTT; j++)      // j 表示摄像头一出多通道数
        {
            if (j == MPP_VPSS_CHN_VO)    /* VO通道由VO/VMIX拉取 */
                continue;

            if (j == MPP_VPSS_CHN_ALG && m_stVpssInfo.enAlgType[i] == ALG_OFF)
                continue;

            if (j == MPP_VPSS_CHN_PRI || j == MPP_VPSS_CHN_SEC)
                continue;

            index = j + i * MPP_VPSS_CHN_BUTT;
            rchannel[index] = index;

            if (j != MPP_VPSS_CHN_ALG)
            {
                //s32Ret = pthread_create(&u32Tid, &attr, mpp_vpss_MainBody, &rchannel[index]);
            }
            else
            {
                s32Ret = pthread_create(&u32Tid, &attr, mpp_vpss_AlgBody, &rchannel[index]);
            }

            if (0 != s32Ret)
            {
                print_level(SV_ERROR, "Start thread[%d %d] for VPSS failed! [err: %s]\n", i, j, strerror(errno));
                return s32Ret;
            }
            m_stVpssInfo.u32Tid[index] = u32Tid;
        }
    }
    pthread_attr_destroy(&attr);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Stop()
{
    sint32 s32Ret = 0, i, j, index;
    void * pvRetval = NULL;

    m_stVpssInfo.bRunning = SV_FALSE;
    for(i = 0; i < m_stVpssInfo.u32ChnNum; i++)
    {
        for(j = 0; j < MPP_VPSS_CHN_BUTT; j++)
        {
            if(j == MPP_VPSS_CHN_VO)
                continue;
            index = j + i * MPP_VPSS_CHN_BUTT;
            //s32Ret = pthread_join(m_stVpssInfo.u32Tid[index], &pvRetval);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "Stop thread for VENC failed! [err=%d]\n", s32Ret);
                return s32Ret;
            }
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 暂停VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Interrupt(SV_BOOL bInterrupt)
{
    int i = 0;

    for (i = 0; i < m_stVpssInfo.u32ChnNum * MPP_VPSS_CHN_BUTT; i++)
    {
        m_stVpssInfo.bInterrupt[i] = bInterrupt;
    }
    bGetFrame = SV_FALSE;

    return SV_SUCCESS;
}

static sint32 mpp_vpss_getArrayIdx(uint32 *array, uint32 elem, sint32 len)
{
    sint32 i, idx = -1;
    for(i = 0; i < len; i++)
    {
        if(array[i] == elem)
        {
            idx = i;
            break;
        }
    }
    return idx;
}

sint32 mpp_vpss_Alg_Rotate(VIDEO_FRAME_INFO_S *pstVideoFrameInfo)
{
    sint32 s32Ret = 0;
    SV_ROT_ANGLE_E enAngle = m_stVpssInfo.enRotateAngle;
    ROTATION_E enRotation;
    TDE_HANDLE hHandle;
    TDE_SURFACE_S pstSrc = {0};
    TDE_RECT_S pstSrcRect = {0, 0, VPSS_ALG_IMAGE_WIDTH, VPSS_ALG_IMAGE_HEIGHT};
    TDE_SURFACE_S pstDst = {0};
    TDE_RECT_S pstDstRect = {0, 0, VPSS_ALG_IMAGE_HEIGHT, VPSS_ALG_IMAGE_WIDTH};
    RK_U32 u32TaskIndex = 0;
    uint32 u32Width = pstVideoFrameInfo->stVFrame.u32Width;
    uint32 u32Height = pstVideoFrameInfo->stVFrame.u32Height;
    uint32 u32BufSize = u32Width * u32Height * 3;

    if (NULL == pstVideoFrameInfo->stVFrame.pMbBlk ||  NULL == m_stVpssInfo.mbAlgRotateBlk)
    {
        print_level(SV_ERROR, "mbBlk is null!\n");
        goto exit;
    }

    switch (enAngle)
    {
        case SV_ROTATION_0:
        case SV_ROTATION_180:
            return SV_SUCCESS;

        case SV_ROTATION_90:
            enRotation = ROTATION_270;
            break;
        case SV_ROTATION_270:
            enRotation = ROTATION_90;
            break;
        default:
            print_level(SV_ERROR, "enAgle:%d is illegal!\n", enAngle);
            return SV_FAILURE;
    }

    hHandle = RK_TDE_BeginJob();
    if (RK_ERR_TDE_INVALID_HANDLE == hHandle)
    {
        print_level(SV_ERROR, "start job fail\n");
        goto exit;
    }

    pstSrc.u32Width         = VPSS_ALG_IMAGE_WIDTH;
    pstSrc.u32Height        = VPSS_ALG_IMAGE_HEIGHT;
    pstSrc.enColorFmt       = RK_FMT_RGB888;
    pstSrc.enComprocessMode = COMPRESS_MODE_NONE;
    pstSrc.pMbBlk           = pstVideoFrameInfo->stVFrame.pMbBlk;

    pstDst.u32Width         = VPSS_ALG_IMAGE_HEIGHT;
    pstDst.u32Height        = VPSS_ALG_IMAGE_WIDTH;
    pstDst.enColorFmt       = RK_FMT_RGB888;
    pstDst.enComprocessMode = COMPRESS_MODE_NONE;
    pstDst.pMbBlk           = m_stVpssInfo.mbAlgRotateBlk;

    s32Ret = RK_TDE_Rotate(hHandle, &pstSrc, &pstSrcRect, &pstDst, &pstDstRect, enRotation);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_TDE_Rotate failed! [err=%#x]\n", s32Ret);
        goto exit;
    }

    s32Ret = RK_TDE_EndJob(hHandle, RK_FALSE, RK_TRUE, 10);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_TDE_EndJob failed! [err=%#x]\n", s32Ret);
        RK_TDE_CancelJob(hHandle);
        goto exit;
    }

    s32Ret = RK_TDE_WaitForDone(hHandle);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_TDE_WaitForDone failed! [err=%#x]\n", s32Ret);
        goto exit;
    }

    void *pstSrcFrame = RK_MPI_MB_Handle2VirAddr(pstVideoFrameInfo->stVFrame.pMbBlk);
    if (NULL == pstSrcFrame)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        goto exit;
    }

    void *pstDstFrame = RK_MPI_MB_Handle2VirAddr(pstDst.pMbBlk);
    if (NULL == pstDstFrame)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        goto exit;
    }

    memcpy(pstSrcFrame, pstDstFrame, u32BufSize);
    pstVideoFrameInfo->stVFrame.u32Width = u32Height;
    pstVideoFrameInfo->stVFrame.u32Height = u32Width;
    pstVideoFrameInfo->stVFrame.u32VirWidth = u32Height;
    pstVideoFrameInfo->stVFrame.u32VirHeight = u32Width;

    #if 0
    remove("/mnt/nfs/alg_90.rgb");
    FILE *fp = fopen("/mnt/nfs/alg_90.rgb", "wb+");
    if (NULL == fp)
    {
        print_level(SV_ERROR, "open file failed\n");
    }
    else
    {
        fwrite(pstSrcFrame, 1, u32BufSize, fp);
        fflush(fp);
        fclose(fp);
    }
    #endif
    return SV_SUCCESS;

exit:
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: VPSS算法初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_Init()
{
    sint32 s32Ret;

    MS_Fini();
#if ALG_MUTLIT_BUFFER
    MH_Fini();
#endif
    s32Ret = MS_Init();
    if(s32Ret != NULL)
    {
        print_level(SV_ERROR, "MS_Init failed!\n");
        return SV_FAILURE;
    }
#if ALG_MUTLIT_BUFFER
    s32Ret = MH_Init();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Init failed!\n");
        return SV_FAILURE;
    }

    s32Ret = MH_Pretreat();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Pretreat failed!\n");
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS算法去初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_Fini()
{
    MS_Fini();
#if ALG_MUTLIT_BUFFER
    MH_Fini();
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS模块线程体(控制主码流通道、子码流通道和图片流通道)
 * 输入参数: pstVpssInfo --- 视频处理控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_vpss_MainBody(void *pvArg)
{
    #if 1
    sint32 s32Ret = 0;
    void *mb = NULL, *mb_s = NULL;
    int channel = *((int*)pvArg);
    sint32 s32ViChn, s32VpssChn;
    char thread_name[32];
    MPP_VPSS_INFO_S *pstVpssInfo = (MPP_VPSS_INFO_S *)&m_stVpssInfo;
    VIDEO_FRAME_INFO_S stVideoFrame = {0};
    //VIDEO_FRAME_INFO_S stLastVideoFrame = {0};

    s32ViChn = channel / MPP_VPSS_CHN_BUTT;
    s32VpssChn = channel % MPP_VPSS_CHN_BUTT;

    sprintf(thread_name, "mpp_vpss_body[%d %d]", s32ViChn, s32VpssChn);
    s32Ret = prctl(PR_SET_NAME, thread_name);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    print_level(SV_INFO, "++%s start\n", thread_name);
    while (pstVpssInfo->bRunning)
    {
        if (pstVpssInfo->bInterrupt[channel])
        {
            print_level(SV_INFO, "skip [%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(1000);
            continue;
        }

        s32Ret = mpp_vpss_GetFrame(s32ViChn, s32VpssChn, &stVideoFrame);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vpss_GetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(30);
            continue;
        }

#if (BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32C4))
        if(s32VpssChn != MPP_VPSS_CHN_EXT)
#endif
        {
            s32Ret = mpp_venc_SetFrame(s32ViChn, s32VpssChn, &stVideoFrame);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "mpp_venc_SetFrame[%d %d] fail, w: %d, h: %d, %p, ret[%#x]\n", s32ViChn, s32VpssChn, stVideoFrame.stVFrame.u32Width, stVideoFrame.stVFrame.u32Height, stVideoFrame.stVFrame.pMbBlk, s32Ret);
                mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stVideoFrame);
                continue;
            }
        }


        mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stVideoFrame);
    }

    print_level(SV_INFO, "++mpp_venc_MainBody end\n");
    #endif
    return NULL;
}

/******************************************************************************
 * 函数功能: VPSS模块线程体(控制算法通道)
 * 输入参数: pstVpssInfo --- 视频处理控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_vpss_AlgBody(void *pvArg)
{
    sint32 s32Ret = 0, i;
    uint32 u32Fd = 0;
    sint32 s32ViChn, s32VpssChn, s32Idx;
    int channel = *((int*)pvArg);
    char thread_name[32];
    MPP_VPSS_INFO_S *pstVpssInfo = (MPP_VPSS_INFO_S *)&m_stVpssInfo;
    VIDEO_FRAME_INFO_S stVideoFrame = {0};
    VIDEO_FRAME_INFO_S stMultiVideoFrame[MPP_VPSS_ALG_BUF_NUM] = {0};

    sprintf(thread_name, "mpp_vpss_AlgBody[%d]", channel);
    s32Ret = prctl(PR_SET_NAME, thread_name);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    s32ViChn = channel / MPP_VPSS_CHN_BUTT;
    s32VpssChn = channel %  MPP_VPSS_CHN_BUTT;

#if ALG_MUTLIT_BUFFER
    /********************************************/
    /* 和算法同步数据的初始化 */
    print_level(SV_INFO, "++mpp_vpss_AlgBody Init\n");
    for(i = 0; i < MPP_VPSS_ALG_BUF_NUM;)
    {
        s32Ret = mpp_vpss_GetFrame(s32ViChn, s32VpssChn, &stMultiVideoFrame[i]);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vpss_GetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(30);
            continue;
        }

        u32Fd = RK_MPI_MB_Handle2Fd(stMultiVideoFrame[i].stVFrame.pMbBlk);
        pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn][i] = u32Fd;
        print_level(SV_INFO, "get media_buf[%d] fd: %d\n", i, pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn][i]);
        i++;
    }

    for(i = 0; i < MPP_VPSS_ALG_BUF_NUM; i++)
    {
        mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stMultiVideoFrame[i]);
    }

    pstVpssInfo->stVpssAlgInfo.bInit[s32ViChn] = SV_TRUE;
    s32Ret = MH_Setup(s32ViChn);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Setup failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
    /*************************************************/
#endif

    print_level(SV_INFO, "++mpp_vpss_AlgBody start\n");
    while (pstVpssInfo->bRunning)
    {
        if (pstVpssInfo->bInterrupt[channel] || mpp_vi_IsRestarting())
        {
            sleep_ms(50);
            continue;
        }

        s32Ret = mpp_vpss_GetFrame(s32ViChn, s32VpssChn, &stVideoFrame);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "mpp_vpss_GetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(30);
            continue;
        }

        s32Ret = mpp_vpss_Alg_Rotate(&stVideoFrame);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "mpp_vpss_Alg_Rotate[%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(30);
            continue;
        }

#if ALG_MUTLIT_BUFFER
        s32Ret = MS_P(s32ViChn);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stVideoFrame);
            continue;
        }

        /* pd那边用完的帧会把帧设置成可写，这里清除掉已经被使用完的帧 */
        for(i = 0; i < MPP_VPSS_ALG_BUF_NUM; i++)
        {
            if (stMultiVideoFrame[i].stVFrame.pMbBlk != NULL)
            {
                u32Fd = RK_MPI_MB_Handle2Fd(stMultiVideoFrame[i].stVFrame.pMbBlk);
                s32Idx = mpp_vpss_getArrayIdx(pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn], u32Fd, MPP_VPSS_ALG_BUF_NUM);

                /* pd那边正在使用的帧被设置成了可读，所以这里只对可写的帧进行清除 */
                if(s32Idx >= 0 && MH_IsWrite(s32ViChn, s32Idx))
                {
                    MH_SetForbid(s32ViChn, s32Idx, 0);
                    mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stMultiVideoFrame[i]);
                }
            }
        }

        /* 新生成的块找到可存放的位置，并设置为可读写，表示可以被pd那边用 */
        for(i = 0; i < MPP_VPSS_ALG_BUF_NUM; i++)
        {
            if (stMultiVideoFrame[i].stVFrame.pMbBlk == NULL)
            {
                stMultiVideoFrame[i] = stVideoFrame;
                u32Fd = RK_MPI_MB_Handle2Fd(stMultiVideoFrame[i].stVFrame.pMbBlk);
                s32Idx = mpp_vpss_getArrayIdx(pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn], u32Fd, MPP_VPSS_ALG_BUF_NUM);
                if(s32Idx >= 0)
                {
                    MH_SetRW(s32ViChn, s32Idx, 0);
                }

                break;
            }
        }

        s32Ret = MS_V(s32ViChn);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stVideoFrame);
            continue;
        }
#else

        pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn][0] = RK_MPI_MB_Handle2Fd(stVideoFrame.stVFrame.pMbBlk);
        pstVpssInfo->stVpssAlgInfo.bInit[s32ViChn] = SV_TRUE;

        s32Ret = MS_V(s32ViChn);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stVideoFrame);
            continue;
        }

        sleep_ms(2);

        s32Ret = MS_P(s32ViChn);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stVideoFrame);
            continue;
        }

        mpp_vpss_ReleaseFrame(s32ViChn, s32VpssChn, &stVideoFrame);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "mpp_vpss_GetFrame[%d] fail\n", channel);
            sleep_ms(2);
            continue;
        }
#endif
    }

    print_level(SV_INFO, "++mpp_vpss_AlgBody end\n");
    return NULL;
}


/******************************************************************************
 * 函数功能: VPSS获取算法BUF的FD句柄
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_GetFD(int *pfd, int devIdx, int bufIdx)
{
    if(devIdx >= 1)
    {
        print_level(SV_ERROR, "idx:%d is invalid!\n", devIdx);
        return SV_FAILURE;
    }

    if(!m_stVpssInfo.stVpssAlgInfo.bInit[devIdx])
    {
        return SV_FAILURE;
    }
#if ALG_MUTLIT_BUFFER
    *pfd = m_stVpssInfo.stVpssAlgInfo.u32FD[devIdx][bufIdx];
#else
	*pfd = m_stVpssInfo.stVpssAlgInfo.u32FD[devIdx][0];
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS释放算法BUF的FD句柄
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_ReleaseFD(int *pfd, int devIdx)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS获取算法的图片尺寸
 * 输入参数: devIdx --- 通道号
 * 输出参数: pWidth --- 宽
             pHeight --- 高
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_GetRes(int *pWidth, int *pHeight, int devIdx)
{
    if(devIdx >= 1)
    {
        print_level(SV_ERROR, "devIdx:%d is invalid!\n", devIdx);
        return SV_FAILURE;
    }

    if (0 == m_stVpssInfo.stVpssAlgInfo.u32Width || 0 == m_stVpssInfo.stVpssAlgInfo.u32Height)
    {
        return SV_FAILURE;
    }

    *pWidth = m_stVpssInfo.stVpssAlgInfo.u32Width;
    *pHeight = m_stVpssInfo.stVpssAlgInfo.u32Height;
    return SV_SUCCESS;
}

sint32 mpp_vpss_EnableChn(sint32 s32GrpId, sint32 s32Chn)
{
    sint32 s32Ret;

    //if (!m_stVpssInfo.bChnEnable[s32Chn])
    {
        print_level(SV_INFO, "RK_MPI_VPSS_EnableChn[%d, %d].\n", s32GrpId, s32Chn);
        s32Ret = RK_MPI_VPSS_EnableChn(s32GrpId, s32Chn);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VPSS_DisableChn[%d %d] failed.\n", s32GrpId, s32Chn);
            //return SV_FAILURE;
        }
        m_stVpssInfo.bChnEnable[s32Chn] = RK_TRUE;
    }

    return SV_SUCCESS;
}

sint32 mpp_vpss_DisableChn(sint32 s32GrpId, sint32 s32Chn)
{
    sint32 s32Ret;

    //if (m_stVpssInfo.bChnEnable[s32Chn])
    {
        print_level(SV_INFO, "RK_MPI_VPSS_DisableChn[%d, %d].\n", s32GrpId, s32Chn);
        s32Ret = RK_MPI_VPSS_DisableChn(s32GrpId, s32Chn);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VPSS_DisableChn[%d %d] failed.\n", s32GrpId, s32Chn);
            //return SV_FAILURE;
        }
        m_stVpssInfo.bChnEnable[s32Chn] = RK_FALSE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建VPSS通道组
 * 输入参数: s32GrpId --- 通道组ID
             u32MaxW --- 最大图像宽度
             u32MaxH --- 最大图像高度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_CreateGrp(sint32 s32GrpId, SV_SIZE_S *pstSize)
{
    RK_S32 i = 0;
	RK_S32 s32Ret = RK_SUCCESS;
	VPSS_GRP_ATTR_S stGrpVpssAttr = {0};
	VPSS_CHN_ATTR_S stVpssChnAttr = {0};
    VIDEO_PROC_DEV_TYPE_E enProcDevType = VIDEO_PROC_DEV_RGA;

	if (s32GrpId >= VPSS_MAX_GRP_NUM)
    {
		print_level(SV_ERROR, "s32GrpId is less than the maximum channel: %d", VPSS_MAX_GRP_NUM);
		return RK_FAILURE;
	}

	stGrpVpssAttr.enPixelFormat = RK_FMT_YUV420SP;
	stGrpVpssAttr.enCompressMode = COMPRESS_MODE_NONE; // no compress
	stGrpVpssAttr.u32MaxW = 4096;
	stGrpVpssAttr.u32MaxH = 4096;
	stGrpVpssAttr.stFrameRate.s32SrcFrameRate = -1;
	stGrpVpssAttr.stFrameRate.s32DstFrameRate = -1;
	s32Ret = RK_MPI_VPSS_CreateGrp(s32GrpId, &stGrpVpssAttr);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_CreateGrp failed with %#x!\n", s32Ret);
		return s32Ret;
	}

    s32Ret = RK_MPI_VPSS_SetVProcDev(s32GrpId, enProcDevType);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_SetVProcDev failed with %#x!\n", s32Ret);
		return s32Ret;
	}

    for (i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {
        if (i == MPP_VPSS_CHN_ALG && m_stVpssInfo.enAlgType[s32GrpId] == ALG_OFF)
            continue;

        /* 主子码流改成VI直连VENC，VPSS这里不再创建通道 */
        if (i == MPP_VPSS_CHN_PRI || i == MPP_VPSS_CHN_SEC)
            continue;

        memset(&stVpssChnAttr, 0, sizeof(VPSS_CHN_ATTR_S));
        stVpssChnAttr.enChnMode = VPSS_CHN_MODE_USER;
        stVpssChnAttr.enDynamicRange = DYNAMIC_RANGE_SDR8;
        stVpssChnAttr.enCompressMode = COMPRESS_MODE_NONE;
        stVpssChnAttr.enPixelFormat = RK_FMT_YUV420SP;
        stVpssChnAttr.stFrameRate.s32SrcFrameRate = -1;
        stVpssChnAttr.stFrameRate.s32DstFrameRate = -1;
        stVpssChnAttr.u32Depth = 3;
        stVpssChnAttr.u32FrameBufCnt = 3;

        switch (i)
        {
            case MPP_VPSS_CHN_PRI:
                stVpssChnAttr.u32Width = 1920;
                stVpssChnAttr.u32Height = 1080;
                break;
            case MPP_VPSS_CHN_SEC:
                stVpssChnAttr.u32Width = pstSize[i].u32Width;
                stVpssChnAttr.u32Height = pstSize[i].u32Height;
                break;
            case MPP_VPSS_CHN_ALG:
                if(m_stVpssInfo.enAlgType[s32GrpId] == ALG_DMS)
                {
                    stVpssChnAttr.u32Width = 1280;
                    stVpssChnAttr.u32Height  = 720;
                    stVpssChnAttr.enPixelFormat = RK_FMT_YUV420SP;

                }else if (m_stVpssInfo.enAlgType[s32GrpId] == ALG_ADAS || m_stVpssInfo.enAlgType[s32GrpId] == ALG_PDS ||
                       m_stVpssInfo.enAlgType[s32GrpId] == ALG_APC)
                {
                    stVpssChnAttr.stFrameRate.s32SrcFrameRate = 30;
                    stVpssChnAttr.stFrameRate.s32DstFrameRate = 20;

                    if (ARG_IsExist("algWidth"))
                        stVpssChnAttr.u32Width = ARG_GetIntValue("algWidth");
                    else
                        stVpssChnAttr.u32Width = VPSS_ALG_IMAGE_WIDTH;

                    if (ARG_IsExist("algHeight"))
                        stVpssChnAttr.u32Height = ARG_GetIntValue("algHeight");
                    else
                        stVpssChnAttr.u32Height = VPSS_ALG_IMAGE_HEIGHT;
#if ALG_MUTLIT_BUFFER
                    stVpssChnAttr.u32FrameBufCnt = MPP_VPSS_ALG_BUF_NUM;
#endif
                    stVpssChnAttr.enPixelFormat = RK_FMT_RGB888;
                    m_stVpssInfo.stVpssAlgInfo.u32Width = stVpssChnAttr.u32Width;
                    m_stVpssInfo.stVpssAlgInfo.u32Height = stVpssChnAttr.u32Height;
                }
                break;
            case MPP_VPSS_CHN_VO:
                
                stVpssChnAttr.u32FrameBufCnt = 3;
                stVpssChnAttr.u32Width = 1280;
                stVpssChnAttr.u32Height = 720;
                stVpssChnAttr.enPixelFormat = RK_FMT_RGB565;        // RK的RGB其实是BGR，反的
                break;
            default:
                return SV_FAILURE;
        }

        print_level(SV_INFO, "VPSS[%d], width: %d, height: %d\n", i, stVpssChnAttr.u32Width, stVpssChnAttr.u32Height);
		s32Ret = RK_MPI_VPSS_SetChnAttr(s32GrpId, i, &stVpssChnAttr);
		if (s32Ret != RK_SUCCESS)
        {
			print_level(SV_ERROR, "RK_MPI_VPSS_SetChnAttr failed with %#x!\n", s32Ret);
			return s32Ret;
		}

        #if 0
		s32Ret = RK_MPI_VPSS_GetChnAttr(s32GrpId, i, &stVpssChnAttr);
		if (s32Ret != RK_SUCCESS)
        {
			print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed with %#x!\n", s32Ret);
			return s32Ret;
		}
        print_level(SV_INFO, "channel: %d, foramt: %d, w: %d, h: %d\n", i, stVpssChnAttr.enPixelFormat, stVpssChnAttr.u32Width, stVpssChnAttr.u32Height);
        #endif

		s32Ret = mpp_vpss_EnableChn(s32GrpId, i);
		if (s32Ret != RK_SUCCESS)
        {
			print_level(SV_ERROR, "mpp_vpss_EnableChn failed with %#x!\n", s32Ret);
			return s32Ret;
		}
    }

	s32Ret = RK_MPI_VPSS_StartGrp(s32GrpId);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_StartGrp failed with %#x!\n", s32Ret);
		return s32Ret;
	}

    /* 建立通道时指定1080P大小的buf，再根据实际配置的大小修改通道参数，如果建立了720P大小的buf，在web改为1080P时buf会不足 */
    //s32Ret = mpp_vpss_ReCreateChannel(s32GrpId, MPP_VPSS_CHN_PRI, pstSize[MPP_VPSS_CHN_PRI]);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_StartGrp failed with %#x!\n", s32Ret);
		return s32Ret;
	}

	return RK_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁VPSS通道组
 * 输入参数: s32GrpId --- 通道组ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_DestroyGrp(sint32 s32GrpId)
{
    RK_S32 s32Ret = 0, i;
    VPSS_GRP VpssGrp;
    VPSS_CHN VpssChn;
    VPSS_CHN_ATTR_S stVpssChnAttr = {0};

    VpssGrp = s32GrpId;
    s32Ret = RK_MPI_VPSS_StopGrp(VpssGrp);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_StopGrp failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    for (i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {
        VpssChn = i;
        s32Ret = RK_MPI_VPSS_GetChnAttr(VpssGrp, VpssChn, &stVpssChnAttr);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed! [err=%d]\n", s32Ret);
            return SV_FAILURE;
        }

        if (stVpssChnAttr.u32Width && stVpssChnAttr.u32Height)
        {
            print_level(SV_INFO, "Disable VPSS chn[%d], width: %d, height: %d\n", VpssChn, stVpssChnAttr.u32Width, stVpssChnAttr.u32Height);
            s32Ret = mpp_vpss_DisableChn(VpssGrp, VpssChn);
            if (RK_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vpss_DisableChn failed! [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
        }
    }

    s32Ret = RK_MPI_VPSS_DestroyGrp(VpssGrp);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_DestroyGrp failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新创建VPSS通道组
 * 输入参数: s32GrpId --- 通道组ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_ReCreateGrp(sint32 s32GrpId)
{
    RK_S32 i = 0;
	RK_S32 s32Ret = RK_SUCCESS;
	VPSS_GRP_ATTR_S stGrpVpssAttr = {0};
    RK_S32 s32ViChn = 0;

	if (s32GrpId >= VPSS_MAX_GRP_NUM)
    {
		print_level(SV_ERROR, "s32GrpId is less than the maximum channel: %d", VPSS_MAX_GRP_NUM);
		return RK_FAILURE;
	}

    s32Ret = mpp_sys_ViVpssUnBind(s32ViChn, s32GrpId);
    if (RK_SUCCESS != s32Ret)
    {
        printf("mpp_sys_ViVpssUnBind[%d][%d] failed! [err=%#x]\n", s32ViChn, s32GrpId, s32Ret);
        return RK_FAILURE;
    }

    s32Ret = RK_MPI_VPSS_StopGrp(s32GrpId);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_StopGrp[%d] failed.\n", s32GrpId);
    }

    for (i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {
        s32Ret = mpp_vpss_DisableChn(s32GrpId, i);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_DisableChn[%d %d] failed.\n", s32GrpId, i);
        }
    }

    s32Ret = RK_MPI_VPSS_DestroyGrp(s32GrpId);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_DestroyGrp[%d] failed.\n", s32GrpId);
    }

	stGrpVpssAttr.enPixelFormat = RK_FMT_YUV420SP;
	stGrpVpssAttr.enCompressMode = COMPRESS_MODE_NONE; // no compress
	stGrpVpssAttr.u32MaxW = 4096;
	stGrpVpssAttr.u32MaxH = 4096;
	stGrpVpssAttr.stFrameRate.s32SrcFrameRate = -1;
	stGrpVpssAttr.stFrameRate.s32DstFrameRate = -1;
	s32Ret = RK_MPI_VPSS_CreateGrp(s32GrpId, &stGrpVpssAttr);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_CreateGrp failed with %#x!\n", s32Ret);
		return s32Ret;
	}

	return RK_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新启用VPSS通道组
 * 输入参数: s32GrpId --- 通道组ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_ReStartGrp(sint32 s32GrpId)
{
    RK_S32 i = 0;
	RK_S32 s32Ret = RK_SUCCESS;
	VPSS_GRP_ATTR_S stGrpVpssAttr = {0};
    RK_S32 s32ViChn = 0;

	if (s32GrpId >= VPSS_MAX_GRP_NUM)
    {
		print_level(SV_ERROR, "s32GrpId is less than the maximum channel: %d", VPSS_MAX_GRP_NUM);
		return RK_FAILURE;
	}

    s32Ret = RK_MPI_VPSS_StartGrp(s32GrpId);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_StartGrp failed with %#x!\n", s32Ret);
		return s32Ret;
	}

    s32Ret = mpp_sys_ViVpssBind(s32ViChn, s32GrpId);
    if (RK_SUCCESS != s32Ret)
    {
        printf("mpp_sys_ViVpssUnBind[%d][%d] failed! [err=%#x]\n", s32ViChn, s32GrpId, s32Ret);
        return RK_FAILURE;
    }

	return RK_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新创建Vpss通道
 * 输入参数: s32Chn          ---  视频源通道
             s32VpssChn  ---  VPSS通道号
             u32Size      --- 通道尺寸
             bTransfor   ---  几何变换使能
             bMirror     ---  使能镜像
             bFlip            使能翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_ReCreateChannel(sint32 s32GrpId, sint32 s32VpssChn, SV_SIZE_S stSize)
{
    sint32 s32Ret = 0, i;
    sint32 s32ViChn = 0, s32VpssGroup = 0;
    sint32 rchannel = 0;
	VPSS_CHN_ATTR_S stVpssChnAttr = {0};

    if (s32GrpId < 0 || s32GrpId >= MEDIA_IN_CHN)
        return ERR_ILLEGAL_PARAM;

    if (s32VpssChn < 0 || s32VpssChn >= MPP_VPSS_CHN_BUTT)
        return ERR_ILLEGAL_PARAM;

    rchannel = s32GrpId * MPP_VPSS_CHN_BUTT + s32VpssChn;

    if (rchannel == MPP_VPSS_CHN_ALG && m_stVpssInfo.enAlgType[s32GrpId] == ALG_OFF)
        return SV_SUCCESS;

    s32Ret = RK_MPI_VPSS_GetChnAttr(s32GrpId, rchannel, &stVpssChnAttr);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed with %#x!\n", s32Ret);
		return s32Ret;
	}

    if (stVpssChnAttr.u32Width == stSize.u32Width && stVpssChnAttr.u32Height == stSize.u32Height)
    {
        print_level(SV_INFO, "vpss[%d %d] is not changed\n", s32GrpId, rchannel);
        return SV_SUCCESS;
    }

    print_level(SV_INFO, "mpp_vpss_ReCreateChannel[%d %d]\n", s32GrpId, rchannel);
    switch (rchannel)
    {
        case MPP_VPSS_CHN_VO:
            stVpssChnAttr.u32Width = 1280;
            stVpssChnAttr.u32Height = 720;
            break;
        case MPP_VPSS_CHN_ALG:
            if (m_stVpssInfo.enAlgType[s32GrpId] == ALG_ADAS || m_stVpssInfo.enAlgType[s32GrpId] == ALG_PDS ||
                   m_stVpssInfo.enAlgType[s32GrpId] == ALG_APC)
            {
                stVpssChnAttr.u32Width = VPSS_ALG_IMAGE_WIDTH;
                stVpssChnAttr.u32Height = VPSS_ALG_IMAGE_HEIGHT;
            }
            break;
        default:
            stVpssChnAttr.u32Width = stSize.u32Width;
            stVpssChnAttr.u32Height = stSize.u32Height;
            break;
    }

    print_level(SV_INFO, "VPSS[%d], width: %d, height: %d\n", rchannel, stVpssChnAttr.u32Width, stVpssChnAttr.u32Height);
	s32Ret = RK_MPI_VPSS_SetChnAttr(s32GrpId, rchannel, &stVpssChnAttr);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_SetChnAttr failed with %#x!\n", s32Ret);
		return s32Ret;
	}

    #if 0
	s32Ret = RK_MPI_VPSS_GetChnAttr(s32GrpId, rchannel, &stVpssChnAttr);
	if (s32Ret != RK_SUCCESS)
    {
		print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed with %#x!\n", s32Ret);
		return s32Ret;
	}
    print_level(SV_INFO, "channel: %d, foramt: %d, w: %d, h: %d\n", rchannel, stVpssChnAttr.enPixelFormat, stVpssChnAttr.u32Width, stVpssChnAttr.u32Height);
    #endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道画面是否镜像或反转
 * 输入参数: s32Chn --- VPSS通道号
             bMirror --- 是否水平翻转
             bFlip --- 是否垂直翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_SetChnMirrorFlip(sint32 s32Chn, SV_BOOL bMirror, SV_BOOL bFlip)
{
    sint32 s32Ret = 0, i;
    VPSS_GRP VpssGrp = s32Chn;
    VPSS_CHN VpssChn = 0;
    VPSS_CHN_ATTR_S stChnAttr = {0};

    if (s32Chn < 0 || s32Chn > VPSS_MAX_GRP_NUM)
    {
        return ERR_INVALID_CHNID;
    }

    print_level(SV_INFO, "bMirror:%d, bFlip:%d\n", bMirror, bFlip);
    for (i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {
        if (i == MPP_VPSS_CHN_ALG && m_stVpssInfo.enAlgType[s32Chn] == ALG_OFF)
            continue;

        s32Ret = RK_MPI_VPSS_GetChnAttr(VpssGrp, i, &stChnAttr);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        print_level(SV_INFO, "VPSS[%d], w: %d, h: %d, format: %d, depth: %d, mirror: %d, flip: %d\n",
                            i, stChnAttr.u32Width, stChnAttr.u32Height, stChnAttr.enPixelFormat, stChnAttr.u32Depth, stChnAttr.bMirror, stChnAttr.bFlip);

        printf("%d %d %d %d %d %d %d %d %d %d %d %d %d\n", stChnAttr.enChnMode, stChnAttr.enVideoFormat, stChnAttr.enDynamicRange,
            stChnAttr.enCompressMode, stChnAttr.stFrameRate.s32DstFrameRate, stChnAttr.stFrameRate.s32SrcFrameRate,
            stChnAttr.stAspectRatio.enMode, stChnAttr.stAspectRatio.u32BgColor, stChnAttr.stAspectRatio.stVideoRect.s32X, stChnAttr.stAspectRatio.stVideoRect.s32Y,
            stChnAttr.stAspectRatio.stVideoRect.u32Width, stChnAttr.stAspectRatio.stVideoRect.u32Height, stChnAttr.u32FrameBufCnt);

        //s32Ret = mpp_vpss_DisableChn(VpssGrp, i);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_DisableChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }


        stChnAttr.bMirror = bMirror;
        stChnAttr.bFlip = bFlip;
        s32Ret = RK_MPI_VPSS_SetChnAttr(VpssGrp, i, &stChnAttr);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_VPSS_SetChnAttr failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        //s32Ret = mpp_vpss_EnableChn(VpssGrp, i);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_DisableChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

    }

    m_stVpssInfo.bImageMirror = bMirror;
    m_stVpssInfo.bImageFlip = bFlip;
    bGetFrame = SV_FALSE;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道画面顺时针旋转角度
 * 输入参数: s32Chn --- VPSS通道号
             u32Angle --- 旋转角度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 目前是所有通道全局设置，通道号暂时无用
 *****************************************************************************/
sint32 mpp_vpss_SetChnRotate(sint32 s32Chn, SV_ROT_ANGLE_E enAngle)
{
    m_stVpssInfo.enRotateAngle = enAngle;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道是否启动低延时
 * 输入参数: s32Chn --- VPSS通道号
             bLowDelay --- 是否启动低延时
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_SetChnLowDelay(sint32 s32Chn, SV_BOOL bLowDelay)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取VPSS通道数据
 * 输入参数: s32Chn     --- 视频源通道号
             s32VpssChn --- VPSS通道号 [0, MPP_VPSS_CHN_BUTT)
             ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vpss_GetFrame(sint32 s32Chn, sint32 s32VpssChn, VIDEO_FRAME_INFO_S *pstVideoFrameInfo)
{
    sint32 s32Ret = 0;
    void *szMB;
    void *pbmp;
    sint32 rchannel = 0;
	VPSS_CHN_ATTR_S stVpssChnAttr = {0};
    static FILE *fp = NULL;
    static sint32 s32Index = 0;
    static sint32 s32Count = 0;

    if (pstVideoFrameInfo == NULL)
    {
        return ERR_NULL_PTR;
    }

    if (s32Chn >= MPP_VPSS_CHN_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    rchannel = s32Chn * MPP_VPSS_CHN_BUTT + s32VpssChn;
    s32Ret = RK_MPI_VPSS_GetChnFrame(s32Chn, rchannel, pstVideoFrameInfo, 200);
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_GetChnFrame [%d %d] fail, err[%#x]\n", s32Chn, rchannel, s32Ret);
        return SV_FAILURE;
    }

    pbmp = RK_MPI_MB_Handle2VirAddr(pstVideoFrameInfo->stVFrame.pMbBlk);
    if(pbmp == NULL)
    {
        print_level(SV_ERROR, "RK_MPI_MB_GetPtr fail\n");
        return SV_FAILURE;
    }


    #if 0
    if (rchannel == MPP_VPSS_CHN_ALG)
    {
        if (!bGetFrame)
        {
            s32Index++;
            //if (s32Index > 10)
            {
                if (NULL == fp)
                {
                    remove("/root/vpss.bin");
                    fp = fopen("/root/vpss.bin", "wb+");
                    if (NULL == fp)
                    {
                        print_level(SV_ERROR, "open file failed\n");
                    }
                }

                if (NULL != fp)
                {
                    s32Ret = RK_MPI_VPSS_GetChnAttr(s32Chn, rchannel, &stVpssChnAttr);
            		if (s32Ret != RK_SUCCESS)
                    {
            			print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed with %#x!\n", s32Ret);
            			return s32Ret;
            		}
                    print_level(SV_INFO, "channel: %d, foramt: %d, w: %d, h: %d\n", rchannel, stVpssChnAttr.enPixelFormat, stVpssChnAttr.u32Width, stVpssChnAttr.u32Height);
                    print_level(SV_INFO, "w: %d, h: %d\n", pstVideoFrameInfo->stVFrame.u32Width, pstVideoFrameInfo->stVFrame.u32Height);
                    print_level(SV_INFO, "enPixelFormat: %d\n", pstVideoFrameInfo->stVFrame.enPixelFormat);
                    fwrite(pbmp, 1, stVpssChnAttr.u32Width*stVpssChnAttr.u32Height*3, fp);
                    s32Count++;

                    if (s32Count > 0)
                    {
                        print_level(SV_INFO, "close file\n");
                        fclose(fp);
                        fp = NULL;
                        bGetFrame = SV_TRUE;
                    }
                }
            }
        }
        else
        {
            s32Index = 0;
            s32Count = 0;
        }

    }
    #endif

    if (s32VpssChn != MPP_VPSS_CHN_ALG)
    {
        if (s32VpssChn == MPP_VPSS_CHN_VO
            || (s32VpssChn != MPP_VPSS_CHN_VO && (m_stVpssInfo.stNetworkStat[NETWORK_TYPE_LAN].bExist || m_stVpssInfo.stNetworkStat[NETWORK_TYPE_WIFI].bExist)))
        {
            s32Ret = mpp_vosd_internal_callback(s32Chn, s32VpssChn, pbmp, pstVideoFrameInfo->stVFrame.u32Width, pstVideoFrameInfo->stVFrame.u32Height);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_WARN, "mpp_vosd_callback fail\n");
            }

            s32Ret = mpp_vosd_external_callback(s32Chn, s32VpssChn, pbmp, pstVideoFrameInfo->stVFrame.u32Width, pstVideoFrameInfo->stVFrame.u32Height);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_WARN, "mpp_vosd_callback fail\n");
            }
        }
    }

    RK_MPI_SYS_MmzFlushCache(pstVideoFrameInfo->stVFrame.pMbBlk, RK_FALSE);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 释放VPSS通道数据
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_VPSS_CHN_BUTT)
             ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
 sint32 mpp_vpss_ReleaseFrame(sint32 s32Chn, sint32 s32VpssChn, VIDEO_FRAME_INFO_S *pstVideoFrameInfo)
{
    sint32 s32Ret = 0;
    if (NULL == pstVideoFrameInfo || NULL == pstVideoFrameInfo->stVFrame.pMbBlk)
    {
        print_level(SV_INFO, "get null frame...\n");
        return SV_SUCCESS;
    }

    s32Ret = RK_MPI_VPSS_ReleaseChnFrame(s32Chn, s32VpssChn, pstVideoFrameInfo);
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_ReleaseChnFrame failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    memset(pstVideoFrameInfo, 0, sizeof(VIDEO_FRAME_INFO_S));

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 获取通道一张灰阶图
 * 输入参数: s32Chn --- VPSS通道号
 * 输出参数: ppvBuf --- 数据缓存指针
             pu32Width --- 画面宽度
             pu32Height --- 画面高度
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 读取完数据后需要调用 mpp_vpss_ReleaseChnGrayFrame 释放数据
 *****************************************************************************/
sint32 mpp_vpss_GetChnGrayFrame(sint32 s32Chn, sint32 s32VpssChn, void **ppvBuf, uint32 *pu32Width, uint32 *pu32Height)
{
    sint32 s32Ret = 0;
    VPSS_GRP VpssGrp = 0;
    VPSS_CHN_ATTR_S stChnAttr;
    sint32 s32GetFrameMilliSec = 2000;

    if (s32VpssChn >= 2)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = RK_MPI_VPSS_GetChnAttr(VpssGrp, s32VpssChn, &stChnAttr);
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed!\n");
        return SV_FAILURE;
    }
    stChnAttr.u32Depth = 2;
    s32Ret = RK_MPI_VPSS_SetChnAttr(VpssGrp, s32VpssChn, &stChnAttr) ;
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_SetChnAttr failed!\n");
        return SV_FAILURE;
    }

    s32Ret = RK_MPI_VPSS_GetChnFrame(VpssGrp, s32VpssChn, &m_stVideoFrame, s32GetFrameMilliSec);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_GetChnFrame failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    #if 0
    //print_level(SV_DEBUG, "u32PoolId:%d, u32Field:%d, enPixelFormat:%d, enVideoFormat:%d, enCompressMode:%d, u32PhyAddr:%#x, pVirAdd:%#x, u32Stride:%d, pHeaderVirAddr:%#x, u32HeaderStride:%d\n", m_stVideoFrame.u32PoolId, m_stVideoFrame.stVFrame.u32Field, \
                    m_stVideoFrame.stVFrame.enPixelFormat, m_stVideoFrame.stVFrame.enVideoFormat, m_stVideoFrame.stVFrame.enCompressMode, m_stVideoFrame.stVFrame.u32PhyAddr[0], m_stVideoFrame.stVFrame.pVirAddr[0], \
                    m_stVideoFrame.stVFrame.u32Stride[0], m_stVideoFrame.stVFrame.pHeaderVirAddr[0], m_stVideoFrame.stVFrame.u32HeaderStride[0]);
    m_pvAddr = RK_MPI_SYS_Mmap(m_stVideoFrame.stVFrame.u64PhyAddr[0], m_stVideoFrame.stVFrame.u32Width*m_stVideoFrame.stVFrame.u32Height);
    *ppvBuf = m_pvAddr;//m_stVideoFrame.stVFrame.pVirAddr[0];
    *pu32Width = m_stVideoFrame.stVFrame.u32Width;
    *pu32Height = m_stVideoFrame.stVFrame.u32Height;
    #endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 释放灰阶图数据
 * 输入参数: s32Chn --- 视频源通道号
             s32VpssChn--- VPSS通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_ReleaseChnGrayFrame(sint32 s32Chn, sint32 s32VpssChn)
{
    sint32 s32Ret = 0;
    VPSS_GRP VpssGrp = 0;

    if (s32VpssChn >= 2)
    {
        return ERR_ILLEGAL_PARAM;
    }

    //RK_MPI_SYS_Munmap(m_pvAddr, m_stVideoFrame.stVFrame.u32Width*m_stVideoFrame.stVFrame.u32Height);
    s32Ret = RK_MPI_VPSS_ReleaseChnFrame(VpssGrp, s32VpssChn, &m_stVideoFrame);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_VPSS_ReleaseChnFrame failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

static sint32 mpp_vpss_CreatCompoundVB(MPP_VPSS_DUMP_MEMBUF_S *pstVpssMem)
{
    return SV_SUCCESS;
}

static sint32 mpp_vpss_ReleaseCompoundVB(void)
{
    return SV_SUCCESS;
}

static void *mpp_vpss_GetSDFrameBody(void *pvArg)
{
    return NULL;
}

sint32 mpp_vpss_GetSDFrame_Start(void)
{
    sint32 s32Ret = 0;
    pthread_t thread = 0;
    struct sched_param stsched_param = {0};

    stVpssDEI.bRunning = SV_TRUE;

    s32Ret = pthread_create(&thread, NULL, mpp_vpss_GetSDFrameBody, &stVpssDEI);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VPSS_SD failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    stVpssDEI.u32DEITid = thread;
    return SV_SUCCESS;
}

sint32 mpp_vpss_GetSDFrame_Stop(void)
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;

    stVpssDEI.bRunning = SV_FALSE;
    s32Ret = pthread_join(stVpssDEI.u32DEITid, &pvRetval);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Stop thread for VENC failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}


sint32 mpp_vpss_RenewGrp(sint32 s32GrpId, VIDEO_MODE_EE newVideoMode, uint32 u32NewW, uint32 u32NewH)
{
    RK_S32 s32Ret = 0, i, s32ViChn = 0;
	VPSS_CHN_ATTR_S stVpssChnAttr = {0};
    SV_SIZE_S stSizes[MPP_VPSS_CHN_BUTT] = {0};

    if(s32GrpId >= MEDIA_MAX_CHN || s32GrpId < 0)
    {
        return ERR_ILLEGAL_PARAM;
    }
/*
    if(m_stVpssInfo.enVideoMode[s32GrpId] == newVideoMode)
    {
        return SV_SUCCESS;
    }
*/
    print_level(SV_INFO, "mpp_vpss_RenewGrp s32GrpId:%d\n", s32GrpId);

    for (i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {
        s32Ret = RK_MPI_VPSS_GetChnAttr(s32GrpId, i, &stVpssChnAttr);
    	if (s32Ret != RK_SUCCESS)
        {
    		print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed with %#x!\n", s32Ret);
    		return s32Ret;
    	}

        stSizes[i].u32Width = stVpssChnAttr.u32Width;
        stSizes[i].u32Height = stVpssChnAttr.u32Height;
    }

    s32Ret = mpp_vpss_DestroyGrp(s32GrpId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_DestroyGrp[%d] failed! [err=%#x]\n", s32GrpId, s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vpss_CreateGrp(s32GrpId, stSizes);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_CreateGrp[%d] failed! [err=%#x]\n", s32GrpId, s32Ret);
        return s32Ret;
    }

    m_stVpssInfo.enVideoMode[s32GrpId] = newVideoMode;
    return SV_SUCCESS;
}

void mpp_vpss_SetPriVencSize(SV_SIZE_S stPriVencSize)
{
    stVpssDEI.stPriVencSize = stPriVencSize;
}



