/******************************************************************************
Copyright (C) 2022 广州敏视数码科技有限公司版权所有.

文件名: libgb28181.h

文件功能描述: GB28181设备端端功能定义

版本: v1.0.0(最新版本号)
  
历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#ifndef _LIBGB28181_H
#define _LIBGB28181_H

#include "common.h"
#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

typedef enum tagCallBackType_E
{
    CB_ENABLE = 0,                       
	CB_UNENABLE,
	CB_UPDATECFG,
	CB_IPCHANGE,
	CB_PORTCHANGE,
	
    CB_BUTT
} callback_type;


/* 模块配置参数 */
typedef struct tag_libgb28181Cfg_S
{
	uint32  u32SipPort;
	char    szSipId[48];
    char 	szSipDOMAIN[48];
	char 	szSipIp[48];
	char 	szSipUser[48];
	char 	szSipPwd[48];
	char    szLocalIp[48];
	uint32  u32DevPort;
	uint32  u32KaPeriod;
	uint32  u32RegInterval;
	SV_BOOL	bEnable;
}GB28181_CFG_PARAM_S;

char *GetLocalIP();

char *GetSipDOMAIN();

char *GetSipUser();

char *GetSipPasswd();

uint32 GetKaPeriod();

SV_BOOL GetRunning();

SV_BOOL GetRegister();

void SetRegister(SV_BOOL bReg);

void SetRunning(SV_BOOL bStart);

/******************************************************************************
 * 函数功能: control模块启用/关闭 gb28181接口
 * 输入参数: cfg_enable --- 配置文件参数
 			 set_enable --- 设置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
SV_BOOL gb28181_controlCallback(callback_type CBType,GB28181_CFG_PARAM_S *pstSetParam);

SV_BOOL gb28181_ipChangeCallback(GB28181_CFG_PARAM_S *pstSetParam);

/******************************************************************************
 * 函数功能: 初始化GB28181服务器模块
 * 输入参数: pstInitParam --- 初始化配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 GB28181_SVR_Init(GB28181_CFG_PARAM_S *pstInitParam);

/******************************************************************************
 * 函数功能: 去GB28181服务器模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 GB28181_SVR_Fini();

/******************************************************************************
 * 函数功能: 启动GB28181服务器模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 GB28181_SVR_Start();

/******************************************************************************
 * 函数功能: 停止GB28181服务器模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 GB28181_SVR_Stop();

/******************************************************************************
 * 函数功能: 设置GB28181服务器模块配置参数
 * 输入参数: pstConnCfg --- 配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 GB28181_SVR_ConfigSet(GB28181_CFG_PARAM_S *pstConnCfg);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */



#endif // _LIBGN28181_H


