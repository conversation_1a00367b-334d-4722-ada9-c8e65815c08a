﻿/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_vpss.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2021-04-26

文件功能描述: 封装RK MPP视频处理子系统模块功能

其他:

版本: v1.0.0(最新版本号)

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述


*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <math.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "common.h"
#include "../../../include/board.h"
#include "rkmedia_api.h"
#include "mpp_com.h"
#include "mpp_vpss.h"
#include "media.h"
#include "mpp_vosd.h"
#include "media_sem.h"
#include "media_shm.h"


#define MPP_VPSS_TMP_FORMAT IMAGE_TYPE_NV12


#define MPP_VPSS_TOTAL_CHN (MPP_VPSS_CHN_BUTT*MEDIA_MAX_CHN)

#define printf_RgaAttr(stRgaAttr) \
{ \
    print_level(SV_INFO, "u16Rotaion:%d bEnBufPool:%d u16BufPoolCnt:%d enFlip:%d \n"  \
                         "In==> imgType:%d u32X:%d u32Y:%d u32Width:%d u32Height:%d u32HorStride:%d u32VirStride:%d \n"  \
                         "OUt=> imgType:%d u32X:%d u32Y:%d u32Width:%d u32Height:%d u32HorStride:%d u32VirStride:%d \n\n",  \
                         stRgaAttr.u16Rotaion, stRgaAttr.bEnBufPool, stRgaAttr.u16BufPoolCnt,stRgaAttr.enFlip,  \
                         stRgaAttr.stImgIn.imgType,stRgaAttr.stImgIn.u32X,stRgaAttr.stImgIn.u32Y,stRgaAttr.stImgIn.u32Width,stRgaAttr.stImgIn.u32Height,stRgaAttr.stImgIn.u32HorStride,stRgaAttr.stImgIn.u32VirStride,  \
                         stRgaAttr.stImgOut.imgType,stRgaAttr.stImgOut.u32X,stRgaAttr.stImgOut.u32Y,stRgaAttr.stImgOut.u32Width,stRgaAttr.stImgOut.u32Height,stRgaAttr.stImgOut.u32HorStride,stRgaAttr.stImgOut.u32VirStride \
                         ); \
}

#define MPP_VPSS_ALG_BUF_NUM    3

/*************************************************************
 * 通道 0 为 主码流绑定VENC H264 通道
 * 通道 1 为 子码流绑定VENC H264 通道
 * 通道 2 为 jpeg流绑定VENC mjpeg 通道
 * 通道 3 为 VO输出通道
 * 通道 4 为 算法接口
 *************************************************************/

/* 算法通道信息 */
typedef struct tagVpssAlgInfo_S
{
    SV_BOOL bInit[VIM_MAX_DEV_NUM];
    uint32  u32FD[VIM_MAX_DEV_NUM][MPP_VPSS_ALG_BUF_NUM];
    uint64  u32Pts[VIM_MAX_DEV_NUM][MPP_VPSS_ALG_BUF_NUM];
    uint32  u32Width;
    uint32  u32Height;
} MPP_VPSS_ALG_INFO_S;

/* 视频处理模块控制信息 */
typedef struct tagVpssInfo_S
{
    uint32              u32ChnNum;                          /* 视频源通道数目 */
    VIDEO_MODE_EE       enVideoMode[MEDIA_MAX_CHN];         /* 视频制式 */
    CHN_ALG_E           enAlgType[MEDIA_MAX_CHN];           /* 算法类型 */
    RGA_FLIP_E          enFlip;                             /* 画面水平垂直翻转模式 */
    SV_ROT_ANGLE_E      enRotateAngle;                      /* 画面旋转角度 */
    SV_SIZE_S           u32Sizes[MPP_VPSS_MAX_CHN_NUM];     /* 不同通道的尺寸 */ //对于RK版本的venc,不支持设置尺寸,所以应该在VPSS先设置
    uint32              u32Tid[MPP_VPSS_TOTAL_CHN];         /* 线程id */
    uint32              u32MainTid[VIM_MAX_DEV_NUM];        /* 主码流线程id */
    uint32              u32SecTid[VIM_MAX_DEV_NUM];         /* 子码流线程id */
    uint32              u32JpegTid[VIM_MAX_DEV_NUM];        /* jpeg流线程id */
    uint32              u32AlgTid[VIM_MAX_DEV_NUM];         /* 算法流线程id */
    SV_BOOL             bRunning;                           /* 线程运行标志位 */
    SV_BOOL             bInterrupt[MPP_VPSS_TOTAL_CHN];     /* 是否暂停获取通道数据 */
    NETWORK_STAT_S      stNetworkStat[NETWORK_TYPE_BUTT];   /* 网络状态，决定是否要进行网络流的osd叠加 */
    MEDIA_YUV_CALLBACK  pfYuvCallback;                      /* YUV数据回调函数指针 */
    MEDIA_RGB_CALLBACK  pfRGBCallback;                      /* RGB数据回调函数指针 */
    MEDIA_BUF_CALLBACK  pfBufCallback;                      /* BUF数据回调函数指针 */
    SV_BOOL             bShowChn[4];                        /* OSD 绘图通道使能标志 */
    SEN_TYPE_E          enSenChipType;                      /* 前端sensor芯片类型 */
    MPP_VPSS_ALG_INFO_S stVpssAlgInfo;
} MPP_VPSS_INFO_S;

MPP_VPSS_INFO_S m_stVpssInfo = {0};     /* 视频处理模块控制信息 */

typedef struct tagVpss_DUMP_MEMBUF_S
{
    SV_BOOL bCreated;
    uint64  u64PhyAddr;
    uint64  u64VirAddr;
    uint32  u32MemSize;
} MPP_VPSS_DUMP_MEMBUF_S;

typedef struct tagVpss_DEI_S
{
    SV_BOOL   bRunning;                 /* 线程是否正在运行 */
    SV_SIZE_S stPriVencSize;            /* 主码流编码画面大小 (宽高) */
    uint32    u32DEITid;                /* DEI线程ID */
}MPP_VPSS_DEI_S;

static MPP_VPSS_DUMP_MEMBUF_S stVpssMem[3] = {0};
static MPP_VPSS_DEI_S stVpssDEI = {0};
static MEDIA_BUFFER stRgaMb[MEDIA_MAX_CHN] = {RK_NULL};

void * mpp_vpss_MainBody(void *pvArg);
void * mpp_vpss_AlgBody(void *pvArg);
sint32 mpp_vpss_Alg_Init();
sint32 mpp_vpss_Alg_Fini();

sint32 mpp_vpss_SetNetwork(NETWORK_STAT_S *pstNetworkStat)
{
    switch(pstNetworkStat->enNetworkType)
    {
        case NETWORK_TYPE_LAN:
            m_stVpssInfo.stNetworkStat[NETWORK_TYPE_LAN].bExist = pstNetworkStat->bExist;
            print_level(SV_INFO, "lan: %d, wifi: %d\n", m_stVpssInfo.stNetworkStat[NETWORK_TYPE_LAN].bExist,
                        m_stVpssInfo.stNetworkStat[NETWORK_TYPE_WIFI].bExist);
            break;

        case NETWORK_TYPE_WIFI:
            m_stVpssInfo.stNetworkStat[NETWORK_TYPE_WIFI].bExist = pstNetworkStat->bExist;
            print_level(SV_INFO, "lan: %d, wifi: %d\n", m_stVpssInfo.stNetworkStat[NETWORK_TYPE_LAN].bExist,
                        m_stVpssInfo.stNetworkStat[NETWORK_TYPE_WIFI].bExist);
            break;

        default:
            break;
    }

    return SV_SUCCESS;
}

sint32 mpp_vpss_get_ViChn(sint32 s32VpssChn)
{

    sint32 s32ViChn = 1;    // 默认无OSD叠加

    if (s32VpssChn < MPP_VPSS_CHN_ALG && m_stVpssInfo.bShowChn[s32VpssChn] == SV_TRUE)
    {
        s32ViChn = 0;
    }

    return s32ViChn;
}

/******************************************************************************
 * 函数功能: 初始化VPSS模块
 * 输入参数: pstVpssConf --- 视频处理配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS --- 成功
             ERR_NULL_PTR --- 传入参数指针为NULL
             ERR_ILLEGAL_PARAM --- 参数错误
             ERR_NOT_SURPPORT --- 不支持配置
             ERR_SYS_NOTREADY --- 系统未初始化
             SV_FAILURE --- 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Init(MPP_VPSS_CONF_S *pstVpssConf)
{

    sint32 s32Ret = 0, i;
    uint32 u32MaxW, u32MaxH;
    SV_SIZE_S *pu32Sizes = m_stVpssInfo.u32Sizes;

    if (NULL == pstVpssConf)
    {
        return ERR_NULL_PTR;
    }

    if (0 == pstVpssConf->u32ChnNum || pstVpssConf->u32ChnNum > VIM_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    for (i = 0; i < NETWORK_TYPE_BUTT; i++)
    {
        m_stVpssInfo.stNetworkStat[i].enNetworkType = i;
    }

    s32Ret = mpp_vpss_Alg_Init();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vpss_Alg_Init fail! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    pu32Sizes[0].u32Width   = pstVpssConf->stPriVencSize.u32Width;
    pu32Sizes[0].u32Height  = pstVpssConf->stPriVencSize.u32Height;
    pu32Sizes[1].u32Width   = pstVpssConf->stSubVencSize.u32Width;
    pu32Sizes[1].u32Height  = pstVpssConf->stSubVencSize.u32Height;
    pu32Sizes[2].u32Width   = pstVpssConf->stJpegVencSize.u32Width;
    pu32Sizes[2].u32Height  = pstVpssConf->stJpegVencSize.u32Height;

    for (i=0; i<4; i++)
    {
        print_level(SV_INFO, "pu32Sizes[%d]: w:%d, h:%d\n", i, pu32Sizes[i].u32Width, pu32Sizes[i].u32Height);
    }

    u32MaxW = pstVpssConf->u32MaxW;
    u32MaxH = pstVpssConf->u32MaxH;

    for(i = 0; i < pstVpssConf->u32ChnNum; i++)
    {
        print_level(SV_INFO, "enAlgType:%d\n", pstVpssConf->enAlgType[i]);
        m_stVpssInfo.enAlgType[i] = pstVpssConf->enAlgType[i];
    }

    for(i = 0; i < 4; i++)
    {
        m_stVpssInfo.bShowChn[i] = pstVpssConf->bShowChn[i];
    }

    m_stVpssInfo.enRotateAngle = pstVpssConf->enRotateAngle;
    for (i = 0; i < pstVpssConf->u32ChnNum; i++)
    {
        s32Ret = mpp_vpss_CreateGrp(i, pu32Sizes);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_CreateGrp[%d] failed! [err=%#x]\n", i, s32Ret);
            return s32Ret;
        }
    }

    m_stVpssInfo.u32ChnNum = pstVpssConf->u32ChnNum;
    m_stVpssInfo.enSenChipType = pstVpssConf->enSenChipType;
    m_stVpssInfo.pfYuvCallback = pstVpssConf->pfYuvCallback;
    m_stVpssInfo.pfRGBCallback = pstVpssConf->pfRgbCallback;
    m_stVpssInfo.pfBufCallback = pstVpssConf->pfBufCallback;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Fini()
{

    sint32 s32Ret = 0, i;

    s32Ret = mpp_vpss_Alg_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Alg_Fini failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vpss_DestroyGrp(0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_DestroyGrp failed! [err=%d]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Start()
{
    sint32 s32Ret = 0, i, j, index;
    uint32 u32Tid = 0;
    static int rchannel[MPP_VPSS_TOTAL_CHN] = {0};
    pthread_attr_t 	attr;
    m_stVpssInfo.bRunning = SV_TRUE;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
    for(i = 0; i < m_stVpssInfo.u32ChnNum; i ++)    // i 表示摄像头通道数
    {
        for(j = 0; j < MPP_VPSS_CHN_BUTT; j++)      // j 表示摄像头一出多通道数
        {

            if (j == MPP_VPSS_CHN_PRI)
                continue;

            if(j == MPP_VPSS_CHN_VO)    /* VO通道由VO/VMIX拉取 */\
                continue;

            if(j == MPP_VPSS_CHN_ALG && m_stVpssInfo.enAlgType[i] == ALG_OFF)
                continue;

            index = j + i * MPP_VPSS_CHN_BUTT;
            rchannel[index] = index;

            if(j != MPP_VPSS_CHN_ALG)
            {
                s32Ret = pthread_create(&u32Tid, &attr, mpp_vpss_MainBody, &rchannel[index]);
            }
            else
            {
                s32Ret = pthread_create(&u32Tid, &attr, mpp_vpss_AlgBody, &rchannel[index]);
            }

            if(0 != s32Ret)
            {
                print_level(SV_ERROR, "Start thread[%d %d] for VPSS failed! [err: %s]\n", i, j, strerror(errno));
                return s32Ret;
            }
            m_stVpssInfo.u32Tid[index] = u32Tid;
        }
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Stop()
{
    sint32 s32Ret = 0, i, j, index;
    void * pvRetval = NULL;

    m_stVpssInfo.bRunning = SV_FALSE;
    for(i = 0; i < m_stVpssInfo.u32ChnNum; i++)
    {
        for(j = 0; j < MPP_VPSS_CHN_BUTT; j++)
        {
            if(j == MPP_VPSS_CHN_VO)
                continue;
            index = j + i * MPP_VPSS_CHN_BUTT;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 暂停VPSS模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Interrupt(SV_BOOL bInterrupt)
{
    int i, j;
    for(i = 0; i < m_stVpssInfo.u32ChnNum * MPP_VPSS_CHN_BUTT; i ++)
    {
        for(j = 0; j < MPP_VPSS_CHN_BUTT; j++)
        {

            m_stVpssInfo.bInterrupt[i] = bInterrupt;
        }
    }

    return SV_SUCCESS;
}

static sint32 mpp_vpss_getArrayIdx(uint32 *array, uint32 elem, sint32 len)
{
    sint32 i, idx = -1;
    for(i = 0; i < len; i++)
    {
        if(array[i] == elem)
        {
            idx = i;
            break;
        }
    }
    return idx;
}

/******************************************************************************
 * 函数功能: VPSS算法初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_Init()
{
    sint32 s32Ret;
    MS_Fini();
#if ALG_MUTLIT_BUFFER
    MH_Fini();
#endif
    s32Ret = MS_Init();
    if(s32Ret != NULL)
    {
        return SV_FAILURE;
    }
#if ALG_MUTLIT_BUFFER
    s32Ret = MH_Init();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Init failed!\n");
        return SV_FAILURE;
    }

    s32Ret = MH_Pretreat();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Pretreat failed!\n");
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS算法去初始化
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_Fini()
{
    sint32 s32Ret;
    s32Ret = MS_Fini();
    if(s32Ret != NULL)
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS模块线程体(控制主码流通道、子码流通道和图片流通道)
 * 输入参数: pstVpssInfo --- 视频处理控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_vpss_MainBody(void *pvArg)
{
    sint32 s32Ret = 0;
    void *mb = NULL, *mb_s = NULL;
    int channel = *((int*)pvArg);
    sint32 s32ViChn, s32VpssChn;
    char thread_name[32];
    MPP_VPSS_INFO_S *pstVpssInfo = (MPP_VPSS_INFO_S *)&m_stVpssInfo;

    s32ViChn = channel / MPP_VPSS_CHN_BUTT;
    s32VpssChn = channel % MPP_VPSS_CHN_BUTT;

    print_level(SV_INFO, "VPSS main body, VPSS(RGA)[%d] to VENC.\n", channel);
    sprintf(thread_name, "mpp_vpss_body[%d %d]", s32ViChn, s32VpssChn);
    s32Ret = prctl(PR_SET_NAME, thread_name);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstVpssInfo->bRunning)
    {
        if(pstVpssInfo->bInterrupt[channel])
        {
            mpp_vpss_ReleaseFrame(&mb_s);
            sleep_ms(30);
            continue;
        }

        s32Ret = mpp_vpss_GetFrame(s32ViChn, s32VpssChn, &mb);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vpss_GetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(30);
            continue;
        }

#if (BOARD == BOARD_ADA32V2)
        if(s32VpssChn != MPP_VPSS_CHN_EXT)
#endif
        {
            s32Ret = mpp_venc_SetFrame(s32ViChn, s32VpssChn, mb);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_INFO, "mpp_venc_SetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
                mpp_vpss_ReleaseFrame(&mb);
                continue;
            }
        }

        mpp_vpss_ReleaseFrame(&mb_s);
        mb_s = mb;
        mb = NULL;
    }

    mpp_vpss_ReleaseFrame(&mb_s);

    return NULL;
}


/******************************************************************************
 * 函数功能: VPSS模块线程体(控制算法通道)
 * 输入参数: pstVpssInfo --- 视频处理控制信息
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void * mpp_vpss_AlgBody(void *pvArg)
{
    sint32 s32Ret = 0, i;
    uint32 u32Fd;
    sint32 s32ViChn, s32VpssChn, s32Idx;
    void *mb = NULL;
    void *szMB[MPP_VPSS_ALG_BUF_NUM] = {NULL};
    int channel = *((int*)pvArg);
    char thread_name[32];
    MPP_VPSS_INFO_S *pstVpssInfo = (MPP_VPSS_INFO_S *)&m_stVpssInfo;
    memset(szMB, NULL, sizeof(szMB));

    sprintf(thread_name, "mpp_vpss_AlgBody[%d]", channel);
    s32Ret = prctl(PR_SET_NAME, thread_name);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    s32ViChn = channel / MPP_VPSS_CHN_BUTT;
    s32VpssChn = channel %  MPP_VPSS_CHN_BUTT;
    print_level(SV_INFO, "VPSS alg body, VPSS(RGA)[%d] to VENC.\n", channel);

#if ALG_MUTLIT_BUFFER
    /********************************************/
    /* 和算法同步数据的初始化 */
    print_level(SV_INFO, "++mpp_vpss_AlgBody Init\n");
    for(i = 0; i < MPP_VPSS_ALG_BUF_NUM;)
    {
        s32Ret = mpp_vpss_GetFrame(s32ViChn, s32VpssChn, &szMB[i]);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "mpp_vpss_GetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(30);
            continue;
        }

        u32Fd = RK_MPI_MB_GetFD(szMB[i]);
        pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn][i] = u32Fd;
        i++;
    }

    for(i = 0; i < MPP_VPSS_ALG_BUF_NUM; i++)
    {
        mpp_vpss_ReleaseFrame(&szMB[i]);
    }

    pstVpssInfo->stVpssAlgInfo.bInit[s32ViChn] = SV_TRUE;
    s32Ret = MH_Setup(s32ViChn);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MH_Setup failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }

    if (MS_GetValue(s32ViChn) <= 0)
    {
        print_level(SV_INFO, "MS value <= 0, MS_P first!\n");
        s32Ret = MS_V(s32ViChn);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
        }
    }
    /*************************************************/
#endif

    print_level(SV_INFO, "++mpp_vpss_AlgBody start\n");
    while (pstVpssInfo->bRunning)
    {
        if(pstVpssInfo->bInterrupt[channel])
        {
            sleep_ms(30);
            continue;
        }

        s32Ret = mpp_vpss_GetFrame(s32ViChn, s32VpssChn, &mb);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vpss_GetFrame[%d %d] fail\n", s32ViChn, s32VpssChn);
            sleep_ms(30);
            continue;
        }

#if ALG_MUTLIT_BUFFER
        u32Fd = RK_MPI_MB_GetFD(mb);

        s32Ret = MS_P(s32ViChn);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(&mb);
            continue;
        }

        /* 清理多余的图片帧 */
        for(i = 0; i < MPP_VPSS_ALG_BUF_NUM; i++)
        {
            if(szMB[i] != NULL)
            {
                u32Fd = RK_MPI_MB_GetFD(szMB[i]);
                s32Idx = mpp_vpss_getArrayIdx(pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn], u32Fd, MPP_VPSS_ALG_BUF_NUM);
                if(s32Idx >= 0 && MH_IsWrite(s32ViChn, s32Idx))
                {
                    MH_SetForbid(s32ViChn, s32Idx, 0);

                    u32Fd = RK_MPI_MB_GetFD(szMB[i]);
                    mpp_vpss_ReleaseFrame(&szMB[i]);
                }
            }
        }

        /* 增加可读写块 */
        for(i = 0; i < MPP_VPSS_ALG_BUF_NUM; i++)
        {
            if(szMB[i] == NULL)
            {
                szMB[i] = mb;
                u32Fd = RK_MPI_MB_GetFD(szMB[i]);
                s32Idx = mpp_vpss_getArrayIdx(pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn], u32Fd, MPP_VPSS_ALG_BUF_NUM);
                if(s32Idx >= 0)
                {
                    MH_SetRW(s32ViChn, s32Idx, 0);
                }

                break;
            }
        }


        s32Ret = MS_V(s32ViChn);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(&mb);
            continue;
        }
#else
        pstVpssInfo->stVpssAlgInfo.u32FD[s32ViChn][0] = RK_MPI_MB_GetFD(mb);
        pstVpssInfo->stVpssAlgInfo.bInit[s32ViChn] = SV_TRUE;

        s32Ret = MS_V(s32ViChn);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(&mb);
            continue;
        }

        sleep_ms(2);

        s32Ret = MS_P(s32ViChn);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "MS_V[%d] fail\n", 0);
            mpp_vpss_ReleaseFrame(&mb);
            continue;
        }

        s32Ret = mpp_vpss_ReleaseFrame(&mb);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "mpp_vpss_GetFrame[%d] fail\n", channel);
            sleep_ms(2);
            continue;
        }
#endif
    }

    print_level(SV_INFO, "++mpp_vpss_AlgBody end\n");
    return NULL;
}


/******************************************************************************
 * 函数功能: VPSS获取算法BUF的FD句柄
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_GetFD(int *pfd, int devIdx, int bufIdx)
{

    if(devIdx >= 1)
    {
        print_level(SV_ERROR, "idx:%d is invalid!\n", devIdx);
        return SV_FAILURE;
    }

    if(!m_stVpssInfo.stVpssAlgInfo.bInit[devIdx])
    {
        return SV_FAILURE;
    }
#if ALG_MUTLIT_BUFFER
    *pfd = m_stVpssInfo.stVpssAlgInfo.u32FD[devIdx][bufIdx];
#else
	*pfd = m_stVpssInfo.stVpssAlgInfo.u32FD[devIdx][0];
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS释放算法BUF的FD句柄
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_ReleaseFD(int *pfd, int devIdx)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: VPSS获取算法的图片尺寸
 * 输入参数: devIdx --- 通道号
 * 输出参数: pWidth --- 宽
             pHeight --- 高
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_Alg_GetRes(int *pWidth, int *pHeight, int devIdx)
{

    if(devIdx >= 1)
    {
        print_level(SV_ERROR, "devIdx:%d is invalid!\n", devIdx);
        return SV_FAILURE;
    }

    if (0 == m_stVpssInfo.stVpssAlgInfo.u32Width || 0 == m_stVpssInfo.stVpssAlgInfo.u32Height)
    {
        return SV_FAILURE;
    }

    *pWidth = m_stVpssInfo.stVpssAlgInfo.u32Width;
    *pHeight = m_stVpssInfo.stVpssAlgInfo.u32Height;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 创建VPSS通道组
 * 输入参数: s32GrpId --- 通道组ID
             u32MaxW --- 最大图像宽度
             u32MaxH --- 最大图像高度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_CreateGrp(sint32 s32GrpId, SV_SIZE_S *pstSize)
{
    RK_S32 s32Ret = 0, i;
    sint32 s32ViChn;
    RGA_CHN RgaChn;
    RGA_ATTR_S stRgaAttr = {0};
    sint32 s32ImgType = 0;

    for(i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {

        stRgaAttr.stImgIn.u32X          = 0;
        stRgaAttr.stImgIn.u32Y          = 0;
        stRgaAttr.stImgIn.imgType       = IMAGE_TYPE_NV12;


        if (i == MPP_VPSS_CHN_PRI || i == MPP_VPSS_CHN_VO)
            continue;

        if(i == MPP_VPSS_CHN_ALG && m_stVpssInfo.enAlgType[s32GrpId] == ALG_OFF)
            continue;

        stRgaAttr.stImgIn.u32Width = mpp_vi_get_width();
        stRgaAttr.stImgIn.u32Height = mpp_vi_get_Height();
        stRgaAttr.stImgIn.u32HorStride = mpp_vi_get_width();
        stRgaAttr.stImgIn.u32VirStride = mpp_vi_get_Height();

        if (i == MPP_VPSS_CHN_SEC || i == MPP_VPSS_CHN_JPEG || i == MPP_VPSS_CHN_ALG)
        {
            if (BOARD_ADA32N1_V2 == BOARD_GetVersion())
            {
                stRgaAttr.stImgIn.u32Width = 960;
                stRgaAttr.stImgIn.u32Height = 540;
                stRgaAttr.stImgIn.u32HorStride = 960;
                stRgaAttr.stImgIn.u32VirStride = 540;
            }
            else
            {
                stRgaAttr.stImgIn.u32Width = VI_CHN1_WIDTH;
                stRgaAttr.stImgIn.u32Height = VI_CHN1_HEIGHT;
                stRgaAttr.stImgIn.u32HorStride = VI_CHN1_WIDTH;
                stRgaAttr.stImgIn.u32VirStride = VI_CHN1_HEIGHT;
            }
        }

        RgaChn = i + s32GrpId * MPP_VPSS_CHN_BUTT;
        stRgaAttr.u16Rotaion            = 0;
        stRgaAttr.stImgOut.u32X         = 0;
        stRgaAttr.stImgOut.u32Y         = 0;

        switch(i)
        {
            case MPP_VPSS_CHN_VO:
                stRgaAttr.stImgOut.u32Width  = 1920;
                stRgaAttr.stImgOut.u32Height = 1080;
                stRgaAttr.stImgOut.u32HorStride = 1920;
                stRgaAttr.stImgOut.u32VirStride = 1080;
                stRgaAttr.stImgOut.imgType      = MPP_VPSS_TMP_FORMAT;
                stRgaAttr.bEnBufPool            = RK_TRUE;
                stRgaAttr.u16BufPoolCnt         = 3;
                break;
            case MPP_VPSS_CHN_ALG:
                if(m_stVpssInfo.enAlgType[s32GrpId] == ALG_DMS)
                {
                    stRgaAttr.stImgOut.u32Width = 1280;
                    stRgaAttr.stImgOut.u32Height = 720;
                    stRgaAttr.stImgOut.u32HorStride = 1280;
                    stRgaAttr.stImgOut.u32VirStride = 720;
                    stRgaAttr.stImgOut.imgType = IMAGE_TYPE_NV12;
                }
                else if(m_stVpssInfo.enAlgType[s32GrpId] == ALG_ADAS || m_stVpssInfo.enAlgType[s32GrpId] == ALG_PDS ||
                       m_stVpssInfo.enAlgType[s32GrpId] == ALG_APC)
                {

                    if (ARG_IsExist("algWidth"))
                    {
                        stRgaAttr.stImgOut.u32Width = ARG_GetIntValue("algWidth");
                        stRgaAttr.stImgOut.u32HorStride = ARG_GetIntValue("algWidth");
                    }
                    else if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion() || 
                        BOARD_ADA32N1_V2 == BOARD_GetVersion())
                    {
                        stRgaAttr.stImgOut.u32Width     = 544;
                        stRgaAttr.stImgOut.u32HorStride = 544;
                    }
                    else
                    {
                        stRgaAttr.stImgOut.u32Width     = 608;
                        stRgaAttr.stImgOut.u32HorStride = 608;
                    }

                    if (ARG_IsExist("algHeight"))
                    {
                        stRgaAttr.stImgOut.u32Height    = ARG_GetIntValue("algHeight");
                        stRgaAttr.stImgOut.u32VirStride = ARG_GetIntValue("algHeight");
                    }
                    else if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion() || 
                        BOARD_ADA32N1_V2 == BOARD_GetVersion())
                    {
                        stRgaAttr.stImgOut.u32Height    = 416;
                        stRgaAttr.stImgOut.u32VirStride = 416;
                    }
                    else
                    {
                        stRgaAttr.stImgOut.u32Height    = 352;
                        stRgaAttr.stImgOut.u32VirStride = 352;
                    }

                    stRgaAttr.stImgOut.imgType = IMAGE_TYPE_BGR888;
                }

                /* 解析传进来的算法图片格式类型 */
                if (ARG_IsExist("algImgType"))
                {
                    s32ImgType = ARG_GetIntValue("algImgType");
                    switch (s32ImgType)
                    {
                        case 0:
                            stRgaAttr.stImgOut.imgType = IMAGE_TYPE_BGR888;
                            break;
                        case 1:
                            stRgaAttr.stImgOut.imgType = IMAGE_TYPE_RGB888;
                            break;
                        case 2:
                            stRgaAttr.stImgOut.imgType = IMAGE_TYPE_NV12;
                            break;
                        default:
                            print_level(SV_WARN, "not support imgType option: %d\n", s32ImgType);
                            if(m_stVpssInfo.enAlgType[s32GrpId] == ALG_DMS)
                            {
                                stRgaAttr.stImgOut.imgType = IMAGE_TYPE_NV12;
                            }
                            else if(m_stVpssInfo.enAlgType[s32GrpId] == ALG_ADAS || m_stVpssInfo.enAlgType[s32GrpId] == ALG_PDS ||
                                    m_stVpssInfo.enAlgType[s32GrpId] == ALG_APC)
                            {
                                stRgaAttr.stImgOut.imgType = IMAGE_TYPE_BGR888;
                            }
                            break;
                    }
                }

                m_stVpssInfo.stVpssAlgInfo.u32Width = stRgaAttr.stImgOut.u32Width;
                m_stVpssInfo.stVpssAlgInfo.u32Height = stRgaAttr.stImgOut.u32Height;

                stRgaAttr.bEnBufPool            = RK_TRUE;
#if ALG_MUTLIT_BUFFER
                stRgaAttr.u16BufPoolCnt         = MPP_VPSS_ALG_BUF_NUM;
#else
                stRgaAttr.u16BufPoolCnt         = 1;
#endif
                break;

            default:
                stRgaAttr.stImgOut.u32Width     = pstSize[i].u32Width;
                stRgaAttr.stImgOut.u32Height    = pstSize[i].u32Height;
                stRgaAttr.stImgOut.u32HorStride = pstSize[i].u32Width;
                stRgaAttr.stImgOut.u32VirStride = pstSize[i].u32Height;
                stRgaAttr.stImgOut.imgType      = IMAGE_TYPE_NV12;
                stRgaAttr.bEnBufPool            = RK_TRUE;
                stRgaAttr.u16BufPoolCnt         = 3;
                break;
        }
        print_level(SV_INFO, "VPSS(RGA) channel created successfully, group id [%d], rga channel [%d].\n",
            i, RgaChn);
        printf_RgaAttr(stRgaAttr);

        s32Ret = RK_MPI_RGA_CreateChn(RgaChn, &stRgaAttr);
        if (s32Ret != RK_SUCCESS)
        {
            print_level(SV_ERROR, "RK_MPI_RGA_CreateChn[%d] failed! [err=%d]\n", i, s32Ret);
            return SV_FAILURE;
        }

    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 销毁VPSS通道组
 * 输入参数: s32GrpId --- 通道组ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_DestroyGrp(sint32 s32GrpId)
{
    sint32 s32ViChn;
    RK_S32 s32Ret = 0, i;
    RGA_CHN RgaChn;

    for (i = 0; i < MPP_VPSS_CHN_BUTT; i++)
    {

        if(i == MPP_VPSS_CHN_ALG && m_stVpssInfo.enAlgType[s32GrpId] == ALG_OFF)
            continue;

        s32ViChn = mpp_vpss_get_ViChn(i);
        s32Ret = mpp_sys_ViVpssChnUnBind(s32ViChn, 0, RgaChn);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssChnBind[%d] failed! [err=%d]\n", i, s32Ret);
            return SV_FAILURE;
        }

        RgaChn = i + s32GrpId * MPP_VPSS_CHN_BUTT;
        s32Ret = RK_MPI_RGA_DestroyChn(RgaChn);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "RK_MPI_RGA_DestroyChn failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 重新绑定VPSS通道
 * 输入参数: s32Chn          ---  视频源通道
             s32VpssChn  ---  VPSS通道号
             u32Size      --- 通道尺寸
             bTransfor   ---  几何变换使能
             bMirror     ---  使能镜像
             bFlip            使能翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_SetOsdAttr(sint32 s32Chn, SV_BOOL bShowChn[4])
{
    sint32 s32Ret = 0, i;
    sint32 s32ViChn, s32VpssChn, s32VpssGroup = 0;
    sint32 rchannel = 0;
    for (i = 0; i < 4; i++)
    {
        if (i == MPP_VPSS_CHN_SEC)
            continue;

        if (i == MPP_VPSS_CHN_VO)
            continue;

        if (bShowChn[i] == m_stVpssInfo.bShowChn[i])
            continue;

        s32VpssChn = i;
        s32ViChn = mpp_vpss_get_ViChn(s32VpssChn);
        s32Ret = mpp_sys_ViVpssChnUnBind(s32ViChn, s32VpssGroup, s32VpssChn);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssChnUnBind[%d][%d] failed! [err=%#x]\n", s32Chn, s32VpssChn, s32Ret);
            return SV_FAILURE;
        }

        /* 更新OSD显示属性 */
        m_stVpssInfo.bShowChn[i] = bShowChn[i];

        s32ViChn = mpp_vpss_get_ViChn(s32VpssChn);
        s32Ret = mpp_sys_ViVpssChnBind(s32ViChn, s32VpssGroup, s32VpssChn);
        if (RK_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssChnUnBind[%d][%d] failed! [err=%#x]\n", s32Chn, s32VpssChn, s32Ret);
            return SV_FAILURE;
        }
    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 重新创建Vpss通道
 * 输入参数: s32Chn          ---  视频源通道
             s32VpssChn  ---  VPSS通道号
             u32Size      --- 通道尺寸
             bTransfor   ---  几何变换使能
             bMirror     ---  使能镜像
             bFlip            使能翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_ReCreateChannel(sint32 s32Chn, sint32 s32VpssChn, SV_SIZE_S u32Size)
{
    sint32 s32Ret = 0, i;
    sint32 s32ViChn = 0, s32VpssGroup = 0;
    RGA_ATTR_S stRgaAttr = {0};
    sint32 rchannel = 0;

    if(s32Chn < 0 || s32Chn >= MEDIA_IN_CHN)
        return ERR_ILLEGAL_PARAM;

    if(s32VpssChn < 0 || s32VpssChn >= MPP_VPSS_CHN_BUTT)
        return ERR_ILLEGAL_PARAM;

#if (BOARD != BOARD_ADA32N1 && BOARD != BOARD_ADA32C4)
#if (BOARD == BOARD_ADA47V1)
    if (BOARD_IsNotCustomer(BOARD_C_ADA47V1_CZAEX))
#endif
    {
        if (s32VpssChn == MPP_VPSS_CHN_SEC)
            return SV_SUCCESS;
    }
#endif

    rchannel = s32Chn * MPP_VPSS_CHN_BUTT + s32VpssChn;
    s32Ret = RK_MPI_RGA_GetChnAttr(rchannel, &stRgaAttr);
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_RGA_GetChnAttr[%d][%d] failed! [err=%d]\n", rchannel, s32Ret);
        return SV_FAILURE;
    }

    s32ViChn = mpp_vpss_get_ViChn(s32VpssChn);
    s32VpssGroup = 0;
    print_level(SV_INFO, "ViChn:%d VpssGroup:%d rchannel:%d\n", s32ViChn, s32VpssGroup, rchannel);

#if (USING_SHAREFIFO_OPT == 0)
    s32Ret = mpp_sys_ViVpssChnUnBind(s32ViChn, s32VpssGroup, rchannel);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_ViVpssChnUnBind[%d][%d] failed! [err=%d]\n", s32Chn, s32VpssChn, s32Ret);
        return SV_FAILURE;
    }
#endif

    sleep_ms(100);
    s32Ret = RK_MPI_RGA_DestroyChn(rchannel);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_RGA_DestroyChn[%d] failed! [err=%d]\n", rchannel, s32Ret);
        return SV_FAILURE;
    }

    sleep_ms(100);


    stRgaAttr.stImgIn.u32X          = 0;
    stRgaAttr.stImgIn.u32Y          = 0;
    stRgaAttr.stImgIn.imgType       = IMAGE_TYPE_NV12;
    stRgaAttr.u16Rotaion            = 0;
    stRgaAttr.stImgOut.u32X         = 0;
    stRgaAttr.stImgOut.u32Y         = 0;
    stRgaAttr.bEnBufPool            = RK_TRUE;
    stRgaAttr.u16BufPoolCnt         = 3;
    switch(s32VpssChn)
    {
        default:
            stRgaAttr.stImgOut.imgType  = IMAGE_TYPE_NV12;
            break;
    }

    stRgaAttr.stImgOut.u32Width     = u32Size.u32Width;
    stRgaAttr.stImgOut.u32Height    = u32Size.u32Height;
    stRgaAttr.stImgOut.u32HorStride = u32Size.u32Width;
    stRgaAttr.stImgOut.u32VirStride = u32Size.u32Height;

    print_level(SV_INFO, "RK_MPI_RGA_CreateChn[%d %d]: u32Width:%d, u32Height:%d\n", s32Chn, s32VpssChn, stRgaAttr.stImgOut.u32Width, stRgaAttr.stImgOut.u32Height);

    s32Ret = RK_MPI_RGA_CreateChn(rchannel, &stRgaAttr);
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_RGA_CreateChn[%d] failed! [err=%d]\n", rchannel, s32Ret);
        return SV_FAILURE;
    }

#if (USING_SHAREFIFO_OPT == 0)
    s32Ret = mpp_sys_ViVpssChnBind(s32ViChn, s32VpssGroup, rchannel);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_ViVpssChnBind failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道画面是否镜像或反转
 * 输入参数: s32Chn         --- 视频源头通道
             s32VpssChn --- VPSS通道号
             bMirror    --- 是否水平翻转
             bFlip      --- 是否垂直翻转
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_SetChnMirrorFlip(sint32 s32Chn, SV_BOOL bMirror, SV_BOOL bFlip)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 设置通道画面顺时针旋转角度
 * 输入参数: s32Chn --- VPSS通道号
             u32Angle --- 旋转角度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_SetChnRotate(sint32 s32Chn, SV_ROT_ANGLE_E enAngle)
{
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 设置通道是否启动低延时
 * 输入参数: s32Chn --- VPSS通道号
             bLowDelay --- 是否启动低延时
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_SetChnLowDelay(sint32 s32Chn, SV_BOOL bLowDelay)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取VPSS通道数据
 * 输入参数: s32Chn     --- 视频源通道号
             s32VpssChn --- VPSS通道号 [0, MPP_VPSS_CHN_BUTT)
             ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_vpss_GetFrame(sint32 s32Chn, sint32 s32VpssChn, void **ppvBuf)
{
    sint32 s32Ret = 0;
    void *szMB;
    void *pbmp;
    MB_IMAGE_INFO_S image_info;
    sint32 rchannel = 0;

    if(ppvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }

    rchannel = s32Chn * MPP_VPSS_CHN_BUTT + s32VpssChn;
    szMB = RK_MPI_SYS_GetMediaBuffer(RK_ID_RGA, rchannel, 2000);
    if (szMB == RK_NULL)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_GetMediaBuffer [%d %d] fail\n", s32Chn, s32VpssChn);
        return SV_FAILURE;
    }

    s32Ret = RK_MPI_MB_GetImageInfo(szMB, &image_info);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_MB_GetImageInfo fail! [err=%d]\n", s32Ret);
        RK_MPI_MB_ReleaseBuffer(szMB);
        return SV_FAILURE;
    }

    pbmp = RK_MPI_MB_GetPtr(szMB);
    if(pbmp == NULL)
    {
        print_level(SV_ERROR, "RK_MPI_MB_GetPtr fail\n");
        return SV_FAILURE;
    }

    if(s32VpssChn != MPP_VPSS_CHN_ALG)
    {
        s32Ret = mpp_vosd_internal_callback(s32Chn, s32VpssChn, pbmp, image_info.u32Width, image_info.u32Height);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_WARN, "mpp_vosd_callback fail\n");
        }
    }

    if(s32VpssChn != MPP_VPSS_CHN_ALG && s32VpssChn != MPP_VPSS_CHN_VO)    //AIBOX VO通道由VMIX负责叠加
    {
        s32Ret = mpp_vosd_external_callback(s32Chn, s32VpssChn, pbmp, image_info.u32Width, image_info.u32Height);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_WARN, "mpp_vosd_callback fail\n");
        }
    }

    RK_MPI_MB_BeginCPUAccess(szMB, RK_FALSE);
    RK_MPI_MB_EndCPUAccess(szMB, RK_FALSE);


    *ppvBuf = szMB;
    return SV_SUCCESS;
}


sint32 mpp_vpss_SetFrame(sint32 s32ViPipe, sint32 s32VpssChn, void *mb)
{
    sint32 s32Ret = 0;
    sint32 rchannel;

    rchannel = s32ViPipe * MPP_VPSS_CHN_BUTT + s32VpssChn;

    s32Ret = RK_MPI_SYS_SendMediaBuffer(RK_ID_RGA, rchannel, mb);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "RK_MPI_SYS_SendMediaBuffer[%d %d %d] fail\n", s32ViPipe, s32VpssChn, rchannel);
        return s32Ret;
    }

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 释放VPSS通道数据
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_VPSS_CHN_BUTT)
             ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
 sint32 mpp_vpss_ReleaseFrame(void **ppvBuf)
{
    sint32 s32Ret = 0;
    void *szMB;
    if(ppvBuf == NULL)
    {
        return SV_SUCCESS;
    }

    if(*ppvBuf == NULL)
    {
        return SV_SUCCESS;
    }

    s32Ret = RK_MPI_MB_ReleaseBuffer(*ppvBuf);
    if(s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_MB_ReleaseBuffer failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    *ppvBuf = NULL;
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 获取通道一张灰阶图
 * 输入参数: s32Chn --- VPSS通道号
 * 输出参数: ppvBuf --- 数据缓存指针
             pu32Width --- 画面宽度
             pu32Height --- 画面高度
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 读取完数据后需要调用 mpp_vpss_ReleaseChnGrayFrame 释放数据
 *****************************************************************************/
static void *m_pvAddr = NULL;
sint32 mpp_vpss_GetChnGrayFrame(sint32 s32Chn, sint32 s32VpssChn, void **ppvBuf, uint32 *pu32Width, uint32 *pu32Height)
{
    sint32 s32Ret = 0;
    sint32 s32GetFrameMilliSec = 2000;
    RGA_ATTR_S stChnAttr;
    sint32 rchannel = 0;

    if (s32VpssChn >= 2)
    {
        return ERR_ILLEGAL_PARAM;
    }

    rchannel = s32Chn * MPP_CHN_BUTT; + s32VpssChn;
    s32Ret = RK_MPI_RGA_GetChnAttr(rchannel, &stChnAttr);
    if (s32Ret != RK_SUCCESS)
    {
        print_level(SV_ERROR, "RK_MPI_RGA_GetChnAttr failed!\n");
        return SV_FAILURE;
    }

    stChnAttr.bEnBufPool = RK_TRUE;
    stChnAttr.u16BufPoolCnt = 2;

    stRgaMb[s32Chn] = RK_MPI_SYS_GetMediaBuffer(RK_ID_RGA, rchannel, -1);
    if (s32Ret != RK_NULL)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_GetMediaBuffer failed. [err=%#x]\n", stRgaMb);
        return SV_FAILURE;
    }

    MB_IMAGE_INFO_S stImageInfo = {0};
    s32Ret = RK_MPI_MB_GetImageInfo(stRgaMb[s32Chn], &stImageInfo);
    if (s32Ret != RK_NULL)
    {
        print_level(SV_ERROR, "RK_MPI_MB_GetImageInfo failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    *ppvBuf = RK_MPI_MB_GetPtr(stRgaMb[s32Chn]);
    *pu32Width = stImageInfo.u32Width;
    *pu32Height = stImageInfo.u32Height;
    print_level(SV_INFO, "pvBuf:0x%x, width:%d, height:%d\n", *ppvBuf, *pu32Width, *pu32Height);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 释放灰阶图数据
 * 输入参数: s32Chn --- VPSS通道号
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vpss_ReleaseChnGrayFrame(sint32 s32Chn, sint32 s32VpssChn)
{
    sint32 s32Ret = 0;

    if (s32VpssChn >= 2)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (stRgaMb[s32Chn] == RK_NULL)
    {
        return ERR_BUF_EMPTY;
    }

    s32Ret = RK_MPI_MB_ReleaseBuffer(stRgaMb[s32Chn]);
    if (RK_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_MB_ReleaseBuffer failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    stRgaMb[s32Chn] = RK_NULL;
    return SV_SUCCESS;
}

static sint32 mpp_vpss_CreatCompoundVB(MPP_VPSS_DUMP_MEMBUF_S *pstVpssMem)
{
    return SV_SUCCESS;
}

static sint32 mpp_vpss_ReleaseCompoundVB(void)
{
    return SV_SUCCESS;
}


static void *mpp_vpss_GetSDFrameBody(void *pvArg)
{
    return NULL;
}

sint32 mpp_vpss_GetSDFrame_Start(void)
{
    return SV_SUCCESS;
}

sint32 mpp_vpss_GetSDFrame_Stop(void)
{
    return SV_SUCCESS;
}


sint32 mpp_vpss_RenewGrp(sint32 s32GrpId, VIDEO_MODE_EE newVideoMode, uint32 u32NewW, uint32 u32NewH)
{
    return SV_SUCCESS;
}

void mpp_vpss_SetPriVencSize(SV_SIZE_S stPriVencSize)
{
    stVpssDEI.stPriVencSize = stPriVencSize;
}



