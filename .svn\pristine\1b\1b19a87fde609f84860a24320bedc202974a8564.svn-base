/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名: onvifHandle.c

作者: 许家铭    版本: v1.0.0(初始版本号)    日期: 2017-11-08

文件功能描述: ONVIF服务器端消息处理定义

版本: v1.0.0(最新版本号)

历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#include <sys/time.h>
#include <errno.h>

#include "soapStub.h"
#include "common.h"
#include "print.h"
#include "../../../include/board.h"
#include "op.h"
#include "msg.h"
#include "config.h"
#include "safefunc.h"
#include "librtsp.h"
#include "libonvif.h"
#include "logFile.h"
#include "wifi.h"

extern ONVIF_CFG_PARAM_S m_stOnvifCfg;

/** Web service operation 'SOAP_ENV__Fault' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 SOAP_ENV__Fault(struct soap *soap, char *faultcode, char *faultstring, char *faultactor, struct SOAP_ENV__Detail *detail, struct SOAP_ENV__Code *SOAP_ENV__Code, struct SOAP_ENV__Reason *SOAP_ENV__Reason, char *SOAP_ENV__Node, char *SOAP_ENV__Role, struct SOAP_ENV__Detail *SOAP_ENV__Detail)
{
    print_level(SV_DEBUG, " \n");
    return SOAP_OK;
}

/** Web service operation '__dn__Hello' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __dn__Hello(struct soap *soap, struct d__HelloType *d__Hello, struct d__ResolveType *dn__HelloResponse)
{
    print_level(SV_DEBUG, " Hello!\n");
    return SOAP_OK;
}

/** Web service operation '__dn__Bye' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __dn__Bye(struct soap *soap, struct d__ByeType *d__Bye, struct d__ResolveType *dn__ByeResponse)
{
    print_level(SV_DEBUG, " Bye!\n");
    return SOAP_OK;
}

/** Web service operation '__dn__Probe' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __dn__Probe(struct soap *soap, struct d__ProbeType *d__Probe, struct d__ProbeMatchesType *d__ProbeMatches)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_DEV_INFO stDevInfo = {0};
    static MSG_NETWORK_CFG stNetworkCfg = {0};
    static MSG_VIDEO_CFG stVideoCfg = {0};
    static struct d__ProbeMatchType ProbeMatch = {0};
    static struct d__ScopesType Scopes = {0};
    static struct wsa5__RelatesToType wsa5__RelatesTo = {0};
    static char szAddres[128];
    static char szScopes[512];
    static char szXAddrs[128];
    static char szChnName[128];

    print_level(SV_DEBUG, "Types: %s\n", d__Probe->Types);
    if (0 == strcmp(d__Probe->Types, "tds:Device") || 0 == strcmp(d__Probe->Types, "dn:NetworkVideoTransmitter"))
    {
        stRetPkt.pu8Data = (uint8 *)&stDevInfo;
        s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_DEVINFO, NULL, &stRetPkt, sizeof(MSG_DEV_INFO));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_GET_DEVINFO failed.\n");
            return SOAP_SVR_FAULT;
        }

        stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
        s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
            return SOAP_SVR_FAULT;
        }

        stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
        s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
            return SOAP_SVR_FAULT;
        }

        sprintf(szAddres, "uuid:%s", stDevInfo.szUuid);
        d__ProbeMatches->__sizeProbeMatch = 1;
        d__ProbeMatches->ProbeMatch = &ProbeMatch;
        ProbeMatch.wsa__EndpointReference.Address = szAddres;
        ProbeMatch.wsa__EndpointReference.__anyAttribute = "Attribute";
        ProbeMatch.Types = "dn:NetworkVideoTransmitter tds:Device";
        ProbeMatch.Scopes = &Scopes;
        ProbeMatch.Scopes->__item = szScopes;
        strcpy(szScopes, " onvif://www.onvif.org/type/Network_Video_Transmitter");
        strcat(szScopes, " onvif://www.onvif.org/Profile/Streaming");
        strcat(szScopes, " onvif://www.onvif.org/Profile/Q/Operational");
    #if (defined(BOARD_IPCR20S3))
        strcat(szScopes, " onvif://www.onvif.org/Profile/S");
    #endif
        strcat(szScopes, " onvif://www.onvif.org/hardware/");
        strcat(szScopes, stDevInfo.szHardware);
        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
        {
            sprintf(szChnName, " onvif://www.onvif.org/name/%s", stVideoCfg.astChnParam[0].szChnName);
            strcat(szScopes, szChnName);
            strcat(szScopes, " onvif://www.onvif.org/location/Not set");
        }
        else
        {
            strcat(szScopes, " onvif://www.onvif.org/name/IPCAM");
            strcat(szScopes, " onvif://www.onvif.org/location/country/China");
        }
        sprintf(szXAddrs, "http://%s/onvif/device_service", stNetworkCfg.szRealIpAddr);
        ProbeMatch.XAddrs = szXAddrs;
        ProbeMatch.MetadataVersion = 1;
        soap->header->wsa5__RelatesTo = &wsa5__RelatesTo;
        soap->header->wsa5__RelatesTo->__item = soap->header->wsa5__MessageID;
        soap->header->wsa5__RelatesTo->__anyAttribute = NULL;
        soap->header->wsa5__RelatesTo->RelationshipType = NULL;
        soap->header->wsa5__Action = "http://schemas.xmlsoap.org/ws/2005/04/discovery/ProbeMatches";
    }

    return SOAP_OK;
}

/** Web service operation '__tds__GetServices' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetServices(struct soap *soap, struct _tds__GetServices *tds__GetServices, struct _tds__GetServicesResponse *tds__GetServicesResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tds__Service astService[4];
    static struct tt__OnvifVersion astVersion[4];
    static char szServiceUri[128];
    static char szServiceNs[128];
    static char szMediaUri[128];
    static char szMediaNs[128];
    static char szImageUri[128];
    static char szImageNs[128];
    static char szPtzUri[128];
    static char szPtzNs[128];

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "IncludeCapability: %d\n", tds__GetServices->IncludeCapability);
    sprintf(szServiceUri, "http://%s/onvif/device_service", stNetworkCfg.szRealIpAddr);
    sprintf(szMediaUri, "http://%s/onvif/media_service", stNetworkCfg.szRealIpAddr);
    sprintf(szImageUri, "http://%s/onvif/image_service", stNetworkCfg.szRealIpAddr);
    sprintf(szPtzUri, "http://%s/onvif/ptz_service", stNetworkCfg.szRealIpAddr);
    strcpy(szServiceNs, SOAP_NAMESPACE_OF_tds);
    strcpy(szMediaNs, SOAP_NAMESPACE_OF_trt);
    strcpy(szImageNs, SOAP_NAMESPACE_OF_timg);
    strcpy(szPtzNs, SOAP_NAMESPACE_OF_tptz);
    tds__GetServicesResponse->__sizeService = (BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() == BOARD_HDW845V1_V1) ? 4 : 3;
    tds__GetServicesResponse->Service = astService;
    astService[0].Namespace = szServiceNs;
    astService[0].XAddr = szServiceUri;
    astService[0].Version = &astVersion[0];
    astService[0].Version->Major = 1;
    astService[0].Version->Minor = 10;
    astService[1].Namespace = szMediaNs;
    astService[1].XAddr = szMediaUri;
    astService[1].Version = &astVersion[1];
    astService[1].Version->Major = 1;
    astService[1].Version->Minor = 10;
    astService[2].Namespace = szImageNs;
    astService[2].XAddr = szImageUri;
    astService[2].Version = &astVersion[2];
    astService[2].Version->Major = 1;
    astService[2].Version->Minor = 10;
    astService[3].Namespace = szPtzNs;
    astService[3].XAddr = szPtzUri;
    astService[3].Version = &astVersion[3];
    astService[3].Version->Major = 1;
    astService[3].Version->Minor = 10;

    return SOAP_OK;
}

/** Web service operation '__tds__GetServiceCapabilities' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetServiceCapabilities(struct soap *soap, struct _tds__GetServiceCapabilities *tds__GetServiceCapabilities, struct _tds__GetServiceCapabilitiesResponse *tds__GetServiceCapabilitiesResponse)
{
    static struct tds__DeviceServiceCapabilities stCapabilities = {0};
    static struct tds__SystemCapabilities stSystem = {0};
    static enum xsd__boolean_ enDiscoveryResolve = _true;
    static enum xsd__boolean_ enDiscoveryBye = _true;
    static enum xsd__boolean_ enFirmwareUpgrade = _true;
    static enum xsd__boolean_ enHttpFirmwareUpgrade = _true;
    static enum xsd__boolean_ enSystemBackup = _true;
    static enum xsd__boolean_ enHttpSystemBackup = _true;

    print_level(SV_DEBUG, "dummy:  \n");
    tds__GetServiceCapabilitiesResponse->Capabilities = &stCapabilities;
    stCapabilities.System = &stSystem;
	stSystem.DiscoveryResolve = &enDiscoveryResolve;
    stSystem.DiscoveryBye = &enDiscoveryBye;
    stSystem.SystemBackup = &enSystemBackup;
    stSystem.HttpSystemBackup = &enHttpSystemBackup;
    stSystem.FirmwareUpgrade = &enFirmwareUpgrade;
    stSystem.HttpFirmwareUpgrade = &enHttpFirmwareUpgrade;

    return SOAP_OK;
}

/** Web service operation '__tds__GetDeviceInformation' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetDeviceInformation(struct soap *soap, struct _tds__GetDeviceInformation *tds__GetDeviceInformation, struct _tds__GetDeviceInformationResponse *tds__GetDeviceInformationResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_DEV_INFO stDevInfo = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stDevInfo;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_DEVINFO, NULL, &stRetPkt, sizeof(MSG_DEV_INFO));
    if (SV_SUCCESS == s32Ret)
    {
        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
        {
            tds__GetDeviceInformationResponse->Manufacturer = "r2p Group";
            tds__GetDeviceInformationResponse->Model = "PTS-CAM-IP-REAR-001";
            tds__GetDeviceInformationResponse->FirmwareVersion = stDevInfo.szVersion;
            tds__GetDeviceInformationResponse->SerialNumber = stDevInfo.szSerial;
            tds__GetDeviceInformationResponse->HardwareId = "1.0";
        }
        else
        {
            tds__GetDeviceInformationResponse->Manufacturer = "General";
            tds__GetDeviceInformationResponse->Model = stDevInfo.szHardware;
            tds__GetDeviceInformationResponse->FirmwareVersion = stDevInfo.szVersion;
            tds__GetDeviceInformationResponse->SerialNumber = stDevInfo.szSerial;
            tds__GetDeviceInformationResponse->HardwareId = "1.0";
        }
    }
    else
    {
        tds__GetDeviceInformationResponse->Manufacturer = "General";
        tds__GetDeviceInformationResponse->Model = "IPC-General";
        tds__GetDeviceInformationResponse->FirmwareVersion = "v1.0.0";
        tds__GetDeviceInformationResponse->SerialNumber = "IPC20180001";
        tds__GetDeviceInformationResponse->HardwareId = "1.0";
    }

    return SOAP_OK;
}

/** Web service operation '__tds__SetSystemDateAndTime' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetSystemDateAndTime(struct soap *soap, struct _tds__SetSystemDateAndTime *tds__SetSystemDateAndTime, struct _tds__SetSystemDateAndTimeResponse *tds__SetSystemDateAndTimeResponse)
{
    sint32 s32Ret = 0, i;
    struct tm tmTime = {0}, *ptm;
    time_t tTime;
    struct timeval tvTime;
    struct timezone tz;
    CFG_SYS_PARAM stSysParam = {0};
    //MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    //MSG_SYS_CFG stSystemCfg = {0};
    _wsse__UsernameToken *pstUsernameToken = soap->header->wsse__Security->UsernameToken;
    char szCmd[64];

#if 0
    struct tagTimeZoneStr
    {
        char   *pszTzName;  /* 时区名 */
        sint32  s32Minutes; /* 和Greenwich时间差了多少分钟 */
    } astTimeZoneList[] = {{"GMT0", 0}, {"CET-1", 60}, {"EET-2", 120}, {"AST-3", 180}, {"IRT-3:30", 210}, {"GMT-4", 240}, {"AFT-4:30", 270}, {"PKT-5", 300}, {"IST-5:30", 330}, \
                        {"NPT-5:45", 345}, {"BDT-6", 360}, {"MMT-6:30", 390}, {"ICT-7", 420}, {"CST-8", 480}, {"JST-9", 540}, {"CST-9:30", 570}, {"EST-10", 600}, {"SBT-11", 660}, \
                        {"NZST-12", 720}, {"TOT-13", 780}, {"CVT1", -60}, {"GST2", -120}, {"BRT3", -180}, {"AST4", -240}, {"VET4:30", -270}, {"EST5", -300}, {"CST6", -360}, {"MST7", -420}, \
                        {"PST8", -480}, {"AKST9", -540}, {"HAST10", -600}, {"SST11", -660}};
#endif

    struct tagTimeZoneStr
    {
        char   *pszTzName;  /* 时区名 */
        sint32  s32Minutes; /* 和Greenwich时间差了多少分钟 */
    } astTimeZoneList[] = {{"UTC-12", -720}, {"UTC-11", -660}, {"UTC-10", -600}, {"UTC-9:30", -570}, {"UTC-9", -540}, {"UTC-8", -480}, {"UTC-7", -420}, {"UTC-6", -360}, {"UTC-5", -300}, \
                        {"UTC-4", -240}, {"UTC-3:30", -210}, {"UTC-3", -180}, {"UTC-2", -120}, {"UTC-1", -60}, {"UTC", 0}, {"UTC+1", 60}, {"UTC+2", 120}, {"UTC+3", 180}, \
                        {"UTC+3:30", 210}, {"UTC+4", 240}, {"UTC+4:30", 270}, {"UTC+5", 300}, {"UTC+5:30", 330}, {"UTC+5:45", 345}, {"UTC+6", 360}, {"UTC+6:30", 390}, {"UTC+7", 420}, {"UTC+8", 480}, \
                        {"UTC+9", 540}, {"UTC+9:30", 570}, {"UTC+10", 600}, {"UTC+10:30", 630}, {"UTC+11", 660}, {"UTC+12", 720}, {"UTC+12:45", 765}, {"UTC+13", 780}, {"UTC+14", 840}, \
                        {"GMT0", 0}, {"CET-1", 60}, {"EET-2", 120}, {"AST-3", 180}, {"IRT-3:30", 210}, {"GMT-4", 240}, {"AFT-4:30", 270}, {"PKT-5", 300}, {"IST-5:30", 330}, \
                        {"NPT-5:45", 345}, {"BDT-6", 360}, {"MMT-6:30", 390}, {"ICT-7", 420}, {"CST-8", 480}, {"JST-9", 540}, {"CST-9:30", 570}, {"EST-10", 600}, {"SBT-11", 660}, \
                        {"NZST-12", 720}, {"TOT-13", 780}, {"CVT1", -60}, {"GST2", -120}, {"BRT3", -180}, {"AST4", -240}, {"VET4:30", -270}, {"EST5", -300}, {"CST6", -360}, {"MST7", -420}, \
                        {"PST8", -480}, {"AKST9", -540}, {"HAST10", -600}, {"SST11", -660}};

    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    print_level(SV_DEBUG, "DateTimeType: %d, DaylightSavings: %d\n", tds__SetSystemDateAndTime->DateTimeType, tds__SetSystemDateAndTime->DaylightSavings);
    tz.tz_dsttime = (_true == tds__SetSystemDateAndTime->DaylightSavings) ? 1 : 0;
/*
    if (NULL != tds__SetSystemDateAndTime->TimeZone)
    {
        print_level(SV_INFO, "TimeZone: %s\n", tds__SetSystemDateAndTime->TimeZone->TZ);

        for (i = 0; i < sizeof(astTimeZoneList)/sizeof(struct tagTimeZoneStr); i++)
        {
            if (0 == strcmp(tds__SetSystemDateAndTime->TimeZone->TZ, astTimeZoneList[i].pszTzName))
            {
                tz.tz_minuteswest = astTimeZoneList[i].s32Minutes;
                break;
            }
        }
        if (i >= sizeof(astTimeZoneList)/sizeof(struct tagTimeZoneStr))
        {
            print_level(SV_INFO, "Invalid TimeZone! Use Existing TimeZone\n");
            tz.tz_minuteswest = stSysParam.s32UTChour*60 + stSysParam.s32UTCminute;
        }
    }
    else
    {
        print_level(SV_INFO, "Use Existing TimeZone\n");
        tz.tz_minuteswest = stSysParam.s32UTChour*60 + stSysParam.s32UTCminute;
    }
*/

    s32Ret = CONFIG_GetSystemParam(&stSysParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetSystemParam failed. [err=%#x]\n", s32Ret);
        return SOAP_SVR_FAULT;
    }

    // 手动设置时间，请求必须包含时间类型、UTC时间以及时区
    if (tt__SetDateTimeType__Manual == tds__SetSystemDateAndTime->DateTimeType && \
        NULL != tds__SetSystemDateAndTime->UTCDateTime && \
        NULL != tds__SetSystemDateAndTime->TimeZone)
    {
        print_level(SV_INFO, "TimeZone: %s\n", tds__SetSystemDateAndTime->TimeZone->TZ);

        for (i = 0; i < sizeof(astTimeZoneList)/sizeof(struct tagTimeZoneStr); i++)
        {
            if (0 == strcmp(tds__SetSystemDateAndTime->TimeZone->TZ, astTimeZoneList[i].pszTzName))
            {
                tz.tz_minuteswest = astTimeZoneList[i].s32Minutes;
                break;
            }
        }
        if (i >= sizeof(astTimeZoneList)/sizeof(struct tagTimeZoneStr))
        {
            print_level(SV_INFO, "Invalid TimeZone! Use Existing TimeZone\n");
            stSysParam.s32UTCminute = (stSysParam.s32UTChour > 0) ? stSysParam.s32UTCminute : -stSysParam.s32UTCminute;
            tz.tz_minuteswest = stSysParam.s32UTChour*60 + stSysParam.s32UTCminute;
        }

        if (NULL != tds__SetSystemDateAndTime->UTCDateTime->Date)
        {
            print_level(SV_INFO, "settime-> %04d-%02d-%02d\n", tds__SetSystemDateAndTime->UTCDateTime->Date->Year, tds__SetSystemDateAndTime->UTCDateTime->Date->Month, tds__SetSystemDateAndTime->UTCDateTime->Date->Day);
            tmTime.tm_year = tds__SetSystemDateAndTime->UTCDateTime->Date->Year - 1900;
            tmTime.tm_mon = tds__SetSystemDateAndTime->UTCDateTime->Date->Month - 1;
            tmTime.tm_mday = tds__SetSystemDateAndTime->UTCDateTime->Date->Day;
        }
        if (NULL != tds__SetSystemDateAndTime->UTCDateTime->Time)
        {
            print_level(SV_INFO, "settime-> %02d:%02d:%02d\n", tds__SetSystemDateAndTime->UTCDateTime->Time->Hour, tds__SetSystemDateAndTime->UTCDateTime->Time->Minute, tds__SetSystemDateAndTime->UTCDateTime->Time->Second);
            tmTime.tm_hour = tds__SetSystemDateAndTime->UTCDateTime->Time->Hour;
            tmTime.tm_min = tds__SetSystemDateAndTime->UTCDateTime->Time->Minute;
            tmTime.tm_sec = tds__SetSystemDateAndTime->UTCDateTime->Time->Second;
        }

        tTime = mktime(&tmTime);
        tvTime.tv_sec = tTime;//计算UTC时间
    	tvTime.tv_usec = 0;
    	print_level(SV_INFO, "tv_sec:%d, tz_minuteswest:%d\n", tvTime.tv_sec, tz.tz_minuteswest);

        if (_true == tds__SetSystemDateAndTime->DaylightSavings)
        {
            tz.tz_minuteswest += 60;    //DST
        }
    	if (settimeofday(&tvTime, &tz) < 0)
    	{
    	    print_level(SV_ERROR, "settimeofday failed.\n");
            return SOAP_SVR_FAULT;
    	}
    }
    else if (tt__SetDateTimeType__NTP == tds__SetSystemDateAndTime->DateTimeType) // NTP自动获取
    {
        sprintf(szCmd, "ntpdate %s", stSysParam.pszNtpServer);
        s32Ret = SAFE_System(szCmd, 30000);
        if(0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            SAFE_System(szCmd, 30000);
        }

        if (gettimeofday(&tvTime, &tz) < 0)//UTC时间
        {
            print_level(SV_ERROR, "gettimeofday failed.\n");
            return SOAP_SVR_FAULT;
        }

        if (NULL != tds__SetSystemDateAndTime->TimeZone)
        {
            print_level(SV_INFO, "TimeZone: %s\n", tds__SetSystemDateAndTime->TimeZone->TZ);

            for (i = 0; i < sizeof(astTimeZoneList)/sizeof(struct tagTimeZoneStr); i++)
            {
                if (0 == strcmp(tds__SetSystemDateAndTime->TimeZone->TZ, astTimeZoneList[i].pszTzName))
                {
                    tz.tz_minuteswest = astTimeZoneList[i].s32Minutes;
                    break;
                }
            }
            if (i >= sizeof(astTimeZoneList)/sizeof(struct tagTimeZoneStr))
            {
                print_level(SV_INFO, "Invalid TimeZone! Use Existing TimeZone\n");
                stSysParam.s32UTCminute = (stSysParam.s32UTChour > 0) ? stSysParam.s32UTCminute : -stSysParam.s32UTCminute;
                tz.tz_minuteswest = stSysParam.s32UTChour*60 + stSysParam.s32UTCminute;
            }
        }
        else
        {
            print_level(SV_INFO, "Use Existing TimeZone\n");
            stSysParam.s32UTCminute = (stSysParam.s32UTChour > 0) ? stSysParam.s32UTCminute : -stSysParam.s32UTCminute;
            tz.tz_minuteswest = stSysParam.s32UTChour*60 + stSysParam.s32UTCminute;
        }

        print_level(SV_INFO, "tv_sec:%d, tz_minuteswest:%d\n",tvTime.tv_sec, tz.tz_minuteswest);

        if (_true == tds__SetSystemDateAndTime->DaylightSavings)
        {
            tz.tz_minuteswest += 60;    //DST
        }
        if (settimeofday(&tvTime, &tz) < 0)//UTC时间
        {
            print_level(SV_ERROR, "settimeofday failed.\n");
            return SOAP_SVR_FAULT;
        }
    }
    else
    {
        print_level(SV_ERROR, "Invalid Request.\n");
        return SOAP_FAULT;
    }

    stSysParam.bNtpEnable = (tt__SetDateTimeType__NTP == tds__SetSystemDateAndTime->DateTimeType) ? SV_TRUE : SV_FALSE;
    stSysParam.bDaylightTime = (_true == tds__SetSystemDateAndTime->DaylightSavings) ? SV_TRUE : SV_FALSE;
    if (_true == tds__SetSystemDateAndTime->DaylightSavings)
    {
        tz.tz_minuteswest -= 60;    // 写配置文件前去掉前面设置时间的夏令时处理
    }
    stSysParam.s32UTChour = tz.tz_minuteswest / 60;
    stSysParam.s32UTCminute = abs(tz.tz_minuteswest % 60);
    s32Ret = CONFIG_SetSystemParam(&stSysParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_SetUserParam failed. [err=%#x]\n", s32Ret);
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__tds__GetSystemDateAndTime' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetSystemDateAndTime(struct soap *soap, struct _tds__GetSystemDateAndTime *tds__GetSystemDateAndTime, struct _tds__GetSystemDateAndTimeResponse *tds__GetSystemDateAndTimeResponse)
{
    sint32 s32Ret = 0, i;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_SYS_CFG stSystemCfg = {0};
    struct timeval tvTime;
    struct timezone tz;
    struct tm stTime = {0};
    static struct tt__SystemDateTime stSystemDateAndTime = {0};
    static struct tt__TimeZone stTimeZone = {0};
    static struct tt__DateTime stUTCDateTime = {0};
    static struct tt__DateTime stLocalDateTime = {0};
    static struct tt__Time astTime[2];
    static struct tt__Date astDate[2];
#if 0
    struct tagTimeZoneStr
    {
        char   *pszTzName;  /* 时区名 */
        sint32  s32Minutes; /* 和Greenwich时间差了多少分钟 */
    } astTimeZoneList[] = {{"GMT0", 0}, {"CET-1", 60}, {"EET-2", 120}, {"AST-3", 180}, {"IRT-3:30", 210}, {"GMT-4", 240}, {"AFT-4:30", 270}, {"PKT-5", 300}, {"IST-5:30", 330}, \
                        {"NPT-5:45", 345}, {"BDT-6", 360}, {"MMT-6:30", 390}, {"ICT-7", 420}, {"CST-8", 480}, {"JST-9", 540}, {"CST-9:30", 570}, {"EST-10", 600}, {"SBT-11", 660}, \
                        {"NZST-12", 720}, {"TOT-13", 780}, {"CVT1", -60}, {"GST2", -120}, {"BRT3", -180}, {"AST4", -240}, {"VET4:30", -270}, {"EST5", -300}, {"CST6", -360}, {"MST7", -420}, \
                        {"PST8", -480}, {"AKST9", -540}, {"HAST10", -600}, {"SST11", -660}};
#endif

    struct tagTimeZoneStr
    {
        char   *pszTzName;  /* 时区名 */
        sint32  s32Minutes; /* 和Greenwich时间差了多少分钟 */
    } astTimeZoneList[] = {{"UTC-12", -720}, {"UTC-11", -660}, {"UTC-10", -600}, {"UTC-9:30", -570}, {"UTC-9", -540}, {"UTC-8", -480}, {"UTC-7", -420}, {"UTC-6", -360}, {"UTC-5", -300}, \
                        {"UTC-4", -240}, {"UTC-3:30", -210}, {"UTC-3", -180}, {"UTC-2", -120}, {"UTC-1", -60}, {"UTC", 0}, {"UTC+1", 60}, {"UTC+2", 120}, {"UTC+3", 180}, \
                        {"UTC+3:30", 210}, {"UTC+4", 240}, {"UTC+4:30", 270}, {"UTC+5", 300}, {"UTC+5:30", 330}, {"UTC+5:45", 345}, {"UTC+6", 360}, {"UTC+6:30", 390}, {"UTC+7", 420}, {"UTC+8", 480}, \
                        {"UTC+9", 540}, {"UTC+9:30", 570}, {"UTC+10", 600}, {"UTC+10:30", 630}, {"UTC+11", 660}, {"UTC+12", 720}, {"UTC+12:45", 765}, {"UTC+13", 780}, {"UTC+14", 840}};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stSystemCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_SYS_CFG, NULL, &stRetPkt, sizeof(MSG_SYS_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_SYS_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (gettimeofday(&tvTime, &tz) < 0)
	{
	    print_level(SV_ERROR, "gettimeofday failed.\n");
	}

    tds__GetSystemDateAndTimeResponse->SystemDateAndTime = &stSystemDateAndTime;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->DateTimeType = stSystemCfg.bNtpEnable ? tt__SetDateTimeType__NTP : tt__SetDateTimeType__Manual;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->DaylightSavings = stSystemCfg.bDaylightTime;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->TimeZone = &stTimeZone;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime = &stUTCDateTime;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime = &stLocalDateTime;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->TimeZone->TZ = "UTC";

    if (SV_TRUE == stSystemCfg.bDaylightTime)
    {
        for (i = 0; i < sizeof(astTimeZoneList)/8; i++)
        {
            if (tz.tz_minuteswest == astTimeZoneList[i].s32Minutes + 60)
            {
                tds__GetSystemDateAndTimeResponse->SystemDateAndTime->TimeZone->TZ = astTimeZoneList[i].pszTzName;
                break;
            }
        }
    }
    else
    {
        for (i = 0; i < sizeof(astTimeZoneList)/8; i++)
        {
            if (tz.tz_minuteswest == astTimeZoneList[i].s32Minutes)
            {
                tds__GetSystemDateAndTimeResponse->SystemDateAndTime->TimeZone->TZ = astTimeZoneList[i].pszTzName;
                break;
            }
        }
    }


    gmtime_r((time_t *)&tvTime.tv_sec, &stTime);
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Time = &astTime[0];
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Date = &astDate[0];
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Time->Hour = stTime.tm_hour;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Time->Minute = stTime.tm_min;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Time->Second = stTime.tm_sec;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Date->Year = stTime.tm_year+1900;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Date->Month = stTime.tm_mon+1;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->UTCDateTime->Date->Day = stTime.tm_mday;
    tvTime.tv_sec += tz.tz_minuteswest * 60;
    gmtime_r((time_t *)&tvTime.tv_sec, &stTime);
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Time = &astTime[1];
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Date = &astDate[1];
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Time->Hour = stTime.tm_hour;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Time->Minute = stTime.tm_min;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Time->Second = stTime.tm_sec;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Date->Year = stTime.tm_year+1900;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Date->Month = stTime.tm_mon+1;
    tds__GetSystemDateAndTimeResponse->SystemDateAndTime->LocalDateTime->Date->Day = stTime.tm_mday;

    return SOAP_OK;
}

/** Web service operation '__tds__SetSystemFactoryDefault' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetSystemFactoryDefault(struct soap *soap, struct _tds__SetSystemFactoryDefault *tds__SetSystemFactoryDefault, struct _tds__SetSystemFactoryDefaultResponse *tds__SetSystemFactoryDefaultResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    stMsgPkt.stMsg.s32Param = (tds__SetSystemFactoryDefault->FactoryDefault == tt__FactoryDefaultType__Soft) ? 0 : 1;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_RESET_FACTORY, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_RESET_FACTORY failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__tds__UpgradeSystemFirmware' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__UpgradeSystemFirmware(struct soap *soap, struct _tds__UpgradeSystemFirmware *tds__UpgradeSystemFirmware, struct _tds__UpgradeSystemFirmwareResponse *tds__UpgradeSystemFirmwareResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__SystemReboot' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SystemReboot(struct soap *soap, struct _tds__SystemReboot *tds__SystemReboot, struct _tds__SystemRebootResponse *tds__SystemRebootResponse)
{
    sint32 s32Ret = 0;

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_REBOOT, NULL, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_execRequestBlock OP_REQ_REBOOT failed. [err=%#x]\n", s32Ret);
    }

    tds__SystemRebootResponse->Message = "now to reboot system...";

    return SOAP_OK;
}

/** Web service operation '__tds__RestoreSystem' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__RestoreSystem(struct soap *soap, struct _tds__RestoreSystem *tds__RestoreSystem, struct _tds__RestoreSystemResponse *tds__RestoreSystemResponse)
{
	print_level(SV_DEBUG, "dummy:  \n");

	print_level(SV_DEBUG, "%d,%s,%s\n",tds__RestoreSystem->__sizeBackupFiles,
	tds__RestoreSystem->BackupFiles->Name,
	tds__RestoreSystem->BackupFiles->Data->xop__Include->href);

	if(soap->mime.list)
	{
		print_level(SV_DEBUG, "dummy:  recvLen=%d\n",soap->mime.list->size);
	}

	//将接收到的mime数据写入文件
	g_Log_nOffset = 0;
	strcpy(g_Log_FileName,"/var/BackupFile");
	writeLogFile(g_Log_FileName,soap->mime.list->ptr,soap->mime.list->size,&g_Log_nOffset);

    return SOAP_OK;
}

/** Web service operation '__tds__GetSystemBackup' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetSystemBackup(struct soap *soap, struct _tds__GetSystemBackup *tds__GetSystemBackup, struct _tds__GetSystemBackupResponse *tds__GetSystemBackupResponse)
{
	static struct soap_multipart stMultiM = {0};
	static struct tt__BackupFile stBak = {0};
	static struct tt__AttachmentData stAtta = {0};
	static struct xop__Include stXop_Include = {0};
	static char id[128] = "<http://tempuri.org/0/systembackup>";
	static char start[128] = "<http://tempuri.org/0>";
	static char type[256] = "application/octet-stream";
	static char contentType[256] = "application/soap+xml";
	static char boundary[256] = "uuid:b00ee99d-8ff7-4180-b033-00e765faf524+id=101";
	static char *pBuf;
	static int len = 0;
	int nFileLen = 0;
	int nReadBytes = 0;
	int nOffset = 0;

	print_level(SV_DEBUG, "%d,%s\n",soap->mode,soap->mime.boundary);
	print_level(SV_DEBUG, "dummy:  \n");

	//从设备中读取备份文件，默认最大5M
	strcpy(g_Log_FileName,"/var/BackupFile");
	nFileLen = GetFileLen(g_Log_FileName);
	if(nFileLen > 0)
	{
		pBuf = (char*)malloc(nFileLen);
		if(pBuf != NULL)
		{
			memset(pBuf,0,nFileLen);
			g_Log_nOffset = 0;
			nOffset = 0;
			while(1)
			{
				nReadBytes = 4096;
				ReadLogFile(g_Log_FileName,(unsigned char*)(pBuf+nOffset),&nReadBytes,&g_Log_nOffset);
				nOffset += nReadBytes;
				g_Log_nOffset += nReadBytes;
				if(nReadBytes == 0)
				{
					break;
				}
			}
			len = nOffset;
		}
	}
	//没有机会free缓冲区，可以考虑固定5M


	soap->mode |= SOAP_ENC_MIME;
	soap->mime.boundary = boundary;
	soap->mime.start = start;
	soap->mime.first = &stMultiM;
	stMultiM.id = id;
	stMultiM.type = type;
	stMultiM.encoding = SOAP_MIME_BINARY;
	stMultiM.size = len;
	stMultiM.ptr = pBuf;

    tds__GetSystemBackupResponse->__sizeBackupFiles = 1;
    tds__GetSystemBackupResponse->BackupFiles = &stBak;
    stBak.Name = "/BackupFile";
    stBak.Data = &stAtta;
    stAtta.xmime__contentType = contentType;
    stAtta.xop__Include = &stXop_Include;
    stXop_Include.href = "cid:http://tempuri.org/0/systembackup";
    stXop_Include.__anyAttribute = "xmlns:xop";
    stXop_Include.__any = "http://www.w3.org/2004/08/xop/include";


    return SOAP_OK;
}

/** Web service operation '__tds__GetSystemLog' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetSystemLog(struct soap *soap, struct _tds__GetSystemLog *tds__GetSystemLog, struct _tds__GetSystemLogResponse *tds__GetSystemLogResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__GetSystemSupportInformation' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetSystemSupportInformation(struct soap *soap, struct _tds__GetSystemSupportInformation *tds__GetSystemSupportInformation, struct _tds__GetSystemSupportInformationResponse *tds__GetSystemSupportInformationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__GetScopes' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetScopes(struct soap *soap, struct _tds__GetScopes *tds__GetScopes, struct _tds__GetScopesResponse *tds__GetScopesResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_VIDEO_CFG stVideoCfg = {0};
    static char szName[128];

    print_level(SV_DEBUG, "dummy:  \n");

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

#if (defined(BOARD_IPCR20S3))
    static struct tt__Scope astScopes[7];
    tds__GetScopesResponse->__sizeScopes = 7;
    tds__GetScopesResponse->Scopes = astScopes;
    astScopes[0].ScopeDef = tt__ScopeDefinition__Fixed;
    astScopes[0].ScopeItem = "onvif://www.onvif.org/type/Network_Video_Transmitter";
    astScopes[1].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[1].ScopeItem = "onvif://www.onvif.org/Profile/Streaming";
    astScopes[2].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[2].ScopeItem = "onvif://www.onvif.org/Profile/Q/Operational";
    astScopes[3].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[3].ScopeItem = "onvif://www.onvif.org/Profile/S";
    astScopes[4].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[4].ScopeItem = "onvif://www.onvif.org/hardware/IPC-General";
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
    {
        sprintf(szName, " onvif://www.onvif.org/name/%s", stVideoCfg.astChnParam[0].szChnName);
        astScopes[5].ScopeDef = tt__ScopeDefinition__Configurable;
        astScopes[5].ScopeItem = szName;
        astScopes[6].ScopeDef = tt__ScopeDefinition__Configurable;
        astScopes[6].ScopeItem = "onvif://www.onvif.org/location/Not set";
    }
    else
    {
        astScopes[5].ScopeDef = tt__ScopeDefinition__Configurable;
        astScopes[5].ScopeItem = "onvif://www.onvif.org/name/IPCAM";
        astScopes[6].ScopeDef = tt__ScopeDefinition__Configurable;
        astScopes[6].ScopeItem = "onvif://www.onvif.org/location/country/China";
    }
#else
    static struct tt__Scope astScopes[6];
    tds__GetScopesResponse->__sizeScopes = 6;
    tds__GetScopesResponse->Scopes = astScopes;
    astScopes[0].ScopeDef = tt__ScopeDefinition__Fixed;
    astScopes[0].ScopeItem = "onvif://www.onvif.org/type/Network_Video_Transmitter";
    astScopes[1].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[1].ScopeItem = "onvif://www.onvif.org/Profile/Streaming";
    astScopes[2].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[2].ScopeItem = "onvif://www.onvif.org/Profile/Q/Operational";
    astScopes[3].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[3].ScopeItem = "onvif://www.onvif.org/hardware/IPC-General";
    astScopes[4].ScopeDef = tt__ScopeDefinition__Fixed;
    astScopes[4].ScopeItem = "onvif://www.onvif.org/name/IPCAM";
    astScopes[5].ScopeDef = tt__ScopeDefinition__Configurable;
    astScopes[5].ScopeItem = "onvif://www.onvif.org/location/country/China";
#endif

    return SOAP_OK;
}

/** Web service operation '__tds__SetScopes' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetScopes(struct soap *soap, struct _tds__SetScopes *tds__SetScopes, struct _tds__SetScopesResponse *tds__SetScopesResponse){
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__AddScopes' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__AddScopes(struct soap *soap, struct _tds__AddScopes *tds__AddScopes, struct _tds__AddScopesResponse *tds__AddScopesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__RemoveScopes' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__RemoveScopes(struct soap *soap, struct _tds__RemoveScopes *tds__RemoveScopes, struct _tds__RemoveScopesResponse *tds__RemoveScopesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__GetDiscoveryMode' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetDiscoveryMode(struct soap *soap, struct _tds__GetDiscoveryMode *tds__GetDiscoveryMode, struct _tds__GetDiscoveryModeResponse *tds__GetDiscoveryModeResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_NETWORK_CFG stNetworkCfg = {0};

    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
        	print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (stNetworkCfg.bDiscoverable)
    {
        tds__GetDiscoveryModeResponse->DiscoveryMode = tt__DiscoveryMode__Discoverable;
    }
    else
    {
        tds__GetDiscoveryModeResponse->DiscoveryMode = tt__DiscoveryMode__NonDiscoverable;
    }

    return SOAP_OK;
}

/** Web service operation '__tds__SetDiscoveryMode' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetDiscoveryMode(struct soap *soap, struct _tds__SetDiscoveryMode *tds__SetDiscoveryMode, struct _tds__SetDiscoveryModeResponse *tds__SetDiscoveryModeResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};

    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
        	print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (NULL != tds__SetDiscoveryMode)
    {
        if (tt__DiscoveryMode__Discoverable == tds__SetDiscoveryMode->DiscoveryMode)
        {
            stNetworkCfg.bDiscoverable = SV_TRUE;
        }
        else
        {
            stNetworkCfg.bDiscoverable = SV_FALSE;
        }
    }

    stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
    stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__tds__GetUsers' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetUsers(struct soap *soap, struct _tds__GetUsers *tds__GetUsers, struct _tds__GetUsersResponse *tds__GetUsersResponse)
{
	static struct tt__User astUsers[2] = {0};
	sint32 s32Ret = 0;
	MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
	MSG_USR_CFG stUsrCfg = {0};

	if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
        	print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }
	stRetPkt.pu8Data = (uint8 *)&stUsrCfg;
	s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_USR_CFG, NULL, &stRetPkt, sizeof(MSG_USR_CFG));
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "OP_REQ_GET_USR_CFG failed.\n");
		return SOAP_SVR_FAULT;
	}
    tds__GetUsersResponse->__sizeUser = 2;
    tds__GetUsersResponse->User = &astUsers[0];
    astUsers[0].Username = "admin";
	astUsers[0].Password = stUsrCfg.szUserPassword[0][2];
    astUsers[0].UserLevel = tt__UserLevel__Administrator;
	astUsers[1].Username = "default";
	astUsers[1].UserLevel = tt__UserLevel__Anonymous;


    return SOAP_OK;
}

/** Web service operation '__tds__CreateUsers' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__CreateUsers(struct soap *soap, struct _tds__CreateUsers *tds__CreateUsers, struct _tds__CreateUsersResponse *tds__CreateUsersResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__DeleteUsers' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__DeleteUsers(struct soap *soap, struct _tds__DeleteUsers *tds__DeleteUsers, struct _tds__DeleteUsersResponse *tds__DeleteUsersResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__SetUser' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetUser(struct soap *soap, struct _tds__SetUser *tds__SetUser, struct _tds__SetUserResponse *tds__SetUserResponse)
{
	sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_USR_CFG stUsrCfg = {0};

    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }
    stRetPkt.pu8Data = (uint8 *)&stUsrCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_USR_CFG, NULL, &stRetPkt, sizeof(MSG_USR_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_USR_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

	if(NULL != tds__SetUser->User)
	{
	   if(NULL != tds__SetUser->User->Password)
	   {
		   strcpy(stUsrCfg.szUserPassword[0][2], tds__SetUser->User->Password);
	   }
	   else
	   {

		   memset(stUsrCfg.szUserPassword[0][2],0,sizeof(stUsrCfg.szUserPassword[0][2]));
	   }
	}

    stMsgPkt.pu8Data = (uint8 *)&stUsrCfg;
    stMsgPkt.u32Size = sizeof(MSG_USR_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_USR_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_USR_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__tds__GetCapabilities' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetCapabilities(struct soap *soap, struct _tds__GetCapabilities *tds__GetCapabilities, struct _tds__GetCapabilitiesResponse *tds__GetCapabilitiesResponse)
{
    sint32 s32Ret = 0, i;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__Capabilities stCapabilities = {0};

    //print_level(SV_DEBUG, "__sizeCategory: %d, Category[0]: %d\n", tds__GetCapabilities->__sizeCategory, tds__GetCapabilities->Category[0]);
    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    tds__GetCapabilitiesResponse->Capabilities = &stCapabilities;
    if (tds__GetCapabilities->__sizeCategory == 0 ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__Device ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__All)
    {
        static struct tt__DeviceCapabilities stDevice = {0};
        static struct tt__NetworkCapabilities stNetwork = {0};
        static enum xsd__boolean_ enIPFilter = _false;
        static enum xsd__boolean_ enZeroConfiguration = _false;
        static enum xsd__boolean_ enIPVersion6 = _false;
        static enum xsd__boolean_ enDynDNS = _false;
        static struct tt__NetworkCapabilitiesExtension stNetworkExtension = {0};
        static enum xsd__boolean_ enDot11Configuration = _false;
        static struct tt__SystemCapabilities stSystem = {0};
        static struct tt__OnvifVersion stSupportedVersions = {0};
        static struct tt__SystemCapabilitiesExtension stSystemExtension = {0};
        static enum xsd__boolean_ enHttpFirmwareUpgrade = _true;
        static enum xsd__boolean_ enHttpSystemBackup = _true;
        static enum xsd__boolean_ enHttpSystemLogging = _false;
        static enum xsd__boolean_ enHttpSupportInformation = _true;
        static struct tt__SecurityCapabilities stSecurity = {0};
        static struct tt__SecurityCapabilitiesExtension stSecurityExtension = {0};
        static struct tt__SecurityCapabilitiesExtension2 stSecurityExtension2 = {0};
        static char szDeviceXAddr[128];

        stCapabilities.Device = &stDevice;
        sprintf(szDeviceXAddr, "http://%s/onvif/device_service", stNetworkCfg.szRealIpAddr);
        stDevice.XAddr = szDeviceXAddr;
        stDevice.Network = &stNetwork;
        stNetwork.IPFilter = &enIPFilter;
        stNetwork.ZeroConfiguration = &enZeroConfiguration;
        stNetwork.IPVersion6 = &enIPVersion6;
        stNetwork.DynDNS = &enDynDNS;
        stNetwork.Extension = &stNetworkExtension;
        stNetwork.Extension->__size = 1;
        stNetwork.Extension->Dot11Configuration = &enDot11Configuration;

        stDevice.System = &stSystem;
        stSystem.DiscoveryResolve = _true;
        stSystem.DiscoveryBye = _true;
        stSystem.RemoteDiscovery = _true;
        stSystem.SystemBackup = _true;
        stSystem.SystemLogging = _false;
        stSystem.FirmwareUpgrade = _true;
        stSystem.__sizeSupportedVersions = 1;
        stSystem.SupportedVersions = &stSupportedVersions;
        stSupportedVersions.Major = 2;
        stSupportedVersions.Minor = 10;
        stSystem.Extension = &stSystemExtension;
        stSystemExtension.HttpFirmwareUpgrade = &enHttpFirmwareUpgrade;
        stSystemExtension.HttpSystemBackup = &enHttpSystemBackup;
        stSystemExtension.HttpSystemLogging = &enHttpSystemLogging;
        stSystemExtension.HttpSupportInformation = &enHttpSupportInformation;

        stDevice.Security = &stSecurity;
        stSecurity.Extension = &stSecurityExtension;
        stSecurityExtension.Extension = &stSecurityExtension2;
    }

    if (tds__GetCapabilities->__sizeCategory == 0 ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__Events ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__All)
    {
        static struct tt__EventCapabilities stEvents = {0};
        static char szEventXAddr[128];

        stCapabilities.Events = &stEvents;
        sprintf(szEventXAddr, "http://%s/onvif/event_service", stNetworkCfg.szRealIpAddr);
        stEvents.XAddr = szEventXAddr;
        stEvents.WSSubscriptionPolicySupport = _true;
        stEvents.WSPullPointSupport = _true;
        stEvents.WSPausableSubscriptionManagerInterfaceSupport = _false;
    }

    if (tds__GetCapabilities->__sizeCategory == 0 ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__Imaging ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__All)
    {
        static struct tt__ImagingCapabilities stImaging = {0};
        static char szImageXAddr[128];

        stCapabilities.Imaging = &stImaging;
        sprintf(szImageXAddr, "http://%s/onvif/image_service", stNetworkCfg.szRealIpAddr);
        stImaging.XAddr = szImageXAddr;
    }

    if (tds__GetCapabilities->__sizeCategory == 0 ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__Media ||
        tds__GetCapabilities->Category[0] == tt__CapabilityCategory__All)
    {
        static struct tt__MediaCapabilities stMedia = {0};
        static struct tt__RealTimeStreamingCapabilities stStreamingCapabilities = {0};
        static enum xsd__boolean_ enRTPMulticast = _false;
        static enum xsd__boolean_ enRTP_USCORETCP = _true;
        static enum xsd__boolean_ enRTP_USCORERTSP_USCORETCP = _true;
        static char szMediaXAddr[128];

        stCapabilities.Media = &stMedia;
        sprintf(szMediaXAddr, "http://%s/onvif/media_service", stNetworkCfg.szRealIpAddr);
        stMedia.XAddr = szMediaXAddr;
        stMedia.StreamingCapabilities = &stStreamingCapabilities;
        stStreamingCapabilities.RTPMulticast = &enRTPMulticast;
        stStreamingCapabilities.RTP_USCORETCP = &enRTP_USCORETCP;
        stStreamingCapabilities.RTP_USCORERTSP_USCORETCP = &enRTP_USCORERTSP_USCORETCP;
    }

    if (BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() == BOARD_HDW845V1_V1 \
    || (BOARD_GetVersion() == BOARD_IPCR20S3_V1 && BOARD_IsCustomer(BOARD_C_IPCR20S3_202120)))
    {
        if (tds__GetCapabilities->__sizeCategory == 0 ||
            tds__GetCapabilities->Category[0] == tt__CapabilityCategory__PTZ ||
            tds__GetCapabilities->Category[0] == tt__CapabilityCategory__All)
        {
            static struct tt__PTZCapabilities stPtz = {0};
            static char szPtzXAddr[128];

            stCapabilities.PTZ = &stPtz;
            sprintf(szPtzXAddr, "http://%s/onvif/ptz_service", stNetworkCfg.szRealIpAddr);
            stPtz.XAddr = szPtzXAddr;
        }
    }

    return SOAP_OK;
}

/** Web service operation '__tds__GetHostname' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetHostname(struct soap *soap, struct _tds__GetHostname *tds__GetHostname, struct _tds__GetHostnameResponse *tds__GetHostnameResponse)
{
    static struct tt__HostnameInformation stHostnameInformation = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    tds__GetHostnameResponse->HostnameInformation = &stHostnameInformation;
    stHostnameInformation.FromDHCP = _false;
    stHostnameInformation.Name = "IPCAM";

    return SOAP_OK;
}

/** Web service operation '__tds__SetHostname' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetHostname(struct soap *soap, struct _tds__SetHostname *tds__SetHostname, struct _tds__SetHostnameResponse *tds__SetHostnameResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__SetHostnameFromDHCP' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetHostnameFromDHCP(struct soap *soap, struct _tds__SetHostnameFromDHCP *tds__SetHostnameFromDHCP, struct _tds__SetHostnameFromDHCPResponse *tds__SetHostnameFromDHCPResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__GetDNS' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetDNS(struct soap *soap, struct _tds__GetDNS *tds__GetDNS, struct _tds__GetDNSResponse *tds__GetDNSResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__DNSInformation stDNSInformation = {0};
    static struct tt__IPAddress stDNS = {0};
    static char szDns[32];

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    tds__GetDNSResponse->DNSInformation = &stDNSInformation;
    stDNSInformation.FromDHCP = stNetworkCfg.bDHCPEnable;
    if (stDNSInformation.FromDHCP)
    {
        stDNSInformation.__sizeDNSFromDHCP = 1;
        stDNSInformation.DNSFromDHCP = &stDNS;
    }
    else
    {
        stDNSInformation.__sizeDNSManual = 1;
        stDNSInformation.DNSManual = &stDNS;
    }
    stDNS.Type = tt__IPType__IPv4;
    stDNS.IPv4Address = szDns;
    strcpy(szDns, stNetworkCfg.szDnsServer);

    return SOAP_OK;
}

/** Web service operation '__tds__SetDNS' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetDNS(struct soap *soap, struct _tds__SetDNS *tds__SetDNS, struct _tds__SetDNSResponse *tds__SetDNSResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (NULL != tds__SetDNS->DNSManual)
    {
        strcpy(stNetworkCfg.szDnsServer, tds__SetDNS->DNSManual->IPv4Address);
    }
    stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
    stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_OK;
}

/** Web service operation '__tds__GetNTP' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetNTP(struct soap *soap, struct _tds__GetNTP *tds__GetNTP, struct _tds__GetNTPResponse *tds__GetNTPResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_SYS_CFG stSystemCfg = {0};
    static struct tt__NTPInformation stNTPInformation;
    static struct tt__NetworkHost stNTPManual;

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stSystemCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_SYS_CFG, NULL, &stRetPkt, sizeof(MSG_SYS_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_SYS_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    stNTPInformation.FromDHCP = _false;
    stNTPInformation.__sizeNTPManual = 1;
    stNTPInformation.NTPManual = &stNTPManual;
    stNTPInformation.NTPManual->Type = tt__NetworkHostType__IPv4;
    stNTPInformation.NTPManual->IPv4Address = stSystemCfg.szNtpServer;
    tds__GetNTPResponse->NTPInformation = &stNTPInformation;

    return SOAP_OK;
}

/** Web service operation '__tds__SetNTP' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetNTP(struct soap *soap, struct _tds__SetNTP *tds__SetNTP, struct _tds__SetNTPResponse *tds__SetNTPResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_SYS_CFG stSystemCfg = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stSystemCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_SYS_CFG, NULL, &stRetPkt, sizeof(MSG_SYS_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_SYS_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (_false == tds__SetNTP->FromDHCP &&
        tds__SetNTP->__sizeNTPManual > 0 &&
        NULL != tds__SetNTP->NTPManual &&
        tt__NetworkHostType__IPv4 == tds__SetNTP->NTPManual->Type)
    {
        strcpy(stSystemCfg.szNtpServer, tds__SetNTP->NTPManual->IPv4Address);
    }
    stMsgPkt.pu8Data = (uint8 *)&stSystemCfg;
    stMsgPkt.u32Size = sizeof(MSG_SYS_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_SYS_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_SYS_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_OK;
}

/** Web service operation '__tds__GetDynamicDNS' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetDynamicDNS(struct soap *soap, struct _tds__GetDynamicDNS *tds__GetDynamicDNS, struct _tds__GetDynamicDNSResponse *tds__GetDynamicDNSResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__SetDynamicDNS' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetDynamicDNS(struct soap *soap, struct _tds__SetDynamicDNS *tds__SetDynamicDNS, struct _tds__SetDynamicDNSResponse *tds__SetDynamicDNSResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__GetNetworkInterfaces' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetNetworkInterfaces(struct soap *soap, struct _tds__GetNetworkInterfaces *tds__GetNetworkInterfaces, struct _tds__GetNetworkInterfacesResponse *tds__GetNetworkInterfacesResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__NetworkInterface stNetworkInterfaces = {0};
    static struct tt__NetworkInterfaceInfo stInfo = {0};
    static struct tt__NetworkInterfaceLink stLink = {0};
    static struct tt__IPv4NetworkInterface stIPv4 = {0};
    static struct tt__NetworkInterfaceConnectionSetting stAdminSettings = {0};
    static struct tt__NetworkInterfaceConnectionSetting stOperSettings = {0};
    static struct tt__IPv4Configuration stConfig = {0};
    static struct tt__PrefixedIPv4Address stManual = {0};
    static struct tt__PrefixedIPv4Address stFromDHCP = {0};
    static int MTU = 1500;

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "szMacAddr: %s, szIpAddr: %s\n", stNetworkCfg.szRealMacAddr, stNetworkCfg.szRealIpAddr);
    tds__GetNetworkInterfacesResponse->__sizeNetworkInterfaces = 1;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces = &stNetworkInterfaces;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->token = "eth0";
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Enabled = _true;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Info = &stInfo;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link = &stLink;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4 = &stIPv4;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Info->Name = "eth0";
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Info->HwAddress = stNetworkCfg.szRealMacAddr;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Info->MTU = &MTU;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->AdminSettings = &stAdminSettings;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->OperSettings = &stOperSettings;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->AdminSettings->AutoNegotiation = _false;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->AdminSettings->Speed = 100;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->AdminSettings->Duplex = tt__Duplex__Full;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->OperSettings->AutoNegotiation = _false;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->OperSettings->Speed = 100;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->OperSettings->Duplex = tt__Duplex__Full;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->Link->InterfaceType = 6;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Enabled = _true;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config = &stConfig;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->__sizeManual = 1;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->Manual = &stManual;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->Manual->Address = stNetworkCfg.szIpAddr;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->Manual->PrefixLength = 24;
    tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->DHCP = _false;
    if (0 != strcmp(stNetworkCfg.szIpAddr, stNetworkCfg.szRealIpAddr))
    {
        tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->DHCP = _true;
        tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->FromDHCP = &stFromDHCP;
        tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->FromDHCP->Address = stNetworkCfg.szRealIpAddr;
        tds__GetNetworkInterfacesResponse->NetworkInterfaces->IPv4->Config->FromDHCP->PrefixLength = 24;
    }


    return SOAP_OK;
}

/** Web service operation '__tds__SetNetworkInterfaces' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetNetworkInterfaces(struct soap *soap, struct _tds__SetNetworkInterfaces *tds__SetNetworkInterfaces, struct _tds__SetNetworkInterfacesResponse *tds__SetNetworkInterfacesResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};

    print_level(SV_DEBUG, "dummy:  %s\n", tds__SetNetworkInterfaces->InterfaceToken);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (0 == strcmp(tds__SetNetworkInterfaces->InterfaceToken, "eth0"))
	{
		if(NULL != tds__SetNetworkInterfaces->NetworkInterface->IPv4)
		{
			if(NULL != tds__SetNetworkInterfaces->NetworkInterface->IPv4->Manual)
    		{
       		 	strcpy(stNetworkCfg.szIpAddr, tds__SetNetworkInterfaces->NetworkInterface->IPv4->Manual->Address);
    		}
			if (NULL != tds__SetNetworkInterfaces->NetworkInterface->IPv4->DHCP)
        	{
				stNetworkCfg.bDHCPEnable = *tds__SetNetworkInterfaces->NetworkInterface->IPv4->DHCP;
			}
		}
	}


    stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
    stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    tds__SetNetworkInterfacesResponse->RebootNeeded = _false;

    return SOAP_OK;
}

/** Web service operation '__tds__GetNetworkProtocols' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetNetworkProtocols(struct soap *soap, struct _tds__GetNetworkProtocols *tds__GetNetworkProtocols, struct _tds__GetNetworkProtocolsResponse *tds__GetNetworkProtocolsResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__NetworkProtocol astNetworkProtocols[2];
    static int PortHttp = 80, PortRtsp = 554;

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS == s32Ret)
        PortRtsp = stNetworkCfg.u32RtspServPort;

    print_level(SV_DEBUG, "dummy:  \n");
    tds__GetNetworkProtocolsResponse->__sizeNetworkProtocols = 2;
    tds__GetNetworkProtocolsResponse->NetworkProtocols = astNetworkProtocols;
    astNetworkProtocols[0].Name = tt__NetworkProtocolType__HTTP;
    astNetworkProtocols[0].Enabled = _true;
    astNetworkProtocols[0].__sizePort = 1;
    astNetworkProtocols[0].Port = &PortHttp;
    astNetworkProtocols[1].Name = tt__NetworkProtocolType__RTSP;
    astNetworkProtocols[1].Enabled = _true;
    astNetworkProtocols[1].__sizePort = 1;
    astNetworkProtocols[1].Port = &PortRtsp;

    return SOAP_OK;
}

/** Web service operation '__tds__SetNetworkProtocols' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetNetworkProtocols(struct soap *soap, struct _tds__SetNetworkProtocols *tds__SetNetworkProtocols, struct _tds__SetNetworkProtocolsResponse *tds__SetNetworkProtocolsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__GetNetworkDefaultGateway' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetNetworkDefaultGateway(struct soap *soap, struct _tds__GetNetworkDefaultGateway *tds__GetNetworkDefaultGateway, struct _tds__GetNetworkDefaultGatewayResponse *tds__GetNetworkDefaultGatewayResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    static MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__NetworkGateway stNetworkGateway = {0};
    static char *apszIpaddr[1] = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS == s32Ret)
    {
        apszIpaddr[0] = stNetworkCfg.szGateway;
        stNetworkGateway.__sizeIPv4Address = 1;
        stNetworkGateway.IPv4Address = apszIpaddr;
        tds__GetNetworkDefaultGatewayResponse->NetworkGateway = &stNetworkGateway;
    }

    return SOAP_OK;
}

/** Web service operation '__tds__SetNetworkDefaultGateway' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetNetworkDefaultGateway(struct soap *soap, struct _tds__SetNetworkDefaultGateway *tds__SetNetworkDefaultGateway, struct _tds__SetNetworkDefaultGatewayResponse *tds__SetNetworkDefaultGatewayResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};

    print_level(SV_DEBUG, "gateway: %s\n", *tds__SetNetworkDefaultGateway->IPv4Address);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (tds__SetNetworkDefaultGateway->__sizeIPv4Address > 0)
    {
        strcpy(stNetworkCfg.szGateway, *tds__SetNetworkDefaultGateway->IPv4Address);
    }
    stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
    stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__tds__GetZeroConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetZeroConfiguration(struct soap *soap, struct _tds__GetZeroConfiguration *tds__GetZeroConfiguration, struct _tds__GetZeroConfigurationResponse *tds__GetZeroConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__SetZeroConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetZeroConfiguration(struct soap *soap, struct _tds__SetZeroConfiguration *tds__SetZeroConfiguration, struct _tds__SetZeroConfigurationResponse *tds__SetZeroConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

SOAP_FMAC5 int SOAP_FMAC6 __tds__GetAccessPolicy(struct soap *soap, struct _tds__GetAccessPolicy *tds__GetAccessPolicy, struct _tds__GetAccessPolicyResponse *tds__GetAccessPolicyResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__SetAccessPolicy' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__SetAccessPolicy(struct soap *soap, struct _tds__SetAccessPolicy *tds__SetAccessPolicy, struct _tds__SetAccessPolicyResponse *tds__SetAccessPolicyResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    if (NULL != tds__SetAccessPolicy->PolicyFile)
    {
        print_level(SV_DEBUG, "xmime__contentType: %s, Data: %s\n", tds__SetAccessPolicy->PolicyFile->xmime__contentType, tds__SetAccessPolicy->PolicyFile->Data);
    }

    return SOAP_OK;
}

/** Web service operation '__tds__GetSystemUris' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__GetSystemUris(struct soap *soap, struct _tds__GetSystemUris *tds__GetSystemUris, struct _tds__GetSystemUrisResponse *tds__GetSystemUrisResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tds__StartFirmwareUpgrade' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__StartFirmwareUpgrade(struct soap *soap, struct _tds__StartFirmwareUpgrade *tds__StartFirmwareUpgrade, struct _tds__StartFirmwareUpgradeResponse *tds__StartFirmwareUpgradeResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static char szUri[128];

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    sprintf(szUri, "http://%s:8080/upgrade", stNetworkCfg.szRealIpAddr);
    tds__StartFirmwareUpgradeResponse->UploadUri = szUri;
    tds__StartFirmwareUpgradeResponse->UploadDelay = "PT5S";
    tds__StartFirmwareUpgradeResponse->ExpectedDownTime = "PT300S";

    return SOAP_OK;
}

/** Web service operation '__tds__StartSystemRestore' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tds__StartSystemRestore(struct soap *soap, struct _tds__StartSystemRestore *tds__StartSystemRestore, struct _tds__StartSystemRestoreResponse *tds__StartSystemRestoreResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__GetServiceCapabilities' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__GetServiceCapabilities(struct soap *soap, struct _tev__GetServiceCapabilities *tev__GetServiceCapabilities, struct _tev__GetServiceCapabilitiesResponse *tev__GetServiceCapabilitiesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__PullMessages' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__PullMessages(struct soap *soap, struct _tev__PullMessages *tev__PullMessages, struct _tev__PullMessagesResponse *tev__PullMessagesResponse)
{
    sint32 s32Ret = 0;
	static uint32 count = 0;
	CFG_MEDIA_PARAM stMediaParam = {0};
	MSG_PACKET_S stMsgPkt = {0};
   	char currentTimeString[30];
   	time_t currentTime;
    static time_t stCurTime = {0};
    static time_t stTerTime = {0};
	struct timeval tvNow;
	struct timezone tz;
	struct tm tmUtcTime;
	struct tm tmNowTime;
	static uint32 u32Count = 0;

	gettimeofday(&tvNow,&tz);
	gmtime_r((time_t *)&tvNow.tv_sec,&tmUtcTime);
	tvNow.tv_sec += (tz.tz_minuteswest * 60);
	gmtime_r((time_t *)&tvNow.tv_sec, &tmNowTime);
	currentTime = (time_t)tvNow.tv_sec;
	stCurTime = currentTime;
	stTerTime = stCurTime + 1800;
	tev__PullMessagesResponse->CurrentTime = stCurTime;
    tev__PullMessagesResponse->TerminationTime = stTerTime;


	if(m_stOnvifCfg.u32GopFlag != 1)
		return SOAP_OK;

	u32Count++;

	if(u32Count < 20)
		return SOAP_OK;

    s32Ret = CONFIG_GetMediaParam(&stMediaParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONFIG_GetMediaParam failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

	stMsgPkt.stMsg.u32Param = stMediaParam.astChnParam[0].u32MainIfrmInterval;
	s32Ret = Msg_submitEvent(EP_CONTROL, OP_REQ_SET_GOP,&stMsgPkt);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
		return SOAP_SVR_FAULT;
	}
	m_stOnvifCfg.u32GopFlag = 0;
	u32Count = 0;

    return SOAP_OK;
}

/** Web service operation '__tev__SetSynchronizationPoint' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__SetSynchronizationPoint(struct soap *soap, struct _tev__SetSynchronizationPoint *tev__SetSynchronizationPoint, struct _tev__SetSynchronizationPointResponse *tev__SetSynchronizationPointResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__CreatePullPointSubscription' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__CreatePullPointSubscription(struct soap *soap, struct _tev__CreatePullPointSubscription *tev__CreatePullPointSubscription, struct _tev__CreatePullPointSubscriptionResponse *tev__CreatePullPointSubscriptionResponse)
{
	sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
   	char currentTimeString[30]; //字符串长度不超过 30 个字符
   	time_t currentTime;
	static char szWsaAddr[128];
    static MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct wsa5__EndpointReferenceType stEpType = {0};
    static time_t stCurTime = {0};
    static time_t stTerTime = {0};
	struct timeval tvNow;
	struct timezone tz;
	struct tm tmUtcTime;
	struct tm tmNowTime;
	stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
	s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
		return SOAP_SVR_FAULT;
	}
	gettimeofday(&tvNow,&tz);
	gmtime_r((time_t *)&tvNow.tv_sec,&tmUtcTime);
	tvNow.tv_sec += (tz.tz_minuteswest * 60);
	gmtime_r((time_t *)&tvNow.tv_sec, &tmNowTime);
	currentTime = (time_t)tvNow.tv_sec;
    strftime(currentTimeString, sizeof(currentTimeString), "%Y-%m-%dT%H:%M:%SZ", &tmNowTime);
	sprintf(szWsaAddr, "http://%s/onvif/Events/PullSubManager_%s_0", stNetworkCfg.szRealIpAddr,currentTimeString);
	stCurTime = currentTime;
	stTerTime = stCurTime + 60;//tev__CreatePullPointSubscription->InitialTerminationTime;
	print_level(SV_DEBUG,"wsaddress:%s \n",szWsaAddr);
    tev__CreatePullPointSubscriptionResponse->SubscriptionReference = stEpType;
    tev__CreatePullPointSubscriptionResponse->SubscriptionReference.Address = szWsaAddr;
    tev__CreatePullPointSubscriptionResponse->wsnt__CurrentTime = stCurTime;
    tev__CreatePullPointSubscriptionResponse->wsnt__TerminationTime = stTerTime;
    return SOAP_OK;
}

/** Web service operation '__tev__GetEventProperties' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__GetEventProperties(struct soap *soap, struct _tev__GetEventProperties *tev__GetEventProperties, struct _tev__GetEventPropertiesResponse *tev__GetEventPropertiesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__Renew' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__Renew(struct soap *soap, struct _wsnt__Renew *wsnt__Renew, struct _wsnt__RenewResponse *wsnt__RenewResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__Unsubscribe' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__Unsubscribe(struct soap *soap, struct _wsnt__Unsubscribe *wsnt__Unsubscribe, struct _wsnt__UnsubscribeResponse *wsnt__UnsubscribeResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__Subscribe' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__Subscribe(struct soap *soap, struct _wsnt__Subscribe *wsnt__Subscribe, struct _wsnt__SubscribeResponse *wsnt__SubscribeResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__Notify' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__Notify(struct soap *soap, struct _wsnt__Notify *wsnt__Notify)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tev__Notify_' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tev__Notify_(struct soap *soap, struct _wsnt__Notify *wsnt__Notify)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__timg__GetServiceCapabilities' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__GetServiceCapabilities(struct soap *soap, struct _timg__GetServiceCapabilities *timg__GetServiceCapabilities, struct _timg__GetServiceCapabilitiesResponse *timg__GetServiceCapabilitiesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__timg__GetImagingSettings' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__GetImagingSettings(struct soap *soap, struct _timg__GetImagingSettings *timg__GetImagingSettings, struct _timg__GetImagingSettingsResponse *timg__GetImagingSettingsResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_IMAGE_CFG stImageCfg = {0};
    static struct tt__ImagingSettings20 stImagingSettings = {0};
    static struct tt__BacklightCompensation20 stBacklightCompensation = {0};
    static enum tt__BacklightCompensationMode enBacklightMode = tt__BacklightCompensationMode__OFF;
    static float fBrightness;
    static float fColorSaturation;
    static float fContrast;
    static struct tt__Exposure20 stExposure = {0};
    static float fMinExposureTime;
    static float fMaxExposureTime;
    static float fMinGain;
    static float fMaxGain;
    static enum tt__IrCutFilterMode enIrCutFilter = tt__IrCutFilterMode__AUTO;
    static float fSharpness;
    static struct tt__WhiteBalance20 stWhiteBalance = {0};
    static float fCrGain;
    static float fCbGain;

    stRetPkt.pu8Data = (uint8 *)&stImageCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_IMAGE_CFG, NULL, &stRetPkt, sizeof(MSG_IMAGE_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_IMAGE_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "dummy: %s \n", timg__GetImagingSettings->VideoSourceToken);
    timg__GetImagingSettingsResponse->ImagingSettings = &stImagingSettings;
    stImagingSettings.BacklightCompensation = &stBacklightCompensation;
    stBacklightCompensation.Mode = tt__BacklightCompensationMode__OFF;
    stImagingSettings.Brightness = &fBrightness;
    fBrightness = (float)stImageCfg.u8Brightness;
    stImagingSettings.ColorSaturation = &fColorSaturation;
    fColorSaturation = (float)stImageCfg.u8Saturation;
    stImagingSettings.Contrast = &fContrast;
    fContrast = (float)stImageCfg.u8Contrast;
    stImagingSettings.Exposure = &stExposure;
    stExposure.Mode = tt__ExposureMode__AUTO;
    stExposure.MinExposureTime = &fMinExposureTime;
    fMinExposureTime = 10.0;
    stExposure.MaxExposureTime = &fMaxExposureTime;
    fMaxExposureTime = 10000.0;
    stExposure.MinGain = &fMinGain;
    fMinGain = 1.0;
    stExposure.MaxGain = &fMaxGain;
    fMaxGain = 36.0;
    stImagingSettings.IrCutFilter = &enIrCutFilter;
    stImagingSettings.Sharpness = &fSharpness;
    fSharpness = (float)stImageCfg.u8Sharpness;
    stImagingSettings.WhiteBalance = &stWhiteBalance;
    stWhiteBalance.Mode = tt__WhiteBalanceMode__AUTO;
    stWhiteBalance.CrGain = &fCrGain;
    fCrGain = 0.0;
    stWhiteBalance.CbGain = &fCbGain;
    fCbGain = 0.0;


    return SOAP_OK;
}

/** Web service operation '__timg__SetImagingSettings' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__SetImagingSettings(struct soap *soap, struct _timg__SetImagingSettings *timg__SetImagingSettings, struct _timg__SetImagingSettingsResponse *timg__SetImagingSettingsResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_IMAGE_CFG stImageCfg = {0};
    struct tt__ImagingSettings20 *ImagingSettings = timg__SetImagingSettings->ImagingSettings;

    print_level(SV_DEBUG, "dummy: %s \n", timg__SetImagingSettings->VideoSourceToken);
    if (NULL != ImagingSettings)
    {
        print_level(SV_DEBUG, "Brightness:%.1f, ColorSaturation:%.1f, Contrast:%.1f, Sharpness:%.1f \n", ImagingSettings->Brightness ? *(ImagingSettings->Brightness) : 0, \
                    ImagingSettings->ColorSaturation ? *(ImagingSettings->ColorSaturation) : 0, ImagingSettings->Contrast ? *(ImagingSettings->Contrast) : 0, ImagingSettings->Sharpness ? *(ImagingSettings->Sharpness) : 0);
    }
    else
    {
        return SOAP_ERR;
    }

    stRetPkt.pu8Data = (uint8 *)&stImageCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_IMAGE_CFG, NULL, &stRetPkt, sizeof(MSG_IMAGE_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_IMAGE_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (NULL != ImagingSettings->Brightness)
    {
        stImageCfg.u8Brightness = (uint8)(*(ImagingSettings->Brightness));
    }
    if (NULL != ImagingSettings->Contrast)
    {
        stImageCfg.u8Contrast = (uint8)(*(ImagingSettings->Contrast));
    }
    if (NULL != ImagingSettings->ColorSaturation)
    {
        stImageCfg.u8Saturation = (uint8)(*(ImagingSettings->ColorSaturation));
    }
    if (NULL != ImagingSettings->Sharpness)
    {
        stImageCfg.u8Sharpness = (uint8)(*(ImagingSettings->Sharpness));
    }

    stMsgPkt.pu8Data = (uint8 *)&stImageCfg;
    stMsgPkt.u32Size = sizeof(MSG_IMAGE_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_IMAGE_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__timg__GetOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__GetOptions(struct soap *soap, struct _timg__GetOptions *timg__GetOptions, struct _timg__GetOptionsResponse *timg__GetOptionsResponse)
{
    static struct tt__ImagingOptions20 stImagingOptions = {0};
    static struct tt__BacklightCompensationOptions20 stBacklightCompensation = {0};
    static enum tt__BacklightCompensationMode aenBacklightMode[2] = {tt__BacklightCompensationMode__OFF, tt__BacklightCompensationMode__ON};
    static struct tt__FloatRange stBrightness = {0};
    static struct tt__FloatRange stColorSaturation = {0};
    static struct tt__FloatRange stContrast = {0};
    static struct tt__ExposureOptions20 stExposure = {0};
    static enum tt__ExposureMode enMode = tt__ExposureMode__AUTO;
    static struct tt__FloatRange stMinExposureTime = {0};
    static struct tt__FloatRange stMaxExposureTime = {0};
    static struct tt__FloatRange stMinGain = {0};
    static struct tt__FloatRange stMaxGain = {0};
    static enum tt__IrCutFilterMode aenIrCutFilterModes[3] = {tt__IrCutFilterMode__ON, tt__IrCutFilterMode__OFF, tt__IrCutFilterMode__AUTO};
    static struct tt__FloatRange stSharpness = {0};
    static struct tt__WhiteBalanceOptions20 stWhiteBalance = {0};
    static enum tt__WhiteBalanceMode aenWBMode[2] = {tt__WhiteBalanceMode__AUTO, tt__WhiteBalanceMode__MANUAL};
    static struct tt__FloatRange stYrGain = {0};
    static struct tt__FloatRange stYbGain = {0};

    print_level(SV_DEBUG, "dummy: %s \n", timg__GetOptions->VideoSourceToken);
    timg__GetOptionsResponse->ImagingOptions = &stImagingOptions;
    stImagingOptions.BacklightCompensation = &stBacklightCompensation;
    stImagingOptions.Brightness = &stBrightness;
    stBrightness.Min = 0.0;
    stBrightness.Max = 100.0;
    stImagingOptions.ColorSaturation = &stColorSaturation;
    stColorSaturation.Min = 0.0;
    stColorSaturation.Max = 100.0;
    stImagingOptions.Contrast = &stContrast;
    stContrast.Min = 0.0;
    stContrast.Max = 100.0;
    stImagingOptions.Exposure = &stExposure;
    stExposure.__sizeMode = 1;
    stExposure.Mode = &enMode;
    stExposure.MinExposureTime = &stMinExposureTime;
    stMinExposureTime.Min = 10.0;
    stMinExposureTime.Max = 10.0;
    stExposure.MaxExposureTime = &stMaxExposureTime;
    stMaxExposureTime.Min = 10.0;
    stMaxExposureTime.Max = 10000.0;
    stExposure.MinGain = &stMinGain;
    stMinGain.Min = 1.0;
    stMinGain.Max = 1.0;
    stExposure.MaxGain = &stMaxGain;
    stMaxGain.Min = 6.0;
    stMaxGain.Max = 42.0;
    stImagingOptions.__sizeIrCutFilterModes = 3;
    stImagingOptions.IrCutFilterModes = &aenIrCutFilterModes[0];
    stImagingOptions.Sharpness = &stSharpness;
    stSharpness.Min = 0.0;
    stSharpness.Max = 100.0;
    stImagingOptions.WhiteBalance = &stWhiteBalance;
    stWhiteBalance.__sizeMode = 2;
    stWhiteBalance.Mode = &aenWBMode[0];
    stWhiteBalance.YrGain = &stYrGain;
    stYrGain.Min = 0.0;
    stYrGain.Max = 100.0;
    stWhiteBalance.YbGain = &stYbGain;
    stYbGain.Min = 0.0;
    stYbGain.Max = 100.0;
    stBacklightCompensation.__sizeMode = 2;
    stBacklightCompensation.Mode = &aenBacklightMode[0];

    return SOAP_OK;
}

/** Web service operation '__timg__Move' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__Move(struct soap *soap, struct _timg__Move *timg__Move, struct _timg__MoveResponse *timg__MoveResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__timg__Stop' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__Stop(struct soap *soap, struct _timg__Stop *timg__Stop, struct _timg__StopResponse *timg__StopResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__timg__GetStatus' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__GetStatus(struct soap *soap, struct _timg__GetStatus *timg__GetStatus, struct _timg__GetStatusResponse *timg__GetStatusResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__timg__GetMoveOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __timg__GetMoveOptions(struct soap *soap, struct _timg__GetMoveOptions *timg__GetMoveOptions, struct _timg__GetMoveOptionsResponse *timg__GetMoveOptionsResponse)
{
    static struct tt__MoveOptions20 stMoveOptions = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    timg__GetMoveOptionsResponse->MoveOptions = &stMoveOptions;

    return SOAP_OK;
}

/** Web service operation '__tptz__GetServiceCapabilities' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetServiceCapabilities(struct soap *soap, struct _tptz__GetServiceCapabilities *tptz__GetServiceCapabilities, struct _tptz__GetServiceCapabilitiesResponse *tptz__GetServiceCapabilitiesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GetConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetConfigurations(struct soap *soap, struct _tptz__GetConfigurations *tptz__GetConfigurations, struct _tptz__GetConfigurationsResponse *tptz__GetConfigurationsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GetPresets' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetPresets(struct soap *soap, struct _tptz__GetPresets *tptz__GetPresets, struct _tptz__GetPresetsResponse *tptz__GetPresetsResponse)
{
    sint32 i;
    static struct tt__PTZPreset astPreset[32];
    static char aszNames[32][16];
    static char aszTokens[32][16];

    print_level(SV_DEBUG, "dummy:  \n");
    if (BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() == BOARD_HDW845V1_V1 \
    || (BOARD_GetVersion() == BOARD_IPCR20S3_V1 && BOARD_IsCustomer(BOARD_C_IPCR20S3_202120)))
    {
        tptz__GetPresetsResponse->__sizePreset = 32;
        tptz__GetPresetsResponse->Preset = astPreset;
        for (i = 0; i < tptz__GetPresetsResponse->__sizePreset; i++)
        {
            astPreset[i].Name = &aszNames[i];
            astPreset[i].token = &aszTokens[i];
            sprintf(astPreset[i].Name, "%d", i);
            sprintf(astPreset[i].token, "%d", i);
        }
    }
    else
    {
        return SOAP_NO_METHOD;
    }
    return SOAP_OK;
}

/** Web service operation '__tptz__SetPreset' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__SetPreset(struct soap *soap, struct _tptz__SetPreset *tptz__SetPreset, struct _tptz__SetPresetResponse *tptz__SetPresetResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__RemovePreset' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__RemovePreset(struct soap *soap, struct _tptz__RemovePreset *tptz__RemovePreset, struct _tptz__RemovePresetResponse *tptz__RemovePresetResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GotoPreset' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GotoPreset(struct soap *soap, struct _tptz__GotoPreset *tptz__GotoPreset, struct _tptz__GotoPresetResponse *tptz__GotoPresetResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GetStatus' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetStatus(struct soap *soap, struct _tptz__GetStatus *tptz__GetStatus, struct _tptz__GetStatusResponse *tptz__GetStatusResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GetConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetConfiguration(struct soap *soap, struct _tptz__GetConfiguration *tptz__GetConfiguration, struct _tptz__GetConfigurationResponse *tptz__GetConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GetNodes' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetNodes(struct soap *soap, struct _tptz__GetNodes *tptz__GetNodes, struct _tptz__GetNodesResponse *tptz__GetNodesResponse)
{
    static struct tt__PTZNode stPTZNode = {0};
    static struct tt__PTZSpaces stSupportedPTZSpaces = {0};
    static struct tt__Space2DDescription stRelativePanTiltTranslationSpace = {0};
    static struct tt__Space1DDescription stRelativeZoomTranslationSpace = {0};
    static struct tt__Space2DDescription stContinuousPanTiltVelocitySpace = {0};
    static struct tt__Space1DDescription stContinuousZoomVelocitySpace = {0};
    static struct tt__Space1DDescription stPanTiltSpeedSpace = {0};
    static struct tt__Space1DDescription stZoomSpeedSpace = {0};
    static struct tt__FloatRange stRange1 = {-1, 1};
    static struct tt__FloatRange stRange2 = {0, 1};
    static struct tt__FloatRange stRange3 = {1, 8};

    const char * auxCmd[] = {"Shutter|Open", "Shutter|Close"};

    print_level(SV_DEBUG, "dummy:  \n");
    if (BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() != BOARD_HDW845V1_V1)
    {
        tptz__GetNodesResponse->__sizePTZNode = 1;
        tptz__GetNodesResponse->PTZNode = &stPTZNode;
        stPTZNode.token = "000";
        stPTZNode.Name = "NOD_000";
        stPTZNode.SupportedPTZSpaces = &stSupportedPTZSpaces;
        stPTZNode.MaximumNumberOfPresets = 255;
        stPTZNode.HomeSupported = _true;
        stSupportedPTZSpaces.__sizeRelativePanTiltTranslationSpace = 1;
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace = &stRelativePanTiltTranslationSpace;
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/TranslationGenericSpace";
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace->XRange = &stRange1;
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace->YRange = &stRange1;
        stSupportedPTZSpaces.__sizeRelativeZoomTranslationSpace = 1;
        stSupportedPTZSpaces.RelativeZoomTranslationSpace = &stRelativeZoomTranslationSpace;
        stSupportedPTZSpaces.RelativeZoomTranslationSpace->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/TranslationGenericSpace";
        stSupportedPTZSpaces.RelativeZoomTranslationSpace->XRange = &stRange2;
        stSupportedPTZSpaces.__sizeContinuousPanTiltVelocitySpace = 1;
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace = &stContinuousPanTiltVelocitySpace;
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace";
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace->XRange = &stRange1;
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace->YRange = &stRange1;
        stSupportedPTZSpaces.__sizeContinuousZoomVelocitySpace = 1;
        stSupportedPTZSpaces.ContinuousZoomVelocitySpace = &stContinuousZoomVelocitySpace;
        stSupportedPTZSpaces.ContinuousZoomVelocitySpace->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/VelocityGenericSpace";
        stSupportedPTZSpaces.ContinuousZoomVelocitySpace->XRange = &stRange1;
        stSupportedPTZSpaces.__sizePanTiltSpeedSpace = 1;
        stSupportedPTZSpaces.PanTiltSpeedSpace = &stPanTiltSpeedSpace;
        stSupportedPTZSpaces.PanTiltSpeedSpace->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/GenericSpeedSpace";
        stSupportedPTZSpaces.PanTiltSpeedSpace->XRange = &stRange3;
        stSupportedPTZSpaces.__sizeZoomSpeedSpace = 1;
        stSupportedPTZSpaces.ZoomSpeedSpace = &stZoomSpeedSpace;
        stSupportedPTZSpaces.ZoomSpeedSpace->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/ZoomGenericSpeedSpace";
        stSupportedPTZSpaces.ZoomSpeedSpace->XRange = &stRange3;
    }
    else if (BOARD_GetVersion() == BOARD_IPCR20S3_V1 && BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
    {
        //char * auxCmd[2];
        //auxCmd[0] = "Shutter|Open";
        //auxCmd[1] = "Shutter|Close";
        //print_level(SV_WARN, "auxCmd assign complete.\n");
        tptz__GetNodesResponse->__sizePTZNode = 1;
        tptz__GetNodesResponse->PTZNode = &stPTZNode;
        stPTZNode.token = "000";
        stPTZNode.Name = "NOD_000";
        stPTZNode.__sizeAuxiliaryCommands = 2;
        stPTZNode.AuxiliaryCommands = &auxCmd;
    }
    else
    {
        return SOAP_NO_METHOD;
    }
    return SOAP_OK;
}

/** Web service operation '__tptz__GetNode' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetNode(struct soap *soap, struct _tptz__GetNode *tptz__GetNode, struct _tptz__GetNodeResponse *tptz__GetNodeResponse)
{
    static struct tt__PTZNode stPTZNode = {0};
    static struct tt__PTZSpaces stSupportedPTZSpaces = {0};
    static struct tt__Space2DDescription stRelativePanTiltTranslationSpace = {0};
    static struct tt__Space1DDescription stRelativeZoomTranslationSpace = {0};
    static struct tt__Space2DDescription stContinuousPanTiltVelocitySpace = {0};
    static struct tt__Space1DDescription stContinuousZoomVelocitySpace = {0};
    static struct tt__Space1DDescription stPanTiltSpeedSpace = {0};
    static struct tt__Space1DDescription stZoomSpeedSpace = {0};
    static struct tt__FloatRange stRange1 = {-1, 1};
    static struct tt__FloatRange stRange2 = {0, 1};
    static struct tt__FloatRange stRange3 = {1, 8};

    const char * auxCmd[] = {"Shutter|Open", "Shutter|Close"};

    print_level(SV_DEBUG, "dummy: %s  \n", tptz__GetNode->NodeToken);
    if (BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() == BOARD_HDW845V1_V1)
    {
        tptz__GetNodeResponse->PTZNode = &stPTZNode;
        stPTZNode.token = "000";
        stPTZNode.Name = "NOD_000";
        stPTZNode.SupportedPTZSpaces = &stSupportedPTZSpaces;
        stPTZNode.MaximumNumberOfPresets = 255;
        stPTZNode.HomeSupported = _true;
        stSupportedPTZSpaces.__sizeRelativePanTiltTranslationSpace = 1;
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace = &stRelativePanTiltTranslationSpace;
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/TranslationGenericSpace";
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace->XRange = &stRange1;
        stSupportedPTZSpaces.RelativePanTiltTranslationSpace->YRange = &stRange1;
        stSupportedPTZSpaces.__sizeRelativeZoomTranslationSpace = 1;
        stSupportedPTZSpaces.RelativeZoomTranslationSpace = &stRelativeZoomTranslationSpace;
        stSupportedPTZSpaces.RelativeZoomTranslationSpace->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/TranslationGenericSpace";
        stSupportedPTZSpaces.RelativeZoomTranslationSpace->XRange = &stRange2;
        stSupportedPTZSpaces.__sizeContinuousPanTiltVelocitySpace = 1;
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace = &stContinuousPanTiltVelocitySpace;
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace";
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace->XRange = &stRange1;
        stSupportedPTZSpaces.ContinuousPanTiltVelocitySpace->YRange = &stRange1;
        stSupportedPTZSpaces.__sizeContinuousZoomVelocitySpace = 1;
        stSupportedPTZSpaces.ContinuousZoomVelocitySpace = &stContinuousZoomVelocitySpace;
        stSupportedPTZSpaces.ContinuousZoomVelocitySpace->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/VelocityGenericSpace";
        stSupportedPTZSpaces.ContinuousZoomVelocitySpace->XRange = &stRange1;
        stSupportedPTZSpaces.__sizePanTiltSpeedSpace = 1;
        stSupportedPTZSpaces.PanTiltSpeedSpace = &stPanTiltSpeedSpace;
        stSupportedPTZSpaces.PanTiltSpeedSpace->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/GenericSpeedSpace";
        stSupportedPTZSpaces.PanTiltSpeedSpace->XRange = &stRange3;
        stSupportedPTZSpaces.__sizeZoomSpeedSpace = 1;
        stSupportedPTZSpaces.ZoomSpeedSpace = &stZoomSpeedSpace;
        stSupportedPTZSpaces.ZoomSpeedSpace->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/ZoomGenericSpeedSpace";
        stSupportedPTZSpaces.ZoomSpeedSpace->XRange = &stRange3;
    }
    else if (BOARD_GetVersion() == BOARD_IPCR20S3_V1 && BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
    {
        //char * auxCmd[2];
        //auxCmd[0] = "Shutter|Open";
        //auxCmd[1] = "Shutter|Close";
        //print_level(SV_WARN, "auxCmd assign complete.\n");

        tptz__GetNodeResponse->PTZNode = &stPTZNode;
        stPTZNode.token = "000";
        stPTZNode.Name = "NOD_000";
        stPTZNode.__sizeAuxiliaryCommands = 2;
        stPTZNode.AuxiliaryCommands = &auxCmd;
    }
    else
    {
        return SOAP_NO_METHOD;
    }

    return SOAP_OK;
}

/** Web service operation '__tptz__SetConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__SetConfiguration(struct soap *soap, struct _tptz__SetConfiguration *tptz__SetConfiguration, struct _tptz__SetConfigurationResponse *tptz__SetConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GetConfigurationOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GetConfigurationOptions(struct soap *soap, struct _tptz__GetConfigurationOptions *tptz__GetConfigurationOptions, struct _tptz__GetConfigurationOptionsResponse *tptz__GetConfigurationOptionsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__GotoHomePosition' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__GotoHomePosition(struct soap *soap, struct _tptz__GotoHomePosition *tptz__GotoHomePosition, struct _tptz__GotoHomePositionResponse *tptz__GotoHomePositionResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__SetHomePosition' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__SetHomePosition(struct soap *soap, struct _tptz__SetHomePosition *tptz__SetHomePosition, struct _tptz__SetHomePositionResponse *tptz__SetHomePositionResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__ContinuousMove' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__ContinuousMove(struct soap *soap, struct _tptz__ContinuousMove *tptz__ContinuousMove, struct _tptz__ContinuousMoveResponse *tptz__ContinuousMoveResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0};
    PTZ_CTRL_S stPtzCtrl = {0};

    print_level(SV_DEBUG, "dummy: %s, Timeout:%s\n", tptz__ContinuousMove->ProfileToken, tptz__ContinuousMove->Timeout);
    if (BOARD_GetVersion() != BOARD_ADA47V1_V1 && BOARD_GetVersion() != BOARD_HDW845V1_V1)
    {
        return SOAP_NO_METHOD;
    }

    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            return s32Ret;
        }
    }

    if (NULL != tptz__ContinuousMove->Velocity)
    {
        if (NULL != tptz__ContinuousMove->Velocity->PanTilt)
        {
            print_level(SV_INFO, "PanTilt: (%f, %f)\n", tptz__ContinuousMove->Velocity->PanTilt->x, tptz__ContinuousMove->Velocity->PanTilt->y);
            stPtzCtrl.enCmd = PTZ_OP_PANTILT;
            stPtzCtrl.fXStep = tptz__ContinuousMove->Velocity->PanTilt->x;
            stPtzCtrl.fYStep = tptz__ContinuousMove->Velocity->PanTilt->y;
        }

        if (NULL != tptz__ContinuousMove->Velocity->Zoom)
        {
            print_level(SV_INFO, "Zoom: %f\n", tptz__ContinuousMove->Velocity->Zoom->x);
            stPtzCtrl.fZStep = tptz__ContinuousMove->Velocity->Zoom->x;
            #if (defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1))
            stPtzCtrl.enCmd = stPtzCtrl.fZStep > 0 ? PTZ_OP_IN : PTZ_OP_OUT;
            #else
            stPtzCtrl.enCmd = PTZ_OP_ZOOM;//hisi3516e
            #endif
        }
    }

    stMsgPkt.pu8Data = (uint8 *)&stPtzCtrl;
    stMsgPkt.u32Size = sizeof(PTZ_CTRL_S);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_PTZ, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_PTZ failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__tptz__RelativeMove' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__RelativeMove(struct soap *soap, struct _tptz__RelativeMove *tptz__RelativeMove, struct _tptz__RelativeMoveResponse *tptz__RelativeMoveResponse)
{
    print_level(SV_DEBUG, "dummy: %s\n", tptz__RelativeMove->ProfileToken);
    if (BOARD_GetVersion() != BOARD_ADA47V1_V1 && BOARD_GetVersion() != BOARD_HDW845V1_V1)
    {
        return SOAP_NO_METHOD;
    }

    if (NULL != tptz__RelativeMove->Translation)
    {
        if (NULL != tptz__RelativeMove->Translation->PanTilt)
        {
            print_level(SV_INFO, "PanTilt: (%f, %f)\n", tptz__RelativeMove->Translation->PanTilt->x, tptz__RelativeMove->Translation->PanTilt->y);
        }

        if (NULL != tptz__RelativeMove->Translation->Zoom)
        {
            print_level(SV_INFO, "Zoom: %f\n", tptz__RelativeMove->Translation->Zoom->x);
        }
    }

    if (NULL != tptz__RelativeMove->Speed)
    {
        if (NULL != tptz__RelativeMove->Speed->PanTilt)
        {
            print_level(SV_INFO, "PanTilt: (%f, %f)\n", tptz__RelativeMove->Speed->PanTilt->x, tptz__RelativeMove->Speed->PanTilt->y);
        }

        if (NULL != tptz__RelativeMove->Speed->Zoom)
        {
            print_level(SV_INFO, "Zoom: %f\n", tptz__RelativeMove->Speed->Zoom->x);
        }
    }

    return SOAP_OK;
}

/** Web service operation '__tptz__SendAuxiliaryCommand' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__SendAuxiliaryCommand(struct soap *soap, struct _tptz__SendAuxiliaryCommand *tptz__SendAuxiliaryCommand, struct _tptz__SendAuxiliaryCommandResponse *tptz__SendAuxiliaryCommandResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0};
    uint8 u8ShutterCtrl = 0;
    const char * auxCmd[] = {"Shutter|Open", "Shutter|Close"};

    print_level(SV_DEBUG, "dummy: %s\n", tptz__SendAuxiliaryCommand->ProfileToken);
    print_level(SV_DEBUG, "receive AuxData: %s\n", tptz__SendAuxiliaryCommand->AuxiliaryData);
    if (BOARD_GetVersion() != BOARD_IPCR20S3_V1 || BOARD_IsNotCustomer(BOARD_C_IPCR20S3_202120))
    {
        return SOAP_NO_METHOD;
    }

    if (0 == strcmp(tptz__SendAuxiliaryCommand->AuxiliaryData, auxCmd[0]))
    {
        u8ShutterCtrl = 1;
    }
    else if (0 == strcmp(tptz__SendAuxiliaryCommand->AuxiliaryData, auxCmd[1]))
    {
        u8ShutterCtrl = 0;
    }

    stMsgPkt.pu8Data = &u8ShutterCtrl;
    stMsgPkt.u32Size = sizeof(uint8);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SHUTTER, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SHUTTER failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__tptz__AbsoluteMove' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__AbsoluteMove(struct soap *soap, struct _tptz__AbsoluteMove *tptz__AbsoluteMove, struct _tptz__AbsoluteMoveResponse *tptz__AbsoluteMoveResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__tptz__Stop' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __tptz__Stop(struct soap *soap, struct _tptz__Stop *tptz__Stop, struct _tptz__StopResponse *tptz__StopResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0};
    PTZ_CTRL_S stPtzCtrl = {0};

    print_level(SV_DEBUG, "dummy: %s \n", tptz__Stop->ProfileToken);
    if (BOARD_GetVersion() != BOARD_ADA47V1_V1 && BOARD_GetVersion() != BOARD_HDW845V1_V1)
    {
        return SOAP_NO_METHOD;
    }

    if (NULL != tptz__Stop->PanTilt)
    {
        print_level(SV_INFO, "PanTilt: %d\n", *tptz__Stop->PanTilt);
    }

    if (NULL != tptz__Stop->Zoom)
    {
        print_level(SV_INFO, "Zoom: %d\n", *tptz__Stop->Zoom);
    }

    stPtzCtrl.enCmd = PTZ_OP_STOP;
    stMsgPkt.pu8Data = (uint8 *)&stPtzCtrl;
    stMsgPkt.u32Size = sizeof(PTZ_CTRL_S);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_PTZ, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_PTZ failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__trt__GetServiceCapabilities' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetServiceCapabilities(struct soap *soap, struct _trt__GetServiceCapabilities *trt__GetServiceCapabilities, struct _trt__GetServiceCapabilitiesResponse *trt__GetServiceCapabilitiesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetVideoSources' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetVideoSources(struct soap *soap, struct _trt__GetVideoSources *trt__GetVideoSources, struct _trt__GetVideoSourcesResponse *trt__GetVideoSourcesResponse)
{
    sint32 s32Ret = 0, i;
    uint32 u32StreamNum = 1;
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    static struct tt__VideoSource stVideoSources;
    static struct tt__VideoResolution stResolution;

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    trt__GetVideoSourcesResponse->__sizeVideoSources = u32StreamNum;
    trt__GetVideoSourcesResponse->VideoSources = &stVideoSources;

    stVideoSources.token = "VideoSource_1";
    stVideoSources.Framerate = stVideoCfg.astChnParam[0].u32MainFramerate;
    stVideoSources.Resolution = &stResolution;
    stResolution.Width = stVideoCfg.astChnParam[0].u32MainWidth;
    stResolution.Height = stVideoCfg.astChnParam[0].u32MainHeight;


    return SOAP_OK;
}

/** Web service operation '__trt__GetAudioSources' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetAudioSources(struct soap *soap, struct _trt__GetAudioSources *trt__GetAudioSources, struct _trt__GetAudioSourcesResponse *trt__GetAudioSourcesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__CreateProfile' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__CreateProfile(struct soap *soap, struct _trt__CreateProfile *trt__CreateProfile, struct _trt__CreateProfileResponse *trt__CreateProfileResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetProfile' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetProfile(struct soap *soap, struct _trt__GetProfile *trt__GetProfile, struct _trt__GetProfileResponse *trt__GetProfileResponse)
{
    sint32 s32Ret = 0, i;
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__Profile stProfiles = {0};
    static struct tt__VideoSourceConfiguration stVideoSourceConfiguration = {0};
    static struct tt__VideoEncoderConfiguration stVideoEncoderConfiguration = {0};
    static struct tt__AudioSourceConfiguration stAudioSourceConfiguration = {0};
    static struct tt__AudioEncoderConfiguration stAudioEncoderConfiguration = {0};
    static struct tt__PTZConfiguration stPTZConfiguration = {0};
    static struct tt__MetadataConfiguration stMetadataConfiguration = {0};
    static struct tt__IntRectangle stBounds = {0};
    static struct tt__VideoResolution stResolution = {0};
    static struct tt__VideoRateControl stRateControl = {0};
    static struct tt__H264Configuration stH264 = {0};
    static struct tt__MulticastConfiguration stMulticast = {0};
    static struct tt__IPAddress stAddress = {0};
    static enum xsd__boolean_ enFixed;
    static struct tt__PTZSpeed stDefaultPTZSpeed = {0};
    static struct tt__Vector2D stPanTilt = {0};
    static struct tt__Vector1D stZoom = {0};
    static struct tt__PanTiltLimits stPanTiltLimits = {0};
    static struct tt__Space2DDescription st2DRange = {0};
    static struct tt__FloatRange stValRange = {-1, 1};
    static struct tt__ZoomLimits stZoomLimits = {0};
    static struct tt__Space1DDescription st1DRange = {0};

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "dummy:  %s\n", trt__GetProfile->ProfileToken);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    if (0 == strcmp(trt__GetProfile->ProfileToken, "MediaProfile000"))
    {
        i = 0;
    }
    else if (0 == strcmp(trt__GetProfile->ProfileToken, "MediaProfile001"))
    {
        i = 1;
    }
    else
    {
        i = 2;
    }

    trt__GetProfileResponse->Profile = &stProfiles;
    stProfiles.Name = (i == 0) ? "Profile_Chn1_MainStream" : ((i == 1) ? "Profile_Chn1_SubStream" : "Profile_Chn1_PicStream");
    stProfiles.token = (i == 0) ? "MediaProfile000" : ((i == 1) ? "MediaProfile001" : "MediaProfile002");
    stProfiles.fixed = &enFixed;
    *stProfiles.fixed = _true;
    stProfiles.VideoSourceConfiguration = &stVideoSourceConfiguration;
    stProfiles.VideoEncoderConfiguration = &stVideoEncoderConfiguration;
    stProfiles.AudioSourceConfiguration = (i == 0) ? &stAudioSourceConfiguration : NULL;
    stProfiles.AudioEncoderConfiguration = (i == 0) ? &stAudioEncoderConfiguration : NULL;
    stVideoSourceConfiguration.Name = "VideoSource_Chn1";
    stVideoSourceConfiguration.token = "VideoSource_1";
    stVideoSourceConfiguration.UseCount = 3;
    stVideoSourceConfiguration.SourceToken = "VideoSource_1";
    stVideoSourceConfiguration.Bounds = &stBounds;
    stVideoSourceConfiguration.Bounds->x = 0;
    stVideoSourceConfiguration.Bounds->y = 0;
    stVideoSourceConfiguration.Bounds->width = stVideoCfg.astChnParam[0].u32MainWidth;
    stVideoSourceConfiguration.Bounds->height = stVideoCfg.astChnParam[0].u32MainHeight;
    stVideoEncoderConfiguration.Name = (i == 0) ? "VideoEncoder_Chn1_MainStream" : ((i == 1) ? "VideoEncoder_Chn1_SubStream" : "VideoEncoder_Chn1_PicStream");
    stVideoEncoderConfiguration.token = (i == 0) ? "VideoEncoder000" : ((i == 1) ? "VideoEncoder001" : "VideoEncoder002");
    stVideoEncoderConfiguration.UseCount = 1;
    stVideoEncoderConfiguration.Encoding = (i < 2) ? tt__VideoEncoding__H264 : tt__VideoEncoding__JPEG;
    stVideoEncoderConfiguration.Resolution = &stResolution;
    stVideoEncoderConfiguration.Resolution->Width = (i == 0) ? stVideoCfg.astChnParam[0].u32MainWidth : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubWidth : stVideoCfg.astChnParam[0].u32JpegWidth);
    stVideoEncoderConfiguration.Resolution->Height = (i == 0) ? stVideoCfg.astChnParam[0].u32MainHeight : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubHeight : stVideoCfg.astChnParam[0].u32JpegHeight);
    stVideoEncoderConfiguration.Quality = (i == 0) ? 6.0 : 4.0;
    stVideoEncoderConfiguration.RateControl = &stRateControl;
    stVideoEncoderConfiguration.RateControl->FrameRateLimit = (i == 0) ? stVideoCfg.astChnParam[0].u32MainFramerate : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubFramerate : 0);
    stVideoEncoderConfiguration.RateControl->EncodingInterval = 1;
    stVideoEncoderConfiguration.RateControl->BitrateLimit = (i == 0) ? stVideoCfg.astChnParam[0].u32MainBitrate : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubBitrate : 0);
    if (i < 2)
    {
        stVideoEncoderConfiguration.H264 = &stH264;
        stVideoEncoderConfiguration.H264->GovLength = (i == 0) ? stVideoCfg.astChnParam[0].u32MainIfrmInterval : stVideoCfg.astChnParam[0].u32SubIfrmInterval;
        stVideoEncoderConfiguration.H264->H264Profile = tt__H264Profile__Baseline;
    }

    if (0 == 1)
    {
        stVideoEncoderConfiguration.Multicast = &stMulticast;
        stVideoEncoderConfiguration.Multicast->Address = &stAddress;
        stVideoEncoderConfiguration.Multicast->Address->Type = tt__IPType__IPv4;
        stVideoEncoderConfiguration.Multicast->Address->IPv4Address = "*********";
        stVideoEncoderConfiguration.Multicast->Port = stNetworkCfg.u32MulticastPort;
        stVideoEncoderConfiguration.Multicast->TTL = 64;
        stVideoEncoderConfiguration.Multicast->AutoStart = _false;
    }
    stVideoEncoderConfiguration.SessionTimeout = "PT60S";

    stAudioSourceConfiguration.Name = "A_SRC_000";
    stAudioSourceConfiguration.UseCount = 2;
    stAudioSourceConfiguration.token = "A_SRC_000";
    stAudioSourceConfiguration.SourceToken = "AudioSourceToken";
    stAudioEncoderConfiguration.Name = "A_ENC_000";
    stAudioEncoderConfiguration.UseCount = 2;
    stAudioEncoderConfiguration.token = "A_ENC_000";
    stAudioEncoderConfiguration.Encoding = (AUD_ENC_G711A == stVideoCfg.enAudioEncType || AUD_ENC_G711U == stVideoCfg.enAudioEncType) ? tt__AudioEncoding__G711 : tt__AudioEncoding__LPCM;
    stAudioEncoderConfiguration.Bitrate = 256;
    stAudioEncoderConfiguration.SampleRate = (stVideoCfg.enAudioSampleRate == AUD_SR_8K) ? 8 : 16;
    stAudioEncoderConfiguration.SessionTimeout = "PT3600000S";
    if (BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() == BOARD_HDW845V1_V1)
    {
        stProfiles.PTZConfiguration = &stPTZConfiguration;
        stPTZConfiguration.Name = "PTZ_000";
        stPTZConfiguration.UseCount = 2;
        stPTZConfiguration.token = "000";
        stPTZConfiguration.NodeToken = "000";
        stPTZConfiguration.DefaultRelativePanTiltTranslationSpace = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/TranslationGenericSpace";
        stPTZConfiguration.DefaultRelativeZoomTranslationSpace = "http://www.onvif.org/ver10/tptz/ZoomSpaces/TranslationGenericSpace";
        stPTZConfiguration.DefaultContinuousPanTiltVelocitySpace = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace";
        stPTZConfiguration.DefaultContinuousZoomVelocitySpace = "http://www.onvif.org/ver10/tptz/ZoomSpaces/VelocityGenericSpace";
        stPTZConfiguration.DefaultPTZSpeed = &stDefaultPTZSpeed;
        stPTZConfiguration.DefaultPTZSpeed->PanTilt = &stPanTilt;
        stPTZConfiguration.DefaultPTZSpeed->PanTilt->x = 1;
        stPTZConfiguration.DefaultPTZSpeed->PanTilt->y = 1;
        stPTZConfiguration.DefaultPTZSpeed->PanTilt->space = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/GenericSpeedSpace";
        stPTZConfiguration.DefaultPTZSpeed->Zoom = &stZoom;
        stPTZConfiguration.DefaultPTZSpeed->Zoom->x = 1;
        stPTZConfiguration.DefaultPTZSpeed->Zoom->space = "http://www.onvif.org/ver10/tptz/ZoomSpaces/ZoomGenericSpeedSpace";
        stPTZConfiguration.PanTiltLimits = &stPanTiltLimits;
        stPTZConfiguration.PanTiltLimits->Range = &st2DRange;
        stPTZConfiguration.PanTiltLimits->Range->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/PositionGenericSpace";
        stPTZConfiguration.PanTiltLimits->Range->XRange = &stValRange;
        stPTZConfiguration.PanTiltLimits->Range->YRange = &stValRange;
        stPTZConfiguration.ZoomLimits = &stZoomLimits;
        stPTZConfiguration.ZoomLimits->Range = &st1DRange;
        stPTZConfiguration.ZoomLimits->Range->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/PositionGenericSpace";
        stPTZConfiguration.ZoomLimits->Range->XRange = &stValRange;
    }
    else if (BOARD_GetVersion() == BOARD_IPCR20S3_V1 && BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
    {
        stProfiles.PTZConfiguration = &stPTZConfiguration;
        stPTZConfiguration.Name = "PTZ_000";
        stPTZConfiguration.UseCount = 2;
        stPTZConfiguration.token = "000";
        stPTZConfiguration.NodeToken = "000";
    }

#if (defined(BOARD_IPCR20S3))
    stProfiles.MetadataConfiguration = &stMetadataConfiguration;
    stMetadataConfiguration.Name = "Metadata";
    stMetadataConfiguration.UseCount = 1;
    stMetadataConfiguration.token = "Metadata";
    stMetadataConfiguration.SessionTimeout = "PT60S";
    stMetadataConfiguration.Multicast = &stMulticast;
#endif

    return SOAP_OK;
}

/** Web service operation '__trt__GetProfiles' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetProfiles(struct soap *soap, struct _trt__GetProfiles *trt__GetProfiles, struct _trt__GetProfilesResponse *trt__GetProfilesResponse)
{
    sint32 s32Ret = 0, i;
    uint32 u32ProfileNum = 1;
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__Profile astProfiles[3] = {0};
    static struct tt__VideoSourceConfiguration stVideoSourceConfiguration = {0};
    static struct tt__VideoEncoderConfiguration astVideoEncoderConfiguration[3] = {0};
    static struct tt__AudioSourceConfiguration stAudioSourceConfiguration = {0};
    static struct tt__AudioEncoderConfiguration stAudioEncoderConfiguration = {0};
    static struct tt__PTZConfiguration stPTZConfiguration = {0};
    static struct tt__MetadataConfiguration stMetadataConfiguration = {0};
    static struct tt__IntRectangle stBounds = {0};
    static struct tt__VideoResolution astResolution[3] = {0};
    static struct tt__VideoRateControl astRateControl[3] = {0};
    static struct tt__H264Configuration astH264[3] = {0};
    static struct tt__MulticastConfiguration astMulticast[3] = {0};
    static struct tt__IPAddress astAddress[3] = {0};
    static enum xsd__boolean_ enFixed[3];
    static struct tt__PTZSpeed stDefaultPTZSpeed = {0};
    static struct tt__Vector2D stPanTilt = {0};
    static struct tt__Vector1D stZoom = {0};
    static struct tt__PanTiltLimits stPanTiltLimits = {0};
    static struct tt__Space2DDescription st2DRange = {0};
    static struct tt__FloatRange stValRange = {-1, 1};
    static struct tt__ZoomLimits stZoomLimits = {0};
    static struct tt__Space1DDescription st1DRange = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "dummy:  \n");

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_IPTR20S1))
    u32ProfileNum = 3;
#elif (defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4))
    u32ProfileNum = 2;
#elif (defined(BOARD_WFTR20S3))
    if ( BOARD_WFTR20S3_V1 == BOARD_GetVersion())
    {
        u32ProfileNum = 2;
    }
#endif
    trt__GetProfilesResponse->__sizeProfiles = u32ProfileNum;
    trt__GetProfilesResponse->Profiles = astProfiles;
    for (i = 0; i < u32ProfileNum; i++)
    {
        /* Profiles */
        switch (i)
        {
            case 0:
                astProfiles[i].Name = "Profile_Chn1_MainStream";
                astProfiles[i].token = "MediaProfile000";
                break;
            case 1:
                astProfiles[i].Name = "Profile_Chn1_SubStream";
                astProfiles[i].token = "MediaProfile001";
                break;
            case 2:
                astProfiles[i].Name = "Profile_Chn1_PicStream";
                astProfiles[i].token = "MediaProfile002";
                break;
            default:
                return ERR_ILLEGAL_PARAM;
        }

        astProfiles[i].fixed = &enFixed[i];
        *astProfiles[i].fixed = _true;
        astProfiles[i].VideoSourceConfiguration = &stVideoSourceConfiguration;
        astProfiles[i].VideoEncoderConfiguration = &astVideoEncoderConfiguration[i];
        astProfiles[i].AudioSourceConfiguration = (i == 0) ? &stAudioSourceConfiguration : NULL;
        astProfiles[i].AudioEncoderConfiguration = (i == 0) ? &stAudioEncoderConfiguration : NULL;
        /* Video Source Configurations */
        stVideoSourceConfiguration.Name = "VideoSource_Chn1";
        stVideoSourceConfiguration.token = "VideoSource_1";
        stVideoSourceConfiguration.UseCount = 3;
        stVideoSourceConfiguration.SourceToken = "VideoSource_1";
        stVideoSourceConfiguration.Bounds = &stBounds;
        stVideoSourceConfiguration.Bounds->x = 0;
        stVideoSourceConfiguration.Bounds->y = 0;
        stVideoSourceConfiguration.Bounds->width = stVideoCfg.astChnParam[0].u32MainWidth;
        stVideoSourceConfiguration.Bounds->height = stVideoCfg.astChnParam[0].u32MainHeight;

        /* Video Encoder Configurations */
        astVideoEncoderConfiguration[i].Name = (i == 0) ? "VideoEncoder_Chn1_MainStream" : ((i == 1) ? "VideoEncoder_Chn1_SubStream": "VideoEncoder_Chn1_PicStream");
        astVideoEncoderConfiguration[i].token = (i == 0) ? "VideoEncoder000" : ((i == 1) ? "VideoEncoder001" : "VideoEncoder002");
        astVideoEncoderConfiguration[i].UseCount = 1;
        astVideoEncoderConfiguration[i].Encoding = (i < 2) ? tt__VideoEncoding__H264 : tt__VideoEncoding__JPEG;
        astVideoEncoderConfiguration[i].Resolution = &astResolution[i];
        astVideoEncoderConfiguration[i].Resolution->Width = (i == 0) ? stVideoCfg.astChnParam[0].u32MainWidth : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubWidth : stVideoCfg.astChnParam[0].u32JpegWidth);
        astVideoEncoderConfiguration[i].Resolution->Height = (i == 0) ? stVideoCfg.astChnParam[0].u32MainHeight : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubHeight : stVideoCfg.astChnParam[0].u32JpegHeight);
        astVideoEncoderConfiguration[i].Quality = (i == 0) ? 6.0 : 4.0;
        astVideoEncoderConfiguration[i].RateControl = &astRateControl[i];
        astVideoEncoderConfiguration[i].RateControl->FrameRateLimit = (i == 0) ? stVideoCfg.astChnParam[0].u32MainFramerate : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubFramerate : 0);
        astVideoEncoderConfiguration[i].RateControl->EncodingInterval = 1;
        astVideoEncoderConfiguration[i].RateControl->BitrateLimit = (i == 0) ? stVideoCfg.astChnParam[0].u32MainBitrate : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubBitrate : 0);
        if (i < 2)
        {
            astVideoEncoderConfiguration[i].H264 = &astH264[i];
            astVideoEncoderConfiguration[i].H264->GovLength = (i == 0) ? stVideoCfg.astChnParam[0].u32MainIfrmInterval : stVideoCfg.astChnParam[0].u32SubIfrmInterval;
            astVideoEncoderConfiguration[i].H264->H264Profile = tt__H264Profile__High;
        }

        if (0 == i)
        {
            astVideoEncoderConfiguration[i].Multicast = &astMulticast[i];
            astVideoEncoderConfiguration[i].Multicast->Address = &astAddress[i];
            astVideoEncoderConfiguration[i].Multicast->Address->Type = tt__IPType__IPv4;
            astVideoEncoderConfiguration[i].Multicast->Address->IPv4Address = "*********";
            astVideoEncoderConfiguration[i].Multicast->Port = stNetworkCfg.u32MulticastPort;
            astVideoEncoderConfiguration[i].Multicast->TTL = 64;
            astVideoEncoderConfiguration[i].Multicast->AutoStart = _false;
        }
        astVideoEncoderConfiguration[i].SessionTimeout = "PT60S";

        /* Audio */
        stAudioSourceConfiguration.Name = "A_SRC_000";
        stAudioSourceConfiguration.UseCount = 2;
        stAudioSourceConfiguration.token = "A_SRC_000";
        stAudioSourceConfiguration.SourceToken = "AudioSourceToken";
        stAudioEncoderConfiguration.Name = "A_ENC_000";
        stAudioEncoderConfiguration.UseCount = 2;
        stAudioEncoderConfiguration.token = "A_ENC_000";
        stAudioEncoderConfiguration.Encoding = (AUD_ENC_G711A == stVideoCfg.enAudioEncType || AUD_ENC_G711U == stVideoCfg.enAudioEncType) ? tt__AudioEncoding__G711 : tt__AudioEncoding__LPCM;
        stAudioEncoderConfiguration.Bitrate = 256;
        stAudioEncoderConfiguration.SampleRate = (stVideoCfg.enAudioSampleRate == AUD_SR_8K) ? 8 : 16;
        stAudioEncoderConfiguration.SessionTimeout = "PT3600000S";

        /* PTZ Control */
        if (BOARD_GetVersion() == BOARD_ADA47V1_V1 || BOARD_GetVersion() == BOARD_HDW845V1_V1)
        {
            astProfiles[i].PTZConfiguration = &stPTZConfiguration;
            stPTZConfiguration.Name = "PTZ_000";
            stPTZConfiguration.UseCount = 2;
            stPTZConfiguration.token = "000";
            stPTZConfiguration.NodeToken = "000";
            stPTZConfiguration.DefaultRelativePanTiltTranslationSpace = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/TranslationGenericSpace";
            stPTZConfiguration.DefaultRelativeZoomTranslationSpace = "http://www.onvif.org/ver10/tptz/ZoomSpaces/TranslationGenericSpace";
            stPTZConfiguration.DefaultContinuousPanTiltVelocitySpace = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace";
            stPTZConfiguration.DefaultContinuousZoomVelocitySpace = "http://www.onvif.org/ver10/tptz/ZoomSpaces/VelocityGenericSpace";
            stPTZConfiguration.DefaultPTZSpeed = &stDefaultPTZSpeed;
            stPTZConfiguration.DefaultPTZSpeed->PanTilt = &stPanTilt;
            stPTZConfiguration.DefaultPTZSpeed->PanTilt->x = 1;
            stPTZConfiguration.DefaultPTZSpeed->PanTilt->y = 1;
            stPTZConfiguration.DefaultPTZSpeed->PanTilt->space = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/GenericSpeedSpace";
            stPTZConfiguration.DefaultPTZSpeed->Zoom = &stZoom;
            stPTZConfiguration.DefaultPTZSpeed->Zoom->x = 1;
            stPTZConfiguration.DefaultPTZSpeed->Zoom->space = "http://www.onvif.org/ver10/tptz/ZoomSpaces/ZoomGenericSpeedSpace";
            stPTZConfiguration.PanTiltLimits = &stPanTiltLimits;
            stPTZConfiguration.PanTiltLimits->Range = &st2DRange;
            stPTZConfiguration.PanTiltLimits->Range->URI = "http://www.onvif.org/ver10/tptz/PanTiltSpaces/PositionGenericSpace";
            stPTZConfiguration.PanTiltLimits->Range->XRange = &stValRange;
            stPTZConfiguration.PanTiltLimits->Range->YRange = &stValRange;
            stPTZConfiguration.ZoomLimits = &stZoomLimits;
            stPTZConfiguration.ZoomLimits->Range = &st1DRange;
            stPTZConfiguration.ZoomLimits->Range->URI = "http://www.onvif.org/ver10/tptz/ZoomSpaces/PositionGenericSpace";
            stPTZConfiguration.ZoomLimits->Range->XRange = &stValRange;
        }
        else if (BOARD_GetVersion() == BOARD_IPCR20S3_V1 && BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
        {
            astProfiles[i].PTZConfiguration = &stPTZConfiguration;
            stPTZConfiguration.Name = "PTZ_000";
            stPTZConfiguration.UseCount = 2;
            stPTZConfiguration.token = "000";
            stPTZConfiguration.NodeToken = "000";
        }

#if (defined(BOARD_IPCR20S3))
        if (0 == i)
        {
            astProfiles[i].MetadataConfiguration = &stMetadataConfiguration;
            stMetadataConfiguration.Name = "Metadata";
            stMetadataConfiguration.UseCount = 1;
            stMetadataConfiguration.token = "Metadata";
            stMetadataConfiguration.SessionTimeout = "PT60S";
            stMetadataConfiguration.Multicast = &astMulticast[i];
        }
#endif
    }

    return SOAP_OK;
}

/** Web service operation '__trt__AddVideoEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__AddVideoEncoderConfiguration(struct soap *soap, struct _trt__AddVideoEncoderConfiguration *trt__AddVideoEncoderConfiguration, struct _trt__AddVideoEncoderConfigurationResponse *trt__AddVideoEncoderConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__AddVideoSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__AddVideoSourceConfiguration(struct soap *soap, struct _trt__AddVideoSourceConfiguration *trt__AddVideoSourceConfiguration, struct _trt__AddVideoSourceConfigurationResponse *trt__AddVideoSourceConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__AddAudioEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__AddAudioEncoderConfiguration(struct soap *soap, struct _trt__AddAudioEncoderConfiguration *trt__AddAudioEncoderConfiguration, struct _trt__AddAudioEncoderConfigurationResponse *trt__AddAudioEncoderConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__AddAudioSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__AddAudioSourceConfiguration(struct soap *soap, struct _trt__AddAudioSourceConfiguration *trt__AddAudioSourceConfiguration, struct _trt__AddAudioSourceConfigurationResponse *trt__AddAudioSourceConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__AddPTZConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__AddPTZConfiguration(struct soap *soap, struct _trt__AddPTZConfiguration *trt__AddPTZConfiguration, struct _trt__AddPTZConfigurationResponse *trt__AddPTZConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__AddMetadataConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__AddMetadataConfiguration(struct soap *soap, struct _trt__AddMetadataConfiguration *trt__AddMetadataConfiguration, struct _trt__AddMetadataConfigurationResponse *trt__AddMetadataConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__RemoveVideoEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__RemoveVideoEncoderConfiguration(struct soap *soap, struct _trt__RemoveVideoEncoderConfiguration *trt__RemoveVideoEncoderConfiguration, struct _trt__RemoveVideoEncoderConfigurationResponse *trt__RemoveVideoEncoderConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__RemoveVideoSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__RemoveVideoSourceConfiguration(struct soap *soap, struct _trt__RemoveVideoSourceConfiguration *trt__RemoveVideoSourceConfiguration, struct _trt__RemoveVideoSourceConfigurationResponse *trt__RemoveVideoSourceConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__RemoveAudioEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__RemoveAudioEncoderConfiguration(struct soap *soap, struct _trt__RemoveAudioEncoderConfiguration *trt__RemoveAudioEncoderConfiguration, struct _trt__RemoveAudioEncoderConfigurationResponse *trt__RemoveAudioEncoderConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__RemoveAudioSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__RemoveAudioSourceConfiguration(struct soap *soap, struct _trt__RemoveAudioSourceConfiguration *trt__RemoveAudioSourceConfiguration, struct _trt__RemoveAudioSourceConfigurationResponse *trt__RemoveAudioSourceConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__RemovePTZConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__RemovePTZConfiguration(struct soap *soap, struct _trt__RemovePTZConfiguration *trt__RemovePTZConfiguration, struct _trt__RemovePTZConfigurationResponse *trt__RemovePTZConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__RemoveMetadataConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__RemoveMetadataConfiguration(struct soap *soap, struct _trt__RemoveMetadataConfiguration *trt__RemoveMetadataConfiguration, struct _trt__RemoveMetadataConfigurationResponse *trt__RemoveMetadataConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__DeleteProfile' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__DeleteProfile(struct soap *soap, struct _trt__DeleteProfile *trt__DeleteProfile, struct _trt__DeleteProfileResponse *trt__DeleteProfileResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetVideoSourceConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetVideoSourceConfigurations(struct soap *soap, struct _trt__GetVideoSourceConfigurations *trt__GetVideoSourceConfigurations, struct _trt__GetVideoSourceConfigurationsResponse *trt__GetVideoSourceConfigurationsResponse)
{
    sint32 s32Ret = 0, i;
    MSG_PACKET_S stRetPkt = {0};
    uint32 u32StreamNum = 1;
    static struct tt__VideoSourceConfiguration stConfigurations = {0};
    static struct tt__IntRectangle stBounds = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    trt__GetVideoSourceConfigurationsResponse->__sizeConfigurations = u32StreamNum;
    trt__GetVideoSourceConfigurationsResponse->Configurations = &stConfigurations;
    stConfigurations.Name = "VideoSource_Chn1";
    stConfigurations.UseCount = 3;
    stConfigurations.token = "VideoSource_1";
    stConfigurations.SourceToken = "VideoSource_1";
    stConfigurations.Bounds = &stBounds;
    stBounds.x = 0;
    stBounds.y = 0;
    stBounds.width = 1920;
    stBounds.height = 1080;
    if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        stBounds.width = 2688;
        stBounds.height = 1944;
    }

    return SOAP_OK;
}

/** Web service operation '__trt__GetVideoEncoderConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetVideoEncoderConfigurations(struct soap *soap, struct _trt__GetVideoEncoderConfigurations *trt__GetVideoEncoderConfigurations, struct _trt__GetVideoEncoderConfigurationsResponse *trt__GetVideoEncoderConfigurationsResponse)
{
    sint32 s32Ret = 0, i;
    uint32 u32StreamNum = 1;
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__VideoEncoderConfiguration astConfigurations[3];
    static struct tt__VideoResolution astResolution[3];
    static struct tt__VideoRateControl astRateControl[3];
    static struct tt__H264Configuration astH264[3];
    static struct tt__MulticastConfiguration astMulticast[3];
    static struct tt__IPAddress astAddress[3];

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_IPTR20S1))
    u32StreamNum = 3;
#elif (defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4))
    u32StreamNum = 2;
#elif (defined(BOARD_WFTR20S3))
    if (BOARD_WFTR20S3_V1 == BOARD_GetVersion())
    {
        u32StreamNum = 2;
    }
#endif
    trt__GetVideoEncoderConfigurationsResponse->__sizeConfigurations = u32StreamNum;
    trt__GetVideoEncoderConfigurationsResponse->Configurations = &astConfigurations;
    for (i = 0; i < u32StreamNum; i++)
    {
        astConfigurations[i].Name = (i == 0) ? "VideoEncoder_Chn1_MainStream" : ((i == 1) ? "VideoEncoder_Chn1_SubStream" : "VideoEncoder_Chn3_PicStream");
        astConfigurations[i].token = (i == 0) ? "VideoEncoder000" : ((i == 1) ? "VideoEncoder001" : "VideoEncoder002");
        astConfigurations[i].UseCount = 1;
        astConfigurations[i].Encoding = (i < 2) ? tt__VideoEncoding__H264 : tt__VideoEncoding__JPEG;
        astConfigurations[i].Resolution = &astResolution[i];
        astConfigurations[i].Resolution->Width = (i == 0) ? stVideoCfg.astChnParam[0].u32MainWidth : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubWidth : stVideoCfg.astChnParam[0].u32JpegWidth);
        astConfigurations[i].Resolution->Height = (i == 0) ? stVideoCfg.astChnParam[0].u32MainHeight : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubHeight : stVideoCfg.astChnParam[0].u32JpegHeight);
        astConfigurations[i].Quality = (i == 0) ? 6.0 : 4.0;
        astConfigurations[i].RateControl = &astRateControl[i];
        astConfigurations[i].RateControl->FrameRateLimit = (i == 0) ? stVideoCfg.astChnParam[0].u32MainFramerate : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubFramerate : 0);
        astConfigurations[i].RateControl->EncodingInterval = 1;
        astConfigurations[i].RateControl->BitrateLimit = (i == 0) ? stVideoCfg.astChnParam[0].u32MainBitrate : ((i == 1) ? stVideoCfg.astChnParam[0].u32SubBitrate : 0);
        if (i < 2)
        {
            astConfigurations[i].H264 = &astH264[i];
            astConfigurations[i].H264->GovLength = (i == 0) ? stVideoCfg.astChnParam[0].u32MainIfrmInterval : stVideoCfg.astChnParam[0].u32SubIfrmInterval;
            astConfigurations[i].H264->H264Profile = tt__H264Profile__High;
        }

        if (0 == i)
        {
            astConfigurations[i].Multicast = &astMulticast[i];
            astConfigurations[i].Multicast->Address = &astAddress[i];
            astConfigurations[i].Multicast->Address->Type = tt__IPType__IPv4;
            astConfigurations[i].Multicast->Address->IPv4Address = "*********";
            astConfigurations[i].Multicast->Port = stNetworkCfg.u32MulticastPort;
            astConfigurations[i].Multicast->TTL = 64;
            astConfigurations[i].Multicast->AutoStart = _false;
        }
        astConfigurations[i].SessionTimeout = "PT60S";
    }

    return SOAP_OK;
}

/** Web service operation '__trt__GetAudioSourceConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetAudioSourceConfigurations(struct soap *soap, struct _trt__GetAudioSourceConfigurations *trt__GetAudioSourceConfigurations, struct _trt__GetAudioSourceConfigurationsResponse *trt__GetAudioSourceConfigurationsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetAudioEncoderConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetAudioEncoderConfigurations(struct soap *soap, struct _trt__GetAudioEncoderConfigurations *trt__GetAudioEncoderConfigurations, struct _trt__GetAudioEncoderConfigurationsResponse *trt__GetAudioEncoderConfigurationsResponse)
{
    sint32 s32Ret = 0;
    static struct tt__AudioEncoderConfiguration stConfiguration = {0};
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    trt__GetAudioEncoderConfigurationsResponse->__sizeConfigurations = 1;
    trt__GetAudioEncoderConfigurationsResponse->Configurations = &stConfiguration;
    trt__GetAudioEncoderConfigurationsResponse->Configurations->Name = "A_ENC_000";
    trt__GetAudioEncoderConfigurationsResponse->Configurations->UseCount = 2;
    trt__GetAudioEncoderConfigurationsResponse->Configurations->token = "A_ENC_000";
    trt__GetAudioEncoderConfigurationsResponse->Configurations->Encoding = (AUD_ENC_G711A == stVideoCfg.enAudioEncType || AUD_ENC_G711U == stVideoCfg.enAudioEncType) ? tt__AudioEncoding__G711 : tt__AudioEncoding__LPCM;
    trt__GetAudioEncoderConfigurationsResponse->Configurations->Bitrate = 256;
    trt__GetAudioEncoderConfigurationsResponse->Configurations->SampleRate = (stVideoCfg.enAudioSampleRate == AUD_SR_8K) ? 8 : ((stVideoCfg.enAudioSampleRate == AUD_SR_16K) ? 16 : 32);
    trt__GetAudioEncoderConfigurationsResponse->Configurations->SessionTimeout = "PT60S";

    return SOAP_OK;
}

/** Web service operation '__trt__GetMetadataConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetMetadataConfigurations(struct soap *soap, struct _trt__GetMetadataConfigurations *trt__GetMetadataConfigurations, struct _trt__GetMetadataConfigurationsResponse *trt__GetMetadataConfigurationsResponse)
{
#if (!defined(BOARD_IPCR20S3))
	{
	    print_level(SV_DEBUG, "dummy:  \n");
	    return SOAP_NO_METHOD;
	}
#else
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__MetadataConfiguration stConfiguration = {0};
    static struct tt__MulticastConfiguration stMulticast = {0};
    static struct tt__IPAddress stAddress = {0};

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    trt__GetMetadataConfigurationsResponse->__sizeConfigurations = 1;
    trt__GetMetadataConfigurationsResponse->Configurations = &stConfiguration;
    stConfiguration.Name = "Metadata";
    stConfiguration.UseCount = 1;
    stConfiguration.token = "Metadata";
    stConfiguration.SessionTimeout = "PT60S";
    stConfiguration.Multicast = &stMulticast;
    stMulticast.Port = stNetworkCfg.u32MulticastPort;
    stMulticast.TTL = 64;
    stMulticast.AutoStart = _false;
    stMulticast.Address = &stAddress;
    stAddress.Type = tt__IPType__IPv4;
    stAddress.IPv4Address = "*********";
    return SOAP_OK;
#endif
}

/** Web service operation '__trt__GetVideoSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetVideoSourceConfiguration(struct soap *soap, struct _trt__GetVideoSourceConfiguration *trt__GetVideoSourceConfiguration, struct _trt__GetVideoSourceConfigurationResponse *trt__GetVideoSourceConfigurationResponse)
{
    sint32 s32Ret = 0;
    sint32 s32StreamId;
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    static struct tt__VideoSourceConfiguration stConfiguration = {0};
    static struct tt__IntRectangle stBounds = {0};

    print_level(SV_DEBUG, "dummy: %s \n", trt__GetVideoSourceConfiguration->ConfigurationToken);

    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    if (0 == strcmp(trt__GetVideoSourceConfiguration->ConfigurationToken, "VideoSource_1"))
    {
        s32StreamId = 0;
    }
    else if (0 == strcmp(trt__GetVideoSourceConfiguration->ConfigurationToken, "VideoSource_2"))
    {
        s32StreamId = 1;
    }
    else
    {
        s32StreamId = 2;
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    trt__GetVideoSourceConfigurationResponse->Configuration = &stConfiguration;
    stConfiguration.Name = "VideoSource_Chn1";
    stConfiguration.UseCount = 3;
    stConfiguration.token = "VideoSource_1";
    stConfiguration.SourceToken = "VideoSource_1";
    stConfiguration.Bounds = &stBounds;
    stBounds.x = 0;
    stBounds.y = 0;
    stBounds.width = stVideoCfg.astChnParam[0].u32MainWidth;
    stBounds.height = stVideoCfg.astChnParam[0].u32MainHeight;

    return SOAP_OK;
}

/** Web service operation '__trt__GetVideoEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetVideoEncoderConfiguration(struct soap *soap, struct _trt__GetVideoEncoderConfiguration *trt__GetVideoEncoderConfiguration, struct _trt__GetVideoEncoderConfigurationResponse *trt__GetVideoEncoderConfigurationResponse)
{
    sint32 s32Ret = 0;
    sint32 s32StreamId = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__VideoEncoderConfiguration stConfiguration = {0};
    static struct tt__VideoResolution stResolution = {0};
    static struct tt__VideoRateControl stRateControl = {0};
    static struct tt__H264Configuration stH264 = {0};
    static struct tt__MulticastConfiguration stMulticast = {0};
    static struct tt__IPAddress stAddress = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    print_level(SV_DEBUG, "ConfigurationToken: %s\n", trt__GetVideoEncoderConfiguration->ConfigurationToken);
    if (0 == strcmp(trt__GetVideoEncoderConfiguration->ConfigurationToken, "VideoEncoder000"))
    {
        s32StreamId = 0;
    }
    else if (0 == strcmp(trt__GetVideoEncoderConfiguration->ConfigurationToken, "VideoEncoder001"))
    {
        s32StreamId = 1;
    }
    else
    {
        s32StreamId = 2;
    }
    trt__GetVideoEncoderConfigurationResponse->Configuration = &stConfiguration;
    stConfiguration.Name = (s32StreamId == 0) ? "VideoEncoder_Chn1_MainStream" : ((s32StreamId == 1) ? "VideoEncoder_Chn1_SubStream" : "VideoEncoder_Chn1_PicStream");
    stConfiguration.token = (s32StreamId == 0) ? "VideoEncoder000" : ((s32StreamId == 1) ? "VideoEncoder001" : "VideoEncoder002");
    stConfiguration.UseCount = 1;
    stConfiguration.Encoding = (s32StreamId < 2) ? tt__VideoEncoding__H264 : tt__VideoEncoding__JPEG;
    stConfiguration.Resolution = &stResolution;
    stConfiguration.Resolution->Width = (s32StreamId == 0) ? stVideoCfg.astChnParam[0].u32MainWidth : ((s32StreamId == 1) ? stVideoCfg.astChnParam[0].u32SubWidth : stVideoCfg.astChnParam[0].u32JpegWidth);
    stConfiguration.Resolution->Height = (s32StreamId == 0) ? stVideoCfg.astChnParam[0].u32MainHeight : ((s32StreamId == 1) ? stVideoCfg.astChnParam[0].u32SubHeight : stVideoCfg.astChnParam[0].u32JpegHeight);
    stConfiguration.Quality = (s32StreamId == 0) ? 6.0 : 4.0;
    stConfiguration.RateControl = &stRateControl;
    stConfiguration.RateControl->FrameRateLimit = (s32StreamId == 0) ? stVideoCfg.astChnParam[0].u32MainFramerate : ((s32StreamId == 1) ? stVideoCfg.astChnParam[0].u32SubFramerate : 0);
    stConfiguration.RateControl->EncodingInterval = 1;
    stConfiguration.RateControl->BitrateLimit = (s32StreamId == 0) ? stVideoCfg.astChnParam[0].u32MainBitrate : ((s32StreamId == 1) ? stVideoCfg.astChnParam[0].u32SubBitrate : 0);
    if (s32StreamId < 2)
    {
        stConfiguration.H264 = &stH264;
        stConfiguration.H264->GovLength = (s32StreamId == 0) ? stVideoCfg.astChnParam[0].u32MainIfrmInterval : stVideoCfg.astChnParam[0].u32SubIfrmInterval;
        stConfiguration.H264->H264Profile = tt__H264Profile__High;
    }
    if (0 == s32StreamId)
    {
        stConfiguration.Multicast = &stMulticast;
        stConfiguration.Multicast->Address = &stAddress;
        stConfiguration.Multicast->Address->Type = tt__IPType__IPv4;
        stConfiguration.Multicast->Address->IPv4Address = "*********";
        stConfiguration.Multicast->Port = stNetworkCfg.u32MulticastPort;
        stConfiguration.Multicast->TTL = 64;
        stConfiguration.Multicast->AutoStart = _false;
    }
    stConfiguration.SessionTimeout = "PT60S";

    return SOAP_OK;
}

/** Web service operation '__trt__GetAudioSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetAudioSourceConfiguration(struct soap *soap, struct _trt__GetAudioSourceConfiguration *trt__GetAudioSourceConfiguration, struct _trt__GetAudioSourceConfigurationResponse *trt__GetAudioSourceConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetAudioEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetAudioEncoderConfiguration(struct soap *soap, struct _trt__GetAudioEncoderConfiguration *trt__GetAudioEncoderConfiguration, struct _trt__GetAudioEncoderConfigurationResponse *trt__GetAudioEncoderConfigurationResponse)
{
    sint32 s32Ret = 0;
    static struct tt__AudioEncoderConfiguration stConfiguration = {0};
    MSG_PACKET_S stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};

    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    trt__GetAudioEncoderConfigurationResponse->Configuration = &stConfiguration;
    trt__GetAudioEncoderConfigurationResponse->Configuration->Name = "A_ENC_000";
    trt__GetAudioEncoderConfigurationResponse->Configuration->UseCount = 2;
    trt__GetAudioEncoderConfigurationResponse->Configuration->token = "A_ENC_000";
    trt__GetAudioEncoderConfigurationResponse->Configuration->Encoding = (AUD_ENC_G711A == stVideoCfg.enAudioEncType || AUD_ENC_G711U == stVideoCfg.enAudioEncType) ? tt__AudioEncoding__G711 : tt__AudioEncoding__LPCM;
    trt__GetAudioEncoderConfigurationResponse->Configuration->Bitrate = 256;
    trt__GetAudioEncoderConfigurationResponse->Configuration->SampleRate = (stVideoCfg.enAudioSampleRate == AUD_SR_8K) ? 8 : ((stVideoCfg.enAudioSampleRate == AUD_SR_16K) ? 16 : 32);
    trt__GetAudioEncoderConfigurationResponse->Configuration->SessionTimeout = "PT60S";

    return SOAP_OK;
}

/** Web service operation '__trt__GetMetadataConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetMetadataConfiguration(struct soap *soap, struct _trt__GetMetadataConfiguration *trt__GetMetadataConfiguration, struct _trt__GetMetadataConfigurationResponse *trt__GetMetadataConfigurationResponse)
{
#if (!defined(BOARD_IPCR20S3))
	{
	    print_level(SV_DEBUG, "dummy:  \n");
	    return SOAP_NO_METHOD;
	}
#else
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__MetadataConfiguration stConfiguration = {0};
    static struct tt__MulticastConfiguration stMulticast = {0};
    static struct tt__IPAddress stAddress = {0};

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    trt__GetMetadataConfigurationResponse->Configuration = &stConfiguration;
    stConfiguration.Name = "Metadata";
    stConfiguration.UseCount = 1;
    stConfiguration.token = "Metadata";
    stConfiguration.SessionTimeout = "PT60S";
    stConfiguration.Multicast = &stMulticast;
    stMulticast.Port = stNetworkCfg.u32MulticastPort;
    stMulticast.TTL = 64;
    stMulticast.AutoStart = _false;
    stMulticast.Address = &stAddress;
    stAddress.Type = tt__IPType__IPv4;
    stAddress.IPv4Address = "*********";
    return SOAP_OK;
#endif
}


/** Web service operation '__trt__GetCompatibleVideoEncoderConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetCompatibleVideoEncoderConfigurations(struct soap *soap, struct _trt__GetCompatibleVideoEncoderConfigurations *trt__GetCompatibleVideoEncoderConfigurations, struct _trt__GetCompatibleVideoEncoderConfigurationsResponse *trt__GetCompatibleVideoEncoderConfigurationsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetCompatibleVideoSourceConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetCompatibleVideoSourceConfigurations(struct soap *soap, struct _trt__GetCompatibleVideoSourceConfigurations *trt__GetCompatibleVideoSourceConfigurations, struct _trt__GetCompatibleVideoSourceConfigurationsResponse *trt__GetCompatibleVideoSourceConfigurationsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetCompatibleAudioEncoderConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetCompatibleAudioEncoderConfigurations(struct soap *soap, struct _trt__GetCompatibleAudioEncoderConfigurations *trt__GetCompatibleAudioEncoderConfigurations, struct _trt__GetCompatibleAudioEncoderConfigurationsResponse *trt__GetCompatibleAudioEncoderConfigurationsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetCompatibleAudioSourceConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetCompatibleAudioSourceConfigurations(struct soap *soap, struct _trt__GetCompatibleAudioSourceConfigurations *trt__GetCompatibleAudioSourceConfigurations, struct _trt__GetCompatibleAudioSourceConfigurationsResponse *trt__GetCompatibleAudioSourceConfigurationsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetCompatibleMetadataConfigurations' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetCompatibleMetadataConfigurations(struct soap *soap, struct _trt__GetCompatibleMetadataConfigurations *trt__GetCompatibleMetadataConfigurations, struct _trt__GetCompatibleMetadataConfigurationsResponse *trt__GetCompatibleMetadataConfigurationsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__SetVideoSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__SetVideoSourceConfiguration(struct soap *soap, struct _trt__SetVideoSourceConfiguration *trt__SetVideoSourceConfiguration, struct _trt__SetVideoSourceConfigurationResponse *trt__SetVideoSourceConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__SetVideoEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__SetVideoEncoderConfiguration(struct soap *soap, struct _trt__SetVideoEncoderConfiguration *trt__SetVideoEncoderConfiguration, struct _trt__SetVideoEncoderConfigurationResponse *trt__SetVideoEncoderConfigurationResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    static struct tt__VideoEncoderConfiguration stConfiguration = {0};
    static struct tt__VideoResolution stResolution = {0};
    static struct tt__VideoRateControl stRateControl = {0};
    static struct tt__H264Configuration stH264 = {0};
    static struct tt__MulticastConfiguration stMulticast = {0};
    static struct tt__IPAddress stAddress = {0};
    uint32 u32MainWidthMax = 1920, u32MainWidthMin = 704, u32MainHeightMax = 1080, u32MainHeightMin = 480;
    uint32 u32SubWidthMax = 704, u32SubWidthMin = 352, u32SubHeightMax = 480, u32SubHeightMin = 240;

#if (defined(BOARD_ADA32N1))
    u32SubWidthMax = 1280;
    u32SubHeightMax = 720;
#endif

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) \
    || defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4)\
    || defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
    u32MainWidthMax = 1920;
    u32MainHeightMax = 1080;
    if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
    {
        u32MainWidthMax = 2688;
        u32MainHeightMax = 1944;
    }
#else
    u32MainWidthMax = 1280;
    u32MainHeightMax = 720;
#endif
    print_level(SV_DEBUG, "dummy:  \n");
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed for 0x%x!\n", s32Ret);
            return s32Ret;
        }
    }

    /* 判断设定编码参数是否超过设备限制 */
    if (NULL != trt__SetVideoEncoderConfiguration->Configuration &&
        tt__VideoEncoding__H264 == trt__SetVideoEncoderConfiguration->Configuration->Encoding)
    {
        if (NULL != trt__SetVideoEncoderConfiguration->Configuration->RateControl
            && (trt__SetVideoEncoderConfiguration->Configuration->RateControl->FrameRateLimit < 2
                || trt__SetVideoEncoderConfiguration->Configuration->RateControl->FrameRateLimit > 30
                || trt__SetVideoEncoderConfiguration->Configuration->RateControl->BitrateLimit < 64
                || trt__SetVideoEncoderConfiguration->Configuration->RateControl->BitrateLimit > 51200))
        {
            print_level(SV_ERROR, "input param error! [Framerate:%d, Bitrate:%d]\n", trt__SetVideoEncoderConfiguration->Configuration->RateControl->FrameRateLimit, \
                                                    trt__SetVideoEncoderConfiguration->Configuration->RateControl->BitrateLimit);
            return SOAP_USER_ERROR;
        }

        if (NULL != trt__SetVideoEncoderConfiguration->Configuration->H264
            && (trt__SetVideoEncoderConfiguration->Configuration->H264->GovLength < trt__SetVideoEncoderConfiguration->Configuration->RateControl->FrameRateLimit
                || trt__SetVideoEncoderConfiguration->Configuration->H264->GovLength > 150))
        {
            print_level(SV_ERROR, "input param error! [GovLength:%d, Framerate:%d]\n", trt__SetVideoEncoderConfiguration->Configuration->H264->GovLength, \
                                                                            trt__SetVideoEncoderConfiguration->Configuration->RateControl->FrameRateLimit);
            return SOAP_USER_ERROR;
        }

        if (0 == strcmp(trt__SetVideoEncoderConfiguration->Configuration->token, "VideoEncoder000"))
        {

            if (trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width < u32MainWidthMin
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width > u32MainWidthMax
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height < u32MainHeightMin
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height > u32MainHeightMax)
            {
                print_level(SV_ERROR, "input param error! [%dx%d]\n", trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width, \
                                                                    trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height);
                return SOAP_USER_ERROR;
            }
        }
        else
        {
            if (trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width < u32SubWidthMin
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width > u32SubWidthMax
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height < u32SubHeightMin
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height > u32SubHeightMax)
            {
                print_level(SV_ERROR, "input param error! [%dx%d]\n", trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width, \
                                                                    trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height);
                return SOAP_USER_ERROR;
            }
        }
    }

    if (NULL != trt__SetVideoEncoderConfiguration->Configuration &&
        tt__VideoEncoding__JPEG == trt__SetVideoEncoderConfiguration->Configuration->Encoding)
    {
        if (trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width < u32SubWidthMin
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width > u32MainWidthMax
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height < u32SubHeightMin
                || trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height > u32MainHeightMax)
        {
            print_level(SV_ERROR, "input param error! [%dx%d]\n", trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width, \
                                                                trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height);
            return SOAP_USER_ERROR;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    if (NULL != trt__SetVideoEncoderConfiguration->Configuration &&
        tt__VideoEncoding__H264 == trt__SetVideoEncoderConfiguration->Configuration->Encoding)
    {
        if (NULL != trt__SetVideoEncoderConfiguration->Configuration->Resolution)
        {
            if (0 == strcmp(trt__SetVideoEncoderConfiguration->Configuration->token, "VideoEncoder000"))
            {
                stVideoCfg.astChnParam[0].u32MainWidth = trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width;
                stVideoCfg.astChnParam[0].u32MainHeight = trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height;
            }
            else
            {
                stVideoCfg.astChnParam[0].u32SubWidth = trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width;
                stVideoCfg.astChnParam[0].u32SubHeight = trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height;
            }
        }
        if (NULL != trt__SetVideoEncoderConfiguration->Configuration->RateControl)
        {
            if (0 == strcmp(trt__SetVideoEncoderConfiguration->Configuration->token, "VideoEncoder000"))
            {
                stVideoCfg.astChnParam[0].u32MainFramerate = trt__SetVideoEncoderConfiguration->Configuration->RateControl->FrameRateLimit;
                stVideoCfg.astChnParam[0].u32MainBitrate = trt__SetVideoEncoderConfiguration->Configuration->RateControl->BitrateLimit;
            }
            else
            {
                stVideoCfg.astChnParam[0].u32SubFramerate = trt__SetVideoEncoderConfiguration->Configuration->RateControl->FrameRateLimit;
                stVideoCfg.astChnParam[0].u32SubBitrate = trt__SetVideoEncoderConfiguration->Configuration->RateControl->BitrateLimit;
            }
        }
        if (NULL != trt__SetVideoEncoderConfiguration->Configuration->H264)
        {
            if (0 == strcmp(trt__SetVideoEncoderConfiguration->Configuration->token, "VideoEncoder000"))
            {
                stVideoCfg.astChnParam[0].u32MainIfrmInterval = trt__SetVideoEncoderConfiguration->Configuration->H264->GovLength;
            }
            else
            {
                stVideoCfg.astChnParam[0].u32SubIfrmInterval = trt__SetVideoEncoderConfiguration->Configuration->H264->GovLength;
            }
        }
    }

    if (NULL != trt__SetVideoEncoderConfiguration->Configuration &&
        tt__VideoEncoding__JPEG == trt__SetVideoEncoderConfiguration->Configuration->Encoding)
    {
        if (NULL != trt__SetVideoEncoderConfiguration->Configuration->Resolution)
        {
            if (0 == strcmp(trt__SetVideoEncoderConfiguration->Configuration->token, "VideoEncoder002"))
            {
                stVideoCfg.astChnParam[0].u32JpegWidth = trt__SetVideoEncoderConfiguration->Configuration->Resolution->Width;
                stVideoCfg.astChnParam[0].u32JpegHeight = trt__SetVideoEncoderConfiguration->Configuration->Resolution->Height;
            }
        }
    }

    stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
    stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__trt__SetAudioSourceConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__SetAudioSourceConfiguration(struct soap *soap, struct _trt__SetAudioSourceConfiguration *trt__SetAudioSourceConfiguration, struct _trt__SetAudioSourceConfigurationResponse *trt__SetAudioSourceConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__SetAudioEncoderConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__SetAudioEncoderConfiguration(struct soap *soap, struct _trt__SetAudioEncoderConfiguration *trt__SetAudioEncoderConfiguration, struct _trt__SetAudioEncoderConfigurationResponse *trt__SetAudioEncoderConfigurationResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};

    print_level(SV_DEBUG, "dummy:  \n");

    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    if (NULL != trt__SetAudioEncoderConfiguration->Configuration)
    {
        print_level(SV_INFO, "Encoding:%d, SampleRate:%d\n", trt__SetAudioEncoderConfiguration->Configuration->Encoding, trt__SetAudioEncoderConfiguration->Configuration->SampleRate);
        if ((tt__AudioEncoding__G711 != trt__SetAudioEncoderConfiguration->Configuration->Encoding
                && tt__AudioEncoding__LPCM != trt__SetAudioEncoderConfiguration->Configuration->Encoding)
            || (trt__SetAudioEncoderConfiguration->Configuration->SampleRate != 8
                && trt__SetAudioEncoderConfiguration->Configuration->SampleRate != 16
                && trt__SetAudioEncoderConfiguration->Configuration->SampleRate != 32))
        {
            print_level(SV_ERROR, "input param error! [acode:%d, samplerate:%d]\n", trt__SetAudioEncoderConfiguration->Configuration->Encoding, trt__SetAudioEncoderConfiguration->Configuration->SampleRate);
            return SOAP_USER_ERROR;
        }

        stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
        s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
            return SOAP_SVR_FAULT;
        }

        stVideoCfg.enAudioEncType = (tt__AudioEncoding__G711 == trt__SetAudioEncoderConfiguration->Configuration->Encoding) ? AUD_ENC_G711A : AUD_ENC_LPCM;
        stVideoCfg.enAudioSampleRate = (trt__SetAudioEncoderConfiguration->Configuration->SampleRate == 8) ? AUD_SR_8K : ((trt__SetAudioEncoderConfiguration->Configuration->SampleRate == 16) ? AUD_SR_16K : AUD_SR_32K);
        stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
        stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
        s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
            return SOAP_SVR_FAULT;
        }
    }

    return SOAP_OK;
}

/** Web service operation '__trt__SetMetadataConfiguration' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__SetMetadataConfiguration(struct soap *soap, struct _trt__SetMetadataConfiguration *trt__SetMetadataConfiguration, struct _trt__SetMetadataConfigurationResponse *trt__SetMetadataConfigurationResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}


/** Web service operation '__trt__GetVideoSourceConfigurationOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetVideoSourceConfigurationOptions(struct soap *soap, struct _trt__GetVideoSourceConfigurationOptions *trt__GetVideoSourceConfigurationOptions, struct _trt__GetVideoSourceConfigurationOptionsResponse *trt__GetVideoSourceConfigurationOptionsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetVideoEncoderConfigurationOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetVideoEncoderConfigurationOptions(struct soap *soap, struct _trt__GetVideoEncoderConfigurationOptions *trt__GetVideoEncoderConfigurationOptions, struct _trt__GetVideoEncoderConfigurationOptionsResponse *trt__GetVideoEncoderConfigurationOptionsResponse)
{
    sint32 s32Ret = 0;
    static struct tt__VideoEncoderConfigurationOptions stOptions = {0};
    static struct tt__IntRange stQualityRange = {0};
    static struct tt__H264Options stH264 = {0};
    static struct tt__JpegOptions stJPEG = {0};
    static struct tt__VideoResolution astResolutionsAvailable[4];
    static struct tt__IntRange stGovLengthRange = {0};
    static struct tt__IntRange stFrameRateRange = {0};
    static struct tt__IntRange stEncodingIntervalRange = {0};
    static enum tt__H264Profile aenH264ProfilesSupported[3];

    if (NULL == trt__GetVideoEncoderConfigurationOptions->ConfigurationToken)
    {
        return SOAP_TAG_MISMATCH;
    }

    print_level(SV_DEBUG, "ConfigurationToken: %s, ProfileToken: %s\n", trt__GetVideoEncoderConfigurationOptions->ConfigurationToken, trt__GetVideoEncoderConfigurationOptions->ProfileToken);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    trt__GetVideoEncoderConfigurationOptionsResponse->Options = &stOptions;
    stOptions.QualityRange = &stQualityRange;
    stOptions.QualityRange->Min = 1;
    stOptions.QualityRange->Max = 6;
    if (0 == strcmp(trt__GetVideoEncoderConfigurationOptions->ConfigurationToken, "VideoEncoder002"))
    {
        stOptions.H264 = NULL;
        stOptions.JPEG = &stJPEG;
        stOptions.JPEG->ResolutionsAvailable = &astResolutionsAvailable[0];
#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
        stOptions.JPEG->__sizeResolutionsAvailable = 4;
        stOptions.JPEG->ResolutionsAvailable[0].Width = 1920;
        stOptions.JPEG->ResolutionsAvailable[0].Height = 1080;
        stOptions.JPEG->ResolutionsAvailable[1].Width = 1280;
        stOptions.JPEG->ResolutionsAvailable[1].Height = 720;
        stOptions.JPEG->ResolutionsAvailable[2].Width = 704;
        stOptions.JPEG->ResolutionsAvailable[2].Height = 480;
        stOptions.JPEG->ResolutionsAvailable[3].Width = 352;
        stOptions.JPEG->ResolutionsAvailable[3].Height = 240;
#elif (defined(BOARD_DMS31V2) \
        || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
        || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4))
        if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
        {
            stOptions.JPEG->__sizeResolutionsAvailable = 4;
            stOptions.JPEG->ResolutionsAvailable[0].Width = 2688;
            stOptions.JPEG->ResolutionsAvailable[0].Height = 1944;
            stOptions.JPEG->ResolutionsAvailable[1].Width = 1920;
            stOptions.JPEG->ResolutionsAvailable[1].Height = 1080;
            stOptions.JPEG->ResolutionsAvailable[2].Width = 1280;
            stOptions.JPEG->ResolutionsAvailable[2].Height = 720;
            stOptions.JPEG->ResolutionsAvailable[3].Width = 704;
            stOptions.JPEG->ResolutionsAvailable[3].Height = 480;
        }
        else
        {
            stOptions.JPEG->__sizeResolutionsAvailable = 3;
            stOptions.JPEG->ResolutionsAvailable[0].Width = 1920;
            stOptions.JPEG->ResolutionsAvailable[0].Height = 1080;
            stOptions.JPEG->ResolutionsAvailable[1].Width = 1280;
            stOptions.JPEG->ResolutionsAvailable[1].Height = 720;
            stOptions.JPEG->ResolutionsAvailable[2].Width = 704;
            stOptions.JPEG->ResolutionsAvailable[2].Height = 480;
        }
#else
        stOptions.JPEG->__sizeResolutionsAvailable = 3;
        stOptions.JPEG->ResolutionsAvailable[0].Width = 1280;
        stOptions.JPEG->ResolutionsAvailable[0].Height = 720;
        stOptions.JPEG->ResolutionsAvailable[1].Width = 704;
        stOptions.JPEG->ResolutionsAvailable[1].Height = 480;
        stOptions.JPEG->ResolutionsAvailable[2].Width = 352;
        stOptions.JPEG->ResolutionsAvailable[2].Height = 240;
#endif
    }
    else
    {
        stOptions.JPEG = NULL;
        stOptions.H264 = &stH264;
        stOptions.H264->ResolutionsAvailable = &astResolutionsAvailable[0];
        if (0 == strcmp(trt__GetVideoEncoderConfigurationOptions->ConfigurationToken, "VideoEncoder000"))
        {
#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4) || defined(BOARD_IPCR20S5) || defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
            stOptions.H264->__sizeResolutionsAvailable = 4;
            stOptions.H264->ResolutionsAvailable[0].Width = 1920;
            stOptions.H264->ResolutionsAvailable[0].Height = 1080;
            stOptions.H264->ResolutionsAvailable[1].Width = 1280;
            stOptions.H264->ResolutionsAvailable[1].Height = 960;
            stOptions.H264->ResolutionsAvailable[2].Width = 1280;
            stOptions.H264->ResolutionsAvailable[2].Height = 720;
            stOptions.H264->ResolutionsAvailable[3].Width = 704;
            stOptions.H264->ResolutionsAvailable[3].Height = 480;
#elif (defined(BOARD_DMS31V2) \
        || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
        || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4))
            if (BOARD_ADA32N1_V1 == BOARD_GetVersion() || BOARD_ADA47V1_V3 == BOARD_GetVersion())
            {
                stOptions.H264->__sizeResolutionsAvailable = 4;
                stOptions.H264->ResolutionsAvailable[0].Width = 2688;
                stOptions.H264->ResolutionsAvailable[0].Height = 1944;
                stOptions.H264->ResolutionsAvailable[1].Width = 1920;
                stOptions.H264->ResolutionsAvailable[1].Height = 1080;
                stOptions.H264->ResolutionsAvailable[2].Width = 1280;
                stOptions.H264->ResolutionsAvailable[2].Height = 720;
                stOptions.H264->ResolutionsAvailable[3].Width = 704;
                stOptions.H264->ResolutionsAvailable[3].Height = 480;
            }
            else
            {
                stOptions.H264->__sizeResolutionsAvailable = 3;
                stOptions.H264->ResolutionsAvailable[0].Width = 1920;
                stOptions.H264->ResolutionsAvailable[0].Height = 1080;
                stOptions.H264->ResolutionsAvailable[1].Width = 1280;
                stOptions.H264->ResolutionsAvailable[1].Height = 720;
                stOptions.H264->ResolutionsAvailable[2].Width = 704;
                stOptions.H264->ResolutionsAvailable[2].Height = 480;
            }
#else
            stOptions.H264->__sizeResolutionsAvailable = 2;
            stOptions.H264->ResolutionsAvailable[0].Width = 1280;
            stOptions.H264->ResolutionsAvailable[0].Height = 720;
            stOptions.H264->ResolutionsAvailable[1].Width = 704;
            stOptions.H264->ResolutionsAvailable[1].Height = 480;
#endif
        }
        else
        {
#if (defined(BOARD_DMS31V2) \
    || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4))
            stOptions.H264->__sizeResolutionsAvailable = 1;
            stOptions.H264->ResolutionsAvailable[0].Width = 704;
            stOptions.H264->ResolutionsAvailable[0].Height = 480;
#else
            stOptions.H264->__sizeResolutionsAvailable = 2;
            stOptions.H264->ResolutionsAvailable[0].Width = 704;
            stOptions.H264->ResolutionsAvailable[0].Height = 480;
            stOptions.H264->ResolutionsAvailable[1].Width = 352;
            stOptions.H264->ResolutionsAvailable[1].Height = 240;
#endif
        }

        stOptions.H264->GovLengthRange = &stGovLengthRange;
        stOptions.H264->FrameRateRange = &stFrameRateRange;
        stOptions.H264->EncodingIntervalRange = &stEncodingIntervalRange;
        stOptions.H264->GovLengthRange->Min = 2;
        stOptions.H264->GovLengthRange->Max = 150;
        stOptions.H264->FrameRateRange->Min = 2;
        stOptions.H264->FrameRateRange->Max = 30;
        stOptions.H264->EncodingIntervalRange->Min = 1;
        stOptions.H264->EncodingIntervalRange->Max = 1;
        stOptions.H264->__sizeH264ProfilesSupported = 3;
        stOptions.H264->H264ProfilesSupported = &aenH264ProfilesSupported[0];
        stOptions.H264->H264ProfilesSupported[0] = tt__H264Profile__Baseline;
        stOptions.H264->H264ProfilesSupported[1] = tt__H264Profile__Main;
        stOptions.H264->H264ProfilesSupported[2] = tt__H264Profile__High;
    }

    return SOAP_OK;
}

/** Web service operation '__trt__GetAudioSourceConfigurationOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetAudioSourceConfigurationOptions(struct soap *soap, struct _trt__GetAudioSourceConfigurationOptions *trt__GetAudioSourceConfigurationOptions, struct _trt__GetAudioSourceConfigurationOptionsResponse *trt__GetAudioSourceConfigurationOptionsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetAudioEncoderConfigurationOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetAudioEncoderConfigurationOptions(struct soap *soap, struct _trt__GetAudioEncoderConfigurationOptions *trt__GetAudioEncoderConfigurationOptions, struct _trt__GetAudioEncoderConfigurationOptionsResponse *trt__GetAudioEncoderConfigurationOptionsResponse)
{
    sint32 s32Ret = 0;
    static struct tt__AudioEncoderConfigurationOptions stOptions = {0};
    static struct tt__AudioEncoderConfigurationOption stOption = {0};
    static struct tt__IntList stBitrateList;
    static struct tt__IntList stSampleRateList;
    static int as32SampleRateList[3];

    print_level(SV_DEBUG, "ConfigurationToken: %s, ProfileToken: %s\n", trt__GetAudioEncoderConfigurationOptions->ConfigurationToken, trt__GetAudioEncoderConfigurationOptions->ProfileToken);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    trt__GetAudioEncoderConfigurationOptionsResponse->Options = &stOptions;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->__sizeOptions = 1;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options = &stOption;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].Encoding = tt__AudioEncoding__G711;
    //trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].BitrateList = &stBitrateList;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].SampleRateList = &stSampleRateList;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].SampleRateList->__sizeItems = 3;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].SampleRateList->Items = &as32SampleRateList[0];
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].SampleRateList->Items[0] = 8;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].SampleRateList->Items[1] = 16;
    trt__GetAudioEncoderConfigurationOptionsResponse->Options->Options[0].SampleRateList->Items[2] = 32;

    return SOAP_OK;
}

/** Web service operation '__trt__GetMetadataConfigurationOptions' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetMetadataConfigurationOptions(struct soap *soap, struct _trt__GetMetadataConfigurationOptions *trt__GetMetadataConfigurationOptions, struct _trt__GetMetadataConfigurationOptionsResponse *trt__GetMetadataConfigurationOptionsResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetGuaranteedNumberOfVideoEncoderInstances' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetGuaranteedNumberOfVideoEncoderInstances(struct soap *soap, struct _trt__GetGuaranteedNumberOfVideoEncoderInstances *trt__GetGuaranteedNumberOfVideoEncoderInstances, struct _trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse *trt__GetGuaranteedNumberOfVideoEncoderInstancesResponse)
{
    print_level(SV_DEBUG, "dummy:  \n");
    return SOAP_NO_METHOD;
}

/** Web service operation '__trt__GetStreamUri' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetStreamUri(struct soap *soap, struct _trt__GetStreamUri *trt__GetStreamUri, struct _trt__GetStreamUriResponse *trt__GetStreamUriResponse)
{
    sint32 s32Ret = 0;
	MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__MediaUri stMediaUri = {0};
    static char szUri[128];
	MSG_VIDEO_CFG stVideoCfg = {0};
	char szUseIP[32];


    print_level(SV_DEBUG, "ProfileToken: %s\n", trt__GetStreamUri->ProfileToken);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

	memset(&stMsgPkt, 0, sizeof(stMsgPkt));
	stMsgPkt.stMsg.u32Param = 10;
	s32Ret = Msg_submitEvent(EP_CONTROL, OP_REQ_SET_GOP,&stMsgPkt);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "OP_REQ_SET_GOP failed.err=%d \n",s32Ret);
		return SOAP_SVR_FAULT;
	}

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

#if (defined(BOARD_WFTR20S3) ||defined(BOARD_WFCR20S2))
	WIFI_STAT_S stWifiStatus = {0};
	WIFI_Query_Status(&stWifiStatus);
	if(stNetworkCfg.bWifiStaEnable)
		strcpy(szUseIP,stWifiStatus.szStaIpAddr);
	else
		strcpy(szUseIP,stNetworkCfg.szWifiApIpAddr);
#else
	strcpy(szUseIP,stNetworkCfg.szRealIpAddr);
#endif

    if (NULL != trt__GetStreamUri->StreamSetup)
    {
        print_level(SV_DEBUG, "Stream: %d\n", trt__GetStreamUri->StreamSetup->Stream);
    }
    if (0 == strcmp(trt__GetStreamUri->ProfileToken, "MediaProfile000"))
    {
        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
        {
            sprintf(szUri, "rtsp://%s:%d/%s", szUseIP, stNetworkCfg.u32RtspServPort, "h264");
        }
        else
        {
            if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
            {
                sprintf(szUri, "rtsp://%s/%s", stNetworkCfg.szRtspServPubAddr, stNetworkCfg.szRtspServMainUri);
            }
            else
            {
                sprintf(szUri, "rtsp://%s:%d/%s", szUseIP, stNetworkCfg.u32RtspServPort, stNetworkCfg.szRtspServMainUri);
            }
        }
    }
    else if (0 == strcmp(trt__GetStreamUri->ProfileToken, "MediaProfile001"))
    {
        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))   // 202120客户使用定制url
        {
            sprintf(szUri, "rtsp://%s:%d/%s", szUseIP, stNetworkCfg.u32RtspServPort, "h264_2");
        }
        else
        {
            if (BOARD_IsCustomer(BOARD_C_ADA32V2_GJJX))
            {
                sprintf(szUri, "rtsp://%s/%s", stNetworkCfg.szRtspServPubAddr, stNetworkCfg.szRtspServSubUri);
            }
            else
            {
                sprintf(szUri, "rtsp://%s:%d/%s", szUseIP, stNetworkCfg.u32RtspServPort, stNetworkCfg.szRtspServSubUri);
            }
        }
    }
#if (!defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))
    else
    {
        sprintf(szUri, "rtsp://%s:%d/%s", szUseIP, stNetworkCfg.u32RtspServPort, stNetworkCfg.szRtspServPicUri);
    }
#endif

    trt__GetStreamUriResponse->MediaUri = &stMediaUri;
    trt__GetStreamUriResponse->MediaUri->Uri = szUri;
    trt__GetStreamUriResponse->MediaUri->InvalidAfterConnect = _true;
    trt__GetStreamUriResponse->MediaUri->InvalidAfterReboot = _true;
    trt__GetStreamUriResponse->MediaUri->Timeout = "PT0S";
    m_stOnvifCfg.u32GopFlag = 1;
    return SOAP_OK;
}

/** Web service operation '__trt__StartMulticastStreaming' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__StartMulticastStreaming(struct soap *soap, struct _trt__StartMulticastStreaming *trt__StartMulticastStreaming, struct _trt__StartMulticastStreamingResponse *trt__StartMulticastStreamingResponse)
{
#if (!defined(BOARD_IPCR20S3))
        {
            print_level(SV_DEBUG, "dummy:  \n");
            return SOAP_NO_METHOD;
        }
#else
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};

    print_level(SV_DEBUG, "ProfileToken: %s\n", trt__StartMulticastStreaming->ProfileToken);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    stNetworkCfg.bRTPMulticast = SV_TRUE;
    stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
    stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
#endif
}

/** Web service operation '__trt__StopMulticastStreaming' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__StopMulticastStreaming(struct soap *soap, struct _trt__StopMulticastStreaming *trt__StopMulticastStreaming, struct _trt__StopMulticastStreamingResponse *trt__StopMulticastStreamingResponse)
{
#if (!defined(BOARD_IPCR20S3))
        {
            print_level(SV_DEBUG, "dummy:  \n");
            return SOAP_NO_METHOD;
        }
#else
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};

    print_level(SV_DEBUG, "ProfileToken: %s\n", trt__StopMulticastStreaming->ProfileToken);
    if (strlen(m_stOnvifCfg.szAdminPassword))
    {
        s32Ret = soap_wsse_verify_Password(soap, m_stOnvifCfg.szAdminPassword);
        if (SOAP_OK != s32Ret)
        {
            print_level(SV_ERROR, "soap_wsse_verify_Password failed.\n");
            return s32Ret;
        }
    }

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    stNetworkCfg.bRTPMulticast = SV_FALSE;
    stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
    stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
#endif
}

/** Web service operation '__trt__SetSynchronizationPoint' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__SetSynchronizationPoint(struct soap *soap, struct _trt__SetSynchronizationPoint *trt__SetSynchronizationPoint, struct _trt__SetSynchronizationPointResponse *trt__SetSynchronizationPointResponse)
{
    sint32 s32Ret = 0;

    print_level(SV_DEBUG, "dummy: %s \n", trt__SetSynchronizationPoint->ProfileToken);
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_EVENT_IFRAME, NULL, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_EVENT_IFRAME failed.\n");
        return SOAP_SVR_FAULT;
    }

    return SOAP_OK;
}

/** Web service operation '__trt__GetSnapshotUri' (returns SOAP_OK or error code) */
SOAP_FMAC5 int SOAP_FMAC6 __trt__GetSnapshotUri(struct soap *soap, struct _trt__GetSnapshotUri *trt__GetSnapshotUri, struct _trt__GetSnapshotUriResponse *trt__GetSnapshotUriResponse)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    static struct tt__MediaUri stMediaUri = {0};
    static char szUri[128];

    print_level(SV_DEBUG, "dummy: %s \n", trt__GetSnapshotUri->ProfileToken);
    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_ONVIFSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed.\n");
        return SOAP_SVR_FAULT;
    }

    trt__GetSnapshotUriResponse->MediaUri = &stMediaUri;
    sprintf(szUri, "http://%s:8080/snap0.jpeg", stNetworkCfg.szRealIpAddr);
    stMediaUri.Uri = szUri;
    stMediaUri.InvalidAfterConnect = _false;
    stMediaUri.InvalidAfterReboot = _false;
    stMediaUri.Timeout = "PT60S";

    return SOAP_OK;
}

/* End of onvifHandle.c */
