Index: include/common.h
===================================================================
--- include/common.h	(revision 4451)
+++ include/common.h	(working copy)
@@ -217,6 +217,7 @@
     MCU_OPS_GET_CONFIG      = 0x0b,         /* 获取配置信息 */
     MCU_OPS_SET_CONFIG      = 0x0c,         /* 设置配置信息 */
     MCU_OPS_CAN_RS232       = 0x0d,         /* 发送给MCU, MCU同时转发CAN和RS232数据出去，方向：输出 */
+    MCU_OPS_LOG             = 0x0e,         /* 日志 */
 
 	MCU_OPS_BUTT
 }MCU_OP_E;
Index: include/config_factory.h
===================================================================
--- include/config_factory.h	(revision 4451)
+++ include/config_factory.h	(working copy)
@@ -43,6 +43,10 @@
     {
         pszAdminPassword = "installer";
     }
+    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
+    {
+        pszAdminPassword = "BVAdmin123";
+    }
 #endif
 #if (defined(BOARD_ADA32IR))
     else if(BOARD_IsCustomer(BOARD_C_ADA32IR_202461))
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 4451)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -6401,9 +6401,6 @@
         case E_PDS_P:
             modefilelist[0] = PD_MODEL_RGB_P;
 
-            if (BOARD_IsCustomer(BOARD_C_ADA32V2_FTC))
-                modefilelist[0] = PD_MODEL_RGB_P_FTC;
-
             if (BOARD_IsCustomer(BOARD_C_ADA32V2_201266B) || BOARD_IsCustomer(BOARD_C_ADA32V2_201266C))
                 modefilelist[0] = PD_MODEL_RGB_P_201266B;
 
Index: src/peripheral/mcu/mcu.c
===================================================================
--- src/peripheral/mcu/mcu.c	(revision 4451)
+++ src/peripheral/mcu/mcu.c	(working copy)
@@ -86,6 +86,17 @@
 
 RS232_DATA_BUF_S m_stMcuRS232DataBuf = {0};
 
+#define MCU_LOG_FILE "/root/mcu_log.txt"
+
+sint32 mcu_file_size2(char* filename)
+{
+    struct stat statbuf;
+    stat(filename,&statbuf);
+    int size = statbuf.st_size;
+
+    return size;
+}
+
 static sint32 mcu_SendSerialData(const char *pszSerialData, sint32 s32SendSize)
 {
 	sint32 s32Ret = 0;
@@ -1024,6 +1035,28 @@
 	return SV_SUCCESS;
 }
 
+sint32 mcu_write_log(char* s8McuLog)
+{
+    sint32 s32Ret = 0;
+    char szCmd[1024] = {0};
+    sint64 s64Pts = 0;
+
+    /* 如果文件大于8M */
+    if (mcu_file_size2(MCU_LOG_FILE) >= 5242880)
+    {
+        snprintf(szCmd, 1024, "rm %s", MCU_LOG_FILE);
+        SAFE_System_Not_Print(szCmd, NORMAL_WAIT_TIME);
+    }
+
+    s64Pts = mcu_GetTimeTickMs();
+
+    snprintf(szCmd, 1024, "echo \"%lld %s\" >> %s", s64Pts, s8McuLog, MCU_LOG_FILE);
+    SAFE_System_Not_Print(szCmd, NORMAL_WAIT_TIME);
+
+    return SV_SUCCESS;
+}
+
+
 /*******************************************************************************
   * @函数名称: mcu_ProcessCan_201244_SendPdDetectInfo
   * @函数说明: 以新的CAN专用协议发送ADA32 201244客户数据, 没有头帧、尾帧，只有单个数据帧
@@ -2610,6 +2643,11 @@
 			}
             break;
 
+        case MCU_OPS_LOG:            
+            mcu_write_log(pstMcuSerialPackage->data);        
+            print_level(SV_DEBUG, "recv mcu log:%s\n", pstMcuSerialPackage->data);
+            break;
+
         default:
             print_level(SV_ERROR, "recv unsupport opcode 0x%hhu.\n", pstMcuSerialPackage->stMcuSerialHeader.u8Opcode);
 			break;
@@ -3198,11 +3236,19 @@
 		for (j = 0; j < s32SendList; j++)
 		{
 			//can头数据填充
+            u32Canid = strtol("0x18FFD0B6", NULL, 16);
 			stMcuCanPacket.u32CanId = u32Canid;
 			stMcuCanPacket.u16BaudRate = s32Baudrate;
 			stMcuCanPacket.u8CanFrameFlag = u8CanFrameFlag;
 			stMcuCanPacket.u8CanDataLen = 24;
 
+#if 1       /* for JLG debug */
+            if (j == 1)
+            {
+                stMcuCanPacket.u8CanDataLen = 8;
+            }
+#endif
+
 			stMcuSerialPacket.stMcuSerialHeader.u16Startcode = 0xAAAA;
 			if (j == 0)
 			{
@@ -3237,6 +3283,7 @@
 			stMcuSerialPacket.stMcuSerialHeader.u16Len	= MCU_CAN_INFO_HEADER_SIZE + stMcuCanPacket.u8CanDataLen;  // can头数据 + can数据长度
 
             //初始化值
+#if 0
             for (i=0; i<8; i++)
                 stMcuCanPacket.u8CanData[i] = 0xfe;
 
@@ -3249,6 +3296,15 @@
             stMcuCanPacket.u8CanData[1] = 3;
             stMcuCanPacket.u8CanData[8] = 0x0f;
 
+#else       /* for JLG debug */
+            for (i=0; i<8; i++)
+            {
+                stMcuCanPacket.u8CanData[i] = 0;
+            }
+            
+            stMcuCanPacket.u8CanData[0] = 0x0f;
+#endif
+
             if (0 == j)
             {
                 stPdRoiNum.s32RedRoiNum = stPdResults.bRedAlarmOut;
@@ -3261,9 +3317,17 @@
                 stPdRoiNum.s32YellowRoiNum = stPdResults.s32YellowRoiNum;
                 stPdRoiNum.s32GreenRoiNum = stPdResults.s32GreenRoiNum;
             }
+
+            
+#if 0
             stMcuCanPacket.u8CanData[2] = crc8_check((uint8 *)&stPdRoiNum, 3);
             memcpy(&stMcuCanPacket.u8CanData[9], &stPdRoiNum, 3);
+            
+#else       /* for JLG debug */
+            memcpy(&stMcuCanPacket.u8CanData[1], &stPdRoiNum, 3);
+#endif
 
+
             if (++s32PrintCnt >= 100)
             {
                 s32PrintCnt = 0;
