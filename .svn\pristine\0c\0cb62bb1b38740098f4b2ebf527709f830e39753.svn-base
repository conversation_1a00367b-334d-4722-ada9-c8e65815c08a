
export BUILD_DIR		=$(TOP_DIR)/build
export TOOL_DIR			=$(TOP_DIR)/tool
export FACTORY_DIR			=$(TOP_DIR)/tool/factory
export BUILD_IPCR20S3_DIR   =$(BUILD_DIR)/ipcr20s3
export BUILD_WFCR20S2_DIR   =$(BUILD_DIR)/wfcr20s2
export BUILD_IPCR20S4_DIR   =$(BUILD_DIR)/ipcr20s4
export BUILD_WFTR20S3_DIR   =$(BUILD_DIR)/wftr20s3
export BUILD_ADA42V1_DIR	=$(BUILD_DIR)/ada42v1
export BUILD_DMS31V2_DIR	=$(BUILD_DIR)/dms31v2
export BUILD_ADA32V2_DIR	=$(BUILD_DIR)/ada32v2
export BUILD_ADA32IR_DIR	=$(BUILD_DIR)/ada32ir
export BUILD_ADA32N1_DIR	=$(BUILD_DIR)/ada32n1
export BUILD_ADA47V1_DIR	=$(BUILD_DIR)/ada47v1
export BUILD_ADA900V1_DIR	=$(BUILD_DIR)/ada900v1
export BUILD_DMS31SDK_DIR	=$(BUILD_DIR)/dms31sdk
export BUILD_ADA32SDK_DIR	=$(BUILD_DIR)/ada32sdk
export BUILD_ADA32V3_DIR	=$(BUILD_DIR)/ada32v3
export BUILD_HDW845V1_DIR	=$(BUILD_DIR)/hdw845v1
export BUILD_IPTR20S1_DIR   =$(BUILD_DIR)/iptr20s1
export BUILD_ADA32C4_DIR	=$(BUILD_DIR)/ada32c4
export BUILD_ADA32E1_DIR	=$(BUILD_DIR)/ada32e1
export BUILD_ADA32ESDK_DIR	=$(BUILD_DIR)/ada32esdk
export BUILD_ADA32NSDK_DIR	=$(BUILD_DIR)/ada32nsdk
export BUILD_IPCR20S5_DIR   =$(BUILD_DIR)/ipcr20s5
export BUILD_ADA46V1_DIR	=$(BUILD_DIR)/ada46v1
export BUILD_NT98539_DIR	=$(BUILD_DIR)/nt98539
export BUILD_DMS51V1_DIR	=$(BUILD_DIR)/dms51v1

export OUTPUT_DIR		=$(TOP_DIR)/output/

export FILESYS_PATH		=$(TOP_DIR)/filesysterm
export INC_PATH			=$(TOP_DIR)/include
export SRC_PATH			=$(TOP_DIR)/src
export WIFI_MCU_PATH	=$(TOP_DIR)/mcu/wifi
export A32_MCU_PATH		=$(TOP_DIR)/mcu/a32
export A32C4_MCU_PATH		=$(TOP_DIR)/mcu/a32c4
export AC601_MCU_PATH	=$(TOP_DIR)/mcu/ac601
export DEBUG_FACTORY_PATH		=$(TOP_DIR)/pctool/IPCTool/IPCTool/IPCTool/Output/Win32/Debug/factory
export RELEASE_FACTORY_PATH		=$(TOP_DIR)/pctool/IPCTool/IPCTool/IPCTool/Output/Win32/Release/factory

ifeq ($(BOARD), IPCR20S3)
export TOP_LIB			=$(TOP_DIR)/lib/ipcr20s3
export TOP_FACTORY		=$(FACTORY_DIR)/ipcr20s3
export BOOT_PATH		=$(BUILD_IPCR20S3_DIR)/boot
export USRBIN_PATH		=$(BUILD_IPCR20S3_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_IPCR20S3_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_IPCR20S3_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_IPCR20S3_DIR)/usr/share
export LIB_PATH			=$(BUILD_IPCR20S3_DIR)/lib
export BIN_PATH			=$(BUILD_IPCR20S3_DIR)/bin
export BMP_PATH			=$(BUILD_IPCR20S3_DIR)/bmp
export ROOT_PATH		=$(BUILD_IPCR20S3_DIR)/root
export WIFI_PATH		=$(BUILD_IPCR20S3_DIR)/root/wifi
export KO_PATH			=$(BUILD_IPCR20S3_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_IPCR20S3_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_IPCR20S3_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_IPCR20S3_DIR)/etc
export INITD_PATH		=$(BUILD_IPCR20S3_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_IPCR20S3_DIR)/delete
export SHELL_PATH		=$(BUILD_IPCR20S3_DIR)/shell
export SOURCE_PATH		=$(BUILD_IPCR20S3_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_IPCR20S3_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), WFCR20S2)
export TOP_LIB			=$(TOP_DIR)/lib/wfcr20s2
export BOOT_PATH		=$(BUILD_WFCR20S2_DIR)/boot
export USRBIN_PATH		=$(BUILD_WFCR20S2_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_WFCR20S2_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_WFCR20S2_DIR)/usr/local
export USRSBIN_PATH     =$(BUILD_WFCR20S2_DIR)/usr/sbin
export USRSHARE_PATH	=$(BUILD_WFCR20S2_DIR)/usr/share
export LIB_PATH			=$(BUILD_WFCR20S2_DIR)/lib
export BIN_PATH			=$(BUILD_WFCR20S2_DIR)/bin
export ROOT_PATH		=$(BUILD_WFCR20S2_DIR)/root
export WIFI_PATH		=$(BUILD_WFCR20S2_DIR)/root/wifi
export KO_PATH			=$(BUILD_WFCR20S2_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_WFCR20S2_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_WFCR20S2_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_WFCR20S2_DIR)/etc
export INITD_PATH		=$(BUILD_WFCR20S2_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_WFCR20S2_DIR)/delete
export SHELL_PATH		=$(BUILD_WFCR20S2_DIR)/shell
export CONFIG_IQ_PATH	=$(BUILD_WFCR20S2_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), IPCR20S4)
export TOP_LIB			=$(TOP_DIR)/lib/ipcr20s4
export TOP_FACTORY		=$(FACTORY_DIR)/ipcr20s4
export BOOT_PATH		=$(BUILD_IPCR20S4_DIR)/boot
export USRBIN_PATH		=$(BUILD_IPCR20S4_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_IPCR20S4_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_IPCR20S4_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_IPCR20S4_DIR)/usr/share
export LIB_PATH			=$(BUILD_IPCR20S4_DIR)/lib
export BIN_PATH			=$(BUILD_IPCR20S4_DIR)/bin
export ROOT_PATH		=$(BUILD_IPCR20S4_DIR)/root
export WIFI_PATH		=$(BUILD_IPCR20S4_DIR)/root/wifi
export KO_PATH			=$(BUILD_IPCR20S4_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_IPCR20S4_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_IPCR20S4_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_IPCR20S4_DIR)/etc
export INITD_PATH		=$(BUILD_IPCR20S4_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_IPCR20S4_DIR)/delete
export SHELL_PATH		=$(BUILD_IPCR20S4_DIR)/shell
export CONFIG_IQ_PATH	=$(BUILD_IPCR20S4_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), WFTR20S3)
export TOP_LIB			=$(TOP_DIR)/lib/wftr20s3
export TOP_FACTORY		=$(FACTORY_DIR)/wftr20s3
export BOOT_PATH		=$(BUILD_WFTR20S3_DIR)/boot
export USRBIN_PATH		=$(BUILD_WFTR20S3_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_WFTR20S3_DIR)/usr/lib
export USRSBIN_PATH     =$(BUILD_WFTR20S3_DIR)/usr/sbin
export USRLOCAL_PATH	=$(BUILD_WFTR20S3_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_WFTR20S3_DIR)/usr/share
export LIB_PATH			=$(BUILD_WFTR20S3_DIR)/lib
export BIN_PATH			=$(BUILD_WFTR20S3_DIR)/bin
export ROOT_PATH		=$(BUILD_WFTR20S3_DIR)/root
export WIFI_PATH		=$(BUILD_WFTR20S3_DIR)/root/wifi
export KO_PATH			=$(BUILD_WFTR20S3_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_WFTR20S3_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_WFTR20S3_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_WFTR20S3_DIR)/etc
export INITD_PATH		=$(BUILD_WFTR20S3_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_WFTR20S3_DIR)/delete
export SHELL_PATH		=$(BUILD_WFTR20S3_DIR)/shell
export CONFIG_IQ_PATH	=$(BUILD_WFTR20S3_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA47V1)
export TOP_LIB			=$(TOP_DIR)/lib/ada47v1
export TOP_FACTORY		=$(FACTORY_DIR)/ada47v1
export BOOT_PATH		=$(BUILD_ADA47V1_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA47V1_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA47V1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA47V1_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA47V1_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA47V1_DIR)/lib
export BIN_PATH			=$(BUILD_ADA47V1_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA47V1_DIR)/root
export WIFI_PATH		=$(BUILD_ADA47V1_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA47V1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA47V1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA47V1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA47V1_DIR)/etc
export INITD_PATH		=$(BUILD_ADA47V1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA47V1_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA47V1_DIR)/shell
export CONFIG_IQ_PATH	=$(BUILD_ADA47V1_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), DMS31V2)
export TOP_LIB			=$(TOP_DIR)/lib/dms31v2
export TOP_FACTORY		=$(FACTORY_DIR)/dms31v2
export BOOT_PATH		=$(BUILD_DMS31V2_DIR)/boot
export USRBIN_PATH		=$(BUILD_DMS31V2_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_DMS31V2_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_DMS31V2_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_DMS31V2_DIR)/usr/share
export LIB_PATH			=$(BUILD_DMS31V2_DIR)/lib
export BIN_PATH			=$(BUILD_DMS31V2_DIR)/bin
export ROOT_PATH		=$(BUILD_DMS31V2_DIR)/root
export WIFI_PATH		=$(BUILD_DMS31V2_DIR)/root/wifi
export KO_PATH			=$(BUILD_DMS31V2_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_DMS31V2_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_DMS31V2_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_DMS31V2_DIR)/etc
export INITD_PATH		=$(BUILD_DMS31V2_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_DMS31V2_DIR)/delete
export SHELL_PATH		=$(BUILD_DMS31V2_DIR)/shell
export SOURCE_PATH		=$(BUILD_DMS31V2_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_DMS31V2_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32V2)
export TOP_LIB			=$(TOP_DIR)/lib/ada32v2
export TOP_FACTORY		=$(FACTORY_DIR)/ada32v2
export BOOT_PATH		=$(BUILD_ADA32V2_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32V2_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32V2_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32V2_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32V2_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32V2_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32V2_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32V2_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32V2_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32V2_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32V2_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32V2_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32V2_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32V2_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32V2_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32V2_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32V2_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32V2_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32IR)
export TOP_LIB			=$(TOP_DIR)/lib/ada32ir
ifeq ($(MOUDLETYPE), 933)
export TOP_LIB_IR		=$(TOP_DIR)/lib/ada32ir/TC933
else ifeq ($(MOUDLETYPE), 639T2)
export TOP_LIB_IR		=$(TOP_DIR)/lib/ada32ir/TC933
else ifeq ($(MOUDLETYPE), 639T3)
export TOP_LIB_IR		=$(TOP_DIR)/lib/ada32ir/TC639
else ifeq ($(MOUDLETYPE), 639T6)
export TOP_LIB_IR		=$(TOP_DIR)/lib/ada32ir/TC933
endif
export TOP_FACTORY		=$(FACTORY_DIR)/ada32ir
export BOOT_PATH		=$(BUILD_ADA32IR_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32IR_DIR)/usr/bin
export USRSBIN_PATH     =$(BUILD_ADA32IR_DIR)/usr/sbin
export USRLIB_PATH		=$(BUILD_ADA32IR_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32IR_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32IR_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32IR_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32IR_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32IR_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32IR_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32IR_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32IR_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32IR_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32IR_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32IR_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32IR_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32IR_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32IR_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32IR_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32N1)
export TOP_LIB			=$(TOP_DIR)/lib/ada32n1
export TOP_FACTORY		=$(FACTORY_DIR)/ada32n1
export BOOT_PATH		=$(BUILD_ADA32N1_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32N1_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32N1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32N1_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32N1_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32N1_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32N1_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32N1_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32N1_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32N1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32N1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32N1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32N1_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32N1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32N1_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32N1_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32N1_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32N1_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA900V1)
export TOP_LIB			=$(TOP_DIR)/lib/ada900v1
export TOP_FACTORY		=$(FACTORY_DIR)/ada900v1
export BOOT_PATH		=$(BUILD_ADA900V1_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA900V1_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA900V1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA900V1_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA900V1_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA900V1_DIR)/lib
export BIN_PATH			=$(BUILD_ADA900V1_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA900V1_DIR)/root
export WIFI_PATH		=$(BUILD_ADA900V1_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA900V1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA900V1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA900V1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA900V1_DIR)/etc
export INITD_PATH		=$(BUILD_ADA900V1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA900V1_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA900V1_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA900V1_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA900V1_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), DMS31SDK)
export TOP_LIB			=$(TOP_DIR)/lib/dms31sdk
export TOP_FACTORY		=$(FACTORY_DIR)/dms31sdk
export BOOT_PATH		=$(BUILD_DMS31SDK_DIR)/boot
export USRBIN_PATH		=$(BUILD_DMS31SDK_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_DMS31SDK_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_DMS31SDK_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_DMS31SDK_DIR)/usr/share
export LIB_PATH			=$(BUILD_DMS31SDK_DIR)/lib
export BIN_PATH			=$(BUILD_DMS31SDK_DIR)/bin
export ROOT_PATH		=$(BUILD_DMS31SDK_DIR)/root
export WIFI_PATH		=$(BUILD_DMS31SDK_DIR)/root/wifi
export KO_PATH			=$(BUILD_DMS31SDK_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_DMS31SDK_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_DMS31SDK_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_DMS31SDK_DIR)/etc
export INITD_PATH		=$(BUILD_DMS31SDK_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_DMS31SDK_DIR)/delete
export SHELL_PATH		=$(BUILD_DMS31SDK_DIR)/shell
export SOURCE_PATH		=$(BUILD_DMS31SDK_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_DMS31SDK_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32SDK)
export TOP_LIB			=$(TOP_DIR)/lib/ada32sdk
export TOP_FACTORY		=$(FACTORY_DIR)/ada32sdk
export BOOT_PATH		=$(BUILD_ADA32SDK_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32SDK_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32SDK_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32SDK_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32SDK_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32SDK_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32SDK_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32SDK_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32SDK_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32SDK_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32SDK_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32SDK_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32SDK_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32SDK_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32SDK_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32SDK_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32SDK_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32SDK_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32V3)
export TOP_LIB			=$(TOP_DIR)/lib/ada32v3
export TOP_FACTORY		=$(FACTORY_DIR)/ada32v3
export BOOT_PATH		=$(BUILD_ADA32V3_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32V3_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32V3_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32V3_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32V3_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32V3_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32V3_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32V3_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32V3_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32V3_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32V3_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32V3_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32V3_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32V3_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32V3_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32V3_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32V3_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32V3_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), HDW845V1)
export TOP_LIB			=$(TOP_DIR)/lib/hdw845v1
export TOP_FACTORY		=$(FACTORY_DIR)/hdw845v1
export BOOT_PATH		=$(BUILD_HDW845V1_DIR)/boot
export USRBIN_PATH		=$(BUILD_HDW845V1_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_HDW845V1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_HDW845V1_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_HDW845V1_DIR)/usr/share
export LIB_PATH			=$(BUILD_HDW845V1_DIR)/lib
export BIN_PATH			=$(BUILD_HDW845V1_DIR)/bin
export ROOT_PATH		=$(BUILD_HDW845V1_DIR)/root
export WIFI_PATH		=$(BUILD_HDW845V1_DIR)/root/wifi
export KO_PATH			=$(BUILD_HDW845V1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_HDW845V1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_HDW845V1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_HDW845V1_DIR)/etc
export INITD_PATH		=$(BUILD_HDW845V1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_HDW845V1_DIR)/delete
export SHELL_PATH		=$(BUILD_HDW845V1_DIR)/shell
export SOURCE_PATH		=$(BUILD_HDW845V1_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_HDW845V1_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), IPTR20S1)
export TOP_LIB			=$(TOP_DIR)/lib/iptr20s1
export TOP_FACTORY		=$(FACTORY_DIR)/iptr20s1
export BOOT_PATH		=$(BUILD_IPTR20S1_DIR)/boot
export USRLIB_PATH		=$(BUILD_IPTR20S1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_IPTR20S1_DIR)/usr/local
export LIB_PATH			=$(BUILD_IPTR20S1_DIR)/lib
export BIN_PATH			=$(BUILD_IPTR20S1_DIR)/bin
export ROOT_PATH		=$(BUILD_IPTR20S1_DIR)/root
export WIFI_PATH		=$(BUILD_IPTR20S1_DIR)/root/wifi
export KO_PATH			=$(BUILD_IPTR20S1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_IPTR20S1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_IPTR20S1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_IPTR20S1_DIR)/etc
export INITD_PATH		=$(BUILD_IPTR20S1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_IPTR20S1_DIR)/delete
export SHELL_PATH		=$(BUILD_IPTR20S1_DIR)/shell
export CONFIG_IQ_PATH	=$(BUILD_IPTR20S1_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32C4)
export TOP_LIB			=$(TOP_DIR)/lib/ada32c4
export TOP_FACTORY		=$(FACTORY_DIR)/ada32c4
export BOOT_PATH		=$(BUILD_ADA32C4_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32C4_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32C4_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32C4_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32C4_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32C4_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32C4_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32C4_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32C4_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32C4_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32C4_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32C4_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32C4_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32C4_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32C4_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32C4_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32C4_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32C4_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32E1)
export TOP_LIB			=$(TOP_DIR)/lib/ada32e1
export TOP_FACTORY		=$(FACTORY_DIR)/ada32e1
export BOOT_PATH		=$(BUILD_ADA32E1_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32E1_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32E1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32E1_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32E1_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32E1_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32E1_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32E1_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32E1_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32E1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32E1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32E1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32E1_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32E1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32E1_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32E1_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32E1_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32E1_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32NSDK)
export TOP_LIB			=$(TOP_DIR)/lib/ada32nsdk
export TOP_FACTORY		=$(FACTORY_DIR)/ada32nsdk
export BOOT_PATH		=$(BUILD_ADA32NSDK_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32NSDK_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32NSDK_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32NSDK_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32NSDK_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32NSDK_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32NSDK_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32NSDK_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32NSDK_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32NSDK_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32NSDK_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32NSDK_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32NSDK_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32NSDK_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32NSDK_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32NSDK_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32NSDK_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32NSDK_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32ESDK)
export TOP_LIB			=$(TOP_DIR)/lib/ada32esdk
export TOP_FACTORY		=$(FACTORY_DIR)/ada32esdk
export BOOT_PATH		=$(BUILD_ADA32ESDK_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA32ESDK_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA32ESDK_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA32ESDK_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA32ESDK_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA32ESDK_DIR)/lib
export BIN_PATH			=$(BUILD_ADA32ESDK_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA32ESDK_DIR)/root
export WIFI_PATH		=$(BUILD_ADA32ESDK_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA32ESDK_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA32ESDK_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA32ESDK_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA32ESDK_DIR)/etc
export INITD_PATH		=$(BUILD_ADA32ESDK_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA32ESDK_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA32ESDK_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA32ESDK_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA32ESDK_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), IPCR20S5)
export TOP_LIB			=$(TOP_DIR)/lib/ipcr20s5
export TOP_FACTORY		=$(FACTORY_DIR)/ipcr20s5
export BOOT_PATH		=$(BUILD_IPCR20S5_DIR)/boot
export USRBIN_PATH		=$(BUILD_IPCR20S5_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_IPCR20S5_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_IPCR20S5_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_IPCR20S5_DIR)/usr/share
export LIB_PATH			=$(BUILD_IPCR20S5_DIR)/lib
export BIN_PATH			=$(BUILD_IPCR20S5_DIR)/bin
export ROOT_PATH		=$(BUILD_IPCR20S5_DIR)/root
export KO_PATH			=$(BUILD_IPCR20S5_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_IPCR20S5_DIR)/root/ko/extdrv
export ETC_PATH			=$(BUILD_IPCR20S5_DIR)/etc
export INITD_PATH		=$(BUILD_IPCR20S5_DIR)/etc/init.d
export SHELL_PATH		=$(BUILD_IPCR20S5_DIR)/shell
export CONFIG_IQ_PATH	=$(BUILD_IPCR20S5_DIR)/config/iqstonkam
export MISC_PATH		=$(BUILD_IPCR20S5_DIR)/misc
endif

ifeq ($(BOARD), ADA46V1)
export TOP_LIB			=$(TOP_DIR)/lib/ada46v1
export TOP_FACTORY		=$(FACTORY_DIR)/ada46v1
export BOOT_PATH		=$(BUILD_ADA46V1_DIR)/boot
export USRBIN_PATH		=$(BUILD_ADA46V1_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_ADA46V1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_ADA46V1_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_ADA46V1_DIR)/usr/share
export LIB_PATH			=$(BUILD_ADA46V1_DIR)/lib
export BIN_PATH			=$(BUILD_ADA46V1_DIR)/bin
export ROOT_PATH		=$(BUILD_ADA46V1_DIR)/root
export WIFI_PATH		=$(BUILD_ADA46V1_DIR)/root/wifi
export KO_PATH			=$(BUILD_ADA46V1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_ADA46V1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_ADA46V1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_ADA46V1_DIR)/etc
export INITD_PATH		=$(BUILD_ADA46V1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_ADA46V1_DIR)/delete
export SHELL_PATH		=$(BUILD_ADA46V1_DIR)/shell
export SOURCE_PATH		=$(BUILD_ADA46V1_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_ADA46V1_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), ADA32V4)
export TOP_LIB			=$(TOP_DIR)/lib/nt98539
export TOP_FACTORY		=$(FACTORY_DIR)/nt98539
export BOOT_PATH		=$(BUILD_NT98539_DIR)/boot
export USRBIN_PATH		=$(BUILD_NT98539_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_NT98539_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_NT98539_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_NT98539_DIR)/usr/share
export LIB_PATH			=$(BUILD_NT98539_DIR)/lib
export BIN_PATH			=$(BUILD_NT98539_DIR)/bin
export ROOT_PATH		=$(BUILD_NT98539_DIR)/root
export WIFI_PATH		=$(BUILD_NT98539_DIR)/root/wifi
export KO_PATH			=$(BUILD_NT98539_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_NT98539_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_NT98539_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_NT98539_DIR)/etc
export INITD_PATH		=$(BUILD_NT98539_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_NT98539_DIR)/delete
export SHELL_PATH		=$(BUILD_NT98539_DIR)/shell
export SOURCE_PATH		=$(BUILD_NT98539_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_NT98539_DIR)/config/iqstonkam
endif

ifeq ($(BOARD), DMS51V1)
export TOP_LIB			=$(TOP_DIR)/lib/dms51v1
export TOP_FACTORY		=$(FACTORY_DIR)/dms51v1
export BOOT_PATH		=$(BUILD_DMS51V1_DIR)/boot
export USRBIN_PATH		=$(BUILD_DMS51V1_DIR)/usr/bin
export USRLIB_PATH		=$(BUILD_DMS51V1_DIR)/usr/lib
export USRLOCAL_PATH	=$(BUILD_DMS51V1_DIR)/usr/local
export USRSHARE_PATH	=$(BUILD_DMS51V1_DIR)/usr/share
export LIB_PATH			=$(BUILD_DMS51V1_DIR)/lib
export BIN_PATH			=$(BUILD_DMS51V1_DIR)/bin
export ROOT_PATH		=$(BUILD_DMS51V1_DIR)/root
export WIFI_PATH		=$(BUILD_DMS51V1_DIR)/root/wifi
export KO_PATH			=$(BUILD_DMS51V1_DIR)/root/ko
export KO_EXTDRV_PATH	=$(BUILD_DMS51V1_DIR)/root/ko/extdrv
export KO_USB_PATH		=$(BUILD_DMS51V1_DIR)/root/ko/usb
export ETC_PATH			=$(BUILD_DMS51V1_DIR)/etc
export INITD_PATH		=$(BUILD_DMS51V1_DIR)/etc/init.d
export DELETE_PATH		=$(BUILD_DMS51V1_DIR)/delete
export SHELL_PATH		=$(BUILD_DMS51V1_DIR)/shell
export SOURCE_PATH		=$(BUILD_DMS51V1_DIR)/source
export CONFIG_IQ_PATH	=$(BUILD_DMS51V1_DIR)/config/iqstonkam
endif

export COMP_DEPEND		=$(wildcard $(USRLIB_PATH)/*.a)
export INC_DEPEND		=$(wildcard $(INC_PATH)/*.h)

export DEPEND_FILE_NAME = dep.mk.rej
export MAKE_LOG_FILE	= make_log.txt
export SUB_DIR_MK		= $(SCRIPTS_DIR)/SubDir.mk
export AUTO_DEP_MK		= $(SCRIPTS_DIR)/AutoDep.mk
export BIN_AUTO_DEP_MK	= $(SCRIPTS_DIR)/BinAutoDep.mk
export BUILD_MK			= $(SCRIPTS_DIR)/BUILD.mk
export BUILD_SH			= $(SCRIPTS_DIR)/BUILD.sh
export PACKET_SH		= $(SCRIPTS_DIR)/Packet.sh
export DRV_TMPL_MK		= $(SCRIPTS_DIR)/DrvTmpl.mk
export FIND_SDK_SH		= $(SCRIPTS_DIR)/FindSDK.sh
export COMMAND_DEF_MK	= $(SCRIPTS_DIR)/CommandDef.mk
export BOARD_CONFIG_MK	= $(SCRIPTS_DIR)/BoardConfig.mk
export TOOLCHAIN_CFG_MK	= $(SCRIPTS_DIR)/ToolchainCfg.mk
export SDK_PATH_MK		= $(SCRIPTS_DIR)/SDKPath.mk
export FS_AUTO_DEP_MK	= $(SCRIPTS_DIR)/FsAutoDep.mk
export COPY_HIS_DRV_MK	= $(SCRIPTS_DIR)/CopyHisDriver.mk
export CHK_SVN_PERM_SH	= $(SCRIPTS_DIR)/CheckSvnPerms.sh
export GET_SRC_RVER_SH	= $(SCRIPTS_DIR)/GetSrcRevision.sh
export SMALL_FLASH_MK	= $(SCRIPTS_DIR)/SmallSizeFlash.mk
export Full_FLASH_MK	= $(SCRIPTS_DIR)/FullSizeFlash.mk
export CLEAN_DIR_MK		= $(SCRIPTS_DIR)/CleanDir.mk
export SPLINT_MK		= $(SCRIPTS_DIR)/Splint.mk
export PACK_TOOL		= $(SCRIPTS_DIR)/PackTool.sh
export MD5_CHECK		= $(SCRIPTS_DIR)/Md5check.sh
export MD5_DELETE		= $(SCRIPTS_DIR)/Md5delete.sh
export MAKE_FS			= $(SCRIPTS_DIR)/MakeFs.sh

# Gui language packing related path


# vim:noet:sw=4:ts=4
