/******************************************************************************
Copyright (C) 2020-2022 广州敏视数码科技有限公司版权所有.

文件名：recorder.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2020-07-20

文件功能描述: 定义录像模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _RECORDER_H_
#define _RECORDER_H_

#include "common.h"
#include "config.h"
#include <time.h>


#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define     REC_MAX_CHN             4                      /* 录像最多支持通道 */
#define     REC_MEDIA_MAX_CHN       (REC_MAX_CHN * 2)      /* 录像支持的媒体流数,一个通道包含报警和普通录像两路 */
#define     REC_DEV_NUM             5                      /* 支持最多存储设备数 */
#define     REC_FILE_NAME_LEN       256                    /* 录像文件名长度 */

#define     REC_CSV_FILE_NEW          "/mnt/sdcard/file/alarmNew.csv"
#define     REC_CSV_FILE_OLD          "/mnt/sdcard/file/alarmOld.csv"

/* 通知CMS图片生成回调函数 */
typedef sint32 (*RECORDER_CMS_NOTICE_CALLBACK)(SV_BOOL *pbResult);

/* 录像类型定义 */
typedef enum tagRecType_E
{
    REC_TYPE_NORMAL         = 0,        /* 普通录像 */
    REC_TYPE_FATIGUE        = 32,       /* 疲劳 */
    REC_TYPE_DISTRACTION    = 33,       /* 分心 */
    REC_TYPE_NO_DRIVER      = 34,       /* 无司机 */
    REC_TYPE_SMOKE          = 35,       /* 抽烟 */
    REC_TYPE_PHONE          = 36,       /* 打电话 */
    REC_TYPE_RADAR          = 42,       /* 雷达报警 */
    REC_TYPE_YAWN           = 47,       /* 打哈欠报警 */
    REC_TYPE_FACE_AUTH_FAILED = 48,     /* 人脸识别登陆失败 */
    REC_TYPE_NO_MASK        = 49,       /* 未配带口罩 */
    REC_TYPE_SUNGLASS        = 50,      /* 配带太阳眼镜 */
    REC_TYPE_FATIGUE_L2     = 51,       /* 二级疲劳 */
    REC_TYPE_PD_ROI         = 37,       /* ROI区域内检测到行人 */
    REC_TYPE_SEATBELT       = 53,       /* 安全带 */
    REC_TYPE_SHELTER        = 54,       /* 遮挡报警 */
    REC_TYPE_FACE_AUTH_TIMEOUT = 55,    /* 人脸识别登陆超时 */
    REC_TYPE_DRINKEAT       = 56,       /* 吃喝东西 */
    REC_TYPE_CHANGE_GUARD   = 57,       /* 司机更换 */
    REC_TYPE_OVERSPEED      = 58,       /* 超速行驶 */
    REC_TYPE_NO_HELMET      = 59,       /* 无安全帽 */
    REC_TYPE_ALARM          = 99,       /* 泛指报警 */
    REC_TYPE_PIC            = 100,      /* 报警抓图事件 */
    REC_TYPE_USER_ALARM     = 101,      /* 客户外部下发报警事件 */

    REC_TYPE_BUTT
} REC_TYPE_E;

/* 录像通道 */
typedef enum RecChn_E
{
    REC_CHN_0 = 0x00,                   /* 录像通道0 */
    REC_CHN_1,                          /* 录像通道1 */
    REC_CHN_2,                          /* 录像通道2 */
    REC_CHN_3,                          /* 录像通道3 */
    REC_CHN_4,                          /* 录像通道4 */
    REC_CHN_ALL=5,                      /* 全部录像通道 */

    REC_CHN_BUTT
} REC_CHN_E;

/* 录像文件名定义 */
typedef struct tagRecFileName_S
{
	sint32	s32Year;                    /* 年 */
	sint32	s32Month;                   /* 月 */
	sint32	s32Day;                     /* 日 */
	sint32	s32Hour;                    /* 时 */
	sint32	s32Minute;                  /* 分 */
	sint32	s32Second;                  /* 秒 */
	char    cEventType[2];              /* 事件类型 */
	sint32	s32Duration;                /* 录像时长 */
	sint32	s32Size;                    /* 文件大小 */
	sint32	s32Width;                   /* 视频宽 */
	sint32	s32Height;                  /* 视频高 */
	sint32	s32FrameRate;               /* 帧率 */
	sint32	s32BitRate;                 /* 码 */
	sint32	cPlateNum[32];              /* 平台车牌号 */
	sint32	s32ChNum;                   /* 通道号 */
	uint64	u64DeviceID;                /* 设备号 */
	uint8	u8Flag;                     /* 上传标志     */
	sint32	s32Msec;
	sint32	s32PreRecordMsec;           /* 预录时长 */
	char    cVersionNum[32];            /* 版本号 */
	char    cCustomerNum[4];            /* 客户号 */
	char    cTimeZone[4];               /* 时区 */
	uint8   cDST;
	char    cFileType[32];              /* 文件类型 */
} REC_FILE_NAME_S;

/* 录像控制信息 */
typedef struct tagRecCtrlInfo
{
    SV_BOOL     bAudioEnable;           /* 是否使能音频 */
    ENCODE_E    enEncodeType;           /* 编码类型 */
} REC_CTRL_INFO_S;


/* 录像文件信息 */
typedef struct tagRecFileInfo
{
    sint32      s32Chn;                     /* 通道号 */
    char        szFilepath[REC_FILE_NAME_LEN]; /* 文件名 */
} REC_FILE_INFO_S;


/* 录像状态 */
typedef enum tagRecStatus_E
{
    REC_STAT_DISABLE = 0x00,                /* 录像通道未使能 */
    REC_STAT_FORBID,                        /* 禁止录像 */
    REC_STAT_RECORDING,                     /* 正在录像 */
    REC_STAT_STOP,                          /* 正在停止录像 */
    REC_STAT_NO_STORAGE,                    /* 无存储设备可读写 */
    REC_STAT_FULL,                          /* 存储设备满 */

    REC_STAT_BUTT
} REC_STAT_E;

/* 录像状态信息 */
typedef struct tagRecStatusInfo_S
{
    SV_BOOL     bVaild;                     /* 通道是否有效 */
    SV_BOOL     bIsReady;                   /* 是否可进行录像 */
    SV_BOOL     bRecording;                 /* osd调用,是否录像 */
    SV_BOOL     bNormalRecording;           /* 是否普通录像 */
    SV_BOOL     bAlarmRecording;            /* 是否报警录像 */
    SV_BOOL     bContinue;                  /* 是否连续录像 */
    SV_BOOL     bAlarmP;                    /* 算法消费者 */
    SV_BOOL     bAlarmV;                    /* 算法生产者*/
} REC_STAT_INFO_S;

/* 写入录像报警文件信息 */
typedef struct tagRecCsvFileInfo_S
{
    double      dLongitude;                 /* 经度 */
    double      dLatitude;                  /* 纬度 */
    double      dSpeed;                     /* 速度 */
    char        szAlarmType[18];            /* 报警类型 */
    sint32      s32Year;                    /* 年 */
    sint32      s32Month;                   /* 月 */
    sint32      s32Day;                     /* 日 */
    sint32      s32Hour;                    /* 时 */
    sint32      s32Minute;                  /* 分 */
    sint32      s32Second;                  /* 秒 */
    uint64      u64SerialNum;               /* 序列号 */
    sint64      u64TimeStamp;               /* 当前帧时间戳 */
    sint64      s64Interval;                /* 前后两帧数据的时间间隔 */
    float       fEyelidLeft;                /* DMS眼睑闭合度（左） */
    SV_BOOL     bEyelidLeftValid;           /* DMS眼睑闭合度（左）数据是否有效 */
    float       fEyelidRight;               /* DMS眼睑闭合度（右） */
    SV_BOOL     bEyelidRightValid;          /* DMS眼睑闭合度（左）数据是否有效 */
} REC_CSV_FILE_S;

typedef struct
{
    REC_TYPE_E  enRecType;                  /* 报警事件 */
    uint64      u64EventTime;               /* 报警时间 */
    uint8       u8AlarmUuid[64];            /* 报警uuid */
    char        szPath[256];                /* 报警文件路径 */
} REC_ALARM_INFO_S;

/* DMS眼睑闭合度 */
typedef struct
{
    sint64          u64TimeStamp;       /* 当前帧时间戳 */
    sint64          s64Interval;        /* 前后两帧数据的时间间隔 */
    float           fEyelidLeft;        /* DMS眼睑闭合度（左） */
    SV_BOOL         bEyelidLeftValid;   /* DMS眼睑闭合度（左）数据是否有效 */
    float           fEyelidRight;       /* DMS眼睑闭合度（右） */
    SV_BOOL         bEyelidRightValid;  /* DMS眼睑闭合度（左）数据是否有效 */
} REC_DMM_EYELID_S;

/* 用户手动录像参数 */
typedef struct
{
    sint32          s32Key;             /* 录像事件键值，相当句柄使用,不需要设置，分配成功会自动设置 */
    sint64          s64PreRecDur;       /* 报警录像预录时长(s) */
    sint64          s64PostRecDur;      /* 报警录像持续时长(s) -1:一直录像*/
    char            szPath[256];        /* 自定义文件名 */

} REC_MAN_CONF_S;


/******************************************************************************
 * 函数功能: 初始化录像模块
 * 输入参数: pstRecParam --- 系统参数
             pstRecCtrlInfo --- 录像控制参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_Init(CFG_SYS_PARAM *pstRecParam, REC_CTRL_INFO_S *pstRecCtrlInfo);

/******************************************************************************
 * 函数功能: 去初始化录像模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_Fini();

/******************************************************************************
 * 函数功能: 启动录像模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_Start();

/******************************************************************************
 * 函数功能: 停止录像模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_Stop();

/******************************************************************************
 * 函数功能: 配置录像模块工作参数
 * 输入参数: pstRecParam --- 系统参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_SetConfig(CFG_SYS_PARAM *pstRecParam);

/******************************************************************************
 * 函数功能: 设置录像流编码类型
 * 输入参数: pstRecCtrlInfo --- 录像控制参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
               SV_FAILURE - 失败

 * 注意     : 无
 *****************************************************************************/
extern sint32 RECORDER_SetMediaConfig(REC_CTRL_INFO_S *pstRecCtrlInfo);

/******************************************************************************
 * 函数功能: 报警录像
 * 输入参数: enRecType --- 报警录像类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
               SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_Alarm(REC_TYPE_E enRecType, REC_ALARM_INFO_S *pstRecAlarmInfo);

/******************************************************************************
 * 函数功能: 报警推送
 * 输入参数: pstRecAlarmInfo -- 推送信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
               SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_AlarmSubmit(REC_ALARM_INFO_S *pstRecAlarmInfo);

/******************************************************************************
 * 函数功能: 写入眼部数据信息
 * 输入参数: enRecType --- 报警录像类型
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
               SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_WriteEyelidData(REC_DMM_EYELID_S *pstEyelidData);

/******************************************************************************
 * 函数功能: 获取录像状态信息(对外)
 * 输入参数: 录像信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_GetState(REC_STAT_INFO_S *pstROut);

/******************************************************************************
 * 函数功能: 录像文件重命名
 * 输入参数: szPath -- 原文件名 u32Size -- 文件名长度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_RenameFile(char *szPath, uint32 u32Size);

/******************************************************************************
 * 函数功能: 重命名图片文件
 * 输入参数: szPath -- 文件名 u32Size -- 名字长度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
extern sint32 RECORDER_RenamePicFile(char *szPicPath, uint32 u32Size);

/******************************************************************************
 * 函数功能: CMS通知回调函数
 * 输入参数: pCmsCallback -- 回调函数指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 RECORDER_CmsRegisterCallbak(RECORDER_CMS_NOTICE_CALLBACK pCmsCallback);

/******************************************************************************
 * 函数功能: 将JPEG抓图数据拷贝到抓图缓存区
 * 输入参数: s32Chn --- 通道号
             pvBuf --- 输出缓存指针
             u32ImgSize --- 图像大小
 * 输出参数:
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
extern sint32 RECORDER_CopyImgBuffer(sint32 s32Chn, void *pvBuf, uint32 u32ImgSize);
/******************************************************************************
 * 函数功能: 获取图片缓存区数据
 * 输入参数: s32Chn -- 通道号
             pvBuf --- 输出缓存指针
             u32ImgSize --- 图像大小
 * 输出参数:
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
extern sint32 RECORDER_GetImgBuffer(sint32 s32Chn, void **pvBuf, uint32 *pu32ImgSize);


/******************************************************************************
 * 函数功能: csv文件锁
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
extern sint32 recorder_CsvLock(void);

/******************************************************************************
 * 函数功能: csv文件解锁
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    :   无
 *****************************************************************************/
extern sint32 recorder_CsvUnlock(void);

/******************************************************************************
 * 函数功能: 注册掉电标志
 * 输入参数: pbPowerFlag -- 掉电标志指针
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 将模块的掉电指针指向control的掉电标志，只读
 *****************************************************************************/
extern sint32 RECORDER_RegisterPowerFlag(const SV_BOOL *pbPowerFlag);

/******************************************************************************
 * 函数功能: 获取最近一次录像的文件名
 * 输入参数: enPos -- SD卡
             enType -- 录像类型
 * 输出参数: pstFileInfo -- 文件名
 * 返回值  : 无
 * 注意    : 将模块的掉电指针指向control的掉电标志，只读
 *****************************************************************************/
extern sint32 RECORDER_GetLastFileInfo
(STORAGE_POS_E enPos, REC_TYPE_E enType, REC_FILE_INFO_S *pstFileInfo);

/******************************************************************************
 * 函数功能: 手动启动录像
 * 输入参数: 手动录像需要的相关参数
 * 输出参数: pstManRecParam -- 录像参数
 * 返回值  : SV_SUCCESS -- 成功
             SV_FAILURE -- 失败
 * 注意    :  pstManRecParam->s32Key值是输出参数，可以不用设置
 *****************************************************************************/
extern sint32 RECORDER_StartRecord(REC_MAN_CONF_S *pstManRecParam);

/******************************************************************************
 * 函数功能: 手动停止录像
 * 输入参数: key -- 录像键值
 * 输出参数: 无
 * 返回值  : SV_SUCCESS -- 成功
             SV_FAILURE -- 失败
 * 注意    :
 *****************************************************************************/
extern sint32 RECORDER_StopRecord(sint32 s32Key);



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _RECORDER_H_ */

