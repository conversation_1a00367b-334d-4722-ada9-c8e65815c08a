﻿#if !defined(__IMX32X_CMOS_H_)
#define __IMX32X_CMOS_H_

#include <stdio.h>
#include <string.h>
#include <assert.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>
#include "hi_comm_sns.h"
#include "hi_comm_video.h"
#include "hi_sns_ctrl.h"
#include "mpi_isp.h"
#include "mpi_ae.h"
#include "mpi_awb.h"
#include "mpi_af.h"

#include "hi_spi.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* End of #ifdef __cplusplus */

#define IMX32X_ID 320

#define myprint(fmt, arg...) do { \
    printf("\033[0;32m %s>%s:#%d, \033[0m "fmt, __FILE__, __FUNCTION__, __LINE__, ## arg);  \
} while(0)


extern HI_S32 cmos_get_ae_default_imx323(AE_SENSOR_DEFAULT_S *pstAeSnsDft);
extern HI_S32 cmos_get_ae_default_imx323(AE_SENSOR_DEFAULT_S *pstAeSnsDft);
extern HI_VOID cmos_fps_set_imx323(HI_FLOAT f32Fps, AE_SENSOR_DEFAULT_S *pstAeSnsDft);
extern HI_VOID cmos_slow_framerate_set_imx323(HI_U32 u32FullLines, AE_SENSOR_DEFAULT_S *pstAeSnsDft);
extern HI_VOID cmos_inttime_update_imx323(HI_U32 u32IntTime);
extern HI_VOID cmos_again_calc_table_imx323(HI_U32 *pu32AgainLin, HI_U32 *pu32AgainDb);
extern HI_VOID cmos_dgain_calc_table_imx323(HI_U32 *pu32DgainLin, HI_U32 *pu32DgainDb);
extern HI_VOID cmos_gains_update_imx323(HI_U32 u32Again, HI_U32 u32Dgain);
extern HI_VOID cmos_ae_fswdr_attr_set_imx323(AE_FSWDR_ATTR_S *pstAeFSWDRAttr);
extern HI_S32 cmos_get_awb_default_imx323(AWB_SENSOR_DEFAULT_S *pstAwbSnsDft);
extern HI_S32 cmos_get_awb_default_imx323(AWB_SENSOR_DEFAULT_S *pstAwbSnsDft);
extern HI_U32 cmos_get_isp_default_imx323(ISP_CMOS_DEFAULT_S *pstDef);
extern HI_U32 cmos_get_isp_default_imx323(ISP_CMOS_DEFAULT_S *pstDef);
extern HI_U32 cmos_get_isp_black_level_imx323(ISP_CMOS_BLACK_LEVEL_S *pstBlackLevel);
extern HI_VOID cmos_set_pixel_detect_imx323(HI_BOOL bEnable);
extern HI_VOID cmos_set_wdr_mode_imx323(HI_U8 u8Mode);
extern HI_S32 cmos_set_image_mode_imx323(ISP_CMOS_SENSOR_IMAGE_MODE_S *pstSensorImageMode);
extern HI_U32 cmos_get_sns_regs_info_imx323(ISP_SNS_REGS_INFO_S *pstSnsRegsInfo);
extern HI_VOID sensor_global_init_imx323();

extern void sensor_init_720p_30fps_imx323();
extern void sensor_init_1080p_30fps_imx323();
extern void sensor_init_720p_60fps_imx323();

extern HI_S32 cmos_get_ae_default_imx327(AE_SENSOR_DEFAULT_S *pstAeSnsDft);
extern HI_VOID cmos_fps_set_imx327(HI_FLOAT f32Fps, AE_SENSOR_DEFAULT_S *pstAeSnsDft);
extern HI_VOID cmos_slow_framerate_set_imx327(HI_U32 u32FullLines, AE_SENSOR_DEFAULT_S *pstAeSnsDft);
extern HI_VOID cmos_inttime_update_imx327(HI_U32 u32IntTime);
extern HI_VOID cmos_again_calc_table_imx327(HI_U32 *pu32AgainLin, HI_U32 *pu32AgainDb);
extern HI_VOID cmos_dgain_calc_table_imx327(HI_U32 *pu32DgainLin, HI_U32 *pu32DgainDb);
extern HI_VOID cmos_again_calc_table_imx327(HI_U32 *pu32AgainLin, HI_U32 *pu32AgainDb);
extern HI_VOID cmos_dgain_calc_table_imx327(HI_U32 *pu32DgainLin, HI_U32 *pu32DgainDb);
extern HI_VOID cmos_gains_update_imx327(HI_U32 u32Again, HI_U32 u32Dgain);
extern HI_VOID cmos_get_inttime_max_imx327(HI_U32 u32Ratio, HI_U32 u32MinTime, HI_U32 *pu32IntTimeMax, HI_U32 *pu32LFMaxIntTime);
extern HI_VOID cmos_ae_fswdr_attr_set_imx327(AE_FSWDR_ATTR_S *pstAeFSWDRAttr);
extern HI_S32 cmos_get_awb_default_imx327(AWB_SENSOR_DEFAULT_S *pstAwbSnsDft);
extern HI_U32 cmos_get_isp_default_imx327(ISP_CMOS_DEFAULT_S *pstDef);
extern HI_U32 cmos_get_isp_black_level_imx327(ISP_CMOS_BLACK_LEVEL_S *pstBlackLevel);
extern HI_VOID cmos_set_pixel_detect_imx327(HI_BOOL bEnable);
extern HI_VOID cmos_set_wdr_mode_imx327(HI_U8 u8Mode);
extern HI_U32 cmos_get_sns_regs_info_imx327(ISP_SNS_REGS_INFO_S *pstSnsRegsInfo);
extern HI_S32 cmos_set_image_mode_imx327(ISP_CMOS_SENSOR_IMAGE_MODE_S *pstSensorImageMode);
extern HI_VOID sensor_global_init_imx327();

extern void sensor_init_720p_30fps_imx327();
extern void sensor_init_720p_60fps_imx327();
extern void sensor_init_1080p_30fps_imx327();
extern void sensor_init_1080p_60fps_imx327();


/****************************************************************************
 * local variables                                                            *
 ****************************************************************************/
#define SENSOR_1080P_30FPS_MODE (1) 
#define SENSOR_720P_30FPS_MODE (2)
#define SENSOR_720P_60FPS_MODE  (3) 

#define SENSOR_IMX323   (0)
#define SENSOR_IMX327   (1)

HI_U8 gu8Sensor = SENSOR_IMX323;
HI_U8 gu8SensorImageMode = SENSOR_1080P_30FPS_MODE;
WDR_MODE_E genSensorMode = WDR_MODE_NONE;
HI_BOOL bSensorInit = HI_FALSE; 
static int g_fd = -1;

#define PATHLEN_MAX 256
#define CMOS_CFG_INI "imx22x_cfg.ini"
static char pcName[PATHLEN_MAX] = "configs/imx22x_cfg.ini";

int sensor_spi_init(void)
{
    if(g_fd >= 0)
    {
        return 0;
    }    
	unsigned int value;
	int ret = 0;
	char file_name[] = "/dev/spidev0.0";

	g_fd = open(file_name, 0);
	if (g_fd < 0)
	{
		printf("Open %s error!\n",file_name);
		return -1;
	}

	value = SPI_MODE_3 | SPI_LSB_FIRST;// | SPI_LOOP;
	ret = ioctl(g_fd, SPI_IOC_WR_MODE, &value);
	if (ret < 0)
	{
		printf("ioctl SPI_IOC_WR_MODE err, value = %d ret = %d\n", value, ret);
		return ret;
	}

	value = 8;
	ret = ioctl(g_fd, SPI_IOC_WR_BITS_PER_WORD, &value);
	if (ret < 0)
	{
		printf("ioctl SPI_IOC_WR_BITS_PER_WORD err, value = %d ret = %d\n",value, ret);
		return ret;
	}

	value = 2000000;
	ret = ioctl(g_fd, SPI_IOC_WR_MAX_SPEED_HZ, &value);
	if (ret < 0)
	{
		printf("ioctl SPI_IOC_WR_MAX_SPEED_HZ err, value = %d ret = %d\n",value, ret);
		return ret;
	}

	return 0;
}

int sensor_spi_exit(void)
{
    if (g_fd >= 0)
    {
        close(g_fd);
        g_fd = -1;
        return 0;
    }
    return -1;
}

int sensor_write_register(int addr, int data)
{
    int ret;
    struct spi_ioc_transfer mesg[1];
    unsigned char  tx_buf[8] = {0};
    unsigned char  rx_buf[8] = {0};

    tx_buf[0] = (addr & 0x0f00) >> 8;
    tx_buf[1] = addr & 0xff;
    tx_buf[2] = data;

	//printf("func:%s tx_buf = %#x, %#x, %#x\n", __func__, tx_buf[0], tx_buf[1], tx_buf[2]);

	memset(mesg, 0, sizeof(mesg));  
	mesg[0].tx_buf = (__u32)tx_buf;  
	mesg[0].len    = 3;  
	mesg[0].rx_buf = (__u32)rx_buf; 
	mesg[0].cs_change = 1;

	ret = ioctl(g_fd, SPI_IOC_MESSAGE(1), mesg);
	if (ret < 0) {  
		printf("SPI_IOC_MESSAGE error \n");  
		return -1;  
	}
	//printf("func:%s ret = %d, rx_buf = %#x, %#x, %#x\n", __func__, ret , rx_buf[0], rx_buf[1], rx_buf[2]);

	return 0;
}

int sensor_read_register(int addr)
{
	int ret = 0;
	struct spi_ioc_transfer mesg[1];
	unsigned char  tx_buf[8] = {0};
	unsigned char  rx_buf[8] = {0};
	
	tx_buf[0] = (addr & 0xff00) >> 8;
	tx_buf[0] |= 0x80;
	tx_buf[1] = addr & 0xff;
	tx_buf[2] = 0;

	memset(mesg, 0, sizeof(mesg));
	mesg[0].tx_buf = (__u32)tx_buf;
	mesg[0].len    = 3;
	mesg[0].rx_buf = (__u32)rx_buf;
	mesg[0].cs_change = 1;

	ret = ioctl(g_fd, SPI_IOC_MESSAGE(1), mesg);
	if (ret  < 0) {  
		printf("SPI_IOC_MESSAGE error \n");  
		return -1;  
	}
	//printf("func:%s ret = %d, rx_buf = %#x, %#x, %#x\n", __func__, ret , rx_buf[0], rx_buf[1], rx_buf[2]);
	
	return rx_buf[2];
}


void sensor_prog(int* rom) 
{
    myprint("step.\n");
}

void setup_sensor(int isp_mode)
{
    myprint("step.\n");
}

void sensor_init()
{
	// sequence according to "Flow  Power-on to Operation Start(Sensor Master Mode)

	// chip_id = 0x2
	/* 1. sensor spi init */
	sensor_spi_init();

    myprint("gu8SensorImageMode=%d, genSensorMode=%d\n", gu8SensorImageMode, genSensorMode);
    if (1 == gu8SensorImageMode)    /* SENSOR_1080P_30FPS_MODE */
    {
        switch (gu8Sensor)
        {
            case SENSOR_IMX323:
                sensor_init_1080p_30fps_imx323();
                bSensorInit = HI_TRUE;
                break;
            case SENSOR_IMX327:
                sensor_init_1080p_30fps_imx327();
                bSensorInit = HI_TRUE;
                break;
        }
        
    }
    else if (2 == gu8SensorImageMode) /* SENSOR_720P_30FPS_MODE */
    {
        if (SENSOR_IMX323 == gu8Sensor)
        {
            sensor_init_720p_30fps_imx323();
        }
        else
        {
            //sensor_init_720p_30fps_imx327();
        }
        bSensorInit = HI_TRUE;
    }
    else if (3 == gu8SensorImageMode && SENSOR_IMX323 == gu8Sensor)
    {
        sensor_init_720p_60fps_imx323();
    }
    else
    {
        printf("Not support this mode\n");
    }
	
}

void sensor_exit()
{
    sensor_spi_exit();

    return;
}

void sensor_check_mode()
{
    sensor_spi_init();
    int value = sensor_read_register(0x204);
    printf("========== value: %#x ==========\n", value);
    if (0)//(0x10 == value)
    {
        printf("========== sensor chip: IMX327 ==========\n");
        gu8Sensor = SENSOR_IMX327;
        gu8SensorImageMode = SENSOR_1080P_30FPS_MODE;
    }
    else
    {
        printf("========== sensor chip: IMX323 ==========\n");
        gu8Sensor = SENSOR_IMX323;
        gu8SensorImageMode = SENSOR_1080P_30FPS_MODE;
    }
}

int  sensor_set_inifile_path(const char *pcPath)
{

    myprint("step.\n");
    memset(pcName, 0, sizeof(pcName));
        
    if (HI_NULL == pcPath)
    {        
        strncat(pcName, "configs/", strlen("configs/"));
        strncat(pcName, CMOS_CFG_INI, sizeof(CMOS_CFG_INI));
    }
    else
    {
		if(strlen(pcPath) > (PATHLEN_MAX - 30))
		{
			printf("Set inifile path is larger PATHLEN_MAX!\n");
			return -1;        
		}
        strncat(pcName, pcPath, strlen(pcPath));
        strncat(pcName, CMOS_CFG_INI, sizeof(CMOS_CFG_INI));
    }
    
    return 0;
}

HI_S32 cmos_init_ae_exp_function(AE_SENSOR_EXP_FUNC_S *pstExpFuncs)
{
    myprint("step.\n");
    memset(pstExpFuncs, 0, sizeof(AE_SENSOR_EXP_FUNC_S));

    if (SENSOR_IMX323 == gu8Sensor)
    {
        pstExpFuncs->pfn_cmos_get_ae_default    = cmos_get_ae_default_imx323;
        pstExpFuncs->pfn_cmos_fps_set           = cmos_fps_set_imx323;
        pstExpFuncs->pfn_cmos_slow_framerate_set= cmos_slow_framerate_set_imx323;    
        pstExpFuncs->pfn_cmos_inttime_update    = cmos_inttime_update_imx323;
        pstExpFuncs->pfn_cmos_gains_update      = cmos_gains_update_imx323;
        pstExpFuncs->pfn_cmos_again_calc_table  = cmos_again_calc_table_imx323;
        pstExpFuncs->pfn_cmos_dgain_calc_table  = cmos_dgain_calc_table_imx323;
        pstExpFuncs->pfn_cmos_get_inttime_max   = NULL;
        pstExpFuncs->pfn_cmos_ae_fswdr_attr_set = cmos_ae_fswdr_attr_set_imx323;
    }
    else
    {
        pstExpFuncs->pfn_cmos_get_ae_default    = cmos_get_ae_default_imx327;
        pstExpFuncs->pfn_cmos_fps_set           = cmos_fps_set_imx327;
        pstExpFuncs->pfn_cmos_slow_framerate_set= cmos_slow_framerate_set_imx327;    
        pstExpFuncs->pfn_cmos_inttime_update    = cmos_inttime_update_imx327;
        pstExpFuncs->pfn_cmos_gains_update      = cmos_gains_update_imx327;
        pstExpFuncs->pfn_cmos_again_calc_table  = cmos_again_calc_table_imx327;
        pstExpFuncs->pfn_cmos_dgain_calc_table  = cmos_dgain_calc_table_imx327;
        pstExpFuncs->pfn_cmos_get_inttime_max   = cmos_get_inttime_max_imx327;  
        pstExpFuncs->pfn_cmos_ae_fswdr_attr_set = cmos_ae_fswdr_attr_set_imx327;  
    }

    return 0;
}

HI_S32 cmos_init_awb_exp_function(AWB_SENSOR_EXP_FUNC_S *pstExpFuncs)
{
    myprint("step.\n");
    memset(pstExpFuncs, 0, sizeof(AWB_SENSOR_EXP_FUNC_S));

    if (SENSOR_IMX323 == gu8Sensor)
    {
        pstExpFuncs->pfn_cmos_get_awb_default = cmos_get_awb_default_imx323;
    }
    else
    {
        pstExpFuncs->pfn_cmos_get_awb_default = cmos_get_awb_default_imx327;
    }

    return 0;
}

HI_S32 cmos_init_sensor_exp_function(ISP_SENSOR_EXP_FUNC_S *pstSensorExpFunc)
{
    myprint("step\n");
    memset(pstSensorExpFunc, 0, sizeof(ISP_SENSOR_EXP_FUNC_S));

    pstSensorExpFunc->pfn_cmos_sensor_init = sensor_init;
    pstSensorExpFunc->pfn_cmos_sensor_exit = sensor_exit;

    if (SENSOR_IMX323 == gu8Sensor)
    {
        pstSensorExpFunc->pfn_cmos_sensor_global_init = sensor_global_init_imx323;
        pstSensorExpFunc->pfn_cmos_set_image_mode = cmos_set_image_mode_imx323;
        pstSensorExpFunc->pfn_cmos_set_wdr_mode = cmos_set_wdr_mode_imx323;
        pstSensorExpFunc->pfn_cmos_get_isp_default = cmos_get_isp_default_imx323;
        pstSensorExpFunc->pfn_cmos_get_isp_black_level = cmos_get_isp_black_level_imx323;
        pstSensorExpFunc->pfn_cmos_set_pixel_detect = cmos_set_pixel_detect_imx323;
        pstSensorExpFunc->pfn_cmos_get_sns_reg_info = cmos_get_sns_regs_info_imx323;
    }
    else
    {
        pstSensorExpFunc->pfn_cmos_sensor_global_init = sensor_global_init_imx327;
        pstSensorExpFunc->pfn_cmos_set_image_mode = cmos_set_image_mode_imx327;
        pstSensorExpFunc->pfn_cmos_set_wdr_mode = cmos_set_wdr_mode_imx327;
        pstSensorExpFunc->pfn_cmos_get_isp_default = cmos_get_isp_default_imx327;
        pstSensorExpFunc->pfn_cmos_get_isp_black_level = cmos_get_isp_black_level_imx327;
        pstSensorExpFunc->pfn_cmos_set_pixel_detect = cmos_set_pixel_detect_imx327;
        pstSensorExpFunc->pfn_cmos_get_sns_reg_info = cmos_get_sns_regs_info_imx327;
    }

    return 0;
}

/****************************************************************************
 * callback structure                                                       *
 ****************************************************************************/

int sensor_register_callback(void)
{
    ISP_DEV IspDev = 0;
    HI_S32 s32Ret;
    ALG_LIB_S stLib;
    ISP_SENSOR_REGISTER_S stIspRegister;
    AE_SENSOR_REGISTER_S  stAeRegister;
    AWB_SENSOR_REGISTER_S stAwbRegister;

    myprint("step\n");
    sensor_check_mode();
    cmos_init_sensor_exp_function(&stIspRegister.stSnsExp);
    s32Ret = HI_MPI_ISP_SensorRegCallBack(IspDev, IMX32X_ID, &stIspRegister);
    if (s32Ret)
    {
        printf("sensor register callback function failed!\n");
        return s32Ret;
    }
    
    stLib.s32Id = 0;
    strncpy(stLib.acLibName, HI_AE_LIB_NAME, sizeof(HI_AE_LIB_NAME));
    cmos_init_ae_exp_function(&stAeRegister.stSnsExp);
    s32Ret = HI_MPI_AE_SensorRegCallBack(IspDev, &stLib, IMX32X_ID, &stAeRegister);
    if (s32Ret)
    {
        printf("sensor register callback function to ae lib failed!\n");
        return s32Ret;
    }

    stLib.s32Id = 0;
    strncpy(stLib.acLibName, HI_AWB_LIB_NAME, sizeof(HI_AWB_LIB_NAME));
    cmos_init_awb_exp_function(&stAwbRegister.stSnsExp);
    s32Ret = HI_MPI_AWB_SensorRegCallBack(IspDev, &stLib, IMX32X_ID, &stAwbRegister);
    if (s32Ret)
    {
        printf("sensor register callback function to awb lib failed!\n");
        return s32Ret;
    }
    
    return 0;
}

int sensor_unregister_callback(void)
{
    ISP_DEV IspDev = 0;
    HI_S32 s32Ret;
    ALG_LIB_S stLib;

    s32Ret = HI_MPI_ISP_SensorUnRegCallBack(IspDev, IMX32X_ID);
    if (s32Ret)
    {
        printf("sensor unregister callback function failed!\n");
        return s32Ret;
    }
    
    stLib.s32Id = 0;
    strncpy(stLib.acLibName, HI_AE_LIB_NAME, sizeof(HI_AE_LIB_NAME));
    s32Ret = HI_MPI_AE_SensorUnRegCallBack(IspDev, &stLib, IMX32X_ID);
    if (s32Ret)
    {
        printf("sensor unregister callback function to ae lib failed!\n");
        return s32Ret;
    }

    stLib.s32Id = 0;
    strncpy(stLib.acLibName, HI_AWB_LIB_NAME, sizeof(HI_AWB_LIB_NAME));
    s32Ret = HI_MPI_AWB_SensorUnRegCallBack(IspDev, &stLib, IMX32X_ID);
    if (s32Ret)
    {
        printf("sensor unregister callback function to awb lib failed!\n");
        return s32Ret;
    }
    
    return 0;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif 
