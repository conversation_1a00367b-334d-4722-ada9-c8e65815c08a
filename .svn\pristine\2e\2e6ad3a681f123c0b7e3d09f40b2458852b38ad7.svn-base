#
# Link all library, and build the final excutable file
#

include ../../Makefile.param
SRCS = $(wildcard ./common/*.c)
SRCS += $(wildcard ./*.c)

# link pthread and math library
SYSTEM_LIB	= -lpthread -lm 

# Link SV Common library
SV_COM_LIBS = -lcontrol -lmedia -lfreetype -lpng -lz -lnetwork -lboard -llog -lconfig -lmxml -lhttp \
			-lsharefifo -lmsg -lsafefunc -lcjson  -luuid -lonvif -lrtsp -lliveMedia -lgroupsock -lBasicUsageEnvironment -lUsageEnvironment

SV_COM_LIBS += -lssl -lcrypto -ldl
# Link other SV libs
OTHER_SV_LIBS 	= 


CFLAGS += -L$(TOP_LIB)
CFLAGS += -L$(TOP_LIB)/bsd
CFLAGS += -I$(SRC_PATH)/media/include


CPPFLAGS = $(CFLAGS)

TARGET_BIN	= bsd
LIB_DEPEND	= $(COMP_DEPEND)
LD_FLAGS	+=$(SYSTEM_LIB) $(OTHER_SV_LIBS) $(SV_COM_LIBS) $(HISIL_LIBS) $(D_LIBS)

COPY_TO_DIR = $(ROOT_PATH)
include $(BIN_AUTO_DEP_MK)

# vim:noet:sw=4:ts=4

