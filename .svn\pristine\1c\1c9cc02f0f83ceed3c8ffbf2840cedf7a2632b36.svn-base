/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file：      emmc.cpp
author:     lyn
version:    1.0.0
date:       2023-12-11
function:   recorder emmc source file
notice:     none
*******************************************************************************/

#include "r_emmc.h"
#include "storage.h"
#include "print.h"
namespace recorder{

REmmc::REmmc(REC_POS_E pos, sint32 idx)
{
    enPos = pos;
    subIdx = idx;
    priority    = pos;

}

REmmc::~REmmc()
{

}

sint32 REmmc::GetRemainSize()
{
    remainSize = STORAGE_GetRemainSize(STORAGE_INNER_EMMC, SV_FALSE);
    isFull = (remainSize < REC_REMAIN_SIZE) ? SV_TRUE : SV_FALSE ;
    return SV_SUCCESS;
}

}