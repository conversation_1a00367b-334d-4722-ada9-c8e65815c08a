ifeq ($(SV_QUIET), y)
export SV_ECHO	=@
else
export SV_ECHO	=
endif

export CC				=$(SV_ECHO)$(CROSS_COMPILE)gcc
export CXX				=$(SV_ECHO)$(CROSS_COMPILE)g++
export AR				=$(SV_ECHO)$(CROSS_COMPILE)ar
export RANLIB			=$(CROSS_COMPILE)ranlib
export STRIP			=$(CROSS_COMPILE)strip
export CONFIGURE_HOST	=$(subst @,,$(subst -gcc,,$(CC)))

ifneq (,$(findstring openssl, $(PWD)))
export AR += rv
endif

ifeq (,$(findstring openssl, $(PWD)))
ifeq (,$(findstring wpa_supplicant, $(PWD)))
CFLAGS 				+= -D_GNU_SOURCE -D_FILE_OFFSET_BITS=64 -rdynamic \
					-Wall -I$(INC_PATH) -L$(USRLIB_PATH) -L$(TOP_LIB) 
endif
else
endif

########################################
#
# Debug param
#
########################################
ifeq ($(DEBUG), y)
export DEBUG		=Y
CFLAGS				+= -DERR_DEBUG -O0 -g3
CPPFLAGS			+= -DERR_DEBUG -O0 -g3
else
CFLAGS				+= -Os
export DEBUG		=N
endif 


ifeq ($(PLATFORM), SSC335)
ifeq ($(BOARD), IPTR20S1)
ifeq (${SDK_SIGMASTAR_SSC335_V042},) 
ifneq ($(IGNORE_SDK), y)
$(error SSC335 Sdk $(SDK_SIGMASTAR_SSC335_V042_DIR_NAME) not found)
endif
endif
else
ifeq (${SDK_SIGMASTAR_SSC335},) 
ifneq ($(IGNORE_SDK), y)
$(error SSC335 Sdk $(SDK_SIGMASTAR_SSC335_DIR_NAME) not found)
endif
endif
endif
INC_LIB_SUFIX		=
CFLAGS				+=-DPLATFORM_SSC335
CFLAGS				+=-I$(HISIL_SDK)/extdrv/
CPPFLAGS			+=-DPLATFORM_SSC335
#CFLAGS 			+=-march=armv7-a -mcpu=cortex-a9
#CXXFlAGS 			+=-march=armv7-a -mcpu=cortex-a9
endif

ifeq ($(PLATFORM), RV1126)
ifeq (${SDK_ROCKCHIP_RV1126},) 
ifneq ($(IGNORE_SDK), y)
$(error RV1126 Sdk $(SDK_ROCKCHIP_RV1126_DIR_NAME) not found)
endif
endif
INC_LIB_SUFIX		=
CFLAGS				+=-DPLATFORM_RV1126
CFLAGS				+=-I$(HISIL_SDK)/extdrv/
CPPFLAGS			+=-DPLATFORM_RV1126
#CFLAGS 			+=-march=armv7-a -mcpu=cortex-a9
#CXXFlAGS 			+=-march=armv7-a -mcpu=cortex-a9
endif

ifeq ($(PLATFORM), RV1106)
ifeq (${SDK_ROCKCHIP_RV1106},) 
ifneq ($(IGNORE_SDK), y)
$(error RV1106 Sdk $(SDK_ROCKCHIP_RV1106_DIR_NAME) not found)
endif
endif
INC_LIB_SUFIX		=
CFLAGS				+=-DPLATFORM_RV1106
CFLAGS				+=-I$(HISIL_SDK)/extdrv/
CPPFLAGS			+=-DPLATFORM_RV1106
#CFLAGS 			+=-march=armv7-a -mcpu=cortex-a9
#CXXFlAGS 			+=-march=armv7-a -mcpu=cortex-a9
endif

# These CFLAGS should also define in DrvTmpl.mk for exdrvs
ifeq ($(SMALL_FLASH_SIZE), true)
export USE_UCLIBC	=true
#CFLAGS				+= -DHBFL_UCLIBC # TODO remove this flag (only used by mem manager module)
#CFLAGS				+= -DPLATFORM_UCLIBC
#CFLAGS 				+= -DPLATFORM_SPI_NORFLASH_16M # 16M nor
else
#CFLAGS				+= -DPLATFORM_GLIBC
#CFLAGS 			+= -DPLATFORM_SPI_NORFLASH_4M  # 4M nor + 256M nand
endif

########################################
#
# Setup Hisilicon include/lib path 
# Setup IC related compile flag
# 
########################################
HISIL_MPP_PATH:=$(HISIL_SDK)/mpp
HISIL_INC_PATH	=$(HISIL_MPP_PATH)/include$(INC_LIB_SUFIX)
HISIL_LIB_PATH	=$(HISIL_MPP_PATH)/lib$(INC_LIB_SUFIX)
HISIL_DRV_PATH	=$(HISIL_MPP_PATH)/ko$(INC_LIB_SUFIX)

CFLAGS			+=-I$(HISIL_INC_PATH) -L$(HISIL_LIB_PATH)
LDFLAGS			+=-L$(HISIL_LIB_PATH) -L$(HISIL_LIB_PATH)/extdrv
CFLAGS			+=-I$(HISIL_SDK)/mpp/extdrv/
#For liteos#
CFLAGS			+=-I$(HISIL_SDK)/osal/include
CFLAGS			+=-I$(HISIL_SDK)/mpp/component/isp/firmware/drv/
CFLAGS			+=-I$(HISIL_SDK)/drv/extdrv/sensor_spi/
#For ssc335#
CFLAGS			+=-I$(HISIL_SDK)/project/release/include -L$(HISIL_SDK)/project/release/ipc/i6b0/common/uclibc/4.9.4/mi_libs/dynamic -L$(HISIL_SDK)/project/release/ipc/i6b0/common/uclibc/4.9.4/ex_libs/dynamic

ifeq ($(ARCH), arm)
CFLAGS			+=	-mapcs 
endif

ifeq ($(BUILD_SLAVE_CHIP), true)
CFLAGS			+=	-DHB_SLAVE_CHIP
endif

export LD_FLAGS			= -L$(USRLIB_PATH) -L$(TOP_LIB) 
export AR_FLAGS			= -rc

# vim:noet:sw=4:ts=4
