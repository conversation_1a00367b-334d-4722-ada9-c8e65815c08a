#
# Link all library, and build the final excutable file
#

include ../../Makefile.param

# link pthread and math library
SYSTEM_LIB	= -lpthread -lm 

# Link SV Common library
SV_COM_LIBS =  -llog -lconfig  -lboard  -lcontrol -lmsg  -lcjson -lsafefunc  -lmxml

ifneq ($(findstring -DMAKE_STORAGE,$(CFLAGS) $(CPPFLAGS)), )
SV_COM_LIBS += -lstorage
endif

# Link other SV libs
OTHER_SV_LIBS 	=  

CFLAGS += -L$(TOP_LIB)
CPPFLAGS += $(CFLAGS)

TARGET_BIN	= wtdSwitch
LIB_DEPEND	= $(COMP_DEPEND)
LD_FLAGS	+=$(SYSTEM_LIB) $(OTHER_SV_LIBS) $(SV_COM_LIBS)

COPY_TO_DIR = $(BIN_PATH)
include $(BIN_AUTO_DEP_MK)

# vim:noet:sw=4:ts=4
