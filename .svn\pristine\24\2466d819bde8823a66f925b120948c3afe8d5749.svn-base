#!/bin/bash

if [[ $# -ne 4 ]];then
  echo "usage: $0 0 10000 5000 0"
  exit -1
fi
pwm_num=$1
period=$2
duty_cycle=$3
value=$4

echo 0 > /sys/class/pwm/pwmchip${pwm_num}/export
if [[ $? -eq 0 ]];then
  echo ${period} > /sys/class/pwm/pwmchip${pwm_num}/pwm0/period
  echo ${duty_cycle} > /sys/class/pwm/pwmchip${pwm_num}/pwm0/duty_cycle
  echo normal > /sys/class/pwm/pwmchip${pwm_num}/pwm0/polarity
  echo ${value} > /sys/class/pwm/pwmchip${pwm_num}/pwm0/enable
  if [[ $? -eq 0 ]];then
    echo "pwmchip${pwm_num}/pwm0 set to period ${period} duty ${duty_cycle} polarity normal enable ${value}"
  fi
  echo 0 > /sys/class/pwm/pwmchip$pwm_num/unexport
fi

