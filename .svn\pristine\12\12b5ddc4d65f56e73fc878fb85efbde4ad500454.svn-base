
#include "type_define.hpp"
#include "sd_ipv4_option.hpp"

sd_ipv4_option::sd_ipv4_option()
    : length_(VSOMEIP_SD_IPV4_OPTION_LENGTH), 
    type_(option_type_e::IP4_ENDPOINT),
    address_({0}),
    protocol_(protocol_e::UDP), 
    port_(0) {
}

sd_ipv4_option::~sd_ipv4_option() {
}

void sd_ipv4_option::set_ipaddr(const ipv4_address_t _ipaddr){
      address_ =_ipaddr; 
}

const ipv4_address_t & sd_ipv4_option::get_ipaddr() const {
    return address_;
}

void sd_ipv4_option::set_port(const uint16_t _port){
      port_ =_port; 
}

uint16_t sd_ipv4_option::get_port() const {
    return port_;
}

uint16_t sd_ipv4_option::get_length() const {
    return length_;
}

bool sd_ipv4_option::serialize(serializer *_to) const {
    bool is_successful = (0 != _to && _to->serialize(length_)
            && _to->serialize(static_cast<uint8_t>(type_))
            && _to->serialize(reserved_byte));
            
    _to->serialize(&address_[0], uint32_t(address_.size()));
    _to->serialize(reserved_byte);
    _to->serialize(static_cast<uint8_t>(protocol_));
    _to->serialize(port_);
    return is_successful;
}

bool sd_ipv4_option::deserialize(deserializer *_from) {
    uint8_t its_type, reserved;
    bool l_result = (0 != _from && _from->deserialize(length_)
            && _from->deserialize(its_type) && _from->deserialize(reserved));

    if (l_result) {
        switch(static_cast<option_type_e>(its_type)) {
            case option_type_e::CONFIGURATION:
            case option_type_e::LOAD_BALANCING:
            case option_type_e::PROTECTION:
            case option_type_e::IP4_ENDPOINT:
            case option_type_e::IP6_ENDPOINT:
            case option_type_e::IP4_MULTICAST:
            case option_type_e::IP6_MULTICAST:
            case option_type_e::SELECTIVE:
                type_ = static_cast<option_type_e>(its_type);
                break;
            default:
                type_ = option_type_e::UNKNOWN;
                // No valid option type --> ignore the remaining parts of the message!
                _from->set_remaining(0);
        }
    }
    
    bool is_successful =  l_result  && length_ == VSOMEIP_SD_IPV4_OPTION_LENGTH;
                            
    uint8_t its_reserved(static_cast<std::uint8_t>(protocol_e::UNKNOWN));
    _from->deserialize(address_.data(), 4);
    _from->deserialize(its_reserved);
    _from->deserialize(its_reserved);
    switch (static_cast<protocol_e>(its_reserved)) {
        case protocol_e::TCP:
        case protocol_e::UDP:
            protocol_ = static_cast<protocol_e>(its_reserved);
            break;
        default:
            protocol_ = protocol_e::UNKNOWN;
    }
    _from->deserialize(port_);
    return is_successful;
}

