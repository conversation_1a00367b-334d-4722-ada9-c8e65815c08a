#include <stdio.h>
#include "mpp_gui.h"
#include "mpp_pixel.h"
#include "mpp_protocol.h"
#include "common.h"
#include "print.h"
#include "math.h"
#include "board.h"
#include <jpeg/jpeglib.h>
#include <jpeg/jerror.h>
#define ALIGN16(x) ((((int)(x)) + 0xf) & 0xfffffff0)               /* 16字节向上对齐 */
#define ALIGN2(x)  ((((int)(x)) + 0x1) & 0xfffffffe)			   /* 2字节向上对齐 */
#define ALIGN4(x)  ((((int)(x)) + 0x3) & 0xfffffffc)			   /* 4字节向上对齐 */

#define ALIGN2D(x) (((int)(x) | 0x1) - 1)                          /* 2字节向下对齐 */


#define abs(x) ((x)<0? -(x) : (x))
#define sign(x) ( (x)<0? -1 : ( (x) > 0 ? 1 : 0 ) )
#define max(x, y) ((x) > (y) ? (x) : (y))
#define min(x, y) ((x) < (y) ? (x) : (y))

#define MACRO_PAINT_LINE_8BPP_TYPE  uint8
#define MACRO_PAINT_LINE_16BPP_TYPE uint16
#define MACRO_PAINT_LINE_24BPP_TYPE uint24
#define MACRO_PAINT_LINE_32BPP_TYPE uint32
#define MACRO_PAINT_LINE_8BPP_COLOR  u8color
#define MACRO_PAINT_LINE_16BPP_COLOR u16color
#define MACRO_PAINT_LINE_24BPP_COLOR u24color
#define MACRO_PAINT_LINE_32BPP_COLOR u32color

#define MACRO_PAINT_8BPP_TOKEN 8BPP
#define MACRO_PAINT_16BPP_TOKEN 16BPP
#define MACRO_PAINT_24BPP_TOKEN 24BPP
#define MACRO_PAINT_32BPP_TOKEN 32BPP

#define LINE_OVERLAP_NONE  0b000
#define LINE_OVERLAP_MAJOR 0b001
#define LINE_OVERLAP_MINOR 0b010


#define RUN_PAINT_LINE1_BPP(BPP) \
MPP_GUI_PAINT_LINE1__## BPP ## __

#define RUN_PAINT_LINE1_BPP_IN(BPP) \
MPP_GUI_PAINT_LINE1_## BPP ## _

#define RUN_PAINT_LINE_BPP(BPP) \
MPP_GUI_PAINT_LINE__## BPP ## __

#define DEFINE_PAINT_LINE1_BPP(BPP) \
sint32 MPP_GUI_PAINT_LINE1__## BPP ## __(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint_start, \
  MPP_GUI_POINT stPoint_end, void *pstColor, uint8 overlap) \
{\
    uint32 x_start, y_start, x_end, y_end;\
    sint32 tDeltaX, tDeltaY, tDeltaXTimes2, tDeltaYTimes2, tError, stepX, stepY;\
\
    MACRO_PAINT_LINE_ ## BPP ## _TYPE (*Filed)[pstGuiImage->Width], value;\
\
    x_start = max(0, min(stPoint_start.x, pstGuiImage->Width-1));\
    x_end   = max(0, min(stPoint_end.x, pstGuiImage->Width-1));\
    y_start = max(0, min(stPoint_start.y, pstGuiImage->Height-1));\
    y_end   = max(0, min(stPoint_end.y, pstGuiImage->Height-1));\
    Filed = pstGuiImage->pbmp;\
    value = *((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)pstColor);\
    overlap = overlap & LINE_OVERLAP_MAJOR;\
\
    tDeltaX = x_end - x_start;\
    tDeltaY = y_end - y_start;\
    if(tDeltaX < 0)\
    {\
        tDeltaX = -tDeltaX;\
        stepX = -1;\
    }\
    else{\
        stepX = +1;\
    }\
    if(tDeltaY < 0)\
    {\
        tDeltaY = -tDeltaY;\
        stepY = -1;\
    }\
    else\
    {\
        stepY = 1;\
    }\
\
    tDeltaXTimes2 = tDeltaX << 1;\
    tDeltaYTimes2 = tDeltaY << 1;\
\
    if (tDeltaX > tDeltaY)\
    {\
        Filed += y_start;\
        tError = tDeltaYTimes2 - tDeltaX;\
        while (x_start != x_end)\
        {\
            x_start += stepX;\
            if(tError >= 0)\
            {\
                if(overlap){\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed)[x_start] = value;\
                }\
                y_start += stepY;\
                Filed += stepY;\
                tError -= tDeltaXTimes2;\
            }\
            tError += tDeltaYTimes2;\
            ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed)[x_start] = value;\
        }\
    }\
    else\
    {\
        Filed += y_start;\
        tError = tDeltaXTimes2 - tDeltaY;\
        while (y_start != y_end)\
        {\
            y_start += stepY;\
            Filed += stepY;\
            if(tError >= 0)\
            {\
                if(overlap){\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed)[x_start] = value;\
                }\
                x_start += stepX;\
                tError -= tDeltaYTimes2;\
            }\
            tError += tDeltaXTimes2;\
            ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed)[x_start] = value;\
        }\
    }\
\
    return SV_SUCCESS;\
}

sint32 MPP_GUI_PAINT_LINE1__YUV420SP__(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint_start,
  MPP_GUI_POINT stPoint_end, void *pstColor, uint8 overlap)
{
    uint32 x_start, y_start, x_end, y_end, x_start_half, y_start_half;
    sint32 tDeltaX, tDeltaY, tDeltaXTimes2, tDeltaYTimes2, tError, stepX, stepY;
    uint8 (*YFiled)[pstGuiImage->Width], Yvalue;
    uint16 (*UVFiled)[pstGuiImage->Width >> 1], UVvalue;

    x_start = max(0, min(stPoint_start.x, pstGuiImage->Width-2));
    x_end   = max(0, min(stPoint_end.x, pstGuiImage->Width-2));
    y_start = max(0, min(stPoint_start.y, pstGuiImage->Height-2));
    y_end   = max(0, min(stPoint_end.y, pstGuiImage->Height-2));
    x_start_half = ALIGN2D(x_start)>>1;
    y_start_half = ALIGN2D(y_start)>>1;

    YFiled  = pstGuiImage->pbmp;
    UVFiled = pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height;

    Yvalue = ((uint8*)pstColor)[0];
    UVvalue = (((uint8*)pstColor)[1] & 0xff) | ((((uint8*)pstColor)[2] << 8) & 0xff00);

    overlap = overlap & LINE_OVERLAP_MAJOR;

    tDeltaX = x_end - x_start;
    tDeltaY = y_end - y_start;
    if(tDeltaX < 0)
    {
        tDeltaX = -tDeltaX;
        stepX = -1;
    }
    else{
        stepX = +1;
    }
    if(tDeltaY < 0)
    {
        tDeltaY = -tDeltaY;
        stepY = -1;
    }
    else
    {
        stepY = 1;
    }

    tDeltaXTimes2 = tDeltaX << 1;
    tDeltaYTimes2 = tDeltaY << 1;

    if (tDeltaX > tDeltaY)
    {
        YFiled += y_start;
        UVFiled += (y_start_half);
        tError = tDeltaYTimes2 - tDeltaX;
        while (x_start != x_end)
        {
            x_start += stepX;
            if(x_start%2)
            {
                x_start_half+=stepX;
            }
            if(tError >= 0)
            {
                if(overlap){
                    ((uint8 *)YFiled)[x_start] = Yvalue;
                    ((uint16 *)UVFiled)[x_start_half] = UVvalue;
                }
                y_start += stepY;
                YFiled += stepY;
                if(y_start%2)
                {
                    UVFiled += stepY;
                }
                    tError -= tDeltaXTimes2;
                }
            tError += tDeltaYTimes2;
            ((uint8 *)YFiled)[x_start] = Yvalue;
            ((uint16 *)UVFiled)[x_start_half] = UVvalue;
        }
    }
    else
    {
        YFiled += y_start;
        UVFiled += (y_start_half);
        tError = tDeltaXTimes2 - tDeltaY;
        while (y_start != y_end)
        {
            y_start += stepY;
            YFiled += stepY;
            if(y_start%2)
            {
                UVFiled += stepY;
            }
            if(tError >= 0)
            {
                if(overlap){
                    ((uint8 *)YFiled)[x_start] = Yvalue;
                    ((uint16 *)UVFiled)[x_start_half] = UVvalue;
                }
                x_start += stepX;
                tError -= tDeltaYTimes2;
                if(x_start%2)
                {
                    x_start_half+=stepX;
                }
            }
            tError += tDeltaXTimes2;
            ((uint8 *)YFiled)[x_start] = Yvalue;
            ((uint16 *)UVFiled)[x_start_half] = UVvalue;
        }
    }

    return SV_SUCCESS;
}


#if 1
#define DEFINE_PAINT_LINE_BPP(BPP) \
sint32 MPP_GUI_PAINT_LINE__## BPP ## __(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint_start, \
  MPP_GUI_POINT stPoint_end, void *pstColor, uint32 stick) \
{\
    uint32 x_start, y_start, x_end, y_end;\
    uint32 ax_start, ay_start;\
    sint32 i, tDeltaX, tDeltaY, tDeltaXTimes2, tDeltaYTimes2, tError, stepX, stepY;\
\
    x_start = max(stick, min(stPoint_start.x, pstGuiImage->Width-1-stick));\
    y_start = max(stick, min(stPoint_start.y, pstGuiImage->Height-1-stick));\
    x_end   = max(stick, min(stPoint_end.x, pstGuiImage->Width-1-stick));\
    y_end   = max(stick, min(stPoint_end.y, pstGuiImage->Height-1-stick));\
\
    /*交换X和Y*/\
    tDeltaY = x_end - x_start;\
    tDeltaX = y_start - y_end;\
    uint8 tSwap = 1;\
\
    if (tDeltaX < 0) {\
        tDeltaX = - tDeltaX;\
        stepX = -1;\
        tSwap = !tSwap;\
    }else{\
        stepX = 1;\
    }\
\
    if (tDeltaY < 0){\
        tDeltaY = -tDeltaY;\
        stepY = -1;\
        tSwap = !tSwap;\
    }else{\
        stepY = 1;\
    }\
\
    tDeltaXTimes2 = tDeltaX << 1;\
    tDeltaYTimes2 = tDeltaY << 1;\
    uint8 tOverlap;\
    int tDrawStartAdjustCount = stick >> 1;\
    /*int tDrawStartAdjustCount = 0;*/\
\
    /*所处八分圆的位置*/\
    if(tDeltaX >= tDeltaY){\
        if (tSwap) {\
            tDrawStartAdjustCount = (stick - 1) - tDrawStartAdjustCount;\
            stepY = -stepY;\
        }else{\
            stepX = -stepX;\
        }\
        tError = tDeltaYTimes2 - tDeltaX;\
        for (i = tDrawStartAdjustCount; i > 0; i--)\
        {\
            x_start -= stepX;\
            x_end -= stepX;\
            if(tError >= 0)\
            {\
                y_start += stepY;\
                y_end += stepY;\
                tError -= tDeltaXTimes2;\
            }\
            tError += tDeltaYTimes2;\
        }\
        stPoint_start.x = x_start;\
        stPoint_start.y = y_start;\
        stPoint_end.x = x_end;\
        stPoint_end.y = y_end;\
        RUN_PAINT_LINE1_BPP_IN(_ ## BPP ## _)(pstGuiImage, stPoint_start, stPoint_end, pstColor, LINE_OVERLAP_MAJOR);\
        /*DRAW*/\
        tError = tDeltaYTimes2 - tDeltaX;\
        for (i=stick; i > 1; i--)\
        {\
            x_start += stepX;\
            x_end += stepX;\
            tOverlap = LINE_OVERLAP_MAJOR;\
            if(tError >= 0)\
            {\
                y_start -= stepY;\
                y_end -= stepY;\
                tError -= tDeltaXTimes2;\
                tOverlap = LINE_OVERLAP_MAJOR;\
            }\
            tError += tDeltaYTimes2;\
            /*DRAW*/\
            stPoint_start.x = x_start;\
            stPoint_start.y = y_start;\
            stPoint_end.x = x_end;\
            stPoint_end.y = y_end;\
            RUN_PAINT_LINE1_BPP_IN(_ ## BPP ## _)(pstGuiImage, stPoint_start, stPoint_end, pstColor, tOverlap);\
        }\
    }\
    else{\
        if (tSwap){\
            stepX = -stepX;\
        }\
        else{\
            tDrawStartAdjustCount = (stick - 1) - tDrawStartAdjustCount;\
            stepY = -stepY;\
        }\
        tError = tDeltaXTimes2 - tDeltaY;\
        for (i = tDrawStartAdjustCount; i > 0; i--)\
        {\
            y_start -= stepY;\
            y_end -= stepY;\
            if(tError >= 0)\
            {\
                x_start += stepX;\
                x_end += stepX;\
                tError -= tDeltaYTimes2;\
            }\
            tError += tDeltaXTimes2;\
        }\
        /*DRAW*/\
        stPoint_start.x = x_start;\
        stPoint_start.y = y_start;\
        stPoint_end.x = x_end;\
        stPoint_end.y = y_end;\
        RUN_PAINT_LINE1_BPP_IN(_ ## BPP ## _)(pstGuiImage, stPoint_start, stPoint_end, pstColor, LINE_OVERLAP_MAJOR);\
        tError = tDeltaXTimes2 - tDeltaY;\
        for (i=stick; i > 1; i--)\
        {\
            y_start += stepY;\
            y_end += stepY;\
            tOverlap = LINE_OVERLAP_MAJOR;\
            if(tError >= 0)\
            {\
                x_start -= stepX;\
                x_end -= stepX;\
                tError -= tDeltaYTimes2;\
                tOverlap = LINE_OVERLAP_MAJOR;\
            }\
            tError += tDeltaXTimes2;\
            /*DRAW*/\
            stPoint_start.x = x_start;\
            stPoint_start.y = y_start;\
            stPoint_end.x = x_end;\
            stPoint_end.y = y_end;\
            RUN_PAINT_LINE1_BPP_IN(_ ## BPP ## _)(pstGuiImage, stPoint_start, stPoint_end, pstColor, tOverlap);\
        }\
\
    }\
\
    return SV_SUCCESS;\
}
#endif

/************************************************
 圆形圆域
              |
       \\  6  |  7   //
        \\+++++++++ //
    5  ++++   |   ++++  8
      +++ \\  |   // +++
     ++++  \\ |  //   ++++
    +++     \\|//     +++
----+++-------+-------+++----
    +++     //|\\      +++
     ++++  // | \\    ++++
      +++ //  |  \\  +++
    4  ++++   |   ++++  1
        //++++++++ \\
       //  3  |  2  \\
              |
************************************************/

#define RUN_PAINT_CIRCLE_BPP(BPP) \
MPP_GUI_PAINT_CIRCLE_## BPP ## _

#define DEFINE_PAINT_CIRCLE_BPP(BPP) \
sint32 MPP_GUI_PAINT_CIRCLE_## BPP ##_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint, \
    void *pstColor, uint32 radius, int fill, int part)\
{\
    sint32 pvalue;	/* 绘制圆形判别量 */\
    sint32 x_bias, y_bias, y_fill;\
    /*stPoint.x = max(0, min(stPoint.x, pstGuiImage->Width-1-radius));*/\
    /*stPoint.y = max(0, min(stPoint.y, pstGuiImage->Height-1-radius));*/\
\
    MACRO_PAINT_LINE_ ## BPP ## _TYPE (*Filed_plus_x)[pstGuiImage->Width], (*Filed_minus_x)[pstGuiImage->Width];\
    MACRO_PAINT_LINE_ ## BPP ## _TYPE (*Filed_plus_y)[pstGuiImage->Width], (*Filed_minus_y)[pstGuiImage->Width];\
    MACRO_PAINT_LINE_ ## BPP ## _TYPE (*Filed_plus_y_fill)[pstGuiImage->Width], (*Filed_minus_y_fill)[pstGuiImage->Width];\
    MACRO_PAINT_LINE_ ## BPP ## _TYPE  value;\
\
    Filed_plus_x  = pstGuiImage->pbmp + ((stPoint.y * pstGuiImage->Width) + stPoint.x) * sizeof(MACRO_PAINT_LINE_ ## BPP ## _TYPE);\
    Filed_minus_x = Filed_plus_x;\
    Filed_plus_y  = Filed_plus_x;\
    Filed_minus_y = Filed_plus_x;\
    value = *(MACRO_PAINT_LINE_ ## BPP ## _TYPE *)pstColor;\
\
    x_bias = 0;\
    y_bias = -radius;\
    pvalue = 3 - (radius >> 1);\
    Filed_plus_x += x_bias;\
    Filed_minus_x -= x_bias;\
    Filed_plus_y += y_bias;\
    Filed_minus_y -= y_bias;\
    if(fill)\
    {\
        while(x_bias <= -y_bias)\
        {\
            Filed_plus_y_fill  = Filed_plus_y;\
            Filed_minus_y_fill = Filed_minus_y;\
            for(y_fill = x_bias; y_fill <= -y_bias; y_fill++)\
            {\
                switch(part) \
                { \
                    case MPP_GUI_CIRCLE_DOWN: \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[y_fill]  = value; /* 圆域1 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y_fill)[x_bias]  = value; /* 圆域2 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y_fill)[-x_bias] = value; /* 圆域3 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[-y_fill] = value; /* 圆域4 */\
                        break; \
                    case MPP_GUI_CIRCLE_UP: \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[-y_fill] = value; /* 圆域5 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y_fill)[-x_bias] = value; /* 圆域6*/ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y_fill)[x_bias]  = value; /* 圆域7 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[y_fill]  = value; /* 圆域8 */\
                        break; \
                    case MPP_GUI_CIRCLE_LEFT: \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y_fill)[-x_bias] = value; /* 圆域3 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[-y_fill] = value; /* 圆域4 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[-y_fill] = value; /* 圆域5 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y_fill)[-x_bias] = value; /* 圆域6*/ \
                        break; \
                    case MPP_GUI_CIRCLE_RIGHT: \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[y_fill]  = value; /* 圆域1 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y_fill)[x_bias]  = value; /* 圆域2 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y_fill)[x_bias]  = value; /* 圆域7 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[y_fill]  = value; /* 圆域8 */ \
                        break; \
                    case MPP_GUI_CIRCLE_TOTAL: \
                    default: \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[y_fill]  = value; /* 圆域1 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y_fill)[x_bias]  = value; /* 圆域2 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y_fill)[-x_bias] = value; /* 圆域3 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[-y_fill] = value; /* 圆域4 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[-y_fill] = value; /* 圆域5 */\
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y_fill)[-x_bias] = value; /* 圆域6*/ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y_fill)[x_bias]  = value; /* 圆域7 */ \
                        ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[y_fill]  = value; /* 圆域8 */ \
                        break; \
                } \
                Filed_plus_y_fill++;\
                Filed_minus_y_fill--;\
            }\
            if(pvalue < 0)\
            {\
                pvalue += (x_bias << 2) + 6;\
            }\
            else\
            {\
                pvalue += ((x_bias + y_bias) << 2) + 10;\
                y_bias++;\
                Filed_plus_y++;\
                Filed_minus_y--;\
            }\
            x_bias++;\
            Filed_plus_x++;\
            Filed_minus_x--;\
        }\
    }\
    else\
    {\
        while(x_bias <= -y_bias)\
        {\
            switch(part) \
            { \
                case MPP_GUI_CIRCLE_DOWN: \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[-y_bias]  = value; /* 圆域1 */ \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y)[x_bias]  = value; /* 圆域2 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y)[-x_bias] = value; /* 圆域3 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[y_bias] = value; /* 圆域4 */ \
                    break; \
                case MPP_GUI_CIRCLE_UP: \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[y_bias] = value; /* 圆域5 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y)[-x_bias] = value; /* 圆域6 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y)[x_bias]  = value; /* 圆域7 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[-y_bias]  = value; /* 圆域8 */\
                    break; \
                case MPP_GUI_CIRCLE_LEFT: \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y)[-x_bias] = value; /* 圆域3 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[y_bias] = value; /* 圆域4 */ \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[y_bias] = value; /* 圆域5 */ \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y)[-x_bias] = value; /* 圆域6 */ \
                    break; \
                case MPP_GUI_CIRCLE_RIGHT: \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[-y_bias]  = value; /* 圆域1 */ \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y)[x_bias]  = value; /* 圆域2 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y)[x_bias]  = value; /* 圆域7 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[-y_bias]  = value; /* 圆域8 */\
                    break; \
                case MPP_GUI_CIRCLE_TOTAL: \
                default: \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[-y_bias]  = value; /* 圆域1 */ \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y)[x_bias]  = value; /* 圆域2 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_y)[-x_bias] = value; /* 圆域3 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_x)[y_bias] = value; /* 圆域4 */ \
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[y_bias] = value; /* 圆域5 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y)[-x_bias] = value; /* 圆域6 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_plus_y)[x_bias]  = value; /* 圆域7 */\
                    ((MACRO_PAINT_LINE_ ## BPP ## _TYPE *)Filed_minus_x)[-y_bias]  = value; /* 圆域8 */\
            } \
            if(pvalue < 0)\
            {\
                pvalue += (x_bias << 2) + 6;\
            }\
            else\
            {\
                pvalue += ((x_bias + y_bias) << 2) + 10;\
                y_bias++;\
                Filed_plus_y++;\
                Filed_minus_y--;\
            }\
            x_bias++;\
            Filed_plus_x++;\
            Filed_minus_x--;\
        }\
    }\
\
    return SV_SUCCESS;\
}


sint32 MPP_GUI_PAINT_CIRCLE_YUV420SP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  void *pstColor, uint32 radius, int fill, int part)
{
    sint32 pvalue;	/* 绘制圆形判别量 */
    sint32 x_bias, y_bias, x_bias_half, y_bias_half, y_fill, y_fill_half;

    uint8 (*YFiled_plus_x)[pstGuiImage->Width], (*YFiled_minus_x)[pstGuiImage->Width];
    uint8 (*YFiled_plus_y)[pstGuiImage->Width], (*YFiled_minus_y)[pstGuiImage->Width];
    uint8 (*YFiled_plus_y_fill)[pstGuiImage->Width], (*YFiled_minus_y_fill)[pstGuiImage->Width];

    uint16 (*UVFiled_plus_x)[pstGuiImage->Width>>1], (*UVFiled_minus_x)[pstGuiImage->Width>>1];
    uint16 (*UVFiled_plus_y)[pstGuiImage->Width>>1], (*UVFiled_minus_y)[pstGuiImage->Width>>1];
    uint16 (*UVFiled_plus_y_fill)[pstGuiImage->Width>>1], (*UVFiled_minus_y_fill)[pstGuiImage->Width>>1];

    uint8 Yvalue;
    uint16 UVvalue;

    stPoint.x = ALIGN2D(stPoint.x);
    stPoint.y = ALIGN2D(stPoint.y);

    switch (part)
    {
        case MPP_GUI_CIRCLE_UP:
            stPoint.x = max(radius+1, min(stPoint.x, pstGuiImage->Width-1-radius));
            stPoint.y = max(radius+1, min(stPoint.y, pstGuiImage->Height-1));
            break;
        case MPP_GUI_CIRCLE_DOWN:
            stPoint.x = max(radius+1, min(stPoint.x, pstGuiImage->Width-1-radius));
            stPoint.y = max(1, min(stPoint.y, pstGuiImage->Height-1-radius));
            break;
        case MPP_GUI_CIRCLE_LEFT:
            stPoint.x = max(radius+1, min(stPoint.x, pstGuiImage->Width-1));
            stPoint.y = max(radius+1, min(stPoint.y, pstGuiImage->Height-1-radius));
            break;
        case MPP_GUI_CIRCLE_RIGHT:
            stPoint.x = max(1, min(stPoint.x, pstGuiImage->Width-1-radius));
            stPoint.y = max(radius+1, min(stPoint.y, pstGuiImage->Height-1-radius));
            break;
        case MPP_GUI_CIRCLE_TOTAL:
            stPoint.x = max(radius+1, min(stPoint.x, pstGuiImage->Width-1-radius));
            stPoint.y = max(radius+1, min(stPoint.y, pstGuiImage->Height-1-radius));
            break;
    }

    /* 偏移找到圆心的Y分量所在位置，YFiled_plus_x，YFiled_minus_x，YFiled_plus_y，YFiled_minus_y初始化都是这个圆心的Y分量位置 */
    YFiled_plus_x  = pstGuiImage->pbmp + ((stPoint.y * pstGuiImage->Width) + stPoint.x) * sizeof(uint8);
    YFiled_minus_x = YFiled_plus_x;
    YFiled_plus_y  = YFiled_plus_x;
    YFiled_minus_y = YFiled_plus_x;

    /* 偏移找到圆心的UV分量所在位置，UVFiled_plus_x，UVFiled_minus_x，UVFiled_plus_y，UVFiled_minus_y初始化都是这个圆心的UV分量位置 */
    UVFiled_plus_x  = pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height + (((stPoint.y>>2) * pstGuiImage->Width)  + (stPoint.x>>1)) * sizeof(uint16);
    UVFiled_minus_x = UVFiled_plus_x;
    UVFiled_plus_y  = UVFiled_plus_x;
    UVFiled_minus_y = UVFiled_plus_x;
    Yvalue = ((uint8*)pstColor)[0];
    UVvalue = (((uint8*)pstColor)[1] & 0xff) | ((((uint8*)pstColor)[2] << 8) & 0xff00);

    x_bias = 0, x_bias_half = 0;
    y_bias = -radius;
    y_bias_half = -(radius / 2);
    YFiled_plus_x += x_bias;            // 指向圆心Y分量位置，往下半圆走，用于圆域1和圆域4
    YFiled_minus_x -= x_bias;           // 指向圆心Y分量位置，往上半圆走，用于圆域5和圆域8
    YFiled_plus_y += y_bias;            // 指向圆心上方最远端点的Y分量位置，用于圆域6和圆域7
    YFiled_minus_y -= y_bias;           // 指向圆心下方最远端点的Y分量位置，用于圆域2和圆域3
    UVFiled_plus_x += x_bias_half;
    UVFiled_minus_x -= x_bias_half;
    UVFiled_plus_y += y_bias_half;
    UVFiled_minus_y -= y_bias_half;
    pvalue = 3 - (radius >> 1);

    if(fill)
    {
        ((uint16*)UVFiled_plus_x)[0] = UVvalue;
        if(radius > 2)
        {
            ((uint16*)(UVFiled_plus_x+1))[1] = UVvalue;
            ((uint16*)(UVFiled_plus_x+1))[-1] = UVvalue;
            ((uint16*)(UVFiled_plus_x-1))[1] = UVvalue;
            ((uint16*)(UVFiled_plus_x-1))[-1] = UVvalue;
        }

        while(x_bias <= -y_bias)
        {
            YFiled_plus_y_fill  = YFiled_plus_y;
            YFiled_minus_y_fill = YFiled_minus_y;
            UVFiled_plus_y_fill = UVFiled_plus_y;
            UVFiled_minus_y_fill = UVFiled_minus_y;
            for(y_fill = x_bias; y_fill <= -y_bias; y_fill++)
            {
                switch(part)
                {
                    case MPP_GUI_CIRCLE_DOWN:
                        ((uint8*)YFiled_plus_x)[y_fill]   = Yvalue; /* 圆域1 */
                        ((uint8*)YFiled_minus_y_fill)[x_bias]  = Yvalue; /* 圆域2 */
                        ((uint8*)YFiled_minus_y_fill)[-x_bias]  = Yvalue; /* 圆域3 */
                        ((uint8*)YFiled_plus_x)[-y_fill] = Yvalue; /* 圆域4 */
                        break;
                    case MPP_GUI_CIRCLE_UP:
                        ((uint8*)YFiled_minus_x)[-y_fill]   = Yvalue; /* 圆域5 */
                        ((uint8*)YFiled_plus_y_fill)[-x_bias]   = Yvalue; /* 圆域6 */
                        ((uint8*)YFiled_plus_y_fill)[x_bias]   = Yvalue; /* 圆域7 */
                        ((uint8*)YFiled_minus_x)[y_fill]   = Yvalue; /* 圆域8 */
                        break;
                    case MPP_GUI_CIRCLE_LEFT:
                        ((uint8*)YFiled_minus_y_fill)[-x_bias]  = Yvalue; /* 圆域3 */
                        ((uint8*)YFiled_plus_x)[-y_fill] = Yvalue; /* 圆域4 */
                        ((uint8*)YFiled_minus_x)[-y_fill]   = Yvalue; /* 圆域5 */
                        ((uint8*)YFiled_plus_y_fill)[-x_bias]   = Yvalue; /* 圆域6 */
                        break;
                    case MPP_GUI_CIRCLE_RIGHT:
                        ((uint8*)YFiled_plus_x)[y_fill]   = Yvalue; /* 圆域1 */
                        ((uint8*)YFiled_minus_y_fill)[x_bias]  = Yvalue; /* 圆域2 */
                        ((uint8*)YFiled_plus_y_fill)[x_bias]   = Yvalue; /* 圆域7 */
                        ((uint8*)YFiled_minus_x)[y_fill]   = Yvalue; /* 圆域8 */
                        break;
                    case MPP_GUI_CIRCLE_TOTAL:
                    default:
                        ((uint8*)YFiled_plus_x)[y_fill]   = Yvalue; /* 圆域1 */
                        ((uint8*)YFiled_minus_y_fill)[x_bias]  = Yvalue; /* 圆域2 */
                        ((uint8*)YFiled_minus_y_fill)[-x_bias]  = Yvalue; /* 圆域3 */
                        ((uint8*)YFiled_plus_x)[-y_fill] = Yvalue; /* 圆域4 */
                        ((uint8*)YFiled_minus_x)[-y_fill]   = Yvalue; /* 圆域5 */
                        ((uint8*)YFiled_plus_y_fill)[-x_bias]   = Yvalue; /* 圆域6 */
                        ((uint8*)YFiled_plus_y_fill)[x_bias]   = Yvalue; /* 圆域7 */
                        ((uint8*)YFiled_minus_x)[y_fill]   = Yvalue; /* 圆域8 */
                        break;
                }
                YFiled_plus_y_fill++;
                YFiled_minus_y_fill--;
            }
            if(x_bias % 2 == 0)
            {
                for(y_fill_half = x_bias_half; y_fill_half <= -y_bias_half; y_fill_half++)
                {
                    switch(part)
                    {
                        case MPP_GUI_CIRCLE_DOWN:
                            ((uint16*)UVFiled_plus_x)[y_fill_half]   = UVvalue; /* 圆域1 */
                            ((uint16*)UVFiled_minus_y_fill)[x_bias_half]  = UVvalue; /* 圆域2 */
                            ((uint16*)UVFiled_minus_y_fill)[-x_bias_half]  = UVvalue; /* 圆域3 */
                            ((uint16*)UVFiled_plus_x)[-y_fill_half] = UVvalue; /* 圆域4 */
                            break;
                        case MPP_GUI_CIRCLE_UP:
                            ((uint16*)UVFiled_minus_x)[-y_fill_half]   = UVvalue; /* 圆域5 */
                            ((uint16*)UVFiled_plus_y_fill)[-x_bias_half]  = UVvalue; /* 圆域6 */
                            ((uint16*)UVFiled_plus_y_fill)[x_bias_half]  = UVvalue; /* 圆域7 */
                            ((uint16*)UVFiled_minus_x)[y_fill_half] = UVvalue; /* 圆域8 */
                            break;
                        case MPP_GUI_CIRCLE_LEFT:
                            ((uint16*)UVFiled_minus_y_fill)[-x_bias_half]  = UVvalue; /* 圆域3 */
                            ((uint16*)UVFiled_plus_x)[-y_fill_half] = UVvalue; /* 圆域4 */
                            ((uint16*)UVFiled_minus_x)[-y_fill_half]   = UVvalue; /* 圆域5 */
                            ((uint16*)UVFiled_plus_y_fill)[-x_bias_half]  = UVvalue; /* 圆域6 */
                            break;
                        case MPP_GUI_CIRCLE_RIGHT:
                            ((uint16*)UVFiled_plus_x)[y_fill_half]   = UVvalue; /* 圆域1 */
                            ((uint16*)UVFiled_minus_y_fill)[x_bias_half]  = UVvalue; /* 圆域2 */
                            ((uint16*)UVFiled_plus_y_fill)[x_bias_half]  = UVvalue; /* 圆域7 */
                            ((uint16*)UVFiled_minus_x)[y_fill_half] = UVvalue; /* 圆域8 */
                            break;
                        case MPP_GUI_CIRCLE_TOTAL:
                        default:
                            ((uint16*)UVFiled_plus_x)[y_fill_half]   = UVvalue; /* 圆域1 */
                            ((uint16*)UVFiled_minus_y_fill)[x_bias_half]  = UVvalue; /* 圆域2 */
                            ((uint16*)UVFiled_minus_y_fill)[-x_bias_half]  = UVvalue; /* 圆域3 */
                            ((uint16*)UVFiled_plus_x)[-y_fill_half] = UVvalue; /* 圆域4 */
                            ((uint16*)UVFiled_minus_x)[-y_fill_half]   = UVvalue; /* 圆域5 */
                            ((uint16*)UVFiled_plus_y_fill)[-x_bias_half]  = UVvalue; /* 圆域6 */
                            ((uint16*)UVFiled_plus_y_fill)[x_bias_half]  = UVvalue; /* 圆域7 */
                            ((uint16*)UVFiled_minus_x)[y_fill_half] = UVvalue; /* 圆域8 */
                            break;
                    }
                    UVFiled_plus_y_fill++;
                    UVFiled_minus_y_fill--;
                }
            }

            if(pvalue < 0)
            {
                pvalue += (x_bias << 2) + 6;
            }
            else
            {
                pvalue += ((x_bias + y_bias) << 2) + 10;
                y_bias++;
                YFiled_plus_y++;
                YFiled_minus_y--;
                if(y_bias % 2 == 0)
                {
                    UVFiled_plus_y++;
                    UVFiled_minus_y--;
                    y_bias_half++;
                }
            }

            x_bias++;
            YFiled_plus_x++;
            YFiled_minus_x--;
            if(x_bias % 2 == 0)
            {
                UVFiled_plus_x++;
                UVFiled_minus_x--;
                x_bias_half++;
            }
        }
    }
    else
    {
        while(x_bias <= -y_bias)
        {
            if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_DOWN)
            {
                ((uint8*)YFiled_plus_x)[y_bias]     = Yvalue; /* 圆域1 */
                ((uint8*)YFiled_minus_y)[x_bias]    = Yvalue; /* 圆域2 */
                ((uint8*)YFiled_minus_y)[-x_bias]   = Yvalue; /* 圆域3 */
                ((uint8*)YFiled_plus_x)[-y_bias]    = Yvalue; /* 圆域4 */

                ((uint8*)YFiled_plus_x)[y_bias+1]     = Yvalue; /* 圆域1 */
                ((uint8*)YFiled_minus_y)[x_bias+1]    = Yvalue; /* 圆域2 */
                ((uint8*)YFiled_minus_y)[-x_bias-1]   = Yvalue; /* 圆域3 */
                ((uint8*)YFiled_plus_x)[-y_bias-1]    = Yvalue; /* 圆域4 */

                ((uint8*)(YFiled_plus_x+1))[y_bias]     = Yvalue; /* 圆域1 */
                ((uint8*)(YFiled_minus_y-1))[x_bias]    = Yvalue; /* 圆域2 */
                ((uint8*)(YFiled_minus_y-1))[-x_bias]   = Yvalue; /* 圆域3 */
                ((uint8*)(YFiled_plus_x+1))[-y_bias]    = Yvalue; /* 圆域4 */

                ((uint8*)(YFiled_plus_x+1))[y_bias+1]     = Yvalue; /* 圆域1 */
                ((uint8*)(YFiled_minus_y-1))[x_bias+1]    = Yvalue; /* 圆域2 */
                ((uint8*)(YFiled_minus_y-1))[-x_bias-1]   = Yvalue; /* 圆域3 */
                ((uint8*)(YFiled_plus_x+1))[-y_bias-1]    = Yvalue; /* 圆域4 */

            }

            /* 以UP为说明：从三个点往中间收缩，这三个点分别为底下带@的三个点
               分别为@1 @2 @3，每次对每一个@点附近的四个像素点都赋值修改

                       \\  6  @2  7   //
                        \\+++++++++ //
                    5  ++++   |   ++++  8
                      +++ \\  |   // +++
                     ++++  \\ |  //   ++++
                    +++     \\|//     +++
                @1---+++-------+-------+++---@3

                下面注释中以第一次循环说明点的偏移方向和走势
            */
            if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_UP)
            {
                ((uint8*)YFiled_minus_x)[-y_bias]   = Yvalue; /* 圆域5, @3 */
                ((uint8*)YFiled_plus_y)[-x_bias]    = Yvalue; /* 圆域6, @2 */
                ((uint8*)YFiled_plus_y)[x_bias]     = Yvalue; /* 圆域7, @2 */
                ((uint8*)YFiled_minus_x)[y_bias]    = Yvalue; /* 圆域8, @1 */

                ((uint8*)YFiled_minus_x)[-y_bias-1]   = Yvalue; /* 圆域5, @3-1，往左一个点 */
                ((uint8*)YFiled_plus_y)[-x_bias-1]    = Yvalue; /* 圆域6, @2-1，往左一个点 */
                ((uint8*)YFiled_plus_y)[x_bias+1]     = Yvalue; /* 圆域7,   @2+1，往右一个点*/
                ((uint8*)YFiled_minus_x)[y_bias+1]    = Yvalue; /* 圆域8，@1+1，往右一个点 */

                ((uint8*)(YFiled_minus_x-1))[-y_bias]   = Yvalue; /* 圆域5，@3-pstGuiImage->Width，上一个点 */
                ((uint8*)(YFiled_plus_y+1))[-x_bias]    = Yvalue; /* 圆域6，@2+pstGuiImage->Width，下一个点 */
                ((uint8*)(YFiled_plus_y+1))[x_bias]     = Yvalue; /* 圆域7，@2+pstGuiImage->Width，下一个点 */
                ((uint8*)(YFiled_minus_x-1))[y_bias]    = Yvalue; /* 圆域8，@1-pstGuiImage->Width，上一个点 */

                ((uint8*)(YFiled_minus_x-1))[-y_bias-1]   = Yvalue; /* 圆域5，@3-pstGuiImage->Width，左上一个点 */
                ((uint8*)(YFiled_plus_y+1))[-x_bias-1]    = Yvalue; /* 圆域6，@2+pstGuiImage->Width，左下一个点 */
                ((uint8*)(YFiled_plus_y+1))[x_bias+1]     = Yvalue; /* 圆域7，@2+pstGuiImage->Width，右下一个点 */
                ((uint8*)(YFiled_minus_x-1))[y_bias+1]    = Yvalue; /* 圆域8，@1-pstGuiImage->Width，右上一个点 */
            }

            if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_LEFT)
            {
                ((uint8*)YFiled_minus_y)[-x_bias]       = Yvalue;   /* 圆域3 */
                ((uint8*)YFiled_plus_x)[y_bias]        = Yvalue;    /* 圆域4 */
                ((uint8*)YFiled_minus_x)[y_bias+1]     = Yvalue;    /* 圆域5 */
                ((uint8*)YFiled_plus_y)[-x_bias-1]      = Yvalue;   /* 圆域6 */

                ((uint8*)YFiled_minus_y)[-x_bias-1]     = Yvalue;   /* 圆域3 */
                ((uint8*)YFiled_plus_x)[y_bias+1]      = Yvalue;    /* 圆域4 */
                ((uint8*)YFiled_minus_x)[y_bias+1]     = Yvalue;    /* 圆域5 */
                ((uint8*)YFiled_plus_y)[-x_bias-1]      = Yvalue;   /* 圆域6 */

                ((uint8*)(YFiled_minus_y-1))[-x_bias]   = Yvalue;   /* 圆域3 */
                ((uint8*)(YFiled_plus_x+1))[y_bias]    = Yvalue;    /* 圆域4 */
                ((uint8*)(YFiled_minus_x-1))[y_bias]   = Yvalue;    /* 圆域5 */
                ((uint8*)(YFiled_plus_y+1))[-x_bias]    = Yvalue;   /* 圆域6 */

                ((uint8*)(YFiled_minus_y-1))[-x_bias-1] = Yvalue;   /* 圆域3 */
                ((uint8*)(YFiled_plus_x+1))[y_bias+1]  = Yvalue;    /* 圆域4 */
                ((uint8*)(YFiled_minus_x-1))[y_bias+1] = Yvalue;    /* 圆域5 */
                ((uint8*)(YFiled_plus_y+1))[-x_bias-1]  = Yvalue;   /* 圆域6 */
            }

            if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_RIGHT)
            {
                ((uint8*)YFiled_plus_x)[-y_bias]        = Yvalue;   /* 圆域1 */
                ((uint8*)YFiled_minus_y)[x_bias]        = Yvalue;   /* 圆域2 */
                ((uint8*)YFiled_plus_y)[x_bias]         = Yvalue;   /* 圆域7 */
                ((uint8*)YFiled_minus_x)[-y_bias]       = Yvalue;   /* 圆域8 */

                ((uint8*)YFiled_plus_x)[-y_bias-1]      = Yvalue;   /* 圆域1 */
                ((uint8*)YFiled_minus_y)[x_bias+1]      = Yvalue;   /* 圆域2 */
                ((uint8*)YFiled_plus_y)[x_bias+1]       = Yvalue;   /* 圆域7 */
                ((uint8*)YFiled_minus_x)[-y_bias-1]     = Yvalue;   /* 圆域8 */

                ((uint8*)(YFiled_plus_x+1))[-y_bias]    = Yvalue;   /* 圆域1 */
                ((uint8*)(YFiled_minus_y-1))[x_bias]    = Yvalue;   /* 圆域2 */
                ((uint8*)(YFiled_plus_y+1))[x_bias]     = Yvalue;   /* 圆域7 */
                ((uint8*)(YFiled_minus_x-1))[-y_bias]   = Yvalue;   /* 圆域8 */

                ((uint8*)(YFiled_plus_x+1))[-y_bias-1]  = Yvalue;   /* 圆域1 */
                ((uint8*)(YFiled_minus_y-1))[x_bias+1]  = Yvalue;   /* 圆域2 */
                ((uint8*)(YFiled_plus_y+1))[x_bias+1]   = Yvalue;   /* 圆域7 */
                ((uint8*)(YFiled_minus_x-1))[-y_bias-1] = Yvalue;   /* 圆域8 */
            }

            /* 每2次Y分量赋值完再处理UV分量，符合YUV420SP协议，要注意的是第一次就要处理UV分量，否则会出现颜色填充不能完全闭合的情况。所以这里要用x_bias % 2 == 0而不是x_bias % 2  */
            if (x_bias % 2 == 0)
            {
                if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_DOWN)
                {
                    ((uint16*)UVFiled_plus_x)[y_bias_half]      = UVvalue; /* 圆域1 */

                    ((uint16*)UVFiled_plus_x)[y_bias_half+1]      = UVvalue;

                    ((uint16*)UVFiled_minus_y)[x_bias_half]     = UVvalue; /* 圆域2 */
                    ((uint16*)UVFiled_minus_y)[-x_bias_half]    = UVvalue; /* 圆域3 */
                    ((uint16*)UVFiled_plus_x)[-y_bias_half]     = UVvalue; /* 圆域4 */

#if 0
                    ((uint16*)UVFiled_plus_x)[y_bias_half+1]      = UVvalue; /* 圆域1 */
                    ((uint16*)UVFiled_minus_y)[x_bias_half+1]     = UVvalue; /* 圆域2 */
                    ((uint16*)UVFiled_minus_y)[-x_bias_half-1]    = UVvalue; /* 圆域3 */
                    ((uint16*)UVFiled_plus_x)[-y_bias_half-1]     = UVvalue; /* 圆域4 */
#endif
                }

                if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_UP)
                {
                    ((uint16*)UVFiled_minus_x)[-y_bias_half]    = UVvalue; /* 圆域5 */
                    ((uint16*)UVFiled_plus_y)[-x_bias_half]     = UVvalue; /* 圆域6 */
                    ((uint16*)UVFiled_plus_y)[x_bias_half]      = UVvalue; /* 圆域7 */
                    ((uint16*)UVFiled_minus_x)[y_bias_half]     = UVvalue; /* 圆域8*/

#if 0
                    ((uint16*)UVFiled_minus_x)[-y_bias_half+1]    = UVvalue; /* 圆域5 */
                    ((uint16*)UVFiled_plus_y)[-x_bias_half+1]     = UVvalue; /* 圆域6 */
                    ((uint16*)UVFiled_plus_y)[x_bias_half-1]      = UVvalue; /* 圆域7 */
                    ((uint16*)UVFiled_minus_x)[y_bias_half-1]     = UVvalue; /* 圆域8*/

                    ((uint16*)UVFiled_minus_x)[-y_bias_half-1]    = UVvalue; /* 圆域5 */
                    ((uint16*)UVFiled_plus_y)[-x_bias_half-1]     = UVvalue; /* 圆域6 */
                    ((uint16*)UVFiled_plus_y)[x_bias_half+1]      = UVvalue; /* 圆域7 */
                    ((uint16*)UVFiled_minus_x)[y_bias_half+1]     = UVvalue; /* 圆域8*/

                    ((uint16*)UVFiled_minus_x-1)[-y_bias_half]    = UVvalue; /* 圆域5 */
                    ((uint16*)UVFiled_plus_y+1)[-x_bias_half]     = UVvalue; /* 圆域6 */
                    ((uint16*)UVFiled_plus_y+1)[x_bias_half]      = UVvalue; /* 圆域7 */
                    ((uint16*)UVFiled_minus_x-1)[y_bias_half]     = UVvalue; /* 圆域8*/

                    ((uint16*)UVFiled_minus_x+1)[-y_bias_half]    = UVvalue; /* 圆域5 */
                    ((uint16*)UVFiled_plus_y-1)[-x_bias_half]     = UVvalue; /* 圆域6 */
                    ((uint16*)UVFiled_plus_y-1)[x_bias_half]      = UVvalue; /* 圆域7 */
                    ((uint16*)UVFiled_minus_x+1)[y_bias_half]     = UVvalue; /* 圆域8*/

                    ((uint16*)UVFiled_minus_x+1)[-y_bias_half+1]    = UVvalue; /* 圆域5 */
                    ((uint16*)UVFiled_plus_y-1)[-x_bias_half+1]     = UVvalue; /* 圆域6 */
                    ((uint16*)UVFiled_plus_y-1)[x_bias_half-1]      = UVvalue; /* 圆域7 */
                    ((uint16*)UVFiled_minus_x+1)[y_bias_half-1]     = UVvalue; /* 圆域8*/
#endif
                }

                if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_LEFT)
                {
                    ((uint16*)UVFiled_minus_y)[-x_bias_half]    = UVvalue; /* 圆域3 */
                    ((uint16*)UVFiled_plus_x)[y_bias_half]      = UVvalue; /* 圆域4 */
                    ((uint16*)UVFiled_minus_x)[y_bias_half]     = UVvalue; /* 圆域5 */
                    ((uint16*)UVFiled_plus_y)[-x_bias_half]     = UVvalue; /* 圆域6 */
                }

                if(part == MPP_GUI_CIRCLE_TOTAL || part == MPP_GUI_CIRCLE_RIGHT)
                {
                    ((uint16*)UVFiled_plus_x)[-y_bias_half]     = UVvalue; /* 圆域1 */
                    ((uint16*)UVFiled_minus_y)[x_bias_half]     = UVvalue; /* 圆域2 */
                    ((uint16*)UVFiled_plus_y)[x_bias_half]      = UVvalue; /* 圆域7 */
                    ((uint16*)UVFiled_minus_x)[-y_bias_half]    = UVvalue; /* 圆域8*/
                }
            }

            if(pvalue < 0)
            {
                pvalue += (x_bias << 2) + 6;
            }
            else
            {
                pvalue += ((x_bias + y_bias) << 2) + 10;
                y_bias++;
                YFiled_plus_y++;
                YFiled_minus_y--;
                if(y_bias % 2)
                {
                    UVFiled_plus_y++;
                    UVFiled_minus_y--;
                    y_bias_half++;
                }
            }

            x_bias++;
            YFiled_plus_x++;
            YFiled_minus_x--;
            if(x_bias % 2)
            {
                UVFiled_plus_x++;
                UVFiled_minus_x--;
                x_bias_half++;
            }
        }
    }

    return SV_SUCCESS;
}

sint32 MPP_GUI_PAINT_ELLIPSE_8BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint8 *pstColor, sint32 sA, sint32 sB, MPP_GUI_CIRCLE_PART partion)
{
    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    SV_BOOL bUpPart = SV_FALSE, bDownPart= SV_FALSE;

    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    uint8 (*Filed_plus_y)[pstGuiImage->Width];
    uint8 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint8);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;

    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_UP)
        bUpPart = SV_TRUE;
    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_DOWN)
        bDownPart = SV_TRUE;

    do
    {
        /* 颜色填充 */
        if(bUpPart)
        {
            ((uint8 *)Filed_plus_y)[sX] = *pstColor;
            ((uint8 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bDownPart)
        {
            ((uint8 *)Filed_minus_y)[sX] = *pstColor;
            ((uint8 *)Filed_minus_y)[-sX] = *pstColor;
        }

        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);

        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        /* 颜色填充 */
        if(bDownPart)
        {
            ((uint8 *)Filed_plus_y)[sX] = *pstColor;
            ((uint8 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bUpPart)
        {
            ((uint8 *)Filed_minus_y)[sX] = *pstColor;
            ((uint8 *)Filed_minus_y)[-sX] = *pstColor;
        }


        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}

sint32 MPP_GUI_PAINT_ELLIPSE_16BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint16 *pstColor, sint32 sA, sint32 sB, MPP_GUI_CIRCLE_PART partion)
{
    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    SV_BOOL bUpPart = SV_FALSE, bDownPart= SV_FALSE;

    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    uint16 (*Filed_plus_y)[pstGuiImage->Width];
    uint16 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint16);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;

    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_UP)
        bUpPart = SV_TRUE;
    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_DOWN)
        bDownPart = SV_TRUE;

    do
    {
        /* 颜色填充 */
        if(bUpPart)
        {
            ((uint8 *)Filed_plus_y)[sX] = *pstColor;
            ((uint8 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bDownPart)
        {
            ((uint8 *)Filed_minus_y)[sX] = *pstColor;
            ((uint8 *)Filed_minus_y)[-sX] = *pstColor;
        }


        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);

        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        /* 颜色填充 */
        if(bDownPart)
        {
            ((uint8 *)Filed_plus_y)[sX] = *pstColor;
            ((uint8 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bUpPart)
        {
            ((uint8 *)Filed_minus_y)[sX] = *pstColor;
            ((uint8 *)Filed_minus_y)[-sX] = *pstColor;
        }


        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}

sint32 MPP_GUI_PAINT_ELLIPSE_24BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint24 *pstColor, sint32 sA, sint32 sB, MPP_GUI_CIRCLE_PART partion)
{
    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    SV_BOOL bUpPart = SV_FALSE, bDownPart= SV_FALSE;

    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    uint24 (*Filed_plus_y)[pstGuiImage->Width];
    uint24 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint24);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;

    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_UP)
        bUpPart = SV_TRUE;
    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_DOWN)
        bDownPart = SV_TRUE;

    do
    {
        /* 颜色填充 */
        if(bUpPart)
        {
            ((uint24 *)Filed_plus_y)[sX] = *pstColor;
            ((uint24 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bDownPart)
        {
            ((uint24 *)Filed_minus_y)[sX] = *pstColor;
            ((uint24 *)Filed_minus_y)[-sX] = *pstColor;
        }


        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);

        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        /* 颜色填充 */
        if(bDownPart)
        {
            ((uint24 *)Filed_plus_y)[sX] = *pstColor;
            ((uint24 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bUpPart)
        {
            ((uint24 *)Filed_minus_y)[sX] = *pstColor;
            ((uint24 *)Filed_minus_y)[-sX] = *pstColor;
        }


        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}

sint32 MPP_GUI_PAINT_ELLIPSE_32BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint32 *pstColor, sint32 sA, sint32 sB, MPP_GUI_CIRCLE_PART partion)
{
    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    SV_BOOL bUpPart = SV_FALSE, bDownPart= SV_FALSE;

    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    uint32 (*Filed_plus_y)[pstGuiImage->Width];
    uint32 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint32);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;

    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_UP)
        bUpPart = SV_TRUE;
    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_DOWN)
        bDownPart = SV_TRUE;

    do
    {
        /* 颜色填充 */
        if(bUpPart)
        {
            ((uint32 *)Filed_plus_y)[sX] = *pstColor;
            ((uint32 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bDownPart)
        {
            ((uint32 *)Filed_minus_y)[sX] = *pstColor;
            ((uint32 *)Filed_minus_y)[-sX] = *pstColor;
        }


        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);

        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        /* 颜色填充 */
        if(bDownPart)
        {
            ((uint32 *)Filed_plus_y)[sX] = *pstColor;
            ((uint32 *)Filed_plus_y)[-sX] = *pstColor;
        }

        if(bUpPart)
        {
            ((uint32 *)Filed_minus_y)[sX] = *pstColor;
            ((uint32 *)Filed_minus_y)[-sX] = *pstColor;
        }


        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}

sint32 MPP_GUI_PAINT_ELLIPSE_YUV420SP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  void *pstColor, sint32 sA, sint32 sB, MPP_GUI_CIRCLE_PART partion)
{
    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    SV_BOOL bUpPart = SV_FALSE, bDownPart= SV_FALSE;

    sint32 sX, sY, sX_half;
    sint32 s2B2X, s2A2Y;
    uint8 Yvalue;
    uint16 UVvalue;

    uint8 (*YFiled_plus_y)[pstGuiImage->Width];
    uint8 (*YFiled_minus_y)[pstGuiImage->Width];
    uint16 (*UVFiled_plus_y)[pstGuiImage->Width>>1];
    uint16 (*UVFiled_minus_y)[pstGuiImage->Width>>1];

    YFiled_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint8);
    YFiled_minus_y = YFiled_plus_y;
    UVFiled_plus_y = pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height + (((stPoint.y>>2) * pstGuiImage->Width)  + (stPoint.x>>1)) * sizeof(uint16);
    UVFiled_minus_y = UVFiled_plus_y;
    Yvalue = ((uint8*)pstColor)[0];
    UVvalue = (((uint8*)pstColor)[1] & 0xff) | ((((uint8*)pstColor)[2] << 8) & 0xff00);


    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    YFiled_minus_y += sB;
    YFiled_plus_y -= sB;
    UVFiled_minus_y += (sB + 1 >> 1);
    UVFiled_plus_y -= (sB + 1 >> 1);

    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_UP)
        bUpPart = SV_TRUE;
    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_DOWN)
        bDownPart = SV_TRUE;

    do
    {
        /* 颜色填充 */
        if(bUpPart)
        {
            ((uint8 *)YFiled_plus_y)[sX] = Yvalue;
            ((uint8 *)YFiled_plus_y)[-sX] = Yvalue;
        }
        if(bDownPart)
        {
            ((uint8 *)YFiled_minus_y)[sX] = Yvalue;
            ((uint8 *)YFiled_minus_y)[-sX] = Yvalue;
        }
        //if(sX % 2 == 0)
        {
            sX_half = sX >> 1;
            if(bDownPart)
            {
                ((uint16 *)UVFiled_plus_y)[sX_half] = UVvalue;
                ((uint16 *)UVFiled_plus_y)[-sX_half] = UVvalue;
            }
            if(bUpPart)
            {
                ((uint16 *)UVFiled_minus_y)[sX_half] = UVvalue;
                ((uint16 *)UVFiled_minus_y)[-sX_half] = UVvalue;
            }
        }

        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);

        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            YFiled_minus_y-=1;
            YFiled_plus_y+=1;
            if(sY % 2)
            {
                UVFiled_minus_y -= 1;
                UVFiled_plus_y += 1;
            }
        }
    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        /* 颜色填充 */
        if(bDownPart)
        {
            ((uint8 *)YFiled_plus_y)[sX] = Yvalue;
            ((uint8 *)YFiled_plus_y)[-sX] = Yvalue;
        }
        if(bUpPart)
        {
            ((uint8 *)YFiled_minus_y)[sX] = Yvalue;
            ((uint8 *)YFiled_minus_y)[-sX] = Yvalue;
        }

        //if(sX % 2 == 0)
        {
            sX_half = sX >> 1;
            if(bDownPart)
            {
                ((uint16 *)UVFiled_plus_y)[sX_half] = UVvalue;
                ((uint16 *)UVFiled_plus_y)[-sX_half] = UVvalue;
            }
            if(bUpPart)
            {
                ((uint16 *)UVFiled_minus_y)[sX_half] = UVvalue;
                ((uint16 *)UVFiled_minus_y)[-sX_half] = UVvalue;
            }
        }

        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            YFiled_minus_y-=1;
            YFiled_plus_y+=1;

            if(sY % 2)
            {
                UVFiled_minus_y -= 1;
                UVFiled_plus_y += 1;
            }
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            YFiled_minus_y-=1;
            YFiled_plus_y+=1;

            if(sY % 2)
            {
                UVFiled_minus_y -= 1;
                UVFiled_plus_y += 1;
            }
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}

/************************************************
 椭圆圆域
              |
    3         |          4
          +++++++++
       ++++   |   ++++
      +++     |      +++
     ++++     |       ++++
    +++       |       +++
----+++-------+-------+++----
    +++       |        +++
     ++++     |       ++++
      +++     |      +++
       ++++   |   ++++
          ++++++++
    2         |       1
              |
************************************************/

sint32 MPP_GUI_FILL_ELLIPSE_8BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint8 *pstColor, sint32 sA, sint32 sB, sint32 sa, sint32 sb, MPP_GUI_CIRCLE_PART partion)
{
    sint32 i;
    sint32 s32FirstTime = 0;
    SV_BOOL bPaint;

    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    sint32 sa2 = sa * sa;
    sint32 sb2 = sb * sb;
    sint32 sa2b2 = sa2 * sb2;
    sint32 sp0, spk;
    sint32 sx, sy;
    sint32 s2b2x, s2a2y;

    uint8 (*Filed_plus_y)[pstGuiImage->Width];
    uint8 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint8);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    sx = 0;
    sy = sb;
    s2b2x = 0;
    s2a2y = 2 * sy * sa2;
    sp0 = sb2 - sa2 * sb + (sa2>>2);
    spk = sp0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;

    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint8 *)Filed_plus_y)[i] = *pstColor;
                    ((uint8 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint8 *)Filed_minus_y)[i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint8 *)Filed_plus_y)[i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint8 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint8 *)Filed_plus_y)[i] = *pstColor;
                    ((uint8 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }

        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 内部再循环小椭圆 */
        while(sy > sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint8 *)Filed_plus_y)[i] = *pstColor;
                    ((uint8 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint8 *)Filed_minus_y)[i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint8 *)Filed_plus_y)[i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint8 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint8 *)Filed_plus_y)[i] = *pstColor;
                    ((uint8 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[i] = *pstColor;
                    ((uint8 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }

        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 再循环小椭圆 */
        while(sy >= sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}


sint32 MPP_GUI_FILL_ELLIPSE_16BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint16 *pstColor, sint32 sA, sint32 sB, sint32 sa, sint32 sb, MPP_GUI_CIRCLE_PART partion)
{
    sint32 i;
    sint32 s32FirstTime = 0;
    SV_BOOL bPaint;

    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    sint32 sa2 = sa * sa;
    sint32 sb2 = sb * sb;
    sint32 sa2b2 = sa2 * sb2;
    sint32 sp0, spk;
    sint32 sx, sy;
    sint32 s2b2x, s2a2y;

    uint16 (*Filed_plus_y)[pstGuiImage->Width];
    uint16 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint16);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    sx = 0;
    sy = sb;
    s2b2x = 0;
    s2a2y = 2 * sy * sa2;
    sp0 = sb2 - sa2 * sb + (sa2>>2);
    spk = sp0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;



    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint16 *)Filed_plus_y)[i] = *pstColor;
                    ((uint16 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint16 *)Filed_minus_y)[i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint16 *)Filed_plus_y)[i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint16 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint16 *)Filed_plus_y)[i] = *pstColor;
                    ((uint16 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }

        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 内部再循环小椭圆 */
        while(sy > sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint16 *)Filed_plus_y)[i] = *pstColor;
                    ((uint16 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint16 *)Filed_minus_y)[i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint16 *)Filed_plus_y)[i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint16 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint16 *)Filed_plus_y)[i] = *pstColor;
                    ((uint16 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[i] = *pstColor;
                    ((uint16 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }

        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 再循环小椭圆 */
        while(sy >= sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}


sint32 MPP_GUI_FILL_ELLIPSE_24BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint24 *pstColor, sint32 sA, sint32 sB, sint32 sa, sint32 sb, MPP_GUI_CIRCLE_PART partion)
{
    sint32 i;
    sint32 s32FirstTime = 0;
    SV_BOOL bPaint;

    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    sint32 sa2 = sa * sa;
    sint32 sb2 = sb * sb;
    sint32 sa2b2 = sa2 * sb2;
    sint32 sp0, spk;
    sint32 sx, sy;
    sint32 s2b2x, s2a2y;

    uint24 (*Filed_plus_y)[pstGuiImage->Width];
    uint24 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(uint24);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    sx = 0;
    sy = sb;
    s2b2x = 0;
    s2a2y = 2 * sy * sa2;
    sp0 = sb2 - sa2 * sb + (sa2>>2);
    spk = sp0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;


    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint24 *)Filed_plus_y)[i] = *pstColor;
                    ((uint24 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint24 *)Filed_minus_y)[i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint24 *)Filed_plus_y)[i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint24 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint24 *)Filed_plus_y)[i] = *pstColor;
                    ((uint24 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }


        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 内部再循环小椭圆 */
        while(sy > sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint24 *)Filed_plus_y)[i] = *pstColor;
                    ((uint24 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint24 *)Filed_minus_y)[i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint24 *)Filed_plus_y)[i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint24 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint24 *)Filed_plus_y)[i] = *pstColor;
                    ((uint24 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[i] = *pstColor;
                    ((uint24 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }


        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 再循环小椭圆 */
        while(sy >= sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}


sint32 MPP_GUI_FILL_ELLIPSE_32BPP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  uint32 *pstColor, sint32 sA, sint32 sB, sint32 sa, sint32 sb, MPP_GUI_CIRCLE_PART partion)
{
    sint32 i;
    sint32 s32FirstTime = 0;
    SV_BOOL bPaint;

    sint64 sA2 = sA * sA;
    sint64 sB2 = sB * sB;
    sint64 sA2B2 = sA2 * sB2;
    sint64 sP0, sPk;
    sint64 sX, sY;
    sint64 s2B2X, s2A2Y;

    sint64 sa2 = sa * sa;
    sint64 sb2 = sb * sb;
    sint64 sa2b2 = sa2 * sb2;
    sint64 sp0, spk;
    sint64 sx, sy;
    sint64 s2b2x, s2a2y;


    sint32 (*Filed_plus_y)[pstGuiImage->Width];
    sint32 (*Filed_minus_y)[pstGuiImage->Width];

    Filed_plus_y = pstGuiImage->pbmp + (pstGuiImage->Width * stPoint.y  + stPoint.x) * sizeof(sint32);
    Filed_minus_y = Filed_plus_y;

    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    sx = 0;
    sy = sb;
    s2b2x = 0;
    s2a2y = 2 * sy * sa2;
    sp0 = sb2 - sa2 * sb + (sa2>>2);
    spk = sp0;

    Filed_minus_y += sB;
    Filed_plus_y -= sB;



    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint32 *)Filed_plus_y)[i] = *pstColor;
                    ((uint32 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint32 *)Filed_minus_y)[i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint32 *)Filed_plus_y)[i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint32 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint32 *)Filed_plus_y)[i] = *pstColor;
                    ((uint32 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }


        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 内部再循环小椭圆 */
        while(sy > sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;

        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint32 *)Filed_plus_y)[i] = *pstColor;
                    ((uint32 *)Filed_plus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint32 *)Filed_minus_y)[i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint32 *)Filed_plus_y)[i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint32 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[-i] = *pstColor;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint32 *)Filed_plus_y)[i] = *pstColor;
                    ((uint32 *)Filed_plus_y)[-i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[i] = *pstColor;
                    ((uint32 *)Filed_minus_y)[-i] = *pstColor;
                    break;
            }
        }

        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            Filed_minus_y-=1;
            Filed_plus_y+=1;
        }

        /* 再循环小椭圆 */
        while(sy >= sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                s32FirstTime++;
                if(s32FirstTime == 1)
                {
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}

sint32 MPP_GUI_FILL_ELLIPSE_YUV420SP_(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  void *pstColor, sint32 sA, sint32 sB, sint32 sa, sint32 sb, MPP_GUI_CIRCLE_PART partion)
{
    sint32 i, j;
    sint32 s32FirstTime = 0;
    SV_BOOL bPaint;

    sint32 sA2 = sA * sA;
    sint32 sB2 = sB * sB;
    sint32 sA2B2 = sA2 * sB2;
    sint32 sP0, sPk;
    sint32 sX, sY;
    sint32 s2B2X, s2A2Y;

    sint32 sa2 = sa * sa;
    sint32 sb2 = sb * sb;
    sint32 sa2b2 = sa2 * sb2;
    sint32 sp0, spk;
    sint32 sx, sy;
    sint32 s2b2x, s2a2y;

    uint8 (*YFiled_plus_y)[pstGuiImage->Width];
    uint8 (*YFiled_minus_y)[pstGuiImage->Width];
    uint16 (*UVFiled_plus_y)[pstGuiImage->Width>>1];
    uint16 (*UVFiled_minus_y)[pstGuiImage->Width>>1];

    uint8 Yvalue;
    uint16 UVvalue;

    YFiled_plus_y = pstGuiImage->pbmp + ((stPoint.y * pstGuiImage->Width) + stPoint.x) * sizeof(uint8);
    YFiled_minus_y = YFiled_plus_y;
    UVFiled_plus_y = pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height + (((stPoint.y>>2) * pstGuiImage->Width)  + (stPoint.x>>1)) * sizeof(uint16);
    UVFiled_minus_y = UVFiled_plus_y;

    Yvalue = ((uint8*)pstColor)[0];
    UVvalue = (((uint8*)pstColor)[1] & 0xff) | ((((uint8*)pstColor)[2] << 8) & 0xff00);


    sX = 0;
    sY = sB;
    s2B2X = 0;
    s2A2Y = 2 * sY * sA2;
    sP0 = sB2 - sA2 * sB + (sA2>>2);
    sPk = sP0;

    sx = 0;
    sy = sb;
    s2b2x = 0;
    s2a2y = 2 * sy * sa2;
    sp0 = sb2 - sa2 * sb + (sa2>>2);
    spk = sp0;

    YFiled_minus_y += sB;
    YFiled_plus_y -= sB;
    UVFiled_minus_y += (sB + 1 >> 1);
    UVFiled_plus_y -= (sB + 1 >> 1);

    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;


        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint8 *)YFiled_plus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_plus_y)[-i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint8 *)YFiled_minus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[-i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint8 *)YFiled_plus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint8 *)YFiled_plus_y)[-i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[-i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint8 *)YFiled_plus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_plus_y)[-i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[-i] = Yvalue;
                    break;
            }

            if(i % 2 == 1)
            {
                j = i >> 1;
                switch(partion)
                {
                    case MPP_GUI_CIRCLE_UP:
                        ((uint16 *)UVFiled_plus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_plus_y)[-j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_DOWN:
                        ((uint16 *)UVFiled_minus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[-j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_RIGHT:
                        ((uint16 *)UVFiled_plus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_LEFT:
                        ((uint16 *)UVFiled_plus_y)[-j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[-j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_TOTAL:
                    default:
                        ((uint16 *)UVFiled_plus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_plus_y)[-j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[-j] = UVvalue;
                        break;
                }
            }
        }

        if(sPk < 0)
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1));
            sX += 1;
            s2B2X += (sB2 << 1);
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 + (sB2 << 1)) - s2A2Y + (sA2 << 1);
            sX += 1;
            sY -= 1;
            s2B2X += (sB2 << 1);
            s2A2Y -= (sA2 << 1);

            YFiled_minus_y-=1;
            YFiled_plus_y+=1;

            if(sY % 2 == 0)
            {
                UVFiled_minus_y-=1;
                UVFiled_plus_y+=1;
            }
        }

        /* 内部再循环小椭圆 */
        while(sy > sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                if(s32FirstTime == 0)
                {
                    s32FirstTime++;
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(s2B2X <= s2A2Y);

    //return SV_SUCCESS;

    //sPk = sB2*(sX + 0.5)*(sX + 0.5) + sA2*(sY - 1)*(sY - 1) - sA2B2;
    do
    {
        bPaint = SV_TRUE;
        if( stPoint.y - sY < 0 &&
            stPoint.y + sY > pstGuiImage->Height &&
            (stPoint.x - sX < 0 || stPoint.x + sX > pstGuiImage->Width)
        )
            bPaint = SV_FALSE;


        /* 颜色填充 */
        for(i = sx; bPaint && i < sX; i++)
        {
            switch(partion)
            {
                case MPP_GUI_CIRCLE_UP:
                    ((uint8 *)YFiled_plus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_plus_y)[-i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_DOWN:
                    ((uint8 *)YFiled_minus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[-i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_RIGHT:
                    ((uint8 *)YFiled_plus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_LEFT:
                    ((uint8 *)YFiled_plus_y)[-i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[-i] = Yvalue;
                    break;
                case MPP_GUI_CIRCLE_TOTAL:
                default:
                    ((uint8 *)YFiled_plus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_plus_y)[-i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[i] = Yvalue;
                    ((uint8 *)YFiled_minus_y)[-i] = Yvalue;
                    break;
            }

            if(i % 2 == 1)
            {
                j = i >> 1;
                switch(partion)
                {
                    case MPP_GUI_CIRCLE_UP:
                        ((uint16 *)UVFiled_plus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_plus_y)[-j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_DOWN:
                        ((uint16 *)UVFiled_minus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[-j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_RIGHT:
                        ((uint16 *)UVFiled_plus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_LEFT:
                        ((uint16 *)UVFiled_plus_y)[-j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[-j] = UVvalue;
                        break;
                    case MPP_GUI_CIRCLE_TOTAL:
                    default:
                        ((uint16 *)UVFiled_plus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_plus_y)[-j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[j] = UVvalue;
                        ((uint16 *)UVFiled_minus_y)[-j] = UVvalue;
                        break;
                }
            }
        }


        if(sPk > 0)
        {
            sPk = sPk - s2A2Y + (sA2 + (sA2 << 1));
            sY -= 1;
            s2A2Y -= (sA2 << 1);

            YFiled_minus_y-=1;
            YFiled_plus_y+=1;

            if(sY % 2 == 0)
            {
                UVFiled_minus_y-=1;
                UVFiled_plus_y+=1;
            }
        }
        else
        {
            sPk = sPk + s2B2X + (sB2 << 1) - s2A2Y + (sA2 + (sA2 << 1));
            sX += 1;
            sY -= 1;

            s2A2Y -= (sA2 << 1);
            s2B2X += (sB2 << 1);

            YFiled_minus_y-=1;
            YFiled_plus_y+=1;

            if(sY % 2 == 0)
            {
                UVFiled_minus_y-=1;
                UVFiled_plus_y+=1;
            }
        }

        /* 再循环小椭圆 */
        while(sy >= sY)
        {
            if(s2b2x <= s2a2y)
            {
                if(spk < 0)
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1));
                    sx += 1;
                    s2b2x += (sb2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 + (sb2 << 1)) - s2a2y + (sa2 << 1);
                    sx += 1;
                    sy -= 1;
                    s2b2x += (sb2 << 1);
                    s2a2y -= (sa2 << 1);
                }

            }
            else
            {
                if(s32FirstTime == 0)
                {
                    s32FirstTime++;
                    //spk = sb2*(sx + 0.5)*(sx + 0.5) + sa2*(sy - 1)*(sy - 1) - sa2b2;
                }

                if(spk > 0)
                {
                    spk = spk - s2a2y + (sa2 + (sa2 << 1));
                    sy -= 1;
                    s2a2y -= (sa2 << 1);
                }
                else
                {
                    spk = spk + s2b2x + (sb2 << 1) - s2a2y + (sa2 + (sa2 << 1));
                    sx += 1;
                    sy -= 1;

                    s2a2y -= (sa2 << 1);
                    s2b2x += (sb2 << 1);
                }
            }
        }

    } while(sY >= 0);

    return SV_SUCCESS;
}



//定义8BPP直线绘图
DEFINE_PAINT_LINE1_BPP(8BPP)
//定义16BPP直线绘图
DEFINE_PAINT_LINE1_BPP(16BPP)
//定义24BPP直线绘图
DEFINE_PAINT_LINE1_BPP(24BPP)
//定义32BPP直线绘图
DEFINE_PAINT_LINE1_BPP(32BPP)

//定义8BPP带宽度直线绘图
DEFINE_PAINT_LINE_BPP(8BPP)
//定义16BPP带宽度直线绘图
DEFINE_PAINT_LINE_BPP(16BPP)
//定义24BPP带宽度直线绘图
DEFINE_PAINT_LINE_BPP(24BPP)
//定义32BPP带宽度直线绘图
DEFINE_PAINT_LINE_BPP(32BPP)
//定义YUV420SP带宽度直线绘图
DEFINE_PAINT_LINE_BPP(YUV420SP)

//定义8BPP圆绘图
DEFINE_PAINT_CIRCLE_BPP(8BPP)
//定义16BPP圆绘图
DEFINE_PAINT_CIRCLE_BPP(16BPP)
//定义24BPP圆绘图
DEFINE_PAINT_CIRCLE_BPP(24BPP)
//定义32BPP圆绘图
DEFINE_PAINT_CIRCLE_BPP(32BPP)
//定义YUV420SP圆绘图
//DEFINE_PAINT_CIRCLE_BPP(YUV420SP)

#pragma pack(1)
/* BMP 文件头部信息块 */
typedef struct tagBitMapFileHeader
{
    uint16 bfType;             //0x4d42, 作为BMP格式文件的描述头
    uint32 bfSize;             //文件大小
    uint16 bfReversed1;        //保留，必须设置为0 (6-7字节)
    uint16 bfReversed2;        //保留，必须设置为0 (8-9字节)
    uint32 bfOffBits;          //从文件头到像素数据的偏移
} BITMAP_FILE_HEADER;

/* BMP 图像信息描述块 */
typedef struct tagBitMapInfoHeader
{
    uint32 biSize;            //此结构体的大小  (14-17 字节)
    uint32 biWidth;           //图像宽  (18-21字节)
    uint32 biHeight;          //图像高度  (22-25字节)
    uint16 biPlanes;          //表示bmp图片的平面属，显然显示器只有一个平面  (26-27字节)
    uint16 biBitCount;        //一像素所占的位数  (28-29字节)
    uint32 biCompression;     //说明图象数据压缩的类型，0为不压缩  (30-33字节)
    uint32 biSizeImage;       //像素数据所占大小, 等于上面文件头结构中bfSize-bfOffBits (34-37字节)
    uint32 biXPelsPerMeter;   //说明水平分辨率，用象素/米表示。一般为0 (38-41字节)
    uint32 biYPelsPerMeter;   //说明垂直分辨率，用象素/米表示。一般为0 (42-45字节)
    uint32 biClrUsed;         //说明位图实际使用的彩色表中的颜色索引数（设为0的话，则说明使用所有调色板项）。 (46-49字节)
    uint32 biClrImportant     //说明对图象显示有重要影响的颜色索引的数目，如果是0，表示都重要。(50-53字节)
} BITMAP_INFO_HEADER;

/* BMP 头部 */
typedef struct tagBitHeader
{
    BITMAP_FILE_HEADER bFileHeader;
    BITMAP_INFO_HEADER bInfoHeader;
} BITMAP_HEADER;
#pragma pack()

/******************************************************************************
 * 函数功能: 绘制点
 * 输入参数: pstPaintImage --- 图像信息
            stPoint --- 绘制的点
            stColor    --- 颜色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_POINT(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
    MPP_GUI_COLOR stColor)
{
    uint8  *u8Filed, u8value;
    uint16 *u16Filed, u16value;
    uint24 *u24Filed, u24value;
    uint32 *u32Filed, u32value;
    uint8 y_value;
    uint16 uv_value;

    int i,j;
    int width, height, width_half, height_half;
    int x, y, x_half, y_half;

    width = pstGuiImage->Width;
    height = pstGuiImage->Height;
#if 0
    x = max(0, min(stPoint.x, width));
    y = max(0, min(stPoint.y, height));
#else
	x = stPoint.x;
	y = stPoint.y;
#endif
    switch(pstGuiImage->format){
        case MPP_GUI_FMT_RGB_8BPP:
            u8Filed = pstGuiImage->pbmp;
            u8Filed += y * width;
            u8Filed[x] = stColor.u8color;
            break;
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
            u16Filed = pstGuiImage->pbmp;
            u16Filed += y * pstGuiImage->Width;
            u16Filed[x] = stColor.u16color;
            break;
        case MPP_GUI_FMT_RGB_888:
            u24Filed = pstGuiImage->pbmp;
            u24Filed += y * width;
            u24value.data[0] = stColor.u24color & 0xff;
            u24value.data[1] = (stColor.u24color >> 8) & 0xff;
            u24value.data[2] = (stColor.u24color >> 16) & 0xff;
            u24Filed[x] = u24value;
            break;
        case MPP_GUI_FMT_ARGB_8888:
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y * width;
            u32Filed[x] = stColor.u32color;
            break;
        case MPP_GUI_FMT_YUV_420SP:
            x_half = x >> 1;
            y_half = y >> 1;
            width_half = width >> 1;
            height_half = height >> 1;

            u8Filed = pstGuiImage->pbmp;
            u8Filed += y * width;
            u16Filed = (pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height);
            u16Filed += y_half * width_half;

            u8value = stColor.u8yuv[0];
            u16value = (stColor.u8yuv[1] & 0xff) | ((stColor.u8yuv[2] << 8)& 0xff00);
            u8Filed[x] = u8value;
            u16Filed[x_half] = u16value;
            break;
        case MPP_GUI_FMT_YUYV:
            u32value = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y * (width>>1);
            u32Filed[x>>1] = u32value;
            break;
        case MPP_GUI_FMT_UYVY:
            u32value = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y * (width>>1);
            u32Filed[x>>1] = u32value;
            break;
        default:
            printf("Cannot find GUI FMT:%d\n", pstGuiImage->format);
            return SV_FAILURE;
            break;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例绘制点
 * 输入参数: pstPaintImage --- 图像信息
            stPoint --- 绘制的点
            stColor    --- 颜色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_POINT_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPoint,
    MPP_GUI_COLOR stColor)
{
    MPP_GUI_POINT point;
#if 1
    stPoint.x = min(1.0, max(0.0, stPoint.x));
    stPoint.y = min(1.0, max(0.0, stPoint.y));
    point.x = pstGuiImage->Width * max(0, stPoint.x);
    point.y = pstGuiImage->Height * max(0, stPoint.y);
#else
    point.x = pstGuiImage->Width * stPoint.x;
    point.y = pstGuiImage->Height *  stPoint.y;
#endif
    return MPP_GUI_PAINT_POINT(pstGuiImage, point, stColor);
}

/******************************************************************************
 * 函数功能: 绘制宽度为1的直线
 * 输入参数: pstPaintImage --- 图像信息
            stPoint_start --- 起始点
            stPoint_end   --- 终止点
            stColor    --- 颜色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_LINE1(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint_start,
  MPP_GUI_POINT stPoint_end, MPP_GUI_COLOR stColor)
{
    sint32 s32Ret;
    MPP_GUI_IMAGE_S stGuiImage_tmp;
    uint8 u8color;
    uint16 u16color;
    uint24 u24color;
    uint32 u32color;
    uint8 u8yuv[3];
    switch (pstGuiImage->format)
    {
    case MPP_GUI_FMT_RGB_8BPP:
        u8color = stColor.u8color;
        return RUN_PAINT_LINE1_BPP(8BPP)(pstGuiImage, stPoint_start, stPoint_end, &u8color, LINE_OVERLAP_NONE);
        break;
    case MPP_GUI_FMT_ARGB_1555:
    case MPP_GUI_FMT_RGB_565:
    case MPP_GUI_FMT_BGR_565:
        u16color = stColor.u16color;
        return RUN_PAINT_LINE1_BPP(16BPP)(pstGuiImage, stPoint_start, stPoint_end, &u16color, LINE_OVERLAP_NONE);
        break;
    case MPP_GUI_FMT_RGB_888:
        u24color.data[0] = stColor.u24color & 0xff;
        u24color.data[1] = (stColor.u24color >> 8) & 0xff;
        u24color.data[2] = (stColor.u24color >> 16) & 0xff;
        return RUN_PAINT_LINE1_BPP(24BPP)(pstGuiImage, stPoint_start, stPoint_end, &u24color, LINE_OVERLAP_NONE);
        break;
    case MPP_GUI_FMT_ARGB_8888:
        u32color = stColor.u32color;
        return RUN_PAINT_LINE1_BPP(32BPP)(pstGuiImage, stPoint_start, stPoint_end, &u32color, LINE_OVERLAP_NONE);
        break;
    case MPP_GUI_FMT_YUV_420SP: //对于YUV420SP先绘制Y，再绘制SP，因此需要分两次绘制
        stGuiImage_tmp = *pstGuiImage;
        u8yuv[0] = stColor.u8yuv[0];
        u8yuv[1] = stColor.u8yuv[1];
        u8yuv[2] = stColor.u8yuv[2];
        return RUN_PAINT_LINE1_BPP(YUV420SP)(pstGuiImage, stPoint_start, stPoint_end, u8yuv, LINE_OVERLAP_NONE);
        break;
    case MPP_GUI_FMT_YUYV:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint_start.x >>= 1;
        stPoint_end.x >>= 1;
        u32color = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
        return RUN_PAINT_LINE1_BPP(32BPP)(pstGuiImage, stPoint_start, stPoint_end, &u32color, LINE_OVERLAP_NONE);
        break;
    case MPP_GUI_FMT_UYVY:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint_start.x >>= 1;
        stPoint_end.x >>= 1;
        u32color = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
        return RUN_PAINT_LINE1_BPP(32BPP)(pstGuiImage, stPoint_start, stPoint_end, &u32color, LINE_OVERLAP_NONE);
        break;

    default:
        printf("%s cannot find format %d\n", __func__, pstGuiImage->format);
        return SV_FAILURE;
        break;
    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 绘制直线
 * 输入参数: pstPaintImage --- 图像信息
            stPoint_start --- 起始点
            stPoint_end   --- 终止点
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_LINE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint_start,
  MPP_GUI_POINT stPoint_end, MPP_GUI_COLOR stColor, uint32 stick)
{
    sint32 s32Ret;
    MPP_GUI_IMAGE_S stGuiImage_tmp;
    MPP_GUI_COLOR   stColor_tmp;
    uint8 u8color;
    uint16 u16color;
    uint24 u24color;
    uint32 u32color;
    uint8  u8yuv[3];

    if(stPoint_start.y == stPoint_end.y && stPoint_start.x == stPoint_end.x)
    {
        return SV_SUCCESS;
    }
    else if(stPoint_start.y == stPoint_end.y)
    {
        uint32 up_part, down_part;
        up_part = stick >> 1;
        down_part = stick - up_part;
        if(stPoint_start.x < stPoint_end.x)
        {
            stPoint_start.y = max(0, stPoint_start.y-up_part);
            stPoint_end.y   = min(pstGuiImage->Height-1, stPoint_end.y + down_part);
            return MPP_GUI_FILL_RECT(pstGuiImage, stPoint_start, stPoint_end, stColor);
        }
        else
        {
            stPoint_end.y = max(0, stPoint_end.y - up_part);
            stPoint_start.y = min(pstGuiImage->Height-1, stPoint_start.y+down_part);
            return MPP_GUI_FILL_RECT(pstGuiImage, stPoint_end, stPoint_start, stColor);
        }
    }
    else if(stPoint_start.x == stPoint_end.x)
    {
        uint32 left_part, right_part;
        left_part = stick >> 1;
        right_part = stick - left_part;
        if(stPoint_start.y < stPoint_end.y)
        {
            stPoint_start.x = max(0, stPoint_start.x - left_part);
            stPoint_end.x   = min(pstGuiImage->Width-1, stPoint_end.x + right_part);
            return MPP_GUI_FILL_RECT(pstGuiImage, stPoint_start, stPoint_end, stColor);
        }
        else
        {
            stPoint_end.x = max(0, stPoint_end.x-left_part);
            stPoint_start.x = min(pstGuiImage->Width-1, stPoint_start.x+right_part);
            return MPP_GUI_FILL_RECT(pstGuiImage, stPoint_end, stPoint_start, stColor);
        }
    }

    if (stick <= 1)
    {
        return MPP_GUI_PAINT_LINE1(pstGuiImage, stPoint_start, stPoint_end, stColor);
    }

    switch (pstGuiImage->format)
    {
    case MPP_GUI_FMT_RGB_8BPP:
        u8color = stColor.u8color;
        return RUN_PAINT_LINE_BPP(8BPP)(pstGuiImage, stPoint_start, stPoint_end, &u8color, stick);
        break;
    case MPP_GUI_FMT_ARGB_1555:
	case MPP_GUI_FMT_RGB_565:
	case MPP_GUI_FMT_BGR_565:
        u16color = stColor.u16color;
        return RUN_PAINT_LINE_BPP(16BPP)(pstGuiImage, stPoint_start, stPoint_end, &u16color, stick);
        break;
    case MPP_GUI_FMT_RGB_888:
        u24color.data[0] = stColor.u24color & 0xff;
        u24color.data[1] = (stColor.u24color >> 8) & 0xff;
        u24color.data[2] = (stColor.u24color >> 16) & 0xff;
        return RUN_PAINT_LINE_BPP(24BPP)(pstGuiImage, stPoint_start, stPoint_end, &u24color, stick);
        break;
    case MPP_GUI_FMT_ARGB_8888:
        u32color = stColor.u32color;
        return RUN_PAINT_LINE_BPP(32BPP)(pstGuiImage, stPoint_start, stPoint_end, &u32color, stick);
        break;
    case MPP_GUI_FMT_YUV_420SP: //对于YUV420SP先绘制Y，再绘制SP，因此需要分两次绘制
        stGuiImage_tmp = *pstGuiImage;
        u8yuv[0] = stColor.u8yuv[0];
        u8yuv[1] = stColor.u8yuv[1];
        u8yuv[2] = stColor.u8yuv[2];
        return RUN_PAINT_LINE_BPP(YUV420SP)(pstGuiImage, stPoint_start, stPoint_end, u8yuv, stick);
        break;
    case MPP_GUI_FMT_YUYV:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint_start.x >>= 1;
        stPoint_end.x >>= 1;
        u32color = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
        return RUN_PAINT_LINE_BPP(32BPP)(&stGuiImage_tmp, stPoint_start, stPoint_end, &u32color, stick);
        break;
    case MPP_GUI_FMT_UYVY:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint_start.x >>= 1;
        stPoint_end.x >>= 1;
        stick = max(stick >> 1, 1);
        u32color = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
        return RUN_PAINT_LINE_BPP(32BPP)(&stGuiImage_tmp, stPoint_start, stPoint_end, &u32color, stick);
    default:
        printf("%s cannot find format %d\n", __func__, pstGuiImage->format);
        return SV_FAILURE;
        break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例绘制直线
 * 输入参数: pstPaintImage --- 图像信息
            stPoint_start --- 起始点
            stPoint_end   --- 终止点
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_LINE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPoint_start,
  MPP_GUI_POINT_S stPoint_end, MPP_GUI_COLOR stColor, uint32 stick)
{
    MPP_GUI_POINT point_start, point_end;
#if 1
    float eps_x = 1.0 * (stick >> 1) / pstGuiImage->Width;
    float eps_y = 1.0 * (stick >> 1) / pstGuiImage->Height;

    stPoint_start.x = min(1.0-eps_x, max(0.0+eps_x, stPoint_start.x));
    stPoint_start.y = min(1.0-eps_y, max(0.0+eps_y, stPoint_start.y));
    stPoint_end.x = min(1.0-eps_x, max(0.0+eps_x, stPoint_end.x));
    stPoint_end.y = min(1.0-eps_y, max(0.0+eps_y, stPoint_end.y));
#endif
    point_start.x = ALIGN2(max(0, stPoint_start.x) * pstGuiImage->Width);
    point_start.y = ALIGN2(max(0, stPoint_start.y) * pstGuiImage->Height);
    point_end.x = ALIGN2(max(0, stPoint_end.x) * pstGuiImage->Width);
    point_end.y = ALIGN2(max(0, stPoint_end.y) * pstGuiImage->Height);

    return MPP_GUI_PAINT_LINE(pstGuiImage, point_start, point_end, stColor, stick);
}

/******************************************************************************
 * 函数功能: 绘制折线图
 * 输入参数: pstPaintImage --- 图像信息
            pstPoint      --- 点坐标
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_MUTIL_LINE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT *pstPoint,
    uint32 lens, MPP_GUI_COLOR stColor, uint32 stick)
{
    int i;
    sint32 s32Ret;
    for(i=0; i<lens - 1; i++)
    {
        s32Ret = MPP_GUI_PAINT_LINE(pstGuiImage, pstPoint[i], pstPoint[i+1], stColor, stick);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_PAINT_LINE fail\n");
            return SV_FAILURE;
        }
        MPP_GUI_FILL_CIRCLE(pstGuiImage, pstPoint[i], stColor, stick/2, MPP_GUI_CIRCLE_TOTAL);
    }
#if 0
	for(i=0; i < lens-2; i++)
	{
		s32Ret = MPP_GUI_FILL_CIRCLE(pstGuiImage, pstPoint[i+1], stColor, stick/2);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_FILL_CIRCLE fail\n");
            return SV_FAILURE;
        }
	}
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例绘制折线图
 * 输入参数: pstPaintImage --- 图像信息
            pstPoint      --- 点坐标
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_MUTIL_LINE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S *pstPoint,
    uint32 lens, MPP_GUI_COLOR stColor, uint32 stick)
{
    int i;
    sint32 s32Ret;

    for(i=0; i<lens; i++)
    {
        pstPoint[i].x = min(1.0, max(0.0, pstPoint[i].x));
        pstPoint[i].y = min(1.0, max(0.0, pstPoint[i].y));
    }

    for(i=0; i<lens - 1; i++)
    {
        s32Ret = MPP_GUI_PAINT_LINE_S(pstGuiImage, pstPoint[i], pstPoint[i+1], stColor, stick);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_PAINT_LINE_S fail\n");
            return SV_FAILURE;
        }
		MPP_GUI_FILL_CIRCLE_S(pstGuiImage, pstPoint[i], stColor, stick/2, MPP_GUI_CIRCLE_TOTAL);
    }
#if 0
    for(i=0; i < lens-2; i++)
    {
        s32Ret = MPP_GUI_FILL_CIRCLE_S(pstGuiImage, pstPoint[i+1], stColor, stick/2);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_FILL_CIRCLE fail\n");
            return SV_FAILURE;
        }
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 绘制封闭图样
 * 输入参数: pstPaintImage --- 图像信息
            pstPoint      --- 点坐标
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_CLOSE_MUTIL_LINE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT *pstPoint,
    uint32 lens, MPP_GUI_COLOR stColor, uint32 stick)
{
    int i;
    sint32 s32Ret;
    for(i=0; i<lens - 1; i++)
    {
        s32Ret = MPP_GUI_PAINT_LINE(pstGuiImage, pstPoint[i], pstPoint[i+1], stColor, stick);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_PAINT_LINE fail\n");
            return SV_FAILURE;
        }
		MPP_GUI_FILL_CIRCLE(pstGuiImage, pstPoint[i], stColor, stick/2, MPP_GUI_CIRCLE_TOTAL);
    }

    s32Ret = MPP_GUI_PAINT_LINE(pstGuiImage, pstPoint[i], pstPoint[0], stColor, stick);
    if (s32Ret != SV_SUCCESS)
    {
        printf("MPP_GUI_PAINT_LINE fail\n");
        return SV_FAILURE;
    }
	MPP_GUI_FILL_CIRCLE(pstGuiImage, pstPoint[i], stColor, stick/2, MPP_GUI_CIRCLE_TOTAL);
#if 0
	for(i=0; i < lens; i++)
	{
		s32Ret = MPP_GUI_FILL_CIRCLE(pstGuiImage, pstPoint[i], stColor, stick);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_FILL_CIRCLE fail\n");
            return SV_FAILURE;
        }
	}
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 填充封闭图样(只适合凸多边形,逆时针排列)
 * 输入参数: pstPaintImage --- 图像信息
            pstPoint      --- 点坐标
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_CLOSE_MUTIL_LINE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT *pstPoint, uint32 lens, MPP_GUI_COLOR stColor)
{
    int i, topL, topR; /* top 表示顶部的点, left, right表示顶部左右两侧 */
    MPP_GUI_POINT pointL, pointL1, pointL2, pointR, pointR1, pointR2;
    int tDeltaXL, tDeltaYL, tDeltaXR, tDeltaYR, tDeltaXL2, tDeltaYL2, tDeltaXR2, tDeltaYR2;
    int tStepL, tErrorL, tStepR, tErrorR;
    int n = 0;
    topL = 0;
    topR = 0;
    pointL = pstPoint[0];
    pointR = pstPoint[0];

    /* 取得顶部两点 */
    for(i = 1; i < lens; i++)
    {
        if(pointL.y > pstPoint[i].y)
        {
            topL = i;
            topR = i;
            pointL = pstPoint[i];
            pointR = pstPoint[i];
        }
        else if(pointL.y == pstPoint[i].y)
        {
            if(pointL.x > pstPoint[i].x)
            {
                topL = i;
                pointL = pstPoint[i];
            }
            if(pointR.x < pstPoint[i].x)
            {
                topR = i;
                pointR = pstPoint[i];
            }
        }
    }

    pointL1 = pstPoint[(topL)   % lens];
    pointL2 = pstPoint[(topL+1) % lens];
    tDeltaXL = abs(pointL2.x - pointL1.x);  tDeltaXL2 = tDeltaXL << 2;
    tDeltaYL = pointL2.y - pointL1.y;       tDeltaYL2 = tDeltaYL << 2;
    tStepL  = pointL2.x - pointL1.x > 0 ? 1 : -1;
    tErrorL = tDeltaXL2 - tDeltaYL;
    pointL = pointL1;

    pointR1 = pstPoint[(topR)   % lens];
    pointR2 = pstPoint[(topR+lens-1) % lens];
    tDeltaXR = abs(pointR2.x - pointR1.x);  tDeltaXR2 = tDeltaXR << 2;
    tDeltaYR = pointR2.y - pointR1.y;       tDeltaYR2 = tDeltaYR << 2;
    tStepR  = pointR2.x - pointR1.x > 0 ? 1 : -1;
    tErrorR = tDeltaXR2 - tDeltaYR;
    pointR = pointR1;

    do {
        while(pointL.y < pointL2.y && pointR.y < pointR2.y)
        {
            MPP_GUI_FILL_RECT(pstGuiImage, pointL, pointR, stColor);
            while(tErrorL > 0)
            {
                pointL.x += tStepL;
                tErrorL -= tDeltaYL2;
            }

            while(tErrorR > 0)
            {
                pointR.x += tStepR;
                tErrorR -= tDeltaYR2;
            }

            pointL.y += 1;
            tErrorL += tDeltaXL2;

            pointR.y += 1;
            tErrorR += tDeltaXR2;
        }

        if(pointL.y == pointL2.y)
        {
            topL = (topL + 1) % lens;
            pointL1 = pstPoint[(topL)   % lens];
            pointL2 = pstPoint[(topL+1) % lens];
            tDeltaXL = abs(pointL2.x - pointL1.x);  tDeltaXL2 = tDeltaXL << 2;
            tDeltaYL = pointL2.y - pointL1.y;       tDeltaYL2 = tDeltaYL << 2;
            tStepL  = pointL2.x - pointL1.x > 0 ? 1 : -1;
            tErrorL = tDeltaXL2 - tDeltaYL;
        }

        if(pointR.y == pointR2.y)
        {
            topR = (topR +lens - 1) % lens;
            pointR1 = pstPoint[(topR)   % lens];
            pointR2 = pstPoint[(topR+lens-1) % lens];
            tDeltaXR = abs(pointR2.x - pointR1.x);  tDeltaXR2 = tDeltaXR << 2;
            tDeltaYR = pointR2.y - pointR1.y;       tDeltaYR2 = tDeltaYR << 2;
            tStepR  = pointR2.x - pointR1.x > 0 ? 1 : -1;
            tErrorR = tDeltaXR2 - tDeltaYR;
            pointR = pointR1;
        }

    } while((topR - topL + lens ) % lens >= 2);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例填充封闭图样
 * 输入参数: pstPaintImage --- 图像信息
            pstPoint      --- 点坐标
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
 sint32 MPP_GUI_FILL_CLOSE_MUTIL_LINE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S *pstPoint, uint32 lens, MPP_GUI_COLOR stColor)
{
    int i;
    sint32 s32Ret;
    MPP_GUI_POINT *stPoint = malloc(sizeof(MPP_GUI_POINT) * lens);

    for(i=0; i < lens; i++)
    {
        pstPoint[i].x = min(1.0, max(0.0, pstPoint[i].x));
        pstPoint[i].y = min(1.0, max(0.0, pstPoint[i].y));
        stPoint[i].x = ALIGN2(max(0, pstPoint[i].x) * pstGuiImage->Width);
        stPoint[i].y = ALIGN2(max(0, pstPoint[i].y) * pstGuiImage->Height);
    }

    s32Ret = MPP_GUI_FILL_CLOSE_MUTIL_LINE(pstGuiImage, stPoint, lens, stColor);
    free(stPoint);

    return s32Ret;
}

/******************************************************************************
 * 函数功能: 判断顶点是否是凹点
 * 输入参数: pre --- 顶点的前一个点
            cur --- 顶点
            next --- 顶点的后一个点
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 isAoPoint(MPP_GUI_POINT_S pre, MPP_GUI_POINT_S curr, MPP_GUI_POINT_S next)
{
    float eps = 1e-6;
    float dx1 = curr.x - pre.x;
    float dy1 = curr.y - pre.y;
    float dx2 = next.x - curr.x;
    float dy2 = next.y - curr.y;
    sint32 isA = SV_FALSE;
    float tmp = dx1 * dy2 - dx2 * dy1;
    if (abs(tmp) < eps)
        isA = SV_FALSE;
    else if (tmp > 0)
        isA = SV_TRUE;
    return isA;
}

/******************************************************************************
 * 函数功能: 判断点是否在三角形之内
 * 输入参数: p --- 该点
            a --- 三角形的一个点
            b --- 三角形的一个点
            c --- 三角形的一个点
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : a, b, c 三个点需要逆时针排列
 *****************************************************************************/
sint32 isInTriangle(MPP_GUI_POINT_S p, MPP_GUI_POINT_S a, MPP_GUI_POINT_S b, MPP_GUI_POINT_S c)
{
    int i;
    float eps = 1e-6, tmp;
    //MPP_GUI_POINT_S *poy[4] = {&a, &b, &c, &a};
    MPP_GUI_POINT_S poy[4];

    poy[0] = a;
    poy[1] = b;
    poy[2] = c;
    poy[3] = a;


    for(i = 0; i < 3; i++)
    {
        tmp = ((poy[i].x - p.x)*(poy[i+1].y - poy[i].y) - (poy[i].y - p.y)*(poy[i+1].x - poy[i].x));
        //if (abs(tmp) < eps)
        //    continue;

        if (tmp > 0)
        {
            return SV_FALSE;
        }

    }
    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: 判断顶点是否是耳朵？
 * 输入参数: pre --- 顶点的前一个点
            cur --- 顶点
            next --- 顶点的后一个点
            pstPoint --- 多边形数组
            lens --- 多边形数组长度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : a, b, c 三个点需要逆时针排列
 *****************************************************************************/
sint32 isEar(MPP_GUI_POINT_S *pPre, MPP_GUI_POINT_S *pCur, MPP_GUI_POINT_S *pNext, MPP_GUI_POINT_S *pstPoint, sint32 lens)
{
    int i;
    if (isAoPoint(*pPre, *pCur, *pNext))
    {
        return SV_FALSE;
    }

    for (i = 0; i < lens; i++)
    {
        if ((&pstPoint[i]) == pPre)
            continue;
        if ((&pstPoint[i]) == pCur)
            continue;
        if ((&pstPoint[i]) == pNext)
            continue;

        if (isInTriangle(pstPoint[i], *pPre, *pCur, *pNext))
        {
            return SV_FALSE;
        }

    }

    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: 按比例填充封闭图样2
 * 输入参数: pstPaintImage --- 图像信息
            pstPoint      --- 点坐标数组
            lens          --- 点坐标数量
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_CLOSE_MUTIL_LINE_S_2(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S *pstPoint, uint32 lens, MPP_GUI_COLOR stColor)
{
    sint32 i, j, index_curr, index_pre, index_next;
    SV_BOOL *pisindice = malloc(lens * sizeof(SV_BOOL));       // 记录状态数组: 0 未完成，1 已完成
    sint32 unComplete, preunComplete;
    sint32 aoNum;   // 凹点数量
    sint32 creRunTime;
    sint32 reRunTime;   // 重复循环次数
    MPP_GUI_POINT_S *pPoint = malloc(sizeof(MPP_GUI_POINT_S) * lens);
    MPP_GUI_POINT_S *pCurr, pNext, pPre;
    sint32 s32MainRun, s32SubRun;
    memcpy(pPoint, pstPoint, sizeof(MPP_GUI_POINT_S) * lens);
    pstPoint = pPoint;

    creRunTime = lens; // 循环次数为总次数
    reRunTime = creRunTime;

recycle:
    if (lens < 3)
        goto exit;

    aoNum = 0;
    unComplete = lens;
    memset(pisindice, 0x00, sizeof(SV_BOOL) * lens);
    for (i = 0; i < lens; i++)
    {
        index_curr = (creRunTime - reRunTime) + i;
        index_pre = (index_curr - 1 + lens) % lens;
        index_next = (index_curr + 1) % lens;

        if (isAoPoint(pstPoint[index_pre], pstPoint[index_curr], pstPoint[index_next]))
        {
            aoNum++;
        }
    }

    s32MainRun = 0;
    preunComplete = unComplete;
    index_curr = (creRunTime - reRunTime);
    while(unComplete > 2)
    {
        /* 获取上一个点 */
        index_pre = (index_curr - 1 + lens) % lens;
        s32SubRun = 0;
        while(1)
        {
            if (pisindice[index_pre] == 0)
                break;
            if (s32SubRun >= lens)
                break;
            index_pre = (index_pre - 1 + lens) % lens;
            s32SubRun++;
        }

        /* 获取下一个点 */
        index_next = (index_curr + 1) % lens;
        s32SubRun = 0;
        while(1)
        {
            if (pisindice[index_next] == 0)
                break;
            if (s32SubRun >= lens)
                break;
            index_next = (index_next + 1) % lens;
            s32SubRun++;
        }

        if (isEar(&pstPoint[index_pre], &pstPoint[index_curr], &pstPoint[index_next], pstPoint, lens))
        {
            MPP_GUI_POINT_S pTmpPoint[3];
            pisindice[index_curr] = 1;
            unComplete -= 1;

            memcpy(&pTmpPoint[0], &pstPoint[index_pre], sizeof(MPP_GUI_POINT_S));
            memcpy(&pTmpPoint[1], &pstPoint[index_curr], sizeof(MPP_GUI_POINT_S));
            memcpy(&pTmpPoint[2], &pstPoint[index_next], sizeof(MPP_GUI_POINT_S));
            MPP_GUI_FILL_CLOSE_MUTIL_LINE_S(pstGuiImage, pTmpPoint, 3, stColor);
        }
        index_curr = index_next;
        s32MainRun++;
        if (s32MainRun % (aoNum+1) == 0)
        {
            if (preunComplete != unComplete)
            {
                preunComplete = unComplete;
            }
            else
            {
                break;
            }
        }
    }

    /* 选取未完成的点，再重复进行填充 */
    if (reRunTime > 0)
    {
        for(i = 0, j = 0; i < lens; i++)
        {
            /* 已经完成的点，则跳过 */
            if (pisindice[i] == 1)
                continue;

            /* 坐标位置不相等，才进行拷贝 */
            if (i != j)
            {
                memcpy(&pstPoint[j], &pstPoint[i], sizeof(MPP_GUI_POINT_S));
            }

            j++;
            continue;
        }

        /* 更新剩余长度 */
        lens = j;
        reRunTime--;
        goto recycle;
    }


exit:
    /* 为了兼容一部分客户的点,可能存在线段的交叉 */
    if (lens > 2)
    {
        MPP_GUI_FILL_CLOSE_MUTIL_LINE_S(pstGuiImage, pstPoint, lens, stColor);
    }
    free(pisindice);
    free(pPoint);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 按比例绘制封闭图样
 * 输入参数: pstPaintImage --- 图像信息
            pstPoint      --- 点坐标
            stColor    --- 颜色
            stick       --- 直线宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_CLOSE_MUTIL_LINE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S *pstPoint,
    uint32 lens, MPP_GUI_COLOR stColor, uint32 stick)
{
    int i;
    sint32 s32Ret;

    for(i=0; i<lens; i++)
    {
        pstPoint[i].x = min(1.0, max(0.0, pstPoint[i].x));
        pstPoint[i].y = min(1.0, max(0.0, pstPoint[i].y));
    }

    for(i=0; i<lens - 1; i++)
    {
        s32Ret = MPP_GUI_PAINT_LINE_S(pstGuiImage, pstPoint[i], pstPoint[i+1], stColor, stick);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_PAINT_LINE_S fail\n");
            return SV_FAILURE;
        }
        MPP_GUI_FILL_CIRCLE_S(pstGuiImage, pstPoint[i], stColor, stick/2, MPP_GUI_CIRCLE_TOTAL);
    }

    s32Ret = MPP_GUI_PAINT_LINE_S(pstGuiImage, pstPoint[i], pstPoint[0], stColor, stick);
    if (s32Ret != SV_SUCCESS)
    {
        printf("MPP_GUI_PAINT_LINE_S fail\n");
        return SV_FAILURE;
    }
    MPP_GUI_FILL_CIRCLE_S(pstGuiImage, pstPoint[i], stColor, stick/2, MPP_GUI_CIRCLE_TOTAL);
#if 0
	for(i=0; i < lens; i++)
	{
		s32Ret = MPP_GUI_FILL_CIRCLE_S(pstGuiImage, pstPoint[i], stColor, stick/2);
        if (s32Ret != SV_SUCCESS)
        {
            printf("MPP_GUI_FILL_CIRCLE fail\n");
            return SV_FAILURE;
        }
	}
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 绘制矩形框
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            stColor        --- 颜色
            stick           --- 矩形边框宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_RECT(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPointUp,
    MPP_GUI_POINT stPointDown, MPP_GUI_COLOR stColor, uint32 stick)
{
    MPP_GUI_POINT pstPoint[4];

    pstPoint[0].x = stPointUp.x;
    pstPoint[0].y = stPointUp.y;

    pstPoint[1].x = stPointUp.x;
    pstPoint[1].y = stPointDown.y;

    pstPoint[2].x = stPointDown.x;
    pstPoint[2].y = stPointDown.y;

    pstPoint[3].x = stPointDown.x;
    pstPoint[3].y = stPointUp.y;

    return MPP_GUI_PAINT_CLOSE_MUTIL_LINE(pstGuiImage, pstPoint, 4, stColor, stick);
}

/******************************************************************************
 * 函数功能: 绘制圆形
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            stColor    --- 颜色
            radius      --- 半径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_CIRCLE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  MPP_GUI_COLOR stColor, uint32 radius, MPP_GUI_CIRCLE_PART part)
{
    sint32 s32Ret;
    MPP_GUI_IMAGE_S stGuiImage_tmp;
    MPP_GUI_COLOR   stColor_tmp;
    int tmp;
    uint8 u8color;
    uint16 u16color;
    uint24 u24color;
    uint32 u32color;
    uint8  u8yuv[3];

#if 0
    if((stPoint.x - radius < 0) || (stPoint.y - radius < 0))
    {
        return SV_SUCCESS;
    }
    else if(((stPoint.x + radius) >= pstGuiImage->Width) || ((stPoint.y + radius) >= pstGuiImage->Height))
    {
        return SV_SUCCESS;
    }
#endif

    if (radius <= 1)
    {
        return MPP_GUI_PAINT_POINT(pstGuiImage, stPoint, stColor);
    }

    switch(part)
    {
        case MPP_GUI_CIRCLE_UP:
            stPoint.x = max(radius, min(stPoint.x, pstGuiImage->Width-radius-1));
            stPoint.y = max(radius, stPoint.y);
            radius = min(radius, stPoint.x);
            radius = min(radius, pstGuiImage->Width - stPoint.x - 1);
            break;
        case MPP_GUI_CIRCLE_DOWN:
            stPoint.x = max(radius, min(stPoint.x, pstGuiImage->Width-radius-1));
            stPoint.y = min(stPoint.y, pstGuiImage->Height-radius-1);
            radius = min(radius, stPoint.x);
            radius = min(radius, pstGuiImage->Width - stPoint.x - 1);
            break;
        case MPP_GUI_CIRCLE_LEFT:
            stPoint.x = max(radius, stPoint.x);
            stPoint.y = max(radius, min(stPoint.y, pstGuiImage->Height-radius-1));
            radius = min(radius, stPoint.y);
            radius = min(radius, pstGuiImage->Height - stPoint.y - 1);
            break;
        case MPP_GUI_CIRCLE_RIGHT:
            stPoint.x = min(stPoint.x, pstGuiImage->Width-radius-1);
            stPoint.y = max(radius, min(stPoint.y, pstGuiImage->Height-radius-1));
            radius = min(radius, stPoint.y);
            radius = min(radius, pstGuiImage->Height - stPoint.y - 1);
            break;
        case MPP_GUI_CIRCLE_TOTAL:
        default:
            stPoint.x = max(radius, min(stPoint.x, pstGuiImage->Width-radius-1));
            stPoint.y = max(radius, min(stPoint.y, pstGuiImage->Height-radius-1));
            radius = min(radius, stPoint.x);
            radius = min(radius, pstGuiImage->Width - stPoint.x - 1);
            radius = min(radius, stPoint.y);
            radius = min(radius, pstGuiImage->Height - stPoint.y - 1);
            break;
    }

    stPoint.x = min(stPoint.x, pstGuiImage->Width-1);
    stPoint.y = min(stPoint.y, pstGuiImage->Height-1);

    switch (pstGuiImage->format)
    {
    case MPP_GUI_FMT_RGB_8BPP:
        u8color = stColor.u8color;
        return RUN_PAINT_CIRCLE_BPP(8BPP)(pstGuiImage, stPoint, &u8color, radius, 0, part);
        break;
    case MPP_GUI_FMT_ARGB_1555:
	case MPP_GUI_FMT_RGB_565:
	case MPP_GUI_FMT_BGR_565:
        u16color = stColor.u16color;
        return RUN_PAINT_CIRCLE_BPP(16BPP)(pstGuiImage, stPoint, &u16color, radius, 0, part);
        break;
    case MPP_GUI_FMT_RGB_888:
        u24color.data[0] = stColor.u24color & 0xff;
        u24color.data[1] = (stColor.u24color >> 8) & 0xff;
        u24color.data[2] = (stColor.u24color >> 16) & 0xff;
        return RUN_PAINT_CIRCLE_BPP(24BPP)(pstGuiImage, stPoint, &u24color, radius, 0, part);
        break;
    case MPP_GUI_FMT_ARGB_8888:
        u32color = stColor.u32color;
        return RUN_PAINT_CIRCLE_BPP(32BPP)(pstGuiImage, stPoint, &u32color, radius, 0, part);
        break;
    case MPP_GUI_FMT_YUV_420SP:
        u8yuv[0] = stColor.u8yuv[0];
        u8yuv[1] = stColor.u8yuv[1];
        u8yuv[2] = stColor.u8yuv[2];
        return RUN_PAINT_CIRCLE_BPP(YUV420SP)(pstGuiImage, stPoint, u8yuv, radius, 0, part);
        break;
    case MPP_GUI_FMT_YUYV:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        u32color = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
        if (part == MPP_GUI_CIRCLE_LEFT || part == MPP_GUI_CIRCLE_RIGHT)
        {
            tmp = (int)(1.0 * radius * pstGuiImage->Width  / pstGuiImage->Height * (9.0 / 16.0));
            return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, tmp>>1, radius, (tmp>>1)-1, radius -2, part);
        }
        else
        {
            tmp = (int)(1.0 * radius * pstGuiImage->Height / pstGuiImage->Width * (16.0 / 9.0));
            return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, radius>>1, tmp, (radius>>1)-1, tmp-2, part);
        }
        break;
    case MPP_GUI_FMT_UYVY:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        u32color = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
        if (part == MPP_GUI_CIRCLE_LEFT || part == MPP_GUI_CIRCLE_RIGHT)
        {
            tmp = (int)(1.0 * radius * pstGuiImage->Width  / pstGuiImage->Height * (9.0 / 16.0));
            return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, tmp>>1, radius, (tmp>>1)-1, radius -2, part);
        }
        else
        {
            tmp = (int)(1.0 * radius * pstGuiImage->Height / pstGuiImage->Width * (16.0 / 9.0));
            return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, radius>>1, tmp, (radius>>1)-1, tmp-2, part);
        }
        break;
    default:
        printf("%s cannot find format %d\n", __func__, pstGuiImage->format);
        return SV_FAILURE;
        break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例绘制圆形
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            stColor    --- 颜色
            radius      --- 半径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_CIRCLE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPoint,
  MPP_GUI_COLOR stColor, uint32 radius, MPP_GUI_CIRCLE_PART part)
{
    MPP_GUI_POINT point;
    stPoint.x = min(1.0, max(0.0, stPoint.x));
    stPoint.y = min(1.0, max(0.0, stPoint.y));
    point.x = max(0,stPoint.x) * pstGuiImage->Width + 0.5;
    point.y = max(0,stPoint.y) * pstGuiImage->Height + 0.5;
    return MPP_GUI_PAINT_CIRCLE(pstGuiImage, point, stColor, radius, part);
}

/******************************************************************************
 * 函数功能: 填充圆形
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            stColor    --- 颜色
            radius      --- 半径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_CIRCLE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  MPP_GUI_COLOR stColor, uint32 radius, MPP_GUI_CIRCLE_PART part)
{
    sint32 s32Ret;
    MPP_GUI_IMAGE_S stGuiImage_tmp;
    MPP_GUI_COLOR   stColor_tmp;
    uint8 u8color;
    uint16 u16color;
    uint24 u24color;
    uint32 u32color;
    uint8  u8yuv[3];

    switch(part)
    {
        case MPP_GUI_CIRCLE_UP:
            stPoint.x = max(radius, min(stPoint.x, pstGuiImage->Width-radius-1));
            stPoint.y = max(radius, stPoint.y);
            radius = min(radius, stPoint.x);
            radius = min(radius, pstGuiImage->Width - stPoint.x - 1);
            break;
        case MPP_GUI_CIRCLE_DOWN:
            stPoint.x = max(radius, min(stPoint.x, pstGuiImage->Width-radius-1));
            stPoint.y = min(stPoint.y, pstGuiImage->Height-radius-1);
            radius = min(radius, stPoint.x);
            radius = min(radius, pstGuiImage->Width - stPoint.x - 1);
            break;
        case MPP_GUI_CIRCLE_LEFT:
            stPoint.x = max(radius, stPoint.x);
            stPoint.y = max(radius, min(stPoint.y, pstGuiImage->Height-radius-1));
            radius = min(radius, stPoint.y);
            radius = min(radius, pstGuiImage->Height - stPoint.y - 1);
            break;
        case MPP_GUI_CIRCLE_RIGHT:
            stPoint.x = min(stPoint.x, pstGuiImage->Width-radius-1);
            stPoint.y = max(radius, min(stPoint.y, pstGuiImage->Height-radius-1));
            radius = min(radius, stPoint.y);
            radius = min(radius, pstGuiImage->Height - stPoint.y - 1);
            break;
        case MPP_GUI_CIRCLE_TOTAL:
        default:
            stPoint.x = max(radius, min(stPoint.x, pstGuiImage->Width-radius-1));
            stPoint.y = max(radius, min(stPoint.y, pstGuiImage->Height-radius-1));
            radius = min(radius, stPoint.x);
            radius = min(radius, pstGuiImage->Width - stPoint.x - 1);
            radius = min(radius, stPoint.y);
            radius = min(radius, pstGuiImage->Height - stPoint.y - 1);
            break;
    }

    stPoint.x = min(stPoint.x, pstGuiImage->Width-1);
    stPoint.y = min(stPoint.y, pstGuiImage->Height-1);

    if (radius <= 1)
    {
        return MPP_GUI_PAINT_POINT(pstGuiImage, stPoint, stColor);
    }

    switch (pstGuiImage->format)
    {
    case MPP_GUI_FMT_RGB_8BPP:
        u8color = stColor.u8color;
        return RUN_PAINT_CIRCLE_BPP(8BPP)(pstGuiImage, stPoint, &u8color, radius, 1, part);
        break;
    case MPP_GUI_FMT_ARGB_1555:
	case MPP_GUI_FMT_RGB_565:
	case MPP_GUI_FMT_BGR_565:
        u16color = stColor.u16color;
        return RUN_PAINT_CIRCLE_BPP(16BPP)(pstGuiImage, stPoint, &u16color, radius, 1, part);
        break;
    case MPP_GUI_FMT_RGB_888:
        u24color.data[0] = stColor.u24color & 0xff;
        u24color.data[1] = (stColor.u24color >> 8) & 0xff;
        u24color.data[2] = (stColor.u24color >> 16) & 0xff;
        return RUN_PAINT_CIRCLE_BPP(24BPP)(pstGuiImage, stPoint, &u24color, radius, 1, part);
        break;
    case MPP_GUI_FMT_ARGB_8888:
        u32color = stColor.u32color;
        return RUN_PAINT_CIRCLE_BPP(32BPP)(pstGuiImage, stPoint, &u32color, radius, 1, part);
        break;
    case MPP_GUI_FMT_YUV_420SP:
        stGuiImage_tmp = *pstGuiImage;
        u8yuv[0] = stColor.u8yuv[0];
        u8yuv[1] = stColor.u8yuv[1];
        u8yuv[2] = stColor.u8yuv[2];
        return RUN_PAINT_CIRCLE_BPP(YUV420SP)(pstGuiImage, stPoint, u8yuv, radius, 1, part);
        return SV_SUCCESS;
        break;
    case MPP_GUI_FMT_YUYV:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        u32color = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
		return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, radius>>1, radius, 0, 0, part);
        break;
    case MPP_GUI_FMT_UYVY:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        u32color = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
		return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, radius>>1, radius, 0, 0, part);
        break;
    default:
        printf("%s cannot find format %d\n", __func__, pstGuiImage->format);
        return SV_FAILURE;
        break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例填充圆形
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            stColor    --- 颜色
            radius      --- 半径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_CIRCLE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPoint,
  MPP_GUI_COLOR stColor, uint32 radius, MPP_GUI_CIRCLE_PART part)
{
    MPP_GUI_POINT point;
    stPoint.x = min(1.0, max(0.0, stPoint.x));
    stPoint.y = min(1.0, max(0.0, stPoint.y));
    point.x = ALIGN2(stPoint.x * pstGuiImage->Width + 0.5);
    point.y = ALIGN2(stPoint.y * pstGuiImage->Height + 0.5);

    point.x = min(1.0, max(0, stPoint.x)) * pstGuiImage->Width;
    point.y = min(1.0, max(0, stPoint.y)) * pstGuiImage->Height;
    return MPP_GUI_FILL_CIRCLE(pstGuiImage, point, stColor, radius, part);
}

/******************************************************************************
 * 函数功能: 绘制椭圆
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            s32A          --- A轴
            s32B          --- B轴
            part          --- 椭圆部位
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_ELLIPSE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  MPP_GUI_COLOR stColor, sint32 s32A, sint32 s32B, MPP_GUI_CIRCLE_PART partion)
{
    sint32 s32Ret;
    MPP_GUI_IMAGE_S stGuiImage_tmp;
    MPP_GUI_COLOR   stColor_tmp;
    uint8 u8color;
    uint16 u16color;
    uint24 u24color;
    uint32 u32color;
    uint8  u8yuv[3];
    if((stPoint.x - s32A < 0) || (stPoint.y - s32B < 0))
    {
        return SV_SUCCESS;
    }
    else if(((stPoint.x + s32A) >= pstGuiImage->Width) || ((stPoint.y + s32B) >= pstGuiImage->Height))
    {
        return SV_SUCCESS;
    }

    if (s32A <= 1 || s32B <= 1)
    {
        return MPP_GUI_PAINT_POINT(pstGuiImage, stPoint, stColor);
    }
    switch (pstGuiImage->format)
    {
    case MPP_GUI_FMT_RGB_8BPP:
        u8color = stColor.u8color;
        return MPP_GUI_PAINT_ELLIPSE_8BPP_(pstGuiImage, stPoint, &u8color, s32A, s32B, partion);
        break;
    case MPP_GUI_FMT_ARGB_1555:
    case MPP_GUI_FMT_RGB_565:
    case MPP_GUI_FMT_BGR_565:
        u16color = stColor.u16color;
        return MPP_GUI_PAINT_ELLIPSE_16BPP_(pstGuiImage, stPoint, &u16color, s32A, s32B, partion);
        break;
    case MPP_GUI_FMT_RGB_888:
        u24color.data[0] = stColor.u24color & 0xff;
        u24color.data[1] = (stColor.u24color >> 8) & 0xff;
        u24color.data[2] = (stColor.u24color >> 16) & 0xff;
        return MPP_GUI_PAINT_ELLIPSE_24BPP_(pstGuiImage, stPoint, &u24color, s32A, s32B, partion);
        break;
    case MPP_GUI_FMT_ARGB_8888:
        u32color = stColor.u32color;
        return MPP_GUI_PAINT_ELLIPSE_32BPP_(pstGuiImage, stPoint, &u32color, s32A, s32B, partion);
        break;
    case MPP_GUI_FMT_YUV_420SP:
        u8yuv[0] = stColor.u8yuv[0];
        u8yuv[1] = stColor.u8yuv[1];
        u8yuv[2] = stColor.u8yuv[2];
        return MPP_GUI_PAINT_ELLIPSE_YUV420SP_(pstGuiImage, stPoint, u8yuv, s32A, s32B, partion);
        break;
    case MPP_GUI_FMT_YUYV:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        s32A >>= 1;
        u32color = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
        return MPP_GUI_PAINT_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, s32A, s32B, partion);
        break;
    case MPP_GUI_FMT_UYVY:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        s32A >>= 1;
        u32color = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
        return MPP_GUI_PAINT_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, s32A, s32B, partion);
        break;
    default:
        printf("%s cannot find format %d\n", __func__, pstGuiImage->format);
        //return SV_FAILURE;
        break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 绘制椭圆
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            s32A          --- A轴
            s32B          --- B轴
            part          --- 椭圆部位
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_ELLIPSE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPoint,
  MPP_GUI_COLOR stColor, float fA, float fB, MPP_GUI_CIRCLE_PART partion)
{
    MPP_GUI_POINT point;
    sint32 s32A, s32B;

    point.x = max(0, stPoint.x) * pstGuiImage->Width;
    point.y = max(0, stPoint.y) * pstGuiImage->Height;
    s32A = fA * pstGuiImage->Width;
    s32B = fB * pstGuiImage->Height;

    return MPP_GUI_PAINT_ELLIPSE(pstGuiImage, point, stColor, s32A, s32B, partion);
}


/******************************************************************************
 * 函数功能: 填充椭圆
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            s32A          --- A轴
            s32B          --- B轴
            part          --- 椭圆部位
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_ELLIPSE(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
  MPP_GUI_COLOR stColor, sint32 s32A, sint32 s32B, sint32 s32a, sint32 s32b, MPP_GUI_CIRCLE_PART partion)
{
    sint32 s32Ret;
    MPP_GUI_IMAGE_S stGuiImage_tmp;
    MPP_GUI_COLOR   stColor_tmp;
    uint8 u8color;
    uint16 u16color;
    uint24 u24color;
    uint32 u32color;
    uint8  u8yuv[3];
/*
    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_UP)
    {
        if ((stPoint.x - s32A < 0) || (stPoint.y - s32B < 0))
            return SV_SUCCESS;
    }

    if(partion == MPP_GUI_CIRCLE_TOTAL || partion == MPP_GUI_CIRCLE_DOWN)
    {
        if(((stPoint.x + s32A) >= pstGuiImage->Width) || ((stPoint.y + s32B) >= pstGuiImage->Height))
            return SV_SUCCESS;
    }
*/
    if (s32A <= 1 || s32B <= 1)
    {
        return MPP_GUI_PAINT_POINT(pstGuiImage, stPoint, stColor);
    }
    switch (pstGuiImage->format)
    {
    case MPP_GUI_FMT_RGB_8BPP:
        u8color = stColor.u8color;
        return MPP_GUI_FILL_ELLIPSE_8BPP_(pstGuiImage, stPoint, &u8color, s32A, s32B, s32a, s32b, partion);
        break;
    case MPP_GUI_FMT_ARGB_1555:
    case MPP_GUI_FMT_RGB_565:
    case MPP_GUI_FMT_BGR_565:
        u16color = stColor.u16color;
        return MPP_GUI_FILL_ELLIPSE_16BPP_(pstGuiImage, stPoint, &u16color, s32A, s32B, s32a, s32b, partion);
        break;
    case MPP_GUI_FMT_RGB_888:
        u24color.data[0] = stColor.u24color & 0xff;
        u24color.data[1] = (stColor.u24color >> 8) & 0xff;
        u24color.data[2] = (stColor.u24color >> 16) & 0xff;
        return MPP_GUI_FILL_ELLIPSE_24BPP_(pstGuiImage, stPoint, &u24color, s32A, s32B, s32a, s32b, partion);
        break;
    case MPP_GUI_FMT_ARGB_8888:
        u32color = stColor.u32color;
        return MPP_GUI_FILL_ELLIPSE_32BPP_(pstGuiImage, stPoint, &u32color, s32A, s32B, s32a, s32b, partion);
        break;
    case MPP_GUI_FMT_YUV_420SP:
        u8yuv[0] = stColor.u8yuv[0];
        u8yuv[1] = stColor.u8yuv[1];
        u8yuv[2] = stColor.u8yuv[2];
        return MPP_GUI_FILL_ELLIPSE_YUV420SP_(pstGuiImage, stPoint, u8yuv, s32A, s32B, s32a, s32b, partion);
        break;
    case MPP_GUI_FMT_YUYV:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        s32A >>= 1;
        s32a >>= 1;
        u32color = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
        return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, s32A, s32B, s32a, s32b, partion);
        break;
    case MPP_GUI_FMT_UYVY:
        stGuiImage_tmp = *pstGuiImage;
        stGuiImage_tmp.Width >>= 1;
        stPoint.x >>= 1;
        s32A >>= 1;
        s32a >>= 1;
        u32color = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
        return MPP_GUI_FILL_ELLIPSE_32BPP_(&stGuiImage_tmp, stPoint, &u32color, s32A, s32B, s32a, s32b, partion);
        break;
    default:
        printf("%s cannot find format %d\n", __func__, pstGuiImage->format);
        //return SV_FAILURE;
        break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 填充椭圆
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            s32A          --- A轴
            s32B          --- B轴
            part          --- 椭圆部位
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_ELLIPSE_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPoint,
  MPP_GUI_COLOR stColor, float fA, float fB, float fa, float fb, MPP_GUI_CIRCLE_PART partion)
{
    MPP_GUI_POINT point;
    sint32 s32A, s32B;
    sint32 s32a, s32b;

    point.x = max(0, stPoint.x) * pstGuiImage->Width;
    point.y = max(0, stPoint.y) * pstGuiImage->Height;
    s32A = fA * pstGuiImage->Width;
    s32B = fB * pstGuiImage->Height;
    s32a = fa * pstGuiImage->Width;
    s32b = fb * pstGuiImage->Height;

    return MPP_GUI_FILL_ELLIPSE(pstGuiImage, point, stColor, s32A, s32B, s32a, s32b, partion);
}


/******************************************************************************
 * 函数功能: 绘制圆弧
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            stColor    --- 颜色
            radius      --- 半径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_ARC(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPoint,
	MPP_GUI_COLOR stColor, uint32 radius, float theta1, float theta2, uint32 stick)
{
	sint32 s32Ret, i, n;
	float theta, step, pi = M_PI;
	uint32 radius_sub = stick / 2;
	MPP_GUI_POINT point;
    theta1 = theta1 > pi ? theta1 - pi : theta1;
    theta2 = theta2 > pi ? theta2 - pi : theta2;

	n = (sint32)((theta2 - theta1) / (1.0 * radius_sub / radius));
	n = n > 0 ? n : -n;
	step = (theta2 - theta1) / n;
	theta = theta1;
	for(i = 0; i < n + 1; i++)
	{
		point.x = stPoint.x + (int)(radius * cos(theta) + 0.5);
		point.y = stPoint.y - (int)(radius * sin(theta) + 0.5);
		s32Ret = MPP_GUI_FILL_CIRCLE(pstGuiImage, point, stColor, radius_sub, MPP_GUI_CIRCLE_TOTAL);
	    if(s32Ret != SV_SUCCESS)
	    {
			return SV_FAILURE;
		}
		theta += step;
	}
	return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例绘制圆弧
 * 输入参数: pstPaintImage --- 图像信息
            stPoint       --- 起始点
            stColor    --- 颜色
            radius      --- 半径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_ARC_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPoint,
	MPP_GUI_COLOR stColor, uint32 radius, float theta1, float theta2, uint32 stick)
{
    MPP_GUI_POINT point;
    stPoint.x = min(1.0, max(0.0, stPoint.x));
    stPoint.y = min(1.0, max(0.0, stPoint.y));
    point.x = ALIGN2(stPoint.x * pstGuiImage->Width + 0.5);
    point.y = ALIGN2(stPoint.y * pstGuiImage->Height + 0.5);
    return MPP_GUI_PAINT_ARC(pstGuiImage, point, stColor, radius, theta1, theta2, stick);
}


/******************************************************************************
 * 函数功能: 按比例绘制矩形框
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            stColor        --- 颜色
            stick           --- 矩形边框宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_RECT_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPointUp,
    MPP_GUI_POINT_S stPointDown, MPP_GUI_COLOR stColor, uint32 stick)
{
    MPP_GUI_POINT pstPoint[4];

	stPointUp.x = min(1.0, max(0.0, stPointUp.x));
	stPointUp.y = min(1.0, max(0.0, stPointUp.y));
	stPointDown.x = min(1.0, max(0.0, stPointDown.x));
	stPointDown.y = min(1.0, max(0.0, stPointDown.y));

    pstPoint[0].x = ALIGN2(max(stPointUp.x, 0) * pstGuiImage->Width + 0.5);
    pstPoint[0].y = ALIGN2(max(stPointUp.y, 0) * pstGuiImage->Height + 0.5);

    pstPoint[1].x = ALIGN2(max(stPointUp.x, 0) * pstGuiImage->Width + 0.5);
    pstPoint[1].y = ALIGN2(max(stPointDown.y, 0) * pstGuiImage->Height + 0.5);

    pstPoint[2].x = ALIGN2(max(stPointDown.x, 0) * pstGuiImage->Width + 0.5);
    pstPoint[2].y = ALIGN2(max(stPointDown.y, 0) * pstGuiImage->Height + 0.5);

    pstPoint[3].x = ALIGN2(max(stPointDown.x, 0) * pstGuiImage->Width + 0.5);
    pstPoint[3].y = ALIGN2(max(stPointUp.y, 0) * pstGuiImage->Height + 0.5);

    return MPP_GUI_PAINT_CLOSE_MUTIL_LINE(pstGuiImage, pstPoint, 4, stColor, stick);
}

/******************************************************************************
 * 函数功能: 填充矩形框
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            stColor        --- 颜色
            stick           --- 矩形边框宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_RECT(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPointUp,
    MPP_GUI_POINT stPointDown, MPP_GUI_COLOR stColor)
{
    uint8  *u8Filed, u8value;
    uint16 *u16Filed, u16value;
    uint24 *u24Filed, u24value;
    uint32 *u32Filed, u32value;
    uint8 y_value;
    uint16 uv_value;

    int i,j;
    int width, height, width_half, height_half;
    int width_save, height_save, width_save_half, height_save_half;
    int x_start, y_start, x_end, y_end, x_start_half, y_start_half;

    x_start = max(0, min(stPointUp.x, pstGuiImage->Width-1));
    y_start = max(0, min(stPointUp.y, pstGuiImage->Height-1));
    x_end   = max(0, min(stPointDown.x, pstGuiImage->Width-1));
    y_end   = max(0, min(stPointDown.y, pstGuiImage->Height-1));

    if(x_end == 0 || y_end == 0 || x_end < x_start || y_end < y_start)
    {
        return SV_SUCCESS;
    }


    width_save  = x_end - x_start + 1;
    height_save = y_end - y_start + 1;
    switch(pstGuiImage->format){
        case MPP_GUI_FMT_RGB_8BPP:
            u8Filed = pstGuiImage->pbmp;
            u8Filed += y_start * pstGuiImage->Width + x_start;
            u8value = stColor.u8color;
            for(i = 0; i < height_save; i++)
            {
                memset(u8Filed, &u8value, width_save);
                u8Filed += pstGuiImage->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
            u16Filed = pstGuiImage->pbmp;
            u16Filed += y_start * pstGuiImage->Width + x_start;
            u16value = stColor.u16color;

            for(i = 0; i < height_save; i++)
            {
                for(j = 0; j < width_save; j++)
                {
                    *(u16Filed++) = u16value;
                }
               u16Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_RGB_888:
            u24Filed = pstGuiImage->pbmp;
            u24Filed += (y_start * pstGuiImage->Width + x_start);
            u24value.data[0] = stColor.u24color & 0xff;
            u24value.data[1] = (stColor.u24color >> 8) & 0xff;
            u24value.data[2] = (stColor.u24color >> 16) & 0xff;
            for(i=0; i < height_save; i++)
            {
                for(j = 0; j < width_save; j++)
                {
                    *(u24Filed++) = u24value;
                }
                u24Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_ARGB_8888:
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y_start * pstGuiImage->Width + x_start;
            u32value = stColor.u32color;
            for(i = 0; i < height_save; i++)
            {
                for(j = 0; j < width_save; j++)
                {
                    *(u32Filed++) = u32value;
                }
               u32Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_YUV_420SP:
            //y_start -= 2;
            x_start_half = ALIGN2D(x_start) >> 1;
            y_start_half = ALIGN2D(y_start) >> 1;
            width = pstGuiImage->Width;
            height = pstGuiImage->Height;
            width_half = ALIGN2(width) >> 1;
            height_half = ALIGN2(height) >> 1;
            width_save_half = ALIGN2(width_save) >> 1;
            height_save_half = ALIGN2(height_save) >> 1;

            u8Filed = pstGuiImage->pbmp;
            u16Filed = (pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height);

            u8Filed += y_start * width + x_start;
            u16Filed += y_start_half * width_half + x_start_half;
            u8value = stColor.u8yuv[0];
            u16value = (stColor.u8yuv[1] & 0xff) | ((stColor.u8yuv[2] << 8)& 0xff00);

            for(i = 0; i < height_save; i++)
            {
                memset(u8Filed, u8value, width_save);             
                u8Filed += pstGuiImage->Width;
            }
            for(i=0; i < height_save_half; i++)
            {
                for(j = 0; j < width_save_half; j++)
                {
                    *(u16Filed++) = u16value;
                }
                u16Filed += (width_half - j);
            }
            break;
        case MPP_GUI_FMT_YUYV:
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y_start * (pstGuiImage->Width >> 1) + (x_start>>1);
            u32value = (stColor.u8yuv[0]) | (stColor.u8yuv[1] << 8) | (stColor.u8yuv[0] << 16) | (stColor.u8yuv[2] << 24);
            width_save_half = width_save>>1;
            width_half = pstGuiImage->Width>>1;
            for(i = 0; i < height_save; i++)
            {
                for(j = 0; j < width_save_half; j++)
                {
                    *(u32Filed++) = u32value;
                }
               u32Filed += (width_half - j);
            }
            break;
        case MPP_GUI_FMT_UYVY:
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y_start * (pstGuiImage->Width >> 1) + (x_start>>1);
            u32value = (stColor.u8yuv[1]) | (stColor.u8yuv[0] << 8) | (stColor.u8yuv[2] << 16) | (stColor.u8yuv[0] << 24);
            width_save_half = max(1, width_save>>1);
            width_half = pstGuiImage->Width>>1;
            for(i = 0; i < height_save; i++)
            {
                for(j = 0; j < width_save_half; j++)
                {
                    *(u32Filed++) = u32value;
                }
               u32Filed += (width_half - j);
            }
            break;
        default:
            printf("Cannot find GUI FMT:%d\n", pstGuiImage->format);
            return SV_FAILURE;
            break;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例填充矩形框
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            stColor        --- 颜色
            stick           --- 矩形边框宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_RECT_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPointUp,
    MPP_GUI_POINT_S stPointDown, MPP_GUI_COLOR stColor)
{
    MPP_GUI_POINT stPoint[2];
    stPointUp.x = min(1.0, max(0.0, stPointUp.x));
    stPointUp.y = min(1.0, max(0.0, stPointUp.y));
    stPointDown.x = min(1.0, max(0.0, stPointDown.x));
    stPointDown.y = min(1.0, max(0.0, stPointDown.y));
    stPoint[0].x = ALIGN2(pstGuiImage->Width * max(0, stPointUp.x));
    stPoint[0].y = ALIGN2(pstGuiImage->Height * max(0, stPointUp.y));
    stPoint[1].x = ALIGN2(pstGuiImage->Width * max(0, stPointDown.x));
    stPoint[1].y = ALIGN2(pstGuiImage->Height * max(0, stPointDown.y));
    return MPP_GUI_FILL_RECT(pstGuiImage, stPoint[0], stPoint[1], stColor);
}

/******************************************************************************
 * 函数功能: 填充矩形框
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            stColor        --- 颜色
            stick           --- 矩形边框宽度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_RECT_HALF(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPointUp,
    MPP_GUI_POINT stPointDown, MPP_GUI_COLOR stColor)
{
    uint8  *u8Filed, u8value;
    uint16 *u16Filed, u16value;
    uint24 *u24Filed, u24value;
    uint32 *u32Filed, u32value;
    uint8 y_value;
    uint16 uv_value;

    int i,j;
    int width, height, width_half, height_half;
    int width_save, height_save, width_save_half, height_save_half;
    int x_start, y_start, x_end, y_end, x_start_half, y_start_half;
    int x_len_half, y_len_half;
    uint8 y, u, v;

    x_start = max(0, min(stPointUp.x, pstGuiImage->Width-1));
    y_start = max(0, min(stPointUp.y, pstGuiImage->Height-1));
    x_end   = max(0, min(stPointDown.x, pstGuiImage->Width-1));
    y_end   = max(0, min(stPointDown.y, pstGuiImage->Height-1));

    if(x_end == 0 || y_end == 0 || x_end < x_start || y_end < y_start)
    {
        return SV_SUCCESS;
    }


    width_save  = x_end - x_start + 1;
    height_save = y_end - y_start + 1;
    switch(pstGuiImage->format){
        case MPP_GUI_FMT_RGB_8BPP:
            u8Filed = pstGuiImage->pbmp;
            u8Filed += y_start * pstGuiImage->Width + x_start;
            u8value = stColor.u8color;
            for(i = 0; i < height_save; i++)
            {
                for(j = 0; j < width_save; j++)
                {
                    *u8Filed = (*u8Filed >> 1) + (u8value>>1);
                    u8Filed++;
                }
            }
            break;
        case MPP_GUI_FMT_ARGB_1555:
            u16Filed = pstGuiImage->pbmp;
            u16Filed += y_start * pstGuiImage->Width + x_start;
            u16value = stColor.u16color;
            for(i = 0; i < height_save; i++)
            {
                for(j = 0; j < width_save; j++)
                {
                    /* 整体右移后,去除最高位,防止数据被侵蚀 */
                    *(u16Filed) = (((*(u16Filed))>> 1) & 0b0111101111101111) + ((u16value >> 1) & 0b0111101111101111);
                    u16Filed++;
                }
               u16Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
            u16Filed = pstGuiImage->pbmp;
            u16Filed += y_start * pstGuiImage->Width + x_start;
            u16value = (stColor.u16color >> 1) & 0b0111101111101111;
            for(i = 0; i < height_save; i++)
            {
                for(j = 0; j < width_save; j++)
                {
                    /* 整体右移后,去除最高位,防止数据被侵蚀 */
                    *(u16Filed) = (((*(u16Filed))>> 1) & 0b0111101111101111) + u16value;
                    u16Filed++;
                }
               u16Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_RGB_888:
            break;
        case MPP_GUI_FMT_ARGB_8888:
            break;
        case MPP_GUI_FMT_YUV_420SP:
            width_save_half  = width_save >> 1;
            height_save_half = height_save >> 1;

            u8Filed = pstGuiImage->pbmp;
            u16Filed = pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height;
            u8Filed  += (x_start + y_start * pstGuiImage->Width);
            u16Filed += ((x_start/2 + (y_start>>1) * pstGuiImage->Width/2));
            y = stColor.u8yuv[0] >> 1;
            u = stColor.u8yuv[1] >> 1;
            v = stColor.u8yuv[2] >> 1;
            for(i= 0; i < height_save ; i++)
            {
                uint8 *buf1;
                buf1 = u8Filed;

                for(j=0; j < width_save; j++)
                {
                    *buf1 = (*buf1>>1) + y;
                    buf1++;
                }
                //memcpy(u8Filed, u8Filed_Src, x_len);
                u8Filed += pstGuiImage->Width;
            }
            width_half = pstGuiImage->Width>>1;
            for(i=0; i < height_save_half; i++)
            {
                uint8 *u8Ubuf1;
                uint8 *u8Vbuf1;
                u8Ubuf1 = u16Filed;
                u8Vbuf1 = u8Ubuf1+1;
                for(j=0; j< width_save_half; j++)
                {
                    *u8Ubuf1 = (*u8Ubuf1>>1) + u;
                    *u8Vbuf1 = (*u8Vbuf1>>1) + v;
                    u8Ubuf1+=2;
                    u8Vbuf1+=2;
                }
                //memcpy(u16Filed, u16Filed_Src, x_len);
                u16Filed += width_half;
            }
            break;
        case MPP_GUI_FMT_YUYV:
            break;
        case MPP_GUI_FMT_UYVY:
            break;
        default:
            printf("Cannot find GUI FMT:%d\n", pstGuiImage->format);
            return SV_FAILURE;
            break;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 对矩形区进行马赛克
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            u32BlkSize     --- 马赛克方块大小(像素点)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_FILL_RECT_HALF_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPointUp,
    MPP_GUI_POINT_S stPointDown, MPP_GUI_COLOR stColor)
{
    MPP_GUI_POINT stPoint[2];
    stPointUp.x = min(1.0, max(0.0, stPointUp.x));
    stPointUp.y = min(1.0, max(0.0, stPointUp.y));
    stPointDown.x = min(1.0, max(0.0, stPointDown.x));
    stPointDown.y = min(1.0, max(0.0, stPointDown.y));
    stPoint[0].x = ALIGN2(pstGuiImage->Width * max(0, stPointUp.x));
    stPoint[0].y = ALIGN2(pstGuiImage->Height * max(0, stPointUp.y));
    stPoint[1].x = ALIGN2(pstGuiImage->Width * max(0, stPointDown.x));
    stPoint[1].y = ALIGN2(pstGuiImage->Height * max(0, stPointDown.y));
    return MPP_GUI_FILL_RECT_HALF(pstGuiImage, stPoint[0], stPoint[1], stColor);
}

/******************************************************************************
 * 函数功能: 对矩形区进行马赛克
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            fBlkSize     --- 马赛克方块大小(占区域比例)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_MOSAIC_RECT(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPointUp,
    MPP_GUI_POINT stPointDown, float fBlkSize)
{
    uint8  *u8Filed, u8value, au8value[1920], *pu8value;
    uint16 *u16Filed, u16value, au16value[1920], *pu16value;
    uint24 *u24Filed, u24value, au24value[1920], *pu24value;
    uint32 *u32Filed, u32value, au32value[1920], *pu32value;
    uint32 u32BlkSize;
    uint8 y_value;
    uint16 uv_value;

    int i,j;
    int width, height, width_half, height_half;
    int width_save, height_save, width_save_half, height_save_half;
    int x_start, y_start, x_end, y_end, x_start_half, y_start_half;


    x_start = max(0, min(stPointUp.x, pstGuiImage->Width-1));
    y_start = max(0, min(stPointUp.y, pstGuiImage->Height-1));
    x_end   = max(0, min(stPointDown.x, pstGuiImage->Width-1));
    y_end   = max(0, min(stPointDown.y, pstGuiImage->Height-1));
    u32BlkSize = max(1, (uint32)(min(x_end-x_start, y_end-y_start)*fBlkSize));

    if(x_end == 0 || y_end == 0 || x_end < x_start || y_end < y_start)
    {
        return SV_SUCCESS;
    }


    width_save  = x_end - x_start + 1;
    height_save = y_end - y_start + 1;
    switch(pstGuiImage->format){
        case MPP_GUI_FMT_RGB_8BPP:
            u8Filed = pstGuiImage->pbmp;
            u8Filed += y_start * pstGuiImage->Width + x_start;
            for (i = 0; i < height_save; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for (j = 0; j < width_save; j++)
                        au8value[j] = u8Filed[j / u32BlkSize * u32BlkSize];
                }

                pu8value = au8value;
                for (j = 0; j < width_save; j++)
                {
                    *(u8Filed++) = *(pu8value++);
                }
                u8Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
            u16Filed = pstGuiImage->pbmp;
            u16Filed += y_start * pstGuiImage->Width + x_start;
            for(i = 0; i < height_save; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for (j = 0; j < width_save_half; j++)
                        au16value[j] = u16Filed[j / u32BlkSize * u32BlkSize];
                }

                pu16value = au16value;
                for(j = 0; j < width_save; j++)
                {
                    *(u16Filed++) = *(pu16value++);;
                }
               u16Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_RGB_888:
            u24Filed = pstGuiImage->pbmp;
            u24Filed += (y_start * pstGuiImage->Width + x_start);
            for(i=0; i < height_save; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for(j = 0; j < width_save_half; j++)
                        au24value[j] = u24Filed[j / u32BlkSize * u32BlkSize];
                }

                pu24value = au24value;
                for(j = 0; j < width_save; j++)
                {
                    *(u24Filed++) = *(pu24value++);
                }
                u24Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_ARGB_8888:
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y_start * pstGuiImage->Width + x_start;
            for(i = 0; i < height_save; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for(j = 0; j < width_save_half; j++)
                        au32value[j] = u32Filed[j / u32BlkSize * u32BlkSize];
                }

                pu32value = au32value;
                for(j = 0; j < width_save; j++)
                {
                    *(u32Filed++) = *(pu32value++);
                }
               u32Filed += (pstGuiImage->Width - j);
            }
            break;
        case MPP_GUI_FMT_YUV_420SP:
            //y_start -= 2;
            x_start_half = ALIGN2D(x_start) >> 1;
            y_start_half = ALIGN2D(y_start) >> 1;
            width = pstGuiImage->Width;
            height = pstGuiImage->Height;
            width_half = ALIGN2(width) >> 1;
            height_half = ALIGN2(height) >> 1;
            width_save_half = ALIGN2(width_save) >> 1;
            height_save_half = ALIGN2(height_save) >> 1;

            u8Filed = pstGuiImage->pbmp;
            u16Filed = (pstGuiImage->pbmp + pstGuiImage->Width * pstGuiImage->Height);

            u8Filed += y_start * width + x_start;
            u16Filed += y_start_half * width_half + x_start_half;
            for (i = 0; i < height_save; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for (j = 0; j < width_save; j++)
                        au8value[j] = u8Filed[j / u32BlkSize * u32BlkSize];
                }

                pu8value = au8value;
                for (j = 0; j < width_save; j++)
                {
                    *(u8Filed++) = *(pu8value++);
                }
                u8Filed += (pstGuiImage->Width - j);
            }
            for (i=0; i < height_save_half; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for (j = 0; j < width_save_half; j++)
                        au16value[j] = u16Filed[j / u32BlkSize * u32BlkSize];
                }

                pu16value = au16value;
                for (j = 0; j < width_save_half; j++)
                {
                    *(u16Filed++) = *(pu16value++);
                }
                u16Filed += (width_half - j);
            }
            break;
        case MPP_GUI_FMT_YUYV:
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y_start * (pstGuiImage->Width >> 1) + (x_start>>1);
            width_save_half = width_save>>1;
            width_half = pstGuiImage->Width>>1;
            for(i = 0; i < height_save; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for(j = 0; j < width_save_half; j++)
                        au32value[j] = u32Filed[j / u32BlkSize * u32BlkSize];
                }

                pu32value = au32value;
                for(j = 0; j < width_save_half; j++)
                {
                    *(u32Filed++) = *(pu32value++);
                }
                u32Filed += (width_half - j);
            }
            break;
        case MPP_GUI_FMT_UYVY:
            u32Filed = pstGuiImage->pbmp;
            u32Filed += y_start * (pstGuiImage->Width >> 1) + (x_start>>1);
            width_save_half = max(1, width_save>>1);
            width_half = pstGuiImage->Width>>1;
            for(i = 0; i < height_save; i++)
            {
                if (0 == (i % u32BlkSize))
                {
                    for(j = 0; j < width_save_half; j++)
                        au32value[j] = u32Filed[j / u32BlkSize * u32BlkSize];
                }

                pu32value = au32value;
                for(j = 0; j < width_save_half; j++)
                {
                    *(u32Filed++) = *(pu32value++);
                }
                u32Filed += (width_half - j);
            }
            break;
        default:
            printf("Cannot find GUI FMT:%d\n", pstGuiImage->format);
            return SV_FAILURE;
            break;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 按比例填充矩形框
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stPointDown    --- 右下角
            fBlkSize     --- 马赛克方块大小(占区域比例)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_MOSAIC_RECT_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPointUp,
    MPP_GUI_POINT_S stPointDown, float fBlkSize)
{
    MPP_GUI_POINT stPoint[2];
    stPointUp.x = min(1.0, max(0.0, stPointUp.x));
    stPointUp.y = min(1.0, max(0.0, stPointUp.y));
    stPointDown.x = min(1.0, max(0.0, stPointDown.x));
    stPointDown.y = min(1.0, max(0.0, stPointDown.y));
    stPoint[0].x = ALIGN2(pstGuiImage->Width * max(0, stPointUp.x));
    stPoint[0].y = ALIGN2(pstGuiImage->Height * max(0, stPointUp.y));
    stPoint[1].x = ALIGN2(pstGuiImage->Width * max(0, stPointDown.x));
    stPoint[1].y = ALIGN2(pstGuiImage->Height * max(0, stPointDown.y));
    return MPP_GUI_MOSAIC_RECT(pstGuiImage, stPoint[0], stPoint[1], fBlkSize);
}

/******************************************************************************
 * 函数功能: 获取绘制的字符串大小
 * 输入参数: stString        --- 字符串指针
            fontsize        --- 字体大小
            enAngle         --- 方向
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_POINT MPP_GUI_Get_String_Size(char *stString, MPP_GUI_FONTSIZE fontsize,
                MPP_GUI_ROTATE_ANGLE enAngle)
{
    MPP_GUI_POINT size;
    int scale;
    scale = fontsize + 1;
    switch(enAngle){
        case MPP_GUI_ROTATE_0:
            size.x = scale * 8 + strlen(stString);
            size.y = scale * 8;
            break;
        case MPP_GUI_ROTATE_90:
            size.x = scale * 8;
            size.y = scale + strlen(stString);
            break;
        case MPP_GUI_ROTATE_180:
            size.x = scale * 8 + strlen(stString);
            size.y = scale * 8;
            break;
        case MPP_GUI_ROTATE_270:
            size.x = scale * 8;
            size.y = scale + strlen(stString);
            break;
    }

    return size;
}

/******************************************************************************
 * 函数功能: 获取绘制的字符串大小
 * 输入参数: stString        --- 字符串指针
            fontsize        --- 字体大小
            enAngle         --- 方向
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_POINT_S MPP_GUI_Get_String_Size_S(MPP_GUI_IMAGE_S *pstGuiImage, char *stString,
                MPP_GUI_FONTSIZE fontsize, MPP_GUI_ROTATE_ANGLE enAngle)
{
    MPP_GUI_POINT size;
    MPP_GUI_POINT_S size_s;
    size = MPP_GUI_Get_String_Size(stString, fontsize, enAngle);
    size_s.x = 1.0 * size.x / pstGuiImage->Width;
    size_s.y = 1.0 * size.y / pstGuiImage->Height;
    return size_s;
}

/******************************************************************************
 * 函数功能: 获取对应的旋转点
 * 输入参数: point        --- 点
 *          enAngle         --- 方向
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_POINT_S MPP_GUI_Get_Rotate_Point(MPP_GUI_POINT_S point, MPP_GUI_ROTATE_ANGLE enAngle)
{
    MPP_GUI_POINT_S tmp;
    switch(enAngle){
        case MPP_GUI_ROTATE_0:
            tmp.x = point.x;
            tmp.y = point.y;
            break;
        case MPP_GUI_ROTATE_90:
            tmp.x = 1 - point.y;
            tmp.y = point.x;
            break;
        case MPP_GUI_ROTATE_180:
            tmp.x = 1 - point.x;
            tmp.y = 1 - point.y;
            break;
        case MPP_GUI_ROTATE_270:
            tmp.x = point.y;
            tmp.y = 1 - point.x;
            break;
    }
    return tmp;
}

/******************************************************************************
 * 函数功能: 绘制字符串
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stString        --- 字符串指针
            stColor        --- 颜色
            fontsize        --- 字体大小
            bBackGround     --- 使能背景
            stColorBG    --- 背景色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_STRING(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT stPointUp,
    char* stString,  MPP_GUI_COLOR stColor, MPP_GUI_FONTSIZE fontsize,
    int bBackGround, MPP_GUI_COLOR stColorBG, MPP_GUI_ROTATE_ANGLE enAngle)
{
    int str_len, paint_len;
    int x_start, y_start, scale, i=0, j=0, k=0;
    uint8 u8char, parse;
    uint8 *pu8fontdata, *pu8fontparse;
    MPP_GUI_POINT stPoint1, stPoint2;
    MPP_GUI_POINT rect1, rect2;
    scale = fontsize + 1;
    x_start = max(2, min(stPointUp.x, pstGuiImage->Width));
    y_start = max(2, min(stPointUp.y, pstGuiImage->Height));

    str_len = strlen(stString);
    switch (enAngle)
    {
        case MPP_GUI_ROTATE_0:{
            if(pstGuiImage->Height - y_start < scale * 8)
            {
                return SV_SUCCESS;
            }
            paint_len = (pstGuiImage->Width - x_start) / (scale * 8);
            paint_len = min(str_len, paint_len);
            if(bBackGround)
            {
                MPP_GUI_POINT stPointRectL, stPointRectR;
                stPointRectL = stPointUp;
                stPointRectR.x = stPointRectL.x + paint_len * scale * 8;
                stPointRectR.y = stPointRectL.y + 1 * scale * 8;
                stPointRectL.x -= scale;
                stPointRectL.y -= scale;
                //stPointRectR.x += scale;
                //stPointRectR.y += scale;
                MPP_GUI_FILL_RECT(pstGuiImage, stPointRectL, stPointRectR, stColorBG);
            }
            for(i = 0; i < paint_len; i++) // 第i个字符
            {
                u8char = stString[i];
                pu8fontdata = mpp_pixel_fontdata[u8char];
                stPoint1.y = y_start;
                for(j = 0; j < 8; j++) // 第j行
                {
                    parse = pu8fontdata[j];
                    pu8fontparse = mpp_pixel_parse[parse];
                    stPoint1.x = x_start + i * 8 * scale;
                    for(k = 0; k < 8; k++, stPoint1.x += scale) // 第k个模点
                    {
                        if(!pu8fontparse[k])
                            continue;
                        //stPoint1.x = x_start + (i * 8 + k) * scale;
                        //stPoint1.y = y_start + j * scale;
                        stPoint2.x = stPoint1.x + scale;
                        stPoint2.y = stPoint1.y + scale;
                        MPP_GUI_FILL_RECT(pstGuiImage, stPoint1, stPoint2, stColor);
                    }
                    stPoint1.y += scale;
                }
            }
        }   break;
        case MPP_GUI_ROTATE_90:{
            if(x_start < scale * 8)
            {
                return SV_SUCCESS;
            }
            paint_len = (pstGuiImage->Height - y_start) / (scale * 8);
            paint_len = min(str_len, paint_len);
            if(bBackGround)
            {
                MPP_GUI_POINT stPointRectL, stPointRectR;
                stPointRectL.x = stPointUp.x - scale * 8;
                stPointRectL.y = stPointUp.y;
                stPointRectR.x = stPointUp.x;
                stPointRectR.y = stPointUp.y + paint_len * scale * 8;

                stPointRectL.x += scale;
                stPointRectL.y -= scale;
                stPointRectR.x += scale;
                //stPointRectR.y -= scale;
                MPP_GUI_FILL_RECT(pstGuiImage, stPointRectL, stPointRectR, stColorBG);
            }
            for(i = 0; i < paint_len; i++) // 第i个字符
            {
                u8char = stString[i];
                pu8fontdata = mpp_pixel_fontdata[u8char];
                stPoint1.x = x_start;
                for(j = 0; j < 8; j++) // 第j行
                {
                    parse = pu8fontdata[j];
                    pu8fontparse = mpp_pixel_parse[parse];
                    stPoint1.y = y_start + (i * 8) * scale;
                    for(k = 0; k < 8; k++, stPoint1.y += scale) // 第k个模点
                    {
                        if(!pu8fontparse[k])
                            continue;

                        //stPoint1.x = x_start - j * scale;
                        //stPoint1.y = y_start + (i * 8 + k) * scale;
                        stPoint2.x = stPoint1.x + scale;
                        stPoint2.y = stPoint1.y + scale;
                        MPP_GUI_FILL_RECT(pstGuiImage, stPoint1, stPoint2, stColor);
                    }
                    stPoint1.x -= scale;
                }
            }
        }   break;
        case MPP_GUI_ROTATE_180:{
            if(pstGuiImage->Width - x_start < scale * 8)
            {
                return SV_SUCCESS;
            }

            paint_len = (x_start) / (scale * 8);
            paint_len = min(str_len, paint_len);
            if(bBackGround)
            {
                MPP_GUI_POINT stPointRectL, stPointRectR;
                stPointRectL.x = stPointUp.x - paint_len * scale * 8;
                stPointRectL.y = stPointUp.y - 1 * scale * 8;
                stPointRectR = stPointUp;
                //stPointRectL.x -= scale;
                stPointRectL.y += scale;
                stPointRectR.x += scale;
                stPointRectR.y += scale;
                MPP_GUI_FILL_RECT(pstGuiImage, stPointRectL, stPointRectR, stColorBG);
            }
            for(i = 0; i < paint_len; i++) // 第i个字符
            {
                u8char = stString[i];
                pu8fontdata = mpp_pixel_fontdata[u8char];
                stPoint1.y = y_start;
                for(j = 0; j < 8; j++) // 第j行
                {
                    parse = pu8fontdata[j];
                    pu8fontparse = mpp_pixel_parse[parse];
                    stPoint1.x = x_start - i * 8 * scale;
                    for(k = 0; k < 8; k++, stPoint1.x -= scale) // 第k个模点
                    {
                        if(!pu8fontparse[k])
                            continue;
                        //stPoint1.x = x_start - (i * 8 + k) * scale;
                        //stPoint1.y = y_start - j * scale;
                        stPoint2.x = stPoint1.x + scale;
                        stPoint2.y = stPoint1.y + scale;
                        MPP_GUI_FILL_RECT(pstGuiImage, stPoint1, stPoint2, stColor);
                    }
                    stPoint1.y -= scale;
                }
            }
        }   break;
        case MPP_GUI_ROTATE_270:{
            if(x_start < scale * 8)
            {
                return SV_SUCCESS;
            }

            paint_len = (y_start) / (scale * 8);
            paint_len = min(str_len, paint_len);
            if(bBackGround)
            {
                MPP_GUI_POINT stPointRectL, stPointRectR;
                stPointRectL.x = stPointUp.x;
                stPointRectL.y = stPointUp.y - paint_len * scale * 8;
                stPointRectR.x = stPointUp.x + 1 * scale * 8;
                stPointRectR.y = stPointUp.y;

                stPointRectL.x -= scale;
                stPointRectL.y += scale;
                stPointRectR.x += scale >> 1;
                stPointRectR.y += scale;
                MPP_GUI_FILL_RECT(pstGuiImage, stPointRectL, stPointRectR, stColorBG);
            }
            for(i = 0; i < paint_len; i++) // 第i个字符
            {
                u8char = stString[i];
                pu8fontdata = mpp_pixel_fontdata[u8char];
                stPoint1.x = x_start;
                for(j = 0; j < 8; j++) // 第j行
                {
                    parse = pu8fontdata[j];
                    pu8fontparse = mpp_pixel_parse[parse];
                    stPoint1.y = y_start - i * 8 * scale;
                    for(k = 0; k < 8; k++, stPoint1.y -= scale) // 第k个模点
                    {
                        if(!pu8fontparse[k])
                            continue;
                        //stPoint1.x = x_start + j * scale;
                        //stPoint1.y = y_start - (i * 8 + k) * scale;

                        //stPoint2.x = stPoint1.x + scale;
                        //stPoint2.y = stPoint1.y + scale;
                        //MPP_GUI_FILL_RECT(pstGuiImage, stPoint1, stPoint2, stColor);

                        rect1.x = stPoint1.x;
                        rect1.y = stPoint1.y - scale;
                        rect2.x = stPoint1.x + scale;
                        rect2.y = stPoint1.y;

                        MPP_GUI_FILL_RECT(pstGuiImage, rect1, rect2, stColor);
                    }
                    stPoint1.x += scale;
                }
            }
        }   break;
    }
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 按比例绘制字符串
 * 输入参数: pstPaintImage  --- 图像信息
            stPointUp      --- 左上角
            stString        --- 字符串指针
            stColor        --- 颜色
            fontsize        --- 字体大小
            bBackGround     --- 使能背景
            stColorBG    --- 背景色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PAINT_STRING_S(MPP_GUI_IMAGE_S *pstGuiImage, MPP_GUI_POINT_S stPointUp,
    char* stString,  MPP_GUI_COLOR stColor, MPP_GUI_FONTSIZE fontsize,
    int bBackGround, MPP_GUI_COLOR stColorBG, MPP_GUI_ROTATE_ANGLE enAngle)
{
    MPP_GUI_POINT stPoint;
    stPointUp.x = min(1.0, max(0.0, stPointUp.x));
    stPointUp.y = min(1.0, max(0.0, stPointUp.y));
    stPoint.x = ALIGN2(pstGuiImage->Width * max(0, stPointUp.x));
    stPoint.y = ALIGN2(pstGuiImage->Height * max(0, stPointUp.y));
    return MPP_GUI_PAINT_STRING(pstGuiImage, stPoint, stString, stColor, fontsize, bBackGround, stColorBG, enAngle);
}


/******************************************************************************
 * 函数功能: 绘制3阶贝塞尔曲线
 * 输入参数: 图像类型的结构体
 *          pstGuiImage --- 图像信息
 *          pstPoint    --- 控制点, 3 阶贝塞尔曲线需要4个点
 *          stColor     --- 颜色
 *          stick       --- 曲线宽度
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
int MPP_GUI_PAINT_3BEZIER_CURVE_S(MPP_GUI_IMAGE_S *pstGuiImage,
    MPP_GUI_POINT_S *pstPoint, MPP_GUI_COLOR stColor, uint32 stick)
{
    const int tNum = 8;
    const float tstep = 1.0 / tNum;
    MPP_GUI_POINT_S stPoint[tNum+1];
    float t = 0, t2, t3, mt, mt2, mt3;
    int n, i;

    for(n = 0; n < tNum + 1; n++)
    {
        t2 = t * t;
        t3 = t2 * t;
        mt = 1 - t;
        mt2 = mt * mt;
        mt3 = mt2 * mt;

        stPoint[n].x = mt3 * pstPoint[0].x + 3 * mt2 * t * pstPoint[1].x + 3 * mt * t2 * pstPoint[2].x + t3 * pstPoint[3].x;
        stPoint[n].y = mt3 * pstPoint[0].y + 3 * mt2 * t * pstPoint[1].y + 3 * mt * t2 * pstPoint[2].y + t3 * pstPoint[3].y;

        t += tstep;
    }

    return MPP_GUI_PAINT_MUTIL_LINE_S(pstGuiImage, stPoint, tNum + 1, stColor, stick);
}

/******************************************************************************
 * 函数功能: 利用3阶贝塞尔函数进行多边形曲线拟合
 * 输入参数: 图像类型的结构体
 *          pstGuiImage --- 图像信息
 *          pstPoint    --- 多边形顶点
 *          lens        --- 多边形顶点数量
 *          scale       --- 拟合平滑度[0,1] 数值越大，生成的曲线越平滑
 *          stColor     --- 颜色
 *          stick       --- 曲线宽度
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
int MPP_GUI_PAINT_3BEZIER_CURVE_FIT_S(MPP_GUI_IMAGE_S *pstGuiImage,
    MPP_GUI_POINT_S *pstPoint, uint32 lens, float scale, MPP_GUI_COLOR stColor, uint32 stick)

{
    int i, j, nexti, backi;
    int extraindex;
    float offsetx, offsety, addx, addy;
    //float scale = 0.4;  // 缩放系数
    MPP_GUI_COLOR stTmpColor;
    MPP_GUI_POINT_S stmidnmid;
    MPP_GUI_POINT_S pstPointControl[4];
    MPP_GUI_POINT_S *pCenter = (MPP_GUI_POINT_S *)malloc(sizeof(MPP_GUI_POINT_S) * lens); // 中心点
    MPP_GUI_POINT_S *pExtra = (MPP_GUI_POINT_S *)malloc(sizeof(MPP_GUI_POINT_S) * lens * 2); // 平移中点
    for (i = 0; i < lens; i++)
    {
        nexti = (i + 1) % lens;
        pCenter[i].x = (pstPoint[i].x + pstPoint[nexti].x) / 2.0;
        pCenter[i].y = (pstPoint[i].y + pstPoint[nexti].y) / 2.0;
    }


    for (i = 0; i < lens; i++)
    {
        nexti = (i + 1) % lens;
        backi = (lens + i - 1) % lens;
        stmidnmid.x = (pCenter[i].x + pCenter[backi].x) / 2;
        stmidnmid.y = (pCenter[i].y + pCenter[backi].y) / 2;

        offsetx = pstPoint[i].x - stmidnmid.x;
        offsety = pstPoint[i].y - stmidnmid.y;

        extraindex = 2 * i;
        pExtra[extraindex].x = pCenter[backi].x + offsetx;
        pExtra[extraindex].y = pCenter[backi].y + offsety;
        // 通过 scale 进行收缩
        addx = (pExtra[extraindex].x - pstPoint[i].x) * scale;
        addy = (pExtra[extraindex].y - pstPoint[i].y) * scale;
        pExtra[extraindex].x = pstPoint[i].x + addx;
        pExtra[extraindex].y = pstPoint[i].y + addy;

        extraindex = (extraindex + 1) % (2 * lens);
        pExtra[extraindex].x = pCenter[i].x + offsetx;
        pExtra[extraindex].y = pCenter[i].y + offsety;
        addx = (pExtra[extraindex].x - pstPoint[i].x) * scale;
        addy = (pExtra[extraindex].y - pstPoint[i].y) * scale;
        pExtra[extraindex].x = pstPoint[i].x + addx;
        pExtra[extraindex].y = pstPoint[i].y + addy;

    }

    stTmpColor = MPP_GUI_GetColor(pstGuiImage->format, 0xffffff00);
    for (i = 0; i < lens; i++)
    {
        pstPointControl[0] = pstPoint[i];
        pstPointControl[1] = pExtra[2*i+1];
        pstPointControl[2] = pExtra[(2*i+2) % (2*lens)];
        pstPointControl[3] = pstPoint[(i+1)%lens];


        MPP_GUI_PAINT_3BEZIER_CURVE_S(pstGuiImage, pstPointControl, stColor, stick);
    }


    free(pCenter);
    free(pExtra);

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 利用3阶贝塞尔函数进行多边形曲线拟合,获取拟合点,但不进行绘图
 * 输入参数: 图像类型的结构体
 *          pstPoint    --- 多边形顶点
 *          lens        --- 多边形顶点数量
 *          scale       --- 拟合平滑度[0,1] 数值越大，生成的曲线越平滑
 * 输出参数:
            pstOutPoint --- 输出的拟合点
            pOutNum       - 输出的拟合点数量
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
int MPP_GUI_3BEZIER_CURVE_FIT_GET_S(MPP_GUI_POINT_S *pstPoint, uint32 lens,
    float scale, MPP_GUI_POINT_S *pstOutPoint, int *pOutNum)
{
    int i, j, nexti, backi;
    int extraindex;
    float offsetx, offsety, addx, addy;
    //float scale = 0.4;  // 缩放系数
    MPP_GUI_COLOR stTmpColor;
    MPP_GUI_POINT_S stmidnmid;
    MPP_GUI_POINT_S stCtl[4];
    MPP_GUI_POINT_S *pCenter = (MPP_GUI_POINT_S *)malloc(sizeof(MPP_GUI_POINT_S) * lens); // 中心点
    MPP_GUI_POINT_S *pExtra = (MPP_GUI_POINT_S *)malloc(sizeof(MPP_GUI_POINT_S) * lens * 2); // 平移中点
    for (i = 0; i < lens; i++)
    {
        nexti = (i + 1) % lens;
        pCenter[i].x = (pstPoint[i].x + pstPoint[nexti].x) / 2.0;
        pCenter[i].y = (pstPoint[i].y + pstPoint[nexti].y) / 2.0;
    }


    for (i = 0; i < lens; i++)
    {
        nexti = (i + 1) % lens;
        backi = (lens + i - 1) % lens;
        stmidnmid.x = (pCenter[i].x + pCenter[backi].x) / 2;
        stmidnmid.y = (pCenter[i].y + pCenter[backi].y) / 2;

        offsetx = pstPoint[i].x - stmidnmid.x;
        offsety = pstPoint[i].y - stmidnmid.y;

        extraindex = 2 * i;
        pExtra[extraindex].x = pCenter[backi].x + offsetx;
        pExtra[extraindex].y = pCenter[backi].y + offsety;
        // 通过 scale 进行收缩
        addx = (pExtra[extraindex].x - pstPoint[i].x) * scale;
        addy = (pExtra[extraindex].y - pstPoint[i].y) * scale;
        pExtra[extraindex].x = pstPoint[i].x + addx;
        pExtra[extraindex].y = pstPoint[i].y + addy;

        extraindex = (extraindex + 1) % (2 * lens);
        pExtra[extraindex].x = pCenter[i].x + offsetx;
        pExtra[extraindex].y = pCenter[i].y + offsety;
        addx = (pExtra[extraindex].x - pstPoint[i].x) * scale;
        addy = (pExtra[extraindex].y - pstPoint[i].y) * scale;
        pExtra[extraindex].x = pstPoint[i].x + addx;
        pExtra[extraindex].y = pstPoint[i].y + addy;

    }


    sint32 totalNum = 0;
    sint32 tNum = 10;       // 10个点拟合贝塞尔曲线
    const float tstep = 1.0 / tNum;
    float t = 0, t2, t3, mt, mt2, mt3;
    for(i = 0; i < lens; i++)
    {
        stCtl[0] = pstPoint[i];
        stCtl[1] = pExtra[2*i+1];
        stCtl[2] = pExtra[(2*i+2) % (2*lens)];
        stCtl[3] = pstPoint[(i+1)%lens];

        t = 0;
        for(j = 0; j < tNum; j++)
        {
            t2 = t * t;
            t3 = t2 * t;
            mt = 1 - t;
            mt2 = mt * mt;
            mt3 = mt2 * mt;

            pstOutPoint[totalNum].x = mt3 * stCtl[0].x + 3 * mt2 * t * stCtl[1].x + 3 * mt * t2 * stCtl[2].x + t3 * stCtl[3].x;
            pstOutPoint[totalNum].y = mt3 * stCtl[0].y + 3 * mt2 * t * stCtl[1].y + 3 * mt * t2 * stCtl[2].y + t3 * stCtl[3].y;

            t += tstep;
            totalNum++;
        }
    }



    *pOutNum = totalNum;
    free(pCenter);
    free(pExtra);
    return SV_SUCCESS;
}




typedef enum tagpastemode
{
    PASTE_NOSKIP_NOALPHA = 0,   /* 直接粘贴 */
    PASTE_SKIP_NOALPHA,         /* 跳过空白区域，适用于logo叠加，跳过大于rgb都大于245的白色区域，表现为镂空 */
    PASTE_NOSKIP_ALPHA,         /* 透明度叠加，适用于时间叠加等 */
    PASTE_SKIP_ALPHA,           /* 跳过空白区域,透明度叠加 */

    PASTE_BUTT
} PASTE_MODE;


/******************************************************************************
 * 函数功能: 粘贴图像(使用半透明(1/4透明度),去除黑色背景)
 * 输入参数: pstDestImage --- 目的图像
             pstSrcImage  --- 源图像
            stPoint       --- 粘贴位置左上角
            bSkip         --- 是否跳过镂空的位置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PASTE_IMAGE2_S(MPP_GUI_IMAGE_S *pstDestImage, MPP_GUI_IMAGE_S *pstSrcImage,
    MPP_GUI_POINT_S stPoint)
{

    uint8  *u8Filed, *u8Filed_Src;
    uint16 *u16Filed, *u16Filed_Src;
    uint24 *u24Filed, *u24Filed_Src;
    uint32 *u32Filed, *u32Filed_Src;
    uint8 y_value;
    uint16 uv_value;
    uint8 r, g, b;
    int i,j;
    int width_save, height_save;
    int x_start, y_start, x_len, y_len, x_len_half, y_len_half, width_src_half, width_dst_half;
    PASTE_MODE enMode;

    if(pstDestImage == NULL || pstSrcImage == NULL || pstDestImage->pbmp == NULL || pstSrcImage->pbmp == NULL)
    {
        print_level(SV_ERROR, "Null ptr!\n");
        return ERR_NULL_PTR;
    }

    x_start = max(0, min(stPoint.x*pstDestImage->Width, pstDestImage->Width));
    y_start = max(0, min(stPoint.y*pstDestImage->Height, pstDestImage->Height));
    x_len = max(0, min(stPoint.x*pstDestImage->Width + pstSrcImage->Width, pstDestImage->Width) - x_start);
    y_len = max(0, min(stPoint.y*pstDestImage->Height + pstSrcImage->Height, pstDestImage->Height) - y_start);

    if(1)//if (pstDestImage->format == MPP_GUI_FMT_YUV_420SP)
    {
        x_start = ALIGN2(x_start);
        y_start = ALIGN2(y_start);
    }

    if(x_len == 0 || y_len == 0)
    {
        printf("there is not image need to paste!\n");
        return SV_FAILURE;
    }

    if(pstDestImage->format != pstSrcImage->format)
    {
        printf("pstDestImage->format[%d] != pstSrcImage->format[%d]\n", pstDestImage->format, pstSrcImage->format);
        return SV_FAILURE;
    }

    switch(pstDestImage->format){
        case MPP_GUI_FMT_ARGB_1555:
            u16Filed = pstDestImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp;
            u16Filed += (x_start + y_start * pstDestImage->Width);
            //u16Filed_Src += (x_start + y_start * pstSrcImage->Width);
            for(i = 0; i < y_len; i++)
            {
                for(i = 0; i < y_len; i++)
                {
                    memcpy(u16Filed, u16Filed_Src, x_len * 2);
                    u16Filed += pstDestImage->Width;
                    u16Filed_Src += pstSrcImage->Width;
                }
                u16Filed += pstDestImage->Width;
                u16Filed_Src += pstSrcImage->Width;
            }
            break;
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
            u16Filed = pstDestImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp;
            u16Filed += (x_start + y_start * pstDestImage->Width);
            for(i = 0; i < y_len; i++)
            {
                if (i % 3 != 0)     // 每3列进行一次赋值
                {
					goto exit;
                }

                for(j = 0; j < x_len; j++)
                {
                    if (j % 2 != 0)     // 每2行就进行一次赋值，i和j的比例是3:2，接近于1280:720，这样是最省CPU的
                    {
                        continue;
                    }

                    r = (u16Filed_Src[j] >> 8) & 0xff;
                    g = (u16Filed_Src[j] >> 3) & 0xff;
                    b = (u16Filed_Src[j] << 3) & 0xff;
                    if((r >= MPP_GUI_SKIP_VALUE &&
                       g >= MPP_GUI_SKIP_VALUE &&
                       b >= MPP_GUI_SKIP_VALUE) || \
                       (r <= MPP_GUI_SKIP_VALUE_LOW &&
                        g <= MPP_GUI_SKIP_VALUE_LOW &&
                        b <= MPP_GUI_SKIP_VALUE_LOW)
                       )
                    {
                       continue;
                    }

                    /* 整体右移后,去除最高位,防止数据被侵蚀 */
                    //u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111101111101111) + ((u16Filed[j] >> 2) & 0b0011100111100111) + ((u16Filed_Src[j] >> 2) & 0b0011100111100111);
                    u16Filed[j] = u16Filed_Src[j];
                }
exit:
                u16Filed += pstDestImage->Width;
                u16Filed_Src += pstSrcImage->Width;
            }

            break;
        case MPP_GUI_FMT_RGB_888:
            u24Filed = pstDestImage->pbmp;
            u24Filed_Src = pstSrcImage->pbmp;
            u24Filed += (x_start + y_start * pstDestImage->Width);
            //u24Filed_Src += (x_start + y_start * pstSrcImage->Width);
            for(i = 0; i < y_len; i++)
            {
                memcpy(u24Filed, u24Filed_Src, x_len * 3);
                u24Filed += pstDestImage->Width;
                u24Filed_Src += pstSrcImage->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_8888:
            u32Filed = pstDestImage->pbmp;
            u32Filed_Src = pstSrcImage->pbmp;
            u32Filed += (x_start + y_start * pstDestImage->Width);
            //u32Filed_Src += (x_start + y_start * pstSrcImage->Width);
            for(i = 0; i < y_len; i++)
            {
                memcpy(u32Filed, u32Filed_Src, x_len * 3);
                u32Filed += pstDestImage->Width;
                u32Filed_Src += pstSrcImage->Width;
            }
            break;
        case MPP_GUI_FMT_YUYV:
            u16Filed = pstDestImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp;
            u16Filed += ((x_start) + y_start * (pstDestImage->Width));
            for(i = 0; i < y_len; i++)
            {
                for(j = 0; j < x_len; j++)
                {
                    if(u16Filed_Src[j] != 0x0000)
                    {
                        u16Filed[j] = u16Filed_Src[j];
                    }
                }
                u16Filed += (pstDestImage->Width);
                u16Filed_Src += (pstSrcImage->Width);
            }
            break;
        case MPP_GUI_FMT_UYVY:
            u16Filed = pstDestImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp;
            u16Filed += ((x_start) + y_start * (pstDestImage->Width));
            for(i = 0; i < y_len; i++)
            {
                for(j = 0; j < x_len; j++)
                {
                    if(u16Filed_Src[j] != 0x0000)
                    {
                        u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111111101111111) + ((u16Filed[j] >> 2) & 0b0011111100111111) + ((u16Filed_Src[j] >> 2) & 0b0011111100111111);
                    }
                }
                u16Filed += (pstDestImage->Width);
                u16Filed_Src += (pstSrcImage->Width);
            }
            break;
        case MPP_GUI_FMT_YUV_420SP:
            x_len_half = x_len >> 1;
            y_len_half = y_len >> 1;
            u8Filed = pstDestImage->pbmp;
            u16Filed = pstDestImage->pbmp + pstDestImage->Width * pstDestImage->Height;
            u8Filed_Src = pstSrcImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp + pstSrcImage->Width * pstSrcImage->Height;
            u8Filed  += (x_start + y_start * pstDestImage->Width);
            u16Filed += ((x_start/2 + (y_start>>1) * pstDestImage->Width/2));

            for(i= 0; i < y_len; i++)
            {
                uint8 *buf1, *buf2;
                buf1 = u8Filed;
                buf2 = u8Filed_Src;
                for(j=0; j<x_len; j++)
                {
                    if(*buf2)
                    {
                        *buf1 = (*buf1>>1) + (*buf1>>2) + (*buf2 >> 2);
                    }
                    buf1++;
                    buf2++;
                }
                //memcpy(u8Filed, u8Filed_Src, x_len);
                u8Filed += pstDestImage->Width;
                u8Filed_Src += pstSrcImage->Width;
            }
            width_dst_half = pstDestImage->Width/2;
            width_src_half = pstSrcImage->Width/2;
            for(i=0; i < y_len_half; i++)
            {
                uint8 *u8Ubuf1, *u8Ubuf2;
                uint8 *u8Vbuf1, *u8Vbuf2;
                u8Ubuf1 = u16Filed;
                u8Ubuf2 = u16Filed_Src;
                u8Vbuf1 = u8Ubuf1+1;
                u8Vbuf2 = u8Ubuf2+1;
                for(j=0; j<x_len_half; j++)
                {
                    if(*u8Ubuf2 || *u8Vbuf2)
                    {
                        *u8Ubuf1 = (*u8Ubuf1>>1) + (*u8Ubuf1>>2) +(*u8Ubuf2>>2);
                        *u8Vbuf1 = (*u8Vbuf1>>1) + (*u8Ubuf1>>2) +(*u8Vbuf2>>2);
                    }
                    u8Ubuf1+=2;
                    u8Ubuf2+=2;
                    u8Vbuf1+=2;
                    u8Vbuf2+=2;
                }
                //memcpy(u16Filed, u16Filed_Src, x_len);
                u16Filed += width_dst_half;
                u16Filed_Src += width_src_half;
            }
            break;

        default:
            printf("Cannot find GUI FMT:%d\n", pstSrcImage->format);
            return SV_FAILURE;
            break;
    }
    return SV_SUCCESS;
}



/******************************************************************************
 * 函数功能: 粘贴图像
 * 输入参数: pstDestImage --- 目的图像
             pstSrcImage  --- 源图像
            stPoint       --- 粘贴位置左上角
            bSkip         --- 是否跳过镂空的位置
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_PASTE_IMAGE_S(MPP_GUI_IMAGE_S *pstDestImage, MPP_GUI_IMAGE_S *pstSrcImage,
    MPP_GUI_POINT_S stPoint, SV_BOOL bSkip, SV_BOOL bAlpha)
{

    uint8  *u8Filed, *u8Filed_Src;
    uint16 *u16Filed, *u16Filed_Src;
    uint24 *u24Filed, *u24Filed_Src;
    uint32 *u32Filed, *u32Filed_Src;
    uint8 y_value;
    uint16 uv_value;
    uint8 r, g, b;
    int i,j;
    int width_save, height_save;
    int x_start, y_start, x_len, y_len, x_len_half, y_len_half, width_src_half, width_dst_half;
    PASTE_MODE enMode;

    if(pstDestImage == NULL || pstSrcImage == NULL || pstDestImage->pbmp == NULL || pstSrcImage->pbmp == NULL)
    {
        print_level(SV_ERROR, "Null ptr!\n");
        return ERR_NULL_PTR;
    }

    x_start = max(0, min(stPoint.x*pstDestImage->Width, pstDestImage->Width));
    y_start = max(0, min(stPoint.y*pstDestImage->Height, pstDestImage->Height));
    x_len = max(0, min(stPoint.x*pstDestImage->Width + pstSrcImage->Width, pstDestImage->Width) - x_start);
    y_len = max(0, min(stPoint.y*pstDestImage->Height + pstSrcImage->Height, pstDestImage->Height) - y_start);

    #if 0
    if (!bSkip && bAlpha)
    {
        printf("stPoint.x: %f, y: %f, dst w: %d, h: %d\n", stPoint.x, stPoint.y, pstDestImage->Width, pstDestImage->Height);
        printf("x_start: %d, y_start: %d, x_len: %d, y_len: %d, w: %d, h: %d\n", x_start, y_start, x_len, y_len, pstSrcImage->Width, pstSrcImage->Height);
    }
    #endif

    if(1)//if (pstDestImage->format == MPP_GUI_FMT_YUV_420SP)
    {
        x_start = ALIGN2(x_start);
        y_start = ALIGN2(y_start);
    }

    if(x_len == 0 || y_len == 0)
    {
        printf("there is not image need to paste!\n");
        return SV_FAILURE;
    }

    if(pstDestImage->format != pstSrcImage->format)
    {
        printf("pstDestImage->format[%d] != pstSrcImage->format[%d]\n", pstDestImage->format, pstSrcImage->format);
        return SV_FAILURE;
    }

    enMode = PASTE_NOSKIP_NOALPHA;
    if(bSkip)
    {
        if(bAlpha)
            enMode = PASTE_SKIP_ALPHA;
        else
            enMode = PASTE_SKIP_NOALPHA;
    }
    else if(bAlpha)
    {
        enMode = PASTE_NOSKIP_ALPHA;
    }

    switch(pstDestImage->format){
        case MPP_GUI_FMT_RGB_8BPP:
            u8Filed = pstDestImage->pbmp;
            u8Filed_Src = pstSrcImage->pbmp;
            u8Filed += (x_start + y_start * pstDestImage->Width);
            //u8Filed_Src += (x_start + y_start * pstSrcImage->Width);
            for(i = 0; i < y_len; i++)
            {
                memcpy(u8Filed, u8Filed_Src, x_len);
                u8Filed += pstDestImage->Width;
                u8Filed_Src += pstSrcImage->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_1555:
            u16Filed = pstDestImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp;
            u16Filed += (x_start + y_start * pstDestImage->Width);
            //u16Filed_Src += (x_start + y_start * pstSrcImage->Width);
            for(i = 0; i < y_len; i++)
            {
                for(i = 0; i < y_len; i++)
                {
                    memcpy(u16Filed, u16Filed_Src, x_len * 2);
                    u16Filed += pstDestImage->Width;
                    u16Filed_Src += pstSrcImage->Width;
                }
                u16Filed += pstDestImage->Width;
                u16Filed_Src += pstSrcImage->Width;
            }
            break;
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
            u16Filed = pstDestImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp;
            u16Filed += (x_start + y_start * pstDestImage->Width);
            switch(enMode)
            {
                case PASTE_NOSKIP_NOALPHA:
                    for(i = 0; i < y_len; i++)
                    {
                        memcpy(u16Filed, u16Filed_Src, x_len*2);
                        u16Filed += pstDestImage->Width;
                        u16Filed_Src += pstSrcImage->Width;
                    }
                    break;
                case PASTE_SKIP_NOALPHA:
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            r = (u16Filed_Src[j] & 0xf800) >> 8;
                            g = (u16Filed_Src[j] & 0x07e0) >> 3;
                            b = (u16Filed_Src[j] & 0x001f) << 3;
                            if(r >= MPP_GUI_SKIP_VALUE &&
                               g >= MPP_GUI_SKIP_VALUE &&
                               b >= MPP_GUI_SKIP_VALUE)
                               continue;
                            u16Filed[j] = u16Filed_Src[j];
                        }
                        u16Filed += pstDestImage->Width;
                        u16Filed_Src += pstSrcImage->Width;
                    }
                    break;
                case PASTE_NOSKIP_ALPHA:
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            /* 整体右移后,去除最高位,防止数据被侵蚀 */
                            u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111101111101111) + ((u16Filed_Src[j] >> 1) & 0b0111101111101111);
                        }
                        u16Filed += pstDestImage->Width;
                        u16Filed_Src += pstSrcImage->Width;
                    }
                    break;
                case PASTE_SKIP_ALPHA:
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            r = (u16Filed_Src[j] & 0xf800) >> 8;
                            g = (u16Filed_Src[j] & 0x07e0) >> 3;
                            b = (u16Filed_Src[j] & 0x001f) << 3;
                            if(r >= MPP_GUI_SKIP_VALUE &&
                               g >= MPP_GUI_SKIP_VALUE &&
                               b >= MPP_GUI_SKIP_VALUE)
                               continue;

                            /* 整体右移后,去除最高位,防止数据被侵蚀 */
                            u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111101111101111) + ((u16Filed_Src[j] >> 1) & 0b0111101111101111);
                        }
                        u16Filed += pstDestImage->Width;
                        u16Filed_Src += pstSrcImage->Width;
                    }
                    break;
            }
            break;
        case MPP_GUI_FMT_RGB_888:
            u24Filed = pstDestImage->pbmp;
            u24Filed_Src = pstSrcImage->pbmp;
            u24Filed += (x_start + y_start * pstDestImage->Width);
            //u24Filed_Src += (x_start + y_start * pstSrcImage->Width);
            for(i = 0; i < y_len; i++)
            {
                memcpy(u24Filed, u24Filed_Src, x_len * 3);
                u24Filed += pstDestImage->Width;
                u24Filed_Src += pstSrcImage->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_8888:
            u32Filed = pstDestImage->pbmp;
            u32Filed_Src = pstSrcImage->pbmp;
            u32Filed += (x_start + y_start * pstDestImage->Width);
            //u32Filed_Src += (x_start + y_start * pstSrcImage->Width);
            for(i = 0; i < y_len; i++)
            {
                memcpy(u32Filed, u32Filed_Src, x_len * 3);
                u32Filed += pstDestImage->Width;
                u32Filed_Src += pstSrcImage->Width;
            }
            break;
        case MPP_GUI_FMT_YUYV:
            switch(enMode)
            {
                case PASTE_NOSKIP_NOALPHA: /* 直接粘贴 */
                    u32Filed = pstDestImage->pbmp;
                    u32Filed_Src = pstSrcImage->pbmp;
                    u32Filed += ((x_start >> 1) + y_start * (pstDestImage->Width >> 1));
                    for(i = 0; i < y_len; i++)
                    {
                        memcpy(u32Filed, u32Filed_Src, x_len * 2);
                        u32Filed += (pstDestImage->Width >> 1);
                        u32Filed_Src += (pstSrcImage->Width >> 1);
                    }
                    break;
                case PASTE_SKIP_NOALPHA: /* 跳过数值为0x0000的粘贴数据 */
                    u16Filed = pstDestImage->pbmp;
                    u16Filed_Src = pstSrcImage->pbmp;
                    u16Filed += ((x_start) + y_start * (pstDestImage->Width));
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            if(u16Filed_Src[j] == 0x0000)
                            {
                                j++;
                                continue;
                            }
                            u16Filed[j] = u16Filed_Src[j];
                        }
                        u16Filed += (pstDestImage->Width);
                        u16Filed_Src += (pstSrcImage->Width);
                    }
                    break;
                case PASTE_NOSKIP_ALPHA: /* 半透明粘贴 */
                    u16Filed = pstDestImage->pbmp;
                    u16Filed_Src = pstSrcImage->pbmp;
                    u16Filed += ((x_start) + y_start * (pstDestImage->Width));
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            /* 整体右移后,去除最高位,防止数据被侵蚀 */
                            u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111111101111111) + ((u16Filed_Src[j] >> 1) & 0b0111111101111111);
                        }
                        u16Filed += (pstDestImage->Width);
                        u16Filed_Src += (pstSrcImage->Width);
                    }
                    break;
                case PASTE_SKIP_ALPHA: /* 跳过数值为0x0000的粘贴数据，并进行半透明粘贴 */
                    u16Filed = pstDestImage->pbmp;
                    u16Filed_Src = pstSrcImage->pbmp;
                    u16Filed += ((x_start) + y_start * (pstDestImage->Width));
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            if(u16Filed_Src[j] == 0x0000)
                            {
                                j++;
                                continue;
                            }
                            /* 整体右移后,去除最高位,防止数据被侵蚀 */
                            u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111111101111111) + ((u16Filed_Src[j] >> 1) & 0b0111111101111111);
                        }
                        u16Filed += (pstDestImage->Width);
                        u16Filed_Src += (pstSrcImage->Width);
                    }
                    break;
            }
            break;
        case MPP_GUI_FMT_UYVY:
            switch(enMode)
            {
                case PASTE_NOSKIP_NOALPHA: /* 直接粘贴 */
                    u32Filed = pstDestImage->pbmp;
                    u32Filed_Src = pstSrcImage->pbmp;
                    u32Filed += ((x_start >> 1) + y_start * (pstDestImage->Width >> 1));
                    for(i = 0; i < y_len; i++)
                    {
                        memcpy(u32Filed, u32Filed_Src, x_len * 2);
                        u32Filed += (pstDestImage->Width >> 1);
                        u32Filed_Src += (pstSrcImage->Width >> 1);
                    }
                    break;
                case PASTE_SKIP_NOALPHA: /* 跳过数值为0x0000的粘贴数据 */
                    u16Filed = pstDestImage->pbmp;
                    u16Filed_Src = pstSrcImage->pbmp;
                    u16Filed += ((x_start) + y_start * (pstDestImage->Width));
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            if(u16Filed_Src[j] == 0x0000)
                            {
                                j++;
                                continue;
                            }
                            u16Filed[j] = u16Filed_Src[j];
                        }
                        u16Filed += (pstDestImage->Width);
                        u16Filed_Src += (pstSrcImage->Width);
                    }
                    break;
                case PASTE_NOSKIP_ALPHA: /* 半透明粘贴 */
                    u16Filed = pstDestImage->pbmp;
                    u16Filed_Src = pstSrcImage->pbmp;
                    u16Filed += ((x_start) + y_start * (pstDestImage->Width));
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            /* 整体右移后,去除最高位,防止数据被侵蚀 */
                            u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111111101111111) + ((u16Filed_Src[j] >> 1) & 0b0111111101111111);
                        }
                        u16Filed += (pstDestImage->Width);
                        u16Filed_Src += (pstSrcImage->Width);
                    }
                    break;
                case PASTE_SKIP_ALPHA: /* 跳过数值为0x0000的粘贴数据，并进行半透明粘贴 */
                    u16Filed = pstDestImage->pbmp;
                    u16Filed_Src = pstSrcImage->pbmp;
                    u16Filed += ((x_start) + y_start * (pstDestImage->Width));
                    for(i = 0; i < y_len; i++)
                    {
                        for(j = 0; j < x_len; j++)
                        {
                            if(u16Filed_Src[j] == 0x0000)
                            {
                                j++;
                                continue;
                            }
                            /* 整体右移后,去除最高位,防止数据被侵蚀 */
                            u16Filed[j] = ((u16Filed[j] >> 1) & 0b0111111101111111) + ((u16Filed_Src[j] >> 1) & 0b0111111101111111);
                        }
                        u16Filed += (pstDestImage->Width);
                        u16Filed_Src += (pstSrcImage->Width);
                    }
                    break;
            }
            break;
        case MPP_GUI_FMT_YUV_420SP:
            x_len_half = x_len >> 1;
            y_len_half = y_len >> 1;
            u8Filed = pstDestImage->pbmp;
            u16Filed = pstDestImage->pbmp + pstDestImage->Width * pstDestImage->Height;
            u8Filed_Src = pstSrcImage->pbmp;
            u16Filed_Src = pstSrcImage->pbmp + pstSrcImage->Width * pstSrcImage->Height;
            u8Filed  += (x_start + y_start * pstDestImage->Width);
            u16Filed += ((x_start/2 + (y_start>>1) * pstDestImage->Width/2));

            if(bSkip && bAlpha)
            {
                for(i= 0; i < y_len; i++)
                {
                    uint8 *buf1, *buf2;
                    buf1 = u8Filed;
                    buf2 = u8Filed_Src;
                    for(j=0; j<x_len; j++)
                    {
                        if(*buf2)
                        {
                            *buf1 = (*buf1>>1) + (*buf2 >> 1);
                        }
                        buf1++;
                        buf2++;
                    }
                    //memcpy(u8Filed, u8Filed_Src, x_len);
                    u8Filed += pstDestImage->Width;
                    u8Filed_Src += pstSrcImage->Width;
                }
                width_dst_half = pstDestImage->Width/2;
                width_src_half = pstSrcImage->Width/2;
                for(i=0; i < y_len_half; i++)
                {
                    uint8 *u8Ubuf1, *u8Ubuf2;
                    uint8 *u8Vbuf1, *u8Vbuf2;
                    u8Ubuf1 = u16Filed;
                    u8Ubuf2 = u16Filed_Src;
                    u8Vbuf1 = u8Ubuf1+1;
                    u8Vbuf2 = u8Ubuf2+1;
                    for(j=0; j<x_len_half; j++)
                    {
                        if(*u8Ubuf2 || *u8Vbuf2)
                        {
                            *u8Ubuf1 = (*u8Ubuf1>>1) + (*u8Ubuf2>>1);
                            *u8Vbuf1 = (*u8Vbuf1>>1) + (*u8Vbuf2>>1);
                        }
                        u8Ubuf1+=2;
                        u8Ubuf2+=2;
                        u8Vbuf1+=2;
                        u8Vbuf2+=2;
                    }
                    //memcpy(u16Filed, u16Filed_Src, x_len);
                    u16Filed += width_dst_half;
                    u16Filed_Src += width_src_half;
                }
            }
            else if(!bSkip && bAlpha)
            {
                for(i= 0; i < y_len; i++)
                {
                    uint8 *buf1, *buf2;
                    buf1 = u8Filed;
                    buf2 = u8Filed_Src;
                    for(j=0; j<x_len; j++)
                    {
                        *buf1 = (*buf1>>1) + (*buf2 >> 1);
                        buf1++;
                        buf2++;
                    }
                    //memcpy(u8Filed, u8Filed_Src, x_len);
                    u8Filed += pstDestImage->Width;
                    u8Filed_Src += pstSrcImage->Width;
                }

                width_dst_half = pstDestImage->Width/2;
                width_src_half = pstSrcImage->Width/2;
                for(i=0; i < y_len_half; i++)
                {
                    uint8 *u8Ubuf1, *u8Ubuf2;
                    uint8 *u8Vbuf1, *u8Vbuf2;
                    u8Ubuf1 = u16Filed;
                    u8Ubuf2 = u16Filed_Src;
                    u8Vbuf1 = u8Ubuf1+1;
                    u8Vbuf2 = u8Ubuf2+1;
                    for(j=0; j<x_len_half; j++)
                    {
                        *u8Ubuf1 = (*u8Ubuf1>>1) + (*u8Ubuf2>>1);
                        *u8Vbuf1 = (*u8Vbuf1>>1) + (*u8Vbuf2>>1);
                        u8Ubuf1+=2;
                        u8Ubuf2+=2;
                        u8Vbuf1+=2;
                        u8Vbuf2+=2;
                    }
                    //memcpy(u16Filed, u16Filed_Src, x_len);
                    u16Filed += width_dst_half;
                    u16Filed_Src += width_src_half;
                }
            }
            else if(bSkip && !bAlpha)
            {
                for(i= 0; i < y_len; i++)
                {
                    uint8 *buf1, *buf2;
                    buf1 = u8Filed;
                    buf2 = u8Filed_Src;
                    for(j=0; j<x_len; j++)
                    {
                        if(*buf2)
                        {
                            *buf1 = *buf2;
                        }
                        buf1++;
                        buf2++;
                    }
                    //memcpy(u8Filed, u8Filed_Src, x_len);
                    u8Filed += pstDestImage->Width;
                    u8Filed_Src += pstSrcImage->Width;
                }
                width_dst_half = pstDestImage->Width/2;
                width_src_half = pstSrcImage->Width/2;
                for(i=0; i < y_len_half; i++)
                {
                    uint8 *u8Ubuf1, *u8Ubuf2;
                    uint8 *u8Vbuf1, *u8Vbuf2;
                    u8Ubuf1 = u16Filed;
                    u8Ubuf2 = u16Filed_Src;
                    u8Vbuf1 = u8Ubuf1+1;
                    u8Vbuf2 = u8Ubuf2+1;
                    for(j=0; j<x_len_half; j++)
                    {
                        if(*u8Ubuf2 || *u8Vbuf2)
                        {
                            *u8Ubuf1 = *u8Ubuf2;
                            *u8Vbuf1 = *u8Vbuf2;
                        }
                        u8Ubuf1+=2;
                        u8Ubuf2+=2;
                        u8Vbuf1+=2;
                        u8Vbuf2+=2;
                    }
                    //memcpy(u16Filed, u16Filed_Src, x_len);
                    u16Filed += width_dst_half;
                    u16Filed_Src += width_src_half;
                }
            }
            else if(!bSkip && !bAlpha)
            {
                for(i= 0; i < y_len; i++)
                {
                    uint8 *buf1, *buf2;
                    buf1 = u8Filed;
                    buf2 = u8Filed_Src;
                    memcpy(u8Filed, u8Filed_Src, x_len);
                    u8Filed += pstDestImage->Width;
                    u8Filed_Src += pstSrcImage->Width;
                }

                width_dst_half = pstDestImage->Width/2;
                width_src_half = pstSrcImage->Width/2;
                for(i=0; i < y_len_half; i++)
                {
                    uint8 *u8Ubuf1, *u8Ubuf2;
                    uint8 *u8Vbuf1, *u8Vbuf2;
                    u8Ubuf1 = u16Filed;
                    u8Ubuf2 = u16Filed_Src;
                    u8Vbuf1 = u8Ubuf1+1;
                    u8Vbuf2 = u8Ubuf2+1;
                    memcpy(u16Filed, u16Filed_Src, x_len);
                    u16Filed += width_dst_half;
                    u16Filed_Src += width_src_half;
                }
            }
            break;

        default:
            printf("Cannot find GUI FMT:%d\n", pstSrcImage->format);
            return SV_FAILURE;
            break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 读取BMP图片
 * 输入参数: pstPaintImage  --- 图像信息
            filename       --- 文件路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_LOAD_BMP(MPP_GUI_IMAGE_S *pstGuiImage, const char *filename)
{
    FILE* pFile;
    uint32 i, j, w_src, h_src, w_dst, h_dst, byte;
    uint8 a, r, g, b, format;
    uint8 y, u, v;
    uint8 *pOrigBMPBuf, *pRGBBuf, *pYBuf, *pUVBuf, *pYUYVBuf, *pUYVYBuf, *pStart;
    uint16 *pDst;
    uint32 stride, u32readn;
    uint16 u16color;


    BITMAP_HEADER szBmpHeader;
    if (NULL == filename)
    {
        printf("MPP_GUI_LOAD_BMP: filename=NULL\n");
        return SV_FAILURE;
    }

    if ((pFile = fopen((char*)filename, "rb")) == NULL)
    {
        printf("Open file faild:%s!\n", filename);
        return SV_FAILURE;
    }


    (void)fread(&szBmpHeader, 1, sizeof(BITMAP_HEADER), pFile);
    if (szBmpHeader.bFileHeader.bfType != 0x4d42)
    {
        printf("not bitmap file\n");
        fclose(pFile);
        return SV_FAILURE;
    }

    byte = szBmpHeader.bInfoHeader.biBitCount / 8;
    if (byte < 2 || byte > 4)
    {
        /* only support 1555 8888 888 bitmap */
        printf("bitmap bitcount:%d not supported!\n", szBmpHeader.bInfoHeader.biBitCount);
        fclose(pFile);
        return SV_FAILURE;
    }

    if (szBmpHeader.bInfoHeader.biCompression != 0)
    {
        printf("not support compressed bitmap file!\n");
        fclose(pFile);
        return SV_FAILURE;
    }

    if (szBmpHeader.bInfoHeader.biHeight < 0)
    {
        printf("bmpInfo.bInfoHeader.biHeight < 0\n");
        fclose(pFile);
        return SV_FAILURE;
    }

    switch (pstGuiImage->format){
        case MPP_GUI_FMT_ARGB_1555:
		case MPP_GUI_FMT_RGB_565:
		case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_RGB_888:
        case MPP_GUI_FMT_ARGB_8888:
        case MPP_GUI_FMT_YUV_420SP:
        case MPP_GUI_FMT_YUYV:
        case MPP_GUI_FMT_UYVY:
            format = pstGuiImage->format;
            break;
        default:
            printf("GuiImage format not supported!\n");
            return SV_FAILURE;
    }

    w_src = szBmpHeader.bInfoHeader.biWidth;
    h_src = szBmpHeader.bInfoHeader.biHeight;
    stride = w_src * byte;
    //yuv420sp必须长宽都为偶数，向下取偶可以方便程序编写
    w_dst = pstGuiImage->format == MPP_GUI_FMT_YUV_420SP ? (w_src & 0xfffe) : w_src;
    h_dst = pstGuiImage->format == MPP_GUI_FMT_YUV_420SP ? (h_src & 0xfffe) : h_src;

    pstGuiImage->Width = w_dst;
    pstGuiImage->Height = h_dst;



#if 1
    if (stride % 4)
    {
        stride = (stride & 0xfffc) + 4;
    }
#elif 0
    if (w % 2)
    {
        stride = ((w & 0xfffe) + 2) * byte;
    }
#endif

    if (pstGuiImage->pbmp == NULL)
    {
        switch (format){
            case MPP_GUI_FMT_ARGB_1555:
            case MPP_GUI_FMT_RGB_565:
            case MPP_GUI_FMT_BGR_565:
            case MPP_GUI_FMT_YUYV:

            case MPP_GUI_FMT_UYVY:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 2);
                break;
            case MPP_GUI_FMT_RGB_888:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 3);
                break;
            case MPP_GUI_FMT_ARGB_8888:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 4);
                break;
            case MPP_GUI_FMT_YUV_420SP:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 3 / 2 );
                break;
        }
    }

    /* RGB8888 or RGB1555 */
    pOrigBMPBuf = (uint8*)malloc(h_src * stride);
    if (NULL == pOrigBMPBuf)
    {
        printf("not enough memory to malloc!\n");
        fclose(pFile);
        return SV_FAILURE;
    }

    fseek(pFile, szBmpHeader.bFileHeader.bfOffBits, SEEK_SET);
    u32readn = fread(pOrigBMPBuf, 1, h_src * stride, pFile);
    if (u32readn != (h_src * stride))
    {
        printf("fread (%d*%d)error!line:%d\n", h_src, stride, __LINE__);
        printf("freadn:%d\n", u32readn);
        perror("fread:");
    }

    pRGBBuf = pstGuiImage->pbmp;
    pYBuf  = pstGuiImage->pbmp;
    pYUYVBuf = pstGuiImage->pbmp;
    pUYVYBuf = pstGuiImage->pbmp;
    pUVBuf = pstGuiImage->pbmp + w_dst * h_dst;
    pStart = pOrigBMPBuf;
    for (i = 0; i < h_dst; i++)
    {
        pStart = pOrigBMPBuf + (h_src-1-i)*stride;  //BMP图片起始y坐标在左下角，与要求的左上角相反
        for (j = 0; j < w_dst; j++)
        {
            switch (byte){
                case 2: //ARGB1555格式
                    u16color = *((uint16*)pStart);
                    r = ((u16color >> 10) & 0x1f) << 3;
                    g = ((u16color >> 5) & 0x1f) << 3;
                    b = ((u16color) & 0x1f) << 3;
                    a = (((u16color) >> 15) & 0x1) << 7;
                    pStart += 2;
                    break;
                case 3: //RGB888格式
                    a = 0xff;
                    r = *(pStart + 2);
                    g = *(pStart + 1);
                    b = *(pStart + 0);
                    pStart += 3;
                    break;
                case 4: //BGRA8888格式
                    a = *(pStart + 3);
                    r = *(pStart + 2);
                    g = *(pStart + 1);
                    b = *(pStart + 0);
                    pStart += 4;
                    break;
            }

            switch(format){
                case MPP_GUI_FMT_ARGB_1555:
                    *((uint16*)pRGBBuf) = (((a >> 7) & 0x1) << 15) | \
                                          (((r >> 3) & 0x1f) << 10) | \
                                          (((g >> 3) & 0x1f) << 5) | \
                                          (((b >> 3) & 0x1f));
                    pRGBBuf += 2;
                    break;
				case MPP_GUI_FMT_RGB_565:
                    *((uint16*)pRGBBuf) = (((r >> 3) & 0x1f) << 11) | \
                                          (((g >> 2) & 0x3f) << 5) | \
                                          (((b >> 3) & 0x1f));
                    pRGBBuf += 2;
                    break;
				case MPP_GUI_FMT_BGR_565:
                    *((uint16*)pRGBBuf) = (((b >> 3) & 0x1f) << 11) | \
                                          (((g >> 2) & 0x3f) << 5) | \
                                          (((r >> 3) & 0x1f));
                    pRGBBuf += 2;
                    break;
                case MPP_GUI_FMT_RGB_888:
                    *(pRGBBuf + 2) = r;
                    *(pRGBBuf + 1) = g;
                    *(pRGBBuf + 0) = b;
                    pRGBBuf += 3;
                    break;
                case MPP_GUI_FMT_ARGB_8888:
                    *(pRGBBuf + 3) = a;
                    *(pRGBBuf + 2) = r;
                    *(pRGBBuf + 1) = g;
                    *(pRGBBuf + 0) = b;
                    pRGBBuf += 4;
                    break;
                case MPP_GUI_FMT_YUYV:
                    if((r >= MPP_GUI_SKIP_VALUE && g >= MPP_GUI_SKIP_VALUE && b >= MPP_GUI_SKIP_VALUE)
#if 0
                    || (r <= 10 && g <= 10 && b <= 10)
#endif
                    )
                    {

                        *pYUYVBuf++ = 0x00;
                        *pYUYVBuf++ = 0x00;
                        break;
                    }

                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    *pYUYVBuf++ = y;
                    if(j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        *pYUYVBuf++ = u;
                    }
                    else
                    {
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        *pYUYVBuf++ = v;
                    }
                    break;
                case MPP_GUI_FMT_UYVY:
                    if((r >= MPP_GUI_SKIP_VALUE && g >= MPP_GUI_SKIP_VALUE && b >= MPP_GUI_SKIP_VALUE)
#if 0
                    || (r <= 10 && g <= 10 && b <= 10)
#endif
                    )
                    {

                        *pUYVYBuf++ = 0x00;
                        *pUYVYBuf++ = 0x00;
                        break;
                    }

                    if(j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        *pUYVYBuf++ = u;
                    }
                    else
                    {
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        *pUYVYBuf++ = v;
                    }
                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    *pUYVYBuf++ = y;
                    break;

                case MPP_GUI_FMT_YUV_420SP:
                    if((r >= MPP_GUI_SKIP_VALUE && g >= MPP_GUI_SKIP_VALUE && b >= MPP_GUI_SKIP_VALUE)
#if 0
                    || (r <= 10 && g <= 10 && b <= 10)
#endif
                    )
                    {
                        y = 0x00;
                        u = 0x00;
                        v = 0x00;
                        *pYBuf++ = y;
                        if(i % 2 == 0 && j % 2 == 0)
                        {
                            *pUVBuf++ = u;
                            *pUVBuf++ = v;
                        }
                        break;
                    }

                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    *pYBuf++ = y;
                    if(i % 2 == 0 && j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        *pUVBuf++ = u;
                        *pUVBuf++ = v;
                    }
                    break;
            }

        }

    }

    fclose(pFile);
    free(pOrigBMPBuf);
    return SV_SUCCESS;
}


sint32 MPP_GUI_SAVE_BMP(MPP_GUI_IMAGE_S *pstGuiImage, const char *filename, int byte)
{
    FILE* pFile;
    uint32 i, j, w, h;
    uint8 a, r, g, b, format;
    sint32 y, u, v;
    uint8 *pBMPBuf, *pRGBBuf, *pYBuf, *pUVBuf, *pStart;
    uint16 *pDst;
    uint16 u16color;

    BITMAP_HEADER szBmpHeader;

    if (NULL == filename)
    {
        printf("MPP_GUI_SAVE_BMP: filename=NULL\n");
        return SV_FAILURE;
    }

    switch (pstGuiImage->format){
        case MPP_GUI_FMT_ARGB_1555:
		case MPP_GUI_FMT_RGB_565:
		case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_RGB_888:
        case MPP_GUI_FMT_ARGB_8888:
        case MPP_GUI_FMT_YUV_420SP:
        case MPP_GUI_FMT_YUYV:
        case MPP_GUI_FMT_UYVY:
            format = pstGuiImage->format;
            break;
        default:
            printf("GuiImage format[%d] not supported!\n", pstGuiImage->format);
            return SV_FAILURE;
    }

    if(byte < 2 && byte > 4)
    {
        printf("bitmap format not supported!\n");
        return SV_FAILURE;
    }

    if ((pFile = fopen((char*)filename, "wb")) == NULL)
    {
        printf("Open file faild:%s!\n", filename);
        return SV_FAILURE;
    }

    w = pstGuiImage->Width;
    h = pstGuiImage->Height;

    szBmpHeader.bFileHeader.bfType = 0x4d42;
    szBmpHeader.bFileHeader.bfSize = sizeof(BITMAP_HEADER) + w * h * byte;
    szBmpHeader.bFileHeader.bfReversed1 = 0x0;
    szBmpHeader.bFileHeader.bfReversed2 = 0x0;
    szBmpHeader.bFileHeader.bfOffBits = sizeof(BITMAP_HEADER);
    szBmpHeader.bInfoHeader.biSize = sizeof(BITMAP_INFO_HEADER);
    szBmpHeader.bInfoHeader.biWidth = w;
    szBmpHeader.bInfoHeader.biHeight = h;
    szBmpHeader.bInfoHeader.biPlanes = 0x1;
    szBmpHeader.bInfoHeader.biBitCount = 8 * byte;
    szBmpHeader.bInfoHeader.biCompression = 0x0;
    szBmpHeader.bInfoHeader.biSizeImage = w * h* byte;
    szBmpHeader.bInfoHeader.biXPelsPerMeter = 0x0;
    szBmpHeader.bInfoHeader.biYPelsPerMeter = 0x0;
    szBmpHeader.bInfoHeader.biClrUsed = 0x0;
    szBmpHeader.bInfoHeader.biClrImportant = 0x0;

    pBMPBuf = malloc(w * h * byte);
    pStart = pBMPBuf;
    pRGBBuf = pstGuiImage->pbmp;
    pYBuf = pstGuiImage->pbmp;
    pUVBuf = pstGuiImage->pbmp + (w * h);

    for(i = 0; i < h; i++)
    {
        pStart = pBMPBuf + (h-1-i)*byte*w;
        for(j = 0; j < w; j++)
        {
            switch (format){
                case MPP_GUI_FMT_ARGB_1555:
                    u16color = *((uint16*)pRGBBuf);
                    a = ((u16color >> 15) & 0x01) ? 0xff : 0x00;
                    r = ((u16color >> 10) & 0x1f) << 3;
                    g = ((u16color >> 5) & 0x1f) << 3;
                    b = ((u16color) & 0x1f) << 3;
                    pRGBBuf += 2;
                    break;
				case MPP_GUI_FMT_RGB_565:
                    u16color = *((uint16*)pRGBBuf);
                    a = 0xff;
                    r = ((u16color >> 11) & 0x1f) << 3;
                    g = ((u16color >> 5) & 0x3f) << 2;
                    b = ((u16color) & 0x1f) << 3;
                    pRGBBuf += 2;
                    break;
				case MPP_GUI_FMT_BGR_565:
                    u16color = *((uint16*)pRGBBuf);
                    a = 0xff;
                    b = ((u16color >> 11) & 0x1f) << 3;
                    g = ((u16color >> 5) & 0x3f) << 2;
                    r = ((u16color) & 0x1f) << 3;
                    pRGBBuf += 2;
                    break;
                case MPP_GUI_FMT_RGB_888:
                    a = 0xff;
                    r = *(pRGBBuf + 2);
                    g = *(pRGBBuf + 1);
                    b = *(pRGBBuf + 0);
                    pRGBBuf += 3;
                    break;
                case MPP_GUI_FMT_ARGB_8888:
                    a = *(pRGBBuf + 3);
                    r = *(pRGBBuf + 2);
                    g = *(pRGBBuf + 1);
                    b = *(pRGBBuf + 0);
                    pRGBBuf += 4;
                    break;
                case MPP_GUI_FMT_YUV_420SP:
                    y = *pYBuf++;
                    if(j % 2 == 0)
                    {
                        u = *pUVBuf++;
                        v = *pUVBuf++;
                    }
                    a = 0xff;
                    r = max(0, min(255, (298*y + 411 * v - 57344)>>8));
                    g = max(0, min(255, (298*y - 101* u - 211* v+ 34739)>>8));
                    b = max(0, min(255, (298*y + 519* u- 71117)>>8));
                    break;
                case MPP_GUI_FMT_YUYV:
                    y = *pYBuf;
                    if(j % 2 == 0)
                    {
                        u = pYBuf[1];
                        v = pYBuf[3];
                    }
                    else
                    {
                        u = pYBuf[-1];
                        v = pYBuf[1];
                    }
                    pYBuf += 2;
                    a = 0xff;
                    r = max(0, min(255, (298*y + 411 * v - 57344)>>8));
                    g = max(0, min(255, (298*y - 101* u - 211* v+ 34739)>>8));
                    b = max(0, min(255, (298*y + 519* u- 71117)>>8));
                    break;
                case MPP_GUI_FMT_UYVY:
                    y = pYBuf[1];
                    if(j % 2 == 0)
                    {
                        u = pYBuf[0];
                        v = pYBuf[2];
                    }
                    else
                    {
                        u = pYBuf[-2];
                        v = pYBuf[0];
                    }
                    pYBuf += 2;
                    a = 0xff;
                    r = max(0, min(255, (298*y + 411 * v - 57344)>>8));
                    g = max(0, min(255, (298*y - 101* u - 211* v+ 34739)>>8));
                    b = max(0, min(255, (298*y + 519* u- 71117)>>8));
                    break;
            }

            switch (byte){
                case 2:  //ARGB1555格式
                    *((uint16*)pStart) = (((a >> 7) & 0x1) << 15) | \
                                          (((r >> 3) & 0x1f) << 10) | \
                                          (((g >> 3) & 0x1f) << 5) | \
                                          (((b >> 3) & 0x1f));
                    pStart += 2;
                    break;
                case 3:  //BGR888格式
                    *(pStart + 2) = r;
                    *(pStart + 1) = g;
                    *(pStart + 0) = b;
                    pStart += 3;
                    break;
                case 4:  //BGRA8888格式
                    *(pStart + 3) = a;
                    *(pStart + 2) = r;//r;
                    *(pStart + 1) = g;//g;
                    *(pStart + 0) = b;//b;

                    pStart += 4;
                    break;
            }
        }
        if(i % 2 == 0)
        {
            pUVBuf = pUVBuf - w;
        }
    }


    fwrite(&szBmpHeader, 1, sizeof(BITMAP_HEADER), pFile);
    fwrite(pBMPBuf, 1, w*h*byte, pFile);

    free(pBMPBuf);
    fclose(pFile);
    return SV_SUCCESS;
}





/******************************************************************************
 * 函数功能: 放大图片
 * 输入参数: pstGuiImage_dst  --- 目的图片
            pstGuiImage_src  --- 源图片
            scale          --- 放大倍数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_Enlarge_BMP(MPP_GUI_IMAGE_S *pstGuiImage_dst, MPP_GUI_IMAGE_S *pstGuiImage_src, MPP_GUI_SCALE_TYPE enScale)
{
    int ret;
    if(NULL == pstGuiImage_src)
    {
        printf("pstGuiImage_src=NULL\n");
        return SV_FAILURE;
    }

    if(pstGuiImage_src->pbmp == NULL)
    {
        printf("LINE: %d, pstGuiImage_src->pbmp cannot be NULL\n", __LINE__);
        return SV_FAILURE;
    }

    switch(enScale)
    {
        case MPP_GUI_SCALE_QUATER:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) >> 2;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) >> 2;
            pstGuiImage_dst->Width = ALIGN4(pstGuiImage_dst->Width);
            pstGuiImage_dst->Height = ALIGN4(pstGuiImage_dst->Height);
            break;
        case MPP_GUI_SCALE_HALF:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) >> 1;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) >> 1;
            pstGuiImage_dst->Width = ALIGN4(pstGuiImage_dst->Width);
            pstGuiImage_dst->Height = ALIGN4(pstGuiImage_dst->Height);
            break;
        case MPP_GUI_SCALE_SAME:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width);
            pstGuiImage_dst->Height = (pstGuiImage_src->Height);
            break;
        case MPP_GUI_SCALE_DOUBLE:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) << 1;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) << 1;
            break;
        case MPP_GUI_SCALE_TRIBLE:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) * 3;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) * 3;
            break;
        case MPP_GUI_SCALE_QUADRPUBLE:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) << 2;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) << 2;
            break;
        default:
            printf("cannot parse scale:%d\n", enScale);
            return -1;
            break;
    }

    //printf("pstGuiImage_dst->Width:%d, pstGuiImage_dst->Height:%d\n", pstGuiImage_dst->Width, pstGuiImage_dst->Height);


    ret = MPP_GUI_ZOOM_BMP(pstGuiImage_dst, pstGuiImage_src);
    if(ret != 0)
    {
        printf("MPP_GUI_ZOOM_BMP failed [err=%d]\n", ret);
        return ret;
    }
    //printf("pstGuiImage_dst->Width:%d, pstGuiImage_dst->Height:%d\n", pstGuiImage_dst->Width, pstGuiImage_dst->Height);

    return 0;

#if 0
    int ret, w_src, h_src, w_dst, h_dst;
    int i, j, scale_end;
    uint8 *pRGBBuf_dst, *pRGBBuf_src, *pRGBBuf_src1;
    uint8 *pYBuf_dst, *pYBuf_src, *pYBuf_src1;
    uint8 *pUVBuf_dst, *pUVBuf_src, *pUVBuf_src1;


    scale = max(1, scale);
    w_src = pstGuiImage_src->Width;
    h_src = pstGuiImage_src->Height;
    w_dst = w_src * scale;
    h_dst = h_src * scale;
    scale_end = scale-1;

    switch(pstGuiImage_src->format){
        case MPP_GUI_FMT_ARGB_1555:
            pstGuiImage_dst->pbmp = malloc(w_dst*h_dst*2);
            pstGuiImage_dst->Height = h_dst;
            pstGuiImage_dst->Width = w_dst;
            pstGuiImage_dst->format = MPP_GUI_FMT_ARGB_1555;
            break;
        case MPP_GUI_FMT_RGB_565:
            pstGuiImage_dst->pbmp = malloc(w_dst*h_dst*2);
            pstGuiImage_dst->Height = h_dst;
            pstGuiImage_dst->Width = w_dst;
            pstGuiImage_dst->format = MPP_GUI_FMT_RGB_565;
            break;
		case MPP_GUI_FMT_BGR_565:
            pstGuiImage_dst->pbmp = malloc(w_dst*h_dst*2);
            pstGuiImage_dst->Height = h_dst;
            pstGuiImage_dst->Width = w_dst;
            pstGuiImage_dst->format = MPP_GUI_FMT_BGR_565;
            break;
        case MPP_GUI_FMT_RGB_888:
            pstGuiImage_dst->pbmp = malloc(w_dst*h_dst*3);
            pstGuiImage_dst->Height = h_dst;
            pstGuiImage_dst->Width = w_dst;
            pstGuiImage_dst->format = MPP_GUI_FMT_RGB_888;
            break;
        case MPP_GUI_FMT_ARGB_8888:
            pstGuiImage_dst->pbmp = malloc(w_dst*h_dst*4);
            pstGuiImage_dst->Height = h_dst;
            pstGuiImage_dst->Width = w_dst;
            pstGuiImage_dst->format = MPP_GUI_FMT_ARGB_8888;
            break;
        case MPP_GUI_FMT_YUV_420SP:
            pstGuiImage_dst->pbmp = malloc(w_dst*h_dst*3/2);
            pstGuiImage_dst->Height = h_dst;
            pstGuiImage_dst->Width = w_dst;
            pstGuiImage_dst->format = MPP_GUI_FMT_YUV_420SP;
            break;
        default:
            printf("GuiImage format not supported!\n");
            return SV_FAILURE;
    }

    pRGBBuf_dst = pstGuiImage_dst->pbmp;
    pRGBBuf_src = pstGuiImage_src->pbmp;
    if(pstGuiImage_src->format == MPP_GUI_FMT_YUV_420SP)
    {
        pYBuf_dst = pstGuiImage_dst->pbmp;
        pYBuf_src = pstGuiImage_src->pbmp;
        pUVBuf_dst = pYBuf_dst + w_dst * h_dst;
        pUVBuf_src = pYBuf_src + w_src * h_src;
    }

    if(pstGuiImage_src->format == MPP_GUI_FMT_ARGB_1555 ||
	   pstGuiImage_src->format == MPP_GUI_FMT_RGB_565 ||
	   pstGuiImage_src->format == MPP_GUI_FMT_BGR_565)
    {
        for(i = 0; i < h_dst; i++)
        {
            for(j = 0; j < w_dst; j++)
            {
                *pRGBBuf_dst++ = pRGBBuf_src[0];
                *pRGBBuf_dst++ = pRGBBuf_src[1];
                if(j % scale == scale_end)
                {
                    pRGBBuf_src+=2;
                }
            }
            if(i % scale != scale_end)
            {
                pRGBBuf_src-=w_src*2;
            }
        }
    }
    else if(pstGuiImage_src->format == MPP_GUI_FMT_RGB_888)
    {
        for(i = 0; i < h_dst; i++)
        {
            for(j = 0; j < w_dst; j++)
            {
                *pRGBBuf_dst++ = pRGBBuf_src[0];
                *pRGBBuf_dst++ = pRGBBuf_src[1];
                *pRGBBuf_dst++ = pRGBBuf_src[2];
                if(j % scale == scale_end)
                {
                    pRGBBuf_src+=3;
                }
            }
            if(i % scale != scale_end)
            {
                pRGBBuf_src-=w_src*3;
            }
        }
    }
    else if(pstGuiImage_src->format == MPP_GUI_FMT_ARGB_8888)
    {
        for(i = 0; i < h_dst; i++)
        {
            for(j = 0; j < w_dst; j++)
            {
                *pRGBBuf_dst++ = pRGBBuf_src[0];
                *pRGBBuf_dst++ = pRGBBuf_src[1];
                *pRGBBuf_dst++ = pRGBBuf_src[2];
                *pRGBBuf_dst++ = pRGBBuf_src[3];
                if(j % scale == scale_end)
                {
                    pRGBBuf_src+=4;
                }
            }
            if(i % scale != scale_end)
            {
                pRGBBuf_src-=w_src*4;
            }
        }
    }
    else if(pstGuiImage_src->format == MPP_GUI_FMT_YUV_420SP)
    {
        for(i = 0; i < h_dst; i++)
        {
            for(j = 0; j < w_dst; j++)
            {
                *pYBuf_dst++ = *pYBuf_src;
                if(j % scale == scale_end)
                {
                    pYBuf_src++;
                }
            }
            if(i % scale != scale_end)
            {
                pYBuf_src-=w_src;
            }
        }

        for(i = 0; i < h_dst / 2; i++)
        {
            for(j = 0; j < w_dst / 2; j++)
            {
                *pUVBuf_dst++ = pUVBuf_src[0];
                *pUVBuf_dst++ = pUVBuf_src[1];
                if(j % scale == scale_end)
                {
                    pUVBuf_src+=2;
                }
            }
            if(i % scale != scale_end)
            {
                pUVBuf_src-=w_src;
            }
        }
    }
    return SV_SUCCESS;
#endif
}

/******************************************************************************
 * 函数功能: 缩放图片
 * 输入参数: color 32位argb颜色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_ZOOM_BMP(MPP_GUI_IMAGE_S *pstGuiImage_dst, MPP_GUI_IMAGE_S *pstGuiImage_src)
{
    int ret, w_src, h_src, w_dst, h_dst;
    int i, j, scale_end;
    uint8 *pRGBBuf_dst, *pRGBBuf_src, *pRGBBuf_src1;
    uint8 *pYBuf_dst, *pYBuf_src, *pYBuf_src1;
    uint8 *pUVBuf_dst, *pUVBuf_src, *pUVBuf_src1;

    if(NULL == pstGuiImage_src)
    {
        printf("pstGuiImage_src=NULL\n");
        return SV_FAILURE;
    }

    if(pstGuiImage_src->pbmp == NULL)
    {
        printf("pstGuiImage_src->pbmp cannot be NULL\n");
        return SV_FAILURE;
    }

    sint32 s32Ret;

    if(pstGuiImage_dst == NULL || pstGuiImage_src == NULL)
        return ERR_NULL_PTR;

    if((0 == pstGuiImage_dst->Height) || (0 == pstGuiImage_dst->Width) ||
       (0 == pstGuiImage_src->Height) || (0 == pstGuiImage_src->Height))
        return SV_SUCCESS;

    unsigned long xrIntFloat_16 = (pstGuiImage_src->Width << 16) / pstGuiImage_dst->Width + 1;
    unsigned long yrIntFloat_16 = (pstGuiImage_src->Height << 16) / pstGuiImage_dst->Height + 1;
    unsigned long dst_width = pstGuiImage_dst->Width;
    unsigned long srcy_16 = 0, srcx_16 = 0;
    unsigned long x, y;

    uint8 *pu8DstLine, *pu8SrcLine;
    uint16 *pu16DstLine, *pu16SrcLine;
    uint24 *pu24DstLine, *pu24SrcLine;
    uint32 *pu32DstLine, *pu32SrcLine;

    switch(pstGuiImage_dst->format)
    {
        case MPP_GUI_FMT_RGB_8BPP:
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu8DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu8SrcLine = (uint8*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);;
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu8DstLine[x] = pu8SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu8DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_RGB_565:
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 2);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu16DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu16SrcLine = (uint16*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu16DstLine[x] = pu16SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu16DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_RGB_888:
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 3);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu24DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu24SrcLine = (uint24*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu24DstLine[x] = pu24SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu24DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_8888:
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 4);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu32DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu32SrcLine = (uint32*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu32DstLine[x] = pu32SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu32DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_UYVY:
        case MPP_GUI_FMT_YUYV:
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 2);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu32DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu32SrcLine = (uint32*)pstGuiImage_src->pbmp + (pstGuiImage_src->Width >> 1)*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < (pstGuiImage_dst->Width >> 1); x++)
                {
                    pu32DstLine[x] = pu32SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu32DstLine+=(pstGuiImage_dst->Width >> 1);
            }
            break;
        case MPP_GUI_FMT_YUV_420SP:
            pstGuiImage_dst->pbmp = malloc((pstGuiImage_dst->Width * pstGuiImage_dst->Height * 3) >> 1);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu8DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu8SrcLine = (uint8*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < (pstGuiImage_dst->Width); x++)
                {
                    pu8DstLine[x] = pu8SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu8DstLine+=pstGuiImage_dst->Width;
            }

            srcx_16 = 0;
            srcy_16 = 0;
            pu16DstLine = pstGuiImage_dst->pbmp + (pstGuiImage_dst->Width * pstGuiImage_dst->Height);
            for(y = 0; y < (pstGuiImage_dst->Height >> 1); y++)
            {
                pu16SrcLine = (uint16*)(pstGuiImage_src->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height) + \
                             (pstGuiImage_src->Width >> 1)*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < (pstGuiImage_dst->Width >> 1); x++)
                {
                    pu16DstLine[x] = pu16SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu16DstLine+=(pstGuiImage_dst->Width >> 1);
            }
            break;
    }

    return SV_SUCCESS;
}

// pyl

/******************************************************************************
 * 函数功能: 缩放图片， 0-1连续比例
 * 输入参数: color 32位argb颜色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
static sint32 MPP_GUI_ZOOM_BMP_BilinearInterpolation(MPP_GUI_IMAGE_S* pstGuiImage_dst, MPP_GUI_IMAGE_S* pstGuiImage_src){
    int ret, w_src, h_src, w_dst, h_dst;
    int i, j, scale_end;
    uint8 *pRGBBuf_dst, *pRGBBuf_src, *pRGBBuf_src1;
    uint8 *pYBuf_dst, *pYBuf_src, *pYBuf_src1;
    uint8 *pUVBuf_dst, *pUVBuf_src, *pUVBuf_src1;

    if(NULL == pstGuiImage_src)
    {
        printf("pstGuiImage_src=NULL\n");
        return SV_FAILURE;
    }

    if(pstGuiImage_src->pbmp == NULL)
    {
        printf("pstGuiImage_src->pbmp cannot be NULL\n");
        return SV_FAILURE;
    }

    sint32 s32Ret;

    if(pstGuiImage_dst == NULL || pstGuiImage_src == NULL)
        return ERR_NULL_PTR;

    if((0 == pstGuiImage_dst->Height) || (0 == pstGuiImage_dst->Width) ||
       (0 == pstGuiImage_src->Height) || (0 == pstGuiImage_src->Height))
        return SV_SUCCESS;

    // 移动步长
    unsigned long xrIntFloat_16 = (pstGuiImage_src->Width << 16) / pstGuiImage_dst->Width + 1;
    unsigned long yrIntFloat_16 = (pstGuiImage_src->Height << 16) / pstGuiImage_dst->Height + 1;
    // （i，j）映射到源图像上的位置, 带小数
    unsigned long srcy_16 = 0, srcx_16 = 0;
    // 目标图像的像素位置(i, j)
    unsigned long x, y;

    uint8 *pu8DstLine, *pu8SrcLine;
    uint16 *pu16DstLine, *pu16SrcLine;
    uint24 *pu24DstLine, *pu24SrcLine;
    uint32 *pu32DstLine, *pu32SrcLine;

    // 记录映射像素与四个像素点之间的位置差
    uint32 delta_x, delta_y;
    // 左上角像素位置
    uint32 ox, oy;
    // 缓存映射点关系
    uint32 tmpx, tmpy;
    // 记录移动行数
    uint32 offSetH;
    // Y的数量
    uint32 size = pstGuiImage_src->Width * pstGuiImage_src->Height;
    // 偏移整行数量
    uint32 offsetY;
    // 记录周围4个像素的值
    uint8 y_plane_color[2][2];
    uint8 u_plane_color[2][2];
    uint8 v_plane_color[2][2];
    // 插值结果
    uint32 y_final, u_final, v_final;
    //  UV插值结果
    uint32 uvPos;
    // UV位置指针
    uint8 *dst_uv;
    // 缩放比例，移动步长
    int xratio, yratio;

    switch (pstGuiImage_dst->format)
    {
        case MPP_GUI_FMT_RGB_8BPP:
            /* code */
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu8DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu8SrcLine = (uint8*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);;
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu8DstLine[x] = pu8SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu8DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_RGB_565:
            /* code */
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 2);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu16DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu16SrcLine = (uint16*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu16DstLine[x] = pu16SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu16DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_RGB_888:
            /* code */
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 3);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu24DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu24SrcLine = (uint24*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu24DstLine[x] = pu24SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu24DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_ARGB_8888:
            /* code */
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 4);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu32DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu32SrcLine = (uint32*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < pstGuiImage_dst->Width; x++)
                {
                    pu32DstLine[x] = pu32SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu32DstLine+=pstGuiImage_dst->Width;
            }
            break;
        case MPP_GUI_FMT_UYVY:
        case MPP_GUI_FMT_YUYV:
            /* code */
            pstGuiImage_dst->pbmp = malloc(pstGuiImage_dst->Width * pstGuiImage_dst->Height * 2);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu32DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu32SrcLine = (uint32*)pstGuiImage_src->pbmp + (pstGuiImage_src->Width >> 1)*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < (pstGuiImage_dst->Width >> 1); x++)
                {
                    pu32DstLine[x] = pu32SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu32DstLine+=(pstGuiImage_dst->Width >> 1);
            }
            break;
        case MPP_GUI_FMT_YUV_420SP:
            pstGuiImage_dst->pbmp = malloc((pstGuiImage_dst->Width * pstGuiImage_dst->Height * 3) >> 1);
            pstGuiImage_dst->format = pstGuiImage_dst->format;
            pu8DstLine = pstGuiImage_dst->pbmp;
            for(y = 0; y < pstGuiImage_dst->Height; y++)
            {
                pu8SrcLine = (uint8*)pstGuiImage_src->pbmp + pstGuiImage_src->Width*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < (pstGuiImage_dst->Width); x++)
                {
                    pu8DstLine[x] = pu8SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu8DstLine+=pstGuiImage_dst->Width;
            }

            srcx_16 = 0;
            srcy_16 = 0;
            pu16DstLine = pstGuiImage_dst->pbmp + (pstGuiImage_dst->Width * pstGuiImage_dst->Height);
            for(y = 0; y < (pstGuiImage_dst->Height >> 1); y++)
            {
                pu16SrcLine = (uint16*)(pstGuiImage_src->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height) + \
                             (pstGuiImage_src->Width >> 1)*(srcy_16>>16);
                srcx_16 = 0;
                for(x = 0; x < (pstGuiImage_dst->Width >> 1); x++)
                {
                    pu16DstLine[x] = pu16SrcLine[srcx_16>>16];
                    srcx_16+=xrIntFloat_16;
                }
                srcy_16+=yrIntFloat_16;
                pu16DstLine+=(pstGuiImage_dst->Width >> 1);
            }

            // pstGuiImage_dst->pbmp = malloc((pstGuiImage_dst->Width * pstGuiImage_dst->Height * 3) >> 1);
            // pstGuiImage_dst->format = pstGuiImage_dst->format;
            // pu8DstLine = (uint8*)pstGuiImage_dst->pbmp;
            // pu8SrcLine = (uint8*)pstGuiImage_src->pbmp;
            // pu16DstLine = pstGuiImage_dst->pbmp + (pstGuiImage_dst->Width * pstGuiImage_dst->Height);
            // // 双线性插值
            // uint32 srcH=pstGuiImage_src->Height, srcW=pstGuiImage_src->Width;
            // uint32 goalH=pstGuiImage_dst->Height, goalW=pstGuiImage_dst->Width;
            // uint8  Y_planer[2][2];
            // uint32 ratioH=(srcH << 8) / goalH;
            // uint32 ratioW=(srcW << 8) / goalW;
            // uint32 HPos=0, WPos=0;
            // uint32 H=0, W=0;  //源图像中的位置-映射到目标图像(h, w)
            // uint32 delta_h, delta_w;
            // uint32 goalUV_H=goalH, goalUV_W=0;
            // uint32 UV_H, UV_W, nextH, nextW;

            // for(int h=0; h<goalH; h++){
            //     H = HPos >> 8;          //src位置-整数
            //     delta_h = HPos & 0xFF;  //src位置差-小数部分

            //     WPos = 0;
            //     for(int w=0; w<goalW; w++){
            //         W = WPos >> 8;
            //         delta_w = WPos & 0xFF;

            //         nextH = (H+1) > srcH ? srcH : (H+1);
            //         nextW = (W+1) > srcW ? srcW : (W+1);
            //         Y_planer[0][0] = pu8SrcLine[H*srcW + W];
            //         Y_planer[0][1] = pu8SrcLine[H*srcW + nextW];
            //         Y_planer[1][0] = pu8SrcLine[nextH*srcW + W];
            //         Y_planer[1][1] = pu8SrcLine[nextH*srcW + nextW];

            //         uint32 Y = delta_h*delta_w*Y_planer[0][0] + delta_h*(0x100-delta_w)*Y_planer[0][1] + (0x100-delta_h)*delta_w*Y_planer[1][0] + (0x100-delta_h)*(0x100-delta_w)*Y_planer[1][1];
            //         pu8DstLine[h*goalW + w] = Y >> 16;

            //         // 这里有问题-需要注意
            //         // if((h&1) == 0 && (w&1) == 0){
            //         //     UV_H = srcH + H / 2;
            //         //     UV_W = W;
            //         //     if((UV_W&1) != 0){
            //         //         UV_W -= 1;
            //         //     }
            //         //     goalUV_H = goalH + h / 2;
            //         //     goalUV_W = w;
            //         //     if((goalUV_W&1) != 0){
            //         //         goalUV_W -= 1;
            //         //     }
            //         //     // pu8DstLine[goalUV_H*pstGuiImage_dst->Width + goalUV_W] = pu8SrcLine[UV_H*pstGuiImage_src->Width + UV_W];
            //         //     // pu8DstLine[goalUV_H*pstGuiImage_dst->Width + goalUV_W + 1] = pu8SrcLine[UV_H*pstGuiImage_src->Width + UV_W + 1];
            //         //     // pu8DstLine[goalUV_H*goalW + goalUV_W] = 50;
            //         //     // pu8DstLine[goalUV_H*goalW + goalUV_W + 1] = 150;
            //         //     // pu16DstLine[goalUV_H*goalW + goalUV_W] = (10 << 8) + 50;
            //         // }
            //         WPos += ratioW;
            //         // print_level(SV_INFO, "BilinearInterpolation.........  w=%d W=%d\n", w, W);
            //     }
            //     HPos += ratioH;
            // }
            // srcx_16 = 0;
            // srcy_16 = 0;
            // pu16DstLine = pstGuiImage_dst->pbmp + (pstGuiImage_dst->Width * pstGuiImage_dst->Height);
            // for(y = 0; y < (pstGuiImage_dst->Height >> 1); y++)
            // {
            //     pu16SrcLine = (uint16*)(pstGuiImage_src->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height) + \
            //                  (pstGuiImage_src->Width >> 1)*(srcy_16>>16);
            //     srcx_16 = 0;
            //     for(x = 0; x < (pstGuiImage_dst->Width >> 1); x++)
            //     {
            //         pu16DstLine[x] = pu16SrcLine[srcx_16>>16];
            //         srcx_16+=xrIntFloat_16;
            //     }
            //     srcy_16+=yrIntFloat_16;
            //     pu16DstLine+=(pstGuiImage_dst->Width >> 1);
            // }
            break;
        default:
            break;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 放大图片; 暂时由MPP_GUI_ZOOM_BMP实现，后续完成其他算法
 * 输入参数: pstGuiImage_dst  --- 目的图片
            pstGuiImage_src  --- 源图片
            scale          --- 放大倍数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_Enlarge_BMP_Arbitrary(MPP_GUI_IMAGE_S *pstGuiImage_dst, MPP_GUI_IMAGE_S *pstGuiImage_src, MPP_GUI_SCALE_TYPE enScale)
{
    int ret;
    if(NULL == pstGuiImage_src)
    {
        printf("pstGuiImage_src=NULL\n");
        return SV_FAILURE;
    }

    if(pstGuiImage_src->pbmp == NULL)
    {
        printf("LINE: %d, pstGuiImage_src->pbmp cannot be NULL\n", __LINE__);
        return SV_FAILURE;
    }

    // 这里注重
    switch(enScale)
    {
        case MPP_GUI_SCALE_QUATER:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) >> 2;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) >> 2;
            pstGuiImage_dst->Width = ALIGN4(pstGuiImage_dst->Width);
            pstGuiImage_dst->Height = ALIGN4(pstGuiImage_dst->Height);
            break;
        case MPP_GUI_SCALE_HALF:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) >> 1;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) >> 1;
            pstGuiImage_dst->Width = ALIGN4(pstGuiImage_dst->Width);
            pstGuiImage_dst->Height = ALIGN4(pstGuiImage_dst->Height);
            break;
        case MPP_GUI_SCALE_SAME:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width);
            pstGuiImage_dst->Height = (pstGuiImage_src->Height);
            break;
        case MPP_GUI_SCALE_DOUBLE:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) << 1;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) << 1;
            break;
        case MPP_GUI_SCALE_TRIBLE:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) * 3;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) * 3;
            break;
        case MPP_GUI_SCALE_QUADRPUBLE:
            pstGuiImage_dst->Width = (pstGuiImage_src->Width) << 2;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) << 2;
            break;
        // pyl
        case MPP_GUI_SCALE_1_5x:
            pstGuiImage_dst->Width  = (pstGuiImage_src->Width)  * 1.5;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) * 1.5;
            break;
        case MPP_GUI_SCALE_W_AND_H:
            // 由MPP_GUI_ZOOM_BMP_BilinearInterpolation函数计算出缩放倍数, 以1280x720为基准
            pstGuiImage_dst->Width  = (pstGuiImage_src->Width)  * 0.5625;
            pstGuiImage_dst->Height = (pstGuiImage_src->Height) * 0.5625;
            break;
        default:
            printf("cannot parse scale:%d\n", enScale);
            return -1;
            break;
    }

    ret = MPP_GUI_ZOOM_BMP_BilinearInterpolation(pstGuiImage_dst, pstGuiImage_src);
    if(ret != 0)
    {
        printf("MPP_GUI_ZOOM_BMP failed [err=%d]\n", ret);
        return ret;
    }

    return 0;
}


/******************************************************************************
 * 函数功能: 旋转图片
 * 输入参数: pstGuiImage_dst  --- 目的图片
            pstGuiImage_src  --- 源图片
            enAngle          --- 旋转角度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_ROTATION_BMP(MPP_GUI_IMAGE_S *pstGuiImage_dst, MPP_GUI_IMAGE_S *pstGuiImage_src, MPP_GUI_ROTATE_ANGLE enAngle)
{
#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    int ret;
    int i, _i, j, _j;
    if(NULL == pstGuiImage_src)
    {
        printf("pstGuiImage_src=NULL\n");
        return SV_FAILURE;
    }

    if(pstGuiImage_src->pbmp == NULL)
    {
        printf("LINE: %d, pstGuiImage_src->pbmp cannot be NULL\n", __LINE__);
        return SV_FAILURE;
    }

    switch (pstGuiImage_src->format)
    {
        case MPP_GUI_FMT_RGB_8BPP:
            pstGuiImage_dst->format = pstGuiImage_src->format;
            pstGuiImage_dst->pbmp = malloc(ALIGN2(pstGuiImage_src->Width) * ALIGN2(pstGuiImage_src->Height));
            switch (enAngle)
            {
                case MPP_GUI_ROTATE_0:
                    memcpy(pstGuiImage_dst->pbmp, pstGuiImage_src->pbmp, pstGuiImage_src->Width * pstGuiImage_src->Height);
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                    break;
                case MPP_GUI_ROTATE_90: {
                    uint8 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint8 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    for(i = 0; i < pstGuiImage_src->Height; i++)
                    {
                        for(j = 0; j < pstGuiImage_src->Width; j++)
                        {
                            pdst[j][pstGuiImage_src->Height - i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                case MPP_GUI_ROTATE_180: {
                    uint8 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint8 (*pdst)[pstGuiImage_src->Width]  = pstGuiImage_dst->pbmp;
                    for(i = 0; i < pstGuiImage_src->Width; i++)
                    {
                        for(j = 0; j < pstGuiImage_src->Height; j++)
                        {
                            pdst[pstGuiImage_src->Width - i][pstGuiImage_src->Height - i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                }   break;
                case MPP_GUI_ROTATE_270: {
                    uint8 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint8 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    for(i = 0; i < pstGuiImage_src->Width; i++)
                    {
                        for(j = 0; j < pstGuiImage_src->Height; j++)
                        {
                            pdst[pstGuiImage_src->Width - j][i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                default:
                    printf("cannot detect enAngle:%d\n", enAngle);
                    return SV_FAILURE;
            }
            break;
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_ARGB_1555:
            pstGuiImage_dst->format = pstGuiImage_src->format;
            pstGuiImage_dst->pbmp = malloc(2 * ALIGN2(pstGuiImage_src->Width) * ALIGN2(pstGuiImage_src->Height));
            switch (enAngle)
            {
                case MPP_GUI_ROTATE_0:
                    memcpy(pstGuiImage_dst->pbmp, pstGuiImage_src->pbmp, 2 * pstGuiImage_src->Width * pstGuiImage_src->Height);
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                    break;
                case MPP_GUI_ROTATE_90: {
                    uint16 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint16 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    _i = pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        for(j = 0; j < pstGuiImage_src->Width; j++)
                        {
                            pdst[j][_i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                case MPP_GUI_ROTATE_180: {
                    uint16 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint16 (*pdst)[pstGuiImage_src->Width]  = pstGuiImage_dst->pbmp;
                    _i = pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            pdst[_i][_j] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                }   break;
                case MPP_GUI_ROTATE_270: {
                    uint16 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint16 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    for(i = 0; i < pstGuiImage_src->Height; i++)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            pdst[_j][i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                default:
                    printf("cannot detect enAngle:%d\n", enAngle);
                    return SV_FAILURE;
            }
            break;
        case MPP_GUI_FMT_RGB_888:
            pstGuiImage_dst->format = pstGuiImage_src->format;
            pstGuiImage_dst->pbmp = malloc(3 * ALIGN2(pstGuiImage_src->Width) * ALIGN2(pstGuiImage_src->Height));
            switch (enAngle)
            {
                case MPP_GUI_ROTATE_0:
                    memcpy(pstGuiImage_dst->pbmp, pstGuiImage_src->pbmp, 3 * pstGuiImage_src->Width * pstGuiImage_src->Height);
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                    break;
                case MPP_GUI_ROTATE_90: {
                    uint24 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint24 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    _i = pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        for(j = 0; j < pstGuiImage_src->Width; j++)
                        {
                            pdst[j][_i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                case MPP_GUI_ROTATE_180: {
                    uint24 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint24 (*pdst)[pstGuiImage_src->Width]  = pstGuiImage_dst->pbmp;
                    _i = pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            pdst[_i][_j] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                }   break;
                case MPP_GUI_ROTATE_270: {
                    uint24 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint24 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    for(i = 0; i < pstGuiImage_src->Height; i++)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            pdst[_j][i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                default:
                    printf("cannot detect enAngle:%d\n", enAngle);
                    return SV_FAILURE;
            }
            break;
        case MPP_GUI_FMT_ARGB_8888:
            pstGuiImage_dst->format = pstGuiImage_src->format;
            pstGuiImage_dst->pbmp = malloc(4 * ALIGN2(pstGuiImage_src->Width) * ALIGN2(pstGuiImage_src->Height));
            switch (enAngle)
            {
                case MPP_GUI_ROTATE_0:
                    memcpy(pstGuiImage_dst->pbmp, pstGuiImage_src->pbmp, 4 * pstGuiImage_src->Width * pstGuiImage_src->Height);
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                    break;
                case MPP_GUI_ROTATE_90: {
                    uint32 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint32 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    _i = pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        for(j = 0; j < pstGuiImage_src->Width; j++)
                        {
                            pdst[j][_i] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                case MPP_GUI_ROTATE_180: {
                    uint32 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint32 (*pdst)[pstGuiImage_src->Width]  = pstGuiImage_dst->pbmp;
                    _i = pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            pdst[_i][_j] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                }   break;
                case MPP_GUI_ROTATE_270: {
                    uint32 (*psrc)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint32 (*pdst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    for(i = 0; i < pstGuiImage_src->Height; i++)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            pdst[_i][_j] = psrc[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                default:
                    printf("cannot detect enAngle:%d\n", enAngle);
                    return SV_FAILURE;
            }
            break;
        case MPP_GUI_FMT_YUV_420SP:
            pstGuiImage_dst->format = pstGuiImage_src->format;
            pstGuiImage_dst->pbmp = malloc((ALIGN2(pstGuiImage_src->Width) * ALIGN2(pstGuiImage_src->Height) * 3) >> 1);
            switch (enAngle)
            {
                case MPP_GUI_ROTATE_0:
                    memcpy(pstGuiImage_dst->pbmp, pstGuiImage_src->pbmp, (pstGuiImage_src->Width * pstGuiImage_src->Height * 3) >> 1);
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                    break;
                case MPP_GUI_ROTATE_90: {
                    uint8 (*p8src)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint8 (*p8dst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    uint16 (*p16src)[pstGuiImage_src->Width >> 1] = pstGuiImage_src->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height;
                    uint16 (*p16dst)[pstGuiImage_src->Height >> 1] = pstGuiImage_dst->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height;
                    uint32 width_half = pstGuiImage_src->Width >> 1;
                    uint32 height_half = pstGuiImage_src->Height >> 1;

                    _i =  pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        for(j = 0; j < pstGuiImage_src->Width; j++)
                        {
                            p8dst[j][_i] = p8src[i][j];
                        }
                    }
                    _i = height_half - 1;
                    for(i = 0; i < height_half; i++, _i--)
                    {
                        for(j = 0; j < width_half; j++)
                        {
                            p16dst[j][_i] = p16src[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                case MPP_GUI_ROTATE_180: {
                    uint8 (*p8src)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint8 (*p8dst)[pstGuiImage_src->Width]  = pstGuiImage_dst->pbmp;
                    uint16 (*p16src)[pstGuiImage_src->Width >> 1] = pstGuiImage_src->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height;
                    uint16 (*p16dst)[pstGuiImage_src->Width >> 1] = pstGuiImage_dst->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height;
                    uint32 width_half = pstGuiImage_src->Width >> 1;
                    uint32 height_half = pstGuiImage_src->Height >> 1;
                    _i = pstGuiImage_src->Height - 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++, _i--)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            p8dst[_i][_j] = p8src[i][j];
                        }
                    }
                    _i = height_half - 1;
                    for(i = 0; i < height_half; i++, _i--)
                    {
                        _j = width_half - 1;
                        for(j = 0; j < width_half; j++, _j--)
                        {
                            p16dst[_i][_j] = p16src[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                }   break;
                case MPP_GUI_ROTATE_270: {
                    uint8 (*p8src)[pstGuiImage_src->Width] = pstGuiImage_src->pbmp;
                    uint8 (*p8dst)[pstGuiImage_src->Height]  = pstGuiImage_dst->pbmp;
                    uint16 (*p16src)[pstGuiImage_src->Width >> 1] = pstGuiImage_src->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height;
                    uint16 (*p16dst)[pstGuiImage_src->Height >> 1] = pstGuiImage_dst->pbmp + pstGuiImage_src->Width * pstGuiImage_src->Height;
                    uint32 width_half = pstGuiImage_src->Width >> 1;
                    uint32 height_half = pstGuiImage_src->Height >> 1;
                    for(i = 0; i < pstGuiImage_src->Height; i++)
                    {
                        _j = pstGuiImage_src->Width - 1;
                        for(j = 0; j < pstGuiImage_src->Width; j++, _j--)
                        {
                            p8dst[_j][i] = p8src[i][j];
                        }
                    }
                    for(i = 0; i < height_half; i++)
                    {
                        _j = width_half - 1;
                        for(j = 0; j < width_half; j++, _j--)
                        {
                            p16dst[_j][i] = p16src[i][j];
                        }
                    }
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;
                }   break;
                default:
                    printf("cannot detect enAngle:%d\n", enAngle);
                    return SV_FAILURE;
            }
            break;
        case MPP_GUI_FMT_YUYV:
            pstGuiImage_dst->format = pstGuiImage_src->format;
            pstGuiImage_dst->pbmp = malloc(ALIGN2(pstGuiImage_src->Width) * ALIGN2(pstGuiImage_src->Height) * 2);
            switch (enAngle){
                case MPP_GUI_ROTATE_0:
                    memcpy(pstGuiImage_dst->pbmp, pstGuiImage_src->pbmp, pstGuiImage_src->Width * pstGuiImage_src->Height * 2);
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;
                    break;
                case MPP_GUI_ROTATE_90:{
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;

                    const int copyBytes = 4;
                    const int bytesPerLine = pstGuiImage_src->Width << 1;
                    const int step = pstGuiImage_src->Height << 2;
                    const int offset = (pstGuiImage_src->Height - 1) * bytesPerLine;
                    unsigned char *dst = pstGuiImage_dst->pbmp;
                    unsigned char *src = pstGuiImage_src->pbmp;

                    unsigned char *dest = dst;
                    unsigned char *source = src;
                    unsigned char *psrc = NULL;
                    unsigned char *pdst[2] = {NULL, NULL};
					int i,j,k;
                    for ( i = 0; i < bytesPerLine; i += copyBytes)
                    {
                        pdst[0] = dest;
                        pdst[1] = dest   + (pstGuiImage_src->Height << 1);
                        psrc    = source + offset;

                        for ( j = 0; j < pstGuiImage_src->Height; ++j)
                        {
                             k = j % 2;
                            // 拷贝4个字节
                            *((unsigned int *)pdst[k]) = *((unsigned int *)psrc);
                            // Y分量交换，保证每个像素点的亮度不改变否则产生锯齿
                            if(1 == k)
                            {
                                unsigned char temp = *(pdst[0] - 2);
                                *(pdst[0] - 2) = *(pdst[1]);
                                *(pdst[1]) = temp;
                            }
                            pdst[k] += copyBytes;
                            psrc    -= bytesPerLine;
                        }
                        dest   += step;
                        source += copyBytes;
                    }
                }   break;
                case MPP_GUI_ROTATE_180:{
                    pstGuiImage_dst->Width = pstGuiImage_src->Width;
                    pstGuiImage_dst->Height = pstGuiImage_src->Height;

                    const int copyBytes  = 4;
                    const int totalBytes = pstGuiImage_src->Height * pstGuiImage_src->Width << 1;
                    const int end        = totalBytes;
                    unsigned char *dst   = pstGuiImage_dst->pbmp + totalBytes - copyBytes;
                    unsigned char *src   = pstGuiImage_src->pbmp;
					int i,j,k;
                    for ( i = 0; i < end; i += copyBytes)
                    {
                        *((unsigned int *)dst) = *((unsigned int *)src);
                        // 交换Y分量
                        unsigned char temp = dst[0];
                        dst[0] = dst[2];
                        dst[2] = temp;

                        dst -= copyBytes;
                        src += copyBytes;
                    }
                }   break;
                case MPP_GUI_ROTATE_270:{
                    pstGuiImage_dst->Width = pstGuiImage_src->Height;
                    pstGuiImage_dst->Height = pstGuiImage_src->Width;

                    const int copyBytes = 4;
                    const int bytesPerLine = pstGuiImage_src->Width << 1;
                    const int step = pstGuiImage_src->Height << 2;
                    const int offset = bytesPerLine - copyBytes;
                    unsigned char *dst = pstGuiImage_dst->pbmp;
                    unsigned char *src = pstGuiImage_src->pbmp;

                    unsigned char *dest = dst;
                    unsigned char *source = src;
                    unsigned char *psrc = NULL;
                    unsigned char *pdst[2] = {NULL, NULL};
					int i,j,k;

                    for ( i = 0; i < bytesPerLine; i += copyBytes)
                    {
                        pdst[0] = dest;
                        pdst[1] = dest   + (pstGuiImage_src->Height << 1);
                        psrc    = source + offset;

                        for ( j = 0; j < pstGuiImage_src->Height; ++j)
                        {
                             k = j % 2;
                            // 拷贝4个字节
                            *((unsigned int *)pdst[k]) = *((unsigned int *)psrc);
                            // Y分量交换，保证每个像素点的亮度不改变否则产生锯齿
                            if(1 == k)
                            {
                                unsigned char temp = *(pdst[0] - 2);
                                *(pdst[0] - 2) = *(pdst[1]);
                                *(pdst[1]) = temp;
                            }
                            pdst[k] += copyBytes;
                            psrc    += bytesPerLine;
                        }
                        dest   += step;
                        source -= copyBytes;
                    }
                }   break;
                default:
                    return SV_FAILURE;
            }
            break;
        case MPP_GUI_FMT_UYVY:
            pstGuiImage_dst->format = pstGuiImage_src->format;
            pstGuiImage_dst->pbmp = malloc(ALIGN2(pstGuiImage_src->Width) * ALIGN2(pstGuiImage_src->Height) * 2);
            switch (enAngle){
                case MPP_GUI_ROTATE_0:
                    memcpy(pstGuiImage_dst->pbmp, pstGuiImage_src->pbmp, pstGuiImage_src->Width * pstGuiImage_src->Height * 2);
                    pstGuiImage_dst->Width = ALIGN2(pstGuiImage_src->Width);
                    pstGuiImage_dst->Height = ALIGN2(pstGuiImage_src->Height);
                    break;
                case MPP_GUI_ROTATE_90:{
                    pstGuiImage_dst->Width = ALIGN2(pstGuiImage_src->Height);
                    pstGuiImage_dst->Height = ALIGN2(pstGuiImage_src->Width);

                    const int copyBytes = 4;
                    const int bytesPerLine = pstGuiImage_src->Width << 1;
                    const int step = pstGuiImage_src->Height << 2;
                    const int offset = (pstGuiImage_src->Height - 1) * bytesPerLine;
                    unsigned char *dst = pstGuiImage_dst->pbmp;
                    unsigned char *src = pstGuiImage_src->pbmp;

                    unsigned char *dest = dst;
                    unsigned char *source = src;
                    unsigned char *psrc = NULL;
                    unsigned char *pdst[2] = {NULL, NULL};
					int i,j,k;
                    for ( i = 0; i < bytesPerLine; i += copyBytes)
                    {
                        pdst[0] = dest;
                        pdst[1] = dest   + (pstGuiImage_src->Height << 1);
                        psrc    = source + offset;

                        for (j = 0; j < pstGuiImage_src->Height; ++j)
                        {
                             k = j % 2;
                            // 拷贝4个字节
                            *((unsigned int *)pdst[k]) = *((unsigned int *)psrc);
                            // Y分量交换，保证每个像素点的亮度不改变否则产生锯齿
                            if(1 == k)
                            {
                                unsigned char temp = *(pdst[0] - 1);
                                *(pdst[0] - 1) = *(pdst[1] + 1);
                                *(pdst[1] + 1) = temp;
                            }
                            pdst[k] += copyBytes;
                            psrc    -= bytesPerLine;
                        }
                        dest   += step;
                        source += copyBytes;
                    }
                }   break;
                case MPP_GUI_ROTATE_180:{
                    pstGuiImage_dst->Width = ALIGN2(pstGuiImage_src->Width);
                    pstGuiImage_dst->Height = ALIGN2(pstGuiImage_src->Height);

                    const int copyBytes  = 4;
                    const int totalBytes = pstGuiImage_src->Height * pstGuiImage_src->Width << 1;
                    const int end        = totalBytes;
                    unsigned char *dst   = pstGuiImage_dst->pbmp + totalBytes - copyBytes;
                    unsigned char *src   = pstGuiImage_src->pbmp;
					int i,j,k;

                    for ( i = 0; i < end; i += copyBytes)
                    {
                        *((unsigned int *)dst) = *((unsigned int *)src);
                        // 交换Y分量
                        unsigned char temp = dst[1];
                        dst[1] = dst[3];
                        dst[3] = temp;

                        dst -= copyBytes;
                        src += copyBytes;
                    }
                }   break;
                case MPP_GUI_ROTATE_270:{
                    pstGuiImage_dst->Width = ALIGN2(pstGuiImage_src->Height);
                    pstGuiImage_dst->Height = ALIGN2(pstGuiImage_src->Width);

                    const int copyBytes = 4;
                    const int bytesPerLine = pstGuiImage_src->Width << 1;
                    const int step = pstGuiImage_src->Height << 2;
                    const int offset = bytesPerLine - copyBytes;
                    unsigned char *dst = pstGuiImage_dst->pbmp;
                    unsigned char *src = pstGuiImage_src->pbmp;

                    unsigned char *dest = dst;
                    unsigned char *source = src;
                    unsigned char *psrc = NULL;
                    unsigned char *pdst[2] = {NULL, NULL};
					int i,j,k;
                    for ( i = 0; i < bytesPerLine; i += copyBytes)
                    {
                        pdst[0] = dest;
                        pdst[1] = dest   + (pstGuiImage_src->Height << 1);
                        psrc    = source + offset;

                        for ( j = 0; j < pstGuiImage_src->Height; ++j)
                        {
                             k = j % 2;
                            // 拷贝4个字节
                            *((unsigned int *)pdst[k]) = *((unsigned int *)psrc);
                            // Y分量交换，保证每个像素点的亮度不改变否则产生锯齿
                            if(1 == k)
                            {
                                unsigned char temp = *(pdst[0] - 1);
                                *(pdst[0] - 1) = *(pdst[1] + 1);
                                *(pdst[1] + 1) = temp;
                            }
                            pdst[k] += copyBytes;
                            psrc    += bytesPerLine;
                        }
                        dest   += step;
                        source -= copyBytes;
                    }
                }   break;
                default:
                    return SV_FAILURE;
            }
            break;
    }
#endif
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 图像格式转换
 * 输入参数: pstDestImage --- 目的图像
             pstSrcImage  --- 源图像
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_Switch_Format(MPP_GUI_IMAGE_S *pstDestImage, MPP_GUI_IMAGE_S *pstSrcImage)
{
    char buf[4];
    sint32 i,j;
    uint32 w,h,color_type, channels;
    uint8 a,r,g,b,y,u,v,format, balpha = 1;
    MPP_GUI_FORMAT format_src, format_dst;
    uint8 *pBufSrc, *pYBufSrc, *pUVBufSrc;
    uint8 *pBufDst, *pYBufDst, *pUVBufDst;
    uint16 u16color;

    if(NULL == pstSrcImage)
    {
        printf("pstGuiImage_src=NULL\n");
        return SV_FAILURE;
    }

    if(pstSrcImage->pbmp == NULL)
    {
        printf("pstSrcImage->pbmp cannot be NULL\n");
        return SV_FAILURE;
    }

    if(NULL == pstDestImage)
    {
        printf("pstDestImage=NULL\n");
        return SV_FAILURE;
    }

    switch(pstSrcImage->format){
        case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_UYVY:
        case MPP_GUI_FMT_YUYV:
        case MPP_GUI_FMT_RGB_888:

        case MPP_GUI_FMT_ARGB_8888:

        case MPP_GUI_FMT_YUV_420SP:
            break;
        default:
            printf("pstSrcImage format not supported!\n");
            return SV_FAILURE;
    }

    switch(pstDestImage->format){
        case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_UYVY:
        case MPP_GUI_FMT_YUYV:
        case MPP_GUI_FMT_RGB_888:

        case MPP_GUI_FMT_ARGB_8888:

        case MPP_GUI_FMT_YUV_420SP:
            break;
        default:
            printf("pstDestImage format not supported!\n");
            return SV_FAILURE;
    }


    w = pstSrcImage->Width;
    h = pstSrcImage->Height;
    if (pstDestImage->pbmp == NULL)
    {
        switch(pstDestImage->format)
        {
            case MPP_GUI_FMT_BGR_565:
            case MPP_GUI_FMT_RGB_565:
            case MPP_GUI_FMT_ARGB_1555:
            case MPP_GUI_FMT_UYVY:
            case MPP_GUI_FMT_YUYV:
                pstDestImage->pbmp = malloc(w*h*2);
                break;
            case MPP_GUI_FMT_RGB_888:

                pstDestImage->pbmp = malloc(w*h*3);
                break;
            case MPP_GUI_FMT_ARGB_8888:

                pstDestImage->pbmp = malloc(w*h*4);
                break;
            case MPP_GUI_FMT_YUV_420SP:
                //pstDestImage->pbmp = malloc((w*h*3)/2);
                pstDestImage->pbmp = malloc(w*h*4);
                break;
            default:
                printf("pstDestImage format not supported!\n");
                return SV_FAILURE;
        }
    }

    pstDestImage->Width = pstSrcImage->Width;
    pstDestImage->Height = pstSrcImage->Height;

    format_src = pstSrcImage->format;
    format_dst = pstDestImage->format;
    pBufSrc = pstSrcImage->pbmp;
    pBufDst = pstDestImage->pbmp;
    pYBufSrc  = pstSrcImage->pbmp;
    pYBufDst  = pstDestImage->pbmp;
    pUVBufSrc = pstSrcImage->pbmp + w * h;
    pUVBufDst = pstDestImage->pbmp + w * h;
    for(i = 0; i < h; i++)
    {
        for(j = 0; j < w; j++)
        {
            //print_level(SV_INFO, "i%d, j%d\n", i, j);
            //print_level(SV_INFO, "0x%x, 0x%x\n", pBufSrc, pYBufDst);
            switch(format_src)
            {
                case MPP_GUI_FMT_ARGB_1555:
                    u16color = *((uint16*)pBufSrc);
                    a = ((u16color >> 15) & 0x01) ? 0xff : 0x00;
                    r = ((u16color >> 10) & 0x1f) << 3;
                    g = ((u16color >> 5) & 0x1f) << 3;
                    b = ((u16color) & 0x1f) << 3;
                    pBufSrc += 2;
                    break;
                case MPP_GUI_FMT_RGB_565:
                    u16color = *((uint16*)pBufSrc);
                    a = 0xff;
                    r = ((u16color >> 11) & 0x1f) << 3;
                    g = ((u16color >> 5) & 0x3f) << 2;
                    b = ((u16color) & 0x1f) << 3;
                    pBufSrc += 2;
                    break;
                case MPP_GUI_FMT_BGR_565:
                    u16color = *((uint16*)pBufSrc);
                    a = 0xff;
                    b = ((u16color >> 11) & 0x1f) << 3;
                    g = ((u16color >> 5) & 0x3f) << 2;
                    r = ((u16color) & 0x1f) << 3;
                    pBufSrc += 2;
                    break;
                case MPP_GUI_FMT_RGB_888:
                    a = 0xff;
                    r = *(pBufSrc + 2);
                    g = *(pBufSrc + 1);
                    b = *(pBufSrc + 0);
                    pBufSrc += 3;
                    break;
                case MPP_GUI_FMT_ARGB_8888:
                    a = *(pBufSrc + 3);
                    r = *(pBufSrc + 2);
                    g = *(pBufSrc + 1);
                    b = *(pBufSrc + 0);
                    pBufSrc += 4;
                    break;
                case MPP_GUI_FMT_YUV_420SP:
                    y = *pYBufSrc++;
                    if(j % 2 == 0)
                    {
                        u = *pUVBufSrc++;
                        v = *pUVBufSrc++;
                    }
                    a = 0xff;
                    r = max(0, min(255, (298*y + 411 * v - 57344)>>8));
                    g = max(0, min(255, (298*y - 101* u - 211* v+ 34739)>>8));
                    b = max(0, min(255, (298*y + 519* u- 71117)>>8));
                    break;
                case MPP_GUI_FMT_YUYV:
                    y = *pBufSrc++;
                    if(j % 2 == 0)
                    {
                        u = pBufSrc[1];
                        v = pBufSrc[3];
                    }
                    else
                    {
                        u = pBufSrc[-1];
                        v = pBufSrc[1];
                    }
                    pBufSrc += 2;
                    a = 0xff;
                    r = max(0, min(255, (298*y + 411 * v - 57344)>>8));
                    g = max(0, min(255, (298*y - 101* u - 211* v+ 34739)>>8));
                    b = max(0, min(255, (298*y + 519* u- 71117)>>8));
                    break;
                case MPP_GUI_FMT_UYVY:
                    y = pBufSrc[1];
                    if(j % 2 == 0)
                    {
                        u = pBufSrc[0];
                        v = pBufSrc[2];
                    }
                    else
                    {
                        u = pBufSrc[-2];
                        v = pBufSrc[0];
                    }
                    pBufSrc += 2;
                    a = 0xff;
                    r = max(0, min(255, (298*y + 411 * v - 57344)>>8));
                    g = max(0, min(255, (298*y - 101* u - 211* v+ 34739)>>8));
                    b = max(0, min(255, (298*y + 519* u- 71117)>>8));
                    break;
            }

            switch(format_dst)
            {
                case MPP_GUI_FMT_ARGB_1555:
                    *((uint16*)pBufDst) = (((a >> 7) & 0x1) << 15) | \
                                          (((r >> 3) & 0x1f) << 10) | \
                                          (((g >> 3) & 0x1f) << 5) | \
                                          (((b >> 3) & 0x1f));
                    pBufDst += 2;
                    break;
                case MPP_GUI_FMT_RGB_565:
                    *((uint16*)pBufDst) = (((r >> 3) & 0x1f) << 11) | \
                                          (((g >> 2) & 0x3f) << 5) | \
                                          (((b >> 3) & 0x1f));
                    pBufDst += 2;
                    break;
                case MPP_GUI_FMT_BGR_565:
                    *((uint16*)pBufDst) = (((b >> 3) & 0x1f) << 11) | \
                                          (((g >> 2) & 0x3f) << 5) | \
                                          (((r >> 3) & 0x1f));
                    pBufDst += 2;
                    break;
                case MPP_GUI_FMT_RGB_888:
                    *(pBufDst + 2) = r;
                    *(pBufDst + 1) = g;
                    *(pBufDst + 0) = b;
                    pBufDst += 3;
                    break;
                case MPP_GUI_FMT_ARGB_8888:
                    *(pBufDst + 3) = a;
                    *(pBufDst + 2) = r;
                    *(pBufDst + 1) = g;
                    *(pBufDst + 0) = b;
                    pBufDst += 4;
                    break;
                case MPP_GUI_FMT_YUYV:
                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    if(y < 20 || y > 230)
                    {
                        *pBufDst++ = 0x00;
                        *pBufDst++ = 0x00;
                        break;
                    }

                    *pBufDst++ = y;
                    if(j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        *pBufDst++ = u;
                    }
                    else
                    {
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        *pBufDst++ = v;
                    }
                    break;
                case MPP_GUI_FMT_UYVY:
                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    if(y < 20 || y > 230)
                        y = 0x00;

                    if(j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        if(y < 20 || y > 230)
                            u = 0x00;
                        *pBufDst++ = u;
                    }
                    else
                    {
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        if(y < 20 || y > 230)
                            v = 0x00;
                        *pBufDst++ = v;
                    }

                    *pBufDst++ = y;
                    break;
                case MPP_GUI_FMT_YUV_420SP:
                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    if(y < 20 || y > 230)
                    {
                        y = 0x00;
                    }
                    *pYBufDst++ = y;

                    if(i % 2 == 0 && j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        if(y < 20 || y > 230)
                        {
                            u = 0x00;
                            v = 0x00;
                        }
                        *pUVBufDst++ = u;
                        *pUVBufDst++ = v;
                    }
                    break;
            }

        }
    }

    return SV_SUCCESS;
}



/******************************************************************************
 * 函数功能: 获取YUV对应的颜色值
 * 输入参数: color 32位argb颜色
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_COLOR MPP_GUI_GetYuvColor(int color)
{
	MPP_GUI_COLOR stcolor;
	int r, g, b;

    r = (color>>16) & 0xff;
    g = (color>>8) & 0xff;
    b = (color) & 0xff;
    stcolor.u8yuv[0] =  ((66*r + 129*g + 25*b) >> 8) + 16;
    stcolor.u8yuv[1] =  ((-38*r - 74*g  + 112*b)>>8) + 128;
    stcolor.u8yuv[2] =  ((112*r - 94*g - 18*b)>>8) + 128;

	return stcolor;
}

/******************************************************************************
 * 函数功能: 获取RGB对应的颜色值
 * 输入参数: color 32位argb颜色
 * 输出参数: 无
 * 返回值  : YUV对应的颜色值
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_COLOR MPP_GUI_GetRGBColor(int color)
{
	MPP_GUI_COLOR stcolor;
	stcolor.u24color = (color & 0x00ffffff);
	return stcolor;
}

/******************************************************************************
 * 函数功能: 获取颜色值
 * 输入参数: color 32位argb颜色
 * 输出参数: 无
 * 返回值  : YUV对应的颜色值
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_COLOR MPP_GUI_GetColor(MPP_GUI_FORMAT format, int color)
{
	int r, g, b;
	MPP_GUI_COLOR stcolor;
	if(format == MPP_GUI_FMT_ARGB_1555)
	{
	    r = (color>>16) & 0xff;
	    g = (color>>8) & 0xff;
	    b = (color) & 0xff;
		stcolor.u16color = ((0x1) | \
                              (((r >> 3) & 0x1f) << 10) | \
                              (((g >> 3) & 0x1f) << 5) | \
                              (((b >> 3) & 0x1f)));
	}
	else if(format == MPP_GUI_FMT_RGB_565)
	{
		r = (color>>16) & 0xff;
		g = (color>>8) & 0xff;
		b = (color) & 0xff;
		stcolor.u16color = (((r >> 3) & 0x1f) << 11) | \
						  (((g >> 2) & 0x3f) << 5) | \
						  (((b >> 3) & 0x1f));
	}
	else if(format == MPP_GUI_FMT_BGR_565)
	{
		r = (color>>16) & 0xff;
		g = (color>>8) & 0xff;
		b = (color) & 0xff;
		stcolor.u16color = (((b >> 3) & 0x1f) << 11) | \
						  (((g >> 2) & 0x3f) << 5) | \
						  (((r >> 3) & 0x1f));
	}
	else if(format == MPP_GUI_FMT_RGB_888)
	{
		stcolor.u24color = (color & 0x00ffffff);
	}
	else if(format == MPP_GUI_FMT_ARGB_8888)
	{
		stcolor.u32color = color;
	}
	else if(format == MPP_GUI_FMT_YUV_420SP || format == MPP_GUI_FMT_YUYV || format == MPP_GUI_FMT_UYVY)
	{
	    r = (color>>16) & 0xff;
	    g = (color>>8) & 0xff;
	    b = (color) & 0xff;
	    stcolor.u8yuv[0] =  ((66*r + 129*g + 25*b) >> 8) + 16;
	    stcolor.u8yuv[1] =  ((-38*r - 74*g  + 112*b)>>8) + 128;
	    stcolor.u8yuv[2] =  ((112*r - 94*g - 18*b)>>8) + 128;
	}
	return stcolor;
}

/******************************************************************************
 * 函数功能: 获取图像类型的结构体
 * 输入参数: pbmp           --- 图像指针
             u32Width   --- 宽度
             u32Height  --- 高度
             format     --- 图像格式
 * 输出参数: 无
 * 返回值  : 图像类型的结构体
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_IMAGE_S MPP_GUI_GetImage(void *pbmp, uint32 u32Width, uint32 u32Height, MPP_GUI_FORMAT format)
{
    MPP_GUI_IMAGE_S stGuiImage;
    stGuiImage.pbmp = pbmp;
    stGuiImage.Width = u32Width;
    stGuiImage.Height = u32Height;
    stGuiImage.format = format;
    return stGuiImage;
}

/******************************************************************************
 * 函数功能: 获取图像占用的内存空间大小
 * 输入参数: 图像类型的结构体
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
int MPP_GUI_GetSize(MPP_GUI_IMAGE_S *pstGuiImage)
{
    int size = 0;
    switch(pstGuiImage->format)
    {
        case MPP_GUI_FMT_RGB_8BPP:
            size = pstGuiImage->Height * pstGuiImage->Width;
            break;
        case MPP_GUI_FMT_ARGB_1555:
        case MPP_GUI_FMT_RGB_565:
        case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_UYVY:
        case MPP_GUI_FMT_YUYV:
            size = (pstGuiImage->Height * pstGuiImage->Width) << 1;
            break;
        case MPP_GUI_FMT_YUV_420SP:
            size = (pstGuiImage->Height * pstGuiImage->Width * 3) >> 1;
            break;
        case MPP_GUI_FMT_RGB_888:
            size = (pstGuiImage->Height * pstGuiImage->Width * 3);
            break;
        case MPP_GUI_FMT_ARGB_8888:
            size = (pstGuiImage->Height * pstGuiImage->Width) << 2;
            break;
        /* 默认用最大来计算 */
        default:
            size = (pstGuiImage->Height * pstGuiImage->Width) << 1;
            break;
    }
    return size;
}

/******************************************************************************
 * 函数功能: 获取旋转后的圆(椭圆)位置
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
MPP_GUI_CIRCLE_PART MPP_GUI_GetRotatePart(MPP_GUI_CIRCLE_PART enPart, MPP_GUI_ROTATE_ANGLE angle)
{
    /* 映射表 */
    static MPP_GUI_CIRCLE_PART table[MPP_GUI_ROTATE_BUTT][MPP_GUI_CIRCLE_BUTT] ={
        {MPP_GUI_CIRCLE_TOTAL, MPP_GUI_CIRCLE_UP, MPP_GUI_CIRCLE_DOWN, MPP_GUI_CIRCLE_LEFT, MPP_GUI_CIRCLE_RIGHT},    /* MPP_GUI_ROTATE_0 */
        {MPP_GUI_CIRCLE_TOTAL, MPP_GUI_CIRCLE_RIGHT, MPP_GUI_CIRCLE_LEFT, MPP_GUI_CIRCLE_DOWN, MPP_GUI_CIRCLE_UP},    /* MPP_GUI_ROTATE_90 */
        {MPP_GUI_CIRCLE_TOTAL, MPP_GUI_CIRCLE_DOWN, MPP_GUI_CIRCLE_UP, MPP_GUI_CIRCLE_RIGHT, MPP_GUI_CIRCLE_LEFT},    /* MPP_GUI_ROTATE_180 */
        {MPP_GUI_CIRCLE_TOTAL, MPP_GUI_CIRCLE_LEFT, MPP_GUI_CIRCLE_RIGHT, MPP_GUI_CIRCLE_UP, MPP_GUI_CIRCLE_DOWN},    /* MPP_GUI_ROTATE_270 */
    };
    /* MPP_GUI_CIRCLE_TOTAL MPP_GUI_CIRCLE_UP MPP_GUI_CIRCLE_DOWN MPP_GUI_CIRCLE_LEFT MPP_GUI_CIRCLE_RIGHT */


    if(enPart >= MPP_GUI_CIRCLE_BUTT || angle >= MPP_GUI_ROTATE_BUTT)
        return enPart;

    return table[angle][enPart];
}



/******************************************************************************
 * 函数功能: 读取JPEG图片
 * 输入参数: pstPaintImage  --- 图像信息
            filename       --- 文件路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_LOAD_JPEG(MPP_GUI_IMAGE_S *pstGuiImage, const char *filename)
{
    struct jpeg_decompress_struct cinfo;
    struct jpeg_error_mgr jerr;
    FILE* pFile;

    uint32 i, j, w_src, h_src, w_dst, h_dst, byte;
    uint8 a, r, g, b, format;
    uint8 y, u, v;
    uint8 *pOrigJPEGBuf, *pRGBBuf, *pYBuf, *pUVBuf, *pYUYVBuf, *pUYVYBuf, *pStart;
    uint16 *pDst;
    uint32 stride, u32readn;
    uint16 u16color;

    if ((pFile = fopen(filename, "rb")) == NULL)
    {
        printf("open %s failed\n", filename);
        return SV_SUCCESS;
    }

    /*
    * init jpeg decompress object error handler
    */
    cinfo.err = jpeg_std_error(&jerr);
    jpeg_create_decompress(&cinfo);

    /*
    * bind jpeg decompress object to infile
    */
    jpeg_stdio_src(&cinfo, pFile);
    /*
    * read jpeg header
    */
    jpeg_read_header(&cinfo, TRUE);
    switch (pstGuiImage->format){
        case MPP_GUI_FMT_ARGB_1555:
		case MPP_GUI_FMT_RGB_565:
		case MPP_GUI_FMT_BGR_565:
        case MPP_GUI_FMT_RGB_888:
        case MPP_GUI_FMT_ARGB_8888:
        case MPP_GUI_FMT_YUYV:
        case MPP_GUI_FMT_UYVY:
        case MPP_GUI_FMT_YUV_420SP:
            format = pstGuiImage->format;
            break;
        default:
            printf("GuiImage format[%d] not supported!\n", pstGuiImage->format);
            return SV_FAILURE;
    }

    w_src = cinfo.image_width;
    h_src = cinfo.image_height;
    byte = 3;
    stride = w_src * byte;
    //yuv420sp必须长宽都为偶数，向下取偶可以方便程序编写
    w_dst = pstGuiImage->format == MPP_GUI_FMT_YUV_420SP ? (w_src & 0xfffe) : w_src;
    h_dst = pstGuiImage->format == MPP_GUI_FMT_YUV_420SP ? (h_src & 0xfffe) : h_src;

    pstGuiImage->Width = w_dst;
    pstGuiImage->Height = h_dst;
    if (pstGuiImage->pbmp == NULL)
    {
        switch (format){
            case MPP_GUI_FMT_ARGB_1555:
            case MPP_GUI_FMT_RGB_565:
            case MPP_GUI_FMT_BGR_565:
            case MPP_GUI_FMT_YUYV:
            case MPP_GUI_FMT_UYVY:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 2);
                break;
            case MPP_GUI_FMT_RGB_888:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 3);
                break;
            case MPP_GUI_FMT_ARGB_8888:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 4);
                break;
            case MPP_GUI_FMT_YUV_420SP:
                pstGuiImage->pbmp = malloc(w_dst * h_dst * 3 / 2 );
                break;
        }
    }

    /*
    * decompress process.
    * note: after jpeg_start_decompress() is called
    * the dimension infomation will be known,
    * so allocate memory buffer for scanline immediately
    */

    jpeg_start_decompress(&cinfo);
    pRGBBuf = pstGuiImage->pbmp;
    pYBuf  = pstGuiImage->pbmp;
    pUVBuf = pstGuiImage->pbmp + w_dst * h_dst;
    pYUYVBuf = pstGuiImage->pbmp;
    pOrigJPEGBuf = (unsigned char *)malloc(cinfo.output_width * cinfo.output_components);
    pStart = pOrigJPEGBuf;
    for(i = 0; i < h_dst; i++)
    {
        pStart = pOrigJPEGBuf;
        jpeg_read_scanlines(&cinfo, &pStart, 1);
        for(j = 0; j < w_dst; j++)
        {
            a = 0xff;
            r = *(pStart++);
            g = *(pStart++);
            b = *(pStart++);

            switch(format){
                case MPP_GUI_FMT_ARGB_1555:
                    *((uint16*)pRGBBuf) = (((a >> 7) & 0x1) << 15) | \
                                          (((r >> 3) & 0x1f) << 10) | \
                                          (((g >> 3) & 0x1f) << 5) | \
                                          (((b >> 3) & 0x1f));
                    pRGBBuf += 2;
                    break;
				case MPP_GUI_FMT_RGB_565:
					*((uint16*)pRGBBuf) = (((r >> 3) & 0x1f) << 11) | \
										  (((g >> 2) & 0x3f) << 5) | \
										  (((b >> 3) & 0x1f));
					pRGBBuf += 2;
					break;
				case MPP_GUI_FMT_BGR_565:
					*((uint16*)pRGBBuf) = (((b >> 3) & 0x1f) << 11) | \
										  (((g >> 2) & 0x3f) << 5) | \
										  (((r >> 3) & 0x1f));
					pRGBBuf += 2;
					break;
                case MPP_GUI_FMT_RGB_888:
                    *(pRGBBuf + 2) = r;
                    *(pRGBBuf + 1) = g;
                    *(pRGBBuf + 0) = b;
                    pRGBBuf += 3;
                    break;
                case MPP_GUI_FMT_ARGB_8888:
                    *(pRGBBuf + 3) = a;
                    *(pRGBBuf + 2) = r;
                    *(pRGBBuf + 1) = g;
                    *(pRGBBuf + 0) = b;
                    pRGBBuf += 4;
                    break;
                case MPP_GUI_FMT_YUYV:
                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    *(pYUYVBuf++) = y;
                    if(j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        *(pYUYVBuf++) = u;
                    }
                    else
                    {
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        *(pYUYVBuf++) = v;
                    }
                    break;
                case MPP_GUI_FMT_UYVY:
                    if(j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        *(pYUYVBuf++) = u;
                    }
                    else
                    {
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        *(pYUYVBuf++) = v;
                    }
                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    *(pYUYVBuf++) = y;
                    break;
                case MPP_GUI_FMT_YUV_420SP:
                    y = ((66*r + 129*g + 25*b) >> 8) + 16;
                    *pYBuf++ = y;
                    if(i % 2 == 0 && j % 2 == 0)
                    {
                        u = ((-38*r - 74*g  + 112*b)>>8) + 128;
                        v = ((112*r - 94*g - 18*b)>>8) + 128;
                        *pUVBuf++ = u;
                        *pUVBuf++ = v;
                    }
                    break;
            }
        }
    }

    /*
    * finish decompress, destroy decompress object
    */
    jpeg_finish_decompress(&cinfo);
    jpeg_destroy_decompress(&cinfo);

    fclose(pFile);
    free(pOrigJPEGBuf);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 保存JPEG图片
 * 输入参数: pstPaintImage  --- 图像信息
            filename       --- 文件路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 MPP_GUI_SAVE_JPEG(MPP_GUI_IMAGE_S *pstGuiImage, const char *filename)
{
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 将行人算法检测数据转化为YUV叠加信息(YUV协议)
 * 输入参数:YUV_Y              --- Y Buffer
            pstGuiPerson   --- 行人数据
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : Y Buffer 需要足够大
 *****************************************************************************/
sint32 MPP_GUI_YUV_IMPROTOCOL(void *YUV_Y, MPP_GUI_PERSON_S *pstGuiPerson)
{
    sint32 s32Ret, i, j;
    SV_stAICameraSendBuff_S ai_buff = {0};
    SV_stTargetInfo_S *pstTarget = ai_buff.AiCameraInfo.aStTarget;
    static int index =0;
    static struct timeval pretime = {0}, nowtime = {0};
    uint8 len = 0;
    int bskip = 0;
    int total_len = 0;
    int check_len = 0;
    sint32 s32Check;
    uint16 u16Bit[2];
    uint32 u32Bit[2];
    float fXOffset = 0.0;

#if (BOARD == BOARD_DMS31V2 || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR \
    || (BOARD == BOARD_ADA32N1) || BOARD == BOARD_ADA47V1 || (BOARD == BOARD_ADA32C4) || BOARD == BOARD_ADA32E1)

    if(YUV_Y == NULL || pstGuiPerson == NULL)
    {
        return SV_FAILURE;
    }

    #if PLATFORM == PLATFORM_RV1126
        len = min(pstGuiPerson->num, MAX_TARGET);
    #elif PLATFORM == PLATFORM_RV1106
        len = min(pstGuiPerson->num, MAX_TARGET);
    #else
        print_level(SV_ERROR, " platform is others !!! ... \n");
    #endif

    total_len = sizeof(SV_stAICameraInfo_S) - sizeof(SV_stTargetInfo_S)*(MAX_TARGET - len);
    check_len = total_len - sizeof(ai_buff.AiCameraInfo.u16StartCode) - sizeof(ai_buff.AiCameraInfo.u32CrcCode);

    ai_buff.u8ZeroSlicent = 0x00;
    ai_buff.AiCameraInfo.u16StartCode = AICAM_HEAD;
    ai_buff.AiCameraInfo.u8Factory = 0x01;
    ai_buff.AiCameraInfo.u8Hardware = 0x00;

    // print_level(SV_INFO, " 1-mpp_vosd_f_person_rect_add platform is RV1106... %d \n", sizeof(SV_stAICameraInfo_S));

    #if PLATFORM == PLATFORM_RV1126
        if(BOARD_IsSVersion(BOARD_S_H_1M45))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_1M45_1080;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_1M99))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_1M99_1080;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_V_2M3))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_2M3_1080;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_V_6M))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_6M_1080;
        }
        fXOffset = 0.0;
        // print_level(SV_ERROR, " RV1126 platform is %d !!! ... \n", ai_buff.AiCameraInfo.u8Hardware);
    #elif PLATFORM == PLATFORM_RV1106
        if(BOARD_IsSVersion(BOARD_S_H_1M45))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_1M45_720;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_1M99))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_1M99_720;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_2M3) || BOARD_IsSVersion(BOARD_S_H_2M8) || BOARD_IsSVersion(BOARD_S_V_2M3))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_2M3_720;
        }
        else if(BOARD_IsSVersion(BOARD_S_H_6M) || BOARD_IsSVersion(BOARD_S_V_6M))
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_6M_720;
        }
        else
        {
            ai_buff.AiCameraInfo.u8Hardware = HardWare_1M45_720;
        }
        fXOffset = 0.022;
    #else
         print_level(SV_ERROR, " platform is others !!! ... \n");
    #endif

    ai_buff.AiCameraInfo.u8Software = Software_ALG_2; // 新软件版本
    ai_buff.AiCameraInfo.u16Reserve = index++;//0x00;
    ai_buff.AiCameraInfo.u8TargetTotal = len;

    s32Ret = gettimeofday(&nowtime, NULL);
    if(nowtime.tv_sec - pretime.tv_sec >= 10)   /* 每隔10秒将ROI检测框信息发送 */
    {
        if(len < MAX_TARGET)
        {
            len++;
            total_len = sizeof(SV_stAICameraInfo_S) - sizeof(SV_stTargetInfo_S)*(MAX_TARGET - len);
            check_len = total_len - sizeof(ai_buff.AiCameraInfo.u16StartCode) - sizeof(ai_buff.AiCameraInfo.u32CrcCode);
            ai_buff.AiCameraInfo.u8TargetTotal = len;
        }
        else
        {
            bskip = 1;
        }

        pstTarget->stTargetAttr.type = OBJECT_TYPE_DETECT_BOX; /* ROI检测框 */
        pstTarget->stTopLeft.u16X = 0;
        pstTarget->stTopLeft.u16Y = 0;
        pstTarget->stBottomRight.u16X = 65535;
        pstTarget->stBottomRight.u16Y = 65535;
        pstTarget++;
        pretime = nowtime;
    }

    for(i = 0; i < len - bskip; i++)
    {
        pstTarget->stTargetAttr.type = 0x0; /* 行人检测框 */
        switch(pstGuiPerson->classes[i])
        {
            case PD_CLS_PERSON:
                pstTarget->stTargetAttr.type = OBJECT_TYPE_PERSON;
                break;
            case PD_CLS_CAR:
                pstTarget->stTargetAttr.type = OBJECT_TYPE_CAR;
                break;
            case PD_CLS_DMS_YUV:
                pstTarget->stTargetAttr.type = OBJECT_TYPE_DMS;
                break;
            default:
                pstTarget->stTargetAttr.type = OBJECT_TYPE_PERSON;
                break;
        }

        if (OBJECT_TYPE_DMS == pstTarget->stTargetAttr.type)
        {
            fXOffset = 0.0;
            pstTarget->stTopLeft.u16X = (uint16)(pstGuiPerson->stTopLeft[i].x);
        }else
        {
            pstTarget->stTargetAttr.confidence  = (uint8)(pstGuiPerson->confidence[i] * 16);
            pstTarget->stTopLeft.u16X           = (uint16)((pstGuiPerson->stTopLeft[i].x + fXOffset) * 65535);
            pstTarget->stTopLeft.u16Y           = (uint16)(pstGuiPerson->stTopLeft[i].y * 65535);
            pstTarget->stBottomRight.u16X       = (uint16)((pstGuiPerson->stBottomRight[i].x + fXOffset) * 65535);
            pstTarget->stBottomRight.u16Y       = (uint16)(pstGuiPerson->stBottomRight[i].y * 65535);
        }

        // print_level(SV_INFO, "class: %d, DMS type: %d, Alarm type: %d ...\n", pstGuiPerson->classes[i], pstTarget->stTargetAttr.type, pstTarget->stTopLeft.u16X);

        pstTarget++;
    }

    s32Check = crc32_check((char*)(&ai_buff.AiCameraInfo.u8Factory), (int)check_len);
    ai_buff.AiCameraInfo.u32CrcCode = s32Check;

    u16Bit[0] = 0x1010;    /* PIXEL0 */
    u16Bit[1] = 0xebeb;    /* PIXEL1 */

	u32Bit[0] = 0x00000000;/* PIXEL0 */
	u32Bit[1] = 0xffffffff;/* PIXEL1 */
#if 0
	printf("check_len:%d\n", check_len);
	char *buf_tmp = (char*)(&ai_buff.AiCameraInfo.u8Factory);
	for(i = 0; i < check_len; i++)
	{
		if(i % 16 == 0)
		{
			printf("\n");
		}
		printf("%02x ", buf_tmp[i]);
	}
	printf("\n");
	printf("s32Check:%d\n", s32Check);
#endif
    // 客户制定版本
    #if PLATFORM == PLATFORM_RV1126
        // 1-计算空间支持存放结构体数量
        /* 将数据转化为YUV420   表示         */
        {
            uint16 *dst = (uint16 *)YUV_Y;
            //uint32 *dst = (uint32 *)YUV_Y;
            uint8 *src = (uint8 *)&ai_buff;
            int bitidx;
            for(i = 0; i < total_len + sizeof(ai_buff.u8ZeroSlicent); i++)
            {
                for(j = 7 ; j > -1 ; j--)
                {
                    bitidx = ((*src)>>j)&0x1;
                    *(dst++) = u16Bit[bitidx];
                    //*(dst++) = u32Bit[bitidx];
                }
                src++;
            }
        }
    #elif PLATFORM == PLATFORM_RV1106
        // 1-计算空间支持存放结构体数量
        /* 将数据转化为RGB565 表示         */
        {
            uint32 *dst = (uint32 *)YUV_Y;
            //uint32 *dst = (uint32 *)YUV_Y;
            uint8 *src = (uint8 *)&ai_buff;
            int bitidx;
            for(i = 0; i < total_len + sizeof(ai_buff.u8ZeroSlicent); i++)
            {
                for(j = 7 ; j > -1 ; j--)
                {
                    bitidx = ((*src)>>j)&0x1;
                    //*(dst++) = u16Bit[bitidx];
                    *(dst++) = u32Bit[bitidx];
                }
                src++;
            }
        }
    #else
        print_level(SV_INFO, " platform is others... \n");
    #endif

#endif
    return SV_SUCCESS;
}