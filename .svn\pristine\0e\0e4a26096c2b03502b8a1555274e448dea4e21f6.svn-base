/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：network.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-12-18

文件功能描述: 定义网络模块功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <errno.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip_icmp.h>
#include <linux/netlink.h>
#include <linux/rtnetlink.h>
#include <linux/if.h>
#include <linux/ethtool.h>
#include <linux/sockios.h>
#include <regex.h>
#include <fcntl.h>
#include <unistd.h>

#include "common.h"
#include "uuid.h"
#include "print.h"
#include "../../include/board.h"
#include "safefunc.h"
#include "wifi.h"
#include "bluetooth.h"
#include "network.h"
#include "msg.h"
#include "utils.h"
#include "cjson/cJSON.h"

/* 模块控制信息 */
typedef struct tagNetworkInfo_S
{
    //SV_BOOL         bConfInit;        /* 是否为初始化 */
    SV_BOOL         bConfUpdate;        /* 是否有配置更新 */
    SV_BOOL         bConfDhcp;          /* Lan 配置是否使用DHCP */
	sint32   		s32DhcpTimeout;		/* Lan 使用DHCP分配ip超时时间 */
	SV_BOOL			bConfStatic;		/* Lan 配置是否使用静态IP */
    SV_BOOL         bSetMacXml;         /* 是否设置Mac地址到配置xml文件 */
    SV_BOOL         bEthAvailable;      /* Lan 网络是否可用 */
    SV_BOOL         bEth1Exist;         /* 外置网卡是否存在 */
    SV_BOOL         bResetChip;         /* 是否复位网卡 */
	SV_BOOL			bAutoTesting;		/* 是否正在进行自动化测试 */
	SV_BOOL			bInitFinished;      /* 首次运行网络初始化是否完成 */
    char            szConfMacAddr[32];  /* Lan 配置的MAC 地址 */
    char            szConfIpAddr[32];   /* Lan 配置的IP 地址 */
    char            szConfSubmask[32];  /* Lan 配置的子网掩码 */
    char            szConfGateway[32];  /* Lan 配置的网关地址 */
    char            szRealMacAddr[32];  /* Lan 实际的MAC 地址 */
    char            szRealIpAddr[32];   /* Lan 实际的IP 地址 */
    char            szRealSubmask[32];  /* Lan 实际的子网掩码 */
    char            szRealGateway[32];  /* Lan 实际的网关地址 */
    uint32          u32TidWatch;        /* 监测线程ID */
    uint32          u32TidNetlink;      /* 监测网卡拔插ID */
    SV_BOOL         bRunning;           /* 线程是否正在运行 */
} NETWORK_INFO_S;

NETWORK_INFO_S m_stNetworkInfo = {0};    /* 模块控制信息 */

uint16 network_CalCheckSum(uint16 *addr, sint32 len)
{
    int nleft=len;
    int sum=0;
    unsigned short *w=addr;
    unsigned short answer=0;

    while(nleft > 1)
    {
        sum += *w++;
        nleft -= 2;
    }

    if( nleft == 1)
    {
        *(unsigned char *)(&answer) = *(unsigned char *)w;
        sum += answer;
    }

    sum = (sum >> 16) + (sum & 0xffff);
    sum += (sum >> 16);
    answer = ~sum;

    return answer;
}

sint32 network_SubmitNetworkStat(SV_BOOL bHasLan, SV_BOOL bOnvif)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    NETWORK_STAT_S stNetworkStat = {0};

    stNetworkStat.enNetworkType = NETWORK_TYPE_LAN;
    stNetworkStat.bExist = bHasLan;
    stMsgPkt.pu8Data = (uint8*)&stNetworkStat;
    stMsgPkt.u32Size = sizeof(NETWORK_STAT_S);
    s32Ret = Msg_submitEvent(EP_HTTPSERVER, OP_EVENT_NETWORK_STAT, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent EP_HTTPSERVER failed. [err=%#x]\n", s32Ret);
    }

#if (defined(BOARD_ADA32V4) || defined(BOARD_IPCR20S3) || defined(BOARD_WFCR20S2) || defined(BOARD_IPTR20S1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32IR))
    {
        s32Ret = Msg_submitEvent(EP_SOMEIPSERVER, OP_EVENT_NETWORK_STAT, &stMsgPkt);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent EP_SOMEIPSERVER failed. [err=%#x]\n", s32Ret);
        }
    }
#endif

    if (bOnvif)
    {
        s32Ret = Msg_submitEvent(EP_ONVIFSERVER, OP_EVENT_NETWORK_STAT, &stMsgPkt);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent EP_ONVIFSERVER failed. [err=%#x]\n", s32Ret);
        }
    }

#if 0
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_NETWORK_STAT, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent EP_RTSPSERVER failed. [err=%#x]\n", s32Ret);
    }
#endif

    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_AUTOSI))
    {
        // 将网络断开的时间写入root下的文件中，并另存
        if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
        {
            print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
            return SV_FAILURE;
        }

        struct timeval tv;
        gettimeofday(&tv, NULL);

        time_t current_time = tv.tv_sec;
        struct tm *local_time = localtime(&current_time);

        char formatted_time[100];
        strftime(formatted_time, sizeof(formatted_time), "%Y/%m/%d %H:%M:%S", local_time);

        FILE *file = fopen("system_time_recording.txt", "a");
        if (file)
        {
            if (!bHasLan)
            {
                fprintf(file, "Network Link Down Time: %s\n", formatted_time);
            }
            else
            {
                fprintf(file, "Network Link Up Time: %s\n", formatted_time);
            }

            fclose(file);

            char *szcmd[128];
            sprintf(szcmd, "cp /root/system_time_recording.txt /root/system_time.txt");
            s32Ret = SAFE_System(szcmd, NORMAL_WAIT_TIME);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "Failed to copy file\n");
            }
            print_level(SV_INFO, "System time saved to 'system_time.txt'\n");
        }
        else
        {
            print_level(SV_ERROR, "Failed to open file\n");
        }
        CONFIG_FlashProtection(SV_TRUE);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 复位网卡
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_ResetPhyChip(SV_BOOL bCheckExist)
{
    sint32 s32Ret = 0;
    char szCmd[64] = {0};
	char szBuf[128] = {0};

#if (defined(BOARD_DMS31V2))
    if (bCheckExist)
    {
        strcpy(szCmd, "ifconfig -a | grep eth0");
    	s32Ret = SAFE_System_Recv(szCmd, szBuf, 128);
    	if(0 != s32Ret)
    	{
    		print_level(SV_ERROR, "cmd: %s failed.", szCmd);
    		return SV_FAILURE;
    	}

    	if (strstr(szBuf, "eth0") != NULL)
    	{
    		print_level(SV_WARN, "eth0 exist, skip reset usb hub...\n");
    		return SV_SUCCESS;
    	}
    }

    strcpy(szCmd, "/root/gpio.sh 3 9 0");
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME); //USB 电源断开
	if(0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.", szCmd);
        return SV_FAILURE;
    }

	sleep_ms(1000);
    strcpy(szCmd, "/root/gpio.sh 3 9 1");
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME); //USB 电源启动
	if(0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.", szCmd);
        return SV_FAILURE;
    }

    if (SV_SUCCESS == access(NETWORK_CHECK_FILE, F_OK))
    {
        remove(NETWORK_CHECK_FILE);
    }
#endif

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 判断eth0网卡设备是否存在
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL network_IsPhyExist()
{
    sint32 s32Ret, sockfd;
	struct ifreq stIfreq = {0};
	struct ethtool_cmd ecmd;
	char szCmd[128] = {0};
	char szBuf[256] = {0};
	uint32 mask = 0;

#if (!defined(PLATFORM_NT98539) && !defined(PLATFORM_RV1126) && !defined(PLATFORM_RV1106))
    strcpy(stIfreq.ifr_name, "eth0");
	sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0)
	{
        print_level(SV_ERROR, "socket failed. [err=%#x]\n", errno);
        return SV_FALSE;
	}

	ecmd.cmd = ETHTOOL_GSET;
	stIfreq.ifr_data = (caddr_t)&ecmd;
	s32Ret = ioctl(sockfd, SIOCETHTOOL, &stIfreq);
    if (0 != s32Ret)
	{
        print_level(SV_ERROR, "ioctl ETHTOOL_GSET failed. [err=%#x]\n", errno);
        close(sockfd);
        return SV_FALSE;
	}

    close(sockfd);

    mask = ecmd.supported;
    if (mask & SUPPORTED_1000baseT_Half || mask & SUPPORTED_1000baseT_Full)
    {
        return SV_FALSE;
    }
#else

    if (BOARD_ADA900V1_V2 == BOARD_GetVersion() || BOARD_ADA900V1_V3 == BOARD_GetVersion())
    {
        return m_stNetworkInfo.bEth1Exist;
    }

    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0)
    {
        print_level(SV_ERROR, "socket failed. [err=%#x]\n", errno);
        return SV_FALSE;
    }

    strncpy(stIfreq.ifr_name, "eth0", IFNAMSIZ - 1);
    s32Ret = ioctl(sockfd, SIOCGIFINDEX, &stIfreq);
    if (s32Ret < 0)
    {
        close(sockfd);
        return SV_FALSE;
    }

    close(sockfd);
#endif

    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: 判断eth0网卡是否已经配置IP地址
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL network_IsPhyIpAddrExist()
{
    sint32 s32Ret, sockfd;
    struct ifreq ifr;
	struct ethtool_cmd ecmd;
	char szCmd[128] = {0};
	char szBuf[256] = {0};
    char aszIpaddr[INET_ADDRSTRLEN];
    SV_BOOL bExist = SV_FALSE;

    // 创建socket
    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd == -1)
    {
        perror("Failed to create socket");
        exit(1);
    }

    // 设置要查询的网卡名称
    strncpy(ifr.ifr_name, "eth0", IFNAMSIZ - 1);

    // 获取网卡配置信息
    s32Ret = ioctl(sockfd, SIOCGIFADDR, &ifr);
    if (s32Ret == SV_FAILURE)
    {
        bExist = SV_FALSE;
        print_level(SV_ERROR, "ioctl error: %s\n", strerror(errno));
    }
    else
    {
        bExist = SV_TRUE;
        struct sockaddr_in* addr = (struct sockaddr_in*)&(ifr.ifr_addr);
        inet_ntop(AF_INET, &(addr->sin_addr), aszIpaddr, INET_ADDRSTRLEN);
    }

    // 关闭socket
    close(sockfd);

    return bExist;
}

/******************************************************************************
 * 函数功能: 判断eth1网卡设备是否存在
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
SV_BOOL network_Eth1_IsPhyExist()
{
    sint32 s32Ret, sockfd;
    struct ifreq stIfreq = {0};
    struct ethtool_cmd ecmd;
    char szCmd[128] = {0};
    char szBuf[256] = {0};
    uint32 mask = 0;
#if (defined(BOARD_ADA900V1)||defined(BOARD_ADA32C4)||defined(BOARD_DMS31V2))
    strcpy(szCmd, "ifconfig -a | grep eth1");
    s32Ret = SAFE_System_Recv(szCmd, szBuf, 256);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SAFE_System_Recv cmd: %s failed. [err=%#x]\n", szCmd, errno);
        return SV_FALSE;
    }

    if (strstr(szBuf, "eth1") == NULL)
    {
    	return SV_FALSE;
    }
#endif
    return SV_TRUE;
}

SV_BOOL network_NeedSwitchName(char *pszModuleUsbPath)
{
	sint32 s32Ret = 0;
	char szCmd[128] = {0};
	char szPath[128] = {0};
	char szResBuf[1024] = {0};
	char *pstTmp = NULL;

	//sprintf(szCmd,"find /sys/devices/platform/ffe00000.usb/ -type f |xargs grep \"2c7c\" | grep \"idVendor:2c7c\"");
	sprintf(szCmd,"find /sys/devices/platform/ffe00000.usb/ -type f -exec grep -sl -e \"2c7c\" {} + | grep idVendor | head -n 1");
	s32Ret = SAFE_System_Recv(szCmd, szResBuf, sizeof(szResBuf));
	if (SV_SUCCESS != s32Ret || NULL == strstr(szResBuf, "idVendor"))
	{
		//print_level(SV_WARN, "can not find 4g model!\n");
		goto exit;
	}

	pstTmp = strrchr(szResBuf, '/');
	if (NULL == pstTmp)
	{
        print_level(SV_ERROR, "not found key word: / in %s!\n", szResBuf);
        goto exit;
	}
	*pstTmp = '\0';
    strcpy(pszModuleUsbPath, szResBuf);

	memset(szCmd, 0, 128);
	sprintf(szCmd, "find %s -type f -exec grep -sl -e \"wwan*\" {} +", szResBuf);
	s32Ret = SAFE_System_Recv(szCmd, szResBuf, sizeof(szResBuf));
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
		goto exit;
	}

	pstTmp = strstr(szResBuf, "wwan");
	if (0 != strlen(szResBuf) && NULL == pstTmp)
	{
        /* 找出当前4g模块网卡是eth1 还是 eth0，如果不是eth网卡，可直接跳过 */
        memset(szCmd, 0, 128);
        sprintf(szCmd,"find %s -type f -exec grep -sl -e \"eth*\" {} +", pszModuleUsbPath);
        s32Ret = SAFE_System_Recv(szCmd, szResBuf, sizeof(szResBuf));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
            goto exit;
        }
        
        pstTmp = strstr(szResBuf, "eth");
        if (NULL == pstTmp)
        {
            print_level(SV_WARN, "not found key word: eth in %s, szResBuf: %s\n", pszModuleUsbPath, szResBuf);
            
#if defined(BOARD_ADA32C4)
             // ADA32C4 的EC200A模块生成usb网卡，网卡名需要改成wann0才可联网。
             //找出当前模块是否为usb* 网卡，是则需要改名
             sprintf(szCmd, "find %s -path \"*/net/usb*\" | head -n 1", pszModuleUsbPath);
             s32Ret = SAFE_System_Recv(szCmd, szResBuf, sizeof(szResBuf));
             if (SV_SUCCESS != s32Ret)
             {
                print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
                goto exit;
             }

             //判断路径/net后是否有usb字符
             char *pNet = strstr(szResBuf, "/net/");
             if (pNet != NULL)
             {
                 // 向后检查是否有 "usb" 字符
                 if (strstr(pNet + 5, "usb") != NULL)
                 {
                     print_level(SV_INFO, "usb net need change wann0, UsbPath: %s, szResBuf: %s\n", pszModuleUsbPath, szResBuf);
                     return SV_TRUE;
                 }
                 else
                 {
                    print_level(SV_WARN, "not found key word: usb net in %s, szResBuf: %s\n", pszModuleUsbPath, szResBuf);
                    goto exit;
                 }
             }
             else
             {
                print_level(SV_WARN, "not found key word: /net in %s, szResBuf: %s\n", pszModuleUsbPath, szResBuf);
                goto exit;
             }
#else
            goto exit;
#endif
        }

        return SV_TRUE;
	}

exit:
	return SV_FALSE;
}

/******************************************************************************
 * 函数功能: 切换网卡名字
 * 输入参数: pszModuleUsbPath --- 4G模块所在的USB路径
 * 输出参数: 无
 * 返回值  : SV_TRUE - 存在
             SV_FALSE - 不存在
 * 注意    : 无
 *****************************************************************************/
sint32 network_EthName_Switch(char *pszModuleUsbPath)
{
    sint32 s32Ret = 0;
    char szCmd[128] = {0};
	char szResBuf[1024] = {0};
	char szPath[256] = {0};
	sint32 s32EthNum = -1;
	char *pstTmp = NULL;
    char *pNetName = NULL;
    char szNetIf[5] = {0};  // 4 个字符 + 结束符

#if defined(BOARD_ADA900V1)
    /* 关闭eth0网卡 */
    strcpy(szCmd, "ip link set dev eth0 down");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%x]\n", szCmd, s32Ret);
        return s32Ret;
    }

    /* eth0重命名为ethX */
    strcpy(szCmd, "ip link set eth0 name ethX");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%x]\n", szCmd, s32Ret);
        return s32Ret;
    }

    /* 关闭eth1网卡 */
    strcpy(szCmd, "ip link set dev eth1 down");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%x]\n", szCmd, s32Ret);
        return s32Ret;
    }

    /* 重命名eth1网卡为eth0 */
    strcpy(szCmd, "ip link set eth1 name eth0");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%x]\n", szCmd, s32Ret);
        return s32Ret;
    }

    /* 启动重命名后的eth0网卡 */
    strcpy(szCmd, "ip link set dev eth0 up");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%x]\n", szCmd, s32Ret);
        return s32Ret;
    }

#endif

/*****************************************************************************************************
    1.8内核把usb0作为从设备，占用了ec200U/A 4g模块生成的虚拟网卡，内部把新加入的usb0网卡命名为eth网卡
	4g网卡统一使用wwan0，需要把eth网卡重命名为wwan0
*****************************************************************************************************/
#if defined(BOARD_DMS31V2)
    if (NULL == pszModuleUsbPath)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        goto error_exit;
    }

	/* 找出当前4g模块网卡是eth1 还是 eth0 */
	memset(szCmd, 0, 128);
	sprintf(szCmd,"find %s -type f -exec grep -sl -e \"eth*\" {} +", pszModuleUsbPath);
	s32Ret = SAFE_System_Recv(szCmd, szResBuf, sizeof(szResBuf));
	if (SV_SUCCESS != s32Ret)
	{
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
        goto error_exit;
	}

	pstTmp = strstr(szResBuf, "eth");
	if (NULL == pstTmp)
	{
        print_level(SV_ERROR, "not found key word: eth in %s!\n", szResBuf);
        goto error_exit;
	}

	pstTmp = pstTmp + strlen("eth");
	*(pstTmp+1) = '\0';
	s32EthNum = atoi(pstTmp);
	if (s32EthNum < 0)
	{
        print_level(SV_ERROR, "atoi failed! pstTmp: %s\n", pstTmp);
        goto error_exit;
	}

	/* 关闭找出来的这个eth网卡 */
	sprintf(szCmd, "ip link set dev eth%d down", s32EthNum);
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
	if (s32Ret != SV_SUCCESS)
	{
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
        goto error_exit;
	}

	sprintf(szCmd, "ip addr flush dev eth%d", s32EthNum);
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
	if (s32Ret != SV_SUCCESS)
	{
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
        goto error_exit;
	}

	/* 找出来的这个eth网卡重命名为wwan0 */
	sprintf(szCmd, "ip link set eth%d name wwan0",s32EthNum);
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
	if (s32Ret != SV_SUCCESS)
	{
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
        goto error_exit;
	}

    /* 4g网卡是eth0的时候，需要看一下当前是否存在有线网卡为eth1的，如果存在，需要把有线网卡改回eth0 */
	if (s32EthNum == 0)
	{
		memset(szResBuf, 0, 1024);
		strcpy(szCmd, "ifconfig -a | grep eth1");
		s32Ret = SAFE_System_Recv(szCmd, szResBuf, sizeof(szResBuf));
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "SAFE_System_Recv cmd: %s failed. [err=%#x]\n", szCmd, errno);
            goto error_exit;
		}

		if (NULL != strstr(szResBuf, "eth1"))
		{
			sprintf(szCmd, "ip link set dev eth1 down");
			s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
			if (s32Ret != SV_SUCCESS)
			{
				print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
                goto error_exit;
			}

			sprintf(szCmd, "ip link set eth1 name eth0");
			s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
			if (s32Ret != SV_SUCCESS)
			{
				print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
                goto error_exit;
			}
		}
	}
#endif

/*****************************************************************************************************
    ADA32C4 内核把usb*作为4g EC200A模块生成的网卡，4g网卡统一使用wwan0，需要把usb*网卡重命名为wwan0
*****************************************************************************************************/
#if defined(BOARD_ADA32C4)
    if (NULL == pszModuleUsbPath)
    {
        print_level(SV_ERROR, "get null pointer!\n");
        goto error_exit;
    }
    
    memset(szCmd, 0, 128);
    sprintf(szCmd, "find %s -path \"*/net/usb*\" | head -n 1", pszModuleUsbPath);
    s32Ret = SAFE_System_Recv(szCmd, szResBuf, sizeof(szResBuf));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
        goto error_exit;
    }
    
    //查询需要重名的usb*是usb0,usb1...
    pNetName = strstr(szResBuf, "/net/");
    if (pNetName != NULL && strlen(pNetName) >= 5)
    {
        strncpy(szNetIf, pNetName + 5, 4);  // 跳过 "/net/"，取 4 个字符
        print_level(SV_INFO, "network interface card: %s\n", szNetIf);
    }
    else
    {
        print_level(SV_ERROR, "Failed to obtain USB network card name! szNetIf: %s pNetName:%s\n", szNetIf,pNetName);
        goto error_exit;
    }
    
    //如何不存在wwan0的网卡，则修改usb*网卡为wann0
    SAFE_System_Recv("ip link show wwan0", szResBuf, sizeof(szResBuf));
    if (strstr(szResBuf, "wwan0") == NULL)
    {
        //关闭找到的usb*网卡
        sprintf(szCmd, "ip link set dev %s down", szNetIf);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
            goto error_exit;
        }

        // 清空usb* 这个网卡接口上的所有 IP 地址配置。
        sprintf(szCmd, "ip addr flush dev %s", szNetIf);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
            goto error_exit;
        }

        // usb* 重命名为wwan0
        sprintf(szCmd, "ip link set %s name wwan0", szNetIf);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
            goto error_exit;
        }

        //打开wann0网卡
        s32Ret = SAFE_System("ip link set dev wwan0 up", NORMAL_WAIT_TIME);
        if (s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "SAFE_System:%s failed![err=%s]\n", szCmd, strerror(errno));
            goto error_exit;
        }
    }
    else
    {
         print_level(SV_INFO, "Wann0 network card already exists, no need to switch \n");
    }
#endif

    return SV_SUCCESS;

error_exit:
    return SV_FAILURE;
}


/******************************************************************************
 * 函数功能: 获取网线连接状态
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 0 - 未连接
             1 - 已连接
             <0 - 获取失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_GetPhyLinkStatus()
{
    sint32 s32Ret, sockfd;
	struct ifreq stIfreq = {0};
	struct ethtool_value edata = {0};
    strcpy(stIfreq.ifr_name, "eth0");
    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0)
	{
        print_level(SV_ERROR, "socket failed. [err=%#x]\n", errno);
        return -1;
	}

	edata.cmd = ETHTOOL_GLINK;
    stIfreq.ifr_data = (caddr_t)&edata;
    s32Ret = ioctl(sockfd, SIOCETHTOOL, &stIfreq);
    if (0 != s32Ret)
	{
        print_level(SV_ERROR, "ioctl SIOCETHTOOL failed. [err=%#x]\n", errno);
        close(sockfd);
        return -1;
	}

    close(sockfd);
    return edata.data;
}

/******************************************************************************
 * 函数功能: 获取网卡的网关IP地址
 * 输入参数: pszInterface --- 网卡名字
 * 输出参数: pszGatewayIp --- 网关IP地址
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_GetGatewayAddr(char *pszInterface, char *pszGatewayIp)
{
    sint32 s32Ret = 0, i;
    ulng32 u32Val = 0;
    sint32 s32Len = 0;
    char szMatchStr[128];
    char szTmpBuf[1024] = {0};
    char szBuf[128] = {0};
    FILE * pfGateway = NULL;
    struct in_addr stAddr;

	sprintf(szMatchStr, "%s\t00000000", pszInterface);
	pfGateway = fopen("/proc/net/route", "r");
	if(NULL == pfGateway)
	{
		print_level(SV_ERROR," fopen /proc/net/route failed.\n");
		return SV_FAILURE;
	}
	while(fgets(szTmpBuf, 1024, pfGateway) != NULL)
	{
		if(strstr(szTmpBuf, szMatchStr) != NULL)
		{
			sscanf(szTmpBuf, "%*s%*s%s", szBuf);
			break;
		}
	}
	fclose(pfGateway);
    s32Len = strlen(szBuf);
    if (s32Len <= 0)
    {
        //print_level(SV_WARN, "invaled number string.\n");
        return SV_FAILURE;
    }
	print_level(SV_DEBUG, "[%s] GatewayHex: %s\n", pszInterface, szBuf);

    for (i = 0; i < s32Len; i++)
    {
        if (szBuf[i] == '\r' || szBuf[i] == '\n')
        {
            break;
        }

        if (szBuf[i] >= '0' && szBuf[i] <= '9')
        {
            u32Val = u32Val * 16 + (szBuf[i] - '0');
        }
        else if (szBuf[i] >= 'a' && szBuf[i] <= 'f')
        {
            u32Val = u32Val * 16 + (szBuf[i] - 'a') + 10;
        }
        else if (szBuf[i] >= 'A' && szBuf[i] <= 'F')
        {
            u32Val = u32Val * 16 + (szBuf[i] - 'A') + 10;
        }
        else
        {
            print_level(SV_ERROR, "invaled number string: %s\n", szBuf);
            return SV_FAILURE;
        }
    }

    print_level(SV_DEBUG, "GatewayVal: 0x%08x\n", u32Val);
    stAddr.s_addr = u32Val;
    strcpy(pszGatewayIp, inet_ntoa(stAddr));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取自动化测试的IP地址
 * 输入参数: pszInterface --- 网卡名字
 * 输出参数: pszGatewayIp --- 网关IP地址
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_GetAutoTestIpAddr(char *pszIpAddr, char *pszGateway)
{
    sint32 s32Ret = 0, s32Fd = 0, i;
	char szBuf[128] = {0};
	char szIpAddr[32] = {0};
	char szGateway[32] = {0};
    char *pszSave = NULL;
	char *pszNetSegment = NULL;

    char szJsonRet[1024] = {0};
    cJSON *pstJson = NULL, *pstNetwork = NULL;
	cJSON *pstIpAddr = NULL;

    /* 解析JSON字段 */
    s32Ret = cJSON_GetJson(AUTOTEST_IP, szJsonRet);
	if (SV_SUCCESS != s32Ret)
	{
		return s32Ret;
	}

    pstJson = cJSON_Parse(szJsonRet);
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_Parse failed.\n");
        return SV_FAILURE;
    }

    pstNetwork = cJSON_GetObjectItemCaseSensitive(pstJson, "network");
    if (NULL == pstNetwork)
    {
        print_level(SV_ERROR, "keyword network is not exist.\n");
        goto exit;
    }

    pstIpAddr = cJSON_GetObjectItemCaseSensitive(pstNetwork, "ip");
    if (NULL == pstIpAddr)
    {
        print_level(SV_ERROR, "keyword ip is not exist.\n");
        goto exit;
    }

#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) \
    || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1) || defined(BOARD_DMS51V1))
    COMMON_CutLineBreak(szBuf);
#endif

	//strncpy(szIpAddr, szBuf, 31);
	sprintf(szIpAddr, "%s", pstIpAddr->valuestring);

	szIpAddr[31] = '\0';
	strncpy(pszIpAddr, szIpAddr, 31);

	pszNetSegment = szIpAddr;
    pszNetSegment = strtok_r(pszNetSegment, ".", &pszSave);
    pszNetSegment = strtok_r(NULL, ".", &pszSave);
    pszNetSegment = strtok_r(NULL, ".", &pszSave);

	sprintf(szGateway, "192.168.%s.1", pszNetSegment);
	szGateway[31] = '\0';
	strncpy(pszGateway, szGateway, 31);

exit:
    cJSON_Delete(pstJson);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 获取当前设备的MAC地址
 * 输入参数: pszNetCard --- 网卡类型
 * 输出参数: pszMacAddress --- mac地址
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_GetMacAddress(char *ifname, char *pszMacAddress)
{
    sint32 s32Ret = 0, s32Fd = 0, i;
	char szBuf[128] = {0};
	char szMacAddress[32] = {0};
    struct ifreq ifr;

    if (NULL == ifname || NULL == pszMacAddress)
    {
        return SV_FAILURE;
    }

    s32Fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "socket SOCK_DGRAM failed.\n");
        return SV_FAILURE;
    }

    strncpy(ifr.ifr_name, ifname, IFNAMSIZ - 1);
    if (ioctl(s32Fd, SIOCGIFHWADDR, &ifr) < 0)
    {
        print_level(SV_ERROR, "socket SOCK_DGRAM failed.\n");
        return SV_FAILURE;
    }
    close(s32Fd);

    sprintf(szMacAddress, "%02x:%02x:%02x:%02x:%02x:%02x",
        (unsigned char) ifr.ifr_hwaddr.sa_data[0],
        (unsigned char) ifr.ifr_hwaddr.sa_data[1],
        (unsigned char) ifr.ifr_hwaddr.sa_data[2],
        (unsigned char) ifr.ifr_hwaddr.sa_data[3],
        (unsigned char) ifr.ifr_hwaddr.sa_data[4],
        (unsigned char) ifr.ifr_hwaddr.sa_data[5]);

	strncpy(pszMacAddress, szMacAddress, 31);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 测试ping指定IP地址速度
 * 输入参数: pszInterface --- 网卡名字
             pszIpaddr --- 目标IP地址
             u32Timeout --- 等待回包超时时间(ms)
 * 输出参数: pfSpeedTime --- 网速时间(ping包返回的延时ms)
 * 返回值  : SV_SUCCESS - 访问成功
             <0 - 访问失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_PingIpaddr(char *pszInterface, char *pszIpaddr, uint32 u32Timeout, float *pfSpeedTime)
{
    sint32 s32Ret = 0, i;
    sint32 sockfd;
    SV_BOOL bTimeout = SV_FALSE;
    float fElapse = 0.0;
    fd_set readfds;
    struct timeval tvTimeout, *ptvTime = NULL;
    struct timeval tvStart, tvEnd;
    struct ifreq stIfreq = {0};
    struct sockaddr_in addr;
    struct sockaddr_in from;
    struct icmp *icmp = NULL;
    struct ip *iph = NULL;
    char sendpacket[4096];
    char recvpacket[4096];

    if (NULL == pszInterface || NULL == pszIpaddr || NULL == pfSpeedTime)
    {
        print_level(SV_ERROR, "param ptr is null.\n");
        return SV_FAILURE;
    }

    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = inet_addr(pszIpaddr);
    sockfd = socket(AF_INET, SOCK_RAW, IPPROTO_ICMP);
    if (sockfd < 0)
    {
        print_level(SV_ERROR, "socket failed. [err=%#x]\n", errno);
        return -1;
    }

    tvTimeout.tv_sec = u32Timeout / 1000;
    tvTimeout.tv_usec = (u32Timeout % 1000) * 1000;
    s32Ret = setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &tvTimeout, sizeof(tvTimeout));
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "setsockopt failed. [err=%#x]\n", errno);
        close(sockfd);
        return -1;
    }

    strcpy(stIfreq.ifr_name, pszInterface);
    s32Ret = setsockopt(sockfd, SOL_SOCKET, SO_BINDTODEVICE, &stIfreq, sizeof(stIfreq));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "set socket option[SO_BINDTODEVICE] failed. [ifr: %s, err=%#x]\n", pszInterface, errno);
        close(sockfd);
        return -2;
    }

    icmp=(struct icmp*)sendpacket;
    icmp->icmp_type = ICMP_ECHO;
    icmp->icmp_code = 0;
    icmp->icmp_cksum = 0;
    icmp->icmp_seq = 0;
    icmp->icmp_id = 0;
    ptvTime = (struct timeval *)icmp->icmp_data;
    gettimeofday(ptvTime, NULL);
    gettimeofday(&tvStart, NULL);
    icmp->icmp_cksum = network_CalCheckSum((unsigned short *)icmp,sizeof(struct icmp));
    s32Ret = sendto(sockfd, (char *)&sendpacket, sizeof(struct icmp), 0, (struct sockaddr *)&addr, sizeof(addr));
    if (s32Ret < 1)
    {
        print_level(SV_ERROR, "sendto failed. [err=%#x]\n", errno);
        close(sockfd);
        return -1;
    }

    bTimeout = SV_FALSE;
    for (i = 0; i < 10; i++)
    {
        if (!BOARD_IsCustomer(BOARD_C_ADA32E1_200946))
        {
            if (!m_stNetworkInfo.bRunning)
            {
                break;
            }
        }

        FD_ZERO(&readfds);
        FD_SET(sockfd, &readfds);
        tvTimeout.tv_sec = u32Timeout / 1000;
        tvTimeout.tv_usec = (u32Timeout % 1000) * 1000;
        s32Ret = select(sockfd+1, &readfds, NULL, NULL, &tvTimeout);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "select failed. [err=%#x]\n", s32Ret);
            sleep_ms(10);
            continue;
        }
        else if (0 == s32Ret)
        {
        	if(strcmp(pszInterface, "eth0") != 0)
            	print_level(SV_WARN, "[%s] ping ip:%s select failed. [err=%#x]\n", pszInterface, pszIpaddr, s32Ret);
            bTimeout = SV_TRUE;
            break;
        }

        memset(recvpacket, 0, sizeof(recvpacket));
        int fromlen = sizeof(from);
        s32Ret = recvfrom(sockfd, recvpacket, sizeof(recvpacket), 0, (struct sockaddr *)&from, (socklen_t *)&fromlen);
        if (s32Ret < 1)
        {
            print_level(SV_ERROR, "recvfrom failed. [err=%#x]\n", errno);
            continue;
        }

        char *from_ip = (char *)inet_ntoa(from.sin_addr);
        if (strcmp(from_ip, pszIpaddr) != 0)
        {
            print_level(SV_WARN, "not the same ping ip. real[%s] expect[%s]\n", from_ip, pszIpaddr);
            continue;
        }

        iph = (struct ip *)recvpacket;
        icmp= (struct icmp *)(recvpacket + (iph->ip_hl<<2));
        if (icmp->icmp_type == ICMP_ECHOREPLY && icmp->icmp_id == 0)
        {
            gettimeofday(&tvEnd, NULL);
            fElapse = (float)((tvEnd.tv_sec * 1000000 + tvEnd.tv_usec) - (tvStart.tv_sec * 1000000 + tvStart.tv_usec)) / 1000.0;
            //print_level(SV_DEBUG, "[%s] ping ip:%s, icmp->icmp_type:%d ,icmp->icmp_id:%d, time:%.3fms\n", pszInterface, pszIpaddr, icmp->icmp_type, icmp->icmp_id, fElapse);
            break;
        }
    }

    close(sockfd);
    if (i >= 10 || bTimeout)
    {
    	if(strcmp(pszInterface, "eth0") != 0)
        	print_level(SV_WARN, "[%s] ping ip:%s, wait for recive packet timeout.\n", pszInterface, pszIpaddr);
        return -1;
    }

    *pfSpeedTime = fElapse;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 配置以太网静态IP
 * 输入参数: pszIpAddr --- 静态IP地址
             pszSubmask --- 静态子网掩码
          	 pszGateway --- 网关地址
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_StaticEthernet(char *pszIpAddr, char *pszSubmask, char *pszGateway)
{
    sint32 s32Ret = 0;
    char szCmd[64] = {0};

    if (NULL == pszIpAddr || NULL == pszSubmask || NULL == pszGateway)
    {
        return ERR_NULL_PTR;
    }

    sprintf(szCmd, "ip addr flush dev eth0");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    print_level(SV_WARN, "set submask and gateway.\n");
    sprintf(szCmd, "ifconfig eth0 %s netmask %s", pszIpAddr, pszSubmask);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
		if (0 != s32Ret)
		{
			print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            goto error_exit;
		}
    }

    strcpy(m_stNetworkInfo.szRealIpAddr, pszIpAddr);
    strcpy(m_stNetworkInfo.szRealSubmask, pszSubmask);
    strcpy(m_stNetworkInfo.szRealGateway, pszGateway);

    /* 添加默认路由使可以正常回复搜索的广播包 */
#if (!defined(BOARD_WFCR20S2) && !defined(BOARD_WFTR20S3))
    SAFE_SV_System("route add default eth0");
    sprintf(szCmd, "route add default gw %s", pszGateway);
	SAFE_SV_System(szCmd);
#elif (defined(BOARD_WFTR20S3))
    if (BOARD_WFTR20S3_V1 == BOARD_GetVersion())
    {
        SAFE_SV_System("ifconfig wlan1 up");
        SAFE_SV_System("route add default wlan1");
    }
    else
    {
        SAFE_SV_System("route add default eth0");
    }
#else
    SAFE_SV_System("ifconfig wlan1 up");
    SAFE_SV_System("route add default wlan1");
#endif

    return SV_SUCCESS;

error_exit:
    return SV_FAILURE;
}

/******************************************************************************
 * 函数功能: 配置以太有线网络
 * 输入参数: bDHCP --- 是否使用DHCP动态IP
             pszMacAddr --- MAC地址
             pszIpAddr --- 静态IP地址
             pszSubmask --- 静态子网掩码
          	 pszGateway --- 网关地址
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 network_ConfigEthernet(SV_BOOL bDHCP, SV_BOOL bStatic, char *pszMacAddr, char *pszIpAddr, char *pszSubmask, char *pszGateway)
{
    sint32 s32Ret = 0, i = 0;
    SV_BOOL bDHCPFailed = SV_FALSE;
    SV_BOOL bStaticFailed = SV_FALSE;
    char szMacAddr[64];
    char szRealMacAddr[32];
    char szGatewayIp[64];
    char szCmd[64];
    char *pszInterface = "eth0";
    static SV_BOOL bSetStaticIp = SV_FALSE;
    sint32 s32SleepTime = 1;
    char szStaticIpAddr[32] = {0};
    char *pszNet = NULL;
    SV_BOOL bDhcpd = SV_FALSE;
    char szEthIpAddr[32] = {0};
    char szIpLast[4] = {0};
    char *pszEthIpaddr_tmp = NULL;
    sint32 s32IpLast = 0;
    char *pszEthDhcpdConfPath = "/var/udhcpd_eth.conf";
    static sint32 setCount = 0;

    if (BOARD_IsCustomer(BOARD_C_DMS31V2_111340))
    {
        bDhcpd = SV_TRUE;
    }

    if (NULL == pszMacAddr || NULL == pszIpAddr || NULL == pszSubmask || NULL == pszGateway)
    {
        return ERR_NULL_PTR;
    }

    strcpy(szStaticIpAddr, pszIpAddr);
    pszNet = strrchr(szStaticIpAddr, '.');
    *pszNet = '\0';

    if (0 == strcmp(pszMacAddr, "00:00:00:00:00:00"))
    {
        sint32 s32Fd = -1;
        uuid_t au8Uuid;
        char *pszFile = "/etc/uuid";
        char szUuid[128] = {0};

        print_level(SV_WARN, "MAC: 00:00:00:00:00:00 is invalied. try to use uuid map one.\n");
        s32Fd = open(pszFile, O_RDONLY);
        if (s32Fd > 0)
        {
            read(s32Fd, szUuid, 128);
            close(s32Fd);
            uuid_parse(szUuid, au8Uuid);
            sprintf(szMacAddr, "aa:%02x:%02x:%02x:%02x:%02x", au8Uuid[11], au8Uuid[12], au8Uuid[13], au8Uuid[14], au8Uuid[15]);
            print_level(SV_INFO, "uuid map mac: %s\n", szMacAddr);
        }
        else
        {
            struct timeval tvTime;

            print_level(SV_WARN, "MAC: 00:00:00:00:00:00 is invalied. read uuid failed. now to generate one.\n");
            gettimeofday(&tvTime, NULL);
            srand(tvTime.tv_usec);
            sprintf(szMacAddr, "aa:%02x:%02x:%02x:%02x:%02x", (char)rand(), (char)rand(), (char)rand(), (char)rand(), (char)rand());
            print_level(SV_INFO, "auto gen new mac: %s\n", szMacAddr);
        }

        m_stNetworkInfo.bSetMacXml = SV_TRUE;
    }
    else
    {
        strcpy(szMacAddr, pszMacAddr);
    }

    if (0 == strlen(m_stNetworkInfo.szRealMacAddr))
    {
        s32Ret = network_GetMacAddress(pszInterface, szRealMacAddr);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "network_GetMacAddress failed, err: %s\n", strerror(errno));
            if (!network_IsPhyExist())
            {
                print_level(SV_WARN, "network is not exist!!\n");
                goto error_exit;
            }
        }
        else
        {
            strcpy(m_stNetworkInfo.szRealMacAddr, szRealMacAddr);
        }
    }

    if (0 != strcmp(m_stNetworkInfo.szRealMacAddr, szMacAddr))
    {
        strcpy(szCmd, "ifconfig eth0 down");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if(0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.", szCmd);
            if (!network_IsPhyExist())
            {
                print_level(SV_ERROR, "network is not exist!!\n");
                goto error_exit;
            }
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
        }

        print_level(SV_INFO, "ifconfig MAC: %s\n", szMacAddr);
        sprintf(szCmd, "ifconfig eth0 hw ether %s", szMacAddr);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            if (!network_IsPhyExist())
            {
                print_level(SV_ERROR, "network is not exist!!\n");
                goto error_exit;
            }
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
        }

        strcpy(szCmd, "ifconfig eth0 up");
        if(SAFE_System(szCmd, NORMAL_WAIT_TIME)!=0)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            if (!network_IsPhyExist())
            {
                print_level(SV_ERROR, "network is not exist!!\n");
                goto error_exit;
            }
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
        }
    }

    sprintf(szCmd, "udhcpc -i eth0 &");
    SAFE_KillallProcess(szCmd);

    const char *fileName = "dhcp_config";
    FILE *file;
    if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946) && bDHCP == SV_TRUE && setCount == 0) {
        setCount++;
        print_level(SV_ERROR, "network set config. count:%d.\n", setCount);
        file = fopen(fileName, "r");

        if (file != NULL) {
            fscanf(file, "IP: %s\n", pszIpAddr);
            fscanf(file, "Mask: %s\n", pszSubmask);
            fscanf(file, "Gateway: %s\n", pszGateway);
            bStatic = SV_TRUE;
            bDHCP = SV_FALSE;

            fclose(file);
        } else {
            bStatic = SV_FALSE;
            bDHCP = SV_TRUE;
            print_level(SV_WARN, "dhcp read config, can not open file\n");
        }
    }

	if (bStatic)
	{
		s32Ret = network_StaticEthernet(pszIpAddr, pszSubmask, pszGateway);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "network_StaticEthernet failed\n");
            goto error_exit;
        }

        print_level(SV_INFO, "static IP: %s, SubMask: %s, Gateway: %s\n", m_stNetworkInfo.szRealIpAddr, \
            m_stNetworkInfo.szRealSubmask, m_stNetworkInfo.szRealGateway);
	}

	if (bDHCP)
    {
        sint32 s32Socket;
        uint32 u32IpAddr = 0;
        uint32 u32Netmask = 0;
        char *pcIpaddr = (char *)&u32IpAddr;
        char *pcNetmask = (char *)&u32Netmask;
        struct sockaddr_in stIpAddr;
        struct sockaddr_in stNetmask;
        struct ifreq stIfr;

        if (bDhcpd)
        {
            sprintf(szCmd, "udhcpd %s &", pszEthDhcpdConfPath);
            SAFE_KillallProcess(szCmd);
        }

        sprintf(szCmd, "ip addr flush dev eth0");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        }

        #if 0
		sprintf(szCmd, "ifconfig eth0:0 %s netmask ************* up",pszIpAddr);
		if(SAFE_System(szCmd, NORMAL_WAIT_TIME)!=0)
		{
			print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
			goto error_exit;
		}
        #endif

        sprintf(szCmd, "udhcpc -i eth0 -A 1 -T 1 &");
        if(SAFE_System(szCmd, NORMAL_WAIT_TIME)!=0)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            goto error_exit;
        }
        s32Socket = socket(AF_INET, SOCK_DGRAM, 0);
        if (s32Socket < 0)
        {
            print_level(SV_ERROR, "socket failed. [err: %s]\n", strerror(errno));
            goto error_exit;
        }
        strcpy(stIfr.ifr_name, "eth0");

        if(BOARD_IsCustomer(BOARD_C_IPCR20S3_202120))
        {
            s32SleepTime = 12;
        }
        else if(BOARD_IsCustomer(BOARD_C_IPCR20S3_SAV))
        {
            s32SleepTime = 8;

        }
        else if(BOARD_IsCustomer(BOARD_C_IPCR20S3_201368) || BOARD_IsCustomer(BOARD_C_IPTR20S1_201368))
        {

            s32SleepTime = 2;
        }
        else
        {
            s32SleepTime = 1;
        }

        // DHCP等待分配地址时间xxx秒
		while(m_stNetworkInfo.s32DhcpTimeout < 0 || i < m_stNetworkInfo.s32DhcpTimeout/s32SleepTime)
        {
            s32Ret = ioctl(s32Socket, SIOCGIFADDR, &stIfr);    // 获取ip地址
            if (s32Ret >= 0)
            {
                memcpy(&stIpAddr, &stIfr.ifr_addr, sizeof(stIpAddr));
                u32IpAddr = (uint32)stIpAddr.sin_addr.s_addr;
                print_level(SV_DEBUG, "u32IpAddr=%#x\n", u32IpAddr);
            }
            s32Ret = ioctl(s32Socket, SIOCGIFNETMASK, &stIfr); // 获取子网掩码
            if (s32Ret >= 0)
            {
                memcpy(&stNetmask, &stIfr.ifr_netmask, sizeof(stNetmask));
                u32Netmask = (uint32)stNetmask.sin_addr.s_addr;
                print_level(SV_DEBUG, "u32Netmask=%#x\n", u32Netmask);
            }

            if (0 != u32IpAddr && 0 != u32Netmask)
            {
                break;
            }

			i++;
            printf("DHCP allocation in progress, time: %d\n", i);
            if (!bSetStaticIp && (i > m_stNetworkInfo.s32DhcpTimeout / s32SleepTime / 4))
            {
                //print_level(SV_INFO, "get time: %d\n", m_stNetworkInfo.s32DhcpTimeout / s32SleepTime / 4);
                sprintf(szCmd, "ifconfig eth0:0 %s netmask ************* up", pszIpAddr);
        		if (SAFE_System(szCmd, NORMAL_WAIT_TIME)!=0)
        		{
        			print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        		}
                else
                {
                    bSetStaticIp = SV_TRUE;
                }
            }
            sleep(s32SleepTime);
        }
        close(s32Socket);

        if (m_stNetworkInfo.s32DhcpTimeout >= 0 && i >= m_stNetworkInfo.s32DhcpTimeout/s32SleepTime)
        {
            print_level(SV_ERROR, "alloc IP address from DHCP Server failed. try to use the static IP address.\n");
            SAFE_KillallProcess("udhcpc -i eth0 &");
            bDHCPFailed = SV_TRUE;
        }
        else
        {
            network_GetGatewayAddr(pszInterface, szGatewayIp);
            sprintf(m_stNetworkInfo.szRealGateway, szGatewayIp);
            sprintf(m_stNetworkInfo.szRealIpAddr, "%d.%d.%d.%d", pcIpaddr[0], pcIpaddr[1], pcIpaddr[2], pcIpaddr[3]);
            sprintf(m_stNetworkInfo.szRealSubmask, "%d.%d.%d.%d", pcNetmask[0], pcNetmask[1], pcNetmask[2], pcNetmask[3]);
            print_level(SV_INFO, "DHCP IP: %s, SubMask: %s, Gateway: %s\n", \
                       m_stNetworkInfo.szRealIpAddr, m_stNetworkInfo.szRealSubmask, m_stNetworkInfo.szRealGateway);
            if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
                file = fopen(fileName, "w");
                if (file != NULL) {
                    fprintf(file, "IP: %s\n", m_stNetworkInfo.szRealIpAddr);
                    fprintf(file, "Mask: %s\n", m_stNetworkInfo.szRealSubmask);
                    fprintf(file, "Gateway: %s\n", m_stNetworkInfo.szRealGateway);
                    fclose(file);
                } else {
                    print_level(SV_WARN, "dhcp write config, can not create file\n");
                }
            }
        }
    }
    else if (bDhcpd)
    {
        sprintf(szCmd, "cp %s %s", UDHCPD_ETH_CONF, pszEthDhcpdConfPath);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        }

        strcpy(szEthIpAddr, pszIpAddr);

        pszEthIpaddr_tmp = strrchr(szEthIpAddr, '.');
        if(pszEthIpaddr_tmp == NULL)
        {
            print_level(SV_ERROR, "bChangeWifiApIpAddr failed.\n");
            goto udhcpc_eth_exit;
        }

        sprintf(szCmd,"sed -i \"/^opt\trouter/copt\trouter\t%s\" %s", pszGateway, pszEthDhcpdConfPath);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);

        strcpy(szIpLast, pszEthIpaddr_tmp+1);
        s32IpLast = atoi(szIpLast);
        if (s32IpLast <= 0)
        {
            print_level(SV_INFO, "set invalid iplast! szIpLast: %s\n", szIpLast);
            goto udhcpc_eth_exit;
        }

        if (s32IpLast - 2 > 254 - s32IpLast)
        {
            strcpy(pszEthIpaddr_tmp, ".2");
            sprintf(szCmd,"sed -i \"/^start/cstart\t\t%s\" %s", szEthIpAddr, pszEthDhcpdConfPath);
            SAFE_System(szCmd,NORMAL_WAIT_TIME);

            sprintf(pszEthIpaddr_tmp, ".%d", s32IpLast - 1);
            sprintf(szCmd,"sed -i \"/^end/cend\t\t%s\" %s", szEthIpAddr, pszEthDhcpdConfPath);
            SAFE_System(szCmd,NORMAL_WAIT_TIME);
        }
        else
        {
            sprintf(pszEthIpaddr_tmp, ".%d", s32IpLast + 1);
            sprintf(szCmd,"sed -i \"/^start/cstart\t\t%s\" %s", szEthIpAddr, pszEthDhcpdConfPath);
            SAFE_System(szCmd,NORMAL_WAIT_TIME);

            strcpy(pszEthIpaddr_tmp, ".254");
            sprintf(szCmd,"sed -i \"/^end/cend\t\t%s\" %s", szEthIpAddr, pszEthDhcpdConfPath);
            SAFE_System(szCmd,NORMAL_WAIT_TIME);
        }

        strcpy(szCmd, "udhcpc -i eth0 &");
        SAFE_KillallProcess(szCmd);
        sprintf(szCmd, "udhcpd %s &", pszEthDhcpdConfPath);
        SAFE_KillallProcess(szCmd);
        if (SAFE_System(szCmd, NORMAL_WAIT_TIME) != 0)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            goto error_exit;
        }

udhcpc_eth_exit:;
    }

	if (bSetStaticIp && NULL == strstr(m_stNetworkInfo.szRealIpAddr, szStaticIpAddr))
	{
		sprintf(szCmd, "ifconfig eth0:0 down");
		if(SAFE_System(szCmd, NORMAL_WAIT_TIME)!=0)
		{
			print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
		}
        else
        {
            bSetStaticIp = SV_FALSE;
        }
	}

    //print_level(SV_WARN, "%d,%d,%d,%d.\n", bDHCP, bDHCPFailed, bStatic, bStaticFailed);
    if ((!bDHCP || bDHCPFailed) && (!bStatic || bStaticFailed))
    {
		s32Ret = network_StaticEthernet(pszIpAddr, pszSubmask, pszGateway);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "network_StaticEthernet failed\n");
            goto error_exit;
        }
    }
    m_stNetworkInfo.bEthAvailable = SV_TRUE;
    strcpy(m_stNetworkInfo.szRealMacAddr, szMacAddr);
    m_stNetworkInfo.bInitFinished = SV_TRUE;
    return SV_SUCCESS;

error_exit:
    m_stNetworkInfo.bInitFinished = SV_TRUE;
    return SV_FAILURE;
}

void * network_Watch_Body(void *pvArg)
{
    sint32 s32Ret = 0, i, j, k;
    sint32 s32TestWaitSec = 10;
    SV_BOOL bPhyLink = SV_FALSE;
    SV_BOOL bNeedCheck = SV_FALSE;
    SV_BOOL bNeedSwitchName = SV_FALSE;
    FILE * pfArp = NULL;
    uint32 u32Cnt = 0, u32ErrCnt = 0;
	char szAutoTestIp[32] = {0};
	char szAutoTestGateway[32] = {0};
	char szCmd[128] = {0};
	char szBuf[256] = {0};
	char szModulePath[256] = {0};
    char szTmpBuf[1024] = {0};
    char aszArpIpaddr[256][20] = {0};
    char *pszInterface = "eth0";
    float fSpeedTime = 0.0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    NETWORK_INFO_S *pstNetworkInfo = (NETWORK_INFO_S *)pvArg;
    static SV_BOOL pingGateway = SV_TRUE;
    static SV_BOOL restartServer = SV_FALSE;
    static sint32 pingGatewayTime = 0;

    s32Ret = prctl(PR_SET_NAME, "network_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstNetworkInfo->bRunning)
    {
        //print_level(SV_DEBUG, "network_Watch_Body running...\n");
        sleep_ms(1000);

        if (!pstNetworkInfo->bInitFinished)
        {
            continue;
        }

        if (pstNetworkInfo->bConfUpdate)
        {
#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_DMS51V1)\
    || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
			if (m_stNetworkInfo.bAutoTesting)
			{
				s32Ret = network_GetAutoTestIpAddr(szAutoTestIp, szAutoTestGateway);
				if (SV_SUCCESS != s32Ret)
				{
					print_level(SV_ERROR, "network_GetAutoTestIpAddr failed.\n");
				}
				else
				{
					strncpy(pstNetworkInfo->szConfIpAddr, szAutoTestIp, 32);
					strncpy(pstNetworkInfo->szConfGateway, szAutoTestGateway, 32);
				}
			}
#endif

            //print_level(SV_WARN, "pstNetworkInfo->szConfIpAddr = %s.\n", pstNetworkInfo->szConfIpAddr);
            s32Ret = network_ConfigEthernet(pstNetworkInfo->bConfDhcp, pstNetworkInfo->bConfStatic, pstNetworkInfo->szConfMacAddr, pstNetworkInfo->szConfIpAddr, pstNetworkInfo->szConfSubmask, pstNetworkInfo->szConfGateway);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "network_ConfigEthernet failed. [err=%#x]\n", s32Ret);
                if (!network_IsPhyExist())
                {
                    pstNetworkInfo->bConfUpdate = SV_FALSE;
                }
            }
            else
            {
                pstNetworkInfo->bConfUpdate = SV_FALSE;
            }
        }
#if defined(BOARD_ADA900V1)
        /* eth1网卡存在 */
        bNeedSwitchName = network_Eth1_IsPhyExist();
#elif (defined(BOARD_ADA32C4) || defined(BOARD_DMS31V2))
        bNeedSwitchName = network_NeedSwitchName(szModulePath);
#endif
        if (bNeedSwitchName)
        {
            print_level(SV_WARN, "need to switch net card name now!!!\n");
            if (pstNetworkInfo->bEthAvailable)
            {
                print_level(SV_WARN, "not found physical eth0 link.\n");
                pstNetworkInfo->bEthAvailable = SV_FALSE;
                network_SubmitNetworkStat(SV_FALSE, SV_TRUE);
            }
            bPhyLink = SV_FALSE;
            s32Ret = network_EthName_Switch(szModulePath);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "network_EthName_Switch failed. [err=%#x]\n", s32Ret);
            }

            pstNetworkInfo->bEth1Exist = SV_TRUE;
        }

#if (defined(PLATFORM_NT98539) || defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
		if (!network_IsPhyExist())
		{
            /* 提交网卡关闭信息 */
			if (pstNetworkInfo->bEthAvailable)
			{
				print_level(SV_WARN, "not found physical eth0 link.\n");
				pstNetworkInfo->bEthAvailable = SV_FALSE;
                network_SubmitNetworkStat(SV_FALSE, SV_TRUE);
			}
			bPhyLink = SV_FALSE;

#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_DMS51V1)\
    || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
			if (m_stNetworkInfo.bAutoTesting)
			{
				s32Ret = network_ResetPhyChip(SV_TRUE);
				if (SV_SUCCESS != s32Ret)
				{
					print_level(SV_ERROR, "network_ResetPhyChip failed.\n");
				}
				else
				{
					for (k = 1; k <= 5; k++)
					{
						if (!network_IsPhyExist())
						{
							sleep_ms(1000);
							print_level(SV_WARN, "autoTest waiting eth0 %ds...\n", k);
						}
						else
						{
							print_level(SV_INFO, "autoTest found eth0 exist!\n");
							break;
						}
					}
				}
			}
#endif

			continue;
		}
		else
		{
			if (!pstNetworkInfo->bEthAvailable)
			{
				print_level(SV_INFO, "found physical eth0 link.\n");
				pstNetworkInfo->bConfUpdate = SV_TRUE;
				continue;
			}
			else
			{
                if (!network_IsPhyIpAddrExist())
                {
                    print_level(SV_INFO, "eth0 is exist without ip, retry to set ip.\n");
					pstNetworkInfo->bEthAvailable = SV_FALSE;
					pstNetworkInfo->bConfUpdate = SV_TRUE;
					continue;
                }
			}
		}
#endif

        s32Ret = network_GetPhyLinkStatus();
        if (s32Ret < 0)
        {
			bPhyLink = SV_FALSE;
            print_level(SV_WARN, "network_GetPhyLinkStatus failed.\n");
            continue;
        }

        if (0 == s32Ret)
        {
            if (bPhyLink)
            {
                print_level(SV_INFO, "ethernet phy link: %d\n", s32Ret);
                bPhyLink = SV_FALSE;
                network_SubmitNetworkStat(bPhyLink, SV_FALSE);
            }
            continue;
        }

        if (!bPhyLink)
        {
            print_level(SV_INFO, "ethernet phy link: %d\n", s32Ret);
            bPhyLink = SV_TRUE;
            s32TestWaitSec = 0;
            network_SubmitNetworkStat(bPhyLink, SV_TRUE);
        }

        if(m_stNetworkInfo.bSetMacXml)
        {
            stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
            s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed. [err=%#x]\n", s32Ret);
                sleep(1);
                continue;
            }

            strcpy(stNetworkCfg.szMacAddr, m_stNetworkInfo.szRealMacAddr);

            stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
            stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
            s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_SET_NETWORK_CFG failed.\n");
                sleep(1);
                continue;
            }

            m_stNetworkInfo.bSetMacXml = SV_FALSE;
        }

        if (restartServer) {
            stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
            s32Ret = Msg_execRequestBlock(EP_HTTPSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed. [err=%#x]\n", s32Ret);
                sleep(1);
                continue;
            }

            stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
            stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
            s32Ret = Msg_submitEvent(EP_SOMEIPSERVER, OP_EVENT_NETWORK_CHANGE, &stMsgPkt);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "EP_SOMEIPSERVER OP_EVENT_NETWORK_CHANGE failed.\n");
                sleep(1);
                continue;
            }
            restartServer = SV_FALSE;
        }

        if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946) && pstNetworkInfo->bConfDhcp == SV_TRUE && pingGateway) {
            float fSpeedTime = 0.0;
            pingGatewayTime++;
            s32Ret = network_PingIpaddr("eth0", pstNetworkInfo->szRealGateway, 10000, &fSpeedTime);
            print_level(SV_INFO, "In watchbody network ping %s, delay %f, ret %d, time %d.\n", pstNetworkInfo->szRealGateway, fSpeedTime, s32Ret, pingGatewayTime);
            if (s32Ret != SV_SUCCESS) {
                if (pingGatewayTime > 2) {
                    pstNetworkInfo->bConfUpdate = SV_TRUE;
                    restartServer = SV_TRUE;
                }
            } else {
                pingGateway = SV_FALSE;
            }
        }

#if defined(BOARD_DMS31V2)
        // 定时检测网络状态，等出现打静电问题后再开始
        if (COMMON_IsPathExist(NETWORK_CHECK_FILE) && !bNeedCheck)
        {
            bNeedCheck = SV_TRUE;
        }

        if (!bNeedCheck)
        {
            continue;
        }

        s32TestWaitSec--;
        if (s32TestWaitSec > 0)
        {
            if (!COMMON_IsPathExist(NETWORK_CHECK_FILE))
            {
                continue;
            }
            else
            {
                s32TestWaitSec = 0;
                print_level(SV_INFO, "access file: %s, now ping ip!!\n", NETWORK_CHECK_FILE);
            }
        }

        if (pstNetworkInfo->bResetChip)
        {
            print_level(SV_WARN, "just reset chip now, no need to check, continue!!!!\n");
            continue;
        }

        memset(aszArpIpaddr, 0, sizeof(aszArpIpaddr));
        u32Cnt = 0;
        s32Ret = network_GetGatewayAddr(pszInterface, &aszArpIpaddr[0]);
        if (SV_SUCCESS != s32Ret)
        {
            strcpy(aszArpIpaddr[0], pstNetworkInfo->szRealIpAddr);
            char *pcTmp = strrchr(aszArpIpaddr[0], '.');
            if (NULL == pcTmp)
            {
                print_level(SV_ERROR, "ipaddr[%s] is invalid.\n", aszArpIpaddr[0]);
            }
            else
            {
                strcpy(pcTmp, ".1");
                u32Cnt = 1;
            }
        }
        else
        {
            u32Cnt = 1;
        }

        pfArp = fopen("/proc/net/arp", "r");
        if(NULL == pfArp)
        {
            print_level(SV_ERROR, "fopen /proc/net/arp failed.\n");
        }
        else
        {
            while(NULL != fgets(szTmpBuf, 1024, pfArp))
            {
                if ((strstr(szTmpBuf, "eth0") != NULL) && (strstr(szTmpBuf, "00:00:00:00:00:00") == NULL))
                {
                    sscanf(szTmpBuf, "%s", aszArpIpaddr[u32Cnt]);
                    if (0 == strcmp(aszArpIpaddr[u32Cnt], aszArpIpaddr[0]))
                    {
                        memset(&aszArpIpaddr[u32Cnt], 0, sizeof(aszArpIpaddr[u32Cnt]));
                        continue;
                    }
                    u32Cnt++;
                }

                if(u32Cnt > 255)
                {
                    break;
                }
            }
            fclose(pfArp);
        }

        if (0 == u32Cnt)
        {
            s32TestWaitSec = 5;
            //print_level(SV_WARN, "no ipaddr to ping, now to reset eth0!!!\n");
            continue;
        }

        for (i = 0; i < u32Cnt; i++)
        {
            s32Ret = network_PingIpaddr(pszInterface, aszArpIpaddr[i], 1000, &fSpeedTime);
            if (SV_SUCCESS == s32Ret)
            {
                for (j = 0; j < 4; j++)
                {
                    if (SV_SUCCESS != network_PingIpaddr(pszInterface, aszArpIpaddr[i], 200, &fSpeedTime))
                    {
                        break;
                    }
                }
                if (j >= 4)
                {
                    print_level(SV_INFO, "network_PingIpaddr ip[%d]: %s success\n", i, aszArpIpaddr[i]);
                    break;
                }
            }
            else
            {
                print_level(SV_ERROR, "network_PingIpaddr ip[%d]: %s failed\n", i, aszArpIpaddr[i]);
            }
        }

        if (i >= u32Cnt)
        {
            print_level(SV_ERROR, "eth0 cannot access ethernet, now to reset eth0!!!\n");
            pstNetworkInfo->bResetChip = SV_TRUE;
            continue;
        }
        else
        {
            s32TestWaitSec = 30;
        }

        remove(NETWORK_CHECK_FILE);
#endif
    }

    return NULL;
}

/******************************************************************************
 * 函数功能: 初始化监测网络USB网卡拔插套接字
 * 输入参数: 接收消息的buf和大小
 * 输出参数: 创建的sockfd
 * 返回值  : 初始化套接字结果成功
 * 注意    : 无
 *****************************************************************************/
sint32 network_NetlinkInit(sint32 *sockfd)
{
    sint32 s32Ret = 0;
	sint32 s32Fd = 0;
    sint32 enable = 1;
    struct sockaddr_nl sa;
	char msgBuf[1024] = {0};
    const uint32 bufSize = sizeof(msgBuf);

    sa.nl_family = AF_NETLINK;
    sa.nl_pad = 0;
    sa.nl_pid = pthread_self();
    sa.nl_groups = NETLINK_KOBJECT_UEVENT;

    //创建socket
    s32Fd = socket(PF_NETLINK, SOCK_DGRAM, NETLINK_KOBJECT_UEVENT);
    if(-1 == s32Fd)
    {
        print_level(SV_ERROR, "socket creat error\n");
        return SV_FAILURE;
    }

    //修改socket选项
    setsockopt(s32Fd, SOL_SOCKET, SO_RCVBUF, msgBuf, bufSize);
    setsockopt(s32Fd, SOL_SOCKET, SO_REUSEADDR, &enable, sizeof(int));

    //绑定
    s32Ret = bind(s32Fd, (struct sockaddr*)&sa, sizeof(sa));
    if (-1 == s32Ret)
    {
        print_level(SV_WARN, "socket bind error, perhaps it is already bound\n");
        return SV_FAILURE;
    }

    *sockfd = s32Fd;
    return SV_SUCCESS;
}

void * network_Netlink_Body(void *pvArg)
{
    sint32 s32Ret = 0, s32ReadLen;
    sint32 s32NetFd = -1, s32MaxFd = 0;
    fd_set fdSet = {0};
    sint8 szNetBuf[1024] = {0};
    struct timeval timeout;
    NETWORK_INFO_S *pstNetworkInfo = (NETWORK_INFO_S *)pvArg;

    char *pattern = "^remove@/devices/platform/ffe00000\\.usb.*/net/eth0$";  // 匹配以remove@/devices/platform/ffe00000开头并且以/net/eth0结尾的字符串
    regex_t regex;

    // 编译正则表达式
    s32Ret = regcomp(&regex, pattern, REG_EXTENDED);
	if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Failed to compile regex.\n");
        return NULL;
    }

	s32Ret = network_NetlinkInit(&s32NetFd);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "network_NetlinkInit failed.\n");
		return NULL;
	}

	while (pstNetworkInfo->bRunning)
	{
        if (pstNetworkInfo->bResetChip)
        {
            s32Ret = network_ResetPhyChip(SV_FALSE);
			if (SV_SUCCESS != s32Ret)
			{
				print_level(SV_ERROR, "network_ResetPhyChip failed.\n");
			}
            pstNetworkInfo->bResetChip = SV_FALSE;
        }

		FD_ZERO(&fdSet);
	    FD_SET(s32NetFd, &fdSet);
	    sint32 s32MaxFd = s32NetFd + 1;
	    timeout.tv_sec = 1;
	    timeout.tv_usec = 0;

	    s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout);
		if (s32Ret < 0)
		{
			print_level(SV_ERROR, "network_Netlink_Body select failed.\n");
		}
		else if (s32Ret == 0)
		{
			continue;
		}
		else
		{
			memset(szNetBuf, 0, sizeof(szNetBuf));
			s32ReadLen = read(s32NetFd, szNetBuf, sizeof(szNetBuf));
			if (s32ReadLen < 0)
			{
				//print_level(SV_ERROR, "read error.\n");
				sleep_ms(1000);
				continue;
			}
			else
			{
                //print_level(SV_WARN, "---kernel msg: %s\n", szNetBuf);
                s32Ret = regexec(&regex, szNetBuf, 0, NULL, 0);
                if (SV_SUCCESS != s32Ret)
                {
                    continue;
                }

                print_level(SV_WARN, "recv kernel msg: %s, ready to reset usb hub\n", szNetBuf);
				if (!network_IsPhyExist())
				{
					print_level(SV_WARN, "net card is not exist actually, reset usb hub\n");
					s32Ret = network_ResetPhyChip(SV_TRUE);
					if (SV_SUCCESS != s32Ret)
					{
						print_level(SV_ERROR, "network_ResetPhyChip failed.\n");
					}
				}
                else
                {
                    print_level(SV_WARN, "net card is exist actually, skip to reset hub\n");
                }
                pstNetworkInfo->bResetChip = SV_FALSE;
			}
		}
	}

	if (s32NetFd > 0)
		close(s32NetFd);

    // 释放正则表达式资源
    regfree(&regex);

    return NULL;
}

void network_InitEthernet(void *pvArg)
{
    sint32 s32Ret;
    NETWORK_INFO_S *pstNetworkInfo = (NETWORK_INFO_S *)pvArg;

    print_level(SV_INFO, "bConfDhcp: %d, bConfStatic: %d, szConfMacAddr: %s, szConfIpAddr: %s, szConfSubmask: %s, szConfGateway: %s\n",
                pstNetworkInfo->bConfDhcp, pstNetworkInfo->bConfStatic, pstNetworkInfo->szConfMacAddr, pstNetworkInfo->szConfIpAddr, pstNetworkInfo->szConfSubmask, pstNetworkInfo->szConfGateway);

    s32Ret = network_ConfigEthernet(pstNetworkInfo->bConfDhcp, pstNetworkInfo->bConfStatic, pstNetworkInfo->szConfMacAddr, pstNetworkInfo->szConfIpAddr, pstNetworkInfo->szConfSubmask, pstNetworkInfo->szConfGateway);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "network_ConfigEthernet failed. [err=%#x]\n", s32Ret);
        pstNetworkInfo->bConfUpdate = SV_TRUE;
    }

    return;
}

sint32 NETWORK_Init(NETWORK_INIT_S *pstNetworkInit, NETWORK_STAT_S *pstNetworkStat)
{
    sint32 s32Ret = 0, i = 0;
    WIFI_CONF_S stWifiParam = {0};
    BLUETOOTH_CONF_S stBluetoothParam = {0};
    char szCmd[64];
	char szAutoTestIp[32] = {0};
	char szAutoTestGateway[32] = {0};
    SV_BOOL bHasEth = SV_FALSE;
    SV_BOOL bHasWifi = SV_FALSE;
    pthread_t thread_init;

    if (NULL == pstNetworkInit)
    {
        return ERR_NULL_PTR;
    }

    memset(&m_stNetworkInfo, 0, sizeof(NETWORK_INFO_S));
    m_stNetworkInfo.bConfDhcp = pstNetworkInit->stNetworkParam.bEnableDHCP;
	m_stNetworkInfo.s32DhcpTimeout = pstNetworkInit->stNetworkParam.s32DhcpTimeout;
	m_stNetworkInfo.bConfStatic = !m_stNetworkInfo.bConfDhcp;

	if (0 == access(AUTOTEST_IP, F_OK))
		m_stNetworkInfo.bAutoTesting = SV_TRUE;
	else
		m_stNetworkInfo.bAutoTesting = SV_FALSE;
    strcpy(m_stNetworkInfo.szConfMacAddr, pstNetworkInit->stNetworkParam.pszEthMacAddr);
    strcpy(m_stNetworkInfo.szConfIpAddr, pstNetworkInit->stNetworkParam.pszEthIpAddr);
    strcpy(m_stNetworkInfo.szConfSubmask, pstNetworkInit->stNetworkParam.pszEthSubmask);
    strcpy(m_stNetworkInfo.szConfGateway, pstNetworkInit->stNetworkParam.pszEthGateway);

#if defined(BOARD_DMS31V2)
    if (!COMMON_IsPathExist(UDHCPD_ETH_CONF))
    {
        sprintf(szCmd, "cp %s %s &", UDHCPD_ETH_DEFAULT, UDHCPD_ETH_CONF);
        SAFE_System(szCmd, NORMAL_WAIT_TIME);
    }
#endif

#if (defined(BOARD_ADA32V4) || defined(BOARD_ADA32V2)  || defined(BOARD_ADA32C4))
    if (0)
#elif (defined(BOARD_DMS31V2)  \
        || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_DMS51V1)\
        || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
        || defined(BOARD_HDW845V1) || defined(BOARD_ADA32E1))
    if (network_IsPhyExist())
#elif (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3))//16ev300板子通过是否连接来判断是否配置eth0(因其eth0网卡已集成到主板)
    if (network_GetPhyLinkStatus())
#endif
    {
        m_stNetworkInfo.bInitFinished = SV_FALSE;
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_DMS51V1)\
        || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
        || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
 		if (m_stNetworkInfo.bAutoTesting)
 		{
			s32Ret = network_GetAutoTestIpAddr(szAutoTestIp, szAutoTestGateway);
			if (SV_SUCCESS != s32Ret)
			{
				print_level(SV_ERROR, "network_GetAutoTestIpAddr failed.\n");
			}
			else
			{
				strncpy(m_stNetworkInfo.szConfIpAddr, szAutoTestIp, 32);
				strncpy(m_stNetworkInfo.szConfGateway, szAutoTestGateway, 32);
			}
		}
#endif

        print_level(SV_INFO, "network is exist\n");
        bHasEth = SV_TRUE;
        pthread_attr_t 	attr;
        pthread_attr_init(&attr);
        pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程
        s32Ret = pthread_create(&thread_init, &attr, network_InitEthernet, &m_stNetworkInfo);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "pthread_create network_InitEthernet failed. [err: %s]\n", strerror(errno));
            return SV_FAILURE;
        }
        pthread_attr_destroy(&attr);
    }
#if (defined(BOARD_ADA32V4) || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_DMS51V1)\
    || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
    else
    {
        print_level(SV_INFO, "network is not exist\n");
        m_stNetworkInfo.bInitFinished = SV_TRUE;
        strcpy(szCmd, "ifconfig lo 127.0.0.1");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if(0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.", szCmd);
        }

        strcpy(szCmd, "route add default lo");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if(0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        }
    }
#endif

#if (defined(PLATFORM_SSC335))
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_201368) || BOARD_IsCustomer(BOARD_C_IPTR20S1_201368) || BOARD_IsCustomer(BOARD_C_IPCR20S3_TA))
    {
        sprintf(szCmd, "echo swing_100 16 > /sys/devices/virtual/mstar/emac0/turndrv");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        }
    }
#endif


#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) \
    || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) \
    || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4)\
    || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1) || defined(BOARD_DMS51V1))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPCR20S4))
    if (BOARD_WFTR20S3_V1 != BOARD_GetVersion() && BOARD_IPCR20S4_V2 != BOARD_GetVersion())
    {
        goto skip;
    }
#endif

    stWifiParam.enWifiAuth = pstNetworkInit->stNetworkParam.enWifiAuth;
    strcpy(stWifiParam.szWifiApSsid, pstNetworkInit->stNetworkParam.pszWifiApSsid);
	strcpy(stWifiParam.szWifiApIpAddr, pstNetworkInit->stNetworkParam.pszWifiApIpAddr);
    strcpy(stWifiParam.szWifiApPwd, pstNetworkInit->stNetworkParam.pszWifiApPwd);
    strcpy(stWifiParam.szCountryCode, pstNetworkInit->stNetworkParam.pszCountryCode);
    stWifiParam.enWifiFreq = pstNetworkInit->stNetworkParam.enWifiFreq;
    stWifiParam.lSet2GChannel = pstNetworkInit->stNetworkParam.lSet2GChannel;
    stWifiParam.lSet5GChannel = pstNetworkInit->stNetworkParam.lSet5GChannel;
    stWifiParam.bWifiStaEnable = pstNetworkInit->stNetworkParam.bWifiStaEnable;
	strcpy(stWifiParam.szWifiStaIpAddr, pstNetworkInit->stNetworkParam.pszWifiStaIpAddr);
	strcpy(stWifiParam.szWifiStaGateway, pstNetworkInit->stNetworkParam.pszWifiStaGateway);
	strcpy(stWifiParam.szWifiStaSubmask, pstNetworkInit->stNetworkParam.pszWifiStaSubmask);
    strcpy(stWifiParam.szWifiStaSsid, pstNetworkInit->stNetworkParam.pszWifiStaSsid);
    strcpy(stWifiParam.szWifiStaPwd, pstNetworkInit->stNetworkParam.pszWifiStaPwd);
    stWifiParam.pfExcCallback = pstNetworkInit->pfExcCallbak;
    s32Ret = WIFI_Init(&stWifiParam, &bHasWifi);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "WIFI_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if defined(BOARD_ADA47V1)
    stBluetoothParam.bEnableBle = pstNetworkInit->stNetworkParam.bEnableBle;
    stBluetoothParam.pszBleName = pstNetworkInit->stNetworkParam.pszBleName;
    s32Ret = BLUETOOTH_Init(&stBluetoothParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BLUETOOTH_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

skip:;
#endif
    for (i = 0; i < NETWORK_TYPE_BUTT; i++)
    {
        pstNetworkStat[i].enNetworkType = i;
    }
    pstNetworkStat[NETWORK_TYPE_LAN].bExist = bHasEth;
    pstNetworkStat[NETWORK_TYPE_WIFI].bExist = bHasWifi;
    return SV_SUCCESS;
}

sint32 NETWORK_Fini()
{
    sint32 s32Ret;

#if (defined(BOARD_ADA32V4) || defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) \
    || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) \
    || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4)|| defined(BOARD_DMS51V1))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPCR20S4))
    if (BOARD_WFTR20S3_V1 != BOARD_GetVersion() && BOARD_IPCR20S4_V2 != BOARD_GetVersion())
    {
        goto skip;
    }
#endif

    s32Ret = WIFI_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "WIFI_Fini failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if defined(BOARD_ADA47V1)
    s32Ret = BLUETOOTH_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BLUETOOTH_Fini failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
skip:;
#endif

    return SV_SUCCESS;
}

sint32 NETWORK_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread;
    pthread_t thread1;

#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3))//16ev300板子通过是否连接来判断是否配置eth0(因其eth0网卡已集成到主板)
    if(network_GetPhyLinkStatus())
#endif
    {
        pthread_attr_t 	attr;
        pthread_attr_init(&attr);
        pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程

        m_stNetworkInfo.bRunning = SV_TRUE;
        s32Ret = pthread_create(&thread, &attr, network_Watch_Body, &m_stNetworkInfo);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
            return ERR_SYS_NOTREADY;
        }
        m_stNetworkInfo.u32TidWatch = thread;

//#if defined(BOARD_DMS31V2)
#if 0
		if (!m_stNetworkInfo.bAutoTesting)
		{
            s32Ret = pthread_create(&thread1, &attr, network_Netlink_Body, &m_stNetworkInfo);
	        if (0 != s32Ret)
	        {
	            print_level(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
	            return ERR_SYS_NOTREADY;
	        }
	        m_stNetworkInfo.u32TidNetlink = thread1;
		}
#endif

        pthread_attr_destroy(&attr);
    }

#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) \
    || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) \
    || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1)|| defined(BOARD_DMS51V1))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPCR20S4))
    if (BOARD_WFTR20S3_V1 != BOARD_GetVersion() && BOARD_IPCR20S4_V2 != BOARD_GetVersion())
    {
        goto skip;
    }
#endif

    s32Ret = WIFI_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "WIFI_Start failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }


#if defined(BOARD_ADA47V1)
    s32Ret = BLUETOOTH_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BLUETOOTH_Start failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

skip:;
#endif

    return SV_SUCCESS;
}

sint32 NETWORK_Stop()
{
    sint32 s32Ret = 0;
    pthread_t thread = m_stNetworkInfo.u32TidWatch;
    pthread_t thread1 = m_stNetworkInfo.u32TidNetlink;
    void *pvRetval = NULL;

#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3))
    if (network_IsPhyExist())
#endif
    {
        m_stNetworkInfo.bRunning = SV_FALSE;
        //s32Ret = pthread_join(thread, &pvRetval);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
            //return SV_FAILURE;
        }
#if defined(BOARD_DMS31V2)
		//s32Ret = pthread_join(thread1, &pvRetval);
		if (0 != s32Ret)
		{
			print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
			//return SV_FAILURE;
		}
#endif
    }

#if (defined(BOARD_ADA32V4) || defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) \
    || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) \
    || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)|| defined(BOARD_DMS51V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPCR20S4))
    if (BOARD_WFTR20S3_V1 != BOARD_GetVersion() && BOARD_IPCR20S4_V2 != BOARD_GetVersion())
    {
        goto skip;
    }
#endif

    s32Ret = WIFI_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "WIFI_Stop failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if defined(BOARD_ADA47V1)
    s32Ret = BLUETOOTH_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BLUETOOTH_Stop failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

skip:;
#endif


    return SV_SUCCESS;
}

sint32 NETWORK_SetConfig(CFG_NETWORK_PARAM *pstNetworkParam)
{
    sint32 s32Ret = 0;
    WIFI_CONF_S stWifiParam = {0};
    BLUETOOTH_CONF_S stBluetoothParam = {0};

    if (NULL == pstNetworkParam)
    {
        return ERR_NULL_PTR;
    }

    if (pstNetworkParam->bLanNotUpdate)
    {
        print_level(SV_WARN, "lan config is not update this time!!!\n");
        goto wifi_set_config;
    }

    if (m_stNetworkInfo.bConfDhcp != pstNetworkParam->bEnableDHCP
		|| m_stNetworkInfo.s32DhcpTimeout != pstNetworkParam->s32DhcpTimeout
		|| m_stNetworkInfo.bConfStatic != pstNetworkParam->bEnableStatic
        || 0 != strcmp(m_stNetworkInfo.szConfMacAddr, pstNetworkParam->pszEthMacAddr)
        || 0 != strcmp(m_stNetworkInfo.szConfIpAddr, pstNetworkParam->pszEthIpAddr)
        || 0 != strcmp(m_stNetworkInfo.szConfSubmask, pstNetworkParam->pszEthSubmask)
        || 0 != strcmp(m_stNetworkInfo.szConfGateway, pstNetworkParam->pszEthGateway))
    {
        print_level(SV_WARN, "lan config is changed!!!\n");
        m_stNetworkInfo.bConfDhcp = pstNetworkParam->bEnableDHCP;
		m_stNetworkInfo.s32DhcpTimeout = pstNetworkParam->s32DhcpTimeout;
        m_stNetworkInfo.bConfStatic = pstNetworkParam->bEnableStatic;
        strcpy(m_stNetworkInfo.szConfMacAddr, pstNetworkParam->pszEthMacAddr);
        strcpy(m_stNetworkInfo.szConfIpAddr, pstNetworkParam->pszEthIpAddr);
        strcpy(m_stNetworkInfo.szConfSubmask, pstNetworkParam->pszEthSubmask);
        strcpy(m_stNetworkInfo.szConfGateway, pstNetworkParam->pszEthGateway);
        m_stNetworkInfo.bConfUpdate = SV_TRUE;
    }
    else
    {
        print_level(SV_WARN, "lan config is not changed!!!\n");
    }

wifi_set_config:
#if (defined(BOARD_ADA32V4) || defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) \
    || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) \
    || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1) \
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)|| defined(BOARD_DMS51V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPCR20S4))
    if (BOARD_WFTR20S3_V1 != BOARD_GetVersion() && BOARD_IPCR20S4_V2 != BOARD_GetVersion())
    {
        goto skip;
    }
#endif

    stWifiParam.enWifiAuth = pstNetworkParam->enWifiAuth;
    strcpy(stWifiParam.szWifiApSsid, pstNetworkParam->pszWifiApSsid);
	strcpy(stWifiParam.szWifiApIpAddr,pstNetworkParam->pszWifiApIpAddr);
    strcpy(stWifiParam.szWifiApPwd, pstNetworkParam->pszWifiApPwd);
    strcpy(stWifiParam.szCountryCode, pstNetworkParam->pszCountryCode);
    stWifiParam.enWifiFreq = pstNetworkParam->enWifiFreq;
    stWifiParam.lSet2GChannel = pstNetworkParam->lSet2GChannel;
    stWifiParam.lSet5GChannel = pstNetworkParam->lSet5GChannel;
    stWifiParam.bWifiStaEnable = pstNetworkParam->bWifiStaEnable;
    strcpy(stWifiParam.szWifiStaIpAddr, pstNetworkParam->pszWifiStaIpAddr);
    strcpy(stWifiParam.szWifiStaGateway, pstNetworkParam->pszWifiStaGateway);
    strcpy(stWifiParam.szWifiStaSubmask, pstNetworkParam->pszWifiStaSubmask);
    strcpy(stWifiParam.szWifiStaSsid, pstNetworkParam->pszWifiStaSsid);
    strcpy(stWifiParam.szWifiStaPwd, pstNetworkParam->pszWifiStaPwd);
    s32Ret = WIFI_SetPararm(&stWifiParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "WIFI_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if defined(BOARD_ADA47V1)
    stBluetoothParam.bEnableBle = pstNetworkParam->bEnableBle;
    stBluetoothParam.pszBleName = pstNetworkParam->pszBleName;
    s32Ret = BLUETOOTH_SetPararm(&stBluetoothParam);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "BLUETOOTH_SetPararm failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
skip:;
#endif

    return SV_SUCCESS;
}

sint32 NETWORK_SetConfig_Reboot(CFG_NETWORK_PARAM *pstNetworkParam, SV_BOOL bReboot)
{
    sint32 s32Ret = 0;
    WIFI_CONF_S stWifiParam = {0};

    if (NULL == pstNetworkParam)
    {
        return ERR_NULL_PTR;
    }
    //m_stNetworkInfo.bConfUpdate = SV_TRUE;
    m_stNetworkInfo.bConfDhcp = pstNetworkParam->bEnableDHCP;
    strcpy(m_stNetworkInfo.szConfMacAddr, pstNetworkParam->pszEthMacAddr);
    strcpy(m_stNetworkInfo.szConfIpAddr, pstNetworkParam->pszEthIpAddr);
    strcpy(m_stNetworkInfo.szConfSubmask, pstNetworkParam->pszEthSubmask);
    strcpy(m_stNetworkInfo.szConfGateway, pstNetworkParam->pszEthGateway);

#if (defined(BOARD_ADA32V4) || defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) \
    || defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) \
    || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1)\
    || defined(BOARD_ADA47V1) || defined(BOARD_ADA900V1)|| defined(BOARD_DMS51V1)\
    || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPCR20S4))
    if (BOARD_WFTR20S3_V1 != BOARD_GetVersion() && BOARD_IPCR20S4_V2 != BOARD_GetVersion())
    {
        goto skip;
    }
#endif

    stWifiParam.enWifiAuth = pstNetworkParam->enWifiAuth;
    strcpy(stWifiParam.szWifiApSsid, pstNetworkParam->pszWifiApSsid);
	strcpy(stWifiParam.szWifiApIpAddr,pstNetworkParam->pszWifiApIpAddr);
    strcpy(stWifiParam.szWifiApPwd, pstNetworkParam->pszWifiApPwd);
    strcpy(stWifiParam.szCountryCode, pstNetworkParam->pszCountryCode);
    stWifiParam.enWifiFreq = pstNetworkParam->enWifiFreq;
    stWifiParam.lSet2GChannel = pstNetworkParam->lSet2GChannel;
    stWifiParam.lSet5GChannel = pstNetworkParam->lSet5GChannel;
    stWifiParam.bWifiStaEnable = pstNetworkParam->bWifiStaEnable;
	strcpy(stWifiParam.szWifiStaIpAddr,pstNetworkParam->pszWifiStaIpAddr);
	strcpy(stWifiParam.szWifiStaGateway,pstNetworkParam->pszWifiStaGateway);
	strcpy(stWifiParam.szWifiStaSubmask,pstNetworkParam->pszWifiStaSubmask);
    strcpy(stWifiParam.szWifiStaSsid, pstNetworkParam->pszWifiStaSsid);
    strcpy(stWifiParam.szWifiStaPwd, pstNetworkParam->pszWifiStaPwd);
    s32Ret = WIFI_SetPararm_Reboot(&stWifiParam, bReboot);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "WIFI_Init failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }
skip:;
#endif

    return SV_SUCCESS;
}

sint32 NETWORK_CheckStaIpaddr(char *pszStaIpAddr, SV_BOOL *bSame, SV_BOOL *bSegMatch)
{
#if (!defined(BOARD_IPCR20S3) && !defined(BOARD_IPCR20S4) && !defined(BOARD_IPCR20S5) && !defined(BOARD_IPTR20S1))
    return WIFI_CheckStaIpaddr(pszStaIpAddr, bSame, bSegMatch);
#endif
}

sint32 NETWORK_GetStaGateway(char *pszStaGateway)
{
#if (!defined(BOARD_IPCR20S3) && !defined(BOARD_IPCR20S4) && !defined(BOARD_IPCR20S5) && !defined(BOARD_IPTR20S1))
    return WIFI_GetStaGateway(pszStaGateway);
#endif
}


sint32 NETWORK_GetEthAddr(char *pszMacAddr, char *pszIpaddr, char *pszSubmask, char *pszGateway)
{
    if (NULL == pszMacAddr || NULL == pszIpaddr || NULL == pszSubmask || NULL == pszGateway)
    {
        return ERR_NULL_PTR;
    }

#if (defined(BOARD_WFTR20S3) || defined(BOARD_ADA32C4))
#if (!defined(BOARD_WFCR20S2) && !defined(BOARD_WFTR20S3))//16ev300板子通过是否连接来判断是否配置eth0(因其eth0网卡已集成到主板)
    if (!network_IsPhyExist())
#else
    if(!network_GetPhyLinkStatus())
#endif
    {
        sint32 s32Ret = 0;
        sint32 s32Fd = -1;
        uint32 u32IpAddr = 0;
        char *pcIpaddr = (char *)&u32IpAddr;
        struct sockaddr_in stAddr;
        struct ifreq stIfr;

        s32Fd = socket(AF_INET, SOCK_DGRAM, 0);
        if (s32Fd < 0)
        {
            print_level(SV_ERROR, "socket init failed.\n");
        }
        strcpy(stIfr.ifr_name, "wlan0");
        s32Ret = ioctl(s32Fd, SIOCGIFADDR, &stIfr);
        if (s32Ret >= 0)
        {
            memcpy(&stAddr, &stIfr.ifr_addr, sizeof(stAddr));
            u32IpAddr = (uint32)stAddr.sin_addr.s_addr;
            sprintf(m_stNetworkInfo.szRealIpAddr, "%d.%d.%d.%d", pcIpaddr[0], pcIpaddr[1], pcIpaddr[2], pcIpaddr[3]);
            strcpy(m_stNetworkInfo.szRealSubmask, "*************");
            strcpy(m_stNetworkInfo.szRealMacAddr, "aa:bb:cc:dd:ee:ff");
        }
        else
        {
            strcpy(m_stNetworkInfo.szRealIpAddr, "");
        }
        close(s32Fd);
    }
#endif

    strcpy(pszMacAddr, m_stNetworkInfo.szRealMacAddr);
    strcpy(pszIpaddr, m_stNetworkInfo.szRealIpAddr);
    strcpy(pszSubmask, m_stNetworkInfo.szRealSubmask);
    strcpy(pszGateway, m_stNetworkInfo.szRealGateway);

    return SV_SUCCESS;
}

SV_BOOL NETWORK_IsEthAvailable()
{
    return m_stNetworkInfo.bEthAvailable;
}

sint32 NETWORK_Set_TxPower(SV_BOOL bSet, uint32 u32Db)
{
#if (defined(BOARD_ADA32V4) || defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) /*|| defined(BOARD_IPCR20S4) */|| defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
    return WIFI_Set_TxPower(bSet, u32Db);
#endif
}

