
#ifndef _OCD_ALG_DEFINE_H_
#define _OCD_ALG_DEFINE_H_

#include "stdint.h"
#include "stdlib.h"
#include "stdio.h"

namespace ocdalg
{


    /***********************
     * 算法参数
     ***********************/
    struct STAlgParam
    {
        uint32_t u32Width;  // (返回值)算法要求的图像的宽
        uint32_t u32Height;  // (返回值)算法要求的图像的高
        uint32_t u32Channel;  // (返回值)算法要求的图像的通道数
    };

    /***********************
     * 算法类型
     ***********************/
    typedef enum ALG_TYPE_E {
        E_ALG_TYPE_OCD = 0,         /* 遮挡摄像头检测 */
        E_ALG_TYPE_REVERSE    /* 暂未开发.... */

    } EAlgType;


}
#endif