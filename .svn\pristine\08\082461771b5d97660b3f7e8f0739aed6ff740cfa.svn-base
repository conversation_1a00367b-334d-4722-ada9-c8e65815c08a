#!/bin/sh
#
# Starts dropbear sshd.
#

# Allow a few customizations from a config file
test -r /etc/default/dropbear && . /etc/default/dropbear

start() {
	DROPBEAR_ARGS="$DROPBEAR_ARGS -R"

	# If /etc/dropbear is a symlink to /var/run/dropbear, and
	#   - the filesystem is RO (i.e. we can not rm the symlink),
	#     create the directory pointed to by the symlink.
	#   - the filesystem is RW (i.e. we can rm the symlink),
	#     replace the symlink with an actual directory
	if [ -L /etc/dropbear \
	     -a "$(readlink /etc/dropbear)" = "/var/run/dropbear" ]
	then
		if rm -f /etc/dropbear >/dev/null 2>&1; then
			mkdir -p /etc/dropbear
		else
			echo "No persistent location to store SSH host keys. New keys will be"
			echo "generated at each boot. Are you sure this is what you want to do?"
			mkdir -p "$(readlink /etc/dropbear)"
		fi
	fi

	printf "Starting dropbear sshd: "
	umask 077

	start-stop-daemon -S -q -p /var/run/dropbear.pid \
		--exec /usr/sbin/dropbear -- $DROPBEAR_ARGS
	[ $? = 0 ] && echo "OK" || echo "FAIL"
}
stop() {
	printf "Stopping dropbear sshd: "
	start-stop-daemon -K -q -p /var/run/dropbear.pid
	[ $? = 0 ] && echo "OK" || echo "FAIL"
}
restart() {
	stop
	start
}

case "$1" in
  start)
  	start
	;;
  stop)
  	stop
	;;
  restart|reload)
  	restart
	;;
  *)
	echo "Usage: $0 {start|stop|restart}"
	exit 1
esac

exit $?
