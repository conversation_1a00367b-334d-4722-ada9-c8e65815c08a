#ifndef _LIS3DH_H_
#define _LIS3DH_H_

#include "common.h"
#include "config.h"
#include "print.h"
#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */


/******************************************************************************
 * 函数功能: 启动lis3dh模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 LIS3DH_Start();

/******************************************************************************
 * 函数功能: 停止lis3dh模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 LIS3DH_Stop();

/******************************************************************************
 * 函数功能: 初始化lis3dh模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 LIS3DH_Init();

/******************************************************************************
 * 函数功能: 去初始化lis3dh模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 LIS3DH_Fini();

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _LIS3DH_H_ */
