Index: include/config_factory.h
===================================================================
--- include/config_factory.h	(revision 4451)
+++ include/config_factory.h	(working copy)
@@ -43,6 +43,10 @@
     {
         pszAdminPassword = "installer";
     }
+    else if(BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93726) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598_93833))
+    {
+        pszAdminPassword = "BVAdmin123";
+    }
 #endif
 #if (defined(BOARD_ADA32IR))
     else if(BOARD_IsCustomer(BOARD_C_ADA32IR_202461))
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 4451)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -6305,7 +6305,7 @@
         || BOARD_IsCustomer(BOARD_C_ADA32V2_200531) || BOARD_IsCustomer(BOARD_C_ADA32V2_SPILLARD)
         || BOARD_IsCustomer(BOARD_C_ADA32V2_202063) || BOARD_IsCustomer(BOARD_C_ADA32V2_201244)
         || BOARD_IsCustomer(BOARD_C_ADA32V2_201933) || BOARD_IsCustomer(BOARD_C_ADA32V2_200889)
-        || BOARD_IsCustomer(BOARD_C_ADA32V2_202264))
+        || BOARD_IsCustomer(BOARD_C_ADA32V2_202264) || BOARD_IsCustomer(BOARD_C_ADA32V2_201851))
     {
         return SV_TRUE;
     }
@@ -6401,9 +6401,6 @@
         case E_PDS_P:
             modefilelist[0] = PD_MODEL_RGB_P;
 
-            if (BOARD_IsCustomer(BOARD_C_ADA32V2_FTC))
-                modefilelist[0] = PD_MODEL_RGB_P_FTC;
-
             if (BOARD_IsCustomer(BOARD_C_ADA32V2_201266B) || BOARD_IsCustomer(BOARD_C_ADA32V2_201266C))
                 modefilelist[0] = PD_MODEL_RGB_P_201266B;
 
