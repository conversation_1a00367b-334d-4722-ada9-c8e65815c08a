#ifndef _SV_BB_MULTI_MEDIA_H_
#define _SV_BB_MULTI_MEDIA_H_

#if 1
#include <list>
#include "sv_bb_struct.h"
#include "sv_lock.h"
#include "sv_common.h"
#define MAX_SNAP_PIC_CHN (12)
#define PHOTO_BUFFER_SIZE (1024 * 1024)

#define BB_PHOTO_PATH "bb_media/photo"
#define BB_SUBIAO_ALARM_INFO_PATH "subiao_alarm"

typedef struct
{
	ST_BB_PHOTO_EVENT event;
	BB_POS_BASE_S pos;
	char fullpath[256];
	int need_delete;
	time_t lastUploadTime;
	int ReUploadTimes;
}ST_PHOTE_UPLOAD;


typedef std::list<BB_SNAP_PIC_S *> SnapPicList;
typedef std::list<ST_PHOTE_UPLOAD *> PhotoUploadList;

class CSV_BB_MULTIMEDIA
{
public:
	CSV_BB_MULTIMEDIA();
	~CSV_BB_MULTIMEDIA();

	int Init();

	int HandlePhoto(BB_SNAP_PIC_S *pstPhoto, BB_POS_BASE_S *pstPosBase);

	int GetSnapListSize();

	void CmdSnapPic(BB_POS_BASE_S *pstPoseBase);

	void TimerSnapPic(BB_POS_BASE_S *pstPosBase);

	int UploadListAdd(ST_BB_PHOTO_EVENT *pstEvent, BB_POS_BASE_S *pstPosBase, const char *fullpath, int need_delete);

	int GeneratePhotoFullPath(ST_BB_PHOTO_EVENT *pstEvent, BB_POS_BASE_S *pstPosBase, char *pFullPath);

	int GetUploadItem(ST_PHOTE_UPLOAD** pItem);

	int RemoveUploadItem(ST_PHOTE_UPLOAD *pItem);

	int SaveSnapPic(char *pFullPath, int Chn);

	int GetSnapStartIdAndCount(u32 &start_id);
private:
	SnapPicList m_lis_SnapPic;
	CMutex m_lis_SnapPicLock;

	PhotoUploadList m_lis_PhotoUpload;
	CMutex m_lis_PhotoUploadLock;

	u8 *m_pu8_SnapPicBuf;
	char m_bbMediaPhotoPath[256];
	uint32 m_EventId;
};

#endif
#endif
