/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件功能描述: 查看共享媒体队列信息

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/sem.h>
#include <sys/msg.h>
#include <pthread.h>
#include <signal.h>
#include <errno.h>
#include "common.h"
#include "print.h"

#include "sharefifo.h"

static void exit_handle(int signalnum)
{
    printf("catch signalnum %d!\n", signalnum);
    exit(-1);
}

void print_usage()
{
    const char *usage	=
		" filepath: for generate IPC key\n";
    printf("\r\nfifoinfo <filepath>\n", usage);
}

int ipsys_log_level = SV_DEBUG;
int main(int argc, char** argv)
{
    sint32 s32Ret = 0;
    sint32 s32QueId = 0;
    char *pszPathName = NULL;
    key_t key;   
    sint32 s32ShmId = 0;
    void *pvStartVirAddr = NULL;

	if (SIG_ERR == signal(SIGINT, exit_handle))
    {
        printf("catch signal SIGKILL Error: %d, %s\n", errno, strerror(errno));
    }

    if (argc != 2)
	{
		print_usage();
		return -1;
	}

	pszPathName = argv[1];
	if (0 != access(pszPathName, F_OK))
	{
	    printf("file: %s unexist.\n", pszPathName);
	    return -1;
	}

    s32Ret = SFIFO_ForQueryOpen(pszPathName, &s32QueId);
    if (SV_SUCCESS != s32Ret)
    {
        printf("SFIFO_ForQueryOpen failed. [err=%#x]\n", s32Ret);
        return -1;
    }
    
    SFIFO_PrintShmQueInfo(s32QueId);
    
    return 0;
}


