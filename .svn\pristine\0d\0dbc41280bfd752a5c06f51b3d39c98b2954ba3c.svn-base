#!/bin/sh

# GPIO分布：GPIO0: 0~31，GPIO1: 32~64, GPIO2: 64~96，GPIO3: 96~128
#           A: 0~7，B: 8~15，C: 16~23，D: 24~31
# GPIO3_C4=96+20=116，GPIO2_D1=64+25=89

#enable A31 four light
/root/gpio.sh  2 23 1   #GPIO2_C7_d
/root/gpio.sh  2 24 1   #GPIO2_D0_d
/root/gpio.sh  3 15 1   #GPIO3_B7_d
/root/gpio.sh  3 12 1   #GPIO3_B4_d

/root/gpio.sh  3 20 0   #GPIO3_C4_d  A32 trigger line start default low level
/root/gpio.sh  1 25 1   #GPIO1_D1_d  enable audio