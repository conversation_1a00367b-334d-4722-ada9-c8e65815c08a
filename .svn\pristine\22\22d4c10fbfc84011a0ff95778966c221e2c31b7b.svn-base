#include <dirent.h>
#include <stdlib.h>
#include <fnmatch.h>

#include "cms_filePrivateUpload.h"
#include "cms_offlineInfo.h"

#define FILETRANSFER_BUFFNUM 163840
#define FILEPATH_VIDEO  "/mnt/sdcard/alarm/"
#define FILEPATH_PICTURE  "/mnt/sdcard/picture/"

#include <map>
#include <thread>
#include <vector>
#include "../../src/recorder/inc/r_common.h"
using namespace recorder;
extern sint32 GetCurPosFileList(REC_TYPE_E enType, multimap<uint64, REC_FILE_S> **fileList);


/******************************************************************************
 * 函数功能: 扫描全部存储设备的录像文件生成列表信息
 * 输入参数: pstStorageInfo -- 存储信息
 * 输出参数: 文件列表信息
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 scanRecorderFile(UPLOAD_FILE_OPTS_E enUploadOpts, list<SV_STORAGE_FILE_PATH_S>& fileList, SV_BOOL bClear)
{
    sint32 s32Ret = 0, i = 0;
    DIR *pDirRoot = NULL;
    DIR *pDirDate = NULL;
    DIR *pDirHour = NULL;
    DIR *pDirMinute = NULL;
    struct dirent *pstDirentRoot = NULL;
    struct dirent *pstDirentDate = NULL;
    struct dirent *pstDirentHour = NULL;
    struct dirent *pstDirentMinute = NULL;
    char szDirDate[64];
    char szDirHour[64];
    char szDirMinute[64];
    char szFilePath[256];
	SV_STORAGE_FILE_PATH_S stFilePath = {0};
	REC_FILE_S stFileInfo = {0};
	SV_MEDIA_FILE_NAME_PARAMS_ST stFileNameParams;
    std::multimap<uint64, REC_FILE_S> *pstFileMap;
    std::multimap<uint64, REC_FILE_S>::iterator map_itor;
    std::multimap<uint64, REC_FILE_S> *pstFileMapPic;
    std::multimap<uint64, REC_FILE_S>::iterator map_itor_pic;
	char szFilter[114]={0};
    char szFileName[256] = {0};
	uint32 u32Flag;
    char *pszFileName = NULL;
    uint32 u32VideoSize = 0, u32PicSize = 0, u32Total = 0, u32Index = 0;
    SV_BOOL bOnly = SV_TRUE;

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2)  || defined(BOARD_ADA32C4))
    switch (enUploadOpts)
    {
        case UPLOAD_FILE_ALARM_VIDEO:
            s32Ret = GetCurPosFileList(REC_TYPE_ALARM, &pstFileMap);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_ALARM);
                return SV_FAILURE;
            }
            u32VideoSize = (*pstFileMap).size();
            break;

        case UPLOAD_FILE_ALARM_PICTURE:
            s32Ret = GetCurPosFileList(REC_TYPE_PIC, &pstFileMap);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_PIC);
                return SV_FAILURE;
            }
            u32PicSize = (*pstFileMap).size();
            break;

        case UPLOAD_FILE_ALARM_ALL:
            bOnly = SV_FALSE;
            s32Ret = GetCurPosFileList(REC_TYPE_ALARM, &pstFileMap);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_ALARM);
                return SV_FAILURE;
            }
            u32VideoSize = (*pstFileMap).size();

            s32Ret = GetCurPosFileList(REC_TYPE_PIC, &pstFileMapPic);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "RECORDER_GetNodeFileList failed, type: %d\n", REC_TYPE_PIC);
                return SV_FAILURE;
            }
            u32PicSize = (*pstFileMapPic).size();
            break;

        default:
            break;
    }

    u32Total = u32VideoSize + u32PicSize;
    print_level(SV_INFO, "get tatol file list size: %d + %d = %d\n", u32VideoSize, u32PicSize, u32Total);

    if (bClear)
    {
        fileList.clear();
    }

    if (0 == u32Total)
    {
        goto exit;
    }

    if (bOnly)
    {
        for (map_itor = (*pstFileMap).begin(); map_itor != (*pstFileMap).end(); map_itor++)
    	{
    		stFileInfo = ((*map_itor).second);
            pszFileName = strrchr(stFileInfo.name.data(), '/');
            if (NULL != pszFileName)
            {
                memset(szFileName, 0, sizeof(szFileName));
                memcpy(szFileName, pszFileName+1, strlen(pszFileName)-1);
            }
            else
            {
                continue;
            }

            sscanf(szFileName, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
    					,&stFileNameParams.s32Year\
    					,&stFileNameParams.s32Month\
    					,&stFileNameParams.s32Day\
    					,&stFileNameParams.s32Hour\
    					,&stFileNameParams.s32Minute\
    					,&stFileNameParams.s32Second\
    					,&stFileNameParams.cEventType\
    					,&stFileNameParams.s32Duration\
    					,&stFileNameParams.s32Size\
    					,&stFileNameParams.s32Width\
    					,&stFileNameParams.s32Height\
    					,&stFileNameParams.s32FrameRate\
    					,&stFileNameParams.s32BitRate\
    					,&stFileNameParams.cPlateNum\
    					,&stFileNameParams.s32ChNum\
    					,&stFileNameParams.u64DeviceID\
    					,&stFileNameParams.u8Flag\
    					,&stFileNameParams.s32Msec\
    					,&stFileNameParams.s32PreRecordMsec\
    					,&stFileNameParams.cVersionNum\
    					,&stFileNameParams.cCustomerNum\
    					,&stFileNameParams.cTimeZone\
    					,&stFileNameParams.cDST\
    					,&stFileNameParams.cFileType);

    		if (stFileNameParams.s32Size != 0 && stFileNameParams.u8Flag == 2)
    		{
                if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI)
                    && strncmp(stFileNameParams.cEventType, "FT", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "YW", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "AF", 2) != 0)
                {
                    continue;
                }

    			strcpy(stFilePath.szFilePath, stFileInfo.name.data());
    			stFilePath.u32Duration = stFileInfo.duration;
    			printf("file path: %s\n", stFilePath.szFilePath);
    			fileList.push_back(stFilePath);
            }
        }
    }
    else
    {
        map_itor = (*pstFileMap).begin();
        map_itor_pic = (*pstFileMapPic).begin();
        while (0 != u32Total)
        {
            if (map_itor == (*pstFileMap).end() && map_itor_pic == (*pstFileMapPic).end())
            {
                break;
            }

            if (u32Index++ % 2 == 0)
            {
                if (map_itor != (*pstFileMap).end())
                {
                    stFileInfo = ((*map_itor).second);
                    map_itor++;
                }
                else
                {
                    continue;
                }
            }
            else
            {
                if (map_itor_pic != (*pstFileMapPic).end())
                {
                    stFileInfo = ((*map_itor_pic).second);
                    map_itor_pic++;
                }
                else
                {
                    continue;
                }
            }

            pszFileName = strrchr(stFileInfo.name.data(), '/');
            if (NULL != pszFileName)
            {
                memset(szFileName, 0, sizeof(szFileName));
                memcpy(szFileName, pszFileName+1, strlen(pszFileName)-1);
            }
            else
            {
                continue;
            }

            sscanf(szFileName, "%04d%02d%02d%02d%02d%02d_%02s_%d_%d_%04d_%04d_%02d_%d_%016s_%02d_%lld_%02x_%03d_%d_%03s_%03s_%03s_%01d.%s"\
    					,&stFileNameParams.s32Year\
    					,&stFileNameParams.s32Month\
    					,&stFileNameParams.s32Day\
    					,&stFileNameParams.s32Hour\
    					,&stFileNameParams.s32Minute\
    					,&stFileNameParams.s32Second\
    					,&stFileNameParams.cEventType\
    					,&stFileNameParams.s32Duration\
    					,&stFileNameParams.s32Size\
    					,&stFileNameParams.s32Width\
    					,&stFileNameParams.s32Height\
    					,&stFileNameParams.s32FrameRate\
    					,&stFileNameParams.s32BitRate\
    					,&stFileNameParams.cPlateNum\
    					,&stFileNameParams.s32ChNum\
    					,&stFileNameParams.u64DeviceID\
    					,&stFileNameParams.u8Flag\
    					,&stFileNameParams.s32Msec\
    					,&stFileNameParams.s32PreRecordMsec\
    					,&stFileNameParams.cVersionNum\
    					,&stFileNameParams.cCustomerNum\
    					,&stFileNameParams.cTimeZone\
    					,&stFileNameParams.cDST\
    					,&stFileNameParams.cFileType);

    		if (stFileNameParams.s32Size != 0 && stFileNameParams.u8Flag == 2)
    		{
                if (BOARD_IsCustomer(BOARD_C_DMS31V2_SHIQI)
                    && strncmp(stFileNameParams.cEventType, "FT", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "YW", 2) != 0
                    && strncmp(stFileNameParams.cEventType, "AF", 2) != 0)
                {
                    continue;
                }

    			strcpy(stFilePath.szFilePath, stFileInfo.name.data());
    			stFilePath.u32Duration = stFileInfo.duration;
    			printf("file path: %s\n", stFilePath.szFilePath);
    			fileList.push_back(stFilePath);
            }
        }
    }

exit:;
    print_level(SV_DEBUG, "scan recorder file success! need to upload list size: %d\n", fileList.size());
#endif
    return SV_SUCCESS;
}


SV_NETWORK_FILEPRIVATEUPLOAD::SV_NETWORK_FILEPRIVATEUPLOAD(SV_NETWORK_DVRINFO* pTempinfo,
				SV_NETWORK_STATE* pTempState, SV_NETWORK_PROTOCOL* pTempProtocal, SV_NETWORK_LINK* pTempLinkControl)
 	:pDvrinfo(pTempinfo),pState(pTempState),pProtocol(pTempProtocal),pLinkControl(pTempLinkControl)
{
	bRegister = SV_FALSE;
	u32Port = 9093;
	u64SessionId = 0;
	pLink = new SV_NETWORK_LINK();

    /* 初始化文件上传arry的8和9，司机信息上传专用 */
	for(int i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		stFileupLoadArry[i].s32Fd = -1;
	}

	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		stFileupLoadArry[i].s32Fd = -1;
	}

	clearArry();
    m_enUploadFileOpts = pDvrinfo->getUploadFileType();
}


SV_NETWORK_FILEPRIVATEUPLOAD::~SV_NETWORK_FILEPRIVATEUPLOAD()
{
	//delete pLink;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setPort( uint32 u32Temp )
{
	u32Port = u32Temp;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setSessionId( uint64 u64Id)
{
	u64SessionId = u64Id;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isArryEmpty()
{

	for(int i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if( stFileupLoadArry[i].u8UploadStatus != STATUS_NOFILE )
		{
			return SV_FALSE;
		}
	}

	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( stFileupLoadArry[i].u8UploadStatus != STATUS_NOFILE )
		{
			return SV_FALSE;
		}
	}

	return SV_TRUE;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isArryFull()
{
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( stFileupLoadArry[i].u8UploadStatus == STATUS_NOFILE )
		{
			return SV_FALSE;
		}
	}

	return SV_TRUE;

}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isArryFull1()
{
	for(int i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if( stFileupLoadArry[i].u8UploadStatus == STATUS_NOFILE )
		{
			return SV_FALSE;
		}
	}

	return SV_TRUE;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::clearArry()
{
	for(int i=0; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		stFileupLoadArry[i].u8UploadStatus = STATUS_NOFILE;
		if(stFileupLoadArry[i].s32Fd > 0)
		{
			close(stFileupLoadArry[i].s32Fd);
		}
		stFileupLoadArry[i].s32Fd = SV_FAILURE;
		deleSessionID(stFileupLoadArry[i].u32FileSessionId);
	}
}
/* 获取司机信息上传列表索引 */
uint8 SV_NETWORK_FILEPRIVATEUPLOAD::getAccessIndex1()
{
	for (uint8 i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if (stFileupLoadArry[i].u8UploadStatus == STATUS_NOFILE)
		{
			return i;
		}
	}
    return SV_FAILURE;
}

uint8 SV_NETWORK_FILEPRIVATEUPLOAD::getAccessIndex()
{
	for (uint8 i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if (stFileupLoadArry[i].u8UploadStatus == STATUS_NOFILE)
		{
			return i;
		}
	}

    return SV_FAILURE;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::getFileIndex( char *pFileName)
{
	for(uint8 i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if( strstr(stFileupLoadArry[i].szFileName, pFileName) != SV_NULL)
		{
			return i;
		}
	}
	for(uint8 i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( strstr(stFileupLoadArry[i].szFileName, pFileName) != SV_NULL)
		{
			return i;
		}
	}

	print_level(SV_DEBUG,"Can not find index filename:%s\n", pFileName);
	return SV_FAILURE;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::addFile2Arry(SKDvrPrepareToUploadFileRsp* pRsp, char *pFileName)
{
	sint32 s32Index = -1;
	char szFullPath[STORAGE_FULLPATH_LEN] = {0};

	s32Index = getFileIndex(pFileName);
	if(s32Index < 0)
	{
        print_level(SV_ERROR, "not found file: %s in array.\n", pFileName);
		return SV_FAILURE;
	}

	//获取录像的目录，需要实现
	if( SV_COMMON_getAlarmFileDir(m_enUploadFileOpts, pFileName, szFullPath) != SV_SUCCESS )
	{
		print_level(SV_DEBUG,"SV_DVR_STORAGE_GetFileDirctory fail [%s]\n", pFileName);
		return SV_FAILURE;
	}
	sprintf(stFileupLoadArry[s32Index].szFullPath, "%s", szFullPath);

	print_level(SV_DEBUG,"add full path:%s\n", stFileupLoadArry[s32Index].szFullPath);
	if( !SV_DVR_COMMON_IsFileExist(stFileupLoadArry[s32Index].szFullPath) )
	{
		print_level(SV_ERROR,"Not exit!\n");
        deleIndex(s32Index);
        removeFileFromList(stFileupLoadArry[s32Index].szFullPath);
		return SV_FAILURE;
	}

	if(stFileupLoadArry[s32Index].s32Fd < 0)
	{
		stFileupLoadArry[s32Index].u32FileOffest = pRsp->uiUploadedSize;
		stFileupLoadArry[s32Index].u8TimeOutCnt = 0;
		stFileupLoadArry[s32Index].u32FileSeq = 0;
		stFileupLoadArry[s32Index].u32RecFileSeq = 0;
		stFileupLoadArry[s32Index].u32FileSessionId = pRsp->uiSessionId;
		stFileupLoadArry[s32Index].bSendEnd = SV_FALSE;
		stFileupLoadArry[s32Index].s32Fd = open(stFileupLoadArry[s32Index].szFullPath, O_RDWR);
        if (stFileupLoadArry[s32Index].s32Fd <= 0)
        {
            print_level(SV_ERROR, "open file: %s failed.\n", stFileupLoadArry[s32Index].szFullPath);
            deleIndex(s32Index);
            return SV_FAILURE;
        }
		SV_DVR_COMMON_CloseEvec(stFileupLoadArry[s32Index].s32Fd);
		lseek(stFileupLoadArry[s32Index].s32Fd, stFileupLoadArry[s32Index].u32FileOffest, SEEK_SET);
		stFileupLoadArry[s32Index].u8UploadStatus = STATUS_UPLOADING;
		stFileupLoadArry[s32Index].s32PrintCnt = 0;
	}
	else
	{
		print_level(SV_WARN,"File:%s has been opened!\n", stFileupLoadArry[s32Index].szFullPath);
	}

	return SV_SUCCESS;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setStatusPrepare(uint8 u8Index, char *pFileName, uint8 u8Type, uint64 u64FileSize)
{
	stFileupLoadArry[u8Index].u8UploadStatus = STATUS_PREPARE;
	//print_level(SV_DEBUG,"szFullPath:%s\n", pFileName);
	//strcpy(stFileupLoadArry[u8Index].szFullPath, pFileName);
	print_level(SV_DEBUG,"u8Index:%d szFileName:%s\n", u8Index, pFileName);
	strcpy(stFileupLoadArry[u8Index].szFileName, pFileName);
	stFileupLoadArry[u8Index].u8FileType = u8Type;
	stFileupLoadArry[u8Index].u64FileSize = u64FileSize;
	stFileupLoadArry[u8Index].s32PrintCnt = 0;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::sendRegsiter()
{
	SK_HEADER stHeader;
	SKConnectFileSaveServerReq stConnect;
	stConnect.ullGlobalSessionId = u64SessionId;

	pProtocol->createHeader(&stHeader, SK_DVR_TO_CMS_SERVER_CONNECT_FILE_SAVE_SERVER_REQ,
		sizeof(SKConnectFileSaveServerReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&stHeader, (char *)&stConnect, sizeof(SKConnectFileSaveServerReq));

	print_level(SV_DEBUG,"send register u64SessionId:%llu!\n", u64SessionId);

}

/* 0x3023 */
uint64 SV_NETWORK_FILEPRIVATEUPLOAD::sendFileInfo(const char *szFilePath)
{
	char szFileName[128] = {0};
	SK_HEADER stHeader;
	SKDvrPrepareToUploadFileReq stUploadFile;

	stUploadFile.uiFileLen = SV_COMMON_getFileSize(szFilePath);
	SV_COMMON_cutOutName((const char *)szFilePath, szFileName);

	cJSON *root;
	char *out;
	root=cJSON_CreateObject();
	cJSON_AddStringToObject(root, "fn", szFileName);

	out=cJSON_Print(root);
	//print_level(SV_DEBUG,"send file header:\n%s\n",out);
	//print_level(SV_DEBUG,"out len:%d\n", strlen(out)+1);

	stUploadFile.uiDataLen = strlen(out) + 1;
	uint32 u32DataLen = sizeof(SKDvrPrepareToUploadFileReq) + strlen(out) + 1;
	char *pTemp = (char *)malloc(u32DataLen);
	memcpy(pTemp, &stUploadFile, sizeof(SKDvrPrepareToUploadFileReq));
	memcpy(pTemp+sizeof(SKDvrPrepareToUploadFileReq), out, strlen(out) + 1);

	pProtocol->createHeader(&stHeader, SK_DVR_TO_CMS_SERVER_PREPARE_TO_UPLOAD_FILE_REQ,
		u32DataLen, SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&stHeader, pTemp, u32DataLen);

	cJSON_Delete(root);
	free(out);
	free(pTemp);

	return stUploadFile.uiFileLen;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::sendFileInArry()
{

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))

    for (int i = 8; i < FILEUPLOAD_DRIVERNUM; ++i)
    {
        //print_level(SV_INFO,"HighPriority is true and need to upload driver first!\n");
        //print_level(SV_INFO,"u8UploadStatus=%u, u32FileSeq=%d, u32RecFileSeq=%d\n",stFileupLoadArry[i].u8UploadStatus, stFileupLoadArry[i].u32FileSeq, stFileupLoadArry[i].u32RecFileSeq);
        if (stFileupLoadArry[i].u8UploadStatus == STATUS_UPLOADING &&
            stFileupLoadArry[i].u32FileSeq == stFileupLoadArry[i].u32RecFileSeq)
        {
            sendFile(i);
            usleep(20000);
            return SV_SUCCESS;
        }
    }

	for(int i=0; i <FILEUPLOAD_MAXNUM; i++)
	{

		if(stFileupLoadArry[i].u8UploadStatus == STATUS_UPLOADING)
		{
			if(stFileupLoadArry[i].u32FileSeq == stFileupLoadArry[i].u32RecFileSeq)
			{
				sendFile(i);
				usleep(20000);
			}
		}
	}


#endif
	return SV_SUCCESS;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::sendFile(uint8 u8SendIndex)
{
	sint32 s32ReadNum = 0;
	char buf[FILETRANSFER_BUFFNUM + 100] = {0};
	SKDvrTransferFileReq stFileReq;

	if( u8SendIndex >= FILEUPLOAD_DRIVERNUM ||  stFileupLoadArry[u8SendIndex].u8UploadStatus != STATUS_UPLOADING)
	{
		print_level(SV_DEBUG,"Index:%d error!\n", u8SendIndex);
		return SV_FAILURE;
	}

	s32ReadNum = read(stFileupLoadArry[u8SendIndex].s32Fd, buf+sizeof(SKDvrTransferFileReq), FILETRANSFER_BUFFNUM);
	if(s32ReadNum <= 0)
	{
		//print_level(SV_ERROR,"s32ReadNum:%d %s file:%s\n", s32ReadNum, strerror(errno), stFileupLoadArry[u8SendIndex].szFileName );
		SK_HEADER header;
		SKDvrTransferFileResultReq stResultReq = {0};
		stResultReq.uiSessionId = stFileupLoadArry[u8SendIndex].u32FileSessionId;

		if(s32ReadNum == 0)
		{
			stResultReq.common_rsp.ucResult = 0;
			strcpy((char*)stResultReq.common_rsp.szReason, "Finished!");
		}
		else if(s32ReadNum < 0)
		{
			stResultReq.common_rsp.ucResult = 1;
			strcpy((char*)stResultReq.common_rsp.szReason, "Read error!");
		}

		if(!stFileupLoadArry[u8SendIndex].bSendEnd)
		{
			pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RESULT_REQ, sizeof(SKDvrTransferFileResultReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
			pLink->sendToServer(&header, (char *)&stResultReq, sizeof(SKDvrTransferFileResultReq));
			stFileupLoadArry[u8SendIndex].bSendEnd = SV_TRUE;
			//sleep(2);
			return SV_SUCCESS;
		}
		else
		{
			return SV_SUCCESS;
		}
	}

	stFileReq.uiDataSize = s32ReadNum;
	stFileReq.uiFileOffset = stFileupLoadArry[u8SendIndex].u32FileOffest;
	stFileReq.uiSessionId = stFileupLoadArry[u8SendIndex].u32FileSessionId;
	stFileReq.uiFileSeq = stFileupLoadArry[u8SendIndex].u32FileSeq++;
	stFileupLoadArry[u8SendIndex].u32FileOffest += s32ReadNum;
	memcpy(buf, (char *)&stFileReq, sizeof(SKDvrTransferFileReq));
#if 1
	if (stFileupLoadArry[u8SendIndex].s32PrintCnt++ % 30 == 0)
	{
		print_level(SV_DEBUG,"sessid:%d, offeset:%d, seq:%d, datasize:%d fileName:%s\n",
		stFileReq.uiSessionId, stFileReq.uiFileOffset, stFileReq.uiFileSeq, stFileReq.uiDataSize,
		stFileupLoadArry[u8SendIndex].szFileName);
	}
#endif
	SK_HEADER header;
	pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_REQ, sizeof(SKDvrTransferFileReq)+s32ReadNum, SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&header, buf, sizeof(SKDvrTransferFileReq)+s32ReadNum);


	if(s32ReadNum < FILETRANSFER_BUFFNUM)
	{

		SK_HEADER header;
		SKDvrTransferFileResultReq stResultReq = {0};
		stResultReq.uiSessionId = stFileupLoadArry[u8SendIndex].u32FileSessionId;
		stResultReq.common_rsp.ucResult = 0;
		strcpy((char*)stResultReq.common_rsp.szReason, "Finished");
		if(!stFileupLoadArry[u8SendIndex].bSendEnd)
		{
			pProtocol->createHeader(&header, SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RESULT_REQ, sizeof(SKDvrTransferFileResultReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
			pLink->sendToServer(&header, (char *)&stResultReq, sizeof(SKDvrTransferFileResultReq));
			stFileupLoadArry[u8SendIndex].bSendEnd = SV_TRUE;
			//sleep(2);
		}
	}

	return SV_SUCCESS;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::setRecordFileFlag(UPLOAD_FILE_OPTS_E enUploadFileOpts, char *pFileName)
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))
    if (enUploadFileOpts < UPLOAD_FILE_ALARM_VIDEO || enUploadFileOpts > UPLOAD_FILE_ALARM_ALL)
    {
        return;
    }

    RECORDER_RenameFile(pFileName, strlen(pFileName));
#endif
}

void SV_NETWORK_FILEPRIVATEUPLOAD::removeFileFromList(char *pFileName)
{
    std::list<SV_STORAGE_FILE_PATH_S>::iterator list_itor;
    SV_STORAGE_FILE_PATH_S stStorageFilePath = {0};
    char szCmd[256]={0};

    for (list_itor = sendDriverFileList.begin(); list_itor != sendDriverFileList.end(); list_itor++)
    {
        stStorageFilePath = (*list_itor);
        if (NULL != strstr(stStorageFilePath.szFilePath, pFileName))
        {
            print_level(SV_INFO, "erase file: %s\n", pFileName);
            sendDriverFileList.erase(list_itor);

            /* 上传后删除/tmp/目录下文件 */
            char szDelDir[256] = {0};
            snprintf(szCmd,256,"rm -rf  %s",pFileName);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);

            /* 上传后删除/root/ID/目录下文件 */
            const char *pName = strrchr(pFileName, '/');  // 找到文件名
            if (pName)
            {
                pName++;
                snprintf(szCmd, 256, "rm -f  /root/ID/%s", pName);
                SAFE_System(szCmd, NORMAL_WAIT_TIME);
            }
            else
            {
                return;
            }

            /* 上传后删除/root/IDTMP/ID/目录下文件 */
            const char *pUnder = strchr(pName, '_');
            if (!pUnder) return;

            char target[128] = {0};
            strncpy(target, pUnder + 1, sizeof(target) - 1);  // 复制时间戳_类型.tar.gz
            char *ext = strstr(target, ".tar.gz");
            if (ext) *ext = '\0';  // 去掉后缀

            DIR *dir = opendir("/root/IDTMP/ID");
            if (!dir)
            {
                perror("opendir failed");
                return;
            }

            struct dirent *ptr;
            char fullPath[256];

            while ((ptr = readdir(dir)) != NULL)
            {
                if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)
                    continue;

                if (strstr(ptr->d_name, target))  // 文件名中包含目标字段
                {
                    snprintf(fullPath, sizeof(fullPath), "/root/IDTMP/ID/%s", ptr->d_name);
                    snprintf(szCmd,256,"rm -rf  %s",fullPath);
                    print_level(SV_INFO, "Deleting: %s\n", fullPath);
                    SAFE_System(szCmd, NORMAL_WAIT_TIME);
                }
            }
            closedir(dir);
            break;
        }
    }

    for (list_itor = sendWholeFileList.begin(); list_itor != sendWholeFileList.end(); list_itor++)
    {
        stStorageFilePath = (*list_itor);
        if (NULL != strstr(stStorageFilePath.szFilePath, pFileName))
        {
            print_level(SV_INFO, "erase file: %s\n", pFileName);
            sendWholeFileList.erase(list_itor);
#if 0
            /* 上传后删除目录下文件 */
            char szCmd[256]={0};
            char szDelDir[256] = {0};
            snprintf(szCmd,256,"rm -rf  %s",pFileName);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
            print_level(SV_INFO, "rm file: %s\n", pFileName);

            const char *pName = strrchr(pFileName, '/');  // 找到文件名
            if (!pName) return;

            const char *pUnder = strchr(pName, '_');
            if (!pUnder) return;

            char target[128] = {0};
            strncpy(target, pUnder + 1, sizeof(target) - 1);  // 复制时间戳_类型.tar.gz
            char *ext = strstr(target, ".tar.gz");
            if (ext) *ext = '\0';  // 去掉后缀

            // 打开 /root/ID 目录
            DIR *dir = opendir("/root/ID");
            if (!dir) {
                perror("opendir failed");
                return;
            }

            struct dirent *ptr;
            char fullPath[256];

            while ((ptr = readdir(dir)) != NULL)
            {
                if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)
                    continue;

                if (strstr(ptr->d_name, target))  // 文件名中包含目标字段
                {
                    snprintf(fullPath, sizeof(fullPath), "/root/ID/%s", ptr->d_name);
                    print_level(SV_INFO, "Deleting: %s\n", fullPath);
                    char cmd[300] = {0};
                    //snprintf(cmd, sizeof(cmd), "rm -rf \"%s\"", fullPath);
                    snprintf(szCmd,256,"rm -rf  %s",fullPath);
                    SAFE_System(szCmd, NORMAL_WAIT_TIME);
                }
            }
            closedir(dir);
#endif
            break;
        }
    }
}

/* 0x3022 */
void SV_NETWORK_FILEPRIVATEUPLOAD::processRegisterRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKCommonRsp *pRsp = (SKCommonRsp *)pData;
	print_level(SV_DEBUG,"OP=0x3022, pRsp->ucResult:%d, szReason:%s\n", pRsp->ucResult, pRsp->szReason);

	if( pRsp->ucResult == 0 )
	{
		bRegister = SV_TRUE;
	}
	else
	{
		clearArry();
		pLink->closeSocket();
	}
}

/* 0x3024 */
void SV_NETWORK_FILEPRIVATEUPLOAD::processUploadInfoRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
    //print_level(SV_INFO,"OP=0x3024\n");
	SKDvrPrepareToUploadFileRsp *pUploadRsp = (SKDvrPrepareToUploadFileRsp *)pData;
	//print_level(SV_DEBUG,"pRsp->ucResult:%d uiSessionId:%d uiUploadedSize:%u\n", pUploadRsp->ucResult, pUploadRsp->uiSessionId, pUploadRsp->uiUploadedSize);
	char *pJsonData = pData + sizeof(SKDvrPrepareToUploadFileRsp);
	char szFullPath[256] = {0};
	cJSON *json, *param;
	uint8 u8Type;

    json = cJSON_Parse(pJsonData);
	if (NULL == json)
	{
		print_level(SV_ERROR,"Error before: [%s]\n", cJSON_GetErrorPtr());
        return;
	}
	else
	{
		param = cJSON_GetObjectItem(json, "fn");
		if (param == SV_NULL)
		{
            print_level(SV_ERROR,"Error before: [%s]\n", cJSON_GetErrorPtr());
            return;
		}
	}

	sint32 s32Index  = getFileIndex(param->valuestring);
    if (s32Index < 0)
    {
        print_level(SV_ERROR, "not found file: %s in array.\n", param->valuestring);
        goto exit;
    }

	u8Type = stFileupLoadArry[s32Index].u8FileType;
	//print_level(SV_DEBUG,"get file in array[%d] type[%d]\n", s32Index, u8Type);

	if( pUploadRsp->ucResult == 0 ) //can upload
	{
	    //print_level(SV_INFO, "addFile2Arry can uplaod\n");
		addFile2Arry(pUploadRsp, param->valuestring);
	}
	else if(pUploadRsp->ucResult == 1) // file have exit
	{
		//print_level(SV_WARN,"File:%s have exited!\n", param->valuestring);
		if (SV_COMMON_getAlarmFileDir(m_enUploadFileOpts, param->valuestring, szFullPath) != SV_SUCCESS )
		{
			print_level(SV_ERROR,"SV_DVR_STORAGE_GetFileDirctory fail [%s]\n", param->valuestring);
			return;
		}

        print_level(SV_INFO, "file is exist in cms, szFullPath: %s\n", szFullPath);
        setRecordFileFlag(m_enUploadFileOpts, szFullPath);
        deleIndex(s32Index);
        removeFileFromList(szFullPath);
	}
	else
	{
		print_level(SV_WARN,"File:%s can not upload for reason[%d]!\n", param->valuestring, pUploadRsp->ucResult);
        if( SV_COMMON_getAlarmFileDir(m_enUploadFileOpts, param->valuestring, szFullPath) != SV_SUCCESS )
		{
			print_level(SV_ERROR,"SV_DVR_STORAGE_GetFileDirctory fail [%s]\n", param->valuestring);
			return;
		}

		if(stFileupLoadArry[s32Index].u8TimeOutCnt > 1)
		{
		    print_level(SV_WARN, "clean timeout file!\n");
    		stFileupLoadArry[s32Index].u8TimeOutCnt = 0;
            setRecordFileFlag(m_enUploadFileOpts, szFullPath);
		}
        else if (pUploadRsp->ucResult == 4)
        {
            deleIndex(s32Index);
            removeFileFromList(szFullPath);
        }
		else if(pUploadRsp->ucResult != 1)
		{
		    ++stFileupLoadArry[s32Index].u8TimeOutCnt;
		    print_level(SV_WARN,"can not upload timeout[%d]!\n", stFileupLoadArry[s32Index].u8TimeOutCnt);
		}
	}

exit:
	cJSON_Delete(json);

}

/* 0x3026 */
void SV_NETWORK_FILEPRIVATEUPLOAD::processUploadFileRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{

	SKDvrTransferFileRsp* pFileRsp = (SKDvrTransferFileRsp*)pData;
	//print_level(SV_DEBUG,"SessionId:%d FileSeq:%d\n", pFileRsp->uiSessionId, pFileRsp->uiFileSeq);

	for(int i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if(stFileupLoadArry[i].u32FileSessionId == pFileRsp->uiSessionId)
		{
			stFileupLoadArry[i].u32RecFileSeq = pFileRsp->uiFileSeq + 1;
		}
	}
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if(stFileupLoadArry[i].u32FileSessionId == pFileRsp->uiSessionId)
		{
			stFileupLoadArry[i].u32RecFileSeq = pFileRsp->uiFileSeq + 1;
		}
	}
}

/* 0x3028*/
void SV_NETWORK_FILEPRIVATEUPLOAD::processUploadResultRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SKDvrTransferFileResultRsp * pResult = (SKDvrTransferFileResultRsp *)pData;
    std::list<SV_STORAGE_FILE_PATH_S>::iterator list_itor;
    SV_STORAGE_FILE_PATH_S stStorageFilePath = {0};
	//print_level(SV_DEBUG,"SessionId:------------------%d\n", pResult->uiSessionId);

	char szTempFileName[STORAGE_FULLPATH_LEN] = {0};
	uint8 u8Type = 0;
	if( getSessionIdFileName(pResult->uiSessionId, szTempFileName) == SV_SUCCESS )
	{
		getFileType(szTempFileName, &u8Type);
        print_level(SV_DEBUG, "upload file success! rename: %s\n",szTempFileName);
        if (strstr(szTempFileName, "driverInfo") == NULL)
        {
            setRecordFileFlag(m_enUploadFileOpts, szTempFileName);
        }
        print_level(SV_INFO, "szTempFileName=%s\n", szTempFileName);
        removeFileFromList(szTempFileName);
	}
	deleSessionID(pResult->uiSessionId);

}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::getFileType(char *pFileName, uint8* pu8Type)
{
	for(int i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if( strstr(stFileupLoadArry[i].szFileName, pFileName) )
		{
			*pu8Type = stFileupLoadArry[i].u8FileType;
			return SV_SUCCESS;
		}
	}
	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( strstr(stFileupLoadArry[i].szFileName, pFileName) )
		{
			*pu8Type = stFileupLoadArry[i].u8FileType;
			return SV_SUCCESS;
		}
	}

	return SV_FAILURE;
}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::getSessionIdFileName(uint32 u32SessionId, char *pFileName)
{
	for(int i=8; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if( stFileupLoadArry[i].u32FileSessionId == u32SessionId )
		{
			//strncpy(pFileName, stFileupLoadArry[i].szFileName, STORAGE_FULLPATH_LEN);
			strncpy(pFileName, stFileupLoadArry[i].szFullPath, STORAGE_FULLPATH_LEN);
			return SV_SUCCESS;
		}
	}

	for(int i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if( stFileupLoadArry[i].u32FileSessionId == u32SessionId )
		{
			//strncpy(pFileName, stFileupLoadArry[i].szFileName, STORAGE_FULLPATH_LEN);
			strncpy(pFileName, stFileupLoadArry[i].szFullPath, STORAGE_FULLPATH_LEN);
			return SV_SUCCESS;
		}
	}

	return SV_FAILURE;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::deleIndex(uint32 u32Index)
{
	if(stFileupLoadArry[u32Index].s32Fd > 0)
	{
		close(stFileupLoadArry[u32Index].s32Fd);
		stFileupLoadArry[u32Index].s32Fd = -1;
	}

	memset(stFileupLoadArry[u32Index].szFullPath, 0, 256);
	memset(stFileupLoadArry[u32Index].szFileName, 0, STORAGE_NAME_LEN);
	stFileupLoadArry[u32Index].u8UploadStatus = STATUS_NOFILE;
	stFileupLoadArry[u32Index].u32FileSessionId = 0;
	stFileupLoadArry[u32Index].u64FileSize = 0;
	stFileupLoadArry[u32Index].s32PrintCnt = 0;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::deleSessionID(uint32 u32SessionId)
{
	for(int i=0; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if (stFileupLoadArry[i].u32FileSessionId == u32SessionId)
		{
            if (0 != u32SessionId)
            {
                print_level(SV_INFO, "delete session id: %d, index: %d\n", u32SessionId, i);
            }
			deleIndex(i);
			break;
		}
	}
}

void SV_NETWORK_FILEPRIVATEUPLOAD::processStopUploadFile(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{
	SK_HEADER stHeader;
	SKInformDvrStopTransferFileReq * pStopFile = (SKInformDvrStopTransferFileReq *)pData;
	print_level(SV_DEBUG,"uiSessionId:%d\n", pStopFile->uiSessionId);

	pProtocol->createHeader(&stHeader, SK_CMS_SERVER_INFORM_DVR_STOP_TRANSFER_FILE_RSP,
		sizeof(SKInformDvrStopTransferFileReq), SK_DEFAULT_FILE_SAVE_SERVER_ID);
	pLink->sendToServer(&stHeader, (char *)pStopFile, sizeof(SKInformDvrStopTransferFileReq));


}

sint32 SV_NETWORK_FILEPRIVATEUPLOAD::processFileTransferRsp(SK_HEADER *pHeader, char *pData, uint32 *pDataSize)
{

	SV_CHECK(pData);
	switch(pHeader->usCode)
	{
		print_level(SV_DEBUG,"opCode:%d\n", pHeader->usCode);

		case SK_DVR_TO_CMS_SERVER_CONNECT_FILE_SAVE_SERVER_RSP: processRegisterRsp(pHeader, pData, pDataSize); break;
		case SK_DVR_TO_CMS_SERVER_PREPARE_TO_UPLOAD_FILE_RSP: processUploadInfoRsp(pHeader, pData, pDataSize); break;
		case SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RSP: processUploadFileRsp(pHeader, pData, pDataSize);break;
		case SK_DVR_TO_CMS_SERVER_TRANSFER_FILE_RESULT_RSP: processUploadResultRsp(pHeader, pData, pDataSize);break;
		case SK_CMS_SERVER_INFORM_DVR_STOP_TRANSFER_FILE_REQ: processStopUploadFile(pHeader, pData, pDataSize);break;
		default: break;
	}

	return SV_SUCCESS;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isFilePrepare(const char *pFileName)
{

    /* 先检查上传的文件是不是人脸，是人脸就检查8和9 */
    if (NULL != strstr(pFileName, "driverInfo"))
    {
        for(uint8 i=8; i<FILEUPLOAD_DRIVERNUM; i++)
    	{
    	    print_level(SV_INFO,"driverInfo need to be upload!\n");
    		if (strncmp(stFileupLoadArry[i].szFileName, pFileName, 256)  == 0)
    		{
    		    print_level(SV_INFO,"FileupLoadArry=%s! pFileName=%s!\n",stFileupLoadArry[i].szFileName,pFileName);
    			return SV_TRUE;
    		}
    	}
    }

    for(uint8 i=0; i<FILEUPLOAD_MAXNUM; i++)
	{
		if (strncmp(stFileupLoadArry[i].szFileName, pFileName, 256)  == 0)
		{
			return SV_TRUE;
		}
	}
    print_level(SV_INFO, "QTest-----------\n");
	return SV_FALSE;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isFileSending(const char *pFilefullPath)
{
	for(uint8 i=0; i<FILEUPLOAD_DRIVERNUM; i++)
	{
		if( strncmp(stFileupLoadArry[i].szFullPath, pFilefullPath, 256)  == 0 )
		{
			return SV_TRUE;
		}
	}

	return SV_FALSE;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::sendFileInfoArry()
{
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))

    uint8 u8Type = TYPE_VIDEO;
	uint64 u64FileSzie = 0;
    uint8 u8AccessIndex = -1;
    char szFileName[128] = {0};

    if(!isArryFull1())
    {
        //print_level(SV_INFO, "sendDriverFileList.size = %d\n", sendDriverFileList.size());
        list<SV_STORAGE_FILE_PATH_S>::iterator iter;
        for (iter = sendDriverFileList.begin(); iter != sendDriverFileList.end(); iter++)
        {
		    print_level(SV_INFO, "sendDriverFileList.path= %s\n", iter->szFilePath);
            SV_COMMON_cutOutName(iter->szFilePath, szFileName);
            print_level(SV_INFO, "sendDriverFileList.name=%s\n", szFileName);

            if (!isFilePrepare(szFileName))
            {
                print_level(SV_INFO, "QTest-----------\n");
                /* 0x3023发送上传文件信息给服务器 */
				u64FileSzie = sendFileInfo(iter->szFilePath);
                u8AccessIndex = getAccessIndex1();
                print_level(SV_INFO, "u8AccessIndex=%u\n",u8AccessIndex);
                if (SV_FAILURE != u8AccessIndex)
                {
                    if (NULL != strstr(szFileName, ".gz"))
                        u8Type = TYPE_FACE;
                    setStatusPrepare(u8AccessIndex, szFileName, u8Type, u64FileSzie);
                }
            }
            if (isArryFull1())
            {
                break;
            }
        }
    }

    if( !isArryFull() && !isSendListEmpty() )
    {
		list<SV_STORAGE_FILE_PATH_S>::iterator iter;
		for(iter = sendWholeFileList.begin(); iter != sendWholeFileList.end(); iter++ )
		{
            if (!sendDriverFileList.empty())
            {
                print_level(SV_INFO, "sendDriverFileList is not empty!\n");
                break; // 每次遍历前，都先判断有无人脸信息
            }

		    /* 遍历全部推到列表里的待上传文件 */
		    //print_level(SV_INFO, "iter->szFilePath= %s\n", iter->szFilePath);
            SV_COMMON_cutOutName(iter->szFilePath, szFileName);
            //print_level(SV_INFO, "szFileName=%s\n", szFileName);

            /* 判断szFileName是否已经在队列里了，为了避免上传同一个文件 */
			if (!isFilePrepare(szFileName))
			{
				u64FileSzie = sendFileInfo(iter->szFilePath);
                u8AccessIndex = getAccessIndex();
                //print_level(SV_INFO, "u8AccessIndex=%u\n",u8AccessIndex);
                if (SV_FAILURE != u8AccessIndex)
                {
                    if (NULL != strstr(szFileName, ".mp4") || NULL != strstr(szFileName, ".avi"))
                        u8Type = TYPE_VIDEO;
                    else if (NULL != strstr(szFileName, ".jpg"))
                        u8Type = TYPE_JPG;
                    else if (NULL != strstr(szFileName, ".gz"))
                        u8Type = TYPE_FACE;
				    setStatusPrepare(u8AccessIndex, szFileName, u8Type, u64FileSzie);
                }
            }

			if (isArryFull())
			{
				break;
			}
		}
	}
#endif
	return ;
}

sint32  SV_NETWORK_FILEPRIVATEUPLOAD::updateSendList()
{
    sint32 s32Ret = SV_SUCCESS;
    sint32 s32Driver = SV_SUCCESS;
    //char stFilePath[256] = {0};

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32C4))
	clearArry();

    switch (m_enUploadFileOpts)
    {
        case UPLOAD_FILE_OFF:
            sendWholeFileList.clear();
            break;

        case UPLOAD_FILE_ALARM_VIDEO:
        case UPLOAD_FILE_ALARM_PICTURE:
        case UPLOAD_FILE_ALARM_ALL:
            s32Ret = scanRecorderFile(m_enUploadFileOpts, sendWholeFileList, SV_TRUE);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "scanRecorderFile failed\n");
            }
            break;

        default:
            break;
    }
#endif

#if 0
    s32Driver = scanRecorderFile(m_enUploadFileOpts, sendWholeFileList, SV_TRUE);

#endif
#if 1
	SV_STORAGE_FILE_PATH_S stFilePath = {0};
    DIR *dir = NULL;
    struct dirent *ptr = NULL;

    sendDriverFileList.clear();

	if ((dir=opendir("/tmp")) == NULL)
	{
	 	perror("Open dir error...");
		print_level(SV_ERROR, "driverinfo open error\n");
		s32Driver = -1;
	}
	else
	{
        while ((ptr = readdir(dir)) != NULL)
        {
            if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)
                continue;
            if (strstr(ptr->d_name, "tar.gz") != NULL)
            {
                //strcpy(stFilePath.szFilePath, ptr->d_name);
                snprintf(stFilePath.szFilePath, sizeof(stFilePath.szFilePath), "/tmp/%s", ptr->d_name);
                print_level(SV_INFO, "szFilePath=%s\n", stFilePath.szFilePath);

                /* 推入司机信息上传列表，与录像上传列表分开 */
                sendDriverFileList.push_back(stFilePath);
                //sendWholeFileList.push_back(stFilePath);
                print_level(SV_INFO, "[Debug] sendDriverFileList.size() = %lu\n", sendDriverFileList.size());
                s32Driver = 0;
            }
        }
        closedir(dir);
    }
#endif

    if (s32Ret == 0 || s32Driver == 0)
    {
        return 0;  // 成功
    }

    return -1;  // 两者都失败

}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isSendListEmpty()
{
 	if( sendWholeFileList.empty())
	{
		return SV_TRUE;
	}

	return SV_FALSE;
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isSendDriverListEmpty()
{
 	if(sendDriverFileList.empty())
	{
		return SV_TRUE;
	}

	return SV_FALSE;
}

void SV_NETWORK_FILEPRIVATEUPLOAD::closeFd()
{
    print_level(SV_INFO, "close all fd\n");
    clearArry();
}

void SV_NETWORK_FILEPRIVATEUPLOAD::run()
{
    sint32 s32Ret;
	SK_HEADER header;
	char dataBuf[NETWORK_MAX_RECVSIZE];
	uint32 u32DataSize = 0, u32TimeoutCount = 0;
	static uint32 u32Count;
    UPLOAD_FILE_OPTS_E enLastUploadFileOpts = SV_NETWORK_DVRINFO::getInstance()->getUploadFileType();

	while (pState->getNetworkRunning())
	{
	    /* 如果有人脸更新，跳出while直接更新列表 */
	    //print_level(SV_WARN, "pState->getFaceInfoUpdate=%d\n", pState->getFaceInfoUpdate());
 		while (!pState->getControlRegisterFlag() ||(!pState->getFaceInfoUpdate() && !pDvrinfo->getStorageStatus(NULL)))
		{
			if (!pState->getNetworkRunning())
			{
			    print_level(SV_ERROR, "State error! return \n");
				return;
			}
            sendWholeFileList.clear();
            sendDriverFileList.clear();
			clearArry();
			sleep(1);
		}

        /* 上传文件类型配置项更新后，更新枚举参数 */
        if (pState->getConfigUpdate())
        {
            print_level(SV_WARN, "ConfigUpdate! \n");
            m_enUploadFileOpts = SV_NETWORK_DVRINFO::getInstance()->getUploadFileType();
            pState->setConfigUpdate(false);

            if (m_enUploadFileOpts != enLastUploadFileOpts && pDvrinfo->getStorageStatus(NULL))
            {
                print_level(SV_INFO, "get now uploadFileOpts: %d, lastOpts: %d\n", m_enUploadFileOpts, enLastUploadFileOpts);
                pState->setPrivateFileUpdate(true);
            }
            enLastUploadFileOpts = m_enUploadFileOpts;
            pState->setFaceInfoUpdate(true);
        }

        /* 注册人脸信息更新后,更新上传列表 */
        //print_level(SV_WARN, "before updateSendList!\n");
        if (pState->getFaceInfoUpdate())
        {
            print_level(SV_WARN, "updateSendList!\n");
            s32Ret = updateSendList();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "updateSendList failed, try again now!\n");
                sleep_ms(3000);
                continue;
            }
            else
            {
                /* 更新后置为0 */
                pState->setFaceInfoUpdate(false);
            }
        }

        if (!isSendListEmpty() && pDvrinfo->getStorageStatus(NULL))
        {
            print_level(SV_WARN, "last upload file list is not empty, reload file list now!\n");
            pState->setPrivateFileUpdate(true);
        }

        /* 注册报警生成文件后，更新上传文件列表 */
        if (pState->getPrivateFileUpdate())
        {
            //print_level(SV_WARN, "updateSendList!\n");
            s32Ret = updateSendList();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_WARN, "updateSendList failed, try again now!\n");
                sleep_ms(3000);
                continue;
            }
            else
            {
                pState->setPrivateFileUpdate(false);
            }
        }

        /* 录像上传和司机上传列表都为空 */
		if (isSendListEmpty() && isSendDriverListEmpty())
		{
			if(u32Count++ > 90)
			{
 				u32Count = 0;
			}
			//print_level(SV_DEBUG,"File private upload Send List is empty!\n");
			for(int i=0; i<5; i++)
			{
				sleep(1);
				if( !pState->getNetworkRunning() )
				{
					return;
				}
			}
			continue;
		}
		else
		{
			u32Count = 0;
		}

		sleep(1);
		pLink->closeSocket();
		pLink->setServerIp((const char *)pDvrinfo->getIpAddr().c_str());
		pLink->setDeviceName((const char *)pDvrinfo->getNetCardName().c_str());
		pLink->setPort(u32Port);

		if ((isSendListEmpty()&&isSendDriverListEmpty()) || !pState->getControlRegisterFlag())
		{
			pLink->closeSocket();
			sleep(1);
		}
		else
		{
			while (!pLink->isAvalible())
			{
				pLink->setServerIp((const char *)pDvrinfo->getIpAddr().c_str());
				pLink->setDeviceName((const char *)pDvrinfo->getNetCardName().c_str());
				pLink->setPort(u32Port);
				if (pLink->initConnect() != SV_SUCCESS)
				{
					print_level(SV_WARN,"FileUpload link connect error \n");
					for(int i=0; i<5; i++)
					{
						sleep(1);
						if( !pState->getNetworkRunning() )
						{
							return;
						}
					}
					continue;
				}
				else
				{
					print_level(SV_DEBUG,"FileUpload link connect success\n");
				}
			}
			/* 0x3021注册上传请求 */
			sendRegsiter();
			sleep(5);

			while (pState->getNetworkRunning())
			{
                /* 注册人脸信息更新后,更新上传列表 */
                //print_level(SV_WARN, "------before updateSendList!---------\n");
                if (pState->getFaceInfoUpdate())
                {
                    print_level(SV_WARN, "updateSendList!\n");
                    s32Ret = updateSendList();
                    if (SV_SUCCESS != s32Ret)
                    {
                        print_level(SV_WARN, "updateSendList failed, try again now!\n");
                        sleep_ms(3000);
                        continue;
                    }
                    else
                    {
                        /* 更新后置为0 */
                        pState->setFaceInfoUpdate(false);
                    }
                }
				if ((!pDvrinfo->getStorageStatus(NULL) && pState->getFaceInfoUpdate())|| !pState->getControlRegisterFlag() || pState->getConfigUpdate())
				{
				    print_level(SV_INFO, "1=%d,2=%d,3=%d,4=%d\n",!pDvrinfo->getStorageStatus(NULL), !pState->getFaceInfoUpdate(),!pState->getControlRegisterFlag(),pState->getConfigUpdate());
					print_level(SV_WARN,"Filetransfer break\n");
					break;
				}

				fd_set read_set;
				FD_ZERO(&read_set);
				if (pLink->isAvalible())
				{
 					FD_SET(pLink->getSocket(), &read_set);
				}
				else
				{
					print_level(SV_WARN,"Filetransfer break\n");
					break;
				}

				struct timeval tv;
				tv.tv_sec = 1;
				tv.tv_usec = 0;  //1ms
				sint32 s32MaxFd = pLink->getSocket() + 1;
				sint32 s32Ret = select(s32MaxFd, &read_set, NULL, NULL, &tv);
				if (s32Ret < 0)
				{
					print_level(SV_ERROR,"Filetransfer select error\n");
				}
				else if (s32Ret == 0)
				{
				    /* 接收超时 */
				    //print_level(SV_INFO,"---------timeout recev data---------\n");
	 				if (u32TimeoutCount++ > 30)
					{
                        u32TimeoutCount = 0;
						bRegister = SV_FALSE;
						print_level(SV_WARN,"Filetransfer break\n");
						break;
					}

					if (!bRegister)
					{
						continue;
					}

					if (isSendListEmpty() && isArryEmpty())
					{
					    if (pState->getPrivateFileUpdate())
					    {
                            print_level(SV_WARN, "updateSendList\n");
					        updateSendList();
					        pState->setPrivateFileUpdate(false);
					    }

                        #if 0
						if( isSendListEmpty() && isArryEmpty() )
						{
							bRegister = SV_FALSE;
							print_level(SV_WARN,"Filetransfer break\n");
							break;
						}
                        #endif
					}

					if (!isArryFull())
					{
                        if (!isSendListEmpty()||!isSendDriverListEmpty())
                        {
    						sendFileInfoArry(); //0x3023
                        }
                        else
                        {
                            bRegister = SV_FALSE;
                            print_level(SV_WARN, "array is no full but send list is empty, Filetransfer break.\n");
							break;
                        }
					}
					else
					{
						sendFileInArry();
					}
				}
				else
				{
				    //print_level(SV_INFO,"---------Normal recev data---------\n");
					u32TimeoutCount = 0;
					//recev data
					if (pLink->recvFromServer(&header, dataBuf, &u32DataSize) < 0)
					{
						print_level(SV_ERROR,"Filetransfer recev server error\n");
						break;
					}

		 			processFileTransferRsp(&header, dataBuf, &u32DataSize);//接收服务器回复逻辑0x3022/24/26/28/29
		 			sendFileInArry();
					sendFileInfoArry(); //0x3023
				}
			}
		}
	}
}

SV_BOOL SV_NETWORK_FILEPRIVATEUPLOAD::isUploading()
{
	return (SV_BOOL)!isArryEmpty();
}

void SV_NETWORK_FILEPRIVATEUPLOAD::exitFileprivate()
{
    clearArry();
    pLink->closeSocket();
}




















































