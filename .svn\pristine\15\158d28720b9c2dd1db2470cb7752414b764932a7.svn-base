/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：network.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-12-18

文件功能描述: 定义网络模块功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _NETWORK_H_
#define _NETWORK_H_

#include "common.h"
#include "config.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/* 异常处理回调指针 */
typedef sint32 (*EXCEPTION_CALLBACK)(void);

typedef struct tag_network_init_S
{
    EXCEPTION_CALLBACK    pfExcCallbak;
    CFG_NETWORK_PARAM     stNetworkParam;
}NETWORK_INIT_S;

/******************************************************************************
 * 函数功能: 初始化网络模块
 * 输入参数: pstNetworkParam --- 网络参数
 * 输出参数: pstNetworkStat --- 网络状态
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_Init(NETWORK_INIT_S *pstNetworkInit, NETWORK_STAT_S *pstNetworkStat);

/******************************************************************************
 * 函数功能: 去初始化配置模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_Fini();

/******************************************************************************
 * 函数功能: 启动网络监控线程
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_Start();

/******************************************************************************
 * 函数功能: 停止网络监控线程
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_Stop();

/******************************************************************************
 * 函数功能: 配置网络工作参数
 * 输入参数: pstNetworkParam --- 网络参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_SetConfig(CFG_NETWORK_PARAM *pstNetworkParam);

/******************************************************************************
 * 函数功能: 获取以太网络eth0实际的地址
 * 输入参数: 无
 * 输出参数: pszMacAddr --- MAC地址
             pszIpaddr --- IP地址
             pszSubmask --- 子网掩码
             pszGateway --- 网关地址
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_GetEthAddr(char *pszMacAddr, char *pszIpaddr, char *pszSubmask, char *pszGateway);

/******************************************************************************
 * 函数功能: 判断以太网络eth0是否可用
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 可用
             SV_FALSE - 不可用
 * 注意    : 无
 *****************************************************************************/
extern SV_BOOL NETWORK_IsEthAvailable();

/******************************************************************************
 * 函数功能: 配置网络工作参数并强制重启网络
 * 输入参数: pstNetworkParam --- 网络参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_SetConfig_Reboot(CFG_NETWORK_PARAM *pstNetworkParam, SV_BOOL bReboot);

/******************************************************************************
 * 函数功能: 检查要配置的静态STA IP地址和目前被分配的IP地址是否相同以及网段是否相同
 * 输入参数: pszStaIpAddr --- 配置的静态STA IP
 * 输出参数: bSame --- 俩IP是否相同
             bSegMatch --- 俩IP网段是否相同
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_CheckStaIpaddr(char *pszStaIpAddr, SV_BOOL *bSame, SV_BOOL *bSegMatch);

/******************************************************************************
 * 函数功能: 动态设置wifi模块发射功率
 * 输入参数: bSet --- 是否设置为动态功率(FALSE则使用txt脚本配置的功率)
 * 输出参数: u32Db --- 动态功率值 (bSet=TRUE时才有效)
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 NETWORK_Set_TxPower(SV_BOOL bSet, uint32 u32Db);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _NETWORK_H_ */

