# 1. 文件说明：基于FFmpeg底层接口封装的外层接口，提升程序的可复用性。这里是基于bb808移植过来的，因此仍保留大体流程，便于后续增加相应的函数调用。
----
# 2.包含：pb_convert.cpp、pb_convert.h、pb_interface.cpp、pb_interface.h 四个文件

## 2.1 pb_convert.cpp：包括FFmpegOpenAvi、FFmPegCloseAvi、FFmPegSEEKAvi、FFmpeReadAvi的实现方法。通过PB_SetState传递不同type进行相对应的调用。

**注意**：这里需要提醒的是Open和Close接口都采用的是手动（avio_open）实现，所以必须手动添加相对应的关闭操操作；另外也可使用自动打开关闭接口（avformat_open_input）
               更详细的信息可以参考博客：https://blog.csdn.net/wutongshu1321/article/details/143976175

## 2.2 pb_convert.h：头文件

## 2.3 pb_interface.cpp：包括五个外层调用接口，PLAYBACK_OpenAvi、PLAYBACK_CloseAvi、PLAYBACK_SeekAvi、PLAYBACK_ReadAvi、PLAYBACK_GetFiledir

## 2.4 sv_pb_interface.h：头文件
----