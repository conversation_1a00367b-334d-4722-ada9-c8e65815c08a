#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <ctype.h>

#define FORBID_LOG

#include "common.h"
#include "print.h"
#include "CUnit.h"
#include "cJSON.H"
#include "libhttp.h"
#include "sharefifo.h"
#include "faac.h"
#include "flv_format_define.h"

/***************************************************************
*-# 用例编号: itest_cJSON_Create_001
*-# 测试标题: 发现设备
*-# 测试类型: 测试CJSON Create 各个方法是否正确
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_cJSON_Create_001()
{
    sint32 s32Ret = 0;
    cJSON *pstJson = NULL;

    pstJson = cJSON_CreateNull();
    print_level(SV_DEBUG, "%s, %d, %d, %d\n", cJSON_Print(pstJson), cJSON_IsNull(pstJson), cJSON_IsInvalid(pstJson), cJSON_IsNumber(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsNull(pstJson), 1);
    cJSON_Delete(pstJson);

    pstJson = cJSON_CreateTrue();
    print_level(SV_DEBUG, "%s\n", cJSON_Print(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsTrue(pstJson), 1);
    cJSON_Delete(pstJson);

    pstJson = cJSON_CreateFalse();
    print_level(SV_DEBUG, "%s\n", cJSON_Print(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsFalse(pstJson), 1);
    cJSON_Delete(pstJson);

    pstJson = cJSON_CreateBool(1);
    print_level(SV_DEBUG, "%s\n", cJSON_Print(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsBool(pstJson), 1);
    cJSON_Delete(pstJson);

    pstJson = cJSON_CreateNumber(100);
    print_level(SV_DEBUG, "%s\n", cJSON_Print(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsNumber(pstJson), 1);
    cJSON_Delete(pstJson);

    pstJson = cJSON_CreateString("hello world!");
    print_level(SV_DEBUG, "%s\n", cJSON_Print(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsString(pstJson), 1);
    cJSON_Delete(pstJson);

    pstJson = cJSON_CreateArray();
    print_level(SV_DEBUG, "%s\n", cJSON_Print(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsArray(pstJson), 1);
    cJSON_Delete(pstJson);

    pstJson = cJSON_CreateObject();
    print_level(SV_DEBUG, "%s\n", cJSON_Print(pstJson));
    CU_ASSERT_EQUAL(cJSON_IsObject(pstJson), 1);
    cJSON_Delete(pstJson);
}


/***************************************************************
*-# 用例编号: itest_cJSON_Create_002
*-# 测试标题: 测试CJSON CreateObject 和 CJSON AddItemToObject是否正确
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_cJSON_Create_002()
{
    sint32 s32Ret = 0;
    cJSON *pstJsonWifi = NULL, *pstApMode = NULL, *pstStaMode = NULL;
    cJSON *pstApList = NULL, *pstSsidPwd = NULL;

    pstJsonWifi = cJSON_CreateObject();
    pstApMode = cJSON_CreateObject();
    cJSON_AddItemToObject(pstApMode, "enable", cJSON_CreateBool(1));
    cJSON_AddItemToObject(pstApMode, "ssid", cJSON_CreateString("hd900-hd1"));
    cJSON_AddItemToObject(pstApMode, "password", cJSON_CreateString("123456789"));
    cJSON_AddItemToObject(pstJsonWifi, "apMode", pstApMode);
    pstStaMode = cJSON_CreateObject();
    cJSON_AddItemToObject(pstStaMode, "enable", cJSON_CreateFalse());
    pstApList = cJSON_CreateArray();
    pstSsidPwd = cJSON_CreateObject();
    cJSON_AddItemToObject(pstSsidPwd, "ssid", cJSON_CreateString("xiaomi"));
    cJSON_AddItemToObject(pstSsidPwd, "password", cJSON_CreateString("123456789"));
    cJSON_AddItemToArray(pstApList, pstSsidPwd);
    pstSsidPwd = cJSON_CreateObject();
    cJSON_AddItemToObject(pstSsidPwd, "ssid", cJSON_CreateString("tp-link"));
    cJSON_AddItemToObject(pstSsidPwd, "password", cJSON_CreateString("123456789"));
    cJSON_AddItemToArray(pstApList, pstSsidPwd);
    pstSsidPwd = cJSON_CreateObject();
    cJSON_AddItemToObject(pstSsidPwd, "ssid", cJSON_CreateString("ipcap"));
    cJSON_AddItemToObject(pstSsidPwd, "password", cJSON_CreateString("123456789"));
    cJSON_AddItemToArray(pstApList, pstSsidPwd);
    cJSON_AddItemToObject(pstStaMode, "apList", pstApList);
    cJSON_AddItemToObject(pstJsonWifi, "staMode", pstStaMode);
    print_level(SV_DEBUG, "\n%s\n", cJSON_Print(pstJsonWifi));
    cJSON_Delete(pstJsonWifi);
}


/***************************************************************
*-# 用例编号: itest_cJSON_Parse_001
*-# 测试标题: 测试cJSON_Parse 序列化数据包是否正确
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_cJSON_Parse_001()
{
    sint32 s32Ret = 0;
    cJSON *pstJson = NULL;
    char *pszJson = "{}";

    pstJson = cJSON_Parse("");
    print_level(SV_DEBUG, "%#x, %d, %d, %s\n", pstJson, cJSON_IsInvalid(pstJson), cJSON_IsNull(pstJson), cJSON_GetErrorPtr());
    cJSON_Delete(pstJson);

    pstJson = cJSON_Parse("{}");
    print_level(SV_DEBUG, "%#x, %d, %d, %s\n", pstJson, cJSON_IsInvalid(pstJson), cJSON_IsNull(pstJson), cJSON_GetErrorPtr());
    cJSON_Delete(pstJson);

    pstJson = cJSON_Parse("{123456}");
    print_level(SV_DEBUG, "%#x, %d, %d, %s\n", pstJson, cJSON_IsInvalid(pstJson), cJSON_IsNull(pstJson), cJSON_GetErrorPtr());
    cJSON_Delete(pstJson);

    pstJson = cJSON_Parse("{abad,");
    print_level(SV_DEBUG, "%#x, %d, %d, %s\n", pstJson, cJSON_IsInvalid(pstJson), cJSON_IsNull(pstJson), cJSON_GetErrorPtr());
    cJSON_Delete(pstJson);
}


/***************************************************************
*-# 用例编号: itest_cJSON_Parse_002
*-# 测试标题: 测试cJSON_GetObjectItemCaseSensitive 和 cJSON_Parse
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_cJSON_Parse_002()
{
    sint32 s32Ret = 0;
    cJSON *pstJsonWifi = NULL, *pstApMode = NULL, *pstStaMode = NULL;
    cJSON *pstApList = NULL, *pstSsidPwd = NULL, *pstJson;
    char *pszJson = "{\r\n"
                    "    \"apMode\": {\r\n"
                    "        \"enable\": true,\r\n"
                    "        \"ssid\": \"hd900-hd1\",\r\n"
                    "        \"password\": \"123456789\"\r\n"
                    "    },\r\n"
                    "    \"staMode\": {\r\n"
                    "        \"enable\": true,\r\n"
                    "        \"apList\": [\r\n"
                    "           {\r\n"
                    "             \"ssid\": \"xiaomi\",\r\n"
                    "             \"password\": \"123456789\"\r\n"
                    "           },\r\n"
                    "           {\r\n"
                    "             \"ssid\": \"tp-link\",\r\n"
                    "             \"password\": \"123456789\"\r\n"
                    "           },\r\n"
                    "           {\r\n"
                    "             \"ssid\": \"ipcap\",\r\n"
                    "             \"password\": \"123456789\"\r\n"
                    "           }\r\n"
                    "        ]\r\n"
                    "    }\r\n"
                    "}";

    print_level(SV_DEBUG, "\n%s\n", pszJson);
    pstJsonWifi = cJSON_Parse(pszJson);
    print_level(SV_DEBUG, "%#x, %d\n", pstJsonWifi, cJSON_IsObject(pstJsonWifi));
    print_level(SV_DEBUG, "\n%s\n", cJSON_PrintUnformatted(pstJsonWifi));
    pstApMode = cJSON_GetObjectItemCaseSensitive(pstJsonWifi, "apMode");
    CU_ASSERT_EQUAL(cJSON_IsObject(pstApMode), 1);
    
    pstJson = cJSON_GetObjectItemCaseSensitive(pstApMode, "enable");
    CU_ASSERT_EQUAL(cJSON_IsBool(pstJson), 1);
    print_level(SV_DEBUG, "%d\n", pstJson->valueint);

    pstJson = cJSON_GetObjectItemCaseSensitive(pstApMode, "ssid");
    CU_ASSERT_EQUAL(cJSON_IsString(pstJson), 1);
    print_level(SV_DEBUG, "%s\n", pstJson->valuestring);

    pstJson = cJSON_GetObjectItemCaseSensitive(pstApMode, "password");
    CU_ASSERT_EQUAL(cJSON_IsString(pstJson), 1);
    print_level(SV_DEBUG, "%s\n", pstJson->valuestring);
    
    pstStaMode = cJSON_GetObjectItemCaseSensitive(pstJsonWifi, "staMode");
    CU_ASSERT_EQUAL(cJSON_IsObject(pstApMode), 1);
    
    cJSON_Delete(pstJsonWifi);
}


/***************************************************************
*-# 用例编号: itest_HTTP_SVR_Init_001
*-# 测试标题: 测试HTTP服务器初始化
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_HTTP_SVR_Init_001()
{
    sint32 s32Ret = 0;
    HTTP_CFG_PARAM_S stInitParam = {0};

    stInitParam.u32ServicePort = 8080;
    strcpy(stInitParam.szAdminPassword, "123456");
    s32Ret = HTTP_SVR_Init(&stInitParam);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    sleep_ms(1000);

    s32Ret = HTTP_SVR_Fini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}


/***************************************************************
*-# 用例编号: itest_HTTP_SVR_Start_001
*-# 测试标题: 测试HTTP服务器运行
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_HTTP_SVR_Start_001()
{
    sint32 s32Ret = 0;
    sint32 chChoice = 0;
    HTTP_CFG_PARAM_S stInitParam = {0};
    char szUuid[128];
    SV_BOOL bKeyAuth = SV_FALSE;

    //SAFE_System("cleanipcs", 1000);
    //s32Ret = CONTROL_Init(szUuid, &bKeyAuth);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONTROL_Init failed. [err=%#x]\n", s32Ret);
        return;
    }
    //s32Ret = CONTROL_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "CONTROL_Start failed. [err=%#x]\n", s32Ret);
        return;
    }

    stInitParam.u32ServicePort = 8080;
    strcpy(stInitParam.szAdminPassword, "123456");
    s32Ret = HTTP_SVR_Init(&stInitParam);
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = HTTP_SVR_Start();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);

    sleep_ms(1000);
    while (1)
    {
        chChoice = toupper(getchar());
        if (chChoice == 'Q')
        {
            break;
        }
    }

    s32Ret = HTTP_SVR_Stop();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    //s32Ret = CONTROL_Stop();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    s32Ret = HTTP_SVR_Fini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
    //s32Ret = CONTROL_Fini();
    CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
}


/***************************************************************
*-# 用例编号: itest_HTTP_GetPacket_001
*-# 测试标题: 测试在HTTP服务器上抓取视频流保存为h264文件
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_HTTP_GetPacket_001()
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    uint32 u32Cnt = 0;
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    SFIFO_MSHEAD *pstPacket = NULL;
    char *pszFilePath = "http_getpacket.h264";

    remove(pszFilePath);
    s32Fd = open(pszFilePath, O_CREAT | O_RDWR, S_IRWXU | S_IRGRP | S_IROTH);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open:%s failed.\n", pszFilePath);
        return;
    }
    
    s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }

    s32Ret = SFIFO_GetMediaAttr(s32MainQueId, &stMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_GetMediaAttr stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }

    print_level(SV_DEBUG, "(%dx%d) %d\n", stMediaAttr.stMainStreamAttr.u32Width, stMediaAttr.stMainStreamAttr.u32Height, stMediaAttr.stMainStreamAttr.u32FrameRate);
    while (1)
    {
        s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        if (SV_SUCCESS == s32Ret)
        {
            if (pstPacket->type != 2)
            {
                print_level(SV_DEBUG, "%d(%dx%d) pts:%lld, size:%d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->msdsize);
                u32Cnt++;
				write(s32Fd, pstPacket->data, pstPacket->msdsize);
            }
            
            SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (u32Cnt >= 250)
            {
                break;
            }
        }
        sleep_ms(10);
    }

    SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
    close(s32Fd);
}


/***************************************************************
*-# 用例编号: itest_HTTP_GetPacket_002
*-# 测试标题: 测试在HTTP服务器上抓取视频流封装为flv文件
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/

void itest_HTTP_GetPacket_002()
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    uint32 u32Cnt = 0;
	int iOutputSize = 0;
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    SFIFO_MSHEAD *pstPacket = NULL;
    char *pszFilePath = "http_getpacket.flv";
	uint8 *pu8Buf = NULL;

    remove(pszFilePath);
    s32Fd = open(pszFilePath, O_CREAT | O_RDWR, S_IRWXU | S_IRGRP | S_IROTH);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open:%s failed.\n", pszFilePath);
        return;
    }
    
    s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }

    s32Ret = SFIFO_GetMediaAttr(s32MainQueId, &stMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_GetMediaAttr stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }

	pu8Buf = (uint8 *)malloc(512*1024);
	if (NULL == pu8Buf)
    {
        print_level(SV_ERROR, "malloc: pu8Buf failed.\n");
		close(s32Fd);
        return;
    }

    // 创建FlvHeader
    print_level(SV_DEBUG, "Create FlvHeader.\n");
    flv_header_t flv_header = createFlvHeader();

    memcpy(pu8Buf, (char*)&flv_header, sizeof(flv_header));
    write(s32Fd, pu8Buf, FLV_HEAD_SIZE + FLV_TAG_PRE_SIZE);
    memset(pu8Buf, 0, 512*1024);

    // 创建MetaTag
    print_level(SV_DEBUG, "Create MetaTag.\n");
    tag_avInfo_t stAVInfo = {0};

    stAVInfo.avcBitRate = stMediaAttr.stMainStreamAttr.u32Bitrate;
    stAVInfo.avcFrameRate = stMediaAttr.stMainStreamAttr.u32FrameRate;
    stAVInfo.avcWidth = stMediaAttr.stMainStreamAttr.u32Width;
    stAVInfo.avcHeight = stMediaAttr.stMainStreamAttr.u32Height;
    int iOutDataSize = 0;

    createMetaTag(stAVInfo, pu8Buf, &iOutDataSize);
    write(s32Fd, pu8Buf, iOutDataSize);
    memset(pu8Buf, 0, 512*1024);
    
    print_level(SV_DEBUG, "Resolution: (%dx%d) Fps: %d\n", stMediaAttr.stMainStreamAttr.u32Width, stMediaAttr.stMainStreamAttr.u32Height, stMediaAttr.stMainStreamAttr.u32FrameRate);
    while (1)
    {
        s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        if (SV_SUCCESS == s32Ret)
        {
            if (pstPacket->type != 2)
            {
                
                print_level(SV_DEBUG, "FrameType: %d(%dx%d) pts: %lld, size: %d\n", pstPacket->type, pstPacket->width, pstPacket->height, pstPacket->pts, pstPacket->msdsize);
                u32Cnt++;

                uint32 u32pts = ((pstPacket -> pts)/1000)%0xFFFFFF;     // 时间戳
                
                if(u32Cnt == 1)
                {
                    char sps[256];
					char pps[256];
					int iSpsDataLen, iPpsDataLen;
					int iOutDataLen = 0;

					/* 在H264视频流数据中搜索Sps和Pps */
                    print_level(SV_DEBUG, "Search AVCC Data.\n");
					int iRes = HTTP_SearchSpsPpsData(pstPacket -> data, pstPacket -> msdsize, sps, &iSpsDataLen, pps, &iPpsDataLen);
					if(iRes != 0)
					{
					    
						continue;
					}

                    /* 将Sps和Pps封入第一个VideoTag */
                    print_level(SV_DEBUG, "Create First Video Tag.\n");
                    createSpsPpsTag(u32pts, sps, iSpsDataLen, pps, iPpsDataLen, pu8Buf, &iOutDataLen);
                    write(s32Fd, pu8Buf, iOutDataLen);
					memset(pu8Buf, 0, 512*1024);
                    
                }
                else
                {
                    /* 创建一般VideoTag的Header */
                    print_level(SV_DEBUG, "Create Tag Header.\n");
                    tag_header_t tag_header;
                    int iVideoTagHeadSize = 5 + 4 + pstPacket -> msdsize - 4; // video_tag(5) + nalu size(4) + streamSize (do not include 0x0001)                    
                    tag_header = createTagHeader(0x09, iVideoTagHeadSize, u32pts, 0, 0);

                    /* 创建一般VideoTag的Data */
                    print_level(SV_DEBUG, "Create Tag Data.\n");                    
				    createVideoTag(tag_header, (pstPacket -> data) + 4, (pstPacket -> msdsize) - 4, pstPacket -> type, 0x01, pu8Buf, &iOutputSize);
				    write(s32Fd, pu8Buf, iOutputSize);
				    memset(pu8Buf, 0, 512*1024);
				}
            }
            
            SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (u32Cnt >= 250)
            {
                break;
            }
        }
        sleep_ms(10);
    }

    SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
    close(s32Fd);
   	free(pu8Buf);
   	pu8Buf = NULL;
}


/***************************************************************
*-# 用例编号: itest_HTTP_GetPacket_003
*-# 测试标题: 测试在HTTP服务器上抓取lpcm音频流再编码为aac文件
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/
void itest_HTTP_GetPacket_003()
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    uint32 u32Cnt = 0;
    sint32 s32AudQueId;
    sint32 s32AudConsumerId;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    SFIFO_MSHEAD *pstPacket = NULL;
    char *pszFilePath = "http_getpacket.aac";

    sint8 *ps8PCMBuf = NULL;      // 输入数据缓存
    uint8 *pu8AACBuf = NULL;      // 输出数据缓存
    uint32 nChannels = 1;         // PCM数据声道数
    uint32 nPCMBitSize = 16;      // PCM数据单样本采样深度
    ulng32 nSampleRate = 16000;   // PCM数据采样率
    uint32 nInputSize = 0;        // 单次输入数据大小
    ulng32 nInputSamples = 0;     // 单次输入样本数
    uint32 nPCMBufSize = 0;       // 输入缓存大小
    ulng32 nMaxOutputBytes = 0;   // 单次输出数据大小
    
    int nRet;
    faacEncHandle hEncoder;
    faacEncConfigurationPtr pConfiguration;

    remove(pszFilePath);
    s32Fd = open(pszFilePath, O_CREAT | O_RDWR, S_IRWXU | S_IRGRP | S_IROTH);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open:%s failed.\n", pszFilePath);
        return;
    }
    
    s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32AudQueId, &s32AudConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }

    s32Ret = SFIFO_GetMediaAttr(s32AudQueId, &stMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_GetMediaAttr stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }
    
    /* 打开faac编码器 */
    hEncoder = faacEncOpen(nSampleRate, nChannels, &nInputSamples, &nMaxOutputBytes);
    if(hEncoder == NULL)
    {
        print_level(SV_ERROR, "Failed to call faacEncOpen()\n");
        return -1;
    }
    print_level(SV_DEBUG, "InputSampleNum: %d MaxOutputSize: %d\n", nInputSamples, nMaxOutputBytes);
    
    nPCMBufSize = nInputSamples * (nPCMBitSize / 8);
    
    ps8PCMBuf = (sint8 *)malloc(nPCMBufSize);
    if (NULL == ps8PCMBuf)
    {
        print_level(SV_ERROR, "malloc ps8PCMBuf failed.\n");
        close(s32Fd);
        return;
    }

    pu8AACBuf = (uint8 *)malloc(nMaxOutputBytes);
	if (NULL == pu8AACBuf)
    {
        print_level(SV_ERROR, "malloc pu8AACBuf failed.\n");
		close(s32Fd);
        return;
    }

    /*获取和更改当前编码器参数设置*/
    pConfiguration = faacEncGetCurrentConfiguration(hEncoder);
    pConfiguration->inputFormat = FAAC_INPUT_16BIT;             // 输入PCM数据采样深度：16Bit
    pConfiguration->outputFormat = 1;                           // 输出AAC数据类型，Raw或Adts
    pConfiguration->bitRate = 16000;                            // 输出AAC数据采样频率：44kHz
    pConfiguration->aacObjectType = 2;                          // AAC对象类型，设为AAC-LC
    
    /*更新编码器参数设置*/
    nRet = faacEncSetConfiguration(hEncoder, pConfiguration);

    print_level(SV_DEBUG, "%d %d %d\n", stMediaAttr.stAudioStreamAttr.enCodeType, stMediaAttr.stAudioStreamAttr.u32BitWidth, stMediaAttr.stAudioStreamAttr.u32SampleRate);

    int iOffset = 0;
    while (1)
    {
        s32Ret = SFIFO_GetPacket(s32AudQueId, s32AudConsumerId, &pstPacket);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        if (SV_SUCCESS == s32Ret)
        {        
            if (pstPacket->type == 2)
            {
                print_level(SV_DEBUG, "%d %d pts:%lld, size:%d\n", pstPacket->type, pstPacket->algorithm, pstPacket->pts, pstPacket->msdsize);
                u32Cnt++;

                if(nPCMBufSize - iOffset > pstPacket->msdsize)
                {
                    memcpy(ps8PCMBuf + iOffset, pstPacket->data, pstPacket->msdsize);
                    iOffset += pstPacket->msdsize;
                }               
                else
                {
                    memcpy(ps8PCMBuf + iOffset, pstPacket->data, nPCMBufSize - iOffset);                // 写满输入缓存

                    /* 编码 */
                    nRet = faacEncEncode(hEncoder, (int*)ps8PCMBuf, nInputSamples, pu8AACBuf, nMaxOutputBytes);
                
				    write(s32Fd, pu8AACBuf, nRet);
                    print_level(SV_DEBUG, "faacEncEncode returns %d\n", nRet);
                    
                    memset(pu8AACBuf, 0, nMaxOutputBytes);
                    memset(ps8PCMBuf, 0, nPCMBufSize);
                    
                    memcpy(ps8PCMBuf, pstPacket->data + (nPCMBufSize - iOffset), pstPacket->msdsize - (nPCMBufSize - iOffset));
                    iOffset = pstPacket->msdsize - (nPCMBufSize - iOffset);                             // 写入当前packet剩下数据
                }                                  
                
            }
            
            SFIFO_ReleasePacket(s32AudQueId, s32AudConsumerId, pstPacket);
            if (u32Cnt >= 250)
            {
                break;
            }
        }
        sleep_ms(10);
    }

    nRet = faacEncClose(hEncoder);
    SFIFO_ForReadClose(s32AudQueId, s32AudConsumerId);
    close(s32Fd);

    free(ps8PCMBuf);
    ps8PCMBuf = NULL;
    free(pu8AACBuf);
   	pu8AACBuf = NULL;
}


/***************************************************************
*-# 用例编号: itest_HTTP_GetPacket_004
*-# 测试标题: 测试在HTTP服务器上抓取音频流保存为lpcm文件
*-# 测试类型: 
*-# 预置条件: 
* 
*-# 测试步骤: 
* 
*-# 预期结果: 
* 
****************************************************************/

void itest_HTTP_GetPacket_004()
{
    sint32 s32Ret = 0;
    sint32 s32Fd = -1;
    uint32 u32Cnt = 0;
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    SFIFO_MSHEAD *pstPacket = NULL;
    char *pszFilePath = "http_getpacket.lpcm";

    remove(pszFilePath);
    s32Fd = open(pszFilePath, O_CREAT | O_RDWR, S_IRWXU | S_IRGRP | S_IROTH);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "open:%s failed.\n", pszFilePath);
        return;
    }
    
    s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }

    s32Ret = SFIFO_GetMediaAttr(s32MainQueId, &stMediaAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SFIFO_GetMediaAttr stream:%s failed.\n", SFIFO_MAIN_STREAM);
        close(s32Fd);
        return;
    }

    
    while (1)
    {
        s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);
        CU_ASSERT_EQUAL(s32Ret, SV_SUCCESS);
        if (SV_SUCCESS == s32Ret)
        {
            if (pstPacket->type == 2)
            {
                print_level(SV_DEBUG, "%d pts:%lld, size:%d\n", pstPacket->type, pstPacket->pts, pstPacket->msdsize);
                u32Cnt++;
				write(s32Fd, pstPacket->data, pstPacket->msdsize);
            }
            
            SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (u32Cnt >= 250)
            {
                break;
            }
        }
        sleep_ms(10);
    }

    SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
    close(s32Fd);
}


void Suite_http_API()
{
    CU_pSuite pSuite;

    if (NULL == CU_get_registry())
    {
        printf("CUnit not registry!\n");
        return;
    }

    pSuite = CU_ADD_SUITE(NULL, NULL);
    CU_ADD_TEST(pSuite, itest_cJSON_Create_001);
    CU_ADD_TEST(pSuite, itest_cJSON_Create_002);
    CU_ADD_TEST(pSuite, itest_cJSON_Parse_001);
    CU_ADD_TEST(pSuite, itest_cJSON_Parse_002);
    CU_ADD_TEST(pSuite, itest_HTTP_SVR_Init_001);
    CU_ADD_TEST(pSuite, itest_HTTP_SVR_Start_001);
    CU_ADD_TEST(pSuite, itest_HTTP_GetPacket_001);
	CU_ADD_TEST(pSuite, itest_HTTP_GetPacket_002);
    CU_ADD_TEST(pSuite, itest_HTTP_GetPacket_003);
    CU_ADD_TEST(pSuite, itest_HTTP_GetPacket_004);
}


void AddTests_itest_http()
{
    Suite_http_API();
}


