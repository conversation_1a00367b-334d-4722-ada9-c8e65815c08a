#include "type_define.hpp"
#include "server.hpp"
#include "message_sd.hpp"
#include "message_control.hpp"
#include "serializer.hpp"
#include "RtpConnection.hpp"
#include "H264Source.hpp"
#include "MJPEGSource.h"


#include <thread>

#include "sharefifo.h"
#include "config.h"
#include "print.h"
#include "media.h"
#include "board.h"
#include "common.h"

using namespace someip;
using namespace std;

//bool isNetworkInited = SV_FALSE;
static server* pser = nullptr;

server::server()
{
    bIsStartStream = SV_FALSE;
    waiteCommunicate = SV_FALSE;
    offerState = state_type_e::ST_InitOffer;
    findSession = 1;
    findcount = 0;
/*    
    std::random_device rd;
    
    //rtp_port = rd() & 0xfffe;
    //if(BOARD_IsCustomer(BOARD_C_IPCR20S3_201368))    
        stMsgInitParam.rtp_port = (rd() & 0xfff) + 60000;
    //else
        //rtp_port = 50004;
        
    print_level(SV_INFO, "Generate random RTP port[%d]\n", stMsgInitParam.rtp_port);
*/
    video_width = 1280; // 720p
    video_height = 720;
    encode_type = 2;    // H.264

    isNetworkInited = SV_FALSE;
    isofferService = SV_FALSE;
    ishandleMessage = SV_FALSE;
}

typedef union idr_union
{
	char buf[4];
	int value;
} IDR_UNION;

static sint32 isMjpegFrame(void *pbuf, int lens)
{
    int *pdata = pbuf;
    IDR_UNION szHead = {0xff, 0xd8, 0xff, 0xe0};    // JFIF
    if(*pdata != szHead.value)
        return SV_FALSE;
    return SV_TRUE;
}

static sint32 print_hex(char *pbuf, int len)
{
    int i = 0;
    printf("data:");
    for(i = 0; i < len; i++)
    {
        printf("%02x", pbuf[i]);
    }
    printf("\n");
    return 1;
}

void transRes2Number(MEDIA_RES enRes, uint16 *pu16Width, uint16 *pu16Height)
{
    switch (enRes)
    {
        case RES_CIF_N:
            *pu16Width = 352;
            *pu16Height = 240;
            break;
        case RES_CIF:
            *pu16Width = 352;
            *pu16Height = 288;
            break;
        case RES_HVGA:
            *pu16Width = 480;
            *pu16Height = 320;
            break;
        case RES_VGA:
            *pu16Width = 640;
            *pu16Height = 480;
            break;
        case RES_D1:
            *pu16Width = 704;
            *pu16Height = 480;
            break;
        case RES_SVGA:
            *pu16Width = 800;
            *pu16Height = 600;
            break;
        case RES_720P:
            *pu16Width = 1280;
            *pu16Height = 720;
            break;
        case RES_960P:
            *pu16Width = 1280;
            *pu16Height = 960;
            break;
        case RES_1080P:
            *pu16Width = 1920;
            *pu16Height = 1080;
            break;
        case RES_2K:
            *pu16Width = 2560;
            *pu16Height = 1440;
             break;
        case RES_5MP:
            *pu16Width = 2688;
            *pu16Height = 1944;
             break;
        default:
            *pu16Width = 1280;
            *pu16Height = 720;
    }
}

static sint32 callbackNetworkStateChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    sint32 i = 0;
    NETWORK_STAT_S *pstNetworkStat = (NETWORK_STAT_S *)pstMsgPkt->pu8Data;
    //print_level(SV_INFO, "recive: OP_EVENT_NETWORK_STAT, type: %d, exist: %d\n", pstNetworkStat->enNetworkType, pstNetworkStat->bExist);

    if (NETWORK_TYPE_LAN != pstNetworkStat->enNetworkType && NETWORK_TYPE_WIFI != pstNetworkStat->enNetworkType  && SV_FALSE != pstNetworkStat->bExist)
    {
        return SV_SUCCESS;
    }
    else if (NETWORK_TYPE_LAN == pstNetworkStat->enNetworkType && SV_FALSE != pstNetworkStat->bExist)
    {
        pser->isNetworkInited = SV_TRUE;
    }
	else if(NETWORK_TYPE_WIFI == pstNetworkStat->enNetworkType && SV_FALSE != pstNetworkStat->bExist)
	{
		pser->isNetworkInited = SV_TRUE;
	}
    else
    {
        pser->isNetworkInited = SV_FALSE;
    }    

    return SV_SUCCESS;
}

static sint32 callbackNetworkParamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_INFO, "callbackNetworkParamChange.\n");
    MSG_NETWORK_CFG *pstNetworkCfg = (MSG_NETWORK_CFG *)pstMsgPkt->pu8Data;
    char szOldIp[32] = {0};

    sprintf(szOldIp, "%d.%d.%d.%d", \
            pser->stMsgInitParam.ipaddr[0], pser->stMsgInitParam.ipaddr[1], \
            pser->stMsgInitParam.ipaddr[2], pser->stMsgInitParam.ipaddr[3]);
        
    sscanf(pstNetworkCfg->szSubmask, "%hhu.%hhu.%hhu.%hhu", &pser->stMsgInitParam.netmask[0], &pser->stMsgInitParam.netmask[1],&pser->stMsgInitParam.netmask[2],&pser->stMsgInitParam.netmask[3]);        

    printf("real ip: %s\n",pstNetworkCfg->szRealIpAddr);
    //printf("pstNetworkCfg->u32UdpComPort=%d, pser->stMsgInitParam.udp_port=%d.\n", pstNetworkCfg->u32UdpComPort, pser->stMsgInitParam.udp_port);
    if (pstNetworkCfg->u32UdpComPort != pser->stMsgInitParam.udp_port 
        || BOARD_IsCustomer(BOARD_C_ADA32E1_200946)
#if !defined(BOARD_WFCR20S2)
        || pstNetworkCfg->bDHCPEnable != pser->stMsgInitParam.enable_dhcp || \
        (pstNetworkCfg->bDHCPEnable && strcmp(pstNetworkCfg->szRealIpAddr, szOldIp) != 0) || \
        (!pstNetworkCfg->bDHCPEnable && strcmp(pstNetworkCfg->szIpAddr, szOldIp) != 0)
#endif
        )
    {
        print_level(SV_INFO, "Restart SOME/IP server.\n");
        pser->stop();                
        pser->stMsgInitParam.udp_port = pstNetworkCfg->u32UdpComPort;
        pser->stMsgInitParam.enable_dhcp = pstNetworkCfg->bDHCPEnable;
        sscanf(pstNetworkCfg->szIpAddr, "%hhu.%hhu.%hhu.%hhu", &pser->stMsgInitParam.ipaddr[0], &pser->stMsgInitParam.ipaddr[1],&pser->stMsgInitParam.ipaddr[2],&pser->stMsgInitParam.ipaddr[3]);
        pser->startThreads();
    }
    
    return SV_SUCCESS;
}

static sint32 callbackMediaParamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    SV_BOOL isChange = SV_FALSE;
    MSG_VIDEO_CFG *pstVideoCfg = (MSG_VIDEO_CFG *)pstMsgPkt->pu8Data;
    print_level(SV_INFO, "Media param change , someip only work for media format\n");
    print_level(SV_INFO, "Media format: %d (H264=0, H265=1, MJPEG=2)", pstVideoCfg->astChnParam[0].enMainEncode);
    
    if ((pstVideoCfg->astChnParam[0].enMainEncode == 0 && pser->encode_type == 2) ||
        (pstVideoCfg->astChnParam[0].enMainEncode == 2 && pser->encode_type == 1)) {
        return SV_SUCCESS;
    }

    if (pstVideoCfg->astChnParam[0].enMainEncode == 0) {
        pser->encode_type = 2;
    } else if (pstVideoCfg->astChnParam[0].enMainEncode == 2) {
        pser->encode_type = 1;
    }

    if (pser->bIsStartStream) {
        pser->bIsStartStream = SV_FALSE;
    }
    return SV_SUCCESS;
}

void server::startStream()
{
    sint32 s32Ret = 0, i;
    sint32 s32MainQueId;
    sint32 s32MainConsumerId;
    uint32 u32ExtraSize = 0;
    SV_BOOL bMainFirstFrm = SV_FALSE;
    SFIFO_MSHEAD *pstPacket = NULL;
	struct timeval presentationTime = {0};
    someip::AVFrame stMainFrame = {0};
    someip::AVFrame stTmpFrame = {0};
    SFIFO_MEDIA_ATTR stMediaAttr = {0};
    MediaExtraData mediaData = {0};
    MediaSource *video;
    struct sockaddr_in to_addr;    //流目的地址

#if (defined(BOARD_IPCR20S3)) 
    if(BOARD_IsCustomer(BOARD_C_IPCR20S3_JLG))    // IPC for JLG, 只使用MJPEG, 打开图片流
    {
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(2, &stMediaAttr);
            if (SV_SUCCESS == s32Ret)
            {
                print_level(SV_INFO, "get media attr!\n");
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "Picstream wait for extra data timeout!\n");
        }
        
        MSG_PACKET_S stMsgPkt = {0};
        stMsgPkt.stMsg.s32Param = 3; // quality 3, q factor 70
        s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }  

         s32Ret = SFIFO_ForReadOpen(SFIFO_PIC_STREAM, &s32MainQueId, &s32MainConsumerId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
            return;
        }
        else
        {
            bIsStartStream = SV_TRUE;
            bMainFirstFrm = SV_TRUE;
        }

        video = MJPEGSource::CreateNew(15, stMediaAttr.stPicStreamAttr.u32Width>>3, \
                stMediaAttr.stPicStreamAttr.u32Height>>3, 70, 0);
    }
    else    // 335IPC, 拉H.264时打开主码流, MJPEG则打开图片流
    {
        if(2 == encode_type)
        {
            for (i = 0; i < 10; i++)
            {
                s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
                if (SV_SUCCESS == s32Ret && stMediaAttr.stMainStreamAttr.bExtraValid)
                {
                    print_level(SV_INFO, "get extra data!\n");
                    strncpy((char*)&mediaData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(MediaExtraData));
                    break;
                }
                sleep_ms(500);
            }
            if (i >= 10)
            {
                print_level(SV_ERROR, "wait for extra data timeout!\n");
                return;
            }
            
            s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
                return;
            }
            else
            {
                bIsStartStream = SV_TRUE;
                bMainFirstFrm = SV_TRUE;
            }

            video = someip::H264Source::CreateNew(stMediaAttr.stMainStreamAttr.u32FrameRate, &mediaData);
        }
        else 
        {
            for (i = 0; i < 10; i++)
            {
                s32Ret = SFIFO_GetMediaAttr(2, &stMediaAttr);
                if (SV_SUCCESS == s32Ret)
                {
                    print_level(SV_INFO, "get media attr!\n");
                    break;
                }
                sleep_ms(500);
            }
            if (i >= 10)
            {
                print_level(SV_ERROR, "Picstream wait for extra data timeout!\n");
            }
            
            MSG_PACKET_S stMsgPkt = {0};
            stMsgPkt.stMsg.s32Param = 3; // quality 3, q factor 70
            s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }  

             s32Ret = SFIFO_ForReadOpen(SFIFO_PIC_STREAM, &s32MainQueId, &s32MainConsumerId);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
                return;
            }
            else
            {
                bIsStartStream = SV_TRUE;
                bMainFirstFrm = SV_TRUE;
            }

            video = MJPEGSource::CreateNew(15, stMediaAttr.stPicStreamAttr.u32Width>>3, \
                    stMediaAttr.stPicStreamAttr.u32Height>>3, 70, 0);
        }
    }
#elif (defined(BOARD_IPTR20S1) || defined(BOARD_WFCR20S2))    // 主码流能出MJPEG，只需打开主码流
    //if (BOARD_IsCustomer(BOARD_C_IPTR20S1_201368) || BOARD_IsCustomer(BOARD_C_IPTR20S1_200252) || BOARD_IsCustomer(BOARD_C_WFCR20S2_201368))
    {
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
            if (SV_SUCCESS == s32Ret && stMediaAttr.stMainStreamAttr.bExtraValid)
            {
                print_level(SV_INFO, "get extra data!\n");
                strncpy((char*)&mediaData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(MediaExtraData));
                break;
            }
            else if (SV_SUCCESS == s32Ret && stMediaAttr.stMainStreamAttr.bNoExtraData)
            {
                print_level(SV_INFO, "don't need extra data!\n");
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "wait for extra data timeout!\n");
            return;
        }
        
        s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
            return;
        }
        else
        {
            bIsStartStream = SV_TRUE;
            bMainFirstFrm = SV_TRUE;
        }

        if(2 == encode_type)
        {
            video = someip::H264Source::CreateNew(stMediaAttr.stMainStreamAttr.u32FrameRate, &mediaData);
        }
        else
        {
            video = MJPEGSource::CreateNew(15, stMediaAttr.stMainStreamAttr.u32Width>>3, \
                    stMediaAttr.stMainStreamAttr.u32Height>>3, 70, 0);
        }
    }    
#elif (defined(BOARD_ADA32V2) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1))    // for ADA32
    if(BOARD_IsCustomer(BOARD_C_ADA32V2_200946))  // JLG, 固定MJPEG VGA分辨率
    {
        s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
            return;
        }
        else
        {
            bIsStartStream = SV_TRUE;
            bMainFirstFrm = SV_TRUE;
        }
        
        video = MJPEGSource::CreateNew(25, 640>>3, 480>>3, 70, 1);
    }
    else
    {
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(0, &stMediaAttr);
            if (SV_SUCCESS == s32Ret && stMediaAttr.stMainStreamAttr.bExtraValid)
            {
                print_level(SV_INFO, "get extra data!\n");
                strncpy((char*)&mediaData, stMediaAttr.stMainStreamAttr.au8VpsData, sizeof(MediaExtraData));
                break;
            }
            else if (SV_SUCCESS == s32Ret && stMediaAttr.stMainStreamAttr.bNoExtraData)
            {
                print_level(SV_INFO, "don't need extra data!\n");
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "wait for extra data timeout!\n");
            return;
        }
        
        s32Ret = SFIFO_ForReadOpen(SFIFO_MAIN_STREAM, &s32MainQueId, &s32MainConsumerId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_MAIN_STREAM);
            return;
        }
        else
        {
            bIsStartStream = SV_TRUE;
            bMainFirstFrm = SV_TRUE;
        }

        if(2 == encode_type)
        {
            video = someip::H264Source::CreateNew(stMediaAttr.stMainStreamAttr.u32FrameRate, &mediaData);
        }
        else
        {
            video = MJPEGSource::CreateNew(15, stMediaAttr.stMainStreamAttr.u32Width>>3, \
                    stMediaAttr.stMainStreamAttr.u32Height>>3, 70, 1);
        }
    } 
#elif (defined(BOARD_ADA32IR))
        
        for (i = 0; i < 10; i++)
        {
            s32Ret = SFIFO_GetMediaAttr(3, &stMediaAttr);
            if (SV_SUCCESS == s32Ret && stMediaAttr.stVmixStreamAttr.bExtraValid)
            {
                print_level(SV_INFO, "get extra data!\n");
                strncpy((char*)&mediaData, stMediaAttr.stVmixStreamAttr.au8VpsData, sizeof(MediaExtraData));
                break;
            }
            else if (SV_SUCCESS == s32Ret && stMediaAttr.stVmixStreamAttr.bNoExtraData)
            {
                print_level(SV_INFO, "don't need extra data!\n");
                break;
            }
            sleep_ms(500);
        }
        if (i >= 10)
        {
            print_level(SV_ERROR, "wait for extra data timeout!\n");
            return;
        }
        
        s32Ret = SFIFO_ForReadOpen(SFIFO_IR_VMIX_STREAM, &s32MainQueId, &s32MainConsumerId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n", SFIFO_IR_VMIX_STREAM);
            return;
        }
        else
        {
            bIsStartStream = SV_TRUE;
            bMainFirstFrm = SV_TRUE;
        }
    
        if(2 == encode_type)
        {
            video = someip::H264Source::CreateNew(stMediaAttr.stVmixStreamAttr.u32FrameRate, &mediaData);
        }
        else
        {
            video = MJPEGSource::CreateNew(15, stMediaAttr.stVmixStreamAttr.u32Width>>3, \
                    stMediaAttr.stVmixStreamAttr.u32Height>>3, 70, 1);
        }
#else
    print_level(SV_ERROR, "This product does not support someip protocol\n");

#endif
/*
#if defined(BOARD_ADA32E1)
    RtpConnection rtpConn(from_addr);
    print_level(SV_DEBUG, "someip desIP:%s\n", inet_ntoa(from_addr.sin_addr));
#else
    RtpConnection rtpConn(rtp_addr);
    print_level(SV_DEBUG, "someip desIP:%s\n", inet_ntoa(rtp_addr.sin_addr));
#endif
*/

    if (BOARD_IsNotCustomer(BOARD_C_IPCR20S3_201368))
    {       
		memcpy(&to_addr, &from_addr, sizeof(from_addr));        
    }
    else
    {
        memcpy(&to_addr, &rtp_addr, sizeof(rtp_addr));        
    }

    RtpConnection rtpConn(to_addr);
    print_level(SV_DEBUG, "someip desIP:%s\n", inet_ntoa(to_addr.sin_addr));
    
    rtpConn.SetClockRate(someip::channel_0, video->GetClockRate());
    rtpConn.SetPayloadType(someip::channel_0, video->GetPayloadType());
    
    bool ret = rtpConn.SetupRtpOverUdp(someip::channel_0, stMsgInitParam.rtp_port, stMsgInitParam.rtp_port+1);
    if(ret != true)
    {
        print_level(SV_ERROR, "SetupRtpOverUdp failed. [err:%s]\n", strerror(errno));
        return;
    }
    rtpConn.Play();
    video->SetSendFrameCallback(std::bind(&RtpConnection::SendRtpPacket, &rtpConn, std::placeholders::_1, std::placeholders::_2));

#if (defined(PLATFORM_SSC335))
    uint8_t* pu8BufVideo = malloc(501*1024);
    if (NULL == pu8BufVideo)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }
#else
    uint8_t* pu8BufVideo = malloc(800*1024);
    if (NULL == pu8BufVideo)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }
#endif    

    uint8_t* pu8BufHeader = malloc(1024);
    if (NULL == pu8BufHeader)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }

    uint8_t* pu8Tmp = malloc(1024);
    if (NULL == pu8Tmp)
    {
        print_level(SV_ERROR, "malloc failed. [err:%s]\n", strerror(errno));
        return NULL;
    }

    stMainFrame.buffer.reset(pu8BufVideo);   
    stMainFrame.header.reset(pu8BufHeader);
    stTmpFrame.buffer.reset(pu8Tmp);

    while(bIsStartStream)
    {
        if(waiteCommunicate)
        {
            sleep_ms(10);
            continue;
        }

        s32Ret = SFIFO_GetPacket(s32MainQueId, s32MainConsumerId, &pstPacket);

//        FILE *file = fopen("/opt/ir.h264", "a+");
//        fwrite(pstPacket->data, 1, pstPacket->msdsize, file);
//        fclose(file);

        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(1);
            continue;
        }
        
        if (pstPacket->type == 2)
        {
            SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            continue;
        }

#if 0//(defined(PLATFORM_RV1126))
        if (!isMjpegFrame(pstPacket->data, 64))
        {
            print_level(SV_INFO, "isMjpegFrame false!\n");
            SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            sleep_ms(1000);
            continue;
        }
#endif

        presentationTime.tv_sec = pstPacket->pts / 1000000ll;
        presentationTime.tv_usec = pstPacket->pts % 1000000ll;
        if (bMainFirstFrm)
        {
            if (pstPacket->type != 1)
            {
                print_level(SV_WARN, "Get first main frame invalid. [type=%d]\n", pstPacket->type);
                SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
                continue;
            }
            bMainFirstFrm = SV_FALSE;
        }

#if (defined(PLATFORM_SSC335))
        if (BOARD_IsCustomer(BOARD_C_IPCR20S3_JLG)) // JLG IPC   
        {
        #if 1
            int pos = findMjpgData((unsigned char *)pstPacket->data, pstPacket->msdsize);

    		stMainFrame.type = someip::VIDEO_FRAME_I;
    		stMainFrame.size = pstPacket->msdsize - pos;
    		stMainFrame.presentationTime = presentationTime;

            memcpy(stMainFrame.buffer.get(), pstPacket->data + pos, stMainFrame.size);
        #else
    		stMainFrame.type = someip::VIDEO_FRAME_I;
    		stMainFrame.size = pstPacket->msdsize;
    		stMainFrame.presentationTime = presentationTime;

            memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
        #endif
        
            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
    			print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %s]\n", strerror(errno)); 
            }
        }
        else
        //if(BOARD_IsCustomer(BOARD_C_IPCR20S3_201368) || BOARD_IsCustomer(BOARD_C_IPTR20S1_201368) || BOARD_IsCustomer(BOARD_C_IPTR20S1_200252) || BOARD_IsCustomer(BOARD_C_WFCR20S2_201368))
        {
            stMainFrame.type = pstPacket->type == 2 ? someip::AUDIO_FRAME : (pstPacket->type == 1 ? someip::VIDEO_FRAME_I : someip::VIDEO_FRAME_P);
        	stMainFrame.size = pstPacket->msdsize;
        	stMainFrame.presentationTime = presentationTime;
        	switch (stMainFrame.type)
            {
                case someip::VIDEO_FRAME_I:
                    if (2 == encode_type)    // H.264
                    {
                        u32ExtraSize = stMediaAttr.stMainStreamAttr.u32SpsLen + stMediaAttr.stMainStreamAttr.u32PpsLen + stMediaAttr.stMainStreamAttr.u32SeiLen;
                        stMainFrame.size -= (u32ExtraSize + 16);
                        memcpy(stMainFrame.buffer.get(), pstPacket->data+(u32ExtraSize+16), stMainFrame.size);
                    }
                    else    // MJPEG
                    {
#if defined(BOARD_WFCR20S2)   
                        memcpy(stMainFrame.buffer.get() , pstPacket->data, stMainFrame.size);
#else
                        int pos = findMjpgData((unsigned char *)pstPacket->data, pstPacket->msdsize);
                        memcpy(stMainFrame.buffer.get(), pstPacket->data + pos, stMainFrame.size - pos);
#endif

                    }
                    break;
                case someip::VIDEO_FRAME_P:
                    stMainFrame.size -= 4;
                    memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
                    break;
                case someip::AUDIO_FRAME:
                    memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
                    break;
            }
        	
            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
        		print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %#x]\n", s32Ret); 
            }

            /* 拆包单独发送VPS,SPS,PPS,SEI */
            if (2 == encode_type)  // H.264
            {
                if (stMainFrame.type == someip::VIDEO_FRAME_I)
                {
                    stTmpFrame.type = someip::EXTRA_DATA;
            		stTmpFrame.presentationTime = stMainFrame.presentationTime;
                    stTmpFrame.size = stMediaAttr.stMainStreamAttr.u32SpsLen;
                    memcpy(stTmpFrame.buffer.get(), stMediaAttr.stMainStreamAttr.au8SpsData, stTmpFrame.size);
                    video->HandleFrame(someip::channel_0, stTmpFrame);
                    stTmpFrame.size = stMediaAttr.stMainStreamAttr.u32PpsLen;
                    memcpy(stTmpFrame.buffer.get(), stMediaAttr.stMainStreamAttr.au8PpsData, stTmpFrame.size);
                    video->HandleFrame(someip::channel_0, stTmpFrame);
                }
            }
        }        
#elif (defined(BOARD_ADA32V2) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1))
        if(BOARD_IsCustomer(BOARD_C_ADA32V2_200946))  // JLG
        {
            int pos = findMjpgData((unsigned char *)pstPacket->data, pstPacket->msdsize);

    		stMainFrame.type = someip::VIDEO_FRAME_I;
    		stMainFrame.size = pstPacket->msdsize - pos;
    		stMainFrame.presentationTime = presentationTime;

            memcpy(stMainFrame.buffer.get(), pstPacket->data + pos, stMainFrame.size);
    		memcpy(stMainFrame.header.get(), pstPacket->data, pos);
        
            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
    			print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %s]\n", strerror(errno)); 
            }
        }
        else
        {
            stMainFrame.type = pstPacket->type == 2 ? someip::AUDIO_FRAME : (pstPacket->type == 1 ? someip::VIDEO_FRAME_I : someip::VIDEO_FRAME_P);
        	stMainFrame.size = pstPacket->msdsize;
            //print_level(SV_DEBUG, "stMainFrame.size = %d\n", stMainFrame.size);
        	stMainFrame.presentationTime = presentationTime;
        	switch (stMainFrame.type)
            {
                case someip::VIDEO_FRAME_I:
                    if (2 == encode_type)    // H.264
                    {
                        u32ExtraSize = stMediaAttr.stMainStreamAttr.u32SpsLen + stMediaAttr.stMainStreamAttr.u32PpsLen + stMediaAttr.stMainStreamAttr.u32SeiLen;
                        stMainFrame.size -= (u32ExtraSize + 12);
                        memcpy(stMainFrame.buffer.get(), pstPacket->data+(u32ExtraSize + 12), stMainFrame.size);
                    }
                    else    // MJPEG
                    {
                        memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
                    }
                    break;
                case someip::VIDEO_FRAME_P:
                    stMainFrame.size -= 4;
                    memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
                    break;
                case someip::AUDIO_FRAME:
                    memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
                    break;
            }
        	
            s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
        		print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %#x]\n", s32Ret); 
            }

            /* 拆包单独发送VPS,SPS,PPS,SEI */
            if (2 == encode_type)  // H.264
            {
                if (stMainFrame.type == someip::VIDEO_FRAME_I)
                {
                    stTmpFrame.type = someip::EXTRA_DATA;
            		stTmpFrame.presentationTime = stMainFrame.presentationTime;
                    stTmpFrame.size = stMediaAttr.stMainStreamAttr.u32SpsLen;
                    memcpy(stTmpFrame.buffer.get(), stMediaAttr.stMainStreamAttr.au8SpsData, stTmpFrame.size);
                    video->HandleFrame(someip::channel_0, stTmpFrame);
                    stTmpFrame.size = stMediaAttr.stMainStreamAttr.u32PpsLen;
                    memcpy(stTmpFrame.buffer.get(), stMediaAttr.stMainStreamAttr.au8PpsData, stTmpFrame.size);
                    video->HandleFrame(someip::channel_0, stTmpFrame);
                    stTmpFrame.size = stMediaAttr.stMainStreamAttr.u32SeiLen;
                    memcpy(stTmpFrame.buffer.get(), stMediaAttr.stMainStreamAttr.au8SeiData, stTmpFrame.size);
                    video->HandleFrame(someip::channel_0, stTmpFrame);
                }
            }
        }

#elif (defined(BOARD_ADA32IR))
        stMainFrame.type = pstPacket->type == 2 ? someip::AUDIO_FRAME : (pstPacket->type == 1 ? someip::VIDEO_FRAME_I : someip::VIDEO_FRAME_P);
    	stMainFrame.size = pstPacket->msdsize;
        //print_level(SV_DEBUG, "stMainFrame.size = %d\n", stMainFrame.size);
    	stMainFrame.presentationTime = presentationTime;
    	switch (stMainFrame.type)
        {
            case someip::VIDEO_FRAME_I:
                if (2 == encode_type)    // H.264
                {
                    u32ExtraSize = stMediaAttr.stVmixStreamAttr.u32SpsLen + stMediaAttr.stVmixStreamAttr.u32PpsLen + stMediaAttr.stVmixStreamAttr.u32SeiLen;
                    stMainFrame.size -= (u32ExtraSize + 12);
                    memcpy(stMainFrame.buffer.get(), pstPacket->data+(u32ExtraSize + 12), stMainFrame.size);
                }
                else    // MJPEG
                {
                    memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
                }
                break;
            case someip::VIDEO_FRAME_P:
                stMainFrame.size -= 4;
                memcpy(stMainFrame.buffer.get(), pstPacket->data+4, stMainFrame.size);
                break;
            case someip::AUDIO_FRAME:
                memcpy(stMainFrame.buffer.get(), pstPacket->data, stMainFrame.size);
                break;
        }
    	
        s32Ret = SFIFO_ReleasePacket(s32MainQueId, s32MainConsumerId, pstPacket);
        if (SV_SUCCESS != s32Ret)
        {
    		print_level(SV_ERROR, "SFIFO_ReleasePacket failed! [err: %#x]\n", s32Ret); 
        }

        /* 拆包单独发送VPS,SPS,PPS,SEI */
        if (2 == encode_type)  // H.264
        {
            if (stMainFrame.type == someip::VIDEO_FRAME_I)
            {
                stTmpFrame.type = someip::EXTRA_DATA;
        		stTmpFrame.presentationTime = stMainFrame.presentationTime;
                stTmpFrame.size = stMediaAttr.stVmixStreamAttr.u32SpsLen;
                memcpy(stTmpFrame.buffer.get(), stMediaAttr.stVmixStreamAttr.au8SpsData, stTmpFrame.size);
                video->HandleFrame(someip::channel_0, stTmpFrame);
                stTmpFrame.size = stMediaAttr.stVmixStreamAttr.u32PpsLen;
                memcpy(stTmpFrame.buffer.get(), stMediaAttr.stVmixStreamAttr.au8PpsData, stTmpFrame.size);
                video->HandleFrame(someip::channel_0, stTmpFrame);
                stTmpFrame.size = stMediaAttr.stVmixStreamAttr.u32SeiLen;
                memcpy(stTmpFrame.buffer.get(), stMediaAttr.stVmixStreamAttr.au8SeiData, stTmpFrame.size);
                video->HandleFrame(someip::channel_0, stTmpFrame);
            }
        }
    
#endif
        if (stMainFrame.type != someip::AUDIO_FRAME)
            video->HandleFrame(someip::channel_0, stMainFrame);

    }

    print_level(SV_INFO, "stop stream...\n");
    
    if(1 == encode_type)
    {
        MSG_PACKET_S stMsgPkt = {0};
        stMsgPkt.stMsg.s32Param = -1;
        s32Ret = Msg_execRequestBlock(EP_RTSPSERVER, EP_CONTROL, OP_REQ_QUICKJPEG, &stMsgPkt, NULL, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_QUICKJPEG failed. [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
    }
    
    delete video;
    
    s32Ret = SFIFO_ForReadClose(s32MainQueId, s32MainConsumerId);
    if (SV_SUCCESS != s32Ret)
    {
		print_level(SV_ERROR, "SFIFO_ForReadClose failed! [err: %#x]\n", s32Ret); 
    }

    return;
}

uint32_t server::findMjpgData(unsigned char *data, uint32_t length)
{
    uint32_t i = 0;
    uint32_t tmpLen = 0;
    static uint32_t pos = 0;
    SV_BOOL bFindHeader = SV_FALSE;

    if(pos != 0)
        return pos;
    
    while(i < length - 1)
    {
        if(data[i] == 0xff && data[i+1] == 0xda)
        {
            bFindHeader = SV_TRUE;
            break;
        }
        i++;    
    }

    if(!bFindHeader)    // 返回默认值
        return 0x01;

    tmpLen = data[i+2] << 4 | data[i+3];

    pos = i+2+tmpLen;
    
    return pos;
}


message_sd * server::makeSD()
{
    message_sd * sd = new message_sd;
    
    std::shared_ptr<sd_entry> p1(new sd_entry);
    std::shared_ptr<sd_ipv4_option> p2(new sd_ipv4_option);
    
    sd->entries_.push_back(p1);
    sd->options_.push_back(p2);
    
    sd->header_.length_ = sd->get_length();
    sd->header_.client_ = 0xeeee;
    sd->header_.session_ = 1;
    p1->set_type(entry_type_e::OFFER_SERVICE);
    p1->set_major_version(1);
    p1->set_service(0x433f);
    p1->set_instance(stMsgInitParam.ipaddr[3]);
    p1->set_ttl(0xffffff);

    p2->set_ipaddr(stMsgInitParam.ipaddr);
    p2->set_port(stMsgInitParam.udp_port);

    return sd;
}

void server::offerService()
{
    int i = 0, j = 0;
    state_type_e offerStateLast = state_type_e::ST_UNKNOWN;
    SV_BOOL bRespond = SV_FALSE;
    
    message_sd *sdTOBracast = makeSD();
    message_sd *sdTOFind = makeSD();
    
    serializer ser(1);
    sdTOBracast->serialize(&ser);
    
    while(isofferService)
    { 
        switch(offerState)
        {
            case state_type_e::ST_InitOffer:
                if(++i >= 10 && !BOARD_IsCustomer(BOARD_C_ADA32E1_200946)) {
                    i = 0;
                    offerState = state_type_e::ST_TimeoutOffer;
                } else {
                    sleep_ms(100);
                }
                break;
                
            case state_type_e::ST_TimeoutOffer:
                if(i++ >= 10) {
                    i = 0;
                    j = 0;
                    offerState = state_type_e::ST_InitOffer;

                    while(offerState == state_type_e::ST_InitOffer && ++j <= 300)
                        sleep_ms(100);
                } else {
                    sleep(1);
                }
                break;

            case state_type_e::ST_GetFindService:
                switch(findcount++)
                {
                    case 0:
                    {
                        broadcast_addr.sin_port = htons(stMsgInitParam.udp_port);
                        broadcast_addr.sin_addr.s_addr = from_addr.sin_addr.s_addr;

                        sdTOFind->header_.session_ = 2;
                        ser.reset();
                        sdTOFind->serialize(&ser);
                        sleep_ms(50);
                    }
                    break;

                    case 5:
                    {
                        i = 0;
                        offerState = state_type_e::ST_InitOffer;
                        if (BOARD_IsCustomer(BOARD_C_IPCR20S3_JLG) || BOARD_IsCustomer(BOARD_C_IPTR20S1_201590))
                        {
                            broadcast_addr.sin_port = htons(stMsgInitParam.udp_port + 1);
                        }
                        else
                        {
                            //broadcast_addr.sin_port = htons(stMsgInitParam.udp_port);
                        }
                        broadcast_addr.sin_addr.s_addr = htonl(INADDR_BROADCAST);

                        ser.reset();
                        sdTOBracast->serialize(&ser);
                    }
                    break;

                    default:
                    {
                        sleep(2);
                        continue;
                    }
                }                
                break;
                
            default:
                break;
        }

        int ret = sendto(fd_, (const char*)ser.get_data(), VSOMEIP_SOMEIP_HEADER_SIZE+sdTOBracast->get_length(), 0, (struct sockaddr*) &broadcast_addr, sizeof(broadcast_addr));
        if(ret <= 0) {
            print_level(SV_ERROR, "sendto error!\n");
        }
    }

    delete sdTOBracast;
    delete sdTOFind;
}

void server::handleMessage()
{
    uint8_t recv[1024];
    int count;
    struct timeval timeout;
    int errCnt = 0;
    int reSendCnt = 0;
    socklen_t from_len = sizeof(from_addr);
    fd_set readfd;
    SV_BOOL startCommunicate = SV_FALSE;
        
    message_control mc(&stMsgInitParam);
    serializer ser2(1);
    
    while(ishandleMessage)
    {
        errCnt = 0;
        timeout.tv_sec = 0;
        timeout.tv_usec = 600000;
        
        FD_ZERO(&readfd);
        FD_SET(fd_, &readfd);

        int ret = select(fd_ + 1, &readfd, NULL, NULL, &timeout);
        switch (ret)
        {
            case -1:
                print_level(SV_ERROR, "select failed.\n");
                perror("select\n");
                break;
                
            case 0:
                if(startCommunicate && ++reSendCnt < 3)
                {
                    print_level(SV_WARN, "communication timeout, retry[%#x]\n", mc.header_.method_);
                    int ret = sendto(fd_, (const char*)ser2.get_data(), VSOMEIP_SOMEIP_HEADER_SIZE+mc.header_.length_, 0, (struct sockaddr*) &from_addr, sizeof(from_addr));
                    if(ret <= 0)
                    {
                        print_level(SV_ERROR, "sendto retry error!\n");
                        perror("sendto");
                    }
                }    
                else
                {
                    startCommunicate = SV_FALSE;
                    reSendCnt = 0;
                }
                continue;
                
            default:
                if (FD_ISSET(fd_, &readfd))
                {
                    memset(recv, 0x00, sizeof(recv));
                    count = recvfrom(fd_, recv, 1024, 0, (struct sockaddr*) &from_addr, &from_len); 
                    if(count <= 0)
                    {
                        print_level(SV_ERROR, "recvfrom error!\n");
                        perror("recvfrom");
                        continue;
                    }
                    //print_level(SV_INFO, "found client IP is %s, Port is %d\n", inet_ntoa(from_addr.sin_addr), htons(from_addr.sin_port));
                }
                break;
        }

        if(recv[0] == 0xff && recv[1] == 0xff && (from_addr.sin_addr.s_addr >>24 & 0xFF) != stMsgInitParam.ipaddr[3])//sd
        {
        #if 0
            print_level(SV_INFO, "recvmsg is sd type:\n");
            for(int i = 0; i < count; i++)
                printf("%#x ", recv[i]);
            printf("\n");
        #endif    
            offerState = state_type_e::ST_GetFindService;  
            findcount = 0;
        }    
        else if(recv[0] == 0x43 && recv[1] == 0x3f)//control
        {
        #if 0
            print_level(SV_INFO, "recvmsg is control type:\n");
            for(int i = 0; i < count; i++)
                printf("%#x ", recv[i]);
            printf("\n");
        #endif    
            deserializer dser(recv, count, 1);
            mc.deserialize(&dser);
            message_control* ab = mc.respond();
            
            ser2.reset();
            ab->serialize(&ser2);
            
            //printf("response:\n");
            //ser2.show();

            do
            {
                int ret = sendto(fd_, (const char*)ser2.get_data(), VSOMEIP_SOMEIP_HEADER_SIZE+ab->header_.length_, 0, (struct sockaddr*) &from_addr, sizeof(from_addr));
                if(ret <= 0)
                {
                    ++errCnt;
                    print_level(SV_ERROR, "sendto handleMessage error!\n");
                    perror("sendto");
                }

                if(errCnt >= 10)
        		{
        			print_level(SV_ERROR, "send timeout\n");
    				break;
        		}
            }while(ret <= 0);

            if(errCnt >= 3)
                continue;

            print_level(SV_INFO, "17215 request method: %#x\n", mc.header_.method_);
            switch(mc.header_.method_)
            {
                case SUBSCRIBE_VIDEO:
                    if(bIsStartStream == SV_FALSE)
                    {
                        startCommunicate = SV_FALSE;
                        sleep_ms(500);
                        std::thread streamThread(&startStream, this);
                        streamThread.detach();
                        print_level(SV_INFO, "startStream!\n");
                        if (BOARD_IsNotCustomer(BOARD_C_IPCR20S3_201368))
                        {
                            setSomeipInfo(from_addr.sin_addr, stMsgInitParam.rtp_port);
                        }
                        else
                        {
                            setSomeipInfo(rtp_addr.sin_addr, stMsgInitParam.rtp_port);
                        }
                    }
                    break;

                case UNSUBSCRIBE_VIDEO:
                    if(bIsStartStream)
                        bIsStartStream = SV_FALSE;
                    break;

                case SET_CAM_EXC:
                    break;
                    
                case ERS_CAM_EXC:
                    break;    

                case GET_CAM_REGS:
                    startCommunicate = SV_TRUE;
                    bIsStartStream = SV_FALSE;
                    break;    

                case SET_CAM_REGS:
                    /*
                    if(rtp_port != mc.desRtpPort)
                    {
                        print_level(SV_INFO, "update port to %d!\n", mc.desRtpPort);
                        rtp_port = mc.desRtpPort;
                        bIsStartStream = SV_FALSE;
                    }*/

                    handleSetCamRegs(mc);                    
                    break;
                    
                case GET_CAM_ROI:
                    if(bIsStartStream)
                        waiteCommunicate = SV_TRUE;
                    break;

            #if 1//(defined(BOARD_IPCR20S3) || defined(BOARD_IPTR20S1) || defined(BOARD_WFCR20S2))
                case SET_CAM_ROI:                                        
                    if (1 != mc.roiNum)
                    {
                        break;
                    }
                    
                    handleSetCamRoi(mc);
                    break;        
            #endif
            
                default:
                    print_level(SV_ERROR, "Unsupported request method %#x\n", mc.header_.method_);
                    break;
            }
        }
    }
}

int server::handleSetCamRegs(message_control mc)
{
    sint32 s32Ret = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};

    stRetPkt.pu8Data = (uint8 *)&stNetworkCfg;
    s32Ret = Msg_execRequestBlock(EP_SOMEIPSERVER, EP_CONTROL, OP_REQ_GET_NETWORK_CFG, NULL, &stRetPkt, sizeof(MSG_NETWORK_CFG));
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "OP_REQ_GET_NETWORK_CFG failed. [err=%#x]\n", s32Ret);
		return MSG_DEFAULT_FAIL;
	}

    bIsStartStream = SV_FALSE;
    memcpy(&rtp_addr.sin_addr.s_addr, mc.monIP.data(), mc.monIP.size());
    stMsgInitParam.dst_ipaddr = mc.monIP;
    stMsgInitParam.dst_mac = mc.monMAC;
    stMsgInitParam.rtp_port = mc.desRtpPort;
    stMsgInitParam.udp_port = mc.udpPort;
    stMsgInitParam.enable_dhcp = mc.enableDHCP;
    stMsgInitParam.no_stream_at_boot = mc.noStreamAtBoot;

    sprintf(stNetworkCfg.szRtpDstIpAddr, "%d.%d.%d.%d", \
            stMsgInitParam.dst_ipaddr[0], stMsgInitParam.dst_ipaddr[1], \
            stMsgInitParam.dst_ipaddr[2], stMsgInitParam.dst_ipaddr[3]);
    sprintf(stNetworkCfg.szRtpDstMacAddr, "%02X:%02X:%02X:%02X:%02X:%02X", \
            stMsgInitParam.dst_mac[0], stMsgInitParam.dst_mac[1], stMsgInitParam.dst_mac[2], \
            stMsgInitParam.dst_mac[3], stMsgInitParam.dst_mac[4], stMsgInitParam.dst_mac[5]);
    stNetworkCfg.u32RtpDstPort = stMsgInitParam.rtp_port;
    stNetworkCfg.u32UdpComPort = stMsgInitParam.udp_port;
    stNetworkCfg.bDHCPEnable = stMsgInitParam.enable_dhcp ? SV_TRUE : SV_FALSE;
    stNetworkCfg.bNoStreamAtBoot = stMsgInitParam.no_stream_at_boot ? SV_TRUE : SV_FALSE;

    stMsgPkt.pu8Data = (uint8 *)&stNetworkCfg;
    stMsgPkt.u32Size = sizeof(MSG_NETWORK_CFG);
    s32Ret = Msg_execRequestBlock(EP_SOMEIPSERVER, EP_CONTROL, OP_REQ_SET_NETWORK_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
        return SV_FAILURE;
    }
}

int server::handleSetCamRoi(message_control mc)
{
    sint32 s32Ret = 0;
    SV_BOOL bMirror = SV_FALSE;
    SV_BOOL bFlip = SV_FALSE;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
/*
    if (BOARD_IsNotCustomer(BOARD_C_IPCR20S3_201368) && BOARD_IsNotCustomer(BOARD_C_IPTR20S1_201368) && \
        BOARD_IsNotCustomer(BOARD_C_WFCR20S2_201368))
    {
        return SV_SUCCESS;
    }*/

    if (video_width == mc.width && video_height == mc.height)
    {
        print_level(SV_INFO, "same roi param, skip!\n");
        goto skip;
    }
    
    stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
    s32Ret = Msg_execRequestBlock(EP_SOMEIPSERVER, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
        return SV_FAILURE;
    }
    
    //print_level(SV_DEBUG, "bIsStartStream = %d\n", (int)bIsStartStream);
    if(bIsStartStream == SV_FALSE)
    {
        //print_level(SV_DEBUG, "previous resolution is %dx%d!\n", video_width, video_height);                    
        if(video_width != mc.width || video_height != mc.height)
        {
            print_level(SV_INFO, "update resolution to %dx%d!\n", mc.width, mc.height);
            video_width = mc.width;
            video_height = mc.height;                        
            //bIsStartStream = SV_FALSE;
        }

        //print_level(SV_DEBUG, "previous encode type is %d!\n", encode_type);
        if(encode_type != mc.videoCompression)
        {                                               
            print_level(SV_INFO, "update encode type to %d!\n", mc.videoCompression);
            encode_type = mc.videoCompression;
            //bIsStartStream = SV_FALSE;
        }

        if (mc.framerate >= 1 && mc.framerate <= 30)
        {
            print_level(SV_INFO, "new framerate is %d fps!\n", mc.framerate);
            video_framerate = mc.framerate;            
        }

        if (mc.bitrate >= 1 && mc.bitrate <= 20)
        {
            print_level(SV_INFO, "new bitrate is %d Mbps!\n", mc.bitrate);
            video_bitrate = mc.bitrate;            
        }

        if (1 != encode_type && video_bitrate > 6)    // 设置编码格式为h264限制码率不大于6M
        {
            video_bitrate = 6;
        }

        if(BOARD_IsCustomer(BOARD_C_ADA32V2_200001))
        {
            print_level(SV_INFO, "bitrate 2Mbps!\n");
            video_bitrate = 2;                   
        }
                        
#if (defined(PLATFORM_SSC335))
#if (defined(BOARD_IPCR20S3))
        //if(BOARD_IsCustomer(BOARD_C_IPCR20S3_201368))
        {
            if (2 == encode_type)
            {
                stVideoCfg.astChnParam[0].u32MainWidth = video_width;
                stVideoCfg.astChnParam[0].u32MainHeight = video_height;
                
                stVideoCfg.astChnParam[0].u32MainFramerate = video_framerate;
                if (stVideoCfg.astChnParam[0].u32MainIfrmInterval < video_framerate)
                {
                    stVideoCfg.astChnParam[0].u32MainIfrmInterval = video_framerate;
                }
                stVideoCfg.astChnParam[0].u32MainBitrate = video_bitrate * 1024;
                
            }
            else
            {
                stVideoCfg.astChnParam[0].u32JpegWidth = video_width;
                stVideoCfg.astChnParam[0].u32JpegHeight = video_height;
            }
        }
#else
        {
            stVideoCfg.astChnParam[0].enMainEncode = (2 == encode_type) ? ENCODE_H264 : ENCODE_MJPEG;
            stVideoCfg.astChnParam[0].enMainRcMode = (2 == encode_type) ? RC_MODE_CBR : RC_MODE_FIXQP; 
            stVideoCfg.astChnParam[0].u32MainWidth = video_width;
            stVideoCfg.astChnParam[0].u32MainHeight = video_height;
            
            stVideoCfg.astChnParam[0].u32MainFramerate = video_framerate;
            if (stVideoCfg.astChnParam[0].u32MainIfrmInterval < video_framerate)
            {
                stVideoCfg.astChnParam[0].u32MainIfrmInterval = video_framerate;
            }
            stVideoCfg.astChnParam[0].u32MainBitrate = video_bitrate * 1024;
        }
#endif
#else    // for ADA32
        stVideoCfg.astChnParam[0].enMainEncode = (2 == encode_type) ? ENCODE_H264 : ENCODE_MJPEG;
        stVideoCfg.astChnParam[0].u32MainWidth = video_width;
        stVideoCfg.astChnParam[0].u32MainHeight = video_height;
        stVideoCfg.astChnParam[0].u32MainFramerate = video_framerate;
        if (stVideoCfg.astChnParam[0].u32MainIfrmInterval < video_framerate)
        {
            stVideoCfg.astChnParam[0].u32MainIfrmInterval = video_framerate;
        }
        stVideoCfg.astChnParam[0].u32MainBitrate = video_bitrate * 1024;      
#endif
    }
    else
    {
        bMirror = (stVideoCfg.astChnParam[0].bImageMirror ? SV_FALSE : SV_TRUE);
        stVideoCfg.astChnParam[0].bImageMirror = bMirror;
    }
                                                                                 
    stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
    stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
    s32Ret = Msg_execRequestBlock(EP_SOMEIPSERVER, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
        return SV_FAILURE;
    }

skip:    
    waiteCommunicate = SV_FALSE;
    return SV_SUCCESS;
}

int server::getSomeipInfo()
{
    int s32Ret = 0;
    int s32Fd = 0;
    
    char szBuf[128] = {0};
    char *szIP = NULL;
    char *szTmp = NULL;

    s32Fd = open("/etc/someip.conf", O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_WARN, "open failed [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    memset(szBuf, 0, sizeof(szBuf));
    s32Ret = read(s32Fd, szBuf, 128);
	if(s32Ret <=  0)
	{
		print_level(SV_ERROR, "read failed [err: %s]\n", strerror(errno));
		close(s32Fd);
		return SV_FAILURE;
	}
	
	close(s32Fd);
	szIP = strtok_r(szBuf, ":", &szTmp);
	if(szIP != NULL)
	{
/*	
	#if defined(BOARD_ADA32E1)
	    inet_aton(szIP, &from_addr.sin_addr);
    #else
        inet_aton(szIP, &rtp_addr.sin_addr);
    #endif
*/  
        if (BOARD_IsNotCustomer(BOARD_C_IPCR20S3_201368))
        {
            inet_aton(szIP, &from_addr.sin_addr);
        }
        else
        {
            inet_aton(szIP, &rtp_addr.sin_addr);
        }
	    stMsgInitParam.rtp_port = atoi(szTmp);
	}
    else
    {
        print_level(SV_ERROR, "getSomeipInfo error!\n");
        return SV_FAILURE;
    }
    print_level(SV_INFO, "getSomeipInfo[%s:%d]\n", inet_ntoa(from_addr.sin_addr), stMsgInitParam.rtp_port);

    return SV_SUCCESS;
}

int server::setSomeipInfo(struct in_addr peerAddr, uint16_t rtpPort)
{
    int s32Ret = 0;
    int s32Fd = 0;
    char szTmp[8] = {0};
    char szBuf[128] = {0};

    CONFIG_FlashProtection(SV_FALSE);

    s32Fd = open("/etc/someip.conf", O_CREAT|O_RDWR, 0644);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "create file failed. [err: %s]\n", strerror(errno));
        CONFIG_FlashProtection(SV_TRUE);
        return SV_FAILURE;
    }

    strcpy(szBuf, inet_ntoa(peerAddr));
    strcat(szBuf, ":");
    snprintf(szTmp, 8, "%d", rtpPort);
    strcat(szBuf, szTmp);

    s32Ret = write(s32Fd, szBuf, strlen(szBuf));
	if(s32Ret <=  0)
	{
		print_level(SV_ERROR, "write failed [err: %s]\n", strerror(errno));
		close(s32Fd);
		return SV_FAILURE;
	}

	fsync(s32Fd);
    close(s32Fd);
    CONFIG_FlashProtection(SV_TRUE);

    printf("setSomeipInfo:%s\n", szBuf);

    return SV_SUCCESS;
}

void server::start()
{
    sint32 s32Ret;

    s32Ret = MSG_ReciverStart(EP_SOMEIPSERVER);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
    }
  
    s32Ret = Msg_registerOpCallback(EP_SOMEIPSERVER, OP_EVENT_NETWORK_STAT, callbackNetworkStateChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback_ThreadExec(EP_SOMEIPSERVER, OP_EVENT_NETWORK_CHANGE, callbackNetworkParamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_SOMEIPSERVER, OP_EVENT_MEDIA_CHANGE, callbackMediaParamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    startThreads();
    //std::thread initThread(&serverInit, this);
    //initThread.detach();
/*    
    //isofferService = SV_TRUE;
    std::thread offerThread(&offerService, this);
    offerThread.detach();

    //ishandleMessage = SV_TRUE;
    std::thread handleThread(&handleMessage, this);
    handleThread.detach();*/
}

void server::stop()
{
    isofferService = SV_FALSE;
    ishandleMessage = SV_FALSE;
    close(fd_);
}

void server::serverInit()
{
    int so_broadcast = 1;
    struct ifreq ifr;
    struct sockaddr_in sin;
    sint32 s32Ret;
    MSG_PACKET_S stRetPkt = {0};
    MSG_NETWORK_CFG stNetworkCfg = {0};
    
    do
    {   
        print_level(SV_WARN, "Network not inited!\n ");
        sleep(5);
    }
    while(!isNetworkInited);

    fd_ = ::socket(AF_INET, SOCK_DGRAM, 0);
    if(fd_ < 0)
    {
        print_level(SV_ERROR, "server create error");
    }

    print_level(SV_DEBUG, "stMsgInitParam.no_stream_at_boot=%d\n", stMsgInitParam.no_stream_at_boot);
    //if(BOARD_IsCustomer(BOARD_C_IPCR20S3_201368))
    if(!stMsgInitParam.no_stream_at_boot)
    {
        if(getSomeipInfo() == SV_SUCCESS)
        {
            printf("startStream directly\n");
            if(bIsStartStream == SV_FALSE)
            {
                std::thread streamThread(&startStream, this);
                streamThread.detach();
            }
        }
    }

    transRes2Number(stMsgInitParam.media_resolution, &video_width, &video_height);
    encode_type = stMsgInitParam.media_encode_type == 2 ? 1 : 2;
    printf("someip resolution: %dx%d, encode type: %d\n", video_width, video_height, encode_type);
        
    isofferService = SV_TRUE;
    ishandleMessage = SV_TRUE;
    
#if (defined(BOARD_WFCR20S2))
    strcpy(ifr.ifr_name, "wlan0");
#else
    if (!stMsgInitParam.enable_dhcp)
    {
        print_level(SV_INFO, "Use Static IP address.\n");
        goto skip;
    }
    print_level(SV_INFO, "DHCP enabled, obtain actual IP address.\n");
    strcpy(ifr.ifr_name, "eth0");
#endif
    /* 获取网卡ip地址 */
    if(ioctl(fd_, SIOCGIFADDR, &ifr) == -1)
    {
        perror("ioctl error");
    }
    memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    char ip[16];
    //memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    snprintf(ip, 16, "%s", inet_ntoa(sin.sin_addr));
    sscanf(ip, "%hhu.%hhu.%hhu.%hhu", &stMsgInitParam.ipaddr[0], &stMsgInitParam.ipaddr[1],&stMsgInitParam.ipaddr[2],&stMsgInitParam.ipaddr[3]);

    /* 获取网卡mac地址 */
    if(ioctl(fd_, SIOCGIFHWADDR, &ifr) == -1)
    {
        perror("ioctl error");
    }

    for(int i = 0; i < 6; i++)
        stMsgInitParam.mac[i] = ifr.ifr_hwaddr.sa_data[i];

    /* 获取网卡子网掩码 */
    if (ioctl(fd_, SIOCGIFNETMASK, &ifr) == -1) 
    {
        perror("ioctl error");
    }
    
    memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    char mask[16];
    
    //memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    snprintf(mask, 16, "%s", inet_ntoa(sin.sin_addr));
    sscanf(mask, "%hhu.%hhu.%hhu.%hhu", &stMsgInitParam.netmask[0], &stMsgInitParam.netmask[1],&stMsgInitParam.netmask[2],&stMsgInitParam.netmask[3]);
skip:
;
    for(int i = 0; i < 4; i++)
        printf("ip[%d]=%d\n", i, stMsgInitParam.ipaddr[i]);
    
    for(int i = 0; i < 6; i++)
        printf("mac[%d]=%d\n", i, stMsgInitParam.mac[i]);

    for(int i = 0; i < 4; i++)
        printf("netmask[%d]=%d\n", i, stMsgInitParam.netmask[i]);

    if (BOARD_IsCustomer(BOARD_C_ADA32E1_200946) || BOARD_IsCustomer(BOARD_C_IPTR20S1_201590)) {
        stMsgInitParam.udp_port = 50010;
        stMsgInitParam.rtp_port = 5000 + stMsgInitParam.ipaddr[3];
    }
    printf("udp port: %d\n", stMsgInitParam.udp_port);
    printf("rtp port: %d\n", stMsgInitParam.rtp_port);

    service_addr.sin_family = AF_INET;
    service_addr.sin_port = htons(stMsgInitParam.udp_port);
    service_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    
    s32Ret = bind(fd_, (struct sockaddr *)&service_addr, sizeof(service_addr));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "bind socket to port: %d failed.\n", stMsgInitParam.udp_port);
        perror("bind");
    }
    
    s32Ret = setsockopt(fd_, SOL_SOCKET, SO_BROADCAST, &so_broadcast, sizeof(so_broadcast));
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "setsockopt failed.\n");
        perror("bind");
    }
    
#if (defined(BOARD_WFCR20S2))
    bzero(&ifr, sizeof(ifr));
	strncpy(ifr.ifr_name, "wlan0", IFNAMSIZ);
	if (setsockopt(fd_, SOL_SOCKET, SO_BINDTODEVICE, &ifr, sizeof(ifr)) < 0) //fd 绑定网卡
	{
		print_level(SV_WARN, "bind device:%s error\n", "wlan0");
		perror("bind device name");
	}
#endif

    broadcast_addr.sin_family = AF_INET;
    if (BOARD_IsCustomer(BOARD_C_IPCR20S3_JLG) || BOARD_IsCustomer(BOARD_C_ADA32E1_200946) || BOARD_IsCustomer(BOARD_C_IPTR20S1_201590))
    {
        broadcast_addr.sin_port = htons(stMsgInitParam.udp_port + 1);
    }
    else
    {
        broadcast_addr.sin_port = htons(stMsgInitParam.udp_port);
    }    
    broadcast_addr.sin_addr.s_addr = htonl(INADDR_BROADCAST);

    rtp_addr.sin_family = AF_INET;

    std::thread offerThread(&offerService, this);
    offerThread.detach();

    std::thread handleThread(&handleMessage, this);
    handleThread.detach();
}

void server::startThreads()
{
    std::thread initThread(&serverInit, this);
    initThread.detach();
}

sint32 SOMEIP_Server_Init(SOMEIP_CFG_PARAM_S *pstInitParam)
{
    if(pser == nullptr)
	{
		print_level(SV_INFO,"SOMEIP server init.\n");
		pser = new server();
        pser->stMsgInitParam = *pstInitParam;        
        pser->start();
	}

	if(pser != nullptr)	
	{
		return SV_SUCCESS;
	}
	else
	{
		return SV_FAILURE;
	}
}
