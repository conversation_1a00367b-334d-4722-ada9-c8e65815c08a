Index: src/common/msg/msg.c
===================================================================
--- src/common/msg/msg.c	(版本 4066)
+++ src/common/msg/msg.c	(工作副本)
@@ -783,7 +783,7 @@
         return ERR_ILLEGAL_PARAM;
     }
 
-    if (m_stMsgInfo.bMaster && (enReciverId != EP_ALG) && !m_stMsgInfo.astReciverList[enReciverId].bStarted)
+    if (m_stMsgInfo.bMaster && (enReciverId != EP_ALG && enReciverId != EP_FACTORY) && !m_stMsgInfo.astReciverList[enReciverId].bStarted)
     {
         return ERR_UNEXIST;
     }
Index: src/control/control.c
===================================================================
--- src/control/control.c	(版本 4066)
+++ src/control/control.c	(工作副本)
@@ -730,6 +730,15 @@
         return SV_FAILURE;
     }
 
+#if (defined(BOARD_ADA47V1))
+    s32Ret = Msg_submitEvent(EP_FACTORY, OP_EVENT_CFG_UPDATE, NULL);
+    if (SV_SUCCESS != s32Ret)
+    {
+        print_level(SV_ERROR, "OP_EVENT_CFG_UPDATE failed. [err=%#x]\n", s32Ret);
+        return SV_FAILURE;
+    }
+#endif
+
     return SV_SUCCESS;
 }
 
@@ -3031,16 +3040,19 @@
 #endif
 #if (defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1))
     stResetParam.stExtern[0].bAlgEnable = pstAlgCfg->bAlgEnable;
-    stResetParam.stExtern[0].enAlgType = pstAlgCfg->stAlgCh2.enAlgType;
-    stResetParam.stExtern[0].stApcParam.enApcDirection = pstAlgCfg->stAlgCh2.stApcParam.enApcDirection;
-    stResetParam.stExtern[0].stApcParam.stApcDivider = pstAlgCfg->stAlgCh2.stApcParam.stApcDivider;
-    stResetParam.stExtern[0].stApcParam.enRoiGui        = pstAlgCfg->stAlgCh2.stApcParam.enRoiGui;
-    stResetParam.stExtern[0].stApcParam.enApcUi         = pstAlgCfg->stAlgCh2.stApcParam.enApcUi;
-    stResetParam.stExtern[0].stApcParam.bApcUiTotalEnable         = pstAlgCfg->stAlgCh2.stApcParam.bApcUiTotalEnable;
-    stResetParam.stExtern[0].stApcParam.bApcUiRealEnable          = pstAlgCfg->stAlgCh2.stApcParam.bApcUiRealEnable;
-    stResetParam.stExtern[0].stApcParam.bApcUiInEnable            = pstAlgCfg->stAlgCh2.stApcParam.bApcUiInEnable;
-    stResetParam.stExtern[0].stApcParam.bApcUiOutEnable           = pstAlgCfg->stAlgCh2.stApcParam.bApcUiOutEnable;
-    memcpy(stResetParam.stExtern[0].stApcParam.astApcDetectionPoints, pstAlgCfg->stAlgCh2.stApcParam.astApcDetectionPoints, sizeof(SV_POINT2_S)*2);
+    stResetParam.stExtern[0].enAlgType = pstAlgCfg->stAlgCh2.enAlgType;  
+    if (stResetParam.stExtern[0].enAlgType == ALG_APC)
+    {
+        stResetParam.stExtern[0].stApcParam.enApcDirection = pstAlgCfg->stAlgCh2.stApcParam.enApcDirection;
+        stResetParam.stExtern[0].stApcParam.stApcDivider = pstAlgCfg->stAlgCh2.stApcParam.stApcDivider;
+        stResetParam.stExtern[0].stApcParam.enRoiGui        = pstAlgCfg->stAlgCh2.stApcParam.enRoiGui;
+        stResetParam.stExtern[0].stApcParam.enApcUi         = pstAlgCfg->stAlgCh2.stApcParam.enApcUi;
+        stResetParam.stExtern[0].stApcParam.bApcUiTotalEnable         = pstAlgCfg->stAlgCh2.stApcParam.bApcUiTotalEnable;
+        stResetParam.stExtern[0].stApcParam.bApcUiRealEnable          = pstAlgCfg->stAlgCh2.stApcParam.bApcUiRealEnable;
+        stResetParam.stExtern[0].stApcParam.bApcUiInEnable            = pstAlgCfg->stAlgCh2.stApcParam.bApcUiInEnable;
+        stResetParam.stExtern[0].stApcParam.bApcUiOutEnable           = pstAlgCfg->stAlgCh2.stApcParam.bApcUiOutEnable;
+        memcpy(stResetParam.stExtern[0].stApcParam.astApcDetectionPoints, pstAlgCfg->stAlgCh2.stApcParam.astApcDetectionPoints, sizeof(SV_POINT2_S)*2);
+    }
 #endif
 #if (defined(BOARD_HDW845V1))
     stResetParam.stExtern[0].bAlgEnable = pstAlgCfg->bAlgEnable;
Index: src/peripheral/bluetooth/bluetooth.c
===================================================================
--- src/peripheral/bluetooth/bluetooth.c	(版本 4066)
+++ src/peripheral/bluetooth/bluetooth.c	(工作副本)
@@ -178,6 +178,14 @@
             print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
             return SV_FAILURE;
         }
+#if (defined(BOARD_ADA47V1))
+        s32Ret = Msg_submitEvent(EP_FACTORY, OP_EVENT_BLUETOOTH_RECV, &stMsgPkt);
+        if (SV_SUCCESS != s32Ret)
+        {
+            print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
+            return SV_FAILURE;
+        }
+#endif
     }
     
     return SV_SUCCESS;
