/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：config.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-12-15

文件功能描述: 定义参数配置功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _CONFIG_H_
#define _CONFIG_H_

#include "common.h"
#include "media.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

extern const char *m_pszDmsWorkSpeed[ALARM_NO_HELMET+1];
extern sint32 m_s32DmsWorkSpeedNum;

#define CONFIG_TMP_PATH         "/var/config.xml"   /* 配置文件临时路径 */
#if (defined(BOARD_IPCR20S5))
#define CONFIG_XML              "/config/config.xml"
#define CONFIG_BAK1             "/config/config_bak1.xml"
#define CONFIG_BAK2             "/config/config_bak2.xml"
#define CONFIG_DEFAULT          "/config/config_default.xml"
#define CONFIG_DEFAULT_BAK      "/config/config_default_bak.xml"
#define CONFIG_SERAILNUM        "/config/serialNumber"         /* 序列号 */
#define CONFIG_FIRMWARE         "/config/firmwareVersion"      /* 固件版本 */
#define CONFIG_HARDWARE         "/config/hardwareVersion"      /* 硬件版本 */
#define CONFIG_UUID             "/config/uuid"                 /* uuid */
#define CONFIG_IPADDR           "/config/defaultIpaddr"        /* 出厂IP地址 */
#define CONFIG_SUPER_PWD        "/config/superPassword"        /* 超级密码 */
#define CONFIG_HTTPS_CRT        "/config/server_crt.pem"       /* HTTPS服务器证书 */
#define CONFIG_HTTPS_KEY        "/config/server_key.pem"       /* HTTPS服务器密钥 */
#define CUSTOM_IQ_FILE          "/config/customIQ"             /* IQ参数文件 */
#define PRELOAD_TXT             "/misc/PreloadSetting.txt"     /* 媒体预初始化配置 */
#define PRELOAD_TXT_DEFAULT     "/misc/PreloadSetting_default.txt"
#else
#define CONFIG_XML              "/etc/config.xml"
#define CONFIG_BAK1             "/etc/config_bak1.xml"
#define CONFIG_BAK2             "/etc/config_bak2.xml"
#define CONFIG_DEFAULT          "/etc/config_default.xml"
#define CONFIG_DEFAULT_BAK      "/etc/config_default_bak.xml"
#define CONFIG_SERAILNUM        "/etc/serialNumber"         /* 序列号 */
#define CONFIG_FIRMWARE         "/etc/firmwareVersion"      /* 固件版本 */
#define CONFIG_HARDWARE         "/etc/hardwareVersion"      /* 硬件版本 */
#define CONFIG_UUID             "/etc/uuid"                 /* uuid */
#define CONFIG_IPADDR           "/etc/defaultIpaddr"        /* 出厂IP地址 */
#define CONFIG_SUPER_PWD        "/etc/superPassword"        /* 超级密码 */
#define CONFIG_HTTPS_CRT        "/etc/server_crt.pem"       /* HTTPS服务器证书 */
#define CONFIG_HTTPS_KEY        "/etc/server_key.pem"       /* HTTPS服务器密钥 */
#define CUSTOM_IQ_FILE          "/etc/customIQ"             /* IQ参数文件 */
#endif
#define UDHCPD_CONF				"/etc/udhcpd.conf"
#define UDHCPD_DEFAULT			"/etc/udhcpd_default.conf"
#define UDHCPD_ETH_CONF		    "/etc/udhcpd_eth.conf"
#define UDHCPD_ETH_DEFAULT		"/etc/udhcpd_eth_default.conf"
#define CONFIG_BOOTUUID         "/var/bootUuid"             /* bootUuid */
#define CONFIG_JOSN				"/etc/config.json"			/*a47 专用版自定义算法配置项*/
#define CONFIG_MCUVER           "/etc/mcuVersion"           /* a32c4 mcu版本号 */



#define CONFIG_MAX_BUF_SIZE     128

/* 设备信息 */
typedef struct tagCfgDeviceInfo_S
{
    char  szSerialNum[32];          /* 设备序列号 */
    char  szFirmwareVer[32];        /* 软件版本号 */
    char  szHardwareVer[32];        /* 硬件版本号 */
    char  szUuid[40];               /* 设备UUID */
    char  szBootUuid[40];           /* 设备启动UUID */
    char  szMcuVer[128];            /* 设备MCU版本号 */
} CFG_DEV_INFO;

/* 网络配置参数 */
typedef struct tagCfgNetworkParam_S
{
    char   *pszDnsServer;            /* DNS服务器地址 */
    SV_BOOL bLanNotUpdate;           /* 有线网参数是否更新 */
    SV_BOOL bEnableDHCP;             /* 是否使能DHCP */
	sint32  s32DhcpTimeout;			 /* DHCP 分配ip超时时间 */
    SV_BOOL bEnableStatic;           /* 是否使能静态配置IP */
    char   *pszEthIpAddr;            /* 以太网IP地址 */
    char   *pszEthSubmask;           /* 以太网子网掩码 */
    char   *pszEthMacAddr;           /* 以太网MAC地址 */
    char   *pszEthGateway;           /* 以太网网关地址 */
    WIFI_AUTH enWifiAuth;            /* WiFi热点认证模式 */
    char   *pszWifiApSsid;           /* WiFi热点SSID */
	char   *pszWifiApIpAddr;		 /* WiFi热点AP IP地址*/
    char   *pszWifiApPwd;            /* WiFi热点密码 */
    char   *pszCountryCode;          /* WiFi热点国家码 */
    WIFI_FREQ enWifiFreq;            /* WiFi热点频率 */
    long    lSet2GChannel;           /* WiFi热点配置2G信道 */
    long    lSet5GChannel;           /* WiFi热点配置5G信道 */
    SV_BOOL bWifiStaEnable;          /* 是否使用WiFi STA模式 */
	char   *pszWifiStaIpAddr;		 /* WiFi热点STA IP地址，可在分配到一个IP地址之后，修改同网段的IP，或直接不请求分配IP，使用该IP地址 */
	char   *pszWifiStaGateway;		 /* WiFi热点STA 网关 */
	char   *pszWifiStaSubmask;		 /* WiFi热点STA 子网掩码 */
    char   *pszWifiStaSsid;          /* WiFi STA访问热点的SSID */
    char   *pszWifiStaPwd;           /* WiFi STA访问热点的密码 */
    uint32  u32RtspServPort;         /* RTSP服务端口 */
	char   *pszRtspServMainUri;		 /* RTSP 主码流uri */
	char   *pszRtspServSubUri;		 /* RTSP 子码流uri */
	char   *pszRtspServPicUri;		 /* RTSP 图片流uri */
    char   *pszRtspServPubAddr;		 /* RTSP 公网地址 */
    SV_BOOL bDiscoverable;           /* Onvif发现开关 */
    uint32  u32OnvifServPort;        /* Onvif服务端口 */
    SV_BOOL bRTPMulticast;           /* RTP广播开关 */
    uint32  u32MulticastPort;        /* RTP广播端口 */
	sint8  *ps8Apn;					 /* 4G模块APN */
	sint8  *ps8ApnUserName;			 /* 4G模块APN用户名 */
	sint8  *ps8ApnPassWd;		     /* 4G模块APN密码 */
	SV_BOOL bLogTestMode;            /* 4G模块测试模式使能 */
	SV_BOOL bEnableBle;              /* 是否使能蓝牙 */
	char   *pszBleName;              /* 蓝牙广播名 */
	uint64  u64AvtpStreamID;		 /*avtp 视频流ID */
	char   *pszAvtpDestMAC;			 /* avtp目的设备mac地址 */
    char   *pszRtpDstIpAddr;         /* SOME/IP建立RTP连接的目标IP地址 */
    char   *pszRtpDstMacAddr;        /* SOME/IP建立RTP连接的目标MAC地址 */
    uint32  u32RtpDstPort;           /* SOME/IP建立RTP连接的目标端口 */
    uint32  u32UdpComPort;           /* SOME/IP建立UDP连接的端口 */
    SV_BOOL bNoStreamAtBoot;         /* SOME/IP不直接向已记录的客户端发送媒体数据 */
} CFG_NETWORK_PARAM;

/* 媒体通道参数 */
typedef struct tagCfgChnParam_S
{
    SV_BOOL     bChnEnable;         /* 是否使能通道(仅AIBOX有效) */
    SV_BOOL     bShowTime;          /* OSD是否显示时间 */
    TIME_FMT    enTimeFormat;       /* OSD时间日期格式 */
    SV_BOOL     bShowChnName;       /* OSD是否显示通道名 */
    SV_BOOL     bShowState;         /* OSD是否显示状态信息 */
    SV_BOOL     bShowZoom;          /* OSD是否显示变焦倍数 */
    uint32      bShowGuiMask;       /* OSD是否显示Gui绘图掩码(0b1,0b10,0b100,b10000对于通道0,1,2,3) */
    VOSD_POS_E  enOsdPosition;      /* OSD显示位置 */
    char        szChnName[192];     /* 通道名 */
    SV_BOOL     bRemoveFishEye;     /* 是否去鱼眼 */
    SV_BOOL     bRefScene;           /* 是否启用反光衣IQ文件 */
    SV_BOOL     bImageMirror;       /* 是否使能画面镜像 */
    SV_BOOL     bImageFlip;         /* 是否使能画面翻转 */
    SV_ROT_ANGLE_E enRotateAngle;   /* 画面旋转角度 */
    ENCODE_E    enMainEncode;       /* 主码流编码格式 */
    MEDIA_RES   enMainResolution;   /* 主码流分辨率 */
    uint32      u32MainFramerate;   /* 主码流帧率 */
    uint32      u32MainBitrate;     /* 主码流码率(Kbps) */
    RC_MODE_E   enMainRcMode;       /* 主码流码流控制模式 */
    uint32      u32MainIfrmInterval;/* 主码流I帧间隔 */
    uint32      u32Qfactor;         /* 主码流Q值，用于fixqp */
    ENCODE_E    enSubEncode;        /* 子码流编码格式 */
    MEDIA_RES   enSubResolution;    /* 子码流分辨率 */
    uint32      u32SubFramerate;    /* 子码流帧率 */
    uint32      u32SubBitrate;      /* 子码流码率(Kbps) */
    RC_MODE_E   enSubRcMode;        /* 子码流码流控制模式 */
    uint32      u32SubIfrmInterval; /* 子码流I帧间隔 */
    MEDIA_RES   enJpegResolution;   /* 图片流分辨率 */
    uint32      u32JpegFramerate;   /* 图片流帧率 */
    SV_BOOL     bAudioEnable;       /* 是否使能音频 */
    SV_BOOL     bAudioMicEnable;    /* 是否使能MIC音频 */
    SV_BOOL     bAudioAlarmEnable;  /* 是否使能报警音频 */
    float       fDigZoom;           /* 数字变倍 */
    float       fOptZoom;           /* 光学变倍 */
    char        szExtendData[64];   /* 额外数据 */
} CFG_CHN_PARAM;

/* 媒体配置参数 */
typedef struct tagCfgMediaParam_S
{
    SV_BOOL     bBitrateAdaptive;   /* 是否启动码流自适应功能(只有无线传输平台有效) */
    uint32      u32IRcutMode;       /* IRCUT模式(0:白天,1:自动,2:夜晚) */
    uint32      u32LedBright;       /* 红外灯亮度 [0-10] */
    WDR_MODE_EE enUsrWDRMode;       /* WDR模式(失能,使能,自适应) */
    VIDEO_MODE_EE enVideoMode;      /* 视频制式 */
    uint32      u32ViFramerate;     /* VI 输入帧率 */
    MEDIA_RES   enVoResolution;     /* VO 输出流分辨率 */
    uint32      u32VoFramerate;     /* VO 输出帧率 */
    SPLIT_MODE  enVoSplitMode;      /* VO 显示分屏 */
    MEDIA_CVBS  enExtscreenMode;    /* Extscreen 扩展屏制式 */
    AUD_ENC_E   enAudioEncType;     /* 音频编码类型 */
    AUD_SR_E    enAudioSampleRate;  /* 音频采样率 */
    uint32      u32Volume;          /* 输入音量 [1-30] */
    uint32      u32OutputVolume;    /* 输出音量 [1-23] */
    uint8       u8Brightness;       /* 亮度 [0-100] */
    uint8       u8Contrast;         /* 对比度 [0-100] */
    uint8       u8Saturation;       /* 饱和度 [0-100] */
    uint8       u8Sharpness;        /* 锐度 [0-100] */
    CFG_CHN_PARAM astChnParam[MEDIA_MAX_CHN]; /* 各媒体通道参数 */
    SV_BOOL     bShowRoIcon;        /* 是否显示旋转图标 */
    SV_BOOL     bShowCsrIcon;       /* 是否显示客户专用图标 */
    SV_BOOL     bShowAlarmIcon;     /* 是否显示报警图标 */
	EX_TYPE_E   enExposureTime;		/* 曝光时间 */
	uint32      u32GainAdjustment;	/* 增益调节 [0-255]*/
	FC_MODE_EE 	enFocusControl;		/* 聚焦控制 */
	BLC_AREA_EE	enBLCEnable;		/* 背光补偿区域 */
	uint8		u8BLCLevel;			/* 背光补偿强度 [0-2]*/
	SV_BOOL		bSLSEnable;			/* 强光抑制*/
	uint8		u8SLSLevel;			/* 强光抑制等级 [0-255]*/
	SV_BOOL		bImgEnhance;		/* 图像增强 */
	HDR_MODE_EE enHDRMode;			/* 宽动态*/
	uint8		u8HDRLevel;			/* 宽动态等级 [0-100]*/
	WB_MODE_EE	enWhiteBalance;		/* 白平衡*/
	uint8 		u8RGain;			/* 白平衡R增益 */
	uint8 		u8GGain;			/* 白平衡G增益 */
	uint8 		u8BGain;			/* 白平衡B增益 */
	DNR_MODE_EE	enDNREnable;		/*数字降噪*/
	uint16 		u16DNRLevel;		/* 数字降噪等级 0~7:空域 8~16:时域 */
	DF_MODE_EE	enDefogMode;		/* 透雾模式 */
	uint8		u8DefogLevel;		/* 透雾等级 [0-255] */
} CFG_MEDIA_PARAM;

typedef struct tagCfgDDAWParam_S
{
    float   fBlinkScorer;                                    /* 当因为疲劳出现频繁眨眼增加的分数 */
    float   fYawnScorer;                                     /* 打哈欠一次增加的分数，时限是过去的15秒内 */
    float   fBlinkThreshold;                                 /* 用于判断因为疲劳眨眼次数变化的阈值，也就是给予fBlinkScorer的阈值 */
    float   fGazeThreshold;                                  /* 疲劳时视线俯仰角相对于正前方的范围， 不开放 */
    float   fFatigueYawRadius;                               /* 在头部水平角相对于标定水平角的范围内，才会判断是疲劳，例如，标定值是10,该值是25,那就是[-15,35]范围内才会判断为疲劳 */
    float   fFatigueEcrGradThreshold;                        /* 疲劳闭合度变化率阈值，当闭合度变小时（还没完全闭合），闭合度的变化率较小时，才会认为是疲劳，该值是判断闭合度的变化率的阈值 */
    float   fFatigueYawGradThreshold;                        /* 疲劳时头部水平角变化率应该是较小的，该值是阈值 */
    float   fFatiguePitchGradThreshold;                      /* 疲劳时头部俯仰角变化率应该是较小的，该值是阈值 */
    float   fShelterThreshold;                               /* 摄像头遮挡阈值 */
    sint32  s32KssLevel7Thres;                               /* KSS七级阈值 */
    sint32  s32KssLevel8Thres;                               /* KSS八级阈值 */
    sint32  s32KssLevel9Thres;                               /* KSS九级阈值 */
} CFG_DDAW_PARAM;

/*DDAW法规三级疲劳检测间隔*/
typedef struct tagCfgDDAWLevel_S
{
    sint32      s32MildFatigueInterval;             /* DDAW法规KSS七级疲劳       检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32ModerateFatigueInterval;         /* DDAW法规KSS八级疲劳       检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32SevereFatigueInterval;           /* DDAW法规KSS九级疲劳       检测间隔(s) [-1~300],-1表示不检测 */
} CFG_DDAW_INTERVAL;

typedef enum NO_DRIVER_STRATEGY
{
    NO_DRIVER_STG_BODY = 0,           /* 无司机检测策略：以检测不到身体为判断依据 */
    NO_DRIVER_STG_FACE,               /* 无司机检测策略：以检测不到人脸为判断依据 */
}NO_DRIVER_STRATEGY;

/* DMS算法配置 */
typedef struct tagCfgDmsParam_S
{
    /* 中联重科需求 */
    SV_BOOL             bDmsReadPilotState;                 /* 是否读取CAN先导状态 */
    SV_BOOL             bDmsReadEngineRPM;                  /* 是否读取发动机状态 */
    NO_DRIVER_STRATEGY  enDmsNoDriverStrategy;              /* 无司机检测策略 */
    ALARM_AUD_E enAudioType;                                /* DMS报警声音类型 */

    /* DMS算法间隔设置 */
    sint32      s32FatigueInterval;                 /* 疲劳 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32FatigueL2Interval;               /* 二级疲劳     检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32DistractionInterval;             /* 分心 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32NoDriverInterval;                /* 无司机 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32SmokeInterval;                   /* 抽烟检 测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32PhoneInterval;                   /* 打电话 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32YawnInterval;                    /* 打哈欠 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32NoMaskInterval;                  /* 未配带口罩 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32SunGlassInterval;                /* 配带太阳眼镜 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32SeatBeltInterval;                /* 系安全带 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32ShelterInterval;                 /* DMS画面遮挡 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32DrinkEatInterval;                /* DMS吃喝东西 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32OverspeedInterval;               /* DMS超速 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32NoHelmetInterval;                /* DMS无安全帽 检测间隔(s) [-1~300],-1表示不检测 */
    sint32      s32ChangeGuardInterval;             /* DMS司机换岗 检测间隔(s) [-1, 0], CREARE专用, -1表示不检测, 0表示开启检测 */
    CFG_DDAW_INTERVAL stDDAWInterval;               /* DDAW法规三级疲劳检测 */

    /* DMS工作参数设置 */
    sint32      s32DmsSensitivity;                  /* DMS算法灵敏度[-1~2](0:低, 1:中, 2:高, -1:自动模式:根据当前车速使用以下速度阈值确定灵敏度) */
    sint32      s32DmsMiddleSpeedThr;               /* DMS算法中灵敏度速度阈值(Km/h) <MT:低灵敏度, >=MT&&<HT: 中灵敏度 */
    sint32      s32DmsHighSpeedThr;                 /* DMS算法高灵敏度速度阈值(Km/h) >=HT:高灵敏度 */
    SV_BOOL     bDmsOsdEnable;                      /* 是否使能DMS算法的OSD叠加 */
    SV_BOOL     bDmsCalibrated;                     /* DMS设备是否已校准(由算法程序校准后置位) */
    sint32      ps32CalibrateHeadAngle[2];          /* DMS设备标定头部姿态角度，0，俯仰角，1,水平角 */
    sint32      ps32CalibrateGazeAngle[2];          /* DMS设备标定视线角度，0，俯仰角，1,水平角 */
    sint32      s32DmsCenterAngle;                  /* DMS设备安装的中心角(由算法程序写入) */
    sint32      s32DmsPitchAngle;                   /* DMS设备安装的俯仰角(由算法程序写入) */
    SV_BOOL     bDmsWorkspeed_almNoGPS;             /* DMS算法是否使能在无GPS信号时保持警报 */
    sint32      s32DmsWorkspeed_speed;              /* DMS算法启动速度，弃用，合并到s32DmsWorkspeed[0]中 */
    sint32      s32DmsWorkspeed[ALARM_DMS_BUFF];    /* DMS算法启动速度数组定义 */
    EDmsLogin   enDmsLoginMode;                     /* DMS登陆检测模式 */
    sint32      s32DmsFrInterval;                   /* DMS人脸识别检测间隔(s) [-1, 300, 600, 900, 1200], -1表示关闭自动检测 */
    SV_BOOL     bDmsFaceCapture;                    /* DMS是否在开机时抓取人脸图片（creare客户需求） */
    SV_BOOL     bDmsGazeTracking;                   /* DMS算法是否开启视线跟踪 */
    SV_BOOL     bDmsFatigueOnly;                    /* DMS是否只检测疲劳 */
    SV_BOOL     bDmsAutoCalibration;                /* DMS是否开启自动标定功能 */
    sint32      s32AutoCalibPoseTimeLength;         /* DMS自动标定头部姿态统计间距 */
    SV_BOOL     bDDAWRegulation;                    /* DDAW法规开关 */

    /* DMS算法高级设置 */
    sint32      s32DmsEyelidClosure;            /* DMS算法眼睑闭合度	0~100 */
    sint32      s32DmsFatigueTimelimit;         /* DMS算法疲劳时限	1~6 */
    sint32      s32DmsFatigueSumTime;           /* DMS算法疲劳统计时长 30~120s */
    sint32      s32DmsFatigueClosePercent;      /* DMS算法疲劳闭眼百分比	10%~100% */
    sint32      s32DmsDistractionAngleUp;       /* DMS算法上分心角度	20~50 */
    sint32      s32DmsDistractionAngleDown;     /* DMS算法下分心角度	20~50 */
    sint32      s32DmsDistractionAngleLeft;     /* DMS算法左分心角度	20~75 */
    sint32      s32DmsDistractionAngleRight;    /* DMS算法右分心角度	20~75 */
    sint32      s32DmsDistractionTimelimit;     /* DMS算法分心时限	1~9 */
    sint32      s32DmsNodriverTimelimit;        /* DMS算法无司机时限	1~30 */
    sint32      s32DmsSmokeThreshold;           /* DMS算法抽烟阈值	0~100 */
    sint32      s32DmsSmokeTimelimit;           /* DMS算法抽烟时限	0~5 */
    sint32      s32DmsPhoneThreshold;           /* DMS算法打电话阈值	0~100 */
    sint32      s32DmsPhoneTimelimit;           /* DMS算法打电话时限	0~5 */
    sint32      s32DmsYawnTimelimit;            /* DMS算法打哈欠时限	0~5 */
    sint32      s32DmsNoMaskTimelimit;          /* DMS算法无口罩时限	0~10 */
    sint32      s32DmsSunGlassTimelimit;        /* DMS算法太阳眼镜时限	0~10 */
    sint32      s32DmsSeatBeltThreshold;        /* DMS算法安全带阈值 0~100 */
    sint32      s32DmsSeatBeltTimelimit;        /* DMS算法安全带时限 5~15 */
    sint32      s32DmsShelterTimelimit;         /* DMS算法判断摄像头遮挡时间 1~20 */
    sint32      s32DmsDrinkEatThreshold;        /* DMS吃喝东西阈值	0~100 */
    sint32      s32DmsDrinkEatTimelimit;        /* DMS吃喝东西时限	0~5 */
    sint32      s32DmsOverspeedLimit;           /* DMS超速限制	0~150 */
    sint32      s32DmsNoHelmetThreshold;        /* DMS无安全帽阈值	0~100 */
    sint32      s32DmsNoHelmetTimelimit;        /* DMS无安全帽时限	1~30 */
    sint32      s32DmsFatigueMildThres;         /* DMS31P 轻度疲劳阈值	20~50 */
    sint32      s32DmsFatigueModerateThres;     /* DMS31P 中度疲劳阈值	40~70  */
    sint32      s32DmsFatigueSevereThres;       /* DMS31P 重度疲劳阈值	60~90 */
    CFG_DDAW_PARAM stDDAWParam;                 /* DDAW相关高级参数 */

    /* DMS算法音频使能设置 */
    SV_BOOL     bDmsAudioEnable;            /* 音频报警总开关 */
    SV_BOOL     bDmsAudioWelcome;           /* 欢迎语音频使能 */
    SV_BOOL     bDmsAudioFatigue;           /* 疲劳音频报警使能 */
    SV_BOOL     bDmsAudioDistraction;       /* 分心音频报警使能 */
    SV_BOOL     bDmsAudioNoDriver;          /* 无司机音频报警使能 */
    SV_BOOL     bDmsAudioSmoke;             /* 抽烟音频报警使能 */
    SV_BOOL     bDmsAudioPhone;             /* 电话音频报警使能 */
    SV_BOOL     bDmsAudioYawn;              /* 打哈欠音频报警使能 */
    SV_BOOL     bDmsAudioNoMask;            /* 无口罩音频报警使能 */
    SV_BOOL     bDmsAudioSunGlass;          /* 太阳眼镜音频报警使能 */
    SV_BOOL     bDmsAudioSeatBelt;          /* 安全带音频报警使能 */
    SV_BOOL     bDmsAudioShelter;           /* 遮挡音频报警使能 */
    SV_BOOL     bDmsAudioFatigueL2;         /* 二级疲劳音频报警使能 */
    SV_BOOL     bDmsAudioDrinkEat;          /* 吃喝东西音频报警使能 */
    SV_BOOL     bDmsAudioOverspeed;         /* 超速音频报警使能 */
    SV_BOOL     bDmsAudioNoHelmet;          /* 无安全帽报警使能 */

    sint32      s32DmsPwmDutyCycle;         /* DMM算法AlarmOut模拟输出PWM波的占空比[0, 100] */
    sint32      s32DmsAlarmOutInterval;     /* DMM算法AlarmOut触发间隔(ms)[0, 10000], 0表示不触发 */

    /* DMS算法报警输出使能设置 */
    SV_BOOL     bDmsAlarmOutFatigue;        /* 疲劳报警输出使能 */
    SV_BOOL     bDmsAlarmOutDistraction;    /* 分心报警输出使能 */
    SV_BOOL     bDmsAlarmOutNoDriver;       /* 无司机报警输出使能 */
    SV_BOOL     bDmsAlarmOutSmoke;          /* 抽烟报警输出使能 */
    SV_BOOL     bDmsAlarmOutPhone;          /* 电话报警输出使能 */
    SV_BOOL     bDmsAlarmOutYawn;           /* 打哈欠报警输出使能 */
    SV_BOOL     bDmsAlarmOutNoMask;         /* 无口罩报警输出使能 */
    SV_BOOL     bDmsAlarmOutSunGlass;       /* 太阳眼镜报警输出使能 */
    SV_BOOL     bDmsAlarmOutSeatBelt;       /* 安全带报警输出使能 */
    SV_BOOL     bDmsAlarmOutShelter;        /* 遮挡报警输出使能 */
    SV_BOOL     bDmsAlarmOutFatigueL2;      /* 二级疲劳输出使能 */
    SV_BOOL     bDmsAlarmOutDrinkEat;       /* 吃喝东西报警输出使能 */
    SV_BOOL     bDmsAlarmOutLoginFail;      /* 人脸认证出错报警输出使能 */
    SV_BOOL     bDmsAlarmOutLoginSuccess;   /* 人脸认证成功报警输出使能 */
    SV_BOOL     bDmsAlarmOutOverspeed;      /* 超速报警输出使能 */
    SV_BOOL     bDmsAlarmOutNoHelmet;       /* 无安全帽报警输出使能 */
} CFG_DMS_PARAM;

/* ADAS算法配置(FCW/LDW) */
typedef struct tagCfgAdasParam_S
{
    ALARM_AUD_E enAudioType;        /* ADAS报警声音类型 */
    SV_BOOL     bFcwEnable;         /* 是否检测前车碰撞 */
    SV_BOOL     bLdwEnable;         /* 是否检测轨道偏离 */
    SV_BOOL     bPdsEnable;         /* 是否检测前方行人 */
    sint32      s32Sensitivity;     /* ADAS算法灵敏度[0~2](0:低, 1:中, 2:高) */
} CFG_ADAS_PARAM;

typedef struct tagCfgPdRoiBoard_S
{
    float           fGreenScale;            /* 绿色数量 */
    float           fYellowScale;           /* 黄色数量 */
    float           fRedScale;              /* 红色数量 */

    SV_POINT2_S     fGreenPoint[10];        /* 绿色点坐标 */
    SV_POINT2_S     fYellowPoint[10];       /* 黄色点坐标 */
    SV_POINT2_S     fRedPoint[10];          /* 黄色点坐标 */
} CFG_PDS_ROI_BOARD_S;

typedef struct tagCfgPdSafeHelmet_S
{
    SV_BOOL         bSkipRedHelmet;            /* 跳过红色安全帽报警 */
    SV_BOOL         bSkipYellowHelmet;         /* 跳过黄色安全帽报警 */
    SV_BOOL         bSkipWhiteHelmet;          /* 跳过白色安全帽报警 */
    SV_BOOL         bSkipBlueHelmet;           /* 跳过蓝色安全帽报警 */
} CFG_PDS_SAFE_HELMET_S;

typedef struct tagCfgTriggerWire_S
{
    SV_BOOL        bAlarmOutSwitch;            /* 三线触发的触发线输出使能 */
    sint32         s32Zonemask;                /* 检测区域的掩码 */
    sint32         s32AlarmTypeMask;           /* 报警类型的掩码 */
    sint32         s32Event;                   /* 202661客户用，0: 行人检测报警           1:开机检测报警 */
} CFG_PDS_Trigger_Wire_S;

typedef struct tagCfgLedConfig_S
{

	sint32   fLightFlickerRate;                  /* ada32p灯闪烁频率*/
	sint32   fLightPwmCyclical;                  /* ada32p灯pwm周期*/
    sint32   fLightPwmDutyCycle;                 /* ada32p灯pwm占空比*/
    sint32   fBuzzerPwmCyclical;                 /* ada32p色蜂鸣器周期*/
    sint32   fBuzzerPwmDutyCycle;                /* ada32p色蜂鸣器占空比*/
    SV_BOOL  bLightSwitch;                       /* ada32p灯控开关 */
} CFG_PDS_LED_CONFIG_S;


/* 行人检测算法配置(PDS) */
typedef struct tagCfgPdParam_S
{
    ALARM_AUD_E enAudioType;                    /* PDS报警声音类型 */
    EPdsModel   enPdsModel;                     /* 行人检测模型 */
    SV_BOOL     bR151Regulation;                /* 是否为R151法规版本 */
    SV_BOOL     bshelterEnable;                 /* 遮挡报警使能 */
    SV_BOOL     bshelterAudioEnable;            /* 遮挡报警音频使能 */
    sint32      s32ShelterTimeThreshold;        /* 遮挡报警时间阈值 */
    Media_Icon_POS_E eIconLocation;             /* 人车图标位置 */
    SV_BOOL     bMosaic;                        /* 是否打马赛克 */
    uint32      u32BlkSize;                     /* 马赛克方块大小(占区域比例) */
    SV_BOOL     bGif;                           /* 是否开启GIF */
    sint32      s32PdWorkspeed_minSpeed;        /* 行人算法规避速度(低于该速度不启动) */
    sint32      s32PdWorkspeed_maxSpeed;        /* 行人算法规避速度(高于该速度不启动) */
    sint32      s32PdSensitivity;               /* 行人算法灵敏度[0~2](0:低, 1:中, 2:高) */
    float       astPdRedSensitivity[3];         /* 红色区域对应的灵敏度 */
    float       astPdYellowSensitivity[3];      /* 黄色区域对应的灵敏度 */
    float       astPdGreenSensitivity[3];       /* 绿色区域对应的灵敏度 */
    sint32      s32PdOsdFontSize;               /* 行人算法检测目标OSD叠加字体大小[0~3](0:不叠加, 1~3叠加字体大小1x~3x) */
    SV_BOOL     bPdAlarmIn;                     /* 行人检测算法输入触发时才使能报警 */
    TRIGGER_TYPE_S enPdAlarmInTrigger;          /* 行人检测算法输入触发模式 */
    SV_BOOL     bPdTestMode;                    /* 行人算法工作为测试模式 */
    SV_BOOL     bPdCrosshairIcon;               /* 行人算法的十字光标是否显示              */
    sint32      s32PdRedInterval;               /* 行人算法检测红区报警间隔(s)[-1~300],-1表示不报警 */
    sint32      s32PdYellowInterval;            /* 行人算法检测黄区报警间隔(s)[-1~300],-1表示不报警 */
    sint32      s32PdGreenInterval;             /* 行人算法检测绿区报警间隔(s)[-1~300],-1表示不报警 */
    sint32      s32PdAlarmOutInterval;          /* 行人算法AlarmOut触发间隔(ms)[0, 10000], 0表示不触发, -1表示自动选择触发时长 */
    SV_BOOL     bRectPerson;                    /* 行人算法检测是否OSD显示行人框 */
	SV_BOOL     bAlarmLight;                    /* 行人算法检测是否打开闪光灯 */
    SV_BOOL     bPdAlarmOutGreen;               /* 行人算法 AlarmOut 绿色使能 */
    SV_BOOL     bPdAlarmOutYellow;              /* 行人算法 AlarmOut 黄色使能 */
    SV_BOOL     bPdAlarmOutRed;                 /* 行人算法 AlarmOut 红色使能 */
    SV_BOOL     bPdRoiGreen;                    /* 行人算法检测区域绿色区域使能 */
    SV_BOOL     bPdRoiYellow;                   /* 行人算法检测区域黄色区域使能 */
    SV_BOOL     bPdRoiRed;                      /* 行人算法检测区域红色区域使能 */
    EPdsDetectPart enDetectPart;                /* 行人算法:行人检测框与行人检测区域交叠的判定方式 */
    float       fRectangleX;                    /* 行人算法：行人检测框宽度 */
    float       fRectangleY;                    /* 行人算法：行人检测框高度 */

    SV_BOOL     bOvertakingEnable;              /* 是否使用超车警报策略 */
    SV_BOOL     bOpticalFlowEnable;             /* 是否使用光流报警策略 */
    float       fOvertakingThres;               /* 超车警报阈值 */

    /* 光流相关配置 */
    sint32      s32OpticalFlowFrmInt;           /* 光流检测帧间隔 */
    sint32      s32OvertakingDetFrmCnt;         /* 超车警报检测帧数 */
    float       fOvertakeRefRate;               /* 超车警报帧数占比 */
    SV_BOOL     bOpticalFreeShake;              /* 是否开启超车警报二次防抖 */
    SV_BOOL     bIntervalFrameWay;              /* 间隔帧方式     ：0帧间隔；1时间间隔 */
    uint32      u32OpPointNumLim;               /* 光流允许极限最大点数 */
    float       fMiniFrameIntervalTime;         /* 间隔时间 */

    SV_BOOL     bDrawFilterTarget;              /* 是否绘制超车报警过滤掉的目标框 */
    CFG_PDROI_GUI_E enRoiGui;                   /* 行人算法红黄绿区域绘制样式 */
    CFG_PDROI_E enRoiStyle;                     /* 行人算法红黄绿区域标定排布样式 */
    SV_BOOL     enPdHollow;                     /* 行人算法检测区域镂空使能 */
    SV_POINT2_S astPdHollowPoints[10];          /* 行人算法检测区域镂空点的坐标 */
    SV_POINT2_S astPdCalibrationPoints[12];     /* 行人算法红黄绿区域标定线(两列每列四点) 如下标定图两种排布方式, 坐标取值[0~1]
                                                 *     1.1 --- 2.1      *   1.1   1.2   1.3   1.4   *
                                                 *          绿           *    |     |     |     |    *
                                                 *    1.2 ----- 2.2     *    |     |     |     |    *
                                                 *          黄           *    |红/绿|    黄  |红/绿|      *
                                                 *   1.3 ------- 2.3    *    |     |     |     |    *
                                                 *          红           *    |     |     |     |    *
                                                 *  1.4 --------- 2.4   *   2.1   2.2   2.3   2.4   *
                                                */
    float fEllipseB[3];                         /* 椭圆B轴长度 */
    CFG_PDS_ROI_BOARD_S stPdRoiBoard;           /* 行人检测算法检测区域(画板参数) */
    CFG_PDS_SAFE_HELMET_S stPdSafetyHelmet;     /* 安全帽属性 */
    CFG_PD_WORK_MODE_E enPDWorkMode;            /* 标定的工作模式 */
    SV_BOOL bCalibrated;                        /* 相机是否已经标定 */
    SV_BOOL bADASCalibrated;                    /* 相机是否已经标定（ADAS）,此位仅在同时兼容两种测距模式时使用 */

    float astCalibrationInterParams[9];         /* BSD标定的内部参数 */
    float astCalibrationRotateVector[3];        /* BSD标定的旋转向量 */
    float astCalibrationTranslateVector[3];     /* BSD标定的平移向量 */
    float astCalibrationDistortionFactor[5];    /* BSD标定的畸变系数 */
    float astCalibrationCamPos[3];              /* BSD标定后相机原点的位置 */
    float astCalibrationEulerAngles[3];         /* BSD标定的相机欧拉角 */
    float astCalibrationPrincipal[2];           /* BSD标定的相机光心在像素上的坐标 */

    sint32 s32CalibrationHeight;                /* ADAS标定的参考高度 */
    float fCalibrationGuides;                   /* ADAS标定的参考线的Y轴比例 */

    sint32 s32PdDetectWBorder;                  /* 测距模式下的矩形宽度 */
    sint32 s32PdDetectRedFBorder;               /* 测距模式下的红色区域边界 */
    sint32 s32PdDetectYellowFBorder;            /* 测距模式下的黄色区域边界 */
    sint32 s32PdDetectGreenFBorder;             /* 测距模式下的黄色区域边界 */

    CFG_PDS_Trigger_Wire_S astTriggerSrc[3];    /* 三触发线报警源设置， 0、1、2分别为红、黄、绿触发线*/
    SV_BOOL bShelterAlarmout;                   /* 使用单根触发线时，是否使能遮挡报警的触发输出 */
    SV_BOOL bAlarmOutPWMmode;                   /* 使用单根触发线时，是否开启PWM模式，开启后各个区域用不同的信号输出，202883客户专用 */

    CFG_PD_VIEW_DIRECTION_E ePdViewDirection;   /* 行人检测摄像头相机视角 */
    SV_BOOL bAlarmBorder;                       /* 是否在报警时在画面边沿显示边框 */
    SV_BOOL bFlashIcon;                         /* 201933客户专用，是否在报警时闪烁图标 */
    CFG_PDS_LED_CONFIG_S astLedConfig[3];       /* ada32p灯的参数设置, 0、1、2分别为红、黄、绿灯 */
    float fVLproportion;                        /* ada32ir可见光Y轴比例值 */
    float fIRproportion;                        /* ada32ir红外Y周比例值 */
    sint32 s32VLheight;                         /* ada32ir可见光高度设置 */
    sint32 s32IRheight;                         /* ada32ir红外高度值设置 */
    sint32 astPdSign[6];                        /* ada32六个通用标识 */
    sint32 s32AlarmIconSize;                    /* ada32/202851客户专用缩放功能 */
    sint32 s32pdDetectTimeLimit;                /* ada32/202585客户专用标志3检测延时 */
} CFG_PDS_PARAM;

/* 行人统计算法配置(APC) */
typedef struct tagCfgApcParam_S
{
    SV_BOOL             bOverLoadAlarm;         /* 是否开启超载报警 */
    sint32              s32LoadNum;             /* 车辆核载人数 */
    SV_BOOL             bCrowdedAlarm;          /* 是否开启拥挤报警 */
    sint32              s32CrowdedNum;          /* 拥挤报警人数 */
    CFG_APC_DIRECTION_E enApcDirection;        /* 行人统计时行人进入的方向 */
    SV_POINT2_S stApcDivider;                  /* 分割线:水平方向使用Y坐标,竖直方向使用X坐标 */
    SV_POINT2_S astApcDetectionPoints[2];      /* 行人检测算法区域 */

    CFG_PDROI_GUI_E     enRoiGui;               /* APC GUI风格 */
    APC_UI              enApcUi;                /* APC UI风格选择 */
    SV_BOOL             bApcUiTotalEnable;      /* 车内总人数图标使能 */
    SV_BOOL             bApcUiRealEnable;       /* 检测区域总人数图标使能 */
    SV_BOOL             bApcUiInEnable;         /* 上车人数图标使能 */
    SV_BOOL             bApcUiOutEnable;        /* 下车人数图标使能 */
} CFG_APC_PARAM;

/* 变焦摄像头算法配置 */
typedef struct
{
    SV_BOOL             bAutoZoom;              /* 是否自动对焦跟踪 */
    sint32              s32Sensitivity;         /* 算法灵敏度[0~2](0:低, 1:中, 2:高) */
    sint32              s32AlarmOutInterval;    /* 算法AlarmOut触发间隔(ms)[0, 10000], 0表示不触发, -1表示自动选择触发时长 */
    sint32              s32OsdFontSize;         /* OSD叠加字体大小[0~3](0:不叠加, 1~3叠加字体大小1x~3x) */
    SV_BOOL             bOsd;                   /* OSD叠加开关 */
    SV_BOOL             bTestMode;              /* 测试模式开关 */
    SV_BOOL             abAlarmOutEnable[3];    /* 报警输出开关 0:红色1:黄色2:绿色*/
    SV_BOOL             bAlarmInEnable;         /* 触发输入开关 */
    TRIGGER_TYPE_S      enAlarmOutLv;           /* 触发输出电平*/
    TRIGGER_TYPE_S      enAlarmInLv;            /* 触发输入电平*/
    double              dThreshold;             /* 过滤得分阈值 */
} CFG_ZOOM_PARAM;


/* 第1路算法通道配置 */
typedef struct tagAlgCh1Param_S
{
    SV_BOOL         bAlgEnable;                 /* 算法使能 */
    CHN_ALG_E       enAlgType;                  /* 算法类型(根据类型取以下对应配置) */
    CFG_ADAS_PARAM  stAdasParam;                /* ADAS算法配置 */
    CFG_PDS_PARAM   stPdsParam;                 /* 行人检测算法 */
#if (!defined(BOARD_ADA32IR))
    CFG_ZOOM_PARAM  stZoomParam;                /* 变焦跟踪算法 */
#endif
} CFG_ALG_CH1;

/* 第2路算法通道配置 */
typedef struct tagAlgCh2Param_S
{
    SV_BOOL         bAlgEnable;                 /* 算法使能 */
    CHN_ALG_E       enAlgType;                  /* 算法类型(根据类型取以下对应配置) */
    CFG_DMS_PARAM   stDmsParam;                 /* DMS算法配置 */
    CFG_PDS_PARAM   stPdsParam;                 /* 行人检测算法 */
    CFG_APC_PARAM   stApcParam;                 /* 行人统计算法 */
} CFG_ALG_CH2;

/* 第3路算法通道配置 */
typedef struct tagAlgCh3Param_S
{
    SV_BOOL         bAlgEnable;                 /* 算法使能 */
    CHN_ALG_E       enAlgType;                  /* 算法类型(根据类型取以下对应配置) */
    CFG_PDS_PARAM   stPdsParam;                 /* 行人检测算法 */
} CFG_ALG_CH3;

/* 算法配置参数 */
typedef struct tagCfgAlgParam_S
{
    SV_BOOL     bAlgEnable;             /* 使能算法 */
    SV_BOOL     bImageMirror;           /* 是否开启镜像 */
    TRIGGER_TYPE_S  enAlgTrigger;       /* 算法触发方式 */
    sint32      s32AlgType;             /* 算法类型 */
	sint32		s32EventID;				/* a47 客户使用 */
    sint32      s32AudioVolume;         /* 算法报警的声音音量 0~5 */
    char        szDeviceID[32];			/* 设备ID,外都传递给CMS */
    sint32  s32PdsAlarmerVolume;        /* PDS算法报警的蜂鸣器报警器音量 0~10 */
    sint32  s32PdsLedBrightness;        /* PDS算法报警的PWM灯板警器音量 0~10 */
    sint32  s32PdsAlarmerFreq;          /* PDS算法报警的频次模式 [0~3]          0常亮*/

    CFG_ALG_CH1 stAlgCh1;               /* 第1路算法通道配置 */
    CFG_ALG_CH2 stAlgCh2;               /* 第2路算法通道配置 */
    CFG_ALG_CH3 stAlgCh3;               /* 第3路算法通道配置 */
    SV_BOOL     bYuvProtocol;           /* 使能YUV协议 */
} CFG_ALG_PARAM;

/* 服务器配置参数 */
typedef struct tagCfgSerParam_S
{
    SV_BOOL             bServerEnable;          /* 是否使能服务器 */
    LOG_TYPE_E          eLogType;               /* 日志类型 */
    char                *pszServerAddr;         /* 服务器地址 */
    uint32              u32ServerPort;          /* 服务器端口 */
    uint64              u64DeviceId;            /* 登录服务器设备ID */
    char                *pszUploadFileType;     /* cms上传文件类型 */
    UPLOAD_FILE_OPTS_E  enUploadFileOpts;       /* cms上传文件类型 */
    NETWORK_TYPE_E      enNetType;              /* cms上线网络类型 */
    sint32              s32RecordLevel;         /* 日志记录级别 */
    sint32              s32UploadLevel;         /* 日志上传服务器级别 */
    char                *pszLogSerAddr;         /* 日志服务器地址 */
    char                *pszDevIpAddr;		    /* 设备IP地址 */
	SV_BOOL     		bGB28181Enable;			/* 是否启用协议 */
	char 				*pszSIPDOMAIN;				/* SIP服务器域 */
	char 				*pszSIPServerId;			/* SIP服务器ID  */
	char 				*pszSIPServerIp;			/* SIP服务器ip */
	uint32  			u32SIPServerPort;		/* SIP服务器端口 */
	char 				*pszSIPPwd;					/* 密码 */
	char 				*pszDevSIPId;				/* 设备SIP ID */
	uint32  			u32DevSIPPort;			/* 设备SIP  端口 */
	uint32  			u32KaPeriod;				/* 心跳周期 */
	uint32  			u32RegInterval;			/* 设备注册间隔 */
	uint32  			u32RegPeriod;			/* 注册有效期--配置到SIP服务器 */
	uint32  			u32KaTimeoutCount;		/* 心跳超时次数--配置到SIP服务器 */
} CFG_SER_PARAM;

/* 用户配置参数 */
typedef struct tagCfgUserParam_S
{
    char       (*pszUserPassword)[3][3][CONFIG_MAX_BUF_SIZE];   /* admin管理员密码 */
} CFG_USR_PARAM;

/* 系统配置参数 */
typedef struct tagCfgSysParam_S
{
    SV_BOOL             bWebUiFull;             /* WebUi 是否是完全显示 */
    SV_BOOL             bPlayback;              /* 是否开启回播模式 */
    sint32              s32TimeoutSleep;        /* 待机超时休眠时间(s) [-1~1800],-1:表示永不休眠 */
	sint32              s32TimeoutRestart;      /* wifi模块超时重启 */
	SV_BOOL 	        bEnable4g;				/* 是否使能4g */
    SV_BOOL             bEnableStorage;         /* 是否使能存储 */
    SV_BOOL             bEnableSDAlarm;         /* SD卡丢失音频报警 */
    SV_BOOL             bEnableSDLedAlarm;      /* SD卡丢失LED闪烁报警 */
    REC_ALARM_E         enAlarmRecord;          /* 警报录像方式 */
    REC_NORMAL_E        enNormalRecord;         /* 普通录像方式 */
    SV_BOOL             bLoopOverwrite;         /* 是否循环覆盖录像 */
    uint32              u32PreRecDuration;      /* 报警录像预录时长(s) */
    uint32              u32PostRecDuration;     /* 报警录像持续时长(s) */
    uint32              u32RecFileLen;          /* 录像文件时长(min) */
    SV_BOOL             bEnableGPStime;         /* 是否是同GPS时间*/
    sint32              s32UTChour;             /* UTC时 -12~14*/
    sint32              s32UTCminute;           /* UTC分 0~59*/
    SV_BOOL             bNtpEnable;             /* 是否使能NTP时间同步 */
    char                *pszNtpServer;          /* NTP服务器地址 */
    uint32              u32NtpInterval;         /* NTP同步时间间隔(min) */
	SV_BOOL             bDaylightTime;			/* 是否使用夏时制 */
    char                *pszDmmCanid;           /* DMM canid */
    char                *pszFrsCanid;           /* FRS canid*/
    char                *pszHeartCanid;         /* Heart canid */
    char                *pszDmsExtCanid;        /* DMS扩展协议 canid */
    char                *pszPdsCanid;           /* PDS canid */
    char                *pszPdsExtCanid;        /* PDS扩展协议 canid */
    char                *pszApcCanid;           /* APC canid */
    CAN_PROTOCOL_VER_E  enCanProtocolVer;       /* CAN协议版本 */
    char                szRecFileType[12];      /* 录像文件类型 */
    sint32              s32RecChn;              /* 录像通道 */
    CAN_FORMAT_E        enFrameFormat;          /* can 帧格式 */
    sint32              s32Baudrate;            /* can 波特率 100, 125, 250, 500, 800, 1000 */
    LANG_TYPE_E         enLang;                 /* 系统语言 */
    sint32              s32RS485Baudrate;       /* RS485 波特率 */
    SV_BOOL             bEnableRS232;           /* RS232 使能*/
    sint32              s32RS232Baudrate;       /* RS232 波特率 */
	char				*pszDeviceid;			/* 设备ID */
	sint32              s32VirSpeedConfig;      /* 虚拟速度 */
	sint32 				s32AccDealy;			/* acc 延迟时间 */
} CFG_SYS_PARAM;

typedef struct tagCfgIrMediaParam_S
{
    IR_CAM_RES_E    enIrRes;            /* 红外摄像头输出分比率 */
    SV_BOOL         bShutterPress;      /* 手动快门 */
    sint32          s32IrShutterInr;    /* 快门间隔 */
} CFG_IR_MEDIA_PARAM;

typedef struct tagCfgBBParam_S
{
	SV_BOOL bEnable;
	char szSimCard[32];
	char szAuthCode[256];
	SV_BOOL bTcp;				//1:tcp 0:udp
	SV_BOOL bServerFormat;	//1:ip 0:域名
	uint32 u32NetType;//网络类型 0 有线网 1wifi 2-4g网络

	uint32 u32HeartInterval;
	uint32 u32TcpTimeOut;
	uint32 u32TcpRetrans;
	uint32 u32UdpTimeOut;
	uint32 u32UdpRetrans;
	uint32 u32SmsTimeout;
	uint32 u32SmsRetrans;

	char szMainApn[32];
	char szMainUser[32];
	char szMainPsword[32];
	char szMainIp[64];
	char szBackApn[32];
	char szBackUser[32];
	char szBackPsword[32];
	char szBackIp[64];

	uint32 u32TcpPort;
	uint32 u32UdpPort;
	char szIcMainIp[64];
	uint32 u32IcTcpPort;
	uint32 u32IcUdpPort;


	char szIcBackIp[64];
	uint32 u32PosStrategy;
	uint32 u32PosScheme;
	uint32 u32UnloginTimeInterval;
	uint32 u32SleepTimeInterval;
	uint32 u32AlarmTimeInterval;
	uint32 u32DefaultTimeInterval;

	uint32 u32DefaultDistanceInterval;
	uint32 u32UnloginDistanceInterval;
	uint32 u32SleepDistanceInterval;
	uint32 u32AlarmDistanceInterval;

	uint32 u32Angel;
	uint16 u16FenceRadius;

	char szMonitorPlatNum[64];
	char szResetnum[64];
	char szResetFactorynum[64];
	char szSmsnum[64];
	char szAlarmSmsnum[64];
	uint32 u32PhoneAnswerStrategy;
	uint32 u32OnceMaxCalltime;
	uint32 u32MonthMaxCalltime;
	char szMonitorNum[64];
	char szMonitorPrivalegenum[64];

	uint32 u32AlarmShieldsord;
	uint32 u32AlarmSmsSwitch;
	uint32 u32AlarmSnapSwitch;
	uint32 u32AlarmStorageSwitch;
	uint32 u32Keyflag;
	uint32 u32Maxspeed;
	uint32 u32OverSpeedContinueTime;
	uint32 u32ContinueDrivetimeLmt;
	uint32 u32DayTotaltimeLmt;
	uint32 u32MinResttime;
	uint32 u32MaxStoptime;

	uint16 u16OverSpeedDiff;
	uint16 u16TireDriveDiff;
	uint16 u16CollsionAlarmSet;
	uint16 u16RolloverAlarmSet;

	uint32 u32TimingSnapctrl;
	uint32 u32DistanceSnapctrl;

	uint32 u32Quality;
	uint32 u32Brightness;
	uint32 u32Contrast;
	uint32 u32Saturation;
	uint32 u32Chroma;

	uint32 u32Mileage;
	uint16 u16ProvinceId;
	uint16 u16CityId;
	char szPlateNum[32];
	uint8 u8PlateColor;
	uint8 u8GnssMode;
	uint8 u8GnssBaute;
	uint8 u8GnssoutputFrequency;
	uint32 u32GnssSample;
	uint32 u32GnssUpload_set;
	uint32 u32Can1Collecttime;
	uint32 u32Can2Collecttime;
	uint16 u16Can1Uploadtime;
	uint16 u16Can2Uploadtime;
	char szCanid[8];
	uint8 u8GnssUploadmode;


}CFG_BB808_PARAM;


/* 所有配置参数 */
typedef struct tagCfgAllParam_S
{
    CFG_NETWORK_PARAM   stNetwork;  /* 网络参数 */
    CFG_MEDIA_PARAM     stMedia;    /* 媒体参数 */
    CFG_ALG_PARAM       stAlg;      /* 算法参数 */
    CFG_SER_PARAM       stServer;   /* 服务器参数 */
    CFG_USR_PARAM       stUser;     /* 用户参数 */
    CFG_SYS_PARAM       stSystem;   /* 系统参数 */
	CFG_BB808_PARAM		stBB808;	/* 部标808参数*/
} CFG_ALL_PARAM;

/******************************************************************************
 * 函数功能: alg初始化配置模块
 * 输入参数: pszFilePath --- 配置文件路径
             pszFileBak1 --- 备份文件1路径
             pszFileBak2 --- 备份文件2路径
             pszFileDefault --- 默认配置文件路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_Init_ALG(char *pszFilePath, char *pszFileBak1, char *pszFileBak2, char *pszFileDefault);

/******************************************************************************
 * 函数功能: 初始化配置模块
 * 输入参数: enInitModule --- 初始化进程模块
             pszFilePath --- 配置文件路径
             pszFileBak1 --- 备份文件1路径
             pszFileBak2 --- 备份文件2路径
             pszFileDefault --- 默认配置文件路径
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_Init(INIT_MODULE_E enInitModule, char *pszFilePath, char *pszFileBak1, char *pszFileBak2, char *pszFileDefault);

/******************************************************************************
 * 函数功能: 去初始化配置模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_Fini();

/******************************************************************************
 * 函数功能: 重新加载配置文件
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 应用于多进程访问配置文件时数据同步
 *****************************************************************************/
extern sint32 CONFIG_ReloadFile();

/******************************************************************************
 * 函数功能: 获取设备信息
 * 输入参数: 无
 * 输出参数: pstDevInfo --- 设备信息
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetDevInfo(CFG_DEV_INFO *pstDevInfo);

/******************************************************************************
 * 函数功能: 设置网络参数
 * 输入参数: pstNetworkParam --- 网络参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_SetNetworkParam(CFG_NETWORK_PARAM *pstNetworkParam);

/******************************************************************************
 * 函数功能: 获取网络参数
 * 输入参数: 无
 * 输出参数: pstNetworkParam --- 网络参数
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetNetworkParam(CFG_NETWORK_PARAM *pstNetworkParam);

/******************************************************************************
 * 函数功能: 更新媒体预初始化参数
 * 输入参数: pstMediaParam --- 媒体参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_PreloadConfUpdate(CFG_MEDIA_PARAM *pstMediaParam);

/******************************************************************************
 * 函数功能: 设置媒体参数
 * 输入参数: pstMediaParam --- 媒体参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_SetMediaParam(CFG_MEDIA_PARAM *pstMediaParam);

/******************************************************************************
 * 函数功能: 获取媒体参数
 * 输入参数: 无
 * 输出参数: pstMediaParam --- 媒体参数
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetMediaParam(CFG_MEDIA_PARAM *pstMediaParam);

/******************************************************************************
 * 函数功能: 设置算法参数
 * 输入参数: pstAlgParam --- 算法参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_SetAlgParam(CFG_ALG_PARAM *pstAlgParam);

/******************************************************************************
 * 函数功能: 获取算法参数
 * 输入参数: 无
 * 输出参数: pstAlgParam --- 算法参数
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetAlgParam(CFG_ALG_PARAM *pstAlgParam);

/******************************************************************************
 * 函数功能: 设置服务器参数
 * 输入参数: pstSerParam --- 服务器参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_SetServerParam(CFG_SER_PARAM *pstSerParam);

/******************************************************************************
 * 函数功能: 获取服务器参数
 * 输入参数: 无
 * 输出参数: pstSerParam --- 服务器参数
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetServerParam(CFG_SER_PARAM *pstSerParam);

/******************************************************************************
 * 函数功能: 设置用户参数
 * 输入参数: pstUserParam --- 用户参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_SetUserParam(CFG_USR_PARAM *pstUserParam);

/******************************************************************************
 * 函数功能: 获取用户参数
 * 输入参数: 无
 * 输出参数: pstUserParam --- 用户参数
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetUserParam(CFG_USR_PARAM *pstUserParam);

/******************************************************************************
 * 函数功能: 设置系统参数
 * 输入参数: pstUserParam --- 系统参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_SetSystemParam(CFG_SYS_PARAM *pstSysParam);

/******************************************************************************
 * 函数功能: 获取系统参数
 * 输入参数: 无
 * 输出参数: pstUserParam --- 系统参数
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetSystemParam(CFG_SYS_PARAM *pstSysParam);

/******************************************************************************
 * 函数功能: 获取默认配置参数
 * 输入参数: 无
 * 输出参数: pstDefConf --- 默认配置参数
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 CONFIG_GetDefaultParam(CFG_ALL_PARAM *pstDefConf);

/******************************************************************************
 * 函数功能: 使能/禁止flash写保护机制
 * 输入参数: bEnable --- 是否使能
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 在写系统文件前需要调用该接口禁止写保护
           完成写文件之后需要调用该接口使能写保护
 *****************************************************************************/
extern sint32 CONFIG_FlashProtection(SV_BOOL bEnable);

/******************************************************************************
 * 函数功能: 使能/禁止参数保存到flash(系统启动默认为使能)
 * 输入参数: bTrue --- 是否禁止
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 用于在工厂测试模式下不对配置参数进行保存
 *****************************************************************************/
extern sint32 CONFIG_DisableSaving(SV_BOOL bTrue);

/******************************************************************************
 * 函数功能: 使能/禁止参数保存到默认配置文件(系统启动默认为禁止)
 * 输入参数: bTrue --- 是否使能
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
			 SV_FAILURE - 失败
 * 注意	 : 用于在工厂测试模式下不对配置参数进行保存
 *****************************************************************************/
 extern sint32 CONFIG_EnableSaveDefConfig(uint8 u8Param);


extern sint32 CONFIG_GetBB808Param(CFG_BB808_PARAM *pstBB808Param);

/******************************************************************************
 * 函数功能: 使能/禁止参数保存到默认配置文件(系统启动默认为禁止)
 * 输入参数: bTrue --- 是否使能
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
			 SV_FAILURE - 失败
 * 注意	 : 用于在工厂测试模式下不对配置参数进行保存
 *****************************************************************************/
 extern sint32 CONFIG_EnableSaveDefConfig(uint8 u8Param);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _CONFIG_H_ */

