算法模型介绍
RGB_P.rknn                      通用行人检测算法模型
RGB_P_PC.rknn                 通用人车检测算法模型
RGB_P_201266B.rknn       201266客户 检测躺下的人模型
RGB_P_FTC.rknn               FTC 客户 201570客户 张如平罗可客户   行人检测算法模型,规避啤酒桶误检
RGB_MANHOLE.rknn        井盖模型
RGB_SH.rknn                    安全帽检测模型
RGB_SSR.rknn                  201338客户 检测限速标志
TRAFFIC.rknn                   201338客户 交通灯模型
RGB_CQQ_SIGN.rknn       201165客户 检测SpeedZone标志
RGB_PC_202406.rknn       202406客户 用于全景A37 1.45mm 在叉车上用，要求人+车+躺着的人
RGB_PC_111171.rknn       111171客户，刘婷客户，要求检测那种很大的拖车的车头
RGB_LYQ_BEAR.rknn	 201207客户，李颖琪客户，检测熊模型
BGR_PLATE_REC_e1fb6090.rknn ， RGB_YOLOv5n_PLATE__DET_fb68d20e.rknn 车牌检测模型
RGB_PC_specific_version.rknn ，优化雪糕筒误检、柱状物误检问题。暂不同步到 通用版本种，仅给以下客户出专用版本：1. 林佳朗-200001: 灭火器误检；2. 刘佳&杨金丽-201623: 影子和座椅误检；3. 罗可&张如平-201570: FTC油漆桶误检; 4. 刘惠纯-201933: 桶误检; 5. 刘佳-201306: SPILLARD影子误检; 6. 曾梦婷-200531: 影子&雪糕桶误检。