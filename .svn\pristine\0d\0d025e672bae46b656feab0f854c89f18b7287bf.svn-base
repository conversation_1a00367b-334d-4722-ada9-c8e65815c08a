#
# This file is included by all sub components' Makefile
#
#
ifndef BOARD
export BOARD			= 
endif

ifndef DEBUG
export DEBUG			= n
endif

export TOP_DIR			:= $(dir $(word $(words $(MAKEFILE_LIST)) , $(MAKEFILE_LIST)))
export SCRIPTS_DIR		= $(TOP_DIR)/scripts/
export PATH_DEF_MK		= $(SCRIPTS_DIR)/PathDef.mk

########################################
#
# Define wildly used folder/file path
#
########################################
include $(PATH_DEF_MK)

########################################
#
# Define wildly used command
#
########################################
include $(COMMAND_DEF_MK)

########################################
#
# Search for SDK
#
########################################
include $(SDK_PATH_MK)

########################################
#
# Define ARCH/PLATFORM/SDK/CROSS_COMPILE
#
########################################
include $(BOARD_CONFIG_MK)

########################################
#
# Setup CLFAGS
#
########################################
include $(TOOLCHAIN_CFG_MK)

# vim:noet:sw=4:ts=4

