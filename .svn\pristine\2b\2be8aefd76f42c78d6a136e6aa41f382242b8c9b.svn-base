Index: include/config.h
===================================================================
--- include/config.h	(revision 4594)
+++ include/config.h	(working copy)
@@ -406,6 +406,7 @@
     CFG_PDROI_GUI_E enRoiGui;                   /* 行人算法红黄绿区域绘制样式 */
     CFG_PDROI_E enRoiStyle;                     /* 行人算法红黄绿区域标定排布样式 */
     SV_BOOL     enPdHollow;                     /* 行人算法检测区域镂空使能 */
+    SV_POINT2_S astPdHollowPoints[10];          /* 行人算法检测区域镂空点的坐标 */
     SV_POINT2_S astPdCalibrationPoints[12];     /* 行人算法红黄绿区域标定线(两列每列四点) 如下标定图两种排布方式, 坐标取值[0~1]
                                                  *     1.1 --- 2.1      *   1.1   1.2   1.3   1.4   *
                                                  *          绿           *    |     |     |     |    *
Index: include/config_factory.h
===================================================================
--- include/config_factory.h	(revision 4594)
+++ include/config_factory.h	(working copy)
@@ -476,6 +476,44 @@
     return enPdHollow;
 }
 
+static SV_POINT2_S *config_PdHollowPoints()
+{
+    static SV_POINT2_S stPdHollowPoints[10];
+
+    stPdHollowPoints[0].dX = 0.2;
+    stPdHollowPoints[0].dY = 0.6;
+
+    stPdHollowPoints[1].dX = 0.2;
+    stPdHollowPoints[1].dY = 0.7;
+
+    stPdHollowPoints[2].dX = 0.2;
+    stPdHollowPoints[2].dY = 0.8;
+
+    stPdHollowPoints[3].dX = 0.4;
+    stPdHollowPoints[3].dY = 0.8;
+
+    stPdHollowPoints[4].dX = 0.6;
+    stPdHollowPoints[4].dY = 0.8;
+
+    stPdHollowPoints[5].dX = 0.8;
+    stPdHollowPoints[5].dY = 0.8;
+
+    stPdHollowPoints[6].dX = 0.8;
+    stPdHollowPoints[6].dY = 0.7;
+
+    stPdHollowPoints[7].dX = 0.8;
+    stPdHollowPoints[7].dY = 0.6;
+
+    stPdHollowPoints[8].dX = 0.6;
+    stPdHollowPoints[8].dY = 0.6;
+
+    stPdHollowPoints[9].dX = 0.4;
+    stPdHollowPoints[9].dY = 0.6;
+
+    return stPdHollowPoints;
+}
+
+
 /* ROI 红色区域报警间隔 */
 static int config_s32PdRedInterval()
 {
Index: include/media.h
===================================================================
--- include/media.h	(revision 4594)
+++ include/media.h	(working copy)
@@ -619,6 +619,7 @@
     CFG_PDROI_GUI_E enRoiGui;       /* 行人算法红黄绿区域绘制样式 */
     CFG_PDROI_E     enRoiStyle;     /* 行人算法红黄绿区域标定排布样式 */
     SV_BOOL         enPdHollow;     /* 行人算法检测区域镂空使能 */
+    SV_POINT2_S     astPdHollowPoints[10]; /* 行人算法镂空区域坐标 */
     SV_POINT2_S     astPdPoints[12]; /* 行人算法红黄绿标定线 */
     float           fEllipseB[3];   /* 行人算法椭圆B轴长度 */
     PD_ROI_BOARD_S  stPdRoiBoard;   /* 行人算法检测区域(画板参数) */
Index: src/alg/Makefile
===================================================================
--- src/alg/Makefile	(revision 4594)
+++ src/alg/Makefile	(working copy)
@@ -14,8 +14,7 @@
 endif
 ifeq ($(BOARD),$(findstring $(BOARD),ADA32V2,ADA32SDK,ADA32N1,ADA32NSDK,ADA32C4,ADA32E1))
 SRCPPS = $(wildcard ./pd/*.cpp)
-SRCPPS += $(wildcard ./dmm/*.cpp)
-SV_COM_LIBS = -lpds_general_rv1126 -lcalibrate_radar -ljpeg -ldmm -locclusion 
+SV_COM_LIBS = -lpds_general_rv1126 -lcalibrate_radar -ljpeg -locclusion 
 ifeq ($(BOARD), $(findstring $(BOARD),ADA32V2,ADA32N1,ADA32E1))
 CFLAGS += -DUseOpflow
 SV_COM_LIBS += -lopticalflow_rk
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 4594)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -35,8 +35,8 @@
 #include <unistd.h>
 #include <signal.h>
 #include <dirent.h>
+//#include <vector.h>
 
-
 #include "print.h"
 #include "../../../include/common.h"
 #include "safefunc.h"
@@ -70,6 +70,8 @@
 #include "ocdCommon.h"
 #include "pd_sem.h"
 
+using namespace std;
+
 #define min(x, y) (((x) < (y)) ? (x) : (y))
 #define max(x, y) (((x) > (y)) ? (x) : (y))
 #define abs(x) ((x)<0? -(x) : (x))
@@ -2995,6 +2997,14 @@
     CFG_PDS_PARAM *pstPdsParam = NULL;
     sint32 s32Ret;
     PD_ROI_E enOutRoi = enInRoi;
+    pdsa32::STAlgPoint stRectHollow[10];
+    pdsa32::STAlgPoint stRectDest[4];
+    //SV_POINT2_S stRectDest[4];
+    vector<pdsa32::STAlgPoint> poly1;
+    vector<pdsa32::STAlgPoint> poly2;
+    float fTargetArea = 0.0;
+    float fRectArea = 0.0;
+    double dPercent = 0.0;
 
     if (s32Chn >= ALG_MAX_CHN)
     {
@@ -3045,19 +3055,12 @@
     SV_POINT2_S stP33 =  pstPdsParam->astPdCalibrationPoints[10];
     SV_POINT2_S stP34 =  pstPdsParam->astPdCalibrationPoints[11];
 
-
-    SV_POINT2_S stRectHollow[4];
-    SV_POINT2_S stRectDest[4];
     if (NULL == pstRect)
     {
         return PD_ROI_BUTT;
     }
 
-    //stRectHollow[0] = stP31;
-    //stRectHollow[1] = stP32;
-    //stRectHollow[2] = stP33;
-    //stRectHollow[3] = stP34;
-
+#if 0
     xmin = pstPdsParam->astPdCalibrationPoints[8].dX;
     xmax = pstPdsParam->astPdCalibrationPoints[8].dX;;
     ymin = pstPdsParam->astPdCalibrationPoints[8].dY;
@@ -3074,8 +3077,6 @@
     stRectHollow[3].dX = xmax;
     stRectHollow[3].dY = ymax;
 
-
-
     stRectDest[0].dX = pstRect->fX1;
     stRectDest[0].dY = pstRect->fY1;
     stRectDest[1].dX = pstRect->fX1;
@@ -3089,9 +3090,43 @@
     s32Ret = pd_Polygon2Interior(stRectHollow[0].dX,stRectHollow[0].dY,stRectHollow[3].dX,stRectHollow[3].dY,\
                     stRectDest[0].dX,stRectDest[0].dY,stRectDest[2].dX,stRectDest[2].dY,
                     0.75);
+#endif
+    
+    for (sint32 i = 0; i < 10; i++)
+    {
+        stRectHollow[i].fX = (float)pstPdsParam->astPdHollowPoints[i].dX;
+        stRectHollow[i].fY = (float)pstPdsParam->astPdHollowPoints[i].dY;
+        poly1.insert(poly1.end(), stRectHollow[i]);
+    }
 
-    if( SV_TRUE == s32Ret)
+
+    stRectDest[0].fX = pstRect->fX1;
+    stRectDest[0].fY = pstRect->fY1;
+
+    stRectDest[1].fX = pstRect->fX1;
+    stRectDest[1].fY = pstRect->fY2;
+
+    stRectDest[2].fX = pstRect->fX2;
+    stRectDest[2].fY = pstRect->fY2;
+
+    stRectDest[3].fX = pstRect->fX2;
+    stRectDest[3].fY = pstRect->fY1;
+
+    for (sint32 i = 0; i < 4; i++)
     {
+        //printf("stRectDest[%d](%f, %f)\n", i, stRectDest[i].fX, stRectDest[i].fY);
+        poly2.insert(poly2.end(), stRectDest[i]);
+    }
+    
+    fTargetArea = pdsa32::InterArea(poly1, poly2);
+    fRectArea = (pstRect->fX2 - pstRect->fX1) * (pstRect->fY2 - pstRect->fY1);
+
+    dPercent = fTargetArea / fRectArea; 
+
+    printf("fTargetArea=%f, fRectArea=%f, dPercent=%f\n", fTargetArea, fRectArea, dPercent);
+
+    if(dPercent > 0.75)
+    {
         enOutRoi = PD_ROI_BUTT;
     }
 
Index: src/common/config/config.c
===================================================================
--- src/common/config/config.c	(revision 4594)
+++ src/common/config/config.c	(working copy)
@@ -323,6 +323,7 @@
         || 0 == strcmp(pszName, "bOpticalFreeShake")
         || 0 == strcmp(pszName, "pdWorkMode")
         || 0 == strcmp(pszName, "pdHollow")
+        || 0 == strcmp(pszName, "pdHollowEnable")
         || 0 == strcmp(pszName, "bCalibrated")
         || 0 == strcmp(pszName, "pdDetectWBorder")
         || 0 == strcmp(pszName, "pdDetectRedFBorder")
@@ -9120,6 +9121,19 @@
     iTmp = pstAlgParam->stAlgCh2.stPdsParam.stPdSafetyHelmet.bSkipBlueHelmet;
     config_SetElement(pstPdSafetyHelmet, pstTree, "pdSkipBlueHelmet", CFG_DATA_INT, &iTmp);
 
+
+    pstPdHollow = mxmlFindElement(pstAlg, pstTree, "HOLLOW", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstPdHollow)
+    {
+        pstPdHollow = mxmlNewElement(pstAlg, "HOLLOW");
+    }
+
+    iTmp = pstAlgParam->stAlgCh2.stPdsParam.enPdHollow;
+    config_SetElement(pstPdHollow, pstTree, "pdHollowEnable", CFG_DATA_INT, &iTmp);
+    
+    doubleArray_To_String(szTmpStr, (double *)pstAlgParam->stAlgCh2.stPdsParam.astPdHollowPoints, 20);
+    config_SetElement(pstPdHollow, pstTree, "pdHollowPoints", CFG_DATA_TXT, szTmpStr);
+    
 #endif
 
 #if (!(defined(BOARD_ADA42V1) || defined(BOARD_ADA32IR)))
@@ -9341,6 +9355,8 @@
         }
     }
 #endif
+
+#if 0
 #if (!(defined(BOARD_ADA42V1) || defined(BOARD_ADA32IR)))
 		pstPdHollow = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdHollow", NULL, NULL, MXML_DESCEND);
 		if (NULL == pstPdHollow)
@@ -9396,6 +9412,7 @@
 		}
 
 #endif
+#endif
 
     pstQRCalibration = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdQRCalibration", NULL, NULL, MXML_DESCEND);
     if (NULL == pstQRCalibration)
@@ -10499,7 +10516,7 @@
     config_SetElement(pstPdsCfg, pstTree, "pdAlarmOutGreenEnable",  CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.bPdAlarmOutGreen);
     config_SetElement(pstPdsCfg, pstTree, "pdRoiGui",               CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.enRoiGui);
     config_SetElement(pstPdsCfg, pstTree, "roiStyle",               CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.enRoiStyle);
-    config_SetElement(pstPdsCfg, pstTree, "pdHollow",               CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.enPdHollow);
+    //config_SetElement(pstPdsCfg, pstTree, "pdHollow",               CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.enPdHollow);
     config_SetElement(pstPdsCfg, pstTree, "RedWireAlarmOutSwitch",  CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.astTriggerSrc[0].bAlarmOutSwitch);
     config_SetElement(pstPdsCfg, pstTree, "RedWireZoneMask",        CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.astTriggerSrc[0].s32Zonemask);
     config_SetElement(pstPdsCfg, pstTree, "RedWireModelMask",       CFG_DATA_INT, &pstAlgParam->stAlgCh1.stPdsParam.astTriggerSrc[0].s32AlarmTypeMask);
@@ -10998,7 +11015,8 @@
     stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fRedScale = 0.0;
     memcpy(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fGreenPoint, config_PdRoiGreenPoints(), sizeof(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fGreenPoint));
     memcpy(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fYellowPoint, config_PdRoiYellowPoints(), sizeof(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fYellowPoint));
-    memcpy(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fRedPoint, config_PdRoiRedPoints(), sizeof(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fRedPoint));
+    memcpy(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fRedPoint, config_PdRoiRedPoints(), sizeof(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fRedPoint));    
+    memcpy(stAlgParam.stAlgCh2.stPdsParam.astPdHollowPoints, config_PdHollowPoints(), sizeof(stAlgParam.stAlgCh2.stPdsParam.astPdHollowPoints));
 
 #if (defined(BOARD_ADA32IR))
     memcpy(stAlgParam.stAlgCh2.stPdsParam.astPdRedSensitivity, config_astPdRedSensitivityIr(), sizeof(stAlgParam.stAlgCh2.stPdsParam.astPdRedSensitivity));
@@ -12600,6 +12618,30 @@
         }
     }
 
+    pstPdHollow = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "HOLLOW", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstPdHollow)
+    {
+        print_level(SV_INFO, "pstPdHollow FIND\n");
+        pstTmp = mxmlFindElement(pstPdHollow, m_stConfigInfo.pstTree, "pdHollowEnable", NULL, NULL, MXML_DESCEND);
+        if (NULL != pstTmp)
+        {
+            stAlgParam.stAlgCh2.stPdsParam.enPdHollow = mxmlGetInteger(pstTmp);
+            print_level(SV_INFO, "stAlgParam.stAlgCh2.stPdsParam.enPdHollow=%d\n", (int)stAlgParam.stAlgCh2.stPdsParam.enPdHollow);
+        }
+
+        pstTextTmp = mxmlFindElement(pstPdHollow, m_stConfigInfo.pstTree, "pdHollowPoints", NULL, NULL, MXML_DESCEND);
+        if (NULL != pstTextTmp)
+        {
+            pstStrTmp = mxmlGetText(pstTextTmp, NULL);
+            print_level(SV_INFO, "pdHollowPoints pstStrTmp=%s\n", pstStrTmp);
+            s32Ret = String_To_doubleArray((double *)stAlgParam.stAlgCh2.stPdsParam.astPdHollowPoints, pstStrTmp, NULL);
+            if (s32Ret != SV_SUCCESS)
+            {
+                print_level(SV_ERROR, "String_To_doubleArray failed! [err=#%x]\n", s32Ret);
+            }
+        }
+    }
+
 #endif
 
     pstPdCalibrationPoints = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdCalibrationPoints", NULL, NULL, MXML_DESCEND);
@@ -12667,6 +12709,7 @@
     }
 #endif
 
+#if 0
     pstPdHollow = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdHollow", NULL, NULL, MXML_DESCEND);
     if (NULL != pstPdHollow)
     {
@@ -12686,6 +12729,7 @@
 	}
 #endif
 	}
+#endif
 
     pstPdWorkMode = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdWorkMode", NULL, NULL, MXML_DESCEND);
     if (NULL != pstPdWorkMode)
@@ -13186,7 +13230,7 @@
             s32Ret = config_GetElement(pstPdsCfg, pstTree, "pdAlarmOutGreenEnable", CFG_DATA_INT, &stAlgParam.stAlgCh1.stPdsParam.bPdAlarmOutGreen);
             s32Ret = config_GetElement(pstPdsCfg, pstTree, "pdRoiGui",              CFG_DATA_INT, &stAlgParam.stAlgCh1.stPdsParam.enRoiGui);
             s32Ret = config_GetElement(pstPdsCfg, pstTree, "roiStyle",              CFG_DATA_INT, &stAlgParam.stAlgCh1.stPdsParam.enRoiStyle);
-            s32Ret = config_GetElement(pstPdsCfg, pstTree, "pdHollow",              CFG_DATA_INT, &stAlgParam.stAlgCh1.stPdsParam.enPdHollow);
+            //s32Ret = config_GetElement(pstPdsCfg, pstTree, "pdHollow",              CFG_DATA_INT, &stAlgParam.stAlgCh1.stPdsParam.enPdHollow);
 
             s32Ret = config_GetElement(pstPdsCfg, pstTree, "RedWireAlarmOutSwitch", CFG_DATA_INT, &stAlgParam.stAlgCh1.stPdsParam.astTriggerSrc[0].bAlarmOutSwitch);
             s32Ret = config_GetElement(pstPdsCfg, pstTree, "RedWireZoneMask",       CFG_DATA_INT, &stAlgParam.stAlgCh1.stPdsParam.astTriggerSrc[0].s32Zonemask);
Index: src/control/control.c
===================================================================
--- src/control/control.c	(revision 4594)
+++ src/control/control.c	(working copy)
@@ -3344,6 +3344,7 @@
     stResetParam.stExtern[i].stPdParam.enRoiStyle = pstAlgCfg->stAlgCh2.stPdsParam.enRoiStyle;
     stResetParam.stExtern[i].stPdParam.enPdHollow = pstAlgCfg->stAlgCh2.stPdsParam.enPdHollow;
     memcpy(stResetParam.stExtern[i].stPdParam.astPdPoints, pstAlgCfg->stAlgCh2.stPdsParam.astPdCalibrationPoints, sizeof(SV_POINT2_S)*12);
+    memcpy(stResetParam.stExtern[i].stPdParam.astPdHollowPoints, pstAlgCfg->stAlgCh2.stPdsParam.astPdHollowPoints, sizeof(SV_POINT2_S)*10);
 
     stResetParam.stExtern[i].stPdParam.stPdRoiBoard.fGreenScale = pstAlgCfg->stAlgCh2.stPdsParam.stPdRoiBoard.fGreenScale;
     stResetParam.stExtern[i].stPdParam.stPdRoiBoard.fYellowScale = pstAlgCfg->stAlgCh2.stPdsParam.stPdRoiBoard.fYellowScale;
@@ -3537,6 +3538,7 @@
     stValgExtern[i].stPdParam.enRoiStyle = stAlgParam.stAlgCh2.stPdsParam.enRoiStyle;
     stValgExtern[i].stPdParam.enPdHollow = stAlgParam.stAlgCh2.stPdsParam.enPdHollow;
     memcpy(stValgExtern[i].stPdParam.astPdPoints, stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints, sizeof(SV_POINT2_S)*12);
+    memcpy(stValgExtern[i].stPdParam.astPdHollowPoints, stAlgParam.stAlgCh2.stPdsParam.astPdHollowPoints, sizeof(SV_POINT2_S)*10);
 
     stValgExtern[i].stPdParam.stPdRoiBoard.fGreenScale = stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fGreenScale;
     stValgExtern[i].stPdParam.stPdRoiBoard.fYellowScale = stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard.fYellowScale;
@@ -12165,6 +12167,7 @@
         stInitParam.stExtern[0].stPdParam.bPdRoiRed       = stAlgParam.stAlgCh2.stPdsParam.bPdRoiRed;
         stInitParam.stExtern[0].stPdParam.enPdHollow      = stAlgParam.stAlgCh2.stPdsParam.enPdHollow;
         memcpy(stInitParam.stExtern[0].stPdParam.astPdPoints, stAlgParam.stAlgCh2.stPdsParam.astPdCalibrationPoints, sizeof(SV_POINT2_S)*12);
+        memcpy(stInitParam.stExtern[0].stPdParam.astPdHollowPoints, stAlgParam.stAlgCh2.stPdsParam.astPdHollowPoints, sizeof(SV_POINT2_S)*10);
         memcpy(stInitParam.stExtern[0].stPdParam.fEllipseB, stAlgParam.stAlgCh2.stPdsParam.fEllipseB, sizeof(float)*3);
         memcpy(&stInitParam.stExtern[0].stPdParam.stPdRoiBoard, &stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard, sizeof(stAlgParam.stAlgCh2.stPdsParam.stPdRoiBoard));
     }
Index: src/ipserver/http/jsonHandle.cpp
===================================================================
--- src/ipserver/http/jsonHandle.cpp	(revision 4594)
+++ src/ipserver/http/jsonHandle.cpp	(working copy)
@@ -3015,6 +3015,7 @@
 	cJSON *pstIrCfg, *pstIrMediaCfg, *pstIrAlgCfg, *pstIrPdsCfg;
     cJSON *pstZoomCfg, *pstZoomAOEnable;
     cJSON *pstAvtpStreamID,*pstAvtpDestMAC;
+    cJSON *pstPdHollow;
 	
     char *pszTmp = NULL;
     char szConvert[64] = {0};
@@ -3916,6 +3917,17 @@
             return MSG_DEFAULT_FAIL;
         }
 
+        pstPdHollow = cJSON_CreateObject();
+        if (NULL == pstPdHollow)
+        {
+            print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
+            cJSON_Delete(pstJsonConfig);
+            cJSON_Delete(pstIdentification);
+            cJSON_Delete(pstMedia);
+            cJSON_Delete(pstAlg);
+            return MSG_DEFAULT_FAIL;
+        }
+
         pstRedWireAlarmOut = cJSON_CreateObject();
         if (NULL == pstRedWireAlarmOut)
         {
@@ -3928,7 +3940,7 @@
         }
 
         pstYellowWireAlarmOut = cJSON_CreateObject();
-        if (NULL == pstRedWireAlarmOut)
+        if (NULL == pstYellowWireAlarmOut)
         {
             print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
             cJSON_Delete(pstJsonConfig);
@@ -3939,7 +3951,7 @@
         }
 
         pstGreenWireAlarmOut = cJSON_CreateObject();
-        if (NULL == pstRedWireAlarmOut)
+        if (NULL == pstGreenWireAlarmOut)
         {
             print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
             cJSON_Delete(pstJsonConfig);
@@ -4042,7 +4054,6 @@
         cJSON_AddItemToObject(pstPds, "overtakingDetFrmCnt", cJSON_CreateNumber(apstPdsParam[i]->s32OvertakingDetFrmCnt));
         cJSON_AddItemToObject(pstPds, "overtakeRefRate", cJSON_CreateRaw(szOvertakeRefRate));
         cJSON_AddItemToObject(pstPds, "bOpticalFreeShake", cJSON_CreateNumber(apstPdsParam[i]->bOpticalFreeShake));
-        cJSON_AddItemToObject(pstPds, "pdHollow", cJSON_CreateNumber(apstPdsParam[i]->enPdHollow));
         cJSON_AddItemToObject(pstPds, "pdRoiOutline", cJSON_CreateDoubleArray((double*)apstPdsParam[i]->astPdCalibrationPoints, 24));
         cJSON_AddItemToObject(pstPds, "pdRoiEllipseB", cJSON_CreateFloatArray(apstPdsParam[i]->fEllipseB, 3));
         cJSON_AddItemToObject(pstPds, "pdWorkMode", cJSON_CreateNumber(apstPdsParam[i]->enPDWorkMode));
@@ -4052,6 +4063,10 @@
         cJSON_AddItemToObject(pstPds, "pdDetectYellowFBorder", cJSON_CreateNumber(apstPdsParam[i]->s32PdDetectYellowFBorder));
         cJSON_AddItemToObject(pstPds, "pdDetectGreenFBorder", cJSON_CreateNumber(apstPdsParam[i]->s32PdDetectGreenFBorder));
 
+        cJSON_AddItemToObject(pstPds, "pdHollow", pstPdHollow);
+        cJSON_AddItemToObject(pstPdHollow, "enable", cJSON_CreateNumber(apstPdsParam[i]->enPdHollow));
+        cJSON_AddItemToObject(pstPdHollow, "points", cJSON_CreateDoubleArray((double*)apstPdsParam[i]->astPdHollowPoints, 20));
+
         cJSON_AddItemToObject(pstPds, "RedWireAlarmOut", pstRedWireAlarmOut);
         cJSON_AddItemToObject(pstRedWireAlarmOut, "zonemask", cJSON_CreateNumber(apstPdsParam[i]->astTriggerSrc[0].s32Zonemask));
         cJSON_AddItemToObject(pstRedWireAlarmOut, "modelMask", cJSON_CreateNumber(apstPdsParam[i]->astTriggerSrc[0].s32AlarmTypeMask));
@@ -4621,6 +4636,7 @@
     cJSON *pstWorkSpeed;
     cJSON *pstPdRectPerson,*pstPdAlarmLight, *pstPdRoiEnable, *pstAlarmOutEnable, *pstPdRoiGui, *pstAudioSettings, *pstDmsAlarmOutSettings, *pstAdvancedSettings, *pstEthernet, *pstWifi, *pstApMode, *pstStaMode, *pstNtp, *pstProtocol;
     cJSON *pstRedWireAlarmOut, *pstYellowWireAlarmOut, *pstGreenWireAlarmOut;
+    cJSON *pstPdHollow;
 
     cJSON *pstPdWorkMode, *pstPdDetectWBorder, *pstPdDetectRedFBorder, *pstPdDetectYellowFBorder, *pstPdDetectGreenFBorder;
     cJSON *pstChnAlg, *pstAdasConf, *pstDmsConf, *pstPdsConf, *pstApcConf, *pstPds;
@@ -6435,10 +6451,38 @@
                             apstPdsParam[i]->bOpticalFreeShake = pstTmp->valueint;
                         }
 
-                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdHollow");
-                        if (NULL != pstTmp)
+//                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdHollow");
+//                        if (NULL != pstTmp)
+//                        {
+//                            apstPdsParam[i]->enPdHollow = pstTmp->valueint;
+//                        }
+
+                        pstPdHollow = cJSON_GetObjectItemCaseSensitive(pstPds, "pdHollow");
+                        if(NULL != pstPdHollow)
                         {
-                            apstPdsParam[i]->enPdHollow = pstTmp->valueint;
+                            pstTmp = cJSON_GetObjectItemCaseSensitive(pstPdHollow, "enable");
+                            if (NULL != pstTmp)
+                            {
+                                apstPdsParam[i]->enPdHollow = pstTmp->valueint;
+                            }
+
+                            pstArray = cJSON_GetObjectItemCaseSensitive(pstPdHollow, "points");
+                            if (NULL != pstArray && cJSON_IsArray(pstArray) && cJSON_GetArraySize(pstArray) >= 20)
+                            {
+                                for (j = 0; j < 20; j+=2)
+                                {
+                                    pstTmp = cJSON_GetArrayItem(pstArray, j);
+                                    if (NULL != pstTmp)
+                                    {
+                                        apstPdsParam[i]->astPdHollowPoints[j/2].dX = pstTmp->valuedouble;
+                                    }
+                                    pstTmp = cJSON_GetArrayItem(pstArray, j+1);
+                                    if (NULL != pstTmp)
+                                    {
+                                        apstPdsParam[i]->astPdHollowPoints[j/2].dY = pstTmp->valuedouble;
+                                    }
+                                }
+                            }
                         }
 
                         pstArray = cJSON_GetObjectItemCaseSensitive(pstPds, "pdRoiOutline");
Index: src/media/rockchip/rv1126/mpp_vo.c
===================================================================
--- src/media/rockchip/rv1126/mpp_vo.c	(revision 4594)
+++ src/media/rockchip/rv1126/mpp_vo.c	(working copy)
@@ -2654,6 +2654,8 @@
     MPP_GUI_POINT_S p[2];   /* 镂空矩形点 */
     SV_ROT_ANGLE_E enAngle = m_stVoInfo.enRotateAngle;
     float xmin, xmax, ymin, ymax;
+    MPP_GUI_POINT_S stPoints[10];
+    MEDIA_GUI_LINE_S stGuiLine;
 
     xmin = pstAlgExtern->stPdParam.astPdPoints[8].dX;
     xmax = pstAlgExtern->stPdParam.astPdPoints[8].dX;;
@@ -2673,6 +2675,11 @@
     p[1].y = ymax;
 
 
+    for (i = 0; i < 10; i++)
+    {
+        stPoints[i].x = pstAlgExtern->stPdParam.astPdHollowPoints[i].dX;
+        stPoints[i].y = pstAlgExtern->stPdParam.astPdHollowPoints[i].dY;
+    }
 
     /* 位置出现错乱时交换位置 */
     if (p[0].x > p[1].x)
@@ -2687,6 +2694,7 @@
         p[1].y = tmp;
     }
 
+#if 0
     stGuiRect.x1 = p[0].x;
     stGuiRect.y1 = p[0].y;
     stGuiRect.x2 = p[1].x;
@@ -2699,26 +2707,55 @@
     {
         print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
     }
+#else
+    for(sint32 i = 0; i < 10; i++)
+    {
+        if (i < 9)
+        {
+            stGuiLine.x1 = stPoints[i].x;
+            stGuiLine.y1 = stPoints[i].y;
+            stGuiLine.x2 = stPoints[i+1].x;
+            stGuiLine.y2 = stPoints[i+1].y;
+        }
+        else
+        {
+            stGuiLine.x1 = stPoints[9].x;
+            stGuiLine.y1 = stPoints[9].y;
+            stGuiLine.x2 = stPoints[0].x;
+            stGuiLine.y2 = stPoints[0].y;
+        }
+        
+        stGuiLine.color = GUI_COLOR_WHITE;
+        stGuiLine.stick = 4;
 
+        u16mask = MEDIA_GUI_GET_MASK(s32Chn, 1, MEDIA_GUI_OP_DRAW_LINE);
+        s32Ret = MEDIA_GUI_INSERT(*pstGuiDraw, u16mask, stGuiLine);
+        if (SV_SUCCESS != s32Ret)
+        {
+            print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
+        }
+    }
+#endif
+
     if (m_stVoInfo.bOsdEnable && pstAlgExtern->stPdParam.enRoiGui != CFG_PDROI_GUI_HIDE)
     {
         stTmpColor.u32color = GUI_COLOR_WHITE;
-        s32Ret = MPP_GUI_PAINT_RECT_S(pstGuiImage, RECT_1(p[0],p[1]), RECT_2(p[0],p[1]), stTmpColor, 4 * 2);
+        //s32Ret = MPP_GUI_PAINT_RECT_S(pstGuiImage, RECT_1(p[0],p[1]), RECT_2(p[0],p[1]), stTmpColor, 4 * 2);        
+        MPP_GUI_PAINT_CLOSE_MUTIL_LINE_S(pstGuiImage, stPoints, 9, stTmpColor, 8);
         if (SV_SUCCESS != s32Ret)
         {
-            print_level(SV_ERROR, "MPP_GUI_PAINT_RECT_S failed. [err=%#x]\n", s32Ret);
+            print_level(SV_ERROR, "MPP_GUI_PAINT_CLOSE_MUTIL_LINE_S failed. [err=%#x]\n", s32Ret);
         }
 
         stTmpColor.u32color = GUI_COLOR_NONE;
-        s32Ret = MPP_GUI_FILL_RECT_S(pstGuiImage, RECT_1(p[0],p[1]), RECT_2(p[0],p[1]), stTmpColor);
+        //s32Ret = MPP_GUI_FILL_RECT_S(pstGuiImage, RECT_1(p[0],p[1]), RECT_2(p[0],p[1]), stTmpColor);
+        s32Ret = MPP_GUI_FILL_CLOSE_MUTIL_LINE_S_2(pstGuiImage, stPoints, 9, stTmpColor);
         if (SV_SUCCESS != s32Ret)
         {
-            print_level(SV_ERROR, "MPP_GUI_FILL_RECT_S failed. [err=%#x]\n", s32Ret);
+            print_level(SV_ERROR, "MPP_GUI_FILL_CLOSE_MUTIL_LINE_S_2 failed. [err=%#x]\n", s32Ret);
         }
     }
 
-
-
     return SV_SUCCESS;
 }
 
Index: src/webui/js/webapp-view.js
===================================================================
--- src/webui/js/webapp-view.js	(revision 4603)
+++ src/webui/js/webapp-view.js	(working copy)
@@ -2903,7 +2903,8 @@
             if(this.pdRoiStyle == 5) $(".roi_board_class").show();
             else $(".roi_board_class").hide();
 
-            if(this.customer == "200032" || this.customer == "200889")
+            //if(this.customer == "200032" || this.customer == "200889")
+            if(1)
             {
                 if(this.pdRoiStyle == 5) $("#hollow_div").hide();
                 else $("#hollow_div").show();
@@ -2979,7 +2980,8 @@
             /* 自由拖拽框 */
             $("#switch-freemode-box").show();
             /* 镂空ROI区域 */
-            if(this.customer == "200032" || this.customer == "200889")
+            //if(this.customer == "200032" || this.customer == "200889")
+		    if(1)
             {
                 $("#hollow_div").show();
             }
@@ -3123,7 +3125,8 @@
         if(this.pdRoiStyle == 5) $(".roi_board_class").show();
         else $(".roi_board_class").hide();
         
-        if(this.customer == "200032" || this.customer == "200889")
+        //if(this.customer == "200032" || this.customer == "200889")
+	    if(1)
         {
             if(this.pdRoiStyle == 5) $("#hollow_div").hide();
             else $("#hollow_div").show();
@@ -3425,7 +3428,8 @@
             $("#calibration_start_div").hide();
             $("#switch-freemode-box").hide();
             $(".roi_board_class").hide();
-            if(this.customer == "200032" || this.customer == "200889") 
+            //if(this.customer == "200032" || this.customer == "200889") 
+		    if(1)
             {
                 $("#hollow_div").hide();
             }
@@ -3484,7 +3488,8 @@
         else{
             $("#calibration_start_div").hide();
             $("#switch-freemode-box").hide();
-            if(this.customer == "200032" || this.customer == "200889") 
+            //if(this.customer == "200032" || this.customer == "200889") 
+		    if(1)
             {
                 $("#hollow_div").hide();
             }
