ECHO="/bin/echo"
PRINTF="/usr/bin/printf"
MAKE="/usr/bin/make"
CROSS_COMPILE_LIST = \
	arm-himix100-linux- \
	arm-himix200-linux- \
	arm-linux-gnueabihf- \
	arm-rockchip830-linux-uclibcgnueabihf-

ifdef CROSS_COMPILE_MODE
CROSS_COMPILE:=$(word $(CROSS_COMPILE_MODE), $(CROSS_COMPILE_LIST))
endif

ifeq ($(CROSS_COMPILE_MODE), 1)
DRM_LIB = -L./build/arm-himix100-linux
BUILD_PATH=./build/arm-himix100-linux/
PLATFOMR=PLATFORM_HISI100
else ifeq ($(CROSS_COMPILE_MODE), 2)
DRM_LIB = -L./build/arm-himix200-linux
BUILD_PATH=./build/arm-himix200-linux/
PLATFOMR=PLATFORM_HISI200
else ifeq ($(CROSS_COMPILE_MODE), 3)
DRM_LIB = -L./build/arm-linux-gnueabihf
BUILD_PATH=./build/arm-linux-gnueabihf/
PLATFOMR=PLATFORM_RV1126
else ifeq ($(CROSS_COMPILE_MODE), 4)
DRM_LIB = -L./build/arm-rockchip830-linux-uclibcgnueabihf
BUILD_PATH=./build/arm-rockchip830-linux-uclibcgnueabihf/
PLATFOMR=PLATFORM_RV1106
else
DRM_LIB = -L./build/sys
endif

CC=$(CROSS_COMPILE)gcc
STRIP=$(CROSS_COMPILE)strip
CFLAGS += -I ./include/
CFLAGS += -I ./include/linux/
CFLAGS += -I ./include/libdrm/
CFLAGS += -I ./include/libdrm/drm/
CFLAGS += -D$(PLATFOMR)
#CFLAGS +=  -O0 -g
SRC_DIR=./src
MAIN_DIR=./main
src=$(wildcard $(SRC_DIR)/*.c)
src+=$(wildcard $(SRC_DIR)/linux/*.c)
src+=$(wildcard $(SRC_DIR)/usbtransfer/*.c)
src_obj = $(patsubst %.c,%.o,$(src))

logodisplay=$(wildcard $(MAIN_DIR)/logodisplay.c)
logocontrol=$(wildcard $(MAIN_DIR)/logocontrol.c)

logodisplay_obj = $(patsubst %.c,%.o,$(logodisplay))
logocontrol_obj = $(patsubst %.c,%.o,$(logocontrol))

target_lib=libiDRM.a
target_display=logodisplay
target_control=logocontrol

SYSTEM_LIB	= -L./$(BUILD_PATH)/ -lpthread -liDRM -ljpeg -ldrm -lpng -lz -lm -ludev -lusb-1.0


#.c.o:
#	$(CC) -c $(CFLAGS) $(CPPFLAGS) -o $@ $< 

menu:
	@python3 ./Makefile.py
lib:$(src_obj)
	$(CROSS_COMPILE)ar -rc $(BUILD_PATH)$(target_lib) $(src_obj)
main:$(src_obj) $(logodisplay_obj) $(logocontrol_obj)
	$(CC) -o $(BUILD_PATH)$(target_display) $(logodisplay_obj) $(SYSTEM_LIB)
	$(CC) -o $(BUILD_PATH)$(target_control) $(logocontrol_obj)
	$(STRIP) $(BUILD_PATH)$(target_display)
	$(STRIP) $(BUILD_PATH)$(target_control)
all: lib main
clean_obj:
	rm -rf $(src_obj) $(logodisplay_obj) $(logocontrol_obj)
clean: clean_obj
	rm -rf  $(BUILD_PATH)$(target_display)
	rm -rf  $(BUILD_PATH)$(target_control)




