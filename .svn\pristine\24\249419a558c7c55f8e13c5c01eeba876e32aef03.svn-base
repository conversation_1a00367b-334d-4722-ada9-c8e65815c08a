#ifndef _ITEST_COM_H
#define _ITEST_COM_H

#include "defines.h"
#include "safefunc.h"
#include "errors.h"
#include <pthread.h>

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */


/* 模块ID定义 */
typedef enum tagCOM_MOD_ID_E
{
    COM_ID_ALL = 0,             /* 泛指所有模块 */
	COM_ID_MSG = 1,             /* 消息队列模块 */
	COM_ID_CONTROL = 2,         /* 主控管理模块 */
	COM_ID_MEDIA = 3,           /* 媒体模块 */
	COM_ID_IPSERVER = 4,        /* IP网络服务器模块 */

    COM_ID_BUTT,
} COM_MOD_ID_E;

#ifndef COM_MOD_ID
#define COM_MOD_ID  COM_ID_ALL
#endif

/* 通用错误码定义 */
#define ERR_INVALID_DEVID     COM_ERRNO(COM_MOD_ID, COM_ERR_INVALID_DEVID)  /* 设备ID 无效 */
#define ERR_INVALID_CHNID     COM_ERRNO(COM_MOD_ID, COM_ERR_INVALID_CHNID)  /* 通道ID 无效 */
#define ERR_ILLEGAL_PARAM     COM_ERRNO(COM_MOD_ID, COM_ERR_ILLEGAL_PARAM)  /* 传入非法参数 */
#define ERR_EXIST             COM_ERRNO(COM_MOD_ID, COM_ERR_EXIST)          /* 资源已存在 */
#define ERR_UNEXIST           COM_ERRNO(COM_MOD_ID, COM_ERR_UNEXIST)        /* 资源未初始化 */
#define ERR_NULL_PTR          COM_ERRNO(COM_MOD_ID, COM_ERR_NULL_PTR)       /* 使用了NULL指针 */
#define ERR_NOT_CONFIG        COM_ERRNO(COM_MOD_ID, COM_ERR_NOT_CONFIG)     /* 未配置属性前初始化系统、设备、通道 */
#define ERR_NOT_SURPPORT      COM_ERRNO(COM_MOD_ID, COM_ERR_NOT_SURPPORT)   /* 操作或类型不支持 */
#define ERR_NOT_PERM          COM_ERRNO(COM_MOD_ID, COM_ERR_NOT_PERM)       /* 操作不允许 */
#define ERR_NOMEM             COM_ERRNO(COM_MOD_ID, COM_ERR_NOMEM)          /* 内存不足 */
#define ERR_NOBUF             COM_ERRNO(COM_MOD_ID, COM_ERR_NOBUF)          /* buffer 不足 */
#define ERR_BUF_EMPTY         COM_ERRNO(COM_MOD_ID, COM_ERR_BUF_EMPTY)      /* buffer 为空  */
#define ERR_BUF_FULL          COM_ERRNO(COM_MOD_ID, COM_ERR_BUF_FULL)       /* buffer 已满 */
#define ERR_SYS_NOTREADY      COM_ERRNO(COM_MOD_ID, COM_ERR_SYS_NOTREADY)   /* 系统忙碌或未初始化 */
#define ERR_BADADDR           COM_ERRNO(COM_MOD_ID, COM_ERR_BADADDR)        /* 地址错误 */
#define ERR_BUSY              COM_ERRNO(COM_MOD_ID, COM_ERR_BUSY)           /* 资源忙碌 */
#define ERR_TIMEOUT           COM_ERRNO(COM_MOD_ID, COM_ERR_TIMEOUT)        /* 执行超时 */
#define ERR_INVALID_DATA      COM_ERRNO(COM_MOD_ID, COM_ERR_INVALID_DATA)   /* 无效数据 */


/* 通用功能宏定义 */

#define sleep_ms(ms)    usleep((ms) * 1000)

extern char *http_common_resource_path;

#if (defined(BOARD_IPCR20S3))
#define HTTP_RESOURCE_PATH  "/root/webui/"              /* http静态脚本路径 */
#else
//#define HTTP_RESOURCE_PATH  "/var/webui/"
#define HTTP_RESOURCE_PATH  http_common_resource_path   /* http静态脚本路径 */
#endif

#define COM_STONKAM         "@stonkam.com"

/* 日志文件信号量KEY值 */
#define LOGFILE_SEM_KEY     0x006c6f67

#if (defined(BOARD_ADA32IR))
#define MEDIA_MAX_CHN   4           /* 最大媒体通道数 */
#else
#define MEDIA_MAX_CHN   1
#endif

#define ALG_MAX_CHN     3           /* 算法最大通道数 */

#define MEDIA_IN_CHN    1           /* 通道的视频输入为1 */

#define MCU_CMD_BIT_MAX   6

/* 时间显示格式 */
typedef enum tagTimeFormat_E
{
    TIME_FMT_YYYYMMDD = 0,          /* YYYY-MM-DD 年月日 */
    TIME_FMT_MMDDYYYY,              /* MM-DD-YYYY 月日年 */
    TIME_FMT_DDMMYYYY,              /* DD-MM-YYYY 日月年 */

    TIME_FMT_BUTT
} TIME_FMT;

/* 视频流编码格式 */
typedef enum tag_VencEncode_E
{
    ENCODE_H264 = 0,
    ENCODE_H265,
    ENCODE_MJPEG,

    ENCODE_BUTT
}ENCODE_E;

/* 分辨率 */
typedef enum tagMediaResolution_E
{
    RES_CIF_N = 0,
    RES_CIF,
    RES_480x272,
    RES_HVGA,
    RES_576x320,
    RES_592x336,
    RES_640x400,
    RES_VGA,
    RES_D1,
    RES_800x448,
    RES_SVGA,
    RES_848x480,
    RES_540P,
    RES_1040x576,
    RES_720P,
    RES_720P_10,    // 720P(16:10)
    RES_960P,
    RES_1408x800,
    RES_1080P,
    RES_2K,
    RES_5MP,       // 500万像素(4:3)
    RES_4K,
    RES_BUTT
} MEDIA_RES;

typedef enum tagMediaCvbsMode_E
{
    CVBS_NTSC = 0,
    CVBS_PAL,

    CVBS_BUTT
} MEDIA_CVBS;

/* 码率控制模式 */
typedef enum tagMediaRcMode_E
{
    RC_MODE_VBR = 0,                /* 可变码率 */
    RC_MODE_CBR,                    /* 恒定码率 */
    RC_MODE_ABR,                    /* 平均码率 */
    RC_MODE_FIXQP,                  /* 恒定图像质量 */
    RC_MODE_BUTT,
} RC_MODE_E;

/* 音频编码类型 */
typedef enum tagAudioEncType_E
{
    AUD_ENC_G711A = 0,              /* G711A协议 */
    AUD_ENC_G711U,                  /* G711U协议 */
    AUD_ENC_ADPCM,					/* ADPCM协议*/
    AUD_ENC_LPCM,					/* LPCM协议*/
    AUD_ENC_G726,					/* G726协议*/
    AUD_ENC_BUTT,
} AUD_ENC_E;

/* 音频采样率 */
typedef enum tag_AudioSampleRate_E
{
    AUD_SR_8K = 0,                  /* 8KHz 采样率 */
    AUD_SR_16K,                     /* 16KHz 采样率 */
    AUD_SR_32K,                     /* 32KHz 采样率 */

    AUD_SR_BUTT,
} AUD_SR_E;

/* WIFI热点认证模式 */
typedef enum tagWifiAuthMode_E
{
    WIFI_AUTH_NONE = 0,             /* 无密码 */
    WIFI_AUTH_WPAPSK,               /* WPAPSK/WPA2PSK模式 */

    WIFI_AUTH_BUTT
} WIFI_AUTH;

/* WIFI热点频段 */
typedef enum tagWifiFrequency_E
{
    WIFI_FREQ_2_4G = 0,             /* 2.4G频段 */
    WIFI_FREQ_5G,                   /* 5G频段 */
    WIFI_FREQ_AUTO,                 /* 自动频段(根据环境场景从2.4G/5G中选择最佳频段) */

    WIFI_FREQ_BUTT
} WIFI_FREQ;

/* MCU类型 */
typedef enum tagMcuType_E
{
    MCU_UNKNOWN = 0,                    /* 未知 */
    MCU_NRF52832,                       /* nRF52832 */
    MCU_MPS430,                         /* MPS430 */
	MCU_AT32F421F8P7,					/* AT32F421F8P7 */

    MCU_BUTT
} MCU_TYPE_E;

typedef enum tagMcuOp_E
{
	MCU_OPS_DEFAULT			= 0x00,
	MCU_OPS_UPDATA          = 0x01,         /* 升级 */
	MCU_OPS_CAN             = 0x02,		    /* USB转CAN操作 */
    MCU_OPS_ZONE_WARN       = 0x03,         /* 报警信息输出 */
    MCU_OPS_CHIP_INFO       = 0x04,         /* 获取芯片信息(增加软硬件信息回复) */
    MCU_OPS_SET_ROI         = 0x06,         /* 设置报警探测区域 */
    MCU_OPS_SET_VERSION     = 0x07,         /* 设置软硬件版本 */
    MCU_OPS_RS232_DATA  	= 0x08,         /* RS232数据 */
    MCU_OPS_IS_SEND_GPS     = 0x09,         /* 是否转发GPS数据 */
    MCU_OPS_ALARM_IN        = 0x0a,         /* 一路触发输入 */
    MCU_OPS_GET_CONFIG      = 0x0b,         /* 获取配置信息 */
    MCU_OPS_SET_CONFIG      = 0x0c,         /* 设置配置信息 */
    MCU_OPS_CAN_RS232       = 0x0d,         /* 发送给MCU, MCU同时转发CAN和RS232数据出去，方向：输出 */
    MCU_OPS_RS485           = 0x0e,         /* RS485数据 */
    MCU_OPS_LOG             = 0x0f,         /* 日志上传 */

	MCU_OPS_BUTT
}MCU_OP_E;


/* 通道算法 */
typedef enum tagChnAlg_E
{
    ALG_OFF = 0,                        /* 未开启算法 */
    ALG_ADAS,                           /* FCW/LDW/SSR 装车头向前看 */
    ALG_DMS,                            /* DMS 装车内看司机 */
    ALG_PDS,                            /* PDS 装盲区检行人 */
    ALG_IR_PDS,                         /* 红外检测人车算法 */
    ALG_TRACK,                          /* 跟踪算法 */
    ALG_APC,                            /* 行人统计算法 */
    ALG_ZOOM,                           /* 变焦算法 */

    ALG_CHN_BUTT
} CHN_ALG_E;

/* 算法类型 */
typedef enum tagAlgType_E
{
    ALG_2PD    = 0,                     /* 两路 PD 算法 */
    ALG_DMS_PD,                         /* 一路 DMS + 一路 PD 算法 */

    ALG_UNKNOWN
} ALG_TYPE_E;

/* CAN协议版本 */
typedef enum tagCanProtocolVer_E
{
    CAN_PROTOCOL_VERSION_0          = 0,    /* CAN协议版本0，对于PD来说，是发三路报警数据，红黄绿人数；对于DMS来说，是三个canid那个旧协议 */
    CAN_PROTOCOL_VERSION_1          = 1,    /* CAN协议版本1，新CAN协议，兼容31和32 */

    CAN_PROTOCOL_VERSION_BUTT
}CAN_PROTOCOL_VER_E;

/* 语言类型 */
typedef enum tagLanguageType_E
{
    LANG_EN = 0,                        /* 英语 */
    LANG_CN,                            /* 中文 */
    LANG_JP,                            /* 汉语 */
    LANG_ES,                            /* 西班牙语 */
    LANG_PT,                            /* 葡萄牙语 */
    LANG_RU,                            /* 俄语 */
    LANG_INC,                           /* 印度语 */
    LANG_TEL,                           /* 泰卢固语 */
    LANG_TUR,                           /* 土耳其语 */
    LANG_DG,                            /* 德语 */
    LANG_ITA,                           /* 意大利语 */
    LANG_FRA,                           /* 法语 */
    LANG_FI,                            /* 芬兰语 */
    LANG_BUTT
} LANG_TYPE_E;

/* DMM 警报等级 */
typedef enum tagAlarmLevel_E
{
    ALARM_LEVEL_HIGH,                   /* 报警高等级，红色OSD叠加 */
    ALARM_LEVEL_LOW,                    /* 报警低等级，绿色OSD叠加 */

    ALARM_LEVEL_BUFF
} ALARM_LEVEL_E;

/* ADAS 警报类型 */
typedef enum tagAlarmType_E
{
    ALARM_NOTHING = 0,                  /* 0  无警报 */
    ALARM_FATIGUE,                      /* 1  疲劳   */
    ALARM_DISTRACTION,                  /* 2  分心 */
    ALARM_NO_DRIVER,                    /* 3  无司机 */
    ALARM_SMOKE,                        /* 4  抽烟 */
    ALARM_PHONE,                        /* 5  打电话 */
    ALARM_YAWN,                         /* 6  打哈欠 */
    ALARM_NO_MASK,                      /* 7  未配带口罩 */
    ALARM_SUNGLASS,                     /* 8  配带太阳眼镜 */
    ALARM_NO_SEATBELT,                  /* 9  未佩戴安全带 */
    ALARM_SHELTER,                      /* 10 摄像头被遮挡 */
    ALARM_FATIGUE_L2,                   /* 11 二级疲劳(creare客户要求) */
    ALARM_DRINK_EAT,                    /* 12 吃喝东西 */
    ALARM_OVERSPEED,                    /* 13 超速 */
    ALARM_NO_HELMET,                    /* 14 无安全帽 */
    ALARM_DDAW_KSS7,                    /* 15 DDAW KSS7级 */
    ALARM_DDAW_KSS8,                    /* 16 DDAW KSS8级 */
    ALARM_DDAW_KSS9,                    /* 17 DDAW KSS9级 */
    ALARM_PD_ROI1,                      /* 18 ROI绿色区域内检测到行人 */
    ALARM_PD_ROI2,                      /* 19 ROI黄色区域内检测到行人 */
    ALARM_PD_ROI3,                      /* 20 ROI红色区域内检测到行人 */

    /* 语音提示信息类型 */
    NOTIFY_WELCOME_LANG = 50,           /* 50 提示欢迎使用，人声版 */
    NOTIFY_WELCOME_OTHER,               /* 51 提示欢迎使用，非人声版 */
    NOTIFY_ALGSTART,                    /* 52 提示算法启动中 */
    NOTIFY_ALGRUNNING,                  /* 53 提示算法运行中 */
    NOTIFY_CALIBRATION_START,           /* 54 提示算法标定中 */
    NOTIFY_CALIBRATION_SUCCESS,         /* 55 提示算法标定成功 */
    NOTIFY_CALIBRATION_TOOLEFT,         /* 56 提示算法标定设备偏左 */
    NOTIFY_CALIBRATION_TOORIGHT,        /* 57 提示算法标定设备偏右 */
    NOTIFY_REGISTER_START,              /* 58 提示算法开始注册人脸 */
    NOTIFY_REGISTRE_SUCCESS,            /* 59 提示算法注册人脸成功 */
    NOTIFY_LOGIN_START,                 /* 60 提示算法开始人脸登陆 */
    NOTIFY_LNGIN_SUCCESS,               /* 61 提示算法人脸登陆成功 */
    NOTIFY_LNGIN_FAILED,                /* 62 提示算法人脸登陆失败 */
    NOTIFY_LNGIN_TIMEOUT,               /* 63 提示算法人脸登录超时 */
    NOTIFY_LNGIN_CHANGE_GUARD,          /* 64 提示算法司机人脸已更换 */
    NOTIFY_GPS_CONNECT,                 /* 65 提示GPS已连接 */
    NOTIFY_GPS_INS_FINISH,              /* 66 提示GPS惯导初始化完成 */
    NOTIFY_AUTO_CALIBRATION_SUCCESS,    /* 67 提示算法自动标定成功 */
    NOTIFY_AUTO_CORRECTION_SUCCESS,     /* 68 提示算法自动标定校准成功 */

    ALARM_DMS_BUFF,                     /* 为方便参数使用数组编写，DMS参数定义的数组大小只到这里 */

    /* APC报警类型 */
    ALARM_APC_ON = 100,                 /* APC 上车报警事件 */
    ALARM_APC_OFF,                      /* APC 下车报警事件 */
    ALARM_APC_OVERLOAD,                 /* APC 超载报警 */
    ALARM_APC_CROWDED,                  /* APC 拥挤报警 */
    ALARM_APC_NONE,                     /* APC 无报警 */

    ALARM_TYPE_BUFF
} ALARM_TYPE_E;

/* ADAS 警报音频类型，注意宏和webui POST的值对应，否则会出错 */
typedef enum tagAlarmAudio_E
{
    ALARM_AUD_VOICE = 0,                /* 人声语音播报(DMS算法根据报警类型播放对应语音) */
    ALARM_AUD_DULU,                     /* "嘟噜" */
    ALARM_AUD_TRAIN,                    /* 火车声 */
    ALARM_AUD_DO,                       /* "嘟" */
    ALARM_AUD_PHONE,                    /* 电话声 */
    ALARM_AUD_DIDU,                     /* "嘀嘟" */
    ALARM_AUD_DING,                     /* "叮" */

    ALARM_AUD_CAUTION,                  /* Caution */
    ALARM_AUD_CHECK_AHEAD,              /* Check Ahead */
    ALARM_AUD_CHECK_BEHIND,             /* Check Behind */
    ALARM_AUD_CHECK_LEFT,               /* Check Left */
    ALARM_AUD_CHECK_RIGHT,              /* Check Right */

    ALARM_AUD_GENERAL,                  /* 通用音频，所有报警都用同一个VOICE音频 */

    ALARM_AUD_A4,                       /* A4, 201623客户专用 */
    ALARM_AUD_A5,                       /* A5, 201623客户专用 */
    ALARM_AUD_A6,                       /* A6, 201623客户专用 */
    ALARM_AUD_A7,                       /* A7, 201623客户专用 */
    ALARM_AUD_WIDE,                     /* WIDE, 201623客户专用 */
    ALARM_AUD_PD_ALERT,                 /* PedestrianAlert, 201623客户专用 */
    ALARM_AUD_WARN_PD,                  /* WarningPedestrian, 201623客户专用 */

    ALARM_AUD_BUTT
} ALARM_AUD_E;

typedef enum tagAlarmInType_S
{
    ALARM_IN_OFF = 0,                   /* 报警输入关闭 */
    ALARM_IN_HIGH_LEVEL,                /* 报警输入高电平触发 */
    ALARM_IN_LOW_LEVEL,                 /* 报警输入低电平触发 */
} ALARM_IN_TYPE_S;

typedef enum tagTriggerType_S
{
    TRIGGER_UP = 0,                     /* 高电平触发 */
    TRIGGER_DOWN,                       /* 低电平触发 */

    TRIGGER_BUTT
} TRIGGER_TYPE_S;

/* 行人算法红黄绿区域标定排布类型 */
typedef enum tagCfgPdRoiStyle_E
{
    CFG_PDROI_BOTTOM = 0,           /* 区域纵向排布,红区在底部 */
    CFG_PDROI_LEFT,                 /* 区域横向排布,红区在左边 */
    CFG_PDROI_RIGHT,                /* 区域横向排布,红区在右边 */
    CFG_PDROI_SEMICIRCLE,           /* 区域半圆排布,红区在内部 */
    CFG_PDROI_ELLIPSE,              /* 区域纵向排布,红区在底部,底部为椭圆 */
    CFG_PDROI_DRAWBOARD,            /* 区域为自定义画板类型 */

    CFG_PDROI_BUTT
} CFG_PDROI_E;

/* 行人算法红黄绿区域标定绘制类型 */
typedef enum tagCfgPdRoiGuiStyle_E
{
    CFG_PDROI_GUI_HIDE = 0,         /* ROI 区域隐藏 */
    CFG_PDROI_GUI_LINE,             /* ROI 区域用线段绘制 */
    CFG_PDROI_GUI_FILL,             /* ROI 区域用填充绘制 */

    CFG_PDROI_GUI_BUTT
} CFG_PDROI_GUI_E;

/* 行人算法工作模式 */
typedef enum tagCfgPdWorkMode_E
{
    CFG_PD_WORK_NORMAL = 0,         /* 普通模式 */
    CFG_PD_WORK_DISTANCE,           /* 测距模式     */

    CFG_PD_WORK_BUTT
} CFG_PD_WORK_MODE_E;

/* 行人检测模型 */
typedef enum tagPdsModel_E
{
    E_PDS_P = 0,                    /* (0)检测行人 */
    E_PDS_PC,                       /* (1)检测人车 */
    E_PDS_OWP,                      /* (2)俯视检人 */
    E_PDS_OWPC,                     /* (3)俯视检人车 */
    E_PDS_IR_PC,                    /* (4)红外光检测人车 */
    E_PDS_MANHOLE,                  /* (5)检测井盖 */
    E_PDS_HEAD,                     /* (6)检测人头 */
    E_PDS_C,                        /* (7)检测车 */
    E_PDS_OWC,                      /* (8)俯视检测车 */
    E_PDS_IR_C,                     /* (9)红外光检测车 */
    E_PDS_NIR_P,                    /* (10)红外IRCut版本检测人 */
    E_PDS_NIR_PC,                   /* (11)红外IRCut版本检测人车 */
    E_PDS_NIR_C,                    /* (12)红外IRCut版本检测车 */
    E_PDS_SH,                       /* (13)检测行人带安全帽 */
    E_PDS_IR_P,                     /* (14)红外光检测人 */
    E_PDS_SZ,                       /* (15)限速区 */
    E_PDS_SSR,                      /* (16)检测限速标志 */
    E_PDS_BEAR,                     /* (17)检测熊 */
    E_PDS_UNISIGN,                  /* (18)通用标志 */
    E_PDS_P_UNISIGN,                /* (19)人、通用标志 */
    E_PDS_C_UNISIGN,                /* (20)车、通用标志 */
    E_PDS_PC_UNISIGN,               /* (21)人车、通用标志 */
    E_PDS_,

    E_PDS_BUTT
} EPdsModel;

/* 行人检测交叠判断 */
typedef enum tagPdsDetectionParts
{
    E_PDS_DETECTION_TOTAL = 0,      /* 检测框与探测区域有交集,判断为相交 */
    E_PDS_DETECTION_BOTTOM,         /* 检测框底边与探测区域有交集,判断为相交 */
} EPdsDetectPart;

/* 行人统计的进入方向 */
typedef enum tagApcDirection_E
{
    CFG_APC_DIRECTION_DOWM = 0,     /* 进入方向向下 */
    CFG_APC_DIRECTION_UP,           /* 进入方向向上 */
    CFG_APC_DIRECTION_LEFT,         /* 进入方向向左 */
    CFG_APC_DIRECTION_RIGHT,        /* 进入方向向右 */

    CFG_APC_DIRECTION_BUTT
} CFG_APC_DIRECTION_E;

/* 行人检测摄像头相机视角 */
typedef enum tagPdViewDirection_E
{
    CFG_PD_VIEW_FORWARD = 0,        /* 前视镜头 */
    CFG_PD_VIEW_BACK,               /* 后视镜头 */
    CFG_PD_VIEW_LEFT,               /* 左视镜头 */
    CFG_PD_VIEW_RIGHT,              /* 右视镜头 */
    CFG_PD_VIEW_OFF,                /* 关闭摄像头视角策略 */

    CFG_PD_VIEW_DIRECTION_BUTT
} CFG_PD_VIEW_DIRECTION_E;


/* DMS人脸登陆模式 */
typedef enum tagDmsLogin_E
{
    E_LOGIN_OFF = 0,                /* 不做登陆检测 */
    E_LOGIN_BOOT,                   /* 开机检测一次登陆 */
    E_LOGIN_AUTO,                   /* 自动登陆(每次从无司机到有司机检测一次) */

    E_LOGIN_BUTT
} EDmsLogin;

/* 存储模块当前执行的命令 */
typedef enum tagSInfoCmdType_E
{
    SINFO_CMD_IDLE = 0,             /* 空闲, 无可执行命令 */
    SINFO_CMD_FORMAT_DEVICE,        /* 格式化命令 */
    SINFO_CMD_REPAIR_FILESYS,       /* 修复文件系统 */
    SINFO_CMD_REPAIR_PARTION,       /* 修复分区 */
    SINFO_CMD_CELAN_BACKUPZONE,     /* 清空备份区 */

    SINFO_CMD_BUTT
} SINFO_CMD_E;

/* 存储设备类型 */
typedef enum STORAGE_POS_ENum
{
    STORAGE_MAIN_SD1 = 0,            /* (0)SD卡1 */
    STORAGE_MAIN_SD2,                /* (1)SD卡2 */
    STORAGE_MAIN_SD3,                /* (2)SD卡3 */
    STORAGE_INNER_EMMC,              /* (3)内部EMMC存储器 */
    STORAGE_EXTRA_SD,                /* (4)U盘 额外存储器 */
    STORAGE_MEMORY,                  /* (5)内存目录 */
    STORAGE_MAIN_ALL,                /* (6)泛指所有主存储器 */
    STORAGE_FIRSTSTREAM,             /* (7)主数据流写入区 */
    STORAGE_SECONDSTREAM,            /* (8)第二数据流写入区 */
    STORAGE_ALL,                     /* (9)泛指所有存储器 （STORAGE_MAIN_ALL STORAGE_EXTRA_SD），不包括内存目录 */
    STORAGE_NONE,                    /* (10)无效存储器 */
    STORAGE_UNKNOWN,
} STORAGE_POS_E;

/* 媒体数据存储的文件系统类型 */
typedef enum tagSInfoMediaFsType_E
{
    SINFO_FS_UNKNOWN = 0,            /* 未知类型 */
    SINFO_FS_FAT32,                  /* FAT32类型 */
    SINFO_FS_EXT3,                   /* EXT3类型 */
    SINFO_FS_EXT4,                   /* EXT4类型 */

    SINFO_FS_BUTT
} SINFO_FS_E;

/* 存储设备分区情况 */
typedef enum tagSInfoPartition_E
{
    SINFO_PAR_ALL = 0,
    SINFO_PAR_FAT32 = 1,
    SINFO_PAR_MEDIA = 2,
    SINFO_PAR_BACKUP = 3,

    SINFO_PAR_BUTT
} SINFO_PARTITION_E;

typedef struct tagStoragePath_S
{
    STORAGE_POS_E eStoragePos;
	char szStoragePath[32];
} STORAGE_PATH_S;

/* 日志类型 */
typedef enum tagLogType_E
{
    LOG_TYPE_NORMAL         = 0,
    LOG_TYPE_UPDATE         = 1,
    LOG_TYPE_CAN_UPGRADE    = 2,

    LOG_TYPE_BUTT
} LOG_TYPE_E;

/* CMS上传文件选项 */
typedef enum tagUploadFileOpts_E
{
    UPLOAD_FILE_OFF                 = 0,
    UPLOAD_FILE_ALARM_VIDEO         = 1,
    UPLOAD_FILE_ALARM_PICTURE       = 2,
    UPLOAD_FILE_ALARM_ALL           = 3,
    UPLOAD_FILE_NORMAL_VIDEO        = 4,

    UPLOAD_FILE_OPTS_BUTT
} UPLOAD_FILE_OPTS_E;

/* CMS上线网络类型 */
typedef enum tagNetworkType_E
{
    NETWORK_TYPE_LAN        = 0,
    NETWORK_TYPE_WIFI       = 1,
    NETWORK_TYPE_3G         = 2,
    NETWORK_TYPE_4G         = 3,

    NETWORK_TYPE_BUTT
} NETWORK_TYPE_E;

/* 网络状态 */
typedef struct tagNetworkStat_S
{
    NETWORK_TYPE_E  enNetworkType;  /* 网络类型 */
    SV_BOOL         bExist;         /* 网络是否存在 */
}NETWORK_STAT_S;

/* 警报录像选项 */
typedef enum tagRecAlarm_E
{
    E_REC_OFF = 0,                  /* 不录任何文件 */
    E_REC_VIDEO,                    /* 只录视频文件 */
    E_REC_PIC,                      /* 只录图片文件 */
    E_REC_VIDEO_PIC,                /* 录视频和图片文件 */

    E_REC_BUTT
} REC_ALARM_E;

/* 普通录像选项 */
typedef enum tagRecNoraml_E
{
    E_REC_NOR_OFF = 0,              /* 不录任何文件 */
    E_REC_NOR_CON,                  /* 普通连续录像 */
    E_REC_NOR_DIS,                  /* 普通截断录像 */

    E_REC_NOR_BUTT
} REC_NORMAL_E;

/* CAN 帧格式 */
typedef enum tagCanFormat_E
{
    E_CAN_EXTENDED = 0,              /* CAN 扩展帧 */
    E_CAN_STANDARD,                  /* CAN 标准帧 */

    E_CAN_BUTT
} CAN_FORMAT_E;

/* 旋转角度选项 */
typedef enum tagRotationAngle_E
{
    SV_ROTATION_0 = 0,                 /* 默认不旋转 */
    SV_ROTATION_90,                    /* 旋转90° */
    SV_ROTATION_180,                   /* 旋转180° */
    SV_ROTATION_270,                   /* 旋转270° */

    SV_ROTATION_BUTT
} SV_ROT_ANGLE_E;

/* 红外输入尺寸分辨率 */
typedef enum tagIRCamResolution_E
{
    IR_RES_256X192 = 0,                /* 256x192 */
    IR_RES_384x288,                    /* 384x288 */
    IR_RES_BUTT
} IR_CAM_RES_E;

/* APC显示风格 */
typedef enum tagApcUiStyle
{
    APC_UI_ICON = 0,                    /* 图标 */
    APC_UI_TEXT,                        /* 文字 */

    APC_UI_BUTT
} APC_UI;

/* 通用操作模式 */
typedef enum tagOpMode_E
{
    OPMODE_AUTO   = 0,				/* 自动 */
    OPMODE_MANUAL = 1,				/* 手动 */
    OPMODE_SEMI_AUTO = 2,			/* 半自动 */
    OPMODE_INVAL
} COMMONG_OPMODE_E;

/* 串口发送PTZ请求类型 */
typedef enum tagSerialReq_E
{
	SERIAL_REQ_STOP = 0,
	SERIAL_REQ_LEFT,
	SERIAL_REQ_RIGHT,
	SERIAL_REQ_UP,
	SERIAL_REQ_DOWN,
	SERIAL_REQ_LEFTUP,              /*向左上*/
	SERIAL_REQ_LEFTDOWN,            /*向左下*/
	SERIAL_REQ_RIGHTUP,             /*向右上*/
	SERIAL_REQ_RIGHTDOWN,           /*向右下*/

	SERIAL_REQ_ZOOM_WIDE = 9,
	SERIAL_REQ_ZOOM_TELE,
	SERIAL_REQ_FOCUS_FAR,
	SERIAL_REQ_FOCUS_NEAR,
	SERIAL_REQ_FOCUS_STOP,
	SERIAL_REQ_IRIS_OPEN,           //光圈加
	SERIAL_REQ_IRIS_CLOSE,          //光圈减
	SERIAL_REQ_PRESET_SET,
	SERIAL_REQ_PRESET_CLEAR,
	SERIAL_REQ_PRESET_GOTO,
	SERIAL_REQ_HOME_GOTO,
	SERIAL_REQ_HOME_SET = 20 ,
	SERIAL_REQ_ONEKEYFOCUS,         //一键聚集
	SERIAL_REQ_INITLENS,            //初始化镜头
	SERIAL_REQ_LENS_CORRECTION,     //镜头校正
	SERIAL_REQ_ZOOM_STOP,
	SERIAL_REQ_TOUR_START,          //开始巡航
	SERIAL_REQ_TOUR_STOP,           //停止巡航
	SERIAL_REQ_PT,                  //PT位置值
	SERIAL_REQ_END
} 
ESerialReq;


#define DMM_MAX_FR_NUM              400     /* 最大允许注册的人脸识别用户数量 */
#define DMM_MAX_NAMELEN             32      /* 用户名最大长度 */
#define DMM_MAX_DIRLEN              32      /* 用户目录名最大长度 */
#define DMM_TEXT_BITMAP_NUM         112     /* DMM 报警文本位图数量 */
#define DMM_ICON_BITMAP_NUM         14      /* DMM 报警图标位图数量 */
#define ADA_PERSON_BITMAP_NUM	    4		/* ADA32 行人位图数量 */
#define ADA_CAR_BITMAP_NUM	        7		/* ADA32 汽车位图数量 */
#define ADA_MANHOLE_BITMAP_NUM	    6		/* ADA32 井盖位图数量 */
#define ADA_BEAR_BITMAP_NUM         1       /* 熊图标位图数量 */
#define PER_SD_BITMAP_NUM		    4		/* 外设 SD卡位图数量 */
#define PER_GPS_BITMAP_NUM		    4		/* 外设 GPS位图数量 */
#define PER_CELLULAR_BITMAP_NUM     6       /* 外设 4G位图数量 */
#define ROTATING_ICON_BITMAP_NUM	12	    /* 旋转图标数量 */
#define FIRE_BITMAP_NUM             9       /* 火图标数量 */
#define APCCOUNT_BITMAP_NUM         6       /* 人数统计图标数量 */
#define APCARROW_BITMAP_NUM         8       /* 人数统计箭头图标数量 */
#define SPEED_ZONE_BITMAP_NUM       2       /* SPEED ZONE 图标数量 */
#define GENERALICON_BITMAP_NUM      6       /* 通用图标数量 */

#define ALG_MULTI_BUF_NUM           3       /* 算法media buf的数量 */

#define FRS_USERINFOS_DIR          "ID"			                             /* FRS 用户信息目录名 */
#define FRS_USERINFOS_PATH         "/root/"FRS_USERINFOS_DIR                 /* FRS 用户信息存放路径 */
#define DMM_TEXT_BITMAP_PATH       "/root/res/dmm/%s/%d.bmp"                 /* DMM 报警文本位图路径 */
#define DMM_ICON_BITMAP_PATH       "/root/res/dmm/other/%d.bmp"              /* DMM 报警图标位图路径 */
#define ADA_PERSON_BITMAP_PATH	   "/root/res/person/person%d.bmp"           /* ADA 行人位图路径 */
#define ADA_CAR_BITMAP_PATH	       "/root/res/car/car%d.bmp"                 /* ADA 汽车位图路径 */
#define ADA_MANHOLE_BITMAP_PATH	   "/root/res/manhole/manhole%d.bmp"         /* ADA 井盖位图路径 */
#define ADA_BEAR_BITMAP_PATH       "/root/res/bear/bear%d.bmp"               /* ADA 熊位图路径 */
#define PER_SD_BITMAP_PATH 		   "/root/res/sdcard/SD%d.bmp"  	         /* 外设 SD卡位图路径 */
#define PER_GPS_BITMAP_PATH 	   "/root/res/gps/GPS%d.bmp"	 	         /* 外设 GPS位图路径 */
#define PER_CELLULAR_BITMAP_PATH   "/root/res/cellular/cellular%d.bmp"       /* 外设 4G位图路径 */
#define ROTATING_ICON_BITMAP_PATH  "/root/res/rotating_icon/icon%d.bmp"      /* 旋转图标路径 */
#define NO_KEY_BITMAP_PATH         "/root/res/sys/no_key.bmp"                /* 未激活图标路径 */
#define FIRE_BITMAP_PATH           "/root/res/fire/fire%d.bmp"               /* 火图标路径 */
#define APCCOUNT_BITMAP_PATH       "/root/res/apc/count%d.bmp"               /* 人数统计图标路径 */
#define APCARROW_BITMAP_PATH       "/root/res/apc/arrow%d.bmp"               /* 人数统计箭头图标路径 */
#define SPEED_ZONE_BITMAP_PATH     "/root/res/speedzone/%d.bmp"              /* SPEED ZONE 图标路径 */
#define OPTALERT_ICON_BITMAP_PATH  "/root/res/icon/icon.bmp"                 /* Optalert客户图标路径 */
#define GENERALICON_BITMAP_PATH	   "/root/res/generalicon/PIC%d.bmp"         /* 通用图标位图路径 */
#define USERPICTURE_BITMAP_PATH    "/root/res/generalicon/PIC1.bmp"          /* 用户自定义图标路径*/
#define FLASH_ICON_BITMAP_PATH     "/root/res/flash/flash_person_red.bmp"    /* 201933客户红色区域闪烁图标路径*/


#define NETWORK_CHECK_FILE "/var/checkNetwork"
#define DMM_START_FACE_CAP "/var/dmmStartFaceCapture"                       /* 通知算法进行人脸捕获操作的标志文件 */

#define GPS_DUMP_INFO_FILE "/var/info/gps"

/* 自动化测试相关路径 */
#define AUTOTEST_IPSYS		"/tmp/AutoTest/ipsys"
#define AUTOTEST_FACTORY	"/tmp/AutoTest/factory"
#define AUTOTEST_IP 		"/tmp/AutoTest/ip.txt"
#define AUTOTEST_CELLTEST 	"/tmp/AutoTest/CellTest.txt"

/* GPS有名管道路径 */
#define GPS_FIFO_FILE       "/var/gpsFifo"
#define RK_ISP_ZOOM_FIFO    "/var/zoomFifo"

/* STA主机名字 */
#if defined(BOARD_DMS31V2)
#define HOST_NAME   "DMS31"
#elif (defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined (BOARD_ADA32N1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
#define HOST_NAME   "ADA32"
#elif (defined(BOARD_ADA900V1))
#define HOST_NAME   "ADA900"
#elif (defined(BOARD_ADA47V1))
#define HOST_NAME   "ADA47"
#elif (defined(BOARD_HDW845V1))
#define HOST_NAME   "HDW845"
#else
#define HOST_NAME   "WiFi-Camera"
#endif

/* can升级相关定义 */
#define CAN_UPGRADE_PATH  				"/boot/canUpgrade/"     	/* CAN配合其他设备的升级目录 */
#define CAN_UPGRADE_ENDPAC_PATH 		"/boot/canUpgrade/endpac/"  /* CAN配合其他设备升级存放已经传输完的目录 */
#define CAN_UPGRADE_PACKET_INFO_FILE  	"fileinfo"                  /* CAN升级传输过程中的实时信息记录文件 */
#define CAN_UPGRADE_SUCCESS             "update success"            /* CAN升级成功提示 */

/* can时钟频率设置相关定义 */
#define SIOCGCANCLKFREQ	    0x89F1	/* get can clock freq */
#define SIOCSCANCLKFREQ	    0x89F2	/* set can clock freq */

#if defined(BOARD_ADA47V1)
#define CAN_BAUDRATE_LIMIT_LOW  10	        /* 波特率界限，低于或等于10k波特率，设置低时钟频率 */
#define CAN_BAUDRATE_LIMIT_MIDDLE  100	    /* 波特率界限，低于或等于100k波特率，设置中等时钟频率 */
#else
#define CAN_BAUDRATE_LIMIT  100	    /* 波特率界限，低于100k波特率，设置低时钟频率 */
#endif

typedef enum tag_CanClkFreq_E
{
#if defined(BOARD_ADA47V1)
    CAN_CLK_FREQ_LOW = 4000000,         /* CAN 低时钟频率 */
    CAN_CLK_FREQ_MIDDLE = 12000000,     /* CAN 中等时钟频率 */
    CAN_CLK_FREQ_HIGH = 198000000,      /* CAN 高时钟频率 */
#else
    CAN_CLK_FREQ_LOW = 12000000,        /* CAN 低时钟频率 */
    CAN_CLK_FREQ_HIGH = 198000000,      /* CAN 高时钟频率 */
#endif

    CAN_CLK_FREQ_BUTT
}CAN_CLK_FREQ_E;

#define DUMP_INFO_STORAGE               "/var/info/storage"         /* 存储模块dump信息 */
#define DUMP_INFO_RECORDER              "/var/info/recorder"        /* 存储模块dump信息 */
#define DUMP_INFO_CELLULAR              "/var/info/cellular"        /* 4G模块dump信息 */
#define DUMP_INFO_GPS                   "/var/info/gps"             /* gps模块dump信息 */
#define DUMP_INFO_GPIO                  "/var/info/gpio"            /* gpio模块dump信息 */
#define DUMP_INFO_WIFI                  "/var/info/base"            /* wifi模块dump信息 */
#define DUMP_INFO_PD                    "/var/info/pd"              /* pd模块dump信息 */
#define DUMP_INFO_MCU                   "/var/info/mcu"             /* mcu模块dump信息 */
#define DUMP_INFO_CMS                   "/var/info/cmsServer"       /* cms模块dump信息 */
#define DUMP_INFO_DMM                   "/var/info/dmm"             /* dmm模块dump信息 */
#define DUMP_INFO_DEL_USER_LIST         "/var/info/delUserList"     /* dmm模块dump信息 */
#define DUMP_INFO_EXTSCREEN             "/var/info/extscreen"       /* USB转CVBS模块dump信息 */
#define DUMP_INFO_SHAREFIFO             "/var/info/sharefifo"       /* sharefifo dump信息 */
#define DUMP_INFO_ISP                   "/var/info/isp"             /* ISP模块dump信息 */
#define DUMP_INFO_ZOOM                  "/var/info/zoom"            /* sharefifo dump信息 */
#define DUMP_INFO_APC                   "/var/info/apc"             /* APC进程dump信息 */
#define DUMP_INFO_PLUG                  "/var/info/plug"            /* 算法插件线程dump信息 */

#define DUMP_PD_NIGHT_MODE              "/tmp/night_mode"           /* PD摄像头处于夜晚模式 */

/* 共用路径定义 */
#define HTTP_FACEID_TMP_PATH            "/var/FaceId_tmp.tar.gz"            /* 导出配置时生成的临时配置文件路径 */
#define HTTP_FACEID_PATH                "/var/FaceId.tar.gz"                /* 导入的人脸ID包临时路径 */
#define HTTP_CONFIG_STORE_TMP_PATH      "/tmp/config_tmp.tar.gz"            /* 导入配置包临时存放文件路径 */
#define HTTP_CONFIG_STORE_PATH          "/tmp/config.tar.gz"                /* 导入配置包存放文件路径，先把文件写到这里，然后再解压到下面的HTTP_CONFIG_DIR中 */
#define HTTP_CONFIG_DIR                 "/var/config"                       /* 导入导出配置文件目录 */
#define HTTP_CONFIG_TMP_PATH            "/var/config/config.json-tmp"       /* 导出配置时生成的临时配置文件路径 */
#define HTTP_CONFIG_JSON_PATH           "/var/config/config.json"           /* 导入导出配置包的文件路径 */
#define HTTP_TMP_CONFIG_JSON_PATH       "/tmp/config.json"                  /* TF卡或U盘导入的配置包路径 */

#define IMG_PARAM_TMP_PATH              "/var/config/img_param.json-tmp"
#define IMG_PARAM_JSON_PATH             "/var/config/img_param.json"


/* 注册人脸识别的用户信息 */
typedef struct tag_DmmFrUserInfo_S
{
    char    szName[DMM_MAX_NAMELEN];    /* 用户名 */
    char    szDir[DMM_MAX_DIRLEN];      /* 用户信息存储目录名(user000...user399) */
} USER_INFO_S;

/* 注册人脸的用户列表 */
typedef struct tag_DmmFrUserList_S
{
    uint32          u32Num;         /* 用户数目 */
    USER_INFO_S     astUserList[DMM_MAX_FR_NUM];/* 列表 */
} USER_LIST_S;

typedef struct tagControlSnapInfo_S
{

	pthread_mutex_t  mutexSnap;
    sint32          s32ImgSize;     /* snap0大小 */
	sint32 			s32Flag;
    void            *apvImgBuf;     /* snap0 buf */
    SV_BOOL         bWaitRead;      /* 等待HTTP读取 */
}SNAP_INFO_S;

#if defined(BOARD_ADA47V1)
typedef struct
{
    SV_BOOL     bRunning;       /* 线程运行状态 */
    sint32      s32CurPos;      /* 当前位置 */
    sint32      s32Progress;    /* 进度百分比,[0-100] */
    sint32      s32Status;      /* 当前状态 ,0:未定义 1:正在标定 2:标定成功 3:标定失败*/
} RK_ISP_AF_INFO;
#endif


typedef struct tagDetectPoint_S
{
	uint8 x;
	uint8 y;
}DISTANCE_POINT_S;

/* PD 输出信息 */
typedef struct tagPdRoiNum_S
{
    sint32          s32RedRoiNum : 8;           /* 红色ROI区域检测数量 */
    sint32          s32YellowRoiNum : 8;        /* 黄色ROI区域检测数量 */
    sint32          s32GreenRoiNum : 8;         /* 绿色ROI区域检测数量 */
} PD_ROI_NUM_S;

typedef struct tagPdResult_S
{
    sint64          s64TimeStamp;               /* 时间戳 */
    sint32          s32GreenRoiNum;             /* 绿色ROI区域检测数量 */
    sint32          s32YellowRoiNum;            /* 黄色ROI区域检测数量 */
    sint32          s32RedRoiNum;               /* 红色ROI区域检测数量 */
    uint8           bShelter;                   /* 摄像头是否遮挡 */
    uint8           bNightMode;                 /* 摄像头是否处于夜晚模式 */

	sint32          s32PDWorkMode;              /* 工作模式，标定或者是普通模式 */

    sint32          s32GpsSpeed;                /* GPS速度 */

    SV_BOOL         bHasHelmetNum;              /* 是否包含安全帽数据 */
    sint32          s32RedHelmetNum;            /* 红色安全帽数量 */
    sint32          s32YellowHelmetNum;         /* 黄色安全帽数量 */
    sint32          s32WhiteHelmetNum;          /* 白色安全帽数量 */
    sint32          s32BlueHelmetNum;           /* 蓝色安全帽数量 */
    sint32          s32NoHelmetNum;             /* 不带安全帽数量 */

    sint32          s32DistanceNum;             /* 目标数量 */
    sint32          s32DistanceXY[20*2];        /* 检测到的目标的距离坐标(x,y),上限为20个 */

    SV_BOOL         bRedAlarmOut;               /* 红色触发线是否输出 */
    SV_BOOL         bYellowAlarmOut;            /* 黄色触发线是否输出 */
    SV_BOOL         bGreenAlarmOut;             /* 绿色触发线是否输出 */

    sint32          s32RedDelayTime;            /* 红色触发线持续输出时间 */
    sint32          s32YellowDelayTime;         /* 黄色触发线持续输出时间 */
    sint32          s32GreenDelayTime;          /* 绿色触发线持续输出时间 */

}PD_RESULT_S;

/* 算法中向VOSD回传目前所处通道及红黄蓝区域人数 */
typedef struct tagPdRoiNumResult_S
{
    sint32          s32AlgChn;            /* 算法通道号 */
    sint32          s32RedNum;            /* 红色数量 */
    sint32          s32YellowNum;         /* 黄色数量 */
    sint32          s32GreenNum;          /* 绿色数量 */

}PD_ROI_NUMBER_RESULT_S;

typedef enum maxAlarmLevel
{
    ALARM_LEVEL_GREEN = 0,
    ALARM_LEVEL_YELLOW,
    ALARM_LEVEL_RED,
} ALARM_LEVLE_COLOR_ROI;

typedef struct tag_PdRoiData_S
{
    SV_BOOL         bValid;                     /* 数据是否有效 */
    PD_RESULT_S     stPdResultNum;              /* 红黄绿区域人数和时间戳 */
}PD_ROI_DATA_S;

/* 回播控制信息 */
typedef struct tagPlaybackInfo_S
{
    SV_BOOL                 bStart;                         /* 是否开启回播模式 */
    SV_BOOL                 bBootstart;                     /* 是否在ipsys启动时通过传参启动回播模式 */
    SV_BOOL                 bPlaying;                       /* 是否正在回播中 */
    SV_BOOL                 bLoopPlay;                      /* 是否循环播放同一个录像 */
    char                    szPlaybackFile[512];            /* 回播视频路径 */
}PLAYBACK_INFO_S;

/* 功能宏的定义 */
#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA900V1) /*|| defined(BOARD_ADA32N1) */|| defined(BOARD_ADA32C4) || defined(BOARD_ADA32E1))
#define ALG_MUTLIT_BUFFER 1     /* 算法使用多帧轮换开关,未定义则使用单帧轮换 */
#else
#define ALG_MUTLIT_BUFFER 0     /* 算法使用单帧轮换 */
#endif


/* 使能 mpp_ctl 模块 */
#if (defined(BOARD_ADA32N1) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32V3) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32C4) || defined(BOARD_ADA32IR))
#define USING_MPP_CTRL 1
#else
#define USING_MPP_CTRL 0
#endif



/* ShareFifo 降帧优化 */
#if (defined(BOARD_ADA32N1) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32V3) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32C4))
#define USING_SHAREFIFO_OPT 1
#else
#define USING_SHAREFIFO_OPT 0
#endif

#if (defined(BOARD_ADA32N1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32C4))
#define USING_MAINSTREAM_ALPHA 1    /* 主码流使用半透明叠加 */
#else
#define USING_MAINSTREAM_ALPHA 0
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32IR))
#define USING_VOSTREAM_ALPHA   1    /* VO出图使用半透明叠加 */
#else
#define USING_VOSTREAM_ALPHA   0
#endif

#if (defined(BOARD_ADA32V2) || defined(BOARD_DMS31V2) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32V3))
#define USING_VO_DA_CHIP       1    /* 有DA芯片输出AHD信号 */
#else
#define USING_VO_DA_CHIP       0
#endif

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32N1) || defined(BOARD_ADA32E1)|| defined(BOARD_ADA32C4))
#define MPP_AUDIO_AI_ENABLE 1
#else
#define MPP_AUDIO_AI_ENABLE 0
#endif

#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1))
#define MPP_AUDIO_AO_ENABLE 1
#else
#define MPP_AUDIO_AO_ENABLE 0
#endif

/****************************** 使用RTC时间 **************************************/
#if (defined(BOARD_ADA900V1) || defined(BOARD_ADA47V1) || defined(BOARD_ADA32C4) || defined(BOARD_DMS31V2) ||  \
	defined(BOARD_HDW845V1) || defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
#define USING_RTC_TIME 1
#else
#define USING_RTC_TIME 0
#endif
/*********************************************************************************/

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA900V1) || defined(BOARD_ADA32E1) || defined(BOARD_ADA32N1) || defined(BOARD_ADA46V1))
#define USING_MCU 1
#else
#define USING_MCU 0
#endif

#if (defined(BOARD_DMS31V2) || defined(BOARD_ADA32V2) || defined(BOARD_ADA32V3) || defined(BOARD_ADA32IR) || defined(BOARD_ADA32N1))
#define USING_AVALARMER 1
#else
#define USING_AVALARMER 0
#endif

/* 编译宏和文件宏转接: MAKE_XXX 定义在 scripts/BoardConfig.mk 板级配置中 */
/********************************* 媒体模块 **************************************/
/* USB UVC摄像头 */
#ifdef      MAKE_UVC
#define     USING_UVC       1
#else
#define     USING_UVC       0
#endif
/* 变焦摄像头 */
#ifdef      MAKE_ZOOM
#define     USING_ZOOM       1
#else
#define     USING_ZOOM       0
#endif

/********************************* 外设模块 **************************************/
/* STORAGE */
#ifdef      MAKE_STORAGE
#define     USING_STORAGE   1
#else
#define     USING_STORAGE   0
#endif
/* RECORDER */
#ifdef      MAKE_RECORDER
#define     USING_RECORDER  1
#else
#define     USING_RECORDER  0
#endif
/* LED */
#ifdef      MAKE_LED
#define     USING_LED       1
#else
#define     USING_LED       0
#endif
/* ACC */
#ifdef      MAKE_ACC
#define     USING_ACC       1
#else
#define     USING_ACC       0
#endif

/********************************* 网络协议 **************************************/
/* RTSP */
#ifdef      MAKE_RTP_EXTRA_INFO
#define     RTP_EXTRA_INFO  1
#else
#define     RTP_EXTRA_INFO  0
#endif

/*********************************************************************************/

/********************************部标默认配置参数***************************************/
#define BB_DEFAULT_HEART						20			/* 终端心跳发送间隔，单位为秒（s） */
#define BB_DEFAULT_TCP_TIMEOUT					1			/* TCP 消息应答超时时间，单位为秒（s）*/
#define BB_DEFAULT_TCP_RETRANSMISSION 			3			/* TCP 消息重传次数 */
#define BB_DEFAULT_UDP_TIMEOUT					1			/* UDP 消息应答超时时间，单位为秒（s） */
#define BB_DEFAULT_UDP_RETRANSMISSION 			3			/* UDP 消息重传次数 */
#define BB_DEFAULT_SMS_TIMEOUT					1			/* SMS 消息应答超时时间，单位为秒（s） */
#define BB_DEFAULT_SMS_RETRANSMISSION 			3			/* SMS 消息重传次数 */
#define BB_DEFAULT_TCP 							0			/* 通信方式，1:tcp，0:udp */
#define BB_DEFAULT_NETCARD_4G						2			/* 网络类型 0:lan 1:wifi 2:4g */
#define BB_DEFAULT_SERVER_FORMAT 				1			/* SERVER格式，1:IP格式，0:域名格式 */
#define BB_DEFAULT_MAIN_APN						"3g.net"	/* 主服务器APN，无线通信拨号访问点。若网络制式为CDMA，则该处为PPP 拨号号码 */
#define BB_DEFAULT_MAIN_USER					"user"		/* 主服务器无线通信号拨用户名 */
#define BB_DEFAULT_MAIN_PASSWORD				"123456"	/* 主服务器无线通信拨号密码 */
#define BB_DEFAULT_MAIN_IP						"***********"	/* 主服务器地址,IP 或域名*/
#define BB_DEFAULT_BACKUP_APN 					"3g.net"	/* 备份服务器APN，无线通信拨号访问点 */
#define BB_DEFAULT_BACKUP_USER					"user"		/* 备份服务器无线通信拨号用户名 */
#define BB_DEFAULT_BACKUP_PASSWORD				"123456"	/* 备份服务器无线通信拨号密码 */
#define BB_DEFAULT_BACKUP_IP					"0.0.0.0"	/* 备份服务器地址,IP 或域名 */
#define BB_DEFAULT_TCP_PORT						6608		/* 服务器TCP 端口 */
#define BB_DEFAULT_UDP_PORT						8888		/* 服务器UDP 端口 */
#define BB_DEFAULT_ICCARD_MAIN_IP 				"0.0.0.0"	/* 道路运输证IC 卡认证主服务器IP 地址或域名 */
#define BB_DEFAULT_ICCARD_MAIN_TCP_PORT			8889		/* 道路运输证IC 卡认证主服务器TCP 端口 */
#define BB_DEFAULT_ICCARD_MAIN_UDP_PORT			8889		/* 道路运输证IC 卡认证主服务器UDP 端口 */
#define BB_DEFAULT_ICCARD_BACKUP_IP				"0.0.0.0"	/* 道路运输证IC 卡认证备份服务器IP 地址或域名，端口同主服务器 */
#define BB_DEFAULT_POS_STRATEGY					0			/* 位置汇报策略，0：定时汇报；1：定距汇报；2：定时和定距汇报*/
#define BB_DEFAULT_POS_PLAN						0			/* 位置汇报方案，0：根据ACC 状态； 1：根据登录状态和ACC 状态，先判断登录状态，若登录再根据ACC 状态*/
#define BB_DEFAULT_NO_LOGIN_TIME_INTERVAL 		30			/* 驾驶员未登录汇报时间间隔，单位为秒（s），>0 */
#define BB_DEFAULT_SLEEP_INTERVAL 				30			/* 休眠时汇报时间间隔，单位为秒（s），>0 */
#define BB_DEFAULT_EMERGECY_INTERVAL			30			/* 紧急报警时汇报时间间隔，单位为秒（s），>0 */
#define BB_DEFAULT_DEFAULT_INTERVAL				15			/* 缺省时间汇报间隔，单位为秒（s），>0 */
#define BB_DEFAULT_DISTANCE_INTERVAL			1000		/* 缺省距离汇报间隔，单位为米（m），>0 */
#define BB_DEFAULT_NO_LOGIN_DISTANCE_INTERVAL 	100			/* 驾驶员未登录汇报距离间隔，单位为米（m），>0 */
#define BB_DEFAULT_SLEEP_DISTANCE_INTERVAL		30			/* 休眠时汇报距离间隔，单位为米（m），>0 */
#define BB_DEFAULT_EMERGENCY_DISTANCE_INTERVAL	100			/* 紧急报警时汇报距离间隔，单位为米（m），>0 */
#define BB_DEFAULT_INFLECT_ANGEL_POINT			120			/* 拐点补传角度，<180 */
#define BB_DEFAULT_INFLECT_FENCE_RADIUS			50			/* 电子围栏半径（非法位移阈值），单位为米 */
#define BB_DEFAULT_PLATFORM_NUMBER				"888888"	/* 监控平台电话号码 */
#define BB_DEFAULT_REBOOT_NUMBER				"888888"	/* 复位电话号码，可采用此电话号码拨打终端电话让终端复位 */
#define BB_DEFAULT_RESET						"888888"	/* 恢复出厂设置电话号码，可采用此电话号码拨打终端电话让终端恢复出厂设置 */
#define BB_DEFAULT_SMS_PHONENUMBER				"888888"	/* 监控平台SMS 电话号码 */
#define BB_DEFAULT_SMS_ALARM_PHONENUMBER		"888888"	/* 接收终端SMS 文本报警号码 */
#define BB_DEFAULT_SMS_ANSWER_PHONE_STRATEGY	1			/* 终端电话接听策略，0：自动接听；1：ACC ON 时自动接听，OFF 时手动接听 */
#define BB_DEFAULT_ONCE_CALL_TIME 				30			/* 每次最长通话时间，单位为秒（s），0 为不允许通话，0xFFFFFFFF 为不限制 */
#define BB_DEFAULT_MONTHLY_CALL_TIME			(100 *3600)	/* 当月最长通话时间，单位为秒（s），0 为不允许通话，0xFFFFFFFF 为不限制 */
#define BB_DEFAULT_MONITOR_PHONENUMBER			"888888"	/* 监听电话号码 */
#define BB_DEFAULT_PRIVILEGE_PHONENUMER			"888888"	/* 监管平台特权短信号码 */
#define BB_DEFAULT_SHIELD 						0xffff		/* 报警屏蔽字，与位置信息汇报消息中的报警标志相对应，相应位为1则相应报警被屏蔽*/
#define BB_DEFAULT_ALARM_SMS_SWITCH				0xffff		/* 报警发送文本SMS 开关，与位置信息汇报消息中的报警标志相对应，相应位为1 则相应报警时发送文本SMS*/
#define BB_DEFAULT_ALARM_SHOOT_SWITCH 			0xffff		/* 报警拍摄开关，与位置信息汇报消息中的报警标志相对应，相应位为 1 则相应报警时摄像头拍摄*/
#define BB_DEFAULT_ALARM_STORE_FLAG				0xffff		/* 报警拍摄存储标志，与位置信息汇报消息中的报警标志相对应，相应位为1 则对相应报警时拍的照片进行存储，否则实时上传 */
#define BB_DEFAULT_ALARM_KEY_FLAG 				0xffff		/* 关键标志，与位置信息汇报消息中的报警标志相对应，相应位为1 则对相应报警为关键报警*/
#define BB_DEFAULT_MAX_SPEED					80			/* 最高速度，单位为公里每小时（km/h）*/
#define BB_DEFAULT_SPEEDING_DURATION			5			/* 超速持续时间，单位为秒（s） */
#define BB_DEFAULT_CONTINUE_DRIVE_TIME_LIMIT	(4 * 3600)	/* 连续驾驶时间门限，单位为秒（s） */
#define BB_DEFAULT_CUMULATIVE_DRIVE_TIME_LIMIT	(8 * 3600)	/* 当天累计驾驶时间门限，单位为秒（s） */
#define BB_DEFAULT_MIN_REST_TIME				(1200)		/* 最小休息时间，单位为秒（s） */
#define BB_DEFAULT_MAX_STOP_TIME				(1200)		/* 最长停车时间，单位为秒（s） */
#define BB_DEFAULT_OVER_SPEED_DIFF				50			/* 超速报警预警差值，单位为1/10Km/h */
#define BB_DEFAULT_TIRED_DRIVE_DIFF				5			/* 疲劳驾驶预警差值，单位为秒（s），>0*/
#define BB_DEFAULT_COLLSION_ALARM_PARAM			0x5004		/*碰撞报警参数设置：
															b7-b0： 碰撞时间，单位4ms；
															b15-b8：碰撞加速度，单位0.1g，设置范围在：0-79 之间，默认为10。*/
#define BB_DEFAULT_ROLLOVER_ALARM_PARAM			30			/* 侧翻报警参数设置：侧翻角度，单位1 度，默认为30 度。*/
#define BB_DEFAULT_TIMING_SNAP_CTRL				0 			/* 定时拍照控制，见表13 */
#define BB_DEFAULT_DISTANCE_SNAP_CTRL 			0 			/* 定距拍照控制，见表14 */
#define BB_DEFAULT_IMAGE_QUALITY				5			/* 图像/视频质量，1-10，1 最好 */
#define BB_DEFAULT_BRIGHTNESS 					50			/* 亮度，0-255 */
#define BB_DEFAULT_SATURATION 					50			/* 对比度，0-127 */
#define BB_DEFAULT_CONTRAST						50			/* 饱和度，0-127 */
#define BB_DEFAULT_CHROMA 						50			/* 色度，0-255 */
#define BB_DEFAULT_MILEAGE						0			/* 车辆里程表读数，1/10km */
#define BB_DEFAULT_PROVINCE_ID					0			/* 车辆所在的省域ID */
#define BB_DEFAULT_CITY_ID						0			/* 车辆所在的市域ID */
#define BB_DEFAULT_PLATE_NUMBER					"粤A12345"	/* 公安交通管理部门颁发的机动车号牌 */
#define BB_DEFAULT_PLATE_COLOR					0			/* 车牌颜色，按照JT/T415-2006 的5.4.12 */
#define BB_DEFAULT_GNSS_MODE					0x0f		/*GNSS 定位模式，定义如下：
															bit0，0：禁用GPS 定位， 1：启用GPS 定位；
															bit1，0：禁用北斗定位， 1：启用北斗定位；
															bit2，0：禁用GLONASS 定位， 1：启用GLONASS 定位；
															bit3，0：禁用Galileo 定位， 1：启用Galileo 定位。*/
#define BB_DEFAULT_GNSS_BAUDRATE				0x01		/*GNSS 波特率，定义如下：
															0x00：4800；0x01：9600；
															0x02：19200；0x03：38400；
															0x04：57600；0x05：115200。*/
#define BB_DEFAULT_GNSS_OUTPUT_FREQUENCY		0x00		/*GNSS 模块详细定位数据输出频率，定义如下：
															0x00：500ms；0x01：1000ms（默认值）；
															0x02：2000ms；0x03：3000ms；
															0x04：4000ms。*/
#define BB_DEFAULT_GNSS_SAMPLE_FREQUENCY		1			/* GNSS 模块详细定位数据采集频率，单位为秒，默认为1。 */
#define BB_DEFAULT_GNSS_DATA_UPLOAD_MODE		0			/* GNSS 模块详细定位数据上传方式：
															0x00，本地存储，不上传（默认值）；
															0x01，按时间间隔上传；
															0x02，按距离间隔上传；
															0x0B，按累计时间上传，达到传输时间后自动停止上传；
															0x0C，按累计距离上传，达到距离后自动停止上传；
															0x0D，按累计条数上传，达到上传条数后自动停止上传。*/
#define BB_DEFAULT_GNSS_DATA_SETTING			30			/*GNSS 模块详细定位数据上传设置：
															上传方式为0x01 时，单位为秒；
															上传方式为0x02 时，单位为米；
															上传方式为0x0B 时，单位为秒；
															上传方式为0x0C 时，单位为米；
															上传方式为0x0D 时，单位为条。*/

#define BB_DEFAULT_CAN1_COLLECT_MS				0			/* CAN 总线通道1 采集时间间隔(ms)，0 表示不采集 */
#define BB_DEFAULT_CAN1_COLLECT_S 				0			/*CAN 总线通道1 上传时间间隔(s)，0 表示不上传 */
#define BB_DEFAULT_CAN2_COLLECT_MS				0			/* CAN 总线通道1 上传时间间隔(s)，0 表示不上传*/

#define BB_DEFAULT_CAN2_COLLECT_S 				0			/*CAN 总线通道2 上传时间间隔(s)，0 表示不上传*/
#define BB_DEFAULT_CAN_SETTINGS					"0110"		/*CAN 总线ID 单独采集设置：
															bit63-bit32 表示此ID 采集时间间隔(ms)，0 表示不采集；
															bit31 表示CAN 通道号，0：CAN1，1：CAN2；
															bit30 表示帧类型，0：标准帧，1：扩展帧；
															bit29 表示数据采集方式，0：原始数据，1：采集区间的计算值；
															bit28-bit0 表示CAN 总线ID。*/

/**************************************************************************************/
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif // _ITEST_COM_H
