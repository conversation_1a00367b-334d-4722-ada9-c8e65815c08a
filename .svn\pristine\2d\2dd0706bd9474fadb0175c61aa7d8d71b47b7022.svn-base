#ifndef _PELCO_D_H_
#define _PELCO_D_H_

#include <sys/signal.h>
#include <sys/types.h>
#include <string.h>
#include <termios.h>
#include <unistd.h>
#include <errno.h>
#include "defines.h"

#define PELCO_PRINT(data , size, index) {\
            printf("DATA [ ");\
            for( index = 0; index < size; printf("%02X ",data[index++]));\
            printf("]\n");\
        }

#define     PELCOD_DATA_LEN         7                      /* pelco-d协议默认发送数据为7个字节 */
#define     PELCOD_RES_GEN_LEN      4                      /* 通用响应字节长度 */
#define     PELCOD_RES_EXT_LEN      7                      /* 扩展响应字节长度 */
#define     PELCOD_RES_QUE_LEN      18                     /* 查询响应字节长度 */
#define     PELCO_USER_CMD_START    0x40                   /* 设备地址超过这个阈值后均为用户的自定义命令 */

/* pelco-d 命令字 */
typedef struct tagPelcoCmd_S
{
    unsigned char sense:1;             /* sense,1:scan和onoff为自动，0:手动*/
    unsigned char reverse:2;           /* 保留位 */
    unsigned char scan:1;              /* 扫描 */
    unsigned char onOff:1;             /* 相机开关 */
    unsigned char irClose:1;           /* 光圈关闭 */
    unsigned char irOpen:1;            /* 打开光圈 */
    unsigned char focusNear:1;         /* 近焦 */
    unsigned char focusFar:1;          /* 远焦 */
    unsigned char zoomWide:1;          /* 广角,放大 */
    unsigned char zoomTele:1;          /* 近角,缩小 */
    unsigned char down:1;              /* 下移 */
    unsigned char up:1;                /* 上移 */
    unsigned char left:1;              /* 左移 */
    unsigned char right:1;             /* 右移 */
    unsigned char always:1;            /* 总为0 */
} PELCOD_CMD_S;

typedef struct tagPelcoData_S
{
    unsigned char         addr;         /* 设备地址 */
    PELCOD_CMD_S          command;      /* 命令 */
    unsigned char         data1;        /* pan 速度0x00~0x3f  , 0x40最快速度 */
    unsigned char         data2;        /* tilt 速度   0x00~0x3f  , 0x40最快速度*/
    unsigned short        cmdBak;       /* 命令数据两个字节模式 */
} PELCO_D_DATA;


/* 发送数据格式，七个字节 
| sync | addr | cmd1 | cmd2 | data1 | data2 | checksum |
| 0xFF | 0xXX | 0xXX | 0xXX | 0xXX  | 0xXX  | 0xXX     |
*/
typedef enum 
{
    P_SYNC = 0,                         /* 同步字节，固定为0xFF */
    P_ADDR,                             /* 设备地址，默认一个设备0 */
    P_CMD1,                             /* 命令字节1 */
    P_CMD2,                             /* 命令字节2 */
    P_DATA1,                            /* 数据字节1 */
    P_DATA2,                            /* 数据字节2 */
    P_CKSUM                             /* 校验码前面字节的和再对256取余 */
} PELCO_SEND_T;

/* 回包数据格式，协议规定四种响应 
1. 无回包.对于某些命令，不生成任何响应
2. 4 bytes: 这被称为“通用响应”，几乎所有的命令都会生成它。
3. 7 bytes:这被称为“扩展响应”，通常在请求数据时作为响应发送。当某些命令不被理解时，它也会被发送。
4. 18 bytes:这是由“查询”命令及其变体生成的。它最初是软件号，后来成为设备型号。最近，它还提供了该单位的序列号。

响应数据格式,4字节通用响应 checksum和发送的包一样
| sync | addr | alarms | checksum |
| 0xFF | 0xXX | 0x00   | 0xXX     |

响应数据格式 7字节扩展响应  ,checksum单独计算
| sync | addr | resp1 | resp2 | data1 | data2 | checksum |
| 0xFF | 0xXX | 0xXX  | 0xXX  | 0xXX  | 0xXX  | 0xXX     |

响应数据格式 18字节扩展响应  ,checksum单独计算
| sync | addr | data1 ~ data15 | checksum |
| 0xFF | 0xXX | 0xXX 0xXX      | 0xXX     |

*/

/* 用户自定义指令,采用将地址作为命令码
| sync | user cmd | data1H | data1L | data2H | data2L | checksum |
| 0xFF |    0xXX  | 0xXX   | 0xXX   | 0xXX   | 0xXX   | 0xXX     |
user cmd --- 用户命令
data1H 数据1高字节
data1L 数据1低字节
data2H 数据2高字节
data2L 数据2低字节

用户自定义命令 cmd
上位机->设备端
0x40~0x5f  --- 查询指令
    0x40   --- 屏分辨率参数
0x60~0x8f  --- 设置指令
    0x80   --- 选框左上角坐标
    0x81   --- 选框右下角坐标
0x90~0x9f   ---对焦功能配置
    0x90   --- 手动自动对焦一次
    0x91   --- 手动对焦,0x00 对近焦 0x01 对远焦
    0x92   --  数字变倍 + 
    0x93   --  数字变倍 - 
0xa0~0xaf  --- 行人检测算法配置
    0xa0   --- 开关行人检测算法
0xb0~0xbf  --- 跟踪算法配置
    0xb0   --- 开关跟踪算法
0xf0~0xff  --- 设备控制指令
    0xff   --- 重启设备指令
*/

/* 变焦速度 */
typedef enum 
{
    D_ECD_ZOOM_SPEED_SLOW = 0x00,
    D_ECD_ZOOM_SPEED_MEDIUM,
    D_ECD_ZOOM_SPEED_FAST,
    D_ECD_ZOOM_SPEED_FASTEST,
} PELCOD_ZOOM_SPEED;

extern int pelcoWrite(int fd, unsigned char *buf, uint32 size );
extern int pelcoReadBlock(int fd, unsigned char *buf, uint32 size);
extern int pelcoReadNoBlock(int fd, unsigned char *buf, uint32 size);
extern int pelcoPrase(unsigned char *data, uint32 size, PELCO_D_DATA *pstData);
extern int pelcoGeneralRespone (int fd, unsigned char *data, uint32 size, unsigned char errCode);
extern int pelcoAlarmOutRespone (int fd, uint32 u32YellowNum, uint32 u32RedNum);
extern int pelcoControlRequest (int fd, uint32 u32Cmd, uint32 u32ZoomStatus);
extern int pelcoSendToLiquidCrystal (int fd, char *stDataBuf, uint8 u8DataSize);
extern int pelcoSendToSNC700 (int fd, char *stDataBuf, uint8 u8DataSize);
extern int PelcoAlgStatusRespone (int fd, SV_BOOL bBsdStatus, SV_BOOL bTraceStatus);
extern int PelcoYellowAlarmRoiRespone (int fd, char* stYellowAlarmRoiBuf);
extern int PelcoRedAlarmRoiRespone (int fd, char* stRedAlarmRoiBuf);
extern int PelcoMediaFormatRespone (int fd, uint8 u8MeidaResolution, uint8 u8MeidaFormat);
extern int PelcoPictureMirrorFlipRespone (int fd, SV_BOOL bMirror, SV_BOOL bFlip);

#endif /* _PELCO_D_H_ */
