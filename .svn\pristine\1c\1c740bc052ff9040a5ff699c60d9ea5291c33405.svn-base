;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;                                           module state                                               ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;module state;;;;;;;;;;;;;;
[module_state]
bDebug                  = "0"	
bStaticAE               = "1"
bAeWeightTab            = "1"
bStaticWdrExposure      = "0"
bStaticFsWdr            = "0"
bStaticAWB              = "1"
bStaticAWBEx            = "0"
bStaticCCM              = "1"
bStaticSaturation       = "1"
bStaticClut             = "0"
bStaticLdci             = "1"
bStaticDRC              = "1"
bStaticNr               = "1"
bStaticCa               = "1"
bStaticGlobalCac        = "0"
bStaticLocalCac         = "0"
bStaticDPC              = "0"
bStaticDehaze           = "1"
bStaticShading          = "0"
bStaticCSC              = "0"
bStaticCrosstalk        = "0"
bStaticSharpen          = "1"
bStatic3DNR             = "0"
bStaticVenc             = "0"
bStaticPreGamma         = "0"

bDynamicFps             = "0"
bDynamicAE              = "1"
bDynamicWdrExposure     = "0"
bDynamicFSWDR           = "0"
bDynamicBLC             = "1"
bDynamicDehaze          = "1"
bDynamicDrc             = "1"
bDynamicLinearDrc       = "0"
bDynamicGamma           = "1"
bDynamicNr              = "1"
bDynamicDpc             = "0"
bDynamicCA              = "1"
bDynamicShading         = "0"
bDynamicIsoVenc         = "0"
bDynamicLdci            = "0"
bDynamicFalseColor      = "0"
bDyanamic3DNR           = "1"


;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;                                           static parameter                                           ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;static_AE_parameter;;;;;;;;;;;;;;
[static_ae]
AERunInterval 		   = "1"
AERouteExValid       = "1" 
AutoSysGainMax		   = "123000"
AutoSpeed 		       = "32"
AutoTolerance 		   = "2"
AutoBlackDelayFrame  = "8"
AutoWhiteDelayFrame  = "8"

;;;;;;;;;;;;static_AERouteEX_parameter;;;;;;;;;;
[static_aerouteex]
TotalNum                 = "1"
RouteEXIntTime           = " 32,20000,20000,50000, 50000, 50000, 83000, 83000, 83000, 83000"
;RouteSysGain            = " 1024, 1024, 2048, 2048, 65535, 65535, 65535"
RouteEXAGain             = " 1024, 1024, 2048, 2048, 22924, 22924, 22924, 22924, 22924, 22924"
RouteEXDGain             = " 1024, 1024, 1024, 1024,  1024,  2927,  2927, 10240, 10240, 10240"
RouteEXISPDGain          = " 1024, 1024, 1024, 1024,  1024,  1024,  1024,  1024,  1024,  1024"

;;;;;;;;;;;;AeWeightTab;;;;;;;;;;;;
[static_aeweight]
;ExpWeight_0   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
;ExpWeight_1   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
;ExpWeight_2   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
;ExpWeight_3   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
;ExpWeight_4   = 1,1,1,1,1,2,2,2,2,2,2,2,1,1,1,1,1,
;ExpWeight_5   = 1,1,1,1,1,2,2,3,3,3,2,2,1,1,1,1,1,
;ExpWeight_6   = 1,1,1,1,2,2,3,3,3,3,3,2,2,1,1,1,1,
;ExpWeight_7   = 1,1,1,1,2,3,3,3,3,3,3,3,2,1,1,1,1,
;ExpWeight_8   = 1,1,1,1,2,2,3,3,3,3,3,2,2,1,1,1,1,
;ExpWeight_9   = 1,1,1,1,1,2,2,3,3,3,2,2,1,1,1,1,1,
;ExpWeight_10  = 1,1,1,1,1,2,2,2,2,2,2,2,1,1,1,1,1,
;ExpWeight_11  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
;ExpWeight_12  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
;ExpWeight_13  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
;ExpWeight_14  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,


ExpWeight_0   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
ExpWeight_1   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
ExpWeight_2   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
ExpWeight_3   = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
ExpWeight_4   = 1,1,1,1,1,2,2,2,2,2,2,2,1,1,1,1,1,
ExpWeight_5   = 1,1,1,1,1,5,5,7,7,7,2,2,1,1,1,1,1,
ExpWeight_6   = 1,1,1,1,5,5,8,8,8,8,7,2,2,1,1,1,1,
ExpWeight_7   = 1,1,1,1,5,8,8,8,8,8,7,3,2,1,1,1,1,
ExpWeight_8   = 1,1,1,1,5,5,8,8,8,8,7,2,2,1,1,1,1,
ExpWeight_9   = 1,1,1,1,5,5,5,7,7,7,2,2,1,1,1,1,1,
ExpWeight_10  = 1,1,1,1,1,5,5,2,2,2,2,2,1,1,1,1,1,
ExpWeight_11  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
ExpWeight_12  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
ExpWeight_13  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
ExpWeight_14  = 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,

;;;;;;;;;;;;static_WDRExposure_parameter;;;;;;;;;;;                             ;;This parameter only used in WDR mode
[static_wdrexposure]
ExpRatioType             = "0"    
ExpRatioMax              = "2560"                                              
ExpRatioMin              = "1400"
ExpRatio                 = "1024,  64,  64"                                 ;;ExpRatio for manual type
RefRatioUp               = "4800"
RefRatioDn               = "500"
ExpTh                    = "300"

;;;;;;;;;;;;static_FSWDR_parameter;;;;;;;;;;;                             ;;This parameter only used in WDR mode
[static_FsWdr]
WDRMergeMode             = "1"

;;;;;;;;;;;;;;static_AWB_parameter;;;;;;;;;;;;;
[static_awb]	
AutoStaticWb           = "423,256,256,393"
AutoCurvePara          = "26,230,0,163420,128,-118663"
AutoSpeed              = "256"
AutoLowColorTemp       = "2000"
AutoCrMax              = "336, 336, 336, 352, 368, 384, 400, 416, 432, 448, 464, 480, 496, 496, 496, 496"
AutoCrMin              = " 48,  48,  48,  48,  48,  44,  42,  40,  38,  36,  34,  32,  31,  30,  29,  28"
AutoCbMax              = "304, 304, 304, 304, 320, 336, 352, 368, 384, 400, 416, 432, 432, 432, 432, 432"
AutoCbMin              = " 48,  48,  48,  48,  48,  44,  42,  40,  38,  36,  34,  32,  31,  30,  29,  28"
LumaHistEnable         = "1"
AWBSwitch              = "0"            

;;;;;;;;;;;;;static_AWBEX_parameter;;;;;;;;;;;;;
[static_awbex]
ByPass 		       = "0"
Tolerance              = "2"
OutThresh              = "8192"
LowStop                = "4500"
HighStart              = "6000"
HighStop               = "7200"
MultiLightSourceEn     = "1"
MultiCTWt              = "256, 256, 256, 256, 256, 256, 256, 256"	

;;;;;;;;;;;;static_CCM_parameter;;;;;;;;;;
[static_ccm]
ISOActEn                 = "1"
TempActEn                = "0"
CCMOpType		 = "0"
ManualCCMTable		 = "256, 0, 0,  0, 256, 0,  0, 0, 256"
TotalNum                 = "5"
AutoColorTemp            = "6150, 4850, 3650, 2650, 2200"
AutoCCMTable_0           = "569,33198,117,32792,282,32770,46,32974,416"
AutoCCMTable_1           = "0x025E,  0x81BA,  0x005C,0x8011,  0x0117,  0x8006,0x001D,  0x8118,  0x01FB"
AutoCCMTable_2           = "523,33069,34,32833,343,32790,9,32990,469"
AutoCCMTable_3           = "594,33130,24,32892,353,27,32803,33246,769"
AutoCCMTable_4           = "594,33130,24,32892,353,27,32803,33246,769"

;;;;;;;;;;;;;;;static_saturation_parameter;;;;;;;;;;; 
[static_saturation]
AutoSat      = "118,120,115,110,100,90,95,90,72,64,56,56,56,56,56,56"

;;;;;;;;;;;;static_clut_parameter;;;;;;;;;;
[static_clut]
Enable           = "0"

;;;;;;;;;;;;;;;static_LDCI_parameter;;;;;;;;;;;;;; 
[static_ldci]	
Enable                   = "1"
LDCIOpType               = "0"
LDCIGaussLPFSigma        = "16"
;AutoHePosWgt             = "  53,  53, 53,  53, 100,34, 34,34,34, 0, 0, 0, 0, 0, 0, 0"
;AutoHePosSigma           = "  17,  17, 17,  17, 17, 90, 90,90,90, 8, 6, 2, 1, 1, 1, 1"
;AutoHePosMean            = " 132, 132,132, 132,132, 94, 90,94,94, 0, 0, 0, 0, 0, 0, 0"
;AutoHeNegWgt             = "  58,  58, 58,  58,100, 120,125,125,125, 0, 0, 0, 0, 0, 0, 0"
;AutoHeNegSigma           = "  16,  16, 16,  16, 16,  50, 50, 56, 50, 8, 6, 2, 1, 1, 1, 1"
;AutoHeNegMean            = " 115, 115,115, 115,115, 110, 116,117,111, 0, 0, 0, 0, 0, 0, 0"
;AutoBlcCtrl              = "  30,  30, 30,  30, 30,  30,  30, 30, 30,30,30,30,30,30,30,30"

;                           100   200  400  8   16  32   64 128  256
AutoHePosWgt             = "  0,   0,   0,  50, 40, 120, 120, 20, 0, 0, 0, 0, 0, 0, 0, 0"
AutoHePosSigma           = "  17,  17, 17,  60, 80, 40,  50, 90, 80, 8, 6, 2, 1, 1, 1, 1"
AutoHePosMean            = " 132, 132,132, 132, 120,160, 190, 94, 80, 0, 0, 0, 0, 0, 0, 0"
AutoHeNegWgt             = "  30,  30, 30, 130, 64, 64,  104,100,100, 0, 0, 0, 0, 0, 0, 0"
AutoHeNegSigma           = "  80,  80, 80,  80, 80,  80,  80, 80, 80, 8, 6, 2, 1, 1, 1, 1"
AutoHeNegMean            = "  30,  30, 30,  30, 80,  80,  60, 30, 30, 0, 0, 0, 0, 0, 0, 0"
AutoBlcCtrl              = "  30,  30, 30, 100,200, 200, 200, 30, 30,30,30,30,30,30,30,30"

;;;;;;;;;;;;;;;static_DRC_parameter;;;;;;;;;;;;;;;; 
[static_drc]
Enable                   = "1"
CurveSelect              = "0"
DRCOpType                = "1"
DRCAutoStr               = "512"
DRCAutoStrMin            = "0"
DRCAutoStrMax            = "512"
DRCToneMappingValue      = \
4095,5995,8191,11583,15287,19483,23987,27548,30548,31999,32988,33896,34721,35112,35481,35830,36162,36326,36483,36640,36795,36949,37103,37259,37419,37499,37580,37666,37751,\
37797,37921,38016,38116,38215,38317,38423,38533,38649,38760,38896,39029,39098,39168,39238,39315,39393,39469,39553,39635,39723,39777,39903,39995,40093,40193,40296,40402,40511,\
40624,40740,40850,40979,41111,41244,41380,41512,41670,41820,41977,42138,42305,42477,42655,42746,42840,42935,43028,43119,43195,43326,43433,43538,43644,43753,43863,43976,44090,\
44205,44324,44444,44567,44692,44818,44947,45077,45208,45348,45487,45627,45771,45917,46065,46216,46368,46526,46685,46846,47012,47174,47339,47525,47702,47882,48064,48251,48440,\
48631,48832,49032,49232,49414,49656,49872,50090,50314,50542,50773,51007,51237,51486,51742,51994,52253,52516,52783,53054,53332,53472,53610,53754,53897,54049,54195,54344,54494,\
54645,54797,54952,55107,55263,55421,55581,55737,55906,55985,56227,56403,56572,56743,56914,57088,57263,57440,57617,57799,57980,58163,58349,58533,58721,58915,59107,59301,59496,\
59693,59893,60093,60298,60478,60702,60919,61129,61293,61557,61774,61993,62213,62437,62662,62889,63109,63349,63580,63819,64057,64298,64541,64786,65034,65283

;;;;;;;;;;;;;;;;;static_nr_parameter;;;;;;;;;;;;;;;;; 
[static_nr]
Enable       = "1"
;;;;;;;;;;;;;;;;1  2  4  8 16  32 64 128
FineStr      = "43,40,40,40,60,65,40,60,50,60,50,50,50,50,40,40"
CoringWgt    = "5, 15,10,10,15, 5,20, 0, 0,0,0,0,0,0,0,0"

;;;;;;;;;;;;static_ca_parameter;;;;;;;;;;
[static_ca]
Enable           = "1"
IsoRatio         = "1300, 1300, 1250, 1200, 1150, 1100, 1250, 1300, 1450,2000, 900, 800, 800, 800, 800, 800"

;;;;;;;;;;;;static_GlobalCac_parameter;;;;;;;;;;;
[static_globalcac]
GlobalCacEnable          = "0"
VerCoordinate            = "1079"
HorCoordinate            = "1919"
ParamRedA                = "-17"
ParamRedB                = "-6"
ParamRedC                = "19"
ParamBlueA               = "13"
ParamBlueB               = "-10"
ParamBlueC               = "-3"
VerNormShift             = "7"
VerNormFactor            = "29"
HorNormShift             = "7"
HorNormFactor            = "29"
CorVarThr                = "130"


;;;;;;;;;;;;static_LocalCac_parameter;;;;;;;;;;;
[static_localcac]
LocalCacEnable		 = "1"
PurpleDetRange		 = "60"
VarThr			 	 = "0"
DePurpleCrStr	 	 = "0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"
DePurpleCbStr	 	 = "3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3"

;;;;;;;;;;;;static_dpc_parameter;;;;;;;;;;;
[static_dpc]
DpcEnable		 = "1"
Strength		 = "0, 0, 0, 152, 150, 160, 230, 240, 220, 220,  152,  152,  152,  152,  152, 152"
BlendRatio		 = "0, 0, 0,   0,   0,   0,   0,   0,   0,   0,   50,   50,   50,   50,   50,  50"
;;;;;;;;;;;;;static_DEHAZE_parameter;;;;;;;;;;;;;; 		     
[static_dehaze]		     
Enable                   = "1"
DehazeUserLutEnable      = "1"
DehazeOpType             = "1"		     
DehazeLut                = \
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,\
255,255,255,255,255,255,255,255,255,255,255,255,255

;;;;;;;;;;;;;;;static_Shading_parameter;;;;;;;;;;;;;; 
[static_shading]
Enable                 = "1"

;;;;;;;;;;;;;;;;;static_CSC_parameter;;;;;;;;;;;;;;;; 
[static_csc]
Enable       = "0"
ColorGamut   = "0"

;;;;;;;;;;;;static_crosstalk_parameter;;;;;;;;;;
[static_crosstalk]
Enable           = "0"

;;;;;;;;;;;;;;;static_Sharpen_parameter;;;;;;;;;;;;;; 
[static_sharpen]
Enable                = "1"
;                       100   200    400    800    1600   3200   6400  12800   25600  51200
AutoLumaWgt_0         = "31,   31,    31,    31,    24,    21,    21,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_1         = "31,   31,    31,    31,    24,    21,    21,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_2         = "31,   31,    31,    31,    24,    21,    21,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_3         = "31,   31,    31,    31,    24,    21,    21,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_4         = "31,   31,    31,    31,    24,    22,    22,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_5         = "31,   31,    31,    31,    24,    22,    22,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_6         = "31,   31,    31,    31,    24,    22,    22,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_7         = "31,   31,    31,    31,    24,    22,    22,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_8         = "31,   31,    31,    31,    24,    23,    23,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_9         = "31,   31,    31,    31,    24,    23,    23,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_10        = "31,   31,    31,    31,    24,    23,    23,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_11        = "31,   31,    31,    31,    24,    24,    24,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_12        = "31,   31,    31,    31,    24,    24,    24,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_13        = "31,   31,    31,    31,    24,    24,    24,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_14        = "31,   31,    31,    31,    27,    27,    27,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_15        = "31,   31,    31,    31,    27,    27,    27,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_16        = "31,   31,    31,    31,    28,    28,    28,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_17        = "31,   31,    31,    31,    28,    28,    28,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_18        = "31,   31,    31,    31,    29,    29,    29,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_19        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_20        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_21        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_22        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_23        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_24        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_25        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_26        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_27        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_28        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_29        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_30        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
AutoLumaWgt_31        = "31,   31,    31,    31,    31,    31,    31,    31,    31,    31,    31,    31,   31,    31,    31,    31"
;                         100     200     400   800   1600      3200  6400  12800   25600  51200
AutoTextureStr_0         = "172,  172,    137,  191,  323,     491,  491,   161,   161,   161,    61,     61,    61,     61,     61,     61"
AutoTextureStr_1         = "174,  174,    147,  202,  332,     487,  483,   171,   171,   171,    92,     92,    92,     92,     92,     92"
AutoTextureStr_2         = "177,  177,    158,  213,  340,     482,  475,   183,   183,   183,   124,    124,   124,    124,    124,    124"
AutoTextureStr_3         = "181,  181,    169,  224,  349,     476,  466,   195,   195,   195,   155,    155,   155,    155,    155,    155"
AutoTextureStr_4         = "184,  184,    181,  235,  358,     470,  457,   206,   206,   206,   184,    184,   184,    184,    184,    184"
AutoTextureStr_5         = "188,  188,    191,  246,  366,     463,  449,   217,   217,   217,   210,    210,   210,    210,    210,    210"
AutoTextureStr_6         = "191,  191,    200,  255,  375,     456,  440,   228,   228,   228,   234,    234,   234,    234,    234,    234"
AutoTextureStr_7         = "193,  193,    208,  263,  384,     448,  431,   237,   237,   237,   256,    256,   256,    256,    256,    256"
AutoTextureStr_8         = "194,  194,    214,  269,  393,     440,  423,   245,   245,   245,   275,    275,   275,    275,    275,    275"
AutoTextureStr_9         = "194,  194,    217,  275,  405,     431,  414,   251,   251,   251,   289,    289,   289,    289,    289,    289"
AutoTextureStr_10        = "194,  194,    217,  281,  418,     422,  406,   257,   257,   257,   300,    300,   300,    300,    300,    300"
AutoTextureStr_11        = "193,  193,    215,  286,  430,     413,  399,   261,   261,   261,   309,    309,   309,    309,    309,    309"
AutoTextureStr_12        = "193,  193,    212,  290,  441,     404,  391,   265,   265,   265,   317,    317,   317,    317,    317,    317"
AutoTextureStr_13        = "192,  192,    209,  292,  448,     394,  385,   268,   268,   268,   322,    322,   322,    322,    322,    322"
AutoTextureStr_14        = "191,  191,    205,  292,  450,     384,  379,   270,   270,   270,   326,    326,   326,    326,    326,    326"
AutoTextureStr_15        = "191,  191,    203,  289,  447,     373,  373,   271,   271,   271,   329,    329,   329,    329,    329,    329"
AutoTextureStr_16        = "191,  191,    201,  283,  435,     363,  368,   271,   271,   271,   330,    330,   330,    330,    330,    330"
AutoTextureStr_17        = "191,  191,    199,  275,  417,     351,  364,   268,   268,   268,   328,    328,   328,    328,    328,    328"
AutoTextureStr_18        = "192,  192,    198,  265,  394,     339,  361,   264,   264,   264,   325,    325,   325,    325,    325,    325"
AutoTextureStr_19        = "192,  192,    196,  253,  367,     326,  357,   258,   258,   258,   317,    317,   317,    317,    317,    317"
AutoTextureStr_20        = "193,  193,    194,  239,  340,     312,  354,   251,   251,   251,   308,    308,   308,    308,    308,    308"
AutoTextureStr_21        = "192,  192,    192,  225,  313,     298,  352,   243,   243,   243,   296,    296,   296,    296,    296,    296"
AutoTextureStr_22        = "191,  191,    190,  211,  289,     284,  349,   235,   235,   235,   283,    283,   283,    283,    283,    283"
AutoTextureStr_23        = "189,  189,    188,  197,  269,     269,  347,   229,   229,   229,    267,    267,   267,    267,    267,    267"
AutoTextureStr_24        = "186,  186,    185,  182,  254,     255,  345,   223,   223,   223,    247,    247,   247,    247,    247,    247"
AutoTextureStr_25        = "181,  181,    182,  165,  241,     242,  344,   217,   217,   217,    225,    225,   225,    225,    225,    225"
AutoTextureStr_26        = "176,  176,    179,  147,  229,     229,  342,   211,   211,   211,    201,    201,   201,    201,    201,    201"
AutoTextureStr_27        = "170,  170,    177,  129,  219,     216,  341,   204,   204,   204,    176,    176,   176,    176,    176,    176"
AutoTextureStr_28        = "164,  164,    173,  111,  209,     205,  339,   198,   198,   198,    149,    149,   149,    149,    149,    149"
AutoTextureStr_29        = "159,  159,    170,   95,  200,     195,  338,   192,   192,   192,    121,    121,   121,    121,    121,    121"
AutoTextureStr_30        = "154,  154,    167,   82,  191,     187,  337,   186,   186,   186,     92,     92,    92,     92,     92,     92"
AutoTextureStr_31        = "151,  151,    165,   72,  180,     180,  336,   180,   180,   180,     64,     64,    64,     64,     64,     64"
;                           100   200   400   800    1600  3200  6400   12800   25600  51200
AutoEdgeStr_0            = "131,  131,  61,   173,   399,  459,  459,   161,   161, 161,  324,    324,   324,    324,    324,    324"
AutoEdgeStr_1            = "135,  135,  71,   185,   404,  467,  467,   171,   171, 171,  324,    324,   324,    324,    324,    324"
AutoEdgeStr_2            = "140,  140,  82,   197,   412,  479,  479,   183,   183, 183,   324,    324,   324,    324,    324,    324"
AutoEdgeStr_3            = "145,  145,  93,   209,   421,  494,  494,   195,   195, 195,   324,    324,   324,    324,    324,    324"
AutoEdgeStr_4            = "149,  149,  104,  221,   431,  509,  509,   206,   206, 206,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_5            = "154,  154,  114,  232,   439,  522,  522,   217,   217, 217,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_6            = "158,  158,  124,  242,   447,  531,  531,   228,   228, 228,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_7            = "161,  161,  133,  251,   451,  533,  533,   237,   237, 237,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_8            = "164,  164,  140,  257,   453,  529,  529,   245,   245, 245,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_9            = "167,  167,  145,  263,   454,  519,  519,   251,   251, 251,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_10           = "170,  170,  149,  268,   455,  507,  507,   257,   257, 257,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_11           = "172,  172,  152,  273,   454,  492,  492,   261,   261, 261,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_12           = "175,  175,  154,  276,   452,  476,  476,   265,   265, 265,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_13           = "176,  176,  155,  278,   448,  460,  460,   268,   268, 268,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_14           = "176,  176,  156,  280,   443,  446,  446,   270,   270, 270,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_15           = "176,  176,  156,  280,   435,  435,  435,   271,   271, 271,    324,    324,   324,    324,    324,    324"
AutoEdgeStr_16           = "174,  174,  157,  279,   424,  426,  426,   271,   271, 271,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_17           = "171,  171,  156,  277,   410,  418,  418,   268,   268, 268,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_18           = "167,  167,  154,  273,   393,  411,  411,   264,   264, 264,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_19           = "163,  163,  152,  269,   375,  405,  405,   258,   258, 258,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_20           = "158,  158,  149,  264,   357,  399,  399,   251,   251, 251,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_21           = "153,  153,  146,  258,   339,  394,  394,   243,   243, 243,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_22           = "148,  148,  144,  252,   322,  389,  389,   235,   235, 235,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_23           = "143,  143,  142,  245,   308,  384,  384,   229,   229, 229,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_24           = "139,  139,  139,  237,   296,  380,  380,   223,   223, 223,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_25           = "134,  134,  138,  229,   284,  376,  376,   217,   217, 217,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_26           = "129,  129,  136,  219,   273,  373,  373,   211,   211, 211,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_27           = "125,  125,  131,  209,   263,  370,  370,   204,   204, 204,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_28           = "120,  120,  121,  198,   254,  368,  368,   198,   198, 198,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_29           = "115,  115,  110,  188,   247,  366,  366,   192,   192, 192,    325,    325,   325,    325,    325,    325"
AutoEdgeStr_30           = "111,  111,  97,   177,   240,  364,  364,    186,   186, 186,   325,    325,   325,    325,    325,    325"
AutoEdgeStr_31           = "106,  106,  85,   167,   235,  363,  363,    180,   180, 180,  326,    326,   326,    326,    326,    326"
;                          100   200  400  800 1600  3200 6400 12800 25600 51200
AutoTextureFreq          = "250, 220, 300, 250, 220, 300, 250, 170,  170,  170, 170, 170, 170, 170, 170, 170"
AutoEdgeFreq             = "200, 300, 300, 200, 200, 300, 200, 125,  120,  100,  96,  96,  96,  96,  96,  96"
AutoOverShoot            = " 30,  30,  30,  30,  40,  30,  30,  40,   40,   40,  40,  40,  10,  10,  10,  10"
AutoUnderShoot           = " 60,  60,  60,  60,  70, 60,  60,  55,   45,   45,  50,  50,  15,  15,  15,  15"
AutoShootSupStr          = "  4,   4,   3,   8,   8,  10,   2,   1,    0,    0,   0,   0,   0,   0,   0,   0"
AutoShootSupAdj          = "  4,   4,   3,   3,   4,   3,   2,   1,    0,    0,   0,   0,   0,   0,   0,   0"
AutoDetailCtrl           = "135, 128, 128, 128, 120, 120, 120, 120,  110,  110, 120, 120, 120, 120, 120, 120"
AutoEdgeFiltStr          = " 63,  63,  63,  63,  63,  60,  61,  62,   62,   62,  62,  62,  62,  62,  62,  62"
AutoEdgeFiltMaxCap       = "18,   18,  18 , 18,  18,  18,  18 , 18 ,  18  , 18 ,    18 ,    18 ,    18,    18  ,    18  ,   18"
AutoRGain                = " 31,  31,  31,  31,  31,  31,  31,  31,   31,    8,  31,  31,  31,  31,  31,  31"
AutoBGain                = " 31,  31,  31,  31,  31,  31,  31,  31,   31,   31,  31,  31,  31,  31,  31,  31"
AutoGGain                = " 40,  40,  40,  45,  45,  45,  45,  31,   31,   31,  31,  31,  31,  31,  31,  31"
AutoSkinGain             = " 31,  31,  31,  31,  31,  31,  31,  31,   31,   31,  31,  31,  31,  31,  31,  31"
AutoMaxSharpGain         = " 80,  80,  80,  80,  80,  80, 100,  80,   80,   80,  80,  80,  80,  80,  80,  80"

[static_venc]
h265avbrChangePos = "90"                                       
h265avbrMinIprop = "1"
h265avbrMaxIprop = "100"  
h265avbrMaxReEncodeTimes = "2"        
h265avbrMinStillPercent  = "100"       
h265avbrMaxStillQP = "28"                                 
h265avbrMinStillPSNR = "0"          
h265avbrMaxQp = "48"                 
h265avbrMinQp = "24"                
h265avbrMaxIQp = "48"                 
h265avbrMinIQp = "22"                
h265avbrMinQpDelta = "0"            
h265avbrMotionSensitivity = "100"     
h265avbrQpMapEn = "0"                  
h265avbrQpMapMode = "0" 

h265cvbrMinIprop = "1"              
h265cvbrMaxIprop = "100"               
h265cvbrMaxReEncodeTimes = "2"       
h265cvbrQpMapEn = "0"
h265cvbrQpMapMode = "0"
h265cvbrMaxQp = "47"                  
h265cvbrMinQp = "22"                  
h265cvbrMaxIQp = "47"                 
h265cvbrMinIQp = "20"                 
h265cvbrMinQpDelta = "0"            
h265cvbrMaxQpDelta = "0"             
h265cvbrExtraBitPercent = "5"       
h265cvbrLongTermStatTimeUnit = "60"  
[static_pregamma]
Enable = 0
Table = \
         4,        53,       101,       147,       192,       236,       279,       323,       365,       449,       533,       615,       696,       777,       857,       936,\
      1015,      1093,      1171,      1249,      1326,      1480,      1632,      1784,      1934,      2084,      2232,      2380,      2528,      2674,      2820,      2966,\
      3111,      3255,      3399,      3543,      3686,      3971,      4254,      4536,      4817,      5097,      5375,      5652,      5928,      6204,      6478,      6752,\
      7024,      7296,      7567,      7838,      8107,      8645,      9180,      9712,     10242,     10771,     11297,     11822,     12345,     12866,     13385,     13903,\
     14420,     14936,     15451,     15967,     16482,     17511,     18539,     19566,     20591,     21615,     22637,     23658,     24677,     25693,     26708,     27721,\
     28732,     29741,     30748,     31752,     32755,     33755,     34753,     35749,     36742,     37733,     38722,     39708,     40692,     41674,     42653,     43630,\
     44605,     45577,     46546,     47514,     48478,     49441,     51358,     53266,     55164,     57053,     58931,     60800,     62659,     64509,     66349,     68180,\
     70001,     71812,     73615,     75407,     77191,     78966,     80731,     82487,     84234,     85973,     87702,     89423,     91134,     92837,     94532,     96218,\
     97895,     99562,    101217,    102861,    104493,    106115,    109325,    112495,    115626,    118719,    121777,    124801,    127793,    130754,    133686,    136589,\
    139465,    142316,    145141,    147943,    150722,    153480,    156216,    158932,    161629,    164308,    166968,    169611,    172238,    174849,    177444,    180025,\
    182592,    185145,    187685,    190212,    192726,    195230,    200202,    205132,    210022,    214875,    219694,    224481,    229238,    233966,    238669,    243348,\
    248003,    252637,    257252,    261848,    266426,    270989,    275536,    280070,    284590,    289099,    293596,    298083,    302561,    307030,    311491,    315944,\
    320391,    324826,    329245,    333648,    338035,    342407,    346764,    351106,    355434,    359747,    368333,    376865,    385345,    393775,    402156,    410491,\
    418780,    427025,    435227,    443388,    451509,    459590,    467633,    475639,    483609,    491544,    499444,    507311,    515145,    522947,    530718,    538458,\
    546169,    553850,    561503,    569128,    576726,    584297,    591841,    599360,    606853,    621766,    636584,    651310,    665947,    680499,    694967,    709356,\
    723666,    737902,    752064,    766155,    780178,    794134,    808024,    821851,    835617,    862969,    890092,    916997,    943694,    970192,    996500,   1022625,\
   1048575
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;                                          dynamic parameter                                           ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;;;;;;;;;;;;dynamic_fps;;;;;;;;;;;;
[dynamic_fps]
fpsExpCount               = "4"
fpsExpLtoHThresh          = "640000,1600000, 3200000, 6900000"
fpsExpHtoLThresh          = "600000,1600000, 3000000, 6700000"
fpsMaxExpTime             = "50000, 80000,   84000,   84000"
fps                       = "  25,  25,  25   ,   25 "
VencGop                   = "  50,  50,  50,      50"

;;;;;;;;;;;;;;;;;dynamic_AE_parameter;;;;;;;;;;;;;;;;
[dynamic_ae]
aeExpCount                = "6"								      ;;count and LtoH and HtoL must existed at same time			
aeExpLtoHThresh           = "18414,66960,129162,532453,19541407,30000000"	
aeExpHtoLThresh           = "6000,7000,30000,200000,800000,900000"		 
AutoCompesation           = "72,72,70,70,70,70"
AutoHistOffset            = "12,14,28,28,28,28"

;;;;;;;;;;;;;;;;;dynamic_wdrexposure_parameter;;;;;;;;;;;;;;;; 
[dynamic_wdrexposure]
aeExpCount                = "8"								      ;;count and LtoH and HtoL must existed at same time			
aeExpLtoHThresh           = "200,250,300,8000,40000,300000,1000000,10000000"	
aeExpHtoLThresh           = "10,190,230,280,7000,30000,200000,800000"		 
ExpCompensation           = "50,45,40,30,30,30,28,28"
MaxHistOffset             = "12,12,12,12,12,20,20,20"

;;;;;;;;;;;;;;;;dynamic_FsWdr_parameter;;;;;;;;;;;;;;; 
[dynamic_fswdr]
ISOLtoHThresh             = "100,200,400,800,1600,3200,6400,12800,25600,51200,102400,204800,409600,819200,1638400,3276800"
RatioLtoHThresh           = "1,2,4,8,16,24,32,40,48,56,64"

MdThrLowGain_0            = "45, 45, 45, 45, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_1            = "45, 45, 45, 45, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_2            = "45, 45, 45, 45, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_3            = "24, 24, 24, 45, 64, 128, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_4            = "24, 24, 24, 45, 64, 128, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_5            = "45, 45, 45, 45, 64, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_6            = "45, 45, 45, 45, 64, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_7            = "64, 64, 64, 64, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_8            = "64, 64, 64, 64, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_9            = "128, 128, 128, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrLowGain_10           = "255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"

MdThrHigGain_0            = "64, 64, 64, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_1            = "64, 64, 64, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_2            = "64, 64, 64, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_3            = "45, 45, 45, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_4            = "45, 45, 45, 64, 96, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_5            = "64, 64, 64, 64, 96, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_6            = "64, 64, 64, 64, 96, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_7            = "64, 64, 64, 64, 128, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_8            = "64, 64, 64, 64, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_9            = "255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"
MdThrHigGain_10           = "255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255"

;;;;;;;;;;;;;;;;dynamic_Dehaze_parameter;;;;;;;;;;;;;;; 
[dynamic_dehaze]
ExpThreshCnt              = "7"
; Ref ISO Value             100,     400,     800 ,    1600,   3200,   6400 12ps,12800 12ps
ExpThreshLtoH             = "50000,  200000,  400000,  800000, 1600000,5376000,10752000"
ManualDehazeStr           = "90,     90,     100,    100,   100,    120,    120"



;;;;;;;;;;;;;;;;;;;;;;thread_Linear_DRC_parameter;;;;;;;;;;;;;;;;;;;;
[dynamic_linear_drc]
Enable                    = "1"
IsoCnt                    = "10"
IsoLevel                  = "200, 400, 800,  1600,  3200,  4500,  6400,  12800, 17800, 25600"
LocalMixingBrightMax      = "50,  50,   50,    44,     30,    30,    20,     20,    20,   20"
LocalMixingBrightMin      = " 20,  20,  20,    18,    16,    16,    12,     10,    10,     8"
LocalMixingDarkMax        = " 36,  36,  34,    30,    30,    30,    28,     26,    26,    24"
LocalMixingDarkMin        = " 28,  28,  26,    30,    30,    22,    20,     18,    18,    16"
BrightGainLmt             = "  0,   0,   0,     0,     0,     0,     0,      0,     0,     0"
BrightGainLmtStep         = " 10,  10,  10,    10,    10,    10,    10,     10,    10,    10"
DarkGainLmtY              = " 80,  80,  90,   100,   100,   100,   100,    100,   120,   120"
DarkGainLmtC              = "  0,   0,   0,     0,     0,     0,     0,      0,     0,     0"
FltScaleCoarse            = "  6,   6,   6,     6,     6,     6,     6,      6,     6,     6"
FltScaleFine              = "  6,   6,   6,     6,     6,     6,     6,      6,     6,     6"
ContrastControl           = "  9,   9,   9,     9,     9,     9,     9,      9,     9,     9"
DetailAdjustFactor        = "  9,   9,   9,     9,     9,     9,     9,      9,     9,     9"
Asymmetry                 = "  6,   6,   6,     6,     6,     6,     6,      6,     6,     6"
SecondPole                = "150, 150, 150,   150,   150,   150,   150,    150,   150,   150"
Compress                  = "150, 150, 150,   150,   150,   150,   150,    150,   150,   150"
Stretch                   = " 50,  50,  50,    50,    50,    50,    50,     50,    50,    50"
Strength                  = "500, 450, 420,   450,   420,   400,   360,    250,   200,   200"

;;;;;;;;;;;;;;;;;;;;;;thread_DRC_parameter;;;;;;;;;;;;;;;;;;;;
[dynamic_drc]
RationCnt                 = "6"
RationLevel               = "1,       2,        4,        8,       12,       16"
RefRatioCnt               = "1"
RefRatioLtoH              = "64"
RefRatioAlpha             = "256"
IsoCnt                    = "7"
IsoLevel                  = "200,     800,      1600,     3200,    6400,     12800,     25600"
Enable                    = "1"

Interval                  = "30"
LocalMixingBrightMax      = "50,     50,      50,       100,     100,     100,       100"
LocalMixingBrightMin      = "20,      20,       18,       16,      12,       10,        8"
LocalMixingDarkMax        = "36,      34,       32,       30,      28,       26,        24"
LocalMixingDarkMin        = "28,      26,       24,       22,      20,       18,        16"
DarkGainLmtY              = "0,       0,        0,        0,       0,        0,         0"
DarkGainLmtC              = "0,       0,        0,        0,       0,        0,         0"
DetailAdjustFactor        = "10,      10,       9,        8,       7,        6,         6"
SpatialFltCoef            = "0,       0,        0,        1,       1,        2,         2"
RangeFltCoef              = "1,       1,        1,        2,       2,        2,         2"
GradRevMax                = "30,      30,       30,       30,      30,       30,        30"
GradRevThr                = "50,      50,       50,       50,      50,       50,        50"
Compress                  = "150,     150,      150,      150,     150,      150,       150"
Stretch                   = "50,      50,       50,       50,      50,       50,        50"

AutoStrength_0            = "500"
AutoStrength_1            = "450"
AutoStrength_2            = "400"
AutoStrength_3            = "350"
AutoStrength_4            = "300"
AutoStrength_5            = "200"
AutoStrength_6            = "100"

DRCTMValueLow      = \
0,128,266,423,582,769,968,1178,1408,1660,1931,2224,2536,2868,3227,3612,4019,4437,4880,5331,5799,6258,6702,7121,7487,7782,8034,8302,8587,8887,9204,9540,9894,10265,10657,11066,11499,11948,12416,12902,13406,13924,14455,14996,\
15540,16082,16615,17129,17615,18055,18432,18781,19151,19544,19961,20403,20871,21366,21888,22438,23018,23625,24262,24926,25617,26332,27068,27820,28584,29350,30108,30846,31546,32187,32742,33177,33545,33924,34313,34711,35117,\
35530,35948,36369,36792,37213,37633,38047,38453,38851,39238,39613,39976,40330,40677,41023,41380,41566,41761,41967,42188,42417,42645,42871,43094,43316,43535,43752,43966,44177,44385,44589,44791,44991,45188,45383,45577,45771,\
45966,46163,46364,46570,46785,47012,47253,47513,47785,48061,48340,48622,48909,49200,49497,49799,50108,50425,50751,50918,51087,51260,51437,51616,51800,51988,52180,52378,52580,52789,53003,53224,53452,53686,53925,54169,54418,\
54671,54931,55196,55466,55743,56025,56314,56610,56913,57222,57539,57864,58197,58538,58711,58887,59065,59245,59428,59613,59801,59992,60187,60386,60588,60795,61005,61219,61437,61658,61882,62110,62340,62574,62810,63048,63290,\
63533,63778,64026,64274,64525,64776,65028,65281

DRCTMValueHigh      = \
0,312,652,1037,1429,1884,2362,2858,3387,3941,4509,5079,5631,6144,6653,7195,7767,8353,8973,9606,10268,10925,11573,12205,12789,13312,13794,14287,14794,15305,15822,16344,16866,17384,17901,18408,18909,19394,19865,20336,20823,21323, \
21834,22355,22880,23406,23927,24436,24927,25387,25804,26200,26601,27010,27422,27837,28254,28670,29085,29498,29908,30315,30721,31129,31551,31995,32461,32947,33450,33966,34491,35015,35530,36022,36474,36863,37213,37566,37918,38269,\
38617,38959,39296,39624,39945,40256,40562,40862,41164,41465,41759,42044,42319,42584,42839,43085,43328,43449,43572,43697,43827,43956,44079,44195,44306,44411,44513,44611,44709,44807,44908,45014,45131,45260,45400,45546,45698,45855,\
46019,46187,46362,46541,46726,46916,47110,47308,47509,47714,47924,48139,48360,48588,48823,49068,49324,49592,49875,50022,50175,50331,50490,50653,50821,50994,51174,51360,51554,51756,51968,52192,52428,52676,52935,53206,53487,53778,\
54077,54385,54698,55017,55341,55666,55993,56319,56640,56958,57275,57595,57921,58089,58259,58434,58613,58798,58988,59186,59390,59602,59820,60045,60276,60512,60752,60997,61245,61496,61749,62003,62258,62514,62774,63037,63304,63574,\
63847,64123,64401,64682,64964,65248

;;;;;;;;;;;;;;;;dynamic_gamma_parameter;;;;;;;;;;;;;;;                                         ;;here only have one gamma, maybe mistake
[dynamic_gamma]
Option = 0   
Interval                  = "20"
TotalNum                  = "3"
; Ref ISO Value             100,     400,     800 ,    1600,   3200,   6400 12ps,12800 12ps
;ExpThreshLtoH             = "50000,  200000,  400000,  800000, 1600000,5376000,10752000"
gammaExpThreshLtoH        = "600000, 1800000, 2800000, 556000000"
gammaExpThreshHtoL        = "600000, 1800000, 2800000, 556000000"
;;;GammaExpThresh                                                                             ;;Photo maybe use it, here don't use.
Table_0                   = \
0,11,23,34,45,57,68,80,91,102,114,125,137,148,160,171,183,194,205,217,228,240,251,263,274,286,297,309,320,332,343,354,366,377,389,400,412,423,434,446,457,468,480,491,503,514,525,\
536,548,559,570,581,593,604,615,626,637,649,660,671,682,693,704,715,726,737,748,759,770,781,793,804,815,826,837,849,860,871,882,894,905,916,928,939,950,961,973,984,995,1006,1018,\
1029,1040,1051,1062,1073,1084,1095,1106,1117,1128,1139,1150,1160,1171,1182,1192,1203,1213,1224,1234,1244,1254,1264,1275,1284,1294,1304,1314,1324,1333,1343,1352,1361,1370,1379,1388,\
1397,1406,1415,1423,1432,1440,1448,1457,1465,1473,1481,1489,1496,1504,1512,1519,1527,1534,1542,1549,1556,1563,1571,1578,1585,1592,1598,1605,1612,1619,1626,1632,1639,1645,1652,1658,\
1665,1671,1677,1684,1690,1696,1702,1708,1715,1721,1727,1733,1739,1745,1751,1757,1763,1769,1775,1781,1787,1793,1799,1805,1810,1816,1822,1828,1834,1840,1846,1852,1858,1863,1869,1875,\
1880,1886,1892,1897,1903,1908,1914,1919,1925,1930,1935,1941,1946,1951,1956,1962,1967,1972,1977,1982,1987,1992,1997,2002,2007,2012,2017,2022,2027,2032,2037,2041,2046,2051,2056,2061,\
2066,2070,2075,2080,2085,2089,2094,2099,2103,2108,2113,2117,2122,2127,2131,2136,2141,2145,2150,2155,2159,2164,2168,2173,2177,2182,2187,2191,2196,2200,2205,2210,2214,2219,2223,2228,\
2232,2237,2241,2246,2250,2254,2259,2263,2268,2272,2276,2281,2285,2289,2294,2298,2302,2307,2311,2315,2319,2324,2328,2332,2336,2340,2344,2349,2353,2357,2361,2365,2369,2373,2377,2381,\
2385,2389,2393,2397,2401,2405,2409,2413,2416,2420,2424,2428,2432,2436,2439,2443,2447,2450,2454,2458,2461,2465,2468,2472,2475,2479,2482,2486,2489,2493,2496,2500,2503,2506,2510,2513,\
2516,2520,2523,2526,2529,2533,2536,2539,2542,2546,2549,2552,2555,2558,2561,2565,2568,2571,2574,2577,2580,2583,2586,2590,2593,2596,2599,2602,2605,2608,2611,2614,2617,2621,2624,2627,\
2630,2633,2636,2639,2642,2646,2649,2652,2656,2659,2662,2665,2668,2671,2674,2677,2680,2683,2686,2690,2693,2696,2699,2702,2705,2708,2711,2714,2717,2720,2723,2726,2729,2732,2735,2738,\
2741,2744,2747,2750,2753,2756,2759,2762,2764,2767,2770,2773,2776,2779,2782,2785,2788,2791,2794,2796,2799,2802,2805,2808,2811,2814,2817,2820,2822,2825,2828,2831,2834,2837,2840,2843,\
2845,2848,2851,2853,2856,2859,2862,2865,2868,2871,2874,2877,2879,2882,2885,2887,2890,2893,2896,2899,2901,2904,2907,2909,2912,2915,2918,2920,2923,2926,2929,2932,2934,2937,2940,2942,\
2945,2948,2951,2954,2956,2959,2962,2964,2966,2969,2972,2974,2977,2980,2983,2986,2988,2991,2994,2996,2998,3001,3004,3006,3009,3012,3015,3018,3020,3023,3026,3028,3031,3033,3035,3038,\
3040,3043,3046,3048,3051,3054,3057,3059,3062,3064,3066,3069,3071,3074,3077,3080,3082,3085,3088,3090,3093,3095,3097,3100,3102,3105,3108,3110,3113,3115,3117,3120,3122,3125,3128,3130,\
3133,3135,3138,3140,3143,3145,3147,3150,3152,3155,3158,3160,3163,3165,3167,3170,3172,3175,3178,3180,3183,3185,3187,3189,3192,3194,3196,3199,3201,3204,3207,3209,3211,3214,3217,3219,\
3222,3224,3226,3228,3231,3233,3235,3238,3241,3243,3245,3247,3250,3252,3254,3257,3260,3262,3264,3267,3269,3271,3273,3276,3279,3281,3283,3286,3288,3290,3292,3295,3298,3300,3302,3305,\
3307,3309,3311,3313,3316,3318,3320,3322,3325,3327,3329,3332,3335,3337,3339,3342,3344,3346,3348,3350,3353,3355,3357,3360,3362,3364,3366,3368,3371,3373,3375,3378,3380,3382,3384,3386,\
3389,3391,3393,3396,3398,3400,3402,3405,3407,3409,3411,3413,3416,3418,3420,3423,3425,3427,3429,3431,3434,3436,3438,3441,3443,3445,3447,3449,3452,3454,3456,3459,3461,3463,3465,3467,\
3469,3471,3473,3475,3478,3480,3482,3484,3487,3489,3491,3494,3496,3498,3500,3502,3504,3506,3508,3511,3513,3515,3517,3519,3521,3523,3525,3528,3530,3532,3534,3536,3538,3540,3542,3545,\
3547,3549,3551,3553,3555,3557,3559,3561,3564,3566,3568,3570,3572,3574,3576,3578,3581,3583,3585,3587,3589,3591,3593,3595,3598,3600,3602,3604,3606,3608,3610,3612,3614,3616,3618,3620,\
3622,3624,3626,3628,3631,3633,3635,3637,3639,3641,3643,3645,3647,3649,3651,3653,3655,3657,3659,3661,3663,3665,3667,3670,3672,3674,3676,3678,3680,3682,3684,3686,3688,3690,3692,3694,\
3696,3698,3700,3702,3704,3706,3708,3710,3712,3714,3716,3718,3720,3722,3724,3726,3728,3730,3732,3734,3736,3738,3740,3742,3744,3746,3748,3750,3752,3754,3756,3758,3760,3762,3764,3766,\
3767,3769,3771,3773,3775,3777,3779,3781,3783,3785,3787,3789,3791,3793,3795,3797,3799,3801,3803,3804,3806,3808,3810,3812,3814,3816,3818,3820,3822,3824,3826,3828,3830,3832,3834,3836,\
3837,3839,3841,3843,3845,3847,3849,3851,3853,3855,3857,3858,3860,3862,3864,3866,3868,3870,3872,3873,3875,3877,3879,3881,3883,3885,3887,3888,3890,3892,3894,3896,3898,3900,3902,3904,\
3905,3907,3909,3911,3913,3915,3917,3919,3920,3922,3924,3926,3928,3930,3932,3934,3935,3937,3939,3941,3943,3945,3947,3949,3950,3952,3954,3955,3957,3959,3961,3963,3965,3967,3969,3971,\
3972,3974,3976,3977,3979,3981,3983,3985,3987,3989,3991,3993,3994,3996,3998,4000,4001,4003,4005,4006,4008,4010,4012,4014,4016,4018,4020,4022,4023,4025,4027,4028,4030,4032,4034,4036,\
4037,4039,4041,4042,4044,4046,4048,4050,4052,4054,4056,4058,4059,4061,4063,4064,4066,4068,4070,4072,4073,4075,4077,4078,4080,4082,4084,4086,4087,4089,4091,4092,4094,4095


Table_1                   = \
0,11,23,34,45,57,68,79,91,102,114,125,137,148,160,171,183,194,206,217,229,240,252,263,275,286,298,309,321,332,343,355,366,378,389,401,412,423,435,446,458,469,480,492,503,514,525,537,\
548,559,570,581,592,603,614,625,636,647,658,669,680,691,702,712,723,734,744,755,766,776,787,797,808,819,829,840,850,861,871,882,892,903,913,923,934,944,955,965,975,986,996,1006,1016,\
1026,1037,1047,1057,1067,1077,1087,1097,1107,1117,1127,1137,1147,1157,1167,1177,1187,1196,1206,1216,1226,1235,1245,1255,1264,1274,1283,1293,1302,1312,1321,1331,1340,1349,1359,1368,\
1377,1386,1396,1405,1414,1423,1432,1441,1451,1460,1469,1478,1487,1496,1505,1514,1523,1531,1540,1549,1558,1567,1575,1584,1593,1602,1610,1619,1628,1636,1645,1653,1662,1670,1678,1687,\
1695,1704,1712,1720,1728,1737,1745,1753,1761,1769,1777,1785,1793,1801,1809,1817,1824,1832,1840,1848,1855,1863,1871,1878,1886,1893,1900,1908,1915,1922,1930,1937,1944,1951,1958,1965,\
1972,1979,1986,1993,1999,2006,2013,2019,2026,2033,2039,2046,2052,2059,2065,2071,2078,2084,2090,2096,2103,2109,2115,2121,2127,2133,2139,2146,2152,2158,2164,2169,2175,2181,2187,2193,\
2199,2205,2211,2217,2222,2228,2234,2240,2246,2251,2257,2263,2269,2274,2280,2286,2292,2297,2303,2309,2315,2320,2326,2332,2338,2343,2349,2355,2360,2366,2371,2377,2382,2388,2394,2399,\
2405,2410,2415,2421,2426,2432,2437,2443,2448,2453,2459,2464,2469,2474,2480,2485,2490,2495,2501,2506,2511,2516,2521,2526,2532,2537,2542,2547,2552,2557,2562,2567,2572,2577,2582,2586,\
2591,2596,2601,2606,2611,2616,2620,2625,2630,2635,2639,2644,2649,2653,2658,2662,2667,2671,2676,2680,2685,2689,2694,2698,2702,2707,2711,2715,2720,2724,2728,2733,2737,2741,2745,2750,\
2754,2758,2762,2766,2771,2775,2779,2783,2787,2791,2795,2799,2803,2807,2811,2815,2820,2824,2828,2831,2835,2839,2843,2847,2851,2855,2859,2863,2867,2871,2875,2878,2882,2886,2890,2894,\
2898,2901,2905,2909,2913,2917,2920,2924,2928,2931,2935,2939,2942,2946,2949,2953,2956,2960,2963,2967,2970,2974,2977,2980,2984,2987,2990,2994,2997,3000,3003,3007,3010,3013,3016,3019,\
3022,3025,3029,3032,3035,3038,3041,3044,3047,3050,3053,3056,3059,3062,3065,3068,3070,3073,3076,3079,3082,3085,3088,3090,3093,3096,3099,3102,3104,3107,3110,3113,3115,3118,3121,3123,\
3126,3129,3131,3134,3137,3139,3142,3145,3147,3150,3153,3155,3158,3160,3163,3166,3168,3171,3173,3176,3178,3181,3184,3186,3189,3191,3194,3196,3199,3201,3204,3206,3209,3212,3214,3217,\
3219,3222,3224,3227,3229,3232,3234,3237,3239,3242,3245,3247,3250,3252,3255,3257,3260,3262,3265,3268,3270,3273,3275,3278,3280,3283,3285,3288,3291,3293,3296,3298,3301,3303,3306,3308,\
3311,3314,3316,3319,3321,3324,3326,3329,3331,3334,3336,3339,3341,3344,3346,3349,3351,3354,3356,3359,3361,3364,3366,3368,3371,3373,3376,3378,3381,3383,3385,3388,3390,3393,3395,3397,\
3400,3402,3405,3407,3409,3412,3414,3416,3419,3421,3424,3426,3428,3431,3433,3435,3438,3440,3442,3444,3447,3449,3451,3454,3456,3458,3460,3463,3465,3467,3470,3472,3474,3476,3478,3481,\
3483,3485,3487,3490,3492,3494,3496,3498,3501,3503,3505,3507,3509,3511,3514,3516,3518,3520,3522,3524,3526,3529,3531,3533,3535,3537,3539,3541,3543,3545,3547,3550,3552,3554,3556,3558,\
3560,3562,3564,3566,3568,3570,3572,3574,3576,3578,3580,3582,3584,3586,3588,3590,3592,3594,3596,3598,3600,3601,3603,3605,3607,3609,3611,3613,3615,3617,3618,3620,3622,3624,3626,3628,\
3630,3631,3633,3635,3637,3639,3640,3642,3644,3646,3648,3649,3651,3653,3655,3656,3658,3660,3662,3663,3665,3667,3669,3670,3672,3674,3676,3677,3679,3681,3682,3684,3686,3687,3689,3691,\
3692,3694,3696,3697,3699,3701,3702,3704,3706,3707,3709,3711,3712,3714,3715,3717,3719,3720,3722,3723,3725,3727,3728,3730,3731,3733,3735,3736,3738,3739,3741,3743,3744,3746,3747,3749,\
3750,3752,3753,3755,3757,3758,3760,3761,3763,3764,3766,3767,3769,3771,3772,3774,3775,3777,3778,3780,3781,3783,3784,3786,3787,3789,3790,3792,3794,3795,3797,3798,3800,3801,3803,3804,\
3806,3807,3809,3810,3812,3813,3815,3816,3818,3819,3820,3822,3823,3825,3826,3828,3829,3831,3832,3834,3835,3836,3838,3839,3841,3842,3844,3845,3846,3848,3849,3851,3852,3854,3855,3856,\
3858,3859,3861,3862,3863,3865,3866,3867,3869,3870,3872,3873,3874,3876,3877,3878,3880,3881,3882,3884,3885,3886,3888,3889,3891,3892,3893,3894,3896,3897,3898,3900,3901,3902,3904,3905,\
3906,3908,3909,3910,3911,3913,3914,3915,3917,3918,3919,3920,3922,3923,3924,3926,3927,3928,3929,3931,3932,3933,3934,3936,3937,3938,3939,3940,3942,3943,3944,3945,3947,3948,3949,3950,\
3951,3953,3954,3955,3956,3958,3959,3960,3961,3962,3963,3965,3966,3967,3968,3969,3971,3972,3973,3974,3975,3977,3978,3979,3980,3981,3983,3984,3985,3986,3988,3989,3990,3991,3992,3994,\
3995,3996,3997,3998,4000,4001,4002,4003,4004,4006,4007,4008,4009,4010,4012,4013,4014,4015,4016,4018,4019,4020,4021,4022,4024,4025,4026,4027,4028,4029,4030,4032,4033,4034,4035,4036,\
4037,4038,4040,4041,4042,4043,4044,4045,4046,4047,4048,4049,4050,4051,4052,4053,4054,4056,4057,4058,4059,4060,4061,4061,4062,4063,4064,4065,4066,4067,4068,4069,4070,4071,4072,4073,\
4073,4074,4075,4076,4077,4078,4078,4079,4080,4081,4082,4082,4083,4084,4085,4085,4086,4087,4087,4088,4089,4089,4090,4091,4091,4092,4092,4093,4093,4094,4094,4095


Table_2                   = \
0,10,19,29,38,48,57,67,76,86,95,105,114,124,133,143,152,162,171,180,190,199,209,218,228,237,247,256,265,275,284,294,303,313,322,332,341,351,360,370,379,389,399,408,418,427,437,447,456,\
466,476,485,495,505,515,525,534,544,554,564,574,584,594,604,614,624,634,644,655,665,675,686,696,706,717,727,738,749,759,770,780,791,802,813,823,834,845,856,867,877,888,899,910,921,931,\
942,953,964,975,986,996,1007,1018,1029,1039,1050,1061,1072,1082,1093,1103,1114,1125,1135,1145,1156,1166,1177,1187,1197,1207,1218,1228,1238,1248,1258,1268,1277,1287,1297,1307,1316,1326,\
1336,1345,1355,1364,1374,1384,1393,1403,1412,1421,1431,1440,1450,1459,1468,1478,1487,1496,1506,1515,1524,1533,1542,1551,1560,1570,1579,1588,1597,1606,1614,1623,1632,1641,1650,1659,1667,\
1676,1685,1693,1702,1711,1719,1728,1736,1745,1753,1762,1770,1778,1787,1795,1803,1811,1820,1828,1836,1844,1852,1860,1868,1876,1884,1892,1900,1907,1915,1923,1931,1938,1946,1953,1961,1969,\
1976,1984,1991,1999,2006,2013,2021,2028,2035,2042,2050,2057,2064,2071,2078,2085,2092,2099,2106,2113,2120,2127,2134,2141,2148,2154,2161,2168,2175,2181,2188,2195,2201,2208,2214,2221,2227,\
2234,2240,2246,2253,2259,2265,2272,2278,2284,2291,2297,2303,2309,2315,2321,2327,2333,2339,2345,2351,2357,2363,2368,2374,2380,2386,2391,2397,2402,2408,2414,2419,2424,2430,2435,2441,2446,\
2451,2457,2462,2467,2472,2478,2483,2488,2493,2498,2503,2508,2513,2519,2524,2529,2534,2538,2543,2548,2553,2558,2563,2568,2573,2578,2582,2587,2592,2597,2601,2606,2611,2616,2620,2625,2630,\
2635,2639,2644,2649,2653,2658,2662,2667,2671,2676,2680,2685,2689,2694,2698,2702,2707,2711,2715,2720,2724,2728,2733,2737,2741,2745,2750,2754,2758,2762,2766,2771,2775,2779,2783,2787,2791,\
2795,2799,2803,2807,2811,2815,2820,2824,2828,2831,2835,2839,2843,2847,2851,2855,2859,2863,2867,2871,2875,2878,2882,2886,2890,2894,2898,2901,2905,2909,2913,2917,2920,2924,2928,2931,2935,\
2939,2942,2946,2949,2953,2956,2960,2963,2967,2970,2974,2977,2980,2984,2987,2990,2994,2997,3000,3003,3007,3010,3013,3016,3019,3022,3025,3029,3032,3035,3038,3041,3044,3047,3050,3053,3056,\
3059,3062,3065,3068,3070,3073,3076,3079,3082,3085,3088,3090,3093,3096,3099,3102,3104,3107,3110,3113,3115,3118,3121,3123,3126,3129,3131,3134,3137,3139,3142,3145,3147,3150,3153,3155,3158,\
3160,3163,3166,3168,3171,3173,3176,3178,3181,3184,3186,3189,3191,3194,3196,3199,3201,3204,3206,3209,3212,3214,3217,3219,3222,3224,3227,3229,3232,3234,3237,3239,3242,3245,3247,3250,3252,\
3255,3257,3260,3262,3265,3268,3270,3273,3275,3278,3280,3283,3285,3288,3291,3293,3296,3298,3301,3303,3306,3308,3311,3314,3316,3319,3321,3324,3326,3329,3331,3334,3336,3339,3341,3344,3346,\
3349,3351,3354,3356,3359,3361,3364,3366,3368,3371,3373,3376,3378,3381,3383,3385,3388,3390,3393,3395,3397,3400,3402,3405,3407,3409,3412,3414,3416,3419,3421,3424,3426,3428,3431,3433,3435,\
3438,3440,3442,3444,3447,3449,3451,3454,3456,3458,3460,3463,3465,3467,3470,3472,3474,3476,3478,3481,3483,3485,3487,3490,3492,3494,3496,3498,3501,3503,3505,3507,3509,3511,3514,3516,3518,\
3520,3522,3524,3526,3529,3531,3533,3535,3537,3539,3541,3543,3545,3547,3550,3552,3554,3556,3558,3560,3562,3564,3566,3568,3570,3572,3574,3576,3578,3580,3582,3584,3586,3588,3590,3592,3594,\
3596,3598,3600,3601,3603,3605,3607,3609,3611,3613,3615,3617,3618,3620,3622,3624,3626,3628,3630,3631,3633,3635,3637,3639,3640,3642,3644,3646,3648,3649,3651,3653,3655,3656,3658,3660,3662,\
3663,3665,3667,3669,3670,3672,3674,3676,3677,3679,3681,3682,3684,3686,3687,3689,3691,3692,3694,3696,3697,3699,3701,3702,3704,3706,3707,3709,3711,3712,3714,3715,3717,3719,3720,3722,3723,\
3725,3727,3728,3730,3731,3733,3735,3736,3738,3739,3741,3743,3744,3746,3747,3749,3750,3752,3753,3755,3757,3758,3760,3761,3763,3764,3766,3767,3769,3771,3772,3774,3775,3777,3778,3780,3781,\
3783,3784,3786,3787,3789,3790,3792,3794,3795,3797,3798,3800,3801,3803,3804,3806,3807,3809,3810,3812,3813,3815,3816,3818,3819,3820,3822,3823,3825,3826,3828,3829,3831,3832,3834,3835,3836,\
3838,3839,3841,3842,3844,3845,3846,3848,3849,3851,3852,3854,3855,3856,3858,3859,3861,3862,3863,3865,3866,3867,3869,3870,3872,3873,3874,3876,3877,3878,3880,3881,3882,3884,3885,3886,3888,\
3889,3891,3892,3893,3894,3896,3897,3898,3900,3901,3902,3904,3905,3906,3908,3909,3910,3911,3913,3914,3915,3917,3918,3919,3920,3922,3923,3924,3926,3927,3928,3929,3931,3932,3933,3934,3936,\
3937,3938,3939,3940,3942,3943,3944,3945,3947,3948,3949,3950,3951,3953,3954,3955,3956,3958,3959,3960,3961,3962,3963,3965,3966,3967,3968,3969,3971,3972,3973,3974,3975,3977,3978,3979,3980,\
3981,3983,3984,3985,3986,3988,3989,3990,3991,3992,3994,3995,3996,3997,3998,4000,4001,4002,4003,4004,4006,4007,4008,4009,4010,4012,4013,4014,4015,4016,4018,4019,4020,4021,4022,4024,4025,\
4026,4027,4028,4029,4030,4032,4033,4034,4035,4036,4037,4038,4040,4041,4042,4043,4044,4045,4046,4047,4048,4049,4050,4051,4052,4053,4054,4056,4057,4058,4059,4060,4061,4061,4062,4063,4064,\
4065,4066,4067,4068,4069,4070,4071,4072,4073,4073,4074,4075,4076,4077,4078,4078,4079,4080,4081,4082,4082,4083,4084,4085,4085,4086,4087,4087,4088,4089,4089,4090,4091,4091,4092,4092,4093,\
4093,4094,4094,4095
Table_3                   = \
0,9,18,27,35,44,53,62,71,80,89,97,106,115,124,133,142,151,160,168,177,186,195,204,213,222,231,240,248,257,266,275,284,293,302,311,319,328,337,346,355,364,373,381,\
390,399,408,417,426,434,443,452,461,470,478,487,496,505,514,522,531,540,549,557,566,575,583,592,601,610,619,627,636,645,654,663,671,680,689,698,707,716,724,733,\
742,751,760,768,777,786,795,804,812,821,830,839,847,856,865,874,882,891,900,908,917,926,934,943,951,960,968,977,985,994,1002,1010,1019,1027,1036,1044,1052,1060,\
1068,1077,1085,1093,1101,1109,1117,1125,1133,1141,1149,1157,1164,1172,1180,1188,1196,1203,1211,1219,1227,1234,1242,1249,1257,1265,1272,1280,1287,1295,1302,1310,\
1317,1325,1332,1339,1347,1354,1362,1369,1376,1383,1391,1398,1405,1412,1420,1427,1434,1441,1448,1455,1462,1470,1477,1484,1491,1498,1505,1512,1519,1526,1533,1540,\
1547,1554,1560,1567,1574,1581,1588,1595,1602,1609,1615,1622,1629,1636,1642,1649,1656,1663,1669,1676,1683,1689,1696,1702,1709,1716,1722,1729,1735,1742,1748,1755,\
1761,1768,1774,1781,1787,1793,1800,1806,1812,1819,1825,1831,1838,1844,1850,1856,1863,1869,1875,1881,1887,1893,1900,1906,1912,1918,1924,1930,1936,1942,1948,1954,\
1960,1966,1972,1977,1983,1989,1995,2001,2007,2012,2018,2024,2030,2035,2041,2047,2053,2058,2064,2069,2075,2081,2086,2092,2097,2103,2108,2114,2119,2125,2130,2136,\
2141,2147,2152,2157,2163,2168,2173,2179,2184,2189,2194,2200,2205,2210,2215,2220,2226,2231,2236,2241,2246,2251,2256,2261,2266,2271,2276,2281,2286,2291,2296,2300,\
2305,2310,2315,2320,2325,2329,2334,2339,2343,2348,2353,2357,2362,2366,2371,2376,2380,2385,2389,2393,2398,2402,2407,2411,2415,2420,2424,2428,2433,2437,2441,2445,\
2450,2454,2458,2462,2466,2470,2474,2479,2483,2487,2491,2495,2499,2503,2507,2511,2515,2519,2523,2527,2530,2534,2538,2542,2546,2550,2554,2557,2561,2565,2569,2573,\
2576,2580,2584,2588,2592,2595,2599,2603,2606,2610,2614,2617,2621,2625,2628,2632,2635,2639,2643,2646,2650,2653,2657,2660,2663,2667,2670,2674,2677,2680,2684,2687,\
2691,2694,2697,2701,2704,2707,2710,2714,2717,2720,2723,2727,2730,2733,2736,2739,2743,2746,2749,2752,2755,2758,2762,2765,2768,2771,2774,2777,2780,2783,2786,2790,\
2793,2796,2799,2802,2805,2808,2811,2814,2817,2820,2823,2826,2829,2832,2835,2838,2841,2844,2847,2850,2853,2856,2859,2861,2864,2867,2870,2873,2876,2879,2881,2884,\
2887,2890,2893,2895,2898,2901,2904,2907,2909,2912,2915,2918,2920,2923,2926,2928,2931,2934,2937,2939,2942,2945,2947,2950,2953,2955,2958,2961,2963,2966,2969,2971,\
2974,2977,2979,2982,2985,2987,2990,2993,2995,2998,3001,3003,3006,3009,3011,3014,3017,3019,3022,3025,3027,3030,3032,3035,3037,3040,3043,3045,3048,3050,3053,3055,\
3058,3060,3063,3065,3068,3070,3073,3075,3078,3080,3083,3085,3088,3090,3093,3095,3098,3100,3103,3105,3107,3110,3112,3115,3117,3120,3122,3124,3127,3129,3131,3134,\
3136,3139,3141,3143,3146,3148,3150,3153,3155,3157,3160,3162,3164,3167,3169,3171,3174,3176,3178,3181,3183,3185,3188,3190,3192,3195,3197,3199,3201,3204,3206,3208,\
3211,3213,3215,3217,3220,3222,3224,3226,3229,3231,3233,3235,3238,3240,3242,3244,3247,3249,3251,3253,3256,3258,3260,3262,3265,3267,3269,3271,3274,3276,3278,3280,\
3283,3285,3287,3289,3292,3294,3296,3298,3300,3303,3305,3307,3309,3312,3314,3316,3318,3320,3322,3325,3327,3329,3331,3333,3335,3338,3340,3342,3344,3346,3348,3350,\
3353,3355,3357,3359,3361,3363,3365,3367,3369,3372,3374,3376,3378,3380,3382,3384,3386,3388,3390,3392,3395,3397,3399,3401,3403,3405,3407,3409,3411,3413,3415,3417,\
3419,3421,3423,3425,3427,3429,3431,3433,3436,3438,3440,3442,3444,3446,3448,3450,3452,3454,3456,3458,3460,3462,3464,3466,3468,3470,3472,3474,3476,3478,3480,3482,\
3484,3486,3488,3490,3492,3494,3496,3498,3500,3502,3504,3507,3509,3511,3513,3515,3517,3519,3521,3523,3525,3527,3529,3531,3533,3535,3537,3539,3541,3543,3545,3547,\
3550,3552,3554,3556,3558,3560,3562,3564,3566,3568,3570,3572,3574,3577,3579,3581,3583,3585,3587,3589,3591,3593,3595,3597,3600,3602,3604,3606,3608,3610,3612,3614,\
3616,3618,3620,3622,3625,3627,3629,3631,3633,3635,3637,3639,3641,3643,3645,3647,3650,3652,3654,3656,3658,3660,3662,3664,3666,3668,3670,3673,3675,3677,3679,3681,\
3683,3685,3687,3689,3691,3693,3696,3698,3700,3702,3704,3706,3708,3710,3712,3714,3716,3718,3721,3723,3725,3727,3729,3731,3733,3735,3737,3739,3741,3744,3746,3748,\
3750,3752,3754,3756,3758,3760,3762,3764,3766,3769,3771,3773,3775,3777,3779,3781,3783,3785,3787,3789,3791,3794,3796,3798,3800,3802,3804,3806,3808,3810,3812,3814,\
3817,3819,3821,3823,3825,3827,3829,3831,3833,3835,3837,3839,3841,3844,3846,3848,3850,3852,3854,3856,3858,3860,3862,3864,3866,3869,3871,3873,3875,3877,3879,3881,\
3883,3885,3887,3889,3891,3893,3896,3898,3900,3902,3904,3906,3908,3910,3912,3914,3916,3918,3921,3923,3925,3927,3929,3931,3933,3935,3937,3939,3941,3943,3945,3948,\
3950,3952,3954,3956,3958,3960,3962,3964,3966,3968,3970,3972,3975,3977,3979,3981,3983,3985,3987,3989,3991,3993,3995,3997,3999,4002,4004,4006,4008,4010,4012,4014,\
4016,4018,4020,4022,4024,4026,4029,4031,4033,4035,4037,4039,4041,4043,4045,4047,4049,4051,4053,4056,4058,4060,4062,4064,4066,4068,4070,4072,4074,4076,4078,4080,4083,4085,4087,4089,4091,4093,4095

Table_4                   = \
0,9,18,27,35,44,53,62,71,80,89,97,106,115,124,133,142,151,160,168,177,186,195,204,213,222,231,240,248,257,266,275,284,293,302,311,319,328,337,346,355,364,373,381,\
390,399,408,417,426,434,443,452,461,470,478,487,496,505,514,522,531,540,549,557,566,575,583,592,601,610,619,627,636,645,654,663,671,680,689,698,707,716,724,733,\
742,751,760,768,777,786,795,804,812,821,830,839,847,856,865,874,882,891,900,908,917,926,934,943,951,960,968,977,985,994,1002,1010,1019,1027,1036,1044,1052,1060,\
1068,1077,1085,1093,1101,1109,1117,1125,1133,1141,1149,1157,1164,1172,1180,1188,1196,1203,1211,1219,1227,1234,1242,1249,1257,1265,1272,1280,1287,1295,1302,1310,\
1317,1325,1332,1339,1347,1354,1362,1369,1376,1383,1391,1398,1405,1412,1420,1427,1434,1441,1448,1455,1462,1470,1477,1484,1491,1498,1505,1512,1519,1526,1533,1540,\
1547,1554,1560,1567,1574,1581,1588,1595,1602,1609,1615,1622,1629,1636,1642,1649,1656,1663,1669,1676,1683,1689,1696,1702,1709,1716,1722,1729,1735,1742,1748,1755,\
1761,1768,1774,1781,1787,1793,1800,1806,1812,1819,1825,1831,1838,1844,1850,1856,1863,1869,1875,1881,1887,1893,1900,1906,1912,1918,1924,1930,1936,1942,1948,1954,\
1960,1966,1972,1977,1983,1989,1995,2001,2007,2012,2018,2024,2030,2035,2041,2047,2053,2058,2064,2069,2075,2081,2086,2092,2097,2103,2108,2114,2119,2125,2130,2136,\
2141,2147,2152,2157,2163,2168,2173,2179,2184,2189,2194,2200,2205,2210,2215,2220,2226,2231,2236,2241,2246,2251,2256,2261,2266,2271,2276,2281,2286,2291,2296,2300,\
2305,2310,2315,2320,2325,2329,2334,2339,2343,2348,2353,2357,2362,2366,2371,2376,2380,2385,2389,2393,2398,2402,2407,2411,2415,2420,2424,2428,2433,2437,2441,2445,\
2450,2454,2458,2462,2466,2470,2474,2479,2483,2487,2491,2495,2499,2503,2507,2511,2515,2519,2523,2527,2530,2534,2538,2542,2546,2550,2554,2557,2561,2565,2569,2573,\
2576,2580,2584,2588,2592,2595,2599,2603,2606,2610,2614,2617,2621,2625,2628,2632,2635,2639,2643,2646,2650,2653,2657,2660,2663,2667,2670,2674,2677,2680,2684,2687,\
2691,2694,2697,2701,2704,2707,2710,2714,2717,2720,2723,2727,2730,2733,2736,2739,2743,2746,2749,2752,2755,2758,2762,2765,2768,2771,2774,2777,2780,2783,2786,2790,\
2793,2796,2799,2802,2805,2808,2811,2814,2817,2820,2823,2826,2829,2832,2835,2838,2841,2844,2847,2850,2853,2856,2859,2861,2864,2867,2870,2873,2876,2879,2881,2884,\
2887,2890,2893,2895,2898,2901,2904,2907,2909,2912,2915,2918,2920,2923,2926,2928,2931,2934,2937,2939,2942,2945,2947,2950,2953,2955,2958,2961,2963,2966,2969,2971,\
2974,2977,2979,2982,2985,2987,2990,2993,2995,2998,3001,3003,3006,3009,3011,3014,3017,3019,3022,3025,3027,3030,3032,3035,3037,3040,3043,3045,3048,3050,3053,3055,\
3058,3060,3063,3065,3068,3070,3073,3075,3078,3080,3083,3085,3088,3090,3093,3095,3098,3100,3103,3105,3107,3110,3112,3115,3117,3120,3122,3124,3127,3129,3131,3134,\
3136,3139,3141,3143,3146,3148,3150,3153,3155,3157,3160,3162,3164,3167,3169,3171,3174,3176,3178,3181,3183,3185,3188,3190,3192,3195,3197,3199,3201,3204,3206,3208,\
3211,3213,3215,3217,3220,3222,3224,3226,3229,3231,3233,3235,3238,3240,3242,3244,3247,3249,3251,3253,3256,3258,3260,3262,3265,3267,3269,3271,3274,3276,3278,3280,\
3283,3285,3287,3289,3292,3294,3296,3298,3300,3303,3305,3307,3309,3312,3314,3316,3318,3320,3322,3325,3327,3329,3331,3333,3335,3338,3340,3342,3344,3346,3348,3350,\
3353,3355,3357,3359,3361,3363,3365,3367,3369,3372,3374,3376,3378,3380,3382,3384,3386,3388,3390,3392,3395,3397,3399,3401,3403,3405,3407,3409,3411,3413,3415,3417,\
3419,3421,3423,3425,3427,3429,3431,3433,3436,3438,3440,3442,3444,3446,3448,3450,3452,3454,3456,3458,3460,3462,3464,3466,3468,3470,3472,3474,3476,3478,3480,3482,\
3484,3486,3488,3490,3492,3494,3496,3498,3500,3502,3504,3507,3509,3511,3513,3515,3517,3519,3521,3523,3525,3527,3529,3531,3533,3535,3537,3539,3541,3543,3545,3547,\
3550,3552,3554,3556,3558,3560,3562,3564,3566,3568,3570,3572,3574,3577,3579,3581,3583,3585,3587,3589,3591,3593,3595,3597,3600,3602,3604,3606,3608,3610,3612,3614,\
3616,3618,3620,3622,3625,3627,3629,3631,3633,3635,3637,3639,3641,3643,3645,3647,3650,3652,3654,3656,3658,3660,3662,3664,3666,3668,3670,3673,3675,3677,3679,3681,\
3683,3685,3687,3689,3691,3693,3696,3698,3700,3702,3704,3706,3708,3710,3712,3714,3716,3718,3721,3723,3725,3727,3729,3731,3733,3735,3737,3739,3741,3744,3746,3748,\
3750,3752,3754,3756,3758,3760,3762,3764,3766,3769,3771,3773,3775,3777,3779,3781,3783,3785,3787,3789,3791,3794,3796,3798,3800,3802,3804,3806,3808,3810,3812,3814,\
3817,3819,3821,3823,3825,3827,3829,3831,3833,3835,3837,3839,3841,3844,3846,3848,3850,3852,3854,3856,3858,3860,3862,3864,3866,3869,3871,3873,3875,3877,3879,3881,\
3883,3885,3887,3889,3891,3893,3896,3898,3900,3902,3904,3906,3908,3910,3912,3914,3916,3918,3921,3923,3925,3927,3929,3931,3933,3935,3937,3939,3941,3943,3945,3948,\
3950,3952,3954,3956,3958,3960,3962,3964,3966,3968,3970,3972,3975,3977,3979,3981,3983,3985,3987,3989,3991,3993,3995,3997,3999,4002,4004,4006,4008,4010,4012,4014,\
4016,4018,4020,4022,4024,4026,4029,4031,4033,4035,4037,4039,4041,4043,4045,4047,4049,4051,4053,4056,4058,4060,4062,4064,4066,4068,4070,4072,4074,4076,4078,4080,4083,4085,4087,4089,4091,4093,4095

;;;;;;;;;;;;;;;;dynamic_nr_parameter;;;;;;;;;;;;;;; 
[dynamic_nr]
CoringRatioCount    = "2"
CoringRatioIso      = "100, 200"
CoringRatio_0       = "90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,91,92,93,94,95,96,97,98,99,100,100,100,100,100,100,100,100"
CoringRatio_1       = "50,60,70,80,90,90,90,90,90,90,90,90,90,90,90,90,91,92,93,94,95,96,97,98,99,100,100,100,100,100,100,100,100"
FrameShortStr       = " 5,10"
FrameLongStr        = " 3, 3"

[dynamic_blc]
BLCCount          = "2"
IsoThresh      = "100, 800"
R              = "258,  258"
Gr             = "256,  256"
Gb             = "256,  256"
B              = "256,  256"

[dynamic_ca]
CACount            = "4"
IsoThresh            = "300,800,1600,9400"
CAYRatioLut_0   =    \
370,393,421,454,492,533,576,621,668,715,762,808,853,895,934,969,1000,1032,1064,1095,1126,1156,1186,1214,1241,1265,1289,1309,\
1328,1344,1356,1366,1373,1377,1378,1377,1373,1367,1360,1350,1338,1326,1311,1296,1279,1261,1243,1224,1205,1185,1165,1145,1126,\
1107,1088,1070,1053,1037,1022,1008,996,985,976,969,964,959,955,952,950,948,946,945,945,945,946,947,948,950,952,954,956,959,\
962,964,967,970,973,976,979,981,984,986,988,990,991,992,993,994,995,996,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,\
1007,1008,1009,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024
CAYRatioLut_1   = \
370,393,421,454,492,533,576,621,668,715,762,808,853,895,934,969,1000,1032,1064,1095,1126,1156,1186,1214,1241,1265,1289,1309,\
1328,1344,1356,1366,1373,1377,1378,1377,1373,1367,1360,1350,1338,1326,1311,1296,1279,1261,1243,1224,1205,1185,1165,1145,1126,\
1107,1088,1070,1053,1037,1022,1008,996,985,976,969,964,959,955,952,950,948,946,945,945,945,946,947,948,950,952,954,956,959,\
962,964,967,970,973,976,979,981,984,986,988,990,991,992,993,994,995,996,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,\
1007,1008,1009,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024

CAYRatioLut_2   = \
370,393,421,454,492,533,576,621,668,715,762,808,853,895,934,969,1000,1032,1064,1095,1126,1156,1186,1214,1241,1265,1289,1309,\
1328,1344,1356,1366,1373,1377,1378,1377,1373,1367,1360,1350,1338,1326,1311,1296,1279,1261,1243,1224,1205,1185,1165,1145,1126,\
1107,1088,1070,1053,1037,1022,1008,996,985,976,969,964,959,955,952,950,948,946,945,945,945,946,947,948,950,952,954,956,959,\
962,964,967,970,973,976,979,981,984,986,988,990,991,992,993,994,995,996,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,\
1007,1008,1009,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024

CAYRatioLut_3   =    \
153,174,196,218,240,263,286,309,332,355,378,402,425,448,471,494,517,540,562,584,605,626,647,667,687,705,724,741,758,774,790,804,817,830,842,853,864,875,885,894,903,912,920,927,\
934,941,948,954,960,965,970,975,980,984,988,992,996,1000,1003,1007,1010,1013,1016,1019,1022,1024,1026,1027,1028,1028,1028,1028,1027,1026,1025,1024,1022,1021,1019,1017,1015,1013,\
1011,1008,1006,1004,1002,1000,998,997,995,994,993,993,992,992,992,992,993,993,994,994,995,996,997,998,999,1000,1001,1002,1003,1004,1006,1007,1008,1009,1011,1012,1013,1015,1016,\
1017,1018,1020,1021,1022,1023,1024

;;;;;;;;;;;;;;;;dynamic_shading_parameter;;;;;;;;;;;;;;; 
[dynamic_shading]
ExpThreshCnt              = "5"
ExpThreshLtoH             = "20000, 50000, 80000, 160000, 320000"
ManualStrength            = "512,   512,   512,   256,    0"

;;;;;;;;;;;;;;;;dynamic_LDCI_parameter;;;;;;;;;;;;;;; 
[dynamic_ldci]
EnableCount               = "2"
EnableExpThreshLtoH       = "80000,  85000" 
Enable                    = "0,      0"
ExpThreshCnt              = "5"
ExpThreshLtoH             = "20000, 50000, 80000, 160000, 320000"
ManualLDCIHePosWgt        = "4096,   2048, 1024,   256,    0"

;;;;;;;;;;;;;;;;;dynamic_FalseColor_parameter;;;;;;;;;;;;;;;;		                   
[dynamic_falsecolor]
TotalNum                  = "2"
FalsecolorExpThresh       = "20000,  50000"
ManualStrength            = "31,     31"

;;;;;;;;;;;;;;;static_3DNR_parameter;;;;;;;;;;;;;; 
[static_3dnr]
3DNRCount            = "12"
IsoThresh            = "100, 200, 400, 800, 1600,3200, 6400,8500,12800, 17000, 25600,51200"

;ISO 100
3DnrParam_0 = \
-nXsf1      20:  0: 88 |     20:  0:128 |     25:  0:128 |          40:  0:128           \
-nXsf2      20:  0: 88 |     20:  0:128 |     20:  0:128 |          20:  0:128           \
-nXsf4      20:  0: 88 |     20:  0:128 |     25:  0:128 |          20:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 64: 64 | 64: 64: 64: 64 | 64: 64: 64: 64 |120:100: 64: 64| 80: 80: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  6:  0 |  0:  0:  9:  0 |  0:  0:  9:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         10: 10 |         10: 10 |          5:  8|          5:  8\
                       |                |                |               |               \
-nXsfn       6:  2:  4 |      6:  2:  6 |      6:  2:  4 |      6:  6:  0|      6:  6:  4\
-nXsth          30: 10 |         20: 30 |         30: 20 |         10: 20|         10: 50\
-nXsthd         25:  5 |         20: 30 |         25: 10 |         10: 20|         10: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      150              \
                       |                |                | -mXmathd     100              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          0:  0 |          0     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           1     |          7: 10 |         10     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  0:  0:  0 |     16:  8: 16 |     16:  8: 16 | -sfc          60              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          20              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc          10              \
                       |      8:  0:  0 |                | -trc          30              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |         60:150 |        150     |                               \
-mXmathd               |         30:100 |        100     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 200
3DnrParam_1 = \
-nXsf1      20:  0: 88 |     20:  0:128 |     15:  0:128 |          20:  0:128           \
-nXsf2      20:  0: 88 |     30:  0:128 |     30:  0:128 |          20:  0:128           \
-nXsf4      20:  0: 88 |     30:  0:128 |     30:  0:128 |          20:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 64: 64 | 64: 64: 64: 64 | 55: 55: 55: 55 |120:100: 64: 64|120: 80: 64: 64\
-dzsf5               0 |              0 |              0 |             40|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  6:  0 |  0:  0:  9:  0 |  0:  0:  9:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         10: 10 |         10: 10 |          5: 10|          5: 10\
                       |                |                |               |               \
-nXsfn       6:  2:  4 |      6:  2:  6 |      6:  2:  5 |      6:  6:  0|      6:  6:  4\
-nXsth          30: 10 |         20: 30 |         30: 20 |         10: 20|         10: 50\
-nXsthd         25:  5 |         20: 30 |         25: 10 |         10: 20|         10: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      150              \
                       |                |                | -mXmathd     100              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          0:  0 |          0     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           1     |          7: 12 |         11     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  0:  0:  0 |     16:  8: 16 |     16:  8: 16 | -sfc          60              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          20              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc          10              \
                       |      8:  0:  0 |                | -trc          40              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |         60:150 |        150     |                               \
-mXmathd               |         30:100 |        100     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              2 |          2     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 400
3DnrParam_2 = \
-nXsf1      20:  0: 88 |     20:  0:128 |     20:  0:128 |          20:  0:128           \
-nXsf2      20:  0: 88 |     30:  0:128 |     30:  0:128 |          20:  0:128           \
-nXsf4      20:  0: 88 |     30:  0:128 |     30:  0:128 |          20:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         120:120:120           \
-nXsf5  80: 80: 40: 40 | 64: 64: 64: 64 | 56: 56: 56: 45 |100:100: 64: 80|120:100: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  9:  0 |  0:  0:  9:  0 |  0:  0:  9:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         10: 10 |         10: 10 |          5:  8|          5:  8\
                       |                |                |               |               \
-nXsfn       6:  2:  6 |      6:  2:  4 |      6:  2:  5 |      6:  6:  0|      6:  6:  4\
-nXsth          20: 40 |         40: 60 |         40: 60 |         30: 60|         20: 50\
-nXsthd         15: 20 |         20: 30 |         20: 30 |         20: 20|         20: 30\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      120              \
                       |                |                | -mXmathd      90              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        2              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          0:  0 |          2     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           1     |          8: 12 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |      8:  4:  0 |     16:  8: 16 | -sfc          60              \
             0:  0:  0 |      0:  0:  0 |      8:  0:  0 | -tfc          16              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc          12              \
                       |      8:  0:  0 |                | -trc          50              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |         60:200 |        120     |                               \
-mXmathd               |         40:150 |         90     |                               \
-mXmate                |          4:  5 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                                \
-mXmatw                |              2 |          2     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 800
3DnrParam_3 = \
-nXsf1      20:  0: 78 |     30:  0:128 |     30:  0:128 |          20:  0:128           \
-nXsf2      30:  0: 78 |     60:  0:128 |     50:  0:128 |          20:  0:128           \
-nXsf4      20:  0: 78 |     40:  0:128 |     40:  0:128 |          20:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 30: 30 | 64: 64: 64: 64 | 50: 50: 50: 50 |100:100: 64: 64| 80: 80: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  9:  0 |  0:  0:  9:  0 |  0:  0:  9:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         10: 10 |         12: 12 |          5:  8|          5:  8\
                       |                |                |               |               \
-nXsfn       6:  1:  6 |      6:  2:  6 |      6:  4:  5 |      6:  5:  4|      6:  6:  4\
-nXsth          50:100 |         50:100 |         30: 50 |         50: 50|         20: 50\
-nXsthd         25: 50 |         25: 50 |         25: 33 |         25: 25|         20: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath       80              \
                       |                |                | -mXmathd      80              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        2              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          5:  2 |          2     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           1     |          8: 13 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |     16:  8: 16 |     16:  8: 16 | -sfc         126              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          15              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc          10              \
                       |      8:  0:  5 |                | -trc          80              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |         80:280 |        220     |                               \
-mXmathd               |         50:200 |        100     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 1600
3DnrParam_4 = \
-nXsf1      36:  0: 88 |     30:  0:128 |     50:  0:128 |          30:  0:128           \
-nXsf2      40:  0: 88 |     80:  0:128 |     80:  0:128 |          30:  0:128           \
-nXsf4      30:  0: 88 |     60:  0:128 |     50:  0:128 |          20:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 64: 64 | 80: 80: 50: 50 | 44: 44: 44: 44 |110: 64:100: 64| 90: 90: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0: 10:  0 |  0:  0:  9:  0 |  0:  0: 10:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         10: 10 |         10: 10 |          8: 10|          5: 10\
                       |                |                |               |               \
-nXsfn       6:  2:  6 |      6:  2:  6 |      6:  2:  5 |      6:  5:  4|      6:  6:  4\
-nXsth          50: 60 |         50: 80 |         20: 40 |         60: 50|         40: 50\
-nXsthd         25: 50 |         25: 50 |         20: 40 |         25: 40|         20: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      180              \
                       |                |                | -mXmathd     100              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          5:  1 |          1     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           1     |          8: 13 |         11     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |     16:  8: 16 |     16:  8: 16 | -sfc         190              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          18              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc           8              \
                       |      8:  0:  5 |                | -trc          80              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        100:300 |        300     |                               \
-mXmathd               |         80:200 |        150     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 3200
3DnrParam_5 = \
-nXsf1      20:  0: 28 |     30:  0:128 |     50:  0:128 |          30:  0:128           \
-nXsf2      30:  0: 28 |     80:  0:128 |     80:  0:128 |          30:  0:128           \
-nXsf4      20:  0: 28 |     40:  0:128 |     40:  0:128 |          10:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  80: 20: 64: 80 | 75: 75: 50: 50 | 50: 50: 50: 50 |120: 64: 64: 80|120:120: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0: 10:  0 |  0:  0: 15: 15 |  0:  0: 15: 15 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         15: 15 |         15: 15 |         10: 15|         10: 15\
                       |                |                |               |               \
-nXsfn       6:  2:  6 |      6:  2:  6 |      6:  6:  5 |      5:  6:  0|      6:  6:  4\
-nXsth          50:100 |         50: 80 |         30: 40 |        100: 50|         30: 70\
-nXsthd         25: 50 |         25: 20 |         20: 40 |         25: 40|         30: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      200              \
                       |                |                | -mXmathd     120              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          2:  2 |          2     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           3     |          8: 13 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |     16:  8: 16 |     16:  8: 16 | -sfc         250              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          32              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc           8              \
                       |      8:  0:  5 |                | -trc          80              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        150:330 |        330     |                               \
-mXmathd               |         70:250 |        200     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 6400
3DnrParam_6 = \
-nXsf1      36:  0:  0 |     48:  0:128 |     32:  0:128 |          32:  0:128           \
-nXsf2      40:  0:  0 |    100:  0:128 |    100:  0:128 |          32:  0:128           \
-nXsf4      36:  0:  0 |     48:  0:128 |     45:  0:128 |          20:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  40: 80: 64: 80 | 75: 75: 50: 64 | 45: 45: 45: 45 |120:100: 64: 64|120:120: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  6:  0 |  0:  0:  6:  0 |  0:  0: 15:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         15: 15 |         15: 15 |         15: 15 |          8: 10|          8: 10\
                       |                |                |               |               \
-nXsfn       6:  2:  4 |      6:  2:  6 |      6:  2:  5 |      5:  6:  4|      6:  0:  4\
-nXsth          60:100 |         45: 70 |         45: 35 |         62: 60|         30: 60\
-nXsthd         40: 80 |         33: 58 |         28: 25 |         46: 50|         23: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      220              \
                       |                |                | -mXmathd     120              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          5:  2 |          2     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           2     |          8: 14 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  4:  0:  4 |     16:  8: 16 |     16:  8: 16 | -sfc         200              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          32              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc           8              \
                       |      8:  0:  5 |                | -trc          60              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        150:420 |        420     |                               \
-mXmathd               |         64:300 |        350     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;ISO 8500
3DnrParam_7 = \
-nXsf1      36:  0:  0 |     48:  0:128 |     32:  0:128 |          32:  0:128           \
-nXsf2      40:  0:  0 |     65:  0:128 |     60:  0:128 |          32:  0:128           \
-nXsf4      36:  0:  0 |     48:  0:128 |     45:  0:128 |          10:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  40: 80: 64: 80 | 75: 75: 50: 64 | 45: 45: 45: 45 |120:120: 64: 64|120:120: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  6:  0 |  0:  0:  9:  0 |  0:  0: 15:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         15: 15 |         15: 15 |         15: 15 |          8: 12|          6: 12\
                       |                |                |               |               \
-nXsfn       6:  2:  4 |      6:  2:  6 |      6:  2:  5 |      6:  0:  4|      6:  5:  4\
-nXsth          60:100 |         45: 70 |         45: 35 |         62: 60|         50: 80\
-nXsthd         40: 80 |         33: 58 |         28: 25 |         46: 50|         23: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      220              \
                       |                |                | -mXmathd     120              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          2:  2 |          1     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           3     |         10: 14 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  4:  0:  4 |     16:  8: 16 |     16:  8: 16 | -sfc         200              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          24              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc           8              \
                       |      8:  0:  5 |                | -trc         100              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        150:500 |        420     |                               \
-mXmathd               |         64:300 |        220     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 12800
3DnrParam_8 = \
-nXsf1      36:  0:  0 |     48:  0:128 |     32:  0:128 |          32:  0:128           \
-nXsf2      40:  0:  0 |    100:  0:128 |    102:  0:128 |          32:  0:128           \
-nXsf4      36:  0:  0 |     48:  0:128 |     48:  0:128 |          10:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 64: 64 | 64: 64: 64: 64 | 40: 40: 40: 40 |120:120: 64: 64|120:100: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  9:  0 |  0:  0: 11:  0 |  0:  0: 11:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         15: 15 |         15: 15 |         15: 15 |         10: 10|          5: 10\
                       |                |                |               |               \
-nXsfn       6:  2:  6 |      6:  2:  6 |      6:  2:  5 |      6:  4:  0|      6:  6:  4\
-nXsth          40: 60 |         40: 60 |         30: 50 |         30: 64|         50: 50\
-nXsthd         30: 50 |         20: 30 |         20: 40 |         20: 48|         25: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      220              \
                       |                |                | -mXmathd     220              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          5:  1 |          1     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           1     |          9: 14 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |     16:  8: 16 |     16:  8: 16 | -sfc         230              \
             0:  0:  0 |      8:  0: 31 |      8:  0:  0 | -tfc          22              \
-nXtfr1 (2)            |     26: 18: 16 |                | -tpc           8              \
                       |     22:  0:  5 |                | -trc          60              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        200:450 |        300     |                               \
-mXmathd               |        100:400 |        220     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          5:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 17000
3DnrParam_9 = \
-nXsf1      36:  0:  0 |     68:  0:128 |     32:  0:128 |          32:  0:128           \
-nXsf2      50:  0:  0 |     68:  0:128 |     32:  0:128 |          32:  0:128           \
-nXsf4      36:  0:  0 |     68:  0:128 |     32:  0:128 |          10:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 64: 64 | 64: 64: 64: 64 | 40: 40: 40: 40 |150:120: 64: 64|160:120:100: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  6:  0 |  0:  0:  9:  0 |  0:  0:  9:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         15: 15 |         15: 15 |         15: 15|          5: 10\
                       |                |                |               |               \
-nXsfn       6:  2:  6 |      6:  2:  6 |      6:  2:  5 |      6:  4:  0|      6:  4:  4\
-nXsth          40: 60 |         40: 60 |         30: 50 |         30: 64|         30: 40\
-nXsthd         30: 50 |         20: 30 |         20: 40 |         20: 48|         20: 30\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      220              \
                       |                |                | -mXmathd     220              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          5:  1 |          1     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           3     |          8: 14 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |     16:  8: 16 |     16:  8: 16 | -sfc         225              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          22              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc           8              \
                       |      8:  0:  5 |                | -trc         200              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        150:460 |        320     |                               \
-mXmathd               |         64:400 |        200     |                               \
-mXmate                |          2:  5 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

;ISO 25600
3DnrParam_10 = \
-nXsf1      36:  0:  0 |     68:  0:128 |     32:  0:128 |          32:  0:128           \
-nXsf2      80:  0:  0 |     88:  0:128 |     64:  0:128 |          32:  0:128           \
-nXsf4      36:  0:  0 |     68:  0:128 |     32:  0:128 |          15:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 64: 64 | 64: 64: 64: 64 | 44: 44: 44: 44 |150:120: 64: 64|120:120: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  6:  0 |  0:  0:  9:  0 |  0:  0:  9:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         10: 10 |         15: 15 |         15: 15 |         15: 15|          5: 10\
                       |                |                |               |               \
-nXsfn       6:  2:  6 |      6:  2:  6 |      6:  2:  5 |      6:  4:  0|      6:  6:  4\
-nXsth          40: 60 |         40: 60 |         30: 50 |         30: 64|         30: 80\
-nXsthd         30: 50 |         20: 30 |         20: 40 |         20: 48|         20: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      220              \
                       |                |                | -mXmathd     220              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          2:  2 |          2     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           4     |          8: 14 |         12     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |     16:  8: 16 |     16:  8: 16 | -sfc         225              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          25              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc          10              \
                       |      8:  0:  5 |                | -trc         230              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        150:500 |        360     |                               \
-mXmathd               |         64:450 |        220     |                               \
-mXmate                |          4:  4 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;


;ISO 51200
3DnrParam_11 = \
-nXsf1      36:  0:  0 |     68:  0:128 |     32:  0:128 |          32:  0:128           \
-nXsf2      80:  0:  0 |     99:  0:128 |    100:  0:128 |          32:  0:128           \
-nXsf4      36:  0:  0 |     68:  0:128 |     32:  0:128 |          16:  0:128           \
-SelRt          16: 16 |                | -kmode       0 |                   0           \
-DeRt            0:  4 |                |                |                               \
-sfs5                  |                |                |         128:128:128           \
-nXsf5  64: 64: 64: 64 | 64: 64: 64: 64 | 64: 64: 64: 64 |140:120: 80: 64|150:120: 64: 64\
-dzsf5               0 |              0 |              0 |              0|              0\
-nXsf6   4:  2:  0:  4 |  4:  2:  0:  4 |  4:  2:  0:  4 |  1:  5:  0:  4|  1:  5:  0:  4\
-nXsfr6  0:  0:  9:  0 |  0:  0: 10:  0 |  0:  0: 10:  0 | 22: 22:  0:  0| 22: 22:  0:  0\
-nXsbr6         12: 12 |         12: 12 |         12: 12 |         10: 15|          5: 10\
                       |                |                |               |               \
-nXsfn       6:  2:  6 |      6:  2:  6 |      6:  2:  4 |      6:  4:  4|      6:  4:  4\
-nXsth          40: 60 |         40: 60 |         30: 50 |         30: 64|         30: 30\
-nXsthd         30: 50 |         20: 30 |         20: 40 |         20: 48|         20: 20\
-sfr    (0)     31     |         31     |         31     |         31    |         31    \
                       |                |                |                               \
-ref             1     |          1     |                |                               \
-tedge                 |          0     |          0     | -mXmath      220              \
                       |                |                | -mXmathd     220              \
-nXstr  (1)     31     |         31: 31 |         31     | -mXmate        4              \
-nXsdz           0     |          0:  0 |          0     | -mXmabw        5              \
                       |                |                |                               \
-nXtss          15     |          2:  2 |          1     |                               \
-nXtsi           1     |          1:  1 |          1     |                               \
-nXtfs           4     |          8: 14 |         13     |                               \
-nXtdz  (3)      0     |          0:  0 |          0     |**************NRc**************\
-nXtdx           2     |          2:  2 |          2     | -mode          0              \
-nXtfrs         15     |                |                | -presfc        0              \
-nXtfr0 (2)  3:  0:  5 |     16:  8: 16 |     16:  8: 16 | -sfc         225              \
             0:  0:  0 |      8:  0:  0 |      8:  0:  0 | -tfc          15              \
-nXtfr1 (2)            |     16:  8: 16 |                | -tpc          10              \
                       |      8:  0:  5 |                | -trc         130              \
                       |                |                |                               \
-mXid0                 |      1:  1:  2 |      1:  1:  2 |                               \
-mXid1                 |      2:  2:  2 |                |                               \
-mXmabr                |          0:  0 |          0     |                               \
-AdvMath               |          1     |                |                               \
-AdvTh                 |          0     |                |                               \
-mXmath                |        150:500 |        320     |                               \
-mXmathd               |         64:450 |        220     |                               \
-mXmate                |          4:  5 |          4     |                               \
-mXmabw                |          4:  9 |          5     |                               \
-mXmatw                |              3 |          3     |                               \
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
[dynamic_vencbitrate]
IsoThreshCnt              = "6"
IsoThreshLtoH             = "100, 5000, 7000,  15000, 30000, 50000"
ManualPercent            = "90, 90, 80, 75, 75, 75 "