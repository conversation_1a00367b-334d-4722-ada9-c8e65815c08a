#
# Driver makefile template
#

CFLAGS	:=
.PHONY: clean default


ifeq ($(PLATFORM), SSC335)
export EXTRA_CFLAGS	= -DPLATFORM_SSC335
export KERNEL_SRC_DIR = $(SDK_SIGMASTAR_SSC335)/kernel
endif

ifeq ($(PLATFORM),$(findstring $(PLATFORM),RV1126 RV1126_REBUILD))
export EXTRA_CFLAGS	= -DPLATFORM_RV1126
export KERNEL_SRC_DIR = $(SDK_ROCKCHIP_RV1126)/kernel
endif

ifeq ($(PLATFORM),$(findstring $(PLATFORM),NT98539))
export EXTRA_CFLAGS	= -DPLATFORM_NT98539
export KERNEL_SRC_DIR = $(SDK_NOVATEK_NT98539)/ns02302_linux_sdk/BSP/linux-kernel
endif

ifeq ($(PLATFORM), RV1106)
EXTRA_CFLAGS	+= -DPLATFORM_RV1106
export KERNEL_SRC_DIR = $(SDK_ROCKCHIP_RV1106)/sysdrv/source/kernel
endif

$(warning EXTRA_CFLAGS = $(EXTRA_CFLAGS))
OUTPUT_KO:= $(subst .o,.ko,$(obj-m))

ifndef OUTPUT_KO_DIR
OUTPUT_KO_DIR=$(KO_DVR_PATH)
endif

ifndef BOARD
perr:
	@$(ECHO) --------------------------------------
	@$(ECHO) please use command: "make menu"
	@$(ECHO) --------------------------------------
endif

ifdef IGNORE_THIS_BUILD
ignore:
	@$(ECHO) [ignore build] module:$(IGNORE_THIS_BUILD) plaform:$(PLATFORM) path:$(PWD) 
endif

default:	
	$(MAKE) -C $(KERNEL_SRC_DIR) M=$(PWD) modules
	@$(MKDIR) -p $(OUTPUT_KO_DIR)
	$(CP) $(OUTPUT_KO) $(OUTPUT_KO_DIR)
	
clean:
ifdef IGNORE_THIS_BUILD
	@$(ECHO) [ignore clean] module:$(IGNORE_THIS_BUILD) plaform:$(PLATFORM) path:$(PWD) 
else
	$(MAKE) -C $(KERNEL_SRC_DIR) M=$(PWD) clean
	$(RM) $(OUTPUT_KO_DIR)/$(OUTPUT_KO)
	$(RM) $(MAKE_LOG_FILE)
endif

include $(BUILD_MK)

# vim:noet:sw=4:ts=4

