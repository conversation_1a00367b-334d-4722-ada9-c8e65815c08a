/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：media.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-11-21

文件功能描述: 定义视频模块公用功能函数

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <fcntl.h>
#include <pthread.h>
#include <errno.h>

#include "board.h"
#include "common.h"
#include "print.h"
#include "safefunc.h"
#include "mpp_com.h"
#include "mpp_sys.h"
#include "mpp_vi.h"
#include "mpp_vpss.h"
#include "mpp_venc.h"
#include "mpp_vmix.h"
#include "mpp_vo.h"
#include "mpp_font.h"
#include "mpp_vosd.h"
#include "mpp_aio.h"
#include "mpp_aenc.h"
#include "media.h"
#include "../../include/board.h"
#include "msg.h"
#include "utils.h"

#if (PLATFORM == PLATFORM_SSC335)
#if (BOARD == BOARD_IPCR20S5)
#include "isp/mi_isp.h"
#else
#include "isp/ispahan/mi_isp.h"
#endif
#endif

#if (PLATFORM == PLATFORM_RV1126)
#include "mpp_vdec.h"
#endif

#if (PLATFORM == PLATFORM_RV1106)
#include "rk_comm_vpss.h"
#endif

#if (BOARD == BOARD_ADA32IR)
#include "mpp_ir.h"
#endif

#define MEDIA_VENC_RECREATE_DELAY   100      /* VENC重建通道延时时间(ms)，销毁VECN通道后需要延时一定时间再重建，不然可能会出问题 */

/* 媒体模块控制信息 */
typedef struct tagMediaCtrlInfo_S
{
    TypeMode_S      stTypeMode;     /* 媒体层类型和模式集合 */
    uint32          u32ChnNum;      /* 总通道数目 */
    SV_SIZE_S       stSrcSize;      /* 视频源画面大小 (宽高) */
    SV_SIZE_S       stPriVencSize;  /* 视频主码流编码大小 (宽高) */
    MEDIA_IMAGE_S   stCustomImage;  /* 用户自定义图像参数 */
} MEDIA_CTRL_INFO_S;

MEDIA_CTRL_INFO_S m_stMediaInfo = {0};     /* 媒体模块控制信息 */


sint32 MEDIA_SYS_Init(MEDIA_INIT_S * pstInitParam)
{
    sint32 s32Ret = 0, i, s32Chn;
    MPP_VB_CONF_S   stVbConf   = {0};
    MPP_VI_CONF_S   stViConf   = {0};
    MPP_VPSS_CONF_S stVpssConf = {0};
    MPP_VENC_CONF_S stVencConf = {0};
    MPP_VMIX_CONF_S stVmixConf = {0};
    MPP_VO_CONF_S   stVoConf   = {0};
    MPP_FONT_CONF_S stFontConf = {0};
    MPP_VOSD_CONF_S stVosdConf = {0};
    MPP_AIO_CONF_S  stAiConf   = {0};
    MPP_AO_CONF_S   stAoConf   = {0};
    MPP_AENC_CONF_S stAencConf = {0};
#if (PLATFORM == PLATFORM_RV1126)
    MPP_VDEC_CONF_S stVdecConfig = {0};
#endif
    uint32 u32OsdChnNum = 0;
    uint32 u32OsdSkipN = 0;

    if (NULL == pstInitParam)
    {
        return ERR_NULL_PTR;
    }

#if USING_MPP_CTRL
    return mpp_ctrl_sys_Init(pstInitParam);
#endif

    print_level(SV_INFO, "DevType:%d, Sensor:%d, ADC:%d, VideoMode:%d, WDRMode:%d, ChnNum:%d, AencType:%d, AudioSam:%d.\n", \
                pstInitParam->stTypeMode.enDevType, pstInitParam->stTypeMode.enSenChipType, pstInitParam->stTypeMode.enAdChipType, \
                pstInitParam->stTypeMode.enVideoMode, pstInitParam->stTypeMode.enSysWDRMode, pstInitParam->u32ChnNum, \
                pstInitParam->enAencType, pstInitParam->enAudioSampleRate);

    if (pstInitParam->stTypeMode.enDevType >= DEV_TYPE_BUTT ||
        pstInitParam->stTypeMode.enSenChipType >= SEN_TYPE_BUTT ||
        pstInitParam->stTypeMode.enAdChipType >= AD_TYPE_BUTT ||
        pstInitParam->stTypeMode.enVideoMode >= VIDEO_MODE_BUTTE ||
        pstInitParam->stTypeMode.enSysWDRMode >= WDR_MODE_BUTTT ||
        pstInitParam->u32ChnNum > VIODE_MAX_CHN ||
        pstInitParam->enAencType >= AUDIO_ENCODE_BUTT ||
        pstInitParam->enAudioSampleRate >= AUD_SR_BUTT)
    {
        print_level(SV_ERROR, "Invalid Param!\n");
        return ERR_ILLEGAL_PARAM;
    }

    if (strlen(pstInitParam->szChnName) > VOSD_MAX_CHNNAME_LEN)
    {
        print_level(SV_ERROR, "ChnName exceed max len: %d\n", VOSD_MAX_CHNNAME_LEN);
        return ERR_ILLEGAL_PARAM;
    }

#if (BOARD == BOARD_IPCR20S3 || BOARD == BOARD_IPCR20S4 || BOARD == BOARD_WFCR20S2)
    /* Sigmastar sensor初始化 */
    if (pstInitParam->stTypeMode.enSenChipType == SEN_TYPE_GC2093)
    {
    #if (BOARD == BOARD_IPCR20S3)
        SAFE_System("rmmod drv_ms_cus_gc2093_hdr_MIPI", NORMAL_WAIT_TIME);
    #endif
        print_level(SV_DEBUG, "Set Galaxy GC2093 WDR Mode.\n");
        pstInitParam->stTypeMode.enSysWDRMode = WDR_MODE_ENABLE;
        SAFE_System("insmod /root/ko/gc2093_hdr_MIPI.ko chmap=1 lane_num=2 hdr_lane_num=2", NORMAL_WAIT_TIME);
    }
    else if (pstInitParam->stTypeMode.enSenChipType == SEN_TYPE_GC2053)
    {
    #if (BOARD == BOARD_IPCR20S3)
        SAFE_System("rmmod drv_ms_cus_gc2053_MIPI", NORMAL_WAIT_TIME);
    #endif
        print_level(SV_DEBUG, "Set Galaxy GC2053 Linear Mode.\n");
        pstInitParam->stTypeMode.enSysWDRMode = WDR_MODE_DISABLE;
        SAFE_System("insmod /root/ko/gc2053_MIPI.ko chmap=1 lane_num=2 hdr_lane_num=2", NORMAL_WAIT_TIME);
    }
#endif

    s32Ret = mpp_sys_Init(&stVbConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA47V1)
    /* 初始化红外灯状态 */
    mpp_sys_UpdateLED_Status(pstInitParam->u32LedBright);
#endif

    /* 初始化IRCUT状态 */
    mpp_sys_UpdateIRCUT_Status(pstInitParam->u32IRcutMode);

    /* 初始化VI模块 */
#if (BOARD == BOARD_IPCR20S5)
    stViConf.bPreInited = pstInitParam->stPriVencAttr.bPreInited;
#endif
    stViConf.stTypeMode = pstInitParam->stTypeMode;
    stViConf.u32ChnNum  = pstInitParam->u32ChnNum;
#if (BOARD == BOARD_IPCR20S3 || BOARD == BOARD_IPCR20S4)
    stViConf.u32Fps = (VIDEO_MODE_PAL == stViConf.stTypeMode.enVideoMode ? 25 : 30);
#else
    stViConf.u32Fps = pstInitParam->stVoAttr.u32FrmRate;
#endif
    stViConf.bRemoveFishEye = pstInitParam->bRemoveFishEye;
    stViConf.bRefScene = pstInitParam->bRefScene;
    s32Ret = mpp_vi_Init(&stViConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 1106在VI层设置镜像翻转 */
#if (BOARD == BOARD_DMS31V2 || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 || BOARD == BOARD_HDW845V1 || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA32C4)
    for (i = 0; i < stViConf.u32ChnNum; i++)
    {
        s32Ret = MEDIA_SYS_SetChnMirrorFlip(i, pstInitParam->bImageMirror, pstInitParam->bImageFlip);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vi_SetChnMirrorFlip failed.\n");
        }
    }
#endif

#if (BOARD == BOARD_IPCR20S5)
    stVpssConf.bPreInited = pstInitParam->stPriVencAttr.bPreInited;
#endif
    stVpssConf.enSenChipType = pstInitParam->stTypeMode.enSenChipType;
    stVpssConf.enVideoMode = pstInitParam->stTypeMode.enVideoMode;
    stVpssConf.enSysWDRMode = pstInitParam->stTypeMode.enSysWDRMode;
    stVpssConf.u32ChnNum = pstInitParam->u32ChnNum;
    stVpssConf.bImageMirror = pstInitParam->bImageMirror;
    stVpssConf.bImageFlip = pstInitParam->bImageFlip;

#if (BOARD == BOARD_IPCR20S3 || BOARD == BOARD_IPCR20S4 || BOARD == BOARD_IPCR20S5 || BOARD == BOARD_WFCR20S2 || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1|| BOARD == BOARD_ADA32C4)
    stVpssConf.enRotateAngle = pstInitParam->enRotateAngle;
#endif

    stVpssConf.u32MaxH = pstInitParam->stSrcSize.u32Height;
    stVpssConf.u32MaxW = pstInitParam->stSrcSize.u32Width;

    print_level(SV_DEBUG, "srcW = %d, srcH = %d.\n", pstInitParam->stSrcSize.u32Width, pstInitParam->stSrcSize.u32Height);

#if ((PLATFORM == PLATFORM_SSC335) || (PLATFORM == PLATFORM_RV1126) || (PLATFORM == PLATFORM_RV1106))
    stVpssConf.stPriVencSize.u32Width = pstInitParam->stPriVencAttr.u32PicWidth;
    stVpssConf.stPriVencSize.u32Height = pstInitParam->stPriVencAttr.u32PicHeight;
    stVpssConf.stSubVencSize.u32Width = pstInitParam->stSecVencAttr.u32PicWidth;
    stVpssConf.stSubVencSize.u32Height = pstInitParam->stSecVencAttr.u32PicHeight;
    stVpssConf.stJpegVencSize.u32Width = pstInitParam->stJpegVencAttr.u32PicWidth;
    stVpssConf.stJpegVencSize.u32Height = pstInitParam->stJpegVencAttr.u32PicHeight;
    stVpssConf.stExtscreenSize.u32Width = pstInitParam->stExtScreenAttr.u32OutWidth;
    stVpssConf.stExtscreenSize.u32Height = pstInitParam->stExtScreenAttr.u32OutHeight;
#endif

#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    for(i = 0; i < pstInitParam->u32ChnNum; i ++)
    {
        stVpssConf.enAlgType[i] = pstInitParam->stExtern[i].enAlgType;
    }
#endif

    stVpssConf.pfYuvCallback = pstInitParam->pfYuvCallback;
    stVpssConf.pfRgbCallback = pstInitParam->pfRgbCallback;
    stVpssConf.pfBufCallback = pstInitParam->pfBufCallback;
    stVpssConf.bShowChn[0]   = pstInitParam->stOverlay.stGui.bShowChn[0];
    stVpssConf.bShowChn[1]   = pstInitParam->stOverlay.stGui.bShowChn[1];
    stVpssConf.bShowChn[2]   = pstInitParam->stOverlay.stGui.bShowChn[2];
    stVpssConf.bShowChn[3]   = pstInitParam->stOverlay.stGui.bShowChn[3];
    stVpssConf.bAlgSingalFrame = pstInitParam->bAlgSingalFrame;

    s32Ret = mpp_vpss_Init(&stVpssConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    /* 设置VPSS网络状态 */
    for (i = 0; i < NETWORK_TYPE_BUTT; i++)
    {
        MEDIA_VPSS_SetNetworkState(&pstInitParam->stNetworkStat[i]);
    }

#if (BOARD != BOARD_ADA32N1 && BOARD != BOARD_ADA32E1)
    if(SV_TRUE != pstInitParam->stPriVencAttr.bPreInited)
    {
        for (i = 0; i < pstInitParam->u32ChnNum; i++)
        {
            s32Ret = mpp_sys_ViVpssBind(i, i);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_sys_ViVpssBind failed! [err=%#x]\n", s32Ret);
                return s32Ret;
            }
        }
    }
#endif

#if (BOARD == BOARD_ADA32IR)
    MPP_IR_CONF_S stIRConfig = {0};
    stIRConfig.bImageMirror = pstInitParam->stIrMediaCfg.bImageMirror;
    stIRConfig.bImageFlip = pstInitParam->stIrMediaCfg.bImageFlip;
    stIRConfig.s32IrShutterInr = pstInitParam->stIrMediaCfg.s32IrShutterInr;
    stIRConfig.stSrcVideoSize.u32Width = 1920;
    stIRConfig.stSrcVideoSize.u32Height = 1080;
    stIRConfig.u32ViFrmRate = pstInitParam->stPriVencAttr.u32ViFrmRate;
    stIRConfig.u32Bitrate = pstInitParam->stPriVencAttr.u32Bitrate;
    stIRConfig.enVoSplitMode = pstInitParam->enVoSplitMode;
    stIRConfig.pfStatCallback = pstInitParam->pfStatCallback;
    stIRConfig.pfRgbCallback = pstInitParam->pfRgbCallback;
    stIRConfig.s32ExtscreenWidth = pstInitParam->stExtScreenAttr.u32OutWidth;
    stIRConfig.s32ExtscreenHeight = pstInitParam->stExtScreenAttr.u32OutHeight;
    s32Ret = mpp_ir_Init(&stIRConfig);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif


#if (BOARD == BOARD_DMS31V2 \
    || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || BOARD == BOARD_ADA32C4)
    /* 初始化VO模块 */
    stVoConf.u32ChnNum = pstInitParam->u32ChnNum;
#if ((BOARD == BOARD_ADA32IR))
    stVoConf.u32ChnNum = pstInitParam->u32ChnNum + 1;
#endif
#if (BOARD != BOARD_ADA32V3)
    stVoConf.stInSize.u32Width   = 1920;
    stVoConf.stInSize.u32Height  = 1080;
#else
    stVoConf.stInSize.u32Width   = 1280;
    stVoConf.stInSize.u32Height  = 720;
#endif
    stVoConf.stOutSize.u32Width  = pstInitParam->stVoAttr.u32OutWidth;
    stVoConf.stOutSize.u32Height = pstInitParam->stVoAttr.u32OutHeight;
    stVoConf.u32Fps              = pstInitParam->stVoAttr.u32FrmRate;
    stVoConf.enRotateAngle       = pstInitParam->enRotateAngle;
    stVoConf.bOsdEnable          = pstInitParam->stOverlay.stGui.bShowChn[MPP_VPSS_CHN_VO];
#if (BOARD == BOARD_ADA32V2 \
    || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 || BOARD == BOARD_ADA47V1 \
    || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || BOARD == BOARD_ADA32C4)
    for(i = 0; i < MEDIA_CHNNUM; i++)
    {
        memcpy(&stVoConf.stAlgExtern[i], &pstInitParam->stExtern[i], sizeof(VALG_EXTERN_S));
    }
    stVoConf.enSplitMode = pstInitParam->enSplitMode;
#endif
#if (BOARD != BOARD_ADA47V1 && BOARD != BOARD_ADA32N1 && BOARD != BOARD_ADA32E1)
    s32Ret = mpp_vo_Init(&stVoConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
#endif

#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    stVdecConfig.u32Width = 1920;
    stVdecConfig.u32Height = 1080;
    stVdecConfig.u32ChnNum = pstInitParam->u32ChnNum;
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RK_MPI_SYS_Init failed! [err=%#x]\n", s32Ret);
    }

    s32Ret = mpp_vdec_Init(&stVdecConfig);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vdec_Init failed! [err=%#x]\n", s32Ret);
    }
#endif

    /* 初始化VENC模块 */
    stVencConf.u32ChnNum = pstInitParam->u32ChnNum;
    stVencConf.enVideoMode = pstInitParam->stTypeMode.enVideoMode;
    stVencConf.enSysWDRMode = pstInitParam->stTypeMode.enSysWDRMode;
    stVencConf.stPriVencAttr = pstInitParam->stPriVencAttr;
    stVencConf.stSecVencAttr = pstInitParam->stSecVencAttr;
    stVencConf.stJpegVencAttr = pstInitParam->stJpegVencAttr;
    stVencConf.pfDataCallback = pstInitParam->pfDataCallback;

    s32Ret = mpp_venc_Init(&stVencConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    print_level(SV_DEBUG, "mpp_venc_Init success!\n");

    /* 初始化VOSD模块 */
    strcpy(stFontConf.szFontFile, MEDIA_FONT_FILE_PATH);

    s32Ret = mpp_font_Init(&stFontConf);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_font_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    stVosdConf.bKeyAuth = pstInitParam->bKeyAuth;
    stVosdConf.u32ChnNum = pstInitParam->u32ChnNum;
    stVosdConf.u32PriHeight = pstInitParam->stPriVencAttr.u32PicHeight;
    stVosdConf.u32SecHeight = pstInitParam->stSecVencAttr.u32PicHeight;
    stVosdConf.u32JpegHeight = pstInitParam->stJpegVencAttr.u32PicHeight;
    stVosdConf.stOverlay.bShowTime = pstInitParam->stOverlay.bShowTime;
    stVosdConf.stOverlay.bShowChnName = pstInitParam->stOverlay.bShowChnName;
    stVosdConf.stOverlay.bShowCamera = pstInitParam->stOverlay.bShowCamera;
    stVosdConf.stOverlay.bShowZoom = pstInitParam->stOverlay.bShowZoom;
    stVosdConf.stOverlay.enTimeFmt = pstInitParam->stOverlay.enTimeFmt;
    stVosdConf.stOverlay.stTimePos.s32X = 16;
    stVosdConf.stOverlay.stTimePos.s32Y = 16;
    stVosdConf.stOverlay.stChnNamePos.s32X = 16;//376;
    stVosdConf.stOverlay.stChnNamePos.s32Y = 58;//16;
#if (BOARD == BOARD_WFTR20S3)
    stVosdConf.stOverlay.stChnName2Pos.s32X = 16;
    stVosdConf.stOverlay.stChnName2Pos.s32Y = 100;
#endif
    stVosdConf.stOverlay.stStatePos.s32X = 16;
    stVosdConf.stOverlay.stStatePos.s32Y = 100;
    stVosdConf.stOverlay.stCameraPos.s32X = 1128;
    stVosdConf.stOverlay.stCameraPos.s32Y = 48;
    stVosdConf.stOverlay.bShowBattery = SV_FALSE;
    stVosdConf.stOverlay.bShowSingnal = SV_FALSE;
    stVosdConf.stOverlay.enBatteryStat = BATTERY_CHARGEING;
    stVosdConf.stOverlay.enSingnalStat = SINGNAL_LEVEL1;
    for (i = 0; i < 4; i++)
    {
        stVosdConf.stOverlay.stGui.bShowChn[i] = pstInitParam->stOverlay.stGui.bShowChn[i];
        stVosdConf.stOverlay.stGui.u32Width[i] = pstInitParam->stOverlay.stGui.u32Width[i];
        stVosdConf.stOverlay.stGui.u32Height[i] = pstInitParam->stOverlay.stGui.u32Height[i];
    }
    stVosdConf.stOverlay.enlang = pstInitParam->stOverlay.enlang;
    strcpy(stVosdConf.szChnName, pstInitParam->szChnName);
#if (BOARD == BOARD_IPCR20S3)
    stVosdConf.stOverlay.bShowState = pstInitParam->stOverlay.bShowState;
    strcpy(stVosdConf.szState, "No State");
#endif
	stVosdConf.stOverlay.enOsdPosition = pstInitParam->stOverlay.enOsdPosition;

#if ((BOARD == BOARD_DMS31V2) \
    || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || (BOARD == BOARD_ADA32IR) || (BOARD == BOARD_ADA32N1) \
    || (BOARD == BOARD_ADA47V1) || (BOARD == BOARD_ADA900V1) || (BOARD == BOARD_HDW845V1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    strcpy(stVosdConf.szWifiName, pstInitParam->szWifiName);
    strcpy(stVosdConf.szHardwareVer, pstInitParam->szHardwareVer);
    strcpy(stVosdConf.szFirmwareVer, pstInitParam->szFirmwareVer);
    strcpy(stVosdConf.szPdsCanid, pstInitParam->szPdsCanid);
    sprintf(stVosdConf.szVoFormat, "%s%d", (pstInitParam->stVoAttr.u32OutHeight == 720 ? "720P" : "1080P"), pstInitParam->stVoAttr.u32FrmRate);
    sprintf(stVosdConf.szExternScreenFormat, "%s", pstInitParam->stExtScreenAttr.u32OutHeight == 480 ? "NTSC" : "PAL");
    stVosdConf.bYuvProtocol = pstInitParam->bYuvProtocol;
    stVosdConf.bAutoCalibration = pstInitParam->bAutoCalibration;
    for(i = 0; i < MEDIA_CHNNUM; i++)
    {
        memcpy(&stVosdConf.stAlgExtern[i], &pstInitParam->stExtern[i], sizeof(VALG_EXTERN_S));
    }
#endif

#if (BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32C4)
    stVosdConf.enRotationAngle = pstInitParam->enRotateAngle;
#endif

#if ((BOARD == BOARD_IPCR20S3) || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) \
    || (BOARD == BOARD_ADA32IR) || (BOARD == BOARD_ADA32N1) || (BOARD == BOARD_ADA47V1) || (BOARD == BOARD_HDW845V1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    stVosdConf.stOverlay.bShowRoIcon = pstInitParam->stOverlay.bShowRoIcon;
#endif

#if ((BOARD == BOARD_DMS31V2))
    stVosdConf.stOverlay.bShowCsrIcon = pstInitParam->stOverlay.bShowCsrIcon;
#endif

    stVosdConf.stOverlay.bShowAlarmIcon = pstInitParam->stOverlay.bShowAlarmIcon;

    stVosdConf.stOverlay.bShowCrosshairIcon1 = pstInitParam->bPdCrosshairIcon1;

    stVosdConf.stOverlay.bShowCrosshairIcon2 = pstInitParam->bPdCrosshairIcon2;

#if ((BOARD == BOARD_ADA32IR))
    stVosdConf.u32ChnNum += 1;
#endif

    s32Ret = mpp_vosd_Init(&stVosdConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    for(s32Chn = 0; s32Chn < pstInitParam->u32ChnNum; s32Chn++)
    {
        u32OsdSkipN = 1;
#if ((BOARD == BOARD_WFCR20S2) || (BOARD ==  BOARD_WFTR20S3))
        u32OsdChnNum = 2;           //无线发射盒主码流,子码流有OSD叠加;JPEG流无叠加
#elif((BOARD == BOARD_IPCR20S3) || (BOARD == BOARD_IPCR20S4) || (BOARD == BOARD_IPCR20S5) || (BOARD ==  BOARD_IPTR20S1))
        u32OsdChnNum = 3;           //IPC平台主码流,子码流,JPEG流有OSD叠加
#elif((BOARD == BOARD_DMS31V2) || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || (BOARD == BOARD_ADA32IR) || (BOARD == BOARD_ADA32N1) || (BOARD == BOARD_ADA47V1) || (BOARD == BOARD_ADA900V1) || (BOARD == BOARD_HDW845V1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
        u32OsdChnNum = 4;           //DMS31, ADA32系列产品,ADA900系列产品 主码流,子码流,JPEG流,VO出图有OSD叠加
#else
        u32OsdChnNum = 3;           //HI3516E 平台主码流,JPEG流有OSD叠加;子码流没有OSD叠加
        u32OsdSkipN = 2;
#endif

        for (i = 0; i < u32OsdChnNum; i+=u32OsdSkipN)
        {
            s32Ret = mpp_vosd_ChnOverlayAttrSet(s32Chn, i, &stVosdConf.stOverlay);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vosd_ChnOverlayAttrSet failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
        #if ((BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || (BOARD == BOARD_ADA32IR) || (BOARD == BOARD_ADA32N1) || (BOARD == BOARD_ADA47V1) || (BOARD == BOARD_HDW845V1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
            s32Ret = mpp_vosd_RoIconAttrSet(i, &stVosdConf.stOverlay);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "mpp_vosd_RoIconAttrSet failed. [err=%#x]\n", s32Ret);
                return SV_FAILURE;
            }
        #endif
        }
    }

    /* 初始化AI模块 */
#if (BOARD == BOARD_IPCR20S5)
    stAiConf.bPreInited = pstInitParam->stPriVencAttr.bPreInited;
#endif

    stAiConf.u32ChnNum = pstInitParam->u32ChnNum;
    stAiConf.enAioMode = MPP_AIO_MODE_I2S_SLAVE;
    stAiConf.enSampleRate = pstInitParam->enAudioSampleRate;
    stAiConf.enBitWidth = MPP_AUDIO_BIT_WIDTH_16;
    stAiConf.u32Volume = pstInitParam->u32Volume;
    stAiConf.bAudioEnable = pstInitParam->bAudioEnable;
    s32Ret = mpp_ai_Init(&stAiConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ai_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA47V1)
    if (BOARD_IsNotCustomer(BOARD_C_ADA47V1_CZAEX))
    {
        /* 初始化AO模块 */
        stAoConf.u32Volume = pstInitParam->u32OutputVolume;
        s32Ret = mpp_ao_Init(&stAoConf);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_ao_Init failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif

    /* 初始化AENC模块 */
    stAencConf.u32ChnNum = pstInitParam->u32ChnNum;
    stAencConf.enAencType = pstInitParam->enAencType;
    stAencConf.enAudioSampleRate = pstInitParam->enAudioSampleRate;
    stAencConf.pfDataCallback = pstInitParam->pfDataCallback;
    s32Ret = mpp_aenc_Init(&stAencConf);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA32IR)
#if (MOUDLETYPE == MOUDLETYPE_TC933)
    s32Ret = SAFE_System("chmod 777 /etc/scene_auto/sensor_gc2053/TC933/6M_640IRFIL/da6021config.sh", 10000);
    if(s32Ret !=0 )
    {
        print_level(SV_ERROR, "Failed to set permissions\n");
        return SV_FAILURE;
    }
    SAFE_System("/etc/scene_auto/sensor_gc2053/TC933/6M_640IRFIL/da6021config.sh", 10000);
    if(s32Ret !=0 )
    {
        print_level(SV_ERROR, "Script execution failed\n");
        return SV_FAILURE;
    }
#elif ((MOUDLETYPE == MOUDLETYPE_TC639_T2) || (MOUDLETYPE == MOUDLETYPE_TC639_T6))
    s32Ret = SAFE_System("chmod 777 /etc/scene_auto/sensor_gc2053/TC933/6M_640IRFIL/da6021config.sh", 10000);
    if(s32Ret !=0 )
    {
        print_level(SV_ERROR, "Failed to set permissions\n");
        return SV_FAILURE;
    }
    SAFE_System("/etc/scene_auto/sensor_gc2053/TC933/6M_640IRFIL/da6021config.sh", 10000);
    if(s32Ret !=0 )
    {
        print_level(SV_ERROR, "Script execution failed\n");
        return SV_FAILURE;
    }
#elif (MOUDLETYPE == MOUDLETYPE_TC639_T3)
#endif
#endif


    m_stMediaInfo.stTypeMode = pstInitParam->stTypeMode;
    m_stMediaInfo.u32ChnNum = pstInitParam->u32ChnNum;
    m_stMediaInfo.stSrcSize= pstInitParam->stSrcSize;
    m_stMediaInfo.stPriVencSize.u32Width = pstInitParam->stPriVencAttr.u32PicWidth;
    m_stMediaInfo.stPriVencSize.u32Height = pstInitParam->stPriVencAttr.u32PicHeight;

    print_level(SV_DEBUG, "Media Init Success!\n");
    return SV_SUCCESS;
}

sint32 MEDIA_SYS_Fini()
{
    sint32 s32Ret = 0;

#if USING_MPP_CTRL
    return mpp_ctrl_sys_Fini();
#endif

    s32Ret = mpp_aenc_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_aenc_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_ai_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_ai_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_DMS31V2 \
    || BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32V3) || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || (BOARD == BOARD_HDW845V1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vo_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_vo_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif


#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
	}
#endif/* BOARD_ADA32IR */

    s32Ret = mpp_vosd_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_vosd_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_venc_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vi_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_vi_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_sys_Fini();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_sys_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_SYS_Start()
{
    sint32 s32Ret = 0;
#if USING_MPP_CTRL
    return mpp_ctrl_sys_Start();
#endif

    s32Ret = mpp_sys_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vosd_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vdec_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vdec_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_venc_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#endif/* BOARD_ADA32IR */

#if (BOARD == BOARD_DMS31V2 \
    || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || BOARD == BOARD_ADA32N1 || BOARD == BOARD_ADA32E1 \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4))
    s32Ret = mpp_vpss_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD != BOARD_ADA47V1 && BOARD != BOARD_ADA32N1 && BOARD != BOARD_ADA32E1)
    print_level(SV_INFO, "mpp_vo_Start+++\n");
    s32Ret = mpp_vo_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
#endif

#if (BOARD == BOARD_ADA32N1 || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vi_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#endif

    s32Ret = mpp_aenc_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }


#if (PLATFORM == PLATFORM_SSC335)
#if (BOARD != BOARD_IPCR20S5 && BOARD != BOARD_WFTR20S3 && BOARD != BOARD_IPTR20S1)
    MI_U32 u32Channel = 0;

    if(SEN_TYPE_GC2053 == m_stMediaInfo.stTypeMode.enSenChipType)
    {
        char *szIqfile = "/config/iqstonkam/gc2053_iqfile.bin";
        s32Ret = MI_ISP_API_CmdLoadBinFile(u32Channel, szIqfile, 1234);
        if(s32Ret == MI_ISP_OK)
            print_level(SV_INFO, "MI_ISP_API_CmdLoadBinFile load %s success.\n", szIqfile);
        else
            print_level(SV_ERROR, "MI_ISP_API_CmdLoadBinFile load %s failed.\n", szIqfile);
    }
    else if(SEN_TYPE_GC2093 == m_stMediaInfo.stTypeMode.enSenChipType)
    {
        char *szIqfile = "/config/iqstonkam/gc2093_api_hdr.bin";
        s32Ret = MI_ISP_API_CmdLoadBinFile(u32Channel, szIqfile, 1234);
        if(s32Ret == MI_ISP_OK)
            print_level(SV_INFO, "MI_ISP_API_CmdLoadBinFile load %s success.\n", szIqfile);
        else
            print_level(SV_ERROR, "MI_ISP_API_CmdLoadBinFile load %s failed.\n", szIqfile);
    }
#endif
#if (BOARD != BOARD_WFTR20S3 && BOARD != BOARD_IPTR20S1)
    s32Ret = MEDIA_VI_ImageParamSet(m_stMediaInfo.stCustomImage.u8Brightness, m_stMediaInfo.stCustomImage.u8Contrast, m_stMediaInfo.stCustomImage.u8Saturation, m_stMediaInfo.stCustomImage.u8Sharpness);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VI_ImageParamSet failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif
#endif

    return SV_SUCCESS;
}

sint32 MEDIA_SYS_Stop()
{
    sint32 s32Ret = 0;
#if USING_MPP_CTRL
    return mpp_ctrl_sys_Stop();
#endif

    s32Ret = mpp_vosd_Stop();
    if (0 != s32Ret)
    {
        print_level(SV_WARN, "mpp_vosd_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_aenc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_aenc_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_DMS31V2 \
    || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))

    s32Ret = mpp_vo_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
	}
#endif/* BOARD_ADA32IR */

    s32Ret = mpp_vpss_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_venc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_WARN, "mpp_venc_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_sys_Stop();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_SYS_SetViFramerate(sint32 s32Chn, uint32 u32Framerate)
{
    sint32 s32Ret;
#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    s32Ret = mpp_vi_SetFps(u32Framerate);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vi_SetFps failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_SYS_GetChnMirrorFlip(sint32 s32Chn, SV_BOOL* bMirror, SV_BOOL* bFlip)
{
    sint32 s32Ret = 0;

#if (PLATFORM == PLATFORM_SSC335)
    #if (BOARD != BOARD_WFTR20S3 && BOARD != BOARD_IPTR20S1)
    s32Ret = mpp_vi_GetChnMirrorFlip(s32Chn, bMirror, bFlip);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    #endif
#endif

    return SV_SUCCESS;
}


sint32 MEDIA_SYS_SetChnMirrorFlip(sint32 s32Chn, SV_BOOL bMirror, SV_BOOL bFlip)
{
    sint32 s32Ret = 0, i = 0;

#if (PLATFORM == PLATFORM_SSC335)
    #if (BOARD != BOARD_WFTR20S3 && BOARD != BOARD_IPTR20S1)
    s32Ret = mpp_vi_SetChnMirrorFlip(i, bMirror, bFlip);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    #else
    sleep(1);
    s32Ret = mpp_vpss_SetChnMirrorFlip(i, bMirror, bFlip);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    #endif
#elif ((PLATFORM == PLATFORM_RV1106) || (BOARD == BOARD_DMS31V2) || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32IR) || (BOARD == BOARD_ADA32N1) \
    || (BOARD == BOARD_HDW845V1) || (BOARD == BOARD_ADA47V1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
    {
        bMirror = !bMirror;
        bFlip = !bFlip;
    }
    s32Ret = mpp_vi_SetChnMirrorFlip(s32Chn, bMirror, bFlip);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#elif (PLATFORM == PLATFORM_RV1126)
    for (i = 0; i < 1; i++)
    {
        s32Ret = mpp_vpss_SetChnMirrorFlip(i, bMirror, bFlip);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

#else
    for (i = 0; i < 2; i++)
    {
        s32Ret = mpp_vpss_SetChnMirrorFlip(i, bMirror, bFlip);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#endif

    return SV_SUCCESS;
}

sint32 MEDIA_SYS_SetIrMirrorFlip(SV_BOOL bMirror, SV_BOOL bFlip)
{
    sint32 s32Ret = 0;
#if (BOARD == BOARD_ADA32IR)
        s32Ret = mpp_ir_SetChnMirrorFlip(bMirror, bFlip);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_ir_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VI_SetSnrFps(uint32 u32Fps)
{
    sint32 s32Ret = 0;
#if (BOARD == BOARD_IPCR20S3 || BOARD == BOARD_IPCR20S4)
        s32Ret = mpp_vi_SNR_SetFps(u32Fps);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vi_SNR_SetFps failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_SYS_SetChnRotate(sint32 s32Chn, SV_ROT_ANGLE_E enAngle)
{
    sint32 s32Ret = 0, i = 0;

#if (BOARD == BOARD_IPCR20S3 || BOARD == BOARD_IPCR20S4 || BOARD == BOARD_IPCR20S5 || BOARD == BOARD_WFCR20S2 || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vpss_SetChnRotate(s32Chn, enAngle);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_SetChnRotate failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
#if (BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vosd_SetChnRotate(s32Chn, enAngle);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_SetChnRotate failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vo_SetChnRotate(s32Chn, enAngle);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_SetChnRotate failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#endif
    return SV_SUCCESS;
}

sint32 MEDIA_SYS_GetPts(uint64 *pu64Pts)
{
    sint32 s32Ret = 0;

    s32Ret = mpp_sys_GetPts(pu64Pts);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_GetPts failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_SYS_UpdatePts()
{
    sint32 s32Ret = 0;

    s32Ret = mpp_sys_UpdatePts();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_UpdatePts failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

void MEDIA_VI_SetCustomImage(MEDIA_IMAGE_S *pstCustomImage)
{
    m_stMediaInfo.stCustomImage.u8Brightness = pstCustomImage->u8Brightness;
    m_stMediaInfo.stCustomImage.u8Contrast = pstCustomImage->u8Contrast;
    m_stMediaInfo.stCustomImage.u8Saturation = pstCustomImage->u8Saturation;
    m_stMediaInfo.stCustomImage.u8Sharpness = pstCustomImage->u8Sharpness;
}

void MEDIA_VI_GetCustomImage(MEDIA_IMAGE_S *pstCustomImage)
{
	pstCustomImage->u8Brightness = m_stMediaInfo.stCustomImage.u8Brightness;
	pstCustomImage->u8Contrast = m_stMediaInfo.stCustomImage.u8Contrast;
	pstCustomImage->u8Saturation = m_stMediaInfo.stCustomImage.u8Saturation;
	pstCustomImage->u8Sharpness = m_stMediaInfo.stCustomImage.u8Sharpness;

}
sint32 MEDIA_VI_ImageParamSet(uint8 u8Brightness, uint8 u8Contrast, uint8 u8Saturation, uint8 u8Sharpness)
{
    return mpp_vi_SetImageParam(u8Brightness, u8Contrast, u8Saturation, u8Sharpness);
}

sint32 MEDIA_VI_ImageParamGet(uint8 *u8Brightness, uint8 *u8Contrast, uint8 *u8Saturation, uint8 *u8Sharpness)
{
    return mpp_vi_GetImageParam(u8Brightness, u8Contrast, u8Saturation, u8Sharpness);
}

sint32 MEDIA_VI_UserPicOn(sint32 s32Chn, char* pUserPicPath)
{
#if ((BOARD == BOARD_DMS31V2) \
    || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    sint32 s32Ret;
    s32Ret = mpp_vi_SetUserPic(s32Chn, pUserPicPath);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vi_SetUserPic fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vi_EnableUserPic(s32Chn);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vi_EnableUserPic fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VI_UserPicOff(sint32 s32Chn)
{
#if ((BOARD == BOARD_DMS31V2) \
    || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    sint32 s32Ret;
    s32Ret = mpp_vi_DisableUserPic(s32Chn);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vi_DisableUserPic fail! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VPSS_SetNetworkState(NETWORK_STAT_S *pstNetworkStat)
{
#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    mpp_vpss_SetNetwork(pstNetworkStat);
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VPSS_ReCreateGrp(SV_SIZE_S *pstSize)
{
#if (PLATFORM == PLATFORM_RV1106)
    sint32 s32Ret, i = 0;
    sint32 s32ViChn = 0;
    sint32 s32GrpId = 0;
	VPSS_CHN_ATTR_S stVpssChnAttr = {0};
    SV_BOOL bChangeParam = SV_FALSE;
    SV_SIZE_S stSizes[MPP_VPSS_CHN_BUTT];

    for (i = 0; i < 2; i++)
    {
		s32Ret = RK_MPI_VPSS_GetChnAttr(s32GrpId, i, &stVpssChnAttr);
        if (s32Ret != RK_SUCCESS)
        {
			print_level(SV_ERROR, "RK_MPI_VPSS_GetChnAttr failed with %#x!\n", s32Ret);
			return s32Ret;
		}

        if (stVpssChnAttr.u32Width == pstSize[i].u32Width && stVpssChnAttr.u32Height == pstSize[i].u32Height)
        {
            continue;
        }

        print_level(SV_INFO, "get new vpss[%d] width: %d, height: %d\n", s32GrpId, pstSize[i].u32Width, pstSize[i].u32Height);
        stSizes[0].u32Width   = pstSize[0].u32Width;
        stSizes[0].u32Height  = pstSize[0].u32Height;
        stSizes[1].u32Width   = pstSize[1].u32Width;
        stSizes[1].u32Height  = pstSize[1].u32Height;

        s32Ret = mpp_sys_ViVpssUnBind(s32ViChn, s32GrpId);
        if (RK_SUCCESS != s32Ret)
        {
            printf("mpp_sys_ViVpssUnBind[%d][%d] failed! [err=%#x]\n", s32ViChn, s32GrpId, s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vpss_DestroyGrp(s32GrpId);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_DestroyGrp[%d] failed! [err=%#x]\n", s32GrpId, s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vpss_CreateGrp(s32GrpId, stSizes);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_CreateGrp[%d] failed! [err=%#x]\n", s32GrpId, s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_sys_ViVpssBind(s32ViChn, s32GrpId);
        if (RK_SUCCESS != s32Ret)
        {
            printf("mpp_sys_ViVpssBind[%d][%d] failed! [err=%#x]\n", s32ViChn, s32GrpId, s32Ret);
            return s32Ret;
        }

    }

#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VPSS_GetChnGrayFrame(sint32 s32Chn, sint32 s32VpssChn, void **ppvBuf, uint32 *pu32Width, uint32 *pu32Height)
{
    return mpp_vpss_GetChnGrayFrame(s32Chn, s32VpssChn, ppvBuf, pu32Width, pu32Height);
}

sint32 MEDIA_VPSS_ReleaseChnGrayFrame(sint32 s32Chn, sint32 s32VpssChn)
{
    return mpp_vpss_ReleaseChnGrayFrame(s32Chn, s32VpssChn);
}

sint32 MEDIA_VENC_H264AttrSet(sint32 s32Chn, STREAM_TYPE_E enStreamType, VIDEO_ENCODE_H264_S *pstEncodeH264)
{
    sint32 s32Ret = 0;

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstEncodeH264)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = mpp_venc_H264AttrSet(enStreamType, s32Chn, pstEncodeH264);
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_VENC_H264AttrGet(sint32 s32Chn, STREAM_TYPE_E enStreamType, VIDEO_ENCODE_H264_S *pstEncodeH264)
{
    sint32 s32Ret = 0;

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstEncodeH264)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = mpp_venc_H264AttrGet(enStreamType, s32Chn, pstEncodeH264);
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_VENC_JpegAttrGet(sint32 s32Chn, STREAM_TYPE_E enStreamType, VIDEO_ENCODE_JPEG_S *pstEncodeJpeg)
{
#if(BOARD == BOARD_WFCR20S2)
    sint32 s32Ret = 0;

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstEncodeJpeg)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = mpp_venc_JpegAttrGet(enStreamType, s32Chn, pstEncodeJpeg);
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}


sint32 MEDIA_VENC_H264ChnReset(sint32 s32Chn, STREAM_TYPE_E enStreamType, VIDEO_ENCODE_H264_S *pstEncodeH264, SV_ROT_ANGLE_E enRotateAngle)
{
    sint32 s32Ret = 0;
    MPP_VOSD_SIZE_E enCharSize;
    sint32 s32Num;
#if (PLATFORM == PLATFORM_SSC335)
    if (STREAM_TYPE_PRI != enStreamType && STREAM_TYPE_SEC != enStreamType)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstEncodeH264)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = mpp_venc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Stop failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_IPCR20S4 || BOARD == BOARD_IPCR20S5 || BOARD == BOARD_WFCR20S2)
    if (VIDEO_ENCODE_MJPEG == pstEncodeH264->enEncode)
    {
        s32Ret = mpp_venc_MJpegDestroyChn(enStreamType, s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_MJpegDestroyChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
    else
    {
        s32Ret = mpp_venc_H264DestroyChn(enStreamType, s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#else
    s32Ret = mpp_venc_H264DestroyChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_vpss_ResetChnAttr(s32Chn, enStreamType, pstEncodeH264->u32PicWidth, \
                                     pstEncodeH264->u32PicHeight, enRotateAngle);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ResetChnAttr failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_IPCR20S4 || BOARD == BOARD_IPCR20S5 || BOARD == BOARD_WFCR20S2)
    if (VIDEO_ENCODE_MJPEG == pstEncodeH264->enEncode)
    {
        s32Ret = mpp_venc_MJpegCreateChn(enStreamType, s32Chn, pstEncodeH264);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_MJpegCreateChn PriChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }
    }
    else
    {
        s32Ret = mpp_venc_H264CreateChn(enStreamType, s32Chn, pstEncodeH264);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
#else
    s32Ret = mpp_venc_H264CreateChn(enStreamType, s32Chn, pstEncodeH264);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_venc_EnableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    if (STREAM_TYPE_PRI == enStreamType)
    {
        if (pstEncodeH264->u32PicHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
            s32Num = 0;
        }
        else if (pstEncodeH264->u32PicHeight < 720)
        {
            enCharSize = MPP_VOSD_SIZE_2X;
            s32Num = 1;
        }
        else if (pstEncodeH264->u32PicHeight < 960)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
            s32Num = 2;
        }
        else if (pstEncodeH264->u32PicHeight < 1080)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
            s32Num = 3;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_4X;
            s32Num = 4;
        }
    }
    else
    {
        if (pstEncodeH264->u32PicHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
            s32Num = 0;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_2X;
            s32Num = 1;
        }
    }

    s32Ret = mpp_vosd_SetChnCharStyle(s32Chn, enStreamType, enCharSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnCharStyle failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Start failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VENC_H264ChnRecreate(sint32 s32Chn, STREAM_TYPE_E enStreamType, VIDEO_ENCODE_H264_S *pstEncodeH264)
{
    sint32 s32Ret = 0;
    sint32 VeGroup = s32Chn;
    MPP_VOSD_SIZE_E enCharSize;
    sint32 s32Num;

    if (STREAM_TYPE_PRI != enStreamType && STREAM_TYPE_SEC != enStreamType)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstEncodeH264)
    {
        return ERR_NULL_PTR;
    }

#if (BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32C4))
    /* 跳过参数配置选项 */
    static VIDEO_ENCODE_H264_S stEncodePriH264 = {0};
    static VIDEO_ENCODE_H264_S stEncodeSecH264 = {0};

    switch(enStreamType)
    {
        case STREAM_TYPE_PRI:
            if(STRUCT_EQUAL(stEncodePriH264, *pstEncodeH264))
            {
                return SV_SUCCESS;
            }
            stEncodePriH264 = *pstEncodeH264;
            break;
        case STREAM_TYPE_SEC:
            if(STRUCT_EQUAL(stEncodeSecH264, *pstEncodeH264))
            {
                return SV_SUCCESS;
            }
            stEncodeSecH264 = *pstEncodeH264;
            break;
    }
#endif

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vosd_OverlayRgnDetach(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_OverlayRgnDetach failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    if (VIDEO_ENCODE_MJPEG == pstEncodeH264->enEncode)
    {
        s32Ret = mpp_venc_MJpegDestroyChn(enStreamType, s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_MJpegDestroyChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
    else
    {
        s32Ret = mpp_venc_H264DestroyChn(enStreamType, s32Chn);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }


#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    SV_SIZE_S stSize = {pstEncodeH264->u32PicWidth, pstEncodeH264->u32PicHeight};
    s32Ret = mpp_vpss_ReCreateChannel(s32Chn, enStreamType, stSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ReCreateChannel failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

#if (BOARD == BOARD_ADA32V3)
    sleep_ms(MEDIA_VENC_RECREATE_DELAY);
#endif

#if (PLATFORM == PLATFORM_SSC335)
    s32Ret = mpp_vpss_ResetPortAttr(enStreamType, pstEncodeH264->u32PicWidth, pstEncodeH264->u32PicHeight);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ResetPortAttr failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    if (pstEncodeH264->enEncode == VIDEO_ENCODE_MJPEG)
    {
        s32Ret = mpp_venc_MJpegCreateChn(enStreamType, s32Chn, pstEncodeH264);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_MJpegCreateChn PriChn=%d failed! [err=%#x]\n", s32Chn, s32Ret);
            return s32Ret;
        }
    }
    else
    {
        s32Ret = mpp_venc_H264CreateChn(enStreamType, s32Chn, pstEncodeH264);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }


    s32Ret = mpp_vosd_OverlayRgnAttach(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_OverlayRgnAttach failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    if (STREAM_TYPE_PRI == enStreamType)
    {
        if (pstEncodeH264->u32PicHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
            s32Num = 0;
        }
        else if (pstEncodeH264->u32PicHeight < 720)
        {
            enCharSize = MPP_VOSD_SIZE_2X;
            s32Num = 1;
        }
        else if (pstEncodeH264->u32PicHeight < 960)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
            s32Num = 2;
        }
        else if (pstEncodeH264->u32PicHeight < 1080)
        {
            enCharSize = MPP_VOSD_SIZE_3X;
            s32Num = 3;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_4X;
            s32Num = 4;
        }
    }
    else
    {
        if (pstEncodeH264->u32PicHeight < 480)
        {
            enCharSize = MPP_VOSD_SIZE_1X;
            s32Num = 0;
        }
        else
        {
            enCharSize = MPP_VOSD_SIZE_2X;
            s32Num = 1;
        }
    }

#if (BOARD == BOARD_DMS31V2 \
    || BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32V3) || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vosd_SetChnGui(s32Chn, enStreamType, s32Num);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnGui failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_vosd_SetChnCharStyle(s32Chn, enStreamType, enCharSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnCharStyle failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_VENC_H264ChnEnable(sint32 s32Chn, STREAM_TYPE_E enStreamType)
{
    sint32 s32Ret = 0;

    if (STREAM_TYPE_PRI != enStreamType && STREAM_TYPE_SEC != enStreamType)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = mpp_venc_EnableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_VENC_H264ChnDisable(sint32 s32Chn, STREAM_TYPE_E enStreamType)
{
    sint32 s32Ret = 0;

    if (STREAM_TYPE_PRI != enStreamType && STREAM_TYPE_SEC != enStreamType)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_VENC_H264IFrame(sint32 s32Chn, STREAM_TYPE_E enStreamType)
{
    return mpp_venc_RequestIFrame(s32Chn, enStreamType);
}

sint32 MEDIA_VENC_JpegChnReset(sint32 s32Chn, STREAM_TYPE_E enStreamType, VIDEO_ENCODE_JPEG_S *pstEncodeJpeg, SV_ROT_ANGLE_E enRotateAngle)
{
    sint32 s32Ret = 0;
    MPP_VOSD_SIZE_E enCharSize;
    sint32 s32Num;
#if (PLATFORM == PLATFORM_SSC335)
    if (STREAM_TYPE_SNAP0 != enStreamType)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstEncodeJpeg)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = mpp_venc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Stop failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_JpegDestroyChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vpss_ResetChnAttr(s32Chn, enStreamType, pstEncodeJpeg->u32PicWidth, \
                                     pstEncodeJpeg->u32PicHeight, enRotateAngle);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ResetChnAttr failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_JpegCreateChn(enStreamType, s32Chn, pstEncodeJpeg);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_EnableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    if (pstEncodeJpeg->u32PicHeight < 480)
    {
        enCharSize = MPP_VOSD_SIZE_1X;
        s32Num = 0;
    }
    else if (pstEncodeJpeg->u32PicHeight < 720)
    {
        enCharSize = MPP_VOSD_SIZE_2X;
        s32Num = 1;
    }
    else if (pstEncodeJpeg->u32PicHeight < 960)
    {
        enCharSize = MPP_VOSD_SIZE_3X;
        s32Num = 2;
    }
    else if (pstEncodeJpeg->u32PicHeight < 1080)
    {
        enCharSize = MPP_VOSD_SIZE_3X;
        s32Num = 3;
    }
    else
    {
        enCharSize = MPP_VOSD_SIZE_4X;
        s32Num = 4;
    }

    s32Ret = mpp_vosd_SetChnCharStyle(s32Chn, enStreamType, enCharSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnCharStyle failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Start failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VENC_JpegChnRecreate(sint32 s32Chn, STREAM_TYPE_E enStreamType, VIDEO_ENCODE_JPEG_S *pstEncodeJpeg)
{
    sint32 s32Ret = 0;
    sint32 VeGroup = s32Chn;
    MPP_VOSD_SIZE_E enCharSize;
    sint32 s32Num;


    if (STREAM_TYPE_SNAP0 != enStreamType)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (s32Chn < 0 || s32Chn >= MPP_VENC_MAX_CHN_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pstEncodeJpeg)
    {
        return ERR_NULL_PTR;
    }

#if (BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32C4))
    /* 跳过参数配置选项 */
    static VIDEO_ENCODE_JPEG_S stEncodeJpeg = {0};
    if(STRUCT_EQUAL(stEncodeJpeg, *pstEncodeJpeg))
    {
        return SV_SUCCESS;
    }

    stEncodeJpeg = *pstEncodeJpeg;
#endif

#if (PLATFORM != PLATFORM_RV1126 && PLATFORM != PLATFORM_RV1106)
    s32Ret = mpp_venc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Stop failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif

    s32Ret = mpp_venc_DisableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vosd_OverlayRgnDetach(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_OverlayRgnDetach failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_JpegDestroyChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264DestroyChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (PLATFORM == PLATFORM_RV1126)
    SV_SIZE_S stSize = {pstEncodeJpeg->u32PicWidth, pstEncodeJpeg->u32PicHeight};
    s32Ret = mpp_vpss_ReCreateChannel(s32Chn, enStreamType, stSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ReCreateChannel failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

#if (PLATFORM == PLATFORM_SSC335)
    s32Ret = mpp_vpss_ResetPortAttr(enStreamType, pstEncodeJpeg->u32PicWidth, pstEncodeJpeg->u32PicHeight);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ResetPortAttr failed! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
#endif

#if (BOARD == BOARD_ADA32V3)
    sleep_ms(MEDIA_VENC_RECREATE_DELAY);
#endif

    s32Ret = mpp_venc_JpegCreateChn(enStreamType, s32Chn, pstEncodeJpeg);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_H264CreateChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vosd_OverlayRgnAttach(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_OverlayRgnAttach failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_venc_EnableChn(enStreamType, s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    if (pstEncodeJpeg->u32PicHeight < 480)
    {
        enCharSize = MPP_VOSD_SIZE_1X;
        s32Num = 0;
    }
    else if (pstEncodeJpeg->u32PicHeight < 720)
    {
        enCharSize = MPP_VOSD_SIZE_2X;
        s32Num = 1;
    }
    else if (pstEncodeJpeg->u32PicHeight < 960)
    {
        enCharSize = MPP_VOSD_SIZE_3X;
        s32Num = 2;
    }
    else if (pstEncodeJpeg->u32PicHeight < 1080)
    {
        enCharSize = MPP_VOSD_SIZE_3X;
        s32Num = 3;
    }
    else
    {
        enCharSize = MPP_VOSD_SIZE_4X;
        s32Num = 4;
    }


#if (BOARD == BOARD_DMS31V2 || BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32V3) \
    || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1\
    || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vosd_SetChnGui(s32Chn, enStreamType, s32Num);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnGui failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = mpp_vosd_SetChnCharStyle(s32Chn, enStreamType, enCharSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnCharStyle failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (PLATFORM != PLATFORM_RV1126 && PLATFORM != PLATFORM_RV1106)
    s32Ret = mpp_venc_Start();
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_Start failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
 #endif

    return SV_SUCCESS;
}

sint32 MEDIA_VDEC_H264ChnCreate(sint32 s32Chn, VIDEO_DECODE_H264_S *pstDecodeH264)
{
    sint32 s32Ret = -1;
#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vdec_H264ChnCreate(s32Chn, pstDecodeH264);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "vdec h264 chn create failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VPSS_ExtscreenChnRecreate(sint32 s32Chn, VIDEO_EXTSCREEN_S *pstExtscreen)
{
#if (BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_DMS31V2))
    sint32 s32Ret;
    SV_SIZE_S stSize = {pstExtscreen->u32OutWidth, pstExtscreen->u32OutHeight};


    /* 跳过参数配置选项 */
    static VIDEO_EXTSCREEN_S stExtscreen = {0};
    if(STRUCT_EQUAL(stExtscreen, *pstExtscreen))
    {
        return SV_SUCCESS;
    }

    stExtscreen = *pstExtscreen;
    s32Ret = mpp_vpss_ReCreateChannel(s32Chn, MPP_VPSS_CHN_EXT, stSize);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_ReCreateChannel failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}


sint32 MEDIA_VDEC_H264ChnDestroy(sint32 s32Chn)
{
    sint32 s32Ret = -1;
#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vdec_H264ChnDestroy(s32Chn);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "vdec h264 chn destroy failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}


sint32 MEDIA_VDEC_H264ChnEnable(sint32 s32Chn)
{
    sint32 s32Ret = -1;
#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vdec_H264ChnEnable(s32Chn);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "vdec h264 chn enable failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VDEC_H264ChnDisable(sint32 s32Chn)
{
    sint32 s32Ret = -1;
#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vdec_H264ChnDisable(s32Chn);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "vdec h264 chn enable failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VDEC_H264DataSend(sint32 s32Chn, VDEC_FRAME_S *pstVdecFrame, SV_BOOL bBlock)
{
    sint32 s32Ret = -1;
#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vdec_H264DataSend(s32Chn, pstVdecFrame, bBlock);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "vdec h264 send data failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VI_SetPlayback(SV_BOOL bPlayback)
{
    sint32 s32Ret = -1;
#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vi_SetPlayback(bPlayback);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetPlayback failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}


sint32 MEDIA_VDEC_SetPlayback(SV_BOOL bPlayback)
{
    sint32 s32Ret = -1;
#if (BOARD == BOARD_DMS31V2 || BOARD == DMS31SDK || BOARD == BOARD_ADA32V2 || BOARD == ADA32SDK)
    s32Ret = mpp_vdec_SetPlayback(bPlayback);
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_SetPlayback failed! [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_VDEC_ChnDestroy(sint32 s32Chn)
{
    sint32 s32Ret = -1;

    return SV_SUCCESS;
}

sint32 MEDIA_VDEC_ChnEnable(sint32 s32Chn)
{
    sint32 s32Ret = -1;

    return SV_SUCCESS;
}

sint32 MEDIA_VDEC_ChnDisable(sint32 s32Chn)
{
    sint32 s32Ret = -1;
    return SV_SUCCESS;
}

sint32 MEDIA_VDEC_DataSend(sint32 s32Chn, uint8 *pu8Addr, uint32 u32Size)
{
    sint32 s32Ret = -1;

    return SV_SUCCESS;
}

sint32 MEDIA_VO_Recreate(VIDEO_VO_S *pstVoAttr)
{
#if ((BOARD == BOARD_DMS31V2) \
    || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || BOARD == BOARD_ADA32IR \
    || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4))
    sint32 s32Ret;
    if (NULL == pstVoAttr)
    {
        return ERR_NULL_PTR;
    }

    s32Ret = mpp_vo_ReCreateChannel(pstVoAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vo_ReCreateChannel failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_SetMode(pstVoAttr->enVoSplitMode);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_SetMode failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

#endif
    return SV_SUCCESS;
}



sint32 MEDIA_VMIX_SetAttr(VIDEO_VMIX_S *pstVmixAttr)
{
    return SV_SUCCESS;
}




sint32 MEDIA_VENC_FastSnapStart(sint32 s32ChnMask, uint32 u32Quality)
{
    sint32 s32Ret = 0;
#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_FastSnapStart();
#endif

    s32Ret = mpp_venc_FastSnapStart(s32ChnMask, u32Quality);
    return s32Ret;
}

sint32 MEDIA_VENC_FastSnapStop()
{
    sint32 s32Ret = 0;
#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_FastSnapStop();
#endif

    s32Ret = mpp_venc_FastSnapStop();
    return s32Ret;
}

sint32 MEDIA_AENC_ChnEnable(sint32 s32Chn)
{
    sint32 s32Ret = 0;

    s32Ret = mpp_aenc_EnableChn(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_EnableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_AENC_ChnDisable(sint32 s32Chn)
{
    sint32 s32Ret = 0;

    s32Ret = mpp_aenc_DisableChn(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_DisableChn failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_AENC_SetEncParam(sint32 s32Chn, AUDIO_ENCODE_TYPE_E enEncType, AUD_SR_E enAudioSampleRate)
{
    sint32 s32Ret = 0;

    s32Ret = mpp_aenc_SetEncParam(s32Chn, enEncType, enAudioSampleRate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_SetEncType failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_AENC_SetVolume(sint32 s32Chn, uint32 u32Volume)
{
    sint32 s32Ret = 0;

    s32Ret = mpp_aio_SetVolume(s32Chn, u32Volume);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aio_SetVolume failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_AO_SetVolume(sint32 s32Chn, uint32 u32Volume)
{
    sint32 s32Ret = 0;
#if (PLATFORM != PLATFORM_SSC335)
    s32Ret = mpp_ao_SetVolume(s32Chn, u32Volume);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ao_SetVolume failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}


sint32 MEDIA_AI_SetSampleRate(sint32 s32Chn, AUD_SR_E enAudioSampleRate)
{
    sint32 s32Ret = 0;

#if (PLATFORM == PLATFORM_SSC335)
    s32Ret = mpp_aenc_Stop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    s32Ret = MEDIA_AENC_ChnDisable(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_AI_SetSampleRate failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_aio_SetSampleRate(s32Chn, enAudioSampleRate);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aio_SetSampleRate failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_AENC_ChnEnable(s32Chn);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_AENC_ChnEnable failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#if (PLATFORM == PLATFORM_SSC335)
    s32Ret = mpp_aenc_Start();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_aenc_Start failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    return SV_SUCCESS;
}

sint32 MEDIA_AI_SetMute(sint32 s32Chn, SV_BOOL bEnable)
{
    return mpp_aio_SetMute(s32Chn, bEnable);
}

sint32 MEDIA_OSD_OverlaySet(sint32 s32Chn, sint32 s32OsdChn, VOSD_OVERLAY_S *pstOverlay)
{
    return mpp_vosd_ChnOverlayAttrSet(s32Chn, s32OsdChn, pstOverlay);
}

sint32 MEDIA_OSD_OverlayGet(sint32 s32Chn, sint32 s32OsdChn, VOSD_OVERLAY_S *pstOverlay)
{
    return mpp_vosd_ChnOverlayAttrGet(s32Chn, s32OsdChn, pstOverlay);
}

sint32 MEDIA_OSD_RoIconAttrSet(sint32 s32Chn, VOSD_OVERLAY_S *pstOverlay)
{
#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    return mpp_vosd_RoIconAttrSet(s32Chn, pstOverlay);
#endif
}

sint32 MEDIA_OSD_SetYuvProtocol(sint32 s32Chn, SV_BOOL bYuvProtocol)
{
#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    return mpp_vosd_SetYuvProtocol(s32Chn, bYuvProtocol);
#endif
}

sint32 MEDIA_OSD_SetAutoCalibration(sint32 s32Chn, SV_BOOL bAutoCalibration)
{
#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    return mpp_vosd_SetAutoCalibration(s32Chn, bAutoCalibration);
#endif
}

sint32 MEDIA_OSD_RoIconAttrGet(sint32 s32Chn, VOSD_OVERLAY_S *pstOverlay)
{
#if (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
    return mpp_vosd_RoIconAttrGet(s32Chn, pstOverlay);
#endif
}

sint32 MEDIA_OSD_SetChnName(sint32 s32Chn, char *szChnName)
{
    sint32 s32Ret = 0;

    if (NULL == szChnName)
    {
        return ERR_NULL_PTR;
    }

    if (COMMON_UnicodeEncode(szChnName) > VOSD_MAX_CHNNAME_LEN)
    {
        print_level(SV_ERROR, "ChnName exceed max len: %d\n", VOSD_MAX_CHNNAME_LEN);
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = mpp_vosd_SetChnName(s32Chn, szChnName);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetChnName failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_OSD_SetState(sint32 s32Chn, char *szState)
{
#if (BOARD == BOARD_IPCR20S3)
    sint32 s32Ret = 0;

    if (NULL == szState)
    {
        return ERR_NULL_PTR;
    }

    if (strlen(szState) > VOSD_MAX_STATEINFO_LEN)
    {
        print_level(SV_ERROR, "State info exceed max len: %d\n", VOSD_MAX_STATEINFO_LEN);
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = mpp_vosd_SetState(s32Chn, szState);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetState failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    return SV_SUCCESS;
}

sint32 MEDIA_OSD_SetWifiName(char *szWifiName)
{

#if ((BOARD == BOARD_DMS31V2) \
    || (BOARD == BOARD_ADA32V2) || (BOARD == BOARD_ADA32V3) || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    sint32 s32Ret = 0;

    if (NULL == szWifiName)
    {
        return ERR_NULL_PTR;
    }

    if (strlen(szWifiName) > VOSD_MAX_CHNNAME_LEN)
    {
        print_level(SV_ERROR, "szWifiName exceed max len: %d\n", VOSD_MAX_CHNNAME_LEN);
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = mpp_vosd_SetWifiName(szWifiName);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetWifiName failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif

    return SV_SUCCESS;
}

sint32 MEDIA_OSD_SetPdCanid(char *szPdCanid)
{
#if (BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) || BOARD == BOARD_ADA47V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    sint32 s32Ret = 0;

    if (NULL == szPdCanid)
    {
        return ERR_NULL_PTR;
    }

    if (strlen(szPdCanid) > VOSD_MAX_CHNNAME_LEN)
    {
        print_level(SV_ERROR, "szWifiName exceed max len: %d\n", VOSD_MAX_CHNNAME_LEN);
        return ERR_ILLEGAL_PARAM;
    }

    s32Ret = mpp_vosd_SetPdCanid(szPdCanid);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vosd_SetWifiName failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

#endif
    return SV_SUCCESS;
}

sint32 MEDIA_OSD_FeedBsdOverlay(uint32 u32TimeMs)
{
    return mpp_vosd_FeedBsdOverlay(u32TimeMs);
}

sint32 MEDIA_VOSD_Gui_Draw(MEDIA_GUI_DRAW_S *pstMediaGuiDraw)
{
    sint32 s32Ret, lens, i;
    void *pbuf;
#if (BOARD == BOARD_DMS31V2 \
    || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vosd_gui_draw(pstMediaGuiDraw);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vosd_gui_add_Op fail![%d\n]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_OSD_SetLang(LANG_TYPE_E enlang)
{
    sint32 s32Ret;
#if (BOARD == BOARD_DMS31V2 \
    || BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    s32Ret = mpp_vosd_SetLang(enlang);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "mpp_vosd_SetLang fail!\n");
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_GET_ALG_FD(int *pfd, int devIdx, int bufIdx)
{

#if (BOARD == BOARD_DMS31V2 || BOARD == BOARD_ADA32V2 || (BOARD == BOARD_ADA32N1) \
    || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA47V1 || BOARD == BOARD_ADA900V1\
    || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    sint32 s32Ret;
    s32Ret = mpp_vpss_Alg_GetFD(pfd, devIdx, bufIdx);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vpss_Alg_GetFD fail\n");
        return SV_FAILURE;
    }
#elif (BOARD == BOARD_ADA32IR)
    sint32 s32Ret;
    if(0 == devIdx)
    {
        s32Ret = mpp_vpss_Alg_GetFD(pfd, devIdx, bufIdx);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vpss_Alg_GetFD fail\n");
            return SV_FAILURE;
        }
    }
    else
    {
        s32Ret = mpp_ir_alg_GetFD(pfd, bufIdx);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vpss_Alg_GetFD fail\n");
            return SV_FAILURE;
        }
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_GET_ALG_RES(int *pWidth, int *pHeight, int devIdx)
{
#if (BOARD == BOARD_ADA32V2 \
		|| BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) || BOARD == BOARD_ADA47V1 \
		|| BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
	sint32 s32Ret;
    s32Ret = mpp_vpss_Alg_GetRes(pWidth, pHeight, devIdx);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vpss_Alg_GetRes fail\n");
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_SET_ALG_EXTERN(VALG_EXTERN_S *pstValgExtern)
{
#if (BOARD == BOARD_ADA32V2 \
    || BOARD == BOARD_ADA32V3 || BOARD == BOARD_ADA32IR || (BOARD == BOARD_ADA32N1) || BOARD == BOARD_ADA47V1 \
    || BOARD == BOARD_ADA900V1 || BOARD == BOARD_HDW845V1 || (BOARD == BOARD_ADA32C4) || (BOARD == BOARD_ADA32E1))
    sint32 s32Ret;
    s32Ret = mpp_vo_SetPrimaryStr(pstValgExtern);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "mpp_vo_SetPrimaryStr fail\n");
        return SV_FAILURE;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_SET_AlARM_ROI(ZOOM_ALARM_ROI       stZoomAlarmRoi)
{
#if (BOARD == BOARD_HDW845V1)
    sint32 s32Ret;
    s32Ret = mpp_vo_SetAlarmRoi(stZoomAlarmRoi);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_INFO, "mpp_vo_SetPrimaryStr fail\n");
        return SV_FAILURE;
    }

#endif
    return SV_SUCCESS;
}

sint32 MEDIA_SET_CROSSHAIRICON_STATUS(sint32 s32Chn, SV_BOOL bPdCrosshairIcon)
{
#if (BOARD == BOARD_ADA32V2 || BOARD == BOARD_ADA32IR)
    if (BOARD_IsCustomer(BOARD_C_ADA32V2_202613) || BOARD_IsCustomer(BOARD_C_ADA32IR_202613))
    {
            mpp_vosd_SetCrossHairIconStatus(s32Chn, bPdCrosshairIcon);
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_QueryStatus(MEDIA_STAT_S *pstMediaStat)
{
    if (NULL == pstMediaStat)
    {
        return ERR_NULL_PTR;
    }

    pstMediaStat->bNightMode = mpp_sys_IsNightMode();

    return SV_SUCCESS;
}

sint32 MEDIA_UpdateIRCUT_Status(uint32 u32IRcutMode)
{
    mpp_sys_UpdateIRCUT_Status(u32IRcutMode);

    return SV_SUCCESS;
}

sint32 MEDIA_UpdateLED_Status(uint32 u32LedBright)
{
#if (BOARD == BOARD_ADA47V1)
    mpp_sys_UpdateLED_Status(u32LedBright);
#endif
    return SV_SUCCESS;
}

#if (PLATFORM == PLATFORM_SSC335)
sint32 MEDIA_UpdateVideoMode(VIDEO_MODE_EE *pnewVideoMode, uint32 *pu32NewW, uint32 *pu32NewH)
{
    sint32 s32Ret = 0, s32Chn = 0, s32VpssGrp = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};
    MPP_VOSD_SIZE_E enCharSize;
    SV_BOOL bVideoMode = SV_FALSE;
    static VIDEO_MODE_EE enLastVideoMode = VIDEO_MODE_BUTTE;
    SV_SIZE_S stPriVencSize, stJPGSize;
    CFG_MEDIA_PARAM stMediaParam;

    print_level(SV_WARN, "m_stMediaInfo.stTypeMode.enVideoMode:%d pnewVideoMode:%d\n", m_stMediaInfo.stTypeMode.enVideoMode, *pnewVideoMode);

    s32Ret = mpp_sys_ViVpssUnBind(s32Chn,s32VpssGrp);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_ViVpssUnBind failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_vi_RenewAttr(pnewVideoMode);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_UpdateAttr failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = mpp_sys_ViVpssBind(s32Chn, s32VpssGrp);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_sys_ViVpssBind failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

	s32Ret = CONFIG_GetMediaParam(&stMediaParam);
	if (s32Ret != SV_SUCCESS)
	{
		print_level(SV_ERROR, "CONFIG_GetMediaParam fail [err=#%x] !\n", s32Ret);
		return s32Ret;
	}

	s32Ret = MEDIA_VI_ImageParamSet(stMediaParam.u8Brightness,stMediaParam.u8Contrast,stMediaParam.u8Saturation,stMediaParam.u8Sharpness);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MEDIA_VI_ImageParamSet failed.\n");
		return s32Ret;
	}

    if (VIDEO_NODE_NULL != *pnewVideoMode)
    {
        s32Ret = mpp_vpss_SetChnMirrorFlip(0, stMediaParam.astChnParam[0].bImageMirror, stMediaParam.astChnParam[0].bImageFlip);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }
    else
    {
        s32Ret = mpp_vpss_SetChnMirrorFlip(0, SV_FALSE, SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MEDIA_SYS_SetChnMirrorFlip failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

    m_stMediaInfo.stTypeMode.enVideoMode = *pnewVideoMode;
    m_stMediaInfo.stSrcSize.u32Width = *pu32NewW;
    m_stMediaInfo.stSrcSize.u32Height = *pu32NewH;

    return SV_SUCCESS;
}
#elif (PLATFORM == PLATFORM_RV1126 || PLATFORM == PLATFORM_RV1106)
sint32 MEDIA_UpdateVideoMode(VIDEO_MODE_EE *pnewVideoMode, uint32 *pu32NewW, uint32 *pu32NewH)
{
    sint32 s32Chn;
    sint32 s32Ret = 0, s32ViChn = 0, s32VpssGrp = 0;
    static VIDEO_MODE_EE stoldVideoMode[4] = {0};
    sint32 i;
	CFG_MEDIA_PARAM stMediaParam;


    m_stMediaInfo.stTypeMode.enVideoMode = pnewVideoMode[0];
    m_stMediaInfo.stSrcSize.u32Width = pu32NewW[0];
    m_stMediaInfo.stSrcSize.u32Height = pu32NewH[0];


    s32Ret = mpp_vpss_Interrupt(SV_TRUE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Interrupt failed! [err=%#x]\n", s32Ret);
        goto skip1;
    }

#if (BOARD != BOARD_ADA32V3)
    for(i = 0; i < MEDIA_IN_CHN; i++)
    {
        s32Chn = i;
        s32Ret =  mpp_vmix_Interrupt(s32Chn, SV_TRUE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vmix_Interrupt failed! [err=%#x]\n", s32Ret);
            goto skip2;
        }
    }
#endif
    usleep(1000*30);

    for(i = 0; i < MEDIA_IN_CHN; i++)
    {
        s32Chn = i;
        s32VpssGrp = i;
        s32Ret = mpp_sys_ViVpssUnBind(s32Chn, s32VpssGrp);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssUnBind failed! [err=%#x]\n", s32Ret);
            goto skip3;
        }
    }

    usleep(1000*100);

    s32Ret = mpp_vi_RenewAttr(pnewVideoMode);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vi_UpdateAttr failed! [err=%#x]\n", s32Ret);
        goto skip4;
    }

    for(i = 0; i < MEDIA_IN_CHN; i++)
    {
        s32VpssGrp = i;
        s32Ret = mpp_vpss_RenewGrp(s32VpssGrp, pnewVideoMode[i], pu32NewW[i], pu32NewH[i]);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vi_UpdateAttr failed! [err=%#x]\n", s32Ret);
        }
    }

    s32Ret = mpp_venc_RenewAttr(pnewVideoMode);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_venc_RenewAttr failed! [err=%#x]\n", s32Ret);
    }


skip4:
    for(i = 0; i < MEDIA_IN_CHN; i++)
    {
        s32Chn = i;
        s32VpssGrp = i;
        s32Ret = mpp_sys_ViVpssBind(s32Chn, s32VpssGrp);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssBind failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
    }

skip3:
#if (BOARD != BOARD_ADA32V3)
    for(i = 0; i < MEDIA_IN_CHN; i++)
    {
        s32Chn = i;
        s32Ret =  mpp_vmix_Interrupt(s32Chn, SV_FALSE);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vmix_Interrupt failed! [err=%#x]\n", s32Ret);
            goto skip2;
        }
    }
#endif
skip2:

    s32Ret = mpp_vpss_Interrupt(SV_FALSE);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_vpss_Interrupt failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    stoldVideoMode[s32Chn] = pnewVideoMode[0];
#if (defined(BOARD_ADA47V1))
		s32Ret = CONFIG_GetMediaParam(&stMediaParam);
		if (s32Ret != SV_SUCCESS)
		{
			print_level(SV_ERROR, "CONFIG_GetMediaParam fail [err=#%x] !\n", s32Ret);
			return s32Ret;
		}

		s32Ret = MEDIA_VI_ImageParamSet(stMediaParam.u8Brightness,stMediaParam.u8Contrast,stMediaParam.u8Saturation,stMediaParam.u8Sharpness);
		if (SV_SUCCESS != s32Ret)
		{
			print_level(SV_ERROR, "OP_REQ_GET_IMAGE_CFG failed.\n");
			return s32Ret;
		}
#endif

skip1:
    return s32Ret;
}
#else
sint32 MEDIA_UpdateVideoMode(VIDEO_MODE_EE *pnewVideoMode, uint32 *pu32NewW, uint32 *pu32NewH)
{
    sint32 s32Ret = 0, s32Chn = 0, s32VpssGrp = 0;
    MSG_PACKET_S stMsgPkt = {0}, stRetPkt = {0};
    MSG_VIDEO_CFG stVideoCfg = {0};

    if(m_stMediaInfo.stTypeMode.enVideoMode == pnewVideoMode[0])
    {
        return SV_SUCCESS;
    }
    else
    {
        s32VpssGrp = s32Chn;
        m_stMediaInfo.stTypeMode.enVideoMode = pnewVideoMode[0];
        m_stMediaInfo.stSrcSize.u32Width = pu32NewW[0];
        m_stMediaInfo.stSrcSize.u32Height = pu32NewH[0];

        s32Ret = mpp_vi_RenewAttr(pnewVideoMode);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vi_UpdateAttr failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_vpss_RenewGrp(s32VpssGrp, pnewVideoMode[0], pu32NewW[0], pu32NewH[0]);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_vpss_RenewGrp failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_sys_ViVpssUnBind(s32Chn,s32VpssGrp);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssUnBind failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_sys_ViVpssBind(s32Chn, s32VpssGrp);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_sys_ViVpssBind failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_venc_Stop();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_Stop failed! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }

        stRetPkt.pu8Data = (uint8 *)&stVideoCfg;
        s32Ret = Msg_execRequestBlock(EP_WF697, EP_CONTROL, OP_REQ_GET_VIDEO_CFG, NULL, &stRetPkt, sizeof(MSG_VIDEO_CFG));
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_GET_VIDEO_CFG failed.\n");
            return SV_FAILURE;
        }

        stMsgPkt.pu8Data = (uint8 *)&stVideoCfg;
        stMsgPkt.u32Size = sizeof(MSG_VIDEO_CFG);
        s32Ret = Msg_execRequestBlock(EP_WF697, EP_CONTROL, OP_REQ_SET_VIDEO_CFG, &stMsgPkt, NULL, 0);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "OP_REQ_SET_VIDEO_CFG failed.\n");
            return SV_FAILURE;
        }

        s32Ret = mpp_venc_RenewJpegSize(pnewVideoMode[0], pu32NewW[0], pu32NewH[0]);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_RenewJpegSize failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }

        s32Ret = mpp_venc_Start();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "mpp_venc_Start failed! [err: %s]\n", strerror(errno));
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

#endif

sint32 MEDIA_UpdateWDRMode(MEDIA_INIT_S *pstWDRInitParam)
{
    sint32 s32Ret = 0;

    s32Ret = MEDIA_VENC_H264ChnDisable(0, STREAM_TYPE_SEC);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VENC_H264ChnDisable failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = MEDIA_VENC_H264ChnDisable(0, STREAM_TYPE_PRI);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VENC_H264ChnDisable failed. [err=%#x]\n", s32Ret);
    }

    s32Ret = MEDIA_VENC_FastSnapStop();
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VENC_FastSnapStop failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_SYS_Stop();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_SYS_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_SYS_Fini();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_SYS_Fini failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_SYS_Init(pstWDRInitParam);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_SYS_Init failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_SYS_Start();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "MEDIA_SYS_Stop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_VENC_H264ChnEnable(0, STREAM_TYPE_PRI);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VENC_H264ChnEnable failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_VENC_H264ChnEnable(0, STREAM_TYPE_SEC);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VENC_H264ChnEnable failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_VENC_FastSnapStart(0x1, 3);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_VENC_FastSnapStart failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = MEDIA_AENC_ChnEnable(0);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_AENC_ChnEnable failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 MEDIA_ResetVideoModeParam(SV_SIZE_S stPriVencSize, SV_SIZE_S stSubVencSize, SV_SIZE_S stPicVencSize)
{
    sint32 s32Ret = 0;
#if (PLATFORM == PLATFORM_SSC335)
    mpp_vpss_SetDivpSize(stPriVencSize, stSubVencSize, stPicVencSize);
    mpp_venc_UpdateVedioMode(m_stMediaInfo.stTypeMode.enVideoMode);
#endif
    return SV_SUCCESS;

}

sint32 MEDIA_SetFifoAttr(MEDIA_FIFO_ATTR      *pstMediaFifoAttr)
{
    sint32 s32Ret = 0;
    if (NULL == pstMediaFifoAttr)
    {
        return ERR_NULL_PTR;
    }
#if (BOARD == BOARD_ADA32IR)
    s32Ret = mpp_ir_SetFifoAttr(pstMediaFifoAttr);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "mpp_ir_SetFifoAttr failed[%x]\n",s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_SetGop(sint32 s32Chn,uint32 u32Gop)
{
	sint32 s32Ret;
#if (PLATFORM == PLATFORM_RV1126)
	s32Ret = mpp_venc_SetGop(s32Chn,u32Gop);
	if(SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "mpp_venc_setgop failed[%x]\n",s32Ret);
        return s32Ret;
    }
#endif
	return SV_SUCCESS;
}

sint32 MEDIA_Callback_GetParam(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret;
#if USING_MPP_CTRL
    s32Ret = mpp_ctrl_getParam(pstResetParam);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ctrl_getParam failed! [err=%d]\n", s32Ret);
    }
#endif
    return s32Ret;
}

sint32 MEDIA_Callback_SetParam(MEDIA_RESET_S *pstResetParam)
{
    sint32 s32Ret;
#if USING_MPP_CTRL
    s32Ret = mpp_ctrl_setParam(pstResetParam);
    if (s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ctrl_setParam failed! [err=%d]\n", s32Ret);
    }
#endif
    return s32Ret;
}

sint32 MEDIA_VI_IsSkipFrame(sint32 s32Chn)
{
#if (BOARD == BOARD_ADA32V3)
    return mpp_vi_IsSkipFrame(s32Chn);
#endif
    return SV_FALSE;
}

sint32 MEDIA_IR_ExtscreenParamReset(sint32 s32Width, sint32 *s32Height)
{
#if (BOARD == BOARD_ADA32IR)
    sint32 s32Ret = 0;
    s32Ret = mpp_ir_ExtscreenParamReset(s32Width, s32Height);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ir_ExtscreenParamReset failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_Callback_Ir_Cvbs_Stop()
{
#if (BOARD == BOARD_ADA32IR)
    sint32 s32Ret = 0;
    s32Ret = mpp_ir_CvbsStop();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_ir_CvbsStop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    s32Ret = mpp_vo_CvbsStop();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vo_CvbsStop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
    s32Ret = mpp_vosd_CvbsStop();
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vosd_CvbsStop failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}

sint32 MEDIA_Callback_Ir_Cvbs_Start()
{
#if (BOARD == BOARD_ADA32IR)
        sint32 s32Ret = 0;
        s32Ret = mpp_ir_CvbsStart();
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_ir_CvbsStart failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        s32Ret = mpp_vo_CvbsStart();
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vo_CvbsStart failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
        s32Ret = mpp_vosd_CvbsStart();
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vosd_CvbsStart failed! [err=%#x]\n", s32Ret);
            return s32Ret;
        }
#endif
        return SV_SUCCESS;
}

sint32 MEDIA_SetZoom(MEDIA_ZOOM_CFG *pstZoomCfg)
{
    sint32 s32Ret = 0;
    if (NULL == pstZoomCfg)
    {
        return ERR_NULL_PTR;

    }
#if (BOARD == BOARD_HDW845V1)
    s32Ret = mpp_vpss_SetZoom(pstZoomCfg);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "mpp_vpss_SetZoom failed! [err=%#x]\n", s32Ret);
        return s32Ret;
    }
#endif
    return SV_SUCCESS;
}


sint32 MEDIA_Callback_ir_RecordStatus(SV_BOOL bEnableStorage         , REC_ALARM_E enAlarmRecord, REC_NORMAL_E enNormalRecord)
{
#if (BOARD == BOARD_ADA32IR)
    if (bEnableStorage == SV_TRUE)
    {
        if (enAlarmRecord > 0 ||  enNormalRecord > 0)
        {
            mpp_ir_RecordStart();
        }
        else
        {
            mpp_ir_RecordStop();
        }
    }
    else
        mpp_ir_RecordStop();
#endif
    return SV_SUCCESS;

}

sint32 MEDIA_AO_CrateBuffer()
{
	sint32 s32Ret = 0;
#if MPP_AUDIO_AO_ENABLE

	s32Ret = mpp_ao_crateBuffer();

#endif
	return s32Ret;
}

sint32 MEDIA_AO_PlayTalkBuffer(sint8 *pBuffer)
{
	sint32 s32Ret = 0;
#if MPP_AUDIO_AO_ENABLE

	s32Ret =  mpp_ao_playTalkBuffer(pBuffer);

#endif
	return s32Ret;

}
sint32 MEDIA_AO_TalkBufferRealse()
{
	sint32 s32Ret = 0;
#if MPP_AUDIO_AO_ENABLE

	s32Ret = mpp_ao_playTalkBufferRealse();

#endif
	return s32Ret;

}

