Index: build/ada32v2/usr/lib/libpds_general_rv1126.so
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: src/alg/include/pds_alg.h
===================================================================
--- src/alg/include/pds_alg.h	(revision 5052)
+++ src/alg/include/pds_alg.h	(working copy)
@@ -109,6 +109,8 @@
         E_PDSALG_TYPE_RGB_PC_DEC_2nd,  /* 二阶段 检测网络*/
         E_PDSALG_TYPE_RGB_PC_DEC_2nd_test,  /* 二阶段 检测网络*/
         E_PDSALG_TYPE_RGB_PC_PALLET,      /* 可见光(RGB)检测, 行人(P) + 栈板 pallet */
+        E_PDSALG_TYPE_RGB_DCD,             /* 可见光(RGB)检测, DCD */ 
+        E_PDSALG_TYPE_RGB_PACKINGBOX,      /* 可见光(RGB)检测, 包装盒 */ 
 
 
 
@@ -188,7 +190,13 @@
         E_CLS_UNI_SIGN_PIC6,            /* 六种通用标志识别 标志6 */
 
         E_CLS_PALLET,                   /* 栈板 */
+        E_CLS_PERSON_VEST,              /*  DCD 人+反光衣 */
+        E_CLS_PERSON_HAT_VEST,          /* 栈板 人+安全帽+反光衣*/
 
+        E_CLS_HANDSET,                  /* 包装盒检测 遥控器 */
+        E_CLS_DESICCANT,                /* 包装盒检测 干燥剂 */
+        E_CLS_COVER,                    /* 包装盒检测 盒子 */
+
         E_SPEED_ZONE      = 300,   /* 红色 SPEED ZONE (陈勤芹客户仓库限速标志)*/
         E_END_SPEED_ZONE  = 301,   /* 蓝色 END SPEED ZONE (陈勤芹客户仓库限速标志)*/
 
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 5052)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -108,6 +108,7 @@
 #define PD_MODEL_RGB_PC_202406      "/root/model/RGB_PC_202406.rknn"        /* 202406 客户模型 */
 #define PD_MODEL_RGB_PC_SPECIAL     "/root/model/RGB_PC_SPECIAL.rknn"       /* 有对某些客户针对性优化过的模型 */
 #define PD_MODEL_RGB_PC_UNISIGN     "/root/model/RGB_PC_UNISIGN.rknn"       /* 多分类模型 */
+#define PD_MODEL_RGB_PC_DCD         "/root/model/RGB_PC_DCD.rknn"           /* 反光衣模型 */
 
 
 
@@ -5814,7 +5815,7 @@
             {
                 continue;
             }
-
+            
 #if PD_OVERTAKE_ALARM
             if (!stPdResult.stResults[i].bOverTake)
             {
@@ -5940,6 +5941,7 @@
                     {
                         bBlankClass = SV_TRUE;
                     }
+                    bBlankClass = SV_FALSE;
 					stGuiRect.astPersonsRect[u32RectCnt].color = GUI_COLOR_L_BLUE;
                     break;
                 case PD_ROI_BUTT:
@@ -7161,7 +7163,8 @@
                         }
                         else
                         {
-						    apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC);
+						    //apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC);
+                            apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_DCD);
                         }
                     }
             #endif
@@ -7961,6 +7964,8 @@
         modefilelist[0] = PD_MODEL_RGB_PC_UNISIGN;
     }
 
+    modefilelist[0] = PD_MODEL_RGB_PC_DCD;
+
     return SV_SUCCESS;
 }
 
