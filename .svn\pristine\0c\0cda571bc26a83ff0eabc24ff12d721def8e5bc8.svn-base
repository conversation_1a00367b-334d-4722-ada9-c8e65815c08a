
TARGET_LIB  = libsomeip.a

include ../../../Makefile.param

ifneq ($(BOARD),$(findstring $(BOARD),ADA32V4 IPCR20S3 IPCR20S4 IPTR20S1 WFCR20S2 ADA32N1 ADA32NSDK ADA32C4 ADA32V2 ADA32SDK ADA32IR ADA32E1 ADA32ESDK ADA46V1))
IGNORE_THIS_BUILD = someip
endif

SRCPPS = server.cpp 
SRCPPS += $(wildcard ./message/*.cpp)
SRCPPS += $(wildcard ./serialize/*.cpp)
SRCPPS += $(wildcard ./media/*.cpp)
SRCPPS += $(wildcard ./utilty/*.cpp)
CPPFLAGS += -std=c++11 -O2
CPPFLAGS += -I$(INC_PATH) -I$(INC_PATH)/someip -I$(shell pwd)

_TARGET_DIR_ = $(TOP_LIB)

include $(AUTO_DEP_MK) 
