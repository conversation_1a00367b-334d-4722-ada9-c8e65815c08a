## WEBUI模块说明

WEBUI模块是HD900工程中摄像头产品（ADA47除外）的WEB客户端，基于MVC模式开发，使用jquery插件，包含登录界面(login.html)，预览界面(index.html)，配置界面(config.html)，系统界面(system.html)，查询界面(query.html)。

### 1.登录界面

<img src="C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240709192333677.png" alt="image-20240709192333677" style="zoom: 50%;" />

​	在此页面初始化之前，触发pagebeforecreate事件，向设备发送获取配置请求 GET /config ，并根据不同的客户定制需求，更换CSS 主题样式、添加客户logo、更换文本翻译(luis客户）、允许免密码登录(202018/202122/201623客户)、语言翻译，并将hardware、customer、lang 缓存到浏览器本地。

​	更换CSS主题样式，区分为移动端和PC端，此外LUIS客户定制版本，拥有独立的一套CSS样式。

​	通用版设备默认没有密码，直接登录。



### 2.预览界面

<img src="C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240709193904423.png" alt="image-20240709193904423" style="zoom:50%;" />

预览界面分区：画面预览、菜单栏、语言选择、功能区。

功能区根据不同的产品型号，会有所取舍。标定功能只有AI摄像头才有，此外还有全屏、图像参数、人脸登录、算法参数等等。

在此界面初始化完成后，更换css主题样式、创建model、view、controller模型。

```javascript
<script type="text/javascript">
		$(document).ready(function(){
			var customer;
			var itemCustomer = window.location.host+"-customer";
			window.localStorage && (customer = window.localStorage.getItem(itemCustomer));
			console.log(customer);
			var hardware;
			var itemHardware = window.location.host+"-hardware";
			window.localStorage && (hardware = window.localStorage.getItem(itemHardware));
			if(customer=="200032" && hardware.indexOf("HDW845") == -1 ){
				if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-common.css"}).appendTo("head");
				}
			}
			else{
				if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/common.css"}).appendTo("head");
				}
			}
			var streamType;
            var itemStreamType = window.location.host+"-streamType";
            window.localStorage && (streamType = window.localStorage.getItem(itemStreamType));
			streamType = streamType || 0;
			switchCssStyle(hardware, customer);
			var model = new WebappModel();
			var view = new WebappView(model, "preview");
			var controller = new WebappController(model, view);			
			controller.refreshConfigs(null,"only");		
			controller.startPreview(streamType);
		});
	</script>
```



#### 2.1 A32标定

A32 产品的标定功能，是设置ROI区域。

![image-20240709195403745](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240709195403745.png)

Calibration Mode：标定模式，普通模式/测距模式；

pdRoiStyle：ROI类型有梯形、水平、半圆、椭圆、任意图形(Canvas)

Drag freely：是否启动自由拖拽

Area cutout：裁剪去某个区域，不作为检测区域；

alarmIcon Pos：报警图标的位置；

Select Zone：只显示某个颜色的ROI区域

Smoothness：自由绘画ROI模式下，线段的光滑度；



#### 2.2 DMS31标定和人脸登录

DMS31产品的标定是人脸标定，另外有人脸识别登录，可以注册用户，添加人脸图像、名称。可以进行批量 的用户删除、导入、导出。

<img src="C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240725192149208.png" alt="image-20240725192149208" style="zoom:80%;" />

### 3.查询界面

查询界面分为三个子页面，分别是状态查询、录像查询（目前需要支持录像的设备支持）、日志查询、警报日志查询（DMS31支持）。

<img src="C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240725192731889.png" alt="image-20240725192731889" style="zoom:80%;" />

### 4.配置界面

配置界面如下图所示，算法配置界面只有支持算法的产品才支持。配置界面隐藏了部分高级设置，只开放必需的配置。打开高级配置，点击图中红框所示位置，弹开对话框【Please enter engineer passsword】，输入webui，点击确定后输入true则为打开，输入false则为关闭。

![image-20240725192926620](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240725192926620.png)



### 5.系统界面

系统界面提供设备信息、升级、恢复出厂设置等功能。

<img src="C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240725193609587.png" alt="image-20240725193609587" style="zoom:80%;" />