Index: include/config.h
===================================================================
--- include/config.h	(revision 5578)
+++ include/config.h	(working copy)
@@ -492,7 +492,7 @@
     float fIRproportion;                        /* ada32ir红外Y周比例值 */
     sint32 s32VLheight;                         /* ada32ir可见光高度设置 */
     sint32 s32IRheight;                         /* ada32ir红外高度值设置 */
-
+    sint32 astPdSign[6];                        /* ada32六个通用标识 */
 } CFG_PDS_PARAM;
 
 /* 行人统计算法配置(APC) */
@@ -788,6 +788,20 @@
 } CFG_ALL_PARAM;
 
 /******************************************************************************
+ * 函数功能: alg初始化配置模块
+ * 输入参数: pszFilePath --- 配置文件路径
+             pszFileBak1 --- 备份文件1路径
+             pszFileBak2 --- 备份文件2路径
+             pszFileDefault --- 默认配置文件路径
+ * 输出参数: 无
+ * 返回值  : SV_SUCCESS - 成功
+             ERR_NULL_PTR - 传入参数指针为NULL
+             SV_FAILURE - 失败
+ * 注意    : 无
+ *****************************************************************************/
+extern sint32 CONFIG_Init_ALG(char *pszFilePath, char *pszFileBak1, char *pszFileBak2, char *pszFileDefault);
+
+/******************************************************************************
  * 函数功能: 初始化配置模块
  * 输入参数: pszFilePath --- 配置文件路径
              pszFileBak1 --- 备份文件1路径
Index: output/script/upgrade.py
===================================================================
--- output/script/upgrade.py	(revision 5578)
+++ output/script/upgrade.py	(working copy)
@@ -42,6 +42,10 @@
         code = 832
     elif ("ADA32N1_upgrade" in upgrade_file):
         code = 896
+    elif ("ADA32C4_upgrade" in upgrade_file):
+        code = 816
+    elif ("ADA32E1_upgrade" in upgrade_file):
+        code = 832
     else:
         print("cannot get {} code!".format(upgrade_file))
         exit(-1)
@@ -61,6 +65,10 @@
         updateHeader = "ADA900V1_update"
     elif ("ADA32N1_upgrade" in upgrade_file):
         updateHeader = "ADA32N1_update"
+    elif ("ADA32C4_upgrade" in upgrade_file):
+        updateHeader = "ADA32C4_update"
+    elif ("ADA32E1_upgrade" in upgrade_file):
+        updateHeader = "ADA32E1_update"
     else:
         print("cannot get {} updateHeader!".format(upgrade_file))
         exit(-1)
Index: src/alg/alg.cpp
===================================================================
--- src/alg/alg.cpp	(revision 5578)
+++ src/alg/alg.cpp	(working copy)
@@ -1289,7 +1289,7 @@
         return s32Ret;
     }
 
-    s32Ret = CONFIG_Init(pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
+    s32Ret = CONFIG_Init_ALG(pszConfigFile, pszConfigBak1, pszConfigBak2, pszConfigDefault);
     if (SV_SUCCESS != s32Ret)
     {
         print_level(SV_ERROR, "CONFIG_Init failed. [err=%#x]\n", s32Ret);
Index: src/alg/include/pds_alg.h
===================================================================
--- src/alg/include/pds_alg.h	(revision 5578)
+++ src/alg/include/pds_alg.h	(working copy)
@@ -11,6 +11,8 @@
 #include <vector>
 #include <string>
 
+#include "pds_com.h"
+
 // #include "drmrga.h"
 // #include "RockchipRga.h"
 
@@ -21,9 +23,12 @@
 // #define UseMutilOpFLow
 
 using namespace std;
+
+
+
 namespace pdsa32{
 
-#define SV_MAX_OUTPUT_PLATE_DET_NUM 10 // 最多能输出的车牌检测结果数量
+#define SV_MAX_OUTPUT_PLATE_DET_NUM 15 // 最多能输出的车牌检测结果数量
 
 /**********************************************************
  * 回调函数: 算法库认证
@@ -117,6 +122,10 @@
         E_PDSALG_TYPE_TIME_CONSUME_TEST,   /* 测试模型耗时类型 */ 
         E_PDSALG_TYPE_RGB_SEGMENT,   /* 分割类型 */ 
         E_PDSALG_TYPE_RGB_DEBLUR,   /* 去模糊类型 */ 
+        E_PDSALG_TYPE_RGB_PC_EVEHICLE,  /*人车+工程机械*/ 
+        E_PDSALG_TYPE_RGB_QRCODE,  /*二维码检测*/ 
+        E_PDSALG_TYPE_RGB_ADAS_SEGMENT,  /*adas + 分割*/
+        E_PDSALG_TYPE_RGB_DFL,           /*YOLOv8 + DFL*/
 
         /**********************************************************
          * 一些客户定制的.... 
@@ -156,11 +165,14 @@
         E_CLS_ZEBRA       = 3,     /* 斑马线 */
         E_CLS_MANHOLE     = 4,     /* 沙井盖 */
         E_CLS_FORKLIFT    = 5,     /* 叉车 */
+        E_CLS_MINETRUCK   = 6,     /* 矿车 */
+        E_CLS_EVEHICLE    = 7,     /* 工程汽车 */
 
         E_LIGHT_RED       = 10,    /* 红灯 */
         E_LIGHT_YELLOW    = 11,    /* 黄灯 */
         E_LIGHT_GREEN     = 12,    /* 绿灯 */
-        E_CLS_TRAFFICLIGHT   = 13,     /*交通灯*/
+        E_CLS_TRAFFICLIGHT= 13,    /*交通灯*/
+        E_LIGHT_MIX       = 14,    /* 混合的交通灯 */
         
         E_CLS_PERSON_HAT  = 20,    /* 戴着安全帽的人 */
         E_HAT_RED         = 21,    /* 红色安全帽*/
@@ -201,21 +213,24 @@
         E_CLS_UNI_SIGN_PIC4,            /* 六种通用标志识别 标志4 */
         E_CLS_UNI_SIGN_PIC5,            /* 六种通用标志识别 标志5 */
         E_CLS_UNI_SIGN_PIC6,            /* 六种通用标志识别 标志6 */
+        E_CLS_UNI_SIGN_PIC7,            /* 六种通用标志识别 标志7 */
+        E_CLS_UNI_SIGN_PIC8,            /* 六种通用标志识别 标志8 */
 
-        E_CLS_PALLET,                   /* 栈板 */
+        E_CLS_QRCODE,                   /* 二维码 */
+
+        E_CLS_PALLET = 100,                   /* 栈板 */
         E_CLS_PERSON_VEST,              /*  DCD 人+反光衣 */
         E_CLS_PERSON_HAT_VEST,          /* 栈板 人+安全帽+反光衣*/
 
-        E_CLS_HANDSET,                  /* 包装盒检测 遥控器 */
+        E_CLS_HANDSET ,                  /* 包装盒检测 遥控器 */
         E_CLS_DESICCANT,                /* 包装盒检测 干燥剂 */
-        E_CLS_COVER,                    /* 包装盒检测 盒子 */
+        E_CLS_BOX,                      /* 包装盒检测 盒子*/
+        E_CLS_MONITORLABE,              /* 包装盒检测 显示器标签*/  
 
         E_CLS_SIGN_SUGGEST,             /* 美国建议标志 */
         E_CLS_SIGN_YIELD,               /* 美国yield标志 */
         E_CLS_SIGN_FIN,                 /* 美国fin标志 */
 
-        
-
         E_SPEED_ZONE      = 300,   /* 红色 SPEED ZONE (陈勤芹客户仓库限速标志)*/
         E_END_SPEED_ZONE  = 301,   /* 蓝色 END SPEED ZONE (陈勤芹客户仓库限速标志)*/
 
@@ -225,6 +240,7 @@
         E_CLS_PERSON_HAT_BLUE,      /* 带蓝色安全帽的人 */
 
         
+        
 
         E_CLS_BEAR,                /* 熊*/   
         E_CLS_RESERVE1,            /* 保留类型, 未知 */ 
@@ -254,6 +270,13 @@
         uint32_t u32SpeedLimitLow;  // 限速下限
     }STAlgUSSign;
 
+    typedef struct QRCODE_INFO_
+    {
+        char qrcode_info[1024];
+        uint32_t u32Age;
+        bool bRecognite;
+    }QRCODE_INFO;
+
     /***********************
      * 车道线类型
      ***********************/
@@ -485,7 +508,10 @@
         int32_t s32SpeedIndex;
         pdsa32::E_PDS_OPTICAL_DIC e_PDS_OPTICAL_DIC;
         int32_t s32Age;
+        bool bRoadIntersect; // 道路区域与检测目标是否有交集
 
+        //二维码数据
+        QRCODE_INFO stQRCodeInfo;
 
         // 保留
         float   reserve1;
@@ -583,6 +609,8 @@
         STAlgPLATEDETResult stPlateDetResults[SV_MAX_OUTPUT_PLATE_DET_NUM];// 车牌的结果
         STAlgPLATERECResult stPlateRecResult[SV_MAX_OUTPUT_PLATE_DET_NUM];// 车牌识别的结果
 
+        algcommon::STAlgCommonInfo stAlgCommonInfo; //解耦结构体，给德邻用
+
     }STAlgInfo;
 
 
@@ -636,7 +664,7 @@
         /********************************************************************************
          * 初始化函数，直接把rknn对结构提返回给应用层，全景部门使用
          ********************************************************************************/
-        int32_t AlgInit( char* pModelFile1, rknn_context& ctx, rknn_tensor_mem* & rknn_mem, STAlgParam &parameter_InOut);
+        int32_t AlgInit( char* pModelFile1,rknn_context& ctx,  rknn_tensor_mem* & rknn_mem, STAlgParam &parameter_InOut);
 #endif
         /********************************************************************************
          * 郑南城封装的函数, 暂时不改
Index: src/alg/include/pds_com.h
===================================================================
--- src/alg/include/pds_com.h	(revision 0)
+++ src/alg/include/pds_com.h	(working copy)
@@ -0,0 +1,78 @@
+#ifndef __PDS_ALG_H__
+#define __PDS_ALG_H__
+
+#include "stdint.h"
+#include "stdlib.h"
+#include "stdio.h"
+#include <vector>
+#include <string>
+
+/********************
+algcommon 结构体是在原本结构体基础上抽取出，给德邻用的结构体
+
+********************/
+namespace algcommon
+{
+    typedef enum PDS_ALG_OBJ_CLS 
+    {
+        E_CLS_PERSON      = 0,     /* 人 */
+        E_CLS_CAR         = 1,     /* 车 */
+        E_CLS_OTHERS       = 2      /* 其他 */
+    }EAlgCommonObjectClass;
+
+    typedef enum PDS_ALG_LANE_TYPE {
+        E_LANE_LEFT_SOLID    = 0,     /* 左实线 */
+        E_LANE_LEFT_DASH     = 1,     /* 左虚线 */
+        E_LANE_RIGHT_SOLID   = 2,     /* 右实线 */
+        E_LANE_RIGHT_DASH    = 3,     /* 右虚线 */
+        E_LANE_MIDDLE        = 4,     /* 中线 */
+    } EAlgLaneComType;
+
+    /***********************
+     * 车道线的点, 或者其它的点
+     ***********************/
+    struct STAlgComPoint
+    {
+        float fX;
+        float fY;
+        
+        STAlgComPoint():fX(0),fY(0){}  // 默认值
+        STAlgComPoint(float x, float y):fX(x),fY(y){}
+    };
+
+    struct STAlgComLane
+    {
+        EAlgLaneComType eType;        // 车道线的属性
+        uint32_t u32PointNums;     // 这条线有几个点
+        STAlgComPoint aPoint[100];
+    };
+
+    typedef struct STAlgComResult_
+    {
+        EAlgCommonObjectClass classes;  // 类别, [0]人; [1]车;
+
+        // 该结构体的坐标值, 是基于长宽为1的框, 再乘以实际图像的宽或者高才是实际坐标
+        float  fX1;	   // 左上角x坐标, 区间[0,1]
+        float  fY1;    // 左上角y坐标, 区间[0,1]
+        float  fX2;    // 右下角x坐标, 区间[0,1]
+        float  fY2;    // 右下角y坐标, 区间[0,1]
+        float  fConfidence;   // 目标的置信度
+        float  fDistance;     // 距离, 单位mm, 被检测的目标与相机之间的距离
+        float  fArea;  // 框的面积, 也是用归一化坐标算出来的, 要乘以原图的宽和高才是实际面积
+        uint32_t u32ID;  // 跟踪的id号
+        
+    }STAlgCommonResult;
+
+    typedef struct STAlgComInfo_
+    {
+        uint32_t u32Nums;			// 检测到的目标数量
+        STAlgCommonResult stResults[100];	// 目标信息
+        
+        uint32_t u32LaneNums;   // 车道线的数量
+        STAlgComLane aLane[10];    // 车道线
+
+    }STAlgCommonInfo;
+
+}
+
+#endif
\ No newline at end of file
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 5578)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -4501,6 +4501,74 @@
     return bAlarmOut;
 }
 
+sint32 pd_UNISIGN_AcitveColor(SV_BOOL bSignOn, SV_BOOL bSignOff, sint32 u32Color)
+{
+    sint32 s32Ret;
+    static sint32 s32Cnt[3] = {0};
+    static SV_BOOL bLastStatus[3] = {SV_FALSE, SV_FALSE, SV_FALSE};
+    SV_BOOL bNowStatus = SV_FALSE;
+    static SV_BOOL bAlarmOut[3] = {SV_FALSE, SV_FALSE, SV_FALSE};
+    SV_BOOL bLimit;
+    static SV_BOOL bFirstRun[3] = {SV_FALSE, SV_FALSE, SV_FALSE};
+    PD_ADD_INFO_S stPdAddInfo = {0};
+
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_201921) && bFirstRun)
+    {
+        pd_Get_Add_Info(&stPdAddInfo);
+        bAlarmOut[u32Color] = stPdAddInfo.bLastSingleAlarmOut;
+        bFirstRun[u32Color] = SV_FALSE;
+        return bAlarmOut[u32Color];
+    }
+
+    if (bSignOn && bSignOff)
+    {
+        bNowStatus = SV_TRUE;
+    }
+    else if (bSignOn && !bSignOff)
+    {
+        bNowStatus = SV_TRUE;
+    }
+    else if (!bSignOn && bSignOff)
+    {
+        bNowStatus = SV_FALSE;
+    }
+    else
+    {
+        if (BOARD_IsCustomer(BOARD_C_ADA32V2_201186))
+        {
+            bNowStatus = SV_FALSE;
+        }
+        else
+        {
+            return bAlarmOut[u32Color];
+        }
+    }
+
+    /* 连续一定数量的帧是相同的标志才认为有效 */
+    if (s32Cnt[u32Color] == 0)
+    {
+        bLastStatus[u32Color] = bNowStatus;
+        s32Cnt[u32Color]++;
+        return bAlarmOut[u32Color];
+    }
+    else if (bLastStatus[u32Color] != bNowStatus)
+    {
+        s32Cnt[u32Color] = 0;
+        bLastStatus[u32Color] = bNowStatus;
+        return bAlarmOut[u32Color];
+    }
+    else if (s32Cnt[u32Color] < 5)
+    {
+        s32Cnt[u32Color]++;
+        return bAlarmOut[u32Color];
+    }
+
+    s32Cnt[u32Color] = 0;
+    bAlarmOut[u32Color] = bNowStatus;
+
+    return bAlarmOut[u32Color];
+}
+
 /* 202462、201921等客户通用标志解析, 需要用2根以上的触发线进行输出 */
 pdsa32::EAlgObjectClass pd_UNISIGN_Acitve_special(pdsa32::EAlgObjectClass eClasses)
 {
@@ -5360,6 +5428,40 @@
         eClassesOut = eClasses;
     }
 
+    // 指定客户 do 指定图标控制输出 图标指定是否检测
+    if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
+    {
+        // if (pdsa32::E_CLS_UNI_SIGN_PIC1 == eClasses)
+        // {
+        //     bSignOn = SV_TRUE;
+        // }
+        // else if (pdsa32::E_CLS_UNI_SIGN_PIC4 == eClasses)
+        // {
+        //     bSignOff = SV_TRUE;
+        // }
+        
+        // 匹配检测类型-控制GPIO输出-三条线使用同一个结果设置
+        if (eClasses >= pdsa32::E_CLS_UNI_SIGN_PIC1 && eClasses <= pdsa32::E_CLS_UNI_SIGN_PIC6)
+        {
+            if (0 == m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[eClasses - pdsa32::E_CLS_UNI_SIGN_PIC1])
+            {
+                /* 关闭 */
+                bSignOff = SV_TRUE;
+            }
+            else if(1 == m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[eClasses - pdsa32::E_CLS_UNI_SIGN_PIC1])
+            {
+                /* 打开 */
+                bSignOn = SV_TRUE;
+            }
+            else if(2 == m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[eClasses - pdsa32::E_CLS_UNI_SIGN_PIC1])
+            {
+                ;/* 禁止操作-不检测这个类型 */
+            }
+        }
+        
+        //print_level(SV_INFO, "bSignOn = %d, bSignOff=%d\n", bSignOn, bSignOff);
+    }
+
     return SV_SUCCESS;
 }
 
@@ -6141,7 +6243,7 @@
     return NULL;
 }
 
-/* PD算法后处理线程 */
+/* PD算法后处理线程 pyl */
 void * pd_alg_postProcessing_Body(void *pvArg)
 {
     sint32 s32Ret = 0, i, j;
@@ -6219,6 +6321,11 @@
 
     PD_ADD_INFO_S stPdAddInfo_Tmp = {0};  /* 用于保存上次的检测状态 */
 
+    // 202661客户专用
+    SV_BOOL baUnisignAlarmOut[3] = {SV_FALSE, SV_FALSE, SV_FALSE};    /* 检测通用标志，触发是否输出高电平 */
+    SV_BOOL baSignOn[3]          = {SV_FALSE, SV_FALSE, SV_FALSE};
+    SV_BOOL baSignOff[3]         = {SV_FALSE, SV_FALSE, SV_FALSE};
+
     /* 测距模式标定参数*/
     PD_GUI_IMG_S stCaliImgSrc = {0};
     PD_GUI_IMG_S stCaliImgDst = {0};
@@ -6281,10 +6388,29 @@
         stPdDumpInfo.s64TimeStamp = tvNow.tv_sec * 1000 + tvNow.tv_nsec /1000000;
 
         if((BOARD_IsCustomer(BOARD_C_ADA32V2_202319) || BOARD_IsCustomer(BOARD_C_ADA32V2_202668)) && s32HeartBeatMs > 0)
-        {
+        { 
             s32HeartBeatMs -= u32StepTimeMs;
         }
 
+        // pyl: 获取网页配置项的值, 用于配置检测信号控制电平输出
+        // print_level(SV_INFO, "红: bAlarmOutSwitch=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].bAlarmOutSwitch);
+        // print_level(SV_INFO, "黄: bAlarmOutSwitch=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].bAlarmOutSwitch);
+        // print_level(SV_INFO, "绿: bAlarmOutSwitch=%d \n\n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].bAlarmOutSwitch);
+
+        //print_level(SV_INFO, "红: bAlarmOutSwitch=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32AlarmTypeMask);
+        //print_level(SV_INFO, "黄: bAlarmOutSwitch=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].s32AlarmTypeMask);
+        //print_level(SV_INFO, "绿: bAlarmOutSwitch=%d \n\n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32AlarmTypeMask);
+
+        // print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[0]);
+        // print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[1]);
+        // print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[2]);
+        // print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[3]);
+        // print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[4]);
+        // print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astPdSign[5]);
+        //print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask);
+        //print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].s32Zonemask);
+        //print_level(SV_INFO, "标识: astPdSign=%d \n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32Zonemask);
+
         /* P操作进入临界区 */
         s32Ret = AS_PostProcess_P();
         if (SV_SUCCESS != s32Ret)
@@ -6364,7 +6490,6 @@
         }
 #endif
 
-
         for (i=0; i<pstPdInfo->u32ChnNum; i++)
         {
             if(apstPdsParam[i]->s32PdRedInterval >= 0 && s32RedTimeMs[i] > 0)
@@ -6520,7 +6645,8 @@
                 continue;
             }
 
-            if(pstPdInfo->bSkipCar[s32CurChn] && stPdResult.stResults[i].classes == pdsa32::E_CLS_CAR)
+            if(pstPdInfo->bSkipCar[s32CurChn] && (stPdResult.stResults[i].classes == pdsa32::E_CLS_CAR || stPdResult.stResults[i].classes == pdsa32::E_CLS_FORKLIFT \
+                || stPdResult.stResults[i].classes == pdsa32::E_CLS_MINETRUCK || stPdResult.stResults[i].classes == pdsa32::E_CLS_EVEHICLE))
             {
                 continue;
             }
@@ -6551,6 +6677,12 @@
                     continue;
             }
 
+            // 指定专用客户使用全屏检测
+            if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
+            {
+                ;// enRoi = PD_ROI_BLUE;    // 指定客户，测试版本
+            }
+
             /* 200055 VT客户需要绿色区域只检测车，红黄区域只检测人 */
             if (BOARD_IsCustomer(BOARD_C_ADA32V2_VT) || BOARD_IsCustomer(BOARD_C_ADA32V2_VT_A))
             {
@@ -6566,7 +6698,7 @@
             }
 
             enRoi = pd_CheckRoiUniSign(enRoi, &stPdResult.stResults[i]);
-
+            //print_level(SV_INFO, "1. enRoi=%d ... \n", enRoi);
 #if defined(BOARD_ADA46V1)
             if (apstPdsParam[s32CurChn]->bPdTestMode == SV_FALSE && enRoi <= PD_ROI_GREEN && !pd_R151_Check_TestCase(s32GpsSpeed, enRoi, &stPdResult.stResults[i]))
             {
@@ -6618,7 +6750,6 @@
             /* 检测到通用标志时，判断是需要激活还是不激活触发输出 */
             pd_CheckSignOnOff(stPdResult.stResults[i].classes, bSignOn, bSignOff, eClassTmp);
 
-
             if (BOARD_IsCustomer(BOARD_C_ADA32V2_201338))
             {
                 if (pdsa32::E_CLS_SIGN_SPEED_CHN == stPdResult.stResults[i].classes)
@@ -6628,6 +6759,35 @@
                 }
             }
 
+            // 专用客户
+            if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
+            {
+                //print_level(SV_INFO, "pd_CheckSignOnOff enRoi=%d ...\n", enRoi);
+                switch (enRoi)
+                {
+                    case PD_ROI_RED:
+                        pd_CheckSignOnOff(stPdResult.stResults[i].classes, baSignOn[0], baSignOff[0], eClassTmp);
+                        break;
+                    case PD_ROI_YELLOW:
+                        pd_CheckSignOnOff(stPdResult.stResults[i].classes, baSignOn[1], baSignOff[1], eClassTmp);
+                        break;
+                    case PD_ROI_GREEN:
+                        pd_CheckSignOnOff(stPdResult.stResults[i].classes, baSignOn[2], baSignOff[2], eClassTmp);
+                        break;
+                    case PD_ROI_BLUE:
+        
+                        break;
+                    default:
+                        break;
+                }
+                //print_level(SV_INFO, "baSignOn[0]=%d ...\n",  baSignOn[0]);
+                //print_level(SV_INFO, "baSignOff[0]=%d ...\n", baSignOff[0]);
+                //print_level(SV_INFO, "baSignOn[1]=%d ...\n",  baSignOn[1]);
+                //print_level(SV_INFO, "baSignOff[1]=%d ...\n", baSignOff[1]);
+                //print_level(SV_INFO, "baSignOn[2]=%d ...\n",  baSignOn[2]);
+                //print_level(SV_INFO, "baSignOff[2]=%d ...\n", baSignOff[2]);
+            }
+
             //print_level(SV_DEBUG, "enRoi:%d, classes: %d, confidence:%f, (%f,%f) (%f,%f)\n", enRoi, stPdResult.stResults[i].classes, stPdResult.stResults[i].fConfidence, stPdResult.stResults[i].fX1, stPdResult.stResults[i].fY1, stPdResult.stResults[i].fX2, stPdResult.stResults[i].fY2);
 
             fconfidence = stPdResult.stResults[i].fConfidence;
@@ -6747,7 +6907,7 @@
                 case PD_ROI_BLUE:
 					if (BOARD_IsCustomer(BOARD_C_ADA32V2_202626) || BOARD_IsCustomer(BOARD_C_ADA32V2_202319) || BOARD_IsCustomer(BOARD_C_ADA32V2_201338) \
                         || BOARD_IsCustomer(BOARD_C_ADA32V2_202462) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598) || BOARD_IsCustomer(BOARD_C_ADA32V2_201186) \
-                        || BOARD_IsCustomer(BOARD_C_ADA32V2_201067) || BOARD_IsCustomer(BOARD_C_ADA32V2_200585) || BOARD_IsCustomer(BOARD_C_ADA32V2_201921))
+                        || BOARD_IsCustomer(BOARD_C_ADA32V2_201067) || BOARD_IsCustomer(BOARD_C_ADA32V2_200585) || BOARD_IsCustomer(BOARD_C_ADA32V2_201921) || BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
             		{
                     	bBlankClass = SV_FALSE;
                     }
@@ -6773,6 +6933,10 @@
             pd_DumpInfo_Correct(&stPdDumpInfo, aclass, enRoi, s32RedTimeMs[s32CurChn], s32YellowTimeMs[s32CurChn], s32GreenTimeMs[s32CurChn], \
                 apstPdsParam[s32CurChn]->s32PdRedInterval, apstPdsParam[s32CurChn]->s32PdYellowInterval, apstPdsParam[s32CurChn]->s32PdGreenInterval);
 
+            //print_level(SV_INFO, "stPdDumpInfo.bAlarmOut[0]=%d\n", stPdDumpInfo.bAlarmOut[0]);
+            //print_level(SV_INFO, "stPdDumpInfo.bAlarmOut[1]=%d\n", stPdDumpInfo.bAlarmOut[1]);
+            //print_level(SV_INFO, "stPdDumpInfo.bAlarmOut[2]=%d\n", stPdDumpInfo.bAlarmOut[2]);
+
             if (BOARD_IsCustomer(BOARD_C_ADA32V2_SAFE))
             {
                 if (s32RoiMask >> 0 & 0x01)
@@ -6974,6 +7138,25 @@
             }
         }
 
+        if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
+        {
+            baUnisignAlarmOut[0] = (SV_BOOL)pd_UNISIGN_AcitveColor(baSignOn[0], baSignOff[0], 0);
+            baSignOn[0] = SV_FALSE;
+            baSignOff[0] = SV_FALSE;
+
+            baUnisignAlarmOut[1] = (SV_BOOL)pd_UNISIGN_AcitveColor(baSignOn[1], baSignOff[1], 1);
+            baSignOn[1] = SV_FALSE;
+            baSignOff[1] = SV_FALSE;
+
+            baUnisignAlarmOut[2] = (SV_BOOL)pd_UNISIGN_AcitveColor(baSignOn[2], baSignOff[2], 2);
+            baSignOn[2] = SV_FALSE;
+            baSignOff[2] = SV_FALSE;
+
+            //print_level(SV_INFO, "pd_UNISIGN_Acitve(baSignOn[0], baSignOff[0]) = %d ... \n", pd_UNISIGN_Acitve(baSignOn[0], baSignOff[0]));
+            //print_level(SV_INFO, "pd_UNISIGN_Acitve(baSignOn[0], baSignOff[0]) = %d ... \n", pd_UNISIGN_Acitve(baSignOn[1], baSignOff[1]));
+            //print_level(SV_INFO, "pd_UNISIGN_Acitve(baSignOn[0], baSignOff[0]) = %d ... \n", pd_UNISIGN_Acitve(baSignOn[2], baSignOff[2]));
+        }
+
         if (BOARD_IsCustomer(BOARD_C_ADA32V2_202462) || BOARD_IsCustomer(BOARD_C_ADA32V2_201921))
         {
             if (pstPdInfo->stCfgParam.stAlgCh2.stPdsParam.enPdsModel >= E_PDS_UNISIGN && pstPdInfo->stCfgParam.stAlgCh2.stPdsParam.enPdsModel <= E_PDS_PC_UNISIGN)
@@ -7089,41 +7272,6 @@
             stPdDumpInfo.s32GreenRoiNum = 0;
         }
 
-        if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
-        {
-            if(apstPdsParam[s32CurChn]->bPdAlarmOutRed == SV_FALSE)
-            {
-                stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
-            }
-
-            if(apstPdsParam[s32CurChn]->bPdAlarmOutYellow == SV_FALSE)
-            {
-                stPdDumpInfo.bAlarmOut[1] = SV_FALSE;
-            }
-
-            if(apstPdsParam[s32CurChn]->bPdAlarmOutGreen == SV_FALSE)
-            {
-                stPdDumpInfo.bAlarmOut[2] = SV_FALSE;
-            }
-        }
-        else
-        {
-            if(apstPdsParam[s32CurChn]->astTriggerSrc[0].bAlarmOutSwitch == SV_FALSE)
-            {
-                stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
-            }
-
-            if(apstPdsParam[s32CurChn]->astTriggerSrc[1].bAlarmOutSwitch == SV_FALSE)
-            {
-                stPdDumpInfo.bAlarmOut[1] = SV_FALSE;
-            }
-
-            if(apstPdsParam[s32CurChn]->astTriggerSrc[2].bAlarmOutSwitch == SV_FALSE)
-            {
-                stPdDumpInfo.bAlarmOut[2] = SV_FALSE;
-            }
-        }
-
         if (BOARD_IsCustomer(BOARD_C_ADA32V2_202626))
         {
             if (bUnisignAlarmOut)
@@ -7153,6 +7301,148 @@
             stPdDumpInfo.bAlarmOut[2] = SV_TRUE;    // 201186客户检测到通用标志后，绿色触发线触发输出
         }
 
+        // 三个引脚根据配置决定是否输出信号
+        if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
+        {
+            // 检测区域-红
+            if (SV_TRUE == baUnisignAlarmOut[0])
+            {
+                // 启动报警类型-通用图标, 处理红色区域，分别处理三条引脚
+                if (0x08 == (0x08 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32AlarmTypeMask))
+                {   
+                    // 引脚
+                    if (0x01 == (0x01 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+                    {
+                        stPdDumpInfo.bAlarmOut[0] = SV_TRUE;
+                    }
+                    
+                    // if (0x02 == (0x02 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+                    // {
+                    //     stPdDumpInfo.bAlarmOut[1] = SV_TRUE;
+                    // }
+
+                    // if (0x04 == (0x04 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+                    // {
+                    //     stPdDumpInfo.bAlarmOut[2] = SV_TRUE;
+                    // }
+                }
+            }
+//            else
+//            {
+//                if (0x08 == (0x08 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32AlarmTypeMask))
+//                {   
+//                    if (0x01 == (0x01 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+//                    {
+//                        stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
+//                    }
+//                    
+//                    // if (0x02 == (0x02 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+//                    // {
+//                    //     stPdDumpInfo.bAlarmOut[1] = SV_FALSE;
+//                    // }
+//
+//                    // if (0x04 == (0x04 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+//                    // {
+//                    //     stPdDumpInfo.bAlarmOut[2] = SV_FALSE;
+//                    // }
+//                }
+//            }
+
+            // 检测区域-黄
+            if (SV_TRUE == baUnisignAlarmOut[1])
+            {
+                // 启动报警类型-通用图标, 处理红色区域，分别处理三条引脚
+                if (0x08 == (0x08 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].s32AlarmTypeMask))
+                {   
+                    // 引脚
+                    // if (0x01 == (0x01 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].s32Zonemask))
+                    // {
+                    //     stPdDumpInfo.bAlarmOut[0] = SV_TRUE;
+                    // }
+                    
+                    if (0x02 == (0x02 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].s32Zonemask))
+                    {
+                        stPdDumpInfo.bAlarmOut[1] = SV_TRUE;
+                    }
+
+                    // if (0x04 == (0x04 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].s32Zonemask))
+                    // {
+                    //     stPdDumpInfo.bAlarmOut[2] = SV_TRUE;
+                    // }
+                }
+            }
+//            else
+//            {
+//                if (0x08 == (0x08 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32AlarmTypeMask))
+//                {   
+//                    // if (0x01 == (0x01 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+//                    // {
+//                    //     stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
+//                    // }
+//                    
+//                    if (0x02 == (0x02 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+//                    {
+//                        stPdDumpInfo.bAlarmOut[1] = SV_FALSE;
+//                        print_level(SV_INFO, "111 stPdDumpInfo.bAlarmOut[1]=%d\n", stPdDumpInfo.bAlarmOut[1]);
+//                    }
+//
+//                    // if (0x04 == (0x04 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].s32Zonemask))
+//                    // {
+//                    //     stPdDumpInfo.bAlarmOut[2] = SV_FALSE;
+//                    // }
+//                }
+//            }
+
+            // 检测区域-绿
+            if (SV_TRUE == baUnisignAlarmOut[2])
+            {
+                // 启动报警类型-通用图标, 处理红色区域，分别处理三条引脚
+                if (0x08 == (0x08 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32AlarmTypeMask))
+                {   
+                    // 引脚
+                    // if (0x01 == (0x01 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32Zonemask))
+                    // {
+                    //     stPdDumpInfo.bAlarmOut[0] = SV_TRUE;
+                    // }
+                    
+                    // if (0x02 == (0x02 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32Zonemask))
+                    // {
+                    //     stPdDumpInfo.bAlarmOut[1] = SV_TRUE;
+                    // }
+
+                    if (0x04 == (0x04 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32Zonemask))
+                    {
+                        stPdDumpInfo.bAlarmOut[2] = SV_TRUE;
+                    }
+                }
+            }
+//            else
+//            {
+//                if (0x08 == (0x08 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32AlarmTypeMask))
+//                {   
+//                    // if (0x01 == (0x01 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32Zonemask))
+//                    // {
+//                    //     stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
+//                    // }
+//                    
+//                    // if (0x02 == (0x02 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32Zonemask))
+//                    // {
+//                    //     stPdDumpInfo.bAlarmOut[1] = SV_FALSE;
+//                    // }
+//
+//                    if (0x04 == (0x04 & m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].s32Zonemask))
+//                    {
+//                        stPdDumpInfo.bAlarmOut[2] = SV_FALSE;
+//                        print_level(SV_INFO, "222 stPdDumpInfo.bAlarmOut[2]=%d\n", stPdDumpInfo.bAlarmOut[2]);
+//                    }
+//                }
+//            }
+        }
+        //print_level(SV_INFO, "enROI=%d ... \n", enRoi);
+        //print_level(SV_INFO, "baUnisignAlarmOut[0]=%d ... \n", baUnisignAlarmOut[0]);
+        //print_level(SV_INFO, "baUnisignAlarmOut[1]=%d ... \n", baUnisignAlarmOut[1]);
+        //print_level(SV_INFO, "baUnisignAlarmOut[2]=%d ... \n", baUnisignAlarmOut[2]);
+
         if (BOARD_IsCustomer(BOARD_C_ADA32V2_201338) || BOARD_IsCustomer(BOARD_C_ADA32V2_200598) || BOARD_IsCustomer(BOARD_C_ADA32V2_201067)\
             || BOARD_IsCustomer(BOARD_C_ADA32V2_200585) || BOARD_IsCustomer(BOARD_C_ADA32V2_201921))
         {
@@ -7289,9 +7579,55 @@
             }
         }
 
-        bRedAlarmOut = stPdDumpInfo.bAlarmOut[0];
+        // 调整位置
+        if (BOARD_IsCustomer(BOARD_C_ADA32V2_201623))
+        {
+            if(apstPdsParam[s32CurChn]->bPdAlarmOutRed == SV_FALSE)
+            {
+                stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
+            }
+
+            if(apstPdsParam[s32CurChn]->bPdAlarmOutYellow == SV_FALSE)
+            {
+                stPdDumpInfo.bAlarmOut[1] = SV_FALSE;
+            }
+
+            if(apstPdsParam[s32CurChn]->bPdAlarmOutGreen == SV_FALSE)
+            {
+                stPdDumpInfo.bAlarmOut[2] = SV_FALSE;
+            }
+        }
+        else
+        {
+            if(apstPdsParam[s32CurChn]->astTriggerSrc[0].bAlarmOutSwitch == SV_FALSE)
+            {
+                stPdDumpInfo.bAlarmOut[0] = SV_FALSE;
+            }
+
+            if(apstPdsParam[s32CurChn]->astTriggerSrc[1].bAlarmOutSwitch == SV_FALSE)
+            {
+                stPdDumpInfo.bAlarmOut[1] = SV_FALSE;
+            }
+
+            if(apstPdsParam[s32CurChn]->astTriggerSrc[2].bAlarmOutSwitch == SV_FALSE)
+            {
+                stPdDumpInfo.bAlarmOut[2] = SV_FALSE;
+            }
+        }
+
+        // 测试-输出三根硬引脚的电平结果
+        //for (size_t i = 0; i < 3; i++)
+        //{
+        //    print_level(SV_INFO, "stPdDumpInfo.bAlarmOut[%d] = %d\n", i, stPdDumpInfo.bAlarmOut[i]);
+        //}
+        
+        //print_level(SV_INFO, "红: bAlarmOutSwitch=%d \n",   m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[0].bAlarmOutSwitch);
+        //print_level(SV_INFO, "黄: bAlarmOutSwitch=%d \n",   m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[1].bAlarmOutSwitch);
+        //print_level(SV_INFO, "绿: bAlarmOutSwitch=%d \n\n", m_stPdInfo.stCfgParam.stAlgCh2.stPdsParam.astTriggerSrc[2].bAlarmOutSwitch);
+
+        bRedAlarmOut    = stPdDumpInfo.bAlarmOut[0];
         bYellowAlarmOut = stPdDumpInfo.bAlarmOut[1];
-        bGreenAlarmOut = stPdDumpInfo.bAlarmOut[2];
+        bGreenAlarmOut  = stPdDumpInfo.bAlarmOut[2];
 
         stPdDumpInfo.s32GpsSpeed = s32GpsSpeed;
         pd_DumpInfo(s32CurChn, &stPdDumpInfo);
@@ -7493,7 +7829,7 @@
                 }
 
                 if(BOARD_IsADA38_R159() || BOARD_IsHardware(BOARD_S_ADA46V1_FRONT) || BOARD_IsHardware(BOARD_S_ADA46V1_REAR))
-                {
+                {
 
                     s32Ret = Msg_submitEvent(EP_RS485, OP_EVENT_PD_PERSON_NUM, &stMsgPkt);
                     if (SV_SUCCESS != s32Ret)
@@ -8163,6 +8499,10 @@
                         {
                             apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC_UNISIGN);
                         }
+                        else if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
+                        {
+                            apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC_EVEHICLE);
+                        }
                         else
                         {
 						    apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC);
Index: src/common/config/config.c
===================================================================
--- src/common/config/config.c	(revision 5578)
+++ src/common/config/config.c	(working copy)
@@ -466,6 +466,12 @@
     	|| 0 == strcmp(pszName, "pdVLheight")
     	|| 0 == strcmp(pszName, "pdIRheight")
         || 0 == strcmp(pszName, "accDelay")
+        || 0 == strcmp(pszName, "pdSign1")
+        || 0 == strcmp(pszName, "pdSign2")
+        || 0 == strcmp(pszName, "pdSign3")
+        || 0 == strcmp(pszName, "pdSign4")
+        || 0 == strcmp(pszName, "pdSign5")
+        || 0 == strcmp(pszName, "pdSign6")
         )
     {
         return MXML_INTEGER;
@@ -866,6 +872,7 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 
         sprintf(szCmd, "cp %s %s", CONFIG_TMP_PATH, m_stConfigInfo.szFileBak1);
         s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
@@ -873,6 +880,7 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 
         sprintf(szCmd, "cp %s %s", CONFIG_TMP_PATH, m_stConfigInfo.szFileBak2);
         s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
@@ -880,6 +888,7 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 
         if (m_stConfigInfo.bDefSaving)
         {
@@ -889,6 +898,7 @@
             {
                 print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
             }
+            sync();
 
 #if defined(PLATFORM_SSC335)
             s32Ret = CONFIG_ReloadDefaultFile();
@@ -906,6 +916,7 @@
             {
                 print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
             }
+            sync();
 
             sprintf(szCmd, "cp %s %s", UDHCPD_ETH_CONF, UDHCPD_ETH_DEFAULT);
             s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
@@ -913,6 +924,7 @@
             {
                 print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
             }
+            sync();
 #endif
             m_stConfigInfo.bDefSaving = SV_FALSE;
         }
@@ -1476,6 +1488,18 @@
     return SV_SUCCESS;
 }
 
+
+sint32 CONFIG_Init_ALG(char *pszFilePath, char *pszFileBak1, char *pszFileBak2, char *pszFileDefault)
+{
+    sint32 s32Ret = 0;
+
+    sleep_ms(3000); //alg延时3s执行，属于临时的改动，为了让多进程调用错开时间，后面要做成多进程间之间互斥
+    s32Ret = CONFIG_Init(pszFilePath, pszFileBak1, pszFileBak2, pszFileDefault);
+
+    return s32Ret;
+}
+
+
 sint32 CONFIG_Init(char *pszFilePath, char *pszFileBak1, char *pszFileBak2, char *pszFileDefault)
 {
     sint32 s32Ret = 0;
@@ -1484,6 +1508,10 @@
     mxml_node_t *pstTreeDef = NULL, *pstConfigDef = NULL;
     char szCmd[64];
     sint32 s32ErrCnt = 0;
+    SV_BOOL bConfig_Origin_Bak1 = SV_FALSE;   //config.xml和config_bak1.xml是否相同
+    SV_BOOL bConfig_Origin_Bak2 = SV_FALSE;   //config.xml和config_bak2.xml是否相同
+    SV_BOOL bConfig_Bak1_Bak2 = SV_FALSE;     //config_bak1.xml和config_bak2.xml是否相同
+    
 
     if (NULL == pszFilePath || NULL == pszFileBak1 || NULL == pszFileBak2 || NULL == pszFileDefault)
     {
@@ -1507,6 +1535,7 @@
             print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
             return SV_FAILURE;
         }
+        sync();
 
         sprintf(szCmd, "cp %s %s", pszFileDefault, pszFilePath);
         s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
@@ -1514,6 +1543,7 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 
         sprintf(szCmd, "cp %s %s", pszFileDefault, pszFileBak1);
         s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
@@ -1521,6 +1551,7 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 
         sprintf(szCmd, "cp %s %s", pszFileDefault, pszFileBak2);
         s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
@@ -1528,6 +1559,7 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 
 #if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) || defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
         sprintf(szCmd,"cp %s %s", UDHCPD_DEFAULT, UDHCPD_CONF);
@@ -1536,6 +1568,7 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 
         sprintf(szCmd, "cp %s %s", UDHCPD_ETH_DEFAULT, UDHCPD_ETH_CONF);
         s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
@@ -1543,30 +1576,117 @@
         {
             print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
         }
+        sync();
 #endif
 
         CONFIG_FlashProtection(SV_TRUE);
     }
 
+
+    /* 对比config.xml、config_bak1.xml、 config_bak2.xml，其中一个与另外两个不同，则说明这个文件出了问题，需要替换 */
     sprintf(szCmd, "cmp %s %s", pszFilePath, pszFileBak1);
     s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
+    if (0 == s32Ret)
+    {
+        bConfig_Origin_Bak1 = SV_TRUE;
+    }
+    else
+    {
+        bConfig_Origin_Bak1 = SV_FALSE;
+    }
 
+    sprintf(szCmd, "cmp %s %s", pszFilePath, pszFileBak2);
+    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
     if (0 == s32Ret)
     {
-        sprintf(szCmd, "cp %s /var/config.xml_tmp", pszFilePath);
+        bConfig_Origin_Bak2 = SV_TRUE;
     }
     else
     {
-        print_level(SV_WARN, "cmp %s %s is different.\n", pszFilePath, pszFileBak1);
-        sprintf(szCmd, "cp %s /var/config.xml_tmp", pszFileBak2);
+        bConfig_Origin_Bak2 = SV_FALSE;
     }
 
+    sprintf(szCmd, "cmp %s %s", pszFileBak1, pszFileBak2);
     s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
+    if (0 == s32Ret)
+    {
+        bConfig_Bak1_Bak2 = SV_TRUE;
+    }
+    else
+    {
+        bConfig_Bak1_Bak2 = SV_FALSE;
+    }
+
+    /* 如果3个config文件中，存在一个不同的，则替换掉这个xml文件 */
+    if (SV_TRUE == bConfig_Origin_Bak1 && SV_TRUE == bConfig_Origin_Bak2 && SV_TRUE == bConfig_Bak1_Bak2)
+    {
+        print_level(SV_INFO, "The 3 configuration files are the same.\n");
+    }
+    else
+    {
+        if (SV_TRUE == bConfig_Origin_Bak1)
+        {
+            print_level(SV_WARN, "%s is different.\n", pszFileBak2);
+            sprintf(szCmd, "cp %s %s", pszFilePath, pszFileBak2);
+            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
+            if (0 != s32Ret)
+            {
+                print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
+            }
+            sync();
+        }
+        else
+        {
+            if (SV_TRUE == bConfig_Origin_Bak2)
+            {
+                print_level(SV_WARN, "%s is different.\n", pszFileBak1);
+                sprintf(szCmd, "cp %s %s", pszFilePath, pszFileBak1);
+                s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
+                if (0 != s32Ret)
+                {
+                    print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
+                }
+                sync();
+            }
+            else
+            {
+                print_level(SV_WARN, "%s is different.\n", pszFilePath);
+                sprintf(szCmd, "cp %s %s", pszFileBak2, pszFilePath);
+                s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
+                if (0 != s32Ret)
+                {
+                    print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
+                }
+                sync();
+            }
+        }
+            
+    }
+
+    sprintf(szCmd, "cp %s /var/config.xml_tmp", pszFilePath);
+    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
     if (0 != s32Ret)
     {
         print_level(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
     }
+    sync();
 
+    /* 先检查cp完后的临时文件，保证XML格式是完整的。如果失败返回到上面的init地点 */
+    fp = fopen("/var/config.xml_tmp", "r");
+    if (NULL == fp)
+    {
+        print_level(SV_ERROR, "fopen file: %s failed.\n", CONFIG_TMP_PATH);
+        return SV_FAILURE;
+    }
+    pstTree = mxmlLoadFile(NULL, fp, mxml_load_cb);
+    if (NULL == pstTree)
+    {
+        print_level(SV_ERROR, "mxmlLoadFile file: /var/config.xml_tmp failed. error: %s\n", strerror(errno));
+        fclose(fp);
+        goto init;
+    }
+    fclose(fp);
+
     rename("/var/config.xml_tmp", CONFIG_TMP_PATH);
     fp = fopen(CONFIG_TMP_PATH, "r");
     if (NULL == fp)
@@ -1583,29 +1703,31 @@
         {
             s32ErrCnt = 0;
             print_level(SV_ERROR, "get config file failed.\n");
+
+            if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
+            {
+                print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
+                return SV_FAILURE;
+            }
+            sprintf(szCmd, "rm %s %s %s", pszFilePath, pszFileBak1, pszFileBak2);
+            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
+            if (SV_SUCCESS != s32Ret)
+            {
+                print_level(SV_ERROR, "cmd: %s failed. [err:%s]\n", szCmd, strerror(errno));
+                return SV_FAILURE;
+            }
+            if (SV_SUCCESS != CONFIG_FlashProtection(SV_TRUE))
+            {
+                print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
+                return SV_FAILURE;
+            }
+
+            fclose(fp);
             return SV_FAILURE;
         }
         print_level(SV_ERROR, "mxmlLoadFile file: %s failed. error: %s\n", CONFIG_TMP_PATH, strerror(errno));
         fclose(fp);
 
-        if (SV_SUCCESS != CONFIG_FlashProtection(SV_FALSE))
-        {
-            print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
-            return SV_FAILURE;
-        }
-        sprintf(szCmd, "rm %s %s %s", pszFilePath, pszFileBak1, pszFileBak2);
-        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
-        if (SV_SUCCESS != s32Ret)
-        {
-            print_level(SV_ERROR, "cmd: %s failed. [err:%s]\n", szCmd, strerror(errno));
-            return SV_FAILURE;
-        }
-        if (SV_SUCCESS != CONFIG_FlashProtection(SV_TRUE))
-        {
-            print_level(SV_ERROR, "CONFIG_FlashProtection failed.\n");
-            return SV_FAILURE;
-        }
-
         goto init;
     }
     s32ErrCnt = 0;
@@ -7871,6 +7993,91 @@
     {
         mxmlSetInteger(pstBlkSize, pstAlgParam->stAlgCh2.stPdsParam.u32BlkSize);
     }
+#if 1
+    pstTmp = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdSign1", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstTmp)
+    {
+        pstTmp = mxmlNewElement(pstAlg, "pdSign1");
+        if (NULL != pstTmp)
+        {
+            mxmlNewInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[0]);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[0]);
+    }
+	
+    pstTmp = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdSign2", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstTmp)
+    {
+        pstTmp = mxmlNewElement(pstAlg, "pdSign2");
+        if (NULL != pstTmp)
+        {
+            mxmlNewInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[1]);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[1]);
+    }
+	
+    pstTmp = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdSign3", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstTmp)
+    {
+        pstTmp = mxmlNewElement(pstAlg, "pdSign3");
+        if (NULL != pstTmp)
+        {
+            mxmlNewInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[2]);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[2]);
+    }
+	
+    pstTmp = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdSign4", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstTmp)
+    {
+        pstTmp = mxmlNewElement(pstAlg, "pdSign4");
+        if (NULL != pstTmp)
+        {
+            mxmlNewInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[3]);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[3]);
+    }
+	
+    pstTmp = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdSign5", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstTmp)
+    {
+        pstTmp = mxmlNewElement(pstAlg, "pdSign5");
+        if (NULL != pstTmp)
+        {
+            mxmlNewInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[4]);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[4]);
+    }
+	
+    pstTmp = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdSign6", NULL, NULL, MXML_DESCEND);
+    if (NULL == pstTmp)
+    {
+        pstTmp = mxmlNewElement(pstAlg, "pdSign6");
+        if (NULL != pstTmp)
+        {
+            mxmlNewInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[5]);
+        }
+    }
+    else
+    {
+        mxmlSetInteger(pstTmp, pstAlgParam->stAlgCh2.stPdsParam.astPdSign[5]);
+    }
+#endif
 
 #endif
 
@@ -11836,8 +12043,38 @@
         }
 #endif
     }
-
-
+#if 1
+    pstTmp = mxmlFindElement(pstAlg,m_stConfigInfo.pstTree,"pdSign1", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstTmp)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.astPdSign[0] = mxmlGetInteger(pstTmp);
+    }
+    pstTmp = mxmlFindElement(pstAlg,m_stConfigInfo.pstTree,"pdSign2", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstTmp)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.astPdSign[1] = mxmlGetInteger(pstTmp);
+    }
+    pstTmp = mxmlFindElement(pstAlg,m_stConfigInfo.pstTree,"pdSign3", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstTmp)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.astPdSign[2] = mxmlGetInteger(pstTmp);
+    }
+    pstTmp = mxmlFindElement(pstAlg,m_stConfigInfo.pstTree,"pdSign4", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstTmp)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.astPdSign[3] = mxmlGetInteger(pstTmp);
+    }
+    pstTmp = mxmlFindElement(pstAlg,m_stConfigInfo.pstTree,"pdSign5", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstTmp)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.astPdSign[4] = mxmlGetInteger(pstTmp);
+    }
+    pstTmp = mxmlFindElement(pstAlg,m_stConfigInfo.pstTree,"pdSign6", NULL, NULL, MXML_DESCEND);
+    if (NULL != pstTmp)
+    {
+        stAlgParam.stAlgCh2.stPdsParam.astPdSign[5] = mxmlGetInteger(pstTmp);
+    }
+#endif
     pstPdRedInterval = mxmlFindElement(pstAlg, m_stConfigInfo.pstTree, "pdRedInterval", NULL, NULL, MXML_DESCEND);
     if (NULL != pstPdRedInterval)
     {
Index: src/control/statmach.c
===================================================================
--- src/control/statmach.c	(revision 5578)
+++ src/control/statmach.c	(working copy)
@@ -55,7 +55,9 @@
     SV_BOOL bUpdate;                            /* 制式是否有更新 */
 } AD_VIDEO_INFO_S;
 
+#if (defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1))
 static AD_VIDEO_INFO_S stADVideoInfo;
+#endif
 
 #if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) \
     || defined(BOARD_DMS31V2) || defined(BOARD_ADA47V1) || defined(BOARD_HDW845V1) || defined(BOARD_ADA32C4))
@@ -1150,6 +1152,8 @@
 
 void ctrl_SetAccDelayRes()
 {
+#if (defined(BOARD_WFCR20S2) || defined(BOARD_WFTR20S3) || defined(BOARD_IPTR20S1) || defined(BOARD_ADA32C4))
 	print_level(SV_DEBUG,"-----------------------------------------set delay success \n");
 	bSendAccDelay = SV_TRUE;
+#endif
 }
Index: src/ipserver/http/jsonHandle.cpp
===================================================================
--- src/ipserver/http/jsonHandle.cpp	(revision 5578)
+++ src/ipserver/http/jsonHandle.cpp	(working copy)
@@ -3952,6 +3952,14 @@
         cJSON_AddItemToObject(pstPds, "iconLocation", cJSON_CreateNumber(apstPdsParam[i]->eIconLocation));
         cJSON_AddItemToObject(pstPds, "bMosaic", cJSON_CreateNumber(apstPdsParam[i]->bMosaic));
         cJSON_AddItemToObject(pstPds, "pdBlkSize", cJSON_CreateNumber(apstPdsParam[i]->u32BlkSize));
+
+        cJSON_AddItemToObject(pstPds, "pdSign1", cJSON_CreateNumber(apstPdsParam[i]->astPdSign[0]));
+        cJSON_AddItemToObject(pstPds, "pdSign2", cJSON_CreateNumber(apstPdsParam[i]->astPdSign[1]));
+        cJSON_AddItemToObject(pstPds, "pdSign3", cJSON_CreateNumber(apstPdsParam[i]->astPdSign[2]));
+        cJSON_AddItemToObject(pstPds, "pdSign4", cJSON_CreateNumber(apstPdsParam[i]->astPdSign[3]));
+        cJSON_AddItemToObject(pstPds, "pdSign5", cJSON_CreateNumber(apstPdsParam[i]->astPdSign[4]));
+        cJSON_AddItemToObject(pstPds, "pdSign6", cJSON_CreateNumber(apstPdsParam[i]->astPdSign[5]));
+
         cJSON_AddItemToObject(pstPds, "pdAlarmOutInterval", cJSON_CreateNumber(apstPdsParam[i]->s32PdAlarmOutInterval));
         cJSON_AddItemToObject(pstPds, "pdBorderAlarm", cJSON_CreateNumber(apstPdsParam[i]->bAlarmBorder));
 
@@ -6278,6 +6286,38 @@
                         {
                             apstPdsParam[i]->u32BlkSize = pstTmp->valueint;
                         }
+
+                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdSign1");
+                        if (NULL != pstTmp)
+                        {
+                            apstPdsParam[i]->astPdSign[0] = pstTmp->valueint;
+                        }
+                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdSign2");
+                        if (NULL != pstTmp)
+                        {
+                            apstPdsParam[i]->astPdSign[1] = pstTmp->valueint;
+                        }
+                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdSign3");
+                        if (NULL != pstTmp)
+                        {
+                            apstPdsParam[i]->astPdSign[2] = pstTmp->valueint;
+                        }
+                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdSign4");
+                        if (NULL != pstTmp)
+                        {
+                            apstPdsParam[i]->astPdSign[3] = pstTmp->valueint;
+                        }
+                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdSign5");
+                        if (NULL != pstTmp)
+                        {
+                            apstPdsParam[i]->astPdSign[4] = pstTmp->valueint;
+                        }
+                        pstTmp = cJSON_GetObjectItemCaseSensitive(pstPds, "pdSign6");
+                        if (NULL != pstTmp)
+                        {
+                            apstPdsParam[i]->astPdSign[5] = pstTmp->valueint;
+                        }
+
                         pstPdInterval = cJSON_GetObjectItemCaseSensitive(pstPds, "pdInterval");
                         if (NULL != pstPdInterval)
                         {
Index: src/webui/config.html
===================================================================
--- src/webui/config.html	(revision 5578)
+++ src/webui/config.html	(working copy)
@@ -2137,10 +2137,10 @@
 									{{#isNotCustomer "202319"}}{{getKeyLang "pd-Model-PersonCar"}}{{/isNotCustomer}}
 								</option>
 								<option value="13" {{#equal pdsModel 13}}selected="selected"{{/equal}}{{#isA32OW}}style="display: none;"{{/isA32OW}}{{{hideNotCustomer "100394"}}} {{{hideHardware "ADA32IR"}}}>{{getKeyLang "pd-Model-SafetyHat"}}</option>
-								<option value="18" {{#equal pdsModel 18}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921"}}}>{{getKeyLang "pd-Model-Sign"}}</option>
-								<option value="19" {{#equal pdsModel 19}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921"}}}>{{getKeyLang "pd-Model-PersonSign"}}</option>
-								<option value="20" {{#equal pdsModel 20}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921"}}}>{{getKeyLang "pd-Model-CarSign"}}</option>
-								<option value="21" {{#equal pdsModel 21}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921"}}}>{{getKeyLang "pd-Model-PersonCarSign"}}</option>
+								<option value="18" {{#equal pdsModel 18}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661"}}}>{{getKeyLang "pd-Model-Sign"}}</option>
+								<option value="19" {{#equal pdsModel 19}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661"}}}>{{getKeyLang "pd-Model-PersonSign"}}</option>
+								<option value="20" {{#equal pdsModel 20}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661"}}}>{{getKeyLang "pd-Model-CarSign"}}</option>
+								<option value="21" {{#equal pdsModel 21}}selected="selected"{{/equal}}{{{hideNotCustomer "202626 201338 202319 201067 200585 201921 202661"}}}>{{getKeyLang "pd-Model-PersonCarSign"}}</option>
 
 								<option value="2" {{#equal pdsModel 2}}selected="selected"{{/equal}}{{#isNotA32OW}}style="display: none;"{{/isNotA32OW}}>{{getKeyLang "pd-Model-Person"}}</option>
 								<option value="8" {{#equal pdsModel 8}}selected="selected"{{/equal}}{{#isNotA32OW}}style="display: none;"{{/isNotA32OW}} {{{hideHardware "ADA32V3"}}}>{{getKeyLang "pd-Model-Car"}}</option>
Index: src/webui/js/webapp-view.js
===================================================================
--- src/webui/js/webapp-view.js	(revision 5578)
+++ src/webui/js/webapp-view.js	(working copy)
@@ -6775,7 +6775,7 @@
                     'algEnable': jsonTmpl.algConfig.algEnable,         
                     'algType': jsonTmpl.algConfig.algType,
                     'eventID': jsonTmpl.algConfig.eventID,
-                    'algTrigger': jsonTmpl.algConfig.algTrigge,
+                    'algTrigger': jsonTmpl.algConfig.algTrigger,
                     'audioVolume': jsonTmpl.algConfig.audioVolume,
                     'pdsAlarmerVolume': jsonTmpl.algConfig.pdsAlarmerVolume,
                     'pdsLedBrightness': jsonTmpl.algConfig.pdsLedBrightness,
@@ -6788,7 +6788,7 @@
                     'algEnable': jsonTmpl.algConfig.algEnable,         
                     'algType': jsonTmpl.algConfig.algType,
                     'eventID': jsonTmpl.algConfig.eventID,
-                    'algTrigger': jsonTmpl.algConfig.algTrigge,
+                    'algTrigger': jsonTmpl.algConfig.algTrigger,
                     'audioVolume': jsonTmpl.algConfig.audioVolume,
                     'pdsAlarmerVolume': jsonTmpl.algConfig.pdsAlarmerVolume,
                     'pdsLedBrightness': jsonTmpl.algConfig.pdsLedBrightness,
@@ -6802,7 +6802,7 @@
                     'algEnable': jsonTmpl.algConfig.algEnable,         
                     'algType': jsonTmpl.algConfig.algType,
                     'eventID': jsonTmpl.algConfig.eventID,
-                    'algTrigger': jsonTmpl.algConfig.algTrigge,
+                    'algTrigger': jsonTmpl.algConfig.algTrigger,
                     'audioVolume': jsonTmpl.algConfig.audioVolume,
                     'pdsAlarmerVolume': jsonTmpl.algConfig.pdsAlarmerVolume,
                     'pdsLedBrightness': jsonTmpl.algConfig.pdsLedBrightness,
