﻿/******************************************************************************
Copyright (C) 2014-2016 广州敏视数码科技有限公司版权所有.

文件名：mculink.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2016-06-02

文件功能描述: 定义系统与MCU通讯链路功能函数

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明
  1. :
  2. :
  3. :
  4. :
  5. :
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _MCULINK_H_
#define _MCULINK_H_

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */
#ifndef _DEFINES_H_

/* 数据类型定义 */
typedef signed char         sint8;
typedef signed short        sint16;
typedef signed int          sint32;
typedef signed long         slng32;
typedef signed long long    sint64;
typedef unsigned char       uint8;
typedef unsigned short      uint16;
typedef unsigned int        uint32;
typedef unsigned long       ulng32;
typedef unsigned long long  uint64;

typedef enum
{
    SV_FALSE= 0, 
    SV_TRUE = 1
} SV_BOOL;

#define SV_SUCCESS          0
#define SV_FAILURE          (-1)

#endif

/* 错误码定义 */
#define ERR_MSGD_INVALID_DATA      (-1)     /* 无效数据 */
#define ERR_MSGD_INVALID_PKTID     (-2)     /* 消息包ID 无效 */
#define ERR_MSGD_ILLEGAL_PARAM     (-3)     /* 传入非法参数 */
#define ERR_MSGD_EXIST             (-4)     /* 资源已存在 */
#define ERR_MSGD_UNEXIST           (-5)     /* 资源未初始化 */
#define ERR_MSGD_NULL_PTR          (-6)     /* 使用了NULL指针 */
#define ERR_MSGD_NOT_CONFIG        (-7)     /* 未配置属性前初始化系统、设备、通道 */
#define ERR_MSGD_NOT_SURPPORT      (-8)     /* 操作或类型不支持 */
#define ERR_MSGD_NOT_PERM          (-9)     /* 操作不允许 */
#define ERR_MSGD_NOMEM             (-12)    /* 内存不足 */
#define ERR_MSGD_NOBUF             (-13)    /* buffer 不足 */
#define ERR_MSGD_BUF_EMPTY         (-14)    /* buffer 为空  */
#define ERR_MSGD_BUF_FULL          (-15)    /* buffer 已满 */
#define ERR_MSGD_SYS_NOTREADY      (-16)    /* 系统忙碌或未初始化 */
#define ERR_MSGD_BADADDR           (-17)    /* 地址错误 */
#define ERR_MSGD_BUSY              (-18)    /* 资源忙碌 */
#define ERR_MSGD_TIMEOUT           (-19)    /* 处理超时 */

/* 波特率 */
typedef enum
{
    S115200 = 0,
    S9600,
}Baudrate;

/* 数据包结构体 */
typedef struct tag_MsgMcuCollection_t
{
    uint16  msgOpcode;                      /* MCU 统一操作码 */
    uint16  msgArgLen;                      /* ARG 数据长度 */
    ulng32  mPrivateVal;                    /* 私有值 (若指令有回复，则原样送回) */
    void   *pvArg;                          /* 数据指针 */
} MLINK_PACKET_S;

/******************************************************************************
 * 数据包接收回调函数指针定义
 * 参数 : pstPacket --- 数据包指针
 * 注意 : 该回调函数需要尽快返回, 不可在该回调函数内部做延时阻塞操作
 *****************************************************************************/
typedef void (*MLINK_RECV_CALLBACK)(MLINK_PACKET_S *pstPacket);

/* MCU链路配置结构定义 */
typedef struct tagMlinkCfg_S
{
    char    *pszPortName;                   /* 串口设备文件名: /dev/ttyXXX */
    uint32	u32Timeout;                     /* 数据包发送超时时间 (ms): [推荐值 1000ms] */
    uint32  u32tryMaxTime;                  /* 最大尝试的发送次数 [推荐值 3次] */
    MLINK_RECV_CALLBACK pfnPacketCallback;  /* 数据包接收回调函数指针 */
} MLINK_CONF_S;

/******************************************************************************
* 函数功能: 获取Mcu类型
* 输入参数: 无
* 输出参数: 无
* 返回值  : MCU_TYPE_E - MCU类型
* 注意    : 无
*****************************************************************************/
extern MCU_TYPE_E mlink_getMcuType(void);

/******************************************************************************
 * 函数功能: 初始化MCU链路模块
 * 输入参数: pstMlinkConf --- 模块配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 MLink_Mod_Init(MLINK_CONF_S *pstMlinkConf);

/******************************************************************************
 * 函数功能: 去初始化MCU链路模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 MLink_Mod_Fini();

/******************************************************************************
 * 函数功能: 启动MCU链路模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 无
 *****************************************************************************/
extern sint32 MLink_Mod_Start();

/******************************************************************************
 * 函数功能: 停止MCU链路模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功

 * 注意    : 无
 *****************************************************************************/
extern sint32 MLink_Mod_Stop();

/******************************************************************************
 * 数据包发送结果回调函数指针定义
 * 参数 : pstPacket --- 数据包指针
          s32Res --- 发送结果(0:成功, -1:失败)
 * 注意 : 该回调函数需要尽快返回, 不可在该回调函数内部做延时阻塞操作
 *****************************************************************************/
typedef void (*MLINK_RES_CALLBACK)(MLINK_PACKET_S *pstPacket, sint32 s32Res);

/******************************************************************************
 * 函数功能: 非阻塞式发送数据包
 * 输入参数: pstPacket --- 数据包指针
             resCallback --- 发送结果回函函数指针 (可为NULL)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_MSGD_BUF_FULL - 当前发送队列已满
 * 注意    : 该函数不需要等待数据包发送完成即可立即返回, 数据包发送结果将通过
             调用所注册的回调函数返回结果值。如果不关心结果则传 resCallback=NULL
 *****************************************************************************/
extern sint32 MLink_SendPacketToMcuNonblock(MLINK_PACKET_S *pstPacket, MLINK_RES_CALLBACK resCallback);

/******************************************************************************
 * 函数功能: 阻塞式发送数据包
 * 输入参数: pstPacket --- 数据包指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_MSGD_BUF_FULL - 当前发送队列已满
             ERR_MSGD_TIMEOUT - 消息发送超时
 * 注意    : 该函数需要等待数据包发送完成后才返回, 阻塞最大超时时间为模块
             初始化参数中的: u32Timeout * u32tryMaxTime
 *****************************************************************************/
extern sint32 MLink_SendPacketToMcuBlock(MLINK_PACKET_S *pstPacket);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _MCULINK_H_ */

