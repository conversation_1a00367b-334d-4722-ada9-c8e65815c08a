Index: build/ada32v2/boot/autoUpdate
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: build/ada32v2/root/alg
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: build/ada32v2/root/ipsys
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libalarm.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libarg.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libavalarmer.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libboard.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libcan.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libconfig.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libcontrol.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libextscreen.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libgps.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libhttp.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libled.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/liblog.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libmcu.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libmedia.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libmsg.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libnetwork.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libonvif.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/librecorder.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/librs485.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/librtsp2.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libsafefunc.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libsharefifo.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libsomeip.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libstorage.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libthpool.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libutils.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libuvc.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libwf697.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: lib/ada32v2/libwifi.a
===================================================================
Cannot display: file marked as a binary type.
svn:mime-type = application/octet-stream
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 5795)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -7187,6 +7187,11 @@
                 }
             }
 
+            if (pdsa32::E_CLS_PERSON_HAT_VEST == stPdResult.stResults[i].classes && enRoi < PD_ROI_BLUE)
+            {
+                enRoi = PD_ROI_BLUE;
+            }
+
             if(u32RectCnt >= 20)
             {
                 break;
@@ -8887,7 +8892,8 @@
                         }
                         else
                         {
-						    apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC);
+						    //apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_PC);
+                            apcsPdsAlgRgbPC = new pdsa32::CPdsAlg(pdsa32::E_PDSALG_TYPE_RGB_DCD);
                         }
                     }
             #endif
@@ -9720,6 +9726,7 @@
             break;
     }
 
+
     /* 201165客户未确认的专用功能，先屏蔽 */
 #if 0
     if (BOARD_IsCustomer(BOARD_C_ADA32V2_201165))
@@ -9824,6 +9831,8 @@
         modefilelist[0] = PD_MODEL_RGB_PC_UNISIGN;
     }
 
+    modefilelist[0] = PD_MODEL_RGB_PC_DCD;
+
     return SV_SUCCESS;
 }
 
