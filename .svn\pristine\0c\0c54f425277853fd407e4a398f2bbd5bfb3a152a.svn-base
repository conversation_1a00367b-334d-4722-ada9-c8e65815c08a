#include "sv_bb_808client.h"
#include "sv_bb_interface.h"
#include "sv_bb_media_start.h"
#include "state_api.h"
#include "sv_bb_alarm.h"
#include "sv_bb_terminal.h"
#include "msg.h"
#include "op.h"


static SV_BB_808CLIENT *p808Client = SV_NULL;

sint32 sv_dvr_main_event_notify(int Eventid, int type,u8 u8Param, u16 u16Param1, u32 u32Param2, void *pData, int datalen)
{
	if(SV_NULL == p808Client)
	{
		printf("p808Client is not init! \n");
		return SV_FAILURE;
	}

	if(BB_STATE_TALK_AUDIAO_DATA != Eventid)
	{
		p808Client->OnEventHandle(Eventid, type,u8Param, u16Param1, u32Param2, pData, datalen);
	}
	else
	{
		TalkDataProc((char *)pData, datalen);
	}

	return SV_SUCCESS;
}

sint32 BB808_Start()
{
	sint32 s32Ret = 0;
	
	s32Ret = MSG_ReciverStart(EP_BB808);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
		return s32Ret;
	}

	sv_dvr_bb_create_task();
	return SV_SUCCESS;
}
sint32 sv_dvr_bb_create_task()
{	
	if(p808Client == SV_NULL)
	{		
		p808Client = new SV_BB_808CLIENT();
	}

	if(p808Client != SV_NULL)
	{		
		p808Client->StartBbClient();
		
		InitBbMediaClient(p808Client);

		BbAlarmInit();

		sv_dvr_bb_event_set_cb(sv_dvr_main_event_notify);
		return SV_SUCCESS;
	}
	else
	{
		return SV_FAILURE;
	}
}

sint32 sv_dvr_bb_delete_task()
{	
	if(SV_NULL != p808Client)
	{
		p808Client->StopBbClient();
		delete p808Client;		
		p808Client = SV_NULL;
		DeInitMedia();
	}
	return SV_SUCCESS;
}



