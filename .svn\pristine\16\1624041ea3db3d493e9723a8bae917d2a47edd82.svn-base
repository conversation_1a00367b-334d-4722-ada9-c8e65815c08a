#if defined(WIN32) || defined(_WIN32) 
#ifndef _CRT_SECURE_NO_WARNINGS
#define _CRT_SECURE_NO_WARNINGS
#endif
#endif

#include "MJPEGSource.h"
#include <cstdio>
#include <chrono>
#if defined(__linux) || defined(__linux__) 
#include <sys/time.h>
#endif
#include "math.h"

#define BigLittleSwap24(A) (((A & 0xff0000) >> 16) | (A & 0x00ff00) | ((A & 0x0000ff) << 16))

#define JPEG_MAIN_HEADER_SIZE 8

using namespace someip;
using namespace std;

static uint32_t m_samplerate = 90000;

/*
 * Table K.1 from JPEG spec.
 */
static const int jpeg_luma_quantizer[64] = {
        16, 11, 10, 16, 24, 40, 51, 61,
        12, 12, 14, 19, 26, 58, 60, 55,
        14, 13, 16, 24, 40, 57, 69, 56,
        14, 17, 22, 29, 51, 87, 80, 62,
        18, 22, 37, 56, 68, 109, 103, 77,
        24, 35, 55, 64, 81, 104, 113, 92,
        49, 64, 78, 87, 103, 121, 120, 101,
        72, 92, 95, 98, 112, 100, 103, 99
};

/*
 * Table K.2 from JPEG spec.
 */
static const int jpeg_chroma_quantizer[64] = {
        17, 18, 24, 47, 99, 99, 99, 99,
        18, 21, 26, 66, 99, 99, 99, 99,
        24, 26, 56, 99, 99, 99, 99, 99,
        47, 66, 99, 99, 99, 99, 99, 99,
        99, 99, 99, 99, 99, 99, 99, 99,
        99, 99, 99, 99, 99, 99, 99, 99,
        99, 99, 99, 99, 99, 99, 99, 99,
        99, 99, 99, 99, 99, 99, 99, 99
};

typedef struct jpeghdr {
        uint32_t tspec:8;   /* type-specific field */
        uint32_t off:24;    /* fragment byte offset */
        uint8_t type;       /* id of jpeg decoder params */
        uint8_t q;          /* quantization factor (or table id) */
        uint8_t width;      /* frame width in 8 pixel blocks */
        uint8_t height;     /* frame height in 8 pixel blocks */
}JPEG_MAIN_HEADER;

typedef struct qtablehdr {
        uint8_t  mbz;
        uint8_t  precision;
        uint8_t length;
}QTable_HEADER;

static uint8_t lqt[64];
static uint8_t cqt[64];

#define DCTSIZE2 64
static int getJpegQuality(void *buffer, int size)
{
    int tmp_quality = 0;
    int linear_quality = 0;
    int aver_quality = 0;
    const int aver_times = 1;
    int times = 0;
    int i = 0;
    char *pDQT = (char *)buffer + 0x14 + 0x05;

    static const unsigned int std_luminance_quant_tbl[DCTSIZE2] = {  
      16,  11,  10,  16,  24,  40,  51,  61,
      12,  12,  14,  19,  26,  58,  60,  55,
      14,  13,  16,  24,  40,  57,  69,  56,  
      14,  17,  22,  29,  51,  87,  80,  62,
      18,  22,  37,  56,  68, 109, 103,  77,
      24,  35,  55,  64,  81, 104, 113,  92,
      49,  64,  78,  87, 103, 121, 120, 101,
      72,  92,  95,  98, 112, 100, 103,  99
    };

    if(size < 89)
        return -1;

    for(i = 0; i < DCTSIZE2; i++)
    {
        long temp = pDQT[i];
        if (temp > 0)
        {
            linear_quality = ceil((float)(temp*100L - 50L)/std_luminance_quant_tbl[i]);
            if(linear_quality>100)
            {
                tmp_quality = ceil((float)5000/linear_quality);
            }
            else
            {
                tmp_quality = ceil((200 - linear_quality)/2);
            }
            tmp_quality = tmp_quality < 100 ? tmp_quality : 100;
            aver_quality += tmp_quality;
            if(aver_times==++times)
            {
                aver_quality /= aver_times;
                break;
            }
        }
    }

    return aver_quality;
}

/*
 * Call MakeTables with the Q factor and two u_char[64] return arrays
 */
void MJPEGSource::MakeTables(int q, uint8_t *lqt, uint8_t *cqt)
{
  int i;
  int factor = q;

  if (q < 1) factor = 1;
  if (q > 99) factor = 99;
  if (q < 50)
    q = 5000 / factor;
  else
    q = 200 - factor*2;

  for (i=0; i < 64; i++) {
    int lq = (jpeg_luma_quantizer[i] * q + 50) / 100;
    int cq = (jpeg_chroma_quantizer[i] * q + 50) / 100;

    /* Limit the quantizers to 1 <= q <= 255 */
    if (lq < 1) lq = 1;
    else if (lq > 255) lq = 255;
    lqt[i] = lq;

    if (cq < 1) cq = 1;
    else if (cq > 255) cq = 255;
    cqt[i] = cq;
  }
}

MJPEGSource::MJPEGSource(uint32_t framerate, uint8_t width, uint8_t height, uint8_t q, uint8_t type)
	: framerate_(framerate), width_(width), height_(height), qfactor_(q), type_(type)
{
    payload_    = 26;
    media_type_ = JPEG;
    clock_rate_ = 90000;
	m_samplerate = 90000;
    //MakeTables(60, lqt, cqt);    // 计算两个量化表
}

MJPEGSource* MJPEGSource::CreateNew(uint32_t framerate, uint8_t width, uint8_t height, uint8_t q, uint8_t type)
{
    return new MJPEGSource(framerate, width, height, q, type);
}

MJPEGSource::~MJPEGSource()
{
	
}

string MJPEGSource::GetMediaDescription(uint16_t port)
{
	char buf[100] = {0};
	sprintf(buf, "m=video %hu RTP/AVP 26", port);

	return string(buf);
}
	
string MJPEGSource::GetAttribute()
{
	return string("a=rtpmap:26 JPEG/90000");
}

bool MJPEGSource::HandleFrame(MediaChannelId channelId, AVFrame frame)
{
	uint8_t *frame_buf  = frame.buffer.get();
	uint32_t frame_size = frame.size;
    JPEG_MAIN_HEADER mainHeader;
    uint32_t writeDateSize = 0;



    /* JPEG Main Header */
    mainHeader.tspec = 0;
    mainHeader.off = 0;
    mainHeader.type = MJPEGSource::GetType();
#if (!defined(PLATFORM_RV1126))
    mainHeader.q = MJPEGSource::GetQfactor();
#else
    mainHeader.q = getJpegQuality(frame.header.get(), 1024);
#endif
    mainHeader.width = MJPEGSource::GetWidth();
    mainHeader.height = MJPEGSource::GetHeight();

    //todo q table
    
    if (frame_size <= MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE) {
        RtpPacket rtp_pkt;        
        memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE, &mainHeader, JPEG_MAIN_HEADER_SIZE);

        rtp_pkt.type = frame.type;
        rtp_pkt.presentationTime = frame.presentationTime;
        rtp_pkt.size = 4 + RTP_HEADER_SIZE + JPEG_MAIN_HEADER_SIZE + frame_size;
        rtp_pkt.last =  0;

        memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE + JPEG_MAIN_HEADER_SIZE, frame_buf + mainHeader.off, frame_size); 

        if (send_frame_callback_) {
            if (!send_frame_callback_(channelId, rtp_pkt)) {
                return false;
            }
        }
    }
    else {    // 分片发送
        /*    // 第一片加入两个量化表
        RtpPacket rtp_pkt;
        rtp_pkt.type = frame.type;
		rtp_pkt.presentationTime = frame.presentationTime;
        rtp_pkt.size = 4 + RTP_HEADER_SIZE + MAX_RTP_PAYLOAD_SIZE;
        rtp_pkt.last = 0;
        
        mainHeader.off = BigLittleSwap24(mainHeader.off);
        memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE, &mainHeader, JPEG_MAIN_HEADER_SIZE);
        memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE + JPEG_MAIN_HEADER_SIZE, lqt, 64);
        memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE + JPEG_MAIN_HEADER_SIZE + 64, cqt, 64);
        memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE + JPEG_MAIN_HEADER_SIZE + 128, frame_buf, MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE - 128);
        
        if (send_frame_callback_) {
            if (!send_frame_callback_(channelId, rtp_pkt))
                return false;
        }

        mainHeader.off = BigLittleSwap24(mainHeader.off);
        mainHeader.off += MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE;
        
        frame_buf  += MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE;
        frame_size -= MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE;
        */
        while (frame_size > MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE) {
            RtpPacket rtp_pkt;
            rtp_pkt.type = frame.type;
            rtp_pkt.presentationTime = frame.presentationTime;
            rtp_pkt.size = 4 + RTP_HEADER_SIZE + MAX_RTP_PAYLOAD_SIZE;
            rtp_pkt.last = 0;
            
            mainHeader.off = BigLittleSwap24(mainHeader.off);
            memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE, &mainHeader, JPEG_MAIN_HEADER_SIZE);

            memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE  + JPEG_MAIN_HEADER_SIZE, frame_buf, MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE);

            if (send_frame_callback_) {
                if (!send_frame_callback_(channelId, rtp_pkt))
                    return false;
            }

            mainHeader.off = BigLittleSwap24(mainHeader.off);
            mainHeader.off += MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE;
            
            frame_buf  += MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE;
            frame_size -= MAX_RTP_PAYLOAD_SIZE - JPEG_MAIN_HEADER_SIZE;           
        }

        {
            RtpPacket rtp_pkt;
            rtp_pkt.type = frame.type;
			rtp_pkt.presentationTime = frame.presentationTime;
            rtp_pkt.size = 4 + RTP_HEADER_SIZE + JPEG_MAIN_HEADER_SIZE + frame_size;
            rtp_pkt.last = 1;
            
            mainHeader.off = BigLittleSwap24(mainHeader.off);
            memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE, &mainHeader, JPEG_MAIN_HEADER_SIZE);
            
            memcpy(rtp_pkt.data.get() + 4 + RTP_HEADER_SIZE + JPEG_MAIN_HEADER_SIZE, frame_buf, frame_size);

            if (send_frame_callback_) {
				if (!send_frame_callback_(channelId, rtp_pkt)) {
					return false;
				}              
            }
        }
    }

    return true;
}


uint32_t MJPEGSource::GetTimestamp(struct timeval tv)
{
/* #if defined(__linux) || defined(__linux__) 
	struct timeval tv = {0};
	gettimeofday(&tv, NULL);
	uint32_t ts = ((tv.tv_sec*1000)+((tv.tv_usec+500)/1000))*90; // 90: _clockRate/1000;
	return ts;
#else */
	//auto time_point = chrono::time_point_cast<chrono::milliseconds>(chrono::system_clock::now());
	#if 1
	
	uint32_t timestampIncrement = (m_samplerate*tv.tv_sec);
	timestampIncrement += (uint32_t)(m_samplerate*(tv.tv_usec/1000000.0) + 0.5);
	uint32_t const rtpTimestamp = timestampIncrement;
	return rtpTimestamp;

	#else
    if (pts != 0)
    {
        return ((pts+500)/1000)*90;
    }
    else
    {
    	auto time_point = chrono::time_point_cast<chrono::microseconds>(chrono::steady_clock::now());
    	return (uint32_t)((time_point.time_since_epoch().count() + 500) / 1000 * 90 );
	}
	#endif
//#endif 
}