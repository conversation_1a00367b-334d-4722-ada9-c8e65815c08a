Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 5475)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -764,6 +764,33 @@
           (st_bStop != SV_TRUE)
           )
     {
+        if (BOARD_IsCustomer(BOARD_C_ADA32V2_100346))
+        {
+            switch(lmode)
+            {
+                case PD_ALARM_TYPE_RED:
+                    pd_Alarm_Out_Enable(lmode);
+                    sleep_ms(65);
+                    pd_Alarm_Out_Reset();
+                    sleep_ms(15);
+                    break;
+                case PD_ALARM_TYPE_YELLOW:
+                    pd_Alarm_Out_Enable(lmode);
+                    sleep_ms(120);
+                    pd_Alarm_Out_Reset();
+                    sleep_ms(15);
+                    break;
+                case PD_ALARM_TYPE_GREEN:
+                    pd_Alarm_Out_Enable(lmode);
+                    sleep_ms(180);
+                    pd_Alarm_Out_Reset();
+                    sleep_ms(15);
+                    break;
+                default:
+                    break;
+            }
+        }
+    
         i = (i + 1) % 10;
         /* 设置PWM占用比      绿色占空比为30% 黄色占空比为60% 红色占空比为100%*/
         if(BOARD_IsCustomer(BOARD_C_ADA32V2_FTC))
@@ -797,7 +824,10 @@
         clock_gettime(CLOCK_MONOTONIC, &tvNow);
     }
 
-    pd_Alarm_Out_Reset();
+    if (BOARD_IsNotCustomer(BOARD_C_ADA32V2_100346))
+    {
+        pd_Alarm_Out_Reset();
+    }
     bTrigger = 0;
 
 skip_alg:
