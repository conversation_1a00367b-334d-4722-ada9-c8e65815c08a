#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <unistd.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <ctype.h>
#include <fcntl.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <pthread.h>
#include <errno.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <semaphore.h>

#include <linux/ioctl.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#include <error.h>

#include <fcntl.h>
#include <unistd.h>
#include <poll.h>
#include <signal.h>

#include "common.h"
#include "print.h"
#include "config.h"
#include "../../include/board.h"
#include "op.h"
#include "log.h"
#include "safefunc.h"
#include "utils.h"
#include "storage.h"
#include "power_detect.h"

int ipsys_log_level = SV_DEBUG;

#define PWDT_UMOUNT_RETRY_CNT 5

typedef struct tagPwdtInfo_S
{
	uint32 			u32TidVCC;			/* vcc检测线程 */
    SV_BOOL         bRunning;           /* 线程是否正在运行 */
    SV_BOOL         bPowerFlag;         /* 电源状态位,SV_TRUE:掉电 */

} PWDT_INFO_S;

PWDT_INFO_S m_stPwdtInfo = {0};         /* 模块控制信息 */

void log_timestamp(const char *message)
{
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    printf("[%s] Timestamp: %ld.%09ld seconds\n", message, ts.tv_sec, ts.tv_nsec);
}

void handle_timeout(sint32 sig) {
    if (sig == SIGALRM)
    {
        print_level_without_log(SV_INFO, "sync timed out after 1 second\n");
    }
}

void * pwdt_Monitor_Body(void *pvArg)
{
    sint32 s32Ret = 0, s32PowerFd = 0, s32Cnt = 0, s32MaxFd = 0;
    sint32 s32PowerValue = 0;
    fd_set fdSet = {0};
    struct timeval timeout;
    char szCmd[256] = {0};
    char *pcBoard = NULL;
    PWDT_INFO_S *pstControlInfo = (PWDT_INFO_S *)pvArg;
    char szBuf[10240] = {0};
    sint32 i;

    pcBoard = "DMS31V2";
    POWER_CTRL_PIN_S stPowerPin = {
        10,
        {
            {87, 0},  {88, 0},  {111, 0},
            {108, 0}, {45, 0},  {90, 0},
            {89, 0},  {118, 0}, {119,0},
            {86, 0},
        },
    };

    s32Ret = prctl(PR_SET_NAME, "pwdt_Monitor_Body");
    if (0 != s32Ret)
    {
        print_level_without_log(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    sprintf(szCmd, "insmod /root/ko/extdrv/power_detect.ko power_pin=%d board=%s", POWER_PIN, pcBoard);
    s32Ret = system(szCmd);
    if (0 != s32Ret)
    {
        print_level_without_log(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    s32PowerFd = open("/dev/stonkam-power", O_RDWR);
    if (s32PowerFd < 0)
    {
        print_level_without_log(SV_ERROR, "can't open /dev/stonkam-power !\n");
        return NULL;
    }

    s32Ret = ioctl(s32PowerFd, POWER_SET_PERI_PIN, &stPowerPin);
    if(s32Ret)
    {
        print_level_without_log(SV_ERROR, "power_ioctl error[%d]\n",s32Ret);
        return s32Ret;
    }

    while (pstControlInfo->bRunning)
    {
        FD_ZERO(&fdSet);
        FD_SET(s32PowerFd,&fdSet);
        s32MaxFd = s32PowerFd + 1;
        timeout.tv_sec = 1;
        timeout.tv_usec = 100000;
        s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout);
        if (s32Ret < 0)
        {
            print_level_without_log(SV_ERROR, "select failed\n");
        }
        else if (0 == s32Ret)
        {
            print_level_without_log(SV_INFO, "select failed\n");

        }
        else
        {
            if(FD_ISSET(s32PowerFd,&fdSet))
            {
                s32Ret = read(s32PowerFd, &s32PowerValue, sizeof(int));
                if (s32Ret == sizeof(int))
                {
                    if (1 == s32PowerValue)
                    {
                        print_level_without_log(SV_WARN,"power down\n");
                        SAFE_System("killall -9 wtd.sh", 1000);
                        SAFE_System("killall -9 alg", 1000);
                        SAFE_System("killall -9 ipsys", 1000);
                        sleep_ms(200);
                        sprintf(szCmd, "rm %s/mounted", "/mnt/sdcard");
                        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                        if (0 != s32Ret)
                        {
                            print_level_without_log(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
                        }

                        s32Ret = SAFE_System("sync", 1000);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level_without_log(SV_ERROR, "sync failed.\n");
                        }

                        for (i = 0; i < 5; i++)
                        {
                            s32Ret = SAFE_System("umount  /mnt/sdcard", NORMAL_WAIT_TIME);
                            if (s32Ret == 0)
                            {
                                print_level_without_log(SV_INFO, "umount successful on attempt %d.\n", i + 1);
                                break;
                            }

                            sprintf(szCmd, "fuser -km /mnt/sdcard");
                            SAFE_System(szCmd, NORMAL_WAIT_TIME);

                            print_level_without_log(SV_ERROR, "umount failed on attempt %d. Retrying...\n", i + 1);
                            sprintf(szCmd, "lsof | grep -E \"%s*\"", "/mnt/sdcard");

                            s32Ret = SAFE_System_Recv(szCmd, szBuf, sizeof(szBuf));
                            if (SV_SUCCESS != s32Ret)
                            {
                                return s32Ret;
                            }
                            //sync();
                            print_level_without_log(SV_WARN, "busy file %s\n",szBuf);

                            sleep_ms(200);
                        }

                        if (i >= 3)
                        {
                            print_level_without_log(SV_ERROR, "umount path: %s 3 times failed. Force umount it now. [err=%#x]\n", "/mnt/sdcard", errno);

                            // 杀死占用进程
                            sprintf(szCmd, "fuser -m -k %s", "/mnt/sdcard");
                            SAFE_System(szCmd, NORMAL_WAIT_TIME);

                            // 强制卸载
                            sprintf(szCmd, "umount -l %s", "/mnt/sdcard");
                            SAFE_System(szCmd, NORMAL_WAIT_TIME);

                            sprintf(szCmd, "df -h | grep sdcard");
                            SAFE_System(szCmd, NORMAL_WAIT_TIME);

                            print_level_without_log(SV_INFO, "Force umount issued.\n");
                        }


#if 0
                        sprintf(szCmd, "umount -lf %s 1>/dev/null 2>/dev/null", "/mnt/sdcard");
                        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                        if (0 != s32Ret)
                        {
                            print_level_without_log(SV_ERROR, "cmd: %s failed.\n", szCmd);
                            return SV_FAILURE;
                        }

                        if (s32Ret == 0)
                        {
                            print_level_without_log(SV_INFO, "Success to unmount SD card\n");
                        }
                        else
                        {
                            print_level_without_log(SV_ERROR, "Failed to unmount SD card %s\n", strerror);
                        }
#endif
                    }
                    else if (0 == s32PowerValue)
                    {
                        print_level_without_log(SV_WARN, "power reborn \n");
                        SAFE_System("./wtd.sh &", 1000);
                        SAFE_System("./ipsys &", 1000);
                        SAFE_System("./alg &", 1000);

                    }
                }
            }
       }
    }
    close(s32PowerFd);

}

sint32 pwdt_Storage_Down()
{
    sint32 s32Ret = 0, i = 0, j = 0;
    char szCmd[256] = {0};
    char szBuf[10240] = {0};
    const char* pszMountedPath;
    sint64 s64TimeBegin = 0, s64TimeEnd = 0;

    for (i = 0; i <= STORAGE_EXTRA_SD; i++)
    {
        print_level_without_log(SV_INFO, "check storage%d status...\n", i);

        if (!STORAGE_IsStorageMounted(i))
        {
            print_level_without_log(SV_INFO, "storage%d is not mounted\n", i);
            goto check_exit;
        }

        pszMountedPath = STORAGE_GetMountPath(i);
        sprintf(szCmd, "rm %s/mounted", pszMountedPath);
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level_without_log(SV_ERROR, "cmd: %s failed. [err=%d]\n", szCmd, s32Ret);
        }

        s32Ret = SAFE_System("sync", 1000);
        if (SV_SUCCESS != s32Ret)
        {
            print_level_without_log(SV_ERROR, "sync failed.\n");
        }

        s64TimeBegin = COMMON_GetTimeTickMs();
        print_level_without_log(SV_INFO, "try to umount %s.\n", pszMountedPath);
        for (j = 0; j < PWDT_UMOUNT_RETRY_CNT; j++)
        {
            sprintf(szCmd, "umount %s", pszMountedPath);
            s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
            if (s32Ret == 0)
            {
                print_level_without_log(SV_INFO, "%s successful on attempt %d.\n", szCmd, j + 1);
                break;
            }
            print_level_without_log(SV_ERROR, "%s failed on attempt %d. Retrying...\n", szCmd, j + 1);

            sprintf(szCmd, "fuser -km /mnt/sdcard");
            SAFE_System(szCmd, NORMAL_WAIT_TIME);

            #if 0
            sprintf(szCmd, "lsof | grep -E \"%s*\"", pszMountedPath);
            s32Ret = SAFE_System_Recv(szCmd, szBuf, sizeof(szBuf));
            if (SV_SUCCESS != s32Ret)
            {
                return s32Ret;
            }
            print_level_without_log(SV_WARN, "busy file %s\n",szBuf);
            #endif

            sleep_ms(150);
        }
        s64TimeEnd = COMMON_GetTimeTickMs();
        print_level_without_log(SV_WARN, "check storage time: %lldms\n", s64TimeEnd - s64TimeBegin);

        if (j >= PWDT_UMOUNT_RETRY_CNT)
        {
            print_level_without_log(SV_WARN, "umount path: %s %d times failed. Force umount it now. [err=%#x]\n", pszMountedPath, PWDT_UMOUNT_RETRY_CNT, errno);

            // 杀死占用进程
            sprintf(szCmd, "fuser -m -k %s", pszMountedPath);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);

            // 强制卸载
            sprintf(szCmd, "umount -lf %s", pszMountedPath);
            SAFE_System(szCmd, NORMAL_WAIT_TIME);
            print_level_without_log(SV_WARN, "%s success\n",szCmd);

            sprintf(szCmd, "df -h | grep sdcard");
            SAFE_System(szCmd, NORMAL_WAIT_TIME);

            print_level_without_log(SV_INFO, "Force umount issued.\n");
        }

check_exit:;
#if defined(BOARD_DMS31V2)
        if (STORAGE_MAIN_SD1 == i)
        {
            i = STORAGE_EXTRA_SD-1;
        }
#endif
    }

    print_level_without_log(SV_INFO, "check storage finished!!!\n");

    return SV_SUCCESS;
}

sint32 pwdt_DoPowerDown()
{
    sint32 s32Ret = 0, i = 0;

#if (defined(BOARD_DMS31V2))
    if (BOARD_IsNotDMS31P())
    {
        SAFE_SV_System("/root/gpio.sh  2 23 0 &");     // GPIO2_C7_d
        SAFE_SV_System("/root/gpio.sh  2 24 0 &");     // GPIO2_D0_d
        SAFE_SV_System("/root/gpio.sh  3 15 0 &");     // GPIO3_B7_d
        SAFE_SV_System("/root/gpio.sh  3 12 0 &");     // GPIO3_B4_d
    }
    else
    {
        SAFE_SV_System("/root/gpio.sh  3 12 0 &");     // GPIO3_B4_d
    }

    SAFE_SV_System("/root/gpio_disable.sh &");

    SAFE_System("killall -9 wtd.sh", 1000);
    SAFE_System("killall -9 alg", 1000);
    SAFE_System("killall -9 ipsys", 1000);
    sleep_ms(200);

    pwdt_Storage_Down();
#endif


    return SV_SUCCESS;
}

sint32 pwdt_DoPowerUp()
{
    sint32 s32Ret = 0;
#if (defined(BOARD_DMS31V2))
    if (BOARD_IsNotDMS31P())
    {
        SAFE_SV_System("/root/gpio.sh  2 23 1 &");     // GPIO2_C7_d
        SAFE_SV_System("/root/gpio.sh  2 24 1 &");     // GPIO2_D0_d
        SAFE_SV_System("/root/gpio.sh  3 15 1 &");     // GPIO3_B7_d
        SAFE_SV_System("/root/gpio.sh  3 12 1 &");     // GPIO3_B4_d
    }
    else
    {
        SAFE_SV_System("/root/gpio.sh  3 12 1 &");     // GPIO3_B4_d
    }
#endif

    SAFE_SV_System("/root/gpio_enable.sh power");

    return SV_SUCCESS;
}

int main() {
    sint32 s32Ret = 0, i;
    pthread_t thread = 0;
    sint32 s32PowerFd = 0, s32Cnt = 0, s32MaxFd = 0;
    sint32 s32PowerValue = 0;
    fd_set fdSet = {0};
    struct timeval timeout;
    char szCmd[256] = {0};
    char *pcBoard = NULL;
    char szBuf[10240] = {0};

    m_stPwdtInfo.bRunning = SV_TRUE;

    #if 0
    s32Ret = pthread_create(&thread, NULL, pwdt_Monitor_Body, &m_stPwdtInfo);
    if (0 != s32Ret)
    {
        print_level_without_log(SV_ERROR, "pthread_create failed! [err: %s]\n", strerror(errno));
        return ERR_SYS_NOTREADY;
    }
    #endif

    pcBoard = "DMS31V2";
    POWER_CTRL_PIN_S stPowerPin = {
        10,
        {
            {87, 0},  {88, 0},  {111, 0},
            {108, 0}, {45, 0},  {90, 0},
            {89, 0},  {118, 0}, {119,0},
            {86, 0},
        },
    };

    sprintf(szCmd, "insmod /root/ko/extdrv/power_detect.ko power_pin=%d board=%s", POWER_PIN, pcBoard);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level_without_log(SV_ERROR, "%s failed! [err:%s]\n", szCmd, strerror(errno));
    }

    s32PowerFd = open("/dev/stonkam-power", O_RDWR);
    if (s32PowerFd < 0)
    {
        print_level_without_log(SV_ERROR, "can't open /dev/stonkam-power !\n");
        return NULL;
    }

    s32Ret = ioctl(s32PowerFd, POWER_SET_PERI_PIN, &stPowerPin);
    if (s32Ret)
    {
        print_level_without_log(SV_ERROR, "power_ioctl error[%d]\n",s32Ret);
        return s32Ret;
    }

    while (m_stPwdtInfo.bRunning)
    {
        FD_ZERO(&fdSet);
        FD_SET(s32PowerFd,&fdSet);
        s32MaxFd = s32PowerFd + 1;
        timeout.tv_sec = 0;
        timeout.tv_usec = 200000;
        s32Ret = select(s32MaxFd, &fdSet, NULL, NULL, &timeout);
        if (s32Ret < 0)
        {
            print_level_without_log(SV_ERROR, "select failed\n");
        }
        else if (0 == s32Ret)
        {
            //print_level_without_log(SV_INFO, "select timeout\n");
        }
        else
        {
            if (FD_ISSET(s32PowerFd,&fdSet))
            {
                s32Ret = read(s32PowerFd, &s32PowerValue, sizeof(int));
                if (s32Ret == sizeof(int))
                {
                    if (1 == s32PowerValue)
                    {
                        print_level_without_log(SV_WARN, "power down\n");
                        pwdt_DoPowerDown();
                    }
                    else if (0 == s32PowerValue)
                    {
                        print_level_without_log(SV_WARN, "power reborn\n");
                        pwdt_DoPowerUp();
                        SAFE_System("/root/ipsys >> /dev/null &", NORMAL_WAIT_TIME);
                        SAFE_System("/root/alg >> /dev/null &", NORMAL_WAIT_TIME);
                        SAFE_System("/root/wtd.sh >> /dev/null &", NORMAL_WAIT_TIME);
                    }
                }
            }
       }
    }

    close(s32PowerFd);

    return SV_SUCCESS;
}
