/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：sharefifo.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-12-06

文件功能描述: 定义共享媒体队列功能函数 (单生产者-多消费者模型)

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/sem.h>
#include <sys/msg.h>
#include <error.h>


#include "sharefifo.h"
#include "utils.h"

#if defined(BOARD_ADA32IR)
#define SFIFO_MAX_QUE_NUM       8   /* 最多支持创建的队列数目 */
#else
#define SFIFO_MAX_QUE_NUM       4   /* 最多支持创建的队列数目 */
#endif
#define SFIFO_MAX_CONSUMER      8   /* 同一个队列最多支持的消费者数目 */
#define ADDR_ALIGN4(a)  (((a)+3)&0xfffffffc)  /* 地址4字节对齐 */

/* 解析H264/H265单包I帧信息 */
typedef struct tagIFrame_Info_S
{
    uint32      u32Sps;
    uint32      u32Pps;
    uint32      u32Sei;
    uint32      u32Vps;
    uint32      u32Spslens;
    uint32      u32Ppslens;
    uint32      u32Seilens;
    uint32      u32Vpslens;
    SV_BOOL     bH265;      /* 0:H264, 1:H265 */
    SV_BOOL     bInit;
} SFIFO_IFRAME_INFO_S;

/* 生产者信息 */
typedef struct tagProducer_S
{
    SV_BOOL     bValid;             /* 生产者是否有效 */
    uint32      u32WriteOffset;     /* 当前写入点位置 */
} SFIFO_PRODUCER_S;

/* 消费者信息 */
typedef struct tagConsumer_S
{
    SV_BOOL     bValid;             /* 消费者是否有效 */
    SV_BOOL     bGotFrm;            /* 消费者当前是否占用了数据帧 */
    uint32      u32ReadOffset;      /* 当前读取包位置 */
    uint64      u64ReadPts;         /* 当前读取帧时间 */
} SFIFO_CONSUMER_S;

/* 队列头信息 */
typedef struct tagSfifoQue_S
{
    uint32      u32FreeSize;        /* 队列空闲大小 */
    uint32      u32HeadOffset;      /* 队列头位置(读取位置) */
    uint32      u32TailOffset;      /* 队列尾位置(写入位置) */
    uint32      u32MaxOffset;       /* 队列缓存帧最大允许的偏移量 */
    uint32      u32IfrmOffset;      /* 队列缓存帧的最新I帧位置 */
    uint64      u64StartPts;        /* 队列缓存帧的起始时间 */
    uint64      u64EndPts;          /* 队列缓存帧的结束时间 */
    SFIFO_PRODUCER_S stProducer;    /* 生产者 */
    SFIFO_CONSUMER_S astConsumers[SFIFO_MAX_CONSUMER]; /* 消费者列表 */
    SFIFO_MEDIA_ATTR stMediaAttr;   /* 媒体数据属性 */
    char       *pvStartAddr[];      /* 数据包缓存区起始地址 */
} SFIFO_QUE_S;

/* 共享内存区头信息 */
typedef struct tagShmHead_S
{
    sint32      s32Key;             /* 共享内存块键值 */
    sint32      s32ShmId;           /* 共享内存块ID */
    uint32      u32Size;            /* 共享内存块大小 */
    sint32      s32SemId;           /* 信号量ID (用于队列头互斥访问) */
    uint32      au32Mux[10];        /* 互斥锁(用于LiteOS) */
} SHM_HEAD_S;

/* 队列共享内存信息 */
typedef struct tagSfifoShm_S
{
    SV_BOOL     bValid;             /* 队列是否有效 */
    void       *pvStartVirAddr;     /* 共享内存块映射的起始地址 */
    SHM_HEAD_S *pstShmHead;         /* 共享内存块头信息 */
    SFIFO_QUE_S *pstQueInfo;        /* 队列头信息 */
} SFIFO_SHM_S;

/* 调用的客户端数量监测 */
typedef struct tagSFifoClient_S
{
    sint32      s32MainWtd;         /* 主码流看门狗 */
    sint32      s32SubWtd;          /* 子码流看门狗 */

    uint32      u32WtdTid;          /* 看门狗线程 */
    SV_BOOL     bRunning;           /* 运行标志位 */
} SFIFO_Client_S;

/* 模块控制信息 */
typedef struct tagSfifoInfo_S
{
    SFIFO_SHM_S astQueList[SFIFO_MAX_QUE_NUM];  /* 队列列表 */
    SFIFO_Client_S  client;                     /* 客户端检测 */
} SFIFO_INFO_S;

SFIFO_INFO_S m_stSfifoInfo = {0};   /* 模块控制信息 */

typedef union idr_union
{
	char buf[4];
	int value;
} IDR_UNION;

sint32 isMjpegFrame(void *pbuf, int lens)
{
    int *pdata = pbuf;
    IDR_UNION szHead = {0xff, 0xd8, 0xff, 0xe0};    // JFIF
    if(*pdata != szHead.value)
        return SV_FALSE;
    return SV_TRUE;
}
sint32 get_IFrame_Info(void*pbuf, int lens,  SFIFO_IFRAME_INFO_S*pstIFrameInfo)
{
    IDR_UNION szHead = {0x00, 0x00, 0x00, 0x01};    // 小端字节序，低内存存放低位字节，所以value的值为01 00 00 00，下面处理时pdata要 << 24
    IDR_UNION szMove = {0x00, 0x00, 0x00, 0x00};
    uint8 *pdata = pbuf;
    int mode = 0; // 0 为无模式，1为sps, 2为pps, 3为sei, 4为vps
    int bsps = 0, bpps = 0, bsei = 0, bvps = 0;
    int i, count;

    for(i=0; i<lens; i++)
    {
        szMove.value = ((szMove.value >> 8) & 0x00ffffff) | (pdata[i] << 24);
        if(mode > 0)
            count++;
        if(szMove.value == szHead.value)
        {
            switch (mode) {
                case 0:
                    break;
                case 1:
                    //去掉下一个参数头部的4个字节:0x00,0x00,0x00,0x01.
                    pstIFrameInfo->u32Spslens = count - 4;
                    mode = 0;
                    bsps = SV_TRUE;
                	break;
                case 2:
                    pstIFrameInfo->u32Ppslens = count - 4;
                    mode = 0;
                    bpps = SV_TRUE;
                    break;
                case 3:
                    pstIFrameInfo->u32Seilens = count - 4;
                    mode = 0;
                    bsei = SV_TRUE;
                    break;
                case 4:
                    pstIFrameInfo->u32Vpslens = count - 4;
                    mode = 0;
                    bvps = SV_TRUE;
                default:
                    break;
            }
            if(i >= lens)
                continue;

            count = 0;
            /* H264/H265 sps, pps, sei 信息解析 */
            switch(pdata[i+1]){
                case 0x42:  /* H265 sps 头(0x21<<1)  */
                case 0x67:  /* H264 sps 头 */
                    mode = 1;
                    pstIFrameInfo->u32Sps = i+1;
                    break;
                case 0x44:  /* H265 pps 头 (0x22<<1) */
                case 0x68:  /* H264 pps 头 */
                    mode = 2;
                    pstIFrameInfo->u32Pps = i+1;
                    break;
                case 0x4e:  /* H265 sei 头 (0x27<<1) */
                case 0x06:  /* H264 sei 头 */
                    mode = 3;
                    pstIFrameInfo->u32Sei = i+1;
                    break;
                case 0x40:  /* H265 vps 头(0x20<<1) */
                    mode = 4;
                    pstIFrameInfo->u32Vps = i+1;
                    pstIFrameInfo->bH265 = SV_TRUE;
                    break;
                default:
                    break;
            }
        }
    }

    if(bsps == SV_FALSE ||  bpps == SV_FALSE)
    {
        return SV_FAILURE;
    }
    return SV_SUCCESS;

}

static void sfifo_feed_dog(sint32 s32QueId);
static void sfifo_wtd_body(void *pvArg);


/******************************************************************************
 * 共享内存区分布:
 * | SHM_HEAD_S | SFIFO_QUE_S | 数据区 |
 *****************************************************************************/
void SFIFO_PrintShmQueInfo(sint32 s32QueId)
{
    sint32 i;

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return;
    }

    sfifo_QueInfoLock(s32QueId, 0);
    printf("StartVirAddr: 0x%08x\n", m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr);
    printf("key:          0x%08x\n", m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32Key);
    printf("shmid:        %d\n", m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32ShmId);
    printf("size:         %d\n", m_stSfifoInfo.astQueList[s32QueId].pstShmHead->u32Size);
    printf("semid:        %d\n", m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32SemId);
    printf("mainfps:      %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stMediaAttr.stMainStreamAttr.u32FrameRate);
    printf("mainbitrate:  %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stMediaAttr.stMainStreamAttr.u32Bitrate);
    printf("subfps:       %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stMediaAttr.stSubStreamAttr.u32FrameRate);
    printf("subbitrate:   %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stMediaAttr.stSubStreamAttr.u32Bitrate);
    printf("muxid:        ");
    for (i = 0; i < 10; i++)
    {
        printf("%d ", m_stSfifoInfo.astQueList[s32QueId].pstShmHead->au32Mux[i]);
    }
    printf("\n");
    printf("freesize:     %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32FreeSize);
    printf("headoffset:   %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32HeadOffset);
    printf("tailoffset:   %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32TailOffset);
    printf("maxoffset:    %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32MaxOffset);
    printf("ifrmoffset:   %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32IfrmOffset);
    printf("start_pts:    %lld\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u64StartPts);
    printf("end_pts:      %lld\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u64EndPts);
    printf("start_addr:   0x%08x\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->pvStartAddr);
    printf("producer: %d, writeoffset: %d\n", m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stProducer.bValid, \
                                            m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stProducer.u32WriteOffset);
    for (i = 0; i < SFIFO_MAX_CONSUMER; i++)
    {
        printf("consumer[%d]: %d, %d, readoffset: %d\n", i, m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[i].bValid, \
                                            m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[i].bGotFrm, \
                                            m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[i].u32ReadOffset);
    }
    sfifo_QueInfoUnlock(s32QueId, 0);
}

sint32 SFIFO_CurConsumerNum(sint32 s32QueId)
{
    sint32 s32Cnt = 0, i;

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return -1;
    }

    //sfifo_QueInfoLock(s32QueId, 0);
    for (i = 0; i < SFIFO_MAX_CONSUMER; i++)
    {
        if(m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[i].bValid)
        {
            s32Cnt++;
        }
    }
    //sfifo_QueInfoUnlock(s32QueId, 0);

    return s32Cnt;
}

sint32 sfifo_printExtraInfo(char *pszExtraInfo, sint32 s32Len)
{
    sint32 i = 0;
    if (NULL == pszExtraInfo)
    {
        return SV_FAILURE;
    }

    for (i = 0; i < s32Len; i++)
    {
        printf("%02X ", *pszExtraInfo++);
    }
    printf("\n");

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 锁定队列头信息 (互斥访问)
 * 输入参数: s32QueId --- 队列ID
             s16Level --- 锁级别 [0-9], 用于嵌套加锁
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 sfifo_QueInfoLock(sint32 s32QueId, sint32 s32Level)
{
    sint32 s32Ret = 0;
    sint32 s32SemId = 0;
    sint32 s32MuxId = 0;
    struct sembuf stSemOpt;

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return ERR_UNEXIST;
    }

    s32SemId = m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32SemId;
    stSemOpt.sem_num = s32Level;
    stSemOpt.sem_op = -1;
    stSemOpt.sem_flg = SEM_UNDO;
    s32Ret = semop(s32SemId, &stSemOpt, 1);
    if (s32Ret < 0)
    {
        if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 解锁队列头信息 (互斥访问)
 * 输入参数: s32QueId --- 队列ID
             s32Level --- 锁级别 [0-9], 用于嵌套加锁
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 sfifo_QueInfoUnlock(sint32 s32QueId, sint32 s32Level)
{
    sint32 s32Ret = 0;
    sint32 s32SemId = 0;
    sint32 s32MuxId = 0;
    struct sembuf stSemOpt;

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return ERR_UNEXIST;
    }

    s32SemId = m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32SemId;
    stSemOpt.sem_num = s32Level;
    stSemOpt.sem_op = 1;
    stSemOpt.sem_flg = SEM_UNDO;
    s32Ret = semop(s32SemId, &stSemOpt, 1);
    if (s32Ret < 0)
    {
        if (ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EACCES == errno)
        {
            return ERR_NOT_PERM;
        }
        else if (EIDRM == errno || EINVAL == errno)
        {
            return ERR_UNEXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 清除无效的数据块 (已被所有消费者读过)
 * 输入参数: s32QueId --- 队列ID
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 sfifo_CleanInvalidPacket(sint32 s32QueId)
{
    sint32 i;
    uint32 u32MinOffset = 0, u32Offset;
    uint32 u32WaitReadSize = 0, u32MaxWaitReadSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;
    char *pPacketAddr = NULL;
    SFIFO_MSHEAD *pstPacket = NULL;

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return ERR_UNEXIST;
    }

    sfifo_QueInfoLock(s32QueId, 1);
    pstQueInfo = (SFIFO_QUE_S *)m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    u32MinOffset = pstQueInfo->u32HeadOffset;
    for (i = 0; i < SFIFO_MAX_CONSUMER; i++)
    {
        if (!pstQueInfo->astConsumers[i].bValid)
        {
            continue;
        }

        u32Offset = pstQueInfo->astConsumers[i].u32ReadOffset;
        u32WaitReadSize = (pstQueInfo->stProducer.u32WriteOffset + pstQueInfo->u32MaxOffset - u32Offset) % pstQueInfo->u32MaxOffset;
        if (u32MaxWaitReadSize < u32WaitReadSize)
        {
            u32MinOffset = u32Offset;
            u32MaxWaitReadSize = u32WaitReadSize;
        }
    }

    if (pstQueInfo->u32HeadOffset != u32MinOffset)
    {
        pstQueInfo->u32HeadOffset = u32MinOffset;
        pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
        if (pstQueInfo->u32FreeSize > pstQueInfo->u32MaxOffset)
        {
            printf("#%d, offset: %d->%d, add=%d, ifrm: %d\n", __LINE__, pstQueInfo->u32HeadOffset, u32MinOffset, ((u32MinOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset), pstQueInfo->u32IfrmOffset);
        }
        pstPacket = (SFIFO_MSHEAD *)((char *)pstQueInfo->pvStartAddr + pstQueInfo->u32HeadOffset);
        if (MSHEAD_FLAG == pstPacket->flag)
        {
            pstQueInfo->u64StartPts = pstPacket->pts;
        }
    }
    sfifo_QueInfoUnlock(s32QueId, 1);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 强制丢弃队头若干个数据块 (释放空间进行覆盖写入)
 * 输入参数: s32QueId --- 队列ID
             u32NeedSize --- 需要释放的空间大小
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 sfifo_DiscardHeadPackets(sint32 s32QueId, uint32 u32NeedSize)
{
    sint32 i;
    uint32 u32SizeCnt = 0;
    uint32 u32Offset = 0;
    uint32 u32WaitReadSize = 0, u32ValidSize = 0, u32TmpSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;
    char *pPacketAddr = NULL;
    SFIFO_MSHEAD *pstPacket = NULL;

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return ERR_UNEXIST;
    }

    sfifo_QueInfoLock(s32QueId, 1);
    pstQueInfo = (SFIFO_QUE_S *)m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    if (0 == u32NeedSize || u32NeedSize >= pstQueInfo->u32MaxOffset)
    {
        sfifo_QueInfoUnlock(s32QueId, 1);
        return ERR_ILLEGAL_PARAM;
    }

    u32SizeCnt = 0;
    u32Offset = pstQueInfo->u32HeadOffset;
    while (1)
    {
        pstPacket = (SFIFO_MSHEAD *)((char *)pstQueInfo->pvStartAddr + u32Offset);
        if (MSHEAD_FLAG != pstPacket->flag)
        {
            printf("#%d, pstPacket=0x%08x, pstPacket->flag=%#x, u32Offset=%d, u32SizeCnt=%d, u32MaxOffset=%d\n", __LINE__, pstPacket, pstPacket->flag, u32Offset, u32SizeCnt, pstQueInfo->u32MaxOffset);
            break;
        }

        if ((u32SizeCnt >= u32NeedSize && (1 == pstPacket->type)) || (u32SizeCnt >= 256*1024))
        {
            break;
        }

        u32SizeCnt += (pstPacket->mshsize + pstPacket->msdsize);
        u32SizeCnt = ADDR_ALIGN4(u32SizeCnt);
        u32Offset += (pstPacket->mshsize + pstPacket->msdsize);
        u32Offset = ADDR_ALIGN4(u32Offset);     /* 保证帧地址4字节对齐, 提高访问效率 */
        if (u32Offset > pstQueInfo->u32MaxOffset)
        {
            u32Offset = 0;
        }
    }

    if (MSHEAD_FLAG != pstPacket->flag)
    {
        sfifo_QueInfoUnlock(s32QueId, 1);
        return ERR_BADADDR;
    }

    pstQueInfo->u32HeadOffset = u32Offset;
    pstQueInfo->u64StartPts = pstPacket->pts;
    pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
    u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset;
    u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
    pstQueInfo->u32IfrmOffset = (u32TmpSize > u32ValidSize) ? pstQueInfo->u32HeadOffset : pstQueInfo->u32IfrmOffset;
    for (i = 0; i < SFIFO_MAX_CONSUMER; i++)
    {
        if (!pstQueInfo->astConsumers[i].bValid || pstQueInfo->astConsumers[i].bGotFrm)
        {
            continue;
        }
        //
        sfifo_QueInfoLock(s32QueId, i+2);
        u32WaitReadSize = (pstQueInfo->stProducer.u32WriteOffset + pstQueInfo->u32MaxOffset - pstQueInfo->astConsumers[i].u32ReadOffset) % pstQueInfo->u32MaxOffset;
        if (u32WaitReadSize > u32ValidSize)
        {
            pstQueInfo->astConsumers[i].u32ReadOffset = pstQueInfo->u32HeadOffset;
        }
        sfifo_QueInfoUnlock(s32QueId, i+2);
    }

    sfifo_QueInfoUnlock(s32QueId, 1);

    return SV_SUCCESS;
}

sint32 SFIFO_CreateQue(char *pszPathName, uint32 u32Size, sint32 *ps32QueId)
{
    sint32 s32Ret = 0, i;
    sint32 s32QueId = 0;
    uint32 u32Reserve = 0;
    uint32 u32MinQueSize = 0;
    uint32 u32MaxQueSize = 0;
    key_t key;
    uint32 u32SemNum = 10;
    sint32 s32ShmId = 0;
    sint32 s32SemId = 0;
    uint32 au32Mux[10] = {0};
    union semun {
       int              val;    /* Value for SETVAL */
       struct semid_ds *buf;    /* Buffer for IPC_STAT, IPC_SET */
       unsigned short  *array;  /* Array for GETALL, SETALL */
       struct seminfo  *__buf;  /* Buffer for IPC_INFO */
    } unSemArgs;
    unsigned short au16Val[10];
    void *pvStartVirAddr = NULL;

    if (NULL == pszPathName || NULL == ps32QueId)
    {
        return ERR_NULL_PTR;
    }

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    if (0 == strcmp(pszPathName, SFIFO_MAIN_STREAM))
    {
        u32Reserve = SFIFO_VIDEO_RESERVE;
        u32MinQueSize = SFIFO_VIDEO_MIN_QUE_SIZE;
        u32MaxQueSize = SFIFO_VIDEO_MAX_QUE_SIZE;
    }
    else if (0 == strcmp(pszPathName, SFIFO_SUB_STREAM))
    {
        u32Reserve = SFIFO_SUB_RESERVE;
        u32MinQueSize = SFIFO_SUB_MIN_QUE_SIZE;
        u32MaxQueSize = SFIFO_SUB_MAX_QUE_SIZE;
    }
#if (defined(BOARD_IPCR20S3))
    else if (0 == strcmp(pszPathName, SFIFO_PIC_STREAM))
    {
        u32Reserve = SFIFO_PIC_RESERVE;
        u32MinQueSize = SFIFO_PIC_MIN_QUE_SIZE;
        u32MaxQueSize = SFIFO_PIC_MAX_QUE_SIZE;
    }
#endif
    else
    {
        u32Reserve = SFIFO_AUDIO_RESERVE;
        u32MinQueSize = SFIFO_AUDIO_MIN_QUE_SIZE;
        u32MaxQueSize = SFIFO_AUDIO_MAX_QUE_SIZE;
    }
#else
    u32Reserve = (0 == strcmp(pszPathName, SFIFO_AUD_STREAM)) ? SFIFO_AUDIO_RESERVE : SFIFO_VIDEO_RESERVE;
    u32MinQueSize = (0 == strcmp(pszPathName, SFIFO_AUD_STREAM)) ? SFIFO_AUDIO_MIN_QUE_SIZE : SFIFO_VIDEO_MIN_QUE_SIZE;
    u32MaxQueSize = (0 == strcmp(pszPathName, SFIFO_AUD_STREAM)) ? SFIFO_AUDIO_MAX_QUE_SIZE : SFIFO_VIDEO_MAX_QUE_SIZE;
#endif
    if (u32Size < u32MinQueSize || u32Size > u32MaxQueSize
    || 0 != access(pszPathName, F_OK)
    )
    {
        if (u32Size < u32MinQueSize)
        {
            printf("u32Size < u32MinQueSize!\n");
        }
        else if (u32Size > u32MaxQueSize)
        {
            printf("u32Size > u32MaxQueSize!\n");
        }
        else
        {
            printf("0 != access(pszPathName, F_OK)!\n");
        }
        return ERR_ILLEGAL_PARAM;
    }

    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (!m_stSfifoInfo.astQueList[i].bValid)
        {
            break;
        }
    }
    if (i >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_NOBUF;
    }

    s32QueId = i;
#if defined(BOARD_ADA32IR)
    if (0 == strcmp(pszPathName, SFIFO_IR_VMIX_STREAM))
    {
        printf("Now Create the IR Vmix Queue:%d\n", s32QueId);
    }

    if (0 == strcmp(pszPathName, SFIFO_IR_IRMAIN_STREAM))
    {
        printf("Now Create the IR MainStream Queue:%d\n", s32QueId);
    }
#endif
    key = ftok(pszPathName, 0);
    s32SemId = semget(key, u32SemNum, IPC_CREAT|0600);
    if (s32SemId < 0)
    {
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    unSemArgs.array = au16Val;
    for (i = 0; i < u32SemNum; i++)
    {
        au16Val[i] = 1;
    }
    s32Ret = semctl(s32SemId, 0, SETALL, unSemArgs);
    if (s32Ret < 0)
    {
        semctl(s32SemId, 0, IPC_RMID, 0);
        return ERR_SYS_NOTREADY;
    }

    s32ShmId = shmget(key, u32Size, IPC_CREAT|0600);
    if (s32ShmId < 0)
    {
        semctl(s32SemId, 0, IPC_RMID, 0);
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pvStartVirAddr = shmat(s32ShmId, NULL, 0);
    if (NULL == pvStartVirAddr)
    {
        semctl(s32SemId, 0, IPC_RMID, 0);
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }

    memset(pvStartVirAddr, 0x0, u32Size);
    m_stSfifoInfo.astQueList[s32QueId].bValid = SV_TRUE;
    m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr = pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead = (SHM_HEAD_S *)pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32Key = (sint32)key;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32ShmId = s32ShmId;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead->u32Size = u32Size;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32SemId = s32SemId;
    memcpy(&m_stSfifoInfo.astQueList[s32QueId].pstShmHead->au32Mux[0], &au32Mux[0], sizeof(au32Mux));
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo = (SFIFO_QUE_S *)((char *)pvStartVirAddr + sizeof(SHM_HEAD_S));
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32FreeSize = u32Size - sizeof(SHM_HEAD_S) - sizeof(SFIFO_QUE_S) - u32Reserve;
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32MaxOffset = m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->u32FreeSize;
    *ps32QueId = s32QueId;

    return SV_SUCCESS;
}

sint32 SFIFO_DestroyQue(sint32 s32QueId)
{
    sint32 s32Ret = 0, i;

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return ERR_UNEXIST;
    }

    sfifo_QueInfoLock(s32QueId, 0);
    m_stSfifoInfo.astQueList[s32QueId].bValid = SV_FALSE;
    sfifo_QueInfoUnlock(s32QueId, 0);
    semctl(m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32SemId, 0, IPC_RMID, 0);
    shmctl(m_stSfifoInfo.astQueList[s32QueId].pstShmHead->s32ShmId, IPC_RMID, NULL);
    shmdt(m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr);

    m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr = NULL;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead = NULL;
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo = NULL;

    return SV_SUCCESS;
}

sint32 SFIFO_ForQueryOpen(char *pszPathName, sint32 *ps32QueId)
{
    sint32 s32Ret = 0, i;
    sint32 s32QueId = 0;
    key_t key;
    sint32 s32ShmId = 0;
    void *pvStartVirAddr = NULL;

    if (NULL == pszPathName || NULL == ps32QueId)
    {
        return ERR_NULL_PTR;
    }

    if (0 != access(pszPathName, F_OK))
    {
        return ERR_ILLEGAL_PARAM;
    }

    key = ftok(pszPathName, 0);
    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (m_stSfifoInfo.astQueList[i].bValid && m_stSfifoInfo.astQueList[i].pstShmHead->s32Key == key)
        {
            *ps32QueId = i;
            return SV_SUCCESS;
        }
    }

    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (!m_stSfifoInfo.astQueList[i].bValid)
        {
            break;
        }
    }
    if (i >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_NOBUF;
    }

    s32QueId = i;
    s32ShmId = shmget(key, 0, 0600);
    if ( s32ShmId < 0 )
    {
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pvStartVirAddr = shmat(s32ShmId, NULL, 0);
    if (NULL == pvStartVirAddr)
    {
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }

    m_stSfifoInfo.astQueList[s32QueId].bValid = SV_TRUE;
    m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr = pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead = (SHM_HEAD_S *)pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo = (SFIFO_QUE_S *)((char *)pvStartVirAddr + sizeof(SHM_HEAD_S));
    *ps32QueId = s32QueId;

    return SV_SUCCESS;
}


sint32 SFIFO_ForWriteOpen(char *pszPathName, sint32 *ps32QueId)
{
    sint32 s32Ret = 0, i;
    sint32 s32QueId = 0;
    key_t key;
    sint32 s32ShmId = 0;
    void *pvStartVirAddr = NULL;

    if (NULL == pszPathName || NULL == ps32QueId)
    {
        return ERR_NULL_PTR;
    }

    if (0 != access(pszPathName, F_OK))
    {
        return ERR_ILLEGAL_PARAM;
    }

    key = ftok(pszPathName, 0);
    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (m_stSfifoInfo.astQueList[i].bValid && m_stSfifoInfo.astQueList[i].pstShmHead->s32Key == key)
        {
            if (m_stSfifoInfo.astQueList[i].pstQueInfo->stProducer.bValid)
            {
                return ERR_NOT_PERM;
            }
            else
            {
                sfifo_QueInfoLock(i, 0);
                m_stSfifoInfo.astQueList[i].pstQueInfo->stProducer.bValid = SV_TRUE;
                sfifo_QueInfoUnlock(i, 0);
                *ps32QueId = i;
                return SV_SUCCESS;
            }
        }
    }

    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (!m_stSfifoInfo.astQueList[i].bValid)
        {
            break;
        }
    }
    if (i >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_NOBUF;
    }

    s32QueId = i;
    s32ShmId = shmget(key, 0, 0600);
    if ( s32ShmId < 0 )
    {
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pvStartVirAddr = shmat(s32ShmId, NULL, 0);
    if (NULL == pvStartVirAddr)
    {
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }

    m_stSfifoInfo.astQueList[s32QueId].bValid = SV_TRUE;
    m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr = pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead = (SHM_HEAD_S *)pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo = (SFIFO_QUE_S *)((char *)pvStartVirAddr + sizeof(SHM_HEAD_S));
    sfifo_QueInfoLock(s32QueId, 0);
    if (m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stProducer.bValid)
    {
        sfifo_QueInfoUnlock(s32QueId, 0);
        return ERR_NOT_PERM;
    }

    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stProducer.bValid = SV_TRUE;
    sfifo_QueInfoUnlock(s32QueId, 0);
    *ps32QueId = s32QueId;

    return SV_SUCCESS;
}

sint32 SFIFO_ForReadOpen(char *pszPathName, sint32 *ps32QueId, sint32 *ps32ConsumerId)
{
    sint32 s32Ret = 0, i, j;
    SV_BOOL bNoConsumer = SV_TRUE;
    sint32 s32QueId = 0;
    key_t key;
    sint32 s32ShmId = 0;
    void *pvStartVirAddr = NULL;
    uint32 u32ValidSize = 0, u32TmpSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;

    if (NULL == pszPathName || NULL == ps32QueId)
    {
        return ERR_NULL_PTR;
    }

    if (0 != access(pszPathName, F_OK))
    {
        return ERR_ILLEGAL_PARAM;
    }

    key = ftok(pszPathName, 0);
    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (m_stSfifoInfo.astQueList[i].bValid && m_stSfifoInfo.astQueList[i].pstShmHead->s32Key == key)
        {
            sfifo_QueInfoLock(i, 0);
            pstQueInfo = m_stSfifoInfo.astQueList[i].pstQueInfo;
            for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
            {
                if (pstQueInfo->astConsumers[j].bValid)
                {
                    bNoConsumer = SV_FALSE;
                    break;
                }
            }
            for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
            {
                if (!pstQueInfo->astConsumers[j].bValid)
                {
                    pstQueInfo->astConsumers[j].bValid = SV_TRUE;
                    pstQueInfo->astConsumers[j].bGotFrm = SV_FALSE;
                    u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset;
                    u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
                    printf("#%d, headOffset: %d, ifrmOffset: %d, validSize: %d, tmpSize: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->u32IfrmOffset, u32ValidSize, u32TmpSize);
                    pstQueInfo->astConsumers[j].u32ReadOffset = (u32TmpSize <= u32ValidSize) ? pstQueInfo->u32IfrmOffset : pstQueInfo->u32HeadOffset;
                    if (bNoConsumer)
                    {
                        printf("#%d, headOffset: %d -> %d, addFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->astConsumers[j].u32ReadOffset, \
                               (pstQueInfo->astConsumers[j].u32ReadOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
                        pstQueInfo->u32HeadOffset = pstQueInfo->astConsumers[j].u32ReadOffset;
                        pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
                    }
                    *ps32QueId = i;
                    *ps32ConsumerId = j;
                    sfifo_QueInfoUnlock(i, 0);
                    return SV_SUCCESS;
                }
            }
            sfifo_QueInfoUnlock(i, 0);
            return ERR_BUF_FULL;
        }
    }

    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (!m_stSfifoInfo.astQueList[i].bValid)
        {
            break;
        }
    }
    if (i >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_NOBUF;
    }

    s32QueId = i;
    s32ShmId = shmget(key, 0, 0600);
    if ( s32ShmId < 0 )
    {
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pvStartVirAddr = shmat(s32ShmId, NULL, 0);
    if (NULL == pvStartVirAddr)
    {
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }

    m_stSfifoInfo.astQueList[s32QueId].bValid = SV_TRUE;
    m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr = pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead = (SHM_HEAD_S *)pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo = (SFIFO_QUE_S *)((char *)pvStartVirAddr + sizeof(SHM_HEAD_S));
    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
    {
        if (pstQueInfo->astConsumers[j].bValid)
        {
            bNoConsumer = SV_FALSE;
            break;
        }
    }
    for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
    {
        if (!pstQueInfo->astConsumers[j].bValid)
        {
            pstQueInfo->astConsumers[j].bValid = SV_TRUE;
            pstQueInfo->astConsumers[j].bGotFrm = SV_FALSE;
            u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset;
            u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
            printf("#%d, headOffset: %d, ifrmOffset: %d, validSize: %d, tmpSize: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->u32IfrmOffset, u32ValidSize, u32TmpSize);
            pstQueInfo->astConsumers[j].u32ReadOffset = (u32TmpSize <= u32ValidSize) ? pstQueInfo->u32IfrmOffset : pstQueInfo->u32HeadOffset;
            if (bNoConsumer)
            {
                printf("#%d, headOffset: %d -> %d, addFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->astConsumers[j].u32ReadOffset, \
                                            (pstQueInfo->astConsumers[j].u32ReadOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
                pstQueInfo->u32HeadOffset = pstQueInfo->astConsumers[j].u32ReadOffset;
                pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
            }
            *ps32QueId = s32QueId;
            *ps32ConsumerId = j;
            sfifo_QueInfoUnlock(s32QueId, 0);
            return SV_SUCCESS;
        }
    }
    sfifo_QueInfoUnlock(s32QueId, 0);

    return ERR_BUF_FULL;
}

sint32 SFIFO_ForReadClose(sint32 s32QueId, sint32 s32ConsumerId)
{
    sint32 j;
    SV_BOOL bOnlyConsumer = SV_TRUE;
    uint32 u32ValidSize = 0, u32TmpSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM || s32ConsumerId < 0 || s32ConsumerId >= SFIFO_MAX_CONSUMER)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid || !m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[s32ConsumerId].bValid)
    {
        return ERR_UNEXIST;
    }

    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
    {
        if (j == s32ConsumerId)
        {
            continue;
        }
        if (pstQueInfo->astConsumers[j].bValid)
        {
            bOnlyConsumer = SV_FALSE;
            break;
        }
    }
    //
    //sfifo_QueInfoLock(s32QueId, s32ConsumerId+2);
    pstQueInfo->astConsumers[s32ConsumerId].bValid = SV_FALSE;
    pstQueInfo->astConsumers[s32ConsumerId].bGotFrm = SV_FALSE;
    pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = 0;
    pstQueInfo->astConsumers[s32ConsumerId].u64ReadPts = 0ll;
    //sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);

    if (bOnlyConsumer)
    {
        u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset;
        u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
        printf("#%d, headOffset: %d -> %d, delFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->u32IfrmOffset, u32TmpSize - u32ValidSize);
        if (u32ValidSize < u32TmpSize)
        {
            printf("#%d, headOffset: %d -> %d, delFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->u32IfrmOffset, u32TmpSize - u32ValidSize);
            pstQueInfo->u32HeadOffset = pstQueInfo->u32IfrmOffset;
            pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
        }
    }
    sfifo_CleanInvalidPacket(s32QueId);
    sfifo_QueInfoUnlock(s32QueId, 0);

    return SV_SUCCESS;
}

sint32 SFIFO_WritePacket(sint32 s32QueId, SFIFO_MSHEAD *pstPacketHead, SFIFO_MDADDR *pstDataInfo)
{
    sint32 s32Ret = 0, i;
    uint32 u32Reserve = 0;
    uint32 u32PacketSize = 0, u32Offset = 0;
    uint32 u32AppendSize = 0, u32MinCoverSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;
    SFIFO_VIDEO_ATTR *pstVideoAttr = NULL;
    char *pPacketAddr = NULL;
    static SFIFO_IFRAME_INFO_S szH264_IFrame_Info[SFIFO_MAX_QUE_NUM] = {0};

    if (NULL == pstPacketHead || NULL == pstDataInfo)
    {
        return ERR_NULL_PTR;
    }

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM || MSHEAD_FLAG != pstPacketHead->flag ||
        0 == pstDataInfo->u32DataCnt || pstDataInfo->u32DataCnt > SFIFO_MAX_DATA_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return ERR_UNEXIST;
    }

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    if (1 == s32QueId)
    {
        u32Reserve = SFIFO_SUB_RESERVE;
    }
    else
    {
        u32Reserve = SFIFO_VIDEO_RESERVE;
    }
#else
    u32Reserve = SFIFO_VIDEO_RESERVE;
#endif
    u32PacketSize = sizeof(SFIFO_MSHEAD);
    for (i = 0; i < pstDataInfo->u32DataCnt; i++)
    {
        u32PacketSize += pstDataInfo->au32Len[i];
    }

    if (u32PacketSize > u32Reserve)
    {
        printf("u32PacketSize = %d\n", u32PacketSize);
        return ERR_NOMEM;
    }

#if (defined(BOARD_IPCR20S3) || defined(BOARD_IPCR20S4))
    if (1 == s32QueId)
    {
        u32AppendSize = 100*1024;//128*1024;
        u32MinCoverSize = SFIFO_SUB_MIN_COVER;
    }
    else
    {
        u32AppendSize = 900*1024;
        u32MinCoverSize = SFIFO_VIDEO_MIN_COVER;
    }
#else
    u32AppendSize = 900*1024;
    u32MinCoverSize = SFIFO_VIDEO_MIN_COVER;
#endif

    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    //pstQueInfo->u32FreeSize -= u32PacketSize;
    if (u32PacketSize + u32AppendSize > pstQueInfo->u32FreeSize)
    {
        //printf("Insufficient space left, discard head packets. u32PacketSize = %d, u32AppendSize = %d, u32FreeSize = %d.\n", u32PacketSize, u32AppendSize, pstQueInfo->u32FreeSize);
        s32Ret = sfifo_DiscardHeadPackets(s32QueId, (u32PacketSize < u32MinCoverSize) ? u32MinCoverSize : u32PacketSize);
        if (SV_SUCCESS != s32Ret)
        {
            sfifo_QueInfoUnlock(s32QueId, 0);
            return s32Ret;
        }
    }


    pstPacketHead->mshsize = sizeof(SFIFO_MSHEAD);
    pstPacketHead->msdsize = u32PacketSize - sizeof(SFIFO_MSHEAD);
    pPacketAddr = (char *)pstQueInfo->pvStartAddr + pstQueInfo->u32TailOffset;
    memcpy(pPacketAddr, (char *)pstPacketHead, sizeof(SFIFO_MSHEAD));
    u32Offset = sizeof(SFIFO_MSHEAD);
    for (i = 0; i < pstDataInfo->u32DataCnt; i++)
    {
        if (1 == pstPacketHead->type && pstDataInfo->u32DataCnt == 4 && i < 3)//h264
        {
#if !defined(BOARD_ADA32IR)
            pstVideoAttr = (0 == s32QueId) ? &pstQueInfo->stMediaAttr.stMainStreamAttr : &pstQueInfo->stMediaAttr.stSubStreamAttr;
#else
            switch (s32QueId) 
            {
                case 0:
                    // 主视频流（Main Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stMainStreamAttr;
                    break;
                case 3:
                    // 混合视频流（Vmix Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stVmixStreamAttr;
                    break;
                case 4:
                    // 红外主视频流（新增：Infrared Main Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stIrMainStreamAttr;
                    break;
                default:
                    // 子视频流（Sub Stream）或其他默认情况
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stSubStreamAttr;
                    break;
            }
#endif
            if (!pstVideoAttr->bExtraValid)
            {
                switch (i)
                {
                    case 0:
                        memcpy(pstVideoAttr->au8SpsData, pstDataInfo->pau8Addr[i]+4, pstDataInfo->au32Len[i]-4);
                        pstVideoAttr->u32SpsLen = pstDataInfo->au32Len[i]-4;
                        break;
                    case 1:
                        memcpy(pstVideoAttr->au8PpsData, pstDataInfo->pau8Addr[i]+4, pstDataInfo->au32Len[i]-4);
                        pstVideoAttr->u32PpsLen = pstDataInfo->au32Len[i]-4;
                        break;
                    case 2:
                        memcpy(pstVideoAttr->au8SeiData, pstDataInfo->pau8Addr[i]+4, pstDataInfo->au32Len[i]-4);
                        pstVideoAttr->u32SeiLen = pstDataInfo->au32Len[i]-4;
                        break;
                }

                if (2 == i)
                {
                    sint32 j = 0;
                    printf("SPS[%d]: ", pstVideoAttr->u32SpsLen);
                    for (j = 0; j < pstVideoAttr->u32SpsLen; j++)
                    {
                        printf("%02x ", pstVideoAttr->au8SpsData[j]);
                    }
                    printf("\nPPS[%d]: ", pstVideoAttr->u32PpsLen);
                    for (j = 0; j < pstVideoAttr->u32PpsLen; j++)
                    {
                        printf("%02x ", pstVideoAttr->au8PpsData[j]);
                    }
                    printf("\nSEI[%d]: ", pstVideoAttr->u32SeiLen);
                    for (j = 0; j < pstVideoAttr->u32SeiLen; j++)
                    {
                        printf("%02x ", pstVideoAttr->au8SeiData[j]);
                    }
                    printf("\n");
                    pstVideoAttr->bExtraValid = SV_TRUE;
                }
            }
        }
#if (defined(PLATFORM_RV1126) || defined(PLATFORM_RV1106))
        else if(1 == pstPacketHead->type && isMjpegFrame(pstDataInfo->pau8Addr[0], 32)) //判断MJPEG
        {
            pstVideoAttr = &pstQueInfo->stMediaAttr.stMainStreamAttr;
            if (!pstVideoAttr->bNoExtraData)
            {
                pstVideoAttr->bNoExtraData = SV_TRUE;
            }
        }
        else if(1 == pstPacketHead->type && pstDataInfo->u32DataCnt == 1) //单包h264
        {
#if !defined(BOARD_ADA32IR)
            pstVideoAttr = (0 == s32QueId) ? &pstQueInfo->stMediaAttr.stMainStreamAttr : &pstQueInfo->stMediaAttr.stSubStreamAttr;
#else
            switch (s32QueId) 
            {
                case 0:
                    // 主视频流（Main Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stMainStreamAttr;
                    break;
                case 3:
                    // 混合视频流（Vmix Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stVmixStreamAttr;
                    break;
                case 4:
                    // 红外主视频流（新增：Infrared Main Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stIrMainStreamAttr;
                    break;
                default:
                    // 子视频流（Sub Stream）或其他默认情况
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stSubStreamAttr;
                    break;
            }
#endif
            if (!pstVideoAttr->bExtraValid && pstDataInfo->au32Len[0] > 128)
            {
                s32Ret = get_IFrame_Info(pstDataInfo->pau8Addr[0], 128, &szH264_IFrame_Info[s32QueId]);
                if(SV_SUCCESS !=s32Ret)
                {
                    sfifo_QueInfoUnlock(s32QueId, 0);
                    return SV_FAILURE;
                }

                memcpy(pstVideoAttr->au8SpsData, pstDataInfo->pau8Addr[0]+(int)(szH264_IFrame_Info[s32QueId].u32Sps), szH264_IFrame_Info[s32QueId].u32Spslens);
                pstVideoAttr->u32SpsLen = szH264_IFrame_Info[s32QueId].u32Spslens;
                memcpy(pstVideoAttr->au8PpsData, pstDataInfo->pau8Addr[0]+(int)(szH264_IFrame_Info[s32QueId].u32Pps), szH264_IFrame_Info[s32QueId].u32Ppslens);
                pstVideoAttr->u32PpsLen = szH264_IFrame_Info[s32QueId].u32Ppslens;
                if (SV_TRUE == szH264_IFrame_Info[s32QueId].bH265)
                {
                    memcpy(pstVideoAttr->au8VpsData, pstDataInfo->pau8Addr[0]+(int)(szH264_IFrame_Info[s32QueId].u32Vps), szH264_IFrame_Info[s32QueId].u32Vpslens);
                    pstVideoAttr->u32VpsLen = szH264_IFrame_Info[s32QueId].u32Vpslens;
                }

                printf("vps:%d, vpslen:%d, sps:%d, spslen:%d, pps:%d, ppslen:%d, sei:%d, seilen:%d\n", \
                    szH264_IFrame_Info[s32QueId].u32Vps, szH264_IFrame_Info[s32QueId].u32Vpslens, \
                    szH264_IFrame_Info[s32QueId].u32Sps, szH264_IFrame_Info[s32QueId].u32Spslens, \
                    szH264_IFrame_Info[s32QueId].u32Pps, szH264_IFrame_Info[s32QueId].u32Ppslens, \
                    szH264_IFrame_Info[s32QueId].u32Sei, szH264_IFrame_Info[s32QueId].u32Seilens);

                printf("queId: %d, vpsdata: ", s32QueId);
                sfifo_printExtraInfo(pstVideoAttr->au8VpsData, szH264_IFrame_Info[s32QueId].u32Vpslens);

                printf("queId: %d, spsdata: ", s32QueId);
                sfifo_printExtraInfo(pstVideoAttr->au8SpsData, szH264_IFrame_Info[s32QueId].u32Spslens);

                printf("queId: %d, ppsdata: ", s32QueId);
                sfifo_printExtraInfo(pstVideoAttr->au8PpsData, szH264_IFrame_Info[s32QueId].u32Ppslens);
                //memcpy(pstVideoAttr->au8SeiData, pstDataInfo->pau8Addr[0]+(int)(szH264_IFrame_Info.u32Sei), szH264_IFrame_Info.u32Seilens);
                //pstVideoAttr->u32SeiLen = szH264_IFrame_Info.u32Seilens;
                pstVideoAttr->u32SeiLen = 0;
                pstVideoAttr->bExtraValid = SV_TRUE;
            }
        }
#endif
        else if(1 == pstPacketHead->type && pstDataInfo->u32DataCnt == 5 && i < 4)//h265
        {
#if !defined(BOARD_ADA32IR)
            pstVideoAttr = (0 == s32QueId) ? &pstQueInfo->stMediaAttr.stMainStreamAttr : &pstQueInfo->stMediaAttr.stSubStreamAttr;
#else
            switch (s32QueId) 
            {
                case 0:
                    // 主视频流（Main Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stMainStreamAttr;
                    break;
                case 3:
                    // 混合视频流（Vmix Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stVmixStreamAttr;
                    break;
                case 4:
                    // 红外主视频流（新增：Infrared Main Stream）
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stIrMainStreamAttr;
                    break;
                default:
                    // 子视频流（Sub Stream）或其他默认情况
                    pstVideoAttr = &pstQueInfo->stMediaAttr.stSubStreamAttr;
                    break;
            }
#endif
            if (!pstVideoAttr->bExtraValid)
            {
                switch (i)
                {
                    case 0:
                        memcpy(pstVideoAttr->au8VpsData, pstDataInfo->pau8Addr[i]+4, pstDataInfo->au32Len[i]-4);
                        pstVideoAttr->u32VpsLen = pstDataInfo->au32Len[i]-4;
                        break;
                    case 1:
                        memcpy(pstVideoAttr->au8SpsData, pstDataInfo->pau8Addr[i]+4, pstDataInfo->au32Len[i]-4);
                        pstVideoAttr->u32SpsLen = pstDataInfo->au32Len[i]-4;
                        break;
                    case 2:
                        memcpy(pstVideoAttr->au8PpsData, pstDataInfo->pau8Addr[i]+4, pstDataInfo->au32Len[i]-4);
                        pstVideoAttr->u32PpsLen = pstDataInfo->au32Len[i]-4;
                        break;
                    case 3:
                        memcpy(pstVideoAttr->au8SeiData, pstDataInfo->pau8Addr[i]+4, pstDataInfo->au32Len[i]-4);
                        pstVideoAttr->u32SeiLen = pstDataInfo->au32Len[i]-4;
                        break;
                }

                if (3 == i)
                {
                    sint32 j = 0;
                    printf("VPS[%d]: ", pstVideoAttr->u32VpsLen);
                    for (j = 0; j < pstVideoAttr->u32VpsLen; j++)
                    {
                        printf("%02x ", pstVideoAttr->au8VpsData[j]);
                    }
                    printf("\nSPS[%d]: ", pstVideoAttr->u32SpsLen);
                    for (j = 0; j < pstVideoAttr->u32SpsLen; j++)
                    {
                        printf("%02x ", pstVideoAttr->au8SpsData[j]);
                    }
                    printf("\nPPS[%d]: ", pstVideoAttr->u32PpsLen);
                    for (j = 0; j < pstVideoAttr->u32PpsLen; j++)
                    {
                        printf("%02x ", pstVideoAttr->au8PpsData[j]);
                    }
                    printf("\nSEI[%d]: ", pstVideoAttr->u32SeiLen);
                    for (j = 0; j < pstVideoAttr->u32SeiLen; j++)
                    {
                        printf("%02x ", pstVideoAttr->au8SeiData[j]);
                    }
                    printf("\n");
                    pstVideoAttr->bExtraValid = SV_TRUE;
                }
            }
        }
        else if(1 == pstPacketHead->type && (pstDataInfo->u32DataCnt == 1 || pstDataInfo->u32DataCnt == 2))//mjpeg, hisi 2p, ss 1p
        {
            //printf("Writed MJpeg Packet.\n");
            //pstVideoAttr = (0 == s32QueId) ? &pstQueInfo->stMediaAttr.stMainStreamAttr : &pstQueInfo->stMediaAttr.stSubStreamAttr;
            pstVideoAttr = (0 == s32QueId) ? &pstQueInfo->stMediaAttr.stMainStreamAttr : &pstQueInfo->stMediaAttr.stPicStreamAttr;
            if (!pstVideoAttr->bNoExtraData)
                pstVideoAttr->bNoExtraData = SV_TRUE;
            //printf("Writed Packet Type: %d, Serial: %d, pts: %lld.\n", pstPacketHead->type, pstPacketHead->serial, pstPacketHead->pts);
        }
        else if(2 == pstPacketHead->type)//audio
        {
            //printf("Writed Packet Type: %d, Serial: %d, pts: %lld, size: %d.\n", pstPacketHead->type, pstPacketHead->serial, pstPacketHead->pts, pstPacketHead->msdsize);
        }
        memcpy(pPacketAddr+u32Offset, pstDataInfo->pau8Addr[i], pstDataInfo->au32Len[i]);
        u32Offset += pstDataInfo->au32Len[i];
    }

#if 1
    pstQueInfo->u32FreeSize -= u32PacketSize;
#endif
    pstQueInfo->u64EndPts = pstPacketHead->pts;
    pstQueInfo->u32IfrmOffset = (1 == pstPacketHead->type) ? pstQueInfo->u32TailOffset : pstQueInfo->u32IfrmOffset;
    pstQueInfo->u32TailOffset += u32PacketSize;
    pstQueInfo->u32TailOffset = ADDR_ALIGN4(pstQueInfo->u32TailOffset);     /* 保证帧地址4字节对齐, 提高访问效率 */
    if (pstQueInfo->u32TailOffset > pstQueInfo->u32MaxOffset)
    {
        pstQueInfo->u32TailOffset = 0;
    }
    pstQueInfo->stProducer.u32WriteOffset = pstQueInfo->u32TailOffset;
    sfifo_QueInfoUnlock(s32QueId, 0);

    return SV_SUCCESS;
}

sint32 SFIFO_GetPacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD **ppstPacket)
{
    sint32 s32Ret = 0, i;
    SFIFO_QUE_S *pstQueInfo = NULL;
    SFIFO_MSHEAD *pstPacket = NULL;

    if (NULL == ppstPacket)
    {
        //printf("ERR_NULL_PTR!\n");
        return ERR_NULL_PTR;
    }

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM || s32ConsumerId < 0 || s32ConsumerId >= SFIFO_MAX_CONSUMER)
    {
        //printf("ERR_ILLEGAL_PARAM!\n");
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid || !m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[s32ConsumerId].bValid)
    {
        //printf("ERR_UNEXIST!\n");
        return ERR_UNEXIST;
    }

#if USING_SHAREFIFO_OPT
    sfifo_feed_dog(s32QueId);
#endif

    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = (SFIFO_QUE_S *)m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    //printf("readOffset is %d, writeOffset is %d.\n", pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset, pstQueInfo->stProducer.u32WriteOffset);
    if (pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset == pstQueInfo->stProducer.u32WriteOffset)
    {
        sfifo_QueInfoUnlock(s32QueId, 0);
        //printf("ERR_NOBUF!\n");
        return ERR_NOBUF;
    }

    sfifo_QueInfoLock(s32QueId, s32ConsumerId+2);   // 保证在release时释放该锁
    pstPacket = (SFIFO_MSHEAD *)((char *)pstQueInfo->pvStartAddr + pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset);
    if (MSHEAD_FLAG != pstPacket->flag)
    {
        printf("#%d, pstPacket=0x%08x, pstPacket->flag=%#x, u32Offset=%d\n", __LINE__, pstPacket, pstPacket->flag, pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset);
        sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
        sfifo_QueInfoUnlock(s32QueId, 0);
        return ERR_BADADDR;
    }

    pstQueInfo->astConsumers[s32ConsumerId].u64ReadPts = pstPacket->pts;
    pstQueInfo->astConsumers[s32ConsumerId].bGotFrm = SV_TRUE;
    *ppstPacket = pstPacket;
    sfifo_QueInfoUnlock(s32QueId, 0);

    return SV_SUCCESS;
}

sint32 SFIFO_ReleasePacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD *pstPacket)
{
    sint32 s32Ret = 0;
    uint32 u32ValidSize = 0, u32TmpSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;
    char *pPacketAddr = NULL;
    SFIFO_MSHEAD *ifIframeAddr = NULL;

    if (NULL == pstPacket)
    {
        return ERR_NULL_PTR;
    }

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM || s32ConsumerId < 0 || s32ConsumerId >= SFIFO_MAX_CONSUMER || MSHEAD_FLAG != pstPacket->flag)
    {
        //printf("%d pstPacket->flag = %#x\n", __LINE__, pstPacket->flag);
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid || !m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[s32ConsumerId].bValid)
    {
        return ERR_UNEXIST;
    }

    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = (SFIFO_QUE_S *)m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    pPacketAddr = (char *)((char *)pstQueInfo->pvStartAddr + pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset);
    if (pPacketAddr != pstPacket)
    {
        pstQueInfo->astConsumers[s32ConsumerId].bGotFrm = SV_FALSE;
        sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
        sfifo_QueInfoUnlock(s32QueId, 0);
        return ERR_BADADDR;
    }

    pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset += (sizeof(SFIFO_MSHEAD) + pstPacket->msdsize);
/*
    if(pstPacket->type == 1)
    {
        printf("type=%d WritePts = %lld, ReadPts = %lld.\n", pstPacket->type,pstQueInfo->u64EndPts, pstQueInfo->astConsumers[s32ConsumerId].u64ReadPts);
    }*/

    /* 读取时间戳与写入时间戳的间隔，判断是否读取超时 */
    if (llabs(pstQueInfo->u64EndPts - pstQueInfo->astConsumers[s32ConsumerId].u64ReadPts) > 500000)
    {
        /* 读取位置与写入位置的距离，即待读取的有效数据大小 */
        u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset) % pstQueInfo->u32MaxOffset;
        /* 写入位置与最新I帧位置的距离 */
        u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
        /* 最新I帧地址 */
        ifIframeAddr = (SFIFO_MSHEAD *)((char *)pstQueInfo->pvStartAddr + pstQueInfo->u32IfrmOffset);
        /* 若存在待读取的有效I帧则跳至最新I帧继续读取，否则跳至写入位置读取 */
        pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = (u32TmpSize <= u32ValidSize && ifIframeAddr->type == 1 && ifIframeAddr->flag == MSHEAD_FLAG) ? pstQueInfo->u32IfrmOffset : pstQueInfo->u32TailOffset;
        printf("#%d release, u32TmpSize=%d u32ValidSize=%d u32IfrmOffset=%d u32TailOffset=%d u32ReadOffset=%d TYPE=%d flag=%#x\n",__LINE__, u32TmpSize,u32ValidSize,pstQueInfo->u32IfrmOffset,pstQueInfo->u32TailOffset,pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset,ifIframeAddr->type,ifIframeAddr->flag);
    }

    pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = ADDR_ALIGN4(pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset);     /* 保证帧地址4字节对齐, 提高访问效率 */
    if (pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset > pstQueInfo->u32MaxOffset)
    {
        pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = 0;
    }

    pstQueInfo->astConsumers[s32ConsumerId].bGotFrm = SV_FALSE;
    sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
    sfifo_CleanInvalidPacket(s32QueId);
    sfifo_QueInfoUnlock(s32QueId, 0);

    return SV_SUCCESS;
}

sint32 SFIFO_SetMediaAttr(sint32 s32QueId, SFIFO_MEDIA_ATTR *pstMediaAttr)
{
    if (NULL == pstMediaAttr)
    {
        return ERR_NULL_PTR;
    }

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        return ERR_UNEXIST;
    }

    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stMediaAttr = *pstMediaAttr;

    return SV_SUCCESS;
}

sint32 SFIFO_GetMediaAttr(sint32 s32QueId, SFIFO_MEDIA_ATTR *pstMediaAttr)
{
    if (NULL == pstMediaAttr)
    {
        return ERR_NULL_PTR;
    }

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid)
    {
        printf("%s: s32QueId[%d] is invalid!\n", __func__, s32QueId);
        return ERR_UNEXIST;
    }

    *pstMediaAttr = m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->stMediaAttr;


    return SV_SUCCESS;
}

sint32 SFIFO_ForRecReadOpen(char *pszPathName, sint32 *ps32QueId, sint32 *ps32ConsumerId )
{
    sint32 s32Ret = 0, i, j;
    SV_BOOL bNoConsumer = SV_TRUE;
    sint32 s32QueId = 0;
    key_t key;
    sint32 s32ShmId = 0;
    void *pvStartVirAddr = NULL;
    uint32 u32ValidSize = 0, u32TmpSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;
    SFIFO_MSHEAD *pstPacket = NULL;
    uint32 u32Offset = 0;

    if (NULL == pszPathName || NULL == ps32QueId)
    {
        return ERR_NULL_PTR;
    }

    if (0 != access(pszPathName, F_OK))
    {
        return ERR_ILLEGAL_PARAM;
    }

    key = ftok(pszPathName, 0);
    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (m_stSfifoInfo.astQueList[i].bValid && m_stSfifoInfo.astQueList[i].pstShmHead->s32Key == key)
        {
            sfifo_QueInfoLock(i, 0);
            pstQueInfo = m_stSfifoInfo.astQueList[i].pstQueInfo;
            for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
            {
                if (pstQueInfo->astConsumers[j].bValid)
                {
                    bNoConsumer = SV_FALSE;
                    break;
                }
            }

            for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
            {
                if (!pstQueInfo->astConsumers[j].bValid)
                {
                    pstQueInfo->astConsumers[j].bValid = SV_TRUE;
                    pstQueInfo->astConsumers[j].bGotFrm = SV_FALSE;
                    u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset;
                    u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
                    printf("#%d, headOffset: %d, ifrmOffset: %d, validSize: %d, tmpSize: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->u32IfrmOffset, u32ValidSize, u32TmpSize);

                    //获取头部的包
                    u32Offset = pstQueInfo->u32HeadOffset;
                    pstPacket = (SFIFO_MSHEAD *)((char *)pstQueInfo->pvStartAddr + pstQueInfo->u32HeadOffset);
                    //保证读取的那个帧是I帧
                    while((pstPacket->type != 1)||(MSHEAD_FLAG != pstPacket->flag))
                    {
                        if (MSHEAD_FLAG != pstPacket->flag)
                        {
                            printf("#%d, pstPacket=0x%08x, pstPacket->flag=%#x, u32Offset=%d, u32SizeCnt=%d, u32MaxOffset=%d\n", __LINE__, pstPacket, pstPacket->flag, u32Offset, pstPacket->mshsize+pstPacket->msdsize, pstQueInfo->u32MaxOffset);
                            pstQueInfo->astConsumers[j].bValid = SV_FALSE;
                            sfifo_QueInfoUnlock(i, 0);
                            return ERR_INVALID_DATA;
                        }
                        //printf("loop for read head Iframe\n");
                        u32Offset += sizeof(SFIFO_MSHEAD) + pstPacket->msdsize;
                        u32Offset = ADDR_ALIGN4(u32Offset);
                        if (u32Offset > pstQueInfo->u32MaxOffset)
                        {
                            u32Offset = 0;
                        }

                        if(u32Offset == pstQueInfo->stProducer.u32WriteOffset)
                        {
                            printf("#%d, read over address\n", __LINE__);
                            pstQueInfo->astConsumers[j].bValid = SV_FALSE;
                            sfifo_QueInfoUnlock(i, 0);
                            return ERR_BUSY;
                        }

                        pstPacket = (SFIFO_MSHEAD *)((char *)pstQueInfo->pvStartAddr + u32Offset);
                    }
                    pstQueInfo->astConsumers[j].u32ReadOffset = u32Offset;
                    printf("#%d,  Open packet address 0x%08x\n", __LINE__, pstPacket);
                    if (bNoConsumer)
                    {
                        printf("#%d, headOffset: %d -> %d, addFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->astConsumers[j].u32ReadOffset, \
                                            (pstQueInfo->astConsumers[j].u32ReadOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
                        pstQueInfo->u32HeadOffset = pstQueInfo->astConsumers[j].u32ReadOffset;
                        pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
                    }

                    *ps32QueId = i;
                    *ps32ConsumerId = j;
                    sfifo_QueInfoUnlock(i, 0);
                    return SV_SUCCESS;
                }
            }
            sfifo_QueInfoUnlock(i, 0);
            return ERR_BUF_FULL;
        }
    }

    for (i = 0; i < SFIFO_MAX_QUE_NUM; i++)
    {
        if (!m_stSfifoInfo.astQueList[i].bValid)
        {
            break;
        }
    }
    if (i >= SFIFO_MAX_QUE_NUM)
    {
        return ERR_NOBUF;
    }

    s32QueId = i;
    s32ShmId = shmget(key, 0, 0600);
    if ( s32ShmId < 0 )
    {
        if (EINVAL == errno || ENOSPC == errno || ENOMEM == errno)
        {
            return ERR_NOMEM;
        }
        else if (EEXIST == errno)
        {
            return ERR_EXIST;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    pvStartVirAddr = shmat(s32ShmId, NULL, 0);
    if (NULL == pvStartVirAddr)
    {
        shmctl(s32ShmId, IPC_RMID, NULL);
        return ERR_BADADDR;
    }

    m_stSfifoInfo.astQueList[s32QueId].bValid = SV_TRUE;
    m_stSfifoInfo.astQueList[s32QueId].pvStartVirAddr = pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstShmHead = (SHM_HEAD_S *)pvStartVirAddr;
    m_stSfifoInfo.astQueList[s32QueId].pstQueInfo = (SFIFO_QUE_S *)((char *)pvStartVirAddr + sizeof(SHM_HEAD_S));
    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
    {
        if (pstQueInfo->astConsumers[j].bValid)
        {
            bNoConsumer = SV_FALSE;
            break;
        }
    }
    for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
    {
        if (!pstQueInfo->astConsumers[j].bValid)
        {
            pstQueInfo->astConsumers[j].bValid = SV_TRUE;
            pstQueInfo->astConsumers[j].bGotFrm = SV_FALSE;
            u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset;
            u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
            printf("#%d, headOffset: %d, ifrmOffset: %d, validSize: %d, tmpSize: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->u32IfrmOffset, u32ValidSize, u32TmpSize);
            pstQueInfo->astConsumers[j].u32ReadOffset = (u32TmpSize <= u32ValidSize) ? pstQueInfo->u32IfrmOffset : pstQueInfo->u32HeadOffset;
            if (bNoConsumer)
            {
                printf("#%d, headOffset: %d -> %d, addFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->astConsumers[j].u32ReadOffset, \
                                            (pstQueInfo->astConsumers[j].u32ReadOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
                pstQueInfo->u32HeadOffset = pstQueInfo->astConsumers[j].u32ReadOffset;
                pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
            }
            *ps32QueId = s32QueId;
            *ps32ConsumerId = j;
            sfifo_QueInfoUnlock(s32QueId, 0);
            return SV_SUCCESS;
        }
    }
    sfifo_QueInfoUnlock(s32QueId, 0);

    return ERR_BUF_FULL;
}

sint32 SFIFO_ForRecReadClose(sint32 s32QueId, sint32 s32ConsumerId)
{
    sint32 j;
    SV_BOOL bOnlyConsumer = SV_TRUE;
    uint32 u32ValidSize = 0, u32TmpSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;

    uint32 u32Offset = 0;

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM || s32ConsumerId < 0 || s32ConsumerId >= SFIFO_MAX_CONSUMER)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid || !m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[s32ConsumerId].bValid)
    {
        return ERR_UNEXIST;
    }

    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    for (j = 0; j < SFIFO_MAX_CONSUMER; j++)
    {
        if (j == s32ConsumerId)
        {
            continue;
        }
        if (pstQueInfo->astConsumers[j].bValid)
        {
            bOnlyConsumer = SV_FALSE;
            break;
        }
    }
    //
    u32Offset = pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset;
    pstQueInfo->astConsumers[s32ConsumerId].bValid = SV_FALSE;
    pstQueInfo->astConsumers[s32ConsumerId].bGotFrm = SV_FALSE;
    pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = 0;
    pstQueInfo->astConsumers[s32ConsumerId].u64ReadPts = 0ll;

    if (bOnlyConsumer)
    {
        u32ValidSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset;
        u32TmpSize = (pstQueInfo->u32TailOffset + pstQueInfo->u32MaxOffset - pstQueInfo->u32IfrmOffset) % pstQueInfo->u32MaxOffset;
        printf("#%d, headOffset: %d -> %d, delFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, pstQueInfo->u32IfrmOffset, u32TmpSize - u32ValidSize);
        if (u32ValidSize < u32TmpSize)
        {
            printf("#%d, headOffset: %d -> %d, delFree: %d\n", __LINE__, pstQueInfo->u32HeadOffset, u32Offset, u32TmpSize - u32ValidSize);
            pstQueInfo->u32HeadOffset = u32Offset;
            pstQueInfo->u32FreeSize = pstQueInfo->u32MaxOffset - ((u32Offset + pstQueInfo->u32MaxOffset - pstQueInfo->u32HeadOffset) % pstQueInfo->u32MaxOffset);
        }
    }

    sfifo_CleanInvalidPacket(s32QueId);
    sfifo_QueInfoUnlock(s32QueId, 0);

    return SV_SUCCESS;
}
sint32 SFIFO_RecReleasePacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD *pstPacket)
{
    sint32 s32Ret = 0;
    uint32 u32ValidSize = 0, u32TmpSize = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;
    char *pPacketAddr = NULL;
    SFIFO_MSHEAD *ifIframeAddr = NULL;

    if (NULL == pstPacket)
    {
        return ERR_NULL_PTR;
    }

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM || s32ConsumerId < 0 || s32ConsumerId >= SFIFO_MAX_CONSUMER || MSHEAD_FLAG != pstPacket->flag)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid || !m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[s32ConsumerId].bValid)
    {
        return ERR_UNEXIST;
    }

    sfifo_QueInfoLock(s32QueId, 0);
    pstQueInfo = (SFIFO_QUE_S *)m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    pPacketAddr = (char *)((char *)pstQueInfo->pvStartAddr + pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset);
    if (pPacketAddr != pstPacket)
    {
        pstQueInfo->astConsumers[s32ConsumerId].bGotFrm = SV_FALSE;
        sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
        sfifo_QueInfoUnlock(s32QueId, 0);
        return ERR_BADADDR;
    }

    pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset += (sizeof(SFIFO_MSHEAD) + pstPacket->msdsize);

    pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = ADDR_ALIGN4(pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset);     /* 保证帧地址4字节对齐, 提高访问效率 */
    //printf("release packet address 0x%08x\n",pPacketAddr);
    if (pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset > pstQueInfo->u32MaxOffset)
    {
        pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = 0;
    }

    pstQueInfo->astConsumers[s32ConsumerId].bGotFrm = SV_FALSE;
    sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
    //sfifo_CleanInvalidPacket(s32QueId);
    sfifo_QueInfoUnlock(s32QueId, 0);

    return SV_SUCCESS;
}

sint32 SFIFO_RecGetPacket(sint32 s32QueId, sint32 s32ConsumerId, SFIFO_MSHEAD **ppstPacket)
{
    return SFIFO_GetPacket(s32QueId, s32ConsumerId, ppstPacket);
}

sint32 SFIFO_RecMoveReadFrontPtsFrm(sint32 s32QueId, sint32 s32ConsumerId, sint32 s32FrontMs, uint64 *pu64Pts)
{
    sint32 s32Ret = 0;
    uint32 u32Offset = 0;
    SFIFO_QUE_S *pstQueInfo = NULL;
    SFIFO_MSHEAD *pstPacket = NULL;

    if (s32QueId < 0 || s32QueId >= SFIFO_MAX_QUE_NUM || s32ConsumerId < 0 || s32ConsumerId >= SFIFO_MAX_CONSUMER || s32FrontMs < 0 || s32FrontMs > SFIFO_VIDEO_MAX_FRONT_TIME)
    {
        return ERR_ILLEGAL_PARAM;
    }

    if (NULL == pu64Pts)
    {
        return ERR_NULL_PTR;
    }

    if (!m_stSfifoInfo.astQueList[s32QueId].bValid || !m_stSfifoInfo.astQueList[s32QueId].pstQueInfo->astConsumers[s32ConsumerId].bValid)
    {
        return ERR_UNEXIST;
    }

    pstQueInfo = (SFIFO_QUE_S *)m_stSfifoInfo.astQueList[s32QueId].pstQueInfo;
    sfifo_QueInfoLock(s32QueId, 0);
    sfifo_QueInfoLock(s32QueId, s32ConsumerId+2);
    u32Offset = pstQueInfo->u32HeadOffset;
    while(1)
    {
        pstPacket = (SFIFO_MSHEAD *)((char *)pstQueInfo->pvStartAddr + u32Offset);
        if (MSHEAD_FLAG != pstPacket->flag)
        {
            printf("#%d, pstPacket=0x%08x, pstPacket->flag=%#x, u32Offset=%d, u32SizeCnt=%d, u32MaxOffset=%d\n", __LINE__, pstPacket, pstPacket->flag, u32Offset, pstPacket->mshsize+pstPacket->msdsize, pstQueInfo->u32MaxOffset);
            sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
            sfifo_QueInfoUnlock(s32QueId, 0);
            return ERR_INVALID_DATA;
        }

        if (u32Offset == pstQueInfo->stProducer.u32WriteOffset)
        {
            printf("#%d, read over address\n", __LINE__);
            sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
            sfifo_QueInfoUnlock(s32QueId, 0);
            return ERR_BUSY;
        }

        if (pstPacket->type == 1
            && ((pstPacket->pts > pstQueInfo->u64EndPts-s32FrontMs*1000)
                ||(llabs(pstQueInfo->u64EndPts - (pstPacket->pts+s32FrontMs*1000)) < 500000)))
        {
            break;
        }

        u32Offset += sizeof(SFIFO_MSHEAD) + pstPacket->msdsize;
        u32Offset = ADDR_ALIGN4(u32Offset);
        if (u32Offset > pstQueInfo->u32MaxOffset)
        {
            u32Offset = 0;
        }
    }

    *pu64Pts = pstPacket->pts;
    pstQueInfo->astConsumers[s32ConsumerId].u32ReadOffset = u32Offset;
    sfifo_QueInfoUnlock(s32QueId, s32ConsumerId+2);
    sfifo_QueInfoUnlock(s32QueId, 0);

    return SV_SUCCESS;
}

sint32 SFIFO_Start()
{
    sint32 s32Ret = 0, index;
    uint32 u32Tid = 0;

    pthread_attr_t 	attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程

    m_stSfifoInfo.client.bRunning = SV_TRUE;
    s32Ret = pthread_create(&u32Tid, &attr, sfifo_wtd_body, &m_stSfifoInfo);
    if(0 != s32Ret)
    {
        printf("Start sfifo_wtd_body failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 SFIFO_Stop()
{
    m_stSfifoInfo.client.bRunning = SV_FALSE;
    return SV_SUCCESS;
}

void SFIFO_Reset_Dog()
{
    sfifo_feed_dog(0);
    sfifo_feed_dog(1);
}

static void sfifo_feed_dog(sint32 s32QueId)
{
    if (s32QueId == 0)
    {
        m_stSfifoInfo.client.s32MainWtd = 100;
    }
    else if (s32QueId == 1)
    {
        m_stSfifoInfo.client.s32SubWtd = 100;
    }
}

static void sfifo_wtd_body(void *pvArg)
{
    sint32 s32Ret;
    SFIFO_INFO_S *pstSfifoInfo = (SFIFO_INFO_S *) pvArg;
    DUMP_SHAREFIFO_S stDumpShareFifo = {0};
    SV_BOOL bMainStream = SV_TRUE;
    SV_BOOL bSubStream = SV_TRUE;

    /* 先设置为100 */
    pstSfifoInfo->client.s32MainWtd = 100;
    pstSfifoInfo->client.s32SubWtd = 100;

    stDumpShareFifo.bMainStream = bMainStream;
    stDumpShareFifo.bSubStream = bSubStream;

    s32Ret = dump_SetShareFifoInfo(&stDumpShareFifo);
    if (s32Ret != SV_SUCCESS)
    {
        printf("dump_SetShareFifoInfo failed! [err=%d]!\n", s32Ret);
    }

    while(pstSfifoInfo->client.bRunning)
    {
        if (pstSfifoInfo->client.s32MainWtd > 0)
            pstSfifoInfo->client.s32MainWtd -= 1;

        if (pstSfifoInfo->client.s32SubWtd > 0)
            pstSfifoInfo->client.s32SubWtd -=1;

        /* 使用异或判定 */
        if ( ((pstSfifoInfo->client.s32MainWtd > 0) ^ (bMainStream)) ||
             ((pstSfifoInfo->client.s32SubWtd > 0) ^ (bSubStream))
           )
        {
            bMainStream = pstSfifoInfo->client.s32MainWtd > 0;
            bSubStream = pstSfifoInfo->client.s32SubWtd > 0;

            stDumpShareFifo.bMainStream = bMainStream;
            stDumpShareFifo.bSubStream = bSubStream;

            s32Ret = dump_SetShareFifoInfo(&stDumpShareFifo);
            if (s32Ret != SV_SUCCESS)
            {
                printf("dump_SetShareFifoInfo failed! [err=%d]!\n", s32Ret);
            }
        }

        sleep_ms(100);
    }

}




