/* $Id$ */
/* 
 * Copyright (C) 2008-2009 Teluu Inc. (http://www.teluu.com)
 * Copyright (C) 2003-2008 <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA 
 */
#ifndef __PJSIP_SIP_AUTOCONF_H__
#define __PJSIP_SIP_AUTOCONF_H__

/**
 * @file sip_autoconf.h
 * @brief Describes operating system specifics (automatically detected by
 *        autoconf)
 */

/*
 * Enable/disable TLS transport, as configured by autoconf.
 * But only do this if user doesn't explicitly configure in pj/config_site.h.
 */
/* Since 1.5, the default setting will follow PJ_HAS_SSL_SOCK setting. */
//#ifndef PJSIP_HAS_TLS_TRANSPORT
//#undef PJSIP_HAS_TLS_TRANSPORT
//#endif

#endif	/* __PJSIP_SIP_AUTOCONF_H__ */

