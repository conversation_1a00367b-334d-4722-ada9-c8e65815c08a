<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head>
    <meta http-equiv="Content-Type" content="text/html; charset=us-ascii">
    <link rel="stylesheet" type="text/css" href="style.css">
    <title>Cygwin Licensing Terms</title>
  </head>

<body>
<div id="navbar">
<b class="rtop"><b class="r1"></b><b class="r2"></b><b class="r3"></b><b class="r4"></b></b>
<ul>
<li><a href="http://cygwin.com/index.html">Cygwin</a></li>
<li><a href="http://cygwin.com/install.html">&nbsp;&nbsp;&nbsp;Install Cygwin</a></li>
<li><a href="http://cygwin.com/install.html">&nbsp;&nbsp;&nbsp;Update Cygwin</a></li>
<li><a href="http://cygwin.com/packages/">&nbsp;&nbsp;&nbsp;Search Packages</a></li>
<li><a href="http://cygwin.com/licensing.html">&nbsp;&nbsp;&nbsp;Licensing Terms</a></li>
</ul>
<ul>
<li><a href="http://x.cygwin.com/">Cygwin/X</a></li>
</ul>
<ul>
<li><a href="http://cygwin.com/who.html">Community</a></li>
<li><a href="http://cygwin.com/problems.html">&nbsp;&nbsp;&nbsp;Reporting Problems</a></li>
<li><a href="http://cygwin.com/lists.html">&nbsp;&nbsp;&nbsp;Mailing Lists</a></li>
<li><a href="http://gmane.org/find.php?list=cygwin">&nbsp;&nbsp;&nbsp;Newsgroups</a></li>
<li><a href="http://cygwin.com/goldstars/" style="color: gold;">&nbsp;&nbsp;&nbsp;Gold Stars</a></li>
<li><a href="http://cygwin.com/mirrors.html">&nbsp;&nbsp;&nbsp;Mirror Sites</a></li>
<li><a href="http://cygwin.com/donations.html">&nbsp;&nbsp;&nbsp;Donations</a></li>
</ul>
<ul>
<li><a href="http://cygwin.com/docs.html">Documentation</a></li>
<li><a href="http://cygwin.com/faq.html">&nbsp;&nbsp;&nbsp;FAQ</a></li>
<li><a href="http://cygwin.com/cygwin-ug-net.html">&nbsp;&nbsp;&nbsp;User's Guide</a></li>
<li><a href="http://cygwin.com/cygwin-api/">&nbsp;&nbsp;&nbsp;API Reference</a></li>
<li><a href="http://cygwin.com/acronyms/">&nbsp;&nbsp;&nbsp;Acronyms</a></li>
</ul>
<ul>
<li><a href="http://cygwin.com/contrib.html">Contributing</a></li>
<li><a href="http://cygwin.com/snapshots/">&nbsp;&nbsp;&nbsp;Snapshots</a></li>
<li><a href="http://cygwin.com/cvs.html">&nbsp;&nbsp;&nbsp;Source in CVS</a></li>
<li><a href="http://cygwin.com/setup.html">&nbsp;&nbsp;&nbsp;Cygwin Packages</a></li>
</ul>
<ul>
<li><a href="http://cygwin.com/links.html">Related Sites</a></li>
<li><a href="http://www.redhat.com/software/cygwin/">Red Hat Cygwin Product</a></li>
</ul>
<b class="rbottom"><b class="r4"></b><b class="r3"></b><b class="r2"></b><b class="r1"></b></b>
</div>

<div id="main">
 <div id="top">
  <div id="background">
   <b class="rtop"><b class="r1"></b><b class="r2"></b><b class="r3"></b><b class="r4"></b></b>
   <h1 id="big-title">Cygwin</h1>
   <p class="catchphrase">Get that Linux feeling - on Windows!</p>
   <b class="rbottom"><b class="r4"></b><b class="r3"></b><b class="r2"></b><b class="r1"></b></b>
  </div>
 </div>

<h1>What are the licensing terms?</h1>

<p>
Most of the tools are covered by the GNU GPL, some are public domain,
and others have a X11 style license.  To cover the GNU GPL
requirements, the basic rule is if you give out any binaries, you must
also make the source available.  For the full details, be sure to read
the text of the <a href="http://cygwin.com/COPYING">GNU General Public License (GPL)</a>.
For more information on the GPL see the <a href="http://gnu.org/licenses/gpl-faq.html">GPL FAQ</a>.
</p>

<p>
The Cygwin&#8482; API library found in the winsup subdirectory of the
source code is also covered by the GNU GPL (with exceptions; see
below).  By default, all executables link against this library (and in
the process include GPL'd Cygwin&#8482; glue code).  This means that unless
you modify the tools so that compiled executables do not make use of
the Cygwin&#8482; library, your compiled programs will also have to be free
software distributed under the GPL with source code available to all.
</p>

<p><i>The following text is a partial copy of the CYGWIN_LICENSE file from the
Cygwin&#8482; sources</i></p>

<div id="background">
<b class="rtop"><b class="r1"></b><b class="r2"></b><b class="r3"></b><b class="r4"></b></b>
<h2>Cygwin&#8482; API Licensing Terms</h2>
<b class="rbottom"><b class="r4"></b><b class="r3"></b><b class="r2"></b><b class="r1"></b></b>
</div><br>

<tt>
<p>Cygwin&#8482; is free software.  Red Hat, Inc. licenses Cygwin to you under
the terms of the GNU General Public License as published by the Free Software
Foundation; you can redistribute it and/or modify it under the terms of
the GNU General Public License either version 3 of the license, or (at your
option) any later version (GPLv3+), along with the additional permissions
given below.</p>

<p>There is NO WARRANTY for this software, express or implied, including
the implied warranties of MERCHANTABILITY or FITNESS FOR A PARTICULAR
PURPOSE.  See the GNU General Public License for more details.</p>
<p>You should have received a copy of the GNU General Public License along
with this program.  If not, see
&lt;<a href="http://gnu.org/licenses/">http://gnu.org/licenses/</a>&gt;.</p>
</tt>

<div id="background">
<b class="rtop"><b class="r1"></b><b class="r2"></b><b class="r3"></b><b class="r4"></b></b>
<h2>Cygwin&#8482; Open Source Licensing Exception</h2>
<b class="rbottom"><b class="r4"></b><b class="r3"></b><b class="r2"></b><b class="r1"></b></b>
</div><br>

<tt>
<p>As a special exception to GPLv3+, Red Hat grants you permission to link
software whose sources are distributed under a license that satisfies
the Open Source Definition with libcygwin.a, without libcygwin.a
itself causing the resulting program to be covered by GPLv3+.</p>

<p>This means that you can port an Open Source application to Cygwin&#8482;,
and distribute that executable as if it didn't include a copy of
libcygwin.a linked into it.  Note that this does not apply to
the Cygwin&#8482; DLL itself.  If you distribute a (possibly modified)
version of the Cygwin&#8482; DLL, you must adhere to the terms of the GPLv3+,
including the requirement to provide sources for the Cygwin&#8482; DLL,
unless you have obtained a special Cygwin&#8482; license to distribute the
Cygwin&#8482; DLL in only its binary form (see below).</p>
<p>See <a href="http://www.opensource.org/docs/osd/">http://www.opensource.org/docs/osd/</a>
for the precise Open Source Definition referenced above.</p>
</tt>

<div id="background">
<b class="rtop"><b class="r1"></b><b class="r2"></b><b class="r3"></b><b class="r4"></b></b>
<h2>Cygwin&#8482; Alternative License</h2>
<b class="rbottom"><b class="r4"></b><b class="r3"></b><b class="r2"></b><b class="r1"></b></b>
</div><br>

<tt>
<p>Red Hat sells a special Cygwin&#8482; License for customers who are unable to
provide their application in open source code form.  For more information,
please see:
<a href="http://www.redhat.com/software/cygwin/">http://www.redhat.com/software/cygwin/</a>,
or call +1-866-2REDHAT ext.&nbsp;45300 (toll-free in the US).</p>

<p>Outside the US call your
<a href="http://www.redhat.com/about/contact/ww/">regional Red Hat office</a>.
</p>
</tt>

<div id="background">
<b class="rtop"><b class="r1"></b><b class="r2"></b><b class="r3"></b><b class="r4"></b></b>
<h2>Does Cygwin&#8482; have an ECCN number?</h2>
<b class="rbottom"><b class="r4"></b><b class="r3"></b><b class="r2"></b><b class="r1"></b></b>
</div><br>

<tt>
<p>No.  Cygwin source and binary are made publicly available and free of
charge to download so Cygwin is provided under TSU/TSPA exemption. As a result,
Cygwin does not require an ECCN number.</p>
</tt>



</div></body></html>
