/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名: librtsp.cpp

作者: 许家铭    版本: v1.0.0(初始版本号)    日期: 2017-11-24

文件功能描述: RTSP服务器端功能定义

版本: v1.0.0(最新版本号)
  
历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <errno.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <time.h>

#define COM_MOD_ID  COM_ID_IPSERVER

#include "common.h"
#include "print.h"
#include "op.h"
#include "msg.h"
#include "sharefifo.h"
#include "cJSON.h"
#include "BasicUsageEnvironment.hh"
#include "liveMedia.hh"
#include "InputFile.hh"
#include "GroupsockHelper.hh"
#include "librtsp.h"

/* 模块控制信息 */
typedef struct tag_rtspSvrComInfo_S
{
    uint32      u32MainFramerate;       /* 主码流帧率 */
    uint32      u32SubFramerate;        /* 主码流帧率 */
    SV_BOOL     bAudioEnable;           /* 是否使能音频流 */
    AUD_ENC_E   enAudioEncType;         /* 音频编码类型[AUD_ENC_E] */
    AUD_SR_E    enAudioSampleRate;      /* 音频采样率 */
    TaskScheduler *pcsScheduler;        /* live555 调试器 */
    UsageEnvironment *pcsEnv;           /* live555 使用环境 */
    RTSPServer *pcsRtspLiveServer;      /* live555 rtsp服务器 */
    SV_BOOL     bRtspStreamPause;       /* live555 rtsp数据流暂时 */
    uint32      u32TidService;          /* 服务端线程ID */
    uint32      u32TidStatus;           /* 状态信息线程ID */
    SV_BOOL     bRunning;               /* 线程是否正在运行 */
    char        eventLoopWatchVariable; /* live555 循环监测变量(非0退出) */
} RTSP_COM_INFO_S;

RTSP_COM_INFO_S m_stRtspInfo;     /* 模块控制信息 */
RTSPServer *rtspLiveServer;


static void sendRtspLog(char const *pszClientIp, char const *pszEvent, char const *pszStream)
{
    char logLine[1024];
    snprintf(logLine, 1024, "type=\"RTSP\"\tclient=\"%s\"\tevent=\"%s\"\tstream=\"%s\"", pszClientIp, pszEvent, pszStream);
    LOG_Submit(-1, logLine);
}

static uint32 transSampleRate2Num(AUD_SR_E enSampleRate)
{
    uint32 u32SampleRate = 0;
    
    switch (enSampleRate)
    {
        case AUD_SR_8K:
            u32SampleRate = 8000;
            break;
        case AUD_SR_16K:
            u32SampleRate = 16000;
            break;
        case AUD_SR_32K:
            u32SampleRate = 32000;
            break;
    }

    return u32SampleRate;
}

static WAV_AUDIO_FORMAT transAudioFormat(AUD_ENC_E enAudEnc)
{
    switch (enAudEnc)
    {
        case AUD_ENC_G711A:
            return WA_PCMA;

        case AUD_ENC_G711U:
            return WA_PCMU;

        case AUD_ENC_ADPCM:
            return WA_IMA_ADPCM;

        case AUD_ENC_LPCM:
            return WA_PCM;

        default:
            return WA_PCMA;
    }
}

class MyUsageEnvironment: public BasicUsageEnvironment
{
public:
    static MyUsageEnvironment* createNew(TaskScheduler& taskScheduler)
    {
		return new MyUsageEnvironment(taskScheduler);
	}

	void writeLog(char const* clientAddr, RTSP_EVENT_E eventType, char const* streamName)
	{
	    char szEvent[32] = {0};
	    static time_t tLastMainAlive = 0, tLastSubAlive = 0;
	    time_t tNow;
	    
        //print_level(SV_DEBUG, "[%s] %d %s\n", clientAddr, eventType, streamName);
        switch (eventType)
        {
            case RTSP_CONNECTION:
                strcpy(szEvent, "CONNECTION");
                break;
            case RTSP_OPTIONS:
                strcpy(szEvent, "OPTIONS");
                break;
            case RTSP_DESCRIBE:
                strcpy(szEvent, "DESCRIBE");
                break;
            case RTSP_SETUP:
                strcpy(szEvent, "SETUP");
                break;
            case RTSP_PLAY:
                strcpy(szEvent, "PLAY");
                break;
            case RTSP_PAUSE:
                strcpy(szEvent, "PAUSE");
                break;
            case RTSP_TEARDOWN:
                strcpy(szEvent, "TEARDOWN");
                break;
            case RTSP_ALIVE:
                tNow = time(NULL);
                if (0 == strcmp(streamName, RTSP_MAIN_URI) && abs(tNow - tLastMainAlive) > 60)
                {
                    strcpy(szEvent, "ALIVE");
                    tLastMainAlive = tNow;
                }
                if (0 == strcmp(streamName, RTSP_SUB_URI) && abs(tNow - tLastSubAlive) > 60)
                {
                    strcpy(szEvent, "ALIVE");
                    tLastSubAlive = tNow;
                }
                break;
            case RTSP_TIMEOUT:
                strcpy(szEvent, "TIMEOUT");
                break;
        }

        if (szEvent[0] != 0)
        {
            sendRtspLog(clientAddr, szEvent, streamName);
        }
    }

protected:
    MyUsageEnvironment(TaskScheduler& taskScheduler)
        : BasicUsageEnvironment(taskScheduler)
    {
    }

    ~MyUsageEnvironment()
    {
    }

};

class VideoStreamFrameSource: public FramedSource
{
public:
    VideoStreamFrameSource(UsageEnvironment &env, int chn)
        : FramedSource(env)
    {
        fStarted = 0;
        fToken = NULL;
        fLastFrmPts = 0ll;
        fFrameRate = 20.0;
        char *pszStream = SFIFO_MAIN_STREAM;
        SFIFO_MEDIA_ATTR stMediaAttr;
        
        print_level(SV_INFO, "VideoStreamFrameSource::VideoStreamFrameSource, chn=%d\n", chn);
        if (chn != 0)
            pszStream = SFIFO_SUB_STREAM;

        if (SV_SUCCESS != SFIFO_ForReadOpen(pszStream, &fQueId, &fConsumerId))
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n");
        }

        if (SV_SUCCESS != SFIFO_GetMediaAttr(fQueId, &stMediaAttr))
        {
            print_level(SV_ERROR, "SFIFO_GetMediaAttr failed.\n");
        }
        else
        {
            if (chn == 0 && stMediaAttr.stMainStreamAttr.bValid)
            {
                fFrameRate = stMediaAttr.stMainStreamAttr.u32FrameRate;
            }

            if (chn == 1 && stMediaAttr.stSubStreamAttr.bValid)
            {
                fFrameRate = stMediaAttr.stSubStreamAttr.u32FrameRate;
            }
        }
        
        SFIFO_PrintShmQueInfo(fQueId);
    }

    ~VideoStreamFrameSource()
    {
        print_level(SV_INFO, "VideoStreamFrameSource::~VideoStreamFrameSource\n");
        if (fStarted)
        {
            envir().taskScheduler().unscheduleDelayedTask(fToken);
        }

        if (SV_SUCCESS != SFIFO_ForReadClose(fQueId, fConsumerId))
        {
            print_level(SV_ERROR, "SFIFO_ForReadClose failed.\n");
        }
    }

    double getFrameRate()
    {
        return fFrameRate;
    }

    virtual unsigned maxFrameSize() const
    {
        return 500000;
    }

protected:    
    virtual void doGetNextFrame()
    {
        //print_level(SV_INFO, "VideoStreamFrameSource::doGetNextFrame\n");
        fStarted = 1;
        if (!m_stRtspInfo.bRtspStreamPause)
        {
            fToken = envir().taskScheduler().scheduleDelayedTask(10*1000, getNextFrame, this);
        }
    }

private:
    static void getNextFrame(void *ptr)
    {
        //print_level(SV_INFO, "VideoStreamFrameSource::getNextFrame\n");
        VideoStreamFrameSource *This = (VideoStreamFrameSource*)ptr;
        This->doReadFromFIFO();
    }

    void doReadFromFIFO()
    {
        sint32 s32Ret = 0;
        SFIFO_MSHEAD *pstPacket = NULL;
        
        gettimeofday(&fPresentationTime, 0);
        
        s32Ret = SFIFO_GetPacket(fQueId, fConsumerId, &pstPacket);
        if (SV_SUCCESS != s32Ret)
        {
            fFrameSize = 0;
        }
        else if (2 == pstPacket->type)
        {
            fFrameSize = 0;
            SFIFO_ReleasePacket(fQueId, fConsumerId, pstPacket);
        }
        else
        {
            //print_level(SV_DEBUG, "[%#x] type: %d, msdsize: %d, fMaxSize: %d\n", pstPacket, pstPacket->type, pstPacket->msdsize, fMaxSize);
            fFrameSize = pstPacket->msdsize;
            if (fFrameSize > fMaxSize) {
    			fNumTruncatedBytes = fFrameSize - fMaxSize;
    			fFrameSize = fMaxSize;
    		}
    		else {
    			fNumTruncatedBytes = 0;
    		}
            memmove(fTo, pstPacket->data, pstPacket->msdsize);
            fPresentationTime.tv_sec = pstPacket->pts / 1000000ll;
            fPresentationTime.tv_usec = pstPacket->pts % 1000000ll;
            //print_level(SV_DEBUG, "Video: %d.%06d\n", fPresentationTime.tv_sec, fPresentationTime.tv_usec);
            if (fLastFrmPts != 0)
            {
                fDurationInMicroseconds = (pstPacket->pts - fLastFrmPts) / 1000ll;
            }
            fLastFrmPts = pstPacket->pts;
            s32Ret = SFIFO_ReleasePacket(fQueId, fConsumerId, pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
            }
        }
        //SFIFO_PrintShmQueInfo(fQueId);
        
        FramedSource::afterGetting(this);
        fStarted = 0;
    }

private:
    char fStarted;
    void *fToken;
    int fQueId;
    int fConsumerId;
    uint64 fLastFrmPts;
    double fFrameRate;
};

class VideoStreamServerMediaSubsession: public OnDemandServerMediaSubsession
{
public:
    static VideoStreamServerMediaSubsession *createNew(UsageEnvironment &env, FramedSource *source,int chnNum)
	{
		return new VideoStreamServerMediaSubsession(env, source, chnNum);
	}

protected:
    VideoStreamServerMediaSubsession(UsageEnvironment &env, FramedSource *source, int chnNum)
        : OnDemandServerMediaSubsession(env,True)
    {
        print_level(SV_INFO, "VideoStreamServerMediaSubsession::VideoStreamServerMediaSubsession\n");
        fAuxSDPLine = NULL;
        fChnId = chnNum;
    }

    ~VideoStreamServerMediaSubsession()
    {
        print_level(SV_INFO, "VideoStreamServerMediaSubsession::~VideoStreamServerMediaSubsession\n");
        if(NULL != fAuxSDPLine)
        {
            free(fAuxSDPLine);
            fAuxSDPLine = NULL;
        }
    }

	virtual FramedSource *createNewStreamSource (unsigned sid, unsigned &bitrate)
	{
		print_level(SV_INFO, "VideoStreamServerMediaSubsession::createNewStreamSource\n");
		bitrate = 500;
		VideoStreamFrameSource *videoSource = new VideoStreamFrameSource(envir(), fChnId);
		H264VideoStreamFramer* h264Framer = H264VideoStreamFramer::createNew(envir(), videoSource);
		h264Framer->setFrameRate(videoSource->getFrameRate());
		return h264Framer;
	}

	virtual RTPSink *createNewRTPSink(Groupsock *rtpsock, unsigned char type, FramedSource *source)
	{
		print_level(SV_INFO, "VideoStreamServerMediaSubsession::createNewRTPSink\n");
		return H264VideoRTPSink::createNew(envir(), rtpsock, type);
	}

	virtual const char *getAuxSDPLine (RTPSink *sink, FramedSource *source)
	{
		print_level(SV_INFO, "VideoStreamServerMediaSubsession::getAuxSDPLine\n");
        fDummyRTPSink = sink;
        fDummyRTPSink->startPlaying(*source, NULL, NULL);
        chkForAuxSDPLine(this);
        fDoneFlag = 0;
        envir().taskScheduler().doEventLoop(&fDoneFlag);
        fAuxSDPLine = strdup(fDummyRTPSink->auxSDPLine());
		fDummyRTPSink->stopPlaying();
		print_level(SV_INFO, "fAuxSDPLine: %s\n", fAuxSDPLine);

		return fAuxSDPLine;
	}

private:
    static void afterPlayingDummy (void *ptr)
	{
		print_level(SV_INFO, "VideoStreamServerMediaSubsession::afterPlayingDummy\n");
		VideoStreamServerMediaSubsession *This = (VideoStreamServerMediaSubsession *)ptr;
		This->fDoneFlag = 0xff;
	}	
	static void chkForAuxSDPLine (void *ptr)
	{
		//print_level(SV_INFO, "VideoStreamServerMediaSubsession::chkForAuxSDPLine\n");
		VideoStreamServerMediaSubsession *This = (VideoStreamServerMediaSubsession *)ptr;
		This->chkForAuxSDPLineSelf();
	}	

	void chkForAuxSDPLineSelf()
	{
	    if (fDummyRTPSink->auxSDPLine())
		{
		    fDoneFlag = 0xff;
		}
		else
		{
		    nextTask() = envir().taskScheduler().scheduleDelayedTask(100*1000, chkForAuxSDPLine, this);
		}
	}

private:
    char* fAuxSDPLine;
    char fDoneFlag; // used when setting up "fAuxSDPLine"
    RTPSink* fDummyRTPSink; // ditto
    int fChnId;
};

class AudioStreamFrameSource: public AudioInputDevice 
{
public:
    static AudioStreamFrameSource* createNew(UsageEnvironment& env, char const* fileName)
    {
        FILE* fid = OpenInputFile(env, fileName);
        if (fid == NULL) 
            return NULL;

        AudioStreamFrameSource* newSource = new AudioStreamFrameSource(env, fid);
        if (newSource != NULL && newSource->bitsPerSample() == 0) 
        {
            Medium::close(newSource);
            return NULL;
        }

        newSource->fFileSize = 0;
        return (AudioStreamFrameSource*)EndianSwap16::createNew(env, newSource);
    }

    unsigned numPCMBytes() const
    {
        if (fFileSize < fWAVHeaderSize) 
            return 0;
        return fFileSize - fWAVHeaderSize;
    }
    
    void setScaleFactor(int scale)
    {
        if (!fFidIsSeekable) 
            return;

        fScaleFactor = scale;       
    }
    
    void seekToPCMByte(unsigned byteNumber)
    {
        byteNumber += fWAVHeaderSize;
        if (byteNumber > fFileSize) 
            byteNumber = fFileSize;
    }
    
    void limitNumBytesToStream(unsigned numBytesToStream)
    {
        fNumBytesToStream = numBytesToStream;
        fLimitNumBytesToStream = fNumBytesToStream > 0;
    }

    unsigned char getAudioFormat()
    {
        return fAudioFormat;
    }

protected:
    AudioStreamFrameSource(UsageEnvironment& env, FILE* fid)
        : AudioInputDevice(env, 16, 1, transSampleRate2Num(m_stRtspInfo.enAudioSampleRate), 0), fFid(fid), fQueId(0), 
        fFidIsSeekable(False), fLastPlayTime(20000), fWAVHeaderSize(44), fHaveStartedReading(False), 
        fScaleFactor(1), fLimitNumBytesToStream(False), fNumBytesToStream(0),      
        fAudioFormat(transAudioFormat(m_stRtspInfo.enAudioEncType)), 
        fPlayTimePerSample(1000000.0 / transSampleRate2Num(m_stRtspInfo.enAudioSampleRate)), 
        fPreferredFrameSize(m_stRtspInfo.enAudioEncType == AUD_ENC_LPCM ? 640 : 320)     
    {
        fStarted = 0;
        fToken = NULL;
        if (SV_SUCCESS != SFIFO_ForReadOpen(SFIFO_AUD_STREAM, &fQueId, &fConsumerId))
        {
            print_level(SV_ERROR, "SFIFO_ForReadOpen stream:%s failed.\n");
        }
        SFIFO_PrintShmQueInfo(fQueId);
        print_level(SV_DEBUG, "AudioStreamFrameSource\n");
    }

    virtual ~AudioStreamFrameSource()
    {
		print_level(SV_INFO, "AudioStreamFrameSource::~AudioStreamFrameSource\n");
        if (SV_SUCCESS != SFIFO_ForReadClose(fQueId, fConsumerId))
        {
            print_level(SV_ERROR, "SFIFO_ForReadClose failed.\n");
        }
      
        if (fStarted)
        {
            envir().taskScheduler().unscheduleDelayedTask(fToken);
        }
    }

    static void getNextFrame(void *ptr)
    {
        //print_level(SV_INFO, "AudioStreamFrameSource::getNextFrame\n");
        AudioStreamFrameSource *This = (AudioStreamFrameSource*)ptr;
        This->doReadFromFIFO();
    }
  
    void doReadFromFIFO()
    {
        // Try to read as many bytes as will fit in the buffer provided (or "fPreferredFrameSize" if less)
        if (fLimitNumBytesToStream && fNumBytesToStream < fMaxSize) 
        {
            fMaxSize = fNumBytesToStream;
        }
        
        if (fPreferredFrameSize < fMaxSize) 
        {
            fMaxSize = fPreferredFrameSize;
        }
        
        unsigned bytesPerSample = (fNumChannels*fBitsPerSample)/8;
        if (bytesPerSample == 0) 
            bytesPerSample = 1; // because we can't read less than a byte at a time

        // For 'trick play', read one sample at a time; otherwise (normal case) read samples in bulk:
        unsigned bytesToRead = fScaleFactor == 1 ? fMaxSize - fMaxSize%bytesPerSample : bytesPerSample;
        unsigned numBytesRead;
        while (1) 
        { // loop for 'trick play' only        
            sint32 s32Ret = 0;
            SFIFO_MSHEAD *pstPacket = NULL;
            s32Ret = SFIFO_GetPacket(fQueId, fConsumerId, &pstPacket);
            if (SV_SUCCESS != s32Ret)
            {
                numBytesRead = 0;
            }
            else if (2 != pstPacket->type)
            {
                numBytesRead = 0;
                SFIFO_ReleasePacket(fQueId, fConsumerId, pstPacket);
            }
            else
            {
                numBytesRead = pstPacket->msdsize;
                memmove(fTo, pstPacket->data, pstPacket->msdsize);
                fPresentationTime.tv_sec = pstPacket->pts / 1000000ll;
                fPresentationTime.tv_usec = pstPacket->pts % 1000000ll;
                //print_level(SV_DEBUG, "Audio: %d.%06d\n", fPresentationTime.tv_sec, fPresentationTime.tv_usec);
                s32Ret = SFIFO_ReleasePacket(fQueId, fConsumerId, pstPacket);
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "SFIFO_ReleasePacket failed. [err=%#x]\n", s32Ret);
                }
            }

            if (numBytesRead != 0)
            {
                fFrameSize += numBytesRead;
                fTo += numBytesRead;
                fMaxSize -= numBytesRead;
                fNumBytesToStream -= numBytesRead;
                break; // from the loop (normal case)
            }
      
            //sleep_ms(10);
            break;
        }

        // Remember the play time of this data:
        fLastPlayTime = (unsigned)((fPlayTimePerSample*fFrameSize)/bytesPerSample);
        fDurationInMicroseconds = fLastPlayTime;
        FramedSource::afterGetting(this);
    }

private:
  // redefined virtual functions:
    virtual void doGetNextFrame()
    {
        if (fLimitNumBytesToStream && fNumBytesToStream == 0) {
        handleClosure();
        return;
        }

        fFrameSize = 0; // until it's set later
        fStarted = 1;
        if (!m_stRtspInfo.bRtspStreamPause)
        {
            fToken = envir().taskScheduler().scheduleDelayedTask(5*1000, getNextFrame, this);
        }
    }

    virtual void doStopGettingFrames()
    {
        if (fStarted)
        {
            envir().taskScheduler().unscheduleDelayedTask(fToken);
        }
    }

    virtual Boolean setInputPort(int portIndex)
    {
        return True;
    }

    virtual double getAverageLevel() const
    {
        return 0.0;//##### fix this later
    }
  

protected:
    unsigned fPreferredFrameSize;

private:
    FILE* fFid;
    char fStarted;
    void *fToken;
    int fQueId;
    int fConsumerId;
    double fPlayTimePerSample; // useconds
    Boolean fFidIsSeekable;
    unsigned fLastPlayTime; // useconds
    Boolean fHaveStartedReading;
    unsigned fWAVHeaderSize;
    unsigned fFileSize;
    int fScaleFactor;
    Boolean fLimitNumBytesToStream;
    unsigned fNumBytesToStream; // used iff "fLimitNumBytesToStream" is True
    unsigned char fAudioFormat;
};

class AudioStreamServerMediaSubsession: public OnDemandServerMediaSubsession
{
public:
    static AudioStreamServerMediaSubsession *createNew(UsageEnvironment &env, FramedSource *source,int chnNum)
	{
		return new AudioStreamServerMediaSubsession(env, source, chnNum);
	}

protected:
    AudioStreamServerMediaSubsession(UsageEnvironment &env, FramedSource *source, int chnNum)
        : OnDemandServerMediaSubsession(env,True)
    {
        fAuxSDPLine = NULL;
        fChnId = chnNum;
    }

    ~AudioStreamServerMediaSubsession()
    {
        if(NULL != fAuxSDPLine)
        {
            free(fAuxSDPLine);
            fAuxSDPLine = NULL;
        }
    }

	virtual FramedSource *createNewStreamSource (unsigned sid, unsigned &bitrate)
	{
		print_level(SV_INFO, "AudioStreamServerMediaSubsession::createNewStreamSource\n");
		bitrate = 500;
		return AudioStreamFrameSource::createNew(envir(), "/dev/null");
	}

	virtual RTPSink *createNewRTPSink(Groupsock *rtpsock, unsigned char type, FramedSource *source)
	{
	    sint32 s32Ret = 0;
	    uint32 u32SampleRate = 0;
	    SFIFO_MEDIA_ATTR stMediaAttr;
	    char *pszFmtName = NULL;
	    
		print_level(SV_INFO, "AudioStreamServerMediaSubsession::createNewRTPSink\n");
	    switch (m_stRtspInfo.enAudioEncType)
	    {
	        case AUDIO_ENCODE_G711A:
	            pszFmtName = "PCMA";
	            break;
	        case AUDIO_ENCODE_G711U:
	            pszFmtName = "PCMU";
	            break;
	        case AUDIO_ENCODE_ADPCM:
	            pszFmtName = "DVI4";
	            break;
	        case AUDIO_ENCODE_LPCM:
	            pszFmtName = "L16";
	            break;
	    }

	    switch (m_stRtspInfo.enAudioSampleRate)
	    {
	        case AUD_SR_8K:
	            u32SampleRate = 8000;
	            break;
	        case AUD_SR_16K:
	            u32SampleRate = 16000;
	            break;
	        case AUD_SR_32K:
	            u32SampleRate = 32000;
	            break;
	    }
		
		return SimpleRTPSink::createNew(envir(), rtpsock, 96, u32SampleRate, "audio", pszFmtName, 1);
	}

	virtual const char *getAuxSDPLine (RTPSink *sink, FramedSource *source)
	{
		print_level(SV_INFO, "AudioStreamServerMediaSubsession::getAuxSDPLine\n");
		return fAuxSDPLine;
	}

private:
    static void afterPlayingDummy (void *ptr)
	{
		print_level(SV_INFO, "VideoStreamServerMediaSubsession::afterPlayingDummy\n");
		AudioStreamServerMediaSubsession *This = (AudioStreamServerMediaSubsession *)ptr;
		This->fDoneFlag = 0xff;
	}	
	static void chkForAuxSDPLine (void *ptr)
	{
		//print_level(SV_INFO, "VideoStreamServerMediaSubsession::chkForAuxSDPLine\n");
		AudioStreamServerMediaSubsession *This = (AudioStreamServerMediaSubsession *)ptr;
		This->chkForAuxSDPLineSelf();
	}	

	void chkForAuxSDPLineSelf()
	{
	    if (fDummyRTPSink->auxSDPLine())
		{
		    fDoneFlag = 0xff;
		}
		else
		{
		    nextTask() = envir().taskScheduler().scheduleDelayedTask(100*1000, chkForAuxSDPLine, this);
		}
	}

private:
    char* fAuxSDPLine;
    char fDoneFlag; // used when setting up "fAuxSDPLine"
    RTPSink* fDummyRTPSink; // ditto
    int fChnId;
};

void * rtsp_Service_Body(void *pvArg)
{
    sint32 s32Ret = 0;
    RTSP_COM_INFO_S *pstRtspInfo = (RTSP_COM_INFO_S *)pvArg;

    s32Ret = prctl(PR_SET_NAME, "rtsp_body");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    print_level(SV_DEBUG, "onvif_Service_Body...\n");
    while (pstRtspInfo->bRunning)
    {
        pstRtspInfo->pcsEnv->taskScheduler().doEventLoop(&pstRtspInfo->eventLoopWatchVariable);
    }
    
    return NULL;
}

void * rtsp_Status_Body(void *pvArg)
{
    sint32 s32Ret = 0, i;
    sint32 s32Fd = -1;
    sint32 s32ClientNum = 0;
    CONNECTION_INFO_S astConnectionInfo[32];
    RTSP_COM_INFO_S *pstRtspInfo = (RTSP_COM_INFO_S *)pvArg;
    cJSON *pstJson = NULL, *pstClientList = NULL, *pstClientInfo = NULL;
    char szClientAddr[64];
    char szBuf[5*1024];

    s32Ret = prctl(PR_SET_NAME, "rtsp_status");
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    pstJson = cJSON_CreateObject();
    if (NULL == pstJson)
    {
        print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
        return NULL;
    }
    
    while (pstRtspInfo->bRunning)
    {
        //print_level(SV_DEBUG, "rtsp_Status_Body running...\n");
        sleep_ms(5000);

        if (!pstRtspInfo->bRtspStreamPause)
        {
            cJSON_DeleteItemFromObject(pstJson, "clientList");
            pstClientList = cJSON_CreateArray();
            if (NULL == pstClientList)
            {
                print_level(SV_ERROR, "cJSON_CreateArray failed.\n");
                return NULL;
            }
            
            cJSON_AddItemToObject(pstJson, "clientList", pstClientList);
            memset(astConnectionInfo, 0, sizeof(astConnectionInfo));
            pstRtspInfo->pcsRtspLiveServer->lookupClientConnectionsList(&s32ClientNum, astConnectionInfo);
            for (i = 0; i < s32ClientNum; i++)
            {
                //print_level(SV_DEBUG, "%s:%d - %s\n", AddressString(astConnectionInfo[i].clientAddr).val(), \
                            AddressString(astConnectionInfo[i].clientAddr).port(), astConnectionInfo[i].streamName);
                pstClientInfo = cJSON_CreateObject();
                if (NULL == pstClientInfo)
                {
                    print_level(SV_ERROR, "cJSON_CreateObject failed.\n");
                    continue;
                }
                sprintf(szClientAddr, "%s:%d", AddressString(astConnectionInfo[i].clientAddr).val(), AddressString(astConnectionInfo[i].clientAddr).port());
                cJSON_AddItemToObject(pstClientInfo, "clientAddr", cJSON_CreateString(szClientAddr));
                cJSON_AddItemToObject(pstClientInfo, "streamName", cJSON_CreateString(astConnectionInfo[i].streamName));
                cJSON_AddItemToArray(pstClientList, pstClientInfo);
            }

            memset(szBuf, 0, 5*1024);
            cJSON_PrintPreallocated(pstJson, szBuf, 5*1024, 0);
            s32Fd = open("/var/info/rtsp-tmp", O_CREAT|O_RDWR, S_IRUSR|S_IWUSR);
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "open file: /var/info/rtsp-tmp failed. [err:%s]\n", strerror(errno));
                continue;
            }

            s32Ret = write(s32Fd, szBuf, strlen(szBuf));
            if (s32Fd < 0)
            {
                print_level(SV_ERROR, "write file: /var/info/rtsp-tmp failed. [err:%s]\n", strerror(errno));
                close(s32Fd);
                continue;
            }
            
            close(s32Fd);
            rename("/var/info/rtsp-tmp", "/var/info/rtsp");
        }
    }

    cJSON_Delete(pstJson);
    
    return NULL;
}

sint32 callbackMediaStreamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    MSG_VIDEO_CFG *pstVideoCfg = (MSG_VIDEO_CFG *)pstMsgPkt->pu8Data;
    
    print_level(SV_DEBUG, "Stream Change.\n");
    print_level(SV_INFO, "mainStream: (%dx%d) %dpfs %dkbps rc%d %di, subStream: (%dx%d) %dpfs %dkbps rc%d %di, jpeg:(%dx%d), audio: %d\n", \
                    pstVideoCfg->astChnParam[0].u32MainWidth, pstVideoCfg->astChnParam[0].u32MainHeight, pstVideoCfg->astChnParam[0].u32MainFramerate, pstVideoCfg->astChnParam[0].u32MainBitrate, pstVideoCfg->astChnParam[0].enMainRcMode, pstVideoCfg->astChnParam[0].u32MainIfrmInterval, \
                    pstVideoCfg->astChnParam[0].u32SubWidth, pstVideoCfg->astChnParam[0].u32SubHeight, pstVideoCfg->astChnParam[0].u32SubFramerate, pstVideoCfg->astChnParam[0].u32SubBitrate, pstVideoCfg->astChnParam[0].enSubRcMode, pstVideoCfg->astChnParam[0].u32SubIfrmInterval, \
                    pstVideoCfg->astChnParam[0].u32JpegWidth, pstVideoCfg->astChnParam[0].u32JpegHeight, pstVideoCfg->astChnParam[0].bAudioEnable);

    if (m_stRtspInfo.bAudioEnable != pstVideoCfg->astChnParam[0].bAudioEnable 
        || m_stRtspInfo.enAudioEncType != pstVideoCfg->enAudioEncType
        || m_stRtspInfo.enAudioSampleRate != pstVideoCfg->enAudioSampleRate
        || (m_stRtspInfo.bAudioEnable && m_stRtspInfo.u32MainFramerate != pstVideoCfg->astChnParam[0].u32MainFramerate)
        || (m_stRtspInfo.u32SubFramerate != pstVideoCfg->astChnParam[0].u32SubFramerate))
    {
        VideoStreamFrameSource *videoSource = NULL;
        AudioStreamFrameSource *audioSource = NULL;

        m_stRtspInfo.bRtspStreamPause = SV_TRUE;
        sleep_ms(200);
        m_stRtspInfo.pcsRtspLiveServer->deleteServerMediaSession(RTSP_MAIN_URI);
        m_stRtspInfo.pcsRtspLiveServer->deleteServerMediaSession(RTSP_SUB_URI);

        sleep_ms(200);
        m_stRtspInfo.bRtspStreamPause = SV_FALSE;
        ServerMediaSession *sms1 = ServerMediaSession::createNew(*m_stRtspInfo.pcsEnv, RTSP_MAIN_URI, 0, "Session from mainstream");
        if (NULL == sms1)
        {
            print_level(SV_ERROR, "ServerMediaSession::createNew failed.\n");
            return MSG_DEFAULT_FAIL;
        }
        if (!sms1->addSubsession(VideoStreamServerMediaSubsession::createNew(*m_stRtspInfo.pcsEnv, videoSource, 0)))
        {
            print_level(SV_ERROR, "sms1->addSubsession failed.\n");
            return MSG_DEFAULT_FAIL;
        }
        if (pstVideoCfg->astChnParam[0].bAudioEnable)
        {
            if (!sms1->addSubsession(AudioStreamServerMediaSubsession::createNew(*m_stRtspInfo.pcsEnv, audioSource, 0)))
            {
                print_level(SV_ERROR, "sms1->addSubsession failed.\n");
                return MSG_DEFAULT_FAIL;
            }
        }
        m_stRtspInfo.pcsRtspLiveServer->addServerMediaSession(sms1);
        ServerMediaSession *sms2 = ServerMediaSession::createNew(*m_stRtspInfo.pcsEnv, RTSP_SUB_URI, 0, "Session from substream");
        if (NULL == sms2)
        {
            print_level(SV_ERROR, "ServerMediaSession::createNew failed.\n");
            return MSG_DEFAULT_FAIL;
        }
        if (!sms2->addSubsession(VideoStreamServerMediaSubsession::createNew(*m_stRtspInfo.pcsEnv, videoSource, 1)))
        {
            print_level(SV_ERROR, "sms2->addSubsession failed.\n");
            return MSG_DEFAULT_FAIL;
        }
        m_stRtspInfo.pcsRtspLiveServer->addServerMediaSession(sms2);
        m_stRtspInfo.bAudioEnable = pstVideoCfg->astChnParam[0].bAudioEnable;
        m_stRtspInfo.enAudioEncType = pstVideoCfg->enAudioEncType;
        m_stRtspInfo.enAudioSampleRate = pstVideoCfg->enAudioSampleRate;
    }
    m_stRtspInfo.u32MainFramerate = pstVideoCfg->astChnParam[0].u32MainFramerate;
    m_stRtspInfo.u32SubFramerate = pstVideoCfg->astChnParam[0].u32SubFramerate;

    return SV_SUCCESS;
}

sint32 callbackStopMediaStream(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    print_level(SV_INFO, "recive: OP_EVENT_DOWNLOAD_FIRMWARE\n");
    m_stRtspInfo.bRtspStreamPause = SV_TRUE;
    sleep_ms(200);
    m_stRtspInfo.pcsRtspLiveServer->deleteServerMediaSession(RTSP_MAIN_URI);
    m_stRtspInfo.pcsRtspLiveServer->deleteServerMediaSession(RTSP_SUB_URI);    
    sleep_ms(200);
    
    return SV_SUCCESS;
}

static sint32 callbackUsrParamChange(MSG_PACKET_S *pstMsgPkt, MSG_PACKET_S *pstRetPkt)
{
    MSG_USR_CFG *pstUsrCfg = (MSG_USR_CFG *)pstMsgPkt->pu8Data;
    print_level(SV_INFO, "recive: OP_EVENT_USR_CHANGE\n");
    rtspLiveServer->setAuthenticationDatabase(NULL);
    if (strlen(pstUsrCfg->szUserPassword[0][2]))
    {
    	UserAuthenticationDatabase * newDB = new UserAuthenticationDatabase("LIVE555 Media",False);
		newDB->addUserRecord("admin", pstUsrCfg->szUserPassword[0][2]);
		rtspLiveServer->setAuthenticationDatabase(newDB);
    }
    return SV_SUCCESS;
}


sint32 RTSP_SVR_Init(RTSP_CFG_PARAM_S *pstInitParam)
{
    sint32 s32Ret = 0;

    if (NULL == pstInitParam)
    {
        return ERR_NULL_PTR;
    }

    memset(&m_stRtspInfo, 0x0, sizeof(RTSP_COM_INFO_S));
    TaskScheduler *scheduler = BasicTaskScheduler::createNew();
    UsageEnvironment *env = MyUsageEnvironment::createNew(*scheduler);
    rtspLiveServer = RTSPServer::createNew(*env, pstInitParam->u32ServicePort, NULL, 65);
    if (NULL == rtspLiveServer)
    {
        print_level(SV_ERROR, "create RTSPServer failed.\n");
    }
    if (strlen(pstInitParam->szAdminPassword))
    {
    	UserAuthenticationDatabase * newDB = new UserAuthenticationDatabase("LIVE555 Media",False);
		newDB->addUserRecord("admin", pstInitParam->szAdminPassword);
		rtspLiveServer->setAuthenticationDatabase(newDB);
	}
    VideoStreamFrameSource *videoSource = NULL;
    AudioStreamFrameSource *audioSource = NULL;
    ServerMediaSession *sms1 = ServerMediaSession::createNew(*env, RTSP_MAIN_URI, 0, "Session from mainstream");
    if (NULL == sms1)
    {
        print_level(SV_ERROR, "ServerMediaSession::createNew failed.\n");
        return ERR_SYS_NOTREADY;
    }
    if (!sms1->addSubsession(VideoStreamServerMediaSubsession::createNew(*env, videoSource, 0)))
    {
        print_level(SV_ERROR, "sms1->addSubsession failed.\n");
        return ERR_SYS_NOTREADY;
    }
    if (pstInitParam->bAudioEnable)
    {
        if (!sms1->addSubsession(AudioStreamServerMediaSubsession::createNew(*env, audioSource, 0)))
        {
            print_level(SV_ERROR, "sms1->addSubsession failed.\n");
            return ERR_SYS_NOTREADY;
        }
    }
    rtspLiveServer->addServerMediaSession(sms1);
    print_level(SV_INFO, "url: %s\n", rtspLiveServer->rtspURL(sms1));
    ServerMediaSession *sms2 = ServerMediaSession::createNew(*env, RTSP_SUB_URI, 0, "Session from substream");
    if (NULL == sms2)
    {
        print_level(SV_ERROR, "ServerMediaSession::createNew failed.\n");
        return ERR_SYS_NOTREADY;
    }
    if (!sms2->addSubsession(VideoStreamServerMediaSubsession::createNew(*env, videoSource, 1)))
    {
        print_level(SV_ERROR, "sms2->addSubsession failed.\n");
        return ERR_SYS_NOTREADY;
    }
    rtspLiveServer->addServerMediaSession(sms2);
    print_level(SV_INFO, "url: %s\n", rtspLiveServer->rtspURL(sms2));
    m_stRtspInfo.u32MainFramerate = pstInitParam->u32MainFramerate;
    m_stRtspInfo.u32SubFramerate = pstInitParam->u32SubFramerate;
    m_stRtspInfo.bAudioEnable = pstInitParam->bAudioEnable;
    m_stRtspInfo.enAudioEncType = pstInitParam->enAudioEncType;
    m_stRtspInfo.enAudioSampleRate = pstInitParam->enAudioSampleRate;
    m_stRtspInfo.pcsScheduler = scheduler;
    m_stRtspInfo.pcsEnv = env;
    m_stRtspInfo.pcsRtspLiveServer = rtspLiveServer;

    return SV_SUCCESS;
}

sint32 RTSP_SVR_Fini()
{
    sint32 s32Ret = 0;

    //delete m_stRtspInfo.pcsScheduler;
    //delete m_stRtspInfo.pcsEnv;
    //delete m_stRtspInfo.pcsRtspLiveServer;
    
    return SV_SUCCESS;
}


sint32 RTSP_SVR_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread1 = 0, thread2 = 0;

    s32Ret = MSG_ReciverStart(EP_RTSPSERVER);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStart failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_USR_CHANGE, callbackUsrParamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_MEDIA_CHANGE, callbackMediaStreamChange);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = Msg_registerOpCallback(EP_RTSPSERVER, OP_EVENT_DOWNLOAD_FIRMWARE, callbackStopMediaStream);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_registerOpCallback failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    m_stRtspInfo.bRunning = SV_TRUE;
    s32Ret = pthread_create(&thread1, NULL, rtsp_Service_Body, &m_stRtspInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    s32Ret = pthread_create(&thread2, NULL, rtsp_Status_Body, &m_stRtspInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
        if (EAGAIN == s32Ret)
        {
            return ERR_SYS_NOTREADY;
        }
        else
        {
            return SV_FAILURE;
        }
    }

    m_stRtspInfo.u32TidService = thread1;
    m_stRtspInfo.u32TidStatus = thread2;
    
    return SV_SUCCESS;
}


sint32 RTSP_SVR_Stop()
{
    sint32 s32Ret = 0;
    pthread_t thread1 = m_stRtspInfo.u32TidService;
    pthread_t thread2 = m_stRtspInfo.u32TidStatus;
    void *pvRetval = NULL;

    m_stRtspInfo.bRunning = SV_FALSE;
    m_stRtspInfo.eventLoopWatchVariable = 1;
    s32Ret = pthread_join(thread1, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = pthread_join(thread2, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    s32Ret = MSG_ReciverStop(EP_RTSPSERVER);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MSG_ReciverStop failed. [err=%#x]\n", s32Ret);
    }
    
    return SV_SUCCESS;
}


sint32 RTSP_SVR_ConfigSet(RTSP_CFG_PARAM_S *pstConnCfg)
{

    return SV_SUCCESS;
}


