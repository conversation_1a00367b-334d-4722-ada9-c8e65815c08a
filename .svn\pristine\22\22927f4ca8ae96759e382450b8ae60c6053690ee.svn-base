/******************************************************************************
Copyright (C) 2018-2020 广州敏视数码科技有限公司版权所有.

文件名: jsonHandle.h

作者: 许家铭    版本: v1.0.0(初始版本号)    日期: 2018-01-11

文件功能描述: 定义json消息处理功能函数

版本: v1.0.0(最新版本号)

历史记录: // 历史修改记录
  <作者>     <时间>        <版本>    <说明>

*******************************************************************************/
#ifndef _JSON_HANDLE_H
#define _JSON_HANDLE_H

#include "common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/******************************************************************************
 * 函数功能: 认证JSON消息授权密码
 * 输入参数: pszInJson --- json字符串
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 授权成功
             SV_FAILURE - 授权失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_Authentication(char *pszInJson);

/******************************************************************************
 * 函数功能: 获取配置参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetConfig(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置配置参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetConfig(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 重新加载参数
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_ReloadConfig();

/******************************************************************************
 * 函数功能: 获取配置参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetTestWifiParam(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置配置参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetTestWifiParam(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取测试GPS配置参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetTestGpsParam(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置测试GPS配置参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetTestGpsParam(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置展会专用版
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetExhibitionVesion(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置回播
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_CtrlPlayback(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置OSD开关
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetGuiMask(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置专用版参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetSpecialVesion(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取专用版参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetSpecialVesion(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: PING网络地址
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_PingNetAddr(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置图像参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
 extern sint32 JSON_HDL_GetImageParam(char *pszInJson, char *pszOutJson);


/******************************************************************************
 * 函数功能: 获取设备时间
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetDevTime(char *pszInJson, char *pszOutJson);


/******************************************************************************
 * 函数功能: 设置图像参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetImageParam(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 复位出厂设置
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_RestoreFactory(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 重启系统
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_RebootSystem(char *pszInJson, char *pszOutJson);


extern sint32 JSON_HDL_ResetPwd(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 进入工厂测试模式
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_EnterFactory(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 退出工厂测试模式
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_QuitFactory(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 查询系统状态信息
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_QueryStatus(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 查询报警信息
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_AlarmLog(char *pszInJson,char *pszOutJson);

/******************************************************************************
 * 函数功能: 修改状态信息
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_ChangeState(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 修改OSD显示位置
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_ChangeOsdPos(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 修改密码
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_ChangePwd(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 修改客户的序列号（前半）
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_ChangeSpsNum(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 修改客户的序列号（后半）
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_ChangeArtNum(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 修改系统时间
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_LockInTime(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 查询系统日志信息
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_QueryLog(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 查询录像信息
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_QueryRecord(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 外部产生DMS报警
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_UserAlarm(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 升级准备处理
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_UpdateReady(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 扫码处理
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_ScanCode(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 删除扫码处理
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_DeleteCode(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 打开DHCP动态申请IP地址(不保存配置)
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_OpenDhcp(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 打开telnet服务器
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_OpenTelnet(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 格式化SD卡
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_FormatSd(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 修复文件系统
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_RepairFileSys(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 修复分区
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_RepairPartition(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 为测试IPC做准备工作
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_TestIpc(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置虚拟DDAW报警
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_DDAWAlarm(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 算法标定处理
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_Calibration(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 算法二维码标定
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_QRCodeCalibration(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 算法标定处理
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_FaceRecognition(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取人脸用户列表
 * 输入参数: pszPath --- 目标文件夹
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_LoadFaceUserList(char *pszPath, char *pszOutJson);

/******************************************************************************
 * 函数功能: 插入用户图片
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_InsertUserPic(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 处理CMS对DMS的操作
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_CmsOperate(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 处理CAN传输过来的参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_CanConfig(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 测试接口
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_Test(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 保存接收到的坐标信息
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_DumpTrackPoint(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置ptz参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetPtz(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取ptz参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetPtz(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置AF标定
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetAFCal(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: AF标定测试
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_AFTest(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 执行雨刷动作
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetStepmotor(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取雨刷配置
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetStepmotor(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置变焦参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetZoom(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取变焦参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetZoom(char *pszInJson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取用户自定义的算法参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetCustomAlg(char *pszInson,char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置用户自定义的算法参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetCustomAlg(char *pszInson,char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取算法插件列表配置
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetPlugList(char *pszInson,char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置算法插件列表配置
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetPlugList(char *pszInson,char *pszOutJson);

/******************************************************************************
 * 函数功能: 获取算法插件配置参数
 * 输入参数: pszUrlParam --- GET请求URL的参数部分
             pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_GetPlugConfig(char *pszUrlParam, char *pszInson, char *pszOutJson);

/******************************************************************************
 * 函数功能: 设置算法插件配置参数
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_SetPlugConfig(char *pszInson,char *pszOutJson);

/******************************************************************************
 * 函数功能: 删除算法插件
 * 输入参数: pszInJson --- json字符串
 * 输出参数: pszOutJson --- json字符串
 * 返回值  : SV_SUCCESS - 成功
             其它 - MSG错误码
 * 注意    : 无
 *****************************************************************************/
extern sint32 JSON_HDL_DeletePlug(char *pszInson,char *pszOutJson);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif // _JSON_HANDLE_H

