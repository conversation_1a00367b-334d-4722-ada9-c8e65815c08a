/******************************************************************************
Copyright (C) 2023-2025 广州敏视数码科技有限公司版权所有.
file：       ztrk.cpp
author:     lyn
version:    1.0.0
date:       2023-09-27
function:   zoom track algorithm implementation
notice:     none
*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/poll.h>
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <ctype.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <pthread.h>
#include <math.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <dirent.h>

#include "print.h"
#include "alg.h"
#include "cJSON.h"
#include "utils.h"
#include "board.h"
#include "utils.h"
#include "msg.h"
#include "pd.h"
#include "pds_alg.h"
#include "ztrk.h"

/******************************************************************************
 * 函数功能: 算法前处理
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS -- 成功
             SV_FAILUER -- 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::PreProcess()
{
    sint32 s32Ret = SV_SUCCESS;
    pSFCP->prepare((char *) pbuf,  mediaFd, width,height, stTrkInfo.stFirstBox);
    return s32Ret;
}

/******************************************************************************
 * 函数功能: 算法处理过程
 * 输入参数: 无
 * 输出参数: result -- 算法结果
 * 返回值  : SV_SUCCESS -- 成功
             SV_FAILUER -- 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::DoProcess(ALG_RES_S *result)
{
    sint32                  s32Ret      = SV_SUCCESS;
    static SFCP_BOX         stNextBox;
    static SFCP_BOX         stLastBox;
    ALG_RES_S               stResultTmp = {0};
    static SV_BOOL          bOnce = SV_FALSE;

    
    pSFCP->update(pbuf, mediaFd, stNextBox, stResultTmp.astResult[0].score, scope);

    /* 目标分数过低,认为跟丢, 逻辑处理 */
    //print_level(SV_DEBUG, "score %f box(%d %d) wh(%d %d)\n",\
    //stResultTmp.astResult[0].score,stNextBox.cx, stNextBox.cy,stNextBox.w,stNextBox.h);
    if (stResultTmp.astResult[0].score < float(threshold))
    {
        memcpy(&stNextBox, &stLastBox, sizeof(SFCP_BOX));
        stResultTmp.u32Num = 0;
        stTrkStat.bLost = SV_TRUE;
        if (!bOnce)
        {
            stTrkStat.bClear = SV_TRUE;
            bOnce = SV_TRUE;
        }
        return SV_FAILURE;
    }

    if (bOnce)
    {
        bOnce = SV_FALSE;
    }
    
    if (stTrkStat.bLost)
    {
        stTrkStat.bLost = SV_FALSE;
    }
    /* 返回结果 */
    memcpy(&stLastBox, &stNextBox, sizeof(SFCP_BOX));
    stResultTmp.u32Num = 1;
    stResultTmp.astResult[0].box.x1 = (stNextBox.cx - stNextBox.w / 2) / (width * 1.0);
    stResultTmp.astResult[0].box.y1 = (stNextBox.cy - stNextBox.h / 2) / (height * 1.0);
    stResultTmp.astResult[0].box.x2 = (stNextBox.cx + stNextBox.w / 2) / (width * 1.0);
    stResultTmp.astResult[0].box.y2 = (stNextBox.cy + stNextBox.h / 2) / (height * 1.0);
    memcpy(result, &stResultTmp, sizeof(ALG_RES_S));

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 算法后处理，主要做osd和报警输出等功能实现
 * 输入参数: result -- 算法结果
 * 输出参数: 无
 * 返回值  : SV_SUCCESS -- 成功
             SV_FAILUER -- 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::PostProcess(ALG_RES_S *result)
{
    sint32 s32Ret = 0;
    MEDIA_GUI_DRAW_S        stMediaGuiDraw  = {0};
    MEDIA_GUI_PERSON_S      stGuiRect       = {0};
    MEDIA_GUI_NULL_S        stGuiNull;
    MSG_PACKET_S            stMsgPkt        = {0};
    uint16                  u16mask;

    /* 清空原来的画板 */
    u16mask = MEDIA_GUI_GET_MASK(device, MEDIA_GUI_PLANE_EXTERN1, MEDIA_GUI_OP_CLEAR);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiNull);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }

    if (stTrkStat.bClear)
    {
        memset(&stMsgPkt, 0, sizeof(stMsgPkt));
        stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
        stMsgPkt.u32Size = sizeof(stMediaGuiDraw);
        s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
        }
        stTrkStat.bClear = SV_FALSE;
        return SV_SUCCESS;
    }

    if (stTrkStat.bLost)
    {
        return SV_SUCCESS;
    }
    /* 结果处理 */
    stGuiRect.astPersonsRect[0].stick = 3;
    stGuiRect.astPersonsRect[0].x1 = result->astResult[0].box.x1;
    stGuiRect.astPersonsRect[0].y1 = result->astResult[0].box.y1;
    stGuiRect.astPersonsRect[0].x2 = result->astResult[0].box.x2;
    stGuiRect.astPersonsRect[0].y2 =  result->astResult[0].box.y2;
    stGuiRect.astPersonsRect[0].fontscale = 2;
    stGuiRect.au32Score[0] = 0;
    stGuiRect.as32DistanceX[0] = 0;
    stGuiRect.as32DistanceY[0] = -1;
    stGuiRect.astPersonsRect[0].color = GUI_COLOR_L_YELLOW;
    stGuiRect.classes[0] = PD_CLS_BLANK;
    
    stGuiRect.u32PersonNum = result->u32Num;
/*TODO:2023-10-27跟踪算法osd框默认开启，现阶段做成可配置的意义不大*/
#if 0
    /* 如果未使能行人矩形框则只发送左上角一个点 */
    if (stGuiRect.u32PersonNum != 0 && !bOsd)
    {
        stGuiRect.u32PersonNum = 1;
        stGuiRect.astPersonsRect[0].x1 = 0;
        stGuiRect.astPersonsRect[0].y1 = 0;
        stGuiRect.astPersonsRect[0].x2 = 0.0005;
        stGuiRect.astPersonsRect[0].y2 = 0.0009;
        stGuiRect.astPersonsRect[0].color = GUI_COLOR_L_BLUE;
        stGuiRect.classes[0] = PD_CLS_BLANK;//避免绘制步行小人图标
    }
#endif
    /* 添加行人矩形绘制操作 */
    u16mask = MEDIA_GUI_GET_MASK(device, MEDIA_GUI_PLANE_EXTERN1, MEDIA_GUI_OP_PERSON_RECT);
    s32Ret = MEDIA_GUI_INSERT(stMediaGuiDraw, u16mask, stGuiRect);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "MEDIA_GUI_INSERT failed. [err=%#x]\n", s32Ret);
    }
    
    memset(&stMsgPkt, 0, sizeof(stMsgPkt));
    stMsgPkt.pu8Data = (uint8*)&stMediaGuiDraw;
    stMsgPkt.u32Size = MEDIA_GUI_SIZE(stMediaGuiDraw);
    
    s32Ret = Msg_submitEvent(EP_CONTROL, OP_EVENT_MEDIA_GUI, &stMsgPkt);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "Msg_submitEvent failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 算法配置参数
 * 输入参数: data -- 配置的数据
 * 输出参数: 无
 * 返回值  : SV_SUCCESS -- 成功
             SV_FAILUER -- 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::UpdatePos(void * data)
{
    if (NULL == data)
    {
        return ERR_NULL_PTR;
    }
    memcpy(&stTrkInfo, data, sizeof(TRK_INFO_S));
    stTrkInfo.bPosUpdate = SV_TRUE;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 算法配置参数
 * 输入参数: data -- 配置的数据
 * 输出参数: 无
 * 返回值  : SV_SUCCESS -- 成功
             SV_FAILUER -- 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::SetConifg(void * data)
{
    CFG_ALG_PARAM *pstParam = (CFG_ALG_PARAM *)data;
    if (NULL == data)
    {
        return ERR_NULL_PTR;
    }
    enable              = pstParam->stAlgCh1.bAlgEnable;
    s32Sensitivity      = pstParam->stAlgCh1.stZoomParam.s32Sensitivity;
    s32OsdFontSize      = pstParam->stAlgCh1.stZoomParam.s32OsdFontSize;
    bAlarmInEnable      = pstParam->stAlgCh1.stZoomParam.bAlarmInEnable;
    bTestMode           = pstParam->stAlgCh1.stZoomParam.bTestMode;
    threshold           = min(1.0, max(0.0, pstParam->stAlgCh1.stZoomParam.dThreshold));
    bOsd                = pstParam->stAlgCh1.stZoomParam.bOsd;
    s32AlarmOutInterval = pstParam->stAlgCh1.stZoomParam.s32AlarmOutInterval;
    abAlarmOutEnable[0] = pstParam->stAlgCh1.stZoomParam.abAlarmOutEnable[0];
    enAlarmInLv         = pstParam->stAlgCh1.stZoomParam.enAlarmInLv;
    enAlarmOutLv        = pstParam->stAlgCh1.stZoomParam.enAlarmOutLv;
    scope               = aScopePreset[2 - min(2, max(0, s32Sensitivity))];
    return SV_SUCCESS;

}


/******************************************************************************
 * 函数功能: 跟踪算法线程
 * 输入参数: pvArg --- 初始化配置参数
 * 输出参数: 无
 * 返回值  : NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
void* zoom_trk_Body(void *pvArg)
{
    sint32 s32Ret = 0, i;
    ZoomTrk *pCZoomTrk = (ZoomTrk *)pvArg;
    TRK_INFO_S              *pstTrkInfo     = &pCZoomTrk->stTrkInfo;

    /* 跟踪算法相关变量 */
    SFCP_BOX                stFirstBox;
    SFCP_BOX                stNextBox;
    SFCP_BOX                stLastBox;
    float                   score;
    ALG_RES_S               stAlgResult     = {0};

    memset(pstTrkInfo, 0, sizeof(TRK_INFO_S));
    while(pCZoomTrk->bRunning)
    {
        if (!pCZoomTrk->enable)
        {
            sleep_ms(100);
            continue;
        }

        s32Ret = pCZoomTrk->Lock();
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "ALG_Calculate_Lock failed. [err=%d]\n", s32Ret);
            sleep_ms(1);
            continue;
        }    
        s32Ret = pCZoomTrk->MsgLock(pCZoomTrk->channel);
        if (SV_SUCCESS != s32Ret)
        {
            print_level(SV_ERROR, "MS_P failed. [err=%d]\n", s32Ret);
            pCZoomTrk->unLock();
            sleep_ms(1);
            continue;
        }
        /* 初始化目标 */
        if (pstTrkInfo->bPosUpdate)
        {
            pstTrkInfo->bPosUpdate = SV_FALSE;
            s32Ret = pCZoomTrk->PreProcess();
            if (SV_SUCCESS == s32Ret)
            {
                pstTrkInfo->bIsInit = SV_TRUE;
            }
        }
        /* 跟踪目标下一个位置坐标 */
        if (pstTrkInfo->bIsInit)
        {
            pCZoomTrk->DoProcess(&stAlgResult);
        }
        //print_level(SV_DEBUG, "nextbox (%d %d) (%d %d) \n",stNextBox.cx,stNextBox.cy,stNextBox.w,stNextBox.h);
        pCZoomTrk->MsgunLock(pCZoomTrk->channel);
        pCZoomTrk->unLock();
        /* 未初始化，跳过输出结果 */
        if (!pstTrkInfo->bIsInit)
        {
            sleep_ms(30);
            continue;
        }
        pCZoomTrk->antiShakeFilter(ALG_ZOOM, &stAlgResult, 0.1, 0.2);

        /* 结果后处理 */
        pCZoomTrk->PostProcess(&stAlgResult);
        if (SV_SUCCESS != s32Ret)
        {
            sleep_ms(1);
            continue;
        }

    }
    return NULL;
}

/******************************************************************************
 * 函数功能: 初始化变焦跟踪算法模块
 * 输入参数: initParam --- 初始化配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::Init(void *initParam)
{
    sint32                  s32Ret       = 0;
    ALG_ZOOM_INIT_S         *pstZoomInit = (ALG_ZOOM_INIT_S*)initParam;
    
    size                    = width * height * formatToBytes(format);
    mediaFd                 = pstZoomInit->s32MeidaFd;
    enable                  = pstZoomInit->bEnable;
    s32OsdFontSize          = pstZoomInit->pstZoomParam->s32OsdFontSize;
    bOsd                    = pstZoomInit->pstZoomParam->bOsd;
    bTestMode               = pstZoomInit->pstZoomParam->bTestMode;
    abAlarmOutEnable[0]     = pstZoomInit->pstZoomParam->abAlarmOutEnable[0];
    bAlarmInEnable          = pstZoomInit->pstZoomParam->bAlarmInEnable;
    enAlarmInLv             = pstZoomInit->pstZoomParam->enAlarmInLv;
    enAlarmOutLv            = pstZoomInit->pstZoomParam->enAlarmOutLv;
    s32Sensitivity          = pstZoomInit->pstZoomParam->s32Sensitivity;
    s32AlarmOutInterval     = pstZoomInit->pstZoomParam->s32AlarmOutInterval;
    threshold               = min(1.0, max(0.0, pstZoomInit->pstZoomParam->dThreshold));
    scope                   = aScopePreset[2 - min(2, max(0, s32Sensitivity))];

    pSFCP = new SFCP();
    if (NULL == pSFCP)
    {
        print_level(SV_ERROR, "new SFCP failed.\n");
        return SV_FAILURE;
    }
    s32Ret = pSFCP->init(modelPath, false);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "SFCPInit failed. [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }
    
    pbuf = mmap(NULL, size, PROT_READ, MAP_SHARED, mediaFd, 0);
    if (MAP_FAILED == pbuf)
    {
        print_level(SV_ERROR, "zoom mmap failed.\n");
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 去初始化变焦跟踪算法模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::Fini()
{
    delete pSFCP;
    munmap(pbuf, size);
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 启动变焦跟踪算法模块
 * 输入参数: startParam -- 启动参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::Start(void *startParam)
{
    sint32 s32Ret = 0;
    bRunning = SV_TRUE;
    s32Ret = pthread_create(&thread, NULL, zoom_trk_Body, startParam);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err: %s]\n", strerror(errno));
       return SV_FAILURE;
    }
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止变焦跟踪算法
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
sint32 ZoomTrk::Stop()
{
    sint32 s32Ret = 0;
    void *pvRetval = NULL;
    bRunning = SV_FALSE;
    s32Ret = pthread_join(thread, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err: %s]\n", strerror(errno));
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

