Index: include/board.h
===================================================================
--- include/board.h	(revision 4773)
+++ include/board.h	(working copy)
@@ -388,6 +388,7 @@
 #define BOARD_C_ADA32V2_100214           "100214"   /* 100214客户 */
 #define BOARD_C_ADA32V2_202613           "202613"   /* 202613客户 */
 #define BOARD_C_ADA32V2_202626           "202626"   /* 202626客户 */
+#define BOARD_C_ADA32V2_202319           "202319"   /* 202319客户 */
 
 
 /* ADA32 版本文件路径 */
Index: src/alg/pd/pd.cpp
===================================================================
--- src/alg/pd/pd.cpp	(revision 4773)
+++ src/alg/pd/pd.cpp	(working copy)
@@ -5940,6 +5940,9 @@
     SV_BOOL bRotateLast = pstPdInfo->bRotate;
 
     //sint64 s64LastTime = 0, s64NowTime = 0;
+    
+    sint32 s32TimeMs_202319 = 1000;     /* 202319客户的自检倒计时 */
+    bool bAlarmout_202319 = true;       /* 202319客户的电平变化，通过触发输出的方式模拟心跳包 */
 
     /* 测距模式标定参数*/
     PD_GUI_IMG_S stCaliImgSrc = {0};
@@ -6001,6 +6004,11 @@
         memset(&stAlarmNotic, 0x00, sizeof(stAlarmNotic));
         stPdDumpInfo.s64TimeStamp = tvNow.tv_sec * 1000 + tvNow.tv_nsec /1000000;
 
+        if(BOARD_IsCustomer(BOARD_C_ADA32V2_202319) && s32TimeMs_202319 > 0)
+        {
+            s32TimeMs_202319 -= u32StepTimeMs;
+        }
+
         /* P操作进入临界区 */
         s32Ret = AS_PostProcess_P();
         if (SV_SUCCESS != s32Ret)
@@ -6655,6 +6663,18 @@
             }
         }
 
+        if(BOARD_IsCustomer(BOARD_C_ADA32V2_202319))
+        {
+            if (s32TimeMs_202319 <= 0)
+            {
+                s32TimeMs_202319 = 1000;
+                bAlarmout_202319 = !bAlarmout_202319;
+            }
+            
+            stPdDumpInfo.bAlarmOut[2] = (SV_BOOL)bAlarmout_202319;
+            bGreenAlarmOut = (SV_BOOL)bAlarmout_202319;
+        }
+
         if (BOARD_IsCustomer(BOARD_C_ADA32V2_202661))
         {
             if (1 == apstPdsParam[s32CurChn]->astTriggerSrc[2].s32Event)
Index: src/common/board/board.c
===================================================================
--- src/common/board/board.c	(revision 4773)
+++ src/common/board/board.c	(working copy)
@@ -124,6 +124,7 @@
 	{BOARD_C_ADA32V2_R151,      BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
 	{BOARD_C_ADA32V2_R159,      BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
 	{BOARD_C_ADA32V2_202626,    BOARD_S_H_2M3,      PATH_C_ADA32V2_202626},
+	{BOARD_C_ADA32V2_202319,    BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
     {BOARD_C_ADA32V2_EXHIBITION_A, BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
     {BOARD_C_ADA32V2_EXHIBITION_B, BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
     {BOARD_C_ADA32V2_EXHIBITION_C, BOARD_S_H_2M3,      PATH_C_ADA32V2_DEFAULT},
Index: src/peripheral/mcu/mcu.c
===================================================================
--- src/peripheral/mcu/mcu.c	(revision 4773)
+++ src/peripheral/mcu/mcu.c	(working copy)
@@ -3719,6 +3719,11 @@
                     s32DelayTime = 100;
                     memcpy(&stMcuCanPacket.u8CanData[12], &s32DelayTime, 4);
                 }
+                else if(BOARD_IsCustomer(BOARD_C_ADA32V2_202319))
+                {
+                    s32DelayTime = 50;
+                    memcpy(&stMcuCanPacket.u8CanData[12], &s32DelayTime, 4);
+                }
                 else
                 {
                     s32DelayTime = 2000;
