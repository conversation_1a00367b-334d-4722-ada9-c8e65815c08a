/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_vpss.c

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2021-04-26

文件功能描述: 封装RK MPP视频处理子系统模块功能

其他: 

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述


*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <math.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <error.h>
#include <sys/prctl.h>
#include <pthread.h>
#include <errno.h>

#include "print.h"
#include "common.h"
#include "rkmedia_api.h"
#include "mpp_com.h"
#include "mpp_vpss.h"
#include "mpp_vmix.h"
#include "media.h"

#include "libdrm/xf86drm.h"
#include "libdrm/xf86drmMode.h"
#include "libdrm/drm/drm_fourcc.h"

#include <fcntl.h>
#include <unistd.h>
#include "libdrm/drm/drm_mode.h"
#include <sys/mman.h>

#include "drmrga.h"
#include "rga.h"
#include "RgaApi.h"
#include "im2d.h"

#define VMIX_LINE_WIDTH 6
#define VMIX_DRM_BUF_NUM 3

struct dma_buf_sync {
  __u64 flags;
};
#define DMA_BUF_SYNC_READ (1 << 0)
#define DMA_BUF_SYNC_WRITE (2 << 0)
#define DMA_BUF_SYNC_RW (DMA_BUF_SYNC_READ | DMA_BUF_SYNC_WRITE)
#define DMA_BUF_SYNC_START (0 << 2)
#define DMA_BUF_SYNC_END (1 << 2)
#define DMA_BUF_SYNC_VALID_FLAGS_MASK (DMA_BUF_SYNC_RW | DMA_BUF_SYNC_END)
#define DMA_BUF_BASE 'b'
#define DMA_BUF_IOCTL_SYNC _IOW(DMA_BUF_BASE, 0, struct dma_buf_sync)

typedef struct tagDrmBufInfo_S
{
    sint32  s32Fd;          /* DMA-BUF 对应的文件描述符 */
    sint32  s32Handle;      /* DMA-BUF 对应的句柄 */
    void    *virAddr;       /* DMA-BUF 对应的虚拟内存地址 */
    void    *phyAddr;       /* DMA-BUF 对应的物理地址 */
    uint32  u32Size;        /* BUF对应的大小 */
    uint32  u32Width;       /* BUF对应的宽度 */
    uint32  u32Height;      /* BUF对应的高度 */
    uint32  u32Format;      /* BUF对应的格式 */
    sint32  s32State;       /* BUF的状态: -1不可读不可写, 0不可读可写， 1可读可写 */
} DRM_BUF_INFO_S;


/* 视频合成模块控制信息 */
typedef struct tagVmixInfo_S
{
    struct rgaContext   *rgactx;        /* RGA配置信息 */
    sint32              s32DEV_FD;      /* 显卡设备文件描述符 */
    DRM_BUF_INFO_S      stDrmBuf[VMIX_DRM_BUF_NUM];    /* 申请的DRM_BUF内存块 */
    SV_BOOL             bInterrupt[4];  /* 跳过BUFFER块 */
    uint32              u32ChnNum;      /* 视频源通道数目 */
    SPLIT_MODE          enSplitMode;    /* 分屏布局模式 */
    uint32              u32Width;       /* VMIX宽高 */
    uint32              u32Height;      /* VMIX宽高 */
    uint32              u32MainTid;     /* 主线程 */
    uint32              stBufIdx;       /* 轮换的BufIdx */
    pthread_mutex_t     mutexLock;      /* BUF线程锁 */
    SV_BOOL             bRunning;       /* 取帧线程控制 */
} MPP_VMIX_INFO_S;

extern sint32 drm_create_fb(sint32 s32Fd, DRM_BUF_INFO_S *pstDrmBuf, uint32 u32Width, uint32 u32Height, uint32 u32Format);
extern sint32 drm_destroy_fb(sint32 s32Fd, DRM_BUF_INFO_S *pstDrmBuf);
extern sint32 mpp_vmix_MainBody(void *pvArg);

MPP_VMIX_INFO_S m_stVmixInfo = {0};

inline static sint32 format_to_bpp(sint32 format)
{
    switch (format) {
    case DRM_FORMAT_NV12:
    case DRM_FORMAT_NV12_10:
    case DRM_FORMAT_YUV420:
        return 8;
    case DRM_FORMAT_BGR565:
    case DRM_FORMAT_RGB565:
        return 16;
    case DRM_FORMAT_BGR888:
    case DRM_FORMAT_RGB888:
        return 24;
    case DRM_FORMAT_ARGB8888:
    case DRM_FORMAT_XRGB8888:
    case DRM_FORMAT_RGBA8888:
    default:
        return 32;
    }
}

sint32 drm_create_fb(sint32 s32Fd, DRM_BUF_INFO_S *pstDrmBuf, uint32 u32Width, uint32 u32Height, uint32 u32Format)
{
    sint32 s32Ret;
    struct drm_mode_create_dumb create = {0};
    struct drm_mode_map_dumb map = {0};
    void *viraddr, *phyaddr;
    sint32 s32DrmFd;

    create.width = u32Width;
    create.height = u32Height;
    create.bpp = format_to_bpp(u32Format);
    if(u32Format == DRM_FORMAT_NV12)
    {
        create.height = u32Height * 3 / 2;
    }

    pstDrmBuf->u32Width = u32Width;
    pstDrmBuf->u32Height = u32Height;
    pstDrmBuf->u32Format = u32Format;

    s32Ret = drmIoctl(s32Fd, DRM_IOCTL_MODE_CREATE_DUMB, &create);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "drmIoctl DRM_IOCTL_MODE_CREATE_DUMB failed! [err=%d]\n", s32Ret);
        return SV_FAILURE;
    }
    pstDrmBuf->s32Handle = create.handle;
    uint32 u32handles[4]={0}, u32pitches[4]={0}, u32offsets[4]={0};
    map.handle = create.handle;
    s32Ret = drmIoctl(s32Fd, DRM_IOCTL_MODE_MAP_DUMB, &map);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "drmIoctl DRM_IOCTL_MODE_MAP_DUMB fail! [err=%d]\n", s32Ret);
        drm_destroy_fb(s32Fd, pstDrmBuf);
        return SV_FAILURE;
    }

    viraddr = mmap(0, create.size, PROT_READ | PROT_WRITE, MAP_SHARED,  s32Fd, map.offset);
    if(NULL == viraddr)
    {
        print_level(SV_ERROR, "mmap failed\n [err=%d]\n", s32Ret);
        drm_destroy_fb(s32Fd, pstDrmBuf);
        return SV_FAILURE;
    }
    pstDrmBuf->virAddr   = viraddr;
    pstDrmBuf->u32Size   = create.size;

    s32Ret = drmPrimeHandleToFD(s32Fd, create.handle, 0, &s32DrmFd);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "drmIoctl DRM_IOCTL_MODE_MAP_DUMB fail! [err=%d]\n", s32Ret);
        drm_destroy_fb(s32Fd, pstDrmBuf);
        return SV_FAILURE;
    }
    pstDrmBuf->s32Fd     = s32DrmFd;
    return SV_SUCCESS;
}

sint32 drm_destroy_fb(sint32 s32Fd, DRM_BUF_INFO_S *pstDrmBuf)
{
    if(pstDrmBuf->virAddr && pstDrmBuf->u32Size > 0)
    {
        munmap(pstDrmBuf->virAddr, pstDrmBuf->u32Size);
        pstDrmBuf->virAddr = NULL;
        pstDrmBuf->u32Size = 0;
    }
    
    if(pstDrmBuf->s32Handle)
    {
        struct drm_mode_destroy_dumb destroy = {0};
        destroy.handle = pstDrmBuf->s32Handle;
        drmIoctl(s32Fd, DRM_IOCTL_MODE_DESTROY_DUMB, &destroy);
        pstDrmBuf->s32Handle = 0;
    }
    return SV_SUCCESS;
}

sint32 mpp_vmix_scaleCopy(DRM_BUF_INFO_S *pstDrmBuf_dst, DRM_BUF_INFO_S *pstDrmBuf_src, SV_RECT_S *pstRgn_dst, SV_RECT_S *pstRgn_src)
{
    sint32 s32Ret;
    rga_info_t rga_dst = {0}, rga_src = {0};

    if(pstDrmBuf_dst == NULL || pstDrmBuf_src == NULL || pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        print_level(SV_ERROR, "address invalid!\n");
        return SV_ERROR;
    }

    rga_dst.rect.xoffset    = pstRgn_dst->s32X;
    rga_dst.rect.yoffset    = pstRgn_dst->s32Y;
    rga_dst.rect.width      = pstRgn_dst->u32Width;
    rga_dst.rect.height     = pstRgn_dst->u32Height;
    rga_dst.rect.wstride    = pstDrmBuf_dst->u32Width;
    rga_dst.rect.hstride    = pstDrmBuf_dst->u32Height;
    rga_dst.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_dst.fd              = pstDrmBuf_dst->s32Fd;
    rga_dst.virAddr         = pstDrmBuf_dst->virAddr;
    rga_dst.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_dst->u32Size;

    rga_src.rect.xoffset    = pstRgn_src->s32X;
    rga_src.rect.yoffset    = pstRgn_src->s32Y;
    rga_src.rect.width      = pstRgn_src->u32Width;
    rga_src.rect.height     = pstRgn_src->u32Height;
    rga_src.rect.wstride    = pstDrmBuf_src->u32Width;
    rga_src.rect.hstride    = pstDrmBuf_src->u32Height;
    rga_src.rect.format     = RK_FORMAT_YCbCr_420_SP;
    rga_src.fd              = pstDrmBuf_src->s32Fd;
    rga_src.virAddr         = pstDrmBuf_src->virAddr;
    rga_src.format          = RK_FORMAT_YCbCr_420_SP;
    rga_dst.bufferSize      = pstDrmBuf_src->u32Size;


    s32Ret = RgaBlit(&rga_src, &rga_dst, NULL);
    if(s32Ret != SV_SUCCESS)
    {
        print_level(SV_ERROR, "RgaBlit fail! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/* 设置单一摄像头布局 */
sint32 mpp_vmix_OneLayout(SV_RECT_S *pstRgn_dst, SV_RECT_S *pstRgn_src)
{
    sint32 s32Ret;
    if(pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        return ERR_NULL_PTR;
    }


    pstRgn_dst[0].s32X = 0;
    pstRgn_dst[0].s32Y = 0;
    pstRgn_dst[0].u32Width = m_stVmixInfo.u32Width;
    pstRgn_dst[0].u32Height = m_stVmixInfo.u32Height;
    
    pstRgn_src[0].s32X = 0;
    pstRgn_src[0].s32Y = 0;
    pstRgn_src[0].u32Width = m_stVmixInfo.u32Width;
    pstRgn_src[0].u32Height = m_stVmixInfo.u32Height;

    return SV_SUCCESS;
}

/* 设置双路摄像头布局 */
sint32 mpp_vmix_TwoLayout(SV_RECT_S *pstRgn_dst, SV_RECT_S *pstRgn_src)
{
    sint32 s32Ret, i;
    if(pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        return ERR_NULL_PTR;
    }

    for(i = 0; i < 2; i++)
    {
        pstRgn_dst[i].s32X = i* (m_stVmixInfo.u32Width + VMIX_BLANK_LINE_WIDTH) / 2;
        pstRgn_dst[i].s32Y = 0;
        pstRgn_dst[i].u32Width = (m_stVmixInfo.u32Width - VMIX_BLANK_LINE_WIDTH) / 2;
        pstRgn_dst[i].u32Height = m_stVmixInfo.u32Height;

        pstRgn_src[i].s32X = 0;
        pstRgn_src[i].s32Y = 0;
        pstRgn_src[i].u32Width = m_stVmixInfo.u32Width;
        pstRgn_src[i].u32Height = m_stVmixInfo.u32Height;

    }

    return SV_SUCCESS;
}


/* 设置三路摄像头布局 */
sint32 mpp_vmix_ThreeLayout(SV_RECT_S *pstRgn_dst, SV_RECT_S *pstRgn_src)
{
    sint32 s32Ret, i;
    if(pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        return ERR_NULL_PTR;
    }

    for(i = 0; i < 1; i++)
    {
        pstRgn_dst[i].s32X = 0;
        pstRgn_dst[i].s32Y = 0;
        pstRgn_dst[i].u32Width = (m_stVmixInfo.u32Width - VMIX_BLANK_LINE_WIDTH) / 2 ;
        pstRgn_dst[i].u32Height = m_stVmixInfo.u32Height;
        
        pstRgn_src[i].s32X = 0;
        pstRgn_src[i].s32Y = 0;
        pstRgn_src[i].u32Width = m_stVmixInfo.u32Width;
        pstRgn_src[i].u32Height = m_stVmixInfo.u32Height;
    }

    for(i = 0; i < 2; i++)
    {
        pstRgn_dst[i+1].s32X = (m_stVmixInfo.u32Width + VMIX_BLANK_LINE_WIDTH) / 2;
        pstRgn_dst[i+1].s32Y = i * (m_stVmixInfo.u32Height + VMIX_BLANK_LINE_WIDTH) / 2;
        pstRgn_dst[i+1].u32Width = (m_stVmixInfo.u32Width - VMIX_BLANK_LINE_WIDTH) / 2;
        pstRgn_dst[i+1].u32Height = (m_stVmixInfo.u32Height - VMIX_BLANK_LINE_WIDTH) / 2;
        
        pstRgn_src[i+1].s32X = 0;
        pstRgn_src[i+1].s32Y = 0;
        pstRgn_src[i+1].u32Width = m_stVmixInfo.u32Width;
        pstRgn_src[i+1].u32Height = m_stVmixInfo.u32Height;
    }

    return SV_SUCCESS;
}

/* 设置四路摄像头布局 */
sint32 mpp_vmix_FourLayout(SV_RECT_S * pstRgn_dst, SV_RECT_S * pstRgn_src)
{
    sint32 s32Ret, i, j, n;
    if(pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        return ERR_NULL_PTR;
    }

    for(i = 0, n = 0; i < 2; i++)
    {
        for(j = 0; j < 2; j++, n++)
        {
            pstRgn_dst[n].s32X = j * (m_stVmixInfo.u32Width + VMIX_BLANK_LINE_WIDTH) / 2;
            pstRgn_dst[n].s32Y = i * (m_stVmixInfo.u32Height + VMIX_BLANK_LINE_WIDTH) / 2;
            pstRgn_dst[n].u32Width = (m_stVmixInfo.u32Width - VMIX_BLANK_LINE_WIDTH) / 2;
            pstRgn_dst[n].u32Height = (m_stVmixInfo.u32Height - VMIX_BLANK_LINE_WIDTH) / 2;
            
            pstRgn_src[n].s32X = 0;
            pstRgn_src[n].s32Y = 0;
            pstRgn_src[n].u32Width = m_stVmixInfo.u32Width;
            pstRgn_src[n].u32Height = m_stVmixInfo.u32Height;
            
        }
    }

    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: 设置VMiX的布局
 * 输入参数: pstRgn_dst -- VMIX叠加的目的布局
             pstRgn_src -- VMIX叠加的源图片布局
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 失败
 * 注意    : 
 *****************************************************************************/
sint32 mpp_vmix_GetLayout(SV_RECT_S *pstRgn_dst, SV_RECT_S *pstRgn_src)
{
    sint32 s32Ret, i;
    
    if(pstRgn_dst == NULL || pstRgn_src == NULL)
    {
        return ERR_NULL_PTR;
    }

    switch (m_stVmixInfo.enSplitMode)
    {
        case MEDIA_SPLIT_ONE:
            s32Ret = mpp_vmix_OneLayout(pstRgn_dst, pstRgn_src);
            break;
        case MEDIA_SPLIT_TWO:
            s32Ret = mpp_vmix_TwoLayout(pstRgn_dst, pstRgn_src);
            break;
        case MEDIA_SPLIT_THREE:
            s32Ret = mpp_vmix_ThreeLayout(pstRgn_dst, pstRgn_src);
            break;
        case MEDIA_SPLIT_FOUR:
        default:
            s32Ret = mpp_vmix_FourLayout(pstRgn_dst, pstRgn_src);
            break;
    }

    return s32Ret;
}


sint32 mpp_vmix_Init(MPP_VMIX_CONF_S * pstVmixConf)
{
    sint32 s32Ret = 0, i;
    uint32 u32Width, u32Height;
    sint32 s32DEV_FD;
    DRM_BUF_INFO_S *pstDrmBuf = m_stVmixInfo.stDrmBuf;
    void **prgactx = &m_stVmixInfo.rgactx;

    if (NULL == pstVmixConf)
    {
        return ERR_NULL_PTR;
    }

    s32DEV_FD = open(MPP_FRAMEBUFFER_FD, O_RDWR | O_CLOEXEC);
    if(s32DEV_FD <= 0)
    {
        print_level(SV_ERROR, "open %s fail! [err=%#x]\n", MPP_FRAMEBUFFER_FD, s32DEV_FD);
        return SV_FAILURE;
    }

    RgaInit(prgactx);
    if(prgactx == NULL)
    {
        print_level(SV_ERROR, "RgaInit fail! [err=%#x]\n", s32Ret);
        return SV_FAILURE;
    }

    s32Ret = pthread_mutex_init(&m_stVmixInfo.mutexLock, NULL);
    if (0 != s32Ret)
    {
        return ERR_SYS_NOTREADY;
    }

    u32Width  = pstVmixConf->u32Width;
    u32Height = pstVmixConf->u32Height;
    for(i = 0; i < 3; i++)
    {
        s32Ret = drm_create_fb(s32DEV_FD, pstDrmBuf, u32Width, u32Height, DRM_FORMAT_NV12);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "drm_create_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        pstDrmBuf->s32State = 1;
        pstDrmBuf++;
    }

    m_stVmixInfo.u32Width       = u32Width;
    m_stVmixInfo.u32Height      = u32Height;
    m_stVmixInfo.s32DEV_FD      = s32DEV_FD;
    m_stVmixInfo.enSplitMode    = pstVmixConf->enSplitMode;
    m_stVmixInfo.u32ChnNum      = pstVmixConf->u32ChnNum;
    return SV_SUCCESS;
}

sint32 mpp_vmix_SetAttr(VIDEO_VMIX_S *pstVmixAttr)
{
    if(pstVmixAttr == NULL)
        return ERR_NULL_PTR;

    m_stVmixInfo.enSplitMode = pstVmixAttr->enSplitMode;
    
    return SV_SUCCESS;
}

sint32 mpp_vmix_Fini()
{
    sint32 s32Ret = 0, i;
    sint32 s32DEV_FD = m_stVmixInfo.s32DEV_FD;
    DRM_BUF_INFO_S *pstDrmBuf = m_stVmixInfo.stDrmBuf;
    void *rgactx = m_stVmixInfo.rgactx;

    for(i = 0; i < 3; i++)
    {
        s32Ret = drm_destroy_fb(s32DEV_FD, pstDrmBuf);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "drm_destroy_fb fail! [err=%#x]\n", s32Ret);
            return SV_FAILURE;
        }
        memset(pstDrmBuf, 0x00, sizeof(pstDrmBuf));
        pstDrmBuf++;
    }

    RgaDeInit(rgactx);
    m_stVmixInfo.rgactx = NULL;
    
    close(s32DEV_FD);
    m_stVmixInfo.s32DEV_FD = NULL;
    return SV_SUCCESS;
}

sint32 mpp_vmix_Start()
{
    sint32 s32Ret = 0;
    uint32 u32MainTid = 0;
    pthread_attr_t 	attr;
    m_stVmixInfo.bRunning = SV_TRUE;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);       //设置为分离线程    
    s32Ret = pthread_create(&u32MainTid, &attr, mpp_vmix_MainBody, &m_stVmixInfo);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "Start thread for VPSS failed! [err: %s]\n", strerror(errno));
        return s32Ret;
    }

    m_stVmixInfo.u32MainTid = u32MainTid;
    return SV_SUCCESS;
}

sint32 mpp_vmix_Stop()
{
    sint32 s32Ret = 0;
    void * pvRetval = NULL;

    m_stVmixInfo.bRunning = SV_FALSE;

    return SV_SUCCESS;
}

sint32 mpp_vmix_Interrupt(sint32 s32Chn, SV_BOOL bInterrupt)
{
    if(s32Chn > VIM_MAX_CHN_NUM || s32Chn < 0)
        return ERR_ILLEGAL_PARAM;
    m_stVmixInfo.bInterrupt[s32Chn] = bInterrupt;
    return SV_SUCCESS;
}

sint32 mpp_vmix_MainBody(void *pvArg)
{
    sint32 s32Ret = 0, idx, i, j, k;
    void *mb[4] = {NULL};
    char thread_name[32];
    MPP_VMIX_INFO_S *pstVmixInfo = (MPP_VMIX_INFO_S *)pvArg;
    DRM_BUF_INFO_S stDrmBuf_src = {0};
    SV_RECT_S stRgn_dst[4]={0}, stRgn_src[4] = {0};
    MB_IMAGE_INFO_S stMb_Info;
    static SPLIT_MODE enSplitMode = -1;

    sprintf(thread_name, "mpp_vmix_MainBody");
    s32Ret = prctl(PR_SET_NAME, thread_name);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    print_level(SV_INFO, "++mpp_vmix_MainBody start\n");
    while (pstVmixInfo->bRunning)
    {
        memset(mb, 0x00, sizeof(mb));
        for(i=0; i < pstVmixInfo->u32ChnNum; i++)
        {
            if(pstVmixInfo->bInterrupt[i])
            {
                continue;
            }
            s32Ret = mpp_vpss_GetFrame(i, MPP_VPSS_CHN_VO, &mb[i]);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_INFO, "mpp_vpss_GetFrame[%d %d] fail\n", i, MPP_VPSS_CHN_VO);
                sleep_ms(2);
                continue;
            }
        }

        pthread_mutex_lock(&pstVmixInfo->mutexLock);
        idx = (pstVmixInfo->stBufIdx + 1) % VMIX_DRM_BUF_NUM;
        for(i=0; i < VMIX_DRM_BUF_NUM; i ++)
        {
            if(pstVmixInfo->stDrmBuf[idx].s32State >= 0)
            {
                pstVmixInfo->stBufIdx = idx;
                break;
            }
            idx = (idx + 1) % VMIX_DRM_BUF_NUM;
        }

        if(i >= VMIX_DRM_BUF_NUM)
        {
            goto skip;
        }

        if(enSplitMode != pstVmixInfo->enSplitMode)
        {
            s32Ret = mpp_vmix_GetLayout(stRgn_dst, stRgn_src);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_ERROR, "mpp_vmix_GetLayout fail!\n");
                goto skip;
            }

            /* 将原图全部重新初始化 */
            for(i = 0; i < VMIX_DRM_BUF_NUM; i++)
            {
                sint32 Ysize = pstVmixInfo->stDrmBuf[i].u32Width*pstVmixInfo->stDrmBuf[i].u32Height;
                sint32 UVsize = pstVmixInfo->stDrmBuf[i].u32Width*pstVmixInfo->stDrmBuf[i].u32Height / 2;
                memset(pstVmixInfo->stDrmBuf[i].virAddr, 0xeb, Ysize);
                memset(pstVmixInfo->stDrmBuf[i].virAddr+Ysize, 0x80, UVsize);
            }
            enSplitMode = pstVmixInfo->enSplitMode;
        }

        for(i = 0; i < pstVmixInfo->u32ChnNum; i++)
        {
            if(mb[i] == NULL)
            {
                print_level(SV_WARN, "mb[%d] == NULL fail!\n", i);
                continue;
            }

            if(pstVmixInfo->enSplitMode == MEDIA_SPLIT_ONE)
            {
                if(i >= 1) continue;
            }
            
            if(pstVmixInfo->enSplitMode == MEDIA_SPLIT_TWO)
            {
                if(i >= 2) continue;
            }
            
            if(pstVmixInfo->enSplitMode == MEDIA_SPLIT_THREE)
            {
                if(i >= 3) continue;
            }
            s32Ret = RK_MPI_MB_GetImageInfo(mb[i], &stMb_Info);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_WARN, "RK_MPI_MB_GetImageInfo mb[%d] fail! [err=%d]\,", i, s32Ret);
                continue;
            }



            stDrmBuf_src.s32Fd = RK_MPI_MB_GetFD(mb[i]);
            stDrmBuf_src.virAddr = RK_MPI_MB_GetPtr(mb[i]);
            stDrmBuf_src.u32Width = stMb_Info.u32Width;
            stDrmBuf_src.u32Height = stMb_Info.u32Height;
            stDrmBuf_src.u32Size  = RK_MPI_MB_GetSize(mb[i]);

            s32Ret = mpp_vmix_scaleCopy(&pstVmixInfo->stDrmBuf[idx], &stDrmBuf_src, &stRgn_dst[i], &stRgn_src[i]);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_WARN, "mpp_vmix_scaleCopy[%d] fail! [err=%d]\n,", i, s32Ret);

                continue;
            }
        }


        s32Ret = mpp_vosd_external_callback(0, MPP_VPSS_CHN_VO, pstVmixInfo->stDrmBuf[idx].virAddr, 
                                            pstVmixInfo->stDrmBuf[idx].u32Width, pstVmixInfo->stDrmBuf[idx].u32Height);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_ERROR, "mpp_vosd_external_callback fail! [err=%x]\n", s32Ret);
        }

        pstVmixInfo->stDrmBuf[idx].s32State = 1;
skip:
        pthread_mutex_unlock(&pstVmixInfo->mutexLock);
        for(i=0; i < pstVmixInfo->u32ChnNum; i++)
        {
            s32Ret = mpp_vpss_ReleaseFrame(&mb[i]);
            if(s32Ret != SV_SUCCESS)
            {
                print_level(SV_INFO, "mpp_vpss_ReleaseFrame[%d] fail\n", i);
                sleep_ms(2);
                continue;
            }
        }
    }

    for(i=0; i < pstVmixInfo->u32ChnNum; i++)
    {
        s32Ret = mpp_vpss_ReleaseFrame(&mb[i]);
        if(s32Ret != SV_SUCCESS)
        {
            print_level(SV_INFO, "mpp_vpss_ReleaseFrame[%d] fail\n", i);
            sleep_ms(2);
            continue;
        }
    }

    print_level(SV_INFO, "++mpp_vmix_MainBody end\n");
    return NULL;
}

/******************************************************************************
 * 函数功能: 获取VMIX通道数据
 * 输入参数: ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vmix_GetFrame(void **ppvBuf)
{
    sint32 s32Ret = 0, i, retry, index;
    void *pbmp = NULL;
    SV_BOOL bGetFrame = 0;

    if(ppvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }

    retry = 10;
    while(retry--)
    {
        pthread_mutex_lock(&m_stVmixInfo.mutexLock);
        index = m_stVmixInfo.stBufIdx;
        for(i = 0; i < VMIX_DRM_BUF_NUM; i++)
        {
            if(m_stVmixInfo.stDrmBuf[index].s32State == 1)
            {
                pbmp = m_stVmixInfo.stDrmBuf[index].virAddr;
                m_stVmixInfo.stDrmBuf[index].s32State = -1;
                bGetFrame = 1;
                break;
            }
            index = (index - 1 + VMIX_DRM_BUF_NUM) % VMIX_DRM_BUF_NUM;
        }
        pthread_mutex_unlock(&m_stVmixInfo.mutexLock);

        if(i < VMIX_DRM_BUF_NUM)
        {
            break;
        }
        sleep_ms(10);
    }

    if(bGetFrame == 0)
    {
        print_level(SV_INFO, "get vmix timeout\n");
        return SV_FAILURE;
    }

    *ppvBuf = pbmp;
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 释放VMIX通道数据
 * 输入参数: s32Chn --- 编码通道号 [0, MPP_VPSS_CHN_BUTT)
             ppvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
 sint32 mpp_vmix_ReleaseFrame(void **ppvBuf)
{
    sint32 s32Ret = 0, i;
    void *pbmp = *ppvBuf;
    if(ppvBuf == NULL)
    {
        return SV_SUCCESS;
    }
    
    pthread_mutex_lock(&m_stVmixInfo.mutexLock);
    for(i = 0; i < VMIX_DRM_BUF_NUM; i++)
    {
        if(m_stVmixInfo.stDrmBuf[i].virAddr == pbmp)
        {
            m_stVmixInfo.stDrmBuf[i].s32State = 0;
            break;
        }
    }

    if(i > VMIX_DRM_BUF_NUM)
    {
        print_level(SV_WARN, "Free Buffer fail!\n");
        return SV_FAILURE;
    }

    pthread_mutex_unlock(&m_stVmixInfo.mutexLock);

    *ppvBuf = NULL;
    
    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 根据虚拟地址获取FD文件描述符
 * 输入参数: pvBuf --- 数据缓存指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
sint32 mpp_vmix_GetFD(void *pvBuf)
{
    int i = 0;
    sint32 s32DrmFd = 0;
    
    if(pvBuf == NULL)
    {
        return ERR_NULL_PTR;
    }
    
    for(i = 0; i < VMIX_DRM_BUF_NUM; i++)
    {
        if(m_stVmixInfo.stDrmBuf[i].virAddr == pvBuf)
        {
            s32DrmFd = m_stVmixInfo.stDrmBuf[i].s32Fd;
            break;
        }
    }
    
    return s32DrmFd;
}



