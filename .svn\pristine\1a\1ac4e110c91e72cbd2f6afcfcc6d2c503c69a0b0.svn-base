/******************************************************************************
Copyright (C) 2018-2025 广州敏视数码科技有限公司版权所有.

文件名：rtl8723.c

作者: 孙志平    版本: v1.0.0(初始版本号)   日期: 2022-10-31

文件功能描述: rtl8723模块功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <regex.h>
#include <linux/wireless.h>
#include <linux/in.h>
#include <signal.h>
#include <time.h>
#include <fcntl.h>
#include <pthread.h>
#include <error.h>
#include <errno.h>

#include "print.h"
#include "safefunc.h"
#include "utils.h"
#include "rtl8723.h"


/* rtl8723模块控制信息 */
typedef struct tag_Rtl8723ComInfo_S
{
	WIFI_ID_E enWifiId;				/* wifi型号*/
	char	 szWifiPath[24];		/* wifi驱动在proc里面的目录 */
    SV_BOOL  bStaEnable;            /* STA模式是否使能 */
    SV_BOOL  bStaConfUpdate;        /* 是否STA模式配置更新 */
    SV_BOOL  bApConfigFinished;     /* AP配置是否完成 */
    WIFI_CONN_E enConnStatus;       /* STA连接状态 */
    char     szUsedSsid[64];        /* STA正在连接的热点SSID */
    char     szStaSsid[64];         /* 配置的需要连接的STA热点 */
    char     szStaIpAddr[32];       /* STA连接热点分配的IP地址 */
    char     szStaStatusText[64];   /* STA连接状态文本 */
    SV_BOOL  bCurUse5G;             /* 当前AP热点使用5G频段 */
    sint32   s32SocketId;           /* socket ID */
    uint32   u32TID;                /* 线程ID */
    SV_BOOL  bRunning;              /* 线程是否正在运行 */
    SV_BOOL  bException;            /* 线程是否出现异常 */
    char     szCountryCode[4];      /* 当前配置的国家码 */
} RTL8723_COM_INFO_S;

RTL8723_COM_INFO_S m_stRtl8723Info;   /* 模块控制信息 */

extern SV_BOOL m_WifiException;
extern sint32 network_PingIpaddr(char *pszInterface, char *pszIpaddr, uint32 u32Timeout, float *pfSpeedTime);

void rtl8723_ReviseApInfo(char *strin, char *strout, char *oldstr, char *newstr)
{
    uint32 i = 0;
    char bstr[256];

    memset(bstr, 0, sizeof(bstr));
    for(i = 0; i < strlen(strin); i++)
    {
        if(!strncmp(strin+i, oldstr, strlen(oldstr)))//查找目标字符串
        {
            strcat(bstr, newstr);
            i += strlen(oldstr) - 1;
        }
        else
        {
        	strncat(bstr, strin + i, 1);//保存一字节进缓冲区
	    }
    }

    strcpy(strout,bstr);
}

/******************************************************************************
 * 函数功能: 统计当前环境信道信息
 * 输入参数: enWifiFreq --- 使用频段
 * 输出参数: channel --- 获取的最优信道
 * 返回值  : SV_TRUE - 成功
             SV_FALSE - 其他错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_Statistical_Channel(WIFI_FREQ enWifiFreq, long *channel)
{
    FILE * fp = NULL;
    long ch_optimal24G, ch_optimal5G;
    sint32 s32Ret = 0, i, getint, chmin;
    sint32 channelList[165+1];//1-13为2.4G,其中1,6,11互不干扰(最好三选一);149,153,157,161,165为5G
    char szCmd[256];

    strcpy(szCmd, "iwlist wlan0 scan | grep \"Cell*\" -A 4 | grep Channel* | cut -c 49-  | awk -F ')' '{print$1}' > /var/Statistical_channel.txt");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    fp = fopen("/var/Statistical_channel.txt", "rt+");
    if (fp == NULL)
    {
        print_level(SV_ERROR, "Open file failed!\n");
        return SV_FAILURE;
    }

    memset(channelList, 0, sizeof(channelList));
    while ((fgets(szCmd,5,fp)) != NULL)
    {
        getint = atoi(szCmd);
        if (getint>=1 && getint <= 165)
        {
            channelList[getint]++;
        }
    }

    ch_optimal24G = 1;
    chmin = channelList[1];
    for (i=1;i<=11;i+=5)
    {
        if (chmin > channelList[i])
        {
            ch_optimal24G = i;
            chmin = channelList[i];
        }
    }

    ch_optimal5G = 149;
    chmin = channelList[149];
    for (i = 149; i <= 165; i += 4)
    {
        if (chmin > channelList[i])
        {
            ch_optimal5G = i;
            chmin = channelList[i];
        }
    }

    if (enWifiFreq == WIFI_FREQ_2_4G)
    {
        *channel = ch_optimal24G;
		m_stRtl8723Info.bCurUse5G = SV_FALSE;
    }
    else if (enWifiFreq == WIFI_FREQ_5G)
    {
        *channel = ch_optimal5G;
		m_stRtl8723Info.bCurUse5G = SV_TRUE;
    }
    else
    {
        print_level(SV_INFO, "channel24G=%d, u32ChnNum24G=%d, channel5G=%d, u32ChnNum5G=%d\n", ch_optimal24G, channelList[ch_optimal24G], ch_optimal5G, channelList[ch_optimal5G]);
        if (channelList[ch_optimal24G] > 2 && channelList[ch_optimal24G] > channelList[ch_optimal5G])
        {
            *channel = ch_optimal5G;
			m_stRtl8723Info.bCurUse5G = SV_TRUE;
        }
        else
        {
            *channel = ch_optimal24G;
			m_stRtl8723Info.bCurUse5G = SV_FALSE;
        }
    }

    print_level(SV_INFO, "ch_optimal=%d\n", *channel);

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: AP模式获取当前信道
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 信道号
 * 注意    : 无
 *****************************************************************************/
long rtl8723_GetChannel(void)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    uint32 u32ErrCnt = 0;
    char *pcTmp = NULL;
    long lCurchannel = 0;
    char *pszCurchannel = "cur_channel=";
    char szBuf[4096] = {0};
	char szApInfoPath[256] = {0};
    static sint32 s32ErrCnt = 0;

    memset(szBuf, 0x0, 4096);
    while(s32Fd <= 0)
    {
        if (++u32ErrCnt >= 2)
        {
            return lCurchannel;
        }

		sprintf(szApInfoPath, "/proc/net/%s/wlan0/ap_info", m_stRtl8723Info.szWifiPath);
        s32Fd = open(szApInfoPath, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_WARN, "acess %s failed. [err=%#x]\n", szApInfoPath, errno);
            sleep_ms(1000);
        }
    }

    s32Ret = read(s32Fd, szBuf, 4096);
    if(s32Ret <= 0)
    {
        print_level(SV_WARN, "read %s failed. [err=%#x]\n", szApInfoPath, errno);
    }
    else
    {
        pcTmp = strstr(szBuf, pszCurchannel);
        if (NULL == pcTmp)
        {
            print_level(SV_WARN, "cannot find keyword: %s, AP finish: %d\n", pszCurchannel, m_stRtl8723Info.bApConfigFinished);
#if defined(BOARD_DMS31V2)
            s32ErrCnt++;
            if (m_stRtl8723Info.bApConfigFinished && !m_WifiException && s32ErrCnt >= 5)
            {
                print_level(SV_WARN, "get channel failed more than 5 times, reset module now.\n");
                s32ErrCnt = 0;
                m_stRtl8723Info.bApConfigFinished = SV_FALSE;
                m_WifiException = SV_TRUE;
            }
#endif
        }
        else
        {
            s32ErrCnt = 0;
            pcTmp += strlen(pszCurchannel);
            lCurchannel = atoi(pcTmp);
        }
    }

    close(s32Fd);

    return lCurchannel;
}

 /******************************************************************************
 * 函数功能: AP模式设置当前信道
 * 输入参数: enWifiFreq --- WIFI热点频段
             lChannel --- 设置的信道号
 * 输出参数: 无
 * 返回值  : 信道号
 * 注意    : 无
 *****************************************************************************/
long rtl8723_SetChannel(WIFI_FREQ enWifiFreq, long lChannel)
{
    sint32 s32Ret = 0;
    long selChannel = 0, bestChannel;

	s32Ret = rtl8723_Statistical_Channel(enWifiFreq, &bestChannel);
	if (SV_SUCCESS != s32Ret)
	{
		print_level(SV_ERROR, "rtl8723_Statistical_Channel failed.\n");
		return SV_FAILURE;
	}

	selChannel = lChannel ? lChannel : bestChannel;

    return selChannel;
}

/******************************************************************************
 * 函数功能: 判断当前STA模式是否连接有热点
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_TRUE - 连接
             SV_FALSE - 未连接
 * 注意    : 无
 *****************************************************************************/
SV_BOOL rtl8723_IsStaConnected()
{
    sint32 s32Ret = 0;
    struct ifreq stIfr;			//ifreq和iwreq是内核的结构体
    struct iwreq stWRQ = {0};
    char szCmd[64];
    char szSSID[64] = {0};

    //if (!BOARD_IsWifiExist())
    //{
    //    return SV_FALSE;
    //}

    strcpy(stIfr.ifr_name, "wlan1");
    s32Ret = ioctl(m_stRtl8723Info.s32SocketId, SIOCGIFFLAGS, &stIfr);		//SIOCGIFFLAGS是获取接口标志
    if (0 != s32Ret || !(stIfr.ifr_flags & IFF_UP))
    {
        print_level(SV_ERROR, "ioctl SIOCGIFFLAGS failed or wlan1 is down. [ret=%d, up=%d]\n", s32Ret, (stIfr.ifr_flags & IFF_UP) ? 1 : 0);
        strcpy(szCmd, "ifconfig wlan1 up");
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        }
        sleep_ms(1000);

        return SV_FALSE;
    }

    memset(&stWRQ, 0x0, sizeof(stWRQ));
    strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
    stWRQ.u.essid.pointer = szSSID;
    stWRQ.u.essid.length = 64;
    stWRQ.u.essid.flags = 0;
    s32Ret = ioctl(m_stRtl8723Info.s32SocketId, SIOCGIWESSID, &stWRQ);		//获取SSID
    if (s32Ret < 0 || strlen(szSSID) == 0)
    {
        return SV_FALSE;

    }

    return SV_TRUE;
}

/******************************************************************************
 * 函数功能: STA模式获取当前所连接热点的SSID、MAC地址和分配到的IP地址
 * 输入参数: 无
 * 输出参数: pau8Mac --- MAC 地址
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_StaGetCurSsidMacIp(char *pszSsid, uint8 *pau8Mac, uint32 *pu32IpAddr)
{
    sint32 s32Ret = 0, i;
    char szSSID[64] = {0};
    struct iwreq stWRQ = {0};
    struct sockaddr_in stAddr;
    struct ifreq stIfr;

    if (NULL == pau8Mac)
    {
        return ERR_NULL_PTR;
    }

    memset(&stWRQ, 0x0, sizeof(stWRQ));
    strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
    stWRQ.u.essid.pointer = szSSID;
    stWRQ.u.essid.length = 64;
    stWRQ.u.essid.flags = 0;
    ioctl(m_stRtl8723Info.s32SocketId, SIOCGIWESSID, &stWRQ);
    szSSID[63] = '\0';
    strcpy(pszSsid, szSSID);

    memset(&stWRQ, 0x0, sizeof(stWRQ));
    strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
    stWRQ.u.ap_addr.sa_family = 1;//ARPHRD_ETHER;
    s32Ret = ioctl(m_stRtl8723Info.s32SocketId, SIOCGIWAP, &stWRQ);		//获取AP的mac地址
    if (s32Ret >= 0)
    {
        for (i = 0; i < 6; i++)
        {
            pau8Mac[i] = stWRQ.u.ap_addr.sa_data[i];
        }
    }
    else
    {
        for (i = 0; i < 6; i++)
        {
            pau8Mac[i] = 0xff;
        }
    }

    strcpy(stIfr.ifr_name, "wlan1");
    s32Ret = ioctl(m_stRtl8723Info.s32SocketId, SIOCGIFADDR, &stIfr);		//获取接口地址
    if (s32Ret >= 0)
    {
        memcpy(&stAddr, &stIfr.ifr_addr, sizeof(stAddr));
        *pu32IpAddr = (uint32)stAddr.sin_addr.s_addr;
    }
    else
    {
        *pu32IpAddr = 0;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: STA模式获取当前所连接热点的信号强度值
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 信息强度值
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_StaGetSignal()
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    sint32 s32Signal = 0;
    char szBuf[512] = {0};
    char *pszTmp = NULL;
    char *pszSave = NULL;

    s32Fd = open(PROC_NET_WIRELESS, O_RDONLY);
    if (s32Fd < 0)
    {
        print_level(SV_ERROR, "file: %s is unexist.\n", PROC_NET_WIRELESS);
        return 0;
    }

    s32Ret = read(s32Fd, szBuf, 512);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "read file: %s failed. [err=%#x]\n", PROC_NET_WIRELESS, errno);
        close(s32Fd);
        return 0;
    }

    pszTmp = strstr(szBuf, "wlan1:");
    if (NULL == pszTmp)
    {
        close(s32Fd);
        return 0;
    }

    pszTmp += strlen("wlan1:");
    pszTmp = strtok_r(pszTmp, " ", &pszSave);
    pszTmp = strtok_r(NULL, " ", &pszSave);		//函数返回值是分隔符后的那个字符，这里其实是拿wlan1:后面的第二个字符串，即为link的强度
    if (NULL != pszTmp)
    {
        sscanf(pszTmp, "%d", &s32Signal);
    }

    close(s32Fd);

    return s32Signal;
}

/******************************************************************************
 * 函数功能: 获取当前模块发射功率
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 发射功率值(dBm)
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_GetTxPower(sint32 *txPower)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    uint32 u32ErrCnt = 0;
    char szBuf[512] = {0};
    static sint32 s32TxPower = 0;
    char szCmd[128] = {0};
    char *pcTmp = NULL;
    char *pszTxPower = "TxPower: ";
	char szPowerPath[64] = {0};
	sprintf(szPowerPath, "/lib/firmware/%s/TXPWR_LMT.txt", m_stRtl8723Info.szWifiPath);

#ifndef PLATFORM_RV1126
    if (s32TxPower > 0)
    {
        *txPower = s32TxPower;
        return SV_SUCCESS;
    }

    memset(szBuf, 0x0, 512);
    while(s32Fd <= 0)
    {
        if (++u32ErrCnt >= 3)
        {
            *txPower = s32TxPower;
            return SV_SUCCESS;
        }

        s32Fd = open(szPowerPath, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_WARN, "open %s failed. [err=%#x]\n", szPowerPath, errno);
            sleep_ms(10);
        }

    }

    s32Ret = read(s32Fd, szBuf, 512);
    if(s32Ret <= 0 )
    {
        print_level(SV_WARN, "read %s failed. [err=%#x]\n", szPowerPath, errno);
    }
    else
    {
        pcTmp = strstr(szBuf, pszTxPower);
        if (NULL == pcTmp)
        {
            print_level(SV_WARN, "cannot find keyword: %s\n", pszTxPower);
        }
        else
        {
            pcTmp += strlen(pszTxPower);
            s32TxPower = atoi(pcTmp);
        }
    }
    close(s32Fd);
#else
#if 0
    strcpy(szCmd, "iwpriv wlan0 txpower get | awk -F ':' '{print$2}'");
    s32Ret = SAFE_System_Recv(szCmd, szBuf, 512);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "exec cmd: %s failed!\n", szCmd);
        return SV_FAILURE;
    }

    COMMON_CutLineBreak(szBuf);
    if (0 != strlen(szBuf))
    {
        s32TxPower = atoi(szBuf);
    }
#else
        s32TxPower = 20;
#endif
#endif

    *txPower = s32TxPower;
    return SV_SUCCESS;
}


/******************************************************************************
 * 函数功能: STA模式重新连接热点
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_StaReconnectAp()
{
    sint32 s32Ret = 0;
    char szCmd[64] = {0};
    char szFlushCmd[64] = {0};
    char szOtherCmd[64] = {0};

    strcpy(szOtherCmd, "ifconfig wlan1 down &");
    s32Ret = SAFE_System(szOtherCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szOtherCmd);
        SAFE_System(szOtherCmd, NORMAL_WAIT_TIME);
        rtl8188_SetStaConnStat("ifconfig wlan1 down failure");
    }
    sleep_ms(100);

    snprintf(szCmd, 64, "udhcpc -i wlan1 -q -x hostname %s &", HOST_NAME);
    SAFE_KillallProcess(szCmd);

#ifndef PLATFORM_RV1126
    strcpy(szCmd, "/root/wpa_supplicant2 -B -iwlan1 -c /var/wpa.conf");
#else
    strcpy(szCmd, "wpa_supplicant -B -iwlan1 -c /var/wpa.conf");
#endif
    SAFE_KillallProcess(szCmd);

    rtl8723_SetStaConnStat("flush ip addr");
    strcpy(szFlushCmd, "ip addr flush dev wlan1 &");
    s32Ret = SAFE_System(szFlushCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szFlushCmd);
        SAFE_System(szFlushCmd, NORMAL_WAIT_TIME);
        rtl8723_SetStaConnStat("flush ip addr failure");
    }

    strcpy(szOtherCmd, "ifconfig wlan1 up &");
    s32Ret = SAFE_System(szOtherCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szOtherCmd);
        SAFE_System(szOtherCmd, NORMAL_WAIT_TIME);
        rtl8188_SetStaConnStat("ifconfig wlan1 up failure");
    }
    sleep_ms(100);

    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: STA模式断开热点连接
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_StaDisconnectAp()
{
    sint32 s32Ret = 0;
    char szCmd[64];
    char szSSID[] = "invalid_ssid_7777777";
    struct iwreq stWRQ = {0};

    snprintf(szCmd, 64, "udhcpc -i wlan1 -q -x hostname %s &", HOST_NAME);
    SAFE_KillallProcess(szCmd);

#ifndef PLATFORM_RV1126
    strcpy(szCmd, "/root/wpa_supplicant2 -B -iwlan1 -c /var/wpa.conf");
#else
    strcpy(szCmd, "wpa_supplicant -B -iwlan1 -c /var/wpa.conf");
#endif
    SAFE_KillallProcess(szCmd);

    strcpy(szCmd, "ip addr flush dev wlan1 &");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    if (strlen(m_stRtl8723Info.szUsedSsid) != 0)
    {
        memset(&stWRQ, 0x0, sizeof(stWRQ));
        strcpy(stWRQ.ifr_ifrn.ifrn_name, "wlan1");
        stWRQ.u.essid.pointer = szSSID;
        stWRQ.u.essid.length = strlen(szSSID);
        stWRQ.u.data.flags = 1;
        s32Ret = ioctl(m_stRtl8723Info.s32SocketId, SIOCSIWESSID, &stWRQ);
        if (s32Ret < 0)
        {
            print_level(SV_ERROR, "ioctl SIOCSIWESSID failed. [err=%#x]\n", errno);
            return SV_FAILURE;
        }
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: STA模式重新从热点分配IP
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_StaReallocIp()
{
    sint32 s32Ret = 0;
    char szCmd[64];

    snprintf(szCmd, 64, "udhcpc -i wlan1 -q -x hostname %s &", HOST_NAME);
    SAFE_KillallProcess(szCmd);

    snprintf(szCmd, 64, "udhcpc -i wlan1 -q -x hostname:%s &", HOST_NAME);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}

sint32 rtl8723_SetStaConnStat(char *pszStat)
{
    if (NULL == pszStat)
    {
        return SV_FAILURE;
    }

    strcpy(m_stRtl8723Info.szStaStatusText, pszStat);
    return SV_SUCCESS;
}

void * rtl8723_sta_body(void *pvArg)
{
    sint32 s32Ret = 0;
    uint32 u32DisConnectCnt = 0, u32StaFailCnt = 0;
    RTL8723_COM_INFO_S *pstComInfo = (RTL8723_COM_INFO_S *)pvArg;
    char szCmd[64];
    char szBuf[128];
    char szSsid[64] = {0};
    uint8 au8Mac[6], au8MacOld[6] = {0xff, 0xff, 0xff, 0xaa, 0xbb, 0xcc};
    uint32 u32IpAddr = 0;
    float fSpeedTime = 0;
    char szRouterIp[64];
    SV_BOOL biPhoneHotspot  = SV_FALSE;

    s32Ret = prctl(PR_SET_NAME, "rtl8723_sta_body");			//PR_SET_NAME :把参数arg2作为调用进程的经常名字
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "prctl PR_SET_NAME failed! [err:%s]\n", strerror(errno));
    }

    while (pstComInfo->bRunning)
    {
        //print_level(SV_DEBUG, "rtl8188 model runnig... enConnStatus: %d\n", pstComInfo->enConnStatus);
        sleep_ms(1000);
        if (pstComInfo->bStaConfUpdate)
        {
            pstComInfo->enConnStatus = WIFI_CONN_CONNECTING;
            strcpy(pstComInfo->szStaIpAddr, "0.0.0.0");
            rtl8723_SetStaConnStat("reconnecting");
            s32Ret = rtl8723_StaReconnectAp();
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "rtl8723_StaReconnectAp failed. [err=%#x]\n", s32Ret);
                rtl8723_SetStaConnStat("reconnect failure");
                continue;
            }

            pstComInfo->bStaConfUpdate = SV_FALSE;
        }
        else if (!rtl8723_IsStaConnected())
        {
            rtl8723_SetStaConnStat("waiting for connection");

            if (WIFI_CONN_CONNECTING != pstComInfo->enConnStatus)
            {
                pstComInfo->enConnStatus = WIFI_CONN_CONNECTING;

                rtl8723_SetStaConnStat("flush ip addr");
                strcpy(szCmd, "ip addr flush dev wlan1 &");
                s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                if (0 != s32Ret)
                {
                    print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                    SAFE_System(szCmd, NORMAL_WAIT_TIME);
                    rtl8723_SetStaConnStat("flush ip addr failure");
                }
            }

            snprintf(szCmd, 64, "udhcpc -i wlan1 -q -x hostname %s &", HOST_NAME);
            SAFE_KillallProcess(szCmd);
            u32DisConnectCnt++;
            if (u32DisConnectCnt > 8)
            {
                if (biPhoneHotspot)
                {
                    m_WifiException = SV_TRUE;   // 针对iphone热点重连失败问题做重启驱动来解决
                    pstComInfo->bStaConfUpdate = SV_TRUE;
                }
                else
                {
                    u32StaFailCnt++;
                    if (u32StaFailCnt >= 10)
                    {
                        u32DisConnectCnt = 0;
                        u32StaFailCnt = 0;
                        memset(szBuf, 0, sizeof(szBuf));
                        sprintf(szCmd, "iwlist wlan1 scan | grep %s", pstComInfo->szStaSsid);
                        s32Ret = SAFE_System_Recv(szCmd, szBuf, 128);
                        if (SV_SUCCESS != s32Ret)
                        {
                            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                            continue;
                        }

                        if (NULL != strstr(szBuf, pstComInfo->szStaSsid))
                        {
                            print_level(SV_WARN, "STA ssid is exist but not connected, reset model to fix it now\n");
                            m_WifiException = SV_TRUE;
                        }
                        continue;
                    }

                    rtl8723_SetStaConnStat("need to reconnect");
                    print_level(SV_WARN, "STA need to reconnect!!!!\n");
                    strcpy(szCmd, "ifconfig wlan1 down");
                    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                    if (0 != s32Ret)
                    {
                        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                        return SV_FAILURE;
                    }
                    sleep_ms(1000);

                    strcpy(szCmd, "ifconfig wlan1 up");
                    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
                    if (0 != s32Ret)
                    {
                        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
                        return SV_FAILURE;
                    }
                    pstComInfo->bStaConfUpdate = SV_TRUE;
                }
                u32DisConnectCnt = 0;
            }
        }
        else
        {
            u32DisConnectCnt = 0;
            s32Ret = rtl8723_StaGetCurSsidMacIp(szSsid, au8Mac, &u32IpAddr);
            if (SV_SUCCESS != s32Ret)
            {
                print_level(SV_ERROR, "rtl8723_GetApSsidMac failed. [err=%#x]\n", s32Ret);
                continue;
            }

            if ((au8MacOld[0] != au8Mac[0] || au8MacOld[1] != au8Mac[1] || au8MacOld[2] != au8Mac[2] || \
                au8MacOld[3] != au8Mac[3] || au8MacOld[4] != au8Mac[4] || au8MacOld[5] != au8Mac[5] || \
                u32IpAddr == 0) && strlen(szSsid) != 0)
            {
                pstComInfo->enConnStatus = WIFI_CONN_CONNECTING;
                print_level(SV_INFO, "Connected to AP: %s [%02X:%02X:%02X:%02X:%02X:%02X]. try to allocate IP.\n", szSsid, \
                                    au8Mac[0], au8Mac[1], au8Mac[2], au8Mac[3], au8Mac[4], au8Mac[5]);
                strcpy(pstComInfo->szUsedSsid, szSsid);

                rtl8723_SetStaConnStat("allocating IP");
                s32Ret = rtl8723_StaReallocIp();
                if (SV_SUCCESS != s32Ret)
                {
                    print_level(SV_ERROR, "rtl8723_StaReconnectAp failed. [err=%#x]\n", s32Ret);
                    rtl8723_SetStaConnStat("allocate IP failure");
                    continue;
                }

                memcpy(au8MacOld, au8Mac, 6);
                sleep_ms(2000);
            }
            else if (WIFI_CONN_CONNECTED != pstComInfo->enConnStatus)
            {
                char *pcIpaddr = (char *)&u32IpAddr;
                sprintf(pstComInfo->szStaIpAddr, "%d.%d.%d.%d", pcIpaddr[0], pcIpaddr[1], pcIpaddr[2], pcIpaddr[3]);
                print_level(SV_INFO, "wlan1 allocated ipaddr: %s\n", pstComInfo->szStaIpAddr);
                pstComInfo->enConnStatus = WIFI_CONN_CONNECTED;
                rtl8723_SetStaConnStat("connect success");

                if (pcIpaddr[0] == 172 && pcIpaddr[1] == 20 && pcIpaddr[2] == 10)
                {
                    biPhoneHotspot = SV_TRUE;
                }
                else
                {
                    biPhoneHotspot = SV_FALSE;
                }
            }
            else
            {
                char *pcIpaddr = (char *)&u32IpAddr;
                sprintf(szRouterIp, "%d.%d.%d.1", pcIpaddr[0], pcIpaddr[1], pcIpaddr[2]);
                network_PingIpaddr("wlan1", szRouterIp, 5000, &fSpeedTime);
            }

            sleep_ms(3000);
        }
    }

    rtl8723_StaDisconnectAp();

    return NULL;
}

/******************************************************************************
 * 函数功能: 启动rtl8723模块STA模式
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
sint32 rtl8723_Sta_Start()
{
    sint32 s32Ret = 0;
    pthread_t thread = 0;

    if (m_stRtl8723Info.bRunning)
    {
        return SV_SUCCESS;
    }

    m_stRtl8723Info.bRunning = SV_TRUE;
    m_stRtl8723Info.enConnStatus = WIFI_CONN_DISCONNECTED;
    s32Ret = pthread_create(&thread, NULL, rtl8723_sta_body, &m_stRtl8723Info);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_create failed. [err=%#x]\n", s32Ret);
        return ERR_SYS_NOTREADY;
    }

    m_stRtl8723Info.u32TID = (uint32)thread;

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 停止rtl8723模块STA模式
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
sint32 rtl8723_Sta_Stop()
{
    sint32 s32Ret = 0;
    void *pvRetval = NULL;

    if (!m_stRtl8723Info.bRunning)
    {
        return SV_SUCCESS;
    }

    m_stRtl8723Info.bRunning = SV_FALSE;
    m_stRtl8723Info.enConnStatus = WIFI_CONN_DISCONNECTED;
    strcpy(m_stRtl8723Info.szStaStatusText, "disable");
    strcpy(m_stRtl8723Info.szStaIpAddr, "0.0.0.0");
    s32Ret = pthread_join(m_stRtl8723Info.u32TID, &pvRetval);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "pthread_join failed. [err=%#x]\n", s32Ret);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 配置rtl8723模块STA模式工作参数
 * 输入参数: bEnable --- 是否使能STA模式
             pszSsid --- 访问热点的SSID
             pszPwd  --- 访问热点的密码
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    :
 *****************************************************************************/
sint32 rtl8723_STA_Config(SV_BOOL bEnable, char *pszSsid, char *pszPwd)
{
    sint32 s32Ret = 0;
    char szCmd[64];

    if (NULL == pszSsid || NULL == pszPwd)
    {
        return ERR_NULL_PTR;
    }

    if (strlen(pszPwd) != 0)
    {
#ifndef PLATFORM_RV1126
        sprintf(szCmd, "/root/wpa_passphrase \"%s\" \"%s\" > /var/wpa.conf", pszSsid, pszPwd);
#else
        sprintf(szCmd, "wpa_passphrase \"%s\" \"%s\" > /var/wpa.conf", pszSsid, pszPwd);
#endif
        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
            return SV_FAILURE;
        }
    }
    else
    {
        char szBuf[128];
        sprintf(szBuf, "network={\r\n\tssid=\"%s\"\r\n\tkey_mgmt=NONE\r\n}", pszSsid);
        s32Ret = SAFE_ECHO("/var/wpa.conf", szBuf);
        if (0 != s32Ret)
        {
            print_level(SV_ERROR, "SAFE_ECHO %s failed.\n", szBuf);
            return SV_FAILURE;
        }
    }

    m_stRtl8723Info.bStaEnable = bEnable;
    strcpy(m_stRtl8723Info.szStaSsid, pszSsid);
    m_stRtl8723Info.bStaConfUpdate = SV_TRUE;
    if (bEnable)
    {
        rtl8723_Sta_Start();
    }
    else
    {
        rtl8723_Sta_Stop();
    }

    return SV_SUCCESS;
}

sint32 rtl8723_Ap_Stop(SV_BOOL bRmmod)
{
    sint32 s32Ret = 0;
    char szCmd[64];

	SAFE_System("killall -9 hostapd", NORMAL_WAIT_TIME);
    SAFE_KillallProcess("udhcpd /etc/udhcpd.conf");
    m_stRtl8723Info.bApConfigFinished = SV_FALSE;

    if (bRmmod)
    {
        switch (m_stRtl8723Info.enWifiId)
        {
            case WIFI_ID_RTL8723DU:
                strcpy(szCmd, "rmmod 8723du");
                break;

            default:
                print_level(SV_ERROR, "not support wifi ID: %d\n", m_stRtl8723Info.enWifiId);
                break;
        }

        s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
        if(0 != s32Ret)
        {
            print_level(SV_ERROR, "cmd: %s failed.", szCmd);
            return SV_FAILURE;
        }
        sleep_ms(100);
    }

    return SV_SUCCESS;
}

/******************************************************************************
 * 函数功能: 配置rtl8723 AP 热点参数
 * 输入参数: enWifiAuth --- 热点认证模式
             pszSsid --- AP热点SSID
             pszPassword --- AP登陆密码
             enWifiFreq --- AP使用频段
             lSetChannel --- 选择的信道
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
sint32 rtl8723_AP_Config(WIFI_AUTH enWifiAuth, const char *pszSsid, const char *pszApIpAddr, const char *pszPassword, WIFI_FREQ enWifiFreq, long lSetChannel)
{
    sint32 s32Ret = 0;
    sint32 s32Fd = 0;
    long channel24G = 0, channel5G = 0, selChannel = 0;
    char *pszBestChn24G = "best_channel_24G = ";
    char *pszBestChn5G = "best_channel_5G = ";
    char *pszSelFreq = NULL;
    char *pcTmp = NULL;
    char szStr[64];
    char szCmd[256];
    char szCheckApSsid[256] = {0};
    char szCheckApPwd[256] = {0};
    char szBuf[4096];
    char szWifiApIpAddr[32] = {0};
    char *wifiapipaddr_tmp;

    if (NULL == pszSsid || NULL == pszApIpAddr || NULL == pszPassword)
    {
        print_level(SV_ERROR, "input pointer is null.\n");
        return ERR_NULL_PTR;
    }

    if (enWifiAuth >= WIFI_AUTH_BUTT || enWifiFreq >= WIFI_FREQ_BUTT)
    {
        return ERR_ILLEGAL_PARAM;
    }

    strncpy(szWifiApIpAddr, pszApIpAddr, 32);
    m_stRtl8723Info.bApConfigFinished = SV_FALSE;

    strcpy(szCmd, "ifconfig wlan0 down");
	s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
	if (0 != s32Ret)
	{
		print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
		m_WifiException = SV_TRUE;
	}
	sleep_ms(100);

    SAFE_System("killall -9 hostapd", NORMAL_WAIT_TIME);
    SAFE_KillallProcess("udhcpd /var/udhcpd.conf");

	sleep_ms(100);
	strcpy(szCmd, "ifconfig wlan0 up");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        m_WifiException = SV_TRUE;
    }

	print_level(SV_INFO, "Set AP wlan0 IP: %s\n", szWifiApIpAddr);
	strcpy(szCmd, "ifconfig wlan0 ");
	strcat(szCmd, szWifiApIpAddr);
	print_level(SV_INFO, "szCmd_p: %s\n",szCmd);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        m_WifiException = SV_TRUE;
    }

    s32Ret = RTL8723_SetCountryCode();
    if(SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "RTL8723_SetCountryCode failed.\n");
    }

    print_level(SV_INFO, "lSetChannel=%d\n", lSetChannel);
    selChannel = rtl8723_SetChannel(enWifiFreq, lSetChannel);
	if (selChannel == SV_FAILURE)
	{
        print_level(SV_ERROR, "rtl8723_SetChannel failed.\n");
	}

    pszSelFreq = (enWifiFreq == WIFI_FREQ_2_4G) ? "2.4G" : "5G";
    print_level(SV_INFO, "Set AP: frequency=%s, channel=%d\n", pszSelFreq, selChannel);
    strcpy(szCmd, "cp /etc/hostapd.conf /var/hostapd.conf");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    //处理特殊字符'/'
    print_level(SV_INFO, "ApSsid: %s\n", pszSsid);
    if(strchr(pszSsid, '/'))
        rtl8723_ReviseApInfo(pszSsid, szCheckApSsid, "/","\\/");
    else
        strcpy(szCheckApSsid, pszSsid);

    print_level(SV_INFO, "ApPwd: %s\n", pszPassword);
    if(strchr(pszPassword, '/'))
        rtl8723_ReviseApInfo(pszPassword, szCheckApPwd, "/","\\/");
    else
        strcpy(szCheckApPwd, pszPassword);

    sprintf(szCmd, "sed -i \"s/^ssid=.*/ssid=%s/g\" /var/hostapd.conf", szCheckApSsid);
    print_level(SV_DEBUG, "szCmd: %s\n", szCmd);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    sprintf(szCmd, "sed -i \"s/^wpa_passphrase=.*/wpa_passphrase=%s/g\" /var/hostapd.conf", szCheckApPwd);
    print_level(SV_DEBUG, "szCmd: %s\n", szCmd);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    sprintf(szCmd, "sed -i \"s/^wpa=.*/wpa=%s/g\" /var/hostapd.conf", (enWifiAuth == WIFI_AUTH_WPAPSK) ? "2" : "0");
    print_level(SV_DEBUG, "szCmd: %s\n", szCmd);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    sprintf(szCmd, "sed -i \"s/^channel=.*/channel=%d/g\" /var/hostapd.conf", selChannel);
    print_level(SV_DEBUG, "szCmd: %s\n", szCmd);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    if (selChannel <= 14)
    {
        sprintf(szCmd, "sed -i \"s/^hw_mode=.*/hw_mode=g/g\" /var/hostapd.conf");
    }
    else
    {
        sprintf(szCmd, "sed -i \"s/^hw_mode=.*/hw_mode=a/g\" /var/hostapd.conf");
    }
    print_level(SV_DEBUG, "szCmd: %s\n", szCmd);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    strcpy(szCmd, "hostapd -B /var/hostapd.conf");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        m_WifiException = SV_TRUE;
    }

    /* 修改DHCPD的配置 */
    wifiapipaddr_tmp = strrchr(szWifiApIpAddr, '.');
    print_level(SV_INFO,"change Router Setting in udhcpd.conf\n");
    if(wifiapipaddr_tmp == NULL)
    {
        print_level(SV_ERROR, "bChangeWifiApIpAddr failed.\n");
    }

    sprintf(szCmd, "cp /etc/%s /var/", "udhcpd.conf");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    sprintf(szCmd,"sed -i \"/^opt\trouter/copt\trouter\t%s\" %s", szWifiApIpAddr, "/var/udhcpd.conf");
    SAFE_System(szCmd,NORMAL_WAIT_TIME);

    strcpy(wifiapipaddr_tmp,".100");
    sprintf(szCmd,"sed -i \"/^start/cstart\t\t%s\" %s", szWifiApIpAddr, "/var/udhcpd.conf");
    SAFE_System(szCmd,NORMAL_WAIT_TIME);

    strcpy(wifiapipaddr_tmp,".200");
    sprintf(szCmd,"sed -i \"/^end/cend\t\t%s\" %s", szWifiApIpAddr, "/var/udhcpd.conf");
    SAFE_System(szCmd,NORMAL_WAIT_TIME);

    strcpy(szCmd, "udhcpd /var/udhcpd.conf&");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
    }

    m_stRtl8723Info.bApConfigFinished = SV_TRUE;
    return SV_SUCCESS;
}

sint32 RTL8723_Init(WIFI_CONF_S *pstWifiParam, WIFI_ID_E enWifiId)
{
    sint32 s32Ret = 0;
    long lSetChannel = 0;
    char szCmd[64];

    if (NULL == pstWifiParam)
    {
        return ERR_NULL_PTR;
    }

#ifndef PLATFORM_RV1126
    /* 删除其它多余的WiFi驱动节省flash空间 */
    if (0 == access("/root/ko/extdrv/8821au.ko", F_OK))
    {
        SAFE_System("fs_write_enable 1", NORMAL_WAIT_TIME);
        SAFE_System("rm /root/ko/extdrv/8821au.ko", NORMAL_WAIT_TIME);
        SAFE_System("fs_write_enable 0", NORMAL_WAIT_TIME);
    }

	if (0 == access("/root/ko/extdrv/8821cu.ko", F_OK))
    {
        SAFE_System("fs_write_enable 1", NORMAL_WAIT_TIME);
        SAFE_System("rm /root/ko/extdrv/8821au.ko", NORMAL_WAIT_TIME);
        SAFE_System("fs_write_enable 0", NORMAL_WAIT_TIME);
    }

    if (0 == access("/root/ko/extdrv/mt7601Usta.ko", F_OK))
    {
        SAFE_System("fs_write_enable 1", NORMAL_WAIT_TIME);
        SAFE_System("rm /root/ko/extdrv/mt7601Usta.ko", NORMAL_WAIT_TIME);
        SAFE_System("fs_write_enable 0", NORMAL_WAIT_TIME);
    }

    if (0 == access("/root/wpa_supplicant", F_OK))
    {
        SAFE_System("fs_write_enable 1", NORMAL_WAIT_TIME);
        SAFE_System("rm /root/wpa_supplicant", NORMAL_WAIT_TIME);
        SAFE_System("fs_write_enable 0", NORMAL_WAIT_TIME);
    }
#endif

    memset(&m_stRtl8723Info, 0x0, sizeof(RTL8723_COM_INFO_S));
	m_stRtl8723Info.enWifiId = enWifiId;

    switch (m_stRtl8723Info.enWifiId)
    {
        case WIFI_ID_RTL8723DU:
            strcpy(szCmd, "insmod /root/ko/extdrv/8723du.ko rtw_drv_log_level=3");
		    strcpy(m_stRtl8723Info.szWifiPath, "rtl8723du");
            break;

        default:
            print_level(SV_ERROR, "not support wifi ID: %d\n", enWifiId);
            return SV_FAILURE;
    }

    SAFE_System(szCmd, NORMAL_WAIT_TIME);

    strcpy(szCmd, "> /var/wpa.conf");
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    m_stRtl8723Info.s32SocketId = socket(AF_INET, SOCK_DGRAM, 0);
    if (m_stRtl8723Info.s32SocketId < 0)
    {
        print_level(SV_ERROR, "socket init failed.\n");
        return ERR_SYS_NOTREADY;
    }

    strncpy(m_stRtl8723Info.szCountryCode, pstWifiParam->szCountryCode, 4);
    m_stRtl8723Info.szCountryCode[3] = '\0';
    lSetChannel = pstWifiParam->enWifiFreq == WIFI_FREQ_2_4G ? pstWifiParam->lSet2GChannel : pstWifiParam->lSet5GChannel;
    s32Ret = rtl8723_AP_Config(pstWifiParam->enWifiAuth, pstWifiParam->szWifiApSsid, pstWifiParam->szWifiApIpAddr, pstWifiParam->szWifiApPwd, pstWifiParam->enWifiFreq, lSetChannel);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "rtl8723_AP_Config failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    s32Ret = rtl8723_STA_Config(pstWifiParam->bWifiStaEnable, pstWifiParam->szWifiStaSsid, pstWifiParam->szWifiStaPwd);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "rtl8723_STA_Config failed. [err=%#x]\n", s32Ret);
        return s32Ret;
    }

    return SV_SUCCESS;
}

sint32 RTL8723_Fini(SV_BOOL bRmmod)
{
    sint32 s32Ret = 0;

	rtl8723_Ap_Stop(bRmmod);
	rtl8723_Sta_Stop();
    if (m_stRtl8723Info.s32SocketId >= 0)
    {
        close(m_stRtl8723Info.s32SocketId);
    }

    memset(&m_stRtl8723Info, 0x0, sizeof(RTL8723_COM_INFO_S));

    return SV_SUCCESS;
}

sint32 RTL8723_SetWifiId(WIFI_ID_E enWifiId)
{
	m_stRtl8723Info.enWifiId = enWifiId;
    return SV_SUCCESS;
}

sint32 RTL8723_Query_Status(WIFI_STAT_S *pstStatus)
{
    sint32 s32Ret;
    sint32 u32TxPower = 0;
    if (NULL == pstStatus)
    {
        return COM_ERR_NULL_PTR;
    }

    pstStatus->enWifiId = m_stRtl8723Info.enWifiId;
    pstStatus->enConnStat = m_stRtl8723Info.enConnStatus;
    pstStatus->u32Signal = (WIFI_CONN_CONNECTED == m_stRtl8723Info.enConnStatus) ? rtl8723_StaGetSignal() : 0;

    s32Ret = rtl8723_GetTxPower(&u32TxPower);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "rtl8723_GetTxPower failed.\n");
    }
    else
    {
        pstStatus->u32TxPower = u32TxPower;
    }

    pstStatus->lCurChannel = rtl8723_GetChannel();
    if(m_stRtl8723Info.szCountryCode[0] != '\0')
        memcpy(pstStatus->szCountryCode, m_stRtl8723Info.szCountryCode, sizeof(m_stRtl8723Info.szCountryCode));

    pstStatus->bStaEnable = m_stRtl8723Info.bStaEnable;
    strcpy(pstStatus->szStaStatusText, m_stRtl8723Info.szStaStatusText);
    strcpy(pstStatus->szStaSsid, m_stRtl8723Info.szStaSsid);
    if (WIFI_CONN_CONNECTED == m_stRtl8723Info.enConnStatus)
    {
        strcpy(pstStatus->szStaIpAddr, m_stRtl8723Info.szStaIpAddr);
    }
    else
    {
        strcpy(pstStatus->szStaIpAddr, "0.0.0.0");
    }

    return SV_SUCCESS;
}

uint32 RTL8723_GetSignalLevel(uint32 u32SteamKbps)
{
    sint32 s32Fd = 0;
    uint32 u32Signal = 0;
    char *pszSignalStrength = "signal_qual:";
    char *pcTmp = NULL;
    char szBuf[128];
	char szSignalPath[128];
	sprintf(szSignalPath, "/proc/net/%s/wlan0/rx_signal", m_stRtl8723Info.szWifiPath);

    memset(szBuf, 0x0, 128);
    s32Fd = open(szSignalPath, O_RDONLY);
    if (s32Fd < 0 || read(s32Fd, szBuf, 128) <= 0)
    {
        print_level(SV_ERROR, "acess %s failed. [err=%#x]\n", szSignalPath, errno);
    }
    else
    {
        pcTmp = strstr(szBuf, pszSignalStrength);
        if (NULL == pcTmp)
        {
            print_level(SV_ERROR, "cannot find keyword: %s\n", pszSignalStrength);
        }
        else
        {
            pcTmp += strlen(pszSignalStrength);
            u32Signal = atoi(pcTmp);
            //print_level(SV_DEBUG, "[1]u32Signal:%d\n", u32Signal);
            if (1 >= u32Signal)
            {
                close(s32Fd);
                return 100;     // 当没有手机连入的时候使码流按最高值进行编码
            }
        }
    }
    close(s32Fd);

    return u32Signal;
}

uint32 RTL8723_GetBandwidthLevel()
{
    sint32 s32Fd = 0;
    uint32 u32Signal = 0;
	char *pstOFDM = "cur_tx_rate= OFDM_";
    char *pcTmp = NULL;
    char szBuf[1024];
	char szSignalPath[128];
	uint32 u32Bandwidth = 1;//低速传输CCK调制时，为CCK_1M，所以初始值设定为1
	sprintf(szSignalPath, "/proc/net/%s/wlan0/trx_info_debug", m_stRtl8723Info.szWifiPath);

    memset(szBuf, 0x0, 1024);
    s32Fd = open(szSignalPath, O_RDONLY);
    if (s32Fd < 0 || read(s32Fd, szBuf, 1024) <= 0)
    {
        print_level(SV_ERROR, "acess %s failed. [err=%#x]\n", szSignalPath, errno);
    }
    else
    {

		pcTmp = strstr(szBuf, pstOFDM);
		if (pcTmp == NULL)
		{
			//print_level(SV_ERROR, "cannot find keyword: %s\n", pstOFDM);
		}
		else
		{
			pcTmp += strlen(pstOFDM);
			if (sscanf(pcTmp, "%dM", &u32Bandwidth) == 1)
			{
				//print_level(SV_DEBUG,"get bandwidth:%d.\n", u32Bandwidth);
			}
			else
			{
				//print_level(SV_DEBUG,"cannot get bandwidth!\n");
			}
		}

    }
    close(s32Fd);

    return u32Bandwidth;
}

sint32 RTL8723_SetCountryCode(void)
{
    sint32 s32Ret = 0;
    char szCmd[256];

    sprintf(szCmd, "echo %.2s > /proc/net/%s/wlan0/country_code", m_stRtl8723Info.szCountryCode, m_stRtl8723Info.szWifiPath);
    print_level(SV_INFO, "szCmd: %s\n", szCmd);
    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_WARN, "cmd: %s failed.\n", szCmd);
    }

    return SV_SUCCESS;
}

sint32 RTL8723_GetAvailableChannel(const sint32 **s32Channel, sint32 *s32Number)
{
    sint32 s32Ret = 0, i = 0;
    sint32 s32Fd = 0;
    uint32 u32ErrCnt = 0;
    char szBuf[4096] = {0};
    char *pcTmp = NULL;
    char *pcBufTmp = NULL;
    char *pszChannel = "ch:";
    static sint32 AvailableChannel[42] = {0};
	char szChanPlanPath[64] = {0};
	sprintf(szChanPlanPath, "/proc/net/%s/wlan0/chan_plan", m_stRtl8723Info.szWifiPath);

    memset(szBuf, 0x0, 4096);
    while(s32Fd <= 0)
    {
        if(++u32ErrCnt >= 3)
            break;

        s32Fd = open(szChanPlanPath, O_RDONLY);
        if (s32Fd < 0)
        {
            print_level(SV_WARN, "open %s failed. [err=%#x]\n", szChanPlanPath, errno);
            sleep_ms(10);
        }

    }

    s32Ret = read(s32Fd, szBuf, 4096);
    if(s32Ret <= 0 )
    {
        print_level(SV_ERROR, "read %s failed. [err=%#x]\n", szChanPlanPath, errno);
    }
    else
    {
        pcBufTmp = szBuf;
        while(1)
        {
            pcTmp = strstr(pcBufTmp, pszChannel);
            if (NULL == pcTmp)
            {
                print_level(SV_WARN, "cannot find keyword: %s\n", pszChannel);
                *s32Channel = AvailableChannel;
                *s32Number = i;
                break;
            }
            else
            {
                pcTmp += strlen(pszChannel);
                //print_level(SV_DEBUG, "channel:%d\n", atoi(pcTmp));
                AvailableChannel[i++] = atoi(pcTmp);
            }
            pcBufTmp += (pcTmp - pcBufTmp);
        }
    }
    close(s32Fd);

    return SV_SUCCESS;
}

sint32 RTL8723_Set_TxPower(SV_BOOL bSet, uint32 u32Db)
{
    sint32 s32Ret = 0;
    char szCmd[64];

    if (bSet)
    {
        sprintf(szCmd, "iwpriv wlan0 txpower set=%d", u32Db);
    }
    else
    {
        sprintf(szCmd, "iwpriv wlan0 txpower cancel");
    }

    s32Ret = SAFE_System(szCmd, NORMAL_WAIT_TIME);
    if (0 != s32Ret)
    {
        print_level(SV_ERROR, "cmd: %s failed.\n", szCmd);
        return SV_FAILURE;
    }

    return SV_SUCCESS;
}


