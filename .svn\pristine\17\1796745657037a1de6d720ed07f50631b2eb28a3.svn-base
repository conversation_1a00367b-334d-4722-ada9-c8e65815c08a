/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：mpp_venc.h

作者: 许家铭    版本: v1.0.0(初始版本号)   日期: 2017-11-22

文件功能描述: 封装海思MPP视频编码模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)
  
历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#ifndef _MPP_VENC_H_
#define _MPP_VENC_H_

#include "common.h"
#include "mpp_com.h"
#include "media.h"
#if (PLATFORM == PLATFORM_RV1106)
#include "rk_comm_video.h"
#endif


#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */


/* 视频编码配置结构定义 */
typedef struct tagVENCCfg_S
{
    uint32              u32ChnNum;      /* 输入通道数目 */
    SV_BOOL             bPreInited;     /* 是否预初始化 */
    VIDEO_MODE_EE       enVideoMode;    /* 视频制式 */
    WDR_MODE_EE         enSysWDRMode;   /* WDR模式 */    
    SV_ROT_ANGLE_E      enRotateAngle;  /* 画面旋转角度 */
    VIDEO_ENCODE_H264_S stPriVencAttr;  /* 主码流编码通道属性 */
    VIDEO_ENCODE_H264_S stSecVencAttr;  /* 次码流编码通道属性 */
    VIDEO_ENCODE_JPEG_S stJpegVencAttr; /* 图片流编码通道属性 */
    MEDIA_DATA_CALLBACK pfDataCallback; /* 媒体流数据回调函数指针 */
} MPP_VENC_CONF_S;

/******************************************************************************
 * 函数功能: 初始化VENC模块
 * 输入参数: pstVencConf --- 视频编码配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             ERR_ILLEGAL_PARAM - 传入参数非法
             ERR_EXIST - 编码通道组或通道已存在
             ERR_SYS_NOTREADY - 系统未初始化
             ERR_NOT_PERM - 操作不允许
             ERR_NOMEM - 内存不足
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_Init(MPP_VENC_CONF_S *pstVencConf);

/******************************************************************************
 * 函数功能: 去初始化VENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_UNEXIST - 编码通道不存在
             ERR_SYS_NOTREADY - 系统未初始化
             ERR_NOT_PERM - 操作不允许
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_Fini();

/******************************************************************************
 * 函数功能: 启动VENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_Start();

/******************************************************************************
 * 函数功能: 停止VENC模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_Stop();

/******************************************************************************
 * 函数功能: 创建H264编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_H264CreateChn(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr);

/******************************************************************************
 * 函数功能: 销毁H264编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_H264DestroyChn(STREAM_TYPE_E enStreamType, sint32 s32Chn);

/******************************************************************************
 * 函数功能: 创建MJPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_MJpegCreateChn(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr);

/******************************************************************************
 * 函数功能: 销毁MJPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_MJpegDestroyChn(STREAM_TYPE_E enStreamType, sint32 s32Chn);

/******************************************************************************
 * 函数功能: 创建JPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_SNAP0, STREAM_TYPE_SNAP1)
             s32Chn --- 编码通道号 [0, VIChnNum)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_SURPPORT - 操作不支持
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_JpegCreateChn(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_JPEG_S *pstVencAttr);

/******************************************************************************
 * 函数功能: 销毁JPEG编码通道
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_SNAP0, STREAM_TYPE_SNAP1)
             s32Chn --- 编码通道号 [0, VIChnNum)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_JpegDestroyChn(STREAM_TYPE_E enStreamType, sint32 s32Chn);

/******************************************************************************
 * 函数功能: 将编码通道注册到编码通道组中
 * 输入参数: VeGroup --- 编码通道组
             enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 注册前必需保证编码通道组和编码通道已创建
 *****************************************************************************/
extern sint32 mpp_venc_RegisterChn(sint32 VeGroup, STREAM_TYPE_E enStreamType, sint32 s32Chn);

/******************************************************************************
 * 函数功能: 将编码通道反注册脱离编码通道组
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 注册前必需保证编码通道组和编码通道已创建
 *****************************************************************************/
extern sint32 mpp_venc_UnRegisterChn(STREAM_TYPE_E enStreamType, sint32 s32Chn);

/******************************************************************************
 * 函数功能: 使能彩色转灰色处理
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_Color2GreyEnable();

/******************************************************************************
 * 函数功能: 禁止彩色转灰色处理
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_Color2GreyDisable();

/******************************************************************************
 * 函数功能: 请求立即生成一个H264关键帧
 * 输入参数: s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
             enStreamType --- 码流类型 [STREAM_TYPE_PRI, STREAM_TYPE_SEC]
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_RequestIFrame(sint32 s32Chn, STREAM_TYPE_E enStreamType);

/******************************************************************************
 * 函数功能: 使能某编码通道进行编码
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_EnableChn(STREAM_TYPE_E enStreamType, sint32 s32Chn);

/******************************************************************************
 * 函数功能: 禁止某编码通道编码
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_DisableChn(STREAM_TYPE_E enStreamType, sint32 s32Chn);

/******************************************************************************
 * 函数功能: 开启某路通道的快速抓拍功能
 * 输入参数: s32ChnMask --- 通道掩码
             u32Quality ---- 图片质量级别 [0-5]: 数值超高质量越好
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 可根据掩码开启多路通道
 *****************************************************************************/
extern sint32 mpp_venc_FastSnapStart(sint32 s32ChnMask, uint32 u32Quality);

/******************************************************************************
 * 函数功能: 关闭快速抓拍功能
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_FastSnapStop();

/******************************************************************************
 * 函数功能: 设置H264编码通道动态属性
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
             pstVencAttr --- 编码通道属性
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 只允许修改编码通道的动态属性 (目标帧率, 码率控制模式, 编码码率)
 *****************************************************************************/
extern sint32 mpp_venc_H264AttrSet(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr);

/******************************************************************************
 * 函数功能: 获取H264编码通道动态属性
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: pstVencAttr --- 编码通道属性
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_H264AttrGet(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_H264_S *pstVencAttr);

/******************************************************************************
 * 函数功能: 获取Jpeg编码通道动态属性
 * 输入参数: enStreamType --- 编码通道码流类型 (STREAM_TYPE_PRI, STREAM_TYPE_SEC, STREAM_TYPE_SNAP0)
             s32Chn --- 编码通道号 [0, VIODE_MAX_CHN)
 * 输出参数: pstVencAttr --- 编码通道属性
 * 返回值  : SV_SUCCESS - 成功
             ERR_ILLEGAL_PARAM - 参数非法
             ERR_INVALID_CHNID - 通道号无效
             ERR_UNEXIST - 通道不存在
             ERR_NOT_PERM - 操作不允许
             ERR_SYS_NOTREADY - 系统未初始化
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_JpegAttrGet(STREAM_TYPE_E enStreamType, sint32 s32Chn, VIDEO_ENCODE_JPEG_S *pstVencAttr);

/******************************************************************************
 * 函数功能: 重设JPEG编码通道图源大小
 * 输入参数: u32NewW --- 重设图源宽
             u32NewH --- 重设图源高
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_RenewJpegSize(VIDEO_MODE_EE newVideoMode, uint32 u32NewW, uint32 u32NewH);

#if (PLATFORM != PLATFORM_RV1106)
/******************************************************************************
 * 函数功能: 往编码通道发送图像Buffer
 * 输入参数: s32Chn --- 视频源通道号
             s32VencChn --- VENC通道号
             mb         --- MediaBuffer 指针
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_SetFrame(sint32 s32Chn, sint32 s32VencChn, void *mb);
#else
/******************************************************************************
 * 函数功能: 发送数据给编码通道
 * 输入参数: s32Chn --- VPSS通道 [0, VIODE_MAX_CHN)
             s32VencChn --- VENC通道号
             pstVideoFrame --- 视频帧信息
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_SetFrame(sint32 s32Chn, sint32 s32VencChn, VIDEO_FRAME_INFO_S *pstVideoFrame);
#endif

/*****************************************************************************
 * 函数功能: 重新设置编码的相关制式
 * 输入参数: pnewVideoMode --- 视频制式
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其他错误
 * 注意  : 无
*****************************************************************************/
extern sint32 mpp_venc_RenewAttr(VIDEO_MODE_EE *pnewVideoMode);

/******************************************************************************
 * 函数功能: 暂停VENC模块
 * 输入参数: bInterrupt --- 是否暂停模块
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 无
 *****************************************************************************/
extern sint32 mpp_venc_Interrupt(SV_BOOL bInterrupt);

/******************************************************************************
 * 函数功能: 设置通道画面顺时针旋转角度
 * 输入参数: s32Chn --- 通道号
             enAngle --- 旋转角度
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
 * 注意    : 目前是所有通道全局设置，通道号暂时无用
 *****************************************************************************/
extern sint32 mpp_venc_SetChnRotate(sint32 s32Chn, SV_ROT_ANGLE_E enAngle);

#if 1	//lufeng, debug venc_param
extern sint32 mpp_venc_GetSceneMode(sint32 s32Chn, uint8 *penSceneMode);
extern sint32 mpp_venc_SetSceneMode(sint32 s32Chn, uint8 enSceneMode);
#endif

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* _MPP_VENC_H_ */

